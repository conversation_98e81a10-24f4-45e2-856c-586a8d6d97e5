{"guid": "901B0E6D3EE91ED7A9F02AC8E4A560D7", "sitemId": "SI03: DB_BAA", "sitemTitle": "S4TWL - Removal of Business Application Accelerator", "note": 1694697, "noteTitle": "1694697 - SAP Business Application Accelerator powered by HANA", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are looking for information about the SAP Business Application Accelerator powered by HANA.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Suite Accelerator, Scenarios, Context, RDA_MAINTAIN</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>The SAP Business Application Accelerator powered by HANA has been offered as ABAP Add-on in a restricted shipment mode until end of 2016.</p>\n<p>Starting with 2017 the Add-on is no longer available for new customers.</p>\n<p>The Add-on is in general not supported in S/4 HANA. The existence of the Add-on is blocking technical conversions to S/4HANA 1511 and 1610. Technical conversions to 1709 and beyond are possible.</p>\n<p>Support for existing customers on non-4/HANA products is continued under the same conditions as before.</p>\n<p>To benefit from SAP HANA it is recommended to migrate the entire system to SAP HANA, possibly with a multi node setup for optimized load distribution.</p>\n<p>For a description of the functionality and usage of the SAP Business Application Accelerator powered by HANA please read the attached Customer Guide.</p>\n<p>Contents:</p>\n<p>1 Introduction <br/>2 Concept <br/>2.1 Redirected Operations <br/>2.1.1 Statements <br/>2.1.2 Source <br/>2.2 Context Definition <br/>2.3 Context Evaluation at Runtime <br/>3 Using the SAP Business Application Accelerator <br/>3.1 Installation <br/>3.2 General Activation <br/>3.3 Loading Scenarios <br/>3.3.1 Predefined SAP Scenarios <br/>3.3.2 Custom Scenarios <br/>3.4 Trouble Shooting</p></div>", "noteVersion": 16, "refer_note": [{"note": "1949508", "noteTitle": "1949508 - SWT2DB: No redirection", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>In a system, there is no redirection of SELECT statements in ABAP Open SQL using a configured logical database connection to a HANA database, even though a relevant scenario has been activated.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Redirect Database Access, RDA, SAP HANA Application Accelerator, SWT2DB</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>There is a programming error.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>The problem is solved with the patch levels listed below.</p>", "noteVersion": 1}, {"note": "1716826", "noteTitle": "1716826 - Usage of the downward compatible kernel 721 (EXT)", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You want to use the kernel 721 or 721 EXT kernel as a downward-compatible kernel in one of the following NetWeaver releases:<br/>SAP NetWeaver 7.0 (\"7.00\")<br/>SAP EhP1 for SAP NetWeaver 7.0 (\"7.01\")<br/>SAP EhP2 for SAP NetWeaver 7.0 (\"7.02\")<br/>SAP EhP3 for SAP NetWeaver 7.0 (\"7.03\")<br/>SAP NetWeaver 7.10 (\"7.10\")<br/>SAP EhP1 for SAP NetWeaver 7.10 (\"7.11\")<br/>SAP NetWeaver 7.2 (\"7.20\")<br/>SAP NetWeaver 7.3 (\"7.30\")<br/>SAP EhP1 forNetWeaver 7.3 (\"7.31\");<br/><br/>or SAP products based on these, such as:<br/>SAP ECC 6.0<br/>SAP CRM 5.0, 2007, 7.0<br/>SAP SRM 5.0, 6.0, 7.0<br/>SAP NetWeaver PI 7.1<br/>SAP NetWeaver Mobile 7.1<br/>SAP NetWeaver Composition Environment (CE) 7.1<br/>SAP EhP1 for NW CE 7.1<br/>SAP NetWeaver CE 7.2</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>kernel, dck, akk, 721, 721 EXT, 721_EXT, downward compatible kernel</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p><strong>Where can the 721 kernel be used?</strong><br/><br/>There are two use cases for the 721 (EXT) downward compatible kernel:</p>\n<ol>\n<li>For all systems with NetWeaver Releases 7.00, 7.01 (7.0 Enhancement Package 1), 7.10 and 7.11 (7.1 Enhancement Package 1) that still run with a kernel 700, 701, 710 or 711 which was delivered originally. These kernel versions are out of maintenance maintenance since August 31st, 2012. You may install the kernel 721 (EXT) on these systems as an alternative to the 720 (EXT) downward compatible kernel.</li>\n<li>For all systems running the kernel 720 (EXT) which are:</li>\n</ol>\n<ul>\n<ul>\n<li>Systems originally delivered with the 720 (EXT) kernel such as:<br/>SAP EhP2 for SAP NetWeaver 7.0 (\"7.02\")<br/>SAP EhP3 for SAP NetWeaver 7.0 (\"7.03\")<br/>SAP NetWeaver 7.2 (\"7.20\")<br/>SAP NetWeaver 7.3 (\"7.30\")<br/>SAP EhP1 forNetWeaver 7.3 (\"7.31\")</li>\n<li>Systems where the original kernel 700/701/710/711 was already upgraded to the 720 (EXT) version. In these systems you can upgrade the kernel from 720 (EXT) to 721 (EXT) version.</li>\n</ul>\n</ul>\n<p><br/><strong>What are the benefits of the kernel 721 (EXT)?</strong><br/>The SAP kernel 721 (EXT) was initially introduced as an Innovation Kernel for NetWeaver releases 7.00-7.31 that offered several enhancements as compared to the SAP kernel 720. Please refer to the note 1728283 for more details on these enhancements. <br/>SAP kernel 721 replaced the SAP Kernel 720 as the standard kernel for NW 7.00-7.31 based SAP systems by end of Q1 2015. A new Innovation Kernel is currently Kernel 722.</p>\n<p><strong>Should the standard or the EXT kernel be installed?</strong><br/>If you use a DB/OS platform combination in your system environment where both the DB release and the OS release are supported by the kernel 721_EXT, we recommend to use the 721_EXT kernel instead of the 721 standard kernel.<br/><br/>Although it may be technically possible to go to the 721 standard (non-EXT) kernel from 720 EXT kernel during the system upgrate/update for certain NetWeaver releases (&lt;7.31) and platform/DB combinations, it is not recommended by SAP. If you already have a 720 EXT kernel, the 721 EXT is the recommended successor.<br/><br/>It is very important not to mix executables from kernel 721 and 721_EXT. It is explicitly mentioned in the text if the handling of the 721_EXT kernel differs from the handling of the 721 standard kernel.<br/><br/><strong>Platform-specific requirements</strong><br/>The SAP kernel 721 (EXT) can be in most cases used as DCK to all 7.* kernels without additional platform requirements. The same platform- or DB-specific requirements as for the 720 (EXT) kernel also apply for the 721 (EXT) kernel. So please consider the following limitations:</p>\n<p><strong>For Kernel 721:</strong></p>\n<ul>\n<li>IBM AIX</li>\n</ul>\n<p>Please see SAP Note 1780629 for the minimal OS release and C/C++ runtime requirements</p>\n<ul>\n<li>IBM DB2 LUW</li>\n</ul>\n<p>IBM DB2/UDB 8 is not supported with the SAP kernel 721.</p>\n<ul>\n<li>IBM z/OS</li>\n</ul>\n<p>IBM z/OS 1.6, 1.7 and 1.8 are not supported with the SAP kernel 721 (EXT).</p>\n<ul>\n<li>IBM i</li>\n</ul>\n<p>Please see the Note 68440 for the minimal OS release.</p>\n<ul>\n<li>SAP MaxDB</li>\n</ul>\n<p>The SAP kernel 721 requires at least version 7.7.07.41 of the MaxDB Client software (SQLBC). For more details refer to SAP Notes 822239 and 1487269.</p>\n<ul>\n<li>Microsoft SQL Server</li>\n</ul>\n<p>The SAP kernel 721 is intended for Microsoft SQL Server 2005 and higher. If you are still using Microsoft SQL Server 2000 refer to SAP Note 1341097.</p>\n<p>For additional information about the usage of the SAP kernel 721 as DCK with Microsoft SQL Server refer to SAP Note 1467086.</p>\n<ul>\n<li>Oracle Database</li>\n</ul>\n<p>Oracle 10.1 is not supported with the SAP kernel 721. You have to use at least Oracle 10.2.</p>\n<ul>\n<ul>\n<li>Oracle Database Client</li>\n</ul>\n</ul>\n<p>If your system is running with Oracle 10.2, you may use the existing 10.2. client. Then make sure that the latest 10.2.0.5 version is installed, see SAP note 1110995 for details. However, SAP recommends to install the 11g client that also may be used with Oracle 10.2</p>\n<p><strong>For Kernel 721_EXT only:</strong></p>\n<ul>\n<li>AIX</li>\n</ul>\n<p>Please see SAP Note 1780629 for the minimal OS release and C/C++ runtime requirements.</p>\n<ul>\n<li>Linux</li>\n</ul>\n<p>For details on Linux requirements refer to SAP Note 1563102.</p>\n<ul>\n<li>Microsoft Windows</li>\n</ul>\n<p>The SAP kernel 721_EXT requires a specific Microsoft Windows C Runtime environment. For details refer to SAP Note 1553465.</p>\n<ul>\n<li>IBM i</li>\n</ul>\n<p>Please see the Note 68440 for the minimal OS release.</p>\n<ul>\n<li>Oracle Database</li>\n</ul>\n<p>On all operating system platforms, except IBM Power Linux, Windows IA64 and Linux IA64 (Intel Itanium), only Oracle 11g is supported. On IBM Power Linux, Windows IA64 and on Linux IA64 (Intel Itanium), only Oracle 10g is supported.</p>\n<ul>\n<ul>\n<li>Oracle dababase server</li>\n</ul>\n</ul>\n<p>The Oracle database server has to be Oracle 11.2.0.2 or higher. The latest SAP Bundle Patch needs to be applied on top of the existing patch set release.</p>\n<ul>\n<ul>\n<li>Oracle database client</li>\n</ul>\n</ul>\n<p>Install the Oracle 11g Instant Client on all application servers and thedatabase server as described in SAP Note 1431794. The Oracle client software can be downloaded from the SAP Service Marketplace at: https://service.sap.com/oracle-download<br/>Please note: After the installation of the Oracle 11g Instant Client, make sure that the library path for the &lt;sid&gt;adm user (LIBPATH, SHLIB_PATH, LD_LIBRARY_PATH) no longer contains any references to the old Oracle 10g Instant Client location.<br/>On IBM Power Linux, Windows IA64 and on Linux IA64 (Intel Itanium), only Oracle 10g is supported. For these three platforms you do not have to make any changes to your database client.</p>\n<p><strong>Attention:</strong></p>\n<ol>\n<li>The SAP kernels as of 720 version check complex DDIC structures in more detail than the previous kernels. Short dumps of the type DDIC_TYPELENG_INCONSISTENT may occur, for example, when calling transaction SM66. For more detailed information about correcting these inconsistencies, see Note 1610716. The actions described in this note can be performed anytime, but we recommend to implement it before the kernel switch.</li>\n<li>If you want to use DCK for CE Developer Workplace systems, then check note 1709911.</li>\n<li>Release 7.00 or 7.01: Follow SAP note 1119735 regarding the relocation of the contents of the CCMS agents' working directories. This is described under \"Preface\" in section \"Solution\".</li>\n<li>SAP NW ABAP release 7.11: to avoid syntax errors mentioned in the note 1330869 the support package SAPKB71103 or higher must be installed before the kernel exchange.</li>\n<li>SAP NW ABAP releases 7.00 - 7.30: check note 1882691 to prevent errors when displaying job logs.</li>\n<li>SAP NW ABAP releases 7.00 - 7.31: note 1721936 should be implemented to avoid long running locks on the database during the first system start after kernel upgrade.</li>\n<li>The replication between a standalone enqueue server and replication servers will switch to synchronous mode by default starting with the PL listed in Note <a href=\"/notes/2036171\" target=\"_blank\">2036171</a>. Check this note if the asynchronous replication is needed for performance reasons.</li>\n<li>The following points apply only to <strong>Dual-Stack </strong>or <strong>Java </strong>systems:</li>\n<ul>\n<li>Release 7.00 or 7.01: There was an incompatibility regarding jmon (SAP note 1652660). This has been eliminated with the following versions of the SAP Tech S Offline component:</li>\n</ul>\n</ol>\n<p>Release 7.00: SAP TECH S 7.00 OFFLINE, SP 14, PL 24, included into the support-package stack 14 (SPS14) or higher.</p>\n<p>Release 7.01: SAP TECH S OFFLINE 7.01, SP 3, PL 13, ncluded into the support-package stack 5 (SPS05) or higher.</p>\n<p>This implies that at least support-package stack 14 or 5, respectively, must be installed in your system. Note 1652660 also lists the required patch levels for the higher support packages.</p>\n<ul>\n<ul>\n<li>Release 7.00 or 7.01: JSPM must have at least SP 24 or 9, respectively.</li>\n<li>Release 7.10 or higher: When using the SAPJVM, you have to install the latest patch collection according note 1836705.</li>\n</ul>\n</ul>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Install the 721 (EXT) downward compatible kernel in your SAP system with NW 7.00-7.31 by using one of the below options.</p>\n<p><strong>1. New installation or system sopy</strong><br/><br/>With <strong>SWPM 1.0 SP05</strong> or higher it is possible to install a new SAP system or to perform a system copy of an SAP system with kernel 721 (EXT). See the note 1680045 for more details on SWPM 1.0.<br/><br/><strong>2. Manual exchange of the kernel 721 (EXT) in existing SAP systems</strong><br/>SAP note 1713986 describes in detail how to install the kernel 721 (EXT) manually.<br/><br/><strong>3. Upgrade, EhP Update or SP Update</strong><br/>The following rules also will apply if a product based on the mentioned NetWeaver releases is upgraded/updated.<br/>Before starting the installation of the kernel 721 (EXT) on your SAP system, make sure that the database management and the operating systems fulfill the minimum requirements, please refer to the section \"Reason and Prerequisites\" above.<br/><br/><strong>3.1.  Upgrade <strong>to a 7.nn NW Release </strong>from a lower </strong><strong>Release </strong><br/>This section applies if a product based on the software component version SAP_BASIS 4.6C, 6.20 or 6.40 is to be upgraded to a release based on SAP_BASIS 7.00, 7.01, 7.02, 7.03, 7.10, 7.11, 7.20, 7.30 or 7.31.</p>\n<ul>\n<li>The Kernel must be manually exchanged <strong>after</strong> the upgrade.</li>\n</ul>\n<p><br/><strong>3.2. Upgrade or Update within 7.nn Releases</strong><br/>This section comprises the following cases:</p>\n<ul>\n<li>Update or upgrade of NetWeaver 7.00 to 7.01, 7.02 or 7.03.</li>\n</ul>\n<ul>\n<li>Upgrade of NetWeaver 7.00 to 7.10 or 7.11.</li>\n</ul>\n<ul>\n<li>Upgrade of NetWeaver 7.01 to 7.11.</li>\n</ul>\n<ul>\n<li>Update of NetWeaver or CE 7.10 to 7.11.</li>\n</ul>\n<ul>\n<li>Update of NetWeaver CE 7.1/7.11 to CE 7.2</li>\n</ul>\n<ul>\n<li>Upgrade of NetWeaver 7.00/7.01/7.02/7.10/7.11/7.20 or CE 7.1/7.11/7.2 to NetWeaver 7.30</li>\n</ul>\n<ul>\n<li>Upgrade of NetWeaver 7.00/7.01/7.02/7.10/7.11 or CE 7.1/7.11/7.2 to NetWeaver 7.31</li>\n</ul>\n<ul>\n<li>Upgrade or update of NetWeaver 7.30 to 7.31</li>\n</ul>\n<ul>\n<li>Support Package update of NetWeaver 7.00, 7.01, 7.02, 7.03, 7.10, 7.11, 7.20, 7.30 or 7.31 as well as CE 7.1, 7.11 or 7.2.</li>\n</ul>\n<p><br/>Here please follow the below rules:<br/><br/><strong>3.2.1.</strong> If upgrading to 721 (EXT) from a kernel release &lt;720, the kernel must be manually exchanged <strong>before</strong> the upgrade, update or SP update;<br/><br/><strong>3.2.2.</strong> If upgrading to 721 (EXT) from a kernel release &gt;=720 (including 720 EXT and 721) it will be possible to switch to the 721 (EXT) kernel during the upgrade/update/SP-update under the following conditions:</p>\n<ul>\n<li>for ABAP systems it is possible to switch from 72x to 721 (EXT) kernel if using <strong>SUM 1.0</strong> from <strong>SP05</strong> on;</li>\n</ul>\n<ul>\n<li>For Java systems a switch from 72x  to 721 (EXT) kernel is fully supported from <strong>SUM 1.0</strong> <strong>SP06</strong> on. See the note 1707161 for more detailed information on SUM 1.0.</li>\n</ul>\n<p><br/><strong>3.2.3</strong>. In case of an upgrade from 7.00/7.01 to 7.10/7.11, you have to use a modified kernel DVD. The SAP note 1726899 explains how to create it.<br/><br/><strong>3.2.4.</strong> The kernel upgrade to the 721 (EXT) version has to be performed only once. If the kernel 721 (EXT) has already been installed, no additional actions are necessary, but of course, you have to select a 721 (EXT) kernel during the stack download.<br/><br/><strong>3.2.5</strong>. In case of an upgrade from the NetWeaver start releases 7.00/7.01 with the 720/721 DCK installed on Java or Dual-Stack system:</p>\n<ul>\n<li>set the profile parameter \"ccms/enable_agent = 0\" in the start and instance profiles before starting the upgrade. After the upgrade has been completed, you can reconfigure CCMS agents as appropriate.</li>\n</ul>\n<p><br/><strong>4. Installation of an additional application server</strong><br/>If you want to install additional application servers after the DCK has been applied to your system, check SAP note 1701575.</p>", "noteVersion": 20, "refer_note": [{"note": "171356", "noteTitle": "171356 - SAP Software on Linux: General information", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You want to install SAP software on Linux.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Redhat, redhat, RH, SUSE, SuSE, Suse, suse, Swapspace, swapspace, swap-space, ServicePack, IA-64, IA64, x86_64, AMD, Opteron, EM64T, POWER, zSeries, System z, Oracle Linux, OL</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>You want to install SAP software on Linux.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Due to technical restrictions, this SAP Note had to be closed.<br/>Refer to SAP Note <a href=\"/notes/2369910\" target=\"_blank\">2369910</a> instead.</p>\n<p>Due to technical restrictions, this SAP Note had to be closed.<br/>Refer to SAP Note <a href=\"/notes/2369910\" target=\"_blank\">2369910</a> instead.</p></div>", "noteVersion": 332}, {"note": "1776739", "noteTitle": "1776739 - Release strategy for SAP Screen Personas 1.0/ 2.0", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>This SAP Note contains information about planning the installation and upgrades of the ABAP add-on PERSOS.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Release strategy, PERSOS, PERSOS 100, PERSOS 200</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<ol>\n<li>General information</li>\n<li>Overview</li>\n<ol>\n<li>SAP Product Availability Matrix (PAM)</li>\n<li>Download</li>\n<li>Modifications</li>\n<li>Unicode required</li>\n<li>Patchlevels</li>\n<li>Deinstallation</li>\n</ol>\n<li>PERSOS 100</li>\n<ol>\n<li>Installation</li>\n</ol>\n<li>PERSOS 200</li>\n<ol>\n<li>Installation</li>\n<li>Delta Upgrade</li>\n</ol></ol>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong>1. General information<br/><br/>PERSOS 100/200 has gone out of maintenance at Dec 31 2018.</strong></p>\n<ul>\n<li>See the general information from SAP Note 70228.</li>\n<li>You cannot uninstall ABAP add-ons.</li>\n<li>You can install ABAP add-ons only on certain SAP releases.</li>\n<li>Upgrades with ABAP add-ons</li>\n</ul>\n<p>In systems in which an ABAP add-on is installed, you can upgrade only to SAP releases that are supported for this add-on.</p>\n<p>Note that there is a delay between the delivery of the SAP standard releases and the release of the corresponding add-on releases.</p>\n<p>If an add-on upgrade is connected with a change of the SAP release, it is integrated into the SAP upgrade (repository or system switch). To carry out this upgrade, you require an additional add-on upgrade CD or DVD in addition to the SAP standard upgrade CDs or DVDs. If the SAP standard upgrade is carried out without this additional CD or DVD, the add-on will no longer work after the upgrade. The entire SAP system is inconsistent. For more information about this problem, see SAP Note 33040.</p>\n<p>If you are scheduling an upgrade or the import of an enhancement package, note that you should not import the latest Support Package Stack in the source release directly before the upgrade to ensure that the required, equivalent Support Package is already available in the target release. For more information about this, see SAP Note 832594 and the following information on SAP Service Marketplace. <a href=\"http://service.sap.com/sp-stacks\" target=\"_blank\">http://service.sap.com/sp-stacks</a> -&gt; SP Stack Information -&gt; SP Stack Strategy</p>\n<p><strong>2. Overview</strong><br/>a) SAP Product Availability Matrix<br/>The following information about the add-on in the SAP Product Availability Matrix is available on SAP Service Marketplace at <a href=\"http://service.sap.com/PAM\" target=\"_blank\">http://service.sap.com/PAM</a></p>\n<ul>\n<li>Availability</li>\n<li>End of maintenance</li>\n<li>Release for products with enhancement packages</li>\n<li>Language support</li>\n</ul>\n<p>b) Download<br/>The software is available on SAP Service Marketplace at <a href=\"http://service.sap.com/SWDC\" target=\"_blank\">http://service.sap.com/SWDC</a><br/>    -&gt; Search for Software Downloads \"SAP SCREEN PERSONAS \"</p>\n<p>If you do not use a maintenance transaction in SAP Solution Manager to download the files, be sure to download also the attribute change package PERSOS====100 (or PERSOS====200 for release 200) from Service Marketplace and put it into your EPS/in-directory!</p>\n<p>c) The add-on does not contain any modifications.<br/>You can also perform the installation if you have already imported more Support Packages into your system than are specified in the section \"Required Support Packages\".</p>\n<p>d) Unicode required<br/>Since 2006, SAP has been recommending that its customers running non-Unicode systems upgrade to Unicode. While some customers have deployed SAP Screen Personas onto non-Unicode systems, this is not a supported scenario.<br/><br/>To ensure optimal performance of SAP Screen Personas, we are only supporting Unicode systems. For Personas to function properly, both the system on which Personas is installed, as well as any target systems must be Unicode. If you use Personas in the “remote” scenario with a separate target system, you should limit this to testing and development purposes. We strongly recommend that customers install Personas on every system where they want to use personalized screens.</p>\n<p>e) Patchlevels<br/>You can find below the minimum support package level for the SAP_BASIS component for installation the product. While a higher level may not be required, it is still recommended.<br/>As SAP Screen Personas highly depends on functionality provided by the SAP kernel, SAP strongly recommends using the latest patch level from at least the SAP kernel.</p>\n<p>Details can be found for Personas Silverlight in note 1848339.</p>\n<p>f) Deinstallation<br/>PERSOS 100 and PERSOS 200 can be uninstalled from the system. For details please check note 2226123.</p>\n<p><strong>3. PERSOS 100</strong></p>\n<p><strong>a) Installation</strong></p>\n<ul>\n<li>Name of the Installation Package: SAPK-100AGINPERSOS</li>\n<li>Material number: 51044614</li>\n<li>Path on media: DATA_UNITS -&gt; PERSOS_100_INST -&gt; DATA</li>\n<li>Required Support Packages: <br/><br/>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"3\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td><strong>Component Release</strong></td>\n<td><strong>Support Packages</strong></td>\n</tr>\n<tr>\n<td>SAP_BASIS 700</td>\n<td>SAPKB70024</td>\n</tr>\n<tr>\n<td>or 701</td>\n<td>SAPKB70109</td>\n</tr>\n<tr>\n<td>or 702</td>\n<td>SAPKB70208</td>\n</tr>\n<tr>\n<td>or 731</td>\n<td>SAPKB73101</td>\n</tr>\n<tr>\n<td>or 740 </td>\n<td>none</td>\n</tr>\n</tbody>\n</table></div>\n<br/>Please check note 1885312 for further information regarding Personas Silverlight component.</li>\n</ul>\n<p><strong> </strong></p>\n<p><strong>SAPK-10001INPERSOS</strong><br/>If you have already created groups in your system you have to follow note 1842360 to avoid data inconsistencies!</p>\n<p><strong>4. PERSOS 200<br/></strong></p>\n<ul>\n<li>If you have not yet installed SAPKB70026 (or equivalent) package, you have to provide the attribute change package PERSOS====200 also in your EPS inbox. This file can also be downloaded from Service Marketplace.</li>\n<li>Required notes</li>\n<ul>\n<li><a href=\"/notes/1354957\" target=\"_blank\">1354957</a></li>\n<li><a href=\"/notes/1487337\" target=\"_blank\">1487337</a></li>\n<li><a href=\"/notes/1582870\" target=\"_blank\">1582870</a></li>\n<li><a href=\"/notes/1848339\" target=\"_blank\">1848339</a></li>\n</ul>\n</ul>\n<p><br/><strong>a) Installation</strong></p>\n<ul>\n<li>Name of the Installation Package: SAPK-200AGINPERSOS</li>\n<li>Material number: 51046685</li>\n<li>Path on media:  DATA_UNITS -&gt; PERSOS_200_INST -&gt; DATA</li>\n<li>Required Support Packages:<br/><br/>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"3\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td><strong>Component Release</strong></td>\n<td><strong>Support Packages</strong></td>\n</tr>\n<tr>\n<td>SAP_BASIS 700</td>\n<td>SAPKB70024</td>\n</tr>\n<tr>\n<td>or 701</td>\n<td>SAPKB70109</td>\n</tr>\n<tr>\n<td>or 702</td>\n<td>SAPKB70208</td>\n</tr>\n<tr>\n<td>or 731</td>\n<td>SAPKB73101</td>\n</tr>\n<tr>\n<td>or 740 till 754</td>\n<td>none</td>\n</tr>\n</tbody>\n</table></div>\n</li>\n</ul>\n<p> </p>\n<p><strong>b) Delta Upgrade</strong><br/>If you have already created groups in your system you have to follow note 1842360 before the upgrade to avoid data inconsistencies!</p>\n<ul>\n<li>Name of the delta upgrade package: SAPK-200AGINPERSOS</li>\n<li>Material number: 51046685</li>\n<li>Path on media: DATA_UNITS -&gt; PERSOS_200_INST</li>\n<li>Required Support Packages<br/><br/>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"3\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td><strong>Component Release</strong></td>\n<td><strong>Support Packages</strong></td>\n</tr>\n<tr>\n<td>PERSOS 100</td>\n<td>none</td>\n</tr>\n<tr>\n<td>SAP_BASIS 700</td>\n<td>SAPKB70024</td>\n</tr>\n<tr>\n<td>or 701</td>\n<td>SAPKB70109</td>\n</tr>\n<tr>\n<td>or 702</td>\n<td>SAPKB70208</td>\n</tr>\n<tr>\n<td>or 731</td>\n<td>SAPKB73101</td>\n</tr>\n<tr>\n<td>or 740 till 754</td>\n<td>none</td>\n</tr>\n</tbody>\n</table></div>\n<br/><strong>Note</strong>: <br/>PERSOS 200 is <strong>not released</strong> for any release higher than SAP S/4HANA 1909. In this case you have to migrate your data to SAP Screen Personas 3.0 and uninstall PERSOS 200 <strong>before</strong> the conversion.</li>\n</ul>\n<p> </p>\n<p><strong> </strong></p>", "noteVersion": 38}, {"note": "1776186", "noteTitle": "1776186 - HANA - Scale out: routing to right indexserver", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>******************<br/>This note is only for performance imporvement of BW on HANA which runs in the scale-out environment.<br/>******************</p>\n<p><br/>in most cases, stored procedure based queries of BW are sent to an indexserver where the actual query objects (table, view, etc) do not exist. And the indexserver which received the request must forward it to a right indexserver. This situation leads to a performance problem.</p>\n<p><span>!!! Important information for customers of ABAP Basis Release 731  !!!</span></p>\n<p>If the ABAP system runs in the following conditions:</p>\n<ul>\n<ul>\n<li>ABAP Basis Release 731 and</li>\n<li>ABAP Basis Support Package 6 and</li>\n<li>ABAP Kernel Version &lt; 721</li>\n</ul>\n</ul>\n<p>because changes of this note is only partially available in the ABAP system, BW queries will break ABAP sessions. To avoid this severe situation, apply all the remaining ABAP changes of this note immediately which are available as source code corrections. Just to avoid this situation, applying only ABAP changes is sufficient, and it is not necessary to upgrade the ABAP kernel to 721 or higher. But the kernel Update is required later to get the performance improvement.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>trexviadbsl, trexviadbslwithparameter, TREX_EXT_AGGREGATE, TREX_DBS_AGGREGATE, scaleout</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>The current routing mechanism for the stored procedure based queries is Round-Robin. This does not consider the locations of query objects.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Apply all of the following fixes:</p>\n<ol>1. ABAP support package or source code correction</ol><ol>2. SAP HANA SPS 5 (= Revision 45) or later</ol><ol>3. ABAP kernel version must be 721 or later. Refer to the attached note 1716826.</ol>\n<p><br/>After applying the patch, authorization of required DB users/schemas (normally the user name is SAP&lt;SID of BW system&gt;) needs to be edited on HANA Studio. Add the following SQL object on the tab \"SQL Privileges\":<br/>           <strong>TREXVIADBSLWITHPARAMETER (SYS)</strong><br/>and select \"EXECUTE\" with \"No\" for grantable to others<br/><br/><strong><span>Additional information</span></strong></p>\n<ul>\n<li>Only in case that the above three corrections (ABAP + HANA + ABAP Kernel)  are applied, this feature will be activated. And the order of applying the corrections does not matter.</li>\n</ul>\n<ul>\n<li>If some of the corrections are missing, this feature will not be used at all. The system runs just as before.</li>\n</ul>\n<ul>\n<li>It is harmless to apply the corrections to a single node system. This feature will just not be used in the system.</li>\n</ul>\n<ul>\n<li>It is recommended applying the related note 1798043 as well. This feature could run into the problem described in the note, if very many queries are executed in parallel.</li>\n</ul></div>", "noteVersion": 12}, {"note": "1694697", "noteTitle": "1694697 - SAP Business Application Accelerator powered by HANA", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are looking for information about the SAP Business Application Accelerator powered by HANA.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Suite Accelerator, Scenarios, Context, RDA_MAINTAIN</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>The SAP Business Application Accelerator powered by HANA has been offered as ABAP Add-on in a restricted shipment mode until end of 2016.</p>\n<p>Starting with 2017 the Add-on is no longer available for new customers.</p>\n<p>The Add-on is in general not supported in S/4 HANA. The existence of the Add-on is blocking technical conversions to S/4HANA 1511 and 1610. Technical conversions to 1709 and beyond are possible.</p>\n<p>Support for existing customers on non-4/HANA products is continued under the same conditions as before.</p>\n<p>To benefit from SAP HANA it is recommended to migrate the entire system to SAP HANA, possibly with a multi node setup for optimized load distribution.</p>\n<p>For a description of the functionality and usage of the SAP Business Application Accelerator powered by HANA please read the attached Customer Guide.</p>\n<p>Contents:</p>\n<p>1 Introduction <br/>2 Concept <br/>2.1 Redirected Operations <br/>2.1.1 Statements <br/>2.1.2 Source <br/>2.2 Context Definition <br/>2.3 Context Evaluation at Runtime <br/>3 Using the SAP Business Application Accelerator <br/>3.1 Installation <br/>3.2 General Activation <br/>3.3 Loading Scenarios <br/>3.3.1 Predefined SAP Scenarios <br/>3.3.2 Custom Scenarios <br/>3.4 Trouble Shooting</p></div>", "noteVersion": 16}, {"note": "1789518", "noteTitle": "1789518 - Recording the logon method", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>In the security audit, you cannot tell from AS ABAP which logon method was used for successful and unsuccessful logons.<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>SAL SM19 SM20 AU<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>This information was not previously recorded.<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>Apply the kernel patch (see SAP Note 1716826).<br/>Also import the assigned ABAP Support Package or, alternatively, implement the manual correction instructions.<br/>See SAP Note 320991 for more information about the logon methods.</p></div>", "noteVersion": 6}, {"note": "68440", "noteTitle": "68440 - IBM i: How do I upgrade to a later OS release?", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You plan to upgrade to a higher SAP release and/or an IBM i release.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Release i5/OS OS/400 V5R1 V5R1M0 V5R2 V5R2M0 V5R3 V5R3M0 V5R3M5 V5R4 V5R4M0 V5R4M5 V6R1M0 V6R1 V7R1 V7R2 V7R3 V7R4 V7R5 release upgrade iSeries 'IBM i' AS/400</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>\"Release\" terminology:</p>\n<ul>\n<li>Operating system release IBM i (previously i5/OS or OS/400):</li>\n<ul>\n<li>The following releases are currently supported: IBM i 7.3, IBM i 7.4, and IBM i 7.5</li>\n<li>The following releases were previously supported: V3R6M0, V3R7M0, V4R1M0 V4R2M0, V4R3M0, V4R4M0, V4R5M0, V5R1M0, V5R2M0, V5R3M0, V5R3M5, V5R4M0, V5R4M5, IBM i 6.1, IBM i 6.1.1, IBM i 7.1, and IBM i 7.2.<br/><strong>Customer systems based on operating system releases prior to IBM i 7.3</strong> might also be supported by SAP. However, it might not be possible to deliver suitable patches for such releases.</li>\n</ul>\n<li>Database Release DB2 for IBM i (previously: DB2 for i5/OS or DB2/400). This is identical to the operating system release.</li>\n<li>SAP database release.</li>\n<ul>\n<li>The following releases are currently supported: 7.00, 7.01, 7.02, 7.31, 7.40, 7.50, 7.51, and 7.52.</li>\n<li>The following releases were previously supported: 3.1I, 4.0B, 4.5B, 4.6B, 4.6C, 6.40, 7.10, 7.11, 7.20, and 7.30.</li>\n</ul>\n<li>SAP kernel release:</li>\n<ul>\n<li>The following releases are currently supported: 7.22, 7.53, and 7.54.</li>\n<li>The following releases were previously supported: 3.1I, 4.0B, 4.5B, 4.6D, 6.20, 6.40, 7.00, 7.01, 7.10, 7.11, 7.20, 7.21, 7.40, 7.41, 7.42, 7.45, and 7.49.</li>\n</ul>\n</ul>\n<p><span>The SAP kernel is often downward-compatible, that is, a 7.22 kernel can be used with an older SAP database release. However, a kernel of Release 7.40 or 7.5x cannot be used with a 7.0x or 7.31 database.</span></p>\n<ul>\n<li>This SAP Note is cumulative with regard to the listed releases, particularly in the case of the table in the solution part. If a release is listed as valid, this means only that it was known by this name and supported by SAP at a point in time. For definitive information about current support, see the Product Availability Matrix (<a href=\"https://support.sap.com/pam\" target=\"_blank\">https://support.sap.com/pam</a>).</li>\n</ul>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Upgrade the IBM i release to a target release:</p>\n<p><strong>General procedure</strong><br/>In the Product Availability Matrix in the SAP Support Portal (<a href=\"https://support.sap.com/pam\" target=\"_blank\">https://support.sap.com/pam</a>), search for a kernel that can be used with your current SAP (database) release.<br/><br/>If there is a suitable kernel, you can import this kernel (after you have upgraded IBM i to the target release) and thus operate your SAP system as normal. You may not even have to import a new kernel if the kernel you are currently using meets the criteria. We recommend, however, that you always use the latest available patch level of the kernel.<br/><br/>If there is no suitable kernel, this combination is generally not or not yet supported. For new IBM i releases, delays can occur between the IBM i release becoming generally available and the release of SAP products. For the release, a special \"Release Patch\" is made available for kernels that have already been delivered.</p>\n<p><strong>Required patch levels for IBM i releases<br/></strong>To be able to use the relevant SAP release in a given IBM i release, a certain minimum patch level is required. The table below contains an overview of these patch levels.</p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"3\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td> </td>\n<td><strong>V4R4</strong></td>\n<td><strong>V4R5</strong></td>\n<td><strong>V5R1</strong></td>\n<td><strong>V5R2</strong></td>\n<td><strong>V5R3</strong></td>\n<td><strong>V5R4</strong></td>\n<td><strong>V6R1</strong></td>\n<td><strong>V7R1</strong></td>\n<td><strong>V7R2 </strong></td>\n<td><strong>V7R3 </strong></td>\n<td><strong>V7R4</strong></td>\n<td><strong>V7R5</strong></td>\n</tr>\n<tr>\n<td><strong>3.1I</strong></td>\n<td>351</td>\n<td>524.</td>\n<td>--</td>\n<td>--</td>\n<td>--</td>\n<td>--</td>\n<td>--</td>\n<td>--</td>\n<td>--</td>\n<td>--</td>\n<td>--</td>\n<td>--</td>\n</tr>\n<tr>\n<td><strong>COM</strong></td>\n<td>351</td>\n<td>524.</td>\n<td>604.</td>\n<td>684.</td>\n<td>--</td>\n<td>--</td>\n<td>--</td>\n<td>--</td>\n<td>--</td>\n<td>--</td>\n<td>--</td>\n<td>--</td>\n</tr>\n<tr>\n<td><strong>EXT</strong></td>\n<td>--</td>\n<td>--</td>\n<td>604</td>\n<td>684.</td>\n<td>742.</td>\n<td>768.</td>\n<td>--</td>\n<td>--</td>\n<td>--</td>\n<td>--</td>\n<td>--</td>\n<td>--</td>\n</tr>\n<tr>\n<td><strong>4.0B</strong></td>\n<td>478</td>\n<td>712.</td>\n<td>--</td>\n<td>--</td>\n<td>--</td>\n<td>--</td>\n<td>--</td>\n<td>--</td>\n<td>--</td>\n<td>--</td>\n<td>--</td>\n<td>--</td>\n</tr>\n<tr>\n<td><strong>COM</strong></td>\n<td>478</td>\n<td>712.</td>\n<td>840.</td>\n<td>953.</td>\n<td>--</td>\n<td>--</td>\n<td>--</td>\n<td>--</td>\n<td>--</td>\n<td>--</td>\n<td>--</td>\n<td>--</td>\n</tr>\n<tr>\n<td><strong>EXT</strong></td>\n<td>--</td>\n<td>--</td>\n<td>840</td>\n<td>953.</td>\n<td>1017.</td>\n<td>1056.</td>\n<td>--</td>\n<td>--</td>\n<td>--</td>\n<td>--</td>\n<td>--</td>\n<td>--</td>\n</tr>\n<tr>\n<td><strong>4.5B</strong></td>\n<td>164</td>\n<td>483.</td>\n<td>668.</td>\n<td>840.</td>\n<td>--</td>\n<td>--</td>\n<td>--</td>\n<td>--</td>\n<td>--</td>\n<td>--</td>\n<td>--</td>\n<td>--</td>\n</tr>\n<tr>\n<td><strong>EXT</strong></td>\n<td>--</td>\n<td>--</td>\n<td>668</td>\n<td>840.</td>\n<td>925.</td>\n<td>986.</td>\n<td>--</td>\n<td>--</td>\n<td>--</td>\n<td>--</td>\n<td>--</td>\n<td>--</td>\n</tr>\n<tr>\n<td><strong>4.6D</strong></td>\n<td>0</td>\n<td>0.</td>\n<td>628.</td>\n<td>1278.</td>\n<td>--</td>\n<td>--</td>\n<td>--</td>\n<td>--</td>\n<td>--</td>\n<td>--</td>\n<td>--</td>\n<td>--</td>\n</tr>\n<tr>\n<td><strong>EXT</strong></td>\n<td>--</td>\n<td>--</td>\n<td>628</td>\n<td>1278.</td>\n<td>1850.</td>\n<td>2200.</td>\n<td>2381.</td>\n<td>2582.</td>\n<td>--</td>\n<td>--</td>\n<td>--</td>\n<td>--</td>\n</tr>\n<tr>\n<td><strong>EX2</strong></td>\n<td>--</td>\n<td>--</td>\n<td>--</td>\n<td>--</td>\n<td>--</td>\n<td>2200</td>\n<td>2381.</td>\n<td>2582.</td>\n<td>--</td>\n<td>--</td>\n<td>--</td>\n<td>--</td>\n</tr>\n<tr>\n<td><strong>6.20</strong></td>\n<td>--</td>\n<td>--</td>\n<td>0</td>\n<td>346.</td>\n<td>1505.</td>\n<td>--</td>\n<td>--</td>\n<td>--</td>\n<td>--</td>\n<td>--</td>\n<td>--</td>\n<td>--</td>\n</tr>\n<tr>\n<td><strong>6.40</strong></td>\n<td>--</td>\n<td>--</td>\n<td>--</td>\n<td>0</td>\n<td>19.</td>\n<td>117.</td>\n<td>224.</td>\n<td>329.</td>\n<td>--</td>\n<td>--</td>\n<td>--</td>\n<td>--</td>\n</tr>\n<tr>\n<td><strong>EX2</strong></td>\n<td>--</td>\n<td>--</td>\n<td>--</td>\n<td>--</td>\n<td>19</td>\n<td>117.</td>\n<td>224.</td>\n<td>329.</td>\n<td>--</td>\n<td>--</td>\n<td>--</td>\n<td>--</td>\n</tr>\n<tr>\n<td><strong>7.00</strong></td>\n<td>--</td>\n<td>--</td>\n<td>--</td>\n<td>--</td>\n<td>0</td>\n<td>47.</td>\n<td>150.</td>\n<td>256.</td>\n<td>--</td>\n<td>--</td>\n<td>--</td>\n<td>--</td>\n</tr>\n<tr>\n<td><strong>7.01</strong></td>\n<td>--</td>\n<td>--</td>\n<td>--</td>\n<td>--</td>\n<td>--</td>\n<td>0</td>\n<td>0.</td>\n<td>93.</td>\n<td>--</td>\n<td>--</td>\n<td>--</td>\n<td>--</td>\n</tr>\n<tr>\n<td><strong>7.10</strong></td>\n<td>--</td>\n<td>--</td>\n<td>--</td>\n<td>--</td>\n<td>--</td>\n<td>0</td>\n<td>94.</td>\n<td>200.</td>\n<td>--</td>\n<td>--</td>\n<td>--</td>\n<td>--</td>\n</tr>\n<tr>\n<td><strong>7.11</strong></td>\n<td>--</td>\n<td>--</td>\n<td>--</td>\n<td>--</td>\n<td>--</td>\n<td>0</td>\n<td>0.</td>\n<td>86.</td>\n<td>--</td>\n<td>--</td>\n<td>--</td>\n<td>--</td>\n</tr>\n<tr>\n<td><strong>7.20</strong></td>\n<td>--</td>\n<td>--</td>\n<td>--</td>\n<td>--</td>\n<td>0</td>\n<td>0.</td>\n<td>0.</td>\n<td>49.</td>\n<td>--</td>\n<td>--</td>\n<td>--</td>\n<td>--</td>\n</tr>\n<tr>\n<td><strong>EXT</strong></td>\n<td>--</td>\n<td>--</td>\n<td>--</td>\n<td>--</td>\n<td>--</td>\n<td>--</td>\n<td>--</td>\n<td>49</td>\n<td>--</td>\n<td>--</td>\n<td>--</td>\n<td>--</td>\n</tr>\n<tr>\n<td><strong>7.21</strong></td>\n<td>--</td>\n<td>--</td>\n<td>--</td>\n<td>--</td>\n<td>0</td>\n<td>0.</td>\n<td>0.</td>\n<td>0.</td>\n<td>--</td>\n<td>--</td>\n<td>--</td>\n<td>--</td>\n</tr>\n<tr>\n<td><strong>EXT</strong></td>\n<td>--</td>\n<td>--</td>\n<td>--</td>\n<td>--</td>\n<td>--</td>\n<td>--</td>\n<td>--</td>\n<td>0</td>\n<td>300.</td>\n<td>626</td>\n<td>1218.</td>\n<td>--</td>\n</tr>\n<tr>\n<td><strong>7.22</strong></td>\n<td>--</td>\n<td>--</td>\n<td>--</td>\n<td>--</td>\n<td>--</td>\n<td>0</td>\n<td>0.</td>\n<td>0.</td>\n<td>--</td>\n<td>--</td>\n<td>--</td>\n<td>--</td>\n</tr>\n<tr>\n<td><strong>EXT</strong></td>\n<td>--</td>\n<td>--</td>\n<td>--</td>\n<td>--</td>\n<td>--</td>\n<td>--</td>\n<td>--</td>\n<td>0</td>\n<td>0.</td>\n<td>101</td>\n<td>816</td>\n<td>1118</td>\n</tr>\n<tr>\n<td><strong>EX2</strong></td>\n<td>--</td>\n<td>--</td>\n<td>--</td>\n<td>--</td>\n<td>--</td>\n<td>--</td>\n<td>--</td>\n<td>--</td>\n<td>--</td>\n<td>--</td>\n<td>920</td>\n<td>1118</td>\n</tr>\n<tr>\n<td><strong>7.40</strong></td>\n<td>--</td>\n<td>--</td>\n<td>--</td>\n<td>--</td>\n<td>--</td>\n<td>--</td>\n<td>--</td>\n<td>0</td>\n<td>--</td>\n<td>--</td>\n<td>--</td>\n<td>--</td>\n</tr>\n<tr>\n<td><strong>7.41</strong></td>\n<td>--</td>\n<td>--</td>\n<td>--</td>\n<td>--</td>\n<td>--</td>\n<td>--</td>\n<td>--</td>\n<td>0</td>\n<td>\n<p>31.</p>\n</td>\n<td>\n<p> --</p>\n</td>\n<td>\n<p>--</p>\n</td>\n<td>\n<p>--</p>\n</td>\n</tr>\n<tr>\n<td><strong>7.42</strong></td>\n<td>--</td>\n<td>--</td>\n<td>--</td>\n<td>--</td>\n<td>--</td>\n<td>--</td>\n<td>--</td>\n<td>0</td>\n<td>\n<p>0.</p>\n</td>\n<td>\n<p> --</p>\n</td>\n<td>\n<p>--</p>\n</td>\n<td>\n<p>--</p>\n</td>\n</tr>\n<tr>\n<td><strong>7.45</strong></td>\n<td>--</td>\n<td>--</td>\n<td>--</td>\n<td>--</td>\n<td>--</td>\n<td>--</td>\n<td>--</td>\n<td>0</td>\n<td>\n<p>0.</p>\n</td>\n<td>\n<p> 100</p>\n</td>\n<td>\n<p>--</p>\n</td>\n<td>\n<p>--</p>\n</td>\n</tr>\n<tr>\n<td><strong>7.49</strong></td>\n<td>--</td>\n<td>--</td>\n<td>--</td>\n<td>--</td>\n<td>--</td>\n<td>--</td>\n<td>--</td>\n<td>100</td>\n<td>\n<p>0</p>\n</td>\n<td>\n<p> 0.</p>\n</td>\n<td>\n<p>--</p>\n</td>\n<td>\n<p>--</p>\n</td>\n</tr>\n<tr>\n<td><strong>7.53</strong></td>\n<td>--</td>\n<td>--</td>\n<td>--</td>\n<td>--</td>\n<td>--</td>\n<td>--</td>\n<td>--</td>\n<td>0</td>\n<td>\n<p>0</p>\n</td>\n<td>\n<p> 0.</p>\n</td>\n<td>\n<p>423.</p>\n</td>\n<td>\n<p>1000</p>\n</td>\n</tr>\n<tr>\n<td><strong>7.54</strong></td>\n<td>--</td>\n<td>--</td>\n<td>--</td>\n<td>--</td>\n<td>--</td>\n<td>--</td>\n<td>--</td>\n<td>--</td>\n<td>\n<p>--</p>\n</td>\n<td>\n<p>--</p>\n</td>\n<td>\n<p>--</p>\n</td>\n<td>\n<p>100</p>\n</td>\n</tr>\n</tbody>\n</table></div>\n<p>Thus, in order to use SAP NetWeaver 7.0 on IBM i 7.2, for example, you require a 7.21_EXT kernel with a patch level of a least 300 or a 7.22_EXT kernel.<br/><br/>Release IBM i 6.1 requires patches in addition to DW patches. SAP Note <a href=\"/notes/1148480\" target=\"_blank\">1148480</a> contains a complete list.<br/>For similar information for IBM i 7.1, see SAP Note <a href=\"/notes/1432783\" target=\"_blank\">1432783</a>.<br/>For similar information for IBM i 7.2, see SAP Note <a href=\"/notes/2011710\" target=\"_blank\">2011710</a>.<br/>For similar information for IBM i 7.3, see SAP Note <a href=\"/notes/2299407\" target=\"_blank\">2299407</a>.<br/>For similar information for IBM i 7.4, see SAP Note <a href=\"/notes/2786037\" target=\"_blank\">2786037</a>.<br/>For similar information for IBM i 7.5, see SAP Note <a href=\"/notes/3152440\" target=\"_blank\">3152440</a>.</p>\n<p><br/>Note that you may need to import a kernel from a CD to obtain a certain patch level.</p>\n<p><strong><strong>Mixed IBM i releases with 3-tier SAP systems<br/></strong></strong>Here, you must search in the Product Availability Matrix for a kernel that supports all IBM i releases that you operate at the same time.</p>\n<p><strong>SAP upgrade<br/></strong>For information about possible target releases for your current source release, see the current SAP release strategy (<a href=\"http://support.sap.com/releasestrategy\" target=\"_blank\">http://support.sap.com/releasestrategy</a>). If you plan an SAP upgrade at the same time, note the following before you start the SAP upgrade: The upgrade process of the IBM i release with the import of the appropriate kernel must be complete. The SAP upgrade may automatically carry out an additional kernel exchange.</p>", "noteVersion": 74}, {"note": "1721936", "noteTitle": "1721936 - GUI status inline generation (ABAP part)", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>If several processes of an instance try to generate a GUI status, only the first process will perform an update in the database. The other processes generate inline without a database update. For more information about this, refer to SAP Note 1707062.<br/><br/>After the correction is implemented, updates to the tables D347T and EUDB are avoided during an inline generation so that lock situations cannot occur for these two tables.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>D347T, EUDB</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Please import the specified support package or implement the attached correction instructions.</p></div>", "noteVersion": 4}, {"note": "1882691", "noteTitle": "1882691 - 7.21 kernel: Job log cannot be displayed", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You use the 7.21 kernel and a SAP_BASIS release in the area<br/>7.00 - 7.30.<br/>You make sure that the job log of some jobs is no longer displayed. An error message appears that says that the job log was not found or does not exist.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Joblog, job log, BT166, TRIV, BT 166</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>With SAP Note 1468596 it became possible to automatically delete 'trivial' job logs immediately after the end of the job. For further information, refer to SAP Note 1468596.<br/>Since there are dependencies between the ABAP layer and the kernel layer, the new functions must originally be explicitly activated with a profile parameter.<br/>As of Kernel 7.21, Patch 13, this new function is included as standard.<br/>The problem described above may occur if you have Kernel 7.21, Patch &gt;= 13<br/>AND<br/>a SAP_BASIS release/patch level lower than:<br/><br/>700          SAPKB70023<br/>701          SAPKB70108<br/>702          SAPKB70206<br/>710          SAPKB71011<br/>711          SAPKB71106<br/>720          SAPKB72004<br/>730          SAPKB73001</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Use transaction SNOTE to implement the ABAP correction instructions from SAP Note 1468596.</p>\n<p>If, in exceptional cases, the implementation of SAP Note 1468596 is impossible, set the profile parameter rdisp/del_triv_joblog = 0. Doing so deactivates the deletion of trivial job logs again. Note that you lose all of the advantages of the new procedure in this case, which means that there may be space issues in the global file system in the long-run.</p></div>", "noteVersion": 4}, {"note": "1633731", "noteTitle": "1633731 - Usage of the 7.20 Downward-Compatible Kernel", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You want to use the SAP 7.20 kernel in your SAP system environment as downward-compatible kernel (<strong>DCK</strong>, in German: <strong>AKK</strong>).<br/><br/>This SAP note lists the prerequisites which have to be fulfilled to use the 7.20 kernel as a DCK. It describes in detail the following operations:</p>\n<ul>\n<li>Manual exchange of the default SAP kernel by a 7.20 kernel.</li>\n</ul>\n<ul>\n<li>Exchange of the default SAP kernel by a 7.20 kernel during system update and system upgrade.</li>\n</ul>\n<p><br/>If not explicitly excluded, all directives are also valid for the 7.20 EXT kernel. That means that the 7.20 EXT kernel can be used as downward-compatible kernel in all those situations where the 7.20 kernel can be used. In a given SAP system, you have to use all required components consistently from the 7.20 code line or from the 7.20 EXT code line.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>AKK, DCK, kernel, 720_EXT, maintenance</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>The usage of the 7.20 (EXT) kernel as a downward-compatible kernel is supported for the following SAP NetWeaver releases and the products that are based on these SAP NetWeaver releases:</p>\n<ul>\n<li>SAP NetWeaver 7.0 (\"7.00\")</li>\n</ul>\n<ul>\n<li>SAP EhP 1 for SAP NetWeaver 7.0 (\"7.01\")</li>\n</ul>\n<ul>\n<li>SAP NetWeaver 7.1 (\"7.10\")</li>\n</ul>\n<ul>\n<li>SAP EhP 1 for SAP NetWeaver 7.1 (\"7.11\")</li>\n</ul>\n<p><br/>Everything said for 7.10 and 7.11 holds also true for:</p>\n<ul>\n<li>SAP NetWeaver CE 7.1</li>\n</ul>\n<ul>\n<li>SAP EhP 1 for SAP NW CE 7.1</li>\n</ul>\n<p><strong>Attention</strong></p>\n<ul>\n<li>The 720 kernel can be used with any <strong>ABAP support package</strong> of the above mentioned releases. In an ABAP-only system, it is therefore <strong>not </strong>necessary to install a minimum ABAP support package before you install the 720 (EXT) DCK.</li>\n</ul>\n<ul>\n<li>After installing the 7.20 (EXT) kernel, it is not necessary to update SAP GUI. Your installed SAP GUI also works with the 7.20 (EXT) kernel.</li>\n</ul>\n<ul>\n<li>For compatibility reasons, the 7.20 kernel is still available for certain 32-bit platforms (Windows, Linux). However, we strongly recommend that you use the 64-bit variant. The 32-bit variant is not suitable for production operation.</li>\n</ul>\n<p>           The 7.20 EXT kernel is not available in a 32-bit variant.</p>\n<ul>\n<li>You may have to perform an operating system upgrade before you upgrade the kernel, or you may have to ensure additional database-specific prerequisites before the kernel upgrade (for example, the 7.20 kernel may be supported for certain operating system releases or database releases only). In particular, this applies to SAP kernel 7.20 EXT.</li>\n</ul>\n<p>           Release information is available at: http://service.sap. com/pam</p>\n<ul>\n<li>The 7.20 kernel checks complex DDIC structures in more detail than the previous kernels. Short dumps of the type DDIC_TYPELENG_INCONSISTENT may occur, for example, when calling transaction SM66. For more detailed information about correcting these inconsistencies, see <strong>Note 1610716</strong>.</li>\n</ul>\n<p>           The actions described in this note can be performed anytime, but we recommend to implement it before the kernel switch.<br/><br/>The following four bullet points apply only to <strong>Dual-Stack or Java only systems</strong>:</p>\n<ul>\n<li>Release <strong>7.00 or 7.01</strong>: There was an incompatibility regarding jmon (SAP note <strong>1652660</strong>). This has been eliminated with the following version of the SAP Tech S Offline component:</li>\n</ul>\n<ul>\n<ul>\n<li>Release 7.00: SAP TECH S 7.00 OFFLINE, SP 14, PL 24</li>\n</ul>\n</ul>\n<ul>\n<ul>\n<li>Release 7.01: SAP TECH S OFFLINE 7.01, SP 3, PL 13</li>\n</ul>\n</ul>\n<p>           This implies that at least support-package stack 14 or 3, respectively, must be installed in your system.<br/>           Note 1652660 also lists the required patch levels for the higher support packages.</p>\n<ul>\n<li>The tool <strong>JSPM </strong>must have at least SP 24 or 9, respectively.</li>\n</ul>\n<ul>\n<li>When using the <strong>SAPJVM 5.1</strong>, you have to install the latest patch collection, at least PC 58 (Build 5.1.074).</li>\n</ul>\n<ul>\n<li><strong>SAP Kernel 720 (EXT)</strong>should have at least Patch Level 201.</li>\n</ul>\n<p><br/>Installation or System Copy:</p>\n<ul>\n<li>If you are <strong>installing </strong>a new SAP system that is originally based on a 7.00, 7.01, 7.10, or 7.11 kernel, you will not be able to install the system directly with the 7.20 (EXT) kernel. You have to exchange the kernel manually as part of the post-installation process.</li>\n</ul>\n<ul>\n<li>If you are performing a <strong>system copy </strong>of an SAP system with one of the above mentioned releases that is running with a 7.20 (EXT) kernel, you will not be able to install the new system directly with the 7.20 (EXT) kernel. You have to exchange the kernel manually as part of the post-installation process.</li>\n</ul>\n<p><br/>When you implement the 7.20 (EXT) kernel,</p>\n<ul>\n<li>you must exchange the kernels on <strong>all </strong>application servers of the affected system.</li>\n</ul>\n<p><br/>After you have installed a 7.20 (EXT) kernel, you must</p>\n<ul>\n<li>Continue to import the Support Packages available for your original SAP release when you use Support Packages to implement corrections to repository objects.</li>\n</ul>\n<ul>\n<li>Use 7.20 (EXT) patches when correcting the kernel using kernel patches.</li>\n</ul>\n<p><strong>Platform-Specific Information</strong></p>\n<p>The downward compatible SAP Kernel 7.20 is designed for maximum coverage. As a result the SAP Kernel 7.20 can basically be used as DCK without additional platform requirements. Please verify for your SAP product release which database and operating system releases are supported with the 7.20 kernel. The PAM for your SAP product release can be accessed via the SAP Service Marketplace http://service.sap.com/pam. However, if you intend to use the SAP Kernel 7.20 as DCK you have to consider the following platform specific information:</p>\n<ul>\n<li>IBM AIX</li>\n</ul>\n<p>Please see SAP Note 1780629 for the minimal OS release and C/C++ runtime requirements.</p>\n<ul>\n<li>IBM DB2 LUW</li>\n</ul>\n<p>IBM DB2/UDB 8 is not supported with the SAP Kernel 7.20.</p>\n<ul>\n<li>IBM z/OS</li>\n</ul>\n<p>IBM z/OS 1.6, 1.7 and 1. 8 are not supported with the SAP Kernel 7.20.</p>\n<ul>\n<li>IBM i</li>\n</ul>\n<p>Please see the Note 68440 for the minimal OS release.</p>\n<ul>\n<li>SAP MaxDB</li>\n</ul>\n<p>The SAP Kernel 7.20 requires at least version 7.7.07. 41 of the MaxDB Client software (SQLBC). For more details refer to SAP Notes 822239 and 1487269.</p>\n<ul>\n<li>SAP liveCache</li>\n</ul>\n<p>The SAP Kernel 7.20 requires at least version 7.7.07. 41 of the SAP MaxDB Client software (SQLBC). For more details refer to SAP Notes 822239 and 1487269.</p>\n<p>On special purpose (profile parameter dbs/ada/variable_output has to be set Note 1784668) a 7.8. client is necessary.</p>\n<ul>\n<li>Microsoft SQL Server</li>\n</ul>\n<ul>\n<ul>\n<li>The SAP Kernel 7.20 is intended for Microsoft SQL Server 2005 and higher. If you are still using Microsoft SQL Server 2000 refer to SAP Note 1341097.</li>\n</ul>\n</ul>\n<ul>\n<ul>\n<li>For additional information about the usage of the SAP Kernel 7.20 as DCK with Microsoft SQL Server refer to SAP Note 1467086.</li>\n</ul>\n</ul>\n<ul>\n<li>Oracle Database</li>\n</ul>\n<p>Oracle 10.1 is not supported with the SAP Kernel 7.20. You have to use at least Oracle 10.2.</p>\n<p><br/>The downward compatible SAP Kernel 7.20 EXT is designed for maximum supportability. As a result you may need to update your database and operating system version to use the SAP Kernel 7.20 EXT as DCK. Please verify for your SAP product release which database and operating system releases are supported with the 7.20 EXT kernel. The PAM for your SAP product release can be accessed via the SAP Service Marketplace http://service.sap.com/pam. However, if you intend to use the SAP Kernel 7.20 EXT as DCK you have to consider the following platform specific information:</p>\n<ul>\n<li>AIX</li>\n</ul>\n<p>Please see SAP Note 1780629 for the minimal OS release and C/C++ runtime requirements.</p>\n<ul>\n<li>IBM i</li>\n</ul>\n<p>Please see the Note 68440 for the minimal OS release.</p>\n<ul>\n<li>Linux</li>\n</ul>\n<p>For details on Linux requirements refer to SAP Note 1563102.</p>\n<ul>\n<li>Microsoft Windows</li>\n</ul>\n<p>The SAP Kernel 7. 20 EXT requires a specific Microsoft Windows C Runtime environment. For details refer to SAP Note 1553465.</p>\n<ul>\n<li>Oracle Database</li>\n</ul>\n<p>On all operating system platforms, except IBM Power Linux, Windows IA64 and Linux IA64 (Intel Itanium), only Oracle 11g is supported. On IBM Power Linux, Windows IA64 and Linux IA64 (Intel Itanium), only Oracle 10g is supported.</p>\n<ul>\n<ul>\n<li>Oracle dababase server</li>\n</ul>\n</ul>\n<p>The Oracle database server has to be Oracle 11.2.0.2 or higher. The latest SAP Bundle Patch needs to be applied on top of the existing patch set release.</p>\n<ul>\n<ul>\n<li>Oracle database client</li>\n</ul>\n</ul>\n<p>Install the Oracle 11g Instant Client on all application servers and thedatabase server as described in SAP Note 1431794 The Oracle client software can be downloaded from the SAP Service Marketplace at: https://service.sap.com/oracle-download<br/><strong>Please note: </strong>After the installation of the Oracle 11g Instant Client, make sure that the library path for the &lt;sid&gt;adm user (LIBPATH, SHLIB_PATH, LD_LIBRARY_PATH) no longer contains any references to the old Oracle 10g Instant Client location.</p>\n<ul>\n<ul>\n<li>On IBM Power Linux, Windows IA64 and Linux IA64 (Intel Itanium), only Oracle 10g is supported. For these three platforms you do not have to make any changes to your database client.</li>\n</ul>\n</ul>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Please note <strong>automated Kernel installation</strong> using JSPM or SUM on Java and Dual-Stack Systems is <strong>not supported</strong>.</p>\n<p><strong>Contents</strong></p>\n<ol>1. New installation</ol><ol>2. Manual Exchange of the 7.20 (EXT) Kernel in Existing SAP Systems</ol><ol>3. Applying the 7.20 (EXT) Kernel During Upgrade, Update or SP-Update</ol>\n<p>              3.1  Applying the 7. 20 (EXT) Kernel During an Upgrade from a Lower Release to a 7.nn Release</p>\n<p>              3.2  Applying the 7. 20 (EXT) Kernel During Upgrade, Update or SP Update Within 7.nn Releases</p>\n<ol>4. Installation of an additional application server</ol>\n<p><strong>1  New installation </strong><strong><br/></strong>If you are installing a new SAP system that is originally based on a 700, 701, 710, 711 or 720 (EXT) kernel, it is possible to install the system directly with the 720 (EXT) kernel with the SWPM 1.0 SP04. See the note 1680045 for more details on SWPM 1.0.</p>\n<p><strong>2 Manual Exchange of the 7.20 (EXT) Kernel in Existing SAP Systems</strong></p>\n<p>SAP note <strong>1636252</strong> describes in detail how to implement the 7.20 (EXT) kernel manually.</p>\n<p><strong>3  Applying the 7.20 (EXT) Kernel During Upgrade, Update or SP-Update</strong></p>\n<p>The following rules also apply if a Product based on the mentioned NetWeaver releases is upgraded.<br/>Before starting the installation of the 720 (EXT) kernel on your SAP system, make sure that the database management and the operating systems fulfill the minimum requirements as described in the first section of this note and in the \"Platform-specific\" section.<br/>The 7.20 (EXT) Kernel must be exchanged <strong>manually</strong> in all cases. This is described in detail in SAP note <strong>1636252</strong>.</p>\n<p><strong>3.1  Applying the 7.20 (EXT) Kernel During an Upgrade from a Lower Release to a 7.nn Release</strong></p>\n<p>This section applies to the case that a product based on software component version SAP_BASIS 4.6C, 6.20 or 6.40 is to be upgraded to a release based on SAP_BASIS 7.00, 7.01, 7.10 or 7.11.</p>\n<ul>\n<li>The Kernel must be manually exchanged <strong>after</strong> the upgrade.</li>\n</ul>\n<p><strong>3.2  Applying the 7.20 (EXT) Kernel During Upgrade, Update or SP Update Within 7.nn Releases</strong></p>\n<p>This section comprises the following cases:</p>\n<ul>\n<li>Update or Upgrade of NetWeaver 7.00 to 7.01.</li>\n</ul>\n<ul>\n<li>Update of NetWeaver or CE 7.10 to 7.11.</li>\n</ul>\n<ul>\n<li>Upgrade of NetWeaver 7.00 to 7.10 or 7.11.</li>\n</ul>\n<ul>\n<li>Upgrade of NetWeaver 7.01 to 7.11.</li>\n</ul>\n<ul>\n<li>Support-Package Update of NetWeaver 7.00, 7.01, 7.10 or 7.11 or CE 7.10 or 7.11.</li>\n</ul>\n<p><br/>This section applies equally to any product based on a corresponding NetWeaver release.</p>\n<ul>\n<li>The kernel must be manually exchanged <strong>before</strong> the upgrade, update or SP update.</li>\n</ul>\n<ul>\n<li>This has to be performed only once. If the 720 (EXT) Kernel has already been installed, no additional actions are necessary, but of course, you have to select a 720 (EXT) Kernel during the stack download and not the default kernel.</li>\n</ul>\n<ul>\n<li>In the case of an Upgrade from 7.00/7.01 to 7.10/7.11, you have to use a modified kernel DVD. This is described in SAP note <strong>1679851</strong>.</li>\n</ul>\n<ul>\n<li>It is not allowed to switch from a 70x or 71x kernel to a 720 (EXT) kernel during upgrade/update. But you can switch from a 720 to a 720 EXT kernel during upgrade or update.</li>\n</ul>\n<p><strong>3  Installation of an additional application server</strong></p>\n<p>If you want to install additional application servers after the DCK has been applied to your system, take care to follow <strong>SAP note 1701575</strong> for this installation.</p></div>", "noteVersion": 16}, {"note": "1553301", "noteTitle": "1553301 - 7.20 <PERSON><PERSON> Kernel - Usage", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You want to use the SAP 7.20 EXT kernel in your SAP system environment as an alternative to the SAP 7.20 kernel.<br/>This SAP Note describes how to deploy the 7.20 EXT kernel in your SAP system that is currently using the standard 7.20 kernel by default.<br/>If you intend to use the 7.20 EXT kernel as a downward compatible kernel you can find more details in SAP note 1636252.</p>\n<p><strong>Remark: The maintenance of SAP kernel release 7.20 ended in Q1/2015. After this period, SAP kernel release 7.21 will replace SAP kernel release 7.20 and will become the standard SAP kernel release, for which corrections will be supplied.</strong></p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>EXT, kernel, EXT kernel, 7.02, 7.20, 7.30, maintenance, 720_EXT, AKK, DCK, Downward Compatible Kernel</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>For general information about the SAP 7.20 EXT kernel, see SAP Note 1553300.<br/><br/>The usage of the 7.20 EXT kernel as an alternative to the 7.20 kernel is supported for all SAP NetWeaver releases which are supported with the 7.20 kernel. The following NetWeaver releases are supported with 7.20 and 7.20 EXT kernel:</p>\n<ul>\n<li>SAP NetWeaver 7.0</li>\n</ul>\n<ul>\n<li>SAP EhP1 for SAP NetWeaver 7.0</li>\n</ul>\n<ul>\n<li>SAP EhP2 for SAP NetWeaver 7.0</li>\n</ul>\n<ul>\n<li>SAP NetWeaver 7.1</li>\n</ul>\n<ul>\n<li>SAP EhP1 for SAP NetWeaver 7.1</li>\n</ul>\n<ul>\n<li>SAP NetWeaver CE 7.1</li>\n</ul>\n<ul>\n<li>SAP EhP1 for SAP NetWeaver CE 7.1</li>\n</ul>\n<ul>\n<li>SAP NetWeaver 7.2</li>\n</ul>\n<ul>\n<li>SAP NetWeaver 7.3</li>\n</ul>\n<p><br/>In addition the following NetWeaver releases are supported with the 7.20 EXT kernel:</p>\n<ul>\n<li>SAP EhP3 for SAP NetWeaver 7.0</li>\n</ul>\n<ul>\n<li>SAP EhP1 for SAP NetWeaver 7.3</li>\n</ul>\n<p><br/>You can find more information about the 7.20 downward compatible kernel in SAP note 1629598 and SAP note 1636252.</p>\n<p><strong>Attention</strong></p>\n<ul>\n<li>To use the 7.20 EXT kernel in your SAP systems, you might have to upgrade the operating system and/or the database release used for the operation of your SAP system. To check if your operating system and/or database release is supported with the 7.20 EXT kernel, see the Product Availability Matrix (PAM) on SAP Service Marketplace at<br/>https://service.sap.com/pam.</li>\n</ul>\n<ul>\n<li>The 7.20 EXT kernel is only supported and available in a 64-bit version.</li>\n</ul>\n<ul>\n<li>If you are installing a new SAP system based on SAP NetWeaver 7.0 or higher or if you are creating a system copy of an existing SAP system which is based on SAP NetWeaver 7.0 or higher you have to use the Software Provision Manager (SWPM). The SWPM supports the usage of the 7.20 EXT kernel. For details please see SAP note 1680045.</li>\n</ul>\n<ul>\n<li>When you implement the 7.20 EXT kernel, you must replace the kernels of all application servers of the affected system.</li>\n</ul>\n<ul>\n<li>When you implement the 7.20 EXT kernel, you also have to exchange the Internet Graphics Server (IGS) by SAP IGS 7.20 EXT.</li>\n</ul>\n<ul>\n<li>After updating the system with the new 7.20 EXT kernel, you do not need to update SAP GUI. The installed SAP GUI also works with the 7.20 EXT kernel.</li>\n</ul>\n<ul>\n<li>The 7.20 EXT kernel is a \"binary only\" release. This means that customers can implement the newer kernel without upgrading the SAP system.</li>\n</ul>\n<ul>\n<li>After installing the 7.20 EXT kernel, customers must continue to import the support packages for the installed SAP release when updating or implementing corrections to the repository objects.</li>\n</ul>\n<ul>\n<li>After installing the 7.20 EXT kernel, customers must use only 7.20 EXT patches when implementing corrections to the kernel.</li>\n</ul>\n<ul>\n<li>The database Sybase ASE for Business Suite is only supported with the 7.20 EXT kernel. For the NetWeaver releases supported with Sybase ASE in general check the product availability matrix (PAM).</li>\n</ul>\n<p><strong>Platform-Specific Information</strong></p>\n<ul>\n<li>Microsoft Windows<br/>The 7.20 EXT kernel on Windows requires a specific C runtime that is included in the SAPEXE.SAR archive. SAP Note 1553465 describes how to apply the C runtime to your system.</li>\n</ul>\n<ul>\n<li>AIX<br/>Please see SAP Note 1780629 for the minimal OS release and C/C++ runtime requirements.</li>\n</ul>\n<ul>\n<li>IBM i<br/>No particular activities are required.</li>\n</ul>\n<ul>\n<li>Linux<br/>Before the installation of the 7.20 EXT kernel, see SAP Note 1563102.</li>\n</ul>\n<ul>\n<li>Oracle Database<br/>On IBM Power Linux and on Linux IA64 (Intel Itanium), only Oracle 10g is supported. For these two platforms you do not have to make any changes to your database client.<br/><br/>On all other platforms, make sure that the following prerequisites are met before the installation of the 7.20 EXT kernel:</li>\n</ul>\n<ul>\n<ul>\n<li>The database server runs with Oracle 11.2.0.2 or higher. The latest SAP Bundle Patch needs to be applied on top of the existing patchset release.</li>\n</ul>\n</ul>\n<ul>\n<ul>\n<li>Install the Oracle 11g Instant Client on all application servers and the database server as described in SAP Note 1431794: Oracle 11.2.0 Instant Client.<br/>You can download the Oracle client software from SAP Service Marketplace at: https://service.sap.com/oracle-download<br/>IMPORTANT: After the installation of the Oracle 11g Instant Client, make sure that the library path for the &lt;sid&gt;adm user (LIBPATH, SHLIB_PATH, LD_LIBRARY_PATH) no longer contains any references to the old Oracle 10g Instant Client location.</li>\n</ul>\n</ul>\n<ul>\n<li>Other platforms<br/>No specific information besides the Platform Availability Matrix (PAM) has to be taken into account.</li>\n</ul>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong>Content</strong><br/><strong>1. Manual Exchange of the 7.20 EXT Kernel in Existing SAP Systems</strong><br/><strong>2. Applying the 7.20 EXT Kernel During Upgrade and Update of SAP Systems</strong><br/><strong>3. Applying the 7.20 EXT Kernel Within SP Stacks</strong><br/><strong>4. Updating SAP NetWeaver CE Using the Update Management Service</strong></p>\n<p><strong>1. Manual Exchange of the 7.20 EXT Kernel in Existing SAP Systems</strong></p>\n<p>The 7.20 EXT kernel is supported only on recent versions of the database systems and the operating systems. Refer to the first part of this Note and make sure that your computing environment fullfills all requirements.</p>\n<p><strong>Obtaining the 7.20 EXT Kernel</strong></p>\n<p>Download the latest 7.20 EXT kernel stack for your operating system and database from SAP Service Marketplace.<br/>For Windows and UNIX/Linux platforms and IBM i, the 7.20 EXT kernel stack consists of the database-independent SAPEXE.SAR and the database-dependent SAPEXEDB.SAR archives.<br/><br/>Download the latest support package for the IGS from the SAP IGS 7.20 EXT branch. The package constists of the IGSEXE.SAR and the IGSHELPER.SAR archives.<br/><br/>If you are using SAPCRYPTOLIB, download the EXT version of the corresponding archive in accordance with SAP Note 397175.<br/><br/>For Oracle, you also need the archive DBATOOLS.SAR.</p>\n<p><strong>Replacing the 7.20 Standard Kernel by the 7.20 EXT Kernel for an ABAP-Only Stack and for an ABAP/Java Dual Stack</strong></p>\n<p>If no explicit other statement is given, the following tasks have to be performed as user &lt;SAPSID&gt;adm. The kernel has to be exchanged in the shared directory of the first installation host. The program sapcpe automatically copies all required files to the local directories during the next start of the SAP system.</p>\n<p><strong>Preliminary Tasks</strong></p>\n<ol>1. Stop the SAP system (there is no need to stop the database).</ol><ol>2. Terminate the SAProuter.&lt;DVEBMGS00&gt;/data/stat).</ol>\n<p><strong>Deploying the Kernel</strong><br/><strong>A. On UNIX / Linux Platforms</strong></p>\n<ol>1. Log on to your first installation host as user &lt;SID&gt;adm.</ol><ol>2. Copy the kernel archives SAPEXE.SAR and SAPEXEDB.SAR and the IGS archives IGSEXE.SAR and IGSHELPER.SAR to a temporary directory &lt;TEMPDIR&gt; of your choice (for example, /tmp).</ol><ol>3. If you are using the Oracle database, copy the archive DBATOOLS.SAR to a temporary directory &lt;TEMPDIR&gt; of your choice (for example, /tmp).</ol><ol>4. Switch to one of the following kernel directories:</ol>\n<ul>\n<ul>\n<li>/usr/sap/&lt;SAPSID&gt;/SYS/exe/run<br/>on systems based on EhP 2 for SAP NetWeaver 7.0</li>\n</ul>\n</ul>\n<ul>\n<ul>\n<li>/usr/sap/&lt;SAPSID&gt;/SYS/exe/&lt;codepage&gt;/&lt;platform&gt;<br/>on systems based on SAP NetWeaver 7.30</li>\n</ul>\n</ul>\n<ol><ol>5. We recommend to save the old kernel before deploying the EXT kernel.</ol></ol><ol><ol>Save the old kernel by creating a tar archive of the complete kernel directory using the following command:</ol></ol>\n<p><br/><br/></p>\n<ol>tar -cvf ../sapexe.tar</ol><ol>6. Save the SAPCAR program to a temporary directory &lt;TEMPDIR&gt; of your choice (for example, /tmp).</ol><ol>7. Switch to the user root and change the owner of all files to &lt;SAPSID&gt;adm using one of the following commands:</ol>\n<ul>\n<ul>\n<li>su - root<br/>chown &lt;SAPSID&gt;adm /usr/sap/&lt;SAPSID&gt;/exe/run/*<br/>exit<br/>on systems based on EhP 2 for SAP NetWeaver 7.0</li>\n</ul>\n</ul>\n<ul>\n<ul>\n<li>su - root<br/>chown &lt;SAPSID&gt;adm /usr/sap/&lt;SAPSID&gt;/SYS/exe/&lt;codepage&gt;/&lt;platform&gt;<br/>exit<br/>on systems based on SAP NetWeaver 7.30<br/><br/>Caution: With the OS/390 operating system, you have to use the command \"su &lt;user&gt;\" instead of \"su - root\", where &lt;user&gt; must have the UID 0.</li>\n</ul>\n</ul>\n<ol><ol>8. Unpack the new kernel using the following commands:</ol></ol>\n<p><br/><br/></p>\n<ol><ol>&lt;TEMPDIR&gt;/SAPCAR -xvf &lt;TEMPDIR&gt;/SAPEXE.SAR</ol></ol><ol>&lt;TEMPDIR&gt;/SAPCAR -xvf &lt;TEMPDIR&gt;/SAPEXEDB.SAR</ol><ol><ol>9. If you are using the Oracle database, unpack the archive DBATOOLS.SAR using the following command:</ol></ol>\n<p><br/><br/></p>\n<ol>&lt;TEMPDIR&gt;/SAPCAR -xvf &lt;TEMPDIR&gt;/DBATOOLS.SAR</ol><ol><ol>10. Unpack the new IGS using the following commands:</ol></ol>\n<p><br/><br/></p>\n<ol><ol>&lt;TEMPDIR&gt;/SAPCAR -xvf &lt;TEMPDIR&gt;/IGSEXE.SAR</ol></ol><ol>&lt;TEMPDIR&gt;/SAPCAR -xvf &lt;TEMPDIR&gt;/IGSHELPER.SAR</ol><ol>11. Switch to the user root and run the shell script saproot.sh, which is available in the kernel directory.</ol>\n<p><br/>In addition to the deployment of the IGSHELPER.SAR archive in the installation directory as described in the previous section, you have to deploy the IGSHELPER.SAR archive additionally in the instance directory of the primary application server instance and for every additional application server instance.<br/><br/>For the primary application server instance and every application server instance proceed as follows:</p>\n<ol>1. Logon to your host as user &lt;SID&gt;adm</ol><ol>2. Copy the IGS archive IGSHELPER.SAR to a temporary directory &lt;TEMPDIR&gt; of your choice (for example, /tmp).</ol><ol><ol>3. Switch to one of the following instance directories:</ol></ol>\n<ul>\n<ul>\n<li>/usr/sap/&lt;SAPSID&gt;/DVEBMGS<br/>on the primary application server instance</li>\n</ul>\n</ul>\n<ul>\n<ul>\n<li>/usr/sap/&lt;SAPSID&gt;/D<br/>on the additional application server instance</li>\n</ul>\n</ul>\n<ol><ol>4. Unpack the IGSHERLPER.SAR archive using the following command:</ol></ol>\n<p><br/><br/></p>\n<ol>./exe/SAPCAR -xvf &lt;TEMPDIR&gt;/IGSHELPER.SAR</ol>\n<p><strong>B. On Windows</strong></p>\n<ol>1. Copy the kernel archives SAPEXE.SAR and SAPEXEDB.SAR and the IGS archives IGSEXE.SAR and IGSHELPER.SAR and the archive for the DBA tools DBATOOLS.SAR (Oracle only) to a temporary directory &lt;TEMPDIR&gt; of your choice (for example, c:\\temp).</ol><ol>2. Determine the value of the SAP profile parameter DIR_CT_RUN according to SAP Note 997848.</ol><ol>3. On the global host, log on as &lt;SAPSID&gt;adm and open a command prompt (cmd.exe) as administrator. Set the working directory to your kernel staging directory DIR_CT_RUN, for example, &lt;drive&gt;:\\usr\\sap\\&lt;SAPSID&gt;\\sys\\exe\\[uc|nuc]\\&lt;platform&gt;.</ol><ol><ol>4. Copy sapcar.exe to the parent directory of DIR_CT_RUN</ol></ol><ol>(copy sapcar.exe ..).</ol><ol>5. If you want to keep the old kernel executables, you must use the copy function to create a backup and not the rename function. For more information, see SAP Note 142100, point 18.</ol><ol><ol>6. Unpack the new kernel from the kernel archives stored in the temporary directory in the specified sequence.</ol></ol>\n<p><br/><br/></p>\n<ol><ol>IMPORTANT: Do not call SAPCAR.EXE directly without a path specification. Instead, use the copied version of SAPCAR.EXE in the parent directory of the current kernel directory.</ol></ol>\n<p><br/><br/></p>\n<ol><ol>..\\sapexe.car -xvf &lt;TEMPDIR&gt;\\SAPEXE.SAR</ol></ol><ol><ol>..\\sapexe.car -xvf &lt;TEMPDIR&gt;\\SAPEXEDB.SAR</ol></ol><ol><ol>..\\sapcar.exe -xvf &lt;TEMPDIR&gt;\\DBATOOLS.SAR (Oracle only)</ol></ol>\n<p><br/><br/></p>\n<ol><ol>Note:</ol></ol><ol><ol>If your path to &lt;TEMPDIR&gt; contains blanks you need to put the path in double quotes and include a trailing comma:</ol></ol><ol><ol>Example:</ol></ol><ol>..\\sapcar.exe -xvf \"c:\\&lt;directory_with_blanks&gt;\\dbatools.sar,\"</ol><ol><ol>7. Unpack the new IGS from the IGS archives stored in the temporary directory in the specified sequence.</ol></ol>\n<p><br/><br/></p>\n<ol>..\\sapexe.car -xvf &lt;TEMPDIR&gt;\\IGSEXE.SAR</ol><ol><ol>8. Deploy the IGSHELPER.SAR archive in the instance directory of the primary application server instance and for every additional application server instance.</ol></ol>\n<p><br/><br/></p>\n<ol>For the primary application server instance and every application server instance proceed as follows:</ol><ol><ol>a) Log on to your host as user &lt;SID&gt;adm.</ol></ol><ol><ol>b) Copy the IGS archive IGSHELPER.SAR to a temporary directory &lt;TEMPDIR&gt;  of your choice (for example, c:\\temp).</ol></ol><ol><ol>c) Switch to one of the following instance directories:</ol></ol>\n<ul>\n<ul>\n<li>&lt;drive&gt;:\\usr\\&lt;SAPSID&gt;\\DVEBMGS&lt;No&gt;<br/>on the primary application server instance</li>\n</ul>\n</ul>\n<ul>\n<ul>\n<li>&lt;drive&gt;:\\usr\\sap\\&lt;SAPSID&gt;\\D&lt;No&gt;<br/>on the additional application server instance</li>\n</ul>\n</ul>\n<ol><ol><ol>d) Unpack the IGSHERLPER.SAR archive using the following command:</ol></ol></ol><ol><ol>sapcar.exe -xvf &lt;TEMPDIR&gt;\\IGSHELPER.SAR</ol></ol><ol>9. Install the current C runtime library by executing vcredist_&lt;platform&gt;.msi in the command box (or by double-clicking this file in the Windows Explorer). Before you start the system for the first time, and if you have a distributed system environment, perform this step manually on each node where a component of the system is configured to run.</ol><ol><ol>10. MSCS only:</ol></ol><ol>Update the files on all cluster nodes in the Windows\\SAPCLUSTER directory with the corresponding files in the central kernel staging directory.</ol>\n<p><strong>C. On IBM i</strong></p>\n<ol>1. Log on to your first installation host as user &lt;SID&gt;adm.</ol><ol>2. Copy the kernel archives SAPEXE.SAR and SAPEXEDB.SAR and the IGS archives IGSEXE.SAR and IGSHELPER.SAR to a temporary directory &lt;TEMPDIR&gt; of your choice (for example, /tmp).</ol><ol><ol>3. Run the APYSIDKRN command as follows:</ol></ol><ol><ol>   APYSIDKRN SID(&lt;SAPSID&gt;)</ol></ol><ol><ol>            ARCHIVES('&lt;TEMPDIR&gt;/SAPEXE.SAR'</ol></ol><ol><ol>                      '&lt;TEMPDIR&gt; /SAPEXEDB.SAR'</ol></ol><ol><ol>                      '&lt;TEMPDIR&gt; /IGSEXE.SAR'</ol></ol><ol><ol>                      '&lt;TEMPDIR&gt;/IGSHELPER.SAR')</ol></ol><ol>            MODE(*FULLY) CARPATH(*BUILTIN)</ol>\n<p><strong>D. On all platforms if you are using SAPCRYPTOLIB</strong></p>\n<p>Reinstall SAPCRYPTOLIB according to SAP Note 510007, step 1.<br/><br/>The installation procedure is also described in the SAP Help Portal, http://help.sap.com. Search Documentation for</p>\n<ul>\n<li>\"Installing the SAP Cryptographic Library on the AS ABAP\"</li>\n</ul>\n<ul>\n<li>\"Installing the SAP Cryptographic Library on the AS Java\"</li>\n</ul>\n<p><br/>(enter the search string with quotation marks), Product Type \"SAP<br/>NetWeaver\", Release \"7.0 EHP2\".</p>\n<p><strong>Replacing the 7.20 Standard Kernel by the 7.20 EXT Kernel for a Java-Only Stack</strong></p>\n<p>To replace the 7.20 standard kernel with the 7.20 EXT kernel in a Java-only stack, proceed as described in the SAP Knowledge Base article 1505299. Be aware of choosing the correct 7.20 EXT kernel archives for your platform (correct DB/OS combination, 64 bit, Unicode).<br/>To replace the IGS archives IGSEXE.SAR and IGSHELPER.SAR, you have to follow the instructions provided in the section \"Replacing the 7.20 Standard Kernel by the 7.20 EXT Kernel for an ABAP-Only Stack and for an ABAP/Java Dual Stack\".<br/><br/></p>\n<p><strong>2. Applying the 7.20 EXT Kernel During Upgrade and Update of SAP Systems</strong></p>\n<p>Before starting the upgrade or update of your SAP system, make sure that the database and the operating system fulfill the minimum requirements as described in the first section of this Note and in the \"Platform-Specific Information\" section.</p>\n<p><strong>Defining the SP Stack for Upgrade or Update Using the SAP Maintenance Optimizer</strong></p>\n<p>While creating your SP stack definition, you have to select the appropriate SAP kernel for your SAP system. Instead of selecting the 7.20 standard kernel in the SAP Maintenance Optimizer (MOPZ), you have to select the respective 7.20 EXT kernel for your system landscape. Make sure that you select the correct 7.20 EXT kernel for your platform (correct DB/OS combination, 64 bit, Unicode/Non-Unicode). For IGS, choose SAP IGS 7.20 EXT.</p>\n<p><strong>Defining the SP Stack for Upgrade or Update on SAP Service Marketplace</strong></p>\n<p>While creating your SP stack definition, you have to select the appropriate SAP kernel for your SAP system. Instead of selecting the 7.20 standard kernel on SAP Service Marketplace, you have to select the respective 7.20 EXT kernel for your system landscape. Make sure that you select the correct 7.20 EXT kernel for your platform (correct DB/OS combination, 64 bit, Unicode/Non-Unicode). For IGS, choose SAP IGS 7.20 EXT.<br/><br/>If you upgrade or update an SAP CE system, refer to section 4 of this Note: <strong>Updating SAP NetWeaver CE Using the Update Management Service</strong><br/>The update of SAP NetWeaver CE 7.0 to SAP NetWeaver CE 7.2 and EhP 1 for SAP NetWeaver CE 7.0 to SAP NetWeaver CE 7.2 requires to define the SP stack on SAP Service Marketplace as described above.<br/><br/></p>\n<p><strong>3. Applying the 7.20 EXT Kernel Within SP Stacks</strong></p>\n<p>Before starting the installation of the SAP stack on your SAP system, make sure that the database and the operating system fulfill the minimum requirements as described in the first section of this Note and in the \"Platform-Specific Information\" section.<br/>While creating your SP stack definition, you have to select the appropriate SAP kernel for your SAP system. Instead of selecting the 7.20 standard kernel, you have to select the correct 7.20 EXT kernel for your platform (correct DB/OS combination, 64 bit, Unicode/Non-Unicode). This automatically puts the right archives to your download basket. All other steps in the update procedure of your system do not change. For IGS, choose SAP IGS 7.20 EXT.<br/><br/></p>\n<p><strong>4. Updating SAP NetWeaver CE Using the Update Management Service</strong></p>\n<p>The Update Management Service helps to automate the process of updating the SAP NetWeaver CE system. Using the Update Management Service, you can update your CE system in one of the following ways:</p>\n<ul>\n<li>Using a locally downloaded Support Package Stack</li>\n</ul>\n<ul>\n<li>Using an SMP Automatic Update</li>\n</ul>\n<p><br/>If you intend to apply the 7.20 EXT kernel to your CE system only, the method by using a locally downloaded Support Package Stack is supported. This applies to the following:</p>\n<ul>\n<li>System upgrade and system update</li>\n</ul>\n<ul>\n<li>Deployment of an SP stack</li>\n</ul>\n<ul>\n<li>Any other software lifecycle operation if you are already using the 7.20 EXT kernel in your system</li>\n</ul>\n<p><br/>While creating your SP stack definition, you have to select the appropriate SAP kernel for your SAP system. Instead of selecting the 7.20 standard kernel, you have to select the correct 7.20 EXT kernel for your platform (correct DB/OS combination, 64 bit, Unicode). This automatically puts the right archives to your download basket. All other steps in the update procedure of your system remain unchanged. For IGS, choose SAP IGS 7.20 EXT.<br/><br/><strong>The Update Management Service using an SMP Automatic Update is not supported with the 7.20 EXT kernel at all.</strong></p></div>", "noteVersion": 16}, {"note": "1738494", "noteTitle": "1738494 - Using the new Unicode Printing Capabilities", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>You want to use the new Unicode printing capabilities in SAP NW.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>Unicode, Printing</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>To check if you are interested in the new printing capabilities please read the attached document. It describes in details the benefits of this approach. Also a short installation and testing description is attached.</p></div>", "noteVersion": 9}, {"note": "1563102", "noteTitle": "1563102 - Linux Requirements for 7.20 EXT and higher kernel", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You want to install the SAP kernel 7.2x EXT (7.20 EXT, 7.21 EXT, 7.22 EXT), 7.38 or higher.<br/><br/>Or the installation of a SAP system with one of the kernels mentioned above fails. The SAP system does not start and a program start issues one of the following error messages:<br/><br/>/lib64/libc.so.6: version `GLIBC_2.11' not found (required by disp+work)<br/>/lib64/libc.so.6: version `GLIBC_2.10' not found (...)<br/>/lib64/libc.so.6: version `GLIBC_2.9' not found (...)<br/>/lib64/libc.so.6: version `GLIBC_2.8' not found (...)<br/>/lib64/libc.so.6: version `GLIBC_2.7' not found (...)<br/>/lib64/libc.so.6: version `GLIBC_2.6' not found (...)<br/>/lib64/libc.so.6: version `GLIBC_2.5' not found (...)<br/>/lib64/tls/libc.so.6: version `GLIBC_2.4' not found (...)</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Linux, SLES11, RHEL6, 7.20 EXT, 720_EXT, 7.21 EXT, 721_EXT, 7.38, 7.40, 7.41, 7.42, 7.43, 7.44, IGS, glibc</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>The minimal Linux OS release to install a 7.2x EXT, 7.38 or higher kernel is SuSE Linux Enterprise Server (SLES) 11 SP1, RedHat Enterprise Linux (RHEL) 6, or Oracle Linux (OL) 6.</p>\n<p>If you use the 7.2x EXT variant of non-kernel components (such as SAP IGS), then the SAP kernel must be also 7.2x EXT!</p>\n<p>For certain NetWeaver 7.x releases a 7.2x EXT kernel is optional, e.g. for EhP 2 for NetWeaver 7.0, see SAP note 1553301.<br/><br/>For newest NetWeaver releases at least a 7.2x EXT kernel is required, e.g. for EhP 3 for NetWeaver 7.0, see SAP Product Availability Matrix (PAM, <a href=\"http://service.sap.com/pam\" target=\"_blank\">http://service.sap.com/pam</a>) or note 1591607.</p>\n<p>For general explanations for the 7.2x EXT variant of the 7.2x kernel see SAP note 1553300.</p>\n<p>Note that 7.22 EXT is not supported for Linux on IA-64 and Linux on 32bit, see Product Availability Matrix (PAM).</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>If you want or must use a 7.2x EXT, 7.38, or 7.4x kernel:</p>\n<ul>\n<li>Install or upgrade your Linux operating system to SLES 11 SP1, RHEL 6, or OL 6.</li>\n</ul>\n<p><br/>If you have accidently selected the 7.2x EXT kernel on an older Linux system:</p>\n<ul>\n<li>See note 1574751: Make sure that you do not use a 7.2x EXT kernel. Please install a normal 7.2x kernel instead.</li>\n</ul>", "noteVersion": 10}, {"note": "1753638", "noteTitle": "1753638 - z/OS: Enqueue Replication into System z Coupling Facility", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>Enqueue Replication into IBM Z Cross Coupling Facility (XCF or CF)</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>repstzOSCf.so cleanrepstzOSCf HA reason code 0x17030035</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>The standard mechanism for ensuring high availability for the enqueue server is by running an enqueue replication server on a remote host. The Enqueue Server will replicate its lock data by sending them via TCP/IP to an enqueue replication server. The replication server writes the data received from the enqueue server into a replication table.<br/><br/>In case of a failover the enqueue server moves to the host where the replication server was running. There it reads the data from the replication table to reestablish its enqueue table.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>This SAP note describes an alternate way to replicate enqueue data for the z/OS platform.<br/>This solution is based on the fact that several z/OS hosts (LPARs) can be organized in a z/OS SYSPLEX. In SYSPLEX LPARs share data via the IBM Z Cross Coupling Facility (CF).<br/><br/>Instead of sending replication requests to an enqueue repication server on a remote host the enqueue server writes the replication data directly into the CF.<br/>A replication server is no longer needed.<br/><br/>In case of a failover the enqueue server can be restarted manually or by the automation software on any LPAR within the SYSPLEX and reads the replication data from the CF.<br/><br/></p>\n<p><strong>SAP Releases supporting Enqueue Replication into the CF</strong><br/><br/>The Enqueue Replication into the CF is supported beginning with SAP kernel release 7.21 and patch number 38. <br/>All SAP kernels releases 7.4x and higher have this support.<br/><br/>The SAR package delivering the complete SAP kernel (SAPEXE.SAR Kernel Part I database independent) contains the additional parts:<br/><br/>repstzOSCf.so<br/><br/>This is a shared object library accessing the CF to be loaded by the enqueue server (see SAP profile parameter enque/server/replication_dll below)<br/><br/>cleanrepstzOSCf<br/><br/>A tool to remove an enqueue replication table from the Coupling Facility. This tool is intended for use in the same situations where cleanipc is used to remove shared memory<br/><br/><strong>Note:</strong><br/>The replication solution is suppported for SAP production systems based on SAP NetWeaver 7.0 up to 7.3 with SAP kernel levels 7.21 or higher 7.2x levels. For SAP NetWeaver 7.4 and 7.5 the solution is supported with SAP kernel releases 7.4x and 7.5x<br/><br/><strong>Setup of the Coupling Facility for Enqueue Replication</strong><br/><br/>The required setup of the Coupling Facility for Enqueue Replication and further details of this solution are described in the PDF document SAP_ENQ_REP_Inst.pdf that is attached to this SAP note.</p>\n<p>This document has been updated with some corrections and two paragraphs were added (version of 27th of May 2021):<br/> ·        <em>How to resume replication after a CF outage</em><br/> ·        <em>Secondary note pad structure</em><br/> <br/><strong>High Availability Setup </strong><br/><br/>The steps how to include the Coupling Facility for Enqueue Replication solution into a High Availability setup are described in the book \"Business Continuity for SAP on IBM Z\" available on <a href=\"https://www.ibm.com/docs/en/bcfsoz\" target=\"_blank\">https://www.ibm.com/docs/en/bcfsoz</a>. You may also download the associated PDF from there.</p>\n<p>The book discusses how to include CF replication into a new or already existing setup running under IBM Z System Automation.</p>\n<p>With SA z/OS APAR OA54684 a new REXX utility <span><span>SAPMVCF </span></span>is available which can detect and resolve a <em><span><em><span>co-location </span></em></span></em>situation of an SAP enqueue server and its coupling facility replication data.<br/><br/><strong>SAP Profile Parameters</strong><br/><br/>Enqueue Replication is switched on as usual by specifying the SAP profile parameter:<br/><br/>    enque/server/replication = true<br/><br/>To switch on the enqueue replication into the CF you have to specify the following SAP profile parameters:<br/><br/>    enque/server/replication_local = true<br/><br/>As a consequence the replication is now done locally by the enqueue server by means of a specialized platform-specific shared object library (DLL).<br/>This library must be specified by the SAP profile parameter<br/>    enque/server/replication_dll.<br/>The default for enque/server/replication_local is false.<br/><br/>For the z/OS platform and replication into CF the following shared library must be used:<br/><br/>    enque/server/replication_dll = repstzOSCf.so<br/><br/>The default for this parameter is an empty string (no DLL specified).<br/><br/>If the profile parameters enque/server/replication and enque/server/replication_local are both set to true the parameter<br/>enque/server/replication_dll must be set to a non-empty value. Otherwise the enqueue server terminates with a corresponding error message.<br/><br/>If you have a very high frequency of enqueue requests the throughput of the standard replication mechanism might not be sufficient.<br/>In this case you can parallelize the replication work by setting the SAP profile parameter<br/><br/>    enque/server/replication_thread_count<br/><br/>to a value greater than 1 (the default) and up to 10.</p>\n<p><strong>Important Note:</strong><br/> Before activating the replication_thread_count SAP profile parameter, make sure that your installation fulfills the prerequisites. If you have fragmented enqueue requests, do not change the default value. With a value greater than 1 you can get “out of order” fragments, replication will stop and you loose high availability of the enqueue server. In the file dev_enqrepl_1 you will see warning messages such as this: EnReplicateEnqToRep::process: stamp of fragment (1/556742519/804000) does not match [enclrep.cpp 2686].</p>\n<p dir=\"ltr\"><strong>SAP kernel 7.21 - performance problem when using replication with kernel patch level 313 up to 511</strong></p>\n<p dir=\"ltr\">SAP Enqueue server 7.21 patch level 313 up to 511 and setting profile parameter enque/server/replication_thread_count to a value larger than 1 results in extreme enqueue performance degradation when running with enqueue replication.<br/><strong>The recommendation is <span>not</span> to set enque/server/replication_thread_count or to set the value to 1 for kernel patch level between 313 and 511 both included. SAP kernel 7.22 is affected up to patch level 004.</strong></p>\n<p dir=\"ltr\">The problem was solved for SAP kernel 7.21 with patch level 512 and for SAP kernel 7.22 with patch level 005. See SAP note 2167244.<br/><br/>Before using this parameter read more details in the attached PDF document.<br/><br/><strong>Cleanup of Enqueue Replication Tables in the Coupling Facility</strong><br/><br/>If you need to delete an enqueue replication table you do this usually by the cleanipc tool since the table is stored in shared memory.<br/>If you have set up enqueue replication into the IBM Z Coupling Facility then cleanipc is not able to clear the replication table.<br/>You need to use the cleanup tool cleanrepstzOSCf to remove the enqueue replication data from the z/OS CF.<br/><br/><strong>Warning:</strong> deleting replication data while your SAP system is running will temporarily disrupt your failover capability and should be used with caution.<br/><br/>To delete an enqueue table issue:<br/><br/>cleanrepstzOSCf &lt;sapsid&gt; &lt;instnum&gt;<br/><br/>where &lt;sapsid&gt; is the SAP system id and &lt;instnum&gt; the instance number under<br/>which the enqueue server was running. E.g.:<br/><br/>cleanrepstzOSCf C11 12<br/><br/>As SAP system administrator you normally have set the environment variable SAPSYSTEMNAME. If set its value is taken as SAP system id by the cleanup tool so you can omit the &lt;sapsid&gt; parameter and running cleanrepstzOSCf just specifying the instance number:<br/><br/>cleanrepstzOSCf &lt;instnum&gt;       e.g.    cleanrepstzOSCf 12</p>\n<p><strong>Fix for problem due to missing access rights to RACF FACILITY profile IXCNOTE.SAPC11.ENQUEUE</strong></p>\n<p>A problem in the shared object library repstzOSCf.so causes the enqueue server to fail during startup if the SAP system administrator user id &lt;sid&gt;adm has no read access to RACF class FACILITY profile IXCNOTE.SAPC11.ENQUEUE. In this case the enqueue server fails during startup with error message</p>\n<p>[Thr 215DAE00:00000001] &gt;&gt;&gt; REPSTORE CF (repstzOSCF.so): ERROR (function &lt;isIXCNOTEAvailable()&gt; line 2098): The check for the XCF note pad services needed to store the replication data into the IBM Z Coupling Facility failed with return code 0x0008 - reason code 0x17030035 from IXCNOTE.</p>\n<p>As reason code you may also see a value of 0x1701084C in this message depending of the maintenance level of your z/OS system.</p>\n<p>In the z/OS system log you will get RACF message</p>\n<p>ICH408I USER(&lt;sid&gt;adm) GROUP(SAPSYS ) NAME(*****)<br/>  IXCNOTE.SAPC11.ENQUEUE CL(FACILITY)             <br/>  INSUFFICIENT ACCESS AUTHORITY                   <br/>  ACCESS INTENT(READ   )  ACCESS ALLOWED(NONE   )</p>\n<p>For SAP kernel 7.21 this problem is fixed with ZSCS patch level 326.<br/>For SAP kernel 7.42 it is fixed with patch level 28.</p>\n<p><strong>Fix for enqueue server shutdown when the enqueue replication table in the IBM Z Cross Coupling Facility is not reachable</strong></p>\n<p>If the CF where the enqueue replication table is stored goes down the replication gets suspendend after 2*3 tries to access the replication table. When it gets resumed after the timeout defined by enque/enrep/stop_timeout_s the enqueue server deletes the note pad holding the old replication data and creates a new one in the CF. If this fails the enqueue server shuts down. The enqueue server also terminates at initialization if it cannot reach the CF.</p>\n<p>The fix is available for</p>\n<p>- SAP kernel 7.22 with patch level 1022</p>\n<p>- SAP kernel 7.49 with patch level 948</p>\n<p>- SAP kernel 7.53 with patch level 810</p>\n<p><strong>Fix to ensure all replicas of deleted enqueue locks are removed from the replication <strong>table in the IBM Z Cross Coupling Facility</strong></strong></p>\n<p>When using enqueue lock replication into the IBM Z Cross Coupling Facility (CF) some replicas of deleted enqueue locks might be not be deleted from the replication table in the CF. A restart of the enqueue server will create invalid locks from these erroneously not deleted replicas. This patch corrects the problem.</p>\n<p>Important note:<br/>Even after activating the enqueue server containing this fix, it might still re-create invalid locks from the replication table created by an enqueue server version without this fix. In this case delete this entries using SAP transaction SM12. Alternatively, if there should be no locks in the replication table,  you can delete that table from the CF by using the executable cleanrepstzOSCf before you start the enqueue server including this fix.</p>\n<p>This fix is available for</p>\n<p>- SAP kernel 7.22 with patch level 1111</p>\n<p>- SAP kernel 7.49 with patch level 1022</p>\n<p>- SAP kernel 7.53 with patch level 828</p>\n<p><strong>Fix cut off descriptions of note pads for replication <strong>tables in the IBM Z Cross Coupling Facility</strong></strong></p>\n<p>When displaying an XCF note pad for an SAP enqueue replication table on the MVS console its description is cut-off by the last character as shown in the following example</p>\n<p>The MVS DISPLAY command for an XCF note pad with a SAP enqueue replication table</p>\n<p>D XCF,NP,NPNM=SAPC11.ENQUEUE.11,SCOPE=DET</p>\n<p>shows</p>\n<p>SDSF OPERLOG COH1 11/09/2021 4W <br/> COMMAND INPUT ===&gt; <br/> RESPONSE=COH1 <br/> IXC443I 09.53.07 DISPLAY XCF 246 <br/> INFO FOR NOTE PAD SAPHA1.ENQUEUE.11<br/> DESCRIPTION: <strong>SAPENQRepl SAPSID C11 Instance 1</strong> <br/> HOST STRUCTURE: IXCNP_SAPC1100 <br/> STATUS: CREATED <br/> SYSTEMS CONNECTED: COH1 <br/> CREATED: 11/03/2021 21:32:13.879060 <br/> LIST NUMBER: 16</p>\n<p>The last characater (the last digit of the SAP instance number) in the DESCRIPTION is missing . The description should be <strong>SAPENQRepl SAPSID C11 Instance</strong> <strong>1</strong><em><strong>1</strong><br/></em></p>\n<p>The reason for this is that the value for the description cannot be longer than 32 characters. The actual description would be 33 characters long where the last character is cut off in order to prevent a buffer overflow. So this bug does not cause any functional problem.</p>\n<p>This patch corrects the problem by providing a shorter message displaying the complete instance number. The message with this patch now looks like</p>\n<p><strong>SAPENQRepl SAPSID C11 Inst 11</strong></p>\n<p>and applies to all note pads created after this fix is applied. The fixed part is the shared object library repstzOSCf.so.</p>\n<p>This fix is available for</p>\n<p>- SAP kernel 7.22 with patch level 1115</p>\n<p>- SAP kernel 7.49 with patch level 1032</p>\n<p>- SAP kernel 7.53 with patch level 911</p>\n<p><strong>Fix to handle return code when <strong>remove of records from the replication <strong>table in the IBM Z Cross Coupling Facility</strong></strong> fails</strong></p>\n<p>If the CF containing the enqueue replication records is not available this was not indicated when deleting records since the correponding return code was ignored. As consequence the enqueue replication may remain active although the CF is not available or set activ in those cases.</p>\n<p>This patch fixes this problem. The fixed part is the shared object library repstzOSCf.so.</p>\n<p>The fix is available for</p>\n<p>- SAP kernel 7.22 with patch level 1117</p>\n<p>- SAP kernel 7.49 with patch level 1039</p>\n<p>- SAP kernel 7.53 with patch level 920</p>\n<p><strong>Fix cleanup of thread specific resources in case initialization of the replication table in the IBM Z Cross Coupling Facility fails</strong></p>\n<p>If no CF structure for the note pad to contain the enqueue replication data is available at initialization the thread specific resources are not cleaned up properly when handling that failure. Since e.g every 300 seconds there are retries to create the replication table thread specific resources like the thread keys run short and you get error messages in the dev_enqrepl developer trace like</p>\n<p>[Thr 21EA3800:00000009] &gt;&gt;&gt; REPSTORE CF (repstzOSCF.so): ERROR (function &lt;repstInit(iEnqueueTrace *, int *)&gt; line 379): pthread_key_create to create key for thread specific data returned with -1 and errno 112.</p>\n<p>This patch fixes this problem by proper cleanup of the thread specific resources. The fixed part is the shared object library repstzOSCf.so.</p>\n<p>The fix is available for</p>\n<p>- SAP kernel 7.22 with patch level 1310.</p>\n<p>- SAP kernel 7.53 with patch level 1211</p>\n<p>- SAP kernel 7.54 with patch level 119</p>", "noteVersion": 41}, {"note": "1629598", "noteTitle": "1629598 - SAP Kernel 720 will replace older kernel versions", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p><span><strong>Attention:</strong></span></p>\n<p>As of May 2015, <strong>Kernel 720 went Out of Maintenance</strong>, for details see SAP Note <a href=\"/notes/1975687\" target=\"_blank\">1975687</a>. The latest 720 patch is patch level 800 (see SAP Note <a href=\"/notes/\" target=\"_blank\">2158874</a>). <strong>Kernel 720 is replaced by downward compatible Kernel 721 (EXT)</strong> <strong>becoming the new \"standard\" kernel</strong>, and downward compatible kernel 722 (EXT) the new innovation kernel. Kernel 720 can still be used, but if an issue occurs with kernel 720, a fix will be only provided for SAP kernel 721 and 722.</p>\n<p>Our recommendation is an upgrade to the latest available 721 Stack Kernel. As described in <a href=\"/notes/1728283\" target=\"_blank\">SAP Note 1728283</a>, SAP Kernel 720 can be replaced by SAP Kernel 721 everywhere, and SAP Kernel 721 can be installed by a simple kernel patch.</p>\n<p>New Stack Kernels will be announced on <a href=\"http://scn.sap.com/docs/DOC-53415\" target=\"_blank\">http://scn.sap.com/docs/DOC-53415</a>. Further information on upgrading from 720 to 721 can be found in SAP Note <a href=\"/notes/1975687\" target=\"_blank\">1975687</a>.</p>\n<p>----</p>\n<p>The SAP kernel versions 700, 701, 710 and 711 will run out of maintenance by August 31st, 2012. These kernel versions will be replaced by the SAP kernel 720, which is fully downward-compatible. You can continue using your current kernel after that date, but if an issue occurs with your current kernel, a fix will only be provided for the SAP kernel 720.<br/>Usage of the SAP kernel 720 for SAP NetWeaver releases based on the older kernel versions will be made available in a staged approach, starting in Q4 2011.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Kernel, Downward Compatible Kernel, AKK, DCK, HANA, NewDB, RKS, Rolling Kernel Switch, WEBGUI, SAP GUI for HTML, unified rendering, security session, ACL, access control list, flexible license generator, DBSL, SAPHOSTAGENT, network statistics, 2090, FOR ALL ENTRIES, R3load. 7.00, 7.01, 7.10, 7.11, 7.20</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p><br/><strong>1. System requirements</strong><br/>This SAP Note applies to all systems based on SAP NetWeaver 7.0, SAP EHP1 for SAP NetWeaver 7.0 (i.e., 7.01), SAP NetWeaver 7.1 and SAP EHP1 for SAP NetWeaver 7.1 (i.e., 7.11).<br/>The SAP kernel 720 will be available for all platform combinations (DB/OS) for which the SAP kernel versions 700, 701, 710 and 711 are available.<br/>For newer DB/OS combinations please use Kernel 720_EXT. Please refer to SAP Note 1553301 for an in-depth description of the 720_EXT kernel.<br/><br/><strong>2. Benefits of using SAP kernel 720</strong><br/>Besides the fact that the 720 version of the SAP kernel is becoming the de facto standard throughout the SAP installed base and thus has the highest coverage both within SAP and among its customer base, you may benefit from the many additional features that were built into the SAP kernel 720 and have proven to be very useful in their supported environments. The features below are available without any further upgrade of your ABAP stack. Further enhancements based on functionality contained in the 720 kernel may be made available through SAP support SAP Notes in the future. In the following, we distinguish between new features and enhancements of already existing functionality.<br/><br/><strong>2.1. New Features</strong></p>\n<ul>\n<li>The Rolling Kernel Switch allows to minimize the planned downtimes of SAP systems during kernel patch installations. See SAP Note 953653 for details. New for NW 7.0, EHP1 for NW 7.0.</li>\n</ul>\n<ul>\n<li>The Flexible License Generator offers a solution for high availability environments and for adaptive computing, since the license is not linked to a hardware ID of a particular message server anymore. New for NW 7.0.</li>\n</ul>\n<p>                       See the SAP Online help:</p>\n<p>                       http://help.sap.com/saphelp_nw70ehp2/helpdata/en/20/384ef6bd054d5aae1ddd6f67e07a9e/content.htm</p>\n<ul>\n<li>Native support for accessing HANA from the ABAP stack which is necessary for HANA-based applications like the CO-PA accelerator. See SAP Notes 1597627 and 1627077. New for NW 7.0, EHP1 for NW 7.0, NW 7.1, EHP1 for NW 7.1.</li>\n</ul>\n<ul>\n<li>SAP GUI for HTML (WEBGUI) with unified rendering. See Note 1637287. New with NW 7.0, EHP1 for NW 7.0, NW 7.1, EHP1 for NW 7.1.</li>\n</ul>\n<ul>\n<li>Various new features in the SAP database interface/DBSL:</li>\n</ul>\n<ul>\n<ul>\n<li>MS SQL Server: support of statements with more than 2090 parameters (Note 1552952). New for NW 7.0.</li>\n</ul>\n</ul>\n<ul>\n<ul>\n<li>DB2/LUW (DB6): new configuration option for FOR ALL ENTRIES statements with UNION ALL transformation allows to reduce the complexity of statements (Note 1456251), new with NW 7.0, EHP1 for NW 7.0, NW 7.1, EHP1 for NW 7.1.</li>\n</ul>\n</ul>\n<p><br/><strong>2.2. Enhancements of existing features</strong><br/><br/><strong>2.2.1. Security Enhancements</strong></p>\n<ul>\n<li>Enhanced security configurations are available by the use of access control lists (ACLs) in communication programs:</li>\n</ul>\n<ul>\n<ul>\n<li>SAP gateway</li>\n</ul>\n</ul>\n<ul>\n<ul>\n<li>SAP dispatcher</li>\n</ul>\n</ul>\n<ul>\n<ul>\n<li>ICM</li>\n</ul>\n</ul>\n<ul>\n<ul>\n<li>SAP message server</li>\n</ul>\n</ul>\n<p>                       Learn more about this in the SAP online help:</p>\n<p>                       http://help.sap.com/saphelp_nw70ehp2/helpdata/en/d7/b5d0b1732746b094acf4c1b316bdd5/content.htm</p>\n<ul>\n<li>IBM i: Improved system security through signed kernel programs (Note 1581170), new with NW 7.0, EHP1 for NW 7.0, NW 7.1, EHP1 for NW 7.1.</li>\n</ul>\n<p><br/><strong>2.2.2. Supportability Enhancements</strong></p>\n<ul>\n<li>Improved monitoring on virtualized and cloud environments (Linux: Note 1102124, Windows: Note 1409604) by using the SAPHOSTAGENT version 720.</li>\n</ul>\n<ul>\n<li>IBM i: Simplified system administration and improved kernel maintenance:</li>\n</ul>\n<ul>\n<ul>\n<li>unified kernel structure and handling for all 7.x based SAP technology releases (Note 1078134), new with NW 7.0, EHP1 for NW 7.0;</li>\n</ul>\n</ul>\n<ul>\n<ul>\n<li>simplified kernel structure and unified kernel maintenance command (Note 1632755), new with NW 7.0, EHP1 for NW 7.0, NW 7.1, EHP1 for NW 7.1;</li>\n</ul>\n</ul>\n<ul>\n<ul>\n<li>central offline kernel staging area with kernel activation at system/instance restart (Note 1632754), new with NW 7.0, EHP1 for NW 7.0, NW 7.1, EHP1 for NW 7.1;</li>\n</ul>\n</ul>\n<ul>\n<ul>\n<li>automatic kernel patch distribution to all remote application server instances (SAP Note 1637588), new with NW 7.0, EHP1 for NW 7.0, NW 7.1, EHP1 for NW 7.1;</li>\n</ul>\n</ul>\n<ul>\n<ul>\n<li>central start/stop of all application server instances with one single command (SAP Note 1644051), new with NW 7.0, EHP1 for NW 7.0, NW 7.1, EHP1 for NW 7.1.</li>\n</ul>\n</ul>\n<ul>\n<li>DB2/LUW (DB6): Collection of network statistics is enabled for monitoring purposes (Note 1625157), new with NW 7.0, EHP1 for NW 7.0, NW 7.1, EHP1 for NW 7.1.</li>\n</ul>\n<p><br/><strong>2.2.3. Performance improvements</strong></p>\n<ul>\n<li>Optimized performance via profile based optimization on UNIX platforms (Linux, HPUX, IBM i for the 720_EXT kernel).</li>\n</ul>\n<ul>\n<li>DB2/LUW (DB6): R3load optimization for tables with LOB columns (Note 1433926), new with NW 7.0, EHP1 for NW 7.0, NW 7.1, EHP1 for NW 7.1.</li>\n</ul>\n<ul>\n<li>MSSQL Server: 'client awareness' enablement (Note 1148380), new for MSSQL Server 2005.</li>\n</ul>\n<p><br/><strong>3. Rollout of Kernel 720</strong><br/><br/>The rollout of the SAP kernel 720 for the different SAP NetWeaver releases follows a staged approach.<br/>The 720 kernel was finally released for all 7.0n and 7.1n releases, for ABAP only, Java only and Dual-Stack scenarios.<br/>During HY 2 2012, older kernel versions (700, 701, 710, 711) will be faded out, meaning that corrections for kernel bugs in these kernel versions will be made available through corrections for the SAP kernel 720.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><span><strong>Attention:</strong></span></p>\n<p>As of May 2015, <strong>Kernel 720 went Out of Maintenance</strong> with patch level 800 (see note <a href=\"/notes/\" target=\"_blank\">2158874</a>), <strong>with downward compatible Kernel 721 (EXT)</strong> <strong>becoming the new \"standard\" kernel</strong>, and downward compatible kernel 722 (EXT) the new innovation kernel. Kernel 720 can still be used, but if an issue occurs with kernel 720, a fix will be only provided for SAP kernel 721 and 722.</p>\n<p>Our recommendation is an upgrade <strong>to the latest available 721 Stack Kernel.</strong> As described in <a href=\"https://websmp206.sap-ag.de/sap/support/notes/1728283\" target=\"_blank\">SAP Note 1728283</a>, SAP Kernel 720 can be replaced by SAP Kernel 721 everywhere, and SAP Kernel 721 can be installed by a simple kernel patch.</p>\n<p>New Stack Kernels will be anounced on <a href=\"http://scn.sap.com/docs/DOC-53415\" target=\"_blank\">http://scn.sap.com/docs/DOC-53415</a>. Further information on upgrading from 720 to 721 can be found in note <a href=\"/notes/1975687\" target=\"_blank\">1975687</a>.</p>", "noteVersion": 13}, {"note": "1636252", "noteTitle": "1636252 - Installing a 7.20 kernel in SAP Web AS 7.00/7.01/7.10/7.11", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p><span><strong>Remark: </strong></span></p>\n<p><strong>Since the maintenance of SAP kernel release 720 ended in Q1/2015, SAP kernel release 721 replaces SAP kernel release 720 as the new standard SAP kernel release with corrections being supplied via SAP Kernel 721.</strong></p>\n<p><strong>Our recommendation is an upgrade to the latest available 721 Stack Kernel. New Stack Kernels will be anounced on <a href=\"http://scn.sap.com/docs/DOC-53415\" target=\"_blank\">http://scn.sap.com/docs/DOC-53415</a>. Further information on upgrading from 720 to 721 can be found in note <a href=\"/notes/1975687\" target=\"_blank\">1975687</a>.</strong></p>\n<p>You want to use the 7.20 or 7.20 EXT kernel as a downward-compatible kernel in one of the following NetWeaver releases</p>\n<ul>\n<li>SAP NetWeaver 7.0 (\"7.00\")</li>\n</ul>\n<ul>\n<li>SAP EhP1 for SAP NetWeaver 7.0 (\"7.01\")</li>\n</ul>\n<ul>\n<li>SAP NetWeaver 7.10 (\"7.10\")</li>\n</ul>\n<ul>\n<li>SAP EhP1 for SAP NetWeaver 7.10 (\"7.11\")</li>\n</ul>\n<p><br/>or in</p>\n<ul>\n<li>SAP NetWeaver CE 7.1</li>\n</ul>\n<ul>\n<li>SAP EhP1 for NW CE 7.1</li>\n</ul>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>7.20, 7.20 EXT, 720_EXT, AKK, DCK, Downward-Compatible Kernel</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p><br/><strong>Attention</strong></p>\n<ul>\n<li>This note is valid for all systems with NetWeaver Release 7.00, 7.01 (7.0 Enhancement Package 1), 7.10 and 7.11 (7.1 Enhancement Package 1) as well as CE 7.10 and 7.11 that still run with the 7.00, 7.01, 7.10 or 7.11 kernel that was originally delivered.</li>\n</ul>\n<ul>\n<li>You can also use the 7.20 EXT kernel as a downward-compatible kernel for the NetWeaver releases mentioned above. If you want to use the 7.20 EXT kernel, you have to download <strong>all</strong> kernel archives from the link of the corresponding EXT branch on the SAP Service Marketplace (e.g., from link \"SAP Kernel 7.20 EXT 64-BIT UC\" if you look for 64-bit UC kernel ). Please notice that it is very important not to mix executables from kernel 7.20 and 7.20 EXT. It is explicitly mentioned in the text if the handling of the 7.20 EXT kernel differs from the handling of the 7.20 standard kernel.<br/>For the handling of SAP HostAgent, please refer to section 3 \"Installing SAPHOSTAGENT\".</li>\n</ul>\n<ul>\n<li>You may have to perform an operating system upgrade before you upgrade the kernel, or you may have to ensure additional database-specific prerequisites before the kernel upgrade (for example, the 7.20 kernel may be supported for certain operating system releases or database releases only). In particular, this applies to SAP kernel 7.20 EXT.<br/>Release information is available at: http://service.sap.com/pam</li>\n</ul>\n<ul>\n<li>The 7.20 kernel checks complex DDIC structures in more detail than the previous kernels. Short dumps of the type DDIC_TYPELENG_INCONSISTENT may occur, for example, when calling transaction SM66. For more detailed information about correcting these inconsistencies, see Note <strong>1610716</strong>.<br/>The actions described in this note can be performed anytime, but we recommend to implement it before the kernel switch.</li>\n</ul>\n<ul>\n<li>If you want to use DCK for CE Developer Workplace systems, then check note 1709911.</li>\n</ul>\n<ul>\n<li>Release <strong>7.00 or 7.01</strong>: Follow SAP note 1119735 regarding the relocation of the contents of the CCMS agents' working directories. This is described under \"Preface\" in section \"Solution\".</li>\n</ul>\n<p><br/>The following four bullet points apply only to <strong>Dual-Stack or Java only systems</strong>:</p>\n<ul>\n<li>Release <strong>7.00 or 7.01</strong>: There was an incompatibility regarding jmon (SAP note <strong>1652660</strong>). This has been eliminated with the following versions of the SAP Java Technology S Offline component (SAPTECHF.SCA)</li>\n</ul>\n<ul>\n<ul>\n<li>Release 7.00: SAP TECH S 7.00 OFFLINE, SP 14, PL 24</li>\n</ul>\n</ul>\n<ul>\n<ul>\n<li>Release 7.01: SAP TECH S OFFLINE 7.01, SP 3, PL 13</li>\n</ul>\n</ul>\n<p>           This implies that at least support-package stack 14 or 3, respectively, must be installed in your system.<br/>Note 1652660 also lists the required patch levels for the higher support packages.<br/>           Please note: As described in note 1652660, you have to install the corresponding SAPJTECH<strong><strong>S</strong></strong>.SCA (\"SAP Java Technology Services\") as well if you need to install a suitable SAPJTECH<strong><strong>F</strong></strong>.SCA (cf. also KBA 1738769).</p>\n<ul>\n<li>Release <strong>7.00 or 7.01</strong>: The tool <strong>JSPM </strong>must have at least SP 24 or 9, respectively.</li>\n</ul>\n<ul>\n<li>Release <strong>7.10 or 7.11</strong>: When using the <strong>SAPJVM 5.1</strong>, you have to install the latest patch collection, at least PC 58 (Build 5.1.074).</li>\n</ul>\n<ul>\n<li><strong>SAP Kernel 7.20 (EXT)</strong>should have at least Patch Level 201.</li>\n</ul>\n<p><strong>Platform-Specific Information</strong></p>\n<p>The downward compatible SAP Kernel 7.20 is designed for maximum coverage. As a result the SAP Kernel 7.20 can basically be used as DCK without additional platform requirements. Please verify for your SAP product release which database and operating system releases are supported with the 7.20 kernel. The PAM for your SAP product release can be accessed via the SAP Service Marketplace http://service.sap.com/pam. However, if you intend to use the SAP Kernel 7.20 as DCK you have to consider the following platform specific information:</p>\n<ul>\n<li>IBM AIX<br/>Please see SAP Note 1780629 for the minimal OS release and C/C++ runtime requirements.</li>\n</ul>\n<ul>\n<li>IBM DB2 LUW<br/>IBM DB2/UDB 8 is not supported with the SAP Kernel 7.20.</li>\n</ul>\n<ul>\n<li>IBM z/OS<br/>IBM z/OS 1.6, 1.7 and 1.8 are not supported with the SAP Kernel 7.20.</li>\n</ul>\n<ul>\n<li>SAP MaxDB<br/>The SAP Kernel 7.20 requires at least version 7.7.07.41 of the MaxDB Client software (SQLBC). For more details refer to SAP Notes 822239 and 1487269.</li>\n</ul>\n<ul>\n<li>Microsoft SQL Server</li>\n</ul>\n<ul>\n<ul>\n<li>The SAP Kernel 7.20 is intended for Microsoft SQL Server 2005 and higher. If you are still using Microsoft SQL Server 2000 refer to SAP Note 1341097.</li>\n</ul>\n</ul>\n<ul>\n<ul>\n<li>For additional information about the usage of the SAP Kernel 7.20 as DCK with Microsoft SQL Server refer to SAP Note 1467086.</li>\n</ul>\n</ul>\n<ul>\n<li>Oracle Database<br/>Oracle 10.1 is not supported with the SAP Kernel 7.20. You have to use at least Oracle 10.2.</li>\n</ul>\n<p><br/>The downward compatible SAP Kernel 7.20 EXT is designed for maximum supportability. As a result you may need to update your database and operating system version to use the SAP Kernel 7.20 EXT as DCK. Please verify for your SAP product release which database and operating system releases are supported with the 7.20 EXT kernel. The PAM for your SAP product release can be accessed via the SAP Service Marketplace http://service.sap.com/pam. However, if you intend to use the SAP Kernel 7.20 EXT as DCK you have to consider the following platform specific information:</p>\n<ul>\n<li>AIX<br/>Please see SAP Note 1780629 for the minimal OS release and C/C++ runtime requirements.</li>\n</ul>\n<ul>\n<li>IBM i<br/>The SAP Kernel 7.20 EXT (but only non-Unicode) requires the locales of all languages you use within your SAP system to be installed on within PASE. For details refer to SAP Note 1423600.</li>\n</ul>\n<ul>\n<li>Linux<br/>For details on Linux requirements refer to SAP Note 1563102.</li>\n</ul>\n<ul>\n<li>Microsoft Windows<br/>The SAP Kernel 7.20 EXT requires a specific Microsoft Windows C Runtime environment. For details refer to SAP Note 1553465.</li>\n</ul>\n<ul>\n<li>Oracle Database<br/>On all operating system platforms, except IBM Power Linux, Windows IA64 and Linux IA64 (Intel Itanium), only Oracle 11g is supported. On IBM Power Linux, Windows IA64 and on Linux IA64 (Intel Itanium), only Oracle 10g is supported.</li>\n</ul>\n<ul>\n<ul>\n<li>Oracle dababase server<br/>The Oracle database server has to be Oracle 11.2.0.2 or higher.<br/>The latest SAP Bundle Patch needs to be applied on top of the existing patch set release.</li>\n</ul>\n</ul>\n<ul>\n<ul>\n<li>Oracle database client<br/>Install the Oracle 11g Instant Client on all application servers and the database server as described in SAP Note 1431794. The Oracle client software can be downloaded from the SAP Service Marketplace at: https://service.sap.com/oracle-download<br/><strong>Please note: </strong>After the installation of the Oracle 11g Instant Client, make sure that the library path for the &lt;sid&gt;adm user (LIBPATH, SHLIB_PATH, LD_LIBRARY_PATH) no longer contains any references to the old Oracle 10g Instant Client location.</li>\n</ul>\n</ul>\n<ul>\n<ul>\n<li>On IBM Power Linux, Windows IA64 and on Linux IA64 (Intel Itanium), only Oracle 10g is supported. For these three platforms you do not have to make any changes to your database client.</li>\n</ul>\n</ul>\n<p><br/>If you use a DB/OS platform combination in your system environment where both the DB release and the OS release are supported by the 7.20 EXT kernel, we recommend to use the 7.20 EXT kernel instead of the 7.20 standard kernel.<br/><br/></p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>The 7.20 kernel, which is delivered by SAP for Release 7.02 (7.0 Enhancement Package 2), is downward-compatible to the kernels for releases 7.00, 7.01, 7.10, and 7.11. Therefore, you can eliminate errors by replacing the kernel with a 7.20 kernel so that you do not have to perform an SAP upgrade.</p>\n<p><strong>Important</strong></p>\n<ul>\n<li>You must exchange the kernels on all servers of the affected system.</li>\n</ul>\n<ul>\n<li>The general SAP recommendation to keep up to date with the upgrades or updates remains unaffected by this downward-compatible kernel because it does not correct application errors.</li>\n</ul>\n<ul>\n<li>For compatibility reasons, the 7.20 kernel is still available for certain 32-bit platforms (Windows, Linux); however, we strongly recommend that you use the 64-bit variant. The 32-bit variant is not suitable for production operation.<br/>The 7.20 EXT kernel is not available in a 32-bit variant.</li>\n</ul>\n<ul>\n<li>You can upgrade to the 7.20 kernel without having to install the new GUI version on the front-end PCs.</li>\n</ul>\n<ul>\n<li>After you have installed a 7.20 (EXT) kernel, you must:</li>\n</ul>\n<ul>\n<ul>\n<li>Continue to import the Support Packages available for your original SAP release when you use Support Packages to implement corrections of repository objects.</li>\n</ul>\n</ul>\n<ul>\n<ul>\n<li>In future, when correcting the kernel using kernel patches, use 7.20 (EXT) patches only.</li>\n</ul>\n</ul>\n<p><strong>Definition of terms</strong></p>\n<p><br/>Here, 'kernel' means all executable programs located in /usr/sap/&lt;SAPSID&gt;/SYS/exe/run (UNIX, IBM i) or \\\\$(SAPGLOBALHOST)\\sapmnt\\&lt;SAPSID&gt;\\sys\\exe\\&lt;nuc|uc&gt;\\&lt;platform&gt; (Windows), not just the executable disp+work.<br/><br/><br/><br/></p>\n<p><strong>1. Obtaining the Archives</strong></p>\n<p><br/>The elements that are required for importing the kernel are available as an SAR archive on SAP Service Marketplace under \"SAP Kernel\". There a differentiation is made between 32-bit and 64-bit kernels and also between the character width (Unicode or non-Unicode). As a further sub-option, a differentiation is made between the platform and under this, a differentiation is made between database-independent archives and (for each database supported in the platform) database-dependent archives. The name of the archive is made from a template of type &lt;name&gt;_&lt;plevel&gt;-&lt;uid&gt;.SAR. Here, &lt;name&gt; is the actual name of the archive, followed by the patch level &lt;plevel&gt; and a unique ID &lt;uid&gt; in which the bit value, the character width, the platform and the database type of the relevant archive are included again. In general, only the short form &lt;name&gt;.SAR is used in this note (for example, SAPEXE.SAR). For more information about the download, see Note 19466.<br/>You should download the following archives into the same directory &lt;newkernel&gt;:</p>\n<ul>\n<li>Due to the digital signature of the archives (see Note 1598550), you require the latest <strong>SAPCAR</strong>. Therefore, download the archive SAPCAR_&lt;plevel&gt;-&lt;uid&gt;.EXE from SAP Service Marketplace under the name SAPCAR (UNIX, IBM i) or sapcar.exe (Windows) into your local &lt;newkernel&gt; directory.</li>\n</ul>\n<ul>\n<li>Download the latest 7.20 stack kernel from SAP Service Marketplace. The stack kernel consists of the database-independent archive <strong>SAPEXE.SAR</strong> and the database-dependent archive <strong>SAPEXEDB.SAR</strong>.<br/>On Oracle on Windows, you also still require the current Oracle Instant Client (see Note 998004).<br/>If you use different platforms (for example, Windows Application Server with database DB2 on IBM i), you can immediately download the stack kernel for all of the required platforms.</li>\n</ul>\n<ul>\n<li>If the Internet Graphics Server (IGS) is installed, you must also download the archives <strong>igsexe.sar</strong> and, optionally, <strong>igshelper.sar</strong>. When upgrading to kernel 7.20 or kernel 7.20 EXT you also need to upgrade the IGS installation accordingly:</li>\n</ul>\n<ul>\n<ul>\n<li>SAP IGS 7.20 needs kernel 7.20</li>\n</ul>\n</ul>\n<ul>\n<ul>\n<li>SAP IGS 7.20 EXT needs kernel 7.20 EXT</li>\n</ul>\n</ul>\n<ul>\n<li>The archive <strong>igshelper.sar</strong> contains an optional component (see SAP note 1028690), which is only available as of SAP IGS 7.20. We recommend to apply the igshelper.sar to your installation as described in SAP note 1028690 when you are upgrading to kernel 7.20 or 7.20 EXT. The igshelper.sar is operating-system independent and release independent regarding SAP IGS releases newer than or equal to 7.20.<br/>You can download igshelper.sar from http://service.sap.com/patches -&gt; \"Browse Our Download Catalog\" -&gt; \"SAP Frontend Components\" -&gt; \"SAP IGS HELPER APPLICATIONS\" -&gt; \"SAP IGS HELPER\" -&gt; \"SAP IGS HELPER\".</li>\n</ul>\n<ul>\n<li>If you use <strong>SAPCRYPTOLIB: </strong>from the PL 513 on it is replaced by the new CommonCryptoLib, please refer to the section <strong>5.3</strong>.</li>\n</ul>\n<ul>\n<li><strong>For Oracle only:</strong><br/>Download the package DBATL720O10_&lt;plevel&gt;-&lt;uid&gt;.SAR from http://service.sap.com/patches -&gt; \"Browse Our Download Catalog\" -&gt; \"Additional Components\" -&gt; \"SAP Kernel\" -&gt; SAP KERNEL (32|64)-BIT (UNICODE) -&gt; SAP KERNEL 7.20 (EXT) ... -&gt; &lt;your platform&gt; -&gt; ORACLE.<br/>For detailed information see note 12741.</li>\n</ul>\n<ul>\n<li><strong>For IBM i only:</strong></li>\n</ul>\n<ul>\n<ul>\n<li>Download the package ILE_&lt;plevel&gt;-&lt;uid&gt;.SAR with at least patch level 109 as ILE.SAR.</li>\n</ul>\n</ul>\n<ul>\n<ul>\n<li>If you have already changed your SAP system to SAPJVM, you must also download one of the archives SAPJVM4 or SAPJVM5, depending on which one you currently use.</li>\n</ul>\n</ul>\n<ul>\n<ul>\n<li>If you know that there are already patches on SAP Service Marketplace for the SAPEXE.SAR that is used (for example, DW), download these into the directory. So they are automatically applied with the kernel.</li>\n</ul>\n</ul>\n<ul>\n<ul>\n<li>For a 7.00/7.01 ASCII based dual stack system you used to download only ASCII Packages. The ASCII package contained the UNICODE part for the SCS Instance, too. The 7.20 Kernel does not contain UNICODE parts in the ASCII Packages. Therefore you have to download the UNICODE SAPEXE package in addition.</li>\n</ul>\n</ul>\n<ul>\n<li><strong>For UNIX/Windows only:</strong><br/>Download any additional programs (such as the RFCSDK) if you still want to install such programs after the kernel installation.</li>\n</ul>\n<p><strong>2. Preparations</strong></p>\n<p><br/>After you have downloaded the required kernel components, you must stop all of the relevant processes on all instances for the system to be processed and release or delete the kernel-specific resources. To do this, carry out the following actions as the user &lt;sapsid&gt;adm on all instances:</p>\n<ol>1. Stop the SAP system. (You do not need to stop the database.)</ol>\n<ul>\n<li>On UNIX or Windows:<br/>Stop the SAP system as usual.</li>\n</ul>\n<ul>\n<li>On IBM i:<br/>Stop the SAP system together with sapstartsrv:<br/>STOPSAP INSTANCE(*ALL) STARTUPSRV(*ALL) XDNLISTEN(*YES) WAIT(*YES) ENDSBS(*YES)</li>\n</ul>\n<ol>2. Stop saposcol.</ol>\n<ul>\n<li>On UNIX (Web AS release 7.0 and 7.01):<br/>cd /usr/sap/&lt;SAPSID&gt;/SYS/exe/run<br/>./saposcol -k</li>\n</ul>\n<ul>\n<li>On UNIX (Web AS release 7.10 and 7.11):<br/>cd /usr/sap/hostctrl/exe<br/>./saposcol -k</li>\n</ul>\n<ul>\n<li>On Windows:<br/>If it exists, stop the Windows service SAPOsCol and delete it using the following commands:<br/>net stop saposcol<br/>sc delete saposcol</li>\n</ul>\n<ul>\n<li>On IBM i:<br/>CALL PGM(SAPOSCOL) PARM('-k')</li>\n</ul>\n<ol>3. Stop sapstartsrv (this is required on UNIX and Windows only):</ol>\n<ul>\n<li>On UNIX:<br/>kill -2 &lt;pid of sapstartsrv&gt;</li>\n</ul>\n<ul>\n<li>On Windows:<br/>Stop and deactivate (Startup Type = Disabled) the services SAP&lt;SID&gt;_&lt;INSTANCE no.&gt;.</li>\n</ul>\n<ol>4. If present, unregister all standalone CCMS agents (sapccmsr [-j2ee], sapccm4x). On Unix you can see corresponding processes, on Windows you can see them running as services &lt;agent name&gt;.&lt;instance number&gt;:</ol>\n<ul>\n<li>sapccm4x -u pf=&lt;profile the agent started with&gt;<br/>sapccmsr -u pf=&lt;profile the agent started with&gt; [-j2ee]</li>\n</ul>\n<ol>5. Remove any IPC objects that still exist (this is required on UNIX only):</ol>\n<ul>\n<li>cleanipc &lt;instance no&gt; remove</li>\n</ul>\n<p><strong>3. Installing SAPHOSTAGENT</strong></p>\n<p><br/>For releases 7.00 and 7.01 (7.0 Enhancement Package 1), you must also install the package SAPHOSTAGENT.SAR in the latest 7.20 version available (in accordance with Note 1031096). <strong>On IBM i, </strong>you must also update SAP HostAgent to the latest 7.20 version, when you use SAP systems based on NetWeaver 7.10 or 7.11 (7.10 Enhancement Package 1). <strong>SYBASE ASE:</strong> minimum PL is 115.<br/>Afterwards, you must delete the call of the program SAPOSCOL from all the start profiles. (not Windows)<br/>SAP HostAgent is available on the SAP Service Marketplace (http://service.sap.com/swdc) under following menu path: \"Support Packages and Patches\" -&gt; \"Browse our Download Catalog\" -&gt; \"SAP Technology Components\" -&gt; \"SAP HOST AGENT\".<br/>Please note that a 7.20 EXT version of SAP HostAgent does not exist. You have to install the 7.20 version of SAP HostAgent even if you install SAP Kernel 7.20 EXT.<br/><br/></p>\n<p><strong>4. Importing the new kernel</strong></p>\n<p><br/>Import the 7.20 kernel from the directory &lt;newkernel&gt; on the host of the central instance and then on all of the application servers with local executables.</p>\n<p><strong>4.1 On UNIX</strong></p>\n<ol>1. Log on as user &lt;sapsid&gt;adm and switch to the directory /usr/sap/&lt;SAPSID&gt;/SYS/exe/run.</ol><ol><ol>2. We recommend saving the old kernel before deploying the new kernel.</ol></ol><ol>Save the old kernel by creating a tar archive of the complete kernel directory using the following command:</ol>\n<p>           SAPCAR -cvf ../sapexe.tar ./*</p>\n<ol>3. Save the following files and directories</ol><ol><ol>a) the directory jvm or sapjvm* (e.g., sapjvm, sapjvm_4, sapjvm_5) if it exists</ol></ol><ol><ol>b) the file protect.lst if it exists</ol><ol></ol><ol></ol><ol>c) the files rfcexec, rfcexec.sec if exists</ol></ol><ol><ol>d) the icu libraries (libicu*30.&lt;a sh, so&gt; or libicu*34.&lt;a sh, so&gt; if they exist</ol></ol>\n<p>            The directories jvm or sapjvm* and the file protect.lst only exist in the case of a Java or Dual-Stack implementation.</p>\n<p>The files rfcexec and rfcexec.sec are only used in customer specific non-ALE scenarios. It is mandatory to check if such scenarios exist. With the ALE scenario the classic rfcexec needs to be replaced with the version delivered with NW RFC SDK 720. Details are available in SAP note 1140031.</p>\n<ol>4. Switch to the user root and change the owner of all files to &lt;SAPSID&gt;adm using the following commands:</ol>\n<p>           su - root<br/>chown &lt;sapsid&gt;adm /usr/sap/&lt;SAPSID&gt; /SYS/exe/run/*<br/>exit<br/>              Important: On the operating system OS/390, you must use the command \"su &lt;user&gt;\" instead of \"su - root\", where &lt;user&gt; must have the UID 0.</p>\n<ol>5. Delete all of the files from the kernel directory, including the subdirectories. This ensures that there are no remaining files from the earlier release, which have a different name in Release 7.20 or are in a different place in a subdirectory.</ol>\n<p>           rm -rf *</p>\n<ol>6. Unpack the new kernel with the following commands:</ol>\n<p>           &lt;newkernel&gt;/SAPCAR -xvf &lt;newkernel&gt;/SAPEXE.SAR<br/>&lt;newkernel&gt;/SAPCAR -xvf &lt;newkernel&gt;/SAPEXEDB.SAR</p>\n<p>7. <strong><strong>Oracle only:</strong></strong></p>\n<ol>Unpack the DBATools with the following command:</ol>\n<p>           &lt;newkernel&gt;/SAPCAR -xvf &lt;newkernel&gt;/DBATL720O10. SAR<br/>              Install the Oracle instant client as described in note 819829.</p>\n<ol><ol>8. If you use IGS, you must unpack the IGS archive using the following</ol></ol><ol>command:</ol>\n<p>           &lt;newkernel&gt;/SAPCAR -xvf &lt;newkernel&gt;/igsexe. sar</p>\n<ol>9. If there are files or directories that were saved in step 3, restore them into the current directory</ol><ol>10. To deploy the optional IGSHELPER archive switch to the relevant local directory /usr/sap/&lt;SAPSID&gt;/&lt;INSTANCE&gt; on every instance and execute the command:</ol>\n<p>           &lt;newkernel&gt;/SAPCAR -xvf &lt;newkernel&gt;/IGSHELPER. SAR</p>\n<ol>11. Switch to the user root and run the shell script saproot.sh, which is available in the kernel directory.</ol>\n<p>           su - root (or su &lt;user with UID 0&gt; on OS/390)<br/>cd /usr/sap/&lt;SAPSID&gt;/SYS/exe/run<br/>./saproot.sh &lt;SAPSID&gt;<br/>exit</p>\n<ol>12. Delete all of the local executables on the individual instances. To do this, switch to the relevant local executable directory /usr/sap/&lt;SAPSID&gt;/&lt;INSTANCE&gt;/exe and execute</ol>\n<p>           rm -rf *</p>\n<ol>13. Since executables from the local executable directories may already be executed for the start before sapcpe runs, start an initial copy of the executables.</ol><ol><ol>a) All application server instances (primary application server/central instance, additional application servers/dialog instances):</ol></ol>\n<ul>\n<ul>\n<li>For ABAP-only systems:</li>\n</ul>\n</ul>\n<p>                    cd /usr/sap/&lt;SAPSID&gt;/&lt;INSTANCE&gt;/work<br/>sapcpe pf=/usr/sap/&lt;SAPSID&gt;/SYS/profile/&lt;instance profile&gt;</p>\n<ul>\n<ul>\n<li>For Dual-Stack systems:</li>\n</ul>\n</ul>\n<p>                    cd /usr/sap/&lt;SAPSID&gt;/&lt;INSTANCE&gt;/work<br/>sapcpe pf=/usr/sap/&lt;SAPSID&gt;/SYS/profile/&lt;instance profile&gt;<br/>                    If your system is running with the SAPJVM:<br/>                    sapcpe pf=/usr/sap/&lt;SAPSID&gt;/SYS/profile/&lt;instance profile&gt; source:&lt;sapjvm directory&gt; list:&lt;sapjvm directory&gt;/sapjvm_&lt;version&gt;.lst</p>\n<ul>\n<ul>\n<li>For Java-only systems:</li>\n</ul>\n</ul>\n<p>                    cd /usr/sap/&lt;SAPSID&gt;/&lt;INSTANCE&gt;/work<br/>sapcpe pf=/usr/sap/&lt;SAPSID&gt;/SYS/profile/&lt;instance profile&gt; list:/usr/sap/&lt;SAPSID&gt;/SYS/exe/run/j2eeinst.lst<br/>                    If your system is running with the SAPJVM:<br/>                    sapcpe pf=/usr/sap/&lt;SAPSID&gt;/SYS/profile/&lt;instance profile&gt; source:&lt;sapjvm directory&gt; list:&lt;sapjvm directory&gt;/sapjvm_&lt;version&gt;.lst</p>\n<ol><ol>b) For additional instances such as ASCS, SCS and ERS (exist only in a cluster):</ol></ol>\n<p>                    cd /usr/sap/&lt;SAPSID&gt;/&lt;INSTANCE&gt;/work<br/>sapcpe pf=/usr/sap/&lt;SAPSID&gt;/SYS/profile/&lt;instance profile&gt; list:/usr/sap/&lt;SAPSID&gt;/SYS/exe/run/scs.lst<br/>           NB: the arguments (\"pf\", \"list\", \"source\") are separated by a white space, not by a newline character. &lt;sapjvm directory&gt; means the location of the saved SAPJVM, see the step 9 above.<br/>           <br/>           <br/><strong>4.2 On Windows</strong></p>\n<ol>1. Log on as user &lt;sapsid&gt;adm and switch to the global host in your kernel directory, for example: &lt;drive&gt;:\\usr\\sap\\&lt;SAPSID&gt;\\sys\\exe\\&lt;uc|nuc&gt;</ol><ol>2. Rename the &lt;platform&gt; directory, for example &lt;platform&gt;.save.</ol><ol><ol>3. Create a new directory &lt;platform&gt; and switch to this directory.</ol></ol><ol><ol>Unpack the new kernel from the directory &lt;newkernel&gt; of the downloaded archive in the specified sequence.</ol></ol>\n<p><br/><br/></p>\n<ol><ol>Important: Do not call SAPCAR.EXE directly without specifying a path; instead, use the specified directory structure.</ol></ol>\n<p><br/><br/></p>\n<ol><ol>&lt;newkernel&gt;\\SAPCAR.EXE -xvf &lt;newkernel&gt;\\SAPEXE.SAR</ol></ol><ol>&lt;newkernel&gt;\\SAPCAR.EXE -xvf &lt;newkernel&gt;\\SAPEXEDB.SAR</ol><ol>4. Restore the following directories and files into the newly created directory if they exist in the &lt;platform&gt;.save directory</ol><ol><ol>a) the directory jvm or sapjvm* (e.g., sapjvm, sapjvm_4)</ol></ol><ol><ol>b) the file protect.lst</ol><ol></ol><ol></ol><ol>c) the files rfcexec.exe, rfcexec.sec</ol></ol><ol><ol>d) the icu libraries icu*30.dll or icu*34.dll if they exist</ol></ol>\n<p>            The directories jvm or sapjvm* and the file protect.lst only exist in the case of a Java or Dual-Stack implementation.</p>\n<p>The files rfcexec and rfcexec.sec are only used in customer specific non-ALE scenarios. It is mandatory to check if such scenarios exist. With the ALE scenario the classic rfcexec needs to be replaced with the version delivered with NW RFC SDK 720. Details are available in SAP note 1140031.</p>\n<p>5.</p>\n<p><strong><strong>Oracle only:</strong></strong></p>\n<ol><ol>Unpack the DBATools to the &lt;platform&gt; directory:</ol></ol>\n<p><br/><br/></p>\n<ol><ol>&lt;newkernel&gt;\\SAPCAR.EXE -xvf &lt;newkernel&gt;\\DBATL720O10.SAR</ol></ol>\n<p><br/><br/></p>\n<ol>Install the Oracle instant client as described in note 998004.</ol><ol><ol>6. If you use IGS, unpack the new IGS using the following command:</ol></ol>\n<p><br/><br/></p>\n<ol>&lt;newkernel&gt;\\SAPCAR.EXE -xvf &lt;newkernel&gt;\\igsexe.sar</ol><ol>7. To deploy the optional IGSHELPER archive, still logged in as &lt;sapsid&gt;adm, switch to the relevant local directory on every instance. For example:</ol>\n<ul>\n<ul>\n<li>primary application server instance:<br/>&lt;drive&gt;:\\usr\\sap\\&lt;SAPSID&gt;\\DVEBMGS&lt;No&gt;</li>\n</ul>\n</ul>\n<ul>\n<ul>\n<li>additional application server instance:<br/>&lt;drive&gt;:\\usr\\sap\\&lt;SAPSID&gt;\\D&lt;No&gt;</li>\n</ul>\n</ul>\n<p>              and execute the command<br/><br/>&lt; newkernel&gt;\\SAPCAR.EXE -xvf &lt;newkernel&gt;\\IGSHELPER.SAR</p>\n<ol>8. On all instances in the directories &lt;drive&gt;:\\usr\\sap\\&lt;SAPSID&gt;\\&lt;INSTANCE&gt;\\exe, delete all of the files (including the subdirectories).</ol><ol>9. Install the current C runtime library as per note 1553465 by executing vcredist_&lt;platform&gt;.msi in the command box (or by double-clicking this file in the Windows Explorer). Before you start the system for the first time, and if you have a distributed system environment, perform this step manually on each node where a component of the system is configured to run.</ol><ol>10. Since executables from the local executable directories may already be executed for the start before sapcpe runs, start an initial copy of the executables.</ol><ol><ol>a) For all application server instances (primary application server/central instance, additional application servers/dialog instances):</ol></ol>\n<ul>\n<ul>\n<li>For ABAP-only systems:</li>\n</ul>\n</ul>\n<p>                    &lt; drive&gt;:<br/>cd \\usr\\sap\\&lt;SAPSID&gt;\\&lt;INSTANCE&gt;\\work<br/>sapcpe pf=&lt;drive&gt;:\\usr\\sap\\&lt;SAPSID&gt;\\sys\\profile\\&lt;instance profile&gt;</p>\n<ul>\n<ul>\n<li>For Dual-Stack systems:</li>\n</ul>\n</ul>\n<p>                    &lt; drive&gt;:<br/>cd \\usr\\sap\\&lt;SAPSID&gt;\\&lt;INSTANCE&gt;\\work<br/>sapcpe pf=&lt;drive&gt;:\\usr\\sap\\&lt;SAPSID&gt;\\sys\\profile\\&lt;instance profile&gt;<br/>                    If your system is running with the SAPJVM:<br/>                    sapcpe pf=&lt;drive&gt;:\\usr\\sap\\&lt;SAPSID&gt;\\sys\\profile\\&lt;instance profile&gt; source:&lt;sapjvm directory&gt; list:&lt;sapjvm directory&gt;\\sapjvm_&lt;version&gt;.lst</p>\n<ul>\n<ul>\n<li>For Java-only systems:</li>\n</ul>\n</ul>\n<p>                    &lt; drive&gt;:<br/>cd \\usr\\sap\\&lt;SAPSID&gt;\\&lt;INSTANCE&gt;\\work<br/>sapcpe pf=&lt;drive&gt;:\\usr\\sap\\&lt;SAPSID&gt;\\sys\\profile\\&lt;instance profile&gt; list:&lt;drive&gt;:\\usr\\sap\\&lt;SAPSID&gt;\\sys\\exe\\&lt;uc|nuc&gt;\\&lt;platform&gt;\\j2eeinst.lst<br/>                    If your system is running with the SAPJVM:<br/>                    sapcpe pf=&lt;drive&gt;:\\usr\\sap\\&lt;SAPSID&gt;\\sys\\profile\\&lt;instance profile&gt; source:&lt;sapjvm directory&gt; list:&lt;sapjvm directory&gt;\\sapjvm_&lt;version&gt;.lst</p>\n<ol><ol>b) For additional instances such as ASCS, SCS and ERS (exist only in a cluster):</ol></ol>\n<p>                    &lt; drive&gt;:<br/>cd \\usr\\sap\\&lt;SAPSID&gt;\\&lt;INSTANCE&gt;\\work<br/>sapcpe pf=&lt;drive&gt;:\\usr\\sap\\&lt;SAPSID&gt;\\sys\\profile\\&lt;instance profile&gt; list:&lt;drive&gt;:\\usr\\sap\\&lt;SAPSID&gt;\\sys\\exe\\&lt;uc|nuc&gt;\\&lt;platform&gt;\\scs.lst<br/>           NB: the arguments (\"pf\", \"list\", \"source\") are separated by a white space, not by a line break. &lt;sapjvm directory&gt; means the location of the saved SAPJVM, see the step 4 above.</p>\n<ol>11. Activate (Startup Type = Automatic) and start the Windows Services SAP&lt;SID&gt;_&lt;INSTANCE no.&gt; of primary application server instance and of every additional application server instance to active the new version of sapstartsrv.exe.</ol>\n<p><br/><br/><strong>MSCS only</strong>:</p>\n<ol>1. Start and Stop the clustered (A)SCS instances and the appropriate SAP services using the cluster admin tool or the Powershell.</ol><ol>2. Also see SAP Note 1596496 on how to update the SAP Resource Type DLL.</ol><ol><ol>3. You have to follow step 6-10 also for the ERS instances on your cluster nodes.</ol></ol><ol><ol>Search for REPSRV.lst in all Start Profile of the ERS instances (line Start_Program_00 =)and if it exists replace it with SCS.lst.</ol></ol><ol>Be sure to check if your Enqueue Replication Service is replicating again after starting the ERS instances</ol><ol>4. If you have standalone gateway instances installed on cluster nodes, these also need to be updated with the corresponding files from the kernel staging directory \\\\$(SAPGLOBALHOST)\\sapmnt\\&lt;SAPSID&gt;\\sys\\exe\\&lt;nuc|uc&gt;\\&lt;platform&gt;.</ol>\n<ul>\n<ul>\n<li>On Windows Server 2003 (see Note 657999) the standalone gateway is installed in the &lt;Windows&gt;\\SAPCLUSTER directory.</li>\n</ul>\n</ul>\n<ul>\n<ul>\n<li>On Windows Server 2008 and higher (see Note 1764650 - How to install a standalone gateway in a Microsoft Failover Cluster for Oracle) it is &lt;oracle shared disk&gt;:\\sap\\&lt;DB-SID&gt;\\dbtools.</li>\n</ul>\n</ul>\n<p><br/><br/></p>\n<p><strong>4.3 On IBM i:</strong></p>\n<p><br/>Log on as a QSECOFR-type user and execute the following commands or actions:</p>\n<ol><ol>1. Point the current directory explicitly to the downloaded archives:</ol></ol><ol>CHGCURDIR '&lt;newkernel&gt;'</ol><ol><ol>2. Cleanup the library SAP_TOOLS if it exists, otherwise you need not care</ol></ol><ol>CLRLIB SAP_TOOLS</ol><ol><ol>3. Extract the required tools from *SAVF file ILE_TOOLS in ILE.SAR by program iletools into SAP_TOOLS (*LIB) - use 'Copy &amp; Paste' to transfer the following command to your session and execute it:</ol></ol><ol>CALL QP2SHELL PARM('/QOpenSys/usr/bin/csh' '-c' 'SAPCAR -xvf ILE.SAR iletools ILE_TOOLS;iletools')</ol><ol>4. Since this call to QP2SHELL does not produce any output, check whether library SAP_TOOLS exists now and has some objects in it. If not, check with the WRKSPLF command for spool files with error messages.</ol><ol><ol>5. Set the authorities of the objects in SAP_TOOLS by these commands:</ol></ol><ol><ol>If your starting kernel is already running with the 'newuserconcept' set:</ol></ol><ol><ol>  ADDENVVAR ENVVAR(CLASSICUSERCONCEPT) VALUE('N')</ol></ol><ol><ol>  ADDLIBLE  SAP_TOOLS</ol></ol><ol><ol>  FIXSAPOWN *NONE SAP_TOOLS</ol></ol><ol><ol>If your starting kernel is still running with the 'classicuserconcept' set:</ol></ol><ol><ol>  ADDENVVAR ENVVAR(CLASSICUSERCONCEPT) VALUE('Y')</ol></ol><ol><ol>  ADDLIBLE  SAP_TOOLS</ol></ol><ol><ol>  FIXSAPOWN *NONE SAP_TOOLS</ol></ol>\n<p><br/><br/><strong>note:</strong></p>\n<ol>If you are not sure which userconcept your current system is using please check for the file /sapmnt/&lt;SID&gt;/profile/.newuserconcept The existence of this file says that the newuserconcept is active. You can also check who is the owner/primary-group of the executable files underneath /sapmnt/&lt;SID&gt;/exe. &lt;SID&gt;ADM/R3GROUP is an indicator for the newuserconcept, while R3OWNER/&lt;SID&gt;GROUP is an indicator for the classicuserconcept.</ol><ol>6. For SAP systems with a release lower than 7.10, check the contents of the file /usr/sap/sapservices. If sapstartsrv is started under the name sapstartsrvu (/usr/sap/&lt;sapsid&gt;/SYS/exe/run/sapstartsrvu ...), you must change the entry so that sapstartsrv is started from the subdirectory .../uc of the previous directory in future (/usr/sap/&lt;sapsid&gt;/SYS/exe/run/uc/sapstartsrv ...)</ol>\n<p><br/>Log on as &lt;SAPSID&gt;ADM and execute the following commands for importing the new kernel:</p>\n<ol>1. ADDLIBLE SAP_TOOLS</ol><ol><ol>2. Check whether the environment variable CLASSICUSERCONCEPT is set by the login process (use WRKENVVAR); if it is not set, set it in the following way:</ol></ol><ol><ol>If your starting kernel is already running with the 'newuserconcept' set:</ol></ol><ol><ol>  ADDENVVAR ENVVAR(CLASSICUSERCONCEPT) VALUE('N')</ol></ol><ol><ol>If your starting kernel is still running with the 'classicuserconcept' set:</ol></ol><ol>  ADDENVVAR ENVVAR(CLASSICUSERCONCEPT) VALUE('Y')</ol><ol>3. Check that all instance- and start-profiles of the system contain the parameter DIR_CT_RUN. If missing in a 7.00/7.01 environment set it to: DIR_CT_RUN = $(DIR_EXE_ROOT)/run (for Windows instances: DIR_CT_RUN = $(DIR_EXE_ROOT)\\run ); For 7.10/7.11 set it to: DIR_CT_RUN = $(DIR_EXE_ROOT)/$(OS_UNICODE)/as400_pase_64 (for Windows: DIR_CT_RUN = $(DIR_EXE_ROOT)\\$(OS_UNICODE)\\ntamd64 )</ol><ol>4. Please remove any DLTOLDPKG call from all start profiles, because with the 7.20 Kernel DLTOLDPKG will be started by STARTSAP automatically. For further information please refer to note 1657890.</ol><ol><ol>5. Apply all archives in &lt;newkernel&gt; simultaneously to the system:</ol></ol><ol>APYSIDKRN SID(&lt;SAPSID&gt;) ARCHIVES('&lt;newkernel&gt;/*') SAVSAR(*NONE) MODE(*FULLY) CHGENV(*NO) UPDAPAR(*NO)</ol><ol><ol>6. Remove the SQL packages left over from using the old kernel:</ol></ol><ol>DLTR3PKG SID(&lt;SAPSID&gt;)</ol>\n<p><br/>Log off and then log on again with &lt;SAPSID&gt;ADM. You are now in the new 7.20 environment with the kernel library SAP&lt;sapsid&gt;IND.<br/>(Caution: The name of the kernel library is predefined after you import the 7.20 kernel and can no longer be freely selected.)<br/><br/>If not done yet, it is highly recommended to switch to the SAPCPE process for kernel maintenance now (see SAP note 1632754 for details) in order to minimize the system downtime for kernel maintenance.<br/><br/>If you change a system with a 7.10 or 7.11(7.1 Enhancement Package 1) kernel to the 7.20 kernel and have not yet changed the user concept, use the opportunity and change it now in accordance with Note 1149318 because future upgrade or update paths require the new user concept.The faster storage management with SHM_SEGS can only be used after the changeover (see Note 808607).<br/><br/></p>\n<p><strong>5. Additional steps</strong><br/><strong>5.1 CAUTION: Retroactive kernel patches</strong></p>\n<p><br/>In some executables (in particular, disp+work), errors were corrected at a later date. You must apply these kernel patches in any case. These are available in SAP Support Portal (http://service.sap.com/swdc).<br/><br/>Read Note 19466 (Downloading SAP kernel patches) or Note 1097751 for IBM i.</p>\n<p><strong>5.2 Additional manual changes in Dual-Stack and Java-only systems</strong></p>\n<p><br/>This step applies only to releases 7.00 and 7.01.</p>\n<ul>\n<li>Only necessary in case of a Dual-Stack system: you must set the following parameter in the default system profile DEFAULT.PFL:<br/>system/type = DS</li>\n</ul>\n<ul>\n<li>In all (Dnn, DVEBMGSnn, Jnn, JCnn, etc.) instance profiles, you must set the following parameter:<br/>FN_JSTART = jcontrol$(FT_EXE)</li>\n</ul>\n<p><strong>5.3 Reinstalling SAPCRYPTOLIB</strong></p>\n<p>Starting with the patch level 513, also in SP stack kernels 720 PL &gt;=600, a new CommonCryptoLib is delivered with the SAP Kernel. This library is fully compatible to the SAPCRYPTOLIB and replaces it, so the reinstallation of SAPCRYPTOLIB is no longer necessary. See the Note 1848999 for more detail.</p>\n<p><br/><strong>5.4 Reinstalling additional programs</strong></p>\n<p><br/>If you had installed additional programs such as the RFC Library, you have to install them again. To do this, proceed as follows:<br/><br/><strong>On UNIX:</strong><br/>Execute the following commands as user &lt;sapsid&gt;adm:</p>\n<ol>1. cd /usr/sap/&lt;SAPSID&gt;/SYS/exe/run</ol><ol>2. &lt;newkernel&gt;/SAPCAR -xvf &lt;newkernel&gt;/&lt;additional package&gt;.SAR</ol>\n<p><br/><strong>On Windows:</strong><br/>Execute the following commands as user &lt;SAPSID&gt;ADM:</p>\n<ol>1. CD \\USR\\SAP\\&lt;SAPSID&gt;\\SYS\\EXE\\RUN</ol><ol>2. &lt;newkernel&gt;\\SAPCAR.EXE -xvf &lt;newkernel&gt;\\&lt;additional package&gt;.SAR</ol>\n<p><br/><strong>On IBM i:</strong><br/>On IBM i, you do not have to install any further programs.</p>\n<p><br/><strong>5.4.1 Copying executable files from NetWeaver RFC SDK 720.</strong></p>\n<p><br/>Due to security reasons the program rfcexec (UNIX and IBM i) or rfcexec.exe (Windows) is no longer shipped together with SAP kernel 720. For various reasons, it is mandatory to move to a new version of these files, contained in the NW RFC SDK 720. Please refer to SAP Notes 1581595 and 1025361for detailed instructions and further information.</p>\n<p>Remark: The new version of rfcexec might break some of your application scenarios. Only in this case it is required to use the old version, which was saved in the previous preparation step (Unix, step 3 c, Windows 4 c).</p>\n<p><strong>5.5 Special features for the syslog (ABAP-only and Dual-Stack systems)</strong></p>\n<p><br/>Due to the situation described in note 1517379, you have to set the profile parameter<br/>           rslg/new_layout = 9.<br/><br/>If a syslog file already exists in the new format because this parameter has not been set from the very beginning, the syslog will still be written in the new format even if the parameter has been set in the meantime. In that case, the existing syslog files have to be deleted.</p>\n<p><strong>5.6 Dynamic work processes (NW 7.00 and NW 7.01 systems only)</strong></p>\n<p><br/>The 7.20 kernel supports the dynamic increase of the number of work processes at runtime. However, this function is not fully compatible with NW 7.00 and NW 7.01. To prevent errors from occurring, deactivate it by setting the following parameters:<br/>           rdisp/wp_no_restricted = 0<br/>           rdisp/configurable_wp_no = 0<br/>           rdisp/dynamic_wp_check = FALSE<br/>           rdisp/wp_max_no = Sum of<br/>( rdisp/wp_no_dia + rdisp/wp_no_btc + rdisp/wp_no_vb + rdisp/wp_no_vb2 + rdisp/wp_no_spo +rdisp/wp_no_enq )<br/>           Mind that rdisp/wp_max_no has the default value DEFAULT, which will add two unwanted dynamic processes if not set to the number of configured classical wp types.</p>\n<p><strong><strong>5.7 Configuration of the CCMS central monitoring</strong></strong></p>\n<p><br/>Depending on the SAP kernel release of the monitored system you upgraded from and depending on the release of the central monitoring system (CEN), it is necessary to check the following scenarios:</p>\n<ul>\n<li>You upgraded the monitored system:</li>\n</ul>\n<ul>\n<ul>\n<li>CEN system is based on SAP_BASIS 7.0 (7.00) and lower:<br/>You have to disable the integrated CCMS agents in sapstartsrv of the upgraded system and use standalone CCMS agents instead. sapccm4x for the central monitoring of pure ABAP and Dual-Stack systems, sapccmsr -j2ee for the central monitoring of pure Java systems. Please proceed as outlined in note 1368389, section \"Solution\".</li>\n</ul>\n</ul>\n<ul>\n<ul>\n<li>CEN system is based on SAP_BASIS 7.0 EhP 1 (7.01):<br/>The registration of the integrated CCMS agents from the central system needs to be repeated. Please follow the instructions given in SAP Help (http://help.sap.com/saphelp_nwpi71/helpdata/EN/44/893e933dc912d3e10000000a422035/content.htm).</li>\n</ul>\n</ul>\n<ul>\n<li>You upgraded the central system:<br/>No reconfiguration is necessary.</li>\n</ul>\n<p><br/><strong>SAP note 1667336</strong> contains a detailed description for every case.</p>\n<p><strong>5.8. Check your environment on Linux and UNIX platforms</strong></p>\n<p>On all UNIX/Linux platforms with deactivated interactive user logon for &lt;sidadm&gt; user - check Note 1825259.</p>\n<p><strong>6 System Start</strong></p>\n<p><br/>Start the SAP system with the new kernel and check the poins below if relevant.</p>\n<p><strong><strong>6.1 AS Java or Dual-Stack systems with release 7.00 or 7.01</strong></strong></p>\n<p><br/>If the Java instance or even the complete system does not start, check the following points:</p>\n<ul>\n<li>The minimum support packages and patch levels have been installed as described in section \"Attention\" under \"Reason and Prerequisites\".</li>\n</ul>\n<ul>\n<li>sapjvm: The directory jvm or sapjvm or the file protect.lst have been saved and restored as described in</li>\n</ul>\n<ul>\n<ul>\n<li>Section 4.1, \"On Unix\", steps 3 and 9</li>\n</ul>\n</ul>\n<ul>\n<ul>\n<li>Section 4.2, \"On Windows\", step 4</li>\n</ul>\n</ul>\n<p><strong>6.2 DDIC_TYPELENG_INCONSISTENT short dumps</strong></p>\n<p><br/>If you did not handle the DDIC_TYPELENG_INCONSISTENT issue, which is described in chapter \"Reason and Prerequisites\", section \"Attention\", while the original kernel was still running, you should now install Note 1610716.</p>\n<p><strong>6.3 Load format of ABAP programs</strong></p>\n<p><br/>After you start the SAP system with the new kernel, the following message is displayed in the system log: 'Load format of PROGRAM not valid.'<br/>You can ignore this message because the load is automatically regenerated.<br/>It is also possible to regenerate these ABAP loads directly using transaction SGEN. For more information see Note 185745.</p>\n<p><strong><strong>6.4 </strong>CCMS Monitoring issues</strong></p>\n<p><br/>In case of any issues with the CCMS monitoring after the kernel upgrade, check note 1667336, in particular the section \"Local monitoring in the upgraded double-stack system\".-</p></div>", "noteVersion": 73}, {"note": "822239", "noteTitle": "822239 - FAQ: SAP MaxDB interfaces", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>This SAP Note is a collection of questions that are often asked by colleagues and customers regarding the SAP MaxDB interfaces. It provides answers and refers you to other information sources.<br/>This SAP Note does not claim to be exhaustive.</p>\n<ol>1. What are SAP MaxDB interfaces?</ol><ol>2. What SAP MaxDB interfaces are there?</ol><ol>3. What is the SAP MaxDB precompiler or the precompiler runtime?</ol><ol>4. Which version of the precompiler runtime is used in which SAP releases?</ol><ol>5. Does the precompiler runtime also have to be installed on the application server?</ol><ol>6. How can I determine which versions of the precompiler runtime are installed on a host?</ol><ol>7. Can I also use irconf to determine the installed precompiler runtime versions?</ol><ol>8. Where does irconf receive the information from?</ol><ol>9. How can I register a precompiler runtime version?</ol><ol>10. How can I determine which precompiler runtime is currently in use in the SAP system?</ol><ol>11. How do I install or update the precompiler runtime?</ol><ol>12. How do I activate or deactivate the precompiler trace?</ol><ol>13. What is the SQLDBC interface?</ol><ol>14. Which version of the SQLDBC runtime is used in which SAP releases?</ol><ol>15. Does the SQLDBC runtime also have to be installed on the application server?</ol><ol>16. How can I determine which versions of the SQLDBC runtime are installed on a host?</ol><ol>17. How can I determine which version of the SQLDBC runtime is currently in use?</ol><ol>18. How can I determine which version an SAP MaxDB client library has?</ol><ol>19. How do I install or upgrade the SQLDBC runtime?</ol><ol>20. How do I activate or deactivate the SQLDBC trace?</ol><ol>21. What is the ODBC interface?</ol><ol>22. What is the ODBC interface used for in the SAP environment?</ol><ol>23. How can I determine which ODBC version is installed on the host?</ol><ol>24. How do I install or upgrade the SAP MaxDB ODBC driver?</ol><ol>25. How can I create an SQL trace on the SAP Content Server?</ol><ol>26. What is the JDBC interface?</ol><ol>27. What is the JDBC interface used for in the SAP environment?</ol><ol>28. How can I determine which JDBC version is installed on the host?</ol><ol>29. How do I install or upgrade the SAP MaxDB JDBC driver?</ol><ol>30. How do I activate the JDBC trace?</ol><ol>31. You want to increase the number of J2EE instances. What do I have to consider in the database?</ol><ol>32. Where can I find additional information about SAP MaxDB interfaces?</ol>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>FAQ, precompiler, ODBC, SQLDBC, JDBC, interfaces, could not load libpcr, ODBC trace</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>You use SAP MaxDB as of Version 7.5 in an OLTP, BW or SAP liveCache environment.<br/><br/>For more FAQ notes for SAP MaxDB/liveCache, see <a href=\"https://wiki.scn.sap.com/wiki/x/GkM\" target=\"_blank\">SAP MaxDB FAQ Notes</a>.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<ol>\n<li>What are SAP MaxDB interfaces?<br/><br/>Database applications (such as SAP systems, for example) use interfaces to access SAP MaxDB databases. If the database application (and therefore the SAP MaxDB interface) is located on a different host to the database, the database system also requires the SAP MaxDB X server (communications server) for communication purposes. The JDBC interface also requires the SAP MaxDB X server for local communication.<br/><br/></li>\n<li>What SAP MaxDB interfaces are there?<br/><br/>In the SAP MaxDB documentation (see SAP Note <a href=\"/notes/767598\" target=\"_blank\">767598</a>), a list of all SAP MaxDB interfaces is available under the key word \"Interface\". In this note, only the interfaces that are used in the SAP environment are explained in greater detail:</li>\n<ul>\n<li>C-Precompiler</li>\n<li>SQL Database Connectivity (SQLDBC): Object-oriented C++ interface for accessing the SAP MaxDB database system</li>\n<li>ODBC (3.5.1): SAP MaxDB ODBC driver for accessing the SAP MaxDB database system</li>\n<li>JDBC (2.0/3.0): SAP MaxDB JDBC driver for accessing the SAP MaxDB database system<br/><br/></li>\n</ul>\n<li>What is the SAP MaxDB precompiler or the precompiler runtime?<br/><br/>The interface between the SAP MaxDB database system and application programs (such as the SAP kernel) is the database language SQL (Structured Query Language). SQL statements embedded into the application program (Embedded SQL) allow communication with the database. Parameter values are exchanged through special program variables, which are known as host variables.<br/><br/>The C/C++ Precompiler prepares C/C++ source code with embedded SQL statements for the compilation into an executable application program. It checks the embedded statements syntactically and semantically, converts them into calls of precompiler runtime procedures and creates a C/C++ file, which can then be compiled.<br/><br/>When the application program is running, the precompiler runtime assumes the following functions:<br/><ul>\n<li>Setting up the database connections and opening the database sessions according to the specified connection options</li>\n<li>Assigning parameter values</li>\n<li>Converting data types</li>\n<li>Executing the precompiler statements</li>\n<li>Signaling NULL values (undefined values) using indicator variables</li>\n<li>Displaying confirmations from the database system in the structure sqlca</li>\n<li>Writing the trace file according to the specified trace options</li>\n</ul><br/>In the SAP environment, the precompiler runtime is required for the database-dependent part of the SAP kernel, which is known as the DBSL (dbadaslib).<br/><br/></li>\n<li>Which version of the precompiler runtime is used in which SAP releases?<br/><br/>The precompiler runtime (libpcr) versions are used depending on the SAP release but independent of the SAP MaxDB version.</li>\n<ol>\n<li>31I extended SAP kernel<br/>The precompiler runtime version required by the DBSL (dbadaslib) is 7.3.00, &gt;= Build 33 (on Microsoft Windows), &gt;= Build 26 (on UNIX).</li>\n<li>40B extended SAP kernel<br/>The precompiler runtime version required by the DBSL (dbadaslib) is 7.3.00, &gt;= Build 33 (under Microsoft Windows), &gt;= Build 26 (under Unix).</li>\n<li>45B extended SAP kernel (including APO 1.1, APO 2.0)<br/>The precompiler runtime version required by the DBSL (dbadaslib) is in all cases 7.3.00, &gt;= Build 33 (on Microsoft Windows), or &gt;= Build 26 (on Unix).</li>\n<li>46D extended SAP kernel (including APO 3.0 and APO 3.1)<br/>The precompiler runtime versions required by the DBSL (dbadaslib) are:<br/>- 7.3.01 &gt;= Build 10 (Microsoft Windows NT and UNIX)<br/>- 7.4.03 &gt;= Build 5 (Microsoft Windows NT IA64)<br/>- 7.4.03 &gt;= Build 17 (Linux IA64)<br/>- 7.4.03 &gt;= Build 26 (HP IA64)<br/>- 7.5.00 &gt;= Build 16 (Linux AMD X86_64)</li>\n<li>46D extended 2 SAP kernel<br/>The precompiler runtime versions required by the DBSL (dbadaslib) are:<br/>- 7.5.00 &gt;= Build 47 (Microsoft Windows NT)<br/>- 7.5.00 &gt;= Build 46 (Microsoft Windows NT IA64 and UNIX)</li>\n<li>SAP kernel Release 6.20 Basis, 4.70 (including APO 4.0, Web AS 6.20 and 6.30)<br/>The precompiler runtime versions required by the DBSL (dbadaslib) are:<br/>- 7.3.01 &gt;= Build 10 (Microsoft Windows NT and UNIX)<br/>- 7.4.03 &gt;= Build 5 (Microsoft Windows NT IA64)<br/>- 7.4.03 &gt;= Build 17 (Linux IA64)<br/>- 7.4.03 &gt;= Build 26 (HP IA64)</li>\n<li>SAP kernel Release 6.40 Basis, 6.40 AKK (including NetWeaver 04, Web AS 6.40, SCM 4.1, and 6.30 J2EE)<br/>The precompiler runtime versions required by the DBSL (dbadaslib) are:<br/>- 7.4.03 &gt;= Build 11 (Microsoft Windows NT and UNIX)<br/>- 7.4.03 &gt;= Build 17 (Linux IA64)<br/>- 7.4.03 &gt;= Build 26 (HP IA64)<br/>- 7.5.00 &gt;= Build 16 (LINUX PPC64 and LINUX AMD64)<br/>- 7.5.00 &gt;= Build 15 (LINUX s390)<br/>- 7.5.00 &gt;= Build 24 (Microsoft Windows NT AMD64)</li>\n<li>SAP kernel Release 6.40 ext 2<br/>As of this SAP kernel release, the SQLDBC interface is used instead of the precompiler runtime. The assignment of the SAP kernel to the relevant SQLDBC versions can be found under point 14 in this SAP Note.<br/><br/></li>\n</ol>\n<li>Does the precompiler runtime also have to be installed on the application server?<br/><br/>Yes. You must install the precompiler runtime on each application server and on the database server if you are using one of the aforementioned SAP kernels.<br/><br/></li>\n<li>How can I determine which versions of the precompiler runtime are installed on a host?<br/><br/>You can use the program <span class=\"SNO_DNT\" translate=\"no\">SDBREGVIEW</span> to determine which SAP MaxDB software components are installed on the corresponding host. If precompiler runtime versions are installed on this host, they are displayed as follows.</li>\n<ol>\n<li>On UNIX platforms:<br/><br/>At operating system level, start: <em><span class=\"SNO_DNT\" translate=\"no\">﻿sdbregview -l | grep -i PCR</span><br/></em>You receive a list of the precompiler versions installed on this host.<br/><br/>Example:<br/><span class=\"SNO_DNT\" translate=\"no\"><em>﻿PCR 7300  /program files/sdb/programs  *********           valid</em></span><br/><span class=\"SNO_DNT\" translate=\"no\"><em>PCR 7301  /program files/sdb/programs  *********          valid</em></span><br/><span class=\"SNO_DNT\" translate=\"no\"><em>PCR 7500  /program files/sdb/programs  *********  32 bit   valid</em></span><br/><span class=\"SNO_DNT\" translate=\"no\"><em>PCR 7104  /program files/sdb/programs  *********           valid</em></span><br/><span class=\"SNO_DNT\" translate=\"no\"><em>PCR 7240  /program files/sdb/programs  *********          valid</em></span><br/><span class=\"SNO_DNT\" translate=\"no\"><em>PCR 7600  /program files/sdb/programs  *********  32 bit   valid</em></span><br/><span class=\"SNO_DNT\" translate=\"no\"><em>PCR 7250  /program files/sdb/programs  *********          valid</em></span><br/><span class=\"SNO_DNT\" translate=\"no\"><em>PCR 7403  /program files/sdb/programs  *********  32 bit   valid</em></span><br/><br/></li>\n<li>On Microsoft Windows platforms:<br/><br/>At operating system level, start: <em><span class=\"SNO_DNT\" translate=\"no\">﻿sdbregview -l | find /I \"PCR\"</span><br/></em>You receive a list of the precompiler runtime versions installed on this host.<br/><br/>Example:<br/><span class=\"SNO_DNT\" translate=\"no\"><em>﻿PCR 7300 c:/program files/sdb/programs  *********      valid</em></span><br/><span class=\"SNO_DNT\" translate=\"no\"><em>PCR 7301 c:/program files/sdb/programs  *********      valid</em></span><br/><span class=\"SNO_DNT\" translate=\"no\"><em>PCR 7500 c:/program files/sdb/programs  *********  32 bit valid</em></span><br/><span class=\"SNO_DNT\" translate=\"no\"><em>PCR 7104 c:/program files/sdb/programs  *********       valid</em></span><br/><br/></li>\n</ol>\n<li>Can I also use irconf to determine the installed precompiler runtime versions?<br/><br/>Yes, you can use the call <span class=\"SNO_DNT\" translate=\"no\"><em>﻿irconf -a﻿</em></span> to determine which precompiler runtime versions are installed on a host. For example, the system issues the following output when you call an <span class=\"SNO_DNT\" translate=\"no\"><em>﻿irconf﻿</em></span> of SAP MaxDB Version 7.5 on UNIX:<br/><br/><span class=\"SNO_DNT\" translate=\"no\"><em>﻿/sapdb/programs/runtime/7500 -&gt; ******* from global registration</em></span><br/><span class=\"SNO_DNT\" translate=\"no\"><em>/sapdb/programs/runtime/7240 -&gt; ******* from old registration</em></span><br/><span class=\"SNO_DNT\" translate=\"no\"><em>/sapdb/programs/runtime/7250 -&gt; ******* from old registration</em></span><br/><span class=\"SNO_DNT\" translate=\"no\"><em>/sapdb/programs/runtime/7300 -&gt; ******* from old registration</em></span><br/><span class=\"SNO_DNT\" translate=\"no\"><em>/sapdb/programs/runtime/7301 -&gt; ******* from old registration</em></span><br/><span class=\"SNO_DNT\" translate=\"no\"><em>/sapdb/programs/runtime/7403 -&gt; ******* from old registration﻿</em></span><br/><br/><strong>Note the following:</strong> You must also specify the option <span class=\"SNO_DNT\" translate=\"no\"><em>﻿-g﻿</em></span> if you want to see cross-user registrations.<br/><br/></li>\n<li>Where does irconf receive the information from?<br/><br/>The storage of information changed on UNIX between SAP MaxDB Version 7.4 and SAP MaxDB Version 7.5.</li>\n<ol>\n<li>Up to and including precompiler version 7.4<br/><br/><span class=\"SNO_DNT\" translate=\"no\"><em>﻿/sapdb/programs/runtime/7240 -&gt; ******* from old registration</em></span><br/><span class=\"SNO_DNT\" translate=\"no\"><em>/sapdb/programs/runtime/7250 -&gt; ******* from old registration</em></span><br/><span class=\"SNO_DNT\" translate=\"no\"><em>/sapdb/programs/runtime/7300 -&gt; ******* from old registration</em></span><br/><span class=\"SNO_DNT\" translate=\"no\"><em>/sapdb/programs/runtime/7301 -&gt; ******* from old registration</em></span><br/><span class=\"SNO_DNT\" translate=\"no\"><em>/sapdb/programs/runtime/7403 -&gt; ******* from old registration﻿</em></span><br/><br/>The information regarding which versions are installed on a host is determined from the file <span class=\"SNO_DNT\" translate=\"no\"><em>﻿SAP_DBTECH.ini﻿</em></span> in the directory <span class=\"SNO_DNT\" translate=\"no\"><em>﻿/usr/spool/sql/ini/﻿</em></span>.<br/><br/>On Microsoft Windows, the information is determined from the registry at <span class=\"SNO_DNT\" translate=\"no\"><em>﻿HKLM\\software\\SAP\\SAP DBTECH﻿</em></span>. If an entry is missing in this file or in the registry, or if the file is missing, the system cannot find the precompiler runtime. The following error message, for example, is displayed:<br/><br/><em><span class=\"SNO_DNT\" translate=\"no\">﻿Could not load libpcr (No registered version (7.4.3)</span><br/></em></li>\n<li>As of precompiler Version 7.5<br/><br/><span class=\"SNO_DNT\" translate=\"no\"><em>﻿/sapdb/programs/runtime/7500 -&gt; ******* from global registration﻿</em></span> <br/>On Unix, the information is determined from the file <span class=\"SNO_DNT\" translate=\"no\"><em>﻿runtimes.ini﻿</em></span> in the directory <span class=\"SNO_DNT\" translate=\"no\"><em>﻿&lt;indep_data&gt;/config﻿</em></span>.<br/><br/>On Microsoft Windows, the information is determined from the registry at <span class=\"SNO_DNT\" translate=\"no\"><em>﻿HKEY_LOCAL_MACHINE: Software\\SAP\\SAP DBTech</em> </span>:<br/><br/><strong>Comment:</strong><br/>This new storage is known only by SAP kernel 6.40 on the following platforms:<br/>- Linux: X86_64, PPC64, S390x<br/>- Microsoft Windows X86_6<br/><br/>SAP kernels of 6.40 or lower on all other platforms do NOT know this new storage, and these kernels therefore search only for the old registration.<br/><br/></li>\n</ol>\n<li>How can I register a precompiler runtime version?<br/><br/>Using the command , <span class=\"SNO_DNT\" translate=\"no\"><em>﻿irconf -i -p /sapdb/programs/runtime/7403﻿</em></span>, precompiler runtime Version 7.4.03 can be reregistered, for example.<br/>Here, the version (&lt; 7. 5 or &gt;= 7.5) of \"irconf\" determines where the precompilers are registered.<br/><br/>Example: A precompiler runtime with version 7.4.03 is registered with an \"irconf\" version 7.4 and registration then occurs in <span class=\"SNO_DNT\" translate=\"no\"><em>﻿SAP_DBTech.ini﻿</em></span>. If registration occurs with an <span class=\"SNO_DNT\" translate=\"no\"><em>﻿irconf﻿</em></span> of Version 7.5 or higher, the precompiler runtime Version 7.4.03 is registered at <span class=\"SNO_DNT\" translate=\"no\"><em>﻿/sapdb/programs/runtime/7.4.03﻿</em></span>.<br/><br/></li>\n<li>How can I determine which precompiler runtime is currently in use in the SAP system?<br/><br/>The version of the precompiler runtime that is currently being used by the SAP application is logged on each application server in the developer trace logs (dev_w* files). In transaction SM50, select a work process and choose <em>Process -&gt; Trace -&gt; Display file</em> to go to the log. In the log, search for the following string:<br/><br/><span>C  Precompiler Runtime:<br/></span>Example:<br/><span class=\"SNO_DNT\" translate=\"no\"><em>﻿Precompiler Runtime : C-PreComp 7.3.1    Build 011-000-091-899﻿</em></span><br/>Enter this version specification in the customer message to SAP if there is an error message.<br/><br/></li>\n<li>How do I install or update the precompiler runtime?<br/><br/>The precompiler runtime is part of the SAP MaxDB client software package. To install or upgrade the SAP MaxDB precompiler runtime, use SAP Note <a href=\"/notes/649814\" target=\"_blank\">649814</a>.<br/><br/></li>\n<li>How do I activate or deactivate the precompiler trace?</li>\n<ul>\n<li>SAP Basis Release &lt;= 4.6D or precompiler versions &lt; 7.2.04:<br/><br/>The activation of the precompiler trace is carried out using a parameter in the instance profile of the SAP system (file <span class=\"SNO_DNT\" translate=\"no\"><em>﻿&lt;SID&gt;_DVEBMGS&lt;instance_number&gt;_&lt;computer_name&gt;﻿</em></span> in the directory <span class=\"SNO_DNT\" translate=\"no\"><em>﻿/usr/sap/&lt;SID&gt;/SYS/profile/﻿</em></span>). After you have set the parameter, you must restart the SAP system, or at least the application server for which you are activating the trace. <em>*.pct</em> files are then written in the work directory of the SAP system.<br/><br/>The following options exist:<br/><br/><span class=\"SNO_DNT\" translate=\"no\"><em>﻿dbs/ada/sql_trace = 2</em></span><br/>This is a detailed trace; the PCT files may become very large, but there is no risk of the required information being overwritten.<br/><br/><span class=\"SNO_DNT\" translate=\"no\"><em>﻿dbs/ada/sql_trace = &lt;X&gt;﻿</em></span><br/>&lt;X&gt;: Number above 2: This is an alternate trace; X commands are logged for each PCT file, and then the relevant file is overwritten. There is no risk of the file system becoming full (if you do not select too large a number for X), but if the trace is not deactivated in time or the file is saved, the error may have already been overwritten when the analysis is carried out.<br/><br/></li>\n<li>Irtrace (precompiler Version 7.2.04 and higher (as of SAP Basis 6.10))<br/><br/>As in the earlier versions, the precompiler trace can also be activated by setting the profile parameter. However, you can also activate the trace using \"irtrace\" WITHOUT having to restart the system or the application server.<br/><br/>The tool offers the following options for changing the behavior of the trace:<br/><br/>Activating/deactivating/changing the trace for a particular process:<br/><em><span class=\"SNO_DNT\" translate=\"no\">﻿irtrace -p &lt;process_id&gt; -t &lt;trace_type&gt;</span><br/><br/></em>The following trace types <span class=\"SNO_DNT\" translate=\"no\"><em>﻿&lt;trace_type&gt;﻿</em></span> are available:<br/>-<span class=\"SNO_DNT\" translate=\"no\"><em> ﻿long﻿</em></span> : long trace,<br/>- <span class=\"SNO_DNT\" translate=\"no\"><em>﻿short﻿</em></span> : short trace<br/>- <span class=\"SNO_DNT\" translate=\"no\"><em>﻿off﻿</em></span> : deactivate trace<br/><br/>For more information about the precompiler trace, see the support guide (SAP Note <a href=\"/notes/692274\" target=\"_blank\">692274</a>).<br/><br/></li>\n</ul>\n<li>What is the SQLDBC interface?<br/><br/>Up to and including Database Version 7.5, the precompiler served as an interface between SAP MaxDB databases and C/C++ application programs. As of Database Version 7.6, the SQLDBC interface replaces the precompiler.<br/><br/>SQL Database Connectivity (SQLDBC) is a runtime library for the development of database applications and database interfaces for SAP MaxDB. Applications can use SQLDBC to access SAP MaxDB databases, execute SQL statements and edit data. SQLDBC consists of the runtime library <span class=\"SNO_DNT\" translate=\"no\"><em>﻿libSQLDBC</em></span>, the software development kit SQLDBC SDK and the tool <span class=\"SNO_DNT\" translate=\"no\"><em>﻿sqldbc_cons</em></span>.<br/><br/>The runtime library <span class=\"SNO_DNT\" translate=\"no\"><em>﻿libSQLDBC</em></span> is also called SQLDBC runtime. In the SAP environment, the SQLDBC runtime is required for the database-dependent part of the SAP kernel, which is known as the DBSL (<span class=\"SNO_DNT\" translate=\"no\"><em>﻿dbsdbslib</em></span>).<br/><br/></li>\n<li>Which SQLDBC version is used in which SAP releases?<br/><br/>As of SAP kernel Release 6.40 EXT-2, the precompiler runtime has been replaced with the SQLDBC interface. The SQLDBC runtime (<span class=\"SNO_DNT\" translate=\"no\"><em>﻿libSQLDBC</em></span>) versions are used depending on the SAP release but independent of the SAP MaxDB core version.<br/><br/>As of SAP Release 7.00, <span class=\"SNO_DNT\" translate=\"no\">DBADASLIB</span> is replaced by <span class=\"SNO_DNT\" translate=\"no\">DBSDBSLIB</span>. In general, we recommend that you always install the most current SQLDBC runtime.<br/><br/></li>\n<ul>\n<li>SAP kernel Release 6.40 EXT-2<br/>The SQLDBC runtime is delivered for the first time in SAP kernel Release 6.40 EXT-2. This downward-compatible SAP kernel is used with a DBSL (dbsdbslib) that requires the following minimum SQLDBC runtime version:<br/>7.6.06 &gt;= Build 22 for all available platforms<br/><br/></li>\n<li>SAP kernel Release 7.00 (including NW04s, NW 7.00 Support Package x, 6.45 J2EE and SCM 5.0)<br/>The minimum SQLDBC runtime versions required by the DBSL (dbsdbslib) are:<br/>7.6.06 &gt;= Build 22 for all available platforms<br/><br/></li>\n<li>SAP kernel Release 7.01 (including NW 7.00 Enhancement Package 1)<br/>The minimum SQLDBC runtime versions required by the DBSL (dbsdbslib) are:<br/>7.6.06 &gt;= Build 22 for all available platforms<br/><br/></li>\n<li>SAP kernel Release 7.10 (including NW05, NW 7.10 Support Package x and SCM 6.0)<br/>The minimum SQLDBC runtime versions required by the DBSL (dbsdbslib) are:<br/>7.6.06 &gt;= Build 22 for all available platforms<br/><br/></li>\n<li>SAP kernel Release 7.11 (including NW 7.11, NW 7.1 Enhancement Package x)<br/>The minimum SQLDBC runtime versions required by the DBSL (dbsdbslib) are:<br/>7.7.07 &gt;= Build 41 for all available platforms<br/><br/></li>\n<li>SAP kernel Release 7.2x (incl. 7.20 EXT, 7.21 EXT, NW 7.02, NW 7.03, NW 7.20, NW 7.3x)<br/>The minimum SQLDBC runtime versions required by the DBSL (dbsdbslib) are:<br/>7.7.07 &gt;= Build 41 for all available platforms<br/>7.8.01 &gt;= Build 04 for AIX<br/>7.8.02 &gt;= Build 17 for Linux AMD X86_64 and Microsoft Windows NT<br/><br/></li>\n<li>SAP kernel Release 7.40 (including NW 7.4 SP2, SP3, and SP4)<br/>The minimum SQLDBC runtime versions required by the DBSL (dbsdbslib) are:<br/>7.7.07 &gt;= Build 41 for all available platforms<br/>7.8.01 &gt;= Build 04 for AIX.<br/>7.8.02 &gt;= Build 17 for Linux AMD X86_64 and Microsoft Windows NT<br/><br/></li>\n<li>SAP kernel Release 7.41 (including NW 7.4 SP5)<br/>The minimum SQLDBC runtime versions required by the DBSL (dbsdbslib) are:<br/>7.7.07 &gt;= Build 41 for all available platforms except AIX.<br/>7.8.01 &gt;= Build 04 for AIX.<br/>7.8.02 &gt; = Build 17 for Linux AMD X86_64 Microsoft Windows NT<br/><br/></li>\n<li>SAP kernel Release 7.42 <br/>The minimum SQLDBC runtime versions required by the DBSL (dbsdbslib) are:<br/>7.9.08 &gt;= Build 18 for all available platforms<br/><br/></li>\n<li>SAP Kernel Release 7.45/7.49/7.53<br/>The minimum SQLDBC runtime versions required by the DBSL (dbsdbslib) are:<br/>7.9.08 &gt;= Build 31 for all available platforms<br/><br/></li>\n<li>SAP kernel Release 8.0x (incl. 8.03, 8.04, 8.05, NGAP)<br/>The SQLDBC runtime versions required by the DBSL (dbsdbslib) are:<br/>7.9.03 &gt;= Build 4 for all available platforms<br/><br/></li>\n</ul>\n<li>Does the SQLDBC runtime also have to be installed on the application server?<br/><br/>Yes, SQLDBC runtime must be installed on all application servers and the database server. In general, we recommend that you always install the most current SQLDBC runtime.<br/><br/></li>\n<li>How can I determine which versions of the SQLDBC runtime are installed on a host?<br/><br/>You can use the program <span class=\"SNO_DNT\" translate=\"no\">SDBREGVIEW</span> to determine which SQLDBC versions are installed on the relevant host.<br/><br/><span><strong>On Unix platforms:</strong></span><em><br/></em>At operating system level, start: <span class=\"SNO_DNT\" translate=\"no\"><em>﻿sdbregview -l | grep -i SQLDBC﻿</em></span><br/>The system displays a list of the SQLDBC versions that are installed on the host.<br/>Example:<br/><em><span class=\"SNO_DNT\" translate=\"no\">﻿SQLDBC  /program files/sdb/programs  *********     valid</span><br/><br/></em><span><strong>On Microsoft Windows platforms:<br/></strong></span>At operating system level, start: <span class=\"SNO_DNT\" translate=\"no\"><em>﻿sdbregview -l | find /I \"SQLDBC\"﻿</em></span><br/>Example:<br/><em><span class=\"SNO_DNT\" translate=\"no\">﻿SQLDBC  c:/program files/sdb/programs  *********  32 bit valid</span><br/><br/></em></li>\n<li>How can I determine which version of the SQLDBC runtime is currently in use?<br/><br/>The SQLDBC version that is currently being used by the SAP application is logged on each application server in the developer trace logs (<em>dev_w*</em> files). In transaction SM50, select a work process and choose <em>Process -&gt; Trace -&gt; Display file</em> to go to the log. In the log, search for the following character strings:<br/><span class=\"SNO_DNT\" translate=\"no\"><em>﻿C  SQLDBC SDK Version :</em></span><br/><span class=\"SNO_DNT\" translate=\"no\"><em>C  SQLDBC Library Version :</em></span><br/><em><span class=\"SNO_DNT\" translate=\"no\">C  SQLDBC client runtime is</span><br/><br/></em>Example:<br/><span class=\"SNO_DNT\" translate=\"no\"><em>﻿SQLDBC SDK Version : SQLDBC. H  7.6.0    BUILD 002-121-083-965</em></span><br/><span class=\"SNO_DNT\" translate=\"no\"><em>SQLDBC library Version : libSQLDBC 7.6.6    BUILD 022-123-109-428</em></span><br/><em><span class=\"SNO_DNT\" translate=\"no\">SQLDBC client runtime is MaxDB 7.6.6.022 CL 109428</span><br/><br/></em>If you report an error to SAP, you provide the version specification in the customer incident.<br/><br/></li>\n<li>How can I determine which version an SAP MaxDB client library has?<br/><br/>Switch to the directory that contains the library whose version you want to determine, for example <span class=\"SNO_DNT\" translate=\"no\"><em>﻿/sapdb/clients/&lt;SID&gt;/lib﻿</em></span> (version &gt;= 7.8).<br/><br/>Use the following command: <em><span class=\"SNO_DNT\" translate=\"no\">﻿sqlwhat &lt;library_name&gt; -i Build</span><br/><br/></em>Example:<br/><em><span class=\"SNO_DNT\" translate=\"no\">﻿sqlwhat libSQLDBC76. so -i Build</span><br/></em>Output:<br/><em>:</em><br/><em><span class=\"SNO_DNT\" translate=\"no\">﻿Rel. 7.6.6 Build: 022-123-241-261</span><br/></em>The version is displayed in the output.<br/><br/></li>\n<li>How do I install or upgrade the SQLDBC runtime?<br/><br/>To install or upgrade the SQLDBC runtime, use SAP Note <a href=\"/notes/649814\" target=\"_blank\">649814</a>.<br/><br/></li>\n<li>How do I activate or deactivate the SQLDBC trace?<br/><br/>An SQLDBC trace contains the SQL statements that are sent to the database core by the application, their parameters and the results. All SQLDBC traces contain profile and time stamp information, among other things.<br/><br/>The trace files are stored in the directory <span class=\"SNO_DNT\" translate=\"no\"><em>﻿&lt;independent_data_path&gt;/wrk﻿</em></span> (Microsoft Windows) or <span class=\"SNO_DNT\" translate=\"no\"><em>﻿&lt;user_home&gt;/.sdb﻿</em></span> (UNIX/Linux) as <span class=\"SNO_DNT\" translate=\"no\"><em>﻿sqldbctrace-&lt;pid&gt;.prt﻿</em></span> files. <em>&lt;pid&gt;</em> is the process ID. The files are cyclically overwritten.<br/><br/>Use the tool <span class=\"SNO_DNT\" translate=\"no\">SQLDBC_CONS</span> to configure, start and stop the SQLDBC traces. Start <span class=\"SNO_DNT\" translate=\"no\">SQLDBC_CONS</span> without additional options, so that all SQLDBC trace options (and commands) are displayed.<br/>You can also change the name and the path of the file. You can use the trace command <span class=\"SNO_DNT\" translate=\"no\">SHOW ALL</span> or <span class=\"SNO_DNT\" translate=\"no\">SHOW CONFIG</span> to display the current setting.<br/>For more information about the SQLDBC trace, see the support guide (SAP Note <a href=\"/notes/692274\" target=\"_blank\">692274</a>).<br/><br/>In Microsoft Windows, the R/3 processes usually run as <span class=\"SNO_DNT\" translate=\"no\"><em>﻿SAPService&lt;SID&gt;﻿</em></span>. Therefore, you must specify the -u option when you activate the trace at operating system level: <em><span class=\"SNO_DNT\" translate=\"no\">﻿sqldbc_cons -u SAPService&lt;SID&gt; TRACE SQL ON</span><br/></em>This is not required in the Database Assistant (transaction DB50) or in transaction SM49, since the environment of the <span class=\"SNO_DNT\" translate=\"no\"><em>﻿SAPService&lt;SID&gt;﻿</em></span> already exists there.<br/><br/>Note that for MDM (Master Data Management) installations, the user <span class=\"SNO_DNT\" translate=\"no\"><em>﻿SAPService&lt;SID&gt;﻿</em></span> is entered in the group <span class=\"SNO_DNT\" translate=\"no\"><em>﻿SBD Operators﻿</em></span> or the group for <em>local administrators</em>.<br/><br/></li>\n<li>What is the ODBC interface?<br/><br/>To access the database system SAP MaxDB using the ODBC, you can use the SAP MaxDB ODBC driver. The SAP MaxDB ODBC driver allows you to access SAP MaxDB databases using the ODBC interface. You can use the SAP MaxDB ODBC driver on all operating systems that are supported by the database system.<br/><br/><br/><div class=\"table-responsive\"><table border=\"1\" cellpadding=\"3\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td><strong>Operating system</strong></td>\n<td><strong>ODBC driver/ASCII</strong></td>\n<td><strong>ODBC driver/UNICODE</strong></td>\n</tr>\n<tr>\n<td>Microsoft Windows</td>\n<td><span class=\"SNO_DNT\" translate=\"no\"><em>﻿sdbodbc.dll﻿</em></span></td>\n<td><span class=\"SNO_DNT\" translate=\"no\"><em>﻿sdbodbcw.dll﻿</em></span></td>\n</tr>\n<tr>\n<td>UNIX/Linux</td>\n<td><span class=\"SNO_DNT\" translate=\"no\"><em>﻿libsdbodbc.a|so﻿</em></span></td>\n<td><span class=\"SNO_DNT\" translate=\"no\"><em>﻿libsdbodbcw.a|so﻿</em></span></td>\n</tr>\n</tbody>\n</table></div>Up to SAP MaxDB 7.6, the name of the ODBC driver was <span class=\"SNO_DNT\" translate=\"no\"><em>﻿sqlod32</em></span> (Microsoft Windows) or <span class=\"SNO_DNT\" translate=\"no\"><em>﻿libsqlod</em></span> (Unix/Linux).<br/> <br/>The SAP MaxDB ODBC driver is a part of the SAP MaxDB software. The SAP MaxDB ODBC driver supports the complete ODBC SQL syntax. For detailed information about the ODBC function calls, see the <span class=\"SNO_DNT\" translate=\"no\"><em>﻿ODBC Programmer's Reference﻿</em></span> documentation at <span class=\"SNO_DNT\" translate=\"no\"><em>﻿msdn.microsoft.com﻿</em></span>.<br/><br/>In order to use the SAP MaxDB ODBC driver, you have the following options:<br/>Registering the SAP MaxDB ODBC driver in a driver manager. <br/><br/>You can use the following driver managers:</li>\n<ul>\n<li>Microsoft Windows: Microsoft ODBC Driver Manager 3.52 or higher</li>\n<li>UNIX/Linux: unixODBC 2.0.9 or above, ODBC 3.0.5 or above</li>\n<li>UNIX/Linux: Link custom-developed ODBC applications directly with the SAP MaxDB ODBC driver<br/><br/>Prerequisite:</li>\n<li>On Microsoft Windows, you require Microsoft Data Access Components (MDAC) Version 2.7 or higher. You have installed the SAP MaxDB ODBC driver.</li>\n<li>Under Unix/Linux, it is not necessary to install a driver manager. On Microsoft Windows, a driver manager is generally part of the operating system installation.<br/><br/></li>\n</ul>\n<li>What is the ODBC interface used for in the SAP environment?<br/><br/>The SAP Content Server, which is a server component of the Knowledge Provider (Kpro), uses ODBC as an interface for SAP MaxDB.<br/>The SAP MaxDB Database Analyzer requires ODBC in order to connect to the SAP MaxDB database.<br/><br/></li>\n<li>How can I determine which ODBC version is installed on the host?<br/><br/>You can use the program <span class=\"SNO_DNT\" translate=\"no\">SDBREGVIEW</span> to determine which ODBC versions are installed on the relevant host.<br/><br/><span>On Unix platforms:<br/></span>At operating system level, start: <span class=\"SNO_DNT\" translate=\"no\"><em>﻿sdbregview -l | grep -i ODBC﻿</em></span><br/>The system displays the version of the ODBC driver that is installed on the host.<br/>Example:<br/><em><span class=\"SNO_DNT\" translate=\"no\">﻿ODBC  /program files/sdb/programs  *********     valid</span><br/><br/></em><span>On Microsoft Windows platforms:<br/></span>At operating system level, start: <span class=\"SNO_DNT\" translate=\"no\"><em>﻿sdbregview -l | find /I \"ODBC\"﻿</em></span><br/>Example:<br/><span class=\"SNO_DNT\" translate=\"no\"><em>﻿ODBC  c:/program files/sdb/programs  *********  32 bit valid﻿</em></span><br/><br/>The ODBC library is linked statically for the SAP content server on UNIX, which means that it does not matter which ODBC driver version is installed on the host.<br/><br/></li>\n<li>How do I install or upgrade the SAP MaxDB ODBC driver?<br/><br/>For information about how to install or upgrade the ODBC driver, refer to SAP Note <a href=\"/notes/698915\" target=\"_blank\">698915</a>.<br/><br/></li>\n<li>How can I create an SQL trace on the SAP content server?<br/><br/>To obtain an SQL trace in the SAP Content Server environment, you activate the ODBC trace.<br/>The ODBC version may differ from the database version and it depends on the SAP Content Server version in use.<br/><br/>The ODBC trace must be activated on the server on which the Web server runs. Depending on the ODBC version you use, the following trace options are available:<ol>\n<li>ODBC driver 7.6 (Microsoft Windows only)<br/><span class=\"SNO_DNT\" translate=\"no\"><em>﻿odbcreg﻿</em></span>For more information, see SAP Note <a href=\"/notes/1405031\" target=\"_blank\">1405031</a>.</li>\n<li>ODBC driver 7.7 and higher<br/>As of ODBC Version 7.7, you should preferably use <span class=\"SNO_DNT\" translate=\"no\"><em>﻿odbc_cons﻿</em></span> to create the trace. For details, see SAP Note <a href=\"/notes/1428709\" target=\"_blank\">1428709</a>.</li>\n<li>If you cannot use the two options specified above, use the procedure described below. <br/>However, note that this CANNOT currently be used for SAP Content Server 6.50. You must only proceed in accordance with SAP Note <a href=\"/notes/1428709\" target=\"_blank\">1428709</a> here.<br/><br/>On Microsoft Windows, the ODBC trace (sqltrace) is activated with the help of the file <span class=\"SNO_DNT\" translate=\"no\">C<em>ontentServer.ini﻿</em></span>, as described in SAP Note <a href=\"/notes/329473\" target=\"_blank\">329473</a>, via the relevant parameter (<span class=\"SNO_DNT\" translate=\"no\"><em>﻿sqltrace=1﻿</em></span>). The file <span class=\"SNO_DNT\" translate=\"no\">C<em>ontentServer.ini﻿</em></span> is located in the SAP Content Server installation directory.<br/><br/>On UNIX, you activate the ODBC trace (sqltrace) using the file <span class=\"SNO_DNT\" translate=\"no\"><em>﻿cs.conf﻿</em></span> and the parameter <span class=\"SNO_DNT\" translate=\"no\"><em>﻿SQLTrace=1﻿</em></span>. You use the parameter <span class=\"SNO_DNT\" translate=\"no\"><em>﻿SQLTracePath=&lt;path&gt;﻿</em></span> to determine the storage location of the trace file. The name of the trace file is fixed as <span class=\"SNO_DNT\" translate=\"no\"><em>﻿sqltrace_&lt;pid&gt;.pct﻿</em></span>, whereby &lt;pid&gt; is automatically replaced by the process ID of the Web server. The system distinguishes between uppercase and lowercase. The file <span class=\"SNO_DNT\" translate=\"no\"><em>﻿cs.conf﻿</em></span> is located in the SAP Content Server installation directory.<br/><br/>Deactivate the ODBC trace by removing or commenting out the parameter (preceding semicolon ';').</li>\n</ol>\n<p>You can activate the trace for all repositories or for certain repositories only.<br/>Recommended: Activate the trace only for the repository for which you want to analyze a problem using the SQL trace.<br/>The Web server (Microsoft Windows: IIS; Unix: Apache) must be restarted after you activate the ODBC traces to start writing the trace.</p>\n</li>\n<li>What is the JDBC interface?<br/><br/>You can use the SAP MaxDB JDBC driver and the SAP MaxDB Java classes to incorporate SAP MaxDB databases into Java applications (to execute SQL statements, for example). The SAP MaxDB JDBC driver and the SAP MaxDB Java classes are included in the software component JDBC.<br/><br/></li>\n<li>What is the JDBC interface used for in the SAP environment?<br/><br/>The Java Engine of the Web Application Server (J2EE) requires the JDBC driver to communicate with the database.<br/><br/></li>\n<li>How can I determine which JDBC version is installed on the host?<br/><br/>You can use the program <span class=\"SNO_DNT\" translate=\"no\">SDBREGVIEW</span> to determine which JDBC version is installed on the relevant host.<br/><br/><span>On Unix platforms:<br/></span>At operating system level, start: <span class=\"SNO_DNT\" translate=\"no\"><em>﻿sdbregview -l | grep -i JDBC﻿</em></span><br/>The system displays the version of the JDBC driver that is installed on the host.<br/><br/>Example:<br/><span class=\"SNO_DNT\" translate=\"no\"><em>﻿JDBC /program files/sdb/programs ********* valid﻿</em></span><br/>You can obtain the complete version specification (PTS:1149235) with the following command:<br/><em><span class=\"SNO_DNT\" translate=\"no\">﻿/usr/runtime/jar/java -jar sapdbc.jar -V</span><br/><br/></em><span>On Microsoft Windows platforms:<br/></span>At operating system level, start <span class=\"SNO_DNT\" translate=\"no\"><em>﻿sdbregview -l | find /I \"JDBC\"﻿</em></span><br/><br/>Example:<br/><span class=\"SNO_DNT\" translate=\"no\"><em>﻿JDBC c:/program files/sdb/programs ********* valid</em></span><br/>You can obtain the complete version specification (PTS:1149235) with the following command:<br/><span class=\"SNO_DNT\" translate=\"no\">&lt;<em>drive&gt;\\sapdb\\programs\\runtime\\jar\\java -jar sapdbc. jar -V﻿</em></span><br/><br/></li>\n<li>How do I install or upgrade the SAP MaxDB JDBC driver?<br/><br/>The JDBC driver is a part of the SAP MaxDB client software package. To install or upgrade the JDBC driver, use SAP Note <a href=\"/notes/649814\" target=\"_blank\">649814</a>.<br/><br/></li>\n<li>How do I activate the JDBC trace?<br/><br/>For information about how to activate the trace, refer to SAP Note <a href=\"/notes/903018\" target=\"_blank\">903018</a>.<br/><br/></li>\n<li>You want to increase the number of J2EE instances. What do I have to consider in the database?<br/><br/>If new J2EE instances are connected to a database, then the requirement for parallel user sessions (database parameter MaxUserTasks) in the database increases. To correctly calculate <span class=\"SNO_DNT\" translate=\"no\"><em>﻿MaxUserTasks﻿</em></span>, refer to SAP Note <a href=\"/notes/1173395\" target=\"_blank\">1173395</a>.<br/><br/></li>\n<li>Where can I find additional information about SAP MaxDB interfaces?<br/><br/>In the glossary of the SAP MaxDB documentation (see SAP Note <a href=\"/notes/767598\" target=\"_blank\">767598</a>), you can find more information under the keyword \"interface\".<br/>Alternatively, you can open the SAP MaxDB documentation via the SAP MaxDB community: <a href=\"http://scn.sap.com/community/maxdb\" target=\"_blank\">https://community.sap.com/topics/maxdb</a></li>\n</ol>\n<p> </p></div>", "noteVersion": 70}, {"note": "1726899", "noteTitle": "1726899 - 721 DCK: Upgrade from 7.0n to 7.1n", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>You are going to perform one of the following upgrades with your SAP system:</p> <ul><li>SAP NetWeaver 7.0 to SAP NetWeaver 7.10</li></ul> <ul><li>SAP NetWeaver 7.0 to SAP EhP1 for SAP NetWeaver 7.10 (7.11)</li></ul> <ul><li>SAP EhP1 for SAP NetWeaver 7.0 (7.01) to SAP EhP1 for SAP NetWeaver 7.10 (7.11).</li></ul> <p><br/>and would like to use the downward-compatible kernel 721 (EXT) in the target system after deploying one of 72* kernels (720/720 EXT/7.21/721 EXT) in your start system.<br/><br/>The original kernel DVD used for the affected upgrade procedures contains a kernel release that is not supported if the start system is running with one of 7.2* kernels.<br/>For the upgrade procedure you have to create a modified kernel DVD containing the 7.21 kernel or the 7.21 EXT kernel as explained below.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>720, 721, kernel, AKK, DCK, upgrade, modified kernel DVD, 7.0, 7.01, 7.1, 7.11.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>With the desupport of the 7.00/7.01/7.10/7.11 kernels, they should be replaced by the downward compatible 720 (EXT) or 721 (EXT) kernel that should be installed before the upgrade as outlined in the note 1636252 (720 DCK) or 1713986 (721 DCK).<br/><br/>If you have installed a 7.2* kernel in your start system, you may create a DVD with 7.21 kernel or the 7.21 EXT kernel for the upgrade.<br/><br/>The 7.21 (EXT) kernel that you download to create the modified kernel DVD must have at least the patch level of the kernel deployed in your start system. We recommend to use the kernel with the patch level of the most recent stack kernel if it fulfills this prerequisite.<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>You have to download all mentioned archives either for the 7.21 standard kernel or for the 7.21 EXT kernel.</p> <ol>1. At http://service.sap.com/swdc, download the following archives for your platform:</ol> <ul><li>SAPEXE.SAR</li></ul> <ul><li>SAPEXEDB.SAR</li></ul> <ul><li>SAPJVM5.SAR (only for Dual-Stack or Java systems)</li></ul> <ul><li>DBATOOLS.SAR (only for Oracle database)</li></ul> <ul><li>IGSEXE.SAR</li></ul> <ul><li>IGSHELPER.SAR</li></ul> <p><br/>The archives can be downloaded at: http://service.sap.com/swdc:<br/>-&gt; Support Packages and Patches<br/>  -&gt; Browse Our Download Catalog - Support Packages &amp; Patches<br/>    -&gt; Additional Components<br/>      -&gt; SAP Kernel 64-bit &lt;Unicode&gt;<br/>        -&gt; SAP KERNEL 7.21 &lt;EXT&gt; 64-bit &lt;Unicode&gt;<br/>          -&gt; &lt;OS_Platform&gt;<br/>            -&gt; #Database independent<br/>              -&gt; SAPEXE_*.SAR<br/>            -&gt; &lt;DB_platform&gt;<br/>              -&gt; SAPEXEDB_*.SAR (current version)<br/><br/>Oracle only:<br/>-&gt; Support Packages and Patches<br/>  -&gt; Browse Our Download Catalog - Support Packages &amp; Patches<br/>    -&gt; Additional Components<br/>      -&gt; SAP Kernel 64-bit &lt;Unicode&gt;<br/>        -&gt; SAP KERNEL 7.20 &lt;EXT&gt; 64-bit &lt;Unicode&gt;<br/>          -&gt; &lt;OS_Platform&gt;<br/>            -&gt; ORACLE<br/>              -&gt; DBATL&lt;release&gt;&lt;ora-release&gt;*.SAR<br/><br/>-&gt; Support Packages and Patches<br/>  -&gt; Search for Support Packages and Patches<br/>     Search Term \"SAP JVM 5\"<br/>    -&gt; SAP JVM 5.1<br/>      -&gt; &lt;OS_Platform&gt;<br/>        -&gt; SAPJVM5_*.SAR<br/><br/>-&gt; Support Packages and Patches<br/>  -&gt; Browse Our Download Catalog - Support Packages &amp; Patches<br/>    -&gt; SAP Frontend Components<br/>      -&gt; SAP IGS<br/>        -&gt; SAP IGS 7.20 &lt;EXT&gt;<br/>          -&gt; &lt;OS_Platform&gt;<br/>            -&gt; IGSEXE_*.SAR<br/><br/>-&gt; Support Packages and Patches<br/>  -&gt; Browse Our Download Catalog - Support Packages &amp; Patches<br/>    -&gt; SAP Frontend Components<br/>      -&gt; SAP IGS HELPER APPLICATIONS<br/>        -&gt; SAP IGS HELPER<br/>          -&gt; # OS independent<br/>            -&gt; IGSHELPER_*.SAR<br/></p> <ol>2. Copy the kernel DVD for your SAP product to a local work directory</ol> <p>&lt;local_directory&gt;.<br/><br/>              Replace the original archives with the downloaded archives. You must replace the original archives SAPEXE.SAR, SAPJVM5.SAR (only Dual Stack or Java), IGSEXE.SAR and IGSHELPER.SAR in the directory DBINDEP and the original archives SAPEXEDB.SAR and DBATOOLS.SAR (only Oracle) in the directory &lt;DB_Platform&gt;. <p>              The root directories for the kernel archives are release-dependent. You have to replace both kernels (xV and xI) <p>              - SAP NetWeaver 7.1 (7.10), Unicode: <p>              &lt;local_directory&gt; /DATA_UNITS/K_710_UV_&lt;OS Platform&gt; <p>              &lt;local_directory&gt; /DATA_UNITS/K_710_UI_&lt;OS Platform&gt; <p>              - SAP NetWeaver 7.1 (7. 10), non-Unicode: <p>              &lt;local_directory&gt; /DATA_UNITS/K_710_NV_&lt;OS Platform&gt; <p>              &lt;local_directory&gt; /DATA_UNITS/K_710_NI_&lt;OS Platform&gt; <p>              - SAP NetWeaver 7.1 EHP1 (7. 11), Unicode: <p>              &lt;local_directory&gt; /DATA_UNITS/K_711_UV_&lt;OS Platform&gt; <p>              &lt;local_directory&gt; /DATA_UNITS/K_711_UI_&lt;OS Platform&gt; <p>              - SAP NetWeaver 7.1 EHP1 (7. 11), non-Unicode: <p>              &lt;local_directory&gt; /DATA_UNITS/K_711_NV_&lt;OS Platform&gt; <p>              &lt;local_directory&gt; /DATA_UNITS/K_711_NI_&lt;OS Platform&gt; <ol>3. Use this modified kernel DVD for the upgrade procedure.</ol></p></p></p></p></p></p></p></p></p></p></p></p></p></p></div>", "noteVersion": 2}]}, {"note": "1707579", "noteTitle": "1707579 - SWT2DB: No check of bit in runtime object header", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>You use the add-on SWT2DB (SAP Business Application Accelerator powered by HANA). The ABAP Dictionary check of a runtime object in transaction SE11 or SE14 issues errors for the runtime object header flag 2, bit 6 for tables or views that are used in SAP Business Application Accelerator scenarios. Activating the affected object does not correct the inconsistency.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>The affected bit in the header of the runtime object is not checked by the ABAP Dictionary, and in particular, it is not changed during an activation. Moreover, the ABAP Dictionary does generally not know if the bit is currently set. As a consequence, the flag must not be checked in the ABAP Dictionary.<br/>ABAP Dictionary mass checks (RUTMSJOB) do not provide default settings for bit 6 of flag 2 of the runtime object header. After you implement the corrections contained in this SAP Note, the system also ignores the bit during the runtime checks in transaction SE11 and SE14.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>Implement the correction instructions or import the referenced Support Package.</p></div>", "noteVersion": 3}, {"note": "1728283", "noteTitle": "1728283 - SAP <PERSON> 721: General Information", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>Missing information about SAP Kernel Release 721 and its usage scenarios.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p><PERSON>el, AKK, DCK, downward compatible kernel, 721, 721_EXT, 720, new features</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p><strong>What is the intention of the SAP kernel 721?</strong></p>\n<p>SAP delivers innovations which need contributions in the SAP kernel through a dedicated SAP kernel version (the INNOVATION kernel), clearly separated from, but fully compatible to the standard kernel version.</p>\n<p>The SAP Kernel 721 was initially introduced to deliver kernel-based enhancements of functionality in SAP systems based on NetWeaver 7.00-7.31 non disruptively. SAP kernel 721 replaced the SAP kernel 720 as the standard kernel for NW 7.00-7.31 based SAP systems by end of Q1 2015. A new innovation kernel is currenty Kernel 722.<br/><br/>The SAP kernel 721 is a full replacement of the SAP kernel 720 and older SAP 7XX kernel versions (700, 701, 710, 711) and can be deployed as a kernel patch.<br/><br/>The note 1716826 explains the usage of the SAP kernel 721 as a downward compatible kernel for NW 7.00-7.31 based systems.<br/><br/>The note 1713986 provides detailed information on the installation of the kernel 721.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong>List of enhancements of SAP kernel 721 over SAP kernel 720</strong></p>\n<p>Following is a list of new features and enhancements of the 721 kernel. This list is being updated on a regular basis.<br/>Check the SAP notes listed below for the prerequisites (kernel patch level, AS ABAP/Java version and patch level)</p>\n<p>1. New Functionality</p>\n<p>SAP Kernel 721 enables a set of new features of the NW AS ABAP/Java that are not available with the kernel 720:</p>\n<ul>\n<li>Support for the SAP HANA Application Accelerator (SAP note 1694697)</li>\n<li>Debugging with ABAP in Eclipse (SAP Note 1745243)</li>\n<li>Support for SAP Personas (SAP Note 1825215)</li>\n<li>Unicode Printing Enhancement (SAP Note 1812076)</li>\n<li>Advanced print options for ADS printing with SAPPDFPRINT (SAP Notes 1777740 and 1729666)</li>\n</ul>\n<p>2. Security Enhancements</p>\n<ul>\n<li>SPNego authentication (SAP Note 1798979)</li>\n<li>SMTP over TLS and SMTP authentication (SAP Note 1747180)</li>\n<li>Option to make SNC encryption mandatory (SAP Note 1690662)</li>\n<li>New encryption algorithm aes128_ecb (SAP Note 1862612)</li>\n<li>New PSE table to store PSE data larger than 254745 bytes (SAP Note 1844671)</li>\n<li>Configurable server logon restrictions (SAP Note 1891583)</li>\n<li>Support of authentication method LOGIN as SMTP client (SAP Note 2000465)</li>\n</ul>\n<p>3. Supportability Enhancements</p>\n<p>These enhancements affect logging, tracing and debugging functionality.</p>\n<ul>\n<li>Recording the logon method on AS ABAP (SAP Note 1789518)</li>\n<li>New VMC Debugging (SAP Notes 1709868 and 1678626)</li>\n<li>Read Access Logging (SAP Note 1860677)</li>\n<li>Enhancements of Authorization Trace functionality:</li>\n<ul>\n<li>Filters can be set (SAP Note 1854561)</li>\n<li>Additional information \"authorized by reference user\" (SAP Note 1960916)</li>\n</ul>\n<li>SQL Monitor (SAP Note 1885926)</li>\n<li>Debugging applications of another user (SAP Note 1919888)</li>\n<li>Improved gateway logging with Action Z (SAP Note 1888894)</li>\n<li>Enhancement of kernel statistics for RFC subrecords (SAP Note 1964997)</li>\n<li>Kernel traces for SAP PERSONAS  (SAP Note 1960085)</li>\n<li>Enhancement of C call CLOCK for analysis improvement  (SAP Note 1980499)</li>\n<li>Enhancements of batch job logging functionality</li>\n<ul>\n<li>Automatic deletion of 'trivial' job logs becomes standard (SAP Notes 1707974, 1468596, 1882691)</li>\n<li>Additional trace information for batch job termination (SAP Note 2018923)</li>\n</ul>\n</ul>\n<p>4. GUI-related</p>\n<ul>\n<li>New profile parameter sapgui/nwbc_scripting to enable or disable the NetWeaver Business Client Side Panels functionality (SAP Note 1816392)</li>\n<li>SAPGui for HTML: Support for theme sap_corbu (SAP Notes 1776140 and 1813346)</li>\n<li>SAPGui for HTML: new thema \"SAP Blue Crystal\" - (SAP Note 1913386)</li>\n<li>SAP GUI for HTML: Support for resizing the dialog box (SAP Note 1882891)</li>\n<li>Support for RESTGUI ABAP Interface (SAP Note 1787341)</li>\n<ul>\n<li>Enhancements in RESTGUI: <span class=\"urTxtStd urVt1\"> </span></li>\n<ul>\n<li>Re-enabled xmldiff feature (SAP Note 1905336)</li>\n<li>GET systeminfo request added (SAP Note 1980489)</li>\n<li>Timer feature (SAP Note 2027624)</li>\n</ul>\n</ul>\n<li>Enhancements in sapgui control framework (SAP Note 1872947)</li>\n<li>Enable dynpro output masking (SAP Note 2033606)</li>\n</ul>\n<p>5. Scalability and Performance Enhancements</p>\n<ul>\n<li>Set Update Task Local as a global parameter (SAP Note 1809081)</li>\n<li>Per session quotas for number of enqueue requests (SAP Note 1722708)</li>\n<li>Message Server: save logon groups feature (SAP Note 1787163)</li>\n<li>Support of spool requests larger than 2 GB (SAP Note 1890546)</li>\n<li>Gateway: increase default for profile parameters rdisp/max_gateways and gw/max_sys to 1000 (SAP Note 2052011)</li>\n</ul>\n<p>6. Various Technology Enhancements</p>\n<ul>\n<li>Auditing of dynamic ABAP source code (SAP Note 1655743)</li>\n<li>Binding outbound connections to a network interface (SAP Note 1762956)</li>\n<li>ABAP-PH: Add missing FRIEND dependency (SAP Note 1999681)</li>\n<li>Support for data matrix bar code and QR code for Smart Forms and SAPscript forms (SAP Note 2029589)</li>\n<li>Canceling a transaction (soft cancel) or closing a SAP GUI window during batch input session processing (SAP Note 2037455)</li>\n<li>Unified Connectivity (UCON) Remote Function Call (RFC) data collector (SAP Note 2055322)</li>\n<li>Standalone enqueue server with replication: switch to synchronous replication mode by default (SAP Note 2036171).</li>\n</ul>\n<p>7. Platform- and DB-specific</p>\n<p>The features listed below make use of platform- or DB-specific technologies and utilities.</p>\n<ul>\n<li>z/OS platform: Enqueue Replication via System z Cross Coupling Facility (SAP Note 1753638)</li>\n<li>721_EXT on AIX:</li>\n<ul>\n<li>Improved performance on IBM AIX due to new optimization technology in build process.</li>\n<li>More efficient implementation of TCP/IP communication on AIX (pollset vs. poll) with improved performance and better scalability (SAP Note 1756204).</li>\n</ul>\n<li>721_EXT on on Windows: starting with patch level 100, a new optimization technology was introduced into the build process which yields SAP kernels on Windows with significantly improved performance (SAP Note 1357244)</li>\n<li>Sybase (721_EXT only)</li>\n<ul>\n<li>Executing SQL scripts using sybctrl (SAP Note 1899984)</li>\n<li>New DBMS Type 'SIQ' for Sybase IQ (SAP Note 1969459)</li>\n</ul>\n<li>DB2: Support for CLI failover (SAP Note 1942773)</li>\n</ul>", "noteVersion": 27, "refer_note": [{"note": "1776140", "noteTitle": "1776140 - Support for theme sap_corbu", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>Advance development: Support for the theme sap_corbu in 721</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>WebAS ITS theme sap_corbu</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>This is an advance development.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>Apply the specified patch.</p></div>", "noteVersion": 1}, {"note": "1777740", "noteTitle": "1777740 - Paper tray selection with SAPPDFPRINT", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>You want to use the options for paper tray and duplex printing when printing with SAP Interactive Forms by Adobe with SAPPDFPRINT. Previously, this was not supported.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>Advanced print options, ADS, SAPPDFPRINT, duplex, paper tray</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>A function is missing.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><ul><li>Implement SAP Note 1729666. After you do this, advanced print options are enabled in general for ADS print jobs.</li></ul> <ul><li>Apply the kernel patch mentioned in the download section of this SAP Note.</li></ul> <ul><li>Use SAPSprint 730 Patch 4 or higher, and SAPPDFPRINT 730 Patch 3 or higher.</li></ul> <p><br/>If all three prerequisites are met, you can, as described in SAP Note 1729666, use the advanced print options paper tray and duplex for ADS print jobs also when printing with SAPPDFPRINT. Other options are not supported by SAPPDFPRINT.</p></div>", "noteVersion": 3}, {"note": "1872947", "noteTitle": "1872947 - Enhancements for sapgui control framework", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>Previously, after handling a system event of the Control Framework, all screen elements were sent to the SAPGUI again.<br/><br/>Due to an enhancement in the kernel, it is now possible to suppress this resending of screen elements.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>New kernel functions</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>Use a disp+work that has the patch level specified in the \"SP Patch Level\" section.<br/><br/></p></div>", "noteVersion": 1}, {"note": "1747180", "noteTitle": "1747180 - SMTP via TLS and SMTP authentication", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>You want to use SMTP via TLS or SMTP authentication.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>The implementation of SMTP via TLS and SMTP authentication is contained as of 7.31 Support Package 6 and 7.21 kernel.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>Use at least 7.31 Support Package 6 and a 7.21 kernel.<br/><br/>SAP Note 1702785 contains help for troubleshooting.<br/><br/>Since the configuration of SMTP via TLS and SMTP authentication on the server is not yet described in the online documentation for 7.31 Support Package 6, the description is contained here:<br/></p> <b>Configuring SMTP authentication and SMTP via TLS/SSL for incoming e-mails (system type AS-ABAP)</b><br/> <ul><li>This affects the parameter: icm/server_port_&lt;xx&gt; (AS ABAP)</li></ul> <p>           You have already set the log to be used (PROT) to SMTP and specified the port to be used (PORT) in the parameter icm/server_port_&lt;xx&gt; and you now want to configure the SMTP authentication or the SMTP settings via TLS/SSL for incoming e-mails.<br/>           In this article, the following SMTP-specific configuration options are described:</p> <ul><ul><li>TLS: Configure SMTP via TLS/SSL</li></ul></ul> <ul><ul><li>AUTHMECHANISM: Authentication for incoming e-mails</li></ul></ul> <ul><ul><li>AUTHUSERS: Define authorized AS ABAP users</li></ul></ul> <p>           The following contains two examples for the possible configuration:</p> <ul><ul><li>Example A: icm/server_port_1 = PROT=SMTP, PORT=25000, TLS=2</li></ul></ul> <p>                    This opens the port 25000 for SMTP requests and prompts the client for the TLS encryption. If this is not possible, the connection is terminated.</p> <ul><ul><li>Example B: icm/server_port_1 = PROT=SMTP, PORT=25000, TLS=2, AUTHMECHANISMS=PLAIN; EXTERNAL, AUTHUSERS=ABAPUser1; ABAPUser2</li></ul></ul> <p>                    This opens the port 25000 for SMTP requests and prompts the client for the TLS encryption. If this is not possible, the connection is terminated. The client must identify himself with a user/password or client certificate. ABAPUser1 and ABAPUser2 are entered as authorized users.<br/>           The character string adheres to the following syntax:<br/>           PROT=&lt;SMTP&gt;, PORT=&lt;port or service name&gt;[, TIMEOUT=&lt;timeout&gt;, PROCTIMEOUT=&lt;proctimeout&gt;, EXTBIND=1, HOST=&lt;host name&gt;, SSLCONFIG=ssl_config_&lt;xx&gt;, VCLIENT=&lt;SSL client verification&gt;, ACLFILE=&lt;ACL file&gt;, TLS=&lt;SMTP TLS usage&gt;, AUTHMECHANISMS=&lt;SMTP authentication&gt;, AUTHUSERS=&lt;SMTP user authentication&gt;]</p> <ul><li>Configuration</li></ul> <p>           TLS: Configure SMTP via TLS/SSL</p> <ul><ul><li>0: A TLS prompt does not take place.</li></ul></ul> <ul><ul><li>1: The server prompts the client to encrypt using TLS. If this is not possible, the connection via SMTP is accepted without TLS.</li></ul></ul> <ul><ul><li>2: The client has to use TLS for encryption; otherwise, the connection is terminated.</li></ul></ul> <p>           AUTHMECHANISMS: Authentication for incoming e-mails</p> <ul><ul><li>PLAIN: The authentication takes place with a user/password query</li></ul></ul> <ul><ul><li>EXTERNAL: The authentication takes place with a client certificate</li></ul></ul> <ul><ul><li>NONE: No prompt for authentication takes place</li></ul></ul> <p>           (The internet standard RFC 4422 describes the SMTP authentication in detail.)<br/>           AUTHUSERS: Define authorized users<br/>           With the AUTHUSERS option, the users allowed for the SMTP authentication are specified. For the SMTP authentication, standard AS ABAP users are used in the client 000, user type SYSTEM.<br/>           In the AUTHUSERS, you have to specify at least one user and a maximum of 10 users. The users are separated by a semicolon in the character string; # refer to example B.<br/>           Note the following:<br/>           If you want to use the authentication via user/password (PLAIN), you first have to generate a relevant ABAP user.<br/>           If you want to use the authentication with a client certificate (EXTERNAL), a valid certificate must first be assigned to the relevant ABAP user.<br/></p></div>", "noteVersion": 2}, {"note": "1980489", "noteTitle": "1980489 - RESTGUI: GET systeminfo request", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p id=\"\"> Add GET systeminfo request that will return system information (kernel version, kernel patch level, ABAP SAP basis, ABAP supported package) in specified format (xml,json,text).</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Please install the kernel patch specified in the corresponding section of this note.</p>", "noteVersion": 1}, {"note": "1854561", "noteTitle": "1854561 - Authorization trace with filter (auth/authorization_trace)", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>The authorization trace receives filters that can be set. This means that recording can be limited to the authorization checks that meet the filter conditions.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>The authorization trace can be activated or deactivated while the system is running via RZ11 with the profile parameter auth/authorization_trace documented in SAP Note 543164.<br/><br/>The authorization trace collects data for all clients and all users and stores it in the database.<br/><br/>During the execution of a program, every authorization check is entered exactly once with the name and type of the running application, the point in the program, the authorization object and the checked authorization values.<br/><br/>The trace database table serves to support the maintenance of authorization default values.<br/><br/>Some transactions are generic, because they can be used to start reports, function modules, and methods, for example. It does not make sense to collect trace data to support the maintenance of authorization default values for these transactions, so this is not carried out for the following transactions:</p>\n<ul>\n<li>SE37, SE38, SA38, SA39, SE80, SEU_INT,</li>\n</ul>\n<ul>\n<li>SERP, SARP, SART, SCAM,</li>\n</ul>\n<ul>\n<li>SE0*, that is, all transactions that start with SE0,</li>\n</ul>\n<ul>\n<li>SE10, SE54, SEPS, SHD0, SHD1, SPAM, SPAU, SPDD, SQ00, SQ01.</li>\n</ul>\n<p>In these transactions, the kernel does not take the \"Do Not Check\" check indicator set in transaction SU24 into account. This means that you cannot actually deactivate any authorization check here.<br/><br/>The kernel patch of SAP Note 1872459 adds transaction SE24, Class Builder, to the exception list. In addition, the system now ignores the exception list for the trace with filter, because this is used to examine special scenarios and this usually starts with an empty trace table.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>With the kernel delivered with this SAP Note and the ABAP corrections contained in it, the parameter auth/authorization_trace can now have the following values:</p>\n<ul>\n<li>N: Trace deactivated</li>\n</ul>\n<ul>\n<li>Y: Trace activated</li>\n</ul>\n<ul>\n<li>F: Trace activated with filter. The filter for recording is set in transaction STUSOBTRACE. See SAP Note 1847663. Application type, user and authorization objects can be used as filters. Special scenarios can thus be investigated. At least one filter must be set, or logging will not take place. The data volume, and therefore the influence on performance, is clearly smaller compared to Y.</li>\n</ul>\n<ul>\n<li>Blank characters: This means that the trace is activated in SAP systems (profile parameter transport/systemtype = SAP), and deactivated in customer systems (transport/systemtype = CUSTOMER). This is the standard setting, if the parameter is not set in the instance profile or standard profile (DEFAULT.PFL) or was dynamically changed.</li>\n</ul>\n<p>Caution:<br/>Activating the authorization trace without filters has a seriously detrimental effect on performance!<br/><br/>The installation of the kernel patch is described in SAP Note 19466. For details about the patch level, see the SAP GUI display of this note by choosing Note Administration -&gt; \"SP Patch Level\" tab page, in the HTML display under \"Content: ... SP Patch Level\".<br/><br/>The ABAP corrections are contained in the Support Packages named in the SAP Note in which the transaction STUSOBTRACE for setting the filter is delivered.<br/>The automatic correction instructions for the Note Assistant SNOTE are contained in other SAP Notes for reasons of consistency. Since the filter can only be set using transaction STUSOBTRACE, setting the profile parameter auth/authorization_trace=F has no effect.</p></div>", "noteVersion": 3}, {"note": "1809081", "noteTitle": "1809081 - Force local update", "noteText": "<div class=\"sapMMsgStrip sapMMsgStripWarning sapUiSmallMarginTop sapUiMediumMarginBottom\" id=\"DISCLAIMER\"><div class=\"sapMMsgStripMessage\"><span class=\"sapMText sapUiSelectable sapMTextMaxWidth\" dir=\"auto\" style=\"text-align: left;\"><strong>ATTENTION!</strong> This is a machine-translated document that has not been reviewed for accuracy. To provide feedback regarding the machine translation, please <a href=\"https://sapinsights.eu.qualtrics.com/jfe/form/SV_6nI2C1MOwEviVQG?NoteNumber=1809081&amp;TargetLanguage=EN&amp;Component=BC-ABA-LA&amp;SourceLanguage=DE&amp;Priority=03\" style=\"color:var(--sapLinkColor);\" target=\"_blank\">click here</a>.<br/>You can access the original document via the following link: <a href=\"/notes/1809081/D\" style=\"color:var(--sapLinkColor);\" target=\"_blank\">/notes/1809081/D</a>.</span></div></div><div><div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>It is not possible to force the local update using a global parameter.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Ursache und Voraussetzungen\">Reason and Prerequisites</h3>\n<p>Normally, updates are executed asynchronously. You can use the ABAP statement SET UPDATE TASK LOCAL to execute updates locally with 'Start immediately'.</p>\n<p>The profile parameter abap/force_local_update_task handles all updates as if SET UPDATE TASK LOCAL were set. By setting the profile parameter abap/force_local_update_task to '1', the local update should be forced globally.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Lösung\">Solution</h3>\n<p>Apply the latest kernel patch. For the minimum patch level for the relevant release, see the section \"SP Patch Level\" (patch text: abap/force_local_update_task added).</p>\n<p>Check on SAP Service Marketplace whether it is already available for your platform and implement it in accordance with SAP Note 19466.<br/>After you import the relevant Support Package, you can also set the profile parameter dynamically.</p>\n<p>Caution: Enforcing the local update should be done with caution and only taking into account all transactions running in the system. This may change the semantics of transactions. The duration of holding database locks can also be affected negatively. This is particularly the case if, in contrast to the SAP transaction concept, changes were made to the content of the database in the last dialog step before the update was triggered (e.g. B. Use of the Open SQL commands INSERT, MODIFY, UPDATE, or DELETE).</p></div></div>", "noteVersion": 3}, {"note": "1798979", "noteTitle": "1798979 - SPNego ABAP: Downport", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You want to use SPNego (browser-based Kerberos) authentication with an ABAP system.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Kerberos, Windows Integrated Authentication, www-authenticate: Negotiate</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>You have purchased a licence for the product SAP NetWeaver Single Sign-On 2.0 (or higher release number)</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>You require 3 components</p>\n<ul>\n<li><strong><strong>ABAP </strong></strong>(component SAP_BASIS):</li>\n</ul>\n<ul>\n<ul>\n<li>7.40 SP 2 (or higher)<br/>(additionally apply notes <a href=\"/notes/1819808\" target=\"_blank\">1819808</a> and <a href=\"/notes/1832706\" target=\"_blank\">1832706</a>)</li>\n</ul>\n</ul>\n<ul>\n<ul>\n<li>7.31 SP 7 (or higher)<br/>(additionally apply notes <a href=\"/notes/1819808\" target=\"_blank\">1819808</a> and <a href=\"/notes/1832706\" target=\"_blank\">1832706</a>)</li>\n</ul>\n</ul>\n<ul>\n<ul>\n<li>7.30 SP 10 (or higher) <span>and</span> note <a href=\"/notes/1879371\" target=\"_blank\">1879371</a> (SP 11)<br/>(corrections of notes <a href=\"/notes/1819808\" target=\"_blank\">1819808</a> and <a href=\"/notes/1832706\" target=\"_blank\">1832706</a> are already contained)</li>\n</ul>\n</ul>\n<ul>\n<ul>\n<li>7.02 SP 14 (or higher) <span>and</span> note <a href=\"/notes/1879371\" target=\"_blank\">1879371</a> (SP 15)<br/>(corrections of notes <a href=\"/notes/1819808\" target=\"_blank\">1819808</a> and <a href=\"/notes/1832706\" target=\"_blank\">1832706</a> are already contained)</li>\n</ul>\n</ul>\n<ul>\n<ul>\n<li>7.02 SP 9 - 13: possible on project base<br/>(contact us, if interested, by submitting an inquiry on component BC-SEC-LGN)</li>\n</ul>\n</ul>\n<ul>\n<li><strong>Kernel</strong>: 7.21 (EXT) PL 41 (or higher), see note <a href=\"/notes/1716826\" target=\"_blank\">1716826</a><br/>(recommended: 7.21 EXT PL 110 or higher, see note <a href=\"/notes/1819808\" target=\"_blank\">1819808</a>)</li>\n</ul>\n<ul>\n<li><strong>Secure Login Library</strong> (SLL), as part of SAP NetWeaver Single Sign-On 2.0 (or higher),<br/>respectively <strong>CommonCryptoLib</strong> (CCL), as part of latest stack kernel shipments (see note <a href=\"/notes/1848999\" target=\"_blank\">1848999</a>)</li>\n</ul>\n<p><br/>For the latest documentation refer to <a href=\"http://help.sap.com/nwsso\" target=\"_blank\">http://help.sap.com/nwsso</a>.</p>\n<p><strong>Remark</strong></p>\n<p>It is also possible to disable SPNego authentication per request (\"opt-out\"). You can achieve that by adding a URL parameter \"spnego\" with value \"disabled\". When the ABAP server receives a request that contains this parameter, Kerberos authentication will be skipped even though it might be configured correctly.</p></div>", "noteVersion": 10}, {"note": "1905336", "noteTitle": "1905336 - its: restgui: re-enabled xmldiff feature", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>Re-enabled the XMLDIFF feature of the RESTGUI/ITS interface.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>Please update the kernel to the patch specified in the corresponding section of this note.</p></div>", "noteVersion": 1}, {"note": "1756204", "noteTitle": "1756204 - Activate fast polling", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>POSIX poll is used to communicate between SAP dispatcher and client SAPGUIs.  As the number of SAPGUIs increase this can lead to a significant amount of cpu overhead and increased response time latency.<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>select epoll pollset<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>Improved performnace.<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>As of 7.21 EXT on AIX, the default poll mechanism in the ABAP Kernel has been changed from POSIX poll to an AIX proprietary mechanism call pollset. The pollset mechanism is more efficent, scales better, and has improved responsiveness.<br/>The feature is now active by default.<br/><br/>If you need to disable this feature and revert to the traditional poll mechanism, set the environment variable SAP_USE_STD_POLL (to any value). This may be done in instance start profile.<br/></p></div>", "noteVersion": 1}, {"note": "1844671", "noteTitle": "1844671 - New PSE data table SSF_PSE_T without length restriction", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>Due to the design of PSE data table SSF_PSE_D, it can only save  Personal Security Environment (PSE) data up to a maximum length of 254745 bytes.<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>STRUST STRUSTSSO2 SSF_PSE_H<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>Table design<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>ABAP and kernel patch<br/><br/>To remove this restriction, a new data table SSF_PSE_T is introduced. The old table SSF_PSE_D must be retained for downward compatibility reasons.<br/><br/>The ABAP corrections enable you to save PSEs larger than 254745 bytes.<br/>You must implement the kernel corrections as soon as you want to save a PSE larger than 254745 bytes. Only if you do this can these PSEs be loaded from the database into the file system when an application server is started.</p></div>", "noteVersion": 2}, {"note": "1690662", "noteTitle": "1690662 - Option: Blocking unencrypted SAPGUI/RFC connections", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>You want to ensure that inbound RFC or GUI connections have to be encrypted.<br/>Previously, you could use parameters such as snc/accept_insecure_gui or snc/accept_insecure_rfc to configure only whether you had to use SNC credentials for the logon in the case of an inbound connection secured with SNC, or whether other logon procedures were also accepted.<br/>You have no option to force a connection secured and encrypted with SNC for all other logon procedures (password-based, for example).<br/>To encrypt SAPGUI or RFC connections, you must use an SNC product with the QoP setting \"Encryption\". If you choose a lower QoP level (\"Integrity\", for example), the required condition will not be met.<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>snc/only_encrypted_gui, snc/only_encrypted_rfc, QoP, quality of protection<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>This problem is caused by missing functions.<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>Import a 721 kernel with <b>at least</b> patch level 33 (see SAP Note 1716826).<br/><br/>snc/only_encrypted_gui has the following permitted values:</p> <ul><li>0 (Default) - Inbound GUI connections are treated as before.</li></ul> <ul><li>1 - Inbound GUI connections must be secured <b>AND</b> encrypted with SNC.</li></ul> <p><br/>snc/only_encrypted_rfc has the following permitted values:</p> <ul><li>0 (Default) - Inbound RFC connections are treated as before.</li></ul> <ul><li>1 - Inbound RFC connections of non-ABAP clients must be secured <b>AND</b> encrypted with SNC.</li></ul> <ul><li>2 - In addition to 1, inbound external RFC connections of ABAP clients must also be secured <b>AND</b> encrypted with SNC.</li></ul> <ul><li>3 - In addition to 2, inbound internal RFC connections of other application servers of the same system must also be secured <b>AND</b> encrypted with SNC. IMPORTANT: Take into account the parameters snc/r3int_rfc_secure and snc/r3int_rfc_qop.</li></ul> <p><br/>Encryption corresponds with a negotiated quality of protection (QoP) during the SNC handshake of level 3. Which QoP is negotiated depends on the SNC setting of the client and of the server. The parameters snc/data_protection/max and snc/data_protection/min are relevant for the server.<br/>The SNC product NTLM does not offer a quality of protection of level 3.<br/><br/>The assigned ABAP correction (delivery possible only via Support Package) introduces the two new profile parameters snc/only_encrypted_gui and snc/only_encrypted_rfc in RZ10/RZ11.<br/>However, you can also use the parameters with an earlier ABAP Support Package if you have applied the required kernel patch.<br/>In this case, create system message SNC(100) in transaction SE91 manually:<br/></p> <ul><li>Call transaction SE91.</li></ul> <ul><li>Message class: SNC<br/>Message: 100<br/>Choose \"Change\".</li></ul> <ul><li>Enter the following message text:<br/>Unencrypted communication is rejected by this system</li></ul> <ul><li>Select the \"Self-Explanatory\" checkbox.</li></ul> <ul><li>Save this change.</li></ul> <p><br/>If you set the parameter in RZ10 without having imported the ABAP Support Package, you receive warnings about the parameters not being recognized.<br/></p></div>", "noteVersion": 1}, {"note": "1882891", "noteTitle": "1882891 - Support for resizing the dialog box", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>When you change the size of a dialog box, the content size is not adjusted. If you choose \"Maximize/Restore\" on the top right of the window, this does not result in an adjustment of the window content either.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>WebAS ITS dialog box resize</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>This is a program error.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>Apply the specified patch. If the patch number has not yet been entered, the patch is still in production.</p></div>", "noteVersion": 2}, {"note": "1762956", "noteTitle": "1762956 - Binding outbound connections to a network interface", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>1) You want to bind outbound connections established by the ICM to a specific network interface.<br/><br/>2) Outbound HTTP connections fail e.g. with the error message ICM_HTTP_CONNECTION_FAILED.<br/><br/>The following messages appear in the ICM trace dev_icm:<br/> *** ERROR =&gt; NiIConnectSocket: SiConnect failed for hdl 67/sock 19<br/>    (SI_EINVAL/22; I4; ST; ***********:8000) [nixxi.cpp    2898]<br/> *** WARNING =&gt; Connection request from (4/6/0) to host: server.example.com, service: 8000 failed (NIEINTERN)<br/>     ... {0001000c} [icxxconn_mt.c 2271]<br/><br/>The specific error code is SI_EINVAL/22.<br/><br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>icm/local_addr, ICM_HTTP_CONNECTION_FAILED, SM59<br/><br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>1) You want increased security.<br/><br/>2) Outbound connections are bound to a specific interface, and the destination host cannot be reached through this interface.<br/><br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>The routing algorithm of the Operating System is responsible to choose the correct network interface for any given destination address. Routing tables allow to manually adjust the routing. You should implement the solution introduced by this note only when the desired routing cannot be achieved by adjusting the Operating System's routing table.<br/><br/>WARNING: Making use of the below mentioned parameter can affect the application server's ability to establish outbound TCP/IP connections.<br/><br/>1) You can use instance parameter icm/local_addr to specify the hostname or the IP address of the network interface to use for outbound connections. You have to restart ICM in order to activate changes made to this parameter.<br/><br/>Examples:<br/>icm/local_addr = myhost01<br/>icm/local_addr = *************<br/><br/>If the parameter specifies a hostname that cannot be resolved into a valid IP address upon ICM startup, the parameter will be ignored, and the following messages appear in the ICM trace:<br/> *** ERROR =&gt; IcmCheckParam: NiHostToAddr(invalid_host) failed (rc=-2) [icxxman_mt.c 3461]<br/> *** WARNING =&gt; Parameter icm/local_addr cannot be resolved and will be ignored. [icxxman_mt.c 3463]<br/><br/>If the parameter has been successfully activated upon ICM startup, the following messages appear in the ICM trace:<br/> *** WARNING =&gt; Parameter icm/local_addr enabled - outbound connections will use interface: ************* [icxxman_mt.c 3456]<br/><br/>Additionally, the parameter icm/local_addr is displayed in the parameter overview of the ICM Monitor (SMICM -&gt; Goto -&gt; Parameters -&gt; Display).<br/><br/>2) First of all, review solution 1) and verify that outbound connections are bound to a specific interface. If this is not the case (parameter icm/local_addr is not active) this note does not apply.<br/><br/>Make sure that the destination host can be reached through the interface specified by icm/local_addr.<br/><br/>Specify the approprate interface address with icm/local_addr so that the destination host can be reached.<br/><br/>If you don't have the requirement to bind outbound connections to a specific interface remove the parameter icm/local_addr from your instance profile and restart ICM.<br/><br/></p></div>", "noteVersion": 1}, {"note": "1862612", "noteTitle": "1862612 - SAFT PT webservice requires a new encryption algorithm", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>The webservice solution of note 1872926 requires a new encryption algorithm to be available in the SAP kernel<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>aes128_ecb<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>New functionality<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>Implement a kernel patch (disp+work) with a patch level that corresponds to at least the patch level specified on the \"SP Patch Level\" tab page.<br/>Note 19466 contains further information about the download and installation.<br/>You also have to run a SAPCRYPTOLIB with at least patchlevel 30</p></div>", "noteVersion": 4}, {"note": "1888894", "noteTitle": "1888894 - GW: Improvements for logging with action Z", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>SAP Note 1689663 explains that Action Z is used during gateway logging to write extra records into the logging file. If the simulation mode is activated, these records display that no rule was found in the reg info or sec_info for an attempt to register or start.<br/><br/>Due to the enhancements in this SAP Note, the Z entry is now written independent of the simulation mode.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>gw/sim_mode gw/logging Action Z</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>Enhancement of the logging feature</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>Implement the relevant kernel corrections specified under the \"SP Patch Level\" tab page.</p></div>", "noteVersion": 1}, {"note": "1885926", "noteTitle": "1885926 - ABAP SQL Monitor", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>In a live ABAP-based SAP system, a large number of different SQL requests are executed by the most diverse processes. To find performance hotspots and also potential for optimizations in this very broad SQL load profile, you require specialized SQL monitoring tools that also provide a connection to the running ABAP processes.<br/><br/>The new ABAP SQL monitor allows the aggregated writing of all ABAP-based SQL executions in a live ABAP system.<br/>At this point, aggregated performance key figures (number of executions, execution time, records read) are collected per ABAP SQL command.<br/>Furthermore, the entry point of the related process (transaction code, report name, RFC module, URL) is stored for each aggregated SQL record.<br/><br/>After a certain active SQL monitoring phase (for example, one day, one week, one month), the collected SQL monitor data can be used to answer, for example, the following questions:</p>\n<ul>\n<li>Which SELECT in the customer code is executed most frequently in production?</li>\n</ul>\n<ul>\n<li>Which SELECT statements cause the longest runtime?</li>\n</ul>\n<ul>\n<li>Which SELECT in the customer code reads the most data?</li>\n</ul>\n<ul>\n<li>What does the SQL profile of the report XYZ or of transaction ABCD look like?</li>\n</ul>\n<p>All of this performance/usage data can be used to optimize existing ABAP programs for HANA in the best possible way.<br/><br/>The data collection of the SQL monitor is implemented directly in the NW kernel in a highly optimized manner to ensure that live operation is not disrupted.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Performance analysis, OPEN SQL optimization, HANA optimization</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>Availability of the SQL monitor:</p>\n<ul>\n<li>SQL monitor is available as of NW 740 SP2 as part of the SAP_BASIS component.<br/>Specifically for NW 740 SP2, please implement SAP Note 1834930 before using SQL monitor.</li>\n<li>Furthermore, SQL monitor is available in NW 702 (SAP_BASIS 702 SP 4) and in NW 703/731 (SAP_BASIS 731 SP09).</li>\n<li>In addition, the SQL monitor is available in the ST-PI add-on (ST-PI 2008_1_700 SP8) for all other NW Releases &gt;= NW 700.<br/>(For more information, see SAP Note 1855676.)<br/>Implement the following SAP Note(s) in advance for the ST-PI version of SQL monitor:</li>\n<ul>\n<li>SAP NetWeaver 700 and 701: SAP Note 1831460</li>\n<li>All other SAP NetWeaver 70x and 71x releases: First SAP Note 1806015 and then SAP Note 1831460</li>\n</ul>\n</ul>\n<p>For both variants, a 721 Kernel with PL &gt;= 119 is required for all NW Releases &lt; NW 740.<br/>(See SAP Note 1824769 for more details).</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>You can use transaction SQLM (ST-PI: /SDF/ZQLM) to manage SQL monitor in the system. Here you can activate and deactivate the SQL monitor or delete data.<br/><br/>For NW 740 SP2, in addition to activating the SQL monitor, the data collection must be scheduled as described in SAP Note 1859369.<br/><br/>You can use transaction SQLMD (ST-PI: /SDF/ZQLMD) to directly access the analysis of the SQL monitor data.<br/><br/>Required authorizations:<br/>(in addition to authorizations for executing SQL monitor transactions themselves)<br/><br/>SQL monitor authorizations:<br/>Authorization object S_ADMI_FCD<br/>Authorization value SQMA (administration of SQL monitor)<br/>Authorization value SQMD (display of SQL monitor data)<br/><br/>ST-PI SQL monitor authorizations:<br/>Authorization object S_ADMI_FCD<br/>Authorization value ST0M (administration of SQL monitor)<br/>Authorization value ST0R (display of SQL monitor data)<br/><br/><strong>Important note:</strong> Each production system has different active processes and a different number of process variants. As a result, it is difficult to predict the number of expected SQL monitor entries that will be created within a certain period (such as a day or a month). For this reason, we recommend that you observe the number of SQL monitor data records created during the first hours after you activate SQL monitor (the number of SQL monitor data records is displayed on the start screen of SQL monitor). If more than 2 million SQL monitor records are generated or the SQL monitor is active for more than two weeks in a row, we recommend deactivating SQL monitor and restarting it if necessary. This recommendation applies to SQL monitor as part of the component SAP_BASIS in Releases 740 SP2 - SP7, 731 SP09 and higher, 702 SP14 and higher, and to SQL monitor in the ST-PI add-on. For the SQL monitor as part of the SAP_BASIS component with Release 740 SP8 and above and Release 750 and above, the recommendations given in SAP Note 3242700 - ABAP SQL Monitor: Implementation Guide and Best Practices - apply.</p></div>", "noteVersion": 10}, {"note": "1357244", "noteTitle": "1357244 - High Performance SAP Kernel for Windows", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>SAP introduced a new optimization technology into selected kernels on the Windows platform. With this technology, performance achievements of up to 20% can be achieved, compared to previous versions of SAP kernels.<br/><br/>The optimization technology is a product feature in all supported kernel versions.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>PGO, High Performance Kernel, optimization, benchmark kernel</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>This note informs about PGO availability in SAP kernels for the Windows platform</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>For many years SAP kernels for Windows were optimized using standard compiler functionality which optimizes executables and libraries based on static code analysis. This has changed with more recent releases, where SAP introduced Performance Guided Optimization (PGO) into the product build process on Windows. This technology analyzes SAP code during application runtime and identifies the most frequently used code paths. This information is fed back into the build process and results in better code optimization compared to static code analysis.<br/><br/>Running PGO optimized executables can result in up to 20% more throughput compared to SAP kernels without PGO running on the same hardware.In many situations however, runtimes of jobs/user requests may not benefit in the range of up to 20%, since the performance gain usually shows up in scenarios where the CPU load on servers is extremely high.<br/><br/><br/></p></div>", "noteVersion": 23}, {"note": "1999681", "noteTitle": "1999681 - ABAP Compiler error message informing you that program contains generation errors", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>The activation of an ABAP program results in the issue of a message telling you that the program contains generation errors even though the ABAP syntax check indicates that the program has no errors.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Precompiled header</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>The ABAP program to be activated uses a global ABAP class that has a FRIEND relationship with another ABAP class that has already been deleted.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>A dependency for FRIEND relationships must be added to dependency management for precompiled headers for global classes. This enhancement is delivered through the import of a new kernel that contains the kernel patch with the text \"ABAP-PH: Add missing FRIEND dependency\".</p>", "noteVersion": 2}, {"note": "1722708", "noteTitle": "1722708 - ENQU: Session quota", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>The correction from this SAP Note is a new development in the management of locks. The correction introduces a new function called \"Session Quota\".<br/><br/>At the moment, the lock table can be completely filled by a single user. This causes a problem if there are other users and applications apart from this user who cannot even set a very small quantity of locks. The introduction of \"Session Quota\" can reduce the negative affects of this incorrect behavior or it can help to prevent this behavior from occurring.<br/><br/>When you activate the quota with the profile parameter enque/server/session_quota, you determine which part of the entire lock table a lock owner can occupy. The number applies to all users. The specification is a percentage from 1 to 100 whereby specifications under 10% are automatically increased to 10%. The default value of this parameter is 100; this matches \"Session Quote\" with the current behavior.<br/><br/>Note the following important information: No space is reserved in the lock table. If the quota \"Session Quota\" is reached, nothing other than a table overflow is processed only at user level. Therefore, the error handling is also similar. If there is an overflow of the quota, an entry is written in the syslog (SM21) and an exception is triggered. Applications may react to this in the same way as for a table overflow: Wait and repeat, terminate, release part of the locks to make free space in the lock table.<br/><br/>If the Boolean profile parameter enque/quota_exceeded_dump (= true, TRUE, 1, false, FALSE, 0) is also set, the system generates the short dump \"ENQUEUE_QUOTA_EXCEEDED\" (ST22). In this case, applications terminate without having the option to react to the triggered exception. Locks are automatically released.<br/><br/>This function is available as of Patch 35 for Release 721.<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>enque/server/session_quota, enque/quota_exceeded_dump<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>This is a new development.<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>This new function is available as of Patch 35 for Release 721.<br/></p></div>", "noteVersion": 1}, {"note": "2027624", "noteTitle": "2027624 - RESTGUI: Timer feature is not supported", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>SAP GUI timer feature is not supported in RESTGUI.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Please install the kernel patch specified in the corresponding section of this note.</p>", "noteVersion": 1}, {"note": "1825215", "noteTitle": "1825215 - SAP PERSONAS kernel part backport", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>New function SAP PERSONAS</p>\n<p>- Following a PERSONAS CALL TRANSACTION, the system does not fully refresh the basic screen.</p>\n<p>- Enhancement of kernel call DYNP_SET_STATUS</p>\n<p>- SAP PERSONAS is not available for SAP Kernel 741 and 721.</p>\n<p>    The correct feature query takes place only as of Patch Level 418 for SAP Kernel 721 and Patch Level 214 for SAP Kernel 741.</p>\n<p>- Ensuring a valid XML for DataManager</p>\n<p> </p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>Required kernel enhancements for support of SAP PERSONAS.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Use a disp+work package with the patch level that is specified under \"SP Patch Level\".<br/></p></div>", "noteVersion": 8}, {"note": "1960085", "noteTitle": "1960085 - SAP PERSONAS kernel traces", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>A SAP PERSONAS actuation should write a kernel trace.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Implement a disp+work that has the patch level specified in \"Support Packages &amp; Patches\".<br/> </p>", "noteVersion": 1}, {"note": "1919888", "noteTitle": "1919888 - Debugging the applications of another user", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You want to investigate an error in an SAP production system using debugging. <br/>The application user who can use the application in the production system is not authorized to carry out debugging and does not have the necessary knowledge for this, either.<br/>The support user is authorized to use the debugger but does not have the authorizations and knowledge necessary to use the application.<br/>It is not practical for the two users to meet in order to investigate the error together. This might be the case if, for example, an SAP Support employee wants to investigate a critical application in a customer's production system.</p>\n<p>If would be desirable for the support user to be able to investigate the application - used by the application user in the application user's user context - in the debugger running in the user context of the support user.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>TPDA, external debugging</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>The desired function has not yet been implemented.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Just like the activation of the debugger for the current user with OK code /h, the debugger can now be activated for another user, too. The OK code for this is as follows: /hext user = &lt;user name&gt;.</p>\n<p>Note the following:<br/>If you are using kernel releases lower than 777, spaces must be placed before and after the equals sign, e.g. \"/hext user = U1\".<br/>If you are using kernel release 777 or higher, you can write the command with or without spaces, e.g. \"/hext user = U1\" or \"/hext user=U1\".</p>\n<p>The application user can now activate the debugging of the corresponding application by the support user without requiring debugging authorization.</p>\n<p>The following conditions must be met to activate the debugger (otherwise, the system issues error message 00 127: \"This function is not possible\").</p>\n<ul>\n<li>The user specified in &lt;user_name&gt; has authorizations for general debugging and - specifically - the debugging of the programs of the current user (the user who activates debugging). For more information, see SAP Note 1788514.</li>\n<li>The user specified in &lt;user_name&gt; has already set user breakpoints for the current user (the user who activates debugging).</li>\n<li>The SAP GUI logon of the user specified in &lt;user_name&gt; that was used to set the user breakpoints is still active (the debugger data is displayed in a new external session of this GUI logon).</li>\n<li>For SAP_BASIS Release 740 and above, at least the downward-compatible disp+work package 742 with Patch Level 315 is required. See also SAP Note 2099670 in this regard.</li>\n</ul>\n<p>The debugger is linked with the session to be debugged for as long as in the case of normal debugging with the OK code /h. The link can be explicitly broken by the application user with the OK code /hx.</p>\n<p>The procedure for debugging external programs might be as follows:</p>\n<ol>\n<li>Application user A and support user B make contact by telephone, for example. They decide to carry out a debugging session.</li>\n<li>Support user B sets user breakpoints for application user A at the relevant ABAP statements of the application program. Support user B then asks application user A to start debugging.</li>\n<li>Application user A starts the application and enters the OK code \"/hext user = B\" (without the quotation marks used for clarity here). The system activates the debugger. The application is then used accordingly.</li>\n<li>When a user breakpoint is reached, the system stops. The debugger data is presented to support user B in a separate session in the support user's GUI logon. Support user B can now debug the program.</li>\n<li>Debugging can be ended by application user A exiting the application or explicitly entering the OK code /hx. The debugger window in the logon of user B closes.</li>\n</ol>\n<p>This procedure achieves the following:</p>\n<ul>\n<li>Only the application user can trigger the debugging of an application by another user. This means that no application can be investigated without the knowledge of the application user.</li>\n<li>The application user does not need debugging authorization. The support user can still debug the application.</li>\n<li>The support user does not need application authorizations since the application continues to run in the context of the application user and is controlled by the application user.</li>\n<li>The support user needs general debugging authorization and specific authorization to debug applications of the application user (see SAP Note 1788514).</li>\n<li>The system logs all relevant actions of the support user in the debugger (such as the changing of field content) in the system log and audit log in the application context.</li>\n<li>The application and support users can work in different places; telephone contact is sufficient.</li>\n</ul>", "noteVersion": 3}, {"note": "1860677", "noteTitle": "1860677 - Read Access Log (RAL)", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>Missing Read Access Log functions</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>RAL has not yet been implemented.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><ol>1. Apply the latest kernel patch. For the lowest patch level for the relevant release, see the section \"Support Package patch level\".</ol> <p>              (Patch text: Read Access Log) <p>              Check SAP Service Marketplace to see whether the patch is already available for your platform, and apply it in accordance with SAP Note 19466. <ol>2. Use the specified Support Package (or higher).</ol> <p><br/>RAL can be switched to active or inactive using the (dyn.) profile parameter 'sec/ral_enabled_for_rfc'.</p></p></p></div>", "noteVersion": 1}, {"note": "2000465", "noteTitle": "2000465 - Support of AUTH LOGIN as SMTP client", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>The 3rd party SMTP server supports only authentication with method LOGIN, but the SAP SMTP client does not support this method and does not send any authentication data.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>SASL, SMTP, AUTH PLAIN, Office365, outlook.office365.com</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>Authentication method LOGIN was not yet supported when acting as SMTP client.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Apply at least the patch level specified in the section \"SP Patch Level\".</p>", "noteVersion": 1}, {"note": "1890546", "noteTitle": "1890546 - Spool requests larger than 2 GB", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>Up to now, spool requests had a size restriction of 2 GB. If this is exceeded, the spool request can no longer be processed completely.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>TemSe, spool lists, TemSe part, gigabyte, rstsf_tel_040_22707_131, E06 Assurance in too many DB rows</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>The restriction is caused by the size counter of the TemSe.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Apply the attached kernel patch. The patch creates a new TemSe part when the maximum size is reached. As a result, the restriction is no longer applicable.</p>\n<p>Kernel 721: disp+work package, patch 227</p>\n<p>Kernel 738: disp+work package, patch 55</p>\n<p>Kernel 740: disp+work package, patch 64</p>\n<p>Kernel 741: disp+work package, patch 30</p></div>", "noteVersion": 3}, {"note": "2029589", "noteTitle": "2029589 - Support for QR code and data matrix bar code (kernel part)", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You want to define data matrix or QR code bar codes in your SAPscript forms or Smart Forms.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Data matrix, QR, 2D bar code</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>Missing support for data matrix bar code and QR code for Smart Forms and SAPscript forms</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Apply the kernel patch specified below (\"disp+work package\") and the corresponding ABAP Support Package, or implement the correction instructions from SAP Notes <a href=\"/notes/2029824\" target=\"_blank\">2029824</a> and <a href=\"/notes/2030263\" target=\"_blank\">2030263</a>.</p>", "noteVersion": 4}, {"note": "1816392", "noteTitle": "1816392 - New profile parameter sapgui/nwbc_scripting", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>Evaluation of new profile parameter sapgui/nwbc_scripting.</p>\n<p>Setting this new parameter to \"true\" is the same as:</p>\n<p>sapgui/user_scripting = 'TRUE'</p>\n<p>sapgui/user_scripting_set_readonly = 'TRUE'</p>\n<p>However, this ensures that the parameter user_scripting can only be active in conjunction with the _readonly parameter.</p>\n<p>We recommend that you use this setting if you use NWBC with side panels.</p>\n<p> </p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>RZ11</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Use a disp+work that has the patch level specified in the \"SP Patch Level\" section.</p>", "noteVersion": 3}, {"note": "1787163", "noteTitle": "1787163 - Message Server: save logon groups feature", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>The information about the logon groups could be saved and restored during message server stop/start operations.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>ms/persist_lg_info, logon groups, RFC</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>Special development.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>The feature is controled by profile parameter ms/persist_lg_info:<br/><br/>ms/persist_lg_info = { on | off | global }<br/><br/>A file is created when the feature is activated. The file is updated each time the logon group information is updated.<br/><br/>ms/persist_lg_info = off                no file created<br/>ms/persist_lg_info = on                 creates and updates  $(DIR_HOME)/&lt;SAPSID&gt;_msg_server_adtl_storage<br/>ms/persist_lg_info = global             creates and updates  $(DIR_GLOBAL)/&lt;SAPSID&gt;_msg_server_adtl_storage<br/><br/>The feature is not available in all releases.<br/>For release availability see section \"Releases\" in this note.</p></div>", "noteVersion": 2}, {"note": "1709868", "noteTitle": "1709868 - VMC: Java debugging with the VMC NewDebugger", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>The ABAP report RSVMCRT_MINI_DEBUGGER can be used to debug VMC function modules in a system. This report provides a pure text interface for debugging.<br/><br/>As a replacement on the basis of the ABAP NewDebugger, the VMC NewDebugger was developed.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>VMC, JAVA, RSVMCRT_MINI_DEBUGGER, VMCJDB, VMCDBG</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>This SAP Note deals with the VMC change. The details about the enhancement on the ABAP side are described in SAP Note 1678626.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>The complete function that was contained in the VMC MiniDebugger up to now is made available in a GUI that can be used with the mouse. Moreover, there are new features such as syntax coloring or uploading source code from local sources.<br/><br/>The VMC NewDebugger can be used  in the kernel patch level that is specified in the attachment on the 'SP Patch Level' tab page,<br/>in a VMC system.<br/></p></div>", "noteVersion": 1}, {"note": "1899984", "noteTitle": "1899984 - SYB: Executing SQL scripts using sybctrl", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You want to execute SQL scripts, eg. regular scheduled maintenance jobs, in an unattended way. Good security practice forbids to store the password for user 'sapsa' or 'SAPSR3' unsecured together with the command.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>sybctrl sybxctrl</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>Sybase ASE provides its own user management with traditional user/password authentication upon connection. Advanced security mechanisms eg. Network Based Security or Kerberos can also be employed but require advanced knowledge and infrastructure. To perform periodical administrative tasks in the database it is necessary to connect to ASE and run scripts and checks in the databases with SQL commands. It is not desirable to store the password of the ASE user in plain text somewhere in the file system.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>The sybctrl program has been enhanced to allow execution of defined SQL scripts making use of the secure store of the AS ABAP kernel.<br/>This allows automated execution of SQL scripts and storing credentials in a safe place.<br/><br/>The feature is available for:</p>\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr><th>SAP Kernel</th><th>sybctrl</th><th>DBSL</th></tr>\n<tr>\n<td>7.21 EXT 64Bit UC</td>\n<td>PL 132</td>\n<td>PL 132</td>\n</tr>\n<tr>\n<td> </td>\n</tr>\n</tbody>\n</table></div>\n<p>Download the corresponding software components from SAP Service Marketplace.</p>\n<p><strong>Installing scripts</strong></p>\n<ol>1. Store the SQL commands to be exeucted in a text file.</ol><ol>2. Load the text file into the SAP database using the new 'load_script' option of 'sybctrl':</ol>\n<p><br/>Example:<br/>Store the below SQL commands text in a file 'test.sql' :<br/><br/>use &lt;DBSID&gt; --replace &lt;DBSID&gt; with the name of your SAP database<br/>go<br/>select user_name()<br/>go<br/>setuser 'SAPSR3'<br/>go<br/>select user_name()<br/>go<br/>SELECT * from SVERS<br/>go<br/><br/>Now load the script into the database with the command :<br/><br/>    sybctrl load_script test.sql -exe /&lt;FULL_PATH_TO_DIR&gt;/isql<br/><br/>The above command loads the commands from script test.sql into SAP database for user 'sapsa'.<br/><br/>The full syntax of the load_script option of sybctrl is :<br/><br/>    sybctrl load_script &lt;script&gt; -exe &lt;isql executable&gt; [-path &lt;isql libpath&gt;] [-args &lt;isql arguments&gt;] [-appserver &lt;application server&gt;] [-auth &lt;authorization&gt;]</p>\n<ul>\n<li>&lt;script&gt;</li>\n</ul>\n<p>           This is a mandatory part of the command, provides the name of the script to be loaded. It must exist as a readable file.</p>\n<ul>\n<li>-exe &lt;isql executable&gt;</li>\n</ul>\n<p>           This is a mandatory part. sybctrl calculates a checksum of the isql executable and verifies that the 'isql' executable called when executing the script is the same as when the script was loaded. This is a security feature. It requires scripts to get reloaded after an upgrade of the isql executable.<br/>           N.B. You have to make sure that the 'isql' executable which you specify is not compromised at the time when loading the script.</p>\n<ul>\n<li>-path &lt;isql libpath&gt;</li>\n</ul>\n<p>           This part is optional - it provides the path to the shared libraries used by isql. sybctrl calculates a checksum of the library files found in the directory and compares this to the checksum at execution time.</p>\n<ul>\n<li>-args &lt;isql arguments&gt;</li>\n</ul>\n<p>           This part is optional. Here you can provide additional command line option for isql. For a description of available command line options of 'isql' refer to the documentation for 'isql'.</p>\n<ul>\n<li>-appserver &lt;application server&gt;</li>\n</ul>\n<p>           This part is optional. Specifies the application server where the script is executed.</p>\n<ul>\n<li>-auth &lt;authorization&gt;</li>\n</ul>\n<p>           Optional part of the sybctrl command. Specifies which database server login is used to connect. Possible values are 'sapsa' or 'SAPSR3'. If not specified it defaults to 'sapsa'.<br/><br/></p>\n<p><strong>Executing scripts</strong></p>\n<p><br/>Execute scripts using the 'exec_script' option of sybctrl.<br/><br/>Example:<br/>To execute the test script loaded with the command above, execute:<br/><br/>    sybctrl exec_script test.sql test.out</p>\n<p>On UNIX and Linux use the sybxctrl copy of sybctrl:</p>\n<p>    sybxctrl exec_script test.sql test.out</p>\n<p>This is necessary because sybctrl has the s-bit set; security rules will reset environment variables PATH and LD_LIBRARY_PATH during user switch causing shared libraries not to be found.</p>\n<p>The output of the script 'test.sql' is stored in file test.out. Review the output file for success of the SQL script execution.</p>\n<p><br/><br/>The full syntax of the exec_script option of sybctrl is :<br/><br/>    sybctrl exec_script &lt;script&gt; &lt;script output file&gt; [-auth &lt;authorization&gt;]</p>\n<ul>\n<li>&lt;script&gt;</li>\n</ul>\n<p>           This is a mandatory part of the command, provides the name of the script to be executed. It must have been loaded with the 'load_sript' option for the user specified.</p>\n<ul>\n<li>&lt;script output&gt;</li>\n</ul>\n<p>           This is a mandatory part of the command. It must specify a filename with read/write access.</p>\n<ul>\n<li>-auth &lt;authorization&gt;</li>\n</ul>\n<p>           Optional part of the sybctrl command. Possible values for &lt;authorization&gt; are 'sapsa' and 'SAPSR3'. If not specified it defaults to 'sapsa'.<br/>           <br/>As the return code of isql is passed to the caller of sybctrl, you have to examine the output file to determine whether desired action was indeed carried out.</p>\n<p><strong> </strong></p>\n<p><strong>Deleting scripts</strong></p>\n<p><br/>Use the 'delete_script' option of sybctrl to delete scripts from the database.<br/><br/>Example:<br/>To delete the test script, execute:<br/><br/>    sybctrl delete_script test.sql test.out <br/><br/><br/>The full syntax of the delete_script option of sybctrl is:<br/><br/>    sybctrl delete_script &lt;script&gt; [-auth &lt;authorization&gt;]</p>\n<ul>\n<li>&lt;script&gt;</li>\n</ul>\n<p>           This is a mandatory part of the command, provides the name of the script to be deleted. It must have been loaded with the 'load_sript' option for the user specified.</p>\n<ul>\n<li>-auth &lt;authorization&gt;</li>\n</ul>\n<p>           Optional part of the sybctrl command. Possible values for &lt;authorization&gt; are 'sapsa' and 'SAPSR3'. If not specified it defaults to 'sapsa'.</p>\n<p><strong>Reading scripts stored in the database</strong></p>\n<p><br/>Use the 'unload_script' option of sybctrl to inspect scripts stored in the database.<br/><br/>Example:<br/>To unload the test script, execute:<br/><br/>    sybctrl unload_script test.sql /tmp <br/><br/>The output of the script 'test.sql' is stored in file test.sql in directory /tmp.<br/><br/>The full syntax of the exec_script option of sybctrl is :<br/><br/>    unload_script &lt;script&gt; &lt;directory&gt; [-auth &lt;authorization&gt;]</p>\n<ul>\n<li>&lt;script&gt;</li>\n</ul>\n<p>           This is a mandatory part of the command, provides the name of the script to be unloaded. It must have been loaded with the 'load_sript' option for the user specified.</p>\n<ul>\n<li>-auth &lt;authorization&gt;</li>\n</ul>\n<p>           Optional part of the sybctrl command. Possible values for &lt;authorization&gt; are 'sapsa' and 'SAPSR3'. If not specified it defaults to 'sapsa'.<br/><br/></p>\n<p>Loaded scripts are stored in the &lt;SAPSID&gt; database  in tables \"sybsisql\" for sapsa user scripts, \"sybsisql_sapsr3\" for SAPSR3 user scripts. The name of the script is stored in column \"script_name\" in all three cases.</p>\n<div>\n<div class=\"copy-paste-block\"></div>\n</div></div>", "noteVersion": 4}, {"note": "1969459", "noteTitle": "1969459 - SIQ: DBCON connections to DBMS IQ", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>DBMS type SYB has to be specified as DBMS type in transaction DBCO to configure a secondary database connection to Sybase IQ. DBMS type SYB specifies a connection to either Sybase ASE or Sybase IQ.<br/>The DBA Cockpit and the SAP Solution Manager rely on the DBMS type specified in transaction DBCO. The DBMS type should be unambiguous.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>SAP SYBASE ASE, SAP Sybase IQ</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>See symptom</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>A new DBMS type 'SIQ' (Sybase IQ) has been introduced for remote connections.</p>\n<p>Use SAP Basis 7.02 SP15, 7.30 SP11, 7.31 SP11 or 7.40 SP6 or higher.<br/>Use patch level 21 for SAP kernel release 741.<br/>Use patch level 54 for SAP kernel release 740.<br/>Use patch level 218 for SAP kernel release 721.</p>", "noteVersion": 6}, {"note": "1964997", "noteTitle": "1964997 - ST: Enhancement of kernel statistics for RFC subrecords", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>The rules for storing RFC statistics subrecords have changed. However, they remain fully compatible with the previous rules. As previously, there is a maximum number of entries to be stored. This number now follows extended rules. This SAP note explains the function of the new rules.</p>\n<p><strong>Introduction</strong><br/>The profile parameter stat/rfcrec with default value '5' determines how many entries are stored under RFC subrecords RFC CLIENT, RFC CLEINT DESTINATION, RFC SERVER and RFC SERVER DESTINATION. If a program executes more RFC calls than specified in &lt;stat/rfcrec&gt;, or if an RFC server receives more calls than &lt;stat/rfcrec&gt;, a maximum of &lt;stat/rfcrec&gt; calls are saved. The entries to be saved are selected based on an internal rule that only selects the \"most costly\" entries. All other entries are rejected.</p>\n<p>Example 1 (stat/rfcrec = 3):<br/>F1, F2, F3, F2, F4, F2, F5, F2, F6, F2, F4, F6 are called<br/>F3, F4, F2 are saved</p>\n<p><strong>Enhancement<br/></strong>The new rules are based on the names of the called function modules. Function modules with the same name (independently of which RFC connections they belong to) are combined together in groups. For each group, the same sorting rules are applied as previously. This means that the total number of entries to be saved can grow indefinitely. In other words: If a program executes multiple function modules or if an RFC server receives multiple calls, the function modules are grouped by name and a maximum of &lt;stat/rfc/distinct_depth&gt; are saved in each group.</p>\n<p>Example 2 (stat/rfc/distinct_depth = 3):<br/>F1, F2, F3, F2, F4, F2, F5, F2, F6, F2, F4, F6 are called<br/>F1, F2, F2, F2, F3, F4, F4, F5, F6, F6 are saved</p>\n<p><strong>STAD</strong><br/>Transaction STAD is compatible with the new procedure.</p>\n<p><strong>Activation</strong><br/>The new procedure is not active by default. The procedure is activated using the profile parameter <strong>stat/rfc/distinct</strong> = {1, true, TRUE}. This can be set both in the profile and dynamically. Dynamic setting of the parameter is supported by function module TH_CHANGE_PARAMETER. To dynamically deactivate the new procedure, set the parameter to the value {0, false, FALSE}.</p>\n<p>If the new procedure is active, the new parameter <strong>stat/rfc/distinct_depth</strong> determines the maximum number of entries to be saved. The parameter stat/rfcrec is deactivated. The default value of the parameter stat/rfc/distinct_depth is &lt;stat/rfcrec&gt;. If the parameter stat/rfc/distinct_depth is set to 0, all RFC calls are stored. The value of stat/rfc/distinct_depth can also be changed dynamically.</p>\n<p>Example 3 (stat/rfc/distinct_depth = 0):<br/>F1, F2, F3, F2, F4, F2, F5, F2, F6, F2, F4, F6 are called<br/>F1, F2, F3, F2, F4, F2, F5, F2, F6, F2, F4, F6 are saved</p>\n<p><strong>Maintenance<br/></strong>The dynamic profile parameter <strong>stat/recex/memory_check</strong> = {1, true, TRUE, 0, false, FALSE} has been introduced for internal purposes. When set, this activates complex save checks that can help if problems occur.</p>\n<p><strong>Corrections</strong><br/>1. The ABAP server crashes if statistical profile parameters are changed using transaction ST03 (see \"Online Parameters -&gt; Dialog Step Statistics\"). Patch 223.<br/>2. Forgotten trace in the function \"PfIRecExAdmSlotInitRfcSrvAux\"</p>\n<p><strong>Delivery</strong><br/>The described enhancement exists only in SAP Kernel 721.<br/>Patch 215 - delivery of basic functions<br/>Patch 217 - implementation and handling of new profile parameters<br/>Patch 223 - correction 1 (change of profile parameters using transaction ST03)<br/>Patch 332 - introduction of additional subset for RFC server. The new subset is exclusively handled internally.<br/>Patch 415 - Correction 2 (forgotten trace in the function \"PfIRecExAdmSlotInitRfcSrvAux\")</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>RFC UCON STAT</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>This is an enhancement.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Implement a new kernel patch (but it must be at least the patch specified in the \"SP Patch Level\" section).</p>\n<p> </p>", "noteVersion": 5}, {"note": "1640285", "noteTitle": "1640285 - Determine MIME type with Virus Scan Interface", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>*********************************************************************<br/>Do not implement this SAP Note manually unless SAP requests this explicitly or this note is a prerequisite for the implementation of another SAP Note.<br/><br/>Manual implementation of this SAP Note requires SAP Note 1669429.<br/>*********************************************************************<br/><br/>Uploading or downloading binary files with unknown content might be problematic if the files are opended by other clients.<br/><br/>In cases where you trust the file extension, wrong MIME types or generic MIME types, e.g. \"application/octet-stream\" may lead to so-called mime-sniffing when the files are downloaded by browsers.<br/><br/>Additionally you may want to configure that active content in files (for example macros or scripts) should be blocked generally, not only when the scanner classifies them as harmful.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>VSI, CL_VSI, VSIService, Virus Scan Interface, NW-VSI, VSA, MIME.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>Prerequisite for the configuration in ABAP is SAP Note 1669429.<br/><br/>External Prerequisites (see note 1494278):</p>\n<ul>\n<li>The active content detection is available in the product you have in use.</li>\n</ul>\n<ul>\n<li>The external product, which you use via VSI, returns the detected MIME type.</li>\n</ul>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>The Virus Scan Interface (VSI) uses capabilities of the underlying virus scanners to filter and detect files based on active content and MIME types.</p>\n<ul>\n<li>Enhanced Maintenance Capabilities (ABAP only)<br/><br/>In the customizing of the virus scan profiles (view cluster VSCAN_PROFILE_VC, shortcut transaction VSCANPROFILE), the following new elements are present:</li>\n</ul>\n<ul>\n<ul>\n<li>New maintenance level \"Profile Configuration Parameters\"<br/><br/>In this section, you can maintain the parameters whose name starts with \"CUST_\" and which are partly explained below. These parameters apply to the profile in total.<br/><br/>The former location where those parameters could be maintained was underneath the \"Steps\" and was logically incorrect for \"CUST_\"-parameters (although, in the frequent case that only a single step was defined, equivalent). This section still exists and is now renamed to \"Step Configuration Parameters\".<br/><br/>The section \"Profile Configuration Parameters\" is evaluated only when you set the indicator \"Evaluate Profile Configuration Parameters\" in the profile header. This indicator is set by default for new profiles and new installations, but remains unset for existing profiles during update or patch implementation for compatibility reasons.<br/><br/>When profiles are nested either by using a reference profile or profiles as steps, the profile configuration parameters and the MIME-Type list (see below) are evaluated only for the first profile in the chain that has the \"Evaluate...\" parameter set and then propagated down to the scanner engine.</li>\n</ul>\n</ul>\n<ul>\n<ul>\n<li>New maintenance level \"MIME-Types\"<br/><br/>In this section you can maintain a list of MIME types that shall act as either include list or exclude list for scans executed for this profile.<br/><br/>The evaluation of this section is only enabled when you have maintained the parameter CUST_CHECK_MIME_TYPE with value \"1\" in the profile configuration parameters.<br/><br/>In this case, the data maintained in this section overwrite (without merge) the data maintained in the scan-parameters SCANMIMETYPES and BLOCKMIMETYPES if those are maintained at group or step level.<br/><br/>To make the maintained list of MIME-Types a exclude list, set the profile configuration parameter CUST_MIME_TYPES_ARE_BLACKLIST with value \"1\" in addition.</li>\n</ul>\n</ul>\n<ul>\n<li>New parameters for VSI configuration</li>\n</ul>\n<ul>\n<ul>\n<li>CUST_ACTIVE_CONTENT<br/><br/>If set to \"1\", all files with active content, for example HTML with JavaScript or PDF with JavaScript or office documents with macros will be blocked.</li>\n</ul>\n</ul>\n<ul>\n<ul>\n<li>CUST_CHECK_MIME_TYPE<br/><br/>If set to \"1\", the external VSA must return the content info (MIME type) of the scanned object. The VSA must know the parameters SCANMIMETYPES, SCANEXTENSIONS, BLOCKEXTENSIONS and BLOCKMIMETYPES.</li>\n</ul>\n</ul>\n<ul>\n<ul>\n<li>CUST_MIME_TYPES_ARE_BLACKLIST (only ABAP)<br/><br/>If set to \"1\", the defined MIME type are not used as include list but as exclude list, which means all MIME types in the defined list should be blocked.</li>\n</ul>\n</ul>\n<ul>\n<ul>\n<li>BLOCKEXTENSIONS<br/><br/>This parameter must be known in the VSA. The string specifies a list of file extensions, which must be blocked.</li>\n</ul>\n</ul>\n<ul>\n<ul>\n<li>BLOCKMIMETYPES<br/><br/>This parameter must be known in the VSA. The string specifies a list of MIME types, which must be blocked.</li>\n</ul>\n</ul>\n<ul>\n<li>Meaning of existing VSI parameters</li>\n</ul>\n<ul>\n<ul>\n<li>SCANMIMETYPES<br/><br/>This parameter must be known in the VSA. The string specifies a list of MIME types, which are allowed to be passed, all other types must be blocked.</li>\n</ul>\n</ul>\n<ul>\n<ul>\n<li>SCANEXTENSIONS<br/><br/>This parameter must be known in the VSA. The string specifies a list of file extensions, which are allowed to pass, all other types must be blocked.</li>\n</ul>\n</ul>\n<p><br/>The extension is integrated in VSI version 1.8.</p></div>", "noteVersion": 4}, {"note": "1655743", "noteTitle": "1655743 - Auditing of dynamic ABAP source code", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>The system can log the dynamic creation (ABAP commands: INSERT REPORT and GENERATE SUBROUTINE POOL) and deletion (ABAP command: DELETE REPORT) of ABAP source code together. The system proposes two levels of detail for this. For one, the system proposes an overview logging in the Security Audit Log; for the other, the system proposes a detailed logging (including the generated source code) in the database. Both loggings can be changed and used separately.</p> <ul><li>Overview logging in the Security Audit Log</li></ul> <p>           The overview logging contains information about the time that an ABAP source code was dynamically created or deleted, who created or deleted this ABAP source code or which report was used to change the source code in the system. The generated source code is saved only for the detailed logging.</p> <ul><li>Detailed logging in the database</li></ul> <p>           For the detailed logging, the dynamically created source code is also logged in addition to the administration attributes and program attributes.<br/>           The logging is carried out in database tables.<br/>           <br/>Activating the individual loggings takes place with the following steps:</p> <ul><li>Overview logging in the Security Audit Log</li></ul> <p>           The activation of the overview logging takes place using the Security Audit Log (transaction <b><b>SM19</b></b>). You can activate the logging under the detail display under \"Other events\".</p> <ul><li>Detailed logging in the database</li></ul> <p>           The detailed logging should be activated only if auditing of the generated source code is required.<br/>           The detailed logging can be activated using the profile parameter <b>abap/dyn_abap_log </b>. The default value is \"off\". For further details, refer to the documentation about this profile parameter in transaction <b>RZ11</b>.<br/>           <br/>Explanations to some of the logged data:</p> <ul><li>Event identification</li></ul> <p>           For each logged event, the system generates a unique event identification. A connection between the Security Audit Log and the details in the database tables can be created with this identification number if both loggings are activated.</p> <ul><li>Event type</li></ul> <p>           The event type provides information about the ABAP command that was executed. In the documentation of the entry in the Security Audit Log (transaction <b>SM19</b>), there is an exact description of the event types in the system.</p> <ul><li>Fingerprint</li></ul> <p>           The fingerprint displays a characteristic string of the generated source code. During the generation of the source code, the system calculates if the source code is also persisted. From the persisted source code, an additional fingerprint can be calculated for the display that should be identical to the value that was calculated during the generation.<br/><br/>You can view information about the collected data:</p> <ul><li>Overview logging in the Security Audit Log</li></ul> <p>           The evaluation of the results takes place using the Security Audit Log (transaction <b>SM20</b>). The logs are displayed for each application server.</p> <ul><li>Detailed logging in the database</li></ul> <p>           You can find and evaluate the header data (such as user, date, time, generating report, generated report) using the data browser (transaction <b>SE16</b>) for the database table DYNABAPHDR. In the database table DYNABAPSRC, you can find the relevant source code in a compressed form under the event identification. You can decompress and display the source code using the ABAP program <b>RDYNABAP_SHOW</b>. The event identification is entered on the initial screen of the program. The program compares the fingerprint of the program with the fingerprint on the database and displays the result of the comparison on the detail screen.<br/></p> <b>Impact on performance</b><br/> <p>The activation of the logging of dynamically generated source codes causes the runtime for the programs that create the ABAP source code at runtime to worsen very slightly.<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>Auto ABAP, AutoABAP, SAPMSSY6</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>When you use the detailed logging, the source code and the source code attributes are saved in database tables. Depending on the ABAP programs used, this can lead to a large data volume in the database tables DYNABAPHDR and DYNABAPSRC.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>The tables DYNABAPHDR and DYNABAPSRC are checked periodically by the system and obsolete data records are automatically removed by the system. The persist duration can be defined using the profile parameter <b>abap/dyn_abap_log_storage_days</b>. The proposed value for this is 27 days. If this time frame is exceeded, the system removes the obsolete data from the system in blocks several times a day. Processing takes place in blocks whereby the block size can be defined using the profile parameter <b>abap/dyn_abap_log_deletion_rows</b>. The default value amounts to 10,000 entries that can be removed in one processing step. You can use transaction <b>RZ11</b> to display the profile parameters and you can use the profile files to set the profile files permanently on the application servers.<br/></p></div>", "noteVersion": 2}, {"note": "2037455", "noteTitle": "2037455 - Batch input: Canceling a transaction (soft cancel) and closing a window", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>1.)</p>\n<p>In transaction SM35, there are batch input sessions with the status \"In Process\".<br/>The corresponding batch input log contains message entries of the following type:</p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"0\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td valign=\"top\" width=\"351\">\n<p>OK code /I is not allowed in batch input</p>\n</td>\n<td valign=\"top\" width=\"3\">\n<p>E</p>\n</td>\n<td valign=\"top\" width=\"44\">\n<p>00</p>\n</td>\n<td valign=\"top\" width=\"47\">\n<p>354</p>\n</td>\n</tr>\n</tbody>\n</table></div>\n<p>However, the entry is made WITHOUT the usual processing statistics at the end of the log, such as the following:</p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"0\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td valign=\"top\" width=\"262\">\n<p>Processing statistics</p>\n</td>\n<td valign=\"top\" width=\"28\">\n<p>S</p>\n</td>\n<td valign=\"top\" width=\"37\">\n<p>00</p>\n</td>\n<td valign=\"top\" width=\"44\">\n<p>370</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"262\">\n<p>1 transaction read</p>\n</td>\n<td valign=\"top\" width=\"28\">\n<p>S</p>\n</td>\n<td valign=\"top\" width=\"37\">\n<p>00</p>\n</td>\n<td valign=\"top\" width=\"44\">\n<p>363</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"262\">\n<p>0 transactions processed</p>\n</td>\n<td valign=\"top\" width=\"28\">\n<p>S</p>\n</td>\n<td valign=\"top\" width=\"37\">\n<p>00</p>\n</td>\n<td valign=\"top\" width=\"44\">\n<p>364</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"262\">\n<p>1 transaction with errors</p>\n</td>\n<td valign=\"top\" width=\"28\">\n<p>S</p>\n</td>\n<td valign=\"top\" width=\"37\">\n<p>00</p>\n</td>\n<td valign=\"top\" width=\"44\">\n<p>365</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"262\">\n<p>1 transaction deleted</p>\n</td>\n<td valign=\"top\" width=\"28\">\n<p>S</p>\n</td>\n<td valign=\"top\" width=\"37\">\n<p>00</p>\n</td>\n<td valign=\"top\" width=\"44\">\n<p>366</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"262\">\n<p>Batch input processing ended</p>\n</td>\n<td valign=\"top\" width=\"28\">\n<p>S</p>\n</td>\n<td valign=\"top\" width=\"37\">\n<p>00</p>\n</td>\n<td valign=\"top\" width=\"44\">\n<p>382</p>\n</td>\n</tr>\n</tbody>\n</table></div>\n<p>2.)</p>\n<p>When you cancel a transaction (soft cancel), either only the current transaction ends or the function has no effect at all.</p>\n<p>3.)</p>\n<p>A session is processed in the processing mode \"Display Errors Only\". The popup with the processing options remains.<br/>If you now cancel the transaction (soft cancel) in this popup, the batch input session continues to run.</p>\n<p>4.)</p>\n<p>A batch input session processed in the background runs in a batch job. If you cancel this in transaction SM37, this appears not to have any effect.</p>\n<p> </p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>SM35, SM37</p>\n<p> </p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>1.)</p>\n<p>You try to close a visibly processed batch input session by closing the active session window in the SAP GUI (by clicking [X] in the top right-hand corner of the window). This is prevented by means of the stated error message.<br/>You then close the session from the session list (/O) by closing the user session in transaction SM04 or by closing the SAP logon application in the task manager. The session hangs with the status \"In Process\".</p>\n<p>2.)</p>\n<p>The soft cancel only works if the system is busy (with the exception of if the transaction is currently in the update phase; see SAP Note 888431). Even if the function does work, only the current transaction is canceled; the next transaction is then started in the session.<br/>This can cause you to close the session externally as described in 1.).</p>\n<p>3.)</p>\n<p>Again, as described in 2.), only the current transaction is canceled. Other transactions are then processed further in the batch input session. The popup remains.</p>\n<p>4.)</p>\n<p>Here, too, only the current transaction in the session is canceled.<br/>The job termination results in a SYSTEM_CANCELED runtime error that appears in transaction ST22 and in the batch input log for the session. The current transaction then has errors. The next transactions to be processed in the session are still processed.</p>\n<p> </p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Once you have applied the specified kernel patch level, if you execute a soft cancel during batch input session processing, the session is canceled properly with an error status.</p>\n<p>The batch input log then contains the following entries:</p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"0\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td valign=\"top\" width=\"272\">\n<p>Batch input processing canceled</p>\n</td>\n<td valign=\"top\" width=\"19\">\n<p>A</p>\n</td>\n<td valign=\"top\" width=\"37\">\n<p>00</p>\n</td>\n<td valign=\"top\" width=\"44\">\n<p>383</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"272\">\n<p>Transaction error</p>\n</td>\n<td valign=\"top\" width=\"19\">\n<p>S</p>\n</td>\n<td valign=\"top\" width=\"37\">\n<p>00</p>\n</td>\n<td valign=\"top\" width=\"44\">\n<p>357</p>\n</td>\n</tr>\n</tbody>\n</table></div>\n<p>The processing statistics are included at the end.</p>\n<p> </p>\n<p>In addition, the closing of the external session (using [X] in the SAP GUI window, the OK code \"/I\", or the relevant system menu option) during session processing is no longer prevented by the following message:</p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"0\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td valign=\"top\" width=\"280\">\n<p>OK code /I is not allowed in batch input</p>\n</td>\n<td valign=\"top\" width=\"16\">\n<p>E</p>\n</td>\n<td valign=\"top\" width=\"37\">\n<p>00</p>\n</td>\n<td valign=\"top\" width=\"44\">\n<p>354</p>\n</td>\n</tr>\n</tbody>\n</table></div>\n<p>Instead, it is handled as a batch input cancellation (OK code /BEND).</p>\n<p>As a result, the session is closed in the proper manner with an error status and a popup is displayed.</p>\n<p>The popup tells you that the processing of the batch input session has been canceled.<br/>It contains buttons for calling the session overview and closing batch input.</p>\n<p> <br/>The session can then be processed again without release.</p>\n<p> </p>\n<p>A SYSTEM_CANCELED termination now closes the batch input session.<br/>If the termination is in a background job, the session remains in the status \"In Process\" so that the administrator can check the cause of the job termination and then release the session.</p>", "noteVersion": 2}, {"note": "1891583", "noteTitle": "1891583 - Restricting logon to the application server", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You do not have the option of restricting the logon of users while maintenance work is carried out in the system. During that time, only certain administrators should be able to log on to the system.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>This feature is provided only via Support Package and it requires a minimum kernel patch (disp+work); refer to the \"SP Patch Level\" tab page for this. This function is provided only with kernel releases 721, 740, 741 and subsequent versions as of SAP_BASIS 731.<br/><br/>You can restrict the logon of users to the application server by setting the new profile parameter login/server_logon_restriction.<br/><br/>The following values are possible:</p>\n<ul>\n<li>0: No restriction.<br/><br/>All users can log on to the application server.</li>\n</ul>\n<ul>\n<li>1: A logon to the application server is allowed only with special rights.<br/><br/>Only those users whose assigned security policy contains the new attribute SERVER_LOGON_PRIVILEGE with the value 1 can log on to the system.<br/><br/>To change the security policy, use transaction SECPOL. Change the relevant security policy that you have assigned only to your administrators. Include the guideline attribute SERVER_LOGON_PRIVILEGE in the security policy and set the value to 1.<br/> <br/>Users who log on to the system without special rights see the following error message: Server is currently not generally available (restricted logon).</li>\n</ul>\n<ul>\n<li>2: No logon is allowed to the application server.<br/> <br/>Users who log on to the system see the following error message: Server is currently not available (logon not permitted).</li>\n</ul>\n<p><strong>Comments:</strong></p>\n<ul>\n<li>If you set the dynamic profile parameter, no users are logged off the application server.<br/> </li>\n<li>Use transaction RZ10 to save the value permanently.<br/> </li>\n<li>The where-used list in the user information system lets you determine which users have been assigned a security policy or policy attribute. To use it, call transaction SUIM and choose: Where-Used List -&gt; Security Policies -&gt; In Users.</li>\n</ul>\n<p><strong>Caution:</strong></p>\n<p>If you have activated the emergency user, SAP*, then a logon to the system with the SAP* user is <em>always</em> possible. <br/>The emergency user is active if the profile parameter login/no_automatic_user_sapstar is set to 0 and the SAP* user is <em>not</em> defined in transaction SU01.</p>", "noteVersion": 3}, {"note": "1913386", "noteTitle": "1913386 - WebGUI: Support for the theme SAP Blue Crystal", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>The SAP GUI for HTML contains a new theme called \"SAP Blue Crystal\".</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>its, sap gui for html, urgui</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>This note provides a new development.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>The correction will be delivered in one of the next patches. This SAP Note will be updated automatically as soon as the exact patch number is known. If the patch number for the relevant release is not yet specified in the \"SP Patch Level\", the patch is not available yet.<br/></p></div>", "noteVersion": 1}, {"note": "1745243", "noteTitle": "1745243 - ABAP Development Tools for SAP NetWeaver - debugging", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>In a system with SAP Basis Release 7.31, you use ABAP Development Tools for SAP NetWeaver for development purposes. Unfortunately, debugging does not work.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>You use a 7.20 kernel.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>You should use a 7.21 kernel.</p></div>", "noteVersion": 1}, {"note": "1732161", "noteTitle": "1732161 - SAP Systems on Windows Server 2012 (R2)", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>This note provides information about deploying and using SAP systems on Windows Server 2012 and Windows Server 2012 R2. Windows 2012 and Windows 2012 R2 have different PAM entries.</p>\n<p>This note describes which tools must be used for the installation, system copy, upgrade and update; which tools are deprecated and which prerequisites must be met to run SAP systems on Windows Server 2012 (R2).</p>\n<p>This note applies to the following SAP systems based on:</p>\n<ul>\n<li>SAP NetWeaver 7.0 SR3</li>\n</ul>\n<ul>\n<li>SAP NetWeaver 7.0 EHP1 SR1</li>\n</ul>\n<ul>\n<li>SAP NetWeaver 7.0 EHP2</li>\n</ul>\n<ul>\n<li>SAP NetWeaver 7.0 EHP3</li>\n</ul>\n<ul>\n<li>SAP NetWeaver CE 7.1</li>\n</ul>\n<ul>\n<li>SAP NetWeaver CE 7.2</li>\n</ul>\n<ul>\n<li>SAP NetWeaver PI 7.1</li>\n</ul>\n<ul>\n<li>SAP NetWeaver PI 7.1 EHP1</li>\n</ul>\n<ul>\n<li>SAP NetWeaver 7.3</li>\n</ul>\n<ul>\n<li>SAP NetWeaver 7.3 EHP1</li>\n</ul>\n<ul>\n<li>SAP NetWeaver 7.4</li>\n</ul>\n<ul>\n<li>SAP NetWeaver 7.5</li>\n</ul>\n<p><br/>Database-specific information is provided for the following databases:</p>\n<ul>\n<li>MaxDB</li>\n<li>SQL Server</li>\n<li>IBM DB2 for LUW</li>\n</ul>\n<p><br/>SAP standalone components information:</p>\n<ul>\n<li>SAP liveCache</li>\n</ul>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>liveCache, Windows Server 2012, Windows Server 2012 R2, SAP Netweaver</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>Check whether your system is released for productive use on Windows Server 2012 or Windows Server 2012 R2 in the SAP Product Availablity Matrix (PAM) at:<br/><a href=\"http://support.sap.com/pam\" target=\"_blank\">http://support.sap.com/pam</a></p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong>General Information</strong></p>\n<p><strong>PLEASE NOTE: Windows Server 2012 / 2012 R2 is no longer supported by Microsoft as of October 2023. Please pay attention to SAP note #3281142 (see references)! </strong></p>\n<p><strong>1. Required Service Packs and Patches for Windows Server 2012 (R2)</strong></p>\n<p>Microsoft publishes updates periodically which enhance the stability and performance of their Windows Server products. Therefore it is mandatory to run Windows Update before starting any installation activities.</p>\n<p><strong>2. SAP Software Provisioning Manager (SWPM)</strong></p>\n<p>It is mandatory to use the SAP Software Provisioning Manager for installation and system copy on Windows Server 2012 (R2). Do not use the SAP installation tools from your old installation media. Refer to SAP Note 1680045 on how to obtain the latest version of the SAP SWPM.</p>\n<ul>\n<li><strong>Windows Server 2012:</strong><br/><br/>\n<ul>\n<li>On Windows Server 2012 only the 720 EXT 64 bit fully downward compatible kernel (DCK) with minimum patch level 400 and the 721 EXT 64 bit DCK with minimum patch level 100 are supported for SAP systems running on a 7.00, 7.01, 7.10, 7.11 and 7.20 kernel. You need to use the EXT kernel media for SWPM.<br/><br/></li>\n<li>SAP Host Agent: Due to a problem with SMB 3.0, the SAP Host Agent 720 EXT provided with the SWPM kernel media needs to be updated manually before starting an installation. Download SAP Host Agent 720 EXT SAR file with a minimum patch level of 138 from the SAP Market Place. Rename the downloaded SAR archive to SAPHOSTAGENT.SAR. Locate the DBINDEP folder on your kernel media for SWPM and replace the old SAPHOSTAGENT.SAR archive with the downloaded version. Also have a look at SAP Note <a href=\"/notes/1823833\" target=\"_blank\">1823833</a> \"Accessing shares via SMB 3.0 can result in long waiting times\".</li>\n</ul>\n</li>\n</ul>\n<ul>\n<li><strong>Windows Server 2012 R2:</strong></li>\n</ul>\n<blockquote>\n<ul>\n<li>For SAP systems based on SAP Netweaver 7.0x, 7.1x, 7.2 and 7.3x, only the 721 EXT 64 Bit kernel or newer are supported.</li>\n</ul>\n<ul>\n<li>For SAP systems based on SAP Netweaver 7.4x, only the 741 64 Bit kernel or newer are supported.</li>\n</ul>\n<ul>\n<li>See also SAP Note <a href=\"/notes/1922495\" target=\"_blank\">1922495</a> regarding operating system versioningon Windows Server 2012 R2.</li>\n</ul>\n</blockquote>\n<p><strong>3. SAP Software Update Manager (SUM)</strong></p>\n<p>In order to simplify update/upgrade related activities, SAP consolidated several upgrade/update tools into the SAP SUM tool. In general, running on Windows Server 2012 (R2) only SAP upgrade/update paths are supported that are covered by the SUM tool. Exceptions to this will be documented in this note.</p>\n<p>For more information about SUM, read the relevantg SUM release note at <a href=\"https://support.sap.com/en/tools/software-logistics-tools.html\" target=\"_blank\">https://support.sap.com/en/tools/software-logistics-tools.html</a>.</p>\n<p><strong>4. High-Availability System</strong></p>\n<ol><ol>On Windows Server 2012 R2, the registration of the SAP Resource Type Library might hang. For more information, see SAP Note</ol></ol>\n<p><a href=\"/notes/1927098\" target=\"_blank\">1927098</a></p>\n<ol>.</ol>\n<p><strong>5. SAP Java Support Package Manager (JSPM)</strong></p>\n<p>SAP JSPM will be deprecated at the end of 2013. As a consequence, the SAP JSPM tool is not supported on Windows Server 2012 (R2). Use the SUM instead. For more information, see SAP Note <a href=\"/notes/1589311\" target=\"_blank\">1589311</a>.</p>\n<p><strong>6. Itanium</strong></p>\n<p>The Itanium architecture is not supported on Windows Server 2012 (R2).</p>\n<p><strong>Database-Specific Information for Windows Server 2012 (R2)</strong></p>\n<p><strong>1. MaxDB </strong></p>\n<ul>\n<li><strong>Windows Server 2012:</strong></li>\n</ul>\n<p>Released with at least the following versions:</p>\n<p>7.8.02.31 : Material Number 51046098</p>\n<p>7.9.08.05 : Material Number 51043941</p>\n<ul>\n<li><strong>Windows Server 2012 R2:</strong></li>\n</ul>\n<p>Released with at least the following version:</p>\n<p>7.9.08.08 : Material Number 51046733</p>\n<p><strong>2. SQL Server</strong></p>\n<p>SQL Server 2012 and higher releases are supported productively on Windows Server 2012 (R2). For information on how to install, copy, update, or upgrade your SAP system on SQL Server, see SAP Note <a href=\"/notes/1676665\" target=\"_blank\">1676665</a> (SQL Server 2012) / SAP Note <a href=\"/notes/1966701\" target=\"_blank\">1966701</a> (SQL Server 2014) / SAP Note <a href=\"/notes/2201060\" target=\"_blank\">2201060</a> (SQL Servr 2016).</p>\n<p>For more information about the supported SAP releases on SQL Server, see SAP Note <a href=\"/notes/1651862\" target=\"_blank\">1651862</a> (SQL Server 2012) / SAP Note <a href=\"/notes/1966681\" target=\"_blank\">1966681</a> (SQL Server 2014) /SAP Note <a href=\"/notes/2201059\" target=\"_blank\">2201059</a> (SQL Server 2016).</p>\n<p>On SQL Server 2012, upgrade/update paths to all products of SAP BS7i2008 and higher are supported. This is in particular true for upgrades to SAP SCM 7.0 (incl. EHP1) / CRM 7.0 (incl. EHP1) / SRM 7.0 (incl. EHP1), which are not fully supported by the SUM tool.<br/>Upgrades to target release SAP NetWeaver 7.1 (incl. EHP1) are not supported on SQL Server 2012.</p>\n<p>On SQL Server 2014 or higher releases, upgrade options for your SAP system are limited  to (only) SAP products that are supported by the latest Software Update Manager SP (SUM). See SAP Note <a href=\"/notes/1966681\" target=\"_blank\">1966681</a> (SQL Server 2014) /SAP Note <a href=\"/notes/2201059\" target=\"_blank\">2201059</a> (SQL Server 2016).</p>\n<p>For all upgrades/updates, you must apply SAP Note <a href=\"/notes/1811298\" target=\"_blank\">1811298</a>.</p>\n<p><strong>3. IBM DB2 for LUW</strong></p>\n<ul>\n<li><strong>Windows Server 2012 R2:</strong></li>\n</ul>\n<p>Released with the following versions:</p>\n<p>DB2 10.1: You must use FP4: Material Numbers 51048530, 51048531, 51048532, 51048533</p>\n<p><strong>SAP Standalone Components Information for Windows Server 2012 (R2)</strong></p>\n<p><strong>SAP liveCache</strong></p>\n<p>With SWPM 1.0 SP02 and the released liveCache installation medium for SCM 7.0 EHP2, you cannot install liveCache on Windows Server 2012. Instead, you have to patch the liveCache 7.9 installation medium with the liveCache package. Download the recent LCA build for SCM 7.0 from the Software Download Center as described in SAP note 753528. Copy the extracted binaries to the corresponding location on the liveCache installation medium. Then start the installation.<br/><br/>The following minimum SAP LC/LCAPPS 10.0 SP is required: If you want to run your SAP system on Windows Server 2012, you have to download SAP LC/LCAPPS 10,0 &gt;= SP11. If you want to run your SAP system on Windows Server 2012 R2, you have to download SAP LC/LCAPPS 10,0 &gt;= SP13.</p></div>", "noteVersion": 51}, {"note": "1980499", "noteTitle": "1980499 - Enhancement of C call CLOCK for analysis improvement", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>To enable the improved analysis of problems with the system time of an SAP system, the C call CLOCK is enhanced with this SAP Note. <br/>As of the patch level specified in \"Support Packages &amp; Patches\", you can use the C call CLOCK from an ABAP<br/>program to determine the UTC clicks in accordance with the function \"time()\", the corresponding data from \"localtime()\", and the time zone at operating system level in accordance with \"strftime()\".</p>\n<p> </p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>abcall.c, ab_cftime</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>Functions are missing.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Import the kernel that is specified in \"Support Packages &amp; Patches\". You can then use the report provided in the attachments to output the additional data.</p>", "noteVersion": 2}, {"note": "1960916", "noteTitle": "1960916 - StAuthTrace: Additional information \"authorized by reference user\"", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>The system trace for authorizations - transaction ST01 and, especially, StAuthTrace - currently does not display any additional information as to whether the authorization was received through the reference user or directly.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>To date, the system provides additional information for the following cases:</p>\n<p>A  Globally deactivated<br/>B  Deactivated in this transaction<br/>C  Deactivated for \"Call Transaction\"<br/>D  Deactivated by profile parameter</p>\n<p>When you implement this correction, a new additional information option is displayed:<br/>E  Authorized by reference user</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Import a kernel (disp+work) that has at least a patch level specified in the \"Support Packages &amp; Patches\" section.<br/>SAP Note 19466 contains more information about download and installation.<br/>If no patch level is specified for a kernel yet, then it is still being prepared and has not been delivered yet. In this case, we recommend that you review this SAP Note at regular intervals. The patch level will be added automatically as soon as it becomes available.</p>\n<p>When you import a Support Package listed below, transaction StAuthTrace will display the text \"Authorized by Reference User\" in the \"Additional Info\" column and explained in the F1 help. Before the correction, the \"Additional Info\" column merely displays the value \"E\".</p>\n<p>The F1 help is supplemented by the following text (approximate):</p>\n<ul>\n<li>E - The reference user already has the authorization.</li>\n</ul>\n<p>You have assigned a reference user to the user in transaction SU01. In this case, the system checks the authorizations of the reference user first. If it is not sufficient, the authorizations assigned to the user directly, through roles and profiles, are checked. \"E\" means the reference user already has the necessary authorization, so the directly assigned authorizations are not checked.</p>\n<p>You can use this additional information to test roles. Assign a reference user to the role you want to test and assign a test user with comprehensive authorizations. Test your application with this test user and the activated StAuthTrace. As soon as StAuthTrace displays \"E\" for all authorization checks carried out in the application, or one of the other values (\"A\" through \"D\"), your role is finished and can be assigned to the users, either directly or through a reference user.</p>\n<p> </p>", "noteVersion": 4}, {"note": "2018923", "noteTitle": "2018923 - More trace information for job termination", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>The SAP development support team requires more trace information for a job termination.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Termination of job</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>This is not an error.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Apply a kernel with a patch level at least equal to that stated in this SAP Note.</p>", "noteVersion": 1}]}, {"note": "1716742", "noteTitle": "1716742 - SWT2DB: Supporting a placeholder in the job name", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>Up to now, the SAP Business Application Accelerator powered by HANA (add-on SWT2DB) does not support placeholders at the end of a job name for the specification of a context in a scenario.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>SW2TDB</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>This is a new requirement that is implemented with the solution described below.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>Supporting placeholders at the end of a job name in a context of a scenario is implemented in the kernel with the patch level specified under SP Patch Level. Also, implement the attached correction instructions.<br/><br/>The implementation enables you to specify the placeholder \"*\" at the end of a job name in a context of a scenario. As a result, a job name prefix is specified in the context that corresponds with the job name in the context without \"*\". For example, if you specify \"CUSTOMER_JOB_*\", the job name prefix is \"CUSTOMER_JOB_\". For the runtime, the system first checks, as before, if the specified job name (in the example: \"CUSTOMER_JOB_*\") matches the existing background job name. If this is not the case, the system then checks if the context job name ends with a \"*\", and if so, if the existing background job name begins with the corresponding prefix. For the above example, this would be the case for the background job names \"CUSTOMER_JOB_20120509\", \"CUSTOMER_JOB_20120510\", \"CUSTOMER_JOB_X\", and so on.<br/><br/>If a single \"*\" is specified as the job name in the context of a scenario, this context is valid for all background job names that exist at runtime. However, this must be an execution in the background. For an execution in the dialog, the context of a scenario specified in this way would not be applicable.</p></div>", "noteVersion": 4}, {"note": "1771304", "noteTitle": "1771304 - SWT2DB: Permitting all database connections", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>When you execute the report RDA_MAINTAIN, the system does not permit all database connections from the table DBCON.<br/>In the value help for the field DBCON, not all of the database connections are listed.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>SW2TDB, RDA_MAINTAIN</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>We recommend that you implement the attached correction instructions.<br/><br/>The correction is delivered with Support Package SAPK-10001INSWT2DB.</p></div>", "noteVersion": 2}, {"note": "1696402", "noteTitle": "1696402 - Installation of SWT2DB 100/100_740 on SAP NetWeaver", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p><br/>You want to use transaction SAINT to perform an add-on installation.<br/>Add-on SWT2DB needs to be processed in phase IS_SELECT during an ABAP server update/upgrade process.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p><br/>SAINT, add-on, SWT2DB, 100, SAPK-100AGINSWT2DB, 100_740, SAPK-101AGINSWT2DB, SAPK-101GGINSWT2DB, CD51046002, \"Switch to second database\"<br/><br/></p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p><br/>You want to perform an add-on installation on SAP NetWeaver 7.0 (optionally with Enhancement Packages), SAP NetWeaver 7.4, SAP NetWeaver 7.5</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong>This note is updated on a regular basis. Make sure you have the current version of this note before you start the installation.</strong><br/><br/>Contents<br/>  1. Change history<br/>  2. Prerequisites for installing the SWT2DB<br/>  3. Release of SWT2DB with SAP Enhancement Packages<br/>  4. Preparing the installation<br/>  5. Performing the installation<br/>  6. Known errors<br/>  7. After the installation of SWT2DB<br/>  8. Language support<br/><br/></p>\n<p><strong>1. Change history</strong></p>\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr><th>Date</th><th>Chapter</th><th>Short Description</th></tr>\n<tr>\n<td>18.07.2017</td>\n<td>Various</td>\n<td>Additional comments on availability and ACP</td>\n</tr>\n<tr>\n<td>20.05.2016</td>\n<td>2</td>\n<td>Include SAP NetWeaver 7.5</td>\n</tr>\n<tr>\n<td>03.11.2014</td>\n<td>4</td>\n<td>Emphasize Note 1694697</td>\n</tr>\n<tr>\n<td>14.05.2013</td>\n<td>Various</td>\n<td>include SWT2DB 100_740</td>\n</tr>\n<tr>\n<td>21.03.2012</td>\n<td>Various</td>\n<td>Note Creation</td>\n</tr>\n<tr>\n<td> </td>\n</tr>\n</tbody>\n</table></div>\n<p><br/><strong>2. Prerequisites for installing SWT2DB</strong></p>\n<ul>\n<li>It is not possible to uninstall SWT2DB.<br/>Before you install the SWT2DB, keep in mind that you cannot uninstall ABAP add-ons.</li>\n</ul>\n<ul>\n<ul>\n<li>Obtain the following notes before you begin the installation.</li>\n</ul>\n</ul>\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td>Add-ons: Conditions:</td>\n<td> </td>\n<td>70228</td>\n</tr>\n<tr>\n<td>Problems with transaction SAINT:</td>\n<td> </td>\n<td>822380</td>\n</tr>\n</tbody>\n</table></div>\n<ul>\n<li>\n<p>The Add-on SWT2DB is no longer available for new customers. Support for existing customers is continued under the same conditions as before. Please review note 1694697.</p>\n</li>\n<li>Required release<br/>SWT2DB 100 requires SAP NetWeaver 7.0 (optionally with Enhancement Packages) and is not released for SAP NetWeaver 7.4<br/>SWT2DB 100_740 requires SAP NetWeaver 7.4.</li>\n</ul>\n<ul>\n<ul>\n<li>Required components and Support Packages for SWT2DB 100</li>\n</ul>\n</ul>\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr><th>Component</th><th>Release</th><th>Support Package</th></tr>\n<tr>\n<td>SAP_BASIS</td>\n<td>700</td>\n<td>SAPKB70015</td>\n</tr>\n<tr>\n<td>SAP_ABA</td>\n<td>700</td>\n<td>SAPKA70015</td>\n</tr>\n<tr>\n<td>SAP_BW</td>\n<td>700</td>\n<td>SAPKW70017</td>\n</tr>\n<tr>\n<td>or</td>\n</tr>\n<tr>\n<td>SAP_BASIS</td>\n<td>701</td>\n<td>SAPKB70100</td>\n</tr>\n<tr>\n<td>SAP_ABA</td>\n<td>701</td>\n<td>SAPKA70100</td>\n</tr>\n<tr>\n<td>SAP_BW</td>\n<td>701</td>\n<td>SAPKW70100</td>\n</tr>\n<tr>\n<td>or</td>\n</tr>\n<tr>\n<td>SAP_BASIS</td>\n<td>702</td>\n<td>SAPKB70200</td>\n</tr>\n<tr>\n<td>SAP_ABA</td>\n<td>702</td>\n<td>SAPKA70200</td>\n</tr>\n<tr>\n<td>SAP_BW</td>\n<td>702</td>\n<td>SAPKW70200</td>\n</tr>\n<tr>\n<td>or</td>\n</tr>\n<tr>\n<td>SAP_BASIS</td>\n<td>731</td>\n<td>SAPKB73100</td>\n</tr>\n<tr>\n<td>SAP_ABA</td>\n<td>731</td>\n<td>SAPKA73100</td>\n</tr>\n<tr>\n<td>SAP_BW</td>\n<td>731</td>\n<td>SAPKW73100</td>\n</tr>\n<tr>\n<td> </td>\n</tr>\n</tbody>\n</table></div>\n<ul>\n<ul>\n<li>Required components and Support Packages for SWT2DB 100_740</li>\n<ul>\n<li>The installation of SWT2DB 100_740 to SAP NetWeaver 7.5 - ABAP application server requires in addition to the Add-on software package the Attribute Change Package SWT2DB====100_740. Please download the ACP from SAP Support Portal (archive 73554900102000000648.zip) and upload it to the system (see note 1119856).</li>\n<li>The ACP SWT2DB====100_740 is also required in the Upgrade to SAP NetWeaver 7.5 with SWT2DB 100_740.</li>\n</ul>\n</ul>\n</ul>\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr><th>Component</th><th>Release</th><th>Support Package</th></tr>\n<tr>\n<td>SAP_BASIS</td>\n<td>740</td>\n<td>SAPKB74002</td>\n</tr>\n<tr>\n<td>SAP_ABA</td>\n<td>740</td>\n<td>SAPKA74002</td>\n</tr>\n<tr>\n<td>SAP_BW</td>\n<td>740</td>\n<td>SAPKW74002</td>\n</tr>\n<tr>\n<td>or</td>\n<td> </td>\n<td> </td>\n</tr>\n<tr>\n<td>SAP_BASIS</td>\n<td>750</td>\n<td>SP01</td>\n</tr>\n<tr>\n<td>SAP_ABA</td>\n<td>750</td>\n<td>SP01</td>\n</tr>\n<tr>\n<td>SAP_BW</td>\n<td>750</td>\n<td>SP01</td>\n</tr>\n</tbody>\n</table></div>\n<ul>\n<li>Required Kernel:<br/>7.21 (for more Details see as well SAP Note 1713986)</li>\n</ul>\n<ul>\n<li>Additional Component Support Packages<br/>The add-on SWT2DB 100/100_740 does not contain modifications. You can also perform the installation if you have already imported more Support Packages into your system than are specified in the section 'Required Components and Support Packages'.</li>\n</ul>\n<p> </p>\n<p> </p>\n<p><strong>3. Release of SWT2DB with SAP Enhancement Packages</strong></p>\n<ul>\n<li>SWT2DB 100 can be installed on SAP NetWeaver 7.0 with Enhancement Packages 1- 3.</li>\n</ul>\n<ul>\n<li>SWT2DB 100_740 can be installed to SAP NetWeaver 7.4.</li>\n</ul>\n<p><br/><strong>4. Preparing the installation</strong></p>\n<ul>\n<li>Making the add-on package available:<br/>\n<p>The Add-on SWT2DB is no longer available for new customers. Support for existing customers is continued under the same conditions as before.</p>\r\nThe add-on package for SWT2DB is provided on the CD/DVD with the material number 51046002. Please review note 1694697.</li>\n</ul>\n<ul>\n<li>Log on as user:<br/>&lt;sid&gt;adm          on UNIX<br/>&lt;SID&gt;OFR          on IBM i (previously i5/OS or OS/400)<br/>&lt;SID&gt;adm          on Microsoft Windows</li>\n</ul>\n<ul>\n<li>Switch to the &lt;DIR_EPS_ROOT&gt; directory of your SAP system (usually /usr/sap/trans/EPS). The &lt;DIR_EPS_ROOT&gt; directory is also displayed under DIR_EPS_ROOT after you execute the RSPFPAR report.</li>\n</ul>\n<ul>\n<li>Go to the higher-level directory of &lt;DIR_EPS_ROOT&gt;.</li>\n</ul>\n<ul>\n<li>Unpack the SAR archive &lt;SAR_Archive&gt; on the CD with the following statement:<br/>UNIX:<br/>SAPCAR -xvf /&lt;CD_DIR&gt;/DATA_UNITS/&lt;SWT2DB release&gt;/&lt;ARCHIV&gt;.SAR<br/><br/>IBM i (previously i5/OS or OS/400):<br/>SAPCAR '-xvf /QOPT/&lt;VOLID&gt;/DATA_UNITS/&lt;SWT2DB release&gt;/&lt;ARCHIV&gt;.SAR'<br/><br/>Microsoft Windows:<br/>SAPCAR -xvf &lt;CD_DRIVE&gt;:\\DATA_UNITS\\&lt;SWT2DB release&gt;\\&lt;ARCHIV&gt;.SAR</li>\n</ul>\n<ul>\n<li>The &lt;DIR_EPS_ROOT&gt;/in directory should now have the following PAT files:<br/>SWT2DB 100 installation: CSR0120031469_0063302.PAT (SAPK-100AGINSWT2DB)<br/>SWT2DB 100_740 installation: CSR0120031469_0076334.PAT (SAPK-101AGINSWT2DB)<br/>SWT2DB 100_740 upgrade: CSR0120031469_0076335.PAT (SAPK-101GGINSWT2DB)</li>\n</ul>\n<p><strong>5. Performing the installation</strong></p>\n<ul>\n<li>Installation for SWT2DB with SAINT.</li>\n</ul>\n<ul>\n<ul>\n<li>Call transaction SAINT and choose 'Start'.</li>\n</ul>\n</ul>\n<ul>\n<ul>\n<li>Select the add-on SWT2DB 100 and choose \"Continue\". If all of the necessary conditions for importing the add-on have been fulfilled, the system will now display the relevant queue. The queue consists of the add-on package and can also contain Support Packages and other add-on packages. To start the installation process, choose 'Continue'.</li>\n</ul>\n</ul>\n<ul>\n<ul>\n<li>For more information, call transaction SAINT and choose \"Info\" on the application toolbar.</li>\n</ul>\n</ul>\n<ul>\n<ul>\n<li>The system prompts you to enter a password. The password is:<br/>SWT2DB 100: <strong>8192AD75A1</strong><br/>SWT2DB 100_740: <strong>8192AC75A1</strong></li>\n</ul>\n</ul>\n<ul>\n<li>Change from SWT2DB 100 to SWT2DB 100_740</li>\n</ul>\n<ul>\n<ul>\n<li>Update to SAP NetWeaver 7.4 / SAP NetWeaver 7.5<br/>In the update to SAP NetWeaver 7.4 or an Enhancement Package of an SAP application based on SAP NetWeaver 7.4, a decision for SWT2DB 100 is requested in phase IS_SELECT of the update process.<br/>Please select in IS_SELECT for SWT2DB 100 the option \"Upgrade with SAINT package\" and make package SAPK-101AGINSWT2DB available. For target SAP NetWeaver 7.5 an ACP for SWT2DB 100_740 is needed, see section 2.</li>\n</ul>\n</ul>\n<ul>\n<ul>\n<li>Upgrade to SAP NetWeaver 7.4<br/>In the system switch upgrade to an SAP ABAP application server release based on SAP NetWeaver 7.4, a decision for SWT2DB 100 is requested in phase IS_SELECT of the update process.<br/>Please select in IS_SELECT for SWT2DB 100 the option \"Upgrade with SAINT package\" and make package SAPK-101GGINSWT2DB available.</li>\n</ul>\n</ul>\n<p><strong>6. Known errors</strong></p>\n<ul>\n<li>No Generation Errors are existing.</li>\n</ul>\n<p><strong>7. After the installation of SWT2DB</strong></p>\n<ul>\n<li>Additional information about SWT2DB.<br/><br/>You can import Support Packages of the other components that SWT2DB does not modify without CRTs.</li>\n</ul>\n<p><strong>7. Language support</strong></p>\n<ul>\n<li>SWT2DB supports the following languages:<br/>German<br/>English<br/>Chinese<br/>French<br/>Japanese<br/>Portuguese<br/>Russian<br/>Spanish.<br/><br/>The languages are contained in the add-on package, and you do not need to import any additional language packages.</li>\n</ul>\n<ul>\n<li>For information about subsequently installing further languages in your system, see Note 195442.</li>\n</ul></div>", "noteVersion": 9}, {"note": "1713986", "noteTitle": "1713986 - Installation of kernel 721 (EXT)", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You want to use the kernel 7.21 or 7.21 EXT  as a downward-compatible kernel in one of the following NetWeaver releases and products based on these NetWeaver releases:<br/>SAP NetWeaver 7.0 (\"7.00\")<br/>SAP EhP1 for SAP NetWeaver 7.0 (\"7.01\")<br/>SAP EhP2 for SAP NetWeaver 7.0 (\"7.02\")<br/>SAP EhP3 for SAP NetWeaver 7.0 (\"7.03\")<br/>SAP NetWeaver 7.1 (\"7.10\")<br/>SAP EhP1 for SAP NetWeaver 7.1 (\"7.11\")<br/>SAP NetWeaver 7.2 (\"7.20\")<br/>SAP NetWeaver 7.3 (\"7.30\")<br/>SAP EhP1 forNetWeaver 7.3 (\"7.31\")<br/><br/>or SAP products based on these, such as:<br/>SAP ECC 6.0<br/>SAP CRM 5.0, 2007, 7.0<br/>SAP SRM 5.0, 6.0, 7.0<br/>SAP NetWeaver PI 7.1<br/>SAP NetWeaver Mobile 7.1<br/>SAP NetWeaver Composition Environment (CE) 7.1<br/>SAP EhP1 for NW CE 7.1<br/>SAP NetWeaver CE 7.2</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>kernel, dck, akk, 721, 721 EXT, 721_EXT, downward compatible kernel, installation</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p><br/>There are two <strong>use cases</strong> for the downward compatible kernel 721 (EXT):</p>\n<ol>\n<li>Kernel 721 (EXT) may be installed on systems with NetWeaver Releases 7.00/7.01 (7.0 EhP1)/7.1/7.11 (7.1 EhP1) that still run with the originally delivered kernel 700, 701, 710 or 711.</li>\n<li>Kernel 721 (EXT) as downward-compatible kernel to the kernel 720 (EXT) can be installed on:</li>\n<ul>\n<li>Systems originally delivered with the kernel 720 (EXT)</li>\n<li>Systems listed in the point 1 above where the original kernel 700/701/710/711 was earlier upgraded to the 720 (EXT) version.</li>\n</ul>\n</ol>\n<p>Please make sure that all platform-, database-specific and other system requirements for the kernel 721 (EXT) are met. Please refer to the note 1716826, the section \"<strong>Reason and Prerequisites</strong>\".</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>The kernel 721 (EXT) is downward-compatible to the kernels for SAP NetWeaver releases 7.00 to 7.31 listed in the section \"Symptom\". Therefore, you can eliminate errors and make use of the new features by replacing the current kernel with the kernel 721 so that you do not have to perform an SAP upgrade.<br/><br/><strong>Important:</strong></p>\n<ul>\n<li>You must exchange the kernels on all servers of the affected system.</li>\n</ul>\n<ul>\n<li>You can upgrade to the kernel 721 without having to install a new GUI version on the front-end PCs.</li>\n</ul>\n<ul>\n<li>The general SAP recommendation to keep up to date with the upgrades/updates remains unaffected by this downward-compatible kernel because it does not correct application errors. After you have installed the kernel 721 (EXT), you must:</li>\n</ul>\n<ul>\n<ul>\n<li>continue to import the Support Packages available for your installed SAP release.</li>\n</ul>\n</ul>\n<ul>\n<ul>\n<li>In future, when correcting the kernel, use 721 (EXT) kernel patches only.</li>\n</ul>\n</ul>\n<p><br/><strong>Definition of terms</strong><br/><br/>Here, 'kernel' means all executable programs,libraries and other files located in /usr/sap/&lt;SAPSID&gt;/SYS/exe/run (UNIX, IBM i) or \\\\$(SAPGLOBALHOST)\\sapmnt\\&lt;SAPSID&gt;\\sys\\exe\\&lt;nuc|uc&gt;\\&lt;platform&gt; (Windows), not just the executable disp+work.<br/><br/><br/><strong>1. Obtaining the archives</strong><br/>The installation archives of the kernel 721 (EXT) can be regularly downloaded from the SAP Service Marketplace under \"<strong>Additional Components -&gt; SAP Kernel</strong>\".<br/>Further a differentiation is made between the character <strong>Unicode</strong> or <strong>non-Unicode</strong> kernels. At this level you find the list of platforms and, for each platform, subfolders for database-independent archives and (for each database supported in the platform) database-dependent archives.<br/>The name of each archive is made from a template of type &lt;name&gt;_&lt;plevel&gt;-&lt;uid&gt;.SAR. Here, &lt;name&gt; is the actual name of the archive, followed by the patch level &lt;plevel&gt; and a unique ID &lt;uid&gt; in which the bit value, the character width, the platform and the database type of the relevant archive are included again. In general, only the short form &lt;name&gt;.SAR is used in this note (for example, SAPEXE.SAR).<br/><br/>Besides the kernel itself, there are also additional components required to install and run the kernel. These components are <strong>not</strong> available in the 721 version, the 720 (EXT) version should be used instead.<br/>Please download and copy the following archives into the same directory &lt;newkernel&gt;:</p>\n<ul>\n<li><strong>SAPCAR</strong>: Due to the digital signature of the archives (see Note 1598550), you require the latest SAPCAR, currently it is the release <strong>720</strong>. Therefore, download the archive SAPCAR_&lt;plevel&gt;-&lt;uid&gt;.EXE from SAP Service Marketplace under the name SAPCAR (UNIX, IBM i) or sapcar.exe (Windows) into your local &lt;newkernel&gt; directory. The SAPCAR 720 can be found here: http://service.sap.com/patches -&gt; \"Browse Our Download Catalog\" -&gt; Additional Components -&gt; SAPCAR -&gt; SAPCAR 7.20 -&gt; &lt;your platform&gt;.</li>\n<li><strong>721 stack kernel</strong>: The stack kernel consists of the database-independent archiv <strong>SAPEXE.SAR </strong>and the database-dependent archiv <strong>SAPEXEDB.SAR</strong>. If you use different platforms (for example, Windows Application Server with database DB2 on IBM i), you can immediately download the kernel for all of the required platforms.</li>\n</ul>\n<ol><ol><ol></ol></ol></ol>\n<ul>\n<li>If you use the Internet Graphics Server (IGS): the kernel 721 (EXT) runs with the 720 (EXT) version of the IGS. So you will have to download the archives <strong>igsexe.sar </strong>and, optionally, <strong>igshelper.sar</strong>.</li>\n<ul>\n<li>The path to the igsexe.sar on SMP is http://service.sap.com/patches -&gt; \"Browse Our Download Catalog\" -&gt; \"SAP Frontend Components\" -&gt; \"SAP IGS\"</li>\n</ul>\n<ul>\n<li>The archive igshelper.sar contains an optional component (see SAP note 1028690) that was introduced as of SAP IGS 7.20. We recommend to apply the igshelper.sar to your installation as described in SAP note 1028690 when you are upgrading to the kernel 721 or 721 EXT. The igshelper.sar is operating-system independent and can be used with all 7.* SAP IGS releases. You can download igshelper.sar from http://service.sap.com/patches -&gt; \"Browse Our Download Catalog\" -&gt; \"SAP Frontend Components\" -&gt; \"SAP IGS HELPER APPLICATIONS\" -&gt; \"SAP IGS HELPER\" -&gt; \"SAP IGS HELPER\".</li>\n</ul>\n<li>For the Oracle database:</li>\n<ul>\n<li>you also need the archive <strong>DBATOOLS.SAR </strong>in the 720 version: http://service.sap.com/patches -&gt; \"Browse Our Download Catalog\" -&gt; Additional Components -&gt; SAP Kernel -&gt; SAP KERNEL 64-BIT (UNICODE) -&gt; SAP KERNEL 7.20 (EXT) 64-BIT (UNICODE) -&gt; &lt;your platform&gt; -&gt; ORACLE. For more detailed information see the note 12741.</li>\n<li>If you are using Oracle 10.2 which is still supported for the 721 standard (non-EXT) kernel and for 721_EXT kernel on IBM Power Linux, Windows IA64 and on Linux IA64 (Intel Itanium), you can further use your existing 10.2 database client. Then make sure that its latest version 10.2.0.5 is installed, see Note 1110995 for more details. However, SAP recommends to use the Oracle 11g Instant Client.</li>\n<li>If you are using Oracle 11g (which is mandatory for the 721_EXT kernel except for platforms listed above), download the Oracle 11g Instant Client on all application servers and the database server as described in SAP Notes 819829 (UNIX) or 998004 (Windows). The Oracle client software can be downloaded from the SAP Service Marketplace at: https://service.sap.com/oracle-download<br/>Please note: After the installation of the Oracle 11g Instant Client, make sure that the library path for the &lt;sid&gt;adm user (LIBPATH, SHLIB_PATH, LD_LIBRARY_PATH) no longer contains any references to the old Oracle 10g Instant Client locatio</li>\n</ul>\n<li>For IBM i only:</li>\n<ul>\n<li>Download the package <strong>ILE</strong> (ILE_&lt;plevel&gt;-&lt;uid&gt;.SAR) with the highest available patch level and rename it to ILE.SAR.</li>\n<li>If you have already changed your SAP system to SAPJVM, you must also download one of the archives <strong>SAPJVM4,</strong> <strong>SAPJVM5</strong> or <strong>SAPJVM6</strong> depending on which one you currently use.</li>\n<li>If you know that there are already patches on SAP Service Marketplace for the SAPEXE.SAR that is used (for example, DW), download these into the directory. So they are automatically applied with the kernel.</li>\n<li>For a 7.00/7.01 ASCII based dual stack system you used to download only ASCII Packages. The ASCII package contained the UNICODE part for the SCS Instance, too. The kernel 721 (EXT) does not contain UNICODE parts in the ASCII Packages. Therefore you have to download the UNICODE SAPEXE package in addition</li>\n</ul>\n<li><strong>SAPHOSTAGENT</strong></li>\n</ul>\n<p>SAP HostAgent is available on the SAP Service Marketplace (http://service.sap.com/swdc) under the following menu path: \"Support Packages and Patches\" -&gt; \"Browse our Download Catalog\" -&gt; \"SAP Technology Components\" -&gt; \"SAP HOST AGENT\". Please check the requirements below.</p>\n<ul>\n<ul>\n<li>There is no new SAPHOSTAGENT package for the kernel 721 (EXT), the SAP Host Agent release 720 is used. So, for systems with the 720 (EXT) kernel (7.02, 7.03, 7.20, 7.30, 7.31) this component is already installed.</li>\n<li>There is no EXT version of the SAP Hostagent, so SAP Host Agent 720 should be also used with 72* EXT kernels.</li>\n<li>If using SYBASE ASE, the SAPHOSTAGENT 720 should have at least PL 115</li>\n<li>For systems with kernel releases &lt;720 please check the below requirements:</li>\n<ul>\n<li>If upgrading from kernel releases 700/701 you must install the package <strong>SAPHOSTAGENT.SAR</strong> in the 720 version in accordance with Note 1031096.</li>\n<li>On IBM i platform the SAP HostAgent 720 version should be also installed over kernel releases 710 and 711.</li>\n</ul>\n</ul>\n</ul>\n<p>SAP HostAgent is available on the SAP Service Marketplace (http://service.sap.com/swdc) under following menu path: \"Support Packages and Patches\" -&gt; \"Browse our Download Catalog\" -&gt; \"SAP Technology Components\" -&gt; \"SAP HOST AGENT\".</p>\n<p>After the installation you must delete the call of the program SAPOSCOL from all the start profiles (not Windows).</p>\n<ul>\n<li>\n<div>For UNIX/Windows only:</div>\n</li>\n</ul>\n<p>Download any additional programs (such as the RFCSDK) if you still want to install such programs after the kernel installation.</p>\n<p><br/><strong>2. Importing the kernel 721 (EXT) on top of the kernel 720 (EXT)</strong><br/><br/>If installing the kernel 721 (EXT) on top of the kernel 720 (EXT), either originally delivered or previously installed as DCK (i.e. in systems listed in the section \"Reason and Prerequisites\", 2A and B), the procedure is similar to an installation of a regular kernel patch:</p>\n<ul>\n<li>On UNIX and Windows:</li>\n</ul>\n<ul>\n<ul>\n<li>We strongly recommend to back up your old kernel directory before the kernel exchange.</li>\n</ul>\n</ul>\n<ul>\n<ul>\n<li>Stop the SAP system and copy downloaded kernel archives into your kernel directory as per note 19466.</li>\n</ul>\n</ul>\n<ul>\n<li>On IBM i: Proceed as described in section \"Implementing a backwards compatible kernel (AKK) with APYSIDKRN\" of SAP-note 1097751.</li>\n</ul>\n<p><br/><strong>3. Preparations for the installation of kernel 721 (EXT) on top of 700/701/710/711 kernel versions</strong><br/><br/>After you have downloaded the required kernel components, you must stop all of the relevant processes on all instances for the system to be processed and release or delete the kernel-specific resources. To do this, carry out the following actions as the user &lt;sapsid&gt;adm on all instances:</p>\n<ul>\n<li>Stop the SAP system. (You do not need to stop the database.)</li>\n</ul>\n<p>On UNIX or Windows:<br/>Stop the SAP system as usual.</p>\n<p>On IBM i:<br/>Stop the SAP system together with sapstartsrv:<br/>STOPSAP INSTANCE(*ALL) STARTUPSRV(*ALL) XDNLISTEN(*YES) WAIT(*YES) ENDSBS(*YES)</p>\n<ul>\n<li>Stop saposcol.</li>\n</ul>\n<p>On UNIX (NW release 7.0 and 7.01):<br/>cd /usr/sap/&lt;SAPSID&gt;/SYS/exe/run<br/>./saposcol -k</p>\n<p>On UNIX (NW release 7.10 and higher):<br/>cd /usr/sap/hostctrl/exe<br/>./saposcol -k</p>\n<p>On Windows:<br/>If it exists, stop the Windows service SAPOsCol and delete it using the following commands:<br/>net stop saposcol<br/>sc delete saposcol</p>\n<p>On IBM i:<br/>CALL PGM(SAPOSCOL) PARM('-k')</p>\n<ul>\n<li>Stop sapstartsrv (this is required on UNIX and Windows only):</li>\n</ul>\n<p>On UNIX:<br/>kill -2 &lt;pid of sapstartsrv&gt;</p>\n<p>On Windows:<br/>Stop and deactivate (Startup Type = Disabled) the services SAP&lt;SID&gt;_&lt;INSTANCE no.&gt;.</p>\n<ul>\n<li>If present, unregister all standalone CCMS agents (sapccmsr [-j2ee], sapccm4x). On Unix you can see corresponding processes, on Windows you can see them running as services &lt;agent name&gt;.&lt;instance number&gt;:</li>\n</ul>\n<p>sapccm4x -u pf=&lt;profile the agent started with&gt;<br/>sapccmsr -u pf=&lt;profile the agent started with&gt; [-j2ee]</p>\n<ul>\n<li>Remove any IPC objects that still exist (this is required on UNIX only):</li>\n</ul>\n<p>cleanipc &lt;instance no&gt; remove</p>\n<p><strong>4. Installing SAPHOSTAGENT</strong></p>\n<p>In case SAPHOSTAGENT package should be installed/updated, proceed as outlined in Note <a href=\"/notes/1031096\" target=\"_blank\">1031096</a>.</p>\n<p>After the installation you must delete the call of the program SAPOSCOL from all start profiles on all non-Windows platforms.</p>\n<p><br/><strong>5. Installation of the kernel 721 (EXT) on top of 700/701/710/711 kernel versions</strong><br/><br/>Import the kernel 721 (EXT) from the directory &lt;newkernel&gt; onto the host of the central instance and then on all of the application servers with local executables.<br/><br/><strong>5.1 On UNIX</strong></p>\n<p>1. Log on as user &lt;sapsid&gt;adm and switch to the directory /usr/sap/&lt;SAPSID&gt;/SYS/exe/run.</p>\n<p>2. We recommend saving the old kernel before deploying the new kernel.Save the old kernel by creating a tar archive of the complete kernel directory using the following command:</p>\n<p>SAPCAR -cvf ../sapexe.tar ./*</p>\n<p>3. Save the following files and directories:</p>\n<ul>\n<li>For Java or Dual-Stack systems: the directory jvm or sapjvm* (e.g. sapjvm, sapjvm_4, sapjvm_5, sapjvm_6).</li>\n<li>the file protect.lst if it exists</li>\n<li>the icu libraries (libicu*30.&lt;a sh, so&gt; or libicu*34.&lt;a sh, so&gt; if they exist</li>\n</ul>\n<p>4. Switch to the user root and change the owner of all files to &lt;SAPSID&gt;adm using the following commands:</p>\n<p>su - root<br/>chown &lt;sapsid&gt; adm /usr/sap/&lt;SAPSID&gt;/SYS/exe/run/*<br/>exit</p>\n<p>Important: On the operating system OS/390, you must use the command \"su &lt;user&gt;\" instead of \"su - root\", where &lt;user&gt; must have the UID 0.</p>\n<p>5. Delete all of the files from the kernel directory, including the subdirectories. This ensures that there are no remaining files from the earlier release, which have a different name in Release 7.21 or are in a different place in a subdirectory.</p>\n<p>rm -rf *</p>\n<p>6. Unpack the new kernel with the following commands:</p>\n<p>&lt;newkernel&gt;/SAPCAR -xvf &lt;newkernel&gt;/SAPEXE.SAR</p>\n<p>&lt;newkernel&gt;/SAPCAR -xvf &lt;newkernel&gt;/SAPEXEDB.SAR</p>\n<p>7.Oracle only:</p>\n<p>Unpack the DBATools with the following command:</p>\n<p>&lt;newkernel&gt;/SAPCAR -xvf &lt; newkernel&gt;/DBATOOLS.SAR</p>\n<p>Also unpack the Oracle instant client to the directory as explained in the note 819829.</p>\n<p>8. If you use IGS, you must unpack the IGS archive using the following command:</p>\n<p>&lt;newkernel&gt;/SAPCAR -xvf &lt; newkernel&gt;/igsexe.sar</p>\n<p>9. If there are files or directories that were saved in step 3, restore them into the current directory</p>\n<p>10. To deploy the optional IGSHELPER archive switch to the relevant local directory /usr/sap/&lt;SAPSID&gt;/&lt;INSTANCE&gt; on every instance and execute the command:</p>\n<p>&lt;newkernel&gt;/SAPCAR -xvf &lt; newkernel&gt;/IGSHELPER.SAR</p>\n<p>11. Check file permissions</p>\n<p><span>Oracle only:</span> check permissions for BR* tools according to Notes <a href=\"/notes/113747\" target=\"_blank\">113747</a> and <a href=\"/notes/1598594\" target=\"_blank\">1598594</a>.<br/><span>ICM</span>: if using ports &lt; 1024, check Note <a href=\"/notes/421359\" target=\"_blank\">421359</a>.</p>\n<p>12. Delete all of the local executables on the individual instances. To do this, switch to the relevant local executable directory /usr/sap/&lt;SAPSID&gt;/&lt;INSTANCE&gt;/exe and execute</p>\n<p>rm -rf *</p>\n<p>13. Since executables from the local executable directories may already be executed for the start before sapcpe runs, start an initial copy of the executables.</p>\n<p>For all application server instances (primary application server/central instance, additional application servers/dialog instances):</p>\n<ul>\n<li>For ABAP-only systems:</li>\n</ul>\n<p>cd /usr/sap/&lt;SAPSID&gt;/&lt;INSTANCE&gt;/work<br/>sapcpe pf=/usr/sap/&lt;SAPSID&gt;/SYS/profile/&lt;instance profile&gt;</p>\n<ul>\n<li>For Dual-Stack systems:</li>\n</ul>\n<p>cd /usr/sap/&lt;SAPSID&gt;/&lt;INSTANCE&gt;/work<br/>sapcpe pf=/usr/sap/&lt;SAPSID&gt;/SYS/profile/&lt;instance profile&gt;<br/>If your system is running with the SAPJVM:<br/>sapcpe pf=/usr/sap/&lt;SAPSID&gt;/SYS/profile/&lt;instance profile&gt; source:&lt;sapjvm directory&gt; list:&lt;sapjvm directory&gt;/sapjvm_&lt;version&gt;.lst</p>\n<ul>\n<li>For Java-only systems:</li>\n</ul>\n<p>cd /usr/sap/&lt;SAPSID&gt;/&lt;INSTANCE&gt;/work<br/>sapcpe pf=/usr/sap/&lt;SAPSID&gt;/SYS/profile/&lt;instance profile&gt; list:/usr/sap/&lt;SAPSID&gt;/SYS/exe/run/j2eeinst.lst<br/>If your system is running with the SAPJVM:<br/>sapcpe pf=/usr/sap/&lt;SAPSID&gt;/SYS/profile/&lt;instance profile&gt; source:&lt;sapjvm directory&gt; list:&lt;sapjvm directory&gt;/sapjvm_&lt;version&gt;.lst</p>\n<p>For additional instances such as ASCS, SCS and ERS (exist only in a cluster):</p>\n<p>cd /usr/sap/&lt;SAPSID&gt;/&lt;INSTANCE&gt;/work<br/>sapcpe pf=/usr/sap/&lt;SAPSID&gt;/SYS/profile/&lt;instance profile&gt; list:/usr/sap/&lt;SAPSID&gt;/SYS/exe/run/scs.lst<br/>NB: the arguments (\"pf\", \"list\", \"source\") are separated by a white space, not by a newline character. &lt;sapjvm directory&gt; means the location of the saved SAPJVM, see the steps 3 and 9 above.</p>\n<p><br/><strong>5.2 On Windows</strong></p>\n<p>1. Log on as user &lt;sapsid&gt;adm and switch to the global host in your kernel directory, for example: &lt;drive&gt;:\\usr\\sap\\&lt;SAPSID&gt;\\sys\\exe\\&lt;uc|nuc</p>\n<p>2. Rename the &lt;platform&gt; directory, for example &lt;platform&gt;.save</p>\n<p>3. Create a new directory &lt;platform&gt; and switch to this directory.Unpack the new kernel from the directory &lt;newkernel&gt; of the downloaded archive in the specified sequence.<br/><br/>Important: Do not call SAPCAR.EXE directly without specifying a path; instead, use the specified directory structure</p>\n<p>&lt;newkernel&gt;\\SAPCAR.EXE -xvf &lt;newkernel&gt;\\SAPEXE.SAR</p>\n<ol><ol></ol></ol>\n<p>&lt;newkernel&gt;\\SAPCAR.EXE -xvf &lt;newkernel&gt;\\SAPEXEDB.SAR</p>\n<p>4. Restore the following directories and files into the newly created directory if they exist in the &lt;platform&gt;.save directory</p>\n<ul>\n<li>\n<div>For Java or Dual-Stack systems: the directory jvm or sapjvm* (e.g. sapjvm, sapjvm_4, sapjvm_5, sapjvm_6).</div>\n</li>\n<li>\n<div>the file protect.lst</div>\n</li>\n<li>\n<div>icu*30.dll or icu*34.dll if they exist</div>\n</li>\n</ul>\n<p>5. Oracle only:Unpack the DBATools to the &lt;platform&gt; directory</p>\n<p>&lt;newkernel&gt;\\SAPCAR.EXE -xvf &lt;newkernel&gt;\\DBATOOLS.SAR</p>\n<p>Also unpack the Oracle instant client to the &lt;platform&gt; directory as explained in the note 998004.</p>\n<p>6. If you use IGS, unpack the new IGS using the following command:&lt;newkernel&gt;\\SAPCAR.EXE -xvf &lt;newkernel&gt;\\igsexe.sar7. To deploy the optional IGSHELPER archive, still logged in as &lt;sapsid&gt;adm, switch to the relevant local directory on every instance. For example:</p>\n<ul>\n<li>primary application server instance:<br/>&lt;drive&gt;:\\usr\\&lt;SAPSID&gt;\\DVEBMGS&lt;No&gt;</li>\n</ul>\n<ul>\n<li>additional application server instance:<br/>&lt;drive&gt;:\\usr\\sap\\&lt;SAPSID&gt;\\D&lt;No&gt;</li>\n<li>execute the command &lt; newkernel&gt;\\SAPCAR.EXE -xvf &lt;newkernel&gt;\\IGSHELPER.SAR</li>\n</ul>\n<p>8. On all instances in the directories &lt;drive&gt;:\\usr\\sap\\&lt;SAPSID&gt;\\&lt;INSTANCE&gt;\\exe, delete all of the files (including the subdirectories).</p>\n<p>9. Install the current C runtime library as per note 1553465 by executing vcredist_&lt;platform&gt;.msi in the command box (or by double-clicking this file in the Windows Explorer). Before you start the system for the first time, and if you have a distributed system environment, perform this step manually on each node where a component of the system is configured to run.</p>\n<p>10. Since executables from the local executable directories may already be executed for the start before sapcpe runs, start an initial copy of the executables.</p>\n<p>For all application server instances (primary application server/central instance, additional application servers/dialog instances):</p>\n<ul>\n<li>For ABAP-only systems:</li>\n</ul>\n<p>&lt; drive&gt;:<br/>cd \\usr\\sap\\&lt;SAPSID&gt;\\&lt;INSTANCE&gt;\\work<br/>sapcpe pf=&lt;drive&gt;:\\usr\\sap\\&lt;SAPSID&gt;\\sys\\profile\\&lt;instance profile&gt;</p>\n<ul>\n<li>For Dual-Stack systems:</li>\n</ul>\n<p> &lt; drive&gt;:<br/>cd \\usr\\sap\\&lt;SAPSID&gt;\\&lt;INSTANCE&gt;\\work<br/>sapcpe pf=&lt;drive&gt;:\\usr\\sap\\&lt;SAPSID&gt;\\sys\\profile\\&lt;instance profile&gt;</p>\n<p>If your system is running with the SAPJVM:<br/>sapcpe pf=&lt;drive&gt;:\\usr\\sap\\&lt;SAPSID&gt;\\sys\\profile\\&lt;instance profile&gt; source:&lt;sapjvm directory&gt; list:&lt;sapjvm directory&gt;\\sapjvm_&lt;version&gt;.lst</p>\n<ul>\n<li>For Java-only systems:</li>\n</ul>\n<p>&lt; drive&gt;:<br/>cd \\usr\\sap\\&lt;SAPSID&gt;\\&lt;INSTANCE&gt;\\work<br/>sapcpe pf=&lt;drive&gt;:\\usr\\sap\\&lt;SAPSID&gt;\\sys\\profile\\&lt;instance profile&gt; list:&lt;drive&gt;:\\usr\\sap\\&lt;SAPSID&gt;\\sys\\exe\\&lt;uc|nuc&gt;\\&lt;platform&gt;\\j2eeinst.lst</p>\n<p>If your system is running with the SAPJVM:<br/>sapcpe pf=&lt;drive&gt;:\\usr\\sap\\&lt;SAPSID&gt;\\sys\\profile\\&lt;instance profile&gt; source:&lt;sapjvm directory&gt; list:&lt;sapjvm directory&gt;\\sapjvm_&lt;version&gt;.</p>\n<p>For additional instances such as ASCS, SCS and ERS (exist only in a cluster):</p>\n<p>&lt; drive&gt;:<br/>cd \\usr\\sap\\&lt;SAPSID&gt;\\&lt;INSTANCE&gt;\\work</p>\n<p>sapcpe pf=&lt;drive&gt;:\\usr\\sap\\&lt;SAPSID&gt;\\sys\\profile\\&lt;instance profile&gt; list:&lt;drive&gt;:\\usr\\sap\\&lt;SAPSID&gt;\\sys\\exe\\&lt;uc|nuc&gt;\\&lt;platform&gt;\\scs.lst</p>\n<p>NB: the arguments (\"pf\", \"list\", \"source\") are separated by a white space, not by a line break. &lt;sapjvm directory&gt; means the location of the saved SAPJVM, see the step 4 above.</p>\n<p>11. Activate (Startup Type = Automatic) and start the Windows Services SAP&lt;SID&gt;_&lt;INSTANCE no.&gt; of primary application server instance and of every additional application server instance to active the new version of sapstartsrv.exe.<br/><br/><strong>MSCS only:</strong></p>\n<p>1. Start and Stop the clustered (A)SCS instances and the appropriate SAP services using the cluster admin tool or the Powershell.</p>\n<p>2. You have to follow step 6-10 also for the ERS instances on your cluster nodes.</p>\n<ul>\n<li>Search for REPSRV.lst in all Start Profile of the ERS instances (line Start_Program_00 =). If it exists, replace it with SCS.lst.</li>\n<li>Be sure to check if your Enqueue Replication Service is replicating again after starting the ERS instances.</li>\n</ul>\n<p>3. Also see SAP Note 1596496 on how to update the SAP Resource Type DLL.</p>\n<p>4. If you have standalone gateway instances installed on cluster nodes, these also need to be updated with the corresponding files from the kernel staging directory:</p>\n<ul>\n<li>On Windows Server 2003 (see Note 657999) the standalone gateway is installed in the &lt;Windows&gt;\\SAPCLUSTER directory.</li>\n<li>On Windows Server 2008 and higher (see Note 1764650 - How to install a standalone gateway in a Microsoft Failover Cluster for Oracle) it is &lt;oracle shared disk&gt;:\\sap\\&lt;DB-SID&gt;\\dbtools</li>\n</ul>\n<p> </p>\n<p><strong>5.3 On IBM i:</strong></p>\n<p>1. Log on as a QSECOFR-type user and execute the following commands or actions:</p>\n<p>2. Point the current directory explicitly to the downloaded archives:</p>\n<p>CHGCURDIR '&lt;newkernel&gt;'</p>\n<p>3. Cleanup the library SAP_TOOLS if it exists, otherwise you need not careCLRLIB SAP_TOOLS</p>\n<p>4. Extract the required tools from *SAVF file ILE_TOOLS in ILE.SAR by program iletools into SAP_TOOLS (*LIB) - use 'Copy &amp; Paste' to transfer the following command to your session and execute it:</p>\n<p>CALL QP2SHELL PARM('/QOpenSys/usr/bin/csh' '-c' 'SAPCAR -xvf ILE.SAR iletools ILE_TOOLS;iletools')5. Since this call to QP2SHELL does not produce any output, check whether library SAP_TOOLS exists now and has some objects in it. If not, check with the WRKSPLF command for spool files with error messages.</p>\n<p>6. Set the authorities of the objects in SAP_TOOLS by these commands:</p>\n<p>If your starting kernel is already running with the 'newuserconcept' set:</p>\n<p>ADDENVVAR ENVVAR(CLASSICUSERCONCEPT) VALUE('N')</p>\n<p>ADDLIBLE  SAP_TOOLS</p>\n<p>FIXSAPOWN *NONE SAP_TOOLS</p>\n<p>If your starting kernel is still running with the 'classicuserconcept' set:</p>\n<p>ADDENVVAR ENVVAR(CLASSICUSERCONCEPT) VALUE('Y')</p>\n<p>ADDLIBLE  SAP_TOOLS</p>\n<p>FIXSAPOWN *NONE SAP_TOOLS</p>\n<ol></ol>\n<p><strong>NOTE: </strong>If you are not sure which userconcept your current system is using please check for the file /sapmnt/&lt;SID&gt;/profile/.newuserconcept The existence of this file says that the newuserconcept is active. You can also check who is the owner/primary-group of the executable files underneath /sapmnt/&lt;SID&gt;/exe. &lt;SID&gt;ADM/R3GROUP is an indicator for the newuserconcept, while R3OWNER/&lt;SID&gt;GROUP is an indicator for the classicuserconcept.</p>\n<p>7. For 7.00/7.01 NetWeaver releases, check the contents of the file /usr/sap/sapservices. If sapstartsrv is started under the name sapstartsrvu (/usr/sap/&lt;sapsid&gt;/SYS/exe/run/sapstartsrvu ...), you must change the entry so that sapstartsrv is started from the subdirectory .../uc of the previous directory in future (/usr/sap/&lt;sapsid&gt;/SYS/exe/run/uc/sapstartsrv ...)</p>\n<p>8. Log on as &lt;SAPSID&gt;ADM and execute the following commands for importing the new kernel:</p>\n<p>ADDLIBLE SAP_TOOLS</p>\n<p>Check whether the environment variable CLASSICUSERCONCEPT is set by the login process (use WRKENVVAR); if it is not set, set it in the following way:</p>\n<p>If your starting kernel is already running with the 'newuserconcept' set:</p>\n<p>ADDENVVAR ENVVAR(CLASSICUSERCONCEPT) VALUE('N')</p>\n<p>If your starting kernel is still running with the 'classicuserconcept' set:</p>\n<p>ADDENVVAR ENVVAR(CLASSICUSERCONCEPT) VALUE('Y')</p>\n<p>9. Check that all instance- and start-profiles of the system contain the parameter DIR_CT_RUN. If missing in a 7.00/7.01 environment set it to: DIR_CT_RUN = $(DIR_EXE_ROOT)/run (for Windows instances: DIR_CT_RUN = $(DIR_EXE_ROOT)\\run ); For 7.10/7.11 set it to: DIR_CT_RUN = $(DIR_EXE_ROOT)/$(OS_UNICODE)/as400_pase_64 (for Windows: DIR_CT_RUN = $(DIR_EXE_ROOT)\\$(OS_UNICODE)\\ntamd64 )</p>\n<p>10. For 7.00/7.01/7.10/7.11 releases only:</p>\n<p>Please remove any DLTOLDPKG call from all start profiles, because with the kernel 721 DLTOLDPKG will be started by STARTSAP automatically. For further information please refer to note 1657890.</p>\n<p>11. Apply all archives in &lt;newkernel&gt; simultaneously to the system:</p>\n<p>APYSIDKRN SID(&lt;SAPSID&gt;) ARCHIVES('&lt;newkernel&gt;/*') SAVSAR(*NONE) MODE(*FULLY) CHGENV(*NO) UPDAPAR(*NO)</p>\n<p>12. Remove the SQL packages left over from using the old kernel:</p>\n<p>DLTR3PKG SID(&lt;SAPSID&gt;)</p>\n<p>13. Log off and then log on again with &lt;SAPSID&gt;ADM. You are now in the new 721 environment with the kernel library SAP&lt;sapsid&gt;IND.<br/>(Caution: The name of the kernel library is predefined after you import the kernel 721 (EXT) and can no longer be freely selected.)<br/><br/>14. If you change a system with a 710 or 711 kernel to the kernel 721 (EXT) and have not yet changed the user concept, use the opportunity and change it now in accordance with Note 1149318 because future upgrade or update paths require the new user concept. The faster storage management with SHM_SEGS can only be used after the changeover (see Note 808607).<br/><br/></p>\n<p><strong>6. Additional steps before starting the system with the new kernel</strong><br/><br/><strong>6.1 CAUTION: Retroactive kernel patches</strong><br/><br/>In some executables (in particular, disp+work), errors were corrected at a later date. You must apply these kernel patches in any case. These are available in SAP Support Portal (http://service.sap.com/swdc).<br/><br/>Read Note 19466 (Downloading SAP kernel patches) or Note 1097751 for IBM i.<br/><br/><strong>6.2 Additional manual changes in Dual-Stack and Java-only systems (releases 7.00/7.01)</strong></p>\n<ul>\n<li>Only necessary for a dual-stack system: you must set the following parameter in the default system profile DEFAULT.PFL:<br/>system/type = DS</li>\n</ul>\n<ul>\n<li>For dual-Stack and Java-only systems: in all (Dnn, DVEBMGSnn, Jnn, JCnn etc.) instance profiles, you must set the following parameter:<br/>FN_JSTART = jcontrol$(FT_EXE)</li>\n</ul>\n<p><br/><strong>6.3 Reinstalling SAPCRYPTOLIB</strong><br/><br/></p>\n<p>Starting with the patch level 136, also in SP stack kernels 721 PL &gt;=200, a new CommonCryptoLib is delivered with the SAP Kernel. This library is fully compatible to the SAPCRYPTOLIB and replaces it, so the reinstallation of SAPCRYPTOLIB is no longer necessary. See the Note 1848999 for more detail.<br/><br/><strong>6.4 Reinstalling additional programs</strong><br/><br/>If you had installed additional programs such as the RFC Library, you have to install them again when upgrading from 700/701/710/711 kernel versions as these programs were deleted during the kernel exchange. If upgrading from the 720 kernel version, you also may want to reinstall these programs with a newest patch level.<br/><br/>Proceed as follows:<br/><br/></p>\n<ul>\n<li>On UNIX:<br/>Execute the following commands as user &lt;sapsid&gt;adm:</li>\n</ul>\n<ol>cd /usr/sap/&lt;SAPSID&gt;/SYS/exe/run</ol><ol>&lt;newkernel&gt;/SAPCAR -xvf &lt;newkernel&gt;/&lt;additional package&gt;.SAR</ol>\n<ul>\n<li>On Windows:<br/>Execute the following commands as user &lt;SAPSID&gt;ADM:</li>\n</ul>\n<ol>CD \\USR\\SAP\\&lt;SAPSID&gt;\\SYS\\EXE\\RUN</ol><ol>&lt;newkernel&gt;\\SAPCAR.EXE -xvf &lt;newkernel&gt;\\&lt;additional package&gt;.SAR</ol>\n<ul>\n<li>On IBM i:<br/>On IBM i, you do not have to install any further programs.</li>\n</ul>\n<p><br/><strong>6.4.1 NetWeaver 7.0/7.1/7.10/7.11:</strong><br/>Copying executable file from NetWeaver RFC SDK 720.<br/><br/>The program rfcexec (UNIX and IBM i) or rfcexec.exe (Windows) is no longer shipped together with SAP kernels 720 and 721 which might break some application scenarios. As older versions of this program are no longer compatible with SAP kernels &gt;= 720, it is mandatory to move to a new version of these files, contained in the NW RFC SDK 720. Please refer to SAP Notes 1581595 and 1025361 for detailed instructions and further information.<br/><br/><strong>6.5 Special features for the syslog (if upgrading from 700/701/710/711 kernel versions)</strong><br/>Due to the situation described in note 1517379, you have to set the profile parameter<br/>rslg/new_layout = 9.<br/><br/>If a syslog file already exists in the new format because this parameter has not been set from the very beginning, the syslog will still be written in the new format even if the parameter has been set in the meantime. In that case, the existing syslog files have to be deleted.<br/><br/><strong>6.6 Dynamic work processes (NetWeaver 7.00/7.01)</strong><br/>The 720 and 721 kernel versions support the dynamic increase of the number of work processes at runtime. However, this function is not fully compatible with NW 7.00 and NW 7.01. To prevent errors from occurring, deactivate it by setting the following parameters:<br/>rdisp/wp_no_restricted = 0<br/>rdisp/configurable_wp_no = 0<br/>rdisp/dynamic_wp_check = FALSE<br/>rdisp/wp_max_no = Sum of:( rdisp/wp_no_dia + rdisp/wp_no_vb2 + rdisp/wp_no_btc + rdisp/wp_no_vb + rdisp/wp_no_spo + rdisp/wp_no_enq ).<br/><br/>Mind that rdisp/wp_max_no has the default value DEFAULT, which will add two unwanted dynamic processes if not set the number of configured, classical wp types.<br/><br/><strong>6.7 Configuration of the CCMS central monitoring</strong><br/><br/>Depending on the SAP kernel release you upgraded your monitored system from and on the release of the central monitoring system (CEN), it is necessary to check the following scenarios:</p>\n<ul>\n<li>You upgraded the monitored system:</li>\n<ul>\n<li>CEN system is based on SAP_BASIS 7.0 (7.00) and lower: You have to disable the integrated CCMS agents in sapstartsrv of the upgraded system and use standalone CCMS agents instead: sapccm4x for the central monitoring of pure ABAP and Dual-Stack systems, sapccmsr -j2ee for the central monitoring of pure Java systems. Please proceed as outlined in the note 1368389, section \"Solution\".</li>\n</ul>\n<ul>\n<li>CEN system is based on SAP_BASIS 7.0 EhP 1 (7.01):</li>\n</ul>\n</ul>\n<p>                    The registration of the integrated CCMS agents from the central system needs to be repeated. Please follow the instructions given on SAP Help (http://help.sap.com/saphelp_nwpi71/helpdata/EN/44/893e933dc912d3e10000000a422035/content.htm).</p>\n<ul>\n<li>You upgraded the central system:</li>\n</ul>\n<ul>\n<ul>\n<li>No reconfiguration is necessary.</li>\n</ul>\n</ul>\n<p>SAP note 1667336 contains a detailed description for every case.<br/><br/><strong>6.8. Check your environment on Linux and UNIX platforms  (NetWeaver 7.00/7.01/7.10/7.11) </strong><br/><br/>On all UNIX/Linux platforms with deactivated interactive user logon for &lt;sidadm&gt; user - check Note 1825259.</p>\n<p><strong>6.9.  Batch Job Logs (NetWeaver ABAP 7.00/7.01/7.10/7.11/7.20/7.30) </strong></p>\n<p>Check note <a href=\"/notes/1882691\" target=\"_blank\" title=\"1882691  - 7.21 Kernel: Job Log Cannot Be Displayed\">1882691 </a>to prevent errors when displaying job logs.</p>\n<p><strong>6.10. Web service authentication in sapstartsrv</strong></p>\n<p>Check if prerequisites for web service authentication in sapstartsrv from note <a href=\"/notes/927637\" target=\"_blank\">927637</a> are met.</p>\n<p><br/><br/><strong>7. Start the SAP system with the new kernel</strong><br/>Start the SAP system and check the below points if relevant.<br/><br/><strong>7.1 Java or Dual-Stack systems release 7.00 or 7.01:</strong><br/>If the Java instance or even the complete system does not start, check the following points:</p>\n<ul>\n<li>The minimum support packages and patch levels have been installed as outlined in the note 1716826, the section \"Prerequisites\" -&gt; \"Attention\".</li>\n</ul>\n<ul>\n<li>sapjvm: The directory jvm or sapjvm or the file protect.lst have been saved and restored as described in</li>\n</ul>\n<ul>\n<ul>\n<li>Section 4.1, \"On Unix\", steps 3 and 9</li>\n</ul>\n</ul>\n<ul>\n<ul>\n<li>Section 4.2, \"On Windows\", step 4</li>\n</ul>\n</ul>\n<p><strong>7.2 DDIC_TYPELENG_INCONSISTENT short dumps (for 7.00/7.01/7.10/7.11 ABAP releases)</strong><br/><br/>If you did not handle the DDIC_TYPELENG_INCONSISTENT issue described in the note 1716826, the section \"Prerequisites\" -&gt; \"Attention\" while the original kernel was still running, you should now install the note 1610716.<br/><br/><strong>7.3 Load format of ABAP programs (for 7.00/7.01/7.10/7.11 ABAP releases)</strong><br/><br/>After you start the SAP system with the new kernel, the following message is displayed in the system log: 'Load format of PROGRAM not valid.'<br/><br/>You can ignore this message because the load format is automatically regenerated.<br/><br/>It is also possible to regenerate these ABAP load formats directly. For more information about this, see Notes 170039, 162991 and 155580.<br/><br/><strong>7.4. CCMS Monitoring issues</strong><br/><br/>In case of any issues with the CCMS monitoring after the kernel upgrade, check the note 1667336, in particular the section \"<strong>Local monitoring in the upgraded double-stack system</strong>\".</p>\n<p><strong>7.5 Standalone Enqueue Server</strong></p>\n<p>If using a standalone enqueue server with replication, e.g. in a high availability scenario, the replication between the enqueue server and and replication servers will switch to synchronous mode by default with the patch level listen in Note <a href=\"/notes/2036171\" target=\"_blank\">2036171</a>. Check this note if the old behaviour (asynchronous replication) is needed for performance reasons.</p></div>", "noteVersion": 32, "refer_note": [{"note": "1825259", "noteTitle": "1825259 - UNIX sapstartsrv: the system does not start with 720 kernel", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>The system could not be started any more using startsap or sapcontrol commands, after upgrade from kernel 700/701 to kernel 720 or higher.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>sapstartsrv, akk, 720, sapstart, UNIX</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>The interactive logon for &lt;sid&gt;adm user has been disabled on the OS level (UNIX). In this case the environment for SAP and database related entries are missing.<br/><br/>The startup sequence with startsap, sapstartsrv and sapstart has been modified in kernel stack 720<br/></p> <ul><li>on 700/701 the shell script startsap starts both, the sapstartsrv and the sapstart process.</li></ul> <ul><li>on 720 the shell scrip startsap starts sapstartsrv and then sapstartsrv starts the sapstart process.</li></ul> <p><br/>In former case the environment for sapstart and all its child processes is taken from logon user, in latter case the envrironment is calculated by user option of sapstartsrv, for details see note 1301712.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>Adding the necessary environment to the sapstartsrv process would solve this issue.</p> <ol>1. Check the current environment known by sapstartsrv, using the following command and find the difference to the output of \"env\" command, when logged in as &lt;sid&gt;adm:<br/>sapcontrol -nr &lt;Instance number&gt; -function GetEnvironment</ol> <ol>2. Add missing environement like following to instance profile and restart the sapstartsrv:<br/>SETENV_00 = dbms_type=ORA<br/>SETENV_01 = dbs_ora_tnsname=&lt;SID&gt;<br/>SETENV_02 = ...</ol><p></p></div>", "noteVersion": 1}, {"note": "1097751", "noteTitle": "1097751 - IBM i: Information and recommendations for kernel libraries", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are searching for recommendations concerning the R/3 kernel libraries of your R/3 system.<br/>You have installed more than one SAP system on an iSeries, and you require information about the various options to install kernel libraries or load kernels.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>AS/400, OS400, System i, i5/OS, iSeries, IBM i</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>You require information.<br/>Caution: This SAP Note is valid only for kernels as of Version 7.10 and not for older kernels.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong>1. What is a kernel?</strong></p>\n<p>An SAP kernel on an iSeries is a collection of PASE programs in the IFS (executable AIX binary files in the file system) and an assigned ILE library (with 'classic' IBM i objects such as CMD, PGM, SRVPGM, and so on). All programs of a kernel have been optimized and offer the best possible performance. These are also referred to as optimized kernels.</p>\n<p>The kernel objects are saved to the directory /sapmnt/&lt;SID&gt;/exe when the kernel is imported with APYSIDKRN (see the related notes). In this case, the ILE parts are copied from the IFS file ILE_TOOLS to a library (*LIB).</p>\n<p>When you activate the SAPCPE procedure (see SAP Note 1632754 for a system with SAP NetWeaver 7.0 and its enhancement packages - and, by default, with NetWeaver 7.1), program SAPCPE transfers the kernel objects to the directory /usr/sap/&lt;SID&gt;/&lt;INSTANCE&gt;/exe during the system start; in the program SAPCPE, the ILE objects are unpacked into an assigned *LIB. The programs in this directory and the assigned *LIB are used by the SAP system on the iSeries only by the relevant instance.</p>\n<p><strong>2. How do I name the libraries?</strong></p>\n<p>You <strong>cannot</strong> choose any name for the runtime library; it is assigned by the program APYSIDKRN. This name cannot be changed.</p>\n<p>The name of the LIB created by APYSIDKRN for the instance-independent directory is SAP&lt;SID&gt;&lt;IND&gt; (for example, SAPQASIND) - in older kernel versions SAP&lt;SID&gt;71 (for example, SAPQAS71) -, where &lt;SID&gt; is the name of the SAP system (for example, QAS). The ILE library created by the program SAPCPE is called SAP&lt;SID&gt;I&lt;INSTNR&gt; (for example, SAPQASI00) - in older kernel versions SAP&lt;SID&gt;71&lt;INSTNR&gt; (for example, SAPQAS7100) -, where &lt;INSTNR&gt; represents the number of the relevant SAP instance (for example, 00).</p>\n<p>In this case, kernel library only refers to the name of the LIB that contains the ILE elements of the SAP kernel. These elements are only a small part of the complete SAP kernel.</p>\n<p><strong>3. Should I use only one kernel for several SAP systems?</strong></p>\n<p>No, this option is not supported by SAP.</p>\n<p><strong>4. Should I use a separate kernel for each SAP system?</strong></p>\n<p>Only this option is supported by APYSIDKRN because as of NetWeaver 7.10, each instance has its own kernel in the instance directory.</p>\n<p><strong>5. Implementing kernel corrections with APYSIDKRN</strong></p>\n<p>The command APYSIDKRN transfers SAP archives (SAR files) to the corresponding subdirectory of the central SAP kernel directory of a SAP system (&lt;SID&gt;) /sapmnt/&lt;SID&gt;/exe that were previously copied from a DVD or from SAP Service Marketplace to the IFS. Since the files are not initially transferred to the instance directory when you use the SAPCPE procedure, the SAP kernel can still be used on the instances of the SAP system during the transfer; you do not need to shut down the SAP system during this period. If SAPCPE is not activated (on this topic see Note 1632754), you have to close the instances of the SAP system before you implement the kernel corrections.</p>\n<p>You import SAR archives with APYSIDKRN in mode MODE(*ADD). The parts of the archives assigned to the command are added to the existing kernel directory or overwrite older programs with the same names without emptying the kernel directory first. When you apply patches, you must use the parameter MODE(*ADD).</p>\n<p>You can use the parameter MODE(*TEST) to test which effects the actual application of a patch using MODE(*ADD) will have.</p>\n<p>In addition, when you use the APYSIDKRN procedure to apply patches in the parameter SAVSAR, you can assign the name of a directory into which the current content of the kernel directory is copied prior to applying the patches. If an error occurs, you can import this archive to restore the status to the status prior to applying the patches (see SAP Note 1432807). You can use the following call as an example for the command APYSIDKRN. For more information, use the online help (F1) of the command: ?APYSIDKRN SID(&lt;PRD&gt;)</p>\n<p><br/> ARCHIVES('/home/<USER>/&lt;ARCHIVE&gt;')</p>\n<p> CARPATH('/home/<USER>')</p>\n<p> MODE(*ADD)</p>\n<p><strong>6. Implementing a backwards compatible kernel (AKK) with APYSIDKRN</strong></p>\n<p>The procedure to install, for example, EXT Kernel 7.22 into a system that previously used Kernel 7.21, or Kernel 7.53 into a system that was previously operated with Kernel 7.49, is slightly more elaborate:</p>\n<p>a) Log on as a QSECOFR-type user and execute the following commands or actions:</p>\n<p>b) Switch to the directory containing the downloaded archive and SAPCAR if SAPCAR cannot be found using the environment variable PATH:</p>\n<p>CHGCURDIR '&lt;newkernel&gt;'</p>\n<p>c) If the library SAP_TOOLS exists, execute a cleanup: CLRLIB SAP_TOOLS</p>\n<p>d) The library SAP_TOOLS is filled with data from the save file ILE_TOOLS using the program \"iletools\". The archive SAPEXE.SAR and the archive ILE.SAR contain this program and the save file. Use the archive with the higher patch level.</p>\n<p>You can use copy and paste to copy and execute one of the following commands in your session:</p>\n<p>If you use SAPEXE.SAR:</p>\n<p>CALL QP2SHELL PARM('/QOpenSys/usr/bin/csh' '-c' 'SAPCAR -xvf SAPEXE.SAR iletools ILE_TOOLS; ./iletools')</p>\n<p>If you use ILE.SAR:</p>\n<p>CALL QP2SHELL PARM('/QOpenSys/usr/bin/csh' '-c' 'SAPCAR -xvf ILE.SAR iletools ILE_TOOLS; ./iletools')</p>\n<p>e) Since this call to QP2SHELL produces no output, check whether the library SAP_TOOLS exists and contains some objects; if it does not, you should Sie use the WRKSPLF command to search for spool files with error messages.</p>\n<p>f) You can now use the following commands to set the authorizations of objects in SAP_TOOLS:</p>\n<p>If you already use the new user concept ('newuserconcept'), execute the following steps:</p>\n<p>ADDENVVAR ENVVAR(CLASSICUSERCONCEPT) VALUE('N')</p>\n<p>ADDLIBLE SAP_TOOLS</p>\n<p>FIXSAPOWN *NONE SAP_TOOLS</p>\n<p>If you still use the old user concept ('classicuserconcept'), execute the following steps:</p>\n<p>ADDENVVAR ENVVAR(CLASSICUSERCONCEPT) VALUE('Y')</p>\n<p>ADDLIBLE SAP_TOOLS</p>\n<p>FIXSAPOWN *NONE SAP_TOOLS</p>\n<p>g) Log on to the system as the user &lt;SID&gt;ADM and execute the following commands:</p>\n<p>ADDLIBLE SAP_TOOLS</p>\n<p>If you want to test the kernel installation in advance, you can use *TEST mode. Use the following command to test the intended kernel installation:</p>\n<p>APYSIDKRN SID(&lt;SAPSID&gt;) ARCHIVES('&lt;newkernel&gt;/*') SAVSAR(*NONE) MODE(*TEST) CHGENV(*NO) UPDAPAR(*NO)</p>\n<p>Then, install the new kernel with the following command:</p>\n<p>APYSIDKRN SID(&lt;SAPSID&gt;) ARCHIVES('&lt;newkernel&gt;/*') SAVSAR(*STANDARD) MODE(*ADD) CHGENV(*YES) UPDAPAR(*NO)</p>\n<p>h) Delete the SQL packages that remain from using the old kernel:</p>\n<p>DLTR3PKG SID(&lt;SAPSID&gt;)</p>\n<p>i) Log off and log on again as the user &lt;SID&gt;ADM. You now use the new kernel.</p>\n<p>The implemented corrections only take effect after you stop the SAP system and restart it. When the system is restarted, all programs in the central directory /sapmnt/&lt;SID&gt;/exe that are newer than the programs in the instance directory are copied here by the program SAPCPE; if necessary, the ILE objects of the assigned *LIB are also created again.</p></div>", "noteVersion": 20}]}, {"note": "1699600", "noteTitle": "1699600 - Documentation of profile parameter rsdb/rda missing", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>You are using the SAP Business Application Accelerator powered by HANA Add On and cannot find the documentation of parameter rsdb/rda in transaction RZ11.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>Your release is SAP_BASIS 7.31 or newer.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>In release SAP_BASIS 7.31 or newer the profile parameter documentation is not available in transaction RZ11, as the SAP Business Application Accelerator powered by HANA Add On is developed in release 7.00 and the documentation repository has changed in release 7.31.<br/><br/>For your reference you find the profile parameter documentation in the appendix of the SAP Business Application Accelerator powered by HANA Customer Guide attached to SAP Note 1694697<br/></p></div>", "noteVersion": 3}], "activities": [{"Activity": "Technical System Configuration", "Phase": "During conversion project", "Condition": "Mandatory", "Additional_Information": "Deactivate SAP Business Application Accelarator"}]}