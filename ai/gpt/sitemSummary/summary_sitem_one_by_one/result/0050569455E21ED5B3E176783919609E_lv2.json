{"guid": "0050569455E21ED5B3E176783919609E", "sitemId": "SI37: Logistics_PP", "sitemTitle": "S4TWL - Selected Business Functions in PP area", "note": 2271206, "noteTitle": "2271206 - S4TWL - Selected Business Functions in PP area", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are doing a system conversion from SAP ERP to SAP S/4HANA, on-premise edition. The following SAP S/4HANA Transition Worklist item is applicable in this case.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>The functional scope of a SAP ERP system is determined by a large set of business functions. This was necessary because new or enhanced features were not allowed to change existing reports or transactions. Every release new business functions were added to switch the features developed for the release. Business functions became unclear over time and the combinatorics of different business functions difficult to predict. Therefore business functions have been cleaned up in SAP S/4HANA. Many business functions are always on. A few business functions have been declared obsolete. This note provides details for the obsolete business functions in the PP components.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong>Description</strong></p>\n<p>Selected PP business functions are not part of the target architecture in SAP S/4 HANA, on-premise edition 1511 – FPS1. The following PP business functions are obsolete and cannot be switched on in S/4HANA:</p>\n<ul>\n<li>LOG_PP_INTELI_01  (Power list for process manufacturing)</li>\n<li>LOG_PP_PRSP (Portal for production supervisor)</li>\n<li>LOG_PP_BATCH_HISTORY</li>\n<li>LOG_PP_CFB  (Food and Beverage Solution)</li>\n</ul>\n<p>Business EA-SCM is always on in S/4HANA to enable the migration from SAP-ERP to S/4HANA.</p>\n<ul></ul>\n<p><strong>Basic Information related to Business Functions</strong></p>\n<p>If a business function was switched “ON” in the Business Suite start release system, but defined as “always_off” in the SAP S/4HANA, on-premise edition target release, then a system conversion is not possible with this release. See SAP Note 2240359 - SAP S/4HANA, on-premise edition 1511: Always-Off Business Functions. If a business function is defined as \"Business function is obsolete and cannot be activated\" in the target release (SAP S/4HANA, on-premise edition 1511 – FPS1), then the business function status remains unchanged from the status on start release and system conversion is possible. Nevertheless the functionality related to these business functions is not always available within SAP S/4 HANA, on-premise edition 1511 – FPS1.</p>\n<p><strong>Business Process related Information</strong></p>\n<p>Business function <strong>LOG_PP_INTELI_01</strong> offered a production supervisor work list. This is available as of S/4HANA on premise edition 1611 release as Fiori apps \"Manage production order operations\" and \"Manage production orders\".</p>\n<p>Business function <strong>LOG_PP_PRSP</strong> offered portal roles for the production supervisor. This is available as of S/4HANA on premise edition 1511 release as Fiori roles and launchpad.</p>\n<p>Regarding the functions switched by business function <strong>LOG_PP_BATCH_HISTORY</strong> please see the separate note 2270242.</p>\n<p>Business function <strong>LOG_PP_CFB</strong> switched various functions like</p>\n<ul>\n<li>Catch Weight Management</li>\n<li>Various print reports</li>\n<li>Weigh scale integration</li>\n<li>Enhanced production version management </li>\n</ul>\n<p>Catch weight management is available in S/4HANA w/o switching.</p>\n<p>One of the CFB functions was enhanced production version management implemented by reports CFB_PV_MRP_PREP_MIXCAL and CFB_PV_MRP_RELEASE_LOCK. The reports allowed to move production versions between active and inactive production versions, which were stored in a separate databases. Inactive production versions were invisible to MRP. In S/4HANA production versions have status \"Blocked for automatic sourcing\", which serves the same purpose. If you used reports CFB_PV_MRP_PREP_MIXCAL or CFB_PV_MRP_RELEASE_LOCK then use the production version status in future.</p>\n<p>Enterprise extension <strong>EA-SCM</strong> offered various functions like</p>\n<ul>\n<li>Direct Store Delivery DSD</li>\n<li>Cross Docking</li>\n<li>Vehicle Space Optimization</li>\n<li>Logistical Value Added Services</li>\n</ul>\n<p>The functions switched by EA-SCM are still available as part of the S/4HANA compatibility pack, but should be considered not part of the S/4HANA target architecture. Successor functions for cross docking and vehicle space optimization are available in transportation management SCM-TM.</p>\n<p>Originally enterprise extension EA-SCM also switched PI-sheets, X-steps, MES integration, and production order splitting. These functions have been removed from the switch. In other words the PI-sheets, X-steps, MES integration, and production order splitting work in S/4HANA on premise edition even if business function EA-SCM is switched off. Please also note the corresponding notes for PI-sheets and MES integration.</p>", "noteVersion": 4, "refer_note": [{"note": "2270242", "noteTitle": "2270242 - S4TWL - Batch History", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are doing a system conversion to SAP S/4HANA, on-premise edition. The following SAP S/4HANA Transition Worklist item is applicable in this case.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p id=\"\"></p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p id=\"\"></p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong>Description</strong></p>\n<p>The Batch History is a rarely used part of the broad funtionality in the Batch Information Cockpit. It must NOT be confused with the batch where-used list or the SAP Global Batch Traceability solution.</p>\n<p>The batch history described here is a change log for a single selected batch master record and does not reflect the where-used information in the logistic chain. The batch history is not considered as the target architecture within SAP S/4HANA, on-premise edition (Functionality available in SAP S/4HANA on-premise edition but not considered as future technology).</p>\n<p>There is no functional equivalent planned for future releases.</p>\n<p><strong>Business Process related information</strong></p>\n<p>No influence on business processes expected. The batch-where used functionality (transaction MB56, bottom-up/top-down analysis) are not affected at all.</p>\n<p>The transaction codes related to Batch History are still available within SAP S/4HANA, on-premise edition when the respective switch is activated. Also the configuration in the IMG is only visible after activation of the switch.</p>\n<p>Depending on the configuration of the batch history, it may happen that batch history records comprise references to business partners. These records will not be found by the Business Partner End of Purpose (EoP) Check in Batch Management, nor will they be found by the Information Retrieval Framework (IRF). Hence, if batch history records are still present in the system, it is in the responsibility of the customer to ensure that these records are considered in either frameworks by providing dedicated custom implementations.</p>", "noteVersion": 4}, {"note": "2268070", "noteTitle": "2268070 - S4TWL - Browser-based Process Instruction Sheets/Electronic Work Instructions", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are doing a system conversion to SAP S/4HANA. The following SAP S/4HANA Transition Worklist item is applicable in this case.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Packages COPOC, CMX_POC, CMX_PII, Transactions CO55, CO60, CO60E, CO60XT, CO64, CO67</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong>Description</strong></p>\n<p>The functionality of browser-based PI sheets and electronic Work Instructions is still available in SAP S/4HANA (functionality available in SAP S/4HANA but not considered as future technology).</p>\n<p><a href=\"https://help.sap.com/docs/SAP_S4HANA_ON-PREMISE/feded183701e4ba0b42bc4fc31ad7a12/d588bf53f106b44ce10000000a174cb4.html\" target=\"_blank\">Browser-based Process Instruction Sheets (PI Sheets)</a> and electronic <a href=\"https://help.sap.com/docs/SAP_S4HANA_ON-PREMISE/34de0103497c4b80a7c7fbf6952ff971/4900b753128eb44ce10000000a174cb4.html\" target=\"_blank\">Work Instructions</a> are based on a framework that allows the creation of worker UIs optimized for the needs of individual workers. The content of the PI Sheet/Work Instruction is rendered as plain HTML and displayed in the <a href=\"https://help.sap.com/docs/ABAP_PLATFORM_NEW/70396d7dec4c4f19b9ca3b2e47559d12/4d77a65efe472b8ae10000000a42189b.html?q=%22control%20framework%22\" target=\"_blank\">SAP HTML Viewer</a> of ABAP SAP GUI screens. It requires SAP GUI for Windows as UI technology and does not work as plain WebUI in an internet browser. Browser-based PI sheets and EWIs require the MS Internet Explorer as the HTML viewer of the SAP GUI for Windows dependends on MS Internet Explorer (technical prerequisite for HTML viewer). Please check respective attached notes <a href=\"/notes/2224356\" target=\"_blank\">2224356</a> and <a href=\"/notes/2999251\" target=\"_blank\">2999251</a> regarding MS Internet Explorer lifecycle/compatibilty.</p>\n<p>Content of browser-based PI Sheets and Work Instructions is defined using process instruction characteristics (XStep-based process instructions or characteristic-based process instructions [only PI sheets]). Based on the process instruction content, the <a href=\"https://help.sap.com/docs/SAP_S4HANA_ON-PREMISE/feded183701e4ba0b42bc4fc31ad7a12/9c88bf53f106b44ce10000000a174cb4.html\" target=\"_blank\">Control Recipes</a>/<a href=\"https://help.sap.com/docs/SAP_S4HANA_ON-PREMISE/34de0103497c4b80a7c7fbf6952ff971/4c00b753128eb44ce10000000a174cb4.html\" target=\"_blank\">Control Instructions</a> are created. If the control recipes/instructions belong to the control recipe destination of type 4, the corresponding browser-based PI sheet/Work Instruction is created when the control recipe/instruction is sent.</p>\n<p>The rendered HTML content of browser-based PI Sheets and Work Instructions uses XSL and CSS. SAP standard XSL and CSS can be exchanged by custom style sheets. This allows easy custom adoption of the UI at the customer.</p>\n<p>Browser-based PI sheets are used in the regulated environment (pharmaceutical production, and so on) to control and track the production execution. They can be archived to optical archives as part of the <a href=\"https://help.sap.com/docs/SAP_S4HANA_ON-PREMISE/21aead0c98bd4755abdacd91c99e3393/7725b853ff98b44ce10000000a174cb4.html\" target=\"_blank\">Electronic Batch Record (EBR)</a> to ensure compliant manufacturing (comply with US CFR21 part 11 and so on).</p>\n<p>Starting with SAP R/3 4.6 C, formerly used ABAP-list based process instruction sheets have been replaced by browser-based PI sheets (see SAP Note <a href=\"/notes/397504\" target=\"_blank\">397504 - Support for ABAP list-based PI sheet</a>). Related functionality is still part of SAP ECC and S/4 -&gt; separate simplification item.</p>\n<p>Digital Manufacturing Cloud’s (DMC) Production Operator Dashboard (POD) will provide similar functionality as PI-sheets in future. It will allow displaying Work Instructions, recording of actual data, and enforcing predefined production processes. The POD is developed keeping the following objectives in mind:</p>\n<ul>\n<li>The POD is a highly functional, attractive, and usable UI for shop-floor operators</li>\n<li>DMC runs together with S/4HANA as well as with S/4HANA Cloud</li>\n<li>DMC and S/4HANA communicate through SOAP messages and oData services</li>\n<li>Production engineers shall be able to design the POD w/o programming or HTML skills</li>\n<li>When designing the POD, production engineers can choose from many predefined UI controls</li>\n<li>The POD shall run in the most popular browsers using standard UI5 and Fiori technology</li>\n<li>It will be possible to connect the POD with sensors and actuators on the shop-floor</li>\n</ul>\n<p>For timelines please refer to the official roadmap for <a href=\"https://roadmaps.sap.com/board?PRODUCT=73555000100800001492&amp;range=CURRENT-LAST\" target=\"_blank\">SAP Digital Manufacturing Cloud</a> (https://roadmaps.sap.com).</p>\n<p><strong>Business Process related information</strong></p>\n<p>No migration tool from SAP PP-PI browser-based POC UI configuration to SAP DMC POD configuration will be offered.</p>\n<p><strong>Required and Recommended Action(s)</strong></p>\n<p>You can still use PI-sheets using SAP-GUI for Windows. You should however be aware of the following restrictions:</p>\n<ul>\n<li>Most classic dynpro transactions run not only in SAP GUI but also in Web-GUI. Web-GUI requires zero footprint, no installations on the end-user PCs, and runs on different operatingn systems and thus simplifies administration. PI-sheets only run in SAP GUI for Windows, which still has to be installed on end-user PCs.</li>\n<li>PI-sheets only run in SAP GUI. They do not run in the Web UI. SAP offers a lot of innovations in Fiori. Fiori and Web-Gui applications can be launched from the Fiori launch-pad. Users will miss the innovations shipped in the Fiori environment.</li>\n<li>PI-sheets run in an HTML plugin, which uses MS Internet Explorer technology. Please check </li>\n</ul>\n<p><strong>Custom Code Check / Adaption </strong></p>\n<div>\n<p>Custom enhancements and modifications of Browser-based Process Instruction Sheets/Electronic Work Instructions in SAP S/4HANA will not work for successor UIs/PODs in SAP DMC.</p>\n<p>Due to the tight integration of browser-based PI Sheets/Work Instructions and Production Operator Cockpit functionality (see note <a href=\"/notes/2270235\" target=\"_blank\">2270235 S4TWL - Process Operator Cockpits</a>) please perform a combined custom code check.</p>\n<p>After switching from browser-based PI Sheets/Electronic Work Instructions to a sucessor solution in SAP DMC check your custom code in context of data transfer from browser-based PI Sheets/Electronic Work Instructions  to whatever receiver and clean-up your custom code that makes use of Browser-based Process Instruction Sheets/Electronic Work Instructions functionality. The below (incomplete) list is an entry point for the analysis of your custom code. Make sure that with end of support of the compatibility scope item no newly entered or changed data is transferred with any custom code from browser-based PI Sheets/Electronic Work Instructions and that no custom code initiates save of data (create/update) to respective database tables.</p>\n<p>Transactions (no call or clone in custom code)</p>\n<ul>\n<li>CO55 Worklist for Maintaining PI Sheets</li>\n<li>CO60 Find PI Sheet</li>\n<li>CO60E PI Sheet: Change</li>\n<li>CO60XT Find Work Instructions</li>\n<li>CO64 Worklist for Completing PI Sheets</li>\n<li>CO67 Worklist for Checking PI Sheets</li>\n</ul>\n<p>Database tables (no save of data through custom code)</p>\n<ul>\n<li>POC_DB_APPL_STEP Domain Model: Application Steps</li>\n<li>POC_DB_ARC_CONN Optical Archiving: Link Data (ArchiveLink)</li>\n<li>POC_DB_C_COMMAND Domain Model: Command Collection</li>\n<li>POC_DB_C_COMMENT Domain Model: Comment Collection</li>\n<li>POC_DB_C_DEVIAT Domain Model: Deviation Collection</li>\n<li>POC_DB_C_DEVVAR Domain model: Collection for variable setting (deviation)</li>\n<li>POC_DB_C_METADAT Domain Model: Local Value Collection</li>\n<li>POC_DB_C_OPER Domain Model: Operation Collection</li>\n<li>POC_DB_C_PARTIT Domain Model: Partition Element Collection</li>\n<li>POC_DB_C_PREDEC Domain Model: Predecessor Table</li>\n<li>POC_DB_C_SERVICE Domain Model: Service Operation Collection</li>\n<li>POC_DB_C_STEP Domain Model: Step Collection</li>\n<li>POC_DB_C_SYTABLE Domain Model: Variable Collection (Symbol Table)</li>\n<li>POC_DB_C_TRIGGER Domain Model: Trigger Collection</li>\n<li>POC_DB_C_VALUE Domain Model: Value Container Collection</li>\n<li>POC_DB_COMMAND Domain Model: Table for Step Commands</li>\n<li>POC_DB_CONF_REQ Domain Model: Table for Signature Requests</li>\n<li>POC_DB_DATA_REQ Domain Model: Table for Data Requests</li>\n<li>POC_DB_DEV_SIGN Signature Key: Metadata Signature Process Step</li>\n<li>POC_DB_DOMAIN Domain Model: Database Table for Domains</li>\n<li>POC_DB_EVT_SIGN Signature Keys: Metadata for Event Log</li>\n<li>POC_DB_EXEC_REQ Domain Model: Table for Execution Request</li>\n<li>POC_DB_EXEC_STRA Domain Model: Table for Execution Strategies</li>\n<li>POC_DB_MESS_REQ Domain Model: Table for Message Requests</li>\n<li>POC_DB_OPERATION Domain Model: Table for Operations</li>\n<li>POC_DB_PARTITION Domain Model: Partition</li>\n<li>POC_DB_PROC_STEP Domain Model: Table for Process Steps</li>\n<li>POC_DB_PROTOCOL Domain Model: Event Log for Process Steps</li>\n<li>POC_DB_REAS_SIGN Signature Key: Meta Data Reason for Value Change</li>\n<li>POC_DB_SIG_HANDL Domain Model: Signature Handler</li>\n<li>POC_DB_SIGNATURE Domain Model: Signature</li>\n<li>POC_DB_STEP Domain Model: Table for Step Basic Data</li>\n<li>POC_DB_STEP_SIGN Signature Key: Metadata Signature Process Step</li>\n<li>POC_DB_TRIG_PROT Logging: Trigger for Event in Domain Model</li>\n<li>POC_DB_VALUE Domain Model: DB Table for Value Container</li>\n</ul>\n<p>Programs (no call or clone in custom code)</p>\n<ul>\n<li>RCOPOC_CO55 Worklist for Maintaining PI Sheets</li>\n<li>RCOPOC_CO60E Program PCOPOC_CO60E</li>\n<li>RCOPOC_CO64 Worklist for Maintaining PI Sheets</li>\n<li>RCOPOC_CO65 Worklist for Checking PI Sheets</li>\n<li>RCOPOC_PIWKLT_ARCHIVE Archiving of PI Sheets/Work Instructions</li>\n<li>RCOPOC_TEST_XML_GENERATOR Test: Generate and Download XML File for Control Recipe</li>\n<li>RCOPOC_TEST_XML_PARSING Test Program for Parsing an XML Document for a PI Sheet</li>\n<li>RCOPOC_TESTFRAME_GEN Function Module testframe generation</li>\n<li>RCOPOC_WKLT Worklist for PI Sheets/Work Instructions</li>\n<li>RCOPOC_WKLT_ARC_PROD_INS Worklist of Archived PI Sheets/Work Instructions</li>\n</ul>\n<p>Function Modules (no call or clone in custom code)</p>\n<ul>\n<li>COCB_READ_PI_COMPONENTS Material Listing for PI Sheet</li>\n<li>COCB_TRANSFER_CR_TO_MDE Transferring Control Recipe to Manual Data Entry (PI Sheet)</li>\n<li>COMH_CREATE_MESSAGE_NO_DIALOG Generating Status Confirmation for a PI Sheet</li>\n<li>COP0_ARCHIVE_PI_SHEET Archivieren aller Prozeßvorgabenlisten zu einem Batchrezept</li>\n<li>COP0_DELETE_PI_SHEET Löschen von Herstellanweisungen abhängig vom Prozeßauftrag</li>\n<li>COP0_EDIT_PI_SHEET Bearbeiten von Prozeßvorgabenlisten</li>\n<li>COP0_GET_MODE_OF_PI_SHEET Liefert Modus des aktuell bearbeiteten PI-Sheets</li>\n<li>COP0_GET_PI_SHEET_DEVIATIONS Liefert die Abweichungen zu einem PI-Sheet</li>\n<li>COPF_OPC_ITEM_WRITE2 NOTRANSL: OPC Herstellanweisung: Lesen eines OPC-Item-Werts</li>\n<li>COPF_OPC_ITEM_WRITE_CHAR NOTRANSL: OPC Herstellanweisung: Schreiben eines OPC-Item-Werts</li>\n<li>COPF_OPC_ITEM_WRITE_FLOAT NOTRANSL: OPC Herstellanweisung: Schreiben eines OPC-Item-Werts</li>\n<li>COPF_OPC_ITEM_WRITE_MULTIPLE NOTRANSL: OPC Herstellanweisung: Lesen eines OPC-Item-Werts</li>\n<li>COPF_OPC_ITEM_WRITE_TIME NOTRANSL: OPC Herstellanweisung: Schreiben eines OPC-Item-Werts</li>\n<li>COPI_CHAR_IN_PI_SHEETS NOTRANSL: Verwendungsnachweis eines Merkmales in Prozeßvorgabenlisten und</li>\n<li>COPI_CHECK_CHAR_IN_PI_SHEETS NOTRANSL: Verwendungsnachweis für Merkmale in Prozeßvorgabenlisten und Mel</li>\n<li>COPI_CREATE_ELECTRONIC_SIGN</li>\n<li>COPI_CREATE_NON_INPUT_MS</li>\n<li>COPI_DPS_TEXT_UPDATE</li>\n<li>COPI_EDIT_NEW_DPS_TEXT_VERSION</li>\n<li>COPS_DELETE_PI_SHEET Deleting all Data Records for a PI Sheet</li>\n<li>COPS_FEATURE_VARIABLE_UPDATE Saving PI Sheet Variables</li>\n<li>COPS_MDE_FEATURE_UPDATE Database Update for PI Sheet Process Instructions</li>\n<li>COPS_MDE_MESSAGE_UPDATE Storing Data for Manual Data Entry</li>\n<li>COPS_MS_FT_ASSIGNM_UPDATE Storing Assignment Table of Process Messages and Instructions</li>\n<li>COPS_SIGNATURE_RECORDS_UPDATE</li>\n<li>COPT_BUFFERED_SAVE_TEXT Storing PI Sheet Texts with Buffer</li>\n<li>PISHEET_GET_EVENTS_GLOBAL NOTRANSL: Herstellanweisung: Liste von globale Events</li>\n<li>PISHEET_GET_EVENTS_LOCAL NOTRANSL: Cockpit: lokale Events</li>\n<li>PI_SHEET_GET_STATE NOTRANSL: Neue &amp; alte Herstellanweisung: Lesen des Status</li>\n<li>PI_SHEET_PRINT_WITH_EBR_STYLE NOTRANSL: Neue Herstellanweisung: Anzeige/Ausdruck im EBR-Layout</li>\n<li>PI_SHEET_SHOW NOTRANSL: Neue &amp; alte Herstellanweisung: Anzeige (Anzeige-/Änderungsmodus)</li>\n</ul>\n<p>BAdI's with interface methods (check for custom implementation)</p>\n<ul>\n<li>BADI_CMX_POC_ASYNC_SIGN Handle XML change error</li>\n<ul>\n<li>IF_BADI_CMX_POC_ASYNC_SIGN~CHANGE_ABORT_MESSAGE</li>\n</ul>\n<li>BADI_CMX_POC_DYN_SIGN_STRAT Enhancements for Dynamic Selection of Signature Strategies<br/>Interface methods IF_BADI_CMX_POC_DYN_SIGN_STRAT~</li>\n<ul>\n<li>ADJUST_PI_CHECK_MESSAGE Check Process Instruction: Set Suited Message Texts</li>\n<li>CHECK_DYN_SIGN_STRAT_ALLOWED Check that Dynamic Signature Strategy Function is Allowed</li>\n<li>EXECUTE_INSTR_SEMANTIC_CHECK Executes Additional Checks during Semantic Check</li>\n<li>EXECUTE_XS_INSTR_CONTENT_CHECK XStep-based Process Instruction: Executes Additional Checks</li>\n<li>GET_CUST_DOMAIN_SUBCLASS Value Search: Returns Information on Characteristic &amp; Subcl.</li>\n<li>HANDLE_FAILED_VALUE_SEARCH Search for Selected Signature Strategy: Error Handling</li>\n<li>RESET_STRAT_TO_PLACEHOLDER Reset Signature Strategy to Placeholder Value</li>\n</ul>\n<li>BADI_CMX_POC_REASON_VAL_CHG Processing of Reasons for Value Changes<br/>Interface methods IF_BADI_CMX_POC_REASON_VAL_CHG~</li>\n<ul>\n<li>CHECK_COMMENT_OF_REASON Checks the Entered Comment of Reason for Value Change</li>\n<li>CHECK_REASON_VAL_CHG_ALLOWED Checks that Processing of Reasons is Allowed</li>\n</ul>\n</ul>\n<span>Enhancement Spots (check for custom implementation)</span><br/>\n<ul>\n<li>ES_RCOPOC_WKLT</li>\n<li>ES_SSAPLCMX_PII_CP_SRV_DST_PMC Enhancement PI Interpreter (Name of Destination)</li>\n</ul>\n</div>", "noteVersion": 4, "refer_note": [{"note": "2999251", "noteTitle": "2999251 - FAQ: Microsoft Internet Explorer 11 lifecycle", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>SAP utilizes the Internet Explorer Browser Control of Microsoft's Internet Explorer 11 (<a href=\"https://en.wikipedia.org/wiki/MSHTML\" target=\"_blank\">MSHTML/Trident browser engine</a>, a proprietary browser engine in-built the Microsoft Windows operating system) for the display and maintenance of browser-based process management applications.</p>\n<p>Microsoft made recent announcements with respect to their browser's lifecycle and clients request for clarity on the future usability of</p>\n<ul>\n<li><a href=\"https://help.sap.com/viewer/26aea0a32c784608a7653e84de0db9cd/2020.000/en-US/0f80b8535c39b44ce10000000a174cb4.html\" target=\"_blank\">Browser-based PI Sheets</a></li>\n<li>XStep-based <a href=\"https://help.sap.com/viewer/34de0103497c4b80a7c7fbf6952ff971/2020.000/en-US/4900b753128eb44ce10000000a174cb4.html\" target=\"_blank\">Work Instructions</a></li>\n<li><a href=\"https://help.sap.com/viewer/26aea0a32c784608a7653e84de0db9cd/2020.000/en-US/4368b8535c39b44ce10000000a174cb4.html\" target=\"_blank\">Process Manufacturing Cockpits</a></li>\n</ul>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p id=\"\">IE, IE11, browser control, Internet Explorer Browser Control, MSHTML, Trident, Blink, MS Edge WebView 2</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>The Internet Explorer browser control is the current standard browser engine used for HTML control in SAP GUI for Windows. This HTML control is required to run browser-based SAP applications like the browser-based PI-Sheets.</p>\n<p>As per SAP Note <a href=\"/notes/147519\" target=\"_blank\">147519</a>:</p>\n<ul>\n<li>Current latest available SAP GUI for Windows (version 7.60) still supports the Internet Explorer Browser Control. This SAP GUI version is planned to be supported (full support) by SAP until 12th of April 2022.</li>\n<li>Upcoming SAP GUI for Windows (versions 7.70+) include legacy support for the Internet Explorer Browser Control</li>\n<ul>\n<li>\n<div class=\"O2\">Is basis for next S/4 HANA on-premise release (2021)</div>\n</li>\n<li>\n<div class=\"O2\">Can be used by customers of SAP Business Suite and lower S/4 HANA releases</div>\n</li>\n</ul>\n</ul>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>The impact on the end of the support for Internet Explorer on SAP GUI for Windows directly influences the browser-based process management applications. For a basic understanding of the impact, please carefully read the following SAP Notes:</p>\n<ul>\n<li><a href=\"/notes/2913405\" target=\"_blank\">2913405</a> - SAP GUI for Windows: Dependencies to browsers / browser controls</li>\n<li><a href=\"/notes/3058309\" target=\"_blank\">3058309</a> - End of support for Internet Explorer by Microsoft - impact on SAP GUI for Windows</li>\n</ul>\n<p>Browser-based process management applications are ABAP applications that invoke web content in SAP GUI for Windows using the HTML control (CL_GUI_HTML_VIEWER). This method is named CASE 2 in SAP Note <a href=\"/notes/2913405\" target=\"_blank\">2913405</a>. In accordance with SAP Note <a href=\"/notes/3058309\" target=\"_blank\">3058309</a>, the Internet Explorer Browser Control (mshtml.dll) can still be used to run the HTML content inside SAP GUI for Windows.</p>\n<p>SAP GUI for Windows strongly recommends using Microsoft Edge WebView2 as an alternative for the Internet Explorer Browser Control. Since SAP GUI for Windows version 7.70, both browser controls are supported. Which browser control is used in the HTML control is defined in the configuration of SAP GUI for Windows (parameter<em> Browser Control</em> in the <em>Control Settings</em> of the <em>Interaction Design</em>):</p>\n<ul>\n<li>Internet Explorer Browser Control is used when browser control setting <em>Internet Explorer</em> is chosen</li>\n<li>Microsoft Edge WebView2 Browser Control is used when browser control setting <em>Edge (Based on Chromium)</em> is chosen</li>\n</ul>\n<p>With browser control setting <em>Internet Explorer </em>displaying and maintaining these browser-based process management applications is still possible through the Internet Explorer Browser Control.</p>\n<p>Please note that SAP Note <a href=\"/notes/1672817\" target=\"_blank\">1672817</a> (<em>Browser: Microsoft Legacy Edge and Internet Explorer Support Policy Note</em>) is not applicable to browser-based PP-PI-PMA applications, as these are SAP GUI for Windows applications and not SAP Web applications. Further information about this setup is contained in SAP Note <a href=\"/notes/2913405\" target=\"_blank\">2913405</a> (<em>SAP GUI for Windows: Dependencies to browsers / browser controls</em>).</p>\n<p>SAP's recommendation is to migrate browser-based process management applications to the HTML5 standard, which is compatible with browser control Microsoft Edge WebView2. The following SAP Notes provide technical background and guidance on how to do so.</p>\n<ul>\n<li><a href=\"/notes/3156433\" target=\"_blank\">3156433</a> - HTML5 - Migration Guide for Browser-based Process Management Applications</li>\n<li><a href=\"/notes/https://launchpad.support.sap.com/#/notes/3156434\" target=\"_blank\">3156434</a> - HTML5 - Setup and Usage of HTML5 Compatibility Mode for PI Sheets and Work Instructions</li>\n<li><a href=\"/notes/3156361\" target=\"_blank\">3156361</a> - HTML 5-compliant Browser Framework (BFW) JavaScript Files</li>\n<li><a href=\"/notes/https://launchpad.support.sap.com/#/notes/3156360\" target=\"_blank\">3156360</a> - SAP Standard Stylesheets for Browser-based Process Management Applications</li>\n<li><a href=\"/notes/3156432\" target=\"_blank\">3156432</a> - Required Web Repository Content for HTML 5 Standard Support</li>\n<li><a href=\"/notes/3156358\" target=\"_blank\">3156358</a> - Standard XStep Library 2022 - Newest Reusable Standard XStep Templates</li>\n<li><a href=\"/notes/3215965\" target=\"_blank\">3215965</a> - HTML 5 - ABAP code adjustments of Internet Explorer dependency replacement</li>\n</ul>", "noteVersion": 7}, {"note": "2269324", "noteTitle": "2269324 - Compatibility Scope Matrix for SAP S/4HANA", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>Through the Compatibility Packages (CP) listed in the attachment \"Compatibility Scope Matrix\", SAP provides a limited use right to SAP S/4HANA on-premise customers to run certain classic SAP ERP solutions on their SAP S/4HANA installation. Condition is that these customers have licensed the applicable solutions as set forth in their License Agreements. Compatibility Pack use rights may apply to selected private cloud deployments as well, without the prerequisite of an on-premise classic license. Please refer to the respective Service Description Guide for details.</p>\n<p>This use right expires on Dec 31, 2025, and is available to installed-base as well as net-new customers.</p>\n<p>The <a href=\"https://news.sap.com/2020/02/sap-s4hana-maintenance-2040-clarity-choice-sap-business-suite-7/\" target=\"_blank\">announcement</a> about the extension of maintenance for Business Suite solutions has no influence on the end of compatibility pack use rights - they will be terminated after 2025.<sup>(1)</sup></p>\n<p>Besides reading the attached documents, SAP recommends the latest SAP Community webinar on the topic: <a href=\"https://www.youtube.com/live/muPrV5J7ffM?feature=shared\" target=\"_blank\">https://www.youtube.com/live/muPrV5J7ffM</a></p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>SAP S/4HANA Compatibility Scope Matrix, Way Forward</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>In the “Attachments” section, the “Overview Presentation” and “Detail FAQ” documents explain the “why”, “what” and “how” of CPs. For functional details, please also refer to the Feature Scope Description (FSD) of SAP S/4HANA, <a href=\"https://help.sap.com/doc/e2048712f0ab45e791e6d15ba5e20c68/latest/\" target=\"_blank\">latest version</a> (or through http://help.sap.com/s4hana), chapter 5.</p>\n<p>The “Way Forward” presentation and overview list, also under “Attachments”, provide information about solution alternatives in the perpetual scope of SAP S/4HANA. They describe the status of each Compatibility Pack and provide links to more detailed information about its strategy. This information is provided via SAP standard documentation, such as the Roadmaps and the Innovation Discovery. The “Item ID” helps to cross-reference between the original, descriptive CP matrix and the future-oriented “Way Forward” spreadsheet.</p>\n<p>This blog <a href=\"https://blogs.sap.com/2022/08/09/compatibility-scope-what-happens-after-2025-2030/\" target=\"_blank\">https://blogs.sap.com/2022/08/09/compatibility-scope-what-happens-after-2025-2030/</a> provides more details about the procedure for Compatibility Packs after their use right expiry in 2025/2030.</p>\n<p>SAP is planning regular updates of the attachments. All forward-looking information is non-binding and with preview character. ​The released SAP Roadmaps are the proper source of information.</p>\n<p>For questions and feedback about this note and its content, please create a customer message on component XX-SER-REL with the prefix ‘CompScope’​.</p>\n<p>(1) In the exceptional cases of CS, LE-TRA and PP-PI, the usage right to their respective compatibility pack items (cf. matrix) terminates at the end of 2030.</p>", "noteVersion": 84}, {"note": "2224356", "noteTitle": "2224356 - Internet Explorer 11: No maintenance of browser-based PI Sheets and Electronic Work Instructions", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You maintain browser-based PI Sheets or electronic work instructions. You installed the MS Internet Explorer 11 on your local machine. After the installation you start transaction CO60 or CO60XT to maintain your browser-based PI Sheet/electronic work instruction. The content of the PI Sheet/electronic work instruction is not displayed:</p>\n<ul>\n<li>The message '<em>Loading </em>...' appears in the upper left corner of the HTML control.</li>\n<li>If the log is displayed no error messages are displayed. The message is missing that the document was started successfully: In general this is the last message of the log (message 808 of message class CPOC).</li>\n</ul>\n<p>It is also not possible to display archived browser-based PI Sheets and electronic work instructions. This includes:</p>\n<ul>\n<li>data of electronic batch records (transaction COEBR)</li>\n<li>data stored on optical archives (transaction CO_ARCH_PISHEET).</li>\n</ul>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>browser-based PI Sheet, electronic work instruction, IE 11, C<PERSON>_BFW,</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p><a href=\"http://help.sap.com/erp2005_ehp_07/helpdata/en/c9/147a36c70d2354e10000009b38f839/content.htm\" target=\"_blank\">SAP HTML viewer control</a> displays the HTML content of browser-based PI Sheets and electronic work instructions. These objects require SAP GUI for Windows. In this case the MS Internet Explorer is used as external browser. The current HTML files of the browser framework are not compatible with IE 11. The metadata information in the HTML file about the UX compatibility mode is missings. The HTML files of the browser framework requires the emulation of Internet Explorer 9.0 as compatibility mode (content=\"IE=EmulateIE9\").</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Download the ZIP file that contains the adopted HTML files of the browser framework (BFW). Unzip the files.</p>\n<p>Start the SAP Web Repository via transaction SMW0. Choose option '<em>Binary data for WebRFC applications</em>' and press Enter.</p>\n<p>Select package CMX_BFW and press '<em>Execute</em>' (F8). Mark the following object entries and import the corresponding file:</p>\n<ul>\n<li>\n<div>TML_BFW_DOC_GENERIC.HTML</div>\n</li>\n<li>TML_BFW_DOC_CLIENT.HTML</li>\n</ul>\n<p>By selecting the uploaded files and choosing Web object -&gt; Attributes from the menu make sure that the attribute mimetype has the value of \"text/html\".</p>\n<p>Build the runtime files by starting report RCMX_BFW_BUILD_RUNTIME_FILES. Enter the following data:</p>\n<ul>\n<li>Development Class: CMX_BFW</li>\n<li>Name of Original: BFW_DOC_GENERIC.HTML</li>\n</ul>\n<p>Execute the report. Repeat the procedure for BFW_DOC_CLIENT.HTML of development class CMX_BFW.</p>", "noteVersion": 3}]}, {"note": "2889636", "noteTitle": "2889636 - S4TWL - Cross Docking", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p id=\"\"> You are doing a system conversion to SAP S/4HANA, on-premise edition. The following SAP S/4HANA Transition Worklist item is applicable in this case.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p id=\"\">LE-WM, Stock Room Management, LE-WM-DCK</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>The functionality of Cross Docking in Warehouse Management (LE-WM) is not the target architecture anymore within SAP S/4HANA, on-premise edition (Functionality available in SAP S/4HANA on-premise edition 1511 delivery but not considered as future technology. Functional equivalent is available.). The (already available) alternative functionality is Cross Docking in Extended Warehouse Management (SAP EWM).</p>\n<p><strong>Business Process related information</strong></p>\n<p>No immediate influence on business processes expected related to Cross Docking. The functionality of Cross Docking in Warehouse Management (LE-WM) is still available within the SAP S/4HANA architecture stack as part of the compatibility packages. All related functionalities can be used in an unchanged manner for a limited period of time under the conditions of the compatibility packages (see SAP note 2269324 for more information).</p>\n<p><strong>Required and Recommended Action(s)</strong></p>\n<p>As part of your SAP S/4HANA system conversion or upgrade project you need to decide, if you want to continue using Cross Docking in Warehouse Management (LE-WM) for a limited period of time under the conditions of the compatibility packages (see SAP note 2269324 for more information), or if you want to migrate to Extended Warehouse Management (SAP EWM).</p>\n<p>If your decision is to migrate to Extended Warehouse Management (SAP EWM), plan accordingly for this in your project.<br/>Please check as well the Release information and restrictions for EWM in SAP S/4HANA of the S/4HANA Release you will implement.</p>\n<p>Once you migrate to Extended Warehouse Management (SAP EWM), but latest with expiry of the compatibility scope license (see SAP note 2269324 for more information), you need to remove all usages of Cross Docking development objects delivered by SAP from your custom code, as these are no longer supported after expiry of the compatibility scope license. Such usages are shown by the SAP S/4HANA specific custom code checks in ABAP Test Cockpit (see SAP note 2241080).<em></em></p>", "noteVersion": 3, "refer_note": [{"note": "2882809", "noteTitle": "2882809 - Scope Compliance Check for Stock Room Management", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are running warehouses with LE-WM and plan a system conversion to SAP S/4HANA. As described in SAP Note 2577428, LE-WM is in a compatibility mode and mustn’t be used beyond 2025 in SAP S/4HANA. From SAP S/4HANA 1909, Stock Room Management is available for use in place of LE-WM. Some features that are available in LE-WM are not part of Stock Room Management. You therefore need to know if the functionality you are currently using in your system is covered by Stock Room Management or whether you are using functionality beyond its scope.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>STOCKROOM_COMPLIANCE_CHECK, LE-WM, Stock Room Management</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Implement the note and execute the Compliance Check Report in every productive client in your system to check your warehouses. If your system is compliant with the scope of Stock Room Management, you can use your current setup beyond 2025 with Stock Room Management.</p>\n<p>Steps to execute the report:</p>\n<p class=\"StandardParagraph\">Start the ABAP Editor transaction (SE38) and enter STOCKROOM_COMPLIANCE_CHECK in the Program field. Choose Execute.</p>\n<p class=\"StandardParagraph\">Select the warehouse or warehouses you want to check. To check all the warehouse in your system, leave the Warehouse Number field blank.</p>\n<p class=\"StandardParagraph\">Enter the date range you want to check.</p>\n<p class=\"StandardParagraph\">Choose Execute.</p>", "noteVersion": 3}]}, {"note": "2270211", "noteTitle": "2270211 - S4TWL - Warehouse Management (WM)", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are doing a system conversion to SAP S/4HANA, on-premise edition. The following SAP S/4HANA Transition Worklist item is applicable in this case.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p id=\"\">LE-WM, Stock Room Management</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong>Description</strong></p>\n<p>The Warehouse Management (LE-WM) is not the target architecture anymore within SAP S/4HANA, on-premise edition (Functionality available in SAP S/4HANA on-premise edition 1511 delivery but not considered as future technology. Functional equivalent is available.). The (already available) alternative functionality is Extended Warehouse Management (SAP EWM) and since S/4HANA 1909 Stock Room Management.</p>\n<p><strong>Business Process related information</strong></p>\n<p>No influence on business processes expected related to the Warehouse Management. The functionality related to Warehouse Management (LE-WM) and the other components described above is still available within the SAP S/4HANA architecture stack. All related functionalities can be used in an unchanged manner.</p>\n<p><strong>Required and Recommended Action(s)</strong></p>\n<p>The Warehouse Management (LE-WM) and the other components described above is not the target architecture within SAP S/4HANA, on-premise edition. The (already available) alternative functionality is Extended Warehouse Management (SAP EWM) and since S/4HANA 1909 Stock Room Management and a customer could start introducing EWM upfront to the SAP S/4HANA installation.</p>\n<p>If your decision is to migrate to Extended Warehouse Management (SAP EWM), plan accordingly for this in your project.<br/>Please check as well the Release information and restrictions for EWM in SAP S/4HANA of the S/4HANA Release you will implement.</p>\n<p><strong>Remark</strong></p>\n<p>Stock Room Management reuses major parts of LE-WM and can be used beyond 2025. Task &amp; Resource Management (WM-TRM), Warehouse Control Unit interface (WM-LSR), Value Added Service (WM-VAS), Yard Management (WM-YM), Cross-Docking (WM-CD), Wave Management (WM-TFM-CP), and decentral WM (WM-DWM) are not part of Stock Room Management. These LE-WM functionalities are part of the SAP S/4HANA compatibility scope, which comes with limited usage rights. For more details on the compatibility scope and it’s expiry date and links to further information please refer to SAP note 2269324. Refer to ID 476 in the compatibility matrix attached to SAP note 2269324.</p>\n<p class=\"xmsonormal\">There is no technical migration needed to go from LE-WM to Stockroom Management. You can continue using warehouse management but you need to stick to the guard rails of Stockroom Management.</p>", "noteVersion": 5, "refer_note": [{"note": "2889468", "noteTitle": "2889468 - S4TWL - Warehouse Control Interface", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p id=\"\">You are doing a system conversion to SAP S/4HANA, on-premise edition. The following SAP S/4HANA Transition Worklist item is applicable in this case.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>LE-WM, Stock Room Management</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong>Description</strong></p>\n<p>The integration of external systems via IDOCs in Warehouse Management (LE-WM) is not the target architecture anymore within SAP S/4HANA, on-premise edition (Functionality available in SAP S/4HANA on-premise edition 1511 delivery but not considered as future technology. Functional equivalent is available.). The (already available) alternative functionality is Extended Warehouse Management (SAP EWM).</p>\n<p><strong>Business Process related information</strong></p>\n<p>No immediate influence on business processes expected related to the Integration of external systems via IDOCs. The functionality Integration of external systems via IDOCs in Warehouse Management (LE-WM) is still available within the SAP S/4HANA architecture stack as part of the compatibility packages. All related functionalities can be used in an unchanged manner for a limited period of time under the conditions of the compatibility packages (see SAP note 2269324 for more information).</p>\n<p><strong>Required and Recommended Action(s)</strong></p>\n<p>As part of your SAP S/4HANA system conversion or upgrade project you need to decide, if you want to continue using the integration of external systems via IDOCs in Warehouse Management (LE-WM) for a limited period of time under the conditions of the compatibility packages (see SAP note 2269324 for more information), or if you want to migrate to Extended Warehouse Management (SAP EWM).</p>\n<p>The simplification item check and the Compliance Check Report only evaluate the configuration – nothing else. In case you just have an outdated configuration, but are not using the IDOC Integration, it is sufficient to adjust the configuration. Please check and adjust View V_T327B by either flagging the entries as inactive or by removing the unnecessary entries.</p>\n<p>If your decision is to migrate to Extended Warehouse Management (SAP EWM), plan accordingly for this in your project.<br/>Please check as well the Release information and restrictions for EWM in SAP S/4HANA of the S/4HANA Release you will implement.</p>", "noteVersion": 3}, {"note": "2889637", "noteTitle": "2889637 - S4TWL - Yard Management", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p id=\"\">You are doing a system conversion to SAP S/4HANA, on-premise edition. The following SAP S/4HANA Transition Worklist item is applicable in this case.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p id=\"\">LE-WM, Stock Room Management</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>The functionality of Yard Management in Warehouse Management (LE-WM) is not the target architecture anymore within SAP S/4HANA, on-premise edition (Functionality available in SAP S/4HANA on-premise edition 1511 delivery but not considered as future technology. Functional equivalent is available.). The (already available) alternative functionality is Yard Management in Extended Warehouse Management (SAP EWM).</p>\n<p><strong>Business Process related information</strong></p>\n<p>No immediate influence on business processes expected related to Yard Management. The functionality Yard Managemen in Warehouse Management (LE-WM) is still available within the SAP S/4HANA architecture stack as part of the compatibility packages. All related functionalities can be used in an unchanged manner for a limited period of time under the conditions of the compatibility packages (see SAP note 2269324 for more information).</p>\n<p><strong>Required and Recommended Action(s)</strong></p>\n<p>As part of your SAP S/4HANA system conversion or upgrade project you need to decide, if you want to continue using Yard Management in Warehouse Management (LE-WM) for a limited period of time under the conditions of the compatibility packages (see SAP note 2269324 for more information), or if you want to migrate to Extended Warehouse Management (SAP EWM).</p>\n<p>If your decision is to migrate to Extended Warehouse Management (SAP EWM), plan accordingly for this in your project.<br/>Please check as well the Release information and restrictions for EWM in SAP S/4HANA of the S/4HANA Release you will implement.</p>\n<p>Once you migrate to Extended Warehouse Management (SAP EWM), but latest with expiry of the compatibility scope license (see SAP note 2269324 for more information), you need to remove all usages of Yard Management development objects delivered by SAP from your custom code, as these are no longer supported after expiry of the compatibility scope license. Such usages are shown by the SAP S/4HANA specific custom code checks in ABAP Test Cockpit (see SAP note 2241080).<em></em></p>", "noteVersion": 2}, {"note": "2889540", "noteTitle": "2889540 - S4TWL - Decentral WM", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p id=\"\">You are doing a system conversion to SAP S/4HANA, on-premise edition. The following SAP S/4HANA Transition Worklist item is applicable in this case.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p id=\"\">LE-WM, Stock Room Management</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong>Description</strong></p>\n<p>This note is only relevant if you use have dedicated ERP instances for Warehouse Management and others for Enterprise Resource Management. In this setup warehouse logistics with LE-WM is running on an own decentral ERP instance connected to a central ERP instance. The usage of Warehouse Management (LE-WM) as decentralized Warehouse Management is not the target architecture anymore within SAP S/4HANA, on-premise edition. The (already available) alternative functionality is Extended Warehouse Management (SAP EWM). Connecting a decentral Warehouse Management is still target archeitecture within SAP S/4HANA, on-premise edition.</p>\n<p><strong>Business Process related information</strong></p>\n<p>You mustn't migrate the decentralized Warehouse Management to SAP S/4HANA. Instead stay on ECC with the decentralized Warehouse Management (taking current end of maintenance into consideration) and do the system conversion to SAP S/4HANA only for your central instance where you control your business.</p>\n<p><strong>Required and Recommended Action(s)</strong></p>\n<p>As part of your SAP S/4HANA system conversion or upgrade project you have two options, if you currently use Warehouse Management (LE-WM) as a decentralized system.</p>\n<p>You can proceed using LE-WM as decentralized Wareohouse Management System. In this case this decentralized system needs to stay on ERP and mustn't be converted to S/4HANA. The other option is to migrate to Extended Warehouse Management (SAP EWM).</p>\n<p>If your decision is to migrate to Extended Warehouse Management (SAP EWM), plan accordingly for this in your project.<br/>Please check as well the Release information and restrictions for EWM in SAP S/4HANA of the S/4HANA Release you will implement.</p>\n<p>Once you migrate to Extended Warehouse Management (SAP EWM), but latest with expiry of the compatibility scope license (see SAP note 2269324 for more information), you need to remove all usages of Decentral WM development objects delivered by SAP from your custom code, as these are no longer supported after expiry of the compatibility scope license. Such usages are shown by the SAP S/4HANA specific custom code checks in ABAP Test Cockpit (see SAP note 2241080).<em></em></p>", "noteVersion": 2}, {"note": "2889253", "noteTitle": "2889253 - S4TWL - Task & Resource Management", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are doing a system conversion to SAP S/4HANA, on-premise edition. The following SAP S/4HANA Transition Worklist item is applicable in this case.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>LE-WM, Stock Room Management</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong>Description</strong></p>\n<p>Task and Resource Management (LE-TRM) is not the target architecture anymore within SAP S/4HANA, on-premise edition (Functionality available in SAP S/4HANA on-premise edition 1511 delivery but not considered as future technology. Functional equivalent is available.). The (already available) alternative functionality is Extended Warehouse Management (SAP EWM).</p>\n<p><strong>Business Process related information</strong></p>\n<p>No immediate influence on business processes expected related to Task and Resource Management. The functionality Task and Resource Management (LE-TRM) is still available within the SAP S/4HANA architecture stack as part of the compatibility packages. All related functionalities can be used in an unchanged manner for a limited period of time under the conditions of the compatibilty packages (see SAP note 2269324 for more information).</p>\n<p><strong>Required and Recommended Action(s)</strong></p>\n<p>As part of your SAP S/4HANA system conversion or upgrade project you need to decide, if you want to continue using Task and Resource Management (LE-TRM) for a limited period of time under the conditions of the compatibility packages (see SAP note 2269324 for more information), or if you want to migrate to Extended Warehouse Management (SAP EWM).</p>\n<p>If your decision is to migrate to Extended Warehouse Management (SAP EWM), plan accordingly for this in your project.<br/>Please check as well the Release information and restrictions for EWM in SAP S/4HANA of the S/4HANA Release you will implement.</p>\n<p>Once you migrate to Extended Warehouse Management (SAP EWM), but latest with expiry of the compatibility scope license (see SAP note 2269324 for more information), you need to remove all usages of Task &amp; Resource Management development objects delivered by SAP from your custom code, as these are no longer supported after expiry of the compatibility scope license. Such usages are shown by the SAP S/4HANA specific custom code checks in ABAP Test Cockpit (see SAP note 2241080).<em></em></p>\n<p><strong> </strong></p>", "noteVersion": 2}, {"note": "2889652", "noteTitle": "2889652 - S4TWL - Wave Management / Collective Processing", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p id=\"\">You are doing a system conversion to SAP S/4HANA, on-premise edition. The following SAP S/4HANA Transition Worklist item is applicable in this case.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>LE-WM, Stock Room Management</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong>Description</strong></p>\n<p>The functionality of Wave Management / collective Processing in Warehouse Management (LE-WM) is not the target architecture anymore within SAP S/4HANA, on-premise edition (Functionality available in SAP S/4HANA on-premise edition 1511 delivery but not considered as future technology. Functional equivalent is available.). The (already available) alternative functionality is Wave Management in Extended Warehouse Management (SAP EWM).</p>\n<p><strong>Business Process related information</strong></p>\n<p>No immediate influence on business processes expected related to Wave Management / collective Processing. The functionality Wave Management / collective Processing of Warehouse Management (LE-WM) is still available within the SAP S/4HANA architecture stack as part of the compatibility packages. All related functionalities can be used in an unchanged manner for a limited period of time under the conditions of the compatibility packages (see SAP note 2269324 for more information).</p>\n<p><strong>Required and Recommended Action(s)</strong></p>\n<p>As part of your SAP S/4HANA system conversion or upgrade project you need to decide, if you want to continue using Wave Management / collective Processing in Warehouse Management (LE-WM) for a limited period of time under the conditions of the compatibility packages (see SAP note 2269324 for more information), or if you want to migrate to Extended Warehouse Management (SAP EWM).</p>\n<p>If your decision is to migrate to Extended Warehouse Management (SAP EWM), plan accordingly for this in your project.<br/>Please check as well the Release information and restrictions for EWM in SAP S/4HANA of the S/4HANA Release you will implement.</p>\n<p>Once you migrate to Extended Warehouse Management (SAP EWM), but latest with expiry of the compatibility scope license (see SAP note 2269324 for more information), you need to remove all usages of Wave Management development objects delivered by SAP from your custom code, as these are no longer supported after expiry of the compatibility scope license. Such usages are shown by the SAP S/4HANA specific custom code checks in ABAP Test Cockpit (see SAP note 2241080).<em></em></p>", "noteVersion": 3}]}, {"note": "2889638", "noteTitle": "2889638 - S4TWL - Value Added Services", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p id=\"\">You are doing a system conversion to SAP S/4HANA, on-premise edition. The following SAP S/4HANA Transition Worklist item is applicable in this case.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p id=\"\">LE-WM, Stock Room Management</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>The functionality of Value added Services in Warehouse Management (LE-WM) is not the target architecture anymore within SAP S/4HANA, on-premise edition (Functionality available in SAP S/4HANA on-premise edition 1511 delivery but not considered as future technology. Functional equivalent is available.). The (already available) alternative functionality is Value added Services in Extended Warehouse Management (SAP EWM).</p>\n<p><strong>Business Process related information</strong></p>\n<p>No immediate influence on business processes expected related to Value added Services. The functionality Value added Services in Warehouse Management (LE-WM) is still available within the SAP S/4HANA architecture stack as part of the compatibility packages. All related functionalities can be used in an unchanged manner for a limited period of time under the conditions of the compatibility packages (see SAP note 2269324 for more information).</p>\n<p><strong>Required and Recommended Action(s)</strong></p>\n<p>As part of your SAP S/4HANA system conversion or upgrade project you need to decide, if you want to continue using Value added Services in Warehouse Management (LE-WM) for a limited period of time under the conditions of the compatibility packages (see SAP note 2269324 for more information), or if you want to migrate to Extended Warehouse Management (SAP EWM).</p>\n<p>If your decision is to migrate to Extended Warehouse Management (SAP EWM), plan accordingly for this in your project.<br/>Please check as well the Release information and restrictions for EWM in SAP S/4HANA of the S/4HANA Release you will implement.</p>\n<p>Once you migrate to Extended Warehouse Management (SAP EWM), but latest with expiry of the compatibility scope license (see SAP note 2269324 for more information), you need to remove all usages of Value Added Services development objects delivered by SAP from your custom code, as these are no longer supported after expiry of the compatibility scope license. Such usages are shown by the SAP S/4HANA specific custom code checks in ABAP Test Cockpit (see SAP note 2241080).<em></em></p>", "noteVersion": 2}], "activities": [{"Activity": "Others", "Phase": "During or after conversion project", "Condition": "Optional", "Additional_Information": "The functions switched by business function LOG_PP_BATCH_HISTORY will be replaced by global batch traceability in a future release. See also S4TWL - Batch History."}, {"Activity": "Business Decision", "Phase": "Before conversion project", "Condition": "Mandatory", "Additional_Information": "Decide about your strategy to replace obsolete business functions."}, {"Activity": "Fiori Implementation", "Phase": "During or after conversion project", "Condition": "Conditional", "Additional_Information": "If decided, implement Fiori apps as replacement for business functions."}, {"Activity": "Implementation project required", "Phase": "During or after conversion project", "Condition": "Conditional", "Additional_Information": "If decided, implement alternative functions for cross docking and vehicle space optimization available in SAP Transportation Management."}, {"Activity": "User Training", "Phase": "During conversion project", "Condition": "Mandatory", "Additional_Information": "Inform users about changed business processes."}]}