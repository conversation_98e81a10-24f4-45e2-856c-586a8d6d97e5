{"guid": "6CAE8B3E7F1B1ED6A5D42DFF3948E0C2", "sitemId": "SI21: Logistics_General", "sitemTitle": "S4TWL - Article Hierarchy", "note": 2368680, "noteTitle": "2368680 - S4TWL - Article Hierarchy via transactions MATGRP*", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are doing a system conversion to SAP S/4HANA. The following SAP S/4HANA Transition Worklist item is applicable in this case.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong>Description</strong></p>\n<p>The article hierarchy allows grouping of articles, using a customer-oriented structure. This means it is possible to map any hierarchical grouping of articles, in particular the consumer decision tree (CDT). The CDT is oriented around consumers and their needs and, as far as possible, it reflects how, and according to what needs the consumer decides to make a purchase.</p>\n<p><strong>Business Process related information</strong></p>\n<p>In SAP S/4HANA article hierarchy maintenance via transactions MATGRP* is not available anymore.</p>\n<p>Article hierarchies should be maintained via transactions WMATGRP* which are richer in functionality than transactions MATGRP*.</p>\n<p>It is possible to migrate MATGRP Article Hierarchies to WMATGRP Article Hierarchies before doing the SAP S/4HANA system conversion, see referenced note 2536676.</p>\n<p><strong>Required and Recommended Action(s)</strong></p>\n<p>Switch to article hierarchies via transactions WMATGRP*.</p>\n<p>In case you reuse ABAP objects of packetCL_MD in your custom code, please see attached note.</p>\n<p><strong>How to Determine Relevancy</strong></p>\n<p>This Simplification Item is relevant if article hierarchy of this type is used. <br/>This is the case if transactions MATGRP01, MATGRP02, MATGRP03, MATGRP04, MATGRP05, MATGRP06, MATGRP07 are used.</p>\n<p>This also can be checked via transaction SE16N. Enter table MATGRP_HIER and check whether there are any entries.</p>", "noteVersion": 6, "refer_note": [{"note": "2543543", "noteTitle": "2543543 - Restrictions for BW extractors relevant for S/4HANA in the area of Retail", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>Several data sources for BW extraction are no longer available with S/4 in the area of Retail as part of the product version SAP S/4HANA, on-premise edition 1610 and higher.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>S/4HANA, BW, Extractors, IS-Retail</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>See the reasons given below</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>The following DataSources are not supported any longer:</p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"10\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td>DataSource</td>\n<td>Appl.component</td>\n<td>Restriction</td>\n<td>Comment</td>\n<td>Related SI</td>\n</tr>\n<tr>\n<td>0FIPMATNR_ATTR</td>\n<td>LO-RFM-PUR-FIP and MM-PUR-FIP</td>\n<td>Deprecated</td>\n<td>DS not working - no alternative exists</td>\n<td><span><span #0563c1;=\"\" ar-sa;\"=\"\" calibri',sans-serif;=\"\" calibri;=\"\" color:=\"\" de;=\"\" minor-latin;=\"\" mso-ansi-language:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" mso-fareast-theme-font:=\"\"><a href=\"/notes/2368747\" target=\"_blank\">2368747</a></span></span></td>\n</tr>\n<tr>\n<td>0RT_RMA_VAL_TRAN</td>\n<td>LO-RFM-OBS and LO-LIS-DC</td>\n<td>Deprecated</td>\n<td>DS not working - no alternative exists</td>\n<td><span><span #0563c1;=\"\" ar-sa;\"=\"\" calibri',sans-serif;=\"\" calibri;=\"\" color:=\"\" de;=\"\" minor-latin;=\"\" mso-ansi-language:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" mso-fareast-theme-font:=\"\"><a href=\"/notes/2368736\" target=\"_blank\">2368736</a></span></span></td>\n</tr>\n<tr>\n<td>2LIS_40_S202</td>\n<td>LO-RFM-OBS and LO-MAP</td>\n<td>Deprecated</td>\n<td>DS not working - no alternative exists</td>\n<td><span><span #0563c1;=\"\" ar-sa;\"=\"\" calibri',sans-serif;=\"\" calibri;=\"\" color:=\"\" de;=\"\" minor-latin;=\"\" mso-ansi-language:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" mso-fareast-theme-font:=\"\"><a href=\"/notes/2368741\" target=\"_blank\">2368741</a></span></span></td>\n</tr>\n<tr>\n<td>2LIS_40_S207</td>\n<td>LO-RFM-OBS and LO-MAP</td>\n<td>Deprecated</td>\n<td>DS not working - no alternative exists</td>\n<td><span><span #0563c1;=\"\" ar-sa;\"=\"\" calibri',sans-serif;=\"\" calibri;=\"\" color:=\"\" de;=\"\" minor-latin;=\"\" mso-ansi-language:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" mso-fareast-theme-font:=\"\"><a href=\"/notes/2368741\" target=\"_blank\">2368741</a></span></span></td>\n</tr>\n<tr>\n<td>2LIS_40_S208</td>\n<td>LO-RFM-OBS and LO-MAP</td>\n<td>Deprecated</td>\n<td>DS not working - no alternative exists</td>\n<td><span><span #0563c1;=\"\" ar-sa;\"=\"\" calibri',sans-serif;=\"\" calibri;=\"\" color:=\"\" de;=\"\" minor-latin;=\"\" mso-ansi-language:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" mso-fareast-theme-font:=\"\"><a href=\"/notes/2368741\" target=\"_blank\">2368741</a></span></span></td>\n</tr>\n<tr>\n<td>2LIS_40_S212</td>\n<td>LO-RFM-OBS and LO-MAP</td>\n<td>Deprecated</td>\n<td>DS not working - no alternative exists</td>\n<td><span><span #0563c1;=\"\" ar-sa;\"=\"\" calibri',sans-serif;=\"\" calibri;=\"\" color:=\"\" de;=\"\" minor-latin;=\"\" mso-ansi-language:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" mso-fareast-theme-font:=\"\"><a href=\"/notes/2368741\" target=\"_blank\">2368741</a></span></span></td>\n</tr>\n<tr>\n<td>2LIS_40_S214</td>\n<td>LO-RFM-OBS and LO-MAP</td>\n<td>Deprecated</td>\n<td>DS not working - no alternative exists</td>\n<td><span><span #0563c1;=\"\" ar-sa;\"=\"\" calibri',sans-serif;=\"\" calibri;=\"\" color:=\"\" de;=\"\" minor-latin;=\"\" mso-ansi-language:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" mso-fareast-theme-font:=\"\"><a href=\"/notes/2368741\" target=\"_blank\">2368741</a></span></span></td>\n</tr>\n<tr>\n<td>2LIS_40_S219</td>\n<td>LO-RFM-OBS and LO-MAP</td>\n<td>Deprecated</td>\n<td>DS not working - no alternative exists</td>\n<td><span><span #0563c1;=\"\" ar-sa;\"=\"\" calibri',sans-serif;=\"\" calibri;=\"\" color:=\"\" de;=\"\" minor-latin;=\"\" mso-ansi-language:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" mso-fareast-theme-font:=\"\"><a href=\"/notes/2368741\" target=\"_blank\">2368741</a></span></span></td>\n</tr>\n<tr>\n<td>0RT_RMA_MBEW_TRAN</td>\n<td>LO-RFM-OBS and MM-IM</td>\n<td>Deprecated</td>\n<td>DS not working - no alternative exists</td>\n<td><span><span><a href=\"/notes/2368736\" target=\"_blank\">2368736</a></span></span></td>\n</tr>\n<tr>\n<td>0RT_STOREGA_ATTR</td>\n<td>LO-RFM-OBS and LO-MAP</td>\n<td>Deprecated</td>\n<td>DS not working - no alternative exists</td>\n<td><span><span><a href=\"/notes/2368741\" target=\"_blank\">2368741</a></span></span></td>\n</tr>\n<tr>\n<td>0RT_STOREGR_TEXT</td>\n<td>LO-RFM-OBS and LO-MAP</td>\n<td>Deprecated</td>\n<td>DS not working - no alternative exists</td>\n<td><span><span><a href=\"/notes/2368741\" target=\"_blank\">2368741</a></span></span></td>\n</tr>\n<tr>\n<td>0CM_CDT1_ATTR</td>\n<td>LO-RFM-OBS and LO-MD</td>\n<td>Deprecated</td>\n<td>DS not working - alternative exists, 0RF_ARTHIER1_ATTR</td>\n<td><span><span #0563c1;=\"\" ar-sa;\"=\"\" calibri',sans-serif;=\"\" calibri;=\"\" color:=\"\" de;=\"\" minor-latin;=\"\" mso-ansi-language:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" mso-fareast-theme-font:=\"\"><a href=\"/notes/2368680\" target=\"_blank\">2368680</a></span></span></td>\n</tr>\n<tr>\n<td>0CM_CDT1_TEXT</td>\n<td>LO-RFM-OBS and LO-MD</td>\n<td>Deprecated</td>\n<td>DS not working - alternative exists, 0RF_ARTHIER1_TEXT</td>\n<td><span><span #0563c1;=\"\" ar-sa;\"=\"\" calibri',sans-serif;=\"\" calibri;=\"\" color:=\"\" de;=\"\" minor-latin;=\"\" mso-ansi-language:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" mso-fareast-theme-font:=\"\"><a href=\"/notes/2368680\" target=\"_blank\">2368680</a></span></span></td>\n</tr>\n<tr>\n<td>0CM_CDT2_ATTR</td>\n<td>LO-RFM-OBS and LO-MD</td>\n<td>Deprecated</td>\n<td>DS not working - alternative exists, 0RF_ARTHIER2_ATTR</td>\n<td><span><span #0563c1;=\"\" ar-sa;\"=\"\" calibri',sans-serif;=\"\" calibri;=\"\" color:=\"\" de;=\"\" minor-latin;=\"\" mso-ansi-language:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" mso-fareast-theme-font:=\"\"><a href=\"/notes/2368680\" target=\"_blank\">2368680</a></span></span></td>\n</tr>\n<tr>\n<td>0CM_CDT2_TEXT</td>\n<td>LO-RFM-OBS and LO-MD</td>\n<td>Deprecated</td>\n<td>DS not working - alternative exists, 0RF_ARTHIER2_TEXT</td>\n<td><span><span #0563c1;=\"\" ar-sa;\"=\"\" calibri',sans-serif;=\"\" calibri;=\"\" color:=\"\" de;=\"\" minor-latin;=\"\" mso-ansi-language:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" mso-fareast-theme-font:=\"\"><a href=\"/notes/2368680\" target=\"_blank\">2368680</a></span></span></td>\n</tr>\n<tr>\n<td>0CM_CDT3_ATTR</td>\n<td>LO-RFM-OBS and LO-MD</td>\n<td>Deprecated</td>\n<td>DS not working - alternative exists, 0RF_ARTHIER3_ATTR</td>\n<td><span><span #0563c1;=\"\" ar-sa;\"=\"\" calibri',sans-serif;=\"\" calibri;=\"\" color:=\"\" de;=\"\" minor-latin;=\"\" mso-ansi-language:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" mso-fareast-theme-font:=\"\"><a href=\"/notes/2368680\" target=\"_blank\">2368680</a></span></span></td>\n</tr>\n<tr>\n<td>0CM_CDT3_TEXT</td>\n<td>LO-RFM-OBS and LO-MD</td>\n<td>Deprecated</td>\n<td>DS not working - alternative exists, 0RF_ARTHIER3_TEXT</td>\n<td><span><span #0563c1;=\"\" ar-sa;\"=\"\" calibri',sans-serif;=\"\" calibri;=\"\" color:=\"\" de;=\"\" minor-latin;=\"\" mso-ansi-language:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" mso-fareast-theme-font:=\"\"><a href=\"/notes/2368680\" target=\"_blank\">2368680</a></span></span></td>\n</tr>\n<tr>\n<td>0CM_CDT4_ATTR</td>\n<td>LO-RFM-OBS and LO-MD</td>\n<td>Deprecated</td>\n<td>DS not working - alternative exists, 0RF_ARTHIER4_ATTR</td>\n<td><span><span #0563c1;=\"\" ar-sa;\"=\"\" calibri',sans-serif;=\"\" calibri;=\"\" color:=\"\" de;=\"\" minor-latin;=\"\" mso-ansi-language:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" mso-fareast-theme-font:=\"\"><a href=\"/notes/2368680\" target=\"_blank\">2368680</a></span></span></td>\n</tr>\n<tr>\n<td>0CM_CDT4_TEXT</td>\n<td>LO-RFM-OBS and LO-MD</td>\n<td>Deprecated</td>\n<td>DS not working - alternative exists, 0RF_ARTHIER4_TEXT</td>\n<td><span><span #0563c1;=\"\" ar-sa;\"=\"\" calibri',sans-serif;=\"\" calibri;=\"\" color:=\"\" de;=\"\" minor-latin;=\"\" mso-ansi-language:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" mso-fareast-theme-font:=\"\"><a href=\"/notes/2368680\" target=\"_blank\">2368680</a></span></span></td>\n</tr>\n<tr>\n<td>0CM_CDT5_ATTR</td>\n<td>LO-RFM-OBS and LO-MD</td>\n<td>Deprecated</td>\n<td>DS not working - alternative exists, 0RF_ARTHIER5_ATTR</td>\n<td><span><span #0563c1;=\"\" ar-sa;\"=\"\" calibri',sans-serif;=\"\" calibri;=\"\" color:=\"\" de;=\"\" minor-latin;=\"\" mso-ansi-language:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" mso-fareast-theme-font:=\"\"><a href=\"/notes/2368680\" target=\"_blank\">2368680</a></span></span></td>\n</tr>\n<tr>\n<td>0CM_CDT5_TEXT</td>\n<td>LO-RFM-OBS and LO-MD</td>\n<td>Deprecated</td>\n<td>DS not working - alternative exists, 0RF_ARTHIER5_TEXT</td>\n<td><span><span #0563c1;=\"\" ar-sa;\"=\"\" calibri',sans-serif;=\"\" calibri;=\"\" color:=\"\" de;=\"\" minor-latin;=\"\" mso-ansi-language:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" mso-fareast-theme-font:=\"\"><a href=\"/notes/2368680\" target=\"_blank\">2368680</a></span></span></td>\n</tr>\n<tr>\n<td>0CM_CDT6_ATTR</td>\n<td>LO-RFM-OBS and LO-MD</td>\n<td>Deprecated</td>\n<td>DS not working - alternative exists, 0RF_ARTHIER6_ATTR</td>\n<td><span><span #0563c1;=\"\" ar-sa;\"=\"\" calibri',sans-serif;=\"\" calibri;=\"\" color:=\"\" de;=\"\" minor-latin;=\"\" mso-ansi-language:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" mso-fareast-theme-font:=\"\"><a href=\"/notes/2368680\" target=\"_blank\">2368680</a></span></span></td>\n</tr>\n<tr>\n<td>0CM_CDT6_TEXT</td>\n<td>LO-RFM-OBS and LO-MD</td>\n<td>Deprecated</td>\n<td>DS not working - alternative exists, 0RF_ARTHIER6_TEXT</td>\n<td><span><span #0563c1;=\"\" ar-sa;\"=\"\" calibri',sans-serif;=\"\" calibri;=\"\" color:=\"\" de;=\"\" minor-latin;=\"\" mso-ansi-language:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" mso-fareast-theme-font:=\"\"><a href=\"/notes/2368680\" target=\"_blank\">2368680</a></span></span></td>\n</tr>\n<tr>\n<td>0CM_CDT7_ATTR</td>\n<td>LO-RFM-OBS and LO-MD</td>\n<td>Deprecated</td>\n<td>DS not working - alternative exists, 0RF_ARTHIER7_ATTR</td>\n<td><span><span #0563c1;=\"\" ar-sa;\"=\"\" calibri',sans-serif;=\"\" calibri;=\"\" color:=\"\" de;=\"\" minor-latin;=\"\" mso-ansi-language:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" mso-fareast-theme-font:=\"\"><a href=\"/notes/2368680\" target=\"_blank\">2368680</a></span></span></td>\n</tr>\n<tr>\n<td>0CM_CDT7_TEXT</td>\n<td>LO-RFM-OBS and LO-MD</td>\n<td>Deprecated</td>\n<td>DS not working - alternative exists, 0RF_ARTHIER7_TEXT</td>\n<td><span><span #0563c1;=\"\" ar-sa;\"=\"\" calibri',sans-serif;=\"\" calibri;=\"\" color:=\"\" de;=\"\" minor-latin;=\"\" mso-ansi-language:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" mso-fareast-theme-font:=\"\"><a href=\"/notes/2368680\" target=\"_blank\">2368680</a></span></span></td>\n</tr>\n<tr>\n<td>0CM_HIER_ATTR</td>\n<td>LO-RFM-OBS and LO-MD</td>\n<td>Deprecated</td>\n<td>DS not working - alternative exists, 0RF_HIEID_ATTR</td>\n<td><span><span #0563c1;=\"\" ar-sa;\"=\"\" calibri',sans-serif;=\"\" calibri;=\"\" color:=\"\" de;=\"\" minor-latin;=\"\" mso-ansi-language:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" mso-fareast-theme-font:=\"\"><a href=\"/notes/2368680\" target=\"_blank\">2368680</a></span></span></td>\n</tr>\n<tr>\n<td>0CM_HIER_TEXT</td>\n<td>LO-RFM-OBS and LO-MD</td>\n<td>Deprecated</td>\n<td>DS not working - alternative exists, 0RF_HIEID_TEXT</td>\n<td><span><span #0563c1;=\"\" ar-sa;\"=\"\" calibri',sans-serif;=\"\" calibri;=\"\" color:=\"\" de;=\"\" minor-latin;=\"\" mso-ansi-language:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" mso-fareast-theme-font:=\"\"><a href=\"/notes/2368680\" target=\"_blank\">2368680</a></span></span></td>\n</tr>\n<tr>\n<td>0CM_ROLE_TEXT</td>\n<td>LO-RFM-OBS and LO-MD</td>\n<td>Deprecated</td>\n<td>DS not working - no alternative exists</td>\n<td><span><span #0563c1;=\"\" ar-sa;\"=\"\" calibri',sans-serif;=\"\" calibri;=\"\" color:=\"\" de;=\"\" minor-latin;=\"\" mso-ansi-language:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" mso-fareast-theme-font:=\"\"><a href=\"/notes/2368680\" target=\"_blank\">2368680</a></span></span></td>\n</tr>\n<tr>\n<td>0CM_SKU_ATTR</td>\n<td>LO-RFM-OBS and LO-MD</td>\n<td>Deprecated</td>\n<td>DS not working - alternative exists, 0RF_PRODUCT_ATTR</td>\n<td><span><span #0563c1;=\"\" ar-sa;\"=\"\" calibri',sans-serif;=\"\" calibri;=\"\" color:=\"\" de;=\"\" minor-latin;=\"\" mso-ansi-language:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" mso-fareast-theme-font:=\"\"><a href=\"/notes/2368680\" target=\"_blank\">2368680</a></span></span></td>\n</tr>\n<tr>\n<td>0CM_SKU_CDTH_HIER</td>\n<td>LO-RFM-OBS and LO-MD</td>\n<td>Deprecated</td>\n<td>DS not working - no alternative exists</td>\n<td><span><span #0563c1;=\"\" ar-sa;\"=\"\" calibri',sans-serif;=\"\" calibri;=\"\" color:=\"\" de;=\"\" minor-latin;=\"\" mso-ansi-language:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" mso-fareast-theme-font:=\"\"><a href=\"/notes/2368680\" target=\"_blank\">2368680</a></span></span></td>\n</tr>\n<tr>\n<td>0CM_SKU_TEXT</td>\n<td>LO-RFM-OBS and LO-MD</td>\n<td>Deprecated</td>\n<td>DS not working - alternative exists, 0RF_PRODUCT_TEXT</td>\n<td><span><span #0563c1;=\"\" ar-sa;\"=\"\" calibri',sans-serif;=\"\" calibri;=\"\" color:=\"\" de;=\"\" minor-latin;=\"\" mso-ansi-language:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" mso-fareast-theme-font:=\"\"><a href=\"/notes/2368680\" target=\"_blank\">2368680</a></span></span></td>\n</tr>\n<tr>\n<td>0CM_STRAT_TEXT</td>\n<td>LO-RFM-OBS and LO-MD</td>\n<td>Deprecated</td>\n<td>DS not working - no alternative exists</td>\n<td><span><span #0563c1;=\"\" ar-sa;\"=\"\" calibri',sans-serif;=\"\" calibri;=\"\" color:=\"\" de;=\"\" minor-latin;=\"\" mso-ansi-language:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" mso-fareast-theme-font:=\"\"><a href=\"/notes/2368680\" target=\"_blank\">2368680</a></span></span></td>\n</tr>\n<tr>\n<td>2LIS_40_S278</td>\n<td>CA-OIW</td>\n<td>Not Whitelisted</td>\n<td>DS not working - alternative exists, 2LIS_03_BX</td>\n<td><span><span #0563c1;=\"\" ar-sa;\"=\"\" calibri',sans-serif;=\"\" calibri;=\"\" color:=\"\" de;=\"\" minor-latin;=\"\" mso-ansi-language:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" mso-fareast-theme-font:=\"\"><a href=\"/notes/2370131\" target=\"_blank\">2370131</a></span></span></td>\n</tr>\n<tr>\n<td>0RT_SEASON_TEXT</td>\n<td>CA-OIW</td>\n<td>Not Whitelisted</td>\n<td>DS not working - alternative exists, 0RF_SEASON_TEXT</td>\n<td><span><span #0563c1;=\"\" ar-sa;\"=\"\" calibri',sans-serif;=\"\" calibri;=\"\" color:=\"\" de;=\"\" minor-latin;=\"\" mso-ansi-language:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" mso-fareast-theme-font:=\"\"><a href=\"/notes/2481829\" target=\"_blank\">2481829</a></span></span></td>\n</tr>\n<tr>\n<td>0RF_OAPC_MPA</td>\n<td>LO-RFM-OBS and LO-MD-RA</td>\n<td>Not Whitelisted</td>\n<td>DS not working - alternative exists, 0RF_OAPCMPA_ATTR</td>\n<td><span><span #0563c1;=\"\" ar-sa;\"=\"\" calibri',sans-serif;=\"\" calibri;=\"\" color:=\"\" de;=\"\" minor-latin;=\"\" mso-ansi-language:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" mso-fareast-theme-font:=\"\"><a href=\"/notes/2368913\" target=\"_blank\">2368913</a></span></span></td>\n</tr>\n</tbody>\n</table></div>", "noteVersion": 4, "refer_note": [{"note": "2500202", "noteTitle": "2500202 - S4TWL - BW Extractors in SAP S/4HANA", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p dir=\"ltr\">Customers considering moving to SAP S/4HANA (on premise) seek information whether BW extractors (aka data sources) known form SAP ERP still work, to be able to evaluate the potential impact on the BW environment when moving to SAP S/4HANA. To meet this requirement, SAP reviewed the status and the attached list (MS Excel document) provides the information that we have per extractor. The results are valid from SAP S/4HANA 1709 (on premise) until further notice or otherwise and in most cases apply to SAP S/4HANA 1610 and SAP S/4HANA 1511 as well (see respective flags in attached MS Excel document).</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>BW, Extractors, Datasources, SAP S/4HANA</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p dir=\"ltr\">Parts of this information are already available in CSS and can be found through 2333141 - SAP S/4HANA 1610: Restriction Note. The status of the extractors is collected in one list (the attached MS Excel document), see the attached MS Powerpoint document for information how the list is typically used. Not all extractors that are technically available in SAP S/4HANA are covered, SAP is planning to provide clarifications for additional extractors and share new versions of the list via this note. Hence it is recommended to review this note for updates.</p>\n<p>The information in this file is dated as of 10.09.2018. Please acknowledge that S/4HANA functionality is set forth in the S/4HANA Feature Scope Description. All business functionality not set forth therein is not licensed for use by customer.</p>\n<p><em>Please note that extractors in the namespace 0BWTC*, 0TCT* and 8* are related to the \"Embedded BW\" that is offered as a technology component within the S/4HANA software stack. They are all not explicitly whitelisted in this SAP Note as they are not part of the delivered Business Content by the S/4HANA Lines of Business but can be used for extraction. </em><em> </em></p>\n<ul>\n<li><em>0BWTC* and 0TCT* are extractors providing technical statistical information from the Embedded BW such as query runtime statistics or data loading statistics. </em></li>\n<li><em>8* are <a href=\"https://help.sap.com/viewer/64e2cdef95134a2b8870ccfa29cbedc3/7.5.6/en-US/4c1a1b9054914c86e10000000a42189e.html\" target=\"_blank\">Export DataSources</a> for transferring business data from InfoProviders of the “Embedded BW” system to a target system</em></li>\n<ul>\n<li><em>For a SAP BW target systems this is achieved by the <a href=\"https://help.sap.com/viewer/ccc9cdbdc6cd4eceaf1e5485b1bf8f4b/7.5.6/en-US/4a1411c3174f0452e10000000a421937.html\" target=\"_blank\">SAP Source System</a> type </em></li>\n<li><em>For a data transfer via the <a href=\"https://help.sap.com/viewer/107a6e8a38b74ede94c833ca3b7b6f51/2.0.1/en-US/c6afacb707764885a6fb62f511c24f34.html\" target=\"_blank\">ODP Source System</a> type (only option in SAP BW/4HANA target system) these Export DataSources are obsolete and invisible. Instead, the ODP-BW context is used. For more information on Operational Data Provisioning see the <a href=\"https://blogs.sap.com/2017/07/20/operational-data-provisioning-odp-faq/\" target=\"_blank\">ODP FAQ document</a>.</em></li>\n</ul>\n</ul>\n<p><em> For more information and positioning on the \"Embedded BW\" see:</em> <a href=\"https://www.sap.com/documents/2015/06/3ccee34c-577c-0010-82c7-eda71af511fa.html\" target=\"_blank\"><em>https://www.sap.com/documents/2015/06/3ccee34c-577c-0010-82c7-eda71af511fa.html</em></a></p>\n<p> For more information on partner registered data sources and to find partner details from partner namespaces, please create a message on component XX-SER-DNSP.</p>\n<p><strong>This is a collective note, containing information of several industries and areas. In case issues arrise with extractors listed in the Excel, or missing extractors, please do raise an incident on the APPLICATION COMPONENT of the respective EXTRACTOR. This is the <span>only</span> way to ensure that your incident reaches the responsible desk in the shortest time. You can find this in column E, 'Appl.component' in the attached excel document. Do not use the component from this note. Thank you!</strong></p>\n<p> </p>\n<p> </p>\n<p> </p>\n<p> </p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Details can be found in the respective note per area:</p>\n<p> </p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"3\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td><strong>Item Area - Line of Business</strong></td>\n<td><strong>Note number for details</strong></td>\n</tr>\n<tr>\n<td>Asset Management</td>\n<td>\n<p><a href=\"/notes/2299213\" target=\"_blank\">2299213</a> - Restrictions for BW-Extractors in S/4HANA in the Enterprise Asset Management domain (EAM)</p>\n</td>\n</tr>\n<tr>\n<td>Business Process Management</td>\n<td>\n<p><a href=\"/notes/2796696\" target=\"_blank\">2796696</a> - Restrictions for BW extractors relevant for S/4HANA as part of SAP S/4HANA, on-premise edition 1709 and above</p>\n</td>\n</tr>\n<tr>\n<td>Customer Services</td>\n<td>\n<p><a href=\"/notes/2533548\" target=\"_blank\">2533548</a> - Restrictions for BW-Extractors in S/4HANA in the CS (Customer Service) area</p>\n</td>\n</tr>\n<tr>\n<td>Enterprise Portfolio and Project Management</td>\n<td>\n<p><a href=\"/notes/2496759\" target=\"_blank\">2496759</a> - Restrictions for BW extractors relevant for S/4HANA in the area of Enterprise Portfolio and Project Management</p>\n</td>\n</tr>\n<tr>\n<td>Financials</td>\n<td>\n<p><a href=\"/notes/2270133\" target=\"_blank\">2270133</a> - Restrictions for BW extractors relevant for S/4HANA Finance as part of SAP S/4HANA, on-premise edition 1511</p>\n</td>\n</tr>\n<tr>\n<td>Flexible Real Estate</td>\n<td>\n<p><a href=\"/notes/2270550\" target=\"_blank\">2270550</a> - S4TWL - Real Estate Classic</p>\n</td>\n</tr>\n<tr>\n<td>Global Trade</td>\n<td>\n<p><a href=\"/notes/2498786\" target=\"_blank\"> </a></p>\n</td>\n</tr>\n<tr>\n<td>Globalization Services Finance</td>\n<td><a href=\"/notes/2559556\" target=\"_blank\">2559556</a> - Restrictions for BW extractors in Financial Localizations relevant for S/4HANA Finance as part of SAP S/4HANA, on-premise edition 1511</td>\n</tr>\n<tr>\n<td>Human Resources</td>\n<td><a href=\"/notes/2559556\" target=\"_blank\"> </a></td>\n</tr>\n<tr>\n<td>Incident Management and Risk Assessment </td>\n<td> <a href=\"/notes/2267784\" target=\"_blank\">2267784</a> - S4TWL - Simplification in Incident Management and Risk Assessment</td>\n</tr>\n<tr>\n<td>Master Data</td>\n<td>\n<p><a href=\"/notes/2498786\" target=\"_blank\">2498786</a> - Data Sources supported by Central Master Data in S/4HANA</p>\n<p><a href=\"/notes/2576363\" target=\"_blank\" title=\"2576363  - Data Sources supported by Central Master Data in S/4HANA\">2576363</a> - Data Sources supported by Master Data Governance in S/4HANA</p>\n</td>\n</tr>\n<tr>\n<td>Procurement</td>\n<td>\n<p dir=\"ltr\"><a href=\"/notes/2504508\" target=\"_blank\">2504508</a> - Restrictions for BW Extractors relevant for S/4 HANA Procurement as part of SAP S/4HANA, on premise edition 1709 and above</p>\n</td>\n</tr>\n<tr>\n<td>Produce</td>\n<td>\n<p dir=\"ltr\"><a href=\"/notes/2499728\" target=\"_blank\">2499728</a> - Restrictions for BW extractors relevant for S/4HANA in the area of Production Planning and Detailed Scheduling <br/><a href=\"/notes/2499716\" target=\"_blank\">2499716</a> - Restrictions for BW extractors relevant for S/4HANA in the area of Production Planning and Control <br/><a href=\"/notes/2499589\" target=\"_blank\">2499589</a> - Restrictions for BW extractors relevant for S/4HANA in the area of Quality Management<br/><a href=\"/notes/2499310\" target=\"_blank\">2499310</a> - Restrictions for BW extractors relevant for S/4HANA in the area of Inventory Management</p>\n</td>\n</tr>\n<tr>\n<td>Sales and Distribution</td>\n<td><a href=\"/notes/2498211\" target=\"_blank\">2498211</a> - Restrictions for BW extractors relevant for S/4HANA Sales as part of SAP S/4HANA, on-premise edition 1709</td>\n</tr>\n<tr>\n<td>\n<p>Supply Chain – Extended Warehouse Management</p>\n</td>\n<td>\n<p><a href=\"/notes/2552797\" target=\"_blank\">2552797</a> List of BI Data Sources used in EWM<br/><a href=\"/notes/2382662\" target=\"_blank\">2382662</a> List of BI Data Sources from SCM Basis used in EWM Context</p>\n</td>\n</tr>\n<tr>\n<td>\n<p>Supply Chain – Transportation Management</p>\n</td>\n<td>\n<p>All Data Sources working and whitelisted, no separate note exists</p>\n</td>\n</tr>\n<tr>\n<td>\n<p>Master data governance for Finance</p>\n</td>\n<td>\n<p>All Data Sources working and whitelisted, no separate note exists</p>\n</td>\n</tr>\n</tbody>\n</table></div>\n<p> </p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"3\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td>\n<p><strong>Item Area - Industry</strong></p>\n</td>\n<td><strong>Note number for details</strong>                                                                                                                  </td>\n</tr>\n<tr>\n<td>DIMP Automotive</td>\n<td>\n<p><a href=\"https://launchpad.support.sap.com/\" target=\"_blank\"> </a></p>\n</td>\n</tr>\n<tr>\n<td>Defense and Security</td>\n<td>\n<p><a href=\"https://launchpad.support.sap.com/\" target=\"_blank\">2544193</a> - Restrictions for BW extractors relevant for S/4HANA in the area of Defense &amp; Security</p>\n<p><a href=\"/notes/2273294\" target=\"_blank\">2273294</a> - S4TWL - BI content, Datasources and Extractors for DFPS</p>\n</td>\n</tr>\n<tr>\n<td>Financial Services                                        </td>\n<td>\n<p><a href=\"/notes/2543469\" target=\"_blank\">2543469</a> - \"SAP for Banking\": SAP extractors in connection with \"SAP S/4HANA on-premise edition</p>\n<p><span><a href=\"https://i7p.wdf.sap.corp/sap(bD1lbiZjPTAwMQ==)/bc/bsp/sno/ui_entry/entry.htm?param=69765F6D6F64653D3030312669765F7361706E6F7465735F6E756D6265723D3235343631303226\" target=\"_blank\">2546102</a></span> - \"SAP for Insurance\": SAP extractors in connection with \"SAP S/4HANA on-premise edition“</p>\n</td>\n</tr>\n<tr>\n<td>Higher Education</td>\n<td>\n<p><a href=\"/notes/2543469\" target=\"_blank\"> </a></p>\n</td>\n</tr>\n<tr>\n<td>IS Healthcare</td>\n<td>-</td>\n</tr>\n<tr>\n<td>IS Utilities</td>\n<td>\n<p><a href=\"/notes/2270505\" target=\"_blank\">2270505</a> - S4TWL - IS-U Stock, Transaction and Master Data Statistics (UIS, BW)</p>\n</td>\n</tr>\n<tr>\n<td>Oil and Gas</td>\n<td>\n<p> </p>\n</td>\n</tr>\n<tr>\n<td>Public Sector Collection and Disbursement (PSCD)</td>\n<td>\n<p>All Data Sources working and whitelisted, no separate note exists</p>\n</td>\n</tr>\n<tr>\n<td>Public Sector Management (PSM)</td>\n<td><a href=\"/notes/2556359\" target=\"_blank\">2556359</a> - Restrictions for BW extractors relevant for S/4HANA in the area of Public Sector Management</td>\n</tr>\n<tr>\n<td>IS Retail</td>\n<td><a href=\"/notes/2543543\" target=\"_blank\">2543543</a> - Restrictions for BW extractors relevant for S/4HANA in the area of Retail</td>\n</tr>\n</tbody>\n</table></div>\n<p> </p>\n<p> The classification scheme is:</p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"4\" cellspacing=\"1\" class=\"table table-bordered table-striped col-resizeable\" dir=\"ltr\">\n<tbody>\n<tr>\n<td height=\"32\" valign=\"top\" width=\"27%\">\n<p dir=\"ltr\"><strong>Current status                             </strong></p>\n</td>\n<td height=\"32\" valign=\"top\" width=\"27%\">\n<p dir=\"ltr\"><strong>Status for Publication</strong></p>\n</td>\n<td height=\"32\" valign=\"top\" width=\"45%\">\n<p dir=\"ltr\"><strong>Description                                                             </strong></p>\n</td>\n</tr>\n<tr>\n<td height=\"17\" rowspan=\"2\" valign=\"top\" width=\"27%\">\n<p dir=\"ltr\">Whitelisted</p>\n</td>\n<td height=\"17\" valign=\"top\" width=\"27%\">\n<p dir=\"ltr\">DS working - no restrictions</p>\n</td>\n<td height=\"17\" valign=\"top\" width=\"45%\">\n<p dir=\"ltr\">DataSource is available with S/4 and works without any restrictions compared to ERP</p>\n</td>\n</tr>\n<tr>\n<td height=\"17\" valign=\"top\" width=\"27%\">\n<p dir=\"ltr\">DS working – regeneration of extractor and check of BW content based on this DS is needed                                                                                          .</p>\n</td>\n<td height=\"17\" valign=\"top\" width=\"45%\">\n<p dir=\"ltr\">DataSource is working, because of data model changes, it is recommended to check the upward dataflow.</p>\n</td>\n</tr>\n<tr>\n<td height=\"17\" rowspan=\"3\" valign=\"top\" width=\"27%\">\n<p dir=\"ltr\">Not Whitelisted</p>\n</td>\n<td height=\"17\" valign=\"top\" width=\"27%\">\n<p dir=\"ltr\">DS working - restrictions</p>\n</td>\n<td height=\"17\" valign=\"top\" width=\"45%\">\n<p dir=\"ltr\">DataSource is available with S/4 but works with noteworthy restrictions; e.g. not all fields are available</p>\n</td>\n</tr>\n<tr>\n<td height=\"17\" valign=\"top\" width=\"27%\">\n<p dir=\"ltr\">DS not working - alternative exists</p>\n</td>\n<td height=\"17\" valign=\"top\" width=\"45%\">\n<p dir=\"ltr\">DataSource is not working with S/4 but an alternative exists, such as a new extractor, CDS view</p>\n</td>\n</tr>\n<tr>\n<td height=\"17\" valign=\"top\" width=\"27%\">\n<p dir=\"ltr\">DS not working - alternative planned</p>\n</td>\n<td height=\"17\" valign=\"top\" width=\"45%\">\n<p dir=\"ltr\">DataSource is not working with S/4 but equivalent available on roadmap for future release</p>\n</td>\n</tr>\n<tr>\n<td height=\"17\" rowspan=\"2\" valign=\"top\" width=\"27%\">\n<p dir=\"ltr\">Deprecated</p>\n</td>\n<td height=\"17\" valign=\"top\" width=\"27%\">\n<p dir=\"ltr\">DS obsolete</p>\n</td>\n<td height=\"17\" valign=\"top\" width=\"45%\">\n<p dir=\"ltr\">DataSource is obsolete - legacy extractors</p>\n</td>\n</tr>\n<tr>\n<td height=\"18\" valign=\"top\" width=\"27%\">\n<p dir=\"ltr\">DS not working - no alternative exists</p>\n</td>\n<td height=\"18\" valign=\"top\" width=\"45%\">\n<p dir=\"ltr\">DataSource is not working with S/4 but no alternative exists</p>\n</td>\n</tr>\n<tr>\n<td height=\"18\" valign=\"top\" width=\"27%\">\n<p dir=\"ltr\">Generated Data Source</p>\n</td>\n<td height=\"18\" valign=\"top\" width=\"27%\">\n<p dir=\"ltr\">DS working - no restrictions</p>\n</td>\n<td height=\"18\" valign=\"top\" width=\"45%\">\n<p dir=\"ltr\">Because of the nature of the extractors, being generated in the system, we cannot whitelist those in general. Experience so far showed that they should be working without restrictions.</p>\n</td>\n</tr>\n<tr>\n<td height=\"18\" valign=\"top\" width=\"27%\">\n<p dir=\"ltr\">Not relevant for BW extraction</p>\n</td>\n<td height=\"18\" valign=\"top\" width=\"27%\">\n<p dir=\"ltr\">DS not relevant</p>\n</td>\n<td height=\"18\" valign=\"top\" width=\"45%\">\n<p dir=\"ltr\">Datasource is available in ROOSOURCE, however, not to be used for extraction by BW.</p>\n</td>\n</tr>\n</tbody>\n</table></div>", "noteVersion": 41}]}, {"note": "2536676", "noteTitle": "2536676 - Article Hierarchy: Migration Report", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>This note is relevant for the SAP Retail business context.</p>\n<p>Customers using the old article hierarchy (transactions MATGRPxx) under SAP Business Suite are not able to continue to use it after upgrade to SAP S/4 HANA.</p>\n<p>Under SAP S/4HANA, only the new article hierarchy (transactions WMATGRPxx) is available.</p>\n<p>Under SAP Business Suite - before migration to  SAP S/4HANA - there is a need to migrate old to new hierarchy data, which is provided by this note.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>MATGRP_HIER, WRF_MATGRP_HIER</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>As a prerequisite note 2533957 needs to be implemented.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>After implementing note 2533957 the following steps need to be performed:</p>\n<p>1. Apply the attached correction instruction.</p>\n<p>2. Execute report note_2536676_1 which was created by step 1.</p>\n<p>After this, the new transaction WMATGRP13 is available for the migration of old to new article hierarchies.</p>\n<p>The migration functionality has the following characteristics:</p>\n<ol>\n<li>Usage of the standard BAPIs  BAPI_WRF_MATGRP_CREATE and  BAPI_WRF_MATGRP_CHANGE.</li>\n<li>Usage of the BAdIs BADI_MATGRP_CREATE and BADI_MATGRP_CHANGE; note 2533957 contains additional interface methods to support the migration of customer extension fields.</li>\n<li>Usage of the Business Application Log.</li>\n<li>Migration of article hierarchy customizing as far as possible.</li>\n<li>Deletion of old hierarchies optionally possible.</li>\n<li>Scalability of the data valume on the level of hierarchy header as well as on the level of article-to-node assignments.</li>\n<li>The selected total data volume of article-to-node assignments to be migrated is processed in single packages. The number of items per package can be set in the field \"Number of Articles\" in block \"Processing Parameter\" of the selection screen.</li>\n</ol>\n<p>Please refer also to the report documentation.</p>\n<p>Migration of customer extension fields:</p>\n<ol>\n<li>Customer fields used in the old hierarchy are automatically determined for database tables MATGRP_STRUC and MATGRP_SKU.</li>\n<li>The contents of these fields are mapped sequentially in the EXTENSION fields of BAPIs BAPI_WRF_MATGRP_CREATE and  BAPI_WRF_MATGRP_CHANGE, which are of structure type BAPIPAREX. The component STRUCTURE of BAPIPAREX contains the table name (either MATGRP_STRUC or MATGRP_SKU). The components VALUEPART1 to VALUEPART4 (with length 940 characters) contain the concatenated values of the table entry as &lt;key fields&gt; + &lt;customer fields&gt;.</li>\n<li>In the BAdIs BADI_MATGRP_CREATE and BADI_MATGRP_CHANGE, which are called by the corresponding BAPIs, the EXTENSION fields need to be read, the target data identified by the key fields and the customer field values mapped to the appropriate fields in the new hierarchy target structures (either WRF_MATGRP_STRUC or WRF_MATGRP_SKU). This needs to be done by a customer owned BAdI implementation.</li>\n<li>You can use the public method CL_WRF_ARTICLE_HIER_MIGRATION-&gt;GET_TYPES_FOR_EXTENSION() in your implementation to get the appropriate data types for interpreting either node as well as sku extension data.</li>\n<li>It is a prerequisite that the customer fields are available in the new hierarchy database tables as they are in the old hierarchy database tables.  </li>\n</ol>", "noteVersion": 10, "refer_note": [{"note": "2086815", "noteTitle": "2086815 - Ungenerated constructors of exception classes are not transported correctly", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>The indicator for flagging a constructor of an exception class as generated or not generated is not taken into account for the transport of the method implementation. As a result, this information is lost in the receiving system.</p>\n<p>Furthermore, you cannot transmit this property to target systems using the Note Assistant or Correction Workbench.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Class Builder, exception class, exception classes, CL_OO_EXCEPTION_CLASS, ##ADT_SUPPRESS_GENERATION, transport, SNOTE, SCWB</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>You edit an exception class with ABAP Development Tools for SAP NetWeaver (ADT) and have edited the generated constructor manually.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Import the support package or implement this SAP Note.</p>\n<p>We have introduced the new pragma ##ADT_SUPPRESS_GENERATION for flagging an ungenerated constructor in exception classes that should NOT be overwritten through the editing of the class in the back end with the form-based Class Builder. This pragma is automatically appended to the METHOD statement of the constructor when the save operation takes place in ADT and is thus taken into account when the source code is exported. The new pragma also corrects the behavior of the Note Assistant and Correction Workbench.</p>", "noteVersion": 2}]}, {"note": "2383533", "noteTitle": "2383533 - S4TWL - Retail Deprecated Applications Relevance for Custom Code", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are doing a system conversion to SAP S/4HANA, and you are using some Retail related business applications. The following SAP S/4HANA Transition Worklist item is applicable in this case.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong>Description</strong></p>\n<p>In SAP S/4HANA Retail for merchandise management some applications and the respective development objects are not available anymore. <br/>This might be relevant for customer specific coding. If customer specific coding re-used those development objects in ERP, the coding needs to be adjusted accordingly.</p>\n<p><strong>Required and Recommended Action(s)</strong></p>\n<p>Adjust customer specific coding accordingly.</p>", "noteVersion": 2}], "activities": [{"Activity": "Data migration", "Phase": "During conversion project", "Condition": "Mandatory", "Additional_Information": "The steps outlines in SAP Note 2536676 will have to be followed to migrate MATGRP Article Hierarchies to WMATGRP Article Hierarchies before doing the SAP S/4HANA system conversion"}, {"Activity": "User Training", "Phase": "During conversion project", "Condition": "Mandatory", "Additional_Information": "Users must be trained to maintain Article hierarchies via transactions WMATGRP* which are richer in functionality than transactions MATGRP*."}, {"Activity": "Custom Code Adaption", "Phase": "During conversion project", "Condition": "Conditional", "Additional_Information": "In case customer is reusing ABAP objects of packet CL_MD in custom code, custom code adaption will be requried."}]}