{"guid": "0090FABF32DE1ED690DA7ECD4149C0CA", "sitemId": "SI9: Utilities_Localization for CEE", "sitemTitle": "S4TWL - Central and Eastern European localization for Utility & Telco", "note": 2338097, "noteTitle": "2338097 - S4TWL - Utility & Telco related Business Functions", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are doing a system conversion to SAP S/4HANA, on-premise edition. The following SAP S/4HANA Transition Worklist item is applicable in this case.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>S4TWL, S4TC, CEEISUT, Pre-Check Class, SAP IS-UT CEE</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong>Description</strong></p>\n<p>As of SAP S/4HANA 1610 (components FI-CA 801 and IS-UT 801) the localization Add-On CEEISUT for Telco and Utilities industries is retrofitted into SAP S/4HANA - see the release strategy SAP Note 1014997. Appends of DDIC objects enhancing core structures and tables shipped with the CEEISUT have become switchable. Therefore it is necessary to have these <strong>Switches </strong>switched on (in active state) before upgrade to S/4HANA in order to avoid any data loss.</p>\n<p>A customer using CEEISUT need to ensure that a dedicated <strong>Business Function Set</strong> is active before he converts his system to SAP S/4HANA - either UTILITIES or TELCO.</p>\n<p>The related pre-check (see the SAP Note 2314696) raises an error when CEEISUT Add-On is installed and none of the following <strong>Business Function Sets</strong> is in active state or any its underlying object (<strong>Business Function</strong>, <strong>Switch</strong>, <strong>Package</strong>) is not present in an active version in a system or any of <strong>Business Functions</strong> or <strong>Switches</strong> is not in active state. See the hierarchy of both <strong>Business Function Sets</strong> below.</p>\n<p><strong>Business Function Set 'UTILITIES':</strong></p>\n<ul>\n<li>Business Function /SAPCE/ISU_LOC_CEE</li>\n<ul>\n<li>Switch /SAPCE/ISU_LOC_CEE_SFWS_02 </li>\n<ul>\n<li>Switch Package /SAPCE/IU_LOC_CEE_SFWS_02 </li>\n</ul>\n</ul>\n<ul>\n<li>Switch /SAPCE/FICA_LOC_CEE_SFWS_02</li>\n<ul>\n<li>Switch Package SAPCE/FK_LOC_CEE_SFWS_02</li>\n</ul>\n</ul>\n<li>Business Function ISU_UTIL_WASTE</li>\n<ul>\n<li>Switch /SAPCE/ISU_LOC_CEE_SFWS_01</li>\n<ul>\n<li>Switch Package /SAPCE/IU_LOC_CEE_SFWS_01</li>\n</ul>\n<li>Switch /SAPCE/FICA_LOC_CEE_SFWS_01</li>\n<ul>\n<li>Switch Package /SAPCE/FK_LOC_CEE_SFWS_01</li>\n</ul>\n</ul>\n</ul>\n<p><strong><strong>Business Function Set 'TELCO'</strong>:</strong></p>\n<ul>\n<li>Business Function /SAPCE/TEL_LOC_CEE</li>\n<ul>\n<li> Switch /SAPCE/FICA_LOC_CEE_SFWS_02</li>\n<ul>\n<li>Switch Package /SAPCE/FK_LOC_CEE_SFWS_02</li>\n</ul>\n</ul>\n<li>Business Function /SAPCE/TEL_LOC_CEE_HIDDEN</li>\n<ul>\n<li>Switch /SAPCE/ISU_LOC_CEE_SFWS_01</li>\n<ul>\n<li>Switch Package /SAPCE/IU_LOC_CEE_SFWS_01</li>\n</ul>\n</ul>\n<li>Business Function RM_CA</li>\n<li></li>\n<ul>\n<li>Switch /SAPCE/FICA_LOC_CEE_SFWS_01</li>\n<ul>\n<li>Switch Package /SAPCE/FK_LOC_CEE_SFWS_01</li>\n</ul>\n</ul>\n</ul>\n<p><strong>Business Process related information</strong></p>\n<p>No influence on business processes and custom code.</p>\n<p><strong>Required and Recommended Action(s)</strong></p>\n<p>Execute related pre-check (see the SAP Note 2314696) on SAP Business Suite start-release.</p>", "noteVersion": 4, "refer_note": [{"note": "2323221", "noteTitle": "2323221 - CEEISUT Retrofit: Business Functions", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>The SAP IS-UT CEE add-on (sw. component CEEISUT) has been integrated to S4H On Premise Edition as of version S4H OP 1610 (sw. component versions FI-CA 801 / IS-UT 801). New switches and Business Functions have been provided to make more objects such as Appends, Area Menues, IMG nodes etc. switchable with the corresponding Industry. Having the CEEISUT add-on applied the new BFs need to be implemented and activated before migration to S4H in order not lose any data from the appended tables.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>CEEISUT, Pre-Check, S4TC</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>The SAP IS-UT CEE add-on (sw. component CEEISUT) is implemented. You want to migrate to S4H.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Implement manual correction instructions of this note or apply the corresponding CEEISUT add-on Support Package.</p>", "noteVersion": 1, "refer_note": [{"note": "2399707", "noteTitle": "2399707 - Simplification Item Check", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>As part of your system’s SAP Readiness Check assessment, you are interested in performing a simplification item relevance check, compatibility scope relevancy check, or a simplification item consistency check. Your intent in conducting this analysis is to support the scoping and planning of either the conversion from SAP ERP to SAP S/4HANA or the upgrade from one SAP S/4HANA product version to another SAP S/4HANA product version.</p>\n<p>Alternatively, you are interested in manually performing a simplification item relevancy or consistency check outside SAP Readiness Check.</p>\n<p>To learn more about how to perform simplification item checks and the relation to SAP Readiness Check, you can explore the following links:</p>\n<p><a href=\"https://blogs.sap.com/2018/03/26/sap-s4hana-simplification-item-check-how-to-do-it-right./\" target=\"_blank\">https://blogs.sap.com/2018/03/26/sap-s4hana-simplification-item-check-how-to-do-it-right./</a></p>\n<p><a href=\"https://blogs.sap.com/2020/01/02/simplification-item-catalog-simplification-item-check-and-sap-readiness-check-for-sap-s-4hana/\" target=\"_blank\">https://blogs.sap.com/2020/01/02/simplification-item-catalog-simplification-item-check-and-sap-readiness-check-for-sap-s-4hana/</a></p>\n<p><strong>We strongly recommend analyzing a production system; otherwise, the results might be incomplete or misleading. </strong>If you choose to use another environment, for instance, a copy of a production system, implement and follow SAP Note <a href=\"/notes/2568736\" target=\"_blank\">2568736</a> (in both the productive and non-production systems) to capture and upload the necessary ST03N data.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>SAP Readiness Check, SAP S/4HANA Conversion; SAP S/4HANA Upgrade; Simplification Item, Simplification List, Compatibility Scope</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>Before implementing this SAP Note (2399707), we strongly recommend implementing the latest version of SAP Note <a href=\"/notes/1668882\" target=\"_blank\">1668882</a>.</p>\n<p>In addition, before executing the simplification item analysis in your system, we strongly recommend implementing SAP Note <a href=\"/notes/2502552\" target=\"_blank\">2502552</a>. This note delivers check classes used to refine the list of relevant simplification items.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong>Contents<br/></strong>  1.  Introduction<br/>  2.  Enabling the Simplification Item Checks<br/>       2.1. Note Implementation<br/>       2.2. Simplification Item Catalog Maintenance<br/>  3.  Executing the Simplification Item Checks<br/>       3.1. Using the Data Collection Framework for SAP Readiness Check<br/>       3.2. Executing the Checks Manually<br/>  4.  Reviewing the Results in /SDF/RC_START_CHECK<br/>       4.1. Interpreting the Check Results<br/>       4.2. Administrative Functions within the Check Results View<br/>  5.  Additional Information<br/>  6.  Frequently Asked Questions</p>\n<p><strong>  1. Introduction</strong></p>\n<p>While scoping and planning the conversion of your SAP ERP system to SAP S/4HANA, or the upgrade of an SAP S/4HANA system, we recommend analyzing the impact on your system based on the list of documented simplification items. This note provides the simplification item check capabilities to analyze your system. There are two types of checks integrated into this solution:</p>\n<ul>\n<li><strong>Relevance Check:</strong> Produces a customized list of relevant simplification items for your system. The relevance is determined based on rules maintained in the <a href=\"https://launchpad.support.sap.com/#sic\" target=\"_blank\">simplification item catalog</a>. </li>\n</ul>\n<ul>\n<ul>\n<li>If the rule is not maintained or cannot be processed, the item has the status <em>Relevance to Be Checked</em>.</li>\n<li>For simplification items where the rule evaluates database table content, the analysis evaluates table entries across all clients within the system.</li>\n</ul>\n</ul>\n<p>The results of the relevance check are presented in the <em>Simplification Items</em> tile and the <em>Compatibility Scope Analysis</em> tile within SAP Readiness Check for SAP S/4HANA and SAP Readiness Check for SAP S/4HANA upgrades.</p>\n<ul>\n<li><strong>Consistency Check:</strong> When initiated in client 000, this check analyzes the consistency of the system in preparation for the conversion or upgrade using Software Update Manager. The consistency check only evaluates those simplification items marked as relevant or potentially relevant to the system.</li>\n</ul>\n<p>We recommend resolving all identified inconsistencies before the downtime in Software Update Manager. Otherwise, when Software Update Manager encounters unresolved consistency issues, it will need to be reset or restarted. The check also warns you about critical changes during the conversion or upgrade, for example, potential data loss. To continue, you must confirm that you have understood the warning.</p>\n<p>Individual check classes, developed by the associated application area, are leveraged during the consistency check evaluation. The implementation of SAP Note <a href=\"/notes/2502552\" target=\"_blank\">2502552</a> delivers these classes to your system.</p>\n<p>To initiate the consistency check, you can either activate the option within the SAP Readiness Check selection screen (report RC_COLLECT_ANALYSIS_DATA) or use the simplification item check report (/SDF/RC_START_CHECK).</p>\n<p>The results of the consistency check, when initiated by SAP Readiness Check (report RC_COLLECT_ANALYSIS_DATA) in client 000, can be uploaded to an existing analysis for the productive client. The results are then visible within the detailed view of the <em>Simplification Items</em> tile.</p>\n<p>Detailed results are available in the simplification item check report (/SDF/RC_START_CHECK) by selecting <em>Display Last Check Result</em>. The results are only visible in the client used to perform the analysis (for instance, client 000).</p>\n<p><strong>Note</strong>: The check results include references to SAP Notes that describe how to resolve identified issues.</p>\n<p>To identify and resolve possible issues within time, you must <strong>correctly implement and run the simplification item checks before</strong> starting the technical conversion or upgrade. To allocate sufficient time and resources to resolve any potential issues, we recommend initially performing the analysis as part of the scoping and planning phase of the project within SAP Readiness Check. The check is performed one additional time by the Software Update Manager toolset shortly before the technical downtime of the conversion or upgrade of a system. But do not wait to implement and execute the check just before starting Software Update Manager; otherwise, there is a potential risk to the project timeline.</p>\n<p><strong>  2. Enabling the Simplification Item Checks</strong></p>\n<p>2.1. Note Implementation</p>\n<p>Depending on the target software level of your system conversion or release upgrade, specific <strong>minimum versions</strong> of this SAP Note (2399707) and SAP Note <a href=\"/notes/2502552\" target=\"_blank\">2502552</a> are required. Otherwise, the check may be incomplete or inaccurate.</p>\n<p>The minimum versions are (If the Support Package Stack is not specifically listed, then its version requirement defaults to match that of the closest Support Package Stack.):</p>\n<ul>\n<ul>\n<ul>\n<li>SAP S/4HANA 2021 Initial Shipment</li>\n<ul>\n<li>2399707: Minimum recommended version 147 (Minimum technical version 82)</li>\n<li>2502552: version 97</li>\n</ul>\n<li>SAP S/4HANA 2022 Initial Shipment</li>\n<ul>\n<li>2399707: Minimum recommended version 154 (Minimum technical version 82)</li>\n<li>2502552: version 97</li>\n</ul>\n<li>SAP S/4HANA 2022 Feature Package 1</li>\n<ul>\n<li>2399707: Minimum recommended version 155 (Minimum technical version 82)</li>\n<li>2502552: version 97</li>\n</ul>\n<li>SAP S/4HANA 2023 Initial Shipment</li>\n<ul>\n<li>2399707: Minimum recommended version 161 (Minimum technical version 82)</li>\n<li>2502552: version 105</li>\n</ul>\n</ul>\n</ul>\n</ul>\n<p>While the minimum technical version of SAP Note 2399707 enables the conversion process to proceed, the minimum version may not reveal all application-level issues. As a result, we advise using at least the recommended version referenced above.</p>\n<p>Regardless of the minimum note version, when starting your project, it is recommended to <strong>use the most recent version of SAP Notes 2399707 and 2502552</strong>. When you reach the hard-freeze phase in your project, you should also freeze the version of these notes.</p>\n<p>2.2. Simplification Item Catalog Maintenance</p>\n<p>The simplification item catalog contains a list of generally available target product versions and documented simplification items. A local replica is imported into the system to support the execution of the simplification item checks.</p>\n<p>The initial implementation of the check report (/SDF/RC_START_CHECK) includes the most recent version of the simplification item catalog. While continuing to scope and plan the project, we recommend using an up-to-date version of the simplification item catalog. Since conversion projects can take some time, you should consider and plan catalog updates during the project.</p>\n<p>While we advise you to use the most recent simplification item catalog content in early project phases, you should freeze and synchronize this content as part of the hard-freeze phase in your project. As you prepare to enter the hard-freeze phase of your project, <strong>download the local replica of the simplification item catalog </strong>from the system where you plan to perform your final test conversion. You can download the catalog using the corresponding button on the /SDF/RC_START_CHECK selection screen. We recommend downloading the content before converting the system. Similarly, you can then upload this content using report /SDF/RC_START_CHECK to synchronize the version before performing checks in any subsequent systems.</p>\n<p>2.2.1. Updating the Simplification Item Catalog</p>\n<p>The SAP hosted version of the simplification item catalog is updated periodically to accommodate new product versions, introduce new simplification items, and integrate lessons learned from project experience. By default, the report /SDF/RC_START_CHECK does not automatically update the local replica of the simplification item catalog from SAP servers. If you want to update the content from the SAP servers, you explicitly need to trigger this via the <em>Update catalog with latest version from SAP</em> button in /SDF/RC_START_CHECK.</p>\n<p>If no connection exists between the system and the SAP support backbone, you could download the content as an archive directly from the <a href=\"https://me.sap.com/sic\" target=\"_blank\">simplification item catalog</a> site. Once you download the archive to your local machine, you can manually upload it in /SDF/RC_START_CHECK. The <em>Local Version</em> will be marked with a watch icon when the local replica is over 30 days old.</p>\n<p>The available functions for managing the local replica in /SDF/RC_START_CHECK are:</p>\n<ul>\n<ul>\n<ul>\n<li><em>Update Catalog with latest version from SAP: </em>Used to directly download the latest catalog version from SAP to update the local replica.</li>\n<li><em>Upload Simplification Item Catalog from file</em>: Used to upload a version of the catalog stored on your local machine. This option supports environments without direct connectivity to the SAP support backbone. Additionally, it supports synchronizing the catalog version across systems. </li>\n<li><em>Download Current Simplification Item Catalog</em>: Used to download a copy of the local replica version to your local machine as a backup before updating the catalog. It also supports synchronizing the catalog version across systems.</li>\n</ul>\n</ul>\n</ul>\n<p><strong>  3. Executing the Simplification Item Checks<br/></strong></p>\n<p>As stated previously, the simplification item checks (that is, the relevance check and the consistency check) are available via both the data collection framework for SAP Readiness Check (using report RC_COLLECT_ANALYSIS_DATA) and manually (using report /SDF/RC_START_CHECK).</p>\n<p>When running the simplification item checks close to the technical execution of the conversion from SAP ERP to SAP S/4HANA or the upgrade to a higher SAP S/4HANA product version, it is essential to follow the guidance below. <strong>Otherwise</strong>, the technical conversion or upgrade could end with an error, and you may have to <strong>reset and</strong> <strong>repeat the Software Update Manager procedure.</strong> If forced to reset Software Update Manager, update this SAP Note (2399707) and SAP Note <a href=\"/notes/2502552\" target=\"_blank\">2502552</a>, repeat the analysis, and resolve any new issues found before restarting the Software Update Manager procedure.</p>\n<p>3.1. Using the Data Collection Framework for SAP Readiness Check</p>\n<p>To enable the data collection framework to analyze the simplification item checks, enable the related options within the selection screen of report RC_COLLECT_ANALYSIS_DATA. The data collection framework includes the following options related to simplification items:</p>\n<ul>\n<ul>\n<li><em>Simplification Item and Compatibility Scope relevance</em>: By default, this check is active. We recommend performing this analysis on each of the system’s productive clients. Note that each client requires a separate analysis on the landing page for SAP Readiness Check.</li>\n<li><em>Simplification Item Effort Drivers</em>: This check is not enabled by default as it takes some additional time to collect the related information. However, we recommend enabling this analysis for clarity on the effort required to remediate simplification items that are ranked<em> potentially high</em>. Additionally, by analyzing the effort drivers, the default effort ranking for some <em>potentially high</em> simplification items may be reclassified, further supporting the project’s scoping and planning.</li>\n<li><em>Simplification Item Consistency</em>: This check should be performed within client 000, as this is the same client where Software Update Manager will analyze the system. Log on to client 000 and clear all other options within RC_COLLECT_ANALYSIS_DATA to enable this check. The relevance check will be automatically added to the scope when enabling the consistency check. You can then extend the analysis of the production client by updating it with the archive produced by the consistency check in client 000. Select the <em>Update Analysis</em> button on the dashboard view to add the archive.</li>\n</ul>\n</ul>\n<p>3.2. Executing the Checks Manually</p>\n<p>These are the steps required to execute the simplification item checks manually:</p>\n<ol><ol>\n<li>Start report /SDF/RC_START_CHECK in transaction SA38.</li>\n<li>In the <em>Simplification Item Check Options</em> section, choose the target SAP S/4HANA product version.</li>\n</ol></ol>\n<p>Note: If the target product version is not available in the list, follow the steps above to update the local replica of the simplification item catalog. Only released-to-customer product versions are visible in the list.</p>\n<ol><ol start=\"3\">\n<li>Choose the mode you want to perform the check:</li>\n</ol></ol><ol>\n<ul>\n<ul>\n<li><em>New relevance check in Online mode</em>: The result will be displayed immediately after the check completes.</li>\n<li><em>New relevance &amp; consistency check as background job</em>: We recommend this option when you expect a long runtime or you are uncertain of the runtime.</li>\n</ul>\n</ul>\n</ol><ol><ol start=\"4\">\n<li>Execute the check to receive the simplification item list.</li>\n<li>Review the results (see below).</li>\n</ol></ol>\n<p><strong>  4. Reviewing the Results in /SDF/RC_START_CHECK</strong></p>\n<p>When using the data collection framework for SAP Readiness Check to execute the simplification item checks, the results are collected in the generated archive and can be uploaded to the landing page for SAP Readiness Check: <a href=\"https://me.sap.com/readinesscheck\" target=\"_blank\">https://me.sap.com/readinesscheck</a>.</p>\n<p>Independently of how you initiated the simplification items evaluation, /SDF/RC_START_CHECK can be used to review the detailed results. To find the results, log on to client 000, start report /SDF/RC_START_CHECK, select the targeted SAP S/4HANA product version, then select the <em>Display Last Check Result</em> option from the <em>Simplification Item Check Options </em>section<em>,</em> and then choose <em>Execute</em>.</p>\n<p>Solve any errors identified by the consistency check, as this is <strong>mandatory</strong> for any conversion or upgrade to SAP S/4HANA 1809 or higher.</p>\n<p>4.1. Interpreting the Check Results</p>\n<p>Within the check result view, you will find the following:</p>\n<ul>\n<ul>\n<li>Basic information about a simplification item, including application area, ID, title, application component, category, and business impact note.</li>\n<li>Indication of whether the simplification item is relevant to the system or not.</li>\n<li>Results of the most recent consistency check, with an indication of whether the system is consistent, related to the simplification item, or not. Select the simplification item and choose <em>Display Consistency Check Log</em> to find the latest result and learn how to resolve the inconsistency.</li>\n<li>Indication of whether an exemption is possible for those simplification items ending in error. Some simplification items only require the acknowledgment of the customer. You need only apply an exemption to the simplification item in such cases. In doing so, you acknowledge that you have read and understood the message. Once an item is exempt, it will no longer block the conversion to the corresponding target SAP S/4HANA product version.</li>\n<li>A summary of the relevancy assessment of the simplification item.</li>\n</ul>\n</ul>\n<p>4.2. Administrative Functions within the Check Results View</p>\n<p>From the check results view, various operations regarding the consistency check are possible, including:</p>\n<ul>\n<ul>\n<li>Initiate a consistency check for all relevant items.</li>\n<li>Perform a detailed consistency check for a selected item.</li>\n<li>Display the consistency check log.</li>\n<li>Apply or revoke exemption for consistency check errors able to be skipped.</li>\n<li>Display exemption log.</li>\n</ul>\n</ul>\n<p><strong>  5. Additional Information</strong></p>\n<p>5.1. Executing simplification item checks for a newly released SAP S/4HANA Product Version, Feature Package Stack, or Support Package Stack</p>\n<p>Suppose you encounter the issue where you cannot see the newly released SAP S/4HANA feature package stack or support package stack in the target list. In that case, you need to update the simplification item catalog to the latest version available from SAP. See section 2.2.1 for more information.</p>\n<p>5.2. Framework for storing and managing the simplification item check logs</p>\n<p>The simplification item checks use the application log to retain the check logs. These logs are client-specific; therefore, you must ensure you are in the correct client when analyzing the check logs.</p>\n<p>The check logs produced by Software Update Manager are only available in client 000.</p>\n<p>5.3. Adding an exemption for an inconsistent simplification item</p>\n<p>The simplification item check only supports the exemption of an item when the consistency check return code is 7.</p>\n<p>When creating the exemption, you must make sure the selected target version in /SDF/RC_START_CHECK is the same as the target product version processed by Software Update Manager.</p>\n<p>5.4. Using a central download service system to download simplification item catalog updates</p>\n<p>If you use a central download service system as a communication server and the ST-PI version in the landscape is less than or equal to ST-PI 2008_1_700 SP27, ST-PI 2008_1_710 SP27, or ST-PI 740 SP17, you need to implement this SAP Note (2399707) in the central download service system.</p>\n<p>Additionally, you need to implement SAP Note <a href=\"/notes/2945785\" target=\"_blank\">2945785</a> in the central download service system.</p>\n<p>5.5. Determining the relevant application component to use when creating an SAP incident related to a simplification item</p>\n<p>If you encounter an issue with a specific simplification item, it is best to create the customer support incident under the item’s relevant application component.</p>\n<p>The correct component can be identified by following these steps:</p>\n<ol><ol>\n<li>Open the URL <a href=\"https://me.sap.com/sic\" target=\"_blank\">https://me.sap.com/sic</a> with your S-User.</li>\n<li>Search the item with the Check item ID.</li>\n<li>Open the item.</li>\n<li>Go to the <em>Application Component</em> tab on the detail page.</li>\n</ol></ol>\n<p><strong>  6. Frequently Asked Questions</strong></p>\n<p>6.1. What do I need to check before implementing this SAP Note (2399707)?</p>\n<ul>\n<ul>\n<li>We <strong>strongly recommend </strong>checking that the latest version of SAP Note <a href=\"/notes/1668882\" target=\"_blank\">1668882</a> is “completely implemented” before you implement this SAP Note.</li>\n</ul>\n</ul>\n<p>If you encounter an issue while installing or updating this SAP Note (2399707), for example, syntax error after implementation, perform the following steps:</p>\n<ul>\n<ul>\n<ul>\n<li>Reset the implementation of this SAP Note (2399707) using transaction SNOTE.</li>\n<li>Implement the latest version of SAP Note <a href=\"/notes/1668882\" target=\"_blank\">1668882</a>.</li>\n<li>Exit the transaction SNOTE.</li>\n<li>Open SNOTE and reimplement this SAP Note (2399707).</li>\n</ul>\n</ul>\n</ul>\n<ul>\n<ul>\n<li>When encountering the runtime error DDIC_TYPELENG_INCONSISTENT with report /SDF/RC_START_CHECK, first check to see whether SAP Note <a href=\"/notes/1229062\" target=\"_blank\">1229062</a> is valid for the system. If the SAP Note is not applicable, manually activate the following DDIC structures: SWNCAGGTASKTYPE, SWNCAGGUSERTCODE, SWNCAGGUSERWORKLOAD, and SWNCAGGTASKTIMES.</li>\n</ul>\n</ul>\n<p>6.2. Why do I get syntax errors when activating the objects in this SAP Note (2399707)?</p>\n<p>Usually, this is because the code context for the correction instruction is inconsistent. You can reset the implementation of this SAP Note (2399707) and then reimplement it.</p>\n<p>If this process does not resolve the issue, create a customer support incident under the component CA-TRS-PRCK.</p>\n<p>6.3. Where can I find the SAP Note for performing the simplification item check for SAP Readiness Check for SAP BW/4HANA?</p>\n<p>The SAP Readiness Check for SAP BW/4HANA approach evolved from analyzing a list of simplification items to evaluating the objects within the SAP BW system. Now, simplification item-related SAP Notes are available in the <em>Learn More</em> text and the tables provided within the detailed views.</p>\n<p>6.4. Is it possible to skip a simplification item consistency check?</p>\n<p>Yes, it is technically possible to skip a simplification item consistency check. However, you must create a customer support incident under the component CA-TRS-TDBT requesting access to SAP Note <a href=\"/notes/2641675\" target=\"_blank\">2641675</a>.</p>\n<p>6.5. How can I resolve the warning in /SDF/RC_START_CHECK that I cannot fetch the simplification item catalog through the SAP-SUPPORT_PORTAL HTTP connection?</p>\n<p>This issue arises when the SAP-SUPPORT_PORTAL RFC destination is not working. Reference the steps in SAP Note <a href=\"/notes/91488\" target=\"_blank\">91488</a> to resolve the communication issue.</p>\n<p>Alternatively, you can use the manual download and upload process described above to update the local simplification item catalog replica.</p>\n<p>6.6. In which client should I perform the simplification item checks?</p>\n<ul>\n<ul>\n<li><strong>Relevance Check</strong>: We recommend analyzing each of the system’s productive clients. If there is more than one productive client in the system, we recommend evaluating each client. Note that results from each client will require a separate analysis to display the results. </li>\n<li><strong>Consistency Check</strong>: Client 000 is the recommended client, as this is the same client the Software Update Manager will evaluate during the technical conversion. These results can enhance a system’s analysis when you upload them to the analysis results of the productive client.</li>\n</ul>\n</ul>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"3\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td><strong>Version</strong></td>\n<td><strong>Change</strong></td>\n</tr>\n<tr>\n<td>167</td>\n<td>\n<p>Text change for specific minimum versions</p>\n</td>\n</tr>\n<tr>\n<td>166</td>\n<td>\n<p>Enhance application log when item is relevant but no consistency check defined.</p>\n</td>\n</tr>\n<tr>\n<td>165</td>\n<td>\n<p>Bugfix: The results from the first check didn't automatically sort when clicking \"Display Last Check Result\".</p>\n</td>\n</tr>\n<tr>\n<td>164</td>\n<td>\n<p>Includes ST-PI 740 SP25 and ST-PI 2008_1_700 SP35 into validation component.</p>\n<p>You don't need update to this version if your SP is smaller than above.</p>\n</td>\n</tr>\n<tr>\n<td>162/163</td>\n<td>\n<p>Bugfix: check class is available in the system but got error 'not found'.</p>\n</td>\n</tr>\n<tr>\n<td>161</td>\n<td>\n<p>Includes ST-PI 740 SP24 and ST-PI 2008_1_700 SP34 into validation component.</p>\n<p>You don't need update to this version if your SP is smaller than above.</p>\n</td>\n</tr>\n<tr>\n<td>160</td>\n<td>\n<p>Add the stack into source release adjustment.</p>\n</td>\n</tr>\n<tr>\n<td>158/159</td>\n<td>\n<p>Bugfix: when relevant simple check results contains MISS_ST03N or RULE_ISSUE, the relevent check result may not correct.</p>\n</td>\n</tr>\n<tr>\n<td>157</td>\n<td>\n<p>ST-PI 740 SP21 and ST-PI 2008_1_700/2008_1_710 SP31 will include the code of this version.</p>\n</td>\n</tr>\n<tr>\n<td>156</td>\n<td>\n<p>Enhance the SUM log if the target product version does not exist in Simplification Item check framework.</p>\n</td>\n</tr>\n<tr>\n<td>155</td>\n<td>\n<p>Enhance note check text for SAP Note 2399707. ST-PI 740 SP20 and ST-PI 2008_1_700/2008_1_710 SP30 will include the code of this version.</p>\n</td>\n</tr>\n<tr>\n<td>153/154</td>\n<td>\n<p>Enhance the application log text for note check of consistency check.</p>\n</td>\n</tr>\n<tr>\n<td>152</td>\n<td>\n<p>Bugfix: if \"New relevance &amp; consistency check as background job\" selected and click \"Execute in Background\" (F9) in the menu, there's  too much batch jobs \"RC_NEW_CHECK_IN_JOB\" created.</p>\n<p>Change the note description.</p>\n</td>\n</tr>\n<tr>\n<td>151</td>\n<td>\n<p>Bugfix: replace the SY-DATUM with the date value when checking DB.</p>\n</td>\n</tr>\n<tr>\n<td>150</td>\n<td>\n<p>Fix the bug which from download service server.</p>\n</td>\n</tr>\n<tr>\n<td>148/149</td>\n<td>\n<p>Enhance the IDoc relevance check performance issue. The client number is added in the select SQL statement to avoid performance issue in some system environments.</p>\n</td>\n</tr>\n<tr>\n<td>146/147</td>\n<td>\n<p>Fix the IDoc relevance check bug that the check result is not correct when exectued in different client.</p>\n</td>\n</tr>\n<tr>\n<td>145</td>\n<td>\n<p>Remove the SAP S/4HANA ON-PREMISE 1511 from target version list since it's out of maintenance.</p>\n<p>Add the application log \"commit work\" statement for performance optimization.</p>\n</td>\n</tr>\n<tr>\n<td>144</td>\n<td>\n<p>Fix bug: the xml format error when update catalog with latest version from SAP.</p>\n</td>\n</tr>\n<tr>\n<td>143</td>\n<td>\n<p>Fix bug: Class /SDF/CL_RC_CHK_UTILITY, Public Section, Field \"ICON_CHECKED\" is unknown.</p>\n</td>\n</tr>\n<tr>\n<td>142</td>\n<td>\n<p>Fix bug: avoid creating too many work processes when download service is active</p>\n</td>\n</tr>\n<tr>\n<td>141</td>\n<td>\n<p>Update the note description to add the minimum recommendation for SAP S/4HANA 2020 Feature Package 1.</p>\n<p>No code updated, so for customers who have already implemented the SAP Note, which fulfills the minimum recommendation, it is not required to update.</p>\n</td>\n</tr>\n<tr>\n<td>140</td>\n<td>\n<p>Bugfix: SAP S/4HANA 1709 is still supported for a system upgrade, do not show error message when upgrade to 1709.</p>\n</td>\n</tr>\n<tr>\n<td>139</td>\n<td>\n<p>Includes ST-PI 740 SP14 and ST-PI 2008_1_700 SP24 into validation component.</p>\n<p>You don't need update to this version if your SP is smaller than above.</p>\n</td>\n</tr>\n<tr>\n<td>138</td>\n<td>\n<p>Enhance error message for consistency check since that the target release SAP S/4HANA 1709 is no longer supported for a system conversion.</p>\n</td>\n</tr>\n<tr>\n<td>137</td>\n<td>\n<p>Update the note description to add the minimum recommendation for SAP S/4HANA 1909 Feature Package 2 and SAP S/4HANA 2020 Initial Shipment.</p>\n<p>No code updated, so for customers who have already implemented the SAP Note, which fulfills the minimum recommendation, it is not required to update.</p>\n</td>\n</tr>\n<tr>\n<td>136</td>\n<td>\n<p>Fix issue: Type \"CL_ABAP_DYN_PRG\" is unknown.</p>\n<p>You can ignore this version if you don't have this issue.</p>\n</td>\n</tr>\n<tr>\n<td>134/135</td>\n<td>\n<p>Support Download Service to update the Simplification Item Catalog. Please check more description of this issue in SAP Note <a href=\"/notes/2882166\" target=\"_blank\">2882166</a>.</p>\n<p>You can ignore this version if you are not using Download Service to connect to the SAP Support Backbone.</p>\n</td>\n</tr>\n<tr>\n<td>133</td>\n<td>\n<p>Fix bug: do not block the process when ICM is unavailable.</p>\n<p>You can ignore this version if ICM service is active.</p>\n</td>\n</tr>\n<tr>\n<td>132</td>\n<td>\n<p>Fix bug: the consistency result can not show the latest result for items with exemption applied.</p>\n</td>\n</tr>\n<tr>\n<td>131</td>\n<td>\n<p>Improve the performance of cluster table checking if the database is Sybase.</p>\n<p>You can ignore this version if your database isn't Sybase.</p>\n</td>\n</tr>\n<tr>\n<td>130</td>\n<td>\n<p>Fix bug: The program is blocked when updating BALSUB.</p>\n</td>\n</tr>\n<tr>\n<td>129</td>\n<td>\n<p>Use SAP-SUPPORT_PORTAL HTTP connection instead of SAPOSS RFC connection. Note requirement check works and Simplification Item Catalog content can automatically update from SAP servers in this version. Please check more description of this issue in SAP Note <a href=\"/notes/2882166\" target=\"_blank\">2882166</a>.</p>\n</td>\n</tr>\n<tr>\n<td>128</td>\n<td>\n<p>Remove remote note requirement check because SAPOSS does not work anymore.</p>\n</td>\n</tr>\n<tr>\n<td>127</td>\n<td>\n<p>Includes ST-PI 740 SP level 12 into validation component. You don't need update to this version if your ST-PI 740 SP is smaller than SP12.</p>\n</td>\n</tr>\n<tr>\n<td>126</td>\n<td>\n<p>Update Note description and enhance ST03N data collection.</p>\n</td>\n</tr>\n<tr>\n<td>125</td>\n<td>\n<p>Add new fields \"LoB/Technology\" and \"Business Area\" into check result; Remove usage of CL_ABAP_DYN_PRG.</p>\n</td>\n</tr>\n<tr>\n<td>123/124</td>\n<td>\n<p>Includes ST-PI 740 SP level 11 into validation component. You don't need update to this version if your ST-PI 740 SP is smaller than SP11.</p>\n</td>\n</tr>\n<tr>\n<td>122</td>\n<td>\n<p>Enhance error message for consistency check; Update note text for minimum version requirement; Fix bug: filter lost when resizing check result</p>\n</td>\n</tr>\n<tr>\n<td>120-121</td>\n<td>\n<p>Fix bug: Stop <em>SUM-Phase: PREP_EXTENSION/RUN_S4H_SIF_CHECK_INIT even if the return code is 4.</em></p>\n</td>\n</tr>\n<tr>\n<td>118/119</td>\n<td>\n<p>Add Simplification Item Consistency support for Readiness Check 2.0.</p>\n<p>Add SUM return code support and write the SUM log when error occurs in SUM mode.</p>\n</td>\n</tr>\n<tr>\n<td>114/115/116/117</td>\n<td>\n<p>Change ST03N data check: Do the entry point check if the ST03N entries are available.</p>\n</td>\n</tr>\n<tr>\n<td>113</td>\n<td>\n<p>Enhance performance issue for IDoc relevance check.</p>\n</td>\n</tr>\n<tr>\n<td>109~110</td>\n<td>\n<p>Bugfix:</p>\n<ul>\n<li>Right propagation of highest return code (8 + 7 + applied exemption is still rc=8)</li>\n<li>Check for the missing putstatus “I” to get the correct SUM phase</li>\n</ul>\n</td>\n</tr>\n<tr>\n<td>106~107</td>\n<td>\n<p>Bugfix: the check result is wrong when check the table with DATS or TIMS data type relevant condition.</p>\n</td>\n</tr>\n<tr>\n<td>104~105</td>\n<td>\n<p>Bugfix for relevance check.</p>\n</td>\n</tr>\n<tr>\n<td>103</td>\n<td>\n<p>Update the note title and description. <strong>SAP Note 2502552 is not required for SAP Readiness Check</strong>. It is only for consistency check.</p>\n</td>\n</tr>\n<tr>\n<td>100</td>\n<td>\n<p>Update the note description to add the minimum recommendation for SAP S/4HANA 1809 Initial Shipment.</p>\n<p>No code updated, so for customers who have already implemented the SAP Note, which fulfills the minimum recommendation, it is not required to update.</p>\n</td>\n</tr>\n</tbody>\n</table></div>", "noteVersion": 167}, {"note": "1015193", "noteTitle": "1015193 - Overview note for SAP IS-UT CEE add-on", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>Overview note for SAP IS-UT CEE add-on</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>CEEISUT component, localization of IS-U and IS-T for Central and Eastern Europe</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>Overview of all notes for the add-on SAP IS-UT CEE (technical component name CEEISUT) for the topics:<br/>Releases strategy<br/>New installation<br/>Upgrade<br/>Support packages<br/><br/>***********************************************************************<br/>This note applies to:<br/>SAP IS-UT CEE -  Add-on with localization of IS-U and IS-T for countries in Central and Eastern Europe.<br/>***********************************************************************<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p><br/><b>Notes on the R/3 \"Add-on SAP CORE CEE\"</b></p> <ol>1. Release strategy</ol> <ul><li>Release strategy for the SAP IS-UT CEE add-on:  1014997</li></ul> <ol>2. New installation</ol> <ul><li>SAP IS-UT CEE 606 Add-On Installation:  1645476</li></ul> <ul><li>SAP IS-UT CEE 604 Add-On Installation:  1300345</li></ul> <ul><li>SAP IS-UT CEE 600 Add-On Installation:  1005317</li></ul> <ul><li>SAP IS-UT CEE 472 Add-On Installation:  774230</li></ul> <ol>3. Upgrade</ol> <ul><li>SAP IS-UT CEE 606 Add-On Upgrade   1645477</li></ul> <ul><li>SAP IS-UT CEE 604 Add-On Upgrade   1267871</li></ul> <ul><li>SAP IS-UT CEE 472 Add-On Upgrade   1006196</li></ul> <ol>4. Support packages</ol> <ul><li>SAP IS-UT CEE 606 Add-on Support Packages:  1660394</li></ul> <ul><li>SAP IS-UT CEE 604 Add-on Support Packages:  1318062</li></ul> <ul><li>SAP IS-UT CEE 600 Add-on Support Packages:  1014951</li></ul> <ul><li>SAP IS-UT CEE 472 Add-on Support Packages:  1004617</li></ul></div>", "noteVersion": 4}]}, {"note": "1014997", "noteTitle": "1014997 - Release strategy for the SAP IS-UT CEE add-on", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>Planning installation and upgrade of the R/3 add-on SAP IS-UT CEE</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>CEEISUT</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>Localization of IS-U and IS-T for countries in Central and Eastern Europe</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>0.    Change History<br/>I.    General Information<br/>II.   Overview<br/>III.  Installation<br/>IV.   Upgrade<br/>V.    Combination with other Add-ons<br/>VI.   Maintenance<br/>VII.  Reintegration</p>\n<p><strong>0. Change History</strong></p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"3\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td>Date</td>\n<td>Section</td>\n<td>Description</td>\n</tr>\n<tr>\n<td>8.8.2016</td>\n<td>VIII</td>\n<td>CEEISUT add-on retrofitted to S4H OP 1610</td>\n</tr>\n<tr>\n<td>01.04.2016</td>\n<td>II,III,IV,V </td>\n<td>CEEISUT 606 released for EhP8</td>\n</tr>\n<tr>\n<td>01.04.2015</td>\n<td>V</td>\n<td>CEEISUT 606 released for SAP SFINANCIALS 2.0</td>\n</tr>\n<tr>\n<td>15.08.2014</td>\n<td>V</td>\n<td>CEEISUT 606 released for SAP SFINANCIALS 1.0</td>\n</tr>\n<tr>\n<td>19.12.2013</td>\n<td>II,III,IV,V </td>\n<td>CEEISUT 606 released for EhP7</td>\n</tr>\n<tr>\n<td>23.07.2013</td>\n<td>II</td>\n<td>Polish localization included</td>\n</tr>\n<tr>\n<td>07.12.2011</td>\n<td>II,III,IV,V </td>\n<td>CEEISUT 606 released for EhP6</td>\n</tr>\n<tr>\n<td>27.10.2010</td>\n<td>II</td>\n<td>Slovenian localization included</td>\n</tr>\n<tr>\n<td>06.10.2010</td>\n<td>II,III,IV,V</td>\n<td>CEEISUT 604 released for EhP5</td>\n</tr>\n<tr>\n<td>16.06.2010</td>\n<td>II</td>\n<td>Greek localization included</td>\n</tr>\n<tr>\n<td>26.04.2010</td>\n<td>V</td>\n<td>Planned add-on release date for EhP5</td>\n</tr>\n<tr>\n<td>27.03.2009</td>\n<td>II,III,IV,V</td>\n<td>CEEISUT 604 released for EhP4</td>\n</tr>\n<tr>\n<td>15.05.2008</td>\n<td>II,III,V</td>\n<td>CEEISUT 600 released for EhP3</td>\n</tr>\n<tr>\n<td>12.03.2008</td>\n<td>II,III,V</td>\n<td>CEEISUT 600 released for EhP2</td>\n</tr>\n<tr>\n<td>08.01.2007</td>\n<td> </td>\n<td>Note created</td>\n</tr>\n</tbody>\n</table></div>\n<p> </p>\n<p><strong>I. General Information<br/></strong></p>\n<p>R/3 add-on installations can only be installed to certain R/3 Releases. Thus, SAP IS-UT CEE add-on, can only be used on R/3 Releases listed in section II/1.<br/>SAP IS-UT CEE is official name of the product delivered by the add-on, Corresponding software component is CEEISUT.<br/><br/>Before installation of the add-on take into account following restrictions concerning upgrade and maintenance of your R/3 System which result from the add-on installation.<br/>It is not possible to deinstall an R/3 Add-on.<br/>In systems in which an R/3 add-on is installed only upgrades to R/3 Releases that are supported for this add-on can be carried out. You can find an overview of the upgrades supported for SAP IS-UT CEE in section IV.<br/>Bear in mind that there is a delay between the delivery of the R/3 standard release and the release of the corresponding add-on releases.<br/>If an add-on upgrade is integrated with an R/3 Core release switch, it is integrated into the R/3 Core upgrade (Repository Switch). In addition to the R/3 standard upgrade CDs, an additional add-on upgrade CD/package is needed to execute this upgrade. If the standard R/3 System upgrade is carried out without this additional CD/package, the add-on is no longer functional after the upgrade. The entire R/3 System is inconsistent. For more information on this problem, read Note 33040.<br/><br/>In summary this means: As soon as you have installed an add-on, for which no return to the R/3 standard system is planned (retrofit), you can only select specific R/3 upgrade paths where you always have to wait until these paths are supported.</p>\n<p><strong>II. Overview</strong><br/><br/></p>\n<ol>1. Availability of SAP IS-UT CEE</ol>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"3\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td rowspan=\"2\">\n<p>CEEISUT<br/>Add-On Version</p>\n</td>\n<td rowspan=\"2\">\n<p>Material<br/>Number CD</p>\n</td>\n<td colspan=\"2\">\n<p>Core Version</p>\n</td>\n<td rowspan=\"2\">\n<p>Availability<br/>Date</p>\n</td>\n<td rowspan=\"2\">\n<p>End of Maint.<br/>Date</p>\n</td>\n<td rowspan=\"2\">\n<p>Delivery<br/>Channel</p>\n</td>\n<td colspan=\"2\">\n<p>Minimal SP level</p>\n</td>\n</tr>\n<tr>\n<td>\n<p>ERP</p>\n</td>\n<td>\n<p>IS-UT</p>\n</td>\n<td>IS-UT</td>\n<td>\n<p> CEEISUT</p>\n</td>\n</tr>\n<tr>\n<td rowspan=\"4\">CEEISUT 606</td>\n<td rowspan=\"4\">51042058</td>\n<td>ERP 6.0 EhP8</td>\n<td>IS-UT 6.18</td>\n<td>01.04.2016</td>\n<td>31.12.2027</td>\n<td>(RS)</td>\n<td>IS-UT 618 SP 01</td>\n<td>CEEISUT 606 SP 11</td>\n</tr>\n<tr>\n<td>ERP 6.0 EhP7</td>\n<td>IS-UT 6.17</td>\n<td>19.12.2013</td>\n<td>31.12.2027</td>\n<td>(RS)</td>\n<td>IS-UT 617 SP 02</td>\n<td>CEEISUT 606 SP 07</td>\n</tr>\n<tr>\n<td>ERP 6.0 EhP6 on HANA</td>\n<td>IS-UT 6.16</td>\n<td colspan=\"5\">CEEISUT add-on is not planned to be released for ERP 6.0 EhP6 on HANA</td>\n</tr>\n<tr>\n<td>ERP 6.0 EhP6</td>\n<td>IS-UT 6.06</td>\n<td>08.12.2011</td>\n<td>31.12.2027 </td>\n<td>(RS)</td>\n<td>IS-UT 606 SP 01</td>\n<td>CEEISUT 606 SP 00</td>\n</tr>\n<tr>\n<td rowspan=\"2\">CEEISUT 604</td>\n<td rowspan=\"2\">51035690</td>\n<td>ERP 6.0 EhP5</td>\n<td>IS-UT 6.05</td>\n<td>04.10.2010</td>\n<td>31.12.2025</td>\n<td>(RS)</td>\n<td> </td>\n<td>CEEISUT 604 SP 05</td>\n</tr>\n<tr>\n<td>ERP 6.0 EhP4</td>\n<td>IS-UT 6.04</td>\n<td>24.03.2009</td>\n<td>31.12.2025</td>\n<td>(RS)</td>\n<td>IS-UT 604 SP 01</td>\n<td>CEEISUT 604 SP 00</td>\n</tr>\n<tr>\n<td rowspan=\"3\">CEEISUT 600</td>\n<td rowspan=\"5\">no mat.numb.<br/>download only</td>\n<td>ERP 6.0 EhP3</td>\n<td>IS-UT 6.03</td>\n<td>15.05.2008</td>\n<td>31.12.2025</td>\n<td>(RS)</td>\n<td> </td>\n<td>CEEISUT 600 SP 10</td>\n</tr>\n<tr>\n<td>ERP 6.0 EhP2</td>\n<td>IS-UT 6.02</td>\n<td>12.03.2008</td>\n<td>31.12.2025</td>\n<td>(RS)</td>\n<td> </td>\n<td>CEEISUT 600 SP 10</td>\n</tr>\n<tr>\n<td>ERP 6.0</td>\n<td>IS-UT 6.00</td>\n<td>08.01.2007 </td>\n<td>31.12.2025</td>\n<td>(RS)</td>\n<td>IS-UT 600 SP 06</td>\n<td> </td>\n</tr>\n<tr>\n<td>CEEISUT 472</td>\n<td>R/3 Enterprise 47X200</td>\n<td>IS-U/CCS 4.72</td>\n<td>22.09.2004</td>\n<td>31.03.2013</td>\n<td>(RS)</td>\n<td>SAPKIPUL01 / SAPKIPTL01</td>\n<td> </td>\n</tr>\n<tr>\n<td>transports</td>\n<td>E/3 46C</td>\n<td>IS-U/CCS 4.64</td>\n<td>25.01.2002</td>\n<td>31.03.2013</td>\n<td>(DL)</td>\n<td> </td>\n<td> </td>\n</tr>\n</tbody>\n</table></div>\n<p>Delivery channels:</p>\n<ul>\n<li>(RS): Restricted Shipment - Installation/Upgrade packages are available for download on SAP Service Marketplace \"service.sap.com/swdc\" under SAP Installations and Upgrades, Application Group Country specific Add-Ons, SAP IS-UT CEE. The download is available on request. Only released customers get the access to download the software. Customers interested in the download should apply for the authorization by Customer message sent to component XX-CSC-CZ-IS-U. In the Customer message following information should be provided:<br/>o particular countries that are planned for implementation<br/>o user IDs (S....) that should obtain the access Released customer will get access to all current and further SAP IS-UT CEE deliveries without additional requests.</li>\n</ul>\n<ul>\n<li>(DL): Download - For release 4.64, transport files can be downloaded from attachments of notes.</li>\n</ul>\n<p>For more about CEEISUT and ECC 6. 0 Enhance Packages see chapter V.</p>\n<p>For the exact end-of-maintenance dates SAP IS-UT CEE please see the SAP Service Marketplace - Product Availability Matrix \"service.sap.com/pam\" under Country specific add-ons, SAP IS-UT CEE.</p>\n<p>The SAP IS-UT CEE add-on is Unicode compatible as of version 472.</p>\n<ol>2. Included country versions </ol>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"3\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td>CEEISUT rel.</td>\n<td>IS-UT release</td>\n<td>BG</td>\n<td>CZ</td>\n<td>GR</td>\n<td>HU</td>\n<td>PL</td>\n<td>RO</td>\n<td>RU</td>\n<td>SI</td>\n<td>SK</td>\n<td>TR</td>\n<td>UA</td>\n</tr>\n<tr>\n<td>CEEISUT 606</td>\n<td>618, 617, 606</td>\n<td>x</td>\n<td>x</td>\n<td>x</td>\n<td>x</td>\n<td>x</td>\n<td>x</td>\n<td>x</td>\n<td>x</td>\n<td>x</td>\n<td>x</td>\n<td>x</td>\n</tr>\n<tr>\n<td>CEEISUT 604</td>\n<td>605, 604</td>\n<td>x</td>\n<td>x</td>\n<td>x</td>\n<td>x</td>\n<td>x</td>\n<td>x</td>\n<td>x</td>\n<td>x</td>\n<td>x</td>\n<td>x</td>\n<td>x</td>\n</tr>\n<tr>\n<td>CEEISUT 600</td>\n<td>603, 602, 600</td>\n<td>x</td>\n<td>x</td>\n<td>x</td>\n<td>x</td>\n<td>x</td>\n<td>x</td>\n<td>x</td>\n<td>-</td>\n<td>x</td>\n<td>x</td>\n<td>x</td>\n</tr>\n<tr>\n<td>CEEISUT 472</td>\n<td>4.72</td>\n<td>x</td>\n<td>x</td>\n<td>-</td>\n<td>x</td>\n<td>o</td>\n<td>x</td>\n<td>o</td>\n<td>-</td>\n<td>x</td>\n<td>-</td>\n<td>x</td>\n</tr>\n<tr>\n<td>464 transports</td>\n<td>4.64</td>\n<td>-</td>\n<td>x</td>\n<td>-</td>\n<td>x</td>\n<td>o</td>\n<td>x</td>\n<td>-</td>\n<td>-</td>\n<td>x</td>\n<td>-</td>\n<td>-</td>\n</tr>\n</tbody>\n</table></div>\n<ol><ol></ol><ol></ol></ol>\n<p>\"-\"    country version not available<br/>\"o\"    country version obsolete / not supported any more<br/>\"x\"    country version available</p>\n<p>BG: Bulgaria<br/>CZ: Czech Republic<br/>GR: Greece (included as of CEEISUT 600 SP 16 &amp; CEEISUT 604 SP 04)<br/>HU: Hungary<br/>PL: Poland <br/>RO: Romania<br/>RU: Russia   (officially available as of CEEISUT 600, see notes 921035 and 1014954)<br/>SI: Slovenia (included as of CEEISUT 604 SP 06)<br/>SK: Slovakia<br/>TR: Turkey<br/>UA: Ukraine<br/><br/>The add-on is not released for any other country.</p>\n<p><strong>III. Installations of SAP IS-UT CEE</strong></p>\n<p>The SAP IS-UT CEE add-on installation notes and packages.</p>\n<ol></ol>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"3\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td>Add-On Version</td>\n<td>ERP Version</td>\n<td>Add-On Installation Note</td>\n<td>Add-On Installation Package</td>\n</tr>\n<tr>\n<td rowspan=\"3\">CEEISUT 606</td>\n<td>ERP 6.0 EhP8<br/>(IS-UT 618)</td>\n<td rowspan=\"3\">1645476</td>\n<td rowspan=\"3\">SAPK-606AGINCEEISUT</td>\n</tr>\n<tr>\n<td>ERP 6.0 EhP7<br/>(IS-UT 617)</td>\n</tr>\n<tr>\n<td>ERP 6.0 EhP6<br/>(IS-UT 606)</td>\n</tr>\n<tr>\n<td rowspan=\"2\">\n<p>CEEISUT 604</p>\n</td>\n<td>ERP 6.0 EhP5<br/>(IS-UT 605)</td>\n<td rowspan=\"2\">1300345</td>\n<td rowspan=\"2\">SAPK-604AGINCEEISUT</td>\n</tr>\n<tr>\n<td>ERP 6.0 EhP4<br/>(IS-UT 604)</td>\n</tr>\n<tr>\n<td rowspan=\"3\">CEEISUT 600</td>\n<td>ERP 6.0 EhP3<br/>(IS-UT 603)</td>\n<td rowspan=\"3\">1005317, (1150868)</td>\n<td rowspan=\"3\">SAPK-600I1INCEEISUT</td>\n</tr>\n<tr>\n<td>ERP 6.0 EhP2<br/>(IS-UT 602)</td>\n</tr>\n<tr>\n<td>ERP 6.0<br/>(IS-UT 600)</td>\n</tr>\n<tr>\n<td>CEEISUT 472</td>\n<td>(IS-U/CCS or IS-T 4.72)</td>\n<td>774230</td>\n<td>SAPK-472I1INCEEISUT</td>\n</tr>\n<tr>\n<td>Transports</td>\n<td>(IS-U/CCS or IS-T 4.64)</td>\n<td> </td>\n<td>Transports</td>\n</tr>\n</tbody>\n</table></div>\n<p><br/><strong>IV. Upgrades of SAP IS-UT CEE</strong><br/><br/></p>\n<p>The SAP IS-UT CEE add-on upgrade notes and scenarios</p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"3\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td colspan=\"2\" rowspan=\"3\">Source Version</td>\n<td colspan=\"8\">Target Version</td>\n</tr>\n<tr>\n<td colspan=\"3\">CEEISUT 606</td>\n<td colspan=\"2\">CEEISUT 604</td>\n<td colspan=\"3\">CEEISUT 600</td>\n</tr>\n<tr>\n<td>ERP 6.0 EhP8<br/>(IS-UT 618)</td>\n<td>ERP 6.0 EhP7<br/>(IS-UT 617)</td>\n<td>ERP 6.0 EhP6<br/>(IS-UT 606)</td>\n<td>ERP 6.0 EhP5<br/>(IS-UT 605)</td>\n<td>ERP 6.0 EhP4<br/>(IS-UT 604)</td>\n<td>ERP 6.0 EhP3<br/>(IS-UT 603)</td>\n<td>ERP 6.0 EhP2<br/>(IS-UT 602)</td>\n<td>ERP 6.0<br/>(IS-UT 600)</td>\n</tr>\n<tr>\n<td rowspan=\"2\">CEEISUT 606</td>\n<td>ERP 6.0 EhP7<br/>(IS-UT 617)</td>\n<td>1645476 (A)</td>\n<td>         -</td>\n<td>         -</td>\n<td>         -</td>\n<td>         -</td>\n<td>         -</td>\n<td>         -</td>\n<td>         -</td>\n</tr>\n<tr>\n<td>ERP 6.0 EhP6<br/>(IS-UT 606)</td>\n<td>1645476 (A)</td>\n<td>1645476 (A)</td>\n<td>         -</td>\n<td>         -</td>\n<td>         -</td>\n<td>         -</td>\n<td>         -</td>\n<td>         -</td>\n</tr>\n<tr>\n<td rowspan=\"2\">CEEISUT 604</td>\n<td>ERP 6.0 EhP5<br/>(IS-UT 605)</td>\n<td>1645476 (B)</td>\n<td>1645476 (B)</td>\n<td>1645476 (B)</td>\n<td>         -</td>\n<td>         -</td>\n<td>         -</td>\n<td>         -</td>\n<td>         -</td>\n</tr>\n<tr>\n<td>ERP 6.0 EhP4<br/>(IS-UT 604)</td>\n<td>1645476 (B)</td>\n<td>1645476 (B)</td>\n<td>1645476 (B)</td>\n<td>1267871 (A)</td>\n<td>         -</td>\n<td>         -</td>\n<td>         -</td>\n<td>         -</td>\n</tr>\n<tr>\n<td rowspan=\"3\">CEEISUT 600</td>\n<td>ERP 6.0 EhP3<br/>(IS-UT 603)</td>\n<td>1645476 (B)</td>\n<td>1645476 (B)</td>\n<td>1645476 (B)</td>\n<td>1267871 (B)</td>\n<td>1267871 (B)</td>\n<td>         -</td>\n<td>         -</td>\n<td>         -</td>\n</tr>\n<tr>\n<td>ERP 6.0 EhP2<br/>(IS-UT 602)</td>\n<td>1645476 (B)</td>\n<td>1645476 (B)</td>\n<td>1645476 (B)</td>\n<td>1267871 (B)</td>\n<td>1267871 (B)</td>\n<td>1006196 (A)</td>\n<td>         -</td>\n<td>         -</td>\n</tr>\n<tr>\n<td>ERP 6.0<br/>(IS-UT 600)</td>\n<td>1645476 (B)</td>\n<td>1645476 (B)</td>\n<td>1645476 (B)</td>\n<td>1267871 (B)</td>\n<td>1267871 (B)</td>\n<td>1006196 (A)</td>\n<td>1006196 (A)</td>\n<td>         -</td>\n</tr>\n<tr>\n<td>CEEISUT 472</td>\n<td>(IS-U/CCS or IS-T 472)</td>\n<td>1645477 (C)</td>\n<td>1645477 (C)</td>\n<td>1645477 (C)</td>\n<td>1267871 (C)</td>\n<td>1267871 (C)</td>\n<td>1006196 (C)</td>\n<td>1006196 (C)</td>\n<td>1006196 (C)</td>\n</tr>\n</tbody>\n</table></div>\n<p>Upgrade Scenarios:</p>\n<p>(A) Enhancement package update + keeping the add-on version</p>\n<p>(B) Add-on upgrade combined with an enhancement package update</p>\n<p>(C) Integrated in R/3 upgrade</p>\n<p>The SAP IS-UT CEE add-on upgrade packages:</p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"3\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td rowspan=\"2\">\n<p>CEEISUT add-on<br/>Source Release</p>\n</td>\n<td colspan=\"4\">CEEISUT add-on target release</td>\n</tr>\n<tr>\n<td>CEEISUT 606</td>\n<td> CEEISUT 604</td>\n<td>CEEISUT 600 </td>\n<td>CEEISUT 472 </td>\n</tr>\n<tr>\n<td>CEEISUT 604</td>\n<td>AOI (SAPK-606AGINCEEISUT)</td>\n<td> -</td>\n<td> -</td>\n<td> -</td>\n</tr>\n<tr>\n<td>CEEISUT 600</td>\n<td>AOI (SAPK-606AGINCEEISUT)</td>\n<td>AOU (SAPK-604BGINCEEISUT)</td>\n<td> -</td>\n<td> -</td>\n</tr>\n<tr>\n<td>CEEISUT 472</td>\n<td>AOX (SAPK-606GGINCEEISUT)</td>\n<td>AOX (SAPK-604GGINCEEISUT)</td>\n<td>AOX (SAPK-600I2INCEEISUT)</td>\n<td> -</td>\n</tr>\n<tr>\n<td>4.64 transports</td>\n<td>AOI (SAPK-606AGINCEEISUT)</td>\n<td>AOI (SAPK-604AGINCEEISUT)</td>\n<td>AOI (SAPK-600I1INCEEISUT)</td>\n<td>AOI (SAPK-472I1INCEEISUT)</td>\n</tr>\n</tbody>\n</table></div>\n<p>AOI: CEEISUT add-on installation package<br/>AOX: CEEISUT add-on exchange package<br/>AOU: CEEISUT add-on upgrade package</p>\n<ol></ol>\n<p><strong>V. Combination with other R/3 add-ons</strong></p>\n<p><br/><strong>Combination with other Add-ons</strong></p>\n<p>A combination of SAP IS-UT CEE (CEEISUT) with other R/3 add-ons is not automatically released. Following are the add-ons released with CEEISUT add-on:</p>\n<p>-------------------------------------------------------------------------------------<br/>SAP SFINANCIALS 2.0 (SAP_FIN 720)<br/>SAP SFINANCIALS 1.0 (SAP_FIN 700, EA-FIN 700)<br/>SAP REAL ESTATE CEE (CEERE)<br/>SAP CORE CEE (C-CEE)<br/>SAP TREASURY &amp; RISK M USA (GSTRMUS)<br/>SAP ERP EA-FS - LOCRU (GSEAFS)<br/>SAP HR-CEE (HR-CEE)<br/>SAP HX-CEE (HX-CEE)<br/>-------------------------------------------------------------------------------------</p>\n<p>Compatibility with other add-ons is not tested and thus not guaranteed!<br/><br/>You must note the release strategy notes of the other add-ons (in particular restrictions for supporting Support Packages) if you are planning to use them together in an R/3 System. In case of doubts send please a Customer message on XX-SER-REL component.</p>\n<p><strong>Combination with ERP Enhance Packages (EhPs)</strong></p>\n<ul>\n<li>A combination of CEEISUT add-on with ERP Enhance Packages is not automatically released for any SAP_APPL specific instance of the ERP Enhance Package. It means that:</li>\n<li>If CEEISUT add-on have already been installed then the SAP_APPL sw component can be upgraded only to those EhP versions (e.g. SAP_APPL 602,..) for which a corresponding CEEISUT add-on version have already been released (see the table below)</li>\n<li>The CEEISUT add-on can be installed to a system where an EhP specific version of SAP_APPL component has already been applied only if the combination is allowed by the table below.</li>\n<li>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"3\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td>CEEISUT rel.</td>\n<td>ERP EhP</td>\n<td>IS-UT</td>\n<td>Release Date</td>\n<td>Min. SP level</td>\n</tr>\n<tr>\n<td rowspan=\"4\">CEEISUT 606</td>\n<td>ERP 6.0 EhP8</td>\n<td>618</td>\n<td>01.04.2016</td>\n<td>CEEISUT 606 SP 11</td>\n</tr>\n<tr>\n<td>ERP 6.0 EhP7</td>\n<td>617</td>\n<td>19.12.2013</td>\n<td>CEEISUT 606 SP 07</td>\n</tr>\n<tr>\n<td>ERP 6.0 EhP6 ON HANA</td>\n<td>616</td>\n<td colspan=\"2\">Not planned</td>\n</tr>\n<tr>\n<td>ERP 6.0 EhP6</td>\n<td>606</td>\n<td>08.12.2011</td>\n<td>CEEISUT 606 SP 00</td>\n</tr>\n<tr>\n<td rowspan=\"2\">CEEISUT 604</td>\n<td>ERP 6.0 EhP5</td>\n<td>605</td>\n<td>04.10.2010 </td>\n<td>CEEISUT 604 SP 05</td>\n</tr>\n<tr>\n<td>ERP 6.0 EhP4</td>\n<td>604</td>\n<td>24.03.2009</td>\n<td>CEEISUT 604 SP 00</td>\n</tr>\n<tr>\n<td rowspan=\"2\">CEEISUT 600</td>\n<td>ERP 6.0 EhP3</td>\n<td>603</td>\n<td>15.05.2008</td>\n<td>CEEISUT 600 SP 10</td>\n</tr>\n<tr>\n<td>ERP 6.0 EhP2</td>\n<td>602</td>\n<td>12.03.2008</td>\n<td>CEEISUT 600 SP 10</td>\n</tr>\n</tbody>\n</table></div>\n</li>\n</ul>\n<p>      * Minimal SP level is specified specially when an existing add-on version is release for additional version of the underlying sw. component. In that case import conditions of the add-on packages are updated accordingly via ACP package which is shipped with a specific add-on SP.</p>\n<p><strong>VI. Maintenance of SAP IS-UT CEE</strong></p>\n<ul>\n<li>Add-on corrections for release 4.72 and higher are performed as Add-on Support Packages (AOPs).You find more information on the topic Support Packages in notes described below:</li>\n<li>Add-on corrections for release 4.64: Corrections are made as new versions of transports.</li>\n<li>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"3\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td>CEEISUT rel.</td>\n<td>Note</td>\n<td>Maint.end</td>\n</tr>\n<tr>\n<td>CEEISUT 606</td>\n<td>1660394</td>\n<td>31.12.2027</td>\n</tr>\n<tr>\n<td>CEEISUT 604</td>\n<td>318062 </td>\n<td>31.12.2025</td>\n</tr>\n<tr>\n<td>CEEISUT 600</td>\n<td>1014951 </td>\n<td>31.12.2025</td>\n</tr>\n<tr>\n<td>CEEISUT 472</td>\n<td>1004617 </td>\n<td>31.03.2013</td>\n</tr>\n<tr>\n<td>464 transports</td>\n<td>see attached</td>\n<td>31.03.2013 </td>\n</tr>\n</tbody>\n</table></div>\n</li>\n<li>\n<p>The SAP IS-UT CEE maintenance periods copy the maintenance periods of the SAP IS-U and IS-T releases. This will be held also for future add-on releases.</p>\n</li>\n</ul>\n<p><strong>VII. Country specific documentation</strong></p>\n<p>Check the website service.sap. com/globalization - Country information - Country specific documentation for the actual functional description of a particular country version.</p>\n<p><strong>VIII.Reintegration of CEEISUT</strong></p>\n<p>The CEEISUT add-on has been retrofitted to the core as of SAP S/4HANA 1610 (FI-CA 801 / IS-UT 801).</p></div>", "noteVersion": 26, "refer_note": [{"note": "1494352", "noteTitle": "1494352 - Slovenian specific functionality for IS-UT 604", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>Slovenian specific functionality for IS-UT 604</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>IS-U, IS-T, FI-CA</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>Country specific functionality for Slovenia for IS-UT 604 or 606 delivered in add-on SAP IS-UT CEE 604 or 606. See related note 1014997 for release strategy, installation and upgrade for this add-on. Please implement the highest add-on support patch available for SAP IS-UT CEE 604. The minimal SP level required for Slovenian functionality is SP 06 for SAP IS-UT CEE 604 or SAP IS-UT CEE release 606.<br/><br/>The functionality listed below is currently available for<br/>Slovenia. See related notes and attached documentation for details about delivered functionality and customizing possibilities.<br/><br/>The solution contains the following functionality:<br/>- Non-delivered credit memos - see note 804311<br/>- Tax reporting<br/>- SEPA Direct Debit and SEPA Credit Transfer - see related notes. For SEPA CT, the s add-on SAP CORE CEE must be installed.<br/><br/>The solution for EC Sales List and Report for Goods and Deliveries in FI was extended so that it can read and display data from FI-CA. It is delivered as before in add-on SAP CORE CEE, see notes 1404786 and 1422489.<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>1. Install the the add-on SAP IS-UT CEE as described in Note 1014997<br/>2. Install required SPs<br/><br/>For the documentation please see related notes and read the attached PDF file.<br/><br/>Important!<br/><br/>You must activate Business Configuration (BC) Set /CEEISUT/ISU_SI_01   before you post any documents for the reporting period for which you want to prepare the Tax Report. This is required to generate the report with correct business partner transaction data.<br/><br/>You must activate Business Configuration (BC) Set /CEEISUT/ISU_SI_02   before you post any credit memo which is relevant for the functionality of Non-delivered credit memos.<br/><br/>It is recommended to activate hierarchical BC set /CEEISUT/ISU_SI (if you activate this BC set then the system activates the BC sets mentioned above as well).<br/><br/>For more information about how to activate Business Configuration sets, see Activate BC Sets on SAP Help Portal at http://help.sap.com.<br/><br/>Note:</p> <ul><li>There are no modifications of the SAP standard objects delivered in add-on SAP IS-UT CEE (software component CEEISUT)</li></ul> <ul><li>The enhancements are using reserved name spaces /SAPCE/* and /CEEISUT/* which do not overlap with customer name range or other project development (according to note 72483)</li></ul> <ul><li>These enhancements should not interfere with any add-on support packages (AOP) for IS-UT.</li></ul> <ul><li>Local support is responsible for maintenance and hotline support of these enhancements</li></ul> <p><br/></p></div>", "noteVersion": 4}, {"note": "1645476", "noteTitle": "1645476 - CEEISUT 606:Installation on ERP 6.0", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You want to use Software Update Manager (SUM) or transaction SAINT to perform an Add-on installation or delta upgrade.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>SAINT, SAPehpi, SUM, Add-on, CEEISUT 606, SAPK-606AGINCEEISUT,<br/>CD51042058</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>You want to perform an Add-on installation or a delta upgrade on SAP ERP 6.0  with Enhancement Package 6 for SAP ERP 6.0 or on SAP ERP 6.0 with Enhancement Package 7 for SAP ERP 6.0 or on SAP ERP 6.0 with Enhancement Package 8 for SAP ERP 6.0</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong>This note is updated on a regular basis. Make sure you have the current version of this note before you start the installation.</strong><br/><br/>Contents<br/>  1. Change history<br/>  2. Prerequisites for installing CEEISUT 606<br/>  3. Release of CEEISUT 606 with SAP Enhancement Packages<br/>  4. Preparing the installation/delta upgrade<br/>  5. Performing the installation/delta upgrade<br/>    a. SUM<br/>    b. SAINT<br/>  6. Known errors<br/>  7. After the installation of CEEISUT 606<br/>  8. Language support<br/><br/></p>\n<p><strong>1. Change history</strong></p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"3\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td>Date</td>\n<td>Topic</td>\n<td>Short description</td>\n</tr>\n<tr>\n<td>01-Apr-2016</td>\n<td> </td>\n<td>Release for EHP8</td>\n</tr>\n<tr>\n<td>17-Dec-2013</td>\n<td> </td>\n<td>Release for EHP7</td>\n</tr>\n<tr>\n<td>08-Dec-2011</td>\n<td> </td>\n<td>Initial release of note</td>\n</tr>\n</tbody>\n</table></div>\n<p><strong>2. Prerequisites for installing CEEISUT 606</strong></p>\n<ul>\n<li>It is not possible to uninstall CEEISUT.<br/>Before you install the CEEISUT, keep in mind that you cannot uninstall ABAP Add-ons. Further restrictions that concern the upgrade and maintenance of your SAP system and that occur as a result of installing an Add-on are described in release strategy note 1014997.</li>\n</ul>\n<ul>\n<ul>\n<li>Obtain the following notes before you begin the installation.</li>\n</ul>\n</ul>\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td>Add-ons: Conditions:</td>\n<td> </td>\n<td>70228</td>\n</tr>\n<tr>\n<td>Release strategy Note:</td>\n<td> </td>\n<td>1014997</td>\n</tr>\n<tr>\n<td>support package note</td>\n<td> </td>\n<td>1660394</td>\n</tr>\n<tr>\n<td>Problems with transaction SAINT:</td>\n<td> </td>\n<td>822380</td>\n</tr>\n</tbody>\n</table></div>\n<ul>\n<li>Required release<br/>CEEISUT 606 requires Enhancement Package 6 for SAP ERP 6.0 or Enhancement Package 7 for SAP ERP 6.0 or Enhancement Package 8 for SAP ERP 6.0.</li>\n</ul>\n<ul>\n<ul>\n<li>Required components and Support Packages<br/><br/>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"3\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td>Component</td>\n<td>Release</td>\n<td>Support Package</td>\n</tr>\n<tr>\n<td>IS-UT</td>\n<td>606</td>\n<td>SAPK-60601INISUT</td>\n</tr>\n<tr>\n<td> </td>\n<td>OR</td>\n<td> </td>\n</tr>\n<tr>\n<td>IS-UT</td>\n<td>617</td>\n<td>SAPK-61702INISUT</td>\n</tr>\n<tr>\n<td> </td>\n<td>OR</td>\n<td> </td>\n</tr>\n<tr>\n<td>IS-UT</td>\n<td>618</td>\n<td> </td>\n</tr>\n</tbody>\n</table></div>\n<p>If you have not yet installed the required Support Packages, you can include them in the installation of CEEISUT 606.</p>\n</li>\n</ul>\n</ul>\n<ul>\n<li>Additional Component Support Packages<br/><br/>The Add-on CEEISUT 606 does not contain modifications. You can also perform the installation if you have already imported more Support Packages into your system than are specified in the section 'Required Components and Support Packages'.<br/><br/>The Add-on CEEISUT 606 requires the Industry Solution(s) Telco or Utilities. Before you install the Add-on, you must activate the relevant Business Function Set. For more information, see notes<br/>874477 (Telco).<br/>874416 (Utilities).</li>\n</ul>\n<ul>\n<li>Additional information about the installation:CD material number Add-on installation: 51042058</li>\n</ul>\n<p><strong>3. Release of CEEISUT 606 with SAP Enhancement Packages</strong></p>\n<ul>\n<li>For information about compatibility with SAP Enhancement Packages, refer to release strategy note 1014997.</li>\n</ul>\n<p><br/><strong>4. Preparing the installation/delta upgrade</strong></p>\n<ul>\n<li>Making the Add-on package available<br/>The Add-on package for CEEISUT 606 is provided on the CD/DVD  with the material number 51042058. Download this CD/DVD  from SAP Service Marketplace or request it from your local subsidiary. For more information about the ordering procedure for SAP software, see Note 925690.</li>\n</ul>\n<ul>\n<li>The required SAR archive is available under the following path:<br/>DATA_UNITS/CEEISUT_606/CEEISUT_606_INST/DATA/K-606AGINCEEISUT.SAR</li>\n</ul>\n<ul>\n<li>Load the Add-on package from there into your SAP system (transaction SPAM -&gt; \"Support Package\"-&gt; \"Load Packages\"-&gt; \"From Front End\").</li>\n</ul>\n<p><strong>5. Performing the installation/delta upgrade</strong><br/><br/></p>\n<ul>\n<li>Including CEEISUT 606 in the installation of Enhancement Packages with SUM.</li>\n</ul>\n<ul>\n<ul>\n<li><strong>If you have already installed the required Enhancement Package, you can install or upgrade the Add-on only via SAINT (see next section) .</strong></li>\n</ul>\n</ul>\n<ul>\n<ul>\n<li>The general approach for the installation using SUM is described in the guides for Enhancement Package 6 for  SAP ERP 6.0 at https://service.sap.com/erp-inst.</li>\n</ul>\n</ul>\n<ul>\n<ul>\n<li>Make sure that you use at least SUM Version 1.0 SP02.</li>\n</ul>\n</ul>\n<ul>\n<ul>\n<li>Add-on installation<br/>In the maintenance optimizer transaction (MOPZ), select the relevant Add-on product version for CEEISUT 606: \"SAP IS-UT CEE 6.06\"  at: \"Maintenance optimizer transaction\" -&gt; \"Choose Add-on Products\"</li>\n</ul>\n</ul>\n<ul>\n<ul>\n<li>Add-on delta upgrade.<br/>In the maintenance optimizer, the system recognizes and displays a version of CEEISUT that was already installed. Select the relevant Add-on product version for CEEISUT 606: \"SAP IS-UT CEE 6.06\" as the target.</li>\n</ul>\n</ul>\n<ul>\n<li>Installation for CEEISUT 606 with SAINT.</li>\n</ul>\n<ul>\n<ul>\n<li>Call transaction SAINT and choose 'Start'.</li>\n</ul>\n</ul>\n<ul>\n<ul>\n<li>Select the Add-on CEEISUT 606 and choose \"Continue\". If all of the necessary conditions for importing the Add-on have been fulfilled, the system will now display the relevant queue. The queue consists of the Add-on package and can also contain Support Packages and other Add-on packages. To start the installation process, choose 'Continue'.</li>\n</ul>\n</ul>\n<ul>\n<ul>\n<li>For more information, call transaction SAINT and choose \"Info\" on the application toolbar.</li>\n</ul>\n</ul>\n<ul>\n<ul>\n<li>The system prompts you to enter a password for CEEISUT 606. The password is: B2CD895E20</li>\n</ul>\n</ul>\n<p><strong>6. Known errors</strong></p>\n<ul>\n<li>Generation errors<br/>The existing installation package of the Add-on CEEISUT 606 contains the following generation errors:</li>\n</ul>\n<ul>\n<ul>\n<li>Program /SAPCE/SAPLFK_TFKFBC, Include /SAPCE/LFK_TFKFBCT00: Syntax error The Dictionary structure or table '*T5T52' is either not active or does not exist.</li>\n</ul>\n</ul>\n<ul>\n<ul>\n<li>Program /SAPCE/FK_CZ_MOVE: Syntax error in line 000021 The Dictionary structure or table 'T5T52' is either not active or does not exist.</li>\n</ul>\n</ul>\n<ul>\n<ul>\n<li>Program /SAPCE/FK_SK_EVYBER: Syntax error in line 000020 Field 'T7SK52' is unknown.</li>\n</ul>\n</ul>\n<ul>\n<ul>\n<li>Program /SAPCE/FK_SK_SEPOTV: Syntax error in line 000014 Field 'T7SK52' is unknown.<br/><br/>Please check note 775544 if this functionalty is relevant for your solution.  If so, please implement this note.</li>\n</ul>\n</ul>\n<p><strong>7. After the installation of CEEISUT 606</strong></p>\n<ul>\n<li>Additional information about CEEISUT 606.<br/><br/>You can import Support Packages of the other components that CEEISUT606 does not modify without CRTs.</li>\n</ul>\n<p><strong>7. Language support</strong></p>\n<ul>\n<li>CEEISUT 606 supports the following languages:<br/>Romanian, Slovenian, Ukrainian, Czech, German, English, Greek, Hungarian, Polish, Slovakian, Russian, Turkish, Bulgarian.<br/>The languages are contained in the Add-on package, and you do not need to import any additional language packages.</li>\n</ul>\n<ul>\n<li>For information about subsequently installing further languages in your system, see Note 195442.</li>\n</ul>", "noteVersion": 6}, {"note": "1318062", "noteTitle": "1318062 - CEEISUT 604: Component Support Packages", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>Information about Component support packages for CEEISUT 604</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>support packages, add-on,AOP, CSP, CRT, SPAM</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>You have a system with the CEEISUT add-on installation.<br/>Component support packages (CSPs) contain corrections and fixes for the CEEISUT installation or previous component support packages.<br/>This note contains information about component support packages for the CEEISUT component.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong>Content</strong></p>\n<p>1. Overview of the Add-On Support Packages<br/>2. Preparing the import of Support Packages<br/>3. Problems while importing add-on support packages<br/>4. Postprocessing after importing Support Packages</p>\n<p><strong><strong>CAUTION: This note is updated constantly.</strong><br/>Before you import add-on support packages, read the current version of this note.</strong></p>\n<p><strong>1. Overview of the Add-On Support Packages</strong></p>\n<p>CEEISUT 604 support packages may be dependent on SAP IS-UT packages. You can find a schedule when to expect support packages in this table:</p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"0\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td>\n<p><strong>Support Package</strong></p>\n</td>\n<td>\n<p><strong>Date</strong></p>\n</td>\n<td>\n<p><strong>Comment</strong></p>\n</td>\n</tr>\n<tr>\n<td>\n<p>SAPK-60427INCEEISUT</p>\n</td>\n<td>\n<p>22.10.2018</p>\n</td>\n<td> </td>\n</tr>\n<tr>\n<td>\n<p>SAPK-60426INCEEISUT</p>\n</td>\n<td>\n<p>23.04.2018</p>\n</td>\n<td> </td>\n</tr>\n<tr>\n<td>\n<p>SAPK-60425INCEEISUT</p>\n</td>\n<td>\n<p>23.10.2017</p>\n</td>\n<td> </td>\n</tr>\n<tr>\n<td>\n<p>SAPK-60424INCEEISUT</p>\n</td>\n<td>\n<p>24.04.2017</p>\n</td>\n<td>requires IS-UT 604 SP18 or 605 SP 15 </td>\n</tr>\n<tr>\n<td>\n<p>SAPK-60423INCEEISUT</p>\n</td>\n<td>\n<p>24.04.2017</p>\n</td>\n<td><strong>Its mandatory that SP 23/24 must be installed together.</strong></td>\n</tr>\n<tr>\n<td>\n<p>SAPK-60422INCEEISUT</p>\n</td>\n<td>\n<p>25.04.2016</p>\n</td>\n<td> </td>\n</tr>\n<tr>\n<td>\n<p>SAPK-60421INCEEISUT</p>\n</td>\n<td>\n<p> 11.09.2015</p>\n</td>\n<td>\n<p> </p>\n</td>\n</tr>\n<tr>\n<td>\n<p>SAPK-60420INCEEISUT</p>\n</td>\n<td>\n<p>21.04.2015</p>\n</td>\n<td>\n<p> </p>\n</td>\n</tr>\n<tr>\n<td>\n<p>SAPK-60419INCEEISUT</p>\n</td>\n<td>\n<p>10.10.2014</p>\n</td>\n<td>\n<p> </p>\n</td>\n</tr>\n<tr>\n<td>\n<p>SAPK-60418INCEEISUT</p>\n</td>\n<td>\n<p>21.05.2014</p>\n</td>\n<td>\n<p>requires SAPK-60414ISUT</p>\n</td>\n</tr>\n<tr>\n<td>\n<p>SAPK-60417INCEEISUT</p>\n</td>\n<td>\n<p>24.01.2014</p>\n</td>\n<td> </td>\n</tr>\n<tr>\n<td>\n<p>SAPK-60416INCEEISUT</p>\n</td>\n<td>\n<p>24.09.2013</p>\n</td>\n<td> </td>\n</tr>\n<tr>\n<td>\n<p>SAPK-60415INCEEISUT</p>\n</td>\n<td>\n<p>13.06.2013</p>\n</td>\n<td> </td>\n</tr>\n<tr>\n<td>\n<p>SAPK-60414INCEEISUT</p>\n</td>\n<td>\n<p>23.01.2013</p>\n</td>\n<td> </td>\n</tr>\n<tr>\n<td>\n<p>SAPK-60413INCEEISUT</p>\n</td>\n<td>\n<p>25.09.2012</p>\n</td>\n<td> </td>\n</tr>\n<tr>\n<td>\n<p>SAPK-60412INCEEISUT</p>\n</td>\n<td>\n<p>29.06.2012</p>\n</td>\n<td>\n<p>translation only</p>\n</td>\n</tr>\n<tr>\n<td>\n<p>SAPK-60411INCEEISUT</p>\n</td>\n<td>\n<p>23.05.2012</p>\n</td>\n<td> </td>\n</tr>\n<tr>\n<td>\n<p>SAPK-60410INCEEISUT</p>\n</td>\n<td>\n<p>26.01.2012</p>\n</td>\n<td> </td>\n</tr>\n<tr>\n<td>\n<p>SAPK-60409INCEEISUT</p>\n</td>\n<td>\n<p>24.10.2011</p>\n</td>\n<td>\n<p>Greek language added</p>\n</td>\n</tr>\n<tr>\n<td>\n<p>SAPK-60408INCEEISUT</p>\n</td>\n<td>\n<p>25.05.2011</p>\n</td>\n<td> </td>\n</tr>\n<tr>\n<td>\n<p>SAPK-60407INCEEISUT</p>\n</td>\n<td>\n<p>25.01.2011</p>\n</td>\n<td> </td>\n</tr>\n<tr>\n<td>\n<p>SAPK-60406INCEEISUT</p>\n</td>\n<td>\n<p>22.10.2010</p>\n</td>\n<td> </td>\n</tr>\n<tr>\n<td>\n<p>SAPK-60405INCEEISUT</p>\n</td>\n<td>\n<p>24.09.2010</p>\n</td>\n<td> </td>\n</tr>\n<tr>\n<td>\n<p>SAPK-60404INCEEISUT</p>\n</td>\n<td>\n<p>25.05.2010</p>\n</td>\n<td> </td>\n</tr>\n<tr>\n<td>\n<p>SAPK-60403INCEEISUT</p>\n</td>\n<td>\n<p>25.01.2010</p>\n</td>\n<td> </td>\n</tr>\n<tr>\n<td>\n<p>SAPK-60402INCEEISUT</p>\n</td>\n<td>\n<p>28.09.2009</p>\n</td>\n<td> </td>\n</tr>\n<tr>\n<td>\n<p>SAPK-60401INCEEISUT</p>\n</td>\n<td>\n<p>25.05.2009</p>\n</td>\n<td> </td>\n</tr>\n</tbody>\n</table></div>\n<p><strong>2. Preparing the import of Support Packages.</strong></p>\n<ul>\n<li>Import the latest SPAM/SAINT update.<br/>Make sure that you have installed the latest SPAM/SAINT update on your system. If a newer version is available on SAP Service Marketplace, import the new SPAM/SAINT update.</li>\n<li>Import the latest R3trans and tp.<br/>Ensure that you have imported the latest kernel version into your system. If a newer version is available on SAP Service Marketplace, import the most recent kernel.</li>\n<li>Load all the packages you require from SAP Service Marketplace.<br/>This includes the packages for the add-on itself as well as the required standard Support Packages for CRTs.<br/>The Support Packages for C-CEE 110_602 are available on SAP Service Marketplace:<br/>http://service.sap.com/swdc<br/>-&gt; Download<br/>-&gt; Support Packages and Patches<br/>-&gt; Entry by Application Group<br/>-&gt; Country-specific Add-Ons and Legal Changes<br/>-&gt; SAP IS-UT CEE<br/>-&gt; SAP IS-UT CEE 604<br/><br/>Transfer the SAR archive to your local front-end directory.</li>\n<ul>\n<li>Log on to your SAP system in client 000 as a user with SAP_ALL authorization. Do NOT use the SAP* or DDIC users.</li>\n</ul>\n<ul>\n<li>Transfer the packages to your EPS inbox, by going to transaction SPAM and selecting \"Support Packages -&gt; Load Packages -&gt; From the Frontend\" from the menu.<br/>The system unpacks the packages automatically.</li>\n</ul>\n<ul>\n<li>Displaying packages:<br/>Choose \"Display\" for \"New Support Packages\".</li>\n</ul>\n</ul>\n<p><strong>III. Problems while importing add-on support packages</strong></p>\n<ol>\n<li>Generation errors</li>\n<ol>\n<li>SAPK-60417INCEEISUT<br/>These generation errors might occur. To solve that, see IV.1 below.<br/>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"3\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td>Program /SAPCE/SAPLFKSK_EVENTS, Include /SAPCE/LFKSK_EVENTS$03: Syntax error in line 000008<br/>Type 'FKKOPREL_T' is unknown</td>\n</tr>\n</tbody>\n</table></div>\n<br/>.</li>\n<li>SAPK-60418INCEEISUT<br/>These generation errors might occur. To solve that, see IV.2 below.<br/>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"3\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td> Program /SAPCE/SAPLFKSK_EVENTS, Include /SAPCE/LFKSK_EVENTSU04: Syntax error in line 000027<br/> Type 'FKKSK_STR_SDATA' is unknown</td>\n</tr>\n</tbody>\n</table></div>\n<br/>.</li>\n<li>SAPK-60420INCEEISUT<br/>These generation errors might occur. To solve that, see IV.3. below.\r\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"3\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td>\n<p>Program /SAPCE/FK_SK_EVYBER: Syntax error in line 000021<br/>Field 'T7SK52' is unknown. It is neither in one of the specified tables nor defined by a 'DATA' stat</p>\n<p>Program /SAPCE/CL_EDOCUMENT_ISU_SVN===CP, Include /SAPCE/CL_EDOCUMENT_ISU_SVN===CU: Syntax error in line 000003<br/>Type 'CL_EDOCUMENT_SVN' is unknown</p>\n</td>\n</tr>\n</tbody>\n</table></div>\n</li>\n<li>SAPK-60422INCEEISUT<br/>These generation errors might occur. To solve error 1, see IV.3. below. To solve error 2, see the note <strong>2228198</strong><br/>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"3\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td>\n<p>1)Program /SAPCE/FK_SK_EVYBER: Syntax error in line 000031<br/>Field 'T7SK52' is unknown. It is neither in one of the specified tables nor defined by a 'DATA' stat</p>\n<p>2)Program /SAPCE/IUHU_CL_AUDIT_FUNC===CP, Include /SAPCE/IUHU_CL_AUDIT_FUNC===CU: Syntax error in line 000011<br/>Type 'IF_EX_HU_AUDIT_REPORT' is unknown</p>\n</td>\n</tr>\n</tbody>\n</table></div>\n</li>\n</ol></ol>\n<p><strong>IV. Postprocessing the import of add-on support packages</strong></p>\n<ol>\n<li>SAPK-60418INCEEISUT<br/>The support package requires the following FI-CA notes if you are using the Slovakian localization:<br/><br/>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"3\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"80\">\n<p>1830669</p>\n</td>\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"644\">\n<p>FKK_ADDITIONAL_DOC_DATA_GET downport</p>\n</td>\n</tr>\n<tr>\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"80\">\n<p>1942466</p>\n</td>\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"644\">\n<p>SK FI-CA Electronic VAT Ledger</p>\n</td>\n</tr>\n<tr>\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"80\">\n<p>1954895</p>\n</td>\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"644\">\n<p>SK FI-CA Electronic VAT Ledger</p>\n</td>\n</tr>\n<tr>\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"80\">\n<p>1955535</p>\n</td>\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"644\">\n<p>Correction of SK FI-CA VAT Electronic Ledger</p>\n</td>\n</tr>\n</tbody>\n</table></div>\n</li>\n<li>SAPK-60418INCEEISUT<br/>The support package requires the following FI-CA notes if you are using the Slovakian localization:<br/><br/>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"3\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"80\">\n<p>1942466</p>\n</td>\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"644\">\n<p>SK FI-CA Electronic VAT Ledger</p>\n</td>\n</tr>\n<tr>\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"80\">\n<p>1954895</p>\n</td>\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"644\">\n<p>SK FI-CA Electronic VAT Ledger</p>\n</td>\n</tr>\n<tr>\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"80\">\n<p>1958171</p>\n</td>\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"644\">\n<p>Slovakia: Authorization Object F_KKID_SK cannot be created in customer environment</p>\n</td>\n</tr>\n<tr>\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"80\">\n<p>1955535</p>\n</td>\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"644\">\n<p>Correction of SK FI-CA VAT Electronic Ledger</p>\n</td>\n</tr>\n<tr>\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"80\">\n<p>1957599</p>\n</td>\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"644\">\n<p>Correction #2 SK FI-CA Electronic VAT Ledger</p>\n</td>\n</tr>\n<tr>\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"80\">\n<p>1960475</p>\n</td>\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"644\">\n<p>Correction #3 SK FI-CA Electronic VAT Ledger</p>\n</td>\n</tr>\n<tr>\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"80\">\n<p>1961275</p>\n</td>\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"644\">\n<p>Correction #4 SK FI-CA Electronic VAT Ledger</p>\n</td>\n</tr>\n<tr>\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"80\">\n<p>1960997</p>\n</td>\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"644\">\n<p>Correction #5 SK FI-CA Electronic VAT Ledger</p>\n</td>\n</tr>\n</tbody>\n</table></div>\r\n.<br/><br/><br/></li>\n<li>SAPK-60420INCEEISUT<br/>If you are using the Czech bank interface for IS-U and IS-T you need to apply HR-CEE 110_604 to the system. See note 775544<br/>The support package requires the following FI-CA notes if you are using the Slovakian localization: 2005304, 1995867, 2021860, 2042944</li>\n</ol>", "noteVersion": 35}, {"note": "489912", "noteTitle": "489912 - Czech specific functionality IS Utilities,collective note", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3> <p>relevant documentation for the Czech Republic  IS-Utilities industry<br/>solution.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3> <p></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3> <p>from the notes that can be tracked down in the following way: Once you are in the notes searching display, in the \"Rel.from\" field, entry the number of appropriate release (e.g. 46C) and in the \"Component field\" entry XX-CSC-CZ-IS-U.<br/> <br/>1.1.  Prices including VAT<br/>1.2.  Delayed clearing in payment run<br/>1.3.  Doubtful receivables and write offs<br/>1.4.  VAT calculation using a coefficient (from price including VAT)<br/>1.5.  Statistical budget billing procedure (TAX code for BB)<br/>1.6.  ---<br/>1.7.  Banking CZ<br/>1.8.  Banking  - using SIPO method<br/>1.10. Budget billing copies real consumption<br/>1.11. More than one budget bills in one period (industrial customers)<br/>1.12. Interval meter reading every 1/4 hour<br/>1.13. Budget billing calculation for the next period<br/>1.18. Customizing tg phi / cos phi<br/>1.27. Dunning for incoming payment methods<br/>1.31. Rounding of invoice without carry forward of difference<br/><br/>See also list of related notes.</p></div>", "noteVersion": 3}, {"note": "1866443", "noteTitle": "1866443 - CEEISUT Add-on : IS-U Localization for Poland", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>This localization of IS-U for Poland includes solutions for:<br/>1. IS-Utilities Localization: Budget Billing and Invoicing<br/>2. Contract Accounts Receivable and Payable localization<br/><br/><b>IS-Utilities Localization: Budget Billing and Invoicing </b><br/>Following functionalities are supported:<br/>1. Budget Billing for Small customers<br/>2. Budget Billing for Large customers<br/>3. Additional information in invoice printing<br/>4. Correction invoices<br/>5. Charges for Excessive Reactive power consumption<br/>For further details on individual topics please refer to note 1799113.<br/><br/><b>Contract Accounts Receivable and Payable localization</b><br/>Following functionalities are supported:<br/>1. VAT Register report<br/>2. Business Partner Ledger report<br/>3. Trial Balance report<br/>4. Enhancement of Account Balance transaction FPL9<br/>5. Tax adjustment for Overdue Invoices<br/>For further details on individual topics please refer to notes 1833248, 1891879 and 1864011.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>Poland, IS-UT, CEE, Budget Billing, Correction invoice, Localization, CEEISUT</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>To Meet legal and business requirements for Poland</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>The solution is delivered in SP05 of add-on SAP IS-UT CEE 6.06<br/>The solution is attached to the business function /SAPCE/ISU_INV_PL.<br/><br/>Please refer to following notes for further details:<br/><b>Note: 1799113</b>: IS-U Localization for Budget Billing and Invoicing<br/><b>Note: 1833248</b>: FI-CA CEEISUT: VAT &amp; BP Reports Localization Poland<br/><b>Note: 1864011</b>: FI-CA CEEISUT:FPL9 Icon \"Correction of ISU invoice to zero\"</p></div>", "noteVersion": 2}, {"note": "1014953", "noteTitle": "1014953 - Ukrainian specific functionality for IS-UT 600 (collective)", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>Ukrainian specific functionality for IS-UT 600 (collective)</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>Country specific functionality for Ukraine for IS-UT 600 is delivered in add-on CEEISUT 600. See related notes 1005317 and 1006196 for information about installation and upgrade for this add-on. Please implement the highest add-on support patch available for CEEISUT600. See related note 1014951 for corresponding AOPs.<br/><br/>The functionality listed in related notes is currently available for Ukraine. See related notes and/or attached documentation for details about delivered functionality and customizing possibilities.<br/><br/><br/>New BC Sets were delivered as part of the localization, which have to be activated in your system. Either activate hierarchical BC set<br/>/CEEISUT/ISU_UA or BC sets for individual topics.<br/><br/>Note:</p> <ul><li>There are no modifications of the SAP standard objects delivered in add-on CEEISUT</li></ul> <ul><li>The enhancements are using reserved name spaces /SAPCE/* and /CEEISUT/* which do not overlap with customer name range or other project development (according to note 72483)</li></ul> <ul><li>These enhancements should not interfere with any add-on support packages (AOP) for IS-UT.</li></ul> <ul><li>Local support is responsible for maintenance and hotline support of these enhancements</li></ul> <ul><li>These enhancements are not released for any other country</li></ul></div>", "noteVersion": 1}, {"note": "1014660", "noteTitle": "1014660 - Romanian specific functionality for IS-UT 600 (collective)", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>Romanian specific functionality for IS-UT 600 (collective)</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>Country specific functionality for Romania for IS-UT 600 is delivered in add-on CEEISUT 600. See related notes 1005317 and 1006196 for information about installation and upgrade for this add-on. Please implement the highest add-on support patch available for CEEISUT 600. See related note 1014951 for corresponding AOPs.<br/><br/>The functionality listed in related notes is currently available for Romania. See related notes and/or attached documentation for details about delivered functionality and customizing possibilities.<br/><br/>Note:</p> <ul><li>There are no modifications of the SAP standard objects delivered in add-on CEEISUT</li></ul> <ul><li>The enhancements are using reserved name spaces /SAPCE/* and /CEEISUT/* which do not overlap with customer name range or other project development (according to note 72483)</li></ul> <ul><li>These enhancements should not interfere with any add-on support packages (AOP) for IS-UT.</li></ul> <ul><li>Local support is responsible for maintenance and hotline support of these enhancements</li></ul> <ul><li>These enhancements are not released for any other country</li></ul></div>", "noteVersion": 1}, {"note": "774624", "noteTitle": "774624 - Czech & Slovak specific functionality IS-U 4.72 (collective)", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>Czech &amp; Slovak specific functionality IS-U/CCS release 4.72 (collective)</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>Country specific functionality for Czech Republic and Slovakia for IS-U/CCS is delivered in add-on CEEISUT for release 4.72.<br/>See related note 774230 for information about availability and installation rules of this add-on.<br/><br/>See other related notes for details about delivered functionality, customizing and documentation.<br/><br/>Note:</p> <ul><li>There are no modifications of the SAP standard objects</li></ul> <ul><li>The enhancements are using reserved name spaces /SAPCE/* and /CEEISUT/* which do not overlap with customer name range or other project development (according to note 72483)</li></ul> <ul><li>These enhancements should not interfere with any hot packages or any (AOP) for IS-U/CCS.</li></ul> <ul><li>SAP CR, Prague is responsible for maintenance and hotline support of those enhancements for Czech Republic and Slovakia only</li></ul> <ul><li>These enhancements are not released for any other country</li></ul></div>", "noteVersion": 4}, {"note": "1012274", "noteTitle": "1012274 - Bulgarian specific functionality IS-U 4.72 (collective)", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>Bulgarian specific functionality for IS-U release 4.72 (collective)</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>Country specific functionality for Bulgaria for IS-U is delivered in add-on CEEISUT for release 4.72. See related note 774230 for information about availability and installation rules of this add-on. Please implement the highest add-on support patch available for CEEISUT. See related notes for corresponding AOPs.<br/><br/>The functionality listed in related notes is currently available for Bulgaria. See related notes and/or attached documentation for details about delivered functionality and customizing possibilities.<br/><br/>Note:</p> <ul><li>There are no modifications of the SAP standard objects delivered in add-on CEEISUT</li></ul> <ul><li>The enhancements are using reserved name spaces /SAPCE/* and /CEEISUT/* which do not overlap with customer name range or other project development (according to note 72483)</li></ul> <ul><li>These enhancements should not interfere with any add-on support packages (AOP) for IS-U.</li></ul> <ul><li>Local support is responsible for maintenance and hotline support of these enhancements</li></ul> <ul><li>These enhancements are not released for any other country</li></ul></div>", "noteVersion": 1}, {"note": "1014952", "noteTitle": "1014952 - Bulgarian specific functionality for IS-U 600 (collective)", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>Bulgarian specific functionality for IS-UT 600 (collective)</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>Country specific functionality for Bulgaria for IS-UT 600 is delivered in add-on CEEISUT 600. See related notes 1005317 and 1006196 for information about installation and upgrade for this add-on. Please implement the highest add-on support patch available for CEEISUT600. See related note 1014951 for corresponding AOPs.<br/><br/>The functionality listed in related notes is currently available for Bulgaria. See related notes and/or attached documentation for details about delivered functionality and customizing possibilities.<br/><br/><br/>Note:</p> <ul><li>There are no modifications of the SAP standard objects delivered in add-on CEEISUT</li></ul> <ul><li>The enhancements are using reserved name spaces /SAPCE/* and /CEEISUT/* which do not overlap with customer name range or other project development (according to note 72483)</li></ul> <ul><li>These enhancements should not interfere with any add-on support packages (AOP) for IS-UT.</li></ul> <ul><li>Local support is responsible for maintenance and hotline support of these enhancements</li></ul> <ul><li>These enhancements are not released for any other country</li></ul></div>", "noteVersion": 1}, {"note": "1150868", "noteTitle": "1150868 - SAP IS-UT CEE 600 add-on and ERP Enhancement Packages", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>When you want to install SAP IS-UT CEE 600 add-on to a system where an IS-UT instance of ERP Enhancement Packages has already been applied, the system issues an error message in transaction SAINT stating that the import prerequisites are not met.<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>SAP IS-UT CEE 600, CEEISUT 600, IS-UT, ERP enhancement packages, Support Packages, ACP, attribute change package, SPAM, SAINT<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>You have already installed ERP Ehancement Packages for IS-UT and you want to install the SAP IS-UT CEE 600 add-on on. Due to the installation of Enhancement Packages, release of the relevant component has changed (IS-UT 602). Therefore, import conditions for the underlying sw.componet version (IS-UT 600) of the SAP IS-UT CEE 600 add-on (sw. component CEEISUT 600 ) are no longer valid.<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>You can install earlier SAP IS-UT CEE 600 add-on only if the import prerequisites accept the releases of the enhancement packages as valid (IS-UT 600). For this reason, you must download an attribute change package (ACP) in addition to the SAP IS-UT 600 add-on installation package. For more information about attribute change packages, see Note 1119856.<br/><br/>If you want to install SAP IS-UT CEE 600 add-on, proceed as follows:</p> <ul><li>SPAM Version 25 is a prerequisite for the processing of ACPs.</li></ul> <ul><li>Download the file ACP_CEEISUT600.SAR that is attached to this note.</li></ul> <ul><li>Load the contents of this file into the EPS inbox (SPAM &gt; Support Package &gt; Load Packages &gt; From Front End). The archive contains the ACP of CEEISUT 600 component (package CEEISUT===600).</li></ul> <p><br/>Then you can use SAINT to define the required queue.<br/></p></div>", "noteVersion": 2}, {"note": "1014951", "noteTitle": "1014951 - CEEISUT 600: Component Support Packages", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>SAP IS-UT CEE 600 add-on support package description</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Software component CEEISUT</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>You have installed add-on SAP IS-UT CEE 600 for localization of IS-UT for countries in Central and Eastern Europe. See related notes 1005317 and 1006196.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong>Content</strong></p>\n<ol>\n<li>General information</li>\n<ol>\n<li>Downloading Add-On Support Packages</li>\n<li>SPAM update</li>\n<li>Import of Support Packages</li>\n</ol>\n<li>Support Packages available for CEEISUT 600</li>\n<li>Problems with importing Add-On Support Packages</li>\n<li>Postprocessing after the import of add-on support packages</li>\n</ol>\n<p> </p>\n<p><strong>I. General Information</strong></p>\n<p>The content of this note is specific for the Add-On CEEISUT 600.<br/>If you have not installed CEEISUT 600 on your SAP system, this note is not relevant for you.</p>\n<p><strong>CAUTION: This note is updated constantly.</strong><br/>Before you import Add-On Support Packages, read the current version of this note.</p>\n<ol>\n<li>Downloading Add-On Support PackagesAdd-On Support Packages are available in the SAP Service Marketplace:<br/><ol>http://service.sap.com/patches</ol><ol>-&gt; Browse our Download Catalog</ol><ol>-&gt; Country-specific Add-ons</ol><ol>-&gt; SAP IS-UT CEE</ol><ol>-&gt; SAP IS-UT CEE 600</ol><ol>-&gt; Comprised Software Component Versions</ol><ol>-&gt; CEEISUT 600</ol><ol>-&gt; Support Packages</ol></li>\n<li>SPAM update<br/>Make sure that you have imported the latest SPAM/SAINT update into your system. If a newer version is on the SAP Service Marketplace, import the new SPAM/SAINT update.<br/><br/></li>\n<li>Import of Support Packages<br/>Import Support Packages using transaction SPAM. See the corresponding online documentation for more information.</li>\n</ol>\n<p><strong>II. Available Support Packages for CEEISUT 600</strong></p>\n<p>The list below shows the Add-On Support Packages which are available for CEEISUT 600.<br/>More information on Add-On Support Packages is available in Note 160168.</p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"3\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td>\n<p><strong>Support Package </strong></p>\n</td>\n<td>\n<p><strong>  Release Date   </strong></p>\n</td>\n<td>\n<p><strong>  Comment</strong></p>\n</td>\n</tr>\n<tr>\n<td>\n<p>SAPK-60037INCEEISUT</p>\n</td>\n<td>\n<p>22.10.2018</p>\n</td>\n<td> </td>\n</tr>\n<tr>\n<td>\n<p>SAPK-60036INCEEISUT</p>\n</td>\n<td>\n<p>23.04.2018</p>\n</td>\n<td> </td>\n</tr>\n<tr>\n<td>\n<p>SAPK-60035INCEEISUT</p>\n</td>\n<td>\n<p>23.10.2017</p>\n</td>\n<td> </td>\n</tr>\n<tr>\n<td>\n<p>SAPK-60034INCEEISUT</p>\n</td>\n<td>\n<p>24.04.2017</p>\n</td>\n<td>SAPK-60028INISUT or<br/>SAPK-60218INISUT or<br/>SAPK-60317INISUT</td>\n</tr>\n<tr>\n<td>\n<p>SAPK-60033INCEEISUT</p>\n</td>\n<td>\n<p>24.04.2017</p>\n</td>\n<td><strong>Its mandatory that SP 33 and 34 must be installed together.</strong></td>\n</tr>\n<tr>\n<td>\n<p>SAPK-60032INCEEISUT</p>\n</td>\n<td>\n<p>25.04.2016</p>\n</td>\n<td>SAPK-60027INISUT or<br/>SAPK-60217INISUT or<br/>SAPK-60316INISUT</td>\n</tr>\n<tr>\n<td>\n<p>SAPK-60031INCEEISUT</p>\n</td>\n<td>\n<p>11.09.2015</p>\n</td>\n<td> </td>\n</tr>\n<tr>\n<td>\n<p>SAPK-60030INCEEISUT</p>\n</td>\n<td>\n<p>21.04.2015</p>\n</td>\n<td> </td>\n</tr>\n<tr>\n<td>\n<p>SAPK-60029INCEEISUT</p>\n</td>\n<td>\n<p>10.10.2014</p>\n</td>\n<td> </td>\n</tr>\n<tr>\n<td>\n<p>SAPK-60028INCEEISUT</p>\n</td>\n<td>\n<p>21.05.2014</p>\n</td>\n<td>\n<p>SAPK-60024INISUT or<br/>SAPK-60214INISUT or<br/>SAPK-60313INISUT</p>\n</td>\n</tr>\n<tr>\n<td>\n<p>SAPK-60027INCEEISUT</p>\n</td>\n<td>\n<p>23.01.2014</p>\n</td>\n<td> </td>\n</tr>\n<tr>\n<td>\n<p>SAPK-60026INCEEISUT</p>\n</td>\n<td>\n<p>24.09.2013</p>\n</td>\n<td> </td>\n</tr>\n<tr>\n<td>\n<p>SAPK-60025INCEEISUT</p>\n</td>\n<td>\n<p>17.06.2013</p>\n</td>\n<td> </td>\n</tr>\n<tr>\n<td>\n<p>SAPK-60024INCEEISUT</p>\n</td>\n<td>\n<p>25.01.2013</p>\n</td>\n<td> </td>\n</tr>\n<tr>\n<td>\n<p>SAPK-60023INCEEISUT</p>\n</td>\n<td>\n<p>25.09.2012</p>\n</td>\n<td> </td>\n</tr>\n<tr>\n<td>\n<p>SAPK-60022INCEEISUT</p>\n</td>\n<td>\n<p>29.06.2012</p>\n</td>\n<td> </td>\n</tr>\n<tr>\n<td>\n<p>SAPK-60021INCEEISUT</p>\n</td>\n<td>\n<p>30.01.2012</p>\n</td>\n<td> </td>\n</tr>\n<tr>\n<td>\n<p>SAPK-60020INCEEISUT</p>\n</td>\n<td>\n<p>07.12.2011</p>\n</td>\n<td> </td>\n</tr>\n<tr>\n<td>\n<p>SAPK-60019INCEEISUT</p>\n</td>\n<td>\n<p>24.05.2011</p>\n</td>\n<td> </td>\n</tr>\n<tr>\n<td>\n<p>SAPK-60018INCEEISUT</p>\n</td>\n<td>\n<p>20.01.2011</p>\n</td>\n<td> </td>\n</tr>\n<tr>\n<td>\n<p>SAPK-60017INCEEISUT</p>\n</td>\n<td>\n<p>30.09.2010</p>\n</td>\n<td> </td>\n</tr>\n<tr>\n<td>\n<p>SAPK-60016INCEEISUT</p>\n</td>\n<td>\n<p>24.05.2010</p>\n</td>\n<td>\n<p>SAPK-60017INFICA or<br/>SAPK-60207INFICA or<br/>SAPK-60306INFICA</p>\n</td>\n</tr>\n<tr>\n<td>\n<p>SAPK-60015INCEEISUT</p>\n</td>\n<td>\n<p>25.01.2010</p>\n</td>\n<td> </td>\n</tr>\n<tr>\n<td>\n<p>SAPK-60014INCEEISUT</p>\n</td>\n<td>\n<p>30.09.2009</p>\n</td>\n<td> </td>\n</tr>\n<tr>\n<td>\n<p>SAPK-60013INCEEISUT</p>\n</td>\n<td>\n<p>20.05.2009</p>\n</td>\n<td> </td>\n</tr>\n<tr>\n<td>\n<p>SAPK-60012INCEEISUT</p>\n</td>\n<td>\n<p>19.01.2009</p>\n</td>\n<td> </td>\n</tr>\n<tr>\n<td>\n<p>SAPK-60011INCEEISUT</p>\n</td>\n<td>\n<p>04.08.2008</p>\n</td>\n<td> </td>\n</tr>\n<tr>\n<td>\n<p>SAPK-60010INCEEISUT</p>\n</td>\n<td>\n<p>03.06.2008</p>\n</td>\n<td> </td>\n</tr>\n<tr>\n<td>\n<p>SAPK-60009INCEEISUT</p>\n</td>\n<td>\n<p>14.04.2008</p>\n</td>\n<td> </td>\n</tr>\n<tr>\n<td>\n<p>SAPK-60008INCEEISUT</p>\n</td>\n<td>\n<p>14.03.2008</p>\n</td>\n<td> </td>\n</tr>\n<tr>\n<td>\n<p>SAPK-60007INCEEISUT</p>\n</td>\n<td>\n<p>07.02.2008</p>\n</td>\n<td> </td>\n</tr>\n<tr>\n<td>\n<p>SAPK-60006INCEEISUT</p>\n</td>\n<td>\n<p>24.01.2008</p>\n</td>\n<td> </td>\n</tr>\n<tr>\n<td>\n<p>SAPK-60005INCEEISUT</p>\n</td>\n<td>\n<p>10.12.2007</p>\n</td>\n<td> </td>\n</tr>\n<tr>\n<td>\n<p>SAPK-60004INCEEISUT</p>\n</td>\n<td>\n<p>10.10.2007</p>\n</td>\n<td> </td>\n</tr>\n<tr>\n<td>\n<p>SAPK-60003INCEEISUT</p>\n</td>\n<td>\n<p>13.07.2007</p>\n</td>\n<td> </td>\n</tr>\n<tr>\n<td>\n<p>SAPK-60002INCEEISUT</p>\n</td>\n<td>\n<p>29.05.2007</p>\n</td>\n<td> </td>\n</tr>\n<tr>\n<td>\n<p>SAPK-60001INCEEISUT</p>\n</td>\n<td>\n<p>08.02.2007</p>\n</td>\n<td> </td>\n</tr>\n</tbody>\n</table></div>\n<p><br/>Dates in the future are planned and subject to change<br/><br/></p>\n<p><strong>III. Problems with importing Add-On Support Packages</strong></p>\n<ol>\n<li>SAPK-60028INCEEISUT<br/>These generation errors might occur. To solve that, see IV.1. below.<br/>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"3\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td>\n<p>Program /SAPCE/SAPLFKSK_EVENTS, Include /SAPCE/LFKSK_EVENTSU04: Syntax error in line 000027<br/>Type 'FKKSK_STR_SDATA' is unknown</p>\n</td>\n</tr>\n</tbody>\n</table></div>\n</li>\n<li>SAPK-60030INCEEISUTThese generation errors might occur. To solve that, see IV.2. below.<br/>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"3\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td>\n<p>Program /SAPCE/FK_SK_EVYBER: Syntax error in line 000021<br/>Field 'T7SK52' is unknown. It is neither in one of the specified tables nor defined by a 'DATA' stat</p>\n<p>Program /SAPCE/CL_EDOCUMENT_ISU_SVN===CP, Include /SAPCE/CL_EDOCUMENT_ISU_SVN===CU: Syntax error in line 000003<br/>Type 'CL_EDOCUMENT_SVN' is unknown</p>\n</td>\n</tr>\n</tbody>\n</table></div>\n</li>\n<li>SAPK-60032INCEEISUT<br/>These generation errors might occur. To solve error 1, see IV.2. below. To solve error 2, see the note <strong>2228198</strong><br/>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"3\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td>\n<p>1)Program /SAPCE/FK_SK_EVYBER: Syntax error in line 000031<br/>Field 'T7SK52' is unknown. It is neither in one of the specified tables nor defined by a 'DATA' stat</p>\n<p>2)Program /SAPCE/IUHU_CL_AUDIT_FUNC===CP, Include /SAPCE/IUHU_CL_AUDIT_FUNC===CU: Syntax error in line 000011<br/>Type 'IF_EX_HU_AUDIT_REPORT' is unknown</p>\n</td>\n</tr>\n</tbody>\n</table></div>\n</li>\n</ol>\n<p><strong>IV. Postprocessing after the import of add-on support packages</strong></p>\n<ol>\n<li>SAPK-60028INCEEISUT<br/>The support package requires the following FI-CA notes if you are using the Slovakian localization\r\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"3\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td>\n<p>1954895</p>\n</td>\n<td>SK FI-CA Electronic VAT Ledger</td>\n</tr>\n</tbody>\n</table></div>\n</li>\n<li>SAPK-60030INCEEISUT<br/>If you are using the Czech bank interface for IS-U and IS-T you need to apply at least HR-CEE 110_600 Support Package 26 to the system. See note 775544<br/>The support package requires the following FI-CA notes if you are using the Slovakian localization: 2005304, 1995867, 2021860, 2042944</li>\n</ol>\n<p> </p>", "noteVersion": 31}, {"note": "1012275", "noteTitle": "1012275 - Romanian specific functionality IS-U 4.72 (collective)", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>Romanian specific functionality for IS-U release 4.72 (collective)</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>Country specific functionality for Romania for IS-U is delivered in add-on CEEISUT for release 4.72. See related note 774230 for information about availability and installation rules of this add-on. Please implement the highest add-on support patch available for CEEISUT. See related notes for corresponding AOPs.<br/><br/>The functionality listed in related notes is currently available for Romania. See related notes and/or attached documentation for details about delivered functionality and customizing possibilities.<br/><br/>Note:</p> <ul><li>There are no modifications of the SAP standard objects delivered in add-on CEEISUT</li></ul> <ul><li>The enhancements are using reserved name spaces /SAPCE/* and /CEEISUT/* which do not overlap with customer name range or other project development (according to note 72483)</li></ul> <ul><li>These enhancements should not interfere with any add-on support packages (AOP) for IS-U.</li></ul> <ul><li>Local support is responsible for maintenance and hotline support of these enhancements</li></ul> <ul><li>These enhancements are not released for any other country</li></ul></div>", "noteVersion": 1}, {"note": "1300345", "noteTitle": "1300345 - CEEISUT 604: Installation on SAP ERP 6.0 EHP4/5", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>Add-On installation on EhP 4 or EhP 5 for SAP ERP 6.0.<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>Enhancement Package, CD51035690, SAPehpi, CEEISUT 604<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>Add-On installation<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p><b>This note is updated on a regular basis. Make sure you have the current</b><br/><b>version of this note before you start the installation.</b><br/><br/>Contents<br/> 1. Changelog<br/> 2. Prerequisites for installing CEEISUT 604<br/> 3. Preparing CEEISUT 604 installation<br/> 4. Executing CEEISUT 604 installation<br/> 5. After you have installed CEEISUT 604<br/> 6. Language support<br/> 7. Password<br/></p> <b>1. Changelog</b><br/> <div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\"><tr><th align=\"LEFT\">Date</th><th align=\"LEFT\"> Topic</th><th align=\"LEFT\"> Short Description</th></tr> <tr><td>Mar 20 2009</td><td> </td><td> initial release</td></tr> <tr><td>Mar 25 2009</td><td> </td><td> Moved delta upgrade info to note 1267871</td></tr> <tr><td>Sep 21 2010</td><td> </td><td> Info about EhP5</td></tr> <tr><td></td></tr> </table> <b>2. Prerequisites for installing CEEISUT 604</b><br/> <ul><li>It is not possible to uninstall CEEISUT.<br/>Before you install CEEISUT, keep in mind that you cannot uninstall ABAP add-ons. Further restrictions that concern the upgrade and maintenance of your SAP system and that occur as a result of installing an add-on are described in release strategy note 1014997.</li></ul> <ul><li>Required release<br/>You require SAP ERP Central Component 600 (SAP ECC 600) Enhancement Package 4 for IS-UT 604 or<br/>SAP ERP Central Component 600 (SAP ECC 600) Enhancement Package 5 for IS-UT 605</li></ul> <ul><li>Import the latest SPAM/SAINT update.<br/>Make sure that you have installed the latest SPAM/SAINT update on your system. If a newer version is available on SAP Service Marketplace, import the new SPAM/SAINT update.</li></ul> <ul><li>Import the latest R3trans and tp.<br/>Ensure that you have imported the latest kernel version into your system. If a newer version is available on SAP Service Marketplace, import the most recent kernel.</li></ul> <ul><li>Obtain the following notes before you begin the installation:</li><br/> <div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\"><tr><td>Add-ons: Conditions:</td><td> </td><td> 70228</td></tr> <tr><td>Release strategy note:</td><td> </td><td> 1014997</td></tr> <tr><td>CSP note:</td><td> </td><td> 1318062</td></tr> <tr><td>Problems with transaction SAINT:</td><td> </td><td> 822380</td></tr> <tr><td>SAP Enhancement Package Installer</td><td> 1175848</td></tr> </table></div></ul> <p></p> <ul><li>Prerequisites:</li></ul> <p>           Check that your system meets the following prerequisites:</p> <ul><ul><li>Required components and support packages for the installation</li><br/> <div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\"><tr><th align=\"LEFT\">Component</th><th align=\"LEFT\"> Release</th><th align=\"LEFT\"> Support Package</th></tr> <tr><td>IS-UT</td><td> 604</td><td> SAPK-60401INISUT or</td></tr> <tr><td>IS-UT</td><td> 605</td><td> SAPK-60503INISUT</td></tr> <tr><td></td></tr> </table></div></ul></ul> <p></p> <ul><ul><li>For installation on EHP5 you need to include support package 5 of CEEISUT in the installation queue</li></ul></ul> <ul><ul><li>If you have not yet installed these component support packages, you can include them when you install CEEISUT 604. For more information, see note 83458.</li><br/> <div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\"><tr><td></td></tr> </table></div></ul></ul> <p></p> <ul><li>Required SAP Industry Solutions.<br/>The CEEISUT 604 add-on requires the Industry Solution IS-UT. Before you install the add-on, you must activate the relevant Business Function Set. Refer to note 874416.</li></ul> <ul><li>Additional information about the installation:</li><br/> <div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\"><tr><td>CD material number add-on installation</td><td> 51035690</td></tr> <tr><td></td></tr> <tr><td>Note that CEEISUT 604 does not contain functionality beyond that of CEEISUT 600.</td></tr> <tr><td></td></tr> </table></div></ul> <b>3. Preparing CEEISUT 604 installation</b><br/> <ul><li>Required corrections <b>before</b> the installation<br/>If you do <b><b>not</b></b> have installed HR-CEE 110_604 and are using FI-CA for Czech rebublic or Slovakia already check note 775544 or 775545.</li></ul> <ul><li>Please apply the manual change described in note 1050988 for table ISU_VORB_001.</li></ul> <ul><li>Making the add-on CEEISUT 604 available</li></ul> <ul><ul><li>The installation CD for CEEISUT 604 is not automatically sent to all customers. Request the CD with material number 51035690 from your local subsidiary or download the CD from SAP Service Marketplace. To obtain the access for download please create Customer message under component XX-CSC-CZ-IS-U and specify the S user for the access. and list of the particular countries that are planned for implementation</li></ul></ul> <ul><ul><li>Log on as user:</li><br/> <div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\"><tr><td>&lt;sid&gt;adm</td><td> on UNIX and Microsoft Windows</td></tr> <tr><td>&lt;SID&gt;OFR</td><td> on IBM i (previously i5/OS or OS/400)</td></tr> <tr><td></td></tr> </table></div></ul></ul> <p></p> <ul><ul><li>Switch to the &lt;DIR_EPS_ROOT&gt; directory of your SAP system (usually /usr/sap/trans/EPS). The &lt;DIR_EPS_ROOT&gt; directory is also displayed under DIR_EPS_ROOT after you execute the RSPFPAR report.</li></ul></ul> <ul><ul><li>Go to the higher-level directory of &lt;DIR_EPS_ROOT&gt;.</li></ul></ul> <ul><ul><li>Unpack the SAR archive &lt;SAR_Archive&gt; on the CD with the following statement:</li><br/> <div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\"><tr><td>SAPCAR -xvf /&lt;CD_DIR&gt;/INST/DATA/&lt;ARCHIV&gt;.SAR</td><td> </td><td> </td><td> on UNIX</td></tr> <tr><td>SAPCAR '-xvf /QOPT/&lt;VOLID&gt;/INST/DATA/&lt;ARCHIV&gt;.SAR'</td><td> on AS/400</td></tr> <tr><td>SAPCAR -xvf &lt;CD_DRIVE&gt;:\\INST\\DATA\\&lt;ARCHIV&gt;.SAR</td><td> </td><td> on Windows</td></tr> <tr><td></td></tr> <tr><td>The CSR0120031469_0036252.PAT file should now be in the &lt;DIR_EPS_ROOT&gt;/in directory.</td></tr> <tr><td></td></tr> </table></div></ul></ul> <b>4. Executing CEEISUT 604 installation</b><br/> <ul><li>User to be used<br/>Log on to your SAP system in client 000 as a user with SAP_ALL authorization. Do NOT use the SAP* or DDIC users.</li></ul> <ul><li>Displaying the add-on installation package<br/>Call transaction SAINT and choose 'Start' and 'Load'.<br/>After the list of uploaded packages is displayed, you can return to the initial screen of transaction SAINT by choosing F3 or 'Back'.</li></ul> <ul><li>Starting the installation<br/>Call transaction SAINT and choose 'Start'. Select the add-on CEEISUT 604 and choose 'Continue'. If all of the necessary conditions for importing the add-on have been fulfilled, the system will now display the relevant queue. The queue consists of the installation package, and it may also contain Support Packages and Add-On Support Packages.<br/>To start the installation process, choose 'Continue'. For more information, call transaction SAINT and choose 'Info' on the application toolbar.<br/>The system prompts you to enter a password. This password is provided below.<br/></li></ul> <b>5. After you have installed CEEISUT 604</b><br/> <ul><li>Importing Support Packages after the installation<br/>You can import Support Packages of the other components that CEEISUT 604 does not modify without CRTs.</li></ul> <ul><li>Delivery Customizing<br/>Delivery Customizing is imported into client 000 and may need to be copied to other clients. For more information, see Note 337623.</li></ul> <ul><li>If you are using the Hungarian localization, you have to apply the manual changes described in note 1308851 after the import of CEEISUT 604!</li></ul> <ul><li>Apply the manual changes for type group ISUZ2 as described in note 1050988 and switch on the /SAPCE/IUHU switch.<br/></li></ul> <b>6. Language support</b><br/> <ul><li>In addition to German and English, the following languages are included in CEEISUT 604: BG, CS, HU, PL, RO, RU, SK, TR, UK.</li></ul> <ul><li>With support package 09 (SAPK-60409INCEEISUT) also language Greek is included.<br/></li></ul> <ul><li>All the language-dependent parts of CEEISUT are contained in the installation package of the add-on. If the relevant standard language exists in the system when you import CEEISUT, the language part of the add-on is automatically imported. You do not need to import a language transport separately.</li></ul> <ul><li>If you import a new standard language into your system after installing CEEISUT, you must manually ensure that the corresponding language-dependent part of the add-on is imported. See Note 195442 for further information.<br/></li></ul> <b>8. Password</b><br/> <ul><li>During the installation, the system prompts you to enter a password. This password is: 30C3925207<br/></li></ul></div></div>", "noteVersion": 11}, {"note": "774625", "noteTitle": "774625 - Czech & Slovak specific functionality IS-T 4.72 (collective)", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>Czech &amp; Slovak specific functionality for IS-T release 4.72 (collective)</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>Country specific functionality for Czech Republic and Slovakia for IS-T is delivered in add-on CEEISUT for release 4.72. See related note 774230 for information about availability and installation rules of this add-on.<br/><br/>See other related notes for details about delivered functionality, customizing and documentation.<br/><br/>Note:</p> <ul><li>There are no modifications of the SAP standard objects</li></ul> <ul><li>The enhancements are using reserved name spaces /SAPCE/* and /CEEISUT/* which do not overlap with customer name range or other project development (according to note 72483)</li></ul> <ul><li>These enhancements should not interfere with any add-on support packages (AOP) for IS-T.</li></ul> <ul><li>SAP CR, Prague is responsible for maintenance and hotline support of those enhancements for Czech Republic and Slovakia only</li></ul> <ul><li>These enhancements are not released for any other country</li></ul></div>", "noteVersion": 10}, {"note": "529782", "noteTitle": "529782 - IS-U/CCS 4.64 localization for Hungary - collective note", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>IS-U/CCS country specific enhancement for Hungary</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>IS-U/CCS 4.64 HU Localization, /SAPCE/IU</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>Hungarian utility sector requires a country specific solution for</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>Note:</p> <ul><li>The enhancement objects are using reserved name space /SAPCE/* which does not overlap with customer name range or other project development (according to note 72483)</li></ul> <ul><li>The package contains some changes to SAP standard objects ( see detailed object list in the documentation below) which may interfere with some of the hot packages or other add-on support packages for IS-U</li></ul> <ul><li>To avoid collision of this local enhancement and add-on SPs customer should reach add-on service pack level SAPKXXXXXXX   before implementing this country specific solution</li></ul> <ul><li>Customer may need higher level SP than described above. In this case pleas contact us to discuss possible actions</li></ul> <ul><li>SAP Hungary is responsible for maintenance and hotline support of these enhancements<br/></li></ul> <b>Contents</b><br/> <ul><li>I/   Overview of enhancements</li></ul> <ul><li>II/  Transport requests</li></ul> <ul><li>III/ Installation</li></ul> <p><br/>***********************************************************************<br/><br/><strong>I/ Overview of enhancements</strong></p> <ul><li>Billing</li></ul> <ul><ul><li>Partial Billing for residential customers</li></ul></ul> <ul><ul><li>Advance billing of non-residential customers</li></ul></ul> <ul><ul><li>Manual billing penalty, unmeasured consuption &amp; illegal withdraw</li></ul></ul> <ul><ul><li>Gas price compensation #1</li></ul></ul> <ul><li>Invoicing</li></ul> <ul><ul><li>Outsorting methods #1</li></ul></ul> <ul><ul><li>Numbered bill print outs #1</li></ul></ul> <p><br/>#1 - separate transport. Main transports is not requested!<br/><br/><br/><strong>II/ Transport requests (Main transport)</strong></p> <ul><ul><li>CZUK901222 - DDIC elements (dictionary objects)</li></ul></ul> <ul><ul><li>CZUK901223 - ABAP elements (programming objects)</li></ul></ul> <ul><ul><li>CZUK901224 - Repairs (standard modifications)</li></ul></ul> <ul><ul><li>CZUK901225 - Configuration (customizing)</li></ul></ul> <ul><ul><li>CZUK901265 - Numbered bill...</li></ul></ul> <ul><ul><li>CZUK901376 - Gas price...<br/></li></ul></ul> <p>All objects are localized for IS-U/CCS 4.64 patch level 15. For further information, please see note 0640662.<br/><br/>Documentation files are avaible to download from this note attachment.<br/><br/>Namespace transport CZUK901208 is requested in case of need modify localized object (/SAPCE/... namespace).<br/><br/><br/><strong>III/ Installation</strong><br/>Transports and document files are stored on demand on sapserv3 in the directory '/specific/czsk/HU/ISU/note_0529782'.<br/>File isuhu_locsp15.exe is self extract archive containing all related files.<br/><br/>The customer calls ftp as follows:<br/>&lt;set up link to sapserv3&gt;<br/>ftp sapserv3              /* Start file transfer program      */                           /* Enter 'ftp' as user and password */<br/>cd /specific/czsk/HU/ISU/note_0529782 /* set path<br/>bin                      /* Switch to binary mode             */<br/>get isuhu_locsp15.exe     /* Fetch MS DOS self extract archive */<br/>bye                      /* End ftp. */<br/><br/>Now extract archives under MS DOS or Windows,<br/>than call ftp to transfer the transport files to your R/3 server<br/>in the BINARY mode !<br/><br/>Please see note 13719 for additional information.<br/><strong></strong><br/><strong></strong></p></div>", "noteVersion": 9}, {"note": "1014963", "noteTitle": "1014963 - Czech & Slovak specific functionality IS-UT 600 (collective)", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>Czech &amp; Slovak specific functionality for IS-UT 600 (collective)</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>Country specific functionality for Czech Republic and Slovakia for IS-UT 600 is delivered in add-on CEEISUT 600. See related notes 1005317 and 1006196 for information about installation and upgrade for this add-on. Please implement the highest add-on support patch available for CEEISUT600. See related note 1014951 for corresponding AOPs.<br/><br/>The functionality listed in related notes is currently available for Czech Republic and Slovakia. See related notes and/or attached documentation for details about delivered functionality and customizing possibilities.<br/><br/>Note:</p> <ul><li>There are no modifications of the SAP standard objects</li></ul> <ul><li>The enhancements are using reserved name spaces /SAPCE/* and /CEEISUT/* which do not overlap with customer name range or other project development (according to note 72483)</li></ul> <ul><li>These enhancements should not interfere with any hot packages or any (AOP) for IS-UT.</li></ul> <ul><li>SAP CR, Prague is responsible for maintenance and hotline support of those enhancements for Czech Republic and Slovakia only</li></ul> <ul><li>These enhancements are not released for any other country</li></ul></div>", "noteVersion": 1}, {"note": "921035", "noteTitle": "921035 - Russian specific functionality IS-U 4.72 (collective)", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>Russian specific functionality for IS-U release 4.72 (collective)</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>Country specific functionality for Russia for IS-U is delivered in add-on CEEISUT for release 4.72. See related note 774230 for information about availability and installation rules of this add-on. Please implement the highest add-on support patch available for CEEISUT. See related notes for corresponding AOPs.<br/><br/>Localization of IS-U for Russia is not generally released, but only restricted available for pilot customers. If you intend to implement the solution for productive usage for Russia, please get in contact with the local SAP CIS Product Management first. As the solution for Russia in only available for pilot customers, the implementation can only be done in close coordination with the local SAP CIS Product Management and SAP CIS IS-U Consulting.<br/><br/>The functionality listed below is currently available for Russia. See related notes and/or attached documentation for details about delivered functionality and customizing possibilities:</p> <ul><li>Cash desk reporting (Cashier report, Cash ledger)</li></ul> <ul><li>Tax reporting (Sales ledger, Purchase ledger, Invoice journal)</li></ul> <ul><li>Possibility to separate transformer losses from grid losses</li></ul> <ul><li>Possibility to bill separately shares of consumption registered by one meter</li></ul> <ul><li>Benefits</li></ul> <ul><li>Advance payments are implemented using Budget Billing for Industrial Customers (BBP 5)</li></ul> <ul><li>Seals management</li></ul> <ul><li>Extension of master data for business processes</li></ul> <ul><li>Extension of master data for reporting. Make sure that you have activated properly BC Set /CEEISUT/ISU_RU_01 or the hierarchical BC Set for Russia mentioned below.</li></ul> <ul><li>Accounts receivable report</li></ul> <ul><li>Extension of address is not delivered by add-on CEEISUT. You have to perform a modification in your system as described in note 939002</li></ul> <ul><li>Interface KLADR-DB. Extension of address is required for this interface.</li></ul> <ul><li>Legal forms: Invoice Form</li></ul> <ul><li>Legal forms: Invoice FI-CA</li></ul> <ul><li>Legal forms: Payment forms (payment request and incasso order)</li></ul> <ul><li>Legal forms: Incasso orders register</li></ul> <ul><li>Legal forms: Cash desk forms (cash receipt order KO-1 and cash expenditure order KO-2)</li></ul> <ul><li>2-reg (RAO UES and state statistical reporting) - information about grid losses classified by voltage level</li></ul> <ul><li>26-zkh (RAO UES and state statistical reporting) - information about citizen's benefits for housing and communal services</li></ul> <ul><li>Several invoices for one installation</li></ul> <p><br/>New BC Sets were delivered as part of the localization, which have to be activated in your system. Either activate hierarchical BC set<br/>/CEEISUT/ISU_RU or BC sets for individual topics above.<br/>Optionally you can also activate hierarchical BC Set /CEEISUT/ISU_RU_11 for time zones.<br/><br/>Note:</p> <ul><li>There are no modifications of the SAP standard objects delivered in add-on CEEISUT</li></ul> <ul><li>The enhancements are using reserved name spaces /SAPCE/* and /CEEISUT/* which do not overlap with customer name range or other project development (according to note 72483)</li></ul> <ul><li>These enhancements should not interfere with any add-on support packages (AOP) for IS-U.</li></ul> <ul><li>Local support is responsible for maintenance and hotline support of those enhancements for Russia</li></ul> <ul><li>These enhancements are not released for any other country</li></ul></div>", "noteVersion": 10}, {"note": "1645477", "noteTitle": "1645477 - Upgrade to SAP ERP 6.0 EHP6 with CEEISUT 606", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You want to perform a switch upgrade with Add-on CEEISUT 606 to SAP ERP 6.0 Enhancement Package 6 (EHP6) or to SAP ERP 6.0 Enhancement Package 7 (EHP7) or to SAP ERP 6.0 Enhancement Package 8 (EHP8)<br/>There is no guide that is specific to Add-ons. This note contains additional information about the upgrade that is specific to Add-ons.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Upgrade, CEEISUT, CEEISUT 606, SAPK-606GGINCEEISUT, CD51042058</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>The following initial situations are supported for the upgrade to SAP ERP 6.0 EHP6 and CEEISUT 606:<br/>SAP R/3 Enterprise 470 and CEEISUT 472<br/><br/>For an update of CEEISUT 600 or 604 see note 1645476</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong>This note is updated on a regular basis. Make sure you have the latest version of this note before you start the upgrade.</strong><br/><br/>Contents<br/><br/>1. Change history<br/>2. Important general information<br/>3. Checks before the upgrade<br/>4. Additional information about the upgrade<br/>5. Problems after the import<br/>6. Actions after the upgrade<br/>7. Language support<br/><br/></p>\n<p><strong>1. Change history<br/></strong></p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"1\" cellspacing=\"1\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr><th>Date</th><th>Section</th><th>Short description</th></tr>\n<tr>\n<td>01-Apr-2016</td>\n<td> </td>\n<td>Information about EHP8</td>\n</tr>\n<tr>\n<td>17-Dec-2013</td>\n<td> </td>\n<td>Information about EHP7</td>\n</tr>\n<tr>\n<td>08-Dec-2011</td>\n<td> </td>\n<td>initial release of note</td>\n</tr>\n</tbody>\n</table></div>\n<p><strong>2. Important general information</strong></p>\n<ul>\n<li>Delivery of CEEISUT 606<br/>The upgrade CD for CEEISUT 606 is not automatically sent to all customers. if you want to carry out the upgrade, request the CD with the material number 51042058 from your local subsidiary or download the CD from SAP Service Marketplace. For more information about the ordering procedure for SAP software, see Note 925690.</li>\n</ul>\n<ul>\n<li>Required SAP releases and Add-on releases:<br/>SAP R/3 Enterprise 470 and CEEISUT 472</li>\n</ul>\n<ul>\n<li>Add-on supplement<br/>When you upgrade an SAP release to SAP ERP 6.0, the system automatically recognizes that an ABAP Add-on is installed and it requests an additional CD or an upgrade package for the Add-on. As a result, the ABAP Add-on is also upgraded to the corresponding release during the standard upgrade.<br/><strong>CAUTION</strong><br/>If you upgrade an SAP system with the CEEISUT country version to SAP ERP 6.0 EHP6 without importing the data contained on the upgrade CD with the material number 51042058 (IS_SELECT phase), the ABAP Add-on CEEISUT no longer works after the upgrade. Since the status of the system is inconsistent, SAP can no longer accept any liability.</li>\n</ul>\n<p><strong>3. Checks before the upgrade</strong></p>\n<ul>\n<li>Obtain the following notes before you begin the installation:<br/><br/>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"3\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td>Add-ons general conditions</td>\n<td>70228</td>\n</tr>\n<tr>\n<td>Release strategy note</td>\n<td>1014997</td>\n</tr>\n<tr>\n<td>Support package note</td>\n<td>1660394</td>\n</tr>\n</tbody>\n</table></div>\n</li>\n<li>R3trans, tp, kernel, SPAM<br/>In the <span>initial release</span> of your system, use the latest R3trans, tp and kernel patch, and import the most recent SPAM update.</li>\n<li>Component Support Packages to be included in the upgrade<br/>In addition to the Support Packages contained in SAP ERP 6. 0 EHP6, you require at least the Support Packages of the following components for the upgrade:<br/>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"3\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td>IS-UT</td>\n<td>606</td>\n<td>SAPK-60601INISUT</td>\n</tr>\n<tr>\n<td> </td>\n<td>OR</td>\n<td> </td>\n</tr>\n<tr>\n<td>IS-UT</td>\n<td>617</td>\n<td>SAPK-61702INISUT</td>\n</tr>\n<tr>\n<td> </td>\n<td>OR</td>\n<td> </td>\n</tr>\n<tr>\n<td>IS-UT</td>\n<td>618</td>\n<td> </td>\n</tr>\n</tbody>\n</table></div>\n<br/>Importing additional component Support Packages<br/>The Add-on CEEISUT 606 does not contain modifications. You can also perform the installation of additional support package during the upgrade.</li>\n<li>Obtaining the required component Support Packages<br/>You can download the relevant data files from SAP Service Marketplace. The data files must be made available in the DIR_EPS_ROOT&gt;/in directory before you start the UPLOAD_REQUEST prepare phase. For information about downloading Support Packages, see Note 83458.</li>\n<li>Obtaining the CEEISUT 606 upgrade supplement<br/>Download the CEEISUT 606 CD from SAP Service Marketplace</li>\n<li>As of Basis Release 700, the CEEISUT upgrade package is automatically unpacked from the CD. To do this, specify the directory (under which you want to mount the CEEISUT upgrade CD) in the UPLOAD_REQUEST phase.<br/>If you have downloaded the CEEISUT upgrade package directly from SAP Service Marketplace, unpack it in the directory DIR_EPS_ROOT&gt;/in.</li>\n</ul>\n<p><strong>4. Additional information about the upgrade</strong></p>\n<ul>\n<li>Additional information about PREPARE</li>\n<ul>\n<li>Phase IS_SELECT<br/>Select the upgrade type for the installed Add-ons: CEEISUT -&gt; Upgrade with SAINT Package</li>\n<li>Phase BIND_PATCH<br/>This displays which Support Packages are located in the DIR_EPS_ROOT/in directory and can be included in the upgrade. Make sure that you include at least the Support Packages mentioned in section 4 (\"Checks before the upgrade\") in the upgrade.</li>\n</ul>\n</ul>\n<p><strong>5. Problems after the upgrade</strong></p>\n<ul>\n<li>Generation errors<br/>The existing package of the Add-on CEEISUT 606 contains the following generation errors:</li>\n<ul>\n<li>Program /SAPCE/SAPLFK_TFKFBC, Include /SAPCE/LFK_TFKFBCT00: Syntax error The Dictionary structure or table '*T5T52' is either not active or does not exist</li>\n</ul>\n<ul>\n<li>Program /SAPCE/FK_CZ_MOVE: Syntax error in line 000021 The Dictionary structure or table 'T5T52' is either not active or does not exist</li>\n</ul>\n<ul>\n<li>Program /SAPCE/FK_SK_EVYBER: Syntax error in line 000020 Field 'T7SK52' is unknown.</li>\n</ul>\n<ul>\n<li>Program /SAPCE/FK_SK_SEPOTV: Syntax error in line 000014 Field 'T7SK52' is unknown.</li>\n</ul>\n<li>Please check note 775544 if this functionalty is relevant for your solution. If so, please implement this note.</li>\n</ul>\n<p><strong>6. Actions after the upgrade<br/></strong><br/><strong>7. Language support</strong></p>\n<ul>\n<li>CEEISUT 606 supports the following languages:<br/>Romanian, Slovenian, Ukrainian, Czech, German, English, Greek,<br/>Hungarian, Polish, Slovakian, Russian, Turkish, Bulgarian.</li>\n</ul>\n<ul>\n<li>All the language-dependent parts of CEEISUT are contained in the installation package of the CEEISUT. If the relevant standard language exists in the system when you import CEEISUT, the language part of the CEEISUT is automatically imported. You do not need to import a language transport separately.</li>\n</ul>\n<ul>\n<li>If you import a new standard language into your system after installing CEEISUT, you must manually ensure that the corresponding language-dependent part of the CEEISUT is imported. For more information, see Note 195442.</li>\n</ul>", "noteVersion": 5}, {"note": "774230", "noteTitle": "774230 - CEEISUT rel.4.72 Add-On Installation", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>Country specific functionality of IS-U and IS-T release 4.72 for<br/>Central and Eastern Europe is delivered as add-on CEEISUT release 4.72</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>Localization add-on IS-U/T for CEE</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>Availability</p> <ul><li>You have to install add-on CEEISUT release 4.72 to get the localized functionality for countries and components listed below</li></ul> <ul><li>This add-on is available for customers and partners with valid IS-U or IS-T licence only</li></ul> <ul><li>You may download add-on installation file and add-on support packages from SAP Service Marketplace</li></ul> <ul><li>Please ask your local hotline support for access to installation files and AOPs of CEEISUT on SAP Service Marketplace</li></ul> <p><br/><br/>Localized functionality is available for following countries and components:</p> <ul><li>IS-U 4.72 for Czech Republic</li></ul> <ul><li>IS-T 4.72 for Czech Republic</li></ul> <ul><li>IS-U 4.72 for Slovakia</li></ul> <ul><li>IS-T 4.72 for Slovakia</li></ul> <ul><li>IS-U 4.72 for Hungary</li></ul> <ul><li>IS-U 4.72 for Poland</li></ul> <ul><li>IS-U 4.72 for Russia - restricted availability, please contact SAP CIS for further information</li></ul> <ul><li>IS-U 4.72 for Ukraine</li></ul> <ul><li>IS-U 4.72 for Romania</li></ul> <ul><li>IS-U 4.72 for Bulgaria<br/><br/></li></ul> <p>See related notes for details about delivered functionality, customizing and documentation.</p></div>", "noteVersion": 6}, {"note": "1348841", "noteTitle": "1348841 - Greek specific functionality for IS-UT 600", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>Greek specific functionality for IS-UT 600<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>Greece<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>Country specific functionality for Greece for IS-UT 600 is delivered<br/>in add-on CEEISUT 600. See related note 1014997 for release strategy, installation and upgrade for this add-on. Please implement the highest add-on support patch available for CEEISUT600. The minimal SP level required for Greek functionality is SP 16 for CEEISUT 600 or SP 04 for CEEISUT 604.<br/><br/>The functionality listed below is currently available for<br/>Greece. See attached documentation for details about delivered functionality and customizing possibilities.<br/><br/>The solution contains the following functionality:<br/>- Official Document Numbering<br/>- Annual Sales and Purchases Collective (MYF) Report<br/>- Cash journal <br/>- Document journal<br/>- Accounts Receivable Ledger and Trial Balance reports<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>1. Install the the add-on SAP IS-UT CEE as described in Note 1014997<br/>2. Install required SPs<br/><br/>For the Documentation please regard the attached PDF files.<br/><br/>Important!<br/><br/>You must activate Business Configuration (BC) Set /CEEISUT/ISU_GR_02 before you post any invoicing documents for the reporting period for which you want to prepare the Annual Sales and Purchases Collective (MYF) Report. This is required to generate the MYF report with the correct business partner transaction data.<br/><br/>You must activate Business Configuration (BC) Set /CEEISUT/ISU_GR_05 before you post any documents for the reporting period for which you want to prepare the Accounts Receivable Ledger and Trial Balance reports. This is required for correct calculation of balances.<br/><br/>It is recommended to activate hierarchical BC set /CEEISUT/ISU_GR (If you activate this BC set, the system activates the BC sets mentioned above as well).<br/><br/>For more information about how to activate Business Configuration sets, see Activate BC Sets on SAP Help Portal at http://help.sap.com.<br/><br/>Note:</p> <ul><li>There are no modifications of the SAP standard objects delivered in add-on CEEISUT</li></ul> <ul><li>The enhancements are using reserved name spaces /SAPCE/* and /CEEISUT/* which do not overlap with customer name range or other project development (according to note 72483)</li></ul> <ul><li>These enhancements should not interfere with any add-on support packages (AOP) for IS-UT.</li></ul> <ul><li>Local support is responsible for maintenance and hotline support of these enhancements</li></ul> <ul><li>These enhancements are not released for any other country</li></ul> <p><br/></p></div>", "noteVersion": 2}, {"note": "2349019", "noteTitle": "2349019 - CEEISUT TR Localization: SAP S/4HANA, On Premise Edition 1610 SPxx and higher release: Restriction Note", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You use the CEEISUT (Localization for IS-U, IS-T for Central&amp;Eastern Europe) add-on product that contains address management localization for Turkey (released in SAP Note 1651302), and you would like to upgrade to the releases SAP S/4HANA 1610 or higher, any Feature Pack Stack.</p>\n<p>The purpose of this SAP Note is to inform you about a restriction in the use of the address management function for Turkey in the releases SAP S/4HANA 1610 or higher. Please note that the content of this SAP Note is subject to change.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>CEEISUT 606; Turkey Address Management; Manual Modifications; SAP S/4HANA 1610 FPSxx; SAP S/4HANA 1709 FPSxx; SAP S/4HANA 1809 FPSxx; SAP S/4HANA 1909 FPSxx; SAP S/4HANA 2020 FPSxx; SAP S/4HANA 2021 FPSxx; Switch ADDR_TR_SWITCH</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p><span 'times=\"\" ar-sa;\"=\"\" calibri',sans-serif;=\"\" calibri;=\"\" en-us;=\"\" hu;=\"\" lang=\"EN-US\" minor-latin;=\"\" mso-ansi-language:=\"\" mso-bidi-font-family:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" mso-fareast-theme-font:=\"\" new=\"\" roman';=\"\">There is a restriction in the address management function in the releases SAP S/4HANA 1610 or higher release that did not exist in the localization for Turkey in the CEEISUT add-on product.</span></p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>When you use the address management localization for Turkey in the CEEISUT add-on product (you implemented SAP Note 1651302) and you upgrade to SAP S/4HANA 1610 or higher release, you will not be able to use this functionality productively.</p>\n<p>SAP has prepared migration tool that you can use to migrate your address management data from the add-on into the central address management fields activated with the ADDR_TR_SWITCH switch.</p>\n<p>Please create an incident on <strong>XX-CSC-TR-FS</strong> component and we will enable for you the installation of this migration tool shipped via SAP Note <strong>2370925</strong> - CEEISUT TR Localization: Addresses migration.</p>\n<p>Although the solution for the address management localization for Turkey in the CEEISUT add-on product cannot be used in SAP S/4HANA 1610 or higher release at all, your existing data is not lost during the upgrade.</p>", "noteVersion": 4}, {"note": "786147", "noteTitle": "786147 - Hungary specific functionality IS-U 4.72 (collective)", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>Hungary specific functionality IS-U/CCS release 4.72 (collective)</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>IS-U/CCS 472 HU localization,CEEISUT,AOP,CRT</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>Hungarian utility sector requires a country specific solution for</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>Country specific functionality for Hungary for IS-U/CCS is delivered in add-on CEEISUT for release 4.72.<br/>See related note 774230 for information about availability and installation rules of this add-on. See other related notes for details about delivered functionality, customizing and documentation.<br/><br/>Note:</p> <ul><li>There are some modifications of the SAP standard objects, therefore exist CRT's for every standard service packages.</li></ul> <ul><li>Local service packages (CRT) are depending on AOP and SP level.</li></ul> <ul><li>The enhancements are using reserved name spaces /SAPCE/* and /CEEISUT/* which do not overlap with customer name range or other project development (according to note 72483). .</li></ul> <ul><li>SAP Hungary Kft. is responsible for maintenance and hotline support of those enhancements for Hungary only.</li></ul> <ul><li>These enhancements are not released for any other country<br/></li></ul> <b>Contents</b><br/> <p>Overview of enhancements:</p> <ul><li>Partial Billing for residential customers (CPC)<br/></li></ul> <b>Installation</b><br/> <p>Install guide and packages files are possibly downloaded from<br/>related notes and sap service pages.<br/></p></div>", "noteVersion": 5}, {"note": "1014959", "noteTitle": "1014959 - Hungarian specific functionality for IS-UT 600 (collective)", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>Hungary specific functionality IS-UT 600 (collective)</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>IS-UT 600 HU localization,CEEISUT,AOP,CRT</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>Hungarian utility sector requires a country specific solution</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>Country specific functionality for Hungary for IS-UT 600 is delivered in add-on CEEISUT 600. See related notes 1005317 and 1006196 for information about installation and upgrade for this add-on. Please implement the highest add-on support patch available for CEEISUT600. See related note 1014951 for corresponding AOPs.<br/><br/>The functionality listed in related notes is currently available for<br/>Hungary. See related notes and/or attached documentation for details<br/>about delivered functionality and customizing possibilities.<br/><br/>Note:</p> <ul><li>There are no modifications of the SAP standard objects delivered in add-on CEEISUT</li></ul> <ul><li>However, some modifications of the SAP standard objects are necessary for correct functionality of the solution for Hungary, therefore there are separate CRTs for every standard service package.</li></ul> <ul><li>Local service packages (CRTs) are dependent on AOP and SP level.</li></ul> <ul><li>The enhancements are using reserved name spaces /SAPCE/* and /CEEISUT/* which do not overlap with customer name range or other project development (according to note 72483). .</li></ul> <ul><li>SAP Hungary Kft. is responsible for maintenance and hotline support of those enhancements for Hungary only.</li></ul> <ul><li>These enhancements are not released for any other country<br/></li></ul> <b>Contents</b><br/> <p>Overview of enhancements:</p> <ul><li>Partial Billing for residential customers (CPC)</li></ul> <ul><li>Advance billing of non-residential customers</li></ul> <ul><li>Billing penalty, unmeasured consuption &amp; illegal</li></ul> <ul><li>Gas price compensation</li></ul> <ul><li>Numbered print outs<br/></li></ul> <b>Installation</b><br/> <p>It is possible to download installation guide and package files from related notes and sap service marketplace.<br/></p></div>", "noteVersion": 4}, {"note": "1006196", "noteTitle": "1006196 - Upgrade to SAP ECC 600 with SAP IS-UT CEE add-on", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>Add-on upgrade to Release SAP ERP Central Component ECC 600 (referred to as SAP ECC 600 in the rest of the note) with CEEISUT 600.<br/>There are no add-on-specific instructions. This note contains the add-on-specific additional information about the upgrade.<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>Upgrade, add-on, SAP IS-UT CEE, CEEISUT, 600, AOX, SAPK-...</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>The following starting situations are supported for the upgrade to SAP ECC 600 and CEEISUT 600:<br/><br/>R/3 Enterprise 47x200 and CEEISUT 472<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p><b>This note is updated on a regular basis. Therefore, make sure you obtain the latest version of the note before you start the installation.</b><br/><br/>Contents<br/><br/>1. Change history<br/>2. Important general information<br/>3. Errors on the CD<br/>4. Checks before the upgrade<br/>5. Enhancements to the upgrade<br/>6. Problems after the import<br/>7. Actions after the upgrade<br/>8. Language support<br/><br/></p> <b>1. Change history<br/></b><br/> <div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\"><tr><th align=\"LEFT\">Date</th><th align=\"LEFT\"> Topic</th><th align=\"LEFT\"> Short description</th></tr> <tr><td>&lt;date&gt;</td><td> &lt;section&gt;</td><td> &lt;specification&gt;</td></tr> <tr><td></td></tr> </table> <b>2. Important general information</b><br/> <ul><li>Delivery of CEEISUT 600<br/>The upgrade file can be downloaded from SAP Service Marketplace -&gt; Download -&gt; Installations and upgrades -&gt; Entry by Application Group -&gt; Country specific Add-Ons -&gt; SAP IS-UT CEE -&gt; SAP IS-UT CEE 600 -&gt; Upgrade To obtain the access for download please create Customer message under component XX-CSC-HR-FI and specify the S user for the access. and list of the particular countries that are planned for implementation</li></ul> <ul><li>Required SAP R/3 and CEEISUT release version:<br/>R/3 Enterprise 47x200 and CEEISUT 472<br/>ECC 600 and CEEISUT 600</li></ul> <ul><li>Add-on supplement<br/>When you upgrade an SAP R/3 release to SAP ECC 600, the system recognizes that an R/3 add-on is installed and an additional CD or an upgrade package is requested for the add-on. As a result, the R/3 add-on is also updated to the corresponding version during the standard upgrade.<br/>CAUTION<br/>If you carry out the SAP ECC 600 upgrade into an R/3 System with the CEEISUT add-on without the data of the add-on upgrade package being read (IS_SELECT phase), the R/3 add-on CEEISUT will no longer work after the upgrade. The system will then be inconsistent, which means that SAP will no longer be able to guarantee that the system will work correctly.<br/></li></ul> <b>3. Errors in the package<br/></b><br/> <b>4. Checks before the upgrade</b><br/> <ul><li>Notes that you should obtain before the installation:</li><br/> <div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\"><tr><td>Add-ons: General conditions</td><td> </td><td> 70228</td></tr> <tr><td></td></tr> </table></div></ul> <p></p> <ul><li>R3trans, tp, kernel, SPAM<br/>Use the current R3trans, tp and kernel patch in the R/3 initial release of your system and import the most recent SPAM update.</li></ul> <ul><li>Component Support Packages to be included in the upgrade<br/>In addition to the Support Packages contained in SAP ECC 600, at least the Support Packages of the following components are required for the upgrade:</li><br/> <div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\"><tr><td></td></tr> <tr><td>IS-UT   SAPK-60006INISUT</td></tr> </table></div></ul> <p></p> <ul><li>Include other component Support Packages</li></ul> <ul><ul><li>The CEEISUT 600 add-on is a non modifying add-on. So no CRTs are needed ( For more detailed information about CRTs, see Note 53902.). But some times dependency of CEEISUT add-on packages on other component support packages can occur since this add-on is based on SAP_APPL, IS-UT components.</li></ul></ul> <ul><ul><li>So other component Support Packages can be included in the upgrade without CEEISUT 600 CRTs.</li></ul></ul> <ul><li>Provide the required component Support Packages<br/>You can download the corresponding data files from the SAP Service Marketplace. The data files must be made available in the &lt;DIR_EPS_ROOT&gt;/IN directory before you start the UPLOAD_REQUEST prepare phase. Refer to Note 83458 for information about downloading Support Packages.</li></ul> <ul><li>Provide the CEEISUT 600 upgrade supplements<br/>Download the CEEISUT 600 AOX package from the SAP Service Marketplace and unpack it into the &lt;DIR_EPS_ROOT&gt;/IN directory.<br/>Info: The XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX.PAT file should be in the &lt;DIR_EPS_ROOT&gt;/IN directory (at the latest after the UPLOAD_REQUEST phase).<br/></li></ul> <b>5. Enhancements to the upgrade</b><br/> <ul><li>Enhancements to the prepare</li></ul> <ul><ul><li>IS_SELECT phase<br/>Select the upgrade type for the installed add-ons:</li><br/> <div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\"><tr><td>CEEISUT with AOX:</td><td> \"Upgrade with SAINT Package\"</td></tr> </table></div></ul></ul> <p></p> <ul><ul><li>BIND_PATCH phase<br/>This phase displays which Support Packages were found in the &lt;DIR_EPS_ROOT&gt;/IN directory and can be included in the upgrade. Check whether the Support Packages mentioned in Section 4. \"Checks before the upgrade\" have been included in the upgrade.</li></ul></ul> <ul><li>Enhancements to SAPup</li></ul> <ul><ul><li>ADDONKEY_CHK Phase<br/>The password for CEEISUT 600 is required and reference is made to Note 86985 here. The current note 1006196 is referred to there for the CEEISUT 600 constellation and SAP ECC 600.<br/>The password is 4383546.<br/></li></ul></ul> <b>6. Problems after the import<br/></b><br/> <b>7. Actions after the upgrade</b><br/> <ul><li>SAP R/3 Enterprise Extension Set (activation switch) and SAP R/3 Industry solutions.</li></ul> <ul><ul><li>no activation of any Enterprise Extension Set(s) or Industry solution(s) after this upgrade is needed</li></ul></ul> <ul><li>Importing languages after the upgrade</li></ul> <ul><ul><li>the add-on upgrade package already covers language dependent data. No additional add-on language package is needed See the \"Language Support\" section.</li></ul></ul> <ul><li>Import the Support Packages after the upgrade</li></ul> <ul><ul><li>The CEEISUT 600 add-on does not modify any component. Support Packages of any other component can be imported without CEEISUT 600 CRTs.</li></ul></ul> <ul><li>Generation error</li></ul> <ul><ul><li>No generation errors are known of the CEEISUT 600 add-on upgrade package.</li></ul></ul> <ul><li>Delivery Customizing:</li></ul> <ul><ul><li>Check country specific notes concerning customizing<br/></li></ul></ul> <b>8. Language Support</b><br/> <ul><li>In addition to English, the following languages are supported by CEEISUT 600: Bulgarian, Czech, Hungarian, Polish, Romanian, Russian, Slovakian, Ukrainian. These languages are included directly in the CEEISUT 600 add-on upgrade package. So no additional CEEISUT 600 add-on language package is needed during upgrade.<br/>If you want to install one of these languages later proceed as follows:<br/>The required standard language of SAP ECC 600 must already be installed before you install the add-on language files. The add-on language import must be carried out in accordance with the \"SAP Web AS 7.0 Language Transport\" guidelines. Check note 195442<br/></li></ul></div></div>", "noteVersion": 4}, {"note": "2330962", "noteTitle": "2330962 - CEEISUT Retrofit: Business Functions - Modifying Assignments for CEEISUT 600", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>The SAP IS-UT CEE add-on (sw. component CEEISUT) has been integrated to S4H On Premise Edition as of version S4H OP 1610 (sw. component versions FI-CA 801 / IS-UT 801). New switches and Business Functions have been provided to make more objects such as Appends, Area Menues, IMG nodes etc. switchable with the corresponding Industry. Having the CEEISUT add-on applied the new BFs need to be implemented and activated before migration to S4H in order not lose any data from the appended tables. See also note 2323221. Unfortuntaly Business Functions can be asigned to a Business Function Set only from level of the Business Function Set and Switches can be assigned to Business Functions only from level of Business Function in ERP 6.0, EhP2 and EhP3 so in order not make the CEEISUT add-on modifying such assignment are shipped just via this modifying note and not via the add-on SPs.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>CEEISUT, Pre-Check, S4TC</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>The SAP IS-UT CEE add-on (sw. component CEEISUT) is implemented. You want to migrate to S4H.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Implement manual correction instructions of this note</p>", "noteVersion": 1}, {"note": "1122857", "noteTitle": "1122857 - Turkish functionality for IS-UT 600: Address modification", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>Enhance the existing SAP address structure for Turkey.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>IS-U Utilities Turkey Address Management</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>The address maintenance functions and regional structure have been extended for the Turkish localization of IS-U. New objects are shipped with the add-on service packs for CEEISUT 600(see related note 1122854) IS-UT 600, however modifications are required as well, for the new functions to be usable. These modifications - described in this note - have to be carried out manually.<br/>As a prerequisite you have to install add-on CEEISUT release 600 with available add-on support patches (AOPs). It contains IS-U and IS-T localization for Central and Eastern Europe.<br/>Notification: if you are going to run both the solutions HCM localization and IS-U Address Management for Turkey in one system then please contact with SAP Support.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>Note:<br/>1. These local enhancements are part of the Turkish country version of IS-UT delivered in add-on CEEISUT<br/>2. Local support of SAP Turkey is responsible for maintenance and hotline support of those enhancements for Turkey only.<br/>3. These enhancements are not released for any other country.<br/><br/>For new functionality provided by this note, please refer to the documentation of the Turkish localization, which you can find attached to this note.</p> <ul><li>Install add-on SAP IS-UT CEE 600 (software component CEEISUT 600), see related notes.</li></ul> <ul><li>Install the latest service pack. (see related note 1122854)</li></ul> <ul><li>Check the related notes, whether any further corrections might be available in relation to this functionality.</li></ul> <ul><li>Install the correction instructions of this note. This will automatically modify program code of standard programs and function modules, which had to be changed for the localization.</li></ul><ul><li>Modifications to other types of objects (screens, search helps, etc.) can not be included in correction instructions, so you will need to perform these modifications manually. The required activities are described in the documentation of the Turkish localization, which you can find atteched to this note.</li></ul> <p><br/>-----------------------------------------------------------------------THIS MODIFICATION IS OPTIONAL!!<br/>This modification is not part of the IS-U localization Turkey so the customer can apply it if wanna do.<br/>The following modification is related to the Orsteil screen field label in transaction ES55: the screen field label with (Orsteil) is nice to be changed to (Mahalle).<br/>To carry out this modification please follow these steps:<br/>1. Go to SE80 and display the function group SZA1.<br/>2. Display the screen 0301 and then the element list tab.<br/>3. Switch to change mode.<br/>3. Put the cursor on the ADDR1_DATA-CITY2  text(!) element and then press the Attributes button. <br/>4. In the dialog box at the 3rd line at text please change from (Orsteil) to (Mahalle) and click ok.<br/>5. Save the modification and activate it.<br/>----------------------------------------------------------------------<br/>-----------------------------------------------------------------------<br/></p></div>", "noteVersion": 24}, {"note": "1660394", "noteTitle": "1660394 - CEEISUT 606: Component Support Packages", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>Information about component support packages for CEEISUT 606</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Support Packages, Add-on, AOP, CSP, CRT, SPAM, CEEISUT 606</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>You have a system with the CEEISUT Add-on installed.<br/>Component support packages (CSPs) contain corrections and fixes for the CEEISUT Add-on or previous component support packages.<br/>This note contains information about component support packages for the CEEISUT Add-on.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong>Content</strong></p>\n<p><br/>I.   General information <br/>   1. Downloading Add-on support packages<br/>   2. SPAM update<br/>   3. Import of support packages<br/>II. Schedule<br/>III. Problems while importing Add-on support packages<br/>IV.  Postprocessing the import of Add-on support packages</p>\n<p><strong>I. General information</strong></p>\n<p><br/>The content of this note is specific for CEEISUT 606. If you have not installed this addon in your SAP system, this note is not relevant for you.<br/>More information on addon support packages is available in note 160168.<br/><br/><strong>CAUTION: This note is updated constantly.</strong><br/>Before you import Add-on support packages, read the current version of this note.</p>\n<ol>\n<li>Downloading Add-on support packages<br/>Add-on support packages are available in the SAP Service Marketplace:<br/><a href=\"http://service.sap.com/patches\" target=\"_blank\">http://service.sap.com/patches</a> -&gt; Browse our Download Catalog -&gt; Country-specific Add-ons -&gt;  SAP IS-UT CEE -&gt;  SAP IS-UT CEE 606</li>\n<li>SPAM update<br/>Import the latest SPAM/SAINT update. Make sure that you have imported the latest SPAM/SAINT update into your system. If a newer version is available on the SAP Service Marketplace, import the new SPAM/SAINT update.</li>\n<li>Import of support packages<br/>Import support packages using Transaction SPAM. See the corresponding online documentation for more information.</li>\n</ol>\n<p><strong>II. Schedule</strong></p>\n<p>You can find a schedule when to expect support packages in this table:</p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"0\" cellspacing=\"1\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td>\n<p><strong>Support Package</strong></p>\n</td>\n<td>\n<p><strong>Date</strong></p>\n</td>\n<td>\n<p><strong>Comment</strong></p>\n</td>\n</tr>\n<tr>\n<td>\n<p>SAPK-60617INCEEISUT</p>\n</td>\n<td>\n<p>03.11.2018</p>\n</td>\n<td> </td>\n</tr>\n<tr>\n<td>\n<p>SAPK-60616INCEEISUT</p>\n</td>\n<td>\n<p>23.04.2018</p>\n</td>\n<td> </td>\n</tr>\n<tr>\n<td>\n<p>SAPK-60615INCEEISUT</p>\n</td>\n<td>\n<p>23.10.2017</p>\n</td>\n<td> </td>\n</tr>\n<tr>\n<td>\n<p>SAPK-60614INCEEISUT</p>\n</td>\n<td>\n<p>24.04.2017</p>\n</td>\n<td>requires IS-UT 606 SP17 or 617 SP12 or 618 SP02</td>\n</tr>\n<tr>\n<td>\n<p>SAPK-60613INCEEISUT</p>\n</td>\n<td>\n<p>24.04.2017</p>\n</td>\n<td><strong>Its mandatory that SP 13 / 14 must be installed together.</strong></td>\n</tr>\n<tr>\n<td>\n<p>SAPK-60612INCEEISUT</p>\n</td>\n<td>\n<p>25-apr-2016</p>\n</td>\n<td> </td>\n</tr>\n<tr>\n<td>\n<p>SAPK-60611INCEEISUT</p>\n</td>\n<td>\n<p>11-Sep-2015</p>\n</td>\n<td>\n<p> </p>\n</td>\n</tr>\n<tr>\n<td>\n<p>SAPK-60610INCEEISUT</p>\n</td>\n<td>\n<p>21-Apr-2015</p>\n</td>\n<td>\n<p> requires SAPK-60614INISUT or SAPK-61707INISUT or IS-UT 618</p>\n</td>\n</tr>\n<tr>\n<td>\n<p>SAPK-60609INCEEISUT</p>\n</td>\n<td>\n<p>10-Oct-2014</p>\n</td>\n<td>\n<p> </p>\n</td>\n</tr>\n<tr>\n<td>\n<p>SAPK-60608INCEEISUT</p>\n</td>\n<td>\n<p>21-May-2014</p>\n</td>\n<td>\n<p>requires SAPK-60609INISUT or SAPK-61702INISUT or IS-UT 618</p>\n</td>\n</tr>\n<tr>\n<td>\n<p>SAPK-60607INCEEISUT</p>\n</td>\n<td>\n<p>27-Jan-2014</p>\n</td>\n<td>\n<p>Note 1954087 and SAPKB73105 required</p>\n</td>\n</tr>\n<tr>\n<td>\n<p>SAPK-60606INCEEISUT</p>\n</td>\n<td>\n<p>24-Sep-2013</p>\n</td>\n<td> </td>\n</tr>\n<tr>\n<td>\n<p>SAPK-60605INCEEISUT</p>\n</td>\n<td>\n<p>14-Jun-2013</p>\n</td>\n<td> </td>\n</tr>\n<tr>\n<td>\n<p>SAPK-60604INCEEISUT</p>\n</td>\n<td>\n<p>23-Jan-2013</p>\n</td>\n<td>requires SAPK-60604INISUT or IS-UT 617 or IS-UT 618</td>\n</tr>\n<tr>\n<td>\n<p>SAPK-60603INCEEISUT</p>\n</td>\n<td>\n<p>25-Sep-2012</p>\n</td>\n<td> </td>\n</tr>\n<tr>\n<td>\n<p>SAPK-60602INCEEISUT</p>\n</td>\n<td>\n<p>23-May-2012</p>\n</td>\n<td> </td>\n</tr>\n<tr>\n<td>\n<p>SAPK-60601INCEEISUT</p>\n</td>\n<td>\n<p>26-Jan-2012</p>\n</td>\n<td> </td>\n</tr>\n</tbody>\n</table></div>\n<p><strong>III. Problems during import of Add-on support packages</strong><br/><br/></p>\n<ol>\n<li>SAPK-60607INCEEISUT<br/>Note 1954087 is required before implementation of SAPK-60607INCEEISUT. Transaction SPAM will require a password to ensure this. This password is 34103A0960<br/>.</li>\n<li>SAPK-60608INCEEISUT<br/>These generation errors might occur. To solve that, see IV.1. below.<br/>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"3\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td>Program /SAPCE/SAPLFKSK_EVENTS, Include /SAPCE/LFKSK_EVENTSU04: Syntax error in line 000027<br/>Type 'FKKSK_STR_SDATA' is unknown</td>\n</tr>\n</tbody>\n</table></div>\n<br/>.</li>\n<li>SAPK-60610INCEEISUT<br/>These generation errors might occur. To solve that, see IV.2. below<br/>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"3\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td>Program /SAPCE/FK_SK_EVYBER: Syntax error in line 000021<br/>Field 'T7SK52' is unknown. It is neither in one of the specified tables nor defined by a 'DATA' stat</td>\n</tr>\n</tbody>\n</table></div>\n</li>\n<li>SAPK-60612INCEEISUT<br/>These generation errors might occur. To solve see the note <strong>2153215, 2228198</strong><br/>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"3\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td>\n<p>Program /SAPCE/CL_EDOCUMENT_ISU_SVN===CP:  Include /SAPCE/CL_EDOCUMENT_ISU_SVN===CU Syntax error in line 000007<br/>Class 'CL_EDOC_PROCESS' IS unknown</p>\n<p>Program /SAPCE/IUHU_CL_AUDIT_FUNC===CP, Include /SAPCE/IUHU_CL_AUDIT_FUNC===CU: Syntax error in line 000009<br/>Type 'IF_EX_HU_AUDIT_REPORT' is unknown</p>\n</td>\n</tr>\n</tbody>\n</table></div>\n</li>\n<li><span 'times=\"\" ar-sa;\"=\"\" calibri',sans-serif;=\"\" calibri;=\"\" en-us;=\"\" minor-latin;=\"\" mso-ansi-language:=\"\" mso-bidi-font-family:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" mso-fareast-theme-font:=\"\" new=\"\" roman';=\"\">SAPK-60615INCEEISUT </span><br/>These generation errors might occur. To solve see the note <strong><span #385723;=\"\" 'times=\"\" ar-sa;\"=\"\" calibri',sans-serif;=\"\" calibri;=\"\" color:=\"\" en-us;=\"\" minor-latin;=\"\" mso-ansi-language:=\"\" mso-bidi-font-family:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" mso-fareast-theme-font:=\"\" new=\"\" roman';=\"\">2446369</span>, <span #385723;=\"\" 'times=\"\" ar-sa;\"=\"\" calibri',sans-serif;=\"\" calibri;=\"\" color:=\"\" en-us;=\"\" minor-latin;=\"\" mso-ansi-language:=\"\" mso-bidi-font-family:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" mso-fareast-theme-font:=\"\" new=\"\" roman';=\"\">2427168</span></strong><br/>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"3\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td>\n<p><em>Program /SAPCE/CL_EDOCUMENT_ISU_SVN===CP, Include /SAPCE/CL_EDOCUMENT_ISU_SVN===CM00J: Syntax error in line 000024</em></p>\n<p><em>The data object 'MS_EDOCUMENT' does not have a component called 'EDOCUMENT_CLASS'.</em></p>\n<p><em>Program /SAPCE/CL_EDOC_FACTORY_ISU_SVNCP, Include /SAPCE/CL_EDOC_FACTORY_ISU_SVNCU: Syntax error in line 000003</em></p>\n<p><em>Type 'CL_EDOC_FACTORY' is unknown</em></p>\n<p><em>Program /SAPCE/EDOC_UPDATE_TABLE, Include /SAPCE/EDOC_UPDATE_TABLE_C01: Syntax error in line 000049</em></p>\n<p><em>The data object 'GS_EDOCUMENT' does not have a component called 'EDOCUMENT_CLASS'.</em></p>\n</td>\n</tr>\n</tbody>\n</table></div>\n</li>\n<li>The following Notes are required before implementation of SAPK-60617INCEEISUT.</li>\n</ol>\n<p>  SAPNOTE No 2662557,0002662198,0002659385,0002650662,2650058,2645177,2638811,2637524,2636499,2635824,2632796,2623088,</p>\n<p>2601568,2593403,2592185,2592164,2589248,2587421,2583335,2582557,2561564,2550216.</p>\n<p> These generation errors might occur. By implementation of above notes resolve the errors</p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"3\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td>\n<p>/SAPCE/CL_EDOCUMENT_ISU_HU           Class /SAPCE/CL_EDOCUMENT_ISU_HU, Public Section  Type \"CL_EDOCUMENT_HU_INV\" is unknown</p>\n<p>/SAPCE/CL_EDOC_FACTORY_ISU_HU       Class /SAPCE/CL_EDOCUMENT_ISU_HU, Public Section  Type \"CL_EDOCUMENT_HU_INV\" is unknown</p>\n<p>/SAPCE/CL_EDOC_MAP_HU_ISU              Class /SAPCE/CL_EDOC_MAP_HU_ISU, Public Section     Type \"CL_EDOC_MAP_HU_INV\" is unknown</p>\n</td>\n</tr>\n</tbody>\n</table></div>\n<p><strong> 7. </strong><span>SAPK-60619INCEEISUT:</span></p>\n<p><span> Below mentioned generation errors might occur while impoing the package. In case genration error occurs please implement following notes, in order to   relove the generation error.</span></p>\n<p>2764737,2763851,2764791,2747446.</p>\n<div class=\"table-responsive\"><table border=\"1\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td>Program /SPACE/CL_EDOC_MAP_HU_ISU=====CP,  Include /SPACE/CL_EDOC_MAP_HU_ISU=====CO: syntax error in line 000005<br/>Type 'EDO_HU_I11_OPERATION_TYPE' is unknown</td>\n</tr>\n</tbody>\n</table></div>\n<p><strong>IV. Postprocessing the import of Add-on support packages</strong></p>\n<ol>\n<li>SAPK-60608INCEEISUT<br/>The support package requires the following FI-CA notes if you are using the Slovakian localization:<br/><br/>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"3\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"80\">\n<p>1942466</p>\n</td>\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"644\">\n<p>SK FI-CA Electronic VAT Ledger</p>\n</td>\n</tr>\n<tr>\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"80\">\n<p>1954895</p>\n</td>\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"644\">\n<p>SK FI-CA Electronic VAT Ledger</p>\n</td>\n</tr>\n<tr>\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"80\">\n<p>1958171</p>\n</td>\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"644\">\n<p>Slovakia: Authorization Object F_KKID_SK cannot be created in customer environment</p>\n</td>\n</tr>\n<tr>\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"80\">\n<p>1955535</p>\n</td>\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"644\">\n<p>Correction of SK FI-CA VAT Electronic Ledger</p>\n</td>\n</tr>\n<tr>\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"80\">\n<p>1957599</p>\n</td>\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"644\">\n<p>Correction #2 SK FI-CA Electronic VAT Ledger</p>\n</td>\n</tr>\n<tr>\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"80\">\n<p>1960475</p>\n</td>\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"644\">\n<p>Correction #3 SK FI-CA Electronic VAT Ledger</p>\n</td>\n</tr>\n<tr>\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"80\">\n<p>1961275</p>\n</td>\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"644\">\n<p>Correction #4 SK FI-CA Electronic VAT Ledger</p>\n</td>\n</tr>\n<tr>\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"80\">\n<p>1960997</p>\n</td>\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"644\">\n<p>Correction #5 SK FI-CA Electronic VAT Ledger</p>\n</td>\n</tr>\n</tbody>\n</table></div>\n<br/>.</li>\n<li>SAPK-60610INCEEISUT<br/>The support package requires the following FI-CA notes if you are using the Slovakian localization: 2005304, 1995867, 2021860, 2042944</li>\n</ol>", "noteVersion": 27}, {"note": "1014954", "noteTitle": "1014954 - Russian specific functionality for IS-UT 600 (collective)", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>Russian specific functionality for IS-U 600 and EhPs (collective)</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Country specific functionality for Russia for IS-UT 600 and Enhancement Packages (EhPs) is delivered in add-on SAP IS-UT CEE release 600 and higher. See related note 1014997 for information about release strategy for this add-on, installation, upgrade and support packages. Please implement the highest add-on support package available for software component CEEISUT. The minimal support package level for the functionality listed below is:</p>\n<ul>\n<li>SP 18 for CEEISUT 600</li>\n</ul>\n<ul>\n<li>SP 06 for CEEISUT 604</li>\n</ul>\n<p><br/>The functionality listed below is currently available for Russia. See related notes and/or attached documentation for details about delivered functionality and customizing possibilities:</p>\n<ul>\n<li>Cash desk reporting (Cashier report, Cash ledger)</li>\n</ul>\n<ul>\n<li>Tax reporting (Sales ledger, Purchase ledger, Invoice journal) including additional sheets for corrections of receivables</li>\n</ul>\n<ul>\n<li>Possibility to separate transformer losses from grid losses</li>\n</ul>\n<ul>\n<li>Possibility to bill separately shares of consumption registered by one meter, including enhanced installation groups</li>\n</ul>\n<ul>\n<li>Benefits including benefit mass change</li>\n</ul>\n<ul>\n<li>Advance payments are implemented using Budget Billing for Industrial Customers (BBP 5)</li>\n</ul>\n<ul>\n<li>Seals management</li>\n</ul>\n<ul>\n<li>Extension of master data for business processes</li>\n</ul>\n<ul>\n<li>Extension of master data for reporting. Make sure that you have activated properly BC Set /CEEISUT/ISU_RU_01 or the hierarchical BC Set for Russia mentioned below.</li>\n</ul>\n<ul>\n<li>Accounts receivable report</li>\n</ul>\n<ul>\n<li>Extension of address is not delivered by add-on CEEISUT. You have to perform a modification in your system as described in note 939002</li>\n</ul>\n<ul>\n<li>Interface KLADR-DB. Extension of address is required for this interface.</li>\n</ul>\n<ul>\n<li>Legal forms: Invoice Form</li>\n</ul>\n<ul>\n<li>Legal forms: Invoice FI-CA</li>\n</ul>\n<ul>\n<li>Legal forms: Payment forms (payment request and incasso order)</li>\n</ul>\n<ul>\n<li>Legal forms: Incasso orders register</li>\n</ul>\n<ul>\n<li>Legal forms: Cash desk forms (cash receipt order KO-1 and cash expenditure order KO-2)</li>\n</ul>\n<ul>\n<li>2-reg (RAO UES and state statistical reporting) - information about grid losses classified by voltage level</li>\n</ul>\n<ul>\n<li>26-zkh (RAO UES and state statistical reporting) - information about citizen's benefits for housing and communal services</li>\n</ul>\n<ul>\n<li>Several invoices for one installation</li>\n</ul>\n<ul>\n<li>Distribution of non-time-based consumption between time-based ones</li>\n</ul>\n<ul>\n<li>Adapted settlement procedure</li>\n</ul>\n<ul>\n<li>Energy consumption sheet</li>\n</ul>\n<p><br/>New BC Sets were delivered as part of the localization, which have to be activated in your system. Either activate hierarchical BC set<br/>/CEEISUT/ISU_RU or BC sets for individual topics above.<br/>Optionally you can also activate hierarchical BC Set /CEEISUT/ISU_RU_11 for time zones.<br/><br/>Note:</p>\n<ul>\n<li>There are no modifications of the SAP standard objects delivered in add-on CEEISUT</li>\n</ul>\n<ul>\n<li>The enhancements are using reserved name spaces /SAPCE/* and /CEEISUT/* which do not overlap with customer name range or other project development (according to note 72483)</li>\n</ul>\n<ul>\n<li>These enhancements should not interfere with any add-on support packages (AOP) for IS-UT.</li>\n</ul>\n<ul>\n<li>Local support is responsible for maintenance and hotline support of those enhancements for Russia</li>\n</ul>\n<ul>\n<li>These enhancements are not released for any other country</li>\n</ul></div>", "noteVersion": 14}, {"note": "1512398", "noteTitle": "1512398 - Archiving objects for localization add-on SAP IS-UT CEE", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>After several years of usage of add on SAP IS-UT CEE the number of data in database tables is growing up significiantly. It causes performance problems.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Archiving, CEEISUT, Tax Voucher, Benefit master data, Benefit  posting, Subsidy master data, Subsidy file, Subsidy posting, Social benefits, Seals Management, Internal seals</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>System performance decreases due to large amount of old data in tables delivered in add-on SAP IS-UT CEE (software component CEEISUT).</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>1. Implement the last available support package for the add-on.<br/>2. Read documentation about archiving objects which are currently delivered. The documentation is attached to his note as a PDF file.<br/>3. Activate one of following BC sets:<br/>- /CEEISUT/ISU_ARCH if you use IS-U<br/>- /CEEISUT/IST_ARCH if you use IS-T<br/>4. Set-up customizing of retention periods for relevant archiving objects in SAP Customizing Implementation Guide:<br/>- For Archiving objects related to FI-CA under following path:<br/>SAP IMG-&gt;Financial Accounting-&gt;Contract Accounts Receivable and Payable-&gt;Basic Functions-&gt;Archiving for the SAP IS-UT CEE Add-On<br/>- For Archiving objects related to IS-U under following path:<br/>SAP IMG-&gt; SAP Utilities-&gt;Tools-&gt; Archiving-&gt;Archiving for the SAP IS-UT CEE Add-On<br/>5. Run data archiving and deletion for relevant archiving objects (see documentation)<br/><br/>Following archiving objects are currently available (this list will be updated whenever a new archiving object is added):</p>\n<p><strong>Archiving of Benefit Master Data</strong></p>\n<p>- archiving object /SAPCE/BEM<br/>- archived tables: /SAPCE/IURU_BENC, /SAPCE/IURU_EBEN, /SAPCE/IURU_EVNR</p>\n<p><strong>Archiving of Benefit Posting</strong></p>\n<p>- archiving object /SAPCE/BEP<br/>- archived tables: /SAPCE/IURU_BPOS, /SAPCE/IURU_BCOD, /SAPCE/IURU_BPAU</p>\n<p><strong>Archiving of Subsidy Master Data</strong></p>\n<p>- archiving object /SAPCE/SBM<br/>- archived tables: /SAPCE/IUUA_ESUB</p>\n<p><strong>Archiving of Subsidy File Data</strong></p>\n<p>- archiving object /SAPCE/SBF<br/>- archived tables: /SAPCE/IUUA_SLGH, /SAPCE/IUUA_SLGD</p>\n<p><strong>Archiving of Subsidy Posting Data</strong></p>\n<p>- archiving object /SAPCE/SBP<br/>- archived tables: /SAPCE/IUUA_SCOD, /SAPCE/IUUA_SPAU, /SAPCE/IUUA_SPOS</p>\n<p><strong>Archiving of Social Benefits</strong></p>\n<p>- archiving object /SAPCE/SBN<br/>- archived tables: /SAPCE/IUUA_SBEN<br/><br/></p>\n<p><strong>Archiving of Tax Invoices (Ukraine)</strong></p>\n<p>- archiving object /SAPCE/TXV<br/>- archived tables: /SAPCE/FKUA_TXKO, /SAPCE/FKUA_TXOP, /SAPCE/FKUA_TXR6, /SAPCE/FKUA_MDDP, /SAPCE/FKUA_TXSQ</p>\n<p><strong>Archiving of Seals Management</strong></p>\n<p>- archiving object /SAPCE/SEA<br/>- archived tables: /SAPCE/IURU_SEAL</p>\n<p><strong>Archiving of Internal Seals</strong></p>\n<p>- archiving object /SAPCE/INS<br/>- archived tables: /SAPCE/IURU_INSL<br/><br/></p>\n<p><strong>Archiving of Exchange Rate Difference for VAT reporting Russia</strong></p>\n<p>- archiving object /SAPCE/KDF<br/>- archived tables: /SAPCE/FKRUKDF <br/><br/></p>\n<p><strong>Archiving of Non-delivered credit memos</strong></p>\n<p>- archiving object /SAPCE/NDC<br/>- archived tables: /SAPCE/FK_NDCRN</p>\n<p><strong>Archiving SIPO Payment Format Data (Czech Republic)</strong></p>\n<p>- archiving object /SAPCE/CZS<br/>- archived tables: /SAPCE/FK_CZ_SIH, /SAPCE/FK_CZ_SIP, /SAPCE/FK_CZ_SIK</p>\n<p><strong>Archiving File Data of SIPO Payment Formats (Slovakia)</strong></p>\n<p>- archiving object /SAPCE/SKS<br/>- archived tables: /SAPCE/FK_SK_SIH</p>\n<p><strong>Archiving Master Data of SIPO Payment Formats (Slovakia)</strong></p>\n<p>- archiving object /SAPCE/SK0<br/>- archived tables: /SAPCE/FK_SK_SI0</p>\n<p><strong>Archiving B Cheque Payment Format Data (Czech Republic)</strong></p>\n<p>- archiving object /SAPCE/CZB<br/>- archived tables: /SAPCE/FK_SLOH</p>\n<p> </p>\n<p> </p>\n<p> </p>\n<p> </p></div>", "noteVersion": 12}, {"note": "1012272", "noteTitle": "1012272 - Ukrainian specific functionality IS-U 4.72 (collective)", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>Ukrainian specific functionality for IS-U release 4.72 (collective)</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>Country specific functionality for Ukraine for IS-U is delivered in add-on CEEISUT for release 4.72. See related note 774230 for information about availability and installation rules of this add-on. Please implement the highest add-on support patch available for CEEISUT. See related notes for corresponding AOPs.<br/><br/>The functionality listed in related notes is currently available for Ukraine. See related notes and/or attached documentation for details about delivered functionality and customizing possibilities.<br/><br/>New BC Sets were delivered as part of the localization, which have to be activated in your system. Either activate hierarchical BC set<br/>/CEEISUT/ISU_UA or BC sets for individual topics.<br/><br/>Note:</p> <ul><li>There are no modifications of the SAP standard objects delivered in add-on CEEISUT</li></ul> <ul><li>The enhancements are using reserved name spaces /SAPCE/* and /CEEISUT/* which do not overlap with customer name range or other project development (according to note 72483)</li></ul> <ul><li>These enhancements should not interfere with any add-on support packages (AOP) for IS-U.</li></ul> <ul><li>Local support is responsible for maintenance and hotline support of these enhancements</li></ul> <ul><li>These enhancements are not released for any other country</li></ul></div>", "noteVersion": 2}, {"note": "1004617", "noteTitle": "1004617 - Add-on Support Packages for add-on SAP IS-UT CEE 472", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>SAP IS-UT CEE 472 add-on support pack description</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>Software component CEEISUT</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>You have installed add-on SAP IS-UT 472 for localization of IS-U and IS-T for countries in Central and Eastern Europe. See related note 774230.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p><b>CAUTION: This note is constantly updated.</b><br/><br/>SAP IS-UT CEE 472 AOP    Rel. Date    Level   Precondition<br/>-----------------------------------------------------------------------<br/>SAPK-47201INCEEISUT      06.10.2004   01<br/>SAPK-47202INCEEISUT      21.12.2004   02<br/>SAPK-47203INCEEISUT      03.02.2006   03<br/>SAPK-47204INCEEISUT      09.03.2006   04<br/>SAPK-47205INCEEISUT      04.05.2006   05<br/>SAPK-47206INCEEISUT      29.06.2006  06      SAPKIPUL11 or SAPKIPTL11<br/>SAPK-47207INCEEISUT      28.07.2006   07<br/>SAPK-47208INCEEISUT      07.09.2006   08<br/>SAPK-47209INCEEISUT      05.10.2006   09<br/>SAPK-47210INCEEISUT      02.11.2006   10<br/>SAPK-47211INCEEISUT      29.11.2006   11<br/>SAPK-47212INCEEISUT      30.01.2007   12<br/>SAPK-47213INCEEISUT      29.05.2007   13<br/>SAPK-47214INCEEISUT      09.10.2007   14<br/>SAPK-47215INCEEISUT      21.01.2008   15<br/>SAPK-47216INCEEISUT      14.04.2008   16<br/>SAPK-47217INCEEISUT      03.06.2008   17<br/>SAPK-47218INCEEISUT      04.08.2008   18<br/>SAPK-47219INCEEISUT      19.01.2009   19<br/>SAPK-47220INCEEISUT      20.05.2009   20<br/>SAPK-47221INCEEISUT      30.09.2009   21<br/>SAPK-47222INCEEISUT      25.01.2010   22<br/>SAPK-47223INCEEISUT      24.05.2010   23<br/>SAPK-47224INCEEISUT      30.09.2010   24<br/>SAPK-47225INCEEISUT      20.01.2011   25<br/>SAPK-47226INCEEISUT      24.05.2011   26<br/>SAPK-47227INCEEISUT      07.12.2011   27<br/>SAPK-47228INCEEISUT      30.01.2012   28<br/>SAPK-47229INCEEISUT      29.06.2012 - 29<br/>SAPK-47230INCEEISUT      25.09.2012 - 30<br/>SAPK-47231INCEEISUT      25.01.2013 - 31<br/>SAPK-47232INCEEISUT      CW 05/2014 - planned<br/><br/><br/><br/>***********************************************************************<br/>You can find the add-on Support Packages in SAP Service Marketplace: http://service.sap.com/swdc -&gt; Download -&gt; Support Packages and Patches -&gt; Entry by Application Group -&gt; Country-specific Add-Ons -&gt; SAP IS-UT CEE</p></div>", "noteVersion": 14}, {"note": "554426", "noteTitle": "554426 - Czech & Slovak specific functionality IS-U 4.64 (collective)", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>Czech &amp; Slovak specific functionality IS-U 4.64 (collective)</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>Country specific functionality for Czech Republic and Slovalia for IS-U 4.64 is available.<br/>For details, see related notes.</p></div>", "noteVersion": 3}, {"note": "1005317", "noteTitle": "1005317 - SAP IS-UT CEE 600 installation on SAP ECC 600", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>Add-on Installation of SAP IS-UT CEE 600 on Release SAP ERP Central Component ECC 600 (referred to as SAP ECC 600 in the rest of this note) with Transaction SAINT.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>SAINT, add-on, installation, SAP IS-UT CEE 600, CEEISUT 600</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>Add-on installation with Transaction SAINT.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p><br/><b>This note is updated on a regular basis. Therefore, make sure you obtain the latest version of the note before you start the installation.</b><br/><br/>Contents<br/>  1. Change history<br/>  2. Prerequisites for installing SAP IS-UT CEE 600<br/>  3. Preparing the SAP IS-UT CEE 600 installation<br/>  4. Executing the SAP IS-UT CEE 600 installation<br/>  5. After the SAP IS-UT CEE 600 installation<br/>  6. Language support<br/>  7. Password<br/></p> <b>1. Change history</b><br/> <p>           Date  Topic Short description<br/><br/>01.01.2007 all  General availability<br/>12.03.2008 2,3  CEEISUT 600 released for EhP2<br/>15.05.2008 2  CEEISUT 600 released for EhP3</p> <div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\"><tr><td></td></tr> <tr><td></td></tr> </table> <b>2. Prerequisites for installing SAP IS-UT CEE 600</b><br/> <ul><li>Required release<br/>SAP ERP Central Component 600 (SAP ECC 600) is required.</li></ul> <ul><li>Import the latest SPAM/SAINT update<br/>Make sure that you have imported the latest SPAM/SAINT update into your system. If the SAP Service Marketplace contains a newer version, carry out the new SPAM/SAINT update.</li></ul> <ul><li>Import the latest R3trans and tp<br/>Make sure that you have imported the latest kernel version into your system. If the SAP Service Marketplace contains a newer version, import the latest kernel.</li></ul> <ul><li>Notes that you should obtain before the installation:</li><br/> <div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\"><tr><td>Add-ons: General conditions:</td><td> </td><td> 70228</td></tr> <tr><td>Problems with Transaction SAINT:</td><td> </td><td> 822379</td></tr> </table></div></ul> <p></p> <ul><li>Prerequisites</li></ul> <ul><ul><li>Check whether the following prerequisites have been met in your system:<br/>Required components</li><br/> <div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\"><tr><td>SAP_BASIS</td><td> 700</td></tr> <tr><td>IS-UT</td><td> 600</td></tr> <tr><td>or IS-UT </td><td> 602 - please refere to note 1150868</td></tr> <tr><td>or IS-UT </td><td> 603 - please refere to note 1150868</td></tr> </table></div></ul></ul> <p></p> <ul><ul><li>Industry solution / Business Function Set Utilities has to be activated (see Note 874416).</li></ul></ul> <ul><ul><li>Required Support Package components:</li><br/> <div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\"><tr><td>IS-UT</td><td> 600</td><td> SAPK-60006INISUT</td></tr> <tr><td>or IS-UT</td><td> 602</td><td> -</td></tr> <tr><td>or IS-UT</td><td> 603</td><td> -</td></tr></table></div></ul></ul> <p></p> <ul><ul><li>If this Support Package has not yet been installed, it can be included in the installation of SAP IS-UT CEE 600. For more information, see Note 83458.</li></ul></ul> <ul><ul><li>The SAP IS-UT CEE 600 add-on does not modify any standard component.</li></ul></ul> <ul><li>Additional information about the installation:</li><br/> <div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\"><tr><td>See the corresponding notes assigned to following components:</td></tr> <tr><td>XX-CSC-BG-IS-U</td></tr> <tr><td>XX-CSC-CZ-IS-T</td></tr> <tr><td>XX-CSC-CZ-IS-U</td></tr> <tr><td>XX-CSC-HU-IS-T</td></tr> <tr><td>XX-CSC-HU-IS-U</td></tr> <tr><td>XX-CSC-PL-IS-U</td></tr> <tr><td>XX-CSC-RO-IS-U</td></tr> <tr><td>XX-CSC-RU-FICA</td></tr> <tr><td>XX-CSC-RU-IS-U</td></tr> <tr><td>XX-CSC-SK-IS-T</td></tr> <tr><td>XX-CSC-SK-IS-U</td></tr> <tr><td>XX-CSC-TR-IS-U</td></tr> <tr><td>XX-CSC-UA-IS-U</td></tr> <tr><td></td></tr> </table></div></ul> <b>3. Preparing the SAP IS-UT CEE 600 installation</b><br/> <ul><li>Make the SAP IS-UT CEE 600 add-on available<br/>The installation package for SAP IS-UT CEE 600 is not sent automatically to all customers. The installation file can be downloaded from SAP Service Marketplace -&gt; Download -&gt; Installations and upgrades -&gt; Entry by Application Group -&gt; Country specific Add-Ons -&gt; SAP IS-UT CEE -&gt; SAP IS-UT CEE 600 -&gt; Installation. To obtain the access for download please create Customer message under component XX-CSC-CZ-IS-U and specify the S user for the access. and list of the particular countries that are planned for implementation</li></ul> <ul><ul><li>Log on as user</li><br/> <div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\"><tr><td>&lt;sid&gt;adm</td><td> on UNIX</td></tr> <tr><td>&lt;SID&gt;OFR</td><td> on AS/400</td></tr> <tr><td>&lt;SID&gt;adm</td><td> on Windows</td></tr> </table></div></ul></ul> <p></p> <ul><ul><li>Switch to the &lt;DIR_EPS_ROOT&gt; directory of your R/3 system (usually /usr/sap/trans/EPS). The &lt;DIR_EPS_ROOT&gt; directory is displayed after you execute the RSPFPAR report on DIR_EPS_ROOT.</li></ul></ul> <ul><ul><li>Switch to the higher-level &lt;DIR_EPS_ROOT&gt; directory.</li></ul></ul> <ul><ul><li>Unpack the SAR archive &lt;SAR archive&gt; downloaded from SAP Service Market Place using the following statement:</li><br/> <div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\"><tr><td>SAPCAR -xvf /&lt;CD_DIR&gt;/DATA/&lt;ARCHIV&gt;.SAR</td><td> </td><td> </td><td> on UNIX</td></tr> <tr><td>SAPCAR '-xvf /QOPT/&lt;VOLID&gt;/DATA/&lt;ARCHIV&gt;.SAR'</td><td> on AS/400</td></tr> <tr><td>SAPCAR -xvf &lt;CD_DRIVE&gt;:\\DATA\\&lt;ARCHIV&gt;.SAR</td><td> </td><td> on Windows</td></tr> <tr><td></td></tr> <tr><td>The file called CSN0120061532_0025272.PAT should now be in the &lt;DIR_EPS_ROOT&gt;/IN directory.</td></tr> <tr><td></td></tr> <tr><td>- follow note 1118803 if an IS-UT instance of ERP enhancement packages is already applied</td></tr> <tr><td></td></tr> </table></div></ul></ul> <b>4. Executing the SAP IS-UT CEE 600 installation</b><br/> <ul><li>User to be used<br/>Log on to the 000 client on your R/3 System as a user with SAP_ALL rights. Do not use the SAP* or DDIC user.</li></ul> <ul><li>Display the installation package add-ons<br/>Call Transaction SAINT and choose 'Start' and 'Load'.<br/>After the list of uploaded packages is displayed, you can return to the initial screen of Transaction SAINT again by choosing 'Back' or by pressing F3.</li></ul> <ul><li>Start the installation<br/>Call Transaction SAINT and start it. Select the SAP IS-UT CEE 600 add-on and then choose 'Continue'. If all the required conditions for importing the add-on are met, the corresponding queue will now be displayed. The queue consists of the installation package and can also contain Support Packages and Add-On Packages.<br/>To start the installation process, choose 'Continue'. For more information, call Transaction SAINT and choose the Information button in the application toolbar.<br/></li></ul> <b>5. After the SAP IS-UT CEE 600 installation</b><br/> <ul><li>SAP R/3 Enterprise Extension Set (activation switch) and SAP R/3 Industry solutions.</li></ul> <ul><ul><li>no activation of any Enterprise Extension Set(s) or Industry solution(s) is needed after installation of SAP IS-UT CEE 600.</li></ul></ul> <ul><li>Importing languages after the installation</li></ul> <ul><ul><li>the add-on installation package already covers language dependent data. No additional add-on language package is needed See the \"Language Support\" section.</li></ul></ul> <ul><li>Import the Support Packages after the installation</li></ul> <ul><ul><li>The SAP IS-UT CEE 600 add-on does not modify any component. Support Packages of any other component can be imported without SAP IS-UT CEE 600.</li></ul></ul> <ul><li>Generation error</li></ul> <ul><ul><li>No generation errors are known of the SAP IS-UT CEE 600 add-on installation package</li></ul></ul> <ul><li>Delivery Customizing:</li></ul> <ul><ul><li>Check country specific notes concerning customizing<br/></li></ul></ul> <b>6. Language Support</b><br/> <ul><li>In addition to English, the following languages are supported by SAP IS-UT CEE 600: Bulgarian, Czech, Hungarian, Polish, Romanian, Russian, Slovakian, Ukrainian. These languages are included directly in the SAP IS-UT CEE 600 add-on installation package. So no additional SAP IS-UT CEE 600 add-on language package is needed during installation.<br/>If you want to install one of these languages later (means after the add-on installation package is already installed) proceed as follows:<br/>The required standard language of SAP ECC 600 must already be installed before you install the add-on language files. The add-on language import must be carried out in accordance with the \"SAP Web AS 7.0 Language Transport\" guidelines. Check note 195442.<br/></li></ul> <b>7. Password</b><br/> <ul><li>During the installation, you will be asked to enter a password. This password is B891DC0823.<br/></li></ul></div></div>", "noteVersion": 7}, {"note": "1014960", "noteTitle": "1014960 - Polish specific functionality for IS-UT 600 (collective)", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>!!! This note is OBSOLETE! Replaced with note 1866443</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>Additional development needed to cover Polish legal requirements<br/>Polish version, IS-UT, Polish specific, VAT register, statistical<br/>reports: G-11e, G-10.4k, G-10.8, partner balances, deferred revenues</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>Customer uses IS-UT 600 in Poland</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>See note 1866443<br/>Country specific functionality for Poland for IS-UT 600 is delivered in add-on CEEISUT 600. See related notes 1005317 and 1006196 for information about installation and upgrade for this add-on. Please implement the highest add-on support patch available for CEEISUT600. See related note 1014951 for corresponding AOPs.<br/><br/>The following programs covering Polish version of IS-U were created:<br/>1) /SAPCE/PL_G104K - produces (based on the IS-U documents)<br/>                      statistical report G-10.4k<br/>2) /SAPCE/PL_G108 - produces (based on the IS-U documents)<br/>                      statistical report G-10.8<br/>3) /SAPCE/PL_PARTNER_BALANCES - generates balances and sums in                                   reporting period for IS-U BPs<br/>4) /SAPCE/PL_VAT_REGISTER - generates VAT register based on FI-CA<br/>                            documents<br/>5) /SAPCE/PL_G11E - produces (based on the IS-U documents)<br/>                    statistical report G-11e<br/>6) /SAPCE/PL_G11E_ATTACHMENT - produces analytics for section 1 of<br/>                              report G-11e<br/>7) /SAPCE/PL_DEFERRED_REVENUES - generates reports about deferred revenues deriving from advanced payments<br/><br/>Installation for release 600:<br/>Install add-on CEEISUT 600 with current AOPs (can be downloaded from SAP Service Marketplace). Please ask your local support for access to installation files and AOPs of CEEISUT on SAP Service Marketplace.<br/><br/>The note attachments contain following file:<br/>- IS-U_documentation.ZIP - manuals.<br/><br/>Note:</p> <ul><li>There are no modifications of the SAP standard objects deliveredin add-on CEEISUT</li></ul> <ul><li>The enhancements are using reserved name spaces /SAPCE/* and /CEEISUT/* which do not overlap with customer name range or other project development (according to note 72483)</li></ul> <ul><li>These enhancements should not interfere with any add-on support packages (AOP) for IS-UT.</li></ul> <ul><li>Local support is responsible for maintenance and hotline support of these enhancements</li></ul> <ul><li>These enhancements are not released for any other country</li></ul> <p></p></div>", "noteVersion": 4}, {"note": "637650", "noteTitle": "637650 - Polish specific in IS-utilities", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>!!! This note is OBSOLETE! Replaced with note 1866443</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>Additional development needed to cover Polish legal requirements<br/>Polish version, IS-U, Polish specific, VAT register, statistical<br/>reports: G-11e, G-10.4k, G-10.8, partner balances, deferred revenues</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>Customer uses IS-U/CCS in Poland</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>See note 1866443<br/>The following programs covering Polish version of IS-U were created:<br/>1) /SAPCE/PL_G104K - produces (based on the IS-U documents)<br/>                      statistical report G-10.4k<br/>2) /SAPCE/PL_G108 - produces (based on the IS-U documents)<br/>                      statistical report G-10.8<br/>3) /SAPCE/PL_PARTNER_BALANCES - generates balances and sums in                                   reporting period for IS-U BPs<br/>4) /SAPCE/PL_VATREGISTER - generates VAT register based on FI-CA<br/>                            documents<br/>5) /SAPCE/PL_G11E - produces (based on the IS-U documents)<br/>                    statistical report G-11e<br/>6) /SAPCE/PL_G11E_ATTACHMENT - produces analytics for section 1 of<br/>                              report G-11e<br/>7) /SAPCE/PL_DEFERRED_REVENUES - generates reports about deferred                                       revenues deriving from advanced                                         payments<br/><br/>The note attachments contain following files:<br/>- IS-U_documentation.ZIP - manuals.<br/></p></div>", "noteVersion": 12}]}, {"note": "2314696", "noteTitle": "2314696 - S4TC CEEISUT Master Check for S/4 System Conversion Checks", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>Implementation of CEEISUT check class for S/4 transformation checks as described in SAP note 2182725.<a href=\"/notes/2182725\" target=\"_blank\" title=\"2182725  - S4TC Delivery of the S/4 Pre-Transition Checks\"><br/></a></p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>S4TC, CEEISUT, Pre-Check Class</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>The pre-check class raises an error when none of the following Business Function Sets is in active state or any its underlying object (Business Function, Switch, Package) is not present in an active version in a system or any of Business Functions or Switches is not in active state:</p>\n<p><strong>Business Function Set 'UTILITIES' or 'MINING_WITH_UTILITIES or 'OIL_&amp;_GAS_WITH_UTILITIES':</strong></p>\n<ul>\n<li>Business Function /SAPCE/ISU_LOC_CEE</li>\n<ul>\n<li>Switch /SAPCE/ISU_LOC_CEE_SFWS_02 </li>\n<ul>\n<li>Switch Package /SAPCE/IU_LOC_CEE_SFWS_02 </li>\n</ul>\n</ul>\n<ul>\n<li>Switch /SAPCE/FICA_LOC_CEE_SFWS_02</li>\n<ul>\n<li>Switch Package SAPCE/FK_LOC_CEE_SFWS_02</li>\n</ul>\n</ul>\n<li>Business Function ISU_UTIL_WASTE</li>\n<ul>\n<li>Switch /SAPCE/ISU_LOC_CEE_SFWS_01</li>\n<ul>\n<li>Switch Package /SAPCE/IU_LOC_CEE_SFWS_01</li>\n</ul>\n<li>Switch /SAPCE/FICA_LOC_CEE_SFWS_01</li>\n<ul>\n<li>Switch Package /SAPCE/FK_LOC_CEE_SFWS_01</li>\n</ul>\n</ul>\n</ul>\n<p><strong><strong>Business Function Set 'TELCO'</strong>:</strong></p>\n<ul>\n<li>Business Function /SAPCE/TEL_LOC_CEE</li>\n<ul>\n<li> Switch /SAPCE/FICA_LOC_CEE_SFWS_02</li>\n<ul>\n<li>Switch Package /SAPCE/FK_LOC_CEE_SFWS_02</li>\n</ul>\n</ul>\n<li>Business Function /SAPCE/TEL_LOC_CEE_HIDDEN</li>\n<ul>\n<li>Switch /SAPCE/ISU_LOC_CEE_SFWS_01</li>\n<ul>\n<li>Switch Package /SAPCE/IU_LOC_CEE_SFWS_01</li>\n</ul>\n</ul>\n<li>Business Function RM_CA</li>\n<li>\n<ul>\n<ul>\n<li>Switch /SAPCE/FICA_LOC_CEE_SFWS_01</li>\n<ul>\n<li>Switch Package /SAPCE/FK_LOC_CEE_SFWS_01</li>\n</ul>\n</ul>\n</ul>\n</li>\n</ul>\n<p><strong> </strong></p>\n<p>In case of a negative result of the pre-check please check whether all the condition above are met. The following check identifications can be shown:</p>\n<ul>\n<li>CEEISUT_SFW_BS             Check of Business Function Sets</li>\n<li>CEEISUT_SFW_BF             Check of Business Functions</li>\n<li>CEEISUT_SFW_SW            Check of Switches</li>\n<li>CEEISUT_SFW_PC             Check of Switch Packages</li>\n</ul>\n<p>Please check the note 2323221 whether all the objects have been created a activated properly.</p>\n<p> </p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Implement the technical prerequisites for the S/4 transformation checks via this note.</p>", "noteVersion": 10}, {"note": "2182725", "noteTitle": "2182725 - S4TC Delivery of the SAP S/4HANA System Conversion Checks for SAP S/4HANA 1511 or 1610", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>Checks have to be executed before the conversion to SAP S/4HANA, if all preliminary steps in the source system have been performed.</p>\n<p><strong><strong>Note: </strong>The check report delivered via this note was exclusively used for system conversions to SAP S/4HANA 1511 and SAP S/4HANA 1610. As system conversions to SAP S/4HANA 1511 are no longer supported since May 2018 (and respectively to SAP S/4HANA 1610 since May 2019), this report and this note are obsolete and will be removed in the near future.</strong></p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>S4TC</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>Conversion to SAP S/4HANA.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Implement this note as well as the SAP notes mentioned in the manual activities document.</p>\n<p>For further information please refer to the respective SAP S/4HANA conversion procedure guide.</p>\n<p>The report R_S4_PRE_TRANSITION_CHECKS, which is delivered with this SAP note,</p>\n<ul>\n<li>can be executed <strong>standalone</strong> and calls all available pre–conversion checks which are delivered with the SAP notes mentioned in the manual activities document of this note.</li>\n<li>can be executed as often as required. When called in 'Simulation Mode', nothing is persisted, otherwise the report output is saved as 'Application Log' entry. Use the respective display option on the report selection screen to search for respective application log entries and to display them.</li>\n<li>is called automatically in the conversion procedure by SUM (Software Update Manager) to execute the pre-conversion checks.</li>\n</ul>\n<p>When you execute the pre–conversion checks <strong>standalone </strong>using report R_S4_PRE_TRANSITION_CHECKS:</p>\n<ul>\n<li>We recommend to always choose the option 'Simulation Mode', so that all available pre–conversion checks are executed despite of erroneous or missing pre–check classes.</li>\n<li>We recommend to save the entries on the selection screen of report R_S4_PRE_TRANSITION_CHECKS as a report variant.</li>\n<li>We recommend to execute the report as batch job using the above mentioned report variant.</li>\n<li>You can ignore error messages in the pre–conversion check result list concerning missing pre–conversion check classes. Only when the checks are executed by SUM, information about software components that do not require a pre–conversion check class is available.</li>\n</ul>\n<p>When the report R_S4_PRE_TRANSITION_CHECKS is executed for the very first time, field labels for the selection screen fields are missing, because such texts can not be delivered via correction instruction. But these texts are generated for the current logon language and persisted during this very first report execution.</p>\n<p>When the report R_S4_PRE_TRANSITION_CHECKS is executed <strong>with the option 'Check Class Consistency Check'</strong>, the report only checks if the respective check class methods can be dynamically called, <strong>but the real pre-conversion checks are <span><em>not executed</em></span></strong>.</p>\n<p>When the report R_S4_PRE_TRANSITION_CHECKS is executed and the option <strong>'Pre-Conversion </strong><strong>Check Results' </strong>is selected:</p>\n<ul>\n<li>If the checkbox ‘Simulation Mode’ is selected:</li>\n<ul>\n<li><strong>no</strong> application log entry is persisted, the output is shown on screen (online execution) or in spool (batch execution).</li>\n<li>consistency check errors <strong>are ignored</strong>, all usable checks are executed.</li>\n</ul>\n<li>If the checkbox ‘Simulation Mode’ is not selected:</li>\n<ul>\n<li>The output is only persisted as application log.</li>\n<li>consistency check errors <strong>are not ignored, </strong>checks are only executed if all checks are consistent.</li>\n</ul>\n</ul>\n<p>If one of the dynamically called pre-check classes returns more than 10000 check result lines, a respective error message is written into the check result (and the huge amount of more than 10000 lines is ignored in the result list in order to prevent an internal memory overflow of the used application log functionality). Contact in such a case the pre-check class responsible so that the class code can be corrected. Use the application component that is mentioned in the pre-check result to create an incident.</p>", "noteVersion": 56}], "activities": [{"Activity": "Business Decision", "Phase": "Before conversion project", "Condition": "Mandatory", "Additional_Information": "The localization Add-On CEEISUT for Telco and Utilities industries is retrofitted into SAP S/4HANA since 1610 release, appends of DDIC objects enhancing core structures and tables shipped with the CEEISUT have become switchable"}, {"Activity": "Data correction", "Phase": "Before or during conversion project", "Condition": "Mandatory", "Additional_Information": "Execute pre-check as of Note 2314696, it shows if the CEEISUT is installed but no Business Function Set is switched on"}, {"Activity": "Customizing / Configuration", "Phase": "Before or during conversion project", "Condition": "Conditional", "Additional_Information": "if the pre-check shows that none of the Business Function Sets is switched on -> you need to actiate it (either UTILITIES or TELCO) before the conversion"}]}