{"guid": "6CAE8B3E8C8B1ED6A5A7A744BAAE80C2", "sitemId": "SI6: Logistics_MM-IM", "sitemTitle": "S4TWL - Material Valuation Data Model Simplification in S/4HANA 1610 and Higher", "note": 2337383, "noteTitle": "2337383 - S4TWL - Material Valuation Data Model Simplification in S/4HANA 1610 and Higher", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are doing an upgrade from SAP S/4HANA 1511 to SAP S/4HANA 1610. The following SAP S/4HANA Transition Worklist item is applicable in this case.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>If you upgrade from SAP S/4HANA 1511 to SAP S/4HANA 1610, the Material Ledger (ML) functionalities will be changed for simplification.</p>\n<p>The impacts on the inventory valuation tables xBEW(H) - tables: EBEW, EBEWH, MBEW, MBEWH, OBEW, OBEWH, QBEW, QBEWH - are described in the following:</p>\n<p>The transactional fields LBKUM, SALK3 and VKSAL will be retrieved from the Universal Journal Entry Line Items table (ACDOCA) with on-the-fly aggregation. And the transactional field SALKV will be retrieved from the Material Ledger table.</p>\n<p>For compatibility reasons there are Core Data Service (CDS) Views assigned as proxy objects to all those tables, ensuring that each read access to one of the mentioned tables still returns the data as before. The CDS views consist of database joins in order to retrieve both master data from the original xBEW(H) table and transactional data from Universal Journal Entry Line Items table and Material Ledger table.</p>\n<p>Hence all customer coding, reading data from those tables, will work as before because each read access to one of the tables will get redirected in the database interface layer of NetWeaver to the assigned CDS view. Write accesses to those tables have to be adjusted if transactional fields are affected.</p>\n<p>One impact of the simplified MM-IM Inventory Valuation data model does exist if there are customer appends on the mentioned tables. The NetWeaver redirect capability requires that the database table and the assigned proxy view are compatible in the structure with respect to the number of fields and their type. Thus, if there is an append on one of the above mentioned tables, the assigned DDL source of the CDS proxy view must be made compatible.</p>\n<p>Another impact of the simplified inventory valuation data model is a performance decrease of database read operations on the above mentioned tables. A data fetch on one of the mentioned tables in S/4HANA is slower than in SAP ERP 6.0 due to JOIN operations and on-the-fly aggregation. Hence performance-critical customer coding may need to be adjusted to improve performance.</p>", "noteVersion": 4, "refer_note": [{"note": "2267834", "noteTitle": "2267834 - S4TWL - Material Ledger Obligatory for Material Valuation", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are doing a system conversion to SAP S/4HANA, on-premise edition. The following SAP S/4HANA Transition Worklist item is applicable in this case.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong>Description</strong></p>\n<p>This simplification makes it mandatory to use the Material Ledger (ML) in all SAP S/4HANA systems. After the technical SUM migration, the Material Ledger needs to be migrated.</p>\n<p>Relevant IMG migration activities:</p>\n<ul>\n<li>Customizing migration:</li>\n<ul>\n<li><strong>from SAP ERP or SAP Simple Finance to SAP S/4HANA 1511:</strong> transaction: SPRO --&gt; Migration from SAP ERP Accounting powered by SAP HANA --&gt; Preparations and Migration of Customizing --&gt; Preparations and Migration of Customizing for Material Ledger --&gt; Migrate Material Ledger Customizing</li>\n<li><strong>from SAP ERP or SAP Simple Finance to SAP S/4HANA 1610 or higher:</strong> transaction: SPRO --&gt; Migration to SAP S/4HANA Finance --&gt; Preparations and Migration of Customizing --&gt; Preparations and Migration of Customizing for Material Ledger --&gt; Migrate Material Ledger Customizing</li>\n</ul>\n<li>Data migration:</li>\n<ul>\n<li><strong>from SAP ERP or SAP Simple Finance to SAP S/4HANA 1511:</strong> transaction: SPRO --&gt; Migration from SAP ERP Accounting powered by SAP HANA --&gt; Migration --&gt; Material Ledger Migration</li>\n<li><strong>from SAP ERP or SAP Simple Finance to SAP S/4HANA 1610 or higher: </strong>transaction: SPRO --&gt; Migration to SAP S/4HANA Finance --&gt; Data Migration --&gt; Start and Monitor Data Migration</li>\n</ul>\n</ul>\n<p>You need to migrate the Material Ledger even if you are already using SAP Simple Finance (that is, you are migrating from SAP Simple Finance to SAP S/4 HANA Inventory Management). In addition, you need to migrate the Material Ledger even if you are already using the Material Ledger in your source system. If you performing an upgrade from S/4HANA 1511 to S/4HANA 1610 or higher, no manual IMG Material Ledger data migration is necessary.</p>\n<p>Latest trends in material management aim for improved and more flexible valuation methods in multiple currencies and parallel accounting standards while simultaneously reducing required system resources and improving scalability of the business processes. Since the data model of the Material Ledger module supports these business requirements, it was chosen as the basis for material inventory valuation in the new SAP S/4HANA solution.</p>\n<p>It is very important to distinguish between Material Ledger, in its role as an inventory subledger in Accounting, and the business feature Actual Costing. Material Ledger, as the inventory subledger, values material inventories in multiple currencies and GAAPs in parallel. In addition Material Ledger is a basic prerequisite for the use of Actual Costing. With Actual Costing you can value your material inventories, work in process, and cost of goods sold with weighted average unit costs that are being calculated after the fact by evaluating business transactions of one or more posting periods. Actual Costing also provides additional features, such as actual cost component splits.</p>\n<p>Even though activation of Material Ledger is now mandatory, activation of Actual Costing is still optional.</p>\n<p>Before S/4HANA, the inventory valuation tables xBEW(H) - tables: EBEW, EBEWH, MBEW, MBEWH, OBEW, OBEWH, QBEW, QBEWH - contain transactional as well as master data attributes.</p>\n<p>With S/4HANA, the inventory valuation tables do still exist as DDIC definitions as well as database objects. However, they will only be used to store material master data attributes. The transactional fields LBKUM, SALK3 and SALKV will be retrieved from the Material Ledger. (From <strong>S/4HANA 1610</strong> these transactional fields will be retrieved from the Universal Journal Entry Line Items table ACDOCA and the Material Ledger table. For more detailed information please see SAP Note: 2337383). Hence, those fields are not updated anymore in the original xBEW(H) tables. As a consequence, the above mentioned tables need to be updated less often, which leads to a higher throughput due to fewer database locks.</p>\n<p>For compatibility reasons there are Core Data Service (CDS) Views assigned as proxy objects to all those tables ensuring that each read access to one of the mentioned tables still returns the data as before. The CDS views consist of database joins in order to retrieve both master data from the original xBEW(H) table and transactional data from Material Ledger tables. (From <strong>S/4HANA 1610</strong> transactional data from the Universal Journal Entry Line Items table ACDOCA and the Material Ledger table. For more detailed information please see SAP Note: 2337383).</p>\n<p>Hence all customer coding, reading data from those tables, will work as before because each read access to one of the tables will get redirected in the database interface layer of NetWeaver to the assigned CDS view. Write accesses to those tables have to be adjusted if transactional fields are affected.</p>\n<p>The following table gives an overview of the new relevant database objects in the S/4HANA Inventory Valuation data model:</p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"0\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td valign=\"top\">\n<p><strong>Table</strong></p>\n</td>\n<td valign=\"top\">\n<p><strong>Table description</strong></p>\n</td>\n<td valign=\"top\">\n<p><strong>DDL source of CDS view for redirect (proxy view)</strong></p>\n</td>\n<td valign=\"top\">\n<p><strong>View to read the content of the original database table (w/o redirect to proxy view)</strong></p>\n</td>\n<td valign=\"top\">\n<p><strong>View to read master data attributes only</strong></p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\">\n<p>EBEW</p>\n</td>\n<td valign=\"top\">\n<p>Sales Order Stock Valuation</p>\n</td>\n<td valign=\"top\">\n<p>MBV_EBEW</p>\n</td>\n<td valign=\"top\">\n<p>MBVEBEWOLD</p>\n</td>\n<td valign=\"top\">\n<p>V_EBEW_MD</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\">\n<p>EBEWH</p>\n</td>\n<td valign=\"top\">\n<p>History of Sales Order Stock Valuation</p>\n</td>\n<td valign=\"top\">\n<p>MBV_EBEWH</p>\n</td>\n<td valign=\"top\">\n<p>MBVEBEWHOLD</p>\n</td>\n<td valign=\"top\">\n<p>V_EBEWH_MD</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\">\n<p>MBEW</p>\n</td>\n<td valign=\"top\">\n<p>Material Valuation</p>\n</td>\n<td valign=\"top\">\n<p>MBV_MBEW</p>\n</td>\n<td valign=\"top\">\n<p>MBVMBEWOLD</p>\n</td>\n<td valign=\"top\">\n<p>V_MBEW_MD</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\">\n<p>MBEWH</p>\n</td>\n<td valign=\"top\">\n<p>History of Material Valuation</p>\n</td>\n<td valign=\"top\">\n<p>MBV_MBEWH</p>\n</td>\n<td valign=\"top\">\n<p>MBVMBEWHOLD</p>\n</td>\n<td valign=\"top\">\n<p>V_MBEWH_MD</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\">\n<p>OBEW</p>\n</td>\n<td valign=\"top\">\n<p>Valuated Stock with Subcontractor (Japan)</p>\n</td>\n<td valign=\"top\">\n<p>MBV_OBEW</p>\n</td>\n<td valign=\"top\">\n<p>MBVOBEWOLD</p>\n</td>\n<td valign=\"top\">\n<p>V_OBEW_MD</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\">\n<p>OBEWH</p>\n</td>\n<td valign=\"top\">\n<p>History of Valuated Stock with Subcontractor (Japan)</p>\n</td>\n<td valign=\"top\">\n<p>MBV_OBEWH</p>\n</td>\n<td valign=\"top\">\n<p>MBVOBEWHOLD</p>\n</td>\n<td valign=\"top\">\n<p>V_OBEWH_MD</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\">\n<p>QBEW</p>\n</td>\n<td valign=\"top\">\n<p>Project Stock Valuation</p>\n</td>\n<td valign=\"top\">\n<p>MBV_QBEW</p>\n</td>\n<td valign=\"top\">\n<p>MBVQBEWOLD</p>\n</td>\n<td valign=\"top\">\n<p>V_QBEW_MD</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\">\n<p>QBEWH</p>\n</td>\n<td valign=\"top\">\n<p>History of Project Stock Valuation</p>\n</td>\n<td valign=\"top\">\n<p>MBV_QBEWH</p>\n</td>\n<td valign=\"top\">\n<p>MBVQBEWHOLD</p>\n</td>\n<td valign=\"top\">\n<p>V_QBEWH_MD</p>\n</td>\n</tr>\n</tbody>\n</table></div>\n<p><em>Table 1: Affected tables with information about corresponding proxy objects, views for accessing original database table and views for reading master data only</em></p>\n<p><strong>Business Process related information</strong></p>\n<p>If customers are not already using the material ledger, it will be activated during the conversion process. In MM02 and MR21 material prices can now be maintained in multiple currencies. In Financials the inventory account balances are calculated separately for each currency and result therefore in a cleaner and more consistent valuation in other currencies than the local currency.</p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"0\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td valign=\"top\" width=\"182\">\n<p>Transaction not available in SAP S/4HANA on-premise edition 1511</p>\n</td>\n<td valign=\"top\" width=\"414\">\n<p>MMPI: Initialize period for material master records (Not available with activated Material Ledger)</p>\n</td>\n</tr>\n</tbody>\n</table></div>\n<p> </p>\n<p><strong>Required and Recommended Action(s)</strong></p>\n<p>Perform Conversion Pre-Checks on start release to identify start situation</p>\n<p><strong>Related SAP Notes</strong></p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"0\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td valign=\"top\" width=\"258\">\n<p>Conversion Pre-Checks</p>\n</td>\n<td valign=\"top\" width=\"361\">\n<p>SAP Note: 2194618<br/>SAP Note: 2129306</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"258\">\n<p>Custom Code related information</p>\n</td>\n<td valign=\"top\" width=\"361\">\n<p>SAP Notes: 1804812</p>\n</td>\n</tr>\n</tbody>\n</table></div>", "noteVersion": 5, "refer_note": [{"note": "2217299", "noteTitle": "2217299 - Inventory Valuation (part of Materials Management - Inventory Management) : Change of data model in S/4HANA 1511", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You want to install SAP S/4HANA 1511 Inventory Management and need additional information how to adjust your customer enhancements, modifications or own functionalities to the new, simplified <strong>Inventory Valuation</strong> data model.</p>\n<p><strong>If you want to install SAP S/4HANA 1610, please ignore this SAP note and refer to the</strong> <strong>SAP note 2337368</strong>.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>S4TC, S/4 transition, S4HANA 1511, Migration, S4 HANA, MM-IM, Material Management, Inventory Valuation, Material Valuation, MBEW, compatibility view</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>You have customer enhancements, modifications or own functionalities in the area of inventory valuation (component MM-IM-GF-VAL) which were built for SAP ERP 6.0.</p>\n<p>Before S/4HANA, the inventory valuation tables xBEW(H) (tables: EBEW, EBEWH, MBEW, MBEWH, OBEW, OBEWH, QBEW, QBEWH) contain transactional as well as master data attributes.</p>\n<p>With S/4HANA, the inventory valuation tables do still exist as DDIC definition as well as database object. However, they will only be used to store material master data attributes. The transactional fields LBKUM SALK3 and SALKV will be retrieved from the Material Ledger. Hence, those fields are not updated anymore in the original xBEW(H) tables. As a consequence, the above mentioned tables need to be updated less often, which leads to a higher throughput due to less database locks.</p>\n<p>For compatibility reasons there are Core Data Service (CDS) Views assigned as proxy objects to all those tables ensuring that each read access to one of the mentioned tables still returns the data as before. The CDS-views consist of database joins in order to retrieve both master data from the original xBEW(H) table and transactional data from Material Ledger tables.</p>\n<p>Hence all customer coding, reading data from those tables, will work as before because each read access to one of the tables will get redirected in the database interface layer of NetWeaver to the assigned CDS view. Write accesses to those tables have to be adjusted, if transactional fields are affected.</p>\n<p>The following table gives an overview of the new relevant database objects in the S/4HANA Inventory Valuation data model:</p>\n<div class=\"table-responsive\"><table border=\"2\" cellpadding=\"5\" cellspacing=\"2\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td valign=\"top\">\n<p><strong>Table             </strong></p>\n</td>\n<td valign=\"top\">\n<p><strong>Table description   </strong></p>\n</td>\n<td valign=\"top\">\n<p><strong>DDL source of CDS view for redirect (proxy view)    </strong></p>\n</td>\n<td valign=\"top\">\n<p><strong><strong>Redirected view in ABAP</strong></strong></p>\n</td>\n<td valign=\"top\">\n<p><strong>View to read the content of the original database table (w/o redirect to proxy view)    </strong></p>\n</td>\n<td valign=\"top\">\n<p><strong>View to read master data attributes only    </strong></p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\">\n<p>EBEW</p>\n</td>\n<td valign=\"top\">\n<p>Sales Order Stock Valuation</p>\n</td>\n<td valign=\"top\">\n<p>MBV_EBEW</p>\n</td>\n<td valign=\"top\">\n<p>MBVEBEW</p>\n</td>\n<td valign=\"top\">\n<p>MBVEBEWOLD</p>\n</td>\n<td valign=\"top\">\n<p>V_EBEW_MD</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\">\n<p>EBEWH</p>\n</td>\n<td valign=\"top\">\n<p>History of Sales Order Stock Valuation</p>\n</td>\n<td valign=\"top\">\n<p>MBV_EBEWH</p>\n</td>\n<td valign=\"top\">\n<p>MBVEBEWH</p>\n</td>\n<td valign=\"top\">\n<p>MBVEBEWHOLD</p>\n</td>\n<td valign=\"top\">\n<p>V_EBEWH_MD</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\">\n<p>MBEW</p>\n</td>\n<td valign=\"top\">\n<p>Material Valuation</p>\n</td>\n<td valign=\"top\">\n<p>MBV_MBEW</p>\n</td>\n<td valign=\"top\">\n<p>MBVMBEW</p>\n</td>\n<td valign=\"top\">\n<p>MBVMBEWOLD</p>\n</td>\n<td valign=\"top\">\n<p>V_MBEW_MD</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\">\n<p>MBEWH</p>\n</td>\n<td valign=\"top\">\n<p>History of Material Valuation</p>\n</td>\n<td valign=\"top\">\n<p>MBV_MBEWH</p>\n</td>\n<td valign=\"top\">\n<p>MBVMBEWH</p>\n</td>\n<td valign=\"top\">\n<p>MBVMBEWHOLD</p>\n</td>\n<td valign=\"top\">\n<p>V_MBEWH_MD</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\">\n<p>OBEW</p>\n</td>\n<td valign=\"top\">\n<p>Valuated Stock with Subcontractor (Japan)</p>\n</td>\n<td valign=\"top\">\n<p>MBV_OBEW</p>\n</td>\n<td valign=\"top\">\n<p>MBVOBEW</p>\n</td>\n<td valign=\"top\">\n<p>MBVOBEWOLD</p>\n</td>\n<td valign=\"top\">\n<p>V_OBEW_MD</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\">\n<p>OBEWH</p>\n</td>\n<td valign=\"top\">\n<p>History of Valuated Stock with Subcontractor (Japan)</p>\n</td>\n<td valign=\"top\">\n<p>MBV_OBEWH</p>\n</td>\n<td valign=\"top\">\n<p>MBVOBEWH</p>\n</td>\n<td valign=\"top\">\n<p>MBVOBEWHOLD</p>\n</td>\n<td valign=\"top\">\n<p>V_OBEWH_MD</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\">\n<p>QBEW</p>\n</td>\n<td valign=\"top\">\n<p>Project Stock Valuation</p>\n</td>\n<td valign=\"top\">\n<p>MBV_QBEW</p>\n</td>\n<td valign=\"top\">\n<p>MBVQBEW</p>\n</td>\n<td valign=\"top\">\n<p>MBVQBEWOLD</p>\n</td>\n<td valign=\"top\">\n<p>V_QBEW_MD</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\">\n<p>QBEWH</p>\n</td>\n<td valign=\"top\">\n<p>History of Project Stock Valuation</p>\n</td>\n<td valign=\"top\">\n<p>MBV_QBEWH</p>\n</td>\n<td valign=\"top\">\n<p>MBVQBEWH</p>\n</td>\n<td valign=\"top\">\n<p>MBVQBEWHOLD</p>\n</td>\n<td valign=\"top\">\n<p>V_QBEWH_MD</p>\n</td>\n</tr>\n</tbody>\n</table></div>\n<p><em>Table 1: Affected tables with information about corresponding proxy objects, views for accessing original database table and views for reading master data only</em></p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>One impact of the simplified MM-IM Inventory Valuation data model does exist if there are customer appends on the mentioned tables. The NetWeaver redirect capability requires that database table and assigned proxy view is compatible in the structure: number of fields, their sequence and their type. Thus, if there is an append on one of the above mentioned tables then the assigned DDL source of the CDS proxy view must be made compatible.</p>\n<p>Another impact of the simplified inventory valuation data model is a performance decrease of database read operations on the above mentioned tables just because a data fetch on one of the mentioned tables is in S/4HANA slower than in SAP ERP 6.0 due to JOIN operations. Hence performance critical customer coding may be adjusted to improve performance.</p>\n<p> </p>\n<p><strong>1. Customer appends on xBEW(H) tables</strong></p>\n<p>With SAP Note <a class=\"external-link\" href=\"/notes/2194618\" target=\"_blank\">2194618</a> SAP offers a pre-check to be executed on the start release to identify append issues described in the following sub chapters. Hence customer is not forced to scan all above listed tables manually.</p>\n<p>Please note that this check contains not only the xBEW(H)-tables. Instead, it also analyzes other MM-IM tables.</p>\n<p> </p>\n<p>If the pre-check identifies appends on an xBEW(H) tables, the corresponding CDS views, listed in the following table, needs to be enhanced or replaced.</p>\n<div class=\"table-responsive\"><table border=\"2\" cellpadding=\"5\" cellspacing=\"2\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td valign=\"top\">\n<p><strong>Table with customer appends</strong></p>\n</td>\n<td valign=\"top\">\n<p><strong>DDL sources of CDS-views which needs to be enhanced or replaced in case of customer appends</strong></p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\">\n<p>EBEW</p>\n</td>\n<td valign=\"top\">\n<p>-       MBV_EBEW_EXT</p>\n<p>-       MBV_EBEW_ML_ONLY</p>\n<p>-       MBV_EBEW_BASIS</p>\n<p>-       MBV_EBEW_CASE</p>\n<p>-      <strong> MBV_EBEW</strong> (Proxy view --&gt; same structure and order as EBEW table necessary)</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\">\n<p>EBEWH</p>\n</td>\n<td valign=\"top\">\n<p>-       MBV_EBEWH_EXT</p>\n<p>-       MBV_EBEWH_ASSOC</p>\n<p>-       MBV_EBEWH_ML_ONLY</p>\n<p>-       MBV_EBEWH_BASIS</p>\n<p>-       MBV_EBEWH_CASE</p>\n<p>-       <strong>MBV_EBEWH</strong> (Proxy view --&gt; same structure and order as EBEWH table necessary)</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\">\n<p>MBEW</p>\n</td>\n<td valign=\"top\">\n<p>-       MBV_MBEW_EXT</p>\n<p>-       MBV_MBEW_ML_ONLY</p>\n<p>-       MBV_MBEW_BASIS</p>\n<p>-       MBV_MBEW_CASE</p>\n<p>-       MBV_MBEW_MOTH_SEG</p>\n<p>-       <strong>MBV_MBEW</strong> (Proxy view --&gt; same structure and order as MBEW table necessary)</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\">\n<p>MBEWH</p>\n</td>\n<td valign=\"top\">\n<p>-       MBV_MBEWH_EXT</p>\n<p>-       MBV_MBEWH_ASSOC</p>\n<p>-       MBV_MBEWH_ML_ONLY</p>\n<p>-       MBV_MBEWH_BASIS</p>\n<p>-       MBV_MBEWH_CASE</p>\n<p>-       MBV_MBEWH_MOTH_SEG</p>\n<p>-       <strong>MBV_MBEWH</strong> (Proxy view --&gt; same structure and order as MBEWH table necessary)</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\">\n<p>OBEW</p>\n</td>\n<td valign=\"top\">\n<p>-       MBV_OBEW_EXT</p>\n<p>-       MBV_OBEW_ML_ONLY</p>\n<p>-       MBV_OBEW_BASIS</p>\n<p>-       MBV_OBEW_CASE</p>\n<p>-      <strong> MBV_OBEW</strong> (Proxy view --&gt; same structure and order as OBEW table necessary)</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\">\n<p>OBEWH</p>\n</td>\n<td valign=\"top\">\n<p>-       MBV_OBEWH_EXT</p>\n<p>-       MBV_OBEWH_ASSOC</p>\n<p>-       MBV_OBEWH_ML_ONLY</p>\n<p>-       MBV_OBEWH_BASIS</p>\n<p>-       MBV_OBEWH_CASE</p>\n<p>-       <strong>MBV_OBEWH</strong> (Proxy view --&gt; same structure and order as OBEWH table necessary)</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\">\n<p>QBEW</p>\n</td>\n<td valign=\"top\">\n<p>-       MBV_QBEW_EXT</p>\n<p>-       MBV_QBEW_ML_ONLY</p>\n<p>-       MBV_QBEW_BASIS</p>\n<p>-       MBV_QBEW_CASE</p>\n<p>-       <strong>MBV_QBEW</strong> (Proxy view --&gt; same structure and order as QBEW table necessary)</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\">\n<p>QBEWH</p>\n</td>\n<td valign=\"top\">\n<p>-       MBV_QBEWH_EXT</p>\n<p>-       MBV_QBEWH_ASSOC</p>\n<p>-       MBV_QBEWH_ML_ONLY</p>\n<p>-       MBV_QBEWH_BASIS</p>\n<p>-       MBV_QBEWH_CASE</p>\n<p>-       <strong>MBV_QBEWH</strong> (Proxy view --&gt; same structure and order as QBEWH table necessary)</p>\n</td>\n</tr>\n</tbody>\n</table></div>\n<p><em>Table 2: Affected CDS views which needs to be enhanced or replaced in case of customer appends</em><em></em><em> </em></p>\n<p> </p>\n<p><strong>1.1 Fields containing material master data attributes</strong></p>\n<p>If customer appends only contain material master data attributes, the views could be made compatible either by extension views and view replacements.</p>\n<ol>\n<li>\n<p>Extension view (recommended, if feasible)<br/>The structure of the proxy view and the xBEW(H) table can be made compatible by extension views. Extension views take care that fields, defined in the extension view, are appended at the end of the extended view. Consequently, it is necessary to move existing customer appends between SAP fields, for instance part of different includes, to the end of the corresponding xBEW(H) table. If this is not feasible, the views have to be replaced as described in option 2.<br/>An extension view needs to be defined for each CDS-view, listed in table 2, for the corresponding xBEW(H) table.<br/>The fields in the extension view of the proxy CDS view (bold in table 2), have to be listed in exactly the same order as in the customer append. For more information about view extension see  <a href=\"http://help.sap.com/abapdocu_740/en/index.htm?file=abencds_f1_extend_view.htm\" rel=\"nofollow\" target=\"_blank\">SAP NetWeaver 7.4 documentation</a></p>\n</li>\n<li>\n<p>Replacement of CDS proxy view (bold in table 2) by a new CDS proxy view in customer namespace (Please refer to SAP Note <a href=\"/notes/2242679\" target=\"_blank\">2242679</a> )</p>\n<p>If option 1 is not feasible, for instance due to unmovable customer appends between SAP fields, a new CDS proxy view needs to be created in customer namespace. This view must contain the same fields and types in the same order as the corresponding xBEW(H) table. The newly created CDS view must be specified as a new proxy view via transaction SE11 --&gt; Menu --&gt; Extras --&gt; Proxy Object.</p>\n<p>For the other CDS views in the view-hierarchy the exact order of the fields does not matter. Hence option 1 is applicable. Consequently, in some cases it could be an option to combine the first and second approach, for instance extension view for sub-CDS views in the view-hierarchy and view replacement for the CDS proxy view.</p>\n</li>\n</ol>\n<p> </p>\n<p><strong>1.2 Fields representing a customer defined quantity/value to be aggregated</strong></p>\n<p>If customer appends contain aggregated fields, the proxy view (same approach as described for material master data attributes) and most probably also the views in the view-hierarchy needs to be replaced by new CDS views in customer namespace .</p>\n<p>In most cases extension views cannot be used, since currently they do not support aggregate expressions.</p>\n<p> </p>\n<p><strong>1.3 Customer appends on views</strong></p>\n<p>There are several views in SAP Standard which also do have an assigned proxy view because the view reads transactional data from an xBEW(H) table. Since transactional data are not updated anymore in the xBEW(H) table, view accesses are redirected to corresponding proxy views.</p>\n<p>View and assigned proxy view must be compatible in structure too. If there are customer appends on such view the same rules as for tables apply. Views with assigned proxy compatibility view are identified by the provided pre-check (SAP note <a class=\"external-link\" href=\"/notes/2194618\" target=\"_blank\">2194618</a>).</p>\n<p> </p>\n<p><strong>1.3.1 Customer views using material master attributes</strong></p>\n<p>Such views using only material master data attributes from the xBEW(H) tables do not need to be changed.</p>\n<p> </p>\n<p><strong>1.3.2 Customer views using transactional attributes</strong></p>\n<p>Customer views, accessing xBEW(H) tables and having at least one transactional attribute (LBKUM, SALK3, SALKV or own customer transactional fields)  need to be re-build by creating a new CDS view with a new name.</p>\n<p>Instead of accessing the original xBEW(H) table, the corresponding DDL source, listed in table 1, needs to be accessed.</p>\n<p>There are two options how to handle accesses to the old DDIC SQL view:</p>\n<ol>\n<li>Redirect accesses to new CDS proxy view via transaction SE11 (Extras --&gt; Proxy Object)</li>\n<li>Code adaption of accesses to old SQL DDIC SQL view</li>\n</ol>\n<p> </p>\n<p><strong>2 Code adjustments and optimizations</strong></p>\n<p>Technically it is still possible to do database write operations (INSERT, UPDATE, DELETE, MODIFY) on transactional fields. But such write operations are without any effect, since transactional data is read from the Material Ledger!</p>\n<p>Therefore, write operations on xBEW(H) tables, affecting transactional fields, shall be removed from customer coding.</p>\n<p>Write operations on material master data attributes shall still happen on the xBEW(H) tables.</p>\n<p> </p>\n<p>DB read operations on the xBEW(H) tables have a performance decrease, since transactional data is retrieved from the Material Ledger via database joins. Consequently, it shall be avoided to read transactional data when only master data is required. Therefore it is recommended to adjust the customer coding in the following way:</p>\n<ul>\n<li>If material master data as well transactional data is required then the SELECT &lt;table&gt; should be replaced by using a data access method from class CL_MBV_SELECT_&lt;table&gt;. These classes provide access methods for single (SINGLE_READ) as well as array (ARRAY_READ) read operations.</li>\n<li>If only material master data is required then the SELECT &lt;table&gt; should be replaced by SELECT on view V_&lt;table&gt;_MD. Alternatively, corresponding material master data read methods (READ_MASTER_DATA) in the class CL_MBV_SELECT_&lt;table&gt; can be used. Also the data type declarations should be adjusted from TYPE &lt;table&gt; to TYPE V_&lt;table&gt;_MD.</li>\n<li>If only transactional fields are required then the SELECT &lt;table&gt; could be replaced by using the data access methods ARRAY_READ_TRANSACTIONAL_DATA and SINGLE_READ_TRANSACTIONAL_DATA from class CL_MBV_SELECT_&lt;table&gt;. Currently, the methods for retrieving transactional fields do not have a better performance than the methods ARRAY_READ and SINGLE_READ in class CL_MBV_SELECT_&lt;table&gt;. However, this might change in the following releases.</li>\n</ul>\n<p> </p>\n<p>For performance critical coding parts these adjustments are strongly recommended. For non critical parts it is optional short term but recommended on long term.</p>\n<p>To identify such locations, it is required to make use of the where-used functionality of transaction SE11 and considering other techniques like transaction CODE_SCANNER to find locations which SE11 cannot handle – like dynamic programming or native SQL statements.</p>\n<p>Consider SAP Note <a class=\"external-link\" href=\"/notes/28022\" target=\"_blank\">28022</a>, if there are issues with the where-used functionality in the customer system. In the where-used dialog it is possible via the button \"Search Range\" to search specific for key words like SELECT, INSERT and so on.</p>", "noteVersion": 6}, {"note": "2194618", "noteTitle": "2194618 - <PERSON>4TC SAP_APPL - Checks for MM-IM", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You want to execute a transition of your ERP system to S/4HANA.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>S4TC, S/4 transition</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Implement this note. The checks will be executed during the transition process.</p>", "noteVersion": 36}, {"note": "1804812", "noteTitle": "1804812 - MB transactions: Limited maintenance/decommissioning", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>The following transactions for entering and displaying goods movements (material documents) - called \"MB transactions\" below - have been replaced by the single-screen transaction MIGO since Release 4.6:</p>\n<ul>\n<li>MB01, MB02, MB03, MB04, MB05, MB0A, MB11, MB1A, MB1B, MB1C, MB31, MBNL, MBRL, MBSF, MBSL, MBST, MBSU</li>\n</ul>\n<p>For reasons relating to change protection for existing process configurations, the specified transactions are still available up to the ERP 6.xx releases, that is, also in the Enhancement Packages 7 and 8. However, they are subject to maintenance restrictions:</p>\n<ul>\n<ul>\n<li>Corrections in the MB transactions are carried out only if the equivalent process cannot be posted demonstrably using transaction MIGO or if serious errors occur that cause inconsistencies or other legally-relevant problems.</li>\n</ul>\n</ul>\n<ul>\n<ul>\n<li>New functions are developed only for transaction MIGO.</li>\n</ul>\n</ul>\n<p>In the ERP 6.xx releases (for example in EhP7 = Release 6.17), the system message M7 499 refers to this situation. You can set the message category in Customizing (default: \"I\" Info). The system issues the information message once daily in non-production clients only. Message category \"E\" (from SAP Note 2029600 onwards) results in an error message in live clients as well. When MB03 is called from other transactions, the calls are automatically forwarded to MIGO for document display, if the message category \"E\" is set.</p>\n<p>With batch input, the system does not generally issue message 7499 until further notice.<br/><br/>In ERP in the following SAP products (S/4 HANA, S4CORE), the current plan is to decommission the MB transactions. This means that they are then no longer available. For current information about this, see SAP Note 2210569.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>MIGO_GR, MIGO_GI, MIGO_ST, BAPI_GOODSMVT_CANCEL, M7499, M7-499, omcq</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>The double-maintenance of the transaction families MIGO and MB* delays the processing of problems with MIGO and other transactions, and reduces the development capacity provided for continuous further development. Therefore, the discontinuation of the double-maintenance is also in the interest of all SAP customers.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>You can still use the MB transactions in the ERP 6.xx releases. However, it is advisable to replace them with transaction MIGO.<br/>The BAPI \"BAPI_GOODSMVT_CREATE\" is provided as a replacement for background processing via batch input.</p>", "noteVersion": 15}, {"note": "2129306", "noteTitle": "2129306 - Check Customizing Settings Prior to Upgrade to S/4HANA Finance or S/4HANA", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>The program FINS_MIG_PRECHECK_CUST_SETTNGS provided by this note checks the consistency of your ledger, company code and controlling area settings to determine if a migration to SAP S/4HANA  is possible.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Journal Entry, Ledger, Parallel Valuation, Unified Journal Entry.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>This report checks if your settings can automatically be migrated to the new customizing tables.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Please perform the following steps in your classic ERP prior to the upgrade to S/4HANA Finance or S/4HANA:</p>\n<ol>\n<li>First apply the corrections that are provided in note 2240666: The note 2240666 contains the ABAP check class that is called by the ABAP program FINS_MIG_PRECHECK_CUST_SETTNGS.</li>\n<li>Then apply the corrections of this note 2129306 to create the report.</li>\n</ol>\n<p>Afterwards you can start the program FINS_MIG_PRECHECK_CUST_SETTNGS to perform the checks. In case the result list of this program contains error messages, you can find information about root cause and potential solution in note 2245333.</p>\n<p>The program FINS_MIG_PRECHECK_CUST_SETTNGS shall be started in all clients &lt;&gt; 000: In client 000 no checks will be performed, since the customizing content in client 000 will be overwritten anyway by the sFIN content during the upgrade to sFIN.</p>\n<p>After your system was upgraded to S/4HANA Finance or S/4HANA, please check whether the corrections of note 2209641 are relevant, since the report can also be executed after the upgrade.</p>", "noteVersion": 23}, {"note": "2337383", "noteTitle": "2337383 - S4TWL - Material Valuation Data Model Simplification in S/4HANA 1610 and Higher", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are doing an upgrade from SAP S/4HANA 1511 to SAP S/4HANA 1610. The following SAP S/4HANA Transition Worklist item is applicable in this case.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>If you upgrade from SAP S/4HANA 1511 to SAP S/4HANA 1610, the Material Ledger (ML) functionalities will be changed for simplification.</p>\n<p>The impacts on the inventory valuation tables xBEW(H) - tables: EBEW, EBEWH, MBEW, MBEWH, OBEW, OBEWH, QBEW, QBEWH - are described in the following:</p>\n<p>The transactional fields LBKUM, SALK3 and VKSAL will be retrieved from the Universal Journal Entry Line Items table (ACDOCA) with on-the-fly aggregation. And the transactional field SALKV will be retrieved from the Material Ledger table.</p>\n<p>For compatibility reasons there are Core Data Service (CDS) Views assigned as proxy objects to all those tables, ensuring that each read access to one of the mentioned tables still returns the data as before. The CDS views consist of database joins in order to retrieve both master data from the original xBEW(H) table and transactional data from Universal Journal Entry Line Items table and Material Ledger table.</p>\n<p>Hence all customer coding, reading data from those tables, will work as before because each read access to one of the tables will get redirected in the database interface layer of NetWeaver to the assigned CDS view. Write accesses to those tables have to be adjusted if transactional fields are affected.</p>\n<p>One impact of the simplified MM-IM Inventory Valuation data model does exist if there are customer appends on the mentioned tables. The NetWeaver redirect capability requires that the database table and the assigned proxy view are compatible in the structure with respect to the number of fields and their type. Thus, if there is an append on one of the above mentioned tables, the assigned DDL source of the CDS proxy view must be made compatible.</p>\n<p>Another impact of the simplified inventory valuation data model is a performance decrease of database read operations on the above mentioned tables. A data fetch on one of the mentioned tables in S/4HANA is slower than in SAP ERP 6.0 due to JOIN operations and on-the-fly aggregation. Hence performance-critical customer coding may need to be adjusted to improve performance.</p>", "noteVersion": 4}]}, {"note": "2337368", "noteTitle": "2337368 - Inventory Valuation (part of Materials Management - Inventory Management) : Change of data model in S/4HANA 1610", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You want to install SAP S/4HANA 1610 Inventory Management and need additional information how to adjust your customer enhancements, modifications or own functionalities to the new, simplified <strong>Inventory Valuation</strong> data model.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>S4TC, S/4 transition, S4HANA 1610, Migration, S4 HANA, MM-IM, Material Management, Inventory Valuation, Material Valuation, MBEW, compatibility view</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>You have customer enhancements, modifications or own functionalities in the area of inventory valuation (component MM-IM-GF-VAL) which were built for SAP ERP 6.0.</p>\n<p>Before S/4HANA, the inventory valuation tables xBEW(H) (tables: <PERSON>BE<PERSON>, <PERSON>BE<PERSON><PERSON>, MBE<PERSON>, <PERSON><PERSON><PERSON>, OBE<PERSON>, <PERSON>W<PERSON>, <PERSON>BE<PERSON>, QBEW<PERSON>) contain transactional as well as master data attributes.</p>\n<p>With S/4HANA, the inventory valuation tables do still exist as DDIC definition as well as database object. However, they will only be used to store material master data attributes. The transactional fields LBKUM SALK3 and VKSAL will be retrieved from the Universal Journal Entry Line Items table (ACDOCA) with on-the-fly aggregation. And the transactional field SALKV will be retrieved from the Material Ledger table. Hence, those fields are not updated anymore in the original xBEW(H) tables. As a consequence, the above mentioned tables need to be updated less often, which leads to a higher throughput due to less database locks.</p>\n<p>For compatibility reasons there are Core Data Service (CDS) Views assigned as proxy objects to all those tables ensuring that each read access to one of the mentioned tables still returns the data as before. The CDS-views consist of database joins in order to retrieve both master data from the original xBEW(H) table and transactional data from Universal Journal Entry Line Items table and Material Ledger table.</p>\n<p>Hence all customer coding, reading data from those tables, will work as before because each read access to one of the tables will get redirected in the database interface layer of NetWeaver to the assigned CDS view. Write accesses to those tables have to be adjusted, if transactional fields are affected.</p>\n<p>The following table gives an overview of the new relevant database objects in the S/4HANA Inventory Valuation data model:</p>\n<div class=\"table-responsive\"><table border=\"2\" cellpadding=\"5\" cellspacing=\"2\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td valign=\"top\">\n<p><strong>Table             </strong></p>\n</td>\n<td valign=\"top\">\n<p><strong>Table description   </strong></p>\n</td>\n<td valign=\"top\">\n<p><strong>DDL source of CDS view for redirect (proxy view)    </strong></p>\n</td>\n<td valign=\"top\">\n<p><strong>Redirected view in ABAP</strong></p>\n</td>\n<td valign=\"top\">\n<p><strong>View to read the content of the original database table (w/o redirect to proxy view)    </strong></p>\n</td>\n<td valign=\"top\">\n<p><strong>View to read master data attributes only    </strong></p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\">\n<p>EBEW</p>\n</td>\n<td valign=\"top\">\n<p>Sales Order Stock Valuation</p>\n</td>\n<td valign=\"top\">\n<p>MBV_EBEW</p>\n</td>\n<td valign=\"top\">\n<p>MBVEBEW</p>\n</td>\n<td valign=\"top\">\n<p>MBVEBEWOLD</p>\n</td>\n<td valign=\"top\">\n<p>V_EBEW_MD</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\">\n<p>EBEWH</p>\n</td>\n<td valign=\"top\">\n<p>History of Sales Order Stock Valuation</p>\n</td>\n<td valign=\"top\">\n<p>MBV_EBEWH</p>\n</td>\n<td valign=\"top\">MBVEBEWH</td>\n<td valign=\"top\">\n<p>MBVEBEWHOLD</p>\n</td>\n<td valign=\"top\">\n<p>V_EBEWH_MD</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\">\n<p>MBEW</p>\n</td>\n<td valign=\"top\">\n<p>Material Valuation</p>\n</td>\n<td valign=\"top\">\n<p>MBV_MBEW</p>\n</td>\n<td valign=\"top\">\n<p>MBVMBEW</p>\n</td>\n<td valign=\"top\">\n<p>MBVMBEWOLD</p>\n</td>\n<td valign=\"top\">\n<p>V_MBEW_MD</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\">\n<p>MBEWH</p>\n</td>\n<td valign=\"top\">\n<p>History of Material Valuation</p>\n</td>\n<td valign=\"top\">\n<p>MBV_MBEWH</p>\n</td>\n<td valign=\"top\">\n<p class=\"p1\">MBVMBEWH</p>\n</td>\n<td valign=\"top\">\n<p>MBVMBEWHOLD</p>\n</td>\n<td valign=\"top\">\n<p>V_MBEWH_MD</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\">\n<p>OBEW</p>\n</td>\n<td valign=\"top\">\n<p>Valuated Stock with Subcontractor (Japan)</p>\n</td>\n<td valign=\"top\">\n<p>MBV_OBEW</p>\n</td>\n<td valign=\"top\">MBVOBEW</td>\n<td valign=\"top\">\n<p>MBVOBEWOLD</p>\n</td>\n<td valign=\"top\">\n<p>V_OBEW_MD</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\">\n<p>OBEWH</p>\n</td>\n<td valign=\"top\">\n<p>History of Valuated Stock with Subcontractor (Japan)</p>\n</td>\n<td valign=\"top\">\n<p>MBV_OBEWH</p>\n</td>\n<td valign=\"top\">MBVOBEWH</td>\n<td valign=\"top\">\n<p>MBVOBEWHOLD</p>\n</td>\n<td valign=\"top\">\n<p>V_OBEWH_MD</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\">\n<p>QBEW</p>\n</td>\n<td valign=\"top\">\n<p>Project Stock Valuation</p>\n</td>\n<td valign=\"top\">\n<p>MBV_QBEW</p>\n</td>\n<td valign=\"top\">MBVQBEW</td>\n<td valign=\"top\">\n<p>MBVQBEWOLD</p>\n</td>\n<td valign=\"top\">\n<p>V_QBEW_MD</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\">\n<p>QBEWH</p>\n</td>\n<td valign=\"top\">\n<p>History of Project Stock Valuation</p>\n</td>\n<td valign=\"top\">\n<p>MBV_QBEWH</p>\n</td>\n<td valign=\"top\">MBVQBEWH</td>\n<td valign=\"top\">\n<p>MBVQBEWHOLD</p>\n</td>\n<td valign=\"top\">\n<p>V_QBEWH_MD</p>\n</td>\n</tr>\n</tbody>\n</table></div>\n<p><em>Table 1: Affected tables with information about corresponding proxy objects, views for accessing original database table and views for reading master data only</em></p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>One impact of the simplified MM-IM Inventory Valuation data model does exist if there are customer appends on the mentioned tables. The NetWeaver redirect capability requires that database table and assigned proxy view is compatible in the structure: number of fields and their type. Thus, if there is an append on one of the above mentioned tables then the assigned DDL source of the CDS proxy view must be made compatible.</p>\n<p>Another impact of the simplified inventory valuation data model is a performance decrease of database read operations on the above mentioned tables just because a data fetch on one of the mentioned tables is in S/4HANA slower than in SAP ERP 6.0 due to JOIN operations and on-the-fly aggregation. Hence performance critical customer coding may be adjusted to improve performance.</p>\n<p> </p>\n<p><strong>1. Customer appends on xBEW(H) tables</strong></p>\n<p>With SAP Note <a class=\"external-link\" href=\"/notes/2194618\" target=\"_blank\">2194618</a> SAP offers a pre-check to be executed on the start release to identify append issues described in the following sub chapters. Hence customer is not forced to scan all above listed tables manually.</p>\n<p>Please note that this check contains not only the xBEW(H)-tables. Instead, it also analyzes other MM-IM tables.</p>\n<p> </p>\n<p>If the pre-check identifies appends on an xBEW(H) tables, the corresponding CDS views, listed in the following table, needs to be enhanced or replaced.</p>\n<div class=\"table-responsive\"><table border=\"2\" cellpadding=\"5\" cellspacing=\"2\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td valign=\"top\">\n<p><strong>Table with customer appends</strong></p>\n</td>\n<td valign=\"top\">\n<p><strong>DDL sources of CDS-views which needs to be enhanced or replaced in case of customer appends</strong></p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\">\n<p>EBEW</p>\n</td>\n<td valign=\"top\">\n<p>MBV_EBEW</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\">\n<p>EBEWH</p>\n</td>\n<td valign=\"top\">\n<p>MBV_EBEWH</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\">\n<p>MBEW</p>\n</td>\n<td valign=\"top\">\n<p>MBV_MBEW</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\">\n<p>MBEWH</p>\n</td>\n<td valign=\"top\">\n<p>MBV_MBEWH</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\">\n<p>MBEWH</p>\n</td>\n<td valign=\"top\">\n<p>MBV_MBEWH_BASE</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\">\n<p>OBEW</p>\n</td>\n<td valign=\"top\">\n<p>MBV_OBEW</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\">\n<p>OBEWH</p>\n</td>\n<td valign=\"top\">\n<p>MBV_OBEWH</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\">\n<p>QBEW</p>\n</td>\n<td valign=\"top\">\n<p>MBV_QBEW</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\">\n<p>QBEWH</p>\n</td>\n<td valign=\"top\">\n<p>MBV_QBEWH</p>\n</td>\n</tr>\n</tbody>\n</table></div>\n<p><em>Table 2: Affected CDS views which needs to be enhanced or replaced in case of customer appends</em><em></em><em> </em></p>\n<p>If you upgrade from S/4HANA 1511 to S/4HANA 1610, you do not need the view extensions for the views listed in table 3 anymore. These view extensions should be deleted.</p>\n<div class=\"table-responsive\"><table border=\"2\" cellpadding=\"5\" cellspacing=\"2\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td valign=\"top\">\n<p><strong>Table with customer appends</strong></p>\n</td>\n<td valign=\"top\">\n<p><strong>DDL sources of CDS-views  for which view extensions are not needed anymore<em> </em></strong></p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\">\n<p>EBEW</p>\n</td>\n<td valign=\"top\">\n<p>MBV_EBEW_EXT</p>\n<p>MBV_EBEW_ML_ONLY</p>\n<p>MBV_EBEW_BASIS</p>\n<p>MBV_EBEW_CASE</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\">\n<p>EBEWH</p>\n</td>\n<td valign=\"top\">\n<p>MBV_EBEWH_EXT</p>\n<p>MBV_EBEWH_ASSOC</p>\n<p>MBV_EBEWH_ML_ONLY</p>\n<p>MBV_EBEWH_BASIS</p>\n<p>MBV_EBEWH_CASE</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\">\n<p>MBEW</p>\n</td>\n<td valign=\"top\">\n<p>MBV_MBEW_EXT</p>\n<p>MBV_MBEW_ML_ONLY</p>\n<p>MBV_MBEW_BASIS</p>\n<p>MBV_MBEW_CASE</p>\n<p>MBV_MBEW_MOTH_SEG</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\">\n<p>MBEWH</p>\n</td>\n<td valign=\"top\">\n<p>MBV_MBEWH_EXT</p>\n<p>MBV_MBEWH_ASSOC</p>\n<p>MBV_MBEWH_ML_ONLY</p>\n<p>MBV_MBEWH_BASIS</p>\n<p>MBV_MBEWH_CASE</p>\n<p>MBV_MBEWH_MOTH_SEG</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\">\n<p>OBEW</p>\n</td>\n<td valign=\"top\">\n<p>MBV_OBEW_EXT</p>\n<p>MBV_OBEW_ML_ONLY</p>\n<p>MBV_OBEW_BASIS</p>\n<p>MBV_OBEW_CASE</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\">\n<p>OBEWH</p>\n</td>\n<td valign=\"top\">\n<p>MBV_OBEWH_EXT</p>\n<p>MBV_OBEWH_ASSOC</p>\n<p>MBV_OBEWH_ML_ONLY</p>\n<p>MBV_OBEWH_BASIS</p>\n<p>MBV_OBEWH_CASE</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\">\n<p>QBEW</p>\n</td>\n<td valign=\"top\">\n<p>MBV_QBEW_EXT</p>\n<p>MBV_QBEW_ML_ONLY</p>\n<p>MBV_QBEW_BASIS</p>\n<p>MBV_QBEW_CASE</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\">\n<p>QBEWH</p>\n</td>\n<td valign=\"top\">\n<p>MBV_QBEWH_EXT</p>\n<p>MBV_QBEWH_ASSOC</p>\n<p>MBV_QBEWH_ML_ONLY</p>\n<p>MBV_QBEWH_BASIS</p>\n<p>MBV_QBEWH_CASE</p>\n</td>\n</tr>\n</tbody>\n</table></div>\n<p><em>Table 3: Affected CDS views for which view extensions are not needed anymore</em><em> </em></p>\n<p><em> </em></p>\n<p><strong>1.1 Fields containing material master data attributes</strong></p>\n<p>If customer appends only contain material master data attributes, the views could be made compatible by extension views. Extension views take care that fields, defined in the extension view, are appended at the end of the extended view.</p>\n<p>An extension view needs to be defined for the CDS-view, listed in table 2, for the corresponding xBEW(H) table.<br/>For more information about view extension see  <a href=\"http://help.sap.com/abapdocu_740/en/index.htm?file=abencds_f1_extend_view.htm\" rel=\"nofollow\" target=\"_blank\">SAP NetWeaver 7.4 documentation</a></p>\n<p>Note: if you need to enhance the CDS view (MBV_EBEWH, MBV_MBEWH, MBV_MBEWH_BASE, MBV_OBEWH or MBV_QBEWH) for the corresponding history table, please apply SAP note <a class=\"external-link\" href=\"/notes/2407605\" target=\"_blank\">2407605</a> firstly.</p>\n<p> </p>\n<p><strong>1.2 Fields representing a customer defined quantity/value to be aggregated</strong></p>\n<p>If customer appends contain aggregated fields, the proxy view (same approach as described for material master data attributes) and most probably also the views in the view-hierarchy needs to be replaced by new CDS views in customer namespace. This view must contain the same fields and types as the corresponding xBEW(H) table. The newly created CDS view must be specified as a new proxy view via transaction SE11 --&gt; Menu --&gt; Extras --&gt; Proxy Object.</p>\n<p>In most cases extension views cannot be used, since currently they do not support aggregate expressions.</p>\n<p> </p>\n<p><strong>1.3 Customer appends on views</strong></p>\n<p>There are several views in SAP Standard which also do have an assigned proxy view because the view reads transactional data from an xBEW(H) table. Since transactional data are not updated anymore in the xBEW(H) table, view accesses are redirected to corresponding proxy views.</p>\n<p>View and assigned proxy view must be compatible in structure too. If there are customer appends on such view the same rules as for tables apply. Views with assigned proxy compatibility view are identified by the provided pre-check (SAP note <a class=\"external-link\" href=\"/notes/2194618\" target=\"_blank\">2194618</a>).</p>\n<p> </p>\n<p><strong>1.3.1 Customer views using material master attributes</strong></p>\n<p>Such views using only material master data attributes from the xBEW(H) tables do not need to be changed.</p>\n<p> </p>\n<p><strong>1.3.2 Customer views using transactional attributes</strong></p>\n<p>Customer views, accessing xBEW(H) tables and having at least one transactional attribute (LBKUM, SALK3, VKSAL, SALKV or own customer transactional fields)  need to be re-build by creating a new CDS view with a new name.</p>\n<p>Instead of accessing the original xBEW(H) table, the corresponding DDL source, listed in table 1, needs to be accessed.</p>\n<p>There are two options how to handle accesses to the old DDIC SQL view:</p>\n<ol>\n<li>Redirect accesses to new CDS proxy view via transaction SE11 (Extras --&gt; Proxy Object)</li>\n<li>Code adaption of accesses to old SQL DDIC SQL view</li>\n</ol>\n<p> </p>\n<p><strong>2 Code adjustments and optimizations</strong></p>\n<p>Technically it is still possible to do database write operations (INSERT, UPDATE, DELETE, MODIFY) on transactional fields. But such write operations are without any effect, since transactional data is read from the Universal Journal Entry Line Items table and the Material Ledger table!</p>\n<p>Therefore, write operations on xBEW(H) tables, affecting transactional fields, shall be removed from customer coding.</p>\n<p>Write operations on material master data attributes shall still happen on the xBEW(H) tables.</p>\n<p> </p>\n<p>DB read operations on the xBEW(H) tables have a performance decrease, since transactional data is retrieved from the Universal Journal Entry Line Items table and the Material Ledger via database joins and aggregation. Consequently, it shall be avoided to read transactional data when only master data is required. Therefore it is recommended to adjust the customer coding in the following way:</p>\n<ul>\n<li>If material master data as well transactional data is required then the SELECT &lt;table&gt; should be replaced by using a data access method from class CL_MBV_SELECT_&lt;table&gt;. These classes provide access methods for single (SINGLE_READ) as well as array (ARRAY_READ) read operations.</li>\n<li>If only material master data is required then the SELECT &lt;table&gt; should be replaced by SELECT on view V_&lt;table&gt;_MD. Alternatively, corresponding material master data read methods (READ_MASTER_DATA) in the class CL_MBV_SELECT_&lt;table&gt; can be used. Also the data type declarations should be adjusted from TYPE &lt;table&gt; to TYPE V_&lt;table&gt;_MD.</li>\n<li>If only transactional fields are required then the SELECT &lt;table&gt; could be replaced by using the data access methods ARRAY_READ_TRANSACTIONAL_DATA and SINGLE_READ_TRANSACTIONAL_DATA from class CL_MBV_SELECT_&lt;table&gt;.</li>\n</ul>\n<p> </p>\n<p>For performance critical coding parts these adjustments are strongly recommended. For non critical parts it is optional short term but recommended on long term.</p>\n<p>To identify such locations, it is required to make use of the where-used functionality of transaction SE11 and considering other techniques like transaction CODE_SCANNER to find locations which SE11 cannot handle – like dynamic programming or native SQL statements.</p>\n<p>Consider SAP Note <a class=\"external-link\" href=\"/notes/28022\" target=\"_blank\">28022</a>, if there are issues with the where-used functionality in the customer system. In the where-used dialog it is possible via the button \"Search Range\" to search specific for key words like SELECT, INSERT and so on.</p>", "noteVersion": 10}], "activities": [{"Activity": "Custom Code Adaption", "Phase": "During conversion project", "Condition": "Mandatory", "Additional_Information": "Adjust custom code according to ATC check results and pre-check findings described in note 2197392"}]}