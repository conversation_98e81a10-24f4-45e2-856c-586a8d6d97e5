{"guid": "6CAE8B3EA08B1ED6A5D5B01EDB8CE0C1", "sitemId": "SI33: Logistics_General", "sitemTitle": "S4TWL - Retail Information System", "note": 2370131, "noteTitle": "2370131 - S4TWL - Retail Information System (RIS) and Category Management Workflow", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are doing a system conversion to SAP S/4HANA. The following SAP S/4HANA Transition Worklist item is applicable in this case.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>RIS, Retail Information System, LIS, Logistics Information System, BW/4HANA, Retail</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong>Description</strong></p>\n<p>The information systems used to plan, control, and monitor business events at different stages in the decision-making process. Tools were available in Customizing to allow a self-defined information system to be created and tailored to specific requirements.</p>\n<p><strong>Business Process related information</strong></p>\n<p>In SAP S/4HANA, RIS functionality and Category Management Workflow are not available anymore.</p>\n<p><strong>Required and Recommended Action(s)</strong></p>\n<p>RIS (Retail Information System) technology is not available anymore.</p>\n<p>To cover reporting requirements usage of <a href=\"https://help.sap.com/nwbw\" target=\"_blank\">SAP BW</a> or <a href=\"https://help.sap.com/viewer/p/SAP_BW4HANA\" target=\"_blank\">SAP BW/4HANA</a> is recommended. <br/>There is content for <a href=\"https://help.sap.com/viewer/d1e4c9f0ffc047ec9f945e64026ffab1/7.07.22/en-US/d09a865342bc3d58e10000000a174cb4.html\" target=\"_blank\">Retail</a> for SAP BW, which however does not cover an identical scope as RIS. <br/>There is no retail specific content for SAP BW/4HANA. Thus, content need to be created or adjusted part of the implementation project.</p>\n<p>To cover operational reporting requirements <a href=\"https://help.sap.com/viewer/8308e6d301d54584a33cd04a9861bc52/2020.000/en-US/5418de55938d1d22e10000000a44147b.html\" target=\"_blank\">CDS</a> (Core Data Services) technology should be used. It is planned to offer retail specific content in a future release. Till then, content need to be created part of the implementation project.</p>\n<p>In case you reuse ABAP objects of packets WRB or WRBA in your custom code, please see attached note.</p>\n<p>Information structure S160 is used for perishables procurement.  In SAP S/4HANA, perishables procurement is not available from release SAP S/4HANA 1610 till 1909. Starting with SAP S/4HANA 2020 perishables procurement is available again. Thus, information structure S160 can be used from SAP S/4HANA 2020. It is planned to substitute functionality of information structure S160 by CDS technology in a future release.</p>\n<p><br/>Coding for retail specific enhancements is controlled via settings in table TMCW. Please deactivate all settings, or delete the entry in table TMCW. Active settings can lead to reduction in performance or even errors. You can maintain entries in table TMCW through transaction SM30 view V_TMCW.</p>\n<p><strong>How to Determine Relevancy</strong></p>\n<p>This Simplification Item is relevant if RIS is used. <br/>This can be checked via transaction SE16N. Enter table S077 S078 S079 S080 S081 S082 S083 S084 S085 S086 S087 S105 S106 S107 S108 S110 S117 S119 S120 S121 S122 S123 S124 S160 S200 S202 S203 S204 S205 S206 S207 S208 S209 S210 S211 S212 S214 S216 S217 S218 S219 S270 S271 S272 S273 S274 S275 S276 S277 S278 S279 and check whether there are any entries.<br/>Custom specific RIS structures can be detected via transaction SE16N for table TMC4 with TMC4-MCAPP = 40 and TMC4-KBTCH is not initial (i.e. = 1, 2, 3, D). <br/>RIS structure S160 may be active as it is used for Perishable Procurement, see note 2368739.</p>\n<p>If you have RIS structures with activated updating, and want to convert from ECC to S/4HANA, please deactivate updating prior to the conversion via transaction MCH6.</p>\n<p>Relevant transaction are CMPRO, MC1!, MC1$, MC1§, MCG2, MCG3, MCGC, MCGD, MCGE, MCGF, MCGG, MCGH, MCGJ, MCGK, MCGL, MCH+, MCH0, MCH1, MCH2, MCH3, MCH4, MCH7, MCH8, MCH9, MCH:, MCHA, MCHB, MCHC, MCHD, MCHG, MCHP, MCHS, MCHV, MCHY, MCHZ, MCH_, MCM%, MCMX, MCMY, MCMZ, MCWIS, MCWRP, T108, T109, T110, T111, T112, T113, T114, T115, TR02, WCM1, WCM2, WCM3, WCM4, WCMA, WCMB, WCMC, WCMCP, WCMD, WCME, WDKR.</p>\n<p>In case you reuse ABAP objects of packets CM_WF, MCW, WVKP_RRM, WWGR in your custom code, please see attached note.</p>\n<p>This Simplification Item is relevant if Category Management Workflow is used.<br/>This also can be checked via transaction SE16N. Enter table WCMP and check whether there are any entries.</p>", "noteVersion": 17, "refer_note": [{"note": "2352561", "noteTitle": "2352561 - SAP S/4HANA Conversion Pre-Checks: Information for Industry Solution ISR_RETAIL and Retail Information System", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>During the transition to SAP S/4HANA the pre-check fails with the error message: 'Client `...  `: RIS Infostructures are activ ` S077.....`</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>S4TC; RIS;</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>In SAP S/4HANA the Retail Information System is not available anymore, that means for LIS-application 40 all infostructures and the corresponding standard-analyses cannot be used.</p>\n<p>- here is the list of infostructures:</p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"3\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td><span><em><strong>Title</strong></em></span></td>\n<td><span><em><strong>Infostructures</strong></em></span></td>\n<td><span><em><strong>Standar Analysis</strong></em></span></td>\n</tr>\n<tr>\n<td>STPRS</td>\n<td>S077,  S078, S079 </td>\n<td>RMCW0300, RMCW0300</td>\n</tr>\n<tr>\n<td>Purchasing</td>\n<td>S080, S081, S082 </td>\n<td>RMCW0600, RMCW0605</td>\n</tr>\n<tr>\n<td>Article</td>\n<td>S083, S084, S085 </td>\n<td>RMCW0900, RMCW0905</td>\n</tr>\n<tr>\n<td>Promotion</td>\n<td>S086</td>\n<td>RMCW1000, RMCW1005</td>\n</tr>\n<tr>\n<td>Material / Add-on:</td>\n<td>S087 </td>\n<td>RMCW1100, RMCW1105</td>\n</tr>\n<tr>\n<td>Season</td>\n<td>S105, S106, S107  </td>\n<td>RMCW1400, RMCW1405</td>\n</tr>\n<tr>\n<td>Additionals</td>\n<td>S108</td>\n<td>RMCW2900, RMCW2905</td>\n</tr>\n<tr>\n<td>OTB</td>\n<td>S110 </td>\n<td> </td>\n</tr>\n<tr>\n<td>Receipt / article</td>\n<td>S117, S118 </td>\n<td>RMCW2500, RMCW2505</td>\n</tr>\n<tr>\n<td>\n<p>POS: Sales based on receipts</p>\n<p>POS: Material Aggregation</p>\n<p>POS: Cashier</p>\n</td>\n<td>\n<p>S119, S120,</p>\n<p>S121,</p>\n<p>S122,</p>\n</td>\n<td>\n<p>RMCW1600, RMCW1605, RMCW2700, RMCW2705</p>\n<p>RMCW1700, RMCW1705,</p>\n<p>RMCW1800, RMCW1805, RMCW2800, RMCW2805</p>\n</td>\n</tr>\n<tr>\n<td>\n<p> Customer / Material</p>\n</td>\n<td>\n<p>S123, S124</p>\n</td>\n<td>\n<p>RMCW1900, RMCW1905</p>\n<p>RMCW2000, RMCW2005</p>\n</td>\n</tr>\n<tr>\n<td>Perishables</td>\n<td>S160</td>\n<td>RMCW2300, RMCW2305</td>\n</tr>\n<tr>\n<td>Inventory Controlling Stores</td>\n<td>S200</td>\n<td>RMCW2400, RMCW2405</td>\n</tr>\n<tr>\n<td>MAC</td>\n<td>S202, S203, S204, S205, S206, S207, S208, S209,  S210, S211, S216, S217, S218 </td>\n<td>n.a.</td>\n</tr>\n<tr>\n<td>CatMan</td>\n<td>S212, S214, S219</td>\n<td>n.a.</td>\n</tr>\n</tbody>\n</table></div>\n<p> </p>\n<p> </p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Before the conversion to SAP S/4HANA please deactivate the statistic update of the infostructures which are listed in the pre-check, with transaction MCH6 on the ERP side - a conversion to SAP S/4HANA with 'activ statistic update' is not possible.</p>\n<p>Remark: The transaction WVM1 (Customer Replenishment), which uses the statistic data of the infostructure S130 will be available furthermore therefore the statistic update for S130 can be activated as before.</p>\n<p> </p>", "noteVersion": 6}, {"note": "2486476", "noteTitle": "2486476 - Removing RFC flag for Function Modules in Retail Category Management", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>The following function modules are remote-enabled. They can be called via Remote Function Call (RFC) from remote systems. It is however not intended nor released for customers that these function modules can be called via RFC:</p>\n<ul>\n<li><span 'times=\"\" ar-sa;\"=\"\" calibri',sans-serif;=\"\" calibri;=\"\" de;=\"\" minor-latin;=\"\" mso-ansi-language:=\"\" mso-bidi-font-family:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" mso-fareast-theme-font:=\"\" new=\"\" roman';=\"\"><span 'times=\"\" ar-sa;\"=\"\" calibri',sans-serif;=\"\" calibri;=\"\" de;=\"\" minor-latin;=\"\" mso-ansi-language:=\"\" mso-bidi-font-family:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" mso-fareast-theme-font:=\"\" new=\"\" roman';=\"\">CMPROJECT_DATA_READ</span></span></li>\n<li><span 'times=\"\" ar-sa;\"=\"\" calibri',sans-serif;=\"\" calibri;=\"\" de;=\"\" minor-latin;=\"\" mso-ansi-language:=\"\" mso-bidi-font-family:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" mso-fareast-theme-font:=\"\" new=\"\" roman';=\"\">READ_ALL_CM_PROJECTS</span></li>\n<li><span 'times=\"\" ar-sa;\"=\"\" calibri',sans-serif;=\"\" calibri;=\"\" de;=\"\" minor-latin;=\"\" mso-ansi-language:=\"\" mso-bidi-font-family:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" mso-fareast-theme-font:=\"\" new=\"\" roman';=\"\"><span 'times=\"\" ar-sa;\"=\"\" calibri',sans-serif;=\"\" calibri;=\"\" de;=\"\" en-us;=\"\" lang=\"EN-US\" minor-latin;=\"\" mso-ansi-language:=\"\" mso-bidi-font-family:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" mso-fareast-theme-font:=\"\" new=\"\" roman';=\"\">READ_ALL_CM_PROJECTS_PER_USER</span></span></li>\n</ul>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>RFC, remote-enabled, Retail, Category Management, Workflow</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>The function modules could be called via RFC, which is not intended.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>The correction removes the remote-enabled flags. RFC calls are not processed anymore.</p>\n<p>Implement the support package or the correction instructions.</p>", "noteVersion": 2}, {"note": "2383533", "noteTitle": "2383533 - S4TWL - Retail Deprecated Applications Relevance for Custom Code", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are doing a system conversion to SAP S/4HANA, and you are using some Retail related business applications. The following SAP S/4HANA Transition Worklist item is applicable in this case.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong>Description</strong></p>\n<p>In SAP S/4HANA Retail for merchandise management some applications and the respective development objects are not available anymore. <br/>This might be relevant for customer specific coding. If customer specific coding re-used those development objects in ERP, the coding needs to be adjusted accordingly.</p>\n<p><strong>Required and Recommended Action(s)</strong></p>\n<p>Adjust customer specific coding accordingly.</p>", "noteVersion": 2}, {"note": "2356537", "noteTitle": "2356537 - S4TC SAP_APPL – Pre-Transition Checks for  Retail Information System (RIS)", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p><span>Pre-Transition Checks for Retail Information System (RIS) have to be executed before the upgrade to S/4.</span></p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>S4TC; RIS;</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>Installation of pre-transition check class for RIS.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Refer to the attached Correction Instructions</p>\n<p> </p>", "noteVersion": 5, "refer_note": [{"note": "2370131", "noteTitle": "2370131 - S4TWL - Retail Information System (RIS) and Category Management Workflow", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are doing a system conversion to SAP S/4HANA. The following SAP S/4HANA Transition Worklist item is applicable in this case.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>RIS, Retail Information System, LIS, Logistics Information System, BW/4HANA, Retail</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong>Description</strong></p>\n<p>The information systems used to plan, control, and monitor business events at different stages in the decision-making process. Tools were available in Customizing to allow a self-defined information system to be created and tailored to specific requirements.</p>\n<p><strong>Business Process related information</strong></p>\n<p>In SAP S/4HANA, RIS functionality and Category Management Workflow are not available anymore.</p>\n<p><strong>Required and Recommended Action(s)</strong></p>\n<p>RIS (Retail Information System) technology is not available anymore.</p>\n<p>To cover reporting requirements usage of <a href=\"https://help.sap.com/nwbw\" target=\"_blank\">SAP BW</a> or <a href=\"https://help.sap.com/viewer/p/SAP_BW4HANA\" target=\"_blank\">SAP BW/4HANA</a> is recommended. <br/>There is content for <a href=\"https://help.sap.com/viewer/d1e4c9f0ffc047ec9f945e64026ffab1/7.07.22/en-US/d09a865342bc3d58e10000000a174cb4.html\" target=\"_blank\">Retail</a> for SAP BW, which however does not cover an identical scope as RIS. <br/>There is no retail specific content for SAP BW/4HANA. Thus, content need to be created or adjusted part of the implementation project.</p>\n<p>To cover operational reporting requirements <a href=\"https://help.sap.com/viewer/8308e6d301d54584a33cd04a9861bc52/2020.000/en-US/5418de55938d1d22e10000000a44147b.html\" target=\"_blank\">CDS</a> (Core Data Services) technology should be used. It is planned to offer retail specific content in a future release. Till then, content need to be created part of the implementation project.</p>\n<p>In case you reuse ABAP objects of packets WRB or WRBA in your custom code, please see attached note.</p>\n<p>Information structure S160 is used for perishables procurement.  In SAP S/4HANA, perishables procurement is not available from release SAP S/4HANA 1610 till 1909. Starting with SAP S/4HANA 2020 perishables procurement is available again. Thus, information structure S160 can be used from SAP S/4HANA 2020. It is planned to substitute functionality of information structure S160 by CDS technology in a future release.</p>\n<p><br/>Coding for retail specific enhancements is controlled via settings in table TMCW. Please deactivate all settings, or delete the entry in table TMCW. Active settings can lead to reduction in performance or even errors. You can maintain entries in table TMCW through transaction SM30 view V_TMCW.</p>\n<p><strong>How to Determine Relevancy</strong></p>\n<p>This Simplification Item is relevant if RIS is used. <br/>This can be checked via transaction SE16N. Enter table S077 S078 S079 S080 S081 S082 S083 S084 S085 S086 S087 S105 S106 S107 S108 S110 S117 S119 S120 S121 S122 S123 S124 S160 S200 S202 S203 S204 S205 S206 S207 S208 S209 S210 S211 S212 S214 S216 S217 S218 S219 S270 S271 S272 S273 S274 S275 S276 S277 S278 S279 and check whether there are any entries.<br/>Custom specific RIS structures can be detected via transaction SE16N for table TMC4 with TMC4-MCAPP = 40 and TMC4-KBTCH is not initial (i.e. = 1, 2, 3, D). <br/>RIS structure S160 may be active as it is used for Perishable Procurement, see note 2368739.</p>\n<p>If you have RIS structures with activated updating, and want to convert from ECC to S/4HANA, please deactivate updating prior to the conversion via transaction MCH6.</p>\n<p>Relevant transaction are CMPRO, MC1!, MC1$, MC1§, MCG2, MCG3, MCGC, MCGD, MCGE, MCGF, MCGG, MCGH, MCGJ, MCGK, MCGL, MCH+, MCH0, MCH1, MCH2, MCH3, MCH4, MCH7, MCH8, MCH9, MCH:, MCHA, MCHB, MCHC, MCHD, MCHG, MCHP, MCHS, MCHV, MCHY, MCHZ, MCH_, MCM%, MCMX, MCMY, MCMZ, MCWIS, MCWRP, T108, T109, T110, T111, T112, T113, T114, T115, TR02, WCM1, WCM2, WCM3, WCM4, WCMA, WCMB, WCMC, WCMCP, WCMD, WCME, WDKR.</p>\n<p>In case you reuse ABAP objects of packets CM_WF, MCW, WVKP_RRM, WWGR in your custom code, please see attached note.</p>\n<p>This Simplification Item is relevant if Category Management Workflow is used.<br/>This also can be checked via transaction SE16N. Enter table WCMP and check whether there are any entries.</p>", "noteVersion": 17}]}, {"note": "2750278", "noteTitle": "2750278 - Wrong return code for pre-transition checks for Retail Information System (RIS)", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are doing a system conversion to SAP S/4HANA and are using the Retail Information Systems (RIS) which is not supported by S/4HANA. In the SUM phase PREP_EXTENSION/RUN_S4H_SIF_CHECK_INIT you get and error (return code \"12\") that RIS is active and you cannot continue. You could turn off RIS now to overcome the error but then you could not use RIS in the SUM uptime phase.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Retail, S/4HANA, CLS4SIC_RETAIL_INFORMATION_SYS, Simplifiction Item <a href=\"SI33: Logistics_General\" target=\"_blank\">SI33: Logistics_General</a></p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>You are doing a system conversion to SAP S/4HANA and are using the Retail Information System (RIS) which is not supported by S/4HANA.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>The return code of the check for RIS being active has been changed so that the SUM phase is no longer aborted (return code \"12\") in the preparation phase but the error can be evaluated and a manual decision can be made how severe the error is with the option that you can either skip the error in total or until end of the uptime (return code \"7\").</p>\n<p>Please implement this SAP Note or the corresponding transport based correction instruction.</p>", "noteVersion": 2}, {"note": "2944418", "noteTitle": "2944418 - Retail starter pack service for SAP BW/4HANA", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p id=\"\"> You are running SAP BW/4HANA and want to implement a data model for POS Analytics and/or retail-specific Inventory Management.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p id=\"\"> BW/4HANA, BI Content Add-On, BW on HANA, IS Retail, CAR, POS DTA</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>SAP BW/4HANA 1.0 or higher with BW/4HANA Content Add-On 1.0<br/>or<br/>SAP BW 7.50 or higher with BI Content Add-On<br/><br/>For POS Analytics data model:<br/>SAP Customer Activity Repository 1.0 or higher with POSDM or POSDTA implemented <br/><br/>For Inventory Management data model:<br/>SAP S/4HANA for Merchandise Management 1709 or higher or SAP ECC Retail 6.0</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p id=\"\">The SAP Global Retail Hub within SAP Consulting provides the engineered service \"Retail Starter Pack for SAP BW/4HANA\" that delivers BW objects and data flows as basis for customer-specific POS Analytics and retail-specific Inventory Management reporting.<br/><br/>Further details regarding this service offer can be found in the attached document.<br/><br/>In case you are interested in this service offer, please contact you SAP representative and refer to service 50150314 in the SAP Services and Support Catalog.</p>", "noteVersion": 2}, {"note": "2543543", "noteTitle": "2543543 - Restrictions for BW extractors relevant for S/4HANA in the area of Retail", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>Several data sources for BW extraction are no longer available with S/4 in the area of Retail as part of the product version SAP S/4HANA, on-premise edition 1610 and higher.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>S/4HANA, BW, Extractors, IS-Retail</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>See the reasons given below</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>The following DataSources are not supported any longer:</p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"10\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td>DataSource</td>\n<td>Appl.component</td>\n<td>Restriction</td>\n<td>Comment</td>\n<td>Related SI</td>\n</tr>\n<tr>\n<td>0FIPMATNR_ATTR</td>\n<td>LO-RFM-PUR-FIP and MM-PUR-FIP</td>\n<td>Deprecated</td>\n<td>DS not working - no alternative exists</td>\n<td><span><span #0563c1;=\"\" ar-sa;\"=\"\" calibri',sans-serif;=\"\" calibri;=\"\" color:=\"\" de;=\"\" minor-latin;=\"\" mso-ansi-language:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" mso-fareast-theme-font:=\"\"><a href=\"/notes/2368747\" target=\"_blank\">2368747</a></span></span></td>\n</tr>\n<tr>\n<td>0RT_RMA_VAL_TRAN</td>\n<td>LO-RFM-OBS and LO-LIS-DC</td>\n<td>Deprecated</td>\n<td>DS not working - no alternative exists</td>\n<td><span><span #0563c1;=\"\" ar-sa;\"=\"\" calibri',sans-serif;=\"\" calibri;=\"\" color:=\"\" de;=\"\" minor-latin;=\"\" mso-ansi-language:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" mso-fareast-theme-font:=\"\"><a href=\"/notes/2368736\" target=\"_blank\">2368736</a></span></span></td>\n</tr>\n<tr>\n<td>2LIS_40_S202</td>\n<td>LO-RFM-OBS and LO-MAP</td>\n<td>Deprecated</td>\n<td>DS not working - no alternative exists</td>\n<td><span><span #0563c1;=\"\" ar-sa;\"=\"\" calibri',sans-serif;=\"\" calibri;=\"\" color:=\"\" de;=\"\" minor-latin;=\"\" mso-ansi-language:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" mso-fareast-theme-font:=\"\"><a href=\"/notes/2368741\" target=\"_blank\">2368741</a></span></span></td>\n</tr>\n<tr>\n<td>2LIS_40_S207</td>\n<td>LO-RFM-OBS and LO-MAP</td>\n<td>Deprecated</td>\n<td>DS not working - no alternative exists</td>\n<td><span><span #0563c1;=\"\" ar-sa;\"=\"\" calibri',sans-serif;=\"\" calibri;=\"\" color:=\"\" de;=\"\" minor-latin;=\"\" mso-ansi-language:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" mso-fareast-theme-font:=\"\"><a href=\"/notes/2368741\" target=\"_blank\">2368741</a></span></span></td>\n</tr>\n<tr>\n<td>2LIS_40_S208</td>\n<td>LO-RFM-OBS and LO-MAP</td>\n<td>Deprecated</td>\n<td>DS not working - no alternative exists</td>\n<td><span><span #0563c1;=\"\" ar-sa;\"=\"\" calibri',sans-serif;=\"\" calibri;=\"\" color:=\"\" de;=\"\" minor-latin;=\"\" mso-ansi-language:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" mso-fareast-theme-font:=\"\"><a href=\"/notes/2368741\" target=\"_blank\">2368741</a></span></span></td>\n</tr>\n<tr>\n<td>2LIS_40_S212</td>\n<td>LO-RFM-OBS and LO-MAP</td>\n<td>Deprecated</td>\n<td>DS not working - no alternative exists</td>\n<td><span><span #0563c1;=\"\" ar-sa;\"=\"\" calibri',sans-serif;=\"\" calibri;=\"\" color:=\"\" de;=\"\" minor-latin;=\"\" mso-ansi-language:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" mso-fareast-theme-font:=\"\"><a href=\"/notes/2368741\" target=\"_blank\">2368741</a></span></span></td>\n</tr>\n<tr>\n<td>2LIS_40_S214</td>\n<td>LO-RFM-OBS and LO-MAP</td>\n<td>Deprecated</td>\n<td>DS not working - no alternative exists</td>\n<td><span><span #0563c1;=\"\" ar-sa;\"=\"\" calibri',sans-serif;=\"\" calibri;=\"\" color:=\"\" de;=\"\" minor-latin;=\"\" mso-ansi-language:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" mso-fareast-theme-font:=\"\"><a href=\"/notes/2368741\" target=\"_blank\">2368741</a></span></span></td>\n</tr>\n<tr>\n<td>2LIS_40_S219</td>\n<td>LO-RFM-OBS and LO-MAP</td>\n<td>Deprecated</td>\n<td>DS not working - no alternative exists</td>\n<td><span><span #0563c1;=\"\" ar-sa;\"=\"\" calibri',sans-serif;=\"\" calibri;=\"\" color:=\"\" de;=\"\" minor-latin;=\"\" mso-ansi-language:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" mso-fareast-theme-font:=\"\"><a href=\"/notes/2368741\" target=\"_blank\">2368741</a></span></span></td>\n</tr>\n<tr>\n<td>0RT_RMA_MBEW_TRAN</td>\n<td>LO-RFM-OBS and MM-IM</td>\n<td>Deprecated</td>\n<td>DS not working - no alternative exists</td>\n<td><span><span><a href=\"/notes/2368736\" target=\"_blank\">2368736</a></span></span></td>\n</tr>\n<tr>\n<td>0RT_STOREGA_ATTR</td>\n<td>LO-RFM-OBS and LO-MAP</td>\n<td>Deprecated</td>\n<td>DS not working - no alternative exists</td>\n<td><span><span><a href=\"/notes/2368741\" target=\"_blank\">2368741</a></span></span></td>\n</tr>\n<tr>\n<td>0RT_STOREGR_TEXT</td>\n<td>LO-RFM-OBS and LO-MAP</td>\n<td>Deprecated</td>\n<td>DS not working - no alternative exists</td>\n<td><span><span><a href=\"/notes/2368741\" target=\"_blank\">2368741</a></span></span></td>\n</tr>\n<tr>\n<td>0CM_CDT1_ATTR</td>\n<td>LO-RFM-OBS and LO-MD</td>\n<td>Deprecated</td>\n<td>DS not working - alternative exists, 0RF_ARTHIER1_ATTR</td>\n<td><span><span #0563c1;=\"\" ar-sa;\"=\"\" calibri',sans-serif;=\"\" calibri;=\"\" color:=\"\" de;=\"\" minor-latin;=\"\" mso-ansi-language:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" mso-fareast-theme-font:=\"\"><a href=\"/notes/2368680\" target=\"_blank\">2368680</a></span></span></td>\n</tr>\n<tr>\n<td>0CM_CDT1_TEXT</td>\n<td>LO-RFM-OBS and LO-MD</td>\n<td>Deprecated</td>\n<td>DS not working - alternative exists, 0RF_ARTHIER1_TEXT</td>\n<td><span><span #0563c1;=\"\" ar-sa;\"=\"\" calibri',sans-serif;=\"\" calibri;=\"\" color:=\"\" de;=\"\" minor-latin;=\"\" mso-ansi-language:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" mso-fareast-theme-font:=\"\"><a href=\"/notes/2368680\" target=\"_blank\">2368680</a></span></span></td>\n</tr>\n<tr>\n<td>0CM_CDT2_ATTR</td>\n<td>LO-RFM-OBS and LO-MD</td>\n<td>Deprecated</td>\n<td>DS not working - alternative exists, 0RF_ARTHIER2_ATTR</td>\n<td><span><span #0563c1;=\"\" ar-sa;\"=\"\" calibri',sans-serif;=\"\" calibri;=\"\" color:=\"\" de;=\"\" minor-latin;=\"\" mso-ansi-language:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" mso-fareast-theme-font:=\"\"><a href=\"/notes/2368680\" target=\"_blank\">2368680</a></span></span></td>\n</tr>\n<tr>\n<td>0CM_CDT2_TEXT</td>\n<td>LO-RFM-OBS and LO-MD</td>\n<td>Deprecated</td>\n<td>DS not working - alternative exists, 0RF_ARTHIER2_TEXT</td>\n<td><span><span #0563c1;=\"\" ar-sa;\"=\"\" calibri',sans-serif;=\"\" calibri;=\"\" color:=\"\" de;=\"\" minor-latin;=\"\" mso-ansi-language:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" mso-fareast-theme-font:=\"\"><a href=\"/notes/2368680\" target=\"_blank\">2368680</a></span></span></td>\n</tr>\n<tr>\n<td>0CM_CDT3_ATTR</td>\n<td>LO-RFM-OBS and LO-MD</td>\n<td>Deprecated</td>\n<td>DS not working - alternative exists, 0RF_ARTHIER3_ATTR</td>\n<td><span><span #0563c1;=\"\" ar-sa;\"=\"\" calibri',sans-serif;=\"\" calibri;=\"\" color:=\"\" de;=\"\" minor-latin;=\"\" mso-ansi-language:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" mso-fareast-theme-font:=\"\"><a href=\"/notes/2368680\" target=\"_blank\">2368680</a></span></span></td>\n</tr>\n<tr>\n<td>0CM_CDT3_TEXT</td>\n<td>LO-RFM-OBS and LO-MD</td>\n<td>Deprecated</td>\n<td>DS not working - alternative exists, 0RF_ARTHIER3_TEXT</td>\n<td><span><span #0563c1;=\"\" ar-sa;\"=\"\" calibri',sans-serif;=\"\" calibri;=\"\" color:=\"\" de;=\"\" minor-latin;=\"\" mso-ansi-language:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" mso-fareast-theme-font:=\"\"><a href=\"/notes/2368680\" target=\"_blank\">2368680</a></span></span></td>\n</tr>\n<tr>\n<td>0CM_CDT4_ATTR</td>\n<td>LO-RFM-OBS and LO-MD</td>\n<td>Deprecated</td>\n<td>DS not working - alternative exists, 0RF_ARTHIER4_ATTR</td>\n<td><span><span #0563c1;=\"\" ar-sa;\"=\"\" calibri',sans-serif;=\"\" calibri;=\"\" color:=\"\" de;=\"\" minor-latin;=\"\" mso-ansi-language:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" mso-fareast-theme-font:=\"\"><a href=\"/notes/2368680\" target=\"_blank\">2368680</a></span></span></td>\n</tr>\n<tr>\n<td>0CM_CDT4_TEXT</td>\n<td>LO-RFM-OBS and LO-MD</td>\n<td>Deprecated</td>\n<td>DS not working - alternative exists, 0RF_ARTHIER4_TEXT</td>\n<td><span><span #0563c1;=\"\" ar-sa;\"=\"\" calibri',sans-serif;=\"\" calibri;=\"\" color:=\"\" de;=\"\" minor-latin;=\"\" mso-ansi-language:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" mso-fareast-theme-font:=\"\"><a href=\"/notes/2368680\" target=\"_blank\">2368680</a></span></span></td>\n</tr>\n<tr>\n<td>0CM_CDT5_ATTR</td>\n<td>LO-RFM-OBS and LO-MD</td>\n<td>Deprecated</td>\n<td>DS not working - alternative exists, 0RF_ARTHIER5_ATTR</td>\n<td><span><span #0563c1;=\"\" ar-sa;\"=\"\" calibri',sans-serif;=\"\" calibri;=\"\" color:=\"\" de;=\"\" minor-latin;=\"\" mso-ansi-language:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" mso-fareast-theme-font:=\"\"><a href=\"/notes/2368680\" target=\"_blank\">2368680</a></span></span></td>\n</tr>\n<tr>\n<td>0CM_CDT5_TEXT</td>\n<td>LO-RFM-OBS and LO-MD</td>\n<td>Deprecated</td>\n<td>DS not working - alternative exists, 0RF_ARTHIER5_TEXT</td>\n<td><span><span #0563c1;=\"\" ar-sa;\"=\"\" calibri',sans-serif;=\"\" calibri;=\"\" color:=\"\" de;=\"\" minor-latin;=\"\" mso-ansi-language:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" mso-fareast-theme-font:=\"\"><a href=\"/notes/2368680\" target=\"_blank\">2368680</a></span></span></td>\n</tr>\n<tr>\n<td>0CM_CDT6_ATTR</td>\n<td>LO-RFM-OBS and LO-MD</td>\n<td>Deprecated</td>\n<td>DS not working - alternative exists, 0RF_ARTHIER6_ATTR</td>\n<td><span><span #0563c1;=\"\" ar-sa;\"=\"\" calibri',sans-serif;=\"\" calibri;=\"\" color:=\"\" de;=\"\" minor-latin;=\"\" mso-ansi-language:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" mso-fareast-theme-font:=\"\"><a href=\"/notes/2368680\" target=\"_blank\">2368680</a></span></span></td>\n</tr>\n<tr>\n<td>0CM_CDT6_TEXT</td>\n<td>LO-RFM-OBS and LO-MD</td>\n<td>Deprecated</td>\n<td>DS not working - alternative exists, 0RF_ARTHIER6_TEXT</td>\n<td><span><span #0563c1;=\"\" ar-sa;\"=\"\" calibri',sans-serif;=\"\" calibri;=\"\" color:=\"\" de;=\"\" minor-latin;=\"\" mso-ansi-language:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" mso-fareast-theme-font:=\"\"><a href=\"/notes/2368680\" target=\"_blank\">2368680</a></span></span></td>\n</tr>\n<tr>\n<td>0CM_CDT7_ATTR</td>\n<td>LO-RFM-OBS and LO-MD</td>\n<td>Deprecated</td>\n<td>DS not working - alternative exists, 0RF_ARTHIER7_ATTR</td>\n<td><span><span #0563c1;=\"\" ar-sa;\"=\"\" calibri',sans-serif;=\"\" calibri;=\"\" color:=\"\" de;=\"\" minor-latin;=\"\" mso-ansi-language:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" mso-fareast-theme-font:=\"\"><a href=\"/notes/2368680\" target=\"_blank\">2368680</a></span></span></td>\n</tr>\n<tr>\n<td>0CM_CDT7_TEXT</td>\n<td>LO-RFM-OBS and LO-MD</td>\n<td>Deprecated</td>\n<td>DS not working - alternative exists, 0RF_ARTHIER7_TEXT</td>\n<td><span><span #0563c1;=\"\" ar-sa;\"=\"\" calibri',sans-serif;=\"\" calibri;=\"\" color:=\"\" de;=\"\" minor-latin;=\"\" mso-ansi-language:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" mso-fareast-theme-font:=\"\"><a href=\"/notes/2368680\" target=\"_blank\">2368680</a></span></span></td>\n</tr>\n<tr>\n<td>0CM_HIER_ATTR</td>\n<td>LO-RFM-OBS and LO-MD</td>\n<td>Deprecated</td>\n<td>DS not working - alternative exists, 0RF_HIEID_ATTR</td>\n<td><span><span #0563c1;=\"\" ar-sa;\"=\"\" calibri',sans-serif;=\"\" calibri;=\"\" color:=\"\" de;=\"\" minor-latin;=\"\" mso-ansi-language:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" mso-fareast-theme-font:=\"\"><a href=\"/notes/2368680\" target=\"_blank\">2368680</a></span></span></td>\n</tr>\n<tr>\n<td>0CM_HIER_TEXT</td>\n<td>LO-RFM-OBS and LO-MD</td>\n<td>Deprecated</td>\n<td>DS not working - alternative exists, 0RF_HIEID_TEXT</td>\n<td><span><span #0563c1;=\"\" ar-sa;\"=\"\" calibri',sans-serif;=\"\" calibri;=\"\" color:=\"\" de;=\"\" minor-latin;=\"\" mso-ansi-language:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" mso-fareast-theme-font:=\"\"><a href=\"/notes/2368680\" target=\"_blank\">2368680</a></span></span></td>\n</tr>\n<tr>\n<td>0CM_ROLE_TEXT</td>\n<td>LO-RFM-OBS and LO-MD</td>\n<td>Deprecated</td>\n<td>DS not working - no alternative exists</td>\n<td><span><span #0563c1;=\"\" ar-sa;\"=\"\" calibri',sans-serif;=\"\" calibri;=\"\" color:=\"\" de;=\"\" minor-latin;=\"\" mso-ansi-language:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" mso-fareast-theme-font:=\"\"><a href=\"/notes/2368680\" target=\"_blank\">2368680</a></span></span></td>\n</tr>\n<tr>\n<td>0CM_SKU_ATTR</td>\n<td>LO-RFM-OBS and LO-MD</td>\n<td>Deprecated</td>\n<td>DS not working - alternative exists, 0RF_PRODUCT_ATTR</td>\n<td><span><span #0563c1;=\"\" ar-sa;\"=\"\" calibri',sans-serif;=\"\" calibri;=\"\" color:=\"\" de;=\"\" minor-latin;=\"\" mso-ansi-language:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" mso-fareast-theme-font:=\"\"><a href=\"/notes/2368680\" target=\"_blank\">2368680</a></span></span></td>\n</tr>\n<tr>\n<td>0CM_SKU_CDTH_HIER</td>\n<td>LO-RFM-OBS and LO-MD</td>\n<td>Deprecated</td>\n<td>DS not working - no alternative exists</td>\n<td><span><span #0563c1;=\"\" ar-sa;\"=\"\" calibri',sans-serif;=\"\" calibri;=\"\" color:=\"\" de;=\"\" minor-latin;=\"\" mso-ansi-language:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" mso-fareast-theme-font:=\"\"><a href=\"/notes/2368680\" target=\"_blank\">2368680</a></span></span></td>\n</tr>\n<tr>\n<td>0CM_SKU_TEXT</td>\n<td>LO-RFM-OBS and LO-MD</td>\n<td>Deprecated</td>\n<td>DS not working - alternative exists, 0RF_PRODUCT_TEXT</td>\n<td><span><span #0563c1;=\"\" ar-sa;\"=\"\" calibri',sans-serif;=\"\" calibri;=\"\" color:=\"\" de;=\"\" minor-latin;=\"\" mso-ansi-language:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" mso-fareast-theme-font:=\"\"><a href=\"/notes/2368680\" target=\"_blank\">2368680</a></span></span></td>\n</tr>\n<tr>\n<td>0CM_STRAT_TEXT</td>\n<td>LO-RFM-OBS and LO-MD</td>\n<td>Deprecated</td>\n<td>DS not working - no alternative exists</td>\n<td><span><span #0563c1;=\"\" ar-sa;\"=\"\" calibri',sans-serif;=\"\" calibri;=\"\" color:=\"\" de;=\"\" minor-latin;=\"\" mso-ansi-language:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" mso-fareast-theme-font:=\"\"><a href=\"/notes/2368680\" target=\"_blank\">2368680</a></span></span></td>\n</tr>\n<tr>\n<td>2LIS_40_S278</td>\n<td>CA-OIW</td>\n<td>Not Whitelisted</td>\n<td>DS not working - alternative exists, 2LIS_03_BX</td>\n<td><span><span #0563c1;=\"\" ar-sa;\"=\"\" calibri',sans-serif;=\"\" calibri;=\"\" color:=\"\" de;=\"\" minor-latin;=\"\" mso-ansi-language:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" mso-fareast-theme-font:=\"\"><a href=\"/notes/2370131\" target=\"_blank\">2370131</a></span></span></td>\n</tr>\n<tr>\n<td>0RT_SEASON_TEXT</td>\n<td>CA-OIW</td>\n<td>Not Whitelisted</td>\n<td>DS not working - alternative exists, 0RF_SEASON_TEXT</td>\n<td><span><span #0563c1;=\"\" ar-sa;\"=\"\" calibri',sans-serif;=\"\" calibri;=\"\" color:=\"\" de;=\"\" minor-latin;=\"\" mso-ansi-language:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" mso-fareast-theme-font:=\"\"><a href=\"/notes/2481829\" target=\"_blank\">2481829</a></span></span></td>\n</tr>\n<tr>\n<td>0RF_OAPC_MPA</td>\n<td>LO-RFM-OBS and LO-MD-RA</td>\n<td>Not Whitelisted</td>\n<td>DS not working - alternative exists, 0RF_OAPCMPA_ATTR</td>\n<td><span><span #0563c1;=\"\" ar-sa;\"=\"\" calibri',sans-serif;=\"\" calibri;=\"\" color:=\"\" de;=\"\" minor-latin;=\"\" mso-ansi-language:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" mso-fareast-theme-font:=\"\"><a href=\"/notes/2368913\" target=\"_blank\">2368913</a></span></span></td>\n</tr>\n</tbody>\n</table></div>", "noteVersion": 4, "refer_note": [{"note": "2500202", "noteTitle": "2500202 - S4TWL - BW Extractors in SAP S/4HANA", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p dir=\"ltr\">Customers considering moving to SAP S/4HANA (on premise) seek information whether BW extractors (aka data sources) known form SAP ERP still work, to be able to evaluate the potential impact on the BW environment when moving to SAP S/4HANA. To meet this requirement, SAP reviewed the status and the attached list (MS Excel document) provides the information that we have per extractor. The results are valid from SAP S/4HANA 1709 (on premise) until further notice or otherwise and in most cases apply to SAP S/4HANA 1610 and SAP S/4HANA 1511 as well (see respective flags in attached MS Excel document).</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>BW, Extractors, Datasources, SAP S/4HANA</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p dir=\"ltr\">Parts of this information are already available in CSS and can be found through 2333141 - SAP S/4HANA 1610: Restriction Note. The status of the extractors is collected in one list (the attached MS Excel document), see the attached MS Powerpoint document for information how the list is typically used. Not all extractors that are technically available in SAP S/4HANA are covered, SAP is planning to provide clarifications for additional extractors and share new versions of the list via this note. Hence it is recommended to review this note for updates.</p>\n<p>The information in this file is dated as of 10.09.2018. Please acknowledge that S/4HANA functionality is set forth in the S/4HANA Feature Scope Description. All business functionality not set forth therein is not licensed for use by customer.</p>\n<p><em>Please note that extractors in the namespace 0BWTC*, 0TCT* and 8* are related to the \"Embedded BW\" that is offered as a technology component within the S/4HANA software stack. They are all not explicitly whitelisted in this SAP Note as they are not part of the delivered Business Content by the S/4HANA Lines of Business but can be used for extraction. </em><em> </em></p>\n<ul>\n<li><em>0BWTC* and 0TCT* are extractors providing technical statistical information from the Embedded BW such as query runtime statistics or data loading statistics. </em></li>\n<li><em>8* are <a href=\"https://help.sap.com/viewer/64e2cdef95134a2b8870ccfa29cbedc3/7.5.6/en-US/4c1a1b9054914c86e10000000a42189e.html\" target=\"_blank\">Export DataSources</a> for transferring business data from InfoProviders of the “Embedded BW” system to a target system</em></li>\n<ul>\n<li><em>For a SAP BW target systems this is achieved by the <a href=\"https://help.sap.com/viewer/ccc9cdbdc6cd4eceaf1e5485b1bf8f4b/7.5.6/en-US/4a1411c3174f0452e10000000a421937.html\" target=\"_blank\">SAP Source System</a> type </em></li>\n<li><em>For a data transfer via the <a href=\"https://help.sap.com/viewer/107a6e8a38b74ede94c833ca3b7b6f51/2.0.1/en-US/c6afacb707764885a6fb62f511c24f34.html\" target=\"_blank\">ODP Source System</a> type (only option in SAP BW/4HANA target system) these Export DataSources are obsolete and invisible. Instead, the ODP-BW context is used. For more information on Operational Data Provisioning see the <a href=\"https://blogs.sap.com/2017/07/20/operational-data-provisioning-odp-faq/\" target=\"_blank\">ODP FAQ document</a>.</em></li>\n</ul>\n</ul>\n<p><em> For more information and positioning on the \"Embedded BW\" see:</em> <a href=\"https://www.sap.com/documents/2015/06/3ccee34c-577c-0010-82c7-eda71af511fa.html\" target=\"_blank\"><em>https://www.sap.com/documents/2015/06/3ccee34c-577c-0010-82c7-eda71af511fa.html</em></a></p>\n<p> For more information on partner registered data sources and to find partner details from partner namespaces, please create a message on component XX-SER-DNSP.</p>\n<p><strong>This is a collective note, containing information of several industries and areas. In case issues arrise with extractors listed in the Excel, or missing extractors, please do raise an incident on the APPLICATION COMPONENT of the respective EXTRACTOR. This is the <span>only</span> way to ensure that your incident reaches the responsible desk in the shortest time. You can find this in column E, 'Appl.component' in the attached excel document. Do not use the component from this note. Thank you!</strong></p>\n<p> </p>\n<p> </p>\n<p> </p>\n<p> </p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Details can be found in the respective note per area:</p>\n<p> </p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"3\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td><strong>Item Area - Line of Business</strong></td>\n<td><strong>Note number for details</strong></td>\n</tr>\n<tr>\n<td>Asset Management</td>\n<td>\n<p><a href=\"/notes/2299213\" target=\"_blank\">2299213</a> - Restrictions for BW-Extractors in S/4HANA in the Enterprise Asset Management domain (EAM)</p>\n</td>\n</tr>\n<tr>\n<td>Business Process Management</td>\n<td>\n<p><a href=\"/notes/2796696\" target=\"_blank\">2796696</a> - Restrictions for BW extractors relevant for S/4HANA as part of SAP S/4HANA, on-premise edition 1709 and above</p>\n</td>\n</tr>\n<tr>\n<td>Customer Services</td>\n<td>\n<p><a href=\"/notes/2533548\" target=\"_blank\">2533548</a> - Restrictions for BW-Extractors in S/4HANA in the CS (Customer Service) area</p>\n</td>\n</tr>\n<tr>\n<td>Enterprise Portfolio and Project Management</td>\n<td>\n<p><a href=\"/notes/2496759\" target=\"_blank\">2496759</a> - Restrictions for BW extractors relevant for S/4HANA in the area of Enterprise Portfolio and Project Management</p>\n</td>\n</tr>\n<tr>\n<td>Financials</td>\n<td>\n<p><a href=\"/notes/2270133\" target=\"_blank\">2270133</a> - Restrictions for BW extractors relevant for S/4HANA Finance as part of SAP S/4HANA, on-premise edition 1511</p>\n</td>\n</tr>\n<tr>\n<td>Flexible Real Estate</td>\n<td>\n<p><a href=\"/notes/2270550\" target=\"_blank\">2270550</a> - S4TWL - Real Estate Classic</p>\n</td>\n</tr>\n<tr>\n<td>Global Trade</td>\n<td>\n<p><a href=\"/notes/2498786\" target=\"_blank\"> </a></p>\n</td>\n</tr>\n<tr>\n<td>Globalization Services Finance</td>\n<td><a href=\"/notes/2559556\" target=\"_blank\">2559556</a> - Restrictions for BW extractors in Financial Localizations relevant for S/4HANA Finance as part of SAP S/4HANA, on-premise edition 1511</td>\n</tr>\n<tr>\n<td>Human Resources</td>\n<td><a href=\"/notes/2559556\" target=\"_blank\"> </a></td>\n</tr>\n<tr>\n<td>Incident Management and Risk Assessment </td>\n<td> <a href=\"/notes/2267784\" target=\"_blank\">2267784</a> - S4TWL - Simplification in Incident Management and Risk Assessment</td>\n</tr>\n<tr>\n<td>Master Data</td>\n<td>\n<p><a href=\"/notes/2498786\" target=\"_blank\">2498786</a> - Data Sources supported by Central Master Data in S/4HANA</p>\n<p><a href=\"/notes/2576363\" target=\"_blank\" title=\"2576363  - Data Sources supported by Central Master Data in S/4HANA\">2576363</a> - Data Sources supported by Master Data Governance in S/4HANA</p>\n</td>\n</tr>\n<tr>\n<td>Procurement</td>\n<td>\n<p dir=\"ltr\"><a href=\"/notes/2504508\" target=\"_blank\">2504508</a> - Restrictions for BW Extractors relevant for S/4 HANA Procurement as part of SAP S/4HANA, on premise edition 1709 and above</p>\n</td>\n</tr>\n<tr>\n<td>Produce</td>\n<td>\n<p dir=\"ltr\"><a href=\"/notes/2499728\" target=\"_blank\">2499728</a> - Restrictions for BW extractors relevant for S/4HANA in the area of Production Planning and Detailed Scheduling <br/><a href=\"/notes/2499716\" target=\"_blank\">2499716</a> - Restrictions for BW extractors relevant for S/4HANA in the area of Production Planning and Control <br/><a href=\"/notes/2499589\" target=\"_blank\">2499589</a> - Restrictions for BW extractors relevant for S/4HANA in the area of Quality Management<br/><a href=\"/notes/2499310\" target=\"_blank\">2499310</a> - Restrictions for BW extractors relevant for S/4HANA in the area of Inventory Management</p>\n</td>\n</tr>\n<tr>\n<td>Sales and Distribution</td>\n<td><a href=\"/notes/2498211\" target=\"_blank\">2498211</a> - Restrictions for BW extractors relevant for S/4HANA Sales as part of SAP S/4HANA, on-premise edition 1709</td>\n</tr>\n<tr>\n<td>\n<p>Supply Chain – Extended Warehouse Management</p>\n</td>\n<td>\n<p><a href=\"/notes/2552797\" target=\"_blank\">2552797</a> List of BI Data Sources used in EWM<br/><a href=\"/notes/2382662\" target=\"_blank\">2382662</a> List of BI Data Sources from SCM Basis used in EWM Context</p>\n</td>\n</tr>\n<tr>\n<td>\n<p>Supply Chain – Transportation Management</p>\n</td>\n<td>\n<p>All Data Sources working and whitelisted, no separate note exists</p>\n</td>\n</tr>\n<tr>\n<td>\n<p>Master data governance for Finance</p>\n</td>\n<td>\n<p>All Data Sources working and whitelisted, no separate note exists</p>\n</td>\n</tr>\n</tbody>\n</table></div>\n<p> </p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"3\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td>\n<p><strong>Item Area - Industry</strong></p>\n</td>\n<td><strong>Note number for details</strong>                                                                                                                  </td>\n</tr>\n<tr>\n<td>DIMP Automotive</td>\n<td>\n<p><a href=\"https://launchpad.support.sap.com/\" target=\"_blank\"> </a></p>\n</td>\n</tr>\n<tr>\n<td>Defense and Security</td>\n<td>\n<p><a href=\"https://launchpad.support.sap.com/\" target=\"_blank\">2544193</a> - Restrictions for BW extractors relevant for S/4HANA in the area of Defense &amp; Security</p>\n<p><a href=\"/notes/2273294\" target=\"_blank\">2273294</a> - S4TWL - BI content, Datasources and Extractors for DFPS</p>\n</td>\n</tr>\n<tr>\n<td>Financial Services                                        </td>\n<td>\n<p><a href=\"/notes/2543469\" target=\"_blank\">2543469</a> - \"SAP for Banking\": SAP extractors in connection with \"SAP S/4HANA on-premise edition</p>\n<p><span><a href=\"https://i7p.wdf.sap.corp/sap(bD1lbiZjPTAwMQ==)/bc/bsp/sno/ui_entry/entry.htm?param=69765F6D6F64653D3030312669765F7361706E6F7465735F6E756D6265723D3235343631303226\" target=\"_blank\">2546102</a></span> - \"SAP for Insurance\": SAP extractors in connection with \"SAP S/4HANA on-premise edition“</p>\n</td>\n</tr>\n<tr>\n<td>Higher Education</td>\n<td>\n<p><a href=\"/notes/2543469\" target=\"_blank\"> </a></p>\n</td>\n</tr>\n<tr>\n<td>IS Healthcare</td>\n<td>-</td>\n</tr>\n<tr>\n<td>IS Utilities</td>\n<td>\n<p><a href=\"/notes/2270505\" target=\"_blank\">2270505</a> - S4TWL - IS-U Stock, Transaction and Master Data Statistics (UIS, BW)</p>\n</td>\n</tr>\n<tr>\n<td>Oil and Gas</td>\n<td>\n<p> </p>\n</td>\n</tr>\n<tr>\n<td>Public Sector Collection and Disbursement (PSCD)</td>\n<td>\n<p>All Data Sources working and whitelisted, no separate note exists</p>\n</td>\n</tr>\n<tr>\n<td>Public Sector Management (PSM)</td>\n<td><a href=\"/notes/2556359\" target=\"_blank\">2556359</a> - Restrictions for BW extractors relevant for S/4HANA in the area of Public Sector Management</td>\n</tr>\n<tr>\n<td>IS Retail</td>\n<td><a href=\"/notes/2543543\" target=\"_blank\">2543543</a> - Restrictions for BW extractors relevant for S/4HANA in the area of Retail</td>\n</tr>\n</tbody>\n</table></div>\n<p> </p>\n<p> The classification scheme is:</p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"4\" cellspacing=\"1\" class=\"table table-bordered table-striped col-resizeable\" dir=\"ltr\">\n<tbody>\n<tr>\n<td height=\"32\" valign=\"top\" width=\"27%\">\n<p dir=\"ltr\"><strong>Current status                             </strong></p>\n</td>\n<td height=\"32\" valign=\"top\" width=\"27%\">\n<p dir=\"ltr\"><strong>Status for Publication</strong></p>\n</td>\n<td height=\"32\" valign=\"top\" width=\"45%\">\n<p dir=\"ltr\"><strong>Description                                                             </strong></p>\n</td>\n</tr>\n<tr>\n<td height=\"17\" rowspan=\"2\" valign=\"top\" width=\"27%\">\n<p dir=\"ltr\">Whitelisted</p>\n</td>\n<td height=\"17\" valign=\"top\" width=\"27%\">\n<p dir=\"ltr\">DS working - no restrictions</p>\n</td>\n<td height=\"17\" valign=\"top\" width=\"45%\">\n<p dir=\"ltr\">DataSource is available with S/4 and works without any restrictions compared to ERP</p>\n</td>\n</tr>\n<tr>\n<td height=\"17\" valign=\"top\" width=\"27%\">\n<p dir=\"ltr\">DS working – regeneration of extractor and check of BW content based on this DS is needed                                                                                          .</p>\n</td>\n<td height=\"17\" valign=\"top\" width=\"45%\">\n<p dir=\"ltr\">DataSource is working, because of data model changes, it is recommended to check the upward dataflow.</p>\n</td>\n</tr>\n<tr>\n<td height=\"17\" rowspan=\"3\" valign=\"top\" width=\"27%\">\n<p dir=\"ltr\">Not Whitelisted</p>\n</td>\n<td height=\"17\" valign=\"top\" width=\"27%\">\n<p dir=\"ltr\">DS working - restrictions</p>\n</td>\n<td height=\"17\" valign=\"top\" width=\"45%\">\n<p dir=\"ltr\">DataSource is available with S/4 but works with noteworthy restrictions; e.g. not all fields are available</p>\n</td>\n</tr>\n<tr>\n<td height=\"17\" valign=\"top\" width=\"27%\">\n<p dir=\"ltr\">DS not working - alternative exists</p>\n</td>\n<td height=\"17\" valign=\"top\" width=\"45%\">\n<p dir=\"ltr\">DataSource is not working with S/4 but an alternative exists, such as a new extractor, CDS view</p>\n</td>\n</tr>\n<tr>\n<td height=\"17\" valign=\"top\" width=\"27%\">\n<p dir=\"ltr\">DS not working - alternative planned</p>\n</td>\n<td height=\"17\" valign=\"top\" width=\"45%\">\n<p dir=\"ltr\">DataSource is not working with S/4 but equivalent available on roadmap for future release</p>\n</td>\n</tr>\n<tr>\n<td height=\"17\" rowspan=\"2\" valign=\"top\" width=\"27%\">\n<p dir=\"ltr\">Deprecated</p>\n</td>\n<td height=\"17\" valign=\"top\" width=\"27%\">\n<p dir=\"ltr\">DS obsolete</p>\n</td>\n<td height=\"17\" valign=\"top\" width=\"45%\">\n<p dir=\"ltr\">DataSource is obsolete - legacy extractors</p>\n</td>\n</tr>\n<tr>\n<td height=\"18\" valign=\"top\" width=\"27%\">\n<p dir=\"ltr\">DS not working - no alternative exists</p>\n</td>\n<td height=\"18\" valign=\"top\" width=\"45%\">\n<p dir=\"ltr\">DataSource is not working with S/4 but no alternative exists</p>\n</td>\n</tr>\n<tr>\n<td height=\"18\" valign=\"top\" width=\"27%\">\n<p dir=\"ltr\">Generated Data Source</p>\n</td>\n<td height=\"18\" valign=\"top\" width=\"27%\">\n<p dir=\"ltr\">DS working - no restrictions</p>\n</td>\n<td height=\"18\" valign=\"top\" width=\"45%\">\n<p dir=\"ltr\">Because of the nature of the extractors, being generated in the system, we cannot whitelist those in general. Experience so far showed that they should be working without restrictions.</p>\n</td>\n</tr>\n<tr>\n<td height=\"18\" valign=\"top\" width=\"27%\">\n<p dir=\"ltr\">Not relevant for BW extraction</p>\n</td>\n<td height=\"18\" valign=\"top\" width=\"27%\">\n<p dir=\"ltr\">DS not relevant</p>\n</td>\n<td height=\"18\" valign=\"top\" width=\"45%\">\n<p dir=\"ltr\">Datasource is available in ROOSOURCE, however, not to be used for extraction by BW.</p>\n</td>\n</tr>\n</tbody>\n</table></div>", "noteVersion": 41}]}], "activities": [{"Activity": "Process Design / Blueprint", "Phase": "Before or during conversion project", "Condition": "Mandatory", "Additional_Information": "Bluprint for covering reporting requriements using CDS views or SAP BW for reporting requriements that were being fulfilled by RIS functionality."}, {"Activity": "Custom Code Adaption", "Phase": "During conversion project", "Condition": "Optional", "Additional_Information": "Custom Code adjustments will be requried in case ABAP objects of packets WRB or WRBA are used in custom code."}, {"Activity": "User Training", "Phase": "During conversion project", "Condition": "Mandatory", "Additional_Information": "Provide user training for the reports based on CDS views or SAP BW instead of RIS."}]}