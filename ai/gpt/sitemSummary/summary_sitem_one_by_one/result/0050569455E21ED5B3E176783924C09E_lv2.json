{"guid": "0050569455E21ED5B3E176783924C09E", "sitemId": "SI3_FIN_MISC_RE", "sitemTitle": "S4TWL - Real Estate Classic", "note": 2270550, "noteTitle": "2270550 - S4TWL - Real Estate Classic", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are doing a system conversion to SAP S/4HANA, on-premise edition. The following SAP S/4HANA Transition Worklist item is applicable in this case.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>RE, Classic, real estate management, REMICL, RE-FX Real Estate Management, RE Classic, RE-Classic</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong>Description</strong></p>\n<p>SAP Real Estate Management is the real estate solution from SAP. From SAP ECC up to and including SAP ERP 6.0, SAP has provided two versions of the real estate solution. The older version is \"Real Estate Classic (RE Classic)\" and the new version is \"Real Estate Flexible (RE-FX)\" (see SAP Note <a href=\"/notes/443311\" target=\"_blank\">443311</a>).</p>\n<p>The editions SAP S/4HANA on premise 1511 and SAP S/4HANA Finance 1503 (and all other OP versions) do not support Real Estate Classic (RE Classic). The business requirements can be covered by using Real Estate Flexible (RE-FX) functionality. As RE-FX is the more up-to-date solution and has a much broader functional scope (see SAP Note<a href=\"/notes/517673\" target=\"_blank\"> 517673</a>), only RE-FX is included as the RE component in the SAP S/4HANA on-premise edition.</p>\n<p><strong>Business Process related information</strong></p>\n<p><strong>Before</strong> the system conversion to SAP S/4HANA, on-premise edition 1511 (or SAP S/4HANA Finance edition 1503, or higher releases), customers who use RE Classic need to migrate RE Classic to RE-FX as part of a migration project. It is not possible to migrate after switching to SAP S/4HANA. Customers have to be aware, that it is not possible anymore to view and display classic RE data in S/4 HANA.</p>\n<p>Migrating data from Classic RE to RE-FX is not a simple technical conversion. Similar to a legacy data transfer, the migration must be planned as a project and executed. In this sense, the migration programs are the technical support for the data transfer project. A migration project can be more or less time consuming depending on how complex your data is, the capacity in which you work with customer developments and the extent to which you use the RE-FX functions.</p>\n<p><br/>For more information, see SAP Note <a href=\"/notes/828160\" target=\"_blank\">828160</a>.</p>", "noteVersion": 5, "refer_note": [{"note": "828160", "noteTitle": "828160 - Migration from Classic RE to RE-FX", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>Migration tools</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Classic RE, RE-FX, migration<br/>FAQ</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>Migration from Classic RE to RE-FX</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>As of Release SAP ERP 6.0 (previously SAP ERP 2005 / SAP ECC 6.00, Financials Extension, EA-FIN 600), you can migrate your data from Classic RE to RE-FX. SAP provides migration programs to help you convert Customizing and application data to the new tables in RE-FX, if a 1:1 conversion is possible. For more information, see the presentation attached to this SAP Note.<br/><br/>Migrating data from Classic RE to RE-FX is not a simple technical conversion. Similar to a legacy data transfer, the migration must be planned as a project and executed. In this sense, the migration programs are the technical support for the data transfer project. A migration project can be more or less time consuming depending on how complex your data is, the capacity in which you work with customer developments and the extent to which you use the RE-FX functions.<br/><br/>For this reason, you have to carefully plan the migration from Classic RE to RE-FX, particularly with regard to the following points:</p>\n<ul>\n<li>Familiarizing yourself with the structures and concepts of the new application RE-FX</li>\n</ul>\n<ul>\n<ul>\n<li>relevant training of the project team and (later) the user, and</li>\n</ul>\n</ul>\n<ul>\n<ul>\n<li>testing the RE-FX functions</li>\n</ul>\n</ul>\n<ul>\n<ul>\n<li>comparing the processes used currently by the customer in Classic RE with the processes in RE-FX</li>\n</ul>\n</ul>\n<ul>\n<li>Changing the correspondence letters. Note that you can no longer print SAP script forms from RE-FX. This means that you are required to convert all RE forms and RE-specific dunning forms into PDF-based forms or SAP Smart Forms.</li>\n</ul>\n<ul>\n<li>Changing customer developments and modifications so that the functions covered by this are also available in the migrated system</li>\n</ul>\n<ul>\n<li>Note that the migration programs do not currently convert all data. Some functions are not included in the standard migration; these include:</li>\n</ul>\n<ul>\n<ul>\n<li>Land use management add-on (LUM), see SAP Note 977295 for more information </li>\n</ul>\n</ul>\n<ul>\n<ul>\n<li>Condominium owner administration add-on (WEG)</li>\n</ul>\n</ul>\n<ul>\n<ul>\n<li>Corporate Real Estate Management add-on (CRE)</li>\n</ul>\n</ul>\n<ul>\n<ul>\n<li>Quarter days add-on</li>\n</ul>\n</ul>\n<ul>\n<ul>\n<li>Input tax correction data (correction items, correction object), see SAP Note 1043260 for more information.</li>\n</ul>\n</ul>\n<p>You must read the attached PDF document. This document describes how you should proceed and the errors that can occur. Print out this file and read it before you start the migration.<br/><br/>Before you make the first test migration:</p>\n<ul>\n<li>Import the most up-to-date Support Package status (at least ECC 6.00 Support Package 10) and the SAP Notes based on this status about component RE-FX-MI.</li>\n</ul>\n<p><br/>To test the actual conversion:</p>\n<ul>\n<li>Call transaction REMICL. In the background, you can use transaction REMICLBATCH instead. The steps that require manual actions (for example, settings in the IMG) must always be carried out online.</li>\n</ul>\n<ul>\n<li>Call the individual migration points step by step. Read the information. These explain how each step works. Check the log after every step.</li>\n</ul>\n<ul>\n<li>If errors occur during migration and if it is not immediately clear to you how you should resolve them, search the attached documentation for the error number. However, note that the documentation does not describe every possible error.<br/>If, after having read the documentation and the error message long text, you are still unable to resolve the error, contact SAP Support.</li>\n</ul>\n<p><strong>Experiences and recommendations from previous migration projects</strong></p>\n<ul>\n<li>User exits in the migration: see SAP Note 1079141</li>\n</ul>\n<ul>\n<li>Business partners, and addresses:</li>\n</ul>\n<ul>\n<ul>\n<li>The FS04 pushbutton in the BPCONSTANTS table must be set to 1: see also SAP Note 1065388.</li>\n</ul>\n</ul>\n<ul>\n<ul>\n<li>To avoid problems during the address transfer, deactivate the address check: see SAP Note 1032896.</li>\n</ul>\n</ul>\n<ul>\n<ul>\n<li>You are using the General Real Estate Contract, and have assigned a customer or a vendor in the conditions. In this case, use a customer-specific program to ensure that the TR business partner can be created for these accounts before the migration. See also SAP Note 1090827.</li>\n</ul>\n</ul>\n<ul>\n<ul>\n<li>You have created customer and vendor accounts for business partners. These both have bank details with the same ID (for example 0001), but have different account data. In this case, use a customer-specific program before the migration to ensure that different bank details for the same business partner also have different account IDs. You can use a naming convention to help, for example for the account ID 0001 of the vendor account, you can create the account ID K001 in the business partner. During the migration, ensure that the account ID 0001 is replaced with K001 for vendor conditions. You can do this by using the user exit in SAP Note 1079141.</li>\n</ul>\n</ul>\n<ul>\n<li>Accrual/deferral: See SAP Note 980104.</li>\n</ul>\n<ul>\n<li>Service charge settlement: Advance payments posted for non-billed periods are transferred to the \"Advance payments legacy data\" table. This data can only be transferred if the relevant contracts have a corresponding settlement participation, that is, the billing structure must be fully created before the migration so that advance payments are recognized correctly.</li>\n</ul>\n<ul>\n<li>In some cases, inconsistenices can occur in the occupancy history. See SAP Note 1111447 with regards to this.</li>\n</ul>\n<ul>\n<li>To migrate conditions that do not occur in the contract validity period, see SAP Note 997076.</li>\n</ul>\n<ul>\n<li>To improve performance.</li>\n</ul>\n<ul>\n<ul>\n<li>During the transfer, you must keep the database indexes of RE tables up to date. </li>\n</ul>\n</ul>\n<ul>\n<ul>\n<li>CO settlement rules: See SAP Note 946523.</li>\n</ul>\n</ul>\n<p> </p>\n<p><strong>SAP S/4 HANA release upgrade</strong></p>\n<p>Classic RE cannot be used under SAP S/4HANA and is not released. Existing Classic RE customers must migrate to RE-FX before upgrading to the solution \"SAP S/4HANA, on-premise edition\". Note that the display and call of Classic RE data in SAP S/4 HANA are no longer possible.</p>\n<p> </p>\n<p> </p></div>", "noteVersion": 24, "refer_note": [{"note": "865444", "noteTitle": "865444 - Migration from Classic RE", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>You want to carry out an automatic migration from Classic RE to RE-FX.  To make sure that you are working with the latest version of the migration programs, proceed as described below.<br/><br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>REMICL<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>Import the most recent Support Package level (at least Support Package 10) and implement the notes based on this for component RE-FX-MI.<br/><br/>Prior to migration, make sure that you have implemented all notes that are assigned to this note as \"related notes\". These notes contain corrections to the RE-FX source code that must be implemented so the RE processes work correctly after the migration.<br/><br/>Detailed documentation for the individual steps and errors that may occur is attached to Note 828160. In addition, the individual steps of the migration transaction are documented in the system (in the long text for the step or in the IMG documentation).  After you import the current corrections or implement the notes, call transaction REMICL and call the documentation for the individual steps to read this documentation. The settings to be made are also documented in the relevant settings guide. You can access it in those steps that have an \"Execute\" icon in the IMG column by clicking this icon.<br/><br/></p></div>", "noteVersion": 13}, {"note": "1410237", "noteTitle": "1410237 - Central note for REAL ESTATE (CLASSIC & FX) Swiss spec.", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>For RE Classic, different localizations for Switzerland are available in the customer namespace. In the standard programs of REMICL for the migration of Classic RE to RE-FX, these Swiss localizations are not taken into account. The following documentation displays which methods are available in which areas, and where customer-specific adjustments have to be made.<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>Swiss localization<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>Subject areas<br/>Fuel consumption calculation:<br/>- Fuel levels<br/>- VAT adjustment<br/>Individual heating costs:<br/>- Master data migration (heating system to master settlement unit<br/>- Reconciliation of data medium with settlement companies<br/>Rent adjustment<br/>- Calculation bases for rent adjustment according to Swiss law<br/>Lease-in with ISR<br/>- Summarization of items for each real estate contract<br/>Migration support is missing within REMICL<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>Fuel consumption:<br/>In the fuel consumption calculation for RE Classic, initial fuel levels and ending levels are managed and valuated according to FiFo. Two different tables are available for this in RE Classic, one for managing the warehouse stock and one for managing the related tax information.<br/>The tax information corresponds to the dominant occupancy in the relevant rental objects (option rate) at the time of the input tax reduction. In addition, the stock concerns a composite rate that is difficult to reproduce and requires a large amount of historical data. The value-added tax settlement with the tax authorities created at that time must not be affected either.<br/>The initial fuel level and any existing additional fuel purchases can be transferred from RE Classic to the new environment of RE-FX with the related tax information. After this one-time action, it must be possible to process all further fuel additions and removals in relation to value-added tax in the standard system.<br/>It must be taken into account that the value-added tax to be transferred has already been settled with the tax authorities and this one-time transfer does not negatively affect the following heating expenses settlements and service charge settlements.<br/>Fuel level: Note 1392298 Swiss specifications:   Migration of fuel level<br/>Value-added tax for fuel level: According to customer tests, no note was required for this subject,<br/><br/>Individual heating expenses settlement<br/>The individual heating expenses settlement for Switzerland was implemented in RE Classic with an enhancement of the standard system in the customer namespace. The master data required for this was added to the heating system. In the localization of individual heating costs for Switzerland, the master settlement unit was used instead of the heating system as the central master data element in the standard system of RE-FX. The migration programs of REMICL no longer convert the data of the heating system into the master data of the master settlement units. For the conversion according to Swiss law, no support is available in REMICL. This must be implemented in the customer project.<br/>The data medium with the settlement companies was reconciled with the following settlement companies in Switzerland and was tested with two RE customers in Switzerland:<br/>RAPP, Wärmetechnik AG, Basel<br/>ista swiss ag, Zofingen<br/>neoVac AG, Oberriet<br/>Techem Schweiz, Urdorf<br/><br/>Rent adjustment:<br/>The rent adjustment data according to Swiss law for the master data of rental units and lease-outs is not transferred to RE-FX by the migration with REMICL.<br/>You can use the transactions listed below to subsequently perform the migration of calculation bases after the successful execution of REMICL:<br/>1. Transaction REXCAJMIROCH01: RO Migration: Adj. Data - Swiss Law, Note 1359007<br/>2. Transaction REXCAJMICNCH01: LO Migration: Adj. Data - Swiss Law, Note 1398898<br/><br/>Lease-in with ISR reference (Note 920728)<br/>In the Swiss localization, it is possible to enter the ISR reference of the landlord's invoice for the lease-in for the relevant items in the cash flow.<br/>For this reason, you want invoiced amounts with the same ISR reference to be grouped in one payment for Real Estate management, so that there are separate payments for different ISR numbers only. You want items that were posted under the same real estate contract to be grouped like this. This summarization of items within the lease-in with ISR reference was implemented with Note 920728. As a result, the landlord receives only one amount for each contract and ISR reference due to the periodic posting of the real estate contract.<br/><br/><br/><br/></p></div>", "noteVersion": 1}, {"note": "1449490", "noteTitle": "1449490 - Table VICAINTRENO is not filled after migration", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>The table VICAINTRENO is empty for some objects after the migration. This note provides a program that adds the missing entries.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>Implement the attached corrections.</p></div>", "noteVersion": 2}, {"note": "1407804", "noteTitle": "1407804 - Migration: Exit for calculation rule", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>Due to a different determination of the cash flow, rounding differences may occur when comparing the calculated amounts in RE-FX and \"Classic RE\" if:</p> <ol>1. Monthly amounts are defined in the conditions,</ol> <ol>2. A posting cycle &lt;&gt; 1 is valid,</ol> <ol>3. A tax &lt;&gt; 0 is valid.</ol> <p><br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>REMICL</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>The cash flow determination in RE-FX is different to that in \"Classic RE\".<br/>You use flexible Real Estate Management as of Release 600.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>In accordance with Note <b>1079141</b>, create a class according to the rules in the customer namespace. Redefine the method <b>CHANGE_CONDITION</b> and implement the source code defined in the correction instructions.<br/>After you execute the \"procedure\" described in Note <b>1079141</b> to activate the customer exits for migration (TIVMISET-IMPLCLNAME contains the class name), the system converts the monthly or annual amount to the frequency amount and adjusts the assigned frequency terms accordingly in the migration steps \"Convert Dataset - Lease-Out Conditions\" and \"Convert Dataset - Lease-In Conditions\" for conditions whose monthly frequency does not equal one.</p></div>", "noteVersion": 1}, {"note": "398888", "noteTitle": "398888 - BP_TR1: Composite note and FAQs about bus partner conversion", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>This note answers frequently asked questions and explains problems that occur when converting from TR BP to SAP BP. This mainly involves the business partner conversion and the parallel maintenance of SAP BP and TR BP.<br/><br/><br/>Our experience of business partner conversions with customers has shown that it is necessary to compile all existing information on this topic in one place.<br/><br/>For the business partner conversion and parallel maintenance of TR BP and SAP BP, it is a technically complex activity which requires sufficient preparation and extensive tests as preprocessing for going live. The work required to do this should not be underestimated. The release upgrade when converting to SAP BP is significantly more extensive than a \"normal\" release upgrade.<br/><br/>In any case, before you start all the conversion activities, you should read the documentation for the business partner conversion in detail. It describes which steps must be performed and what happens in the system during each step.<br/><br/>You can find the documentation on the following path:<br/></p> <ol>1. In the SAP menu of your system, start the SAP Library (Menu path: \"Help --&gt; SAP Library\") or start the SAP Service Marketplace (SAP Help Portal http://help.sap.com/ under Documentation-&gt; MySAP ERP) and navigate to the SAP Library.</ol> <ol>2. Within the SAP Library, navigate as follows:</ol> <p>              SAP Library -&gt; SAP ERP Central Component-&gt; Accounting -&gt; SAP Banking -&gt; SAP Business Partner for Financial Services -&gt; Functions -&gt; Guidelines for Converting Business Partners. <p><br/>Make sure that you download the version for Release ERP 2005.  This is the current version that also describes the procedure for the earlier releases.<br/><br/>This Note also lists all existing notes that deal with the conversion process.  You also have to take these into account before you start the conversion. We recommend that the conversion is carried out by a very technically experienced colleague or consultant.<br/><br/>The summary of the notes is followed by frequently asked questions and answers to these questions. Read these questions and answers before you open a customer message on this topic.<br/><br/>Note that we constantly update this Note so as to include the most up-to-date information.<br/><br/>If you are installing Release ERP 2004, you have to take further points into account in addition to the information contained in this Note. See Note 687037 for this purpose.<br/><br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>Business partners, conversion, parallel maintenance, parallel supply, conversion reports, SAP BP, TR BP, business partner conversion, notes<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>See the explanations given in the 'Solution' section.<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>Below you can find a compilation of frequently asked questions and the answers to these questions. Note point 5 in particular (Conversion without Customer Link). The information about this topic is not available in the attached documentation.<br/><br/>In the next section, there is a list of all notes that were created up to now in relation to the business partner conversion and the parallel maintenance between Treasury business partners and SAP business partners. Some notes contain program corrections that you can implement using the Note Assistant. However, you sometimes also have to implement Customizing corrections manually or perform other manual tasks in your system. Some notes were created as consulting Notes to explain certain complex facts.<br/><br/>The general consulting notes apply to all releases. The Support Package in which the note is contained is specified in brackets for the other notes (Customizing and source code corrections). Therefore, all Notes with a Support Package status that is higher than the current Support Package in your system are relevant for you.<br/><br/>The informatory lines have the following structure:<br/>Note Short text (Support Package/ M or C)<br/>An \"M\" in brackets stands for \"manual activities\", a \"C\" for \"correction.\" As far as notes marked with \"M\" are concerned, you must read the text of the note in detail as otherwise syntax errors or errors can occur due to incomplete Customizing.<br/><br/>Make sure you implement all notes that apply to your release before you start the conversion.<br/><br/>The notes are listed below according to release. The following releases are distinguished:<br/>Banking 4.63/CFM 2.0 (BANK/CFM 463_20)<br/>R/3 Enterprise 4.70 Financial Services 1.10 (EA_FINSERV 110)<br/>R/3 Enterprise 4.70 Financial Services 2.0 (EA_FINSERV 20)<br/>R/3 Enterprise Core 4.70 (SAP_APPL 470)<br/>R/3 Enterprise 5.00 Financial Services 5.0 (EA_FINSERV 50)<br/>R/3 Enterprise Core 5.00 (SAP_APPL 500)<br/>R/3 Enterprise 6.00 Financial Services 6.0 (EA_FINSERV 600)<br/>R/3 Enterprise Core 6.00 (SAP_APPL 600)<br/>SAP ABA 6.20<br/>SAP ABA 6.40<br/>Note that you must also implement the Notes for SAP_APPL 4.70 and SAP ABA 6.20 if you are using EA_FINSERV, since the corresponding software layers form the basis of the EA_FINSERV Releases 110 and 20.<br/></p> <b>I. General consulting notes and valid notes</b><br/> <p>Note  Short text:<br/>721787 BP_TR1: Incorrect conversion of the relat. cats 0021, 0022, 0100<br/>721365 BP_ADU: Dialog box \"Address data incomplete\" for SAP BP<br/>687037 ERP 2004: Bus. partner parallel maint. no longer possible<br/>639873 BP_TR1: BP Conversion of industries for R/3 Enterprise 4.70<br/>590148 BP_TR2: Short dump at RFTBUH02_2<br/>589389 Parallel usage RE - TR-TM - IS-B - CFM<br/>588112 BP_TR2: Report RFSADT0010 - conversion of object addresses<br/></p> <b>II. Notes for Release Bank/CFM 463_20 (as of Support Package 14)</b><br/> <p>Note  short text (Support Package)<br/><br/>724144 BP_TR1: Standard indicator not set in table BUT020</p> <ul><ul><li>Note 721609 contains the corrections for this.</li></ul></ul> <p><br/>1068797 BP_TR1 - RFTBUP01 parameter 'Setup SAP business partner'<br/>1063936 BP_TR1: Error when you display relationship details (33/C/M)<br/>1000146 BP_TR1: Empty log entries for RFTBP030 report (32/C)<br/>998597 BP_TR1: Poor performance of report RFTBP030 (32/C)<br/>967302 BP_TR2: Real estate object RFTBUP10_VDGPO not converted (31/C)<br/>965653 BP_TR1 RFTBUP01 Occupation not migrated (31/C)<br/>962443 BP_TR1 BP_TR2 User name not output for conversion (31/C)<br/>948136 BP_TR2: RFTBUP01_BP000 issues message FTBU615 w/o BP number (31/C)<br/>946543 BP_TR2: Partner in table VZGPO not converted (31/M)<br/>938500 BP_TR2: Request is not a Workbench or Customizing request (30/C)<br/>897323 BP_TR1: Incorrect conversion of the \"VIP\" indicator (30/C)<br/>887301 BP_TR2: Transport order number is not displayed (28/C)<br/>884242 BP_TR2: Performance of RFTBUP10_BP000  (28/C)<br/>882599 BP_TR2: Incomplete conversion of table BP000 (28/C)<br/>877345 BP_TR2: Require an order number for order/task field (28/C)<br/>867821 BP_TR1: Vendor assignment in table BC001 (28/C)<br/>861360 BP_TR2: Consistency check in RFTBUP10_BP000 (28/C)<br/>858123 BP_TR2: Table BP000 is deleted by RFTBUP10_BP000 (28/C)<br/>836448 BP_TR2: BP conversion RFTBUP10-TZKN2 termination w/ FTBU582 (27/C)<br/>834661 BP_TR2: Order/task field not ready for input (27/C)<br/>832236 BP_TR2: Runtime error DYNPRO_MSG_IN_HELP (27/C)<br/>822644 BP_TR1: RFTBUP02 ends with the status 'not released' (M)<br/>817729 BP_TR1: RFTBUP02 performance (27/C)<br/>810702 BP_TR1: Termination RFTBUP01 with message AM850 (27/C)<br/>785156 BP_TR2: Update of the business partner conversion 2004 (27/C/M)<br/>786660 BP_TR2: RFTBUP11 Runtime error LOAD_PROGRAM_NOT_FOUND (27/C)<br/>785156 BP_TR2: Update of business partner conversion 2004 (27/C)<br/>762061 BP_TR2: Deactivate consistency check for table BP000 (26/C/M)<br/>736512 BP_TR2: Update of the business partner conversion 2004 (26/C/M)<br/>721609 BP_DIA: Correspondence language w. SAP business partner (26/C)<br/>711251 BP_TR2: COMPUTE_BCD_OVERFLOW during BP conversion (25/C/M)<br/>709880 BP_TR1: Business partner conversion phase I: Miscellaneous (25/C)<br/>707432 BP_TR1: Various errors in the BP conversion phase I (25/C)<br/>703279 BP_PAR: Archiving flag is not transferred to TR BP (25/C)<br/>700960 BP_TR1: Incomplete link between SAP-BP and TR-BP (25/C)<br/>693547 BP_TR1: Correspondence language for persons is missing (25/C)<br/>690288 BP_TR1: Commercial register entry is not transferred (24/C)<br/>689957 BP_TR2: Correction report adjustment for table ADRC (24/C)<br/>689556 BP_PAR: Dialog box for address data when saving a BP (24/C)<br/>685440 BP_TR2: Converting SAP standard data elements (24/M)<br/>679639 BP_TR2: Language missing in organizations after conversion (24/M/C)<br/>670716 BP_TR2: SAP BP: Entry for BP000 missing in BPUM in BP conversion  (26/C)<br/>666488 BP_TR2: Status of report RFTBUP10_TPZ6T is not changed (24/M)<br/>656962 PAR: BUT000 Append for conversn/parallel maintnce empty (22/C)<br/>656108 BP_TR1: Language missing in organizations after the conversion (22/C)<br/>652210 BP_TR2: System does not display all conversion logs (22/C/M)<br/>649333 BP_PAR: Error in parallel maintenance of 'Date Founded' (22/C)<br/>645872 BP_TR1: Conversion of form of addr: Corr report for tab. ADRC (21/C/M)<br/>643800 BP_TR1: Field TITLE in ADRC not supplied with data (21/C)<br/>624883 BP_TR2: No update to BPUM with report RFTBUP10_TPZ6 (20/C)<br/>622812 BP_TR1: RFTBUP01 BP creation no GUID generated (20/C)<br/>622104 BP_PAR: Parallel maintenance: Transfer name entries for group (20/C)<br/>621959 BP_PAR: Parallel update of address usages: incorrect transfer (20/C)<br/>617414 BP_ADU: Adjustments in report RFTBP030 (20/C/M)<br/>615964 BP_TR2: Sel. texts for conversion reports not multilingual (20/C/M)<br/>615424 BP_TR1: Relationship catgy not transfrd to table TB095 (20/C)<br/>608350 BP_TR1: UM: academic title not transferred to SAP BP during conversion (19/C/M)<br/>606360 BP_TR2: RFTBUP10 reports with status 'no values' (19/C)<br/>600222 BP_PAR:  Phonetic search term not transferred to TR (18/C)<br/>598177 BP_PAR: Wrong transfer of pers. number (possible inconsist.) (18/C/M)<br/>592616 BP_PAR: Fields from table BP001 not transferred in TR BP (18/C/M)<br/>592170 BP_PAR:  Phonetic search term not transferred to TR (18/C)<br/>591692 BP_TR2: RFSADT0010 Termination because of incorrect table procg (17/C)<br/>591007 BP_TR2: Composite SAP Note various error correctns (17/C/M)<br/>590392 BP_TR1: Incorrect report RFTBP030 (convert BP addresses) (18/C/M)<br/>590148 BP_TR2: Short dump at RFTBUH02_2<br/>590017 BP_TR1: Double conversion of relationship 0060 in BUR006 (19/M/C)<br/>587932 BP_TR1: RTFBUP02, Release 0030 Branch/Head office (17/M)<br/>575126 BP_PAR: Parallel update: Error deleting the postal code (15/C)<br/>568460 BP_PAR: PO box not deleted CBP (org.) in parallel maintenance (15/C)<br/>565717 BP_PAR: Error in parallel maintenance of legitimation data (15/C)<br/>563482 BP_TR1: Employer converted incorrectly (15/C)<br/></p> <b>III. Notes for Release R/3 Enterprise 1.10 (as of Support Package 04/05)</b><br/> <p>Note  short text (Support Package)<br/><br/>724144 BP_TR1: Standard indicator not set in table BUT020</p> <ul><ul><li>Note 721609 contains the corrections for this.</li></ul></ul> <p>1238632 BP_TR2 report parameter for RFTBUH02_2 (M)<br/>1113553 BP_TR1: Report RFTBUP08 incorrect selection (26/C)<br/>1068797 BP_TR1 - RFTBUP01 parameter 'Setup SAP business partner' (25/C/M)<br/>1065388 BP_TR2: Time-dependency of addresses and BP conversion<br/>1065386 BP_TR2: BPUM Customizing for BP030, BP1000 is deleted (25/M)<br/>1063936 BP_TR1: Error when you display relationship details (25/C/M)<br/>1045148 BP_TR2: Runtime error in program RFTBUP11 (25/M)<br/>1000146 BP_TR1: Empty log entries for RFTBP030 report (24/C)<br/>998597 BP_TR1: Poor performance of report RFTBP030 (24/C)<br/>996892 BP_TR2: Runtime error in program RFTBUP10_CML_ARC_DOC_REC (24/M)<br/>994629 BP_TR1 RFTBUP03 does not assign addresses correctly (24/M)<br/>974521 BP_TR2 Separator for download in Excel (23/C)<br/>967302 BP_TR2: Real estate object RFTBUP10_VDGPO not converted (23/C)<br/>965653 BP_TR1 RFTBUP01 Occupation not migrated (23/C)<br/>962443 BP_TR1 BP_TR2 User name not output for conversion (23/C)<br/>948136 BP_TR2: RFTBUP01_BP000 issues message FTBU615 w/o BP number (23/C)<br/>946543 BP_TR2: Partner in table VZGPO not converted (23/M)<br/>938500 BP_TR2: Request is not a Workbench or Customizing request (23/C)<br/>897417 BP_TR2: Correction delivery Customizing BP conversion (22/M)<br/>897323 BP_TR1: Incorrect conversion of the \"VIP\" indicator (22/C)<br/>887301 BP_TR2: Transport order number is not displayed (22/C)<br/>884242 BP_TR2: Performance of RFTBUP10_BP000  (21/C)<br/>882599 BP_TR2: Incomplete conversion of table BP000 (21/C)<br/>877345 BP_TR2: Require an order number for order/task field (21/C)<br/>867821 BP_TR1: Vendor assignment in table BC001 (21/C)<br/>861360 BP_TR2: Consistency check in RFTBUP10_BP000 (21/C)<br/>858123 BP_TR2: Table BP000 is deleted by RFTBUP10_BP000 (21/C)<br/>856458 BP_TR2: Logs of BP conversion cannot be assigned to client (21/C)<br/>852211 BP_TR2: COMPUTE_BCD_OVERFLOW with step EXEC_01 (21/C)<br/>848218 BP_TR1: Struc of Cstmizing tbls TSAD2, TSAD4, TSAD5, TSAD3 (21/C/M)<br/>836448 BP_TR2: BP conversion RFTBUP10-TZKN2 termination w/ FTBU582 (21/C)<br/>834661 BP_TR2: Order/task field not ready for input (20/C)<br/>832236 BP_TR2: Runtime error DYNPRO_MSG_IN_HELP (20/C)<br/>822644 BP_TR1: RFTBUP02 ends with the status 'not released' (M)<br/>817729 BP_TR1: RFTBUP02 performance (20/C)<br/>810702 BP_TR1: Termination RFTBUP01 with message AM850 (20/C)<br/>799078 BP_TR1: RFTBUP01 Bad Performance w/ BUPGUIDCREATE Step (20/C)<br/>785156 BP_TR2: Update of the business partner conversion 2004 (20/C/M)<br/>786660 BP_TR2: RFTBUP11 Runtime error LOAD_PROGRAM_NOT_FOUND (20/C)<br/>785156 BP_TR2: Update of business partner conversion 2004 (20/C)<br/>762061 BP_TR2: Deactivate consistency check for table BP000 (19/C/M)<br/>740464 BP_TR1: Delivery Customizing subproject/control (19/M)<br/>736512 BP_TR2: Update of the business partner conversion 2004 (19.20/C/M)<br/>721609 BP_DIA: Correspondence language w. SAP business partner (18/C)<br/>709880 BP_TR1: Business partner conversion phase I: Miscellaneous (17/C)<br/>707432 BP_TR1: Various errors in the BP conversion phase I (17/C)<br/>703279 BP_PAR: Archiving flag is not transferred to TR BP (17/C)<br/>700960 BP_TR1: Incomplete link between SAP-BP and TR-BP (17/C)<br/>696766 BP_PAR: Dialog box for address data during BP saving (17/C)<br/>693547 BP_TR1: Correspondence language for persons is missing (17/C)<br/>690288 BP_TR1: Commercial register entry is not transferred (17/C)<br/>689957 BP_TR2: Correction report adjustment for table ADRC (17/C)<br/>689556 BP_PAR: Dialog box for address data when saving a BP (17/C)<br/>685440 BP_TR2: Converting SAP standard data elements (17/M)<br/>684664 BP_TR1: Error in parallel maintenance and address conversion (17/C)<br/>679639 BP_TR2: Language missing in organizations after conversion (24/M/C)<br/>670716 BP_TR2: SAP BP: Entry for BP000 missing in BPUM in BP conversion (18/C)<br/>661078 BP_CVI: Adjustment of function module FLBPC_LOVN (16/C)<br/>656962 PAR: BUT000 Append for conversn/parallel maintnce empty (15/C)<br/>656108 BP_TR1: Language missing in organizations after the conversion (15/C)<br/>652210 BP_TR2: System does not display all conversion logs (15/C/M)<br/>649333 BP_PAR: Error in parallel maintenance of 'Date Founded' (14/C)<br/>645872 BP_TR1: Conversion of form of addr: Corr report for tab. ADRC (14/C/M)<br/>643800 BP_TR1: Field TITLE in ADRC not supplied with data (14/C)<br/>624883 BP_TR2: No update to BPUM with report RFTBUP10_TPZ6 (12/C)<br/>622812 BP_TR1: RFTBUP01 BP creation no GUID generated (11/C)<br/>622104 BP_PAR: Parallel maintenance: Transfer name entries for group (11/C)<br/>617414 BP_ADU: Adjustments in report RFTBP030 (11/C/M)<br/>615964 BP_TR2: Sel. texts for conversion reports not multilingual (11/C/M)<br/>615424 BP_TR1: Relationship catgy not transfrd to table TB095 (10/11/C)<br/>614916 BP_TR2: Runtime error DATA_UC_STRUCT_C_LENGTH (10/C)<br/>608350 BP_TR1: UM: academic title not transferred to SAP BP during conversion (10/C/M)<br/>606360 BP_TR2: RFTBUP10 reports with status 'no values' (09/C)<br/>600222 BP_PAR:  Phonetic search term not transferred to TR BP (09/C)<br/>598177 BP_PAR: Wrong transfer of pers. number (possible inconsist.) (08/C/M)<br/>592616 BP_PAR: Fields from table BP001 not transferred in TR BP (08/C/M)<br/>592170 BP_PAR:  Phonetic search term not transferred to TR (08/C)<br/>591692 BP_TR2: RFSADT0010 Termination because of incorrect table procg (17/C) (08/C)<br/>591007 BP_TR2: Composite SAP Note various error corrections (08/C/M)<br/>590392 BP_TR1: Incorrect report RFTBP030 (convert BP addresses) (08/10/C/M)<br/>590017 BP_TR1: Double conversion of relationship 0060 in BUR006 (10/M/C)<br/>589297 BP_TR2: Short dump in program RFTBUP10_AT16 (07/C)<br/>587932 BP_TR1: RFTBUP02, Release 0030 Branch/Head office (07/M)<br/>575126 BP_PAR: Error deleting the postal code (05/C)<br/>568875 BP_TR1: Local court field occupied incorrectly (15/C/M)<br/>568875 BP_TR1: Local court field occupied incorrectly (05/C/M)<br/>568460 BP_PAR: PO box not deleted in CBP (org.) in parallel maintenance (05/C)<br/>565717 BP_PAR: Error in parallel maintenance of legitimation data (04/C)<br/>563482 BP_TR1: Employer converted incorrectly (05/C)<br/>563420 BP_PAR: Incorrect date and time in BUT000 (06/C)<br/></p> <b>IV. Notes for Release R/3 Enterprise 2.0 (as of Support Package 01)</b><br/> <p>Note  short text (Support Package)<br/><br/>724144 BP_TR1: Standard indicator not set in table BUT020</p> <ul><ul><li>Note 721609 contains the corrections for this.</li></ul></ul> <p>1238632 BP_TR2 report parameter for RFTBUH02_2 (M)<br/>1113553 BP_TR1: Report RFTBUP08 incorrect selection (15/C)<br/>1068797 BP_TR1 - RFTBUP01 parameter 'Setup SAP business partner' (14/C/M)<br/>1065388 BP_TR2: Time-dependency of addresses and BP conversion<br/>1065386 BP_TR2: BPUM Customizing for BP030, BP1000 is deleted (14/M)<br/>1063936 BP_TR1: Error when you display relationship details (14/C/M)<br/>1045148 BP_TR2: Runtime error in program RFTBUP11 (14/M)<br/>1000146 BP_TR1: Empty log entries for RFTBP030 report (13/C)<br/>998597 BP_TR1: Poor performance of report RFTBP030 (13/C)<br/>996892 BP_TR2: Runtime error in program RFTBUP10_CML_ARC_DOC_REC (13/M)<br/>994629 BP_TR1 RFTBUP03 does not assign addresses correctly (13/M)<br/>974521 BP_TR2 Separator for download in Excel (12/C)<br/>967302 BP_TR2: Real estate object RFTBUP10_VDGPO not converted (12/C)<br/>965653 BP_TR1 RFTBUP01 Occupation not migrated (12/C)<br/>962443 BP_TR1 BP_TR2 User name not output for conversion (12/C)<br/>948136 BP_TR2: RFTBUP01_BP000 issues message FTBU615 w/o BP number (12/C)<br/>946543 BP_TR2: Partner in table VZGPO not converted (12/M)<br/>938500 BP_TR2: Request is not a Workbench or Customizing request (12/C)<br/>897417 BP_TR2: Correction delivery Customizing BP conversion (11/M)<br/>897323 BP_TR1: Incorrect conversion of the \"VIP\" indicator (11/C)<br/>887301 BP_TR2: Transport order number is not displayed (11/C)<br/>884242 BP_TR2: Performance of RFTBUP10_BP000  (10/C)<br/>882599 BP_TR2: Incomplete conversion of table BP000 (10/C)<br/>877345 BP_TR2: Require an order number for order/task field (10/C)<br/>867821 BP_TR1: Vendor assignment in table BC001 (10/C)<br/>861360 BP_TR2: Consistency check in RFTBUP10_BP000 (10/C)<br/>858123 BP_TR2: Table BP000 is deleted by RFTBUP10_BP000 (10/C)<br/>856458 BP_TR2: Logs of BP conversion cannot be assigned to client (10/C)<br/>852211 BP_TR2: COMPUTE_BCD_OVERFLOW with step EXEC_01 (10/C)<br/>848218 BP_TR1: Struc of Cstmizing tbls TSAD2, TSAD4, TSAD5, TSAD3 (10/C/M)<br/>836448 BP_TR2: BP conversion RFTBUP10-TZKN2 termination w/ FTBU582 (10/C/M)<br/>834661 BP_TR2: Order/task field not ready for input (09/C)<br/>832236 BP_TR2: Runtime error DYNPRO_MSG_IN_HELP (09/C)<br/>822644 BP_TR1: RFTBUP02 ends with the status 'not released' (M)<br/>817729 BP_TR1: RFTBUP02 performance (09/C)<br/>810702 BP_TR1: Termination RFTBUP01 with message AM850 (09/C)<br/>799078 BP_TR1: RFTBUP01 Bad Performance w/ BUPGUIDCREATE Step (09/C)<br/>785156 BP_TR2: Update of business partner conversion 2004 (09/C/M)<br/>786660 BP_TR2: RFTBUP11 Runtime error LOAD_PROGRAM_NOT_FOUND (09/C)<br/>785156 BP_TR2: Update of the business partner conversion 2004 (09/C)<br/>762061 BP_TR2: Deactivate consistency check for table BP000 (08/C/M)<br/>740464 BP_TR1: Delivery Customizing subproject/control (19/M)<br/>736512 BP_TR2: Update of the business partner conversion 2004 (08/C/M)<br/>721609 BP_DIA: Correspondence language w. SAP business partner (07/C)<br/>709880 BP_TR1: Business partner conversion phase I: Miscellaneous (07/C)<br/>707432 BP_TR1: Various errors in the BP conversion phase I (06/C)<br/>703279 BP_PAR: Archiving flag is not transferred to TR GP (06/C)<br/>700960 BP_TR1: Incomplete link between SAP-BP and TR-BP (05/C)<br/>696766 BP_PAR: Dialog box for address data during BP saving (05/C)<br/>693547 BP_TR1: Correspondence language for persons is missing (04/C)<br/>690288 BP_TR1: Commercial register entry is not transferred (04/C)<br/>689556 BP_PAR: Dialog box for address when saving a BP (04/C)<br/>685440 BP_TR2: Converting SAP standard data elements (04/M)<br/>684664 BP_TR1: Error in parallel maintenance and address conversion (04/C)<br/>670716 BP_TR2: SAP BP: Entry for BP000 missing in BPUM in BP conversion  (07/C)<br/>661078 BP_CVI: Adjustment of function module FLBPC_LOVN (16/C)<br/>656962 PAR: BUT000-Append for conversn/parllel maintnce empty (01/C)<br/>656108 BP_TR1: Language missing in organizations after the conversion (01/C)<br/>652210 BP_TR2: System does not display all conversion logs (01/C/M)<br/>649333 BP_PAR: Error in parallel maintenance of 'Date Founded' (01/C)<br/>645872 BP_TR1: Conversion of form of addr: Corr report for tab. ADRC (06/C/M)<br/></p> <b>V. Notes for Release SAP_APPL 470 (as of Support Package 18)</b><br/> <p>Note  short text (Support Package)<br/>856458 BP_TR2: Logs of BP conversion cannot be assigned to client (25/C)<br/>836448 BP_TR2: BP conversion RFTBUP10-TZKN2 termination w/ FTBU582 (25/C)<br/>762061 BP_TR2: Deactivate consistency check for table BP000 (22/C/M)<br/>736512 BP_TR2: Update of the business partner conversion 2004 (22/C/M)<br/>711251 BP_TR2: COMPUTE_BCD_OVERFLOW during BP conversion (21/C/M)<br/>710088 BP_TR1: Technical settings missing for table TPU5(21/M)<br/>689556 BP_PAR: Dialog box for address data when saving a BP (18/C)<br/>687754 BP_TR2: FTBU_START_EXCEL terminates with a runtime error (18/C)<br/>684664 BP_TR1: Error in parallel maintenance and address conversion (18/C)<br/></p> <b>IVa. Notes for Release R/3 Enterprise 5.0 (as of Support Package 01)</b><br/> <p>Note  short text (Support Package)<br/>1238781 BP_TR2 RFTBUH05 would place SAP objects in transport (14/M)<br/>1238632 BP_TR2 report parameter for RFTBUH02_2 (M)<br/>1113553 BP_TR1: Report RFTBUP08 incorrect selection (19/C)<br/>1068797 BP_TR1 - RFTBUP01 parameter 'Setup SAP business partner' (18/C/M)<br/>1065388 BP_TR2: Time-dependency of addresses and BP conversion<br/>1065386 BP_TR2: BPUM Customizing for BP030, BP1000 is deleted (18/M)<br/>1063936 BP_TR1: Error when you display relationship details (17/C/M)<br/>1055334 BP_TR1 R11245 - Error in validity periods (17/C)<br/>1045148 BP_TR2: Runtime error in program RFTBUP11 (17/M)<br/>1030247 BP_TR1: Transfer of address validity in SAP BP is incorrect (16/C)<br/>1028501 BP_TR1: Bank details and time-dependent roles in BP convers. (16/C)<br/>1014448 BP_TR1: RFTBUP01 changes partner despite BP000-NO_CONV = 'X' (16/C)<br/>1000146 BP_TR1: Empty log entries for RFTBP030 report (15/C)<br/>998597 BP_TR1: Poor performance of report RFTBP030 (15/C)<br/>996892 BP_TR2: Runtime error in program RFTBUP10_CML_ARC_DOC_REC (15/M)<br/>994629 BP_TR1 RFTBUP03 does not assign addresses correctly (15/M)<br/>980778 BP_TR1: Problems with business partner conversion (15/C)<br/>975549 BP_TR1: TRBP conversion even though NO_CONV indicator is set (14/C)<br/>974521 BP_TR2 Separator for download in Excel (14/C)<br/>968290 BP_TR2: Incorrect delivery Customizing for role change (14/M)<br/>967302 BP_TR2: Real estate object RFTBUP10_VDGPO not converted (14/C)<br/>965902 Do not convert BP_TR1 RFTBUP06 BPs with NO_CONV='X' (14/C)<br/>965653 BP_TR1 RFTBUP01 Occupation not migrated (14/C)<br/>962443 BP_TR1 BP_TR2 User name not output for conversion (14/C)<br/>948136 BP_TR2: RFTBUP01_BP000 issues message FTBU615 w/o BP number (13/C)<br/>946543 BP_TR2: Partner in table VZGPO not converted (14/M)<br/>944303 BP_TR1: Error R11245 after business partner conversion (13/K)<br/>942442 BP_TR1: Start date of BP roles too restrictive (13/K)<br/>942028 BP_TR1: Short dump \"DYNPRO_NOT_FOUND\" after BP conversion (13K)<br/>938500 BP_TR2: Request is not a Workbench or Customizing request (13/C)<br/>897417 BP_TR2: Correction delivery Customizing BP conversion (11/M)<br/>897323 BP_TR1: Incorrect conversion of the \"VIP\" indicator (11/C)<br/>887301 BP_TR2: Transport order number is not displayed (11/C)<br/>884242 BP_TR2: Performance of RFTBUP10_BP000  (11/C)<br/>882599 BP_TR2: Incomplete conversion of table BP000 (11/C)<br/>877345 BP_TR2: Require an order number for order/task field (10/C)<br/>867821 BP_TR1: Vendor assignment in table BC001 (10/C)<br/>861360 BP_TR2: Consistency check in RFTBUP10_BP000 (10/C)<br/>858123 BP_TR2: Table BP000 is deleted by RFTBUP10_BP000 (09/C)<br/>856458 BP_TR2: Logs of BP conversion cannot be assigned to client (09/C)<br/>852211 BP_TR2: COMPUTE_BCD_OVERFLOW with step EXEC_01 (09/C)<br/>848218 BP_TR1: Structure of Customizing TSAD2, TSAD4, TSAD5, TSAD3 (09/C/M)<br/>836448 BP_TR2: BP conversion RFTBUP10-TZKN2 termination w/ FTBU582 (08/C/M)<br/>834661 BP_TR2: Order/task field not ready for input (08/C)<br/>832236 BP_TR2: Runtime error DYNPRO_MSG_IN_HELP (08/C)<br/>829534 BP_TR2: Business partner role category texts are lost (08/C)<br/>822644 BP_TR1: RFTBUP02 ends with the status 'not released' (M)<br/>824100 BP_TR1: Roles with deletion flag are temporary (08/C)<br/>817729 BP_TR1: RFTBUP02 performance (07/C)<br/>812281 BP_TR2: BP conversion status for TB003 is not converted (07/M)<br/>810702 BP_TR1: Termination RFTBUP01 with message AM850 (07/C)<br/>803116 BP_TR1: Adjustments in the BP conversion (06/C/M)<br/>799078 BP_TR1: RFTBUP01 Bad Performance w/ BUPGUIDCREATE Step (07/C)<br/>785156 BP_TR2: Update of the business partner conversion 2004 (06/C/M)<br/>786660 BP_TR2: RFTBUP11 Runtime error LOAD_PROGRAM_NOT_FOUND (05/C)<br/>785156 BP_TR2: Update of the business partner conversion 2004 (05/C)<br/>762061 BP_TR2: Deactivate consistency check for table BP000 (04/C/M)<br/>736512 BP_TR2: Update of the business partner conversion 2004 (04/C/M)<br/></p> <b>IVb. Notes for Release SAP_APPL 500 (as of Support Package 01)</b><br/> <p>Note  short text (Support Package)<br/>944765 BP_TR1: Prevent RE BP maintenance after deact. parallel updte (13/M)<br/>856458 BP_TR2: Logs of BP conversion cannot be assigned to client (09/C)<br/>836448 BP_TR2: BP conversion RFTBUP10-TZKN2 termination w/ FTBU582 (08/C)<br/>822644 BP_TR1: RFTBUP02 ends with the status 'not released' (M)<br/>762061 BP_TR2: Deactivate consistency check for table BP000 (04/C/M)<br/>735581 BP_TR2: Role conversion (02, 03) Delivery in Support Package only<br/>736512 BP_TR2: Update of the business partner conversion 2004 (04/C/M)<br/><b>IVc. Notes for Release R/3 Enterprise 6.0 (as of Support Package 01)</b><br/>Note  short text (Support Package)<br/>1238781 BP_TR2 RFTBUH05 would place SAP objects in transport (14/M)<br/>1238632 BP_TR2 report parameter for RFTBUH02_2 (M)<br/>1113553 BP_TR1: Report RFTBUP08 incorrect selection (12/C)<br/>1068797 BP_TR1 - RFTBUP01 parameter 'Setup SAP business partner' (11/C/M)<br/>1065388 BP_TR2: Time-dependency of addresses and BP conversion<br/>1065386 BP_TR2: BPUM Customizing for BP030, BP1000 is deleted (11/M)<br/>1063936 BP_TR1: Error when you display relationship details (11/C/M)<br/>1055334 BP_TR1 R11245 - Error in validity periods (10/C)<br/>1045148 BP_TR2: Runtime error in program RFTBUP11 (10/M)<br/>1030247 BP_TR1: Transfer of address validity in SAP BP is incorrect (09/C)<br/>1028501 BP_TR1: Bank details and time-dependent roles in BP convers. (09/C)<br/>1014448 BP_TR1: RFTBUP01 changes partner despite BP000-NO_CONV = 'X' (08/C)<br/>1000146 BP_TR1: Empty log entries for RFTBP030 report (08/C)<br/>998597 BP_TR1: Poor performance of report RFTBP030 (08/C)<br/>996892 BP_TR2: Runtime error in program RFTBUP10_CML_ARC_DOC_REC (08/M)<br/>994629 BP_TR1 RFTBUP03 does not assign addresses correctly (08/M)<br/>980778 BP_TR1: Problems with business partner conversion (07/C)<br/>975549 BP_TR1: TRBP conversion even though NO_CONV indicator is set (07/C)<br/>974521 BP_TR2 Separator for download in Excel (07/C)<br/>968290 BP_TR2: Incorrect delivery Customizing for role change (06/M)<br/>967302 BP_TR2: Real estate object RFTBUP10_VDGPO not converted (06/C)<br/>965902 Do not convert BP_TR1 RFTBUP06 BPs with NO_CONV='X' (06/C)<br/>965653 BP_TR1 RFTBUP01 Occupation not migrated (06/C)<br/>962443 BP_TR1 BP_TR2 User name not output for conversion (06/C)<br/>948136 BP_TR2: RFTBUP01_BP000 issues message FTBU615 w/o BP number (05/C)<br/>946543 BP_TR2: Partner in table VZGPO not converted (05/M)<br/>944303 BP_TR1: Error R11245 after business partner conversion (05/K)<br/>942442 BP_TR1: Start date of BP roles too restrictive (05/C)<br/>942028 BP_TR1: Short dump \"DYNPRO_NOT_FOUND\" after BP conversion (05/C)<br/>938500 BP_TR2: Request is not a Workbench or Customizing request (05/C)<br/>916346 BP_TR2: Delivery Customizing correction ERP 2005 (04/M)<br/>897417 BP_TR2: Correction delivery Customizing BP conversion (03/M)<br/>897323 BP_TR1: Incorrect conversion of the \"VIP\" indicator (03/C)<br/>887301 BP_TR2: Transport order number is not displayed (02/C)<br/>884242 BP_TR2: Performance of RFTBUP10_BP000  (02/C)<br/>882599 BP_TR2: Incomplete conversion of table BP000 (02/C)<br/>877345 BP_TR2: Require an order number for order/task field (02/C)<br/>867821 BP_TR1: Vendor assignment in Table BC001 (01/C)<br/><br/><b>IVd. Notes for Release SAP_APPL 600 (as of Support Package 01)</b><br/>Note  short text (Support Package)<br/>944765 BP_TR1: Prevent RE BP maintenance after deact. parallel updte (05/M)<br/><br/></p> <b>VI. Notes for Release ABA 6.20:</b><br/> <p>Note  short text (Support Package)<br/>709880 BP_TR1: Business partner conversion phase I: Miscellaneous (38/C)<br/>641363 BP_TR1: Error w/ BP conversion of relationship category 0050 (28/M)<br/>621959 BP_PAR: Parallel update of address usages: incorrect transfer (25/C)<br/><br/></p> <b>VIII. Older Notes</b><br/> <p>If you have a very low Support Package status in Release BANK/CFM 463_20 or R/3 Enterprise 4.70 with Extension Set Financial services 1.10, then see the following Notes. These could apply to your status.<br/>           553328 Tables BP021 and VIEIGE not converted (Support Package 13)<br/>           547397 Business partner conversion: Registration of Structures (Support Package 13)<br/>           543874   BP001-BP_SORT is not filled (Support Package 12)<br/>           542356 Conversion program RFTBUP10_VTB_RULESET_PA (Part II) (Support Package 13)<br/>           537132 Conversion program RFTBUP10_VTB_RULESET_PA (Support Package 12)<br/>           535586 SAP BP conversion: creation of a group (Support Package 12)<br/>           530818 SAP BP conversion: Overflow of the memory (Support Package 11)<br/>           528998   Conversion academic titles,... incorrect (Support Package 11)<br/>           518307   BP: Conversion: Processing of BP with deletion flag<br/>           516621   Problems with report RFTBUP10_BP000 (Support Package 10)<br/>           516569   SAP BP conversion phase I: Comparison of the academic title (Support Package 10)<br/><br/>Below, we deal with questions customers frequently ask during the conversion. These questions are answered here in brief. We advise you to read these questions and answers before you open a customer message regarding BP conversion. The questionnaire is constantly extended.<br/></p> <ol>1. You want to activate parallel maintenance for the business partner using the view V_BPUM_CTL and the system displays error message FTBU 582 \"Central control of parallel maintenance not processed\".</ol> <p>           See Note 735358. This note describes how you must set the view V_BPUM_CTL so that (after executing both phases of the business partner conversion) you can start processing business partners.</p> <ol>2. You want to maintain a business partner and the system displays the error message from (2).</ol> <p>           Before you use the SAP business partner in Release 4.63, you must have a concept of the business partner conversion.<br/>           If you do not want to activate the parallel maintenance for the Treasury business partner and either setup the system again or did not use the Treasury partner before, you must set View V_BPUM_CTL correctly (see below). You can then start working with the partner immediately.<br/>           If you used the Treasury partner in the previous release or if you want to activate parallel maintenance between SAP BP and TR BP, you must execute the business partner conversion.<br/>           The conversion is performed in two phases: Phase I has already been performed for Release 4.62. Therefore, if you carry out an upgrade from Release 4.62 to Release 4.63, you no longer need to perform this phase. In all other cases you must first perform phase I of the business partner conversion and then phase II. Both phases are described in detail (about 30 pages each - the documentation is attached to this Note). Follow this documentation step by step and perform the specified steps as described.</p> <ol>3. You want to maintain an SAP business partner or a Treasury business partner. You receive the error message FTBU 340 \"Default grouping requires number range with internal number assignment.\"</ol> <p>           Using transaction SM30, go to maintenance of the view V_TPZ2U and in each of the two fields with the name \"Grouping\", create a grouping that refers to a number range with an internal number assignment. In the first field a grouping for the Treasury business partner, in the second field a grouping for the SAP business partner.</p> <ol>4. When you maintain the business partner (transactions BUP1, BUP2) or when you try to generate the subscreen container, the system generates a short dump when you select the Control tab: \"Too many views assigned\".</ol> <p>           Implement Note 483239. Too many views are assigned to section BUP030. This Note solves the problem.<br/></p> <ol>5. When you access the business partner maintenance, the system displays the short dump \"Dynpro_not_found\".</ol> <p>           This error can be caused by the fact that the subscreen containers were not regenerated after a change was made to the assignments in the BDT. Start transaction BUSP and enter the object \"BUPA\" there. Choose F8.<br/>You have to generate the subscreen separately in all clients. A transport is not possible.<br/></p> <ol>6. You can exclude existing customer connections (in TR BP) from the conversion with the conversion reports, therefore converting the business partners without customers?</ol> <p>           The link between the SAP BP and the customer is set up in the table BD001. A modification of the conversion programs would not help in this case because the customers would be regenerated online within the parallel supply. As soon as you maintain and save a TR BP, the corresponding customer is assigned to the SAP BP.<br/>           To limit the customer link to all business partners, first perform a business partner conversion without previously modifying the data of the Treasury business partner. To then limit the link to the customer, proceed as follows:<br/>           a) Create a report that deletes the field CUSTOMER for all entries in the table BP000 and execute the report.<br/>           b) Depending on your release, delete the entire contents of the following tables also:<br/>            Table BD001 (in all releases)<br/>            Table CVI_CUST_LINK (as of Release ERP 2005 and higher)<br/>           It is important that you perform the business partner conversion before you clean up the table BP000. Otherwise, the bank details of the TR BP will not be transferred to SAP BP because these details are updated in the customer only.<br/>           Bear in mind that the information about this is not available in the chapter \"Clean up the data of the Treasury BP if necessary\". The procedure described here is correct.</p> <ol>7. How do you control a customer role?</ol> <p>           The role must be entered in the view V_TBD002 so that a customer is always created automatically or when desired. Here, the following settings are possible:</p> <ul><ul><li>Customer yes/no: If this indicator is set, a customer is automatically created if a business partner is created in this role.</li></ul></ul> <ul><ul><li>Assign Customer: If this indicator is set, an indicator is displayed in dialog mode on the initial screen in the business partner maintenance. By setting this indicator (on the initial screen), you can decide individually for each business partner to be created or changed, whether or not you want to assign a customer.</li></ul></ul> <ul><ul><li>Displ.cust.no.: If this indicator is set in dialog mode in the business partner maintenance, the customer number is also displayed.</li></ul></ul> <ul><ul><li>Cust.Ref.: If this indicator is set, you are given the option of using a customer as reference on the initial screen when you create a business partner.</li></ul></ul> <ul><ul><li>FM Cust.Default: If a function module is entered here, this overrides the settings of the above indicators. You can enter your own function module here. In some roles, the module BPOB_TRD001_DEB_ROLLE has already been delivered by SAP.</li></ul></ul> <ul><ul><li>FM Preassigning Customer Data: If a function module is defined here, you can preassign the customer data. We deliver the BPOB_TRD001_VORBEL_DEB module for the main borrower role (TR0100).</li></ul></ul></p></div>", "noteVersion": 69}, {"note": "1503676", "noteTitle": "1503676 - Archiving: Deleting \"RE classic\" data under RE-FX", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>When you activate flexible Real Estate Management (RE FX) of the SAP ECC extension, the Real Estate Management (\"RE classic\") application is deactivated. Therefore, you can only start selected transactions, for example, to display master data. This means it is not possible to archive and delete this \"RE classic\" data.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>SARA</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>The connection from \"RE classic\" to flexible Real Estate Management is missing.<br/>You use flexible Real Estate Management as of Release 600 and have migrated from \"RE classic\" to RE-FX.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>Implement the attached corrections in the relevant source code.<br/>Use transaction <b>SE32</b> (ABAP Text Elements) to enter the following text elements with the maximum length 50 for the class <b>CL_REMI_CHECK_NON_SPECIFIC</b>:</p> <div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\"><tr><td>X01</td><td> Parameter Missing</td></tr> <tr><td>X02</td><td> Object type &amp;1 not supported</td></tr> <tr><td>X03</td><td> Business entity &amp;1 not found</td></tr> <tr><td>X04</td><td> Land &amp;1 not found</td></tr> <tr><td>X05</td><td> Building &amp;1 not found</td></tr> <tr><td>X06</td><td> Rental unit &amp;1 not found</td></tr> <tr><td>X07</td><td> Lease-out &amp;1 not found</td></tr> <tr><td>X08</td><td> General contract &amp;1 not found</td></tr> <tr><td>X09</td><td> Management contract &amp;1 not found</td></tr> <tr><td>X10</td><td> Contract offer &amp;1 not found</td></tr> <tr><td>X11</td><td> Settlement unit &amp;1 not found</td></tr> <tr><td>X12</td><td> Object &amp;1 - &amp;2 not migrated</td></tr> <tr><td>X13</td><td> There is no archiving object for object type &amp;1</td></tr> <tr><td>X14</td><td> Object &amp;1 - &amp;2 not found</td></tr> </table> <p><br/>Save and activate your changes.<br/><br/>The corrections provide a method for checking the current status of migrated real estate objects with regard to the archivability of relevant \"RE classic\" objects. Coincidentally, the ability to call this method is a necessary prerequisite for processing the archiving routines from \"RE classic\" (corrections to Note <b>1501199</b>).<br/>Based on the standard migration (package RE_MI_CL), the system checks the following real estate objects using transferred keys (object number or real estate key (IMKEY) or descriptive key) of the \"RE classic\" objects:</p> <div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\"><tr><td>Settlement unit</td></tr> <tr><td>Business entity</td></tr> <tr><td>Land</td></tr> <tr><td>Building</td></tr> <tr><td>Rental unit</td></tr> <tr><td>Lease-out</td></tr> <tr><td>Real Estate general contract</td></tr> <tr><td>Management contract</td></tr> <tr><td>Offer</td></tr> </table> <p>The results of the check of the migrated objects are:</p> <div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\"><tr><td>A status object from \"RE classic\" is used or</td></tr> <tr><td>a new status object</td></tr> </table> <p>and</p> <div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\"><tr><td>the object is active or</td></tr> <tr><td>the object has a deletion flag or</td></tr> <tr><td>the object has a deletion indicator or</td></tr> <tr><td>the object is archived and deleted or</td></tr> <tr><td>the object is not archived but is deleted or</td></tr> <tr><td>the object does not exist (error) or</td></tr> <tr><td>the \"RE classic\" object does not exist (error) or</td></tr> <tr><td>the \"RE classic\" object is not migrated (error).</td></tr> </table> <p>If the data is not supposed to be transferred using the tools of the standard SAP migration, the method <b>CHECK_ARCHIVABLE_OBJECT</b> of a customer-defined class that is inherited from the class <b>CL_REMI_MIG_ATTR_MODIFIACTION</b> can be defined for the check (Note <b>1079141</b> - Use of user exits in migration).</p></div></div></div></div></div>", "noteVersion": 3}, {"note": "116283", "noteTitle": "116283 - Release strategy for XIS-RE-C", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>You require information about the release strategy for XIS-RE-C (Corporate use of real estate).</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>This note provides information about the release strategy for XIS-RE-C (Corporate use of real estate), relating to SAP releases.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>Latest version at the beginning:<br/><br/>ERP 2005<br/>The component XIS-RE-C is no longer delivered as an independent component - it is replaced by the component RE-FX. The data of XIS-RE-C is retained when you upgrade to ERP 2005, but you must migrate it as described in Notes 443311 and 614298 - it is not converted automatically.<br/><br/>4.6B<br/>2.4 is the last version that will be available for the component XIS-RE- C.<br/>Delta upgrade to 2.4 on 4.6B, starting from Version 2.3 on 4.6B<br/>New installation of Version 2.3 on 4.6B<br/>Exchange upgrade to Version 2.3 on 4.6B, starting from 2.2 on 4.5B<br/><br/>4.5B<br/>New installation of Version 2.1 on 4.5B<br/>Exchange upgrade to Version 2.1 on 4.5B, starting from 1.2 on 4.0B<br/>Delta upgrade to 2.2 on 4.5B<br/><br/>4.5A<br/>New installation of 1.2 on 4.5A, CD 51005698<br/>Exchange upgrade to Version 1.2 on 4.5A (from 1.2 to 4.0B)<br/><br/>Version 1.2<br/>Upgrade to Version 1.2 on 4.0B<br/><br/>4.0B<br/>Version 1.1:<br/>Initial installation of Version 1.1<br/>available on Release 4.0B<br/><br/>Additional information:<br/>Overview Note 116282<br/></p></div>", "noteVersion": 11}, {"note": "1286751", "noteTitle": "1286751 - Long texts for error messages from migration", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>Long texts for error messages from the migration have been revised or added.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>REMICL</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p> </p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>The long texts are provided in a Support Package.</p></div>", "noteVersion": 2}, {"note": "739860", "noteTitle": "739860 - Migrating data from legacy system to SAP system", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>You have to migrate data from a legacy system to the SAP system.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>Data migration, date transfer, LSMW, DX-Workbench, BAPI, migration, legacy system</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>You want to import the SAP Real Estate Management based on Release 4.6C or the Flexible Real Estate Management in the Enterprise Extension 2.0 or higher.<br/>The target client for RE-FX is a client in which RE-Classic was not live before (tools for the automatic data migration in the RE-Classic client will be provided in the standard system for Release ERP 2005). In particular, this procedure is recommended if data and processes are supposed to be re-structured when implementing RE-FX.<br/>Read Note 517673 that describes the functionality contained in the respective RE-FX release.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>The SAP Deutschland AG &amp; Co. KG developed a tool for the support of necessary data migrations. This tool supports both the migration from external systems (legacy systems) and the SAP-to-SAP migration. In this case the different phases of a migration project are supported:</p> <ul><li>Conception</li></ul> <ul><li>Export of all relevant data</li></ul> <ul><li>Clever deposit of the exported data in the target system in your own tables (for the documentation, for Ad Hoc Query and for correction necessities which are recognized later)</li></ul> <ul><li>Mapping (-&gt; Customizing) of the data onto new structures; possible support of redesign approaches</li></ul> <ul><li>Comparison of the data of legacy system and new system;</li></ul> <ul><li>Storage of the data in the target system documentation of the executed system change (-&gt; quantities and values)</li></ul><p><br/>The tool has a modular architecture and is expandable. As a result it can be tailored to your requirements.<br/>It supports organizational changes in the context of migration with the Customizing functions -&gt; Redesign of the used software.<br/>It is also an approved tool for the processing of large datasets.<br/>This tool is not only developed for the SAP Real Estate Management, it contains also a toolbox for the migration of more necessary objects, in particular from FI (-&gt; OI transfers, master data, ...)<br/><br/>If you are interested, refer to:<br/>Sylvia Zerfass, SAP Deutschland AG &amp; Co. KG<br/>Branch office Duesseldorf<br/>Phone: +49 2102 868-365<br/><EMAIL></p></div>", "noteVersion": 7}, {"note": "977295", "noteTitle": "977295 - Migration of LUM from Classic RE to RE-FX", "noteText": "<div class=\"sapMMsgStrip sapMMsgStripWarning sapUiSmallMarginTop sapUiMediumMarginBottom\" id=\"DISCLAIMER\"><div class=\"sapMMsgStripMessage\"><span class=\"sapMText sapUiSelectable sapMTextMaxWidth\" dir=\"auto\" style=\"text-align: left;\"><strong>ATTENTION!</strong> This is a machine-translated document that has not been reviewed for accuracy. To provide feedback regarding the machine translation, please <a href=\"https://sapinsights.eu.qualtrics.com/jfe/form/SV_6nI2C1MOwEviVQG?NoteNumber=977295&amp;TargetLanguage=EN&amp;Component=RE-BD&amp;SourceLanguage=DE&amp;Priority=06\" style=\"color:var(--sapLinkColor);\" target=\"_blank\">click here</a>.<br/>You can access the original document via the following link: <a href=\"/notes/977295/D\" style=\"color:var(--sapLinkColor);\" target=\"_blank\">/notes/977295/D</a>.</span></div></div><div><div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>You use the LUM add-on under Classic RE and want to migrate to RE-FX.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Weitere Begriffe\">Other Terms</h3><p>Classic RE, RE-FX, migration</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Ursache und Voraussetzungen\">Reason and Prerequisites</h3><p>Conversion from Classic RE to RE-FX</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Lösung\">Solution</h3><p>As of ERP 2005 (SAP ECC 6.00, Financials Extension, EA-FIN 600), you can migrate your data from Classic RE to RE-FX. For this purpose, a consultant solution can be provided for the migration of LUM data.<br/>The migration programs convert the Customizing and the application data to the new tables of RE-FX. For more information, see the slide deck attached to this note.<br/><br/>The migration procedure corresponds to that of the standard migration, the migration programs are embedded in the standard migration tools.<br/><br/>Contact Person:<br/>Consting Unit Real Estate Secretariat:<br/> Sylvia Zerfass<br/> Homberger Straße 25, 40882 Ratingen<br/> Tel.: 02102/868-365 Fax: 02102/868-333<br/> Email: <EMAIL><br/><br/></p></div></div>", "noteVersion": 1}, {"note": "1111447", "noteTitle": "1111447 - MIgration: incorrect occupancy history", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>You notice errors in the occupancy history of implemented rental units after performing a migration.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>REMICL, VIMI03, VIBDROOCC</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>If the migrated contract end date is corrected for certain contracts in the migration view \"Required Postprocessing - Contract: Generate cash flow\", the occupancy history generated in the previous step \"Generate vacancy history\" may not be complete.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>Owing to the structure of the migration tool, an automatic program solution would lead to performance losses (postgenerating the occupancy history for the entire holdings).<br/>Such a solution is provided with Release <b>ERP2005 (Ehp2)</b>, which now includes the sub-step \"Correct occupany history\" between the sub-steps \"Contract: Generate cash flow\" and \"Rental object: Generate occupancy cash flow\" in the main migration step \"Required Postprocessing\", which is \"not active\" on delivery. The customer can activate this solution (to correct an SAP object) if a test migration shows such \"inconsistencies\", and accruals and deferrals are not possible on the relevant rental objects.<br/>If the system has not yet been upgraded to Release <b>ERP2005 (Ehp2)</b>, one option is to maintain the occupany histories individually after the \"Required Postprocessing\" migration step:</p> <ol>1. Report <b>RFREMIRUNSTEP</b> with the parameters<br/> <div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\"><tr><td>Step:</td><td> END Required Postprocessing</td></tr> <tr><td>Group:</td><td> OCC Generate vacancy history</td></tr> </table></div></ol> <p></p> <ol>2. If vacancy reasons were determined for the relevant rental objects, run the report <b>RFREMIRUNSTEP</b> with the parameters<br/> <div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\"><tr><td>Step:</td><td> END Required Postprocessing</td></tr> <tr><td>Group:</td><td> VAC Transfer vacancy reasons</td></tr> </table></div></ol> <p><br/>If accruals and deferrals are not possible for the relevant rental units, you can use the correction report <b>RFREDSOCCUPANCYREPAIR</b> to re-generate the occupancy history.<br/></p></div>", "noteVersion": 2}, {"note": "443311", "noteTitle": "443311 - Enterprise extension SAP Real Estate: Technical basis", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You require information about the release strategy in the SAP Real Estate Management area.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Enterprise, Real Estate, flexible Real Estate Management, RE, IS-RE, release<br/>Compatibility, ERP, ECC, SAP ERP</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p></p>\n<p><strong>1 </strong><strong><span>SAP Real Estate Management </span></strong></p>\n<p> </p>\n<p><strong>1.1 </strong><strong>General information</strong></p>\n<p> </p>\n<p>SAP Real Estate Management is the Real Estate solution for SAP. The solution is contained in the scope of delivery for SAP ERP or S/4HANA (on premise).</p>\n<p> </p>\n<p><strong>1.2</strong> <strong>Compatibility and integration for industry solutions</strong></p>\n<p> </p>\n<p>In contrast to earlier releases that are no longer available, SAP Real Estate Management is no longer an Industry Solution. Therefore, SAP Real Estate Management can also be used in conjunction with SAP Industry Solution. However, note that SAP Real Estate Management does not contain any specific integration to the functions of SAP Industry Solution.</p>\n<p> </p>\n<p>SAP Industry Solution for the public sector is an exception. SAP Note 751579 describes the scope of the integration offered here.</p>\n<p> </p>\n<p>The SAP Homebuilder solution is another exception. See SAP Note 870859 for the integration in this solution.</p>\n<p> </p>\n<p><strong>1.3</strong> Classic RE versus flexible Real Estate Management</p>\n<p> </p>\n<p>SAP currently provides two versions of SAP Real Estate Management. The older \"Classic RE\" version will be referred to as the \"old solution\", and the new version \"flexible Real Estate Management\" will be referred to as the \"new solution\".</p>\n<p> </p>\n<p>Classic RE is available in SAP ERP 6.0. Flexible Real Estate Management is available only since SAP R/3 Enterprise Financials Extension Set 2.00.</p>\n<p> </p>\n<p><strong>2 </strong><strong><span>Classic RE (old solution)</span></strong></p>\n<p> </p>\n<p><strong>2.1 </strong><strong>Availability</strong></p>\n<p> </p>\n<p><span>For new customers of SAP Real Estate Management, classic Real Estate is no longer released for a first implementation as of the date of the general availability of SAP ERP 6.0 (as of 2006).</span></p>\n<p><span> </span></p>\n<p>SAP is focused on the stabilization of the product for the old solution, and for this reason will <span>not develop</span> Classic RE further. The existing function is still available in SAP ERP.</p>\n<p><span>The availability and maintenance of Classic RE will end with the general end of maintenance of SAP ERP 6.0.</span></p>\n<p>Customers who are already using the old solution can still use this, as previously mentioned, until the end of the general maintenance of SAP ERP 6.0. By this time, the migration to flexible Real Estate should have been completed because SAP will no longer maintain the old solution as of this time.</p>\n<p> </p>\n<p><span>Classic RE cannot be used under S/4HANA and is not released.</span> Existing Classic RE customers must migrate to RE-FX before upgrading to the solution \"SAP S/4HANA, on-premise edition\". Note that the display and call of Classic RE data in SAP S/4 HANA are no longer possible.</p>\n<p><strong><span> </span></strong></p>\n<p><strong>2.2 </strong><strong>Localization and add-ons</strong></p>\n<p> </p>\n<p>The old solution is available in the standard languages of SAP ERP. During the localization, classic Real Estate was (at least) partially adjusted to the legal requirements in Germany, Italy, Austria and Switzerland. For Portugal and Spain, the withholding tax function was made available.</p>\n<p>SAP will not provide any additional localization of Classic RE.</p>\n<p> </p>\n<p>See SAP Note 448973 for information about the compatibility of add-ons for land use management and condominium owners administration.</p>\n<p> </p>\n<p><strong>3 </strong><strong><span>Flexible Real Estate Management (new solution)</span></strong></p>\n<p> </p>\n<p><strong>3.1 </strong><strong>Availability</strong></p>\n<p> </p>\n<p>Since SAP R/3 Enterprise Financials Extension Set 2.00 of the extension, SAP provides a new Real Estate management with more enhanced and flexible functions with flexible Real Estate.</p>\n<p> </p>\n<p>Note that all the individual functions from the old solution are not contained in all releases of the new solution. See SAP Note 517673 for more detailed information about the scope of functions of the new soution in the various releases.</p>\n<p> </p>\n<p><strong>3.2 </strong><strong>Release restriction</strong></p>\n<p> </p>\n<p> </p>\n<p>Note that Version SAP R/3 Enterprise Financials Extension Set Release 1.10 is not released for implementation, and is not maintained as a result.</p>\n<p> </p>\n<p>We recommend, if possible, that you only use the last available version of the new solution for the implementation. In each case, we recommend that experienced consultants check the feasibility of the implementation as part of a study.</p>\n<p> </p>\n<p><strong>3.3 </strong><strong>Activation</strong></p>\n<p> </p>\n<p>To use the new solution, you must first activate the Financials Extension Set (EA-FIN) across clients</p>\n<p>and</p>\n<p>then activate the Real Estate Extension across clients.</p>\n<p> </p>\n<p>Note that you can no longer use the old Classic RE solution after the activation. To continue to use the old solution in a specific client, do not activate the Real Estate Extension for that client.</p>\n<p> </p>\n<p>As the activation occurs for every client, you cannot use classic and flexible Real Estate in parallel in the same client. Therefore, both solutions can only be used in separate clients.</p>\n<p> </p>\n<p><strong>3.4 </strong><strong>Migration </strong></p>\n<p> </p>\n<p>If you have already used Classic RE, you must convert the existing data (contracts, usage objects, partners, and so on) using a special migration program from Classic RE to flexible Real Estate Management. In this case, the data is converted in the same client. This migration program is available as of the standard delivery of SAP ERP 6.0 and is described in detail in SAP Note 828160.</p>\n<p> </p>\n<p>If you want to convert the data from Classic RE to flexible Real Estate Management without using this migration program, you can alternatively use the existing data transfer tools (BAPIs). In this case, you must prepare and implement the unloading of the data from the source system in the project, as there is no standard migration program. In this case, the data can normally not be converted in the same client. In Customizing, following the data migration, make a setting so the new solution is used instead of the old solution. If you are considering this method, SAP recommends you commission SAP Consulting to perform the conversion.</p>\n<p> </p>\n<p>Migrating data from Classic RE to RE-FX is not a simple technical conversion. Similar to a legacy data transfer, the migration must be planned and performed as a project. In this sense, the migration programs provide the technical support for the data transfer project. A migration project can be more or less time consuming depending on how complex your data is, the extent to which you work with customer developments, and the extent to which you use the RE-FX functions.</p>\n<p> </p>\n<p><strong>3.5 </strong><strong>Localization</strong></p>\n<p> </p>\n<p>The localization of flexible Real Estate is described in detail in SAP Note 771098.</p></div>", "noteVersion": 25}, {"note": "628208", "noteTitle": "628208 - Procedures for activating RE Extension in SAP R/3 Enterprise", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>You want to activate \"SAP Real Estate Management in the SAP R/3 Enterprise Financials Extension\" (called \"RE Extension\" in the following).<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>Flexible Real Estate Management (project name), Flexible RE, Flexible Real Estate management<br/>RE-FX</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>First carefully read Notes 443311, 517673 and related notes.<br/><br/>You must use Release 47x200 (at least Support Package 04) to activate \"RE Extension\". We recommend that you always use the most recent release (currently ERP 2005).<br/>You can obtain information about the availability of the subsequent releases from SAP's general release planning information.<br/><br/>For information about how to determine the countries in which RE-FX can be used, see Note 771098.<br/><br/>If you previously used RE-Classic, you must migrate first. For more information about this, see Notes 865555 and 828160.<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>The functions are part of Financials Extension. Therefore, to activate these functions, you must first activate Financials Extension.<br/><br/>To do this, call transaction SPRO (\"SAP Reference IMG\"), choose \"Activation Switch for SAP R/3 Enterprise Extension Set\". Set the \"Active\" indicator for the \"EA-FIN\" application. This setting is valid for all clients.<br/><br/>Then use transaction RECACUST to choose IMG activity \"Basic settings\" -&gt; \"Activate Real Estate Extension\" and set the \"Extension Active\" indicator in \"Activate Real Estate Extension\".<br/><br/>Also make sure that the \"BTE Application (RE) Active\" is selected (in the more recent Support Packages, this is a prerequisite that must be met so that you can select the \"Extension Active\" indicator). Call transaction SM30 to check this (Table/View TBE11). The \"Active\" indicator must be set for the \"RE\" application. This setting is valid for all clients.<br/><br/>Note:<br/>The RE-FX transactions are contained in the SAP_RE_APPL role.<br/><br/><br/></p></div>", "noteVersion": 9}, {"note": "1043260", "noteTitle": "1043260 - Input tax correction: Data transfer from Classic RE", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>The note is only relevant if you</p> <ul><li>migrate from Classic RE to RE-FX and</li></ul> <ul><li>you use the input tax correction function for RE-FX (for more information about this function, see Note 964834).</li></ul> <p></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>Input tax correction, trivial amounts, correction items, RFVIBAGA, VITAXA<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>Currently, there are no automatic tools for transferring correction data from Classic RE to RE-FX. This means that the transfer must occur as part of the migration project for a specific customer.<br/><br/>You can use the function module API_RE_TC_CREATE to transfer data. With this function module you can transfer the master data for correction items (table VIBEBE) and correction objects (table VIBECO), as well as the correction basis (VITAXA for correction items, VIBEBASE for correction objects) and already posted corrections (table VITAXA for correction items, VIBEITEM for correction items).<br/><br/>If the transfer occurs mid-year, we recommend that you set the date of the first correction (FIRSTCALCDATE) to the beginning of the calendar year. This ensures that all monthly corrections for the previous months are checked in RE-FX. In this case, you must transfer all corrections posted in the operational system (RE Classic) as of the date of the first correction, so that these are taken into account in the recalculation of previous months. The transfer of earlier corrections is required for report purposes only.<br/><br/>In contrast to RE-FX, corrections are posted monthly for correction items in Classic RE, even if the trivial amount limits of Article 44 of the German sales tax law are not exceeded (does not apply to correction objects). These postings are then reversed in the course of the year. However, to ensure correct processing of the postings copied from Classic RE to RE-FX, these trivial postings must not be transferred to RE-FX.<br/><br/>This note provides a transaction that permits you to reverse the correction amounts posted mid-year to correction items that are below the trivial amount limits before the data transfer.<br/><br/>Proceed as follows:</p> <ul><li>Perform the correction runs up to the time of migration in Classic RE - for example, the last correction run in Classic RE occurred on May 31, 2007.</li></ul> <ul><li>Reverse the trivial amounts posted in Classic RE using transaction FOVIM delivered with this note. Enter the date of the last correction run (in our example, May 31, 2007) as the date of the last correction and set the migration preparation parameter.</li></ul> <ul><li>Then transfer all of the corrections (excluding reversed postings and reversal documents) for the current year to RE-FX. Keep in mind that new postings are created as a result of reversing the trivial amounts (the adjustments that were originally posted are not cancelled). To ensure that the trivial amounts are not transferred to RE-FX after they are reversed, the transfer program should include a check that guarantees that the correction postings for the current year are only transferred if the net value is other than zero.</li></ul> <p><br/>The changes are delivered in the relevant Support Package. For an advance correction, proceed as follows:</p> <ol>1. Create the following data elements in the package FVVI:</ol> <ul><li>VVMIDAT</li></ul> <p>           Short text: Preparation run for data migration to RE-FX<br/>           Domain XFLAG</p> <ul><li>VVMIDAT</li></ul> <p>           Short text: Date of last correction run in Classic RE<br/>           Domain: DATUM</p> <ol>2. Call transaction SE93 and copy transaction FOVI to transaction FOVIM, then change the text to \"Migration: Post trivial amounts to a prior period\".</ol> <ol>3. Implement the attached corrections.</ol> <ol>4. For the report RFVIBAGA, maintain:</ol> <ul><li>Selection texts</li></ul> <p>           P_MIGRA Migration Preparation<br/>           P_MIDAT Last Correction Classic RE</p> <ul><li>Text symbols</li></ul> <p>           071 Migration date must be assigned<br/>           072 Migration date must be in calendar year<br/>           073 Preparation for migration to RE-FX<br/>              </p></div>", "noteVersion": 6}, {"note": "1357750", "noteTitle": "1357750 - Migration: Limited partner assignments", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>After the migration of Real Estate general contracts (known as \"tenant rental agreements\" within migration), the processing of these contracts may result in the error message \"Postings [until/from 99.99.9999]: assignment of business partner XXXXXXXXXX to contract is limited in time\".</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>REMICL, VICN01, VZZKOPO, VZGPO, VITMPY, VIBPOBJREL, RETMFI003, 6D 419</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>In the Real Estate general contract, you can assign additional business partners with customer or vendor role types in addition to the required main contractual partner. Unlike the main contractual partner, the assignment of these business partners to the contract can be limited in time.<br/>If the role type of the main contractual partner is not entered in the default settings for the contract type, a main contractual partner is not required in contracts for this contract type.<br/>A prerequisite for using a business partner in a condition is that the business partner is assigned to the contract independent of time-related availability. In certain circumstances (condition start is before assignment start of partner to contract, condition end for condition limited in time is after assignment end of partner to contract), the system issues the warning \"The condition validity (AAAA) is too long for partner XXXXXXXXXX\", which is not relevant for the activation of the contract and the generation of the cash flow.<br/>In the contracts of flexible Real Estate Management (\"external contract\" contract category), the business partner of the condition is no longer assigned directly but using the posting term. To ensure that no errors occur during the data retrieval for the relevant posting system, the following conditions must be met when the contract is activated:</p> <ul><li>The posting term is not limited in time. A limitation is made indirectly using the contract term data or the validity periods of the assigned conditions.</li></ul> <ul><li>Time-based changes to attributes of the posting term (in this case, changing the customer/vendor business partner to be used) are made by generating time slots.</li></ul> <ul><li>The assigned business partner must be assigned to the contract in the validity interval of the contract time slot.</li></ul><ul><li>A business partner must be assigned to each time slot of a posting term.</li></ul> <p><br/>These conditions are not dependent on the validity period of the condition(s) assigned to the term.<br/>Due to this different organizational solution in Classic RE and flexible Real Estate Management, the system may convert data incorrectly during the migration if</p> <ul><li>the assignment of business partners to the contract is limited in time (\"Valid from\", \"Valid to\"), and</li></ul> <ul><li>the conditions are limited in time (\"Valid to\") and are incomplete in their time-based assignment.</li></ul> <p></p> <b>Example:</b><br/> <p>There is a (vendor) tenant rental agreement (valid from 01.01.2008, open-ended) with the following partner assignment:</p> <ul><li>Landlord with vendor account AAA (open-ended, as main contractual partner)</li></ul> <ul><li>Contract partner with vendor account BBB (01.08.2008 - 31.07.2009)</li></ul> <ul><li>Contract partner with vendor account CCC (01.08.2009 - 31.07.2010)</li></ul> <p><br/>The contract has a condition 9999 with the following validities and partner assignments (monthly frequency):</p> <ul><li>01.08.2008 - 31.07.2009 with partner BBB</li></ul> <ul><li>01.08.2009 - 31.07.2010 with partner CCC</li></ul> <p><br/>This means that the validity periods of the conditions correspond to the validity periods of the assigned partners (in \"Classic RE\" this is not necessarily required).<br/>The resulting cash flow (without consideration of due dates) begins on 01.08.2008 with the vendor belonging to partner BBB. On 01.08.2009, this changes to the vendor of partner CCC, and the cash flow ends on 31.07.2010.<br/><br/>In the migration, the system generates a posting term for both condition items, which has the following validity periods (time slots) for the assigned contract partner:</p> <ul><li>__._-.____ - 31.07.2009 partner BBB</li></ul> <ul><li>01.08.2009 - __.__.____ partner CCC</li></ul> <p><br/>A check of the contract returns the following error for the posting term:</p> <ul><li>Postings [to 31.07.2009]: Assignment of business partner BBB to contract is limited in time.</li></ul> <ul><li>Postings [from 01.08.2009]: Assignment of business partner CCC to contract is limited in time.</li></ul> <p><br/>This error must not be suppressed using the migration tool because different customer-specific solutions are possible according to each situation.<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>Only customer-specific solutions are possible. Note <b>1079141</b> describes an interface that can be used within migration.</p></div>", "noteVersion": 3}, {"note": "1501199", "noteTitle": "1501199 - Archiving: Deleting \"Classic RE\" data in RE-FX", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>When you activate flexible Real Estate Management (RE-FX) within the SAP ECC extension, the Real Estate Management (\"Classic RE\") application is deactivated. Therefore, you can only start selected transactions, for example, to display master data. This means it is not possible to archive and delete this \"Classic RE\" data.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>SARA, 6D 381</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>The connection from \"Classic RE\" to flexible Real Estate Management is missing.<br/>You use flexible Real Estate Management as of Release 600 and have migrated from \"Classic RE\" to RE-FX.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Implement the attached corrections in the relevant source code.<br/>Use transaction <strong>SE32</strong> (ABAP Text Elements) to maintain the following text elements for the programs</p>\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td>RFVIAR10</td>\n</tr>\n<tr>\n<td>RFVIAR20</td>\n</tr>\n</tbody>\n</table></div>\n<p>with a maximum length of 30:</p>\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td>X01</td>\n<td>\"Classic RE\" is deactivated</td>\n</tr>\n</tbody>\n</table></div>\n<p>Use transaction <strong>SE32</strong> (ABAP Text Elements) to maintain the following text elements for the programs</p>\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td>RFVIAR43</td>\n</tr>\n<tr>\n<td>RFVIAR53</td>\n</tr>\n<tr>\n<td>RFVIAR63</td>\n</tr>\n<tr>\n<td>RFVIAR73</td>\n</tr>\n<tr>\n<td>RFVIAR83</td>\n</tr>\n<tr>\n<td>RFVIAR93</td>\n</tr>\n<tr>\n<td>RFVIAR103</td>\n</tr>\n</tbody>\n</table></div>\n<p>in the specified maximum length:</p>\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td>X01</td>\n<td>Status of migrated object does not allow processing (mLen 60)</td>\n</tr>\n<tr>\n<td>XS1</td>\n<td>No status change due to migration (mLen 50)</td>\n</tr>\n</tbody>\n</table></div>\n<p><br/>Implement the corrections from SAP Note <strong>1503676</strong>. These corrections are a necessary prerequisite for processing the archiving steps. If the corrections are not implemented, processing terminates with the message \"Transaction is locked; \"classic\" Real Estate is not active\".<br/><br/>After you implement the corrections, you can archive and delete the following \"Classic RE\" archiving objects:</p>\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td>RE_OFFER</td>\n<td>Offer</td>\n</tr>\n<tr>\n<td>RE_RNTL_AG</td>\n<td>Lease-out</td>\n</tr>\n<tr>\n<td>RE_RNTL_UN</td>\n<td>Rental unit</td>\n</tr>\n<tr>\n<td>RE_BUILDNG</td>\n<td>Building</td>\n</tr>\n<tr>\n<td>RE_PROPRTY</td>\n<td>Property</td>\n</tr>\n<tr>\n<td>RE_BUSN_EN</td>\n<td>Business entity</td>\n</tr>\n<tr>\n<td>RE_STLM_UN</td>\n<td>Settlement unit</td>\n</tr>\n<tr>\n<td>RE_MGT_CNT</td>\n<td>Management contract</td>\n</tr>\n</tbody>\n</table></div>\n<p>in RE-FX.<br/><br/>Since the statuses required for archiving (deletion flag, deletion indicator) can no longer be maintained due to the locked maintenance transactions, the only criterion is the status of the migrated object. The conditions for archiving the \"Classic RE\" objects are as follows:<br/> The migrated object is archived (entry in VICAAROBJ). <br/> The migrated object is deleted without archiving (entry in VICAAROBJ).<br/><br/>If one of these conditions is fulfilled, the object is processed according to the archiving step (Preparatory Program, Write, Delete). The checks required in \"Classic RE\" are not executed in this case, except for the following:<br/> Checking the residence time<br/> Checking the existence of subordinate objects, which otherwise may lead to errors in the display transactions (for example, rental units may no longer be assigned to a building to be archived).<br/><br/>If, during migration, no new status object is created for the object transferred, no status change takes place in the archiving steps for the \"Classic RE\" object. In the \"Delete\" archiving step, the status objects for these archiving objects are not deleted.<br/>If none of the above conditions is fulfilled, the \"Classic RE\" object is not processed and a relevant log message is issued. This also applies to objects excluded from the migration.</p></div>", "noteVersion": 2}, {"note": "939971", "noteTitle": "939971 - FAQ and consulting notes for RE-FX", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>This note compiles all consulting notes and FAQ notes for RE-FX.<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>Consulting, RE-FX<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>For more information, see related notes.<br/></p></div>", "noteVersion": 1}, {"note": "1448446", "noteTitle": "1448446 - Unnecessary system statuses in rental objects and contracts", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>In Classic RE , statuses were used to describe certain statuses of rental units (rental objects) or contracts. Some of these statuses are no longer used in RE-FX. The rental objects or contracts migrated from Classic RE still have these statuses. The program RFRE_REMI_STATUS_REPAIR delivered with this note deactivates the statuses that are no longer used in the objects or contracts.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>You have used REMICL to migrate your data from Classic RE to RE-FX.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>First implement Note 1449490. Then execute the report provided with this note from RFRE_REMI_VICAINTRENO_REPAIR (at least for usage objects and contracts) in an update run.<br/><br/>Implement the attached corrections. The corrections provide the report RFRE_REMI_STATUS_REPAIR. In rental objects and contracts, the report deactivates status entries that have been transferred from Classic RE, but are no longer used and no longer have an impact in RE-FX.</p></div>", "noteVersion": 1}, {"note": "1578783", "noteTitle": "1578783 - Customer: Alternative search helps by real estate", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>In the F4 search help for the customer, the search help \"Customers With Lease-Out\" and, in the SAP ECC Extension, the search help \"Customer by Real Estate Contract\" are available.<br/><br/>The search help \"Customers With Lease-Out\" is relevant for RE Classic, but the search help \"Customer by Real Estate Contract \" is only relevant for RE-FX.<br/><br/>The following scenarios are possible for the results list:</p> <ol>1. You use RE Classic. In this case, the search help \"Customer by Real Estate Contract\" does not deliver any results.</ol> <ol>2. You use flexible Real Estate Management without having used RE Classic beforehand. In this case, the search help \"Customers With Lease-Out\" does not deliver any results.</ol> <ol>3. You use flexible Real Estate Management after using RE Classic beforehand. The search help \"Customer by Real Estate Contract\" delivers a result set taking into account the migrated and newly created RE-FX contracts. The search help \"Customers With Lease-Out \" delivers a results set taking into account the lease-outs maintained under RE Classic (status at the time of the migration).</ol> <p><br/>Since the results of only one of the search helps are relevant in each case, the other search help should be hidden in each case. This is not possible in the standard SAP system. This note describes a customer solution without modification.<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>DEBI, DEBIM, REKUNNRCN</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>The problem is caused by a static assignment of the elementary search helps to real estate contracts in the search help of the customer.<br/>You use Real Estate or flexible Real Estate Management Release 500 or higher.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>Use transaction <b>SE11</b> (Data Dictionary) to change the (collective) search help <b>DEBI</b> (use the \"Display\" function). Choose \"Goto -&gt; Append Search Help...\" to receive a list of the defined append search helps. Use the \"Create\" function to maintain a customer-specfic append search help in your namespace (handbook for \"Change and Transport\"). As the short description, enter \"Hide Invalid Search Helps\". On the \"Included search helps\" tab page, create an entry corresponding to the scenarios described above:</p> <ul><li>with the search help <b>DEBIM</b> if RE-FX is <b>active</b> or</li></ul> <ul><li>with the search help <b>REKUNNRCN</b> if RE-FX is <b>not active</b></li></ul> <p><br/>and with the \"Hidden\" indicator set. Save and activate the search help.<br/><b>Important: This change is system-dependent and not client-dependent. If, in the R/3 system, you have clients for which both \"RE classic\" and RE-FX are active, you must not implement this DDIC change.</b><br/><br/></p></div>", "noteVersion": 2}]}, {"note": "1944871", "noteTitle": "1944871 - SAP Simple Finance, On-Premise Edition: Removing RE Classic", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>SAP Note has been replaced by <a href=\"/notes/2270550\" target=\"_blank\">2270550</a></p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>sFIN, SFINANCIALS, RE, Classic, real estate management, REMICL, S/4 HANA</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>Product strategy</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>See <a href=\"/notes/2270550\" target=\"_blank\">2270550</a></p>", "noteVersion": 6, "refer_note": [{"note": "2254013", "noteTitle": "2254013 - SAP Real Estate: Release for S/4HANA", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>This SAP Note describes the releases of SAP Real Estate under S/4HANA.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Flexible Real Estate, RE-FX, Classic Real Estate, Corporate Real Estate, Commercial Real Estate, Residential Real Estate, ramp up, scope of functions, RE Classic, S/4HANA, S/4 HANA, S/4</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>Availability of RE-FX in S/4HANA</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong>Using SAP RE-FX (Flexible Real Estate Management)</strong></p>\n<p>SAP RE-FX (Flexible Real Estate) is released under the \"S/4HANA on premise\" and \"S/4HANA Cloud, Private Edition\" solutions (all generally available OP versions) and can be used. The following scope of functions is available for using this:</p>\n<ul>\n<li>SAP RE-FX scope of functions in accordance with SAP Note 517673</li>\n<li>Integration into S/4HANA Finance</li>\n<li>If you are unsure about the availability of new functions for other components (for example, new Cash Management), create a message in the relevant component.</li>\n<li>With regard to the availability of country-specific functions, please contact us in advance under component RE-FX-LC. </li>\n</ul>\n<p> </p>\n<p><strong>Using SAP Classic RE (Classic Real Estate)</strong></p>\n<p>The use of Classic RE under S/4HANA (or SAP S/4HANA Finance edition 1503 or higher releases) is not possible and is not released. Existing Classic RE customers must migrate to RE-FX before upgrading to the \"SAP S/4HANA, on-premise edition\" and \"S/4HANA Cloud, Private Edition\" solutions.  Note that the display and call of Classic RE data in SAP S/4 HANA are no longer possible.</p>\n<p>We strongly recommend that you archive Classic RE data before the upgrade.</p>\n<p> </p>\n<p> </p>\n<p> </p>", "noteVersion": 12}]}, {"note": "517673", "noteTitle": "517673 - Flexible Real Estate: Functions and restrictions", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>This SAP Note describes which functions are available in the component flexible Real Estate Management (RE-FX) in which release, and which release restrictions apply.<br/><br/></p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Flexible Real Estate, Classic Real Estate, Corporate Real Estate, Commercial Real Estate, Residential Real Estate, ramp up, scope of functions</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>1. General information</p>\n<p>You are considering using flexible Real Estate Management (RE-FX). This SAP Note informs you about the important functions that flexible Real Estate Management contains in the following releases:</p>\n<ul>\n<li>Financials Extension Set 1.10 for SAP R/3 Enterprise</li>\n<li>Financials Extension Set 2.00 for SAP R/3 Enterprise</li>\n<li>SAP ERP 2004</li>\n<li>SAP ERP 6.0</li>\n<li>S/4 HANA (see SAP Note 2254013)</li>\n</ul>\n<p><br/>This SAP Note provides an overview of the functions, but is not necessarily complete. If you have detailed questions, we recommend that you request a feasibility study from SAP Consulting or an SAP Implementation Partner.</p>\n<p>2. Explanation of the release descriptions</p>\n<p>For more information about the specified releases, the release strategy and technical prerequisites, see SAP Note 443311.</p>\n<p>3. Usage restriction for Release 1.10 </p>\n<p>Keep in mind that the Flexible Real Estate Management in Financials Extension Set 1.10 for SAP R/3 Enterprise is no longer released. This is due to the limited functional scope of this version and the basic changes of the user interfaces for subsequent releases, which would require additional training.  Therefore, Flexible Real Estate Management in Financials Extension Set 1.10 for SAP R/3 Enterprise is not suitable for implementation and we no longer maintain it.</p>\n<p>4. International availability</p>\n<p>The following section gives some descriptions about country-specific functions.  However, these are not described completely in this SAP Note. For information about the availability of SAP Real Estate Management for different countries, see SAP Note 771098.</p>\n<p>5. Adjustments to the solution and programming interfaces</p>\n<p>For information about customer-specific adjustments to the solution with the SAP standard tools BAdI and BDT, see SAP Note 690900, and for information about programming interfaces, see SAP Note 782947.</p>\n<p>6. Additional information</p>\n<p>If you are interested in using the solution, visit SAP Service Marketplace (http://service.sap.com/re) and the SAP Help Portal (http://help.sap.com/) and read the information available there.</p>\n<p>For individual questions, use your SAP Account Manager to contact the SAP Product Sales in your country. </p>\n<p>For general questions regarding this SAP Note, please contact Tom Anderson (<a href=\"mailto:<EMAIL>\" target=\"_blank\"><EMAIL></a>) or Marc Hoffmann (<a href=\"mailto:<EMAIL>\" target=\"_blank\"><EMAIL></a>).</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>1. Functional scope of Flexible Real Estate in SAP R/3 Enterprise Financials Extension Set 2.00 </p>\n<p>This release contains the following functions:</p>\n<p>a) Usage view</p>\n<p>In the usage view, you display the objects for the business usage of your real estate.  These include business entities, buildings, properties and rental objects.  Rental objects can be classic rental units, pooled spaces or rental spaces with reference to a pooled space. </p>\n<p>b) Contract management</p>\n<p>With the real estate contract, you can manage all contracts of Real Estate Management, including lease-in contracts, lease-out contracts and general customer or vendor contracts.  You can assign any number of rental objects from different business entities and buildings to a real estate contract.  In addition, you can define (\"cut\") rental spaces with reference to a pooled space when you create a contract.  The real estate contract also enables you to monitor tasks with the reminders function.</p>\n<p>c) Rental accounting</p>\n<p>As in Classic Real Estate, you can create a cash flow from the conditions defined in the contract, which you can use to trigger postings in SAP Financial Accounting (FI).  By means of the partners assigned in the contract, the system determines and posts customer or vendor accounts in FI-AR or FI-AP. </p>\n<p>d) Architecture</p>\n<p>If you want to display characteristics of the architectural view of a real estate, a structure of architectural objects is available in addition to the objects of the usage view mentioned above.  You can define these freely and use them independently of the company code at all hierarchy levels of the system.  Within the architectural view, you have the option of automatically creating and updating functional locations in SAP Plant Maintenance (OM/CS).  The connection between the architectural view and the usage view of real estate objects is comprehensively supported.  For example, you can assign several architectural rooms to one pooled space.  To define pooled spaces and rental spaces, you can refer to the architectural view.  For example, you can define the rooms of the architectural view that comprise a rental space or a pooled space.  This function is especially relevant for the management of real estates for your own use (corporate usage), but this function is also interesting for externally rented buildings if the buildings are not rented as a whole entity. </p>\n<p>e) Correspondence</p>\n<p>To create correspondence from RE-FX, use SAP Smart Forms.  RE-FX no longer supports SAP script technology.  Sample correspondence cases are delivered by SAP for Smart Forms. Compared to the conventional SAP script technology, SAP Smart Forms have the advantage that you can design the letters in a graphic editor and you can display them immediately. As of SAP ERP 6.0, you can use PDF-based forms in the Flexible Real Estate Management (see below). </p>\n<p>f) Business partner</p>\n<p>RE-FX uses SAP Business Partner instead of the Treasury Business Partner used before.</p>\n<p>g) RE Navigator</p>\n<p>You can edit all master data objects, contracts, and settlement units in a central hierarchical view.  From this view, you can navigate across all objects. In addition, a variety of useful functions for the object processing is available here.</p>\n<p>h) Direct input or transfer tools for data from external systems and migration functions from RE-FX of Release 4.6C or lower:</p>\n<p>BAPIs are available to transfer data from external systems.  These BAPIs are linked to the Data Transfer Workbench (SXDA).  Migration tools for the automatic conversion of data from SAP Real Estate Management to SAP R/3 4.7 Core, SAP R/3 4.6C or lower (Classic RE) are not yet available, but you can set up a new client on the basis of this data as part of an implementation project. </p>\n<p>i) Input tax distribution</p>\n<p>You can now perform input tax distribution according to the option rate determined for each real estate object and post the relevant values in Financial Accounting.</p>\n<p>j) Reporting</p>\n<p>Reports for evaluating the occupancy, the measurements and conditions, the master data and the service charge settlement are available in the ECC (Enterprise Core Components).  To evaluate actual and target values from Controlling (CO), extractors are available for Real Estate Management, as well as InfoCubes, queries and workbooks in Business Warehouse (BW). Contract evaluations now support the selection according to partners and real estate objects.</p>\n<p>k) Service charge settlement</p>\n<p>Due to the new master data structure, you can settle contracts with several rental objects using the new service charge settlement.  The most important functions of the service charge settlement are supported in the same way as in Classic Real Estate.  Useful enhancements include the settlement in steps with a restart function, the simulation of service charge settlements with all evaluations, and the fixing of results without posting.</p>\n<p>l) Rent adjustment as free adjustment and as index adjustment</p>\n<p>You can find adjustment methods for the \"Free adjustment\" and \"Index rent adjustment\" methods in the system.  In addition to the main program, which controls the adjustment selection and adjustment processing, the correspondence templates required for the communication with your tenants are also available.</p>\n<p>m) Entry and settlement of sales-based rents</p>\n<p>You can define as many sales-based rent agreements as you want in a contract.  These agreements contain rules for determining sales-based rent (sales rules) and the sales to be reported (reporting rules).  You can use a sales report to calculate as many sales-based conditions as you want. The reporting rules can contain as many material groups (sales types) as you want.  In addition, you manage minimum rents and advance payments for sales-based rent as separate conditions.  To do this, functions for editing contract data, entering sales reports, as well as functions for the sales-based rent settlement, relevant reporting and correspondence templates for communication with tenants or landlords are available in the system. </p>\n<p>n) Security deposit management</p>\n<p>Using security deposit management, you can define different types of security deposit agreements, as well as specifying when, in which form and by whom the security deposits were provided.  In addition to security deposits that are predefined in the contract, you can calculate the agreed security deposit depending on the contract conditions (the rent, for example).  Furthermore, your agreement can state that the agreed security deposit must also be adjusted in the case of a condition adjustment (for example, when you increase a rent).</p>\n<p>o) Integration in SAP Controlling (CO)</p>\n<p>The system supports the update of actual values as well as manual planning of costs, activity inputs and revenues.  In addition, the standard CO copy functions and the transfer of values from the cash flow to CO plan values are available as planning aids.  The CO settlement with real estate object as sender and receiver is also available. Note that net costs are passed on only once for all CO settlement functions and assessment functions. (Proportional tax is taken into account only if it is contained in the sender object as cost). </p>\n<p>p) Reminders</p>\n<p>You can use the reminders functions for all real estate objects (not only for contracts).  In addition, the system automatically copies the reminder dates to SAP Appointment Calendar.</p>\n<p>2. Functional scope of Flexible Real Estate in SAP ERP 2004</p>\n<p>After the upgrade you must perform a conversion.  Refer to the release notes, in particular to \"Basic changes to the master data tables\" (RE_ECC500_BD_CHANGE) and SAP Note 681951.</p>\n<p>This release contains the following new functions:</p>\n<p>a) Accruals and deferrals (delivered in the Support Package)</p>\n<p>You can use the functions for accruals and deferrals to assign cross-period revenues or costs period-specifically in financial accounting.  Each accruals and deferrals run creates relevant documents about all items that belong to the previous period or the current period after the reference period.  This may created many documents. </p>\n<p>Therefore, for system performance reasons, we <span>strongly</span> recommend that you do not use the option of daily accruals and deferrals.</p>\n<p>b) Enhanced link between architectural view and usage view</p>\n<p>The optimized link between the architectural view and the usage view allows you to refer to the architectural objects (rooms, for example) and their measurement when you define rental spaces and pooled spaces. </p>\n<p>c) Reason for vacancy</p>\n<p>You can specify a time-dependent reason for vacancy for the posting parameters.  This enables you to call up alternative account determinations and cost center determinations for each reason for vacancy.</p>\n<p>d) Rent adjustment according to representative list of rents</p>\n<p>You can now also make rent adjustments according to representative lists of rents.  The delivery Customizing contains sample entries for local representative lists of rents. In comparison with Classic RE, the system can display qualified representative lists of rents, in addition to the already existing control table that you can use to display the special features of the relevant local representative list of rents.  The fixtures and fittings characteristics of the rental object were enhanced so that you can assign valuation points and valuation factors to them, depending on the assigned representative list of rents.</p>\n<p>e) Rent adjustment using comparative apartments</p>\n<p>Based on the main program that already exists, you can now also make rent adjustments using comparative apartments.  These comparative apartments can be both apartments from the owned real estate and external comparative apartments owned by other landlords.</p>\n<p>f) Sales grading agreement</p>\n<p>In the new data entry function for graduated rents, you can set the conditions based on the existing data about the number of gradings and the interval between the gradings. </p>\n<p>g) Contract measurements, distribution according to equivalence numbers</p>\n<p>You can define time-dependent measurement amounts for contracts, and you can then use these measurement amounts as calculation base for the condition amount.  Generally you specify these amounts for a specific object (real estate object, order, WBS element or cost center).  You can use the defined amount to calculate condition amounts or to distribute the condition amount that you want to post to the objects (distribution according to the equivalence number).</p>\n<p>For service charge settlement, contract measurements are significant if </p>\n<p>- for the apportionment method, you have to use a measurement type that depends usually on the contract (number of persons, for example).  You usually define these measurement amounts in the contract only, and not for the rental object.</p>\n<p>- for example, if the actual measurement amount for the rental object does not match the (historical) amount in the contract.  In this case, the service charge settlement ignores the measurement amount for the rental object and instead uses the measurement amount defined in the contract.  With a new rental, do not define this measurement amount in the contract so that the system uses the \"correct\" measurement amount for the rental object. </p>\n<p>h) Definition of sets (summary of objects according to criteria required)</p>\n<p>You can combine master data into sets. Sets are groups of objects that you want to reference in reporting and in CO (for example, when you apportion and plan costs).  Therefore, for example, you can define all objects that are relevant for you under a certain aspect once, and access them under this name in most evaluation programs and processing programs.  On the one hand, you can define sets in the RE Navigator, on the other hand, you can also use special reports, which you can schedule in the background so that the sets are automatically updated on a regular basis.</p>\n<p>i) Enhanced assignment of meters for the consumption-dependent service charge settlement</p>\n<p>As of SAP ERP 2004, you have additional options to assign meters:</p>\n<p>- Now you can also assign meters to settlement units and to master settlement units.</p>\n<p>- You can also assign meters to pooled spaces.  They are then distributed to the assigned rental spaces. </p>\n<p>j) Current occupancy principle for service charge settlement (country-specific requirement in Austria)</p>\n<p>As of SAP ERP 2004, you have the option of fully apportioning the service charges of a settlement period to the current tenant regardless of whether or not this tenant used the area in the period concerned.  This apportionment method applies to Austria.  For more information, see SAP Note 890267.</p>\n<p>k) Interfaces for posting in SAP Financial Accounting</p>\n<p>Up to now, essential functions of SAP Financial Accounting were not defined specifically for Real Estate Management.  As of SAP ERP 2004, the number of the real estate contract is included in the incoming payment transaction.  In addition, a new function is available for displaying the tenant account.</p>\n<p>l) Installment agreement and deferral</p>\n<p>To map an installment agreement, you can subsequently split an open item in Financial Accounting into partial amounts.</p>\n<p>m) Enhancement of the integration for SAP Plant Maintenance (PM/CS)</p>\n<p>In Release 1.10, you have the option of assigning functional locations from PM to the real estate objects of the usage view and architectural view. As of SAP ERP 2004, you have the following additional options:</p>\n<p> - You can create malfunction reports for SAP Plant Maintenance directly from real estate objects.</p>\n<p>- From the real estate objects, you can display messages that have been entered and the resulting maintenance orders.</p>\n<p>- You can use a reporting enhanced with data from SAP Plant Maintenance in SAP Business Information Warehouse (BW).</p>\n<p>n) Enhanced integration with SAP Controlling (CO)</p>\n<p>As of SAP ERP 2004, you have additional options for using the integration with SAP Controlling:</p>\n<p>- The apportionment functions of CO are available for real estate objects.</p>\n<p>- You can transfer measurements of real estate objects or real estate contracts to CO as statistical key figures.</p>\n<p>o) Integration with SAP Asset Accounting and SAP Project Management</p>\n<p>You can link the RE-FX usage objects to assets from SAP Asset Accounting or to the WBS elements from the SAP Project System.  You can evaluate these master data links in BW Reporting. </p>\n<p>p) Standard evaluations in reporting</p>\n<p>In reporting, the following evaluations are now also available:</p>\n<p>- CO line items</p>\n<p>- Security deposit agreements with actual security deposits </p>\n<p>- Notice and renewal </p>\n<p>q) Enhancements in BW Reporting</p>\n<p>As of SAP ERP 2004, you have additional evaluation options in BW:  </p>\n<p>- Costs for maintenance orders (SAP Plant Maintenance (PM))  </p>\n<p>- Depreciation for real estate objects (SAP Asset Accounting (FI-AA))  </p>\n<p>- Costs for internal orders of RE objects (SAP Controlling (CO))</p>\n<p>r) Invoice printout</p>\n<p>You can now print the rent invoice from the periodic postings and send it to the tenants.  The invoice document items are grouped together. The system assigns the invoice number on the basis of the RE document.</p>\n<p>s) Quarter days (relevant in UK)</p>\n<p>In the UK and in some other countries it is usual to divide the annual rent into equal partial amounts, which are to be paid for periods of different length.  In the UK, these fixed periods are called quarter days.  For details about this function and the restrictions, see SAP Note 914067. </p>\n<p>t) Archiving</p>\n<p>You can use the standard SAP tools for archiving to archive RE documents and cash flows. A simple deletion function for master data is also available. You can use this function to delete master data that you inadvertently created as long as no dependent structures use these objects.</p>\n<p>3. Functional scope of Flexible Real Estate in SAP ERP 6.0</p>\n<p>a) Migration functions from Classic Real Estate to Flexible Real Estate</p>\n<p>As of SAP ERP 6.0, tools are available to migrate your data from Classic Real Estate to Flexible Real Estate automatically to a large extent. </p>\n<p>b) Adjustment measure</p>\n<p>The adjustment measure provides the option of defining new rents or rent increases for any rental objects and to perform an adjustment based on your definitions.</p>\n<p>In particular, the adjustment measure permits adjustments of conditions due to the following: </p>\n<p>- Modernization measures  </p>\n<p>- Expert opinions  </p>\n<p>- Special assessments (Condominium Owners Association (COA))  </p>\n<p>You can use the adjustment measure if you specified the rent for certain objects according to defined procedures and you want to announce and perform an adjustment to this rent.  You can limit the rent adjustment by a voluntary rent waiver. To do this, define capping provisions and limits.  If the limits are exceeded, the system creates a <strong>waiver condition</strong> and assigns it to the rental objects or to the contracts.</p>\n<p>c) Real estate search</p>\n<p>The real estate search allows you to adjust rental requests with vacant objects and vice versa.  From the rental request, you can create lease abstracts, contract offers and lease-outs.  You can use the following individual functions: </p>\n<p>- You can create <strong>RE search requests</strong>.</p>\n<p>- You can transfer rental objects to <strong>offered objects</strong>.</p>\n<p>- You can search for a suitable offered object for an RE search request.</p>\n<p>- You can search for a suitable RE search request for an offered object.</p>\n<p>- You can print out a lease abstract.  </p>\n<p>- You can create and edit a <strong>contract offer</strong> for a rental object.</p>\n<p>- You can create a real estate contract from the contract offer.</p>\n<p>d) Input tax treatment of CO objects</p>\n<p>You can now also perform the input tax distribution for real estate-related costs that are not assigned directly to the real estate object, but to other objects.  These objects (functional location, WBS elements, CO orders, and so on) can be linked directly or indirectly to real estate objects.  For example, maintenance orders are generally linked using a functional location assigned to the real estate object.  You can assign internal orders and projects directly to the real estate object.  The rules that apply to the real estate objects also apply to the input tax distribution of such real estate-related costs.  For the assigned real estate object, define an option rate method that you can use to determine the option rates.</p>\n<p>e) One-time postings</p>\n<p>It is easier to use one-time postings than the interface of Financial Accounting (FI) to enter documents, and this method is adjusted to suit the requirements of Real Estate Management.  Therefore, the system hides the complexity of the posting process for the end user.</p>\n<p>For one-time postings, you must first define <strong>posting activities</strong> in Customizing. In the application, the person responsible specifies the posting activity and the company code.  Using the settings in Customizing for the posting activity, the system creates one or more documents that the person responsible then completes, for example by entering the invoiced amount or the actual real estate object.  You can use the one-time postings, for example, for the following postings:</p>\n<p>- Posting vendor invoices with reference to real estate objects  </p>\n<p>- Posting one-time receivables for customer contract partners  </p>\n<p>- Posting costs of the COA  </p>\n<p>These are the postings contained in the standard system.</p>\n<p>f) Tax summarization for FI documents</p>\n<p>You now have the option of summarizing the tax items within FI documents.</p>\n<p>This is a legal requirement in different countries (including Austria and Italy).</p>\n<p>The system summarizes line items according to the following criteria:</p>\n<p>- Underlying tax code  </p>\n<p>- Tax category of the account (G/L account or reconciliation account)  </p>\n<p>- Tax jurisdiction key (if this exists) </p>\n<p>- Account type, gross indicator and net indicator of the condition  </p>\n<p>- Currency  </p>\n<p>The system creates a summation tax line for each of these summarized lines.  In particular, tax items on G/L account lines and on subledger account lines are displayed separately. </p>\n<p>g) FI-CA integration (with restricted release)</p>\n<p>By integrating FI-CA, PS-CD customers (contract accounts receivable and payable for public sector) have the option of using their open item account accounting for real estate contracts also. If you use this integration, the system posts the postings (that you usually perform using the FI accounts receivable accounting (FI-AR)) to contract accounts in PS-CD. In this case, a contract partner can have a separate account for each real estate contract.</p>\n<p>Prerequisites:</p>\n<p>- You activated the Public Services business function set (EA-PS).</p>\n<p>- An activation for each company code is possible.</p>\n<p>Note that we have not yet released this function for use.  We will not release this function until after a current pilot project has been completed.</p>\n<p>h) Surcharges for the service charge settlement</p>\n<p>You can now calculate surcharges in the service charge settlement and add these to the settlement result.</p>\n<p>This procedure is in particular useful in the following cases:</p>\n<p>- Apportionment loss risk (price-controlled living space in Germany) </p>\n<p>- Management costs surcharge (country specifications for Switzerland) </p>\n<p>This is calculated for all rental objects.  The amount of the surcharge varies depending on whether it is an internal or external contract.</p>\n<p>i) Enhanced integration with SAP Controlling (CO)</p>\n<p>The indirect activity allocation provides the option of automatic activity allocation (similar to the apportionment) on the basis of actual values, statistical key figures, fixed portions, and so on. </p>\n<p>In addition, you can now indirectly determine the activity quantity of a sender from the activity inputs of the receivers. </p>\n<p>i) Enhancements in reporting</p>\n<p>In the SAP information system, the following additional reports are available:</p>\n<p>- Line item vendor, accruals and deferrals, cash flow  </p>\n<p>- Evaluations for the land use management  </p>\n<p>- CO reports </p>\n<p>By means of SAP query, you can use CO reports for costs and revenues and for statistical key figures for the first time in the Online Transaction Processing system (OLTP).  (Previously, these reports were available in SAP BW only.)</p>\n<p>k) Correspondence</p>\n<p>As of SAP ERP 6.0, you can use PDF-based forms in Flexible Real Estate Management. PDF-based forms are provided with the Adobe solution \"Interactive Forms\". You can use them as an alternative to SAP Smart Forms. Therefore, the SAP Smart Form sample forms delivered in SAP ERP 2004 are also available in SAP ERP 6.0 as PDF-based forms. In addition, the following new correspondence cases are contained in SAP ERP 6.0:</p>\n<p>- Lease abstract and contract offer in Real Estate Search</p>\n<p>- Master data summaries for land use management </p>\n<p>- COA settlement, annual budget and condominium ownership settlement</p>\n<p>If you use individual Smart Forms that already exist, you can continue to use these without any changes.  However, keep in mind that as of SAP ERP 6.0, we no longer develop SAP Smart Form sample forms that were previously delivered in Flexible Real Estate Management and no longer deliver the new correspondence cases specified above as SAP Smart Forms.  However, you can still develop your own forms using SAP Smart Forms.</p>\n<p>In RE-FX, you can set whether you want each form to be a Smart Form or a PDF-based form. If you went live before SAP ERP 6.0, for example, you can continue to use Smart Forms for the previously used forms and use PDF-based forms only for new correspondence cases.</p>\n<p>Keep in mind that Smart Forms and PDF-based forms use completely different technology. To decide which technology you would like to use, familiarize yourself with the special features of the two tools.  For information about restrictions on the use of PDF technology, see SAP Notes 766410, 894389 and 1009567 (and related SAP Notes).</p>\n<p>Refer especially to the section \"High-volume printing\" in SAP Note 894389. This section explains that SAP Interactive Forms by Adobe is not released for productive mass printing scenarios because of performance problems with some print forms that were delivered with SAP ERP 6.0. Therefore, if you have correspondence cases such as the service charge settlement for which mass documents were created at a particular time, you must first evaluate whether you can use PDF-based forms.</p>\n<p>l) Separation of assets, third party management, management of condominium owners' associations (COA) (released for Germany only)</p>\n<p>Using third party management, you can manage real estate of third party owners as of SAP ERP 6.0.  As a legal basis, the owner must create a mandate.  The system displays each mandate and the dependent real estate objects and real estate processes in a separate company code.  In this way, the mandate forms the basis of the separation of assets and the independent settlement.  The COA mandate is a special form of the mandate.</p>\n<p>For COA management, you can use the following functions:</p>\n<p>- For COA management, you can create annual budgets, manage assessment contracts, and perform accounting and COA settlements. </p>\n<p>- For the management of contract relationships between owner and tenant (individual condominium management), you can manage lease-outs and perform accounting and service charge settlement for the tenants.</p>\n<p>m) Land Use Management, management of parcels, tracts of land and local subdistricts (released only for Germany)</p>\n<p>You can now manage master data and processes from the area of Land Use Management. This includes: </p>\n<p>- Land register pages from the land register, including ownership relationships, types of possession and associated rights, charges, restrictions and liens</p>\n<p>- Parcels from the real estate cadaster</p>\n<p>- Entries from other public registers such as development plans, easements, contaminations of sites and so on</p>\n<p>- Contract management using changes of holdings such as purchase and land lease</p>\n<p>- Contract management for the holdings usage such as rent and leasehold</p>\n<p>Land Use Management is integrated with the following components:</p>\n<p>- Asset Accounting (FI-AA) for the valuation of land for accounting purposes</p>\n<p>- Financial Accounting (FI) and Controlling (CO) for the posting of property taxes and fees and contracts</p>\n<p>- Plant Maintenance (PM/CS) for the management of technical activities </p>\n<p>Note that this function was defined according to German law only. Therefore, it is released for use in this jurisdiction only.  There is no plan to release this function for other countries.</p>\n<p>n) Data Retention Tool for RE-FX</p>\n<p>Income tax laws require you to retain certain financial documents for the tax return in sequential file format. As of SAP ERP 6.0, the Data Retention Tool (DART) supports this requirement in the area of RE-FX.  Using this tool, you can extract master data and flow data to sequential files and display them.  By exporting the data, you can perform an evaluation using programs of third party suppliers.</p>\n<p>o) Connection to regulatory reporting for insurance companies (BaFin - German Federal Financial Supervisory Authority - connection)</p>\n<p><br/>As of Support Package 6 and higher for SAP ERP 6.0, the connection to regulatory reporting for insurance companies (BAFin connection) is available according to German requirements.</p>\n<p>p) Extended withholding tax</p>\n<p>Extended withholding tax is available for Spain and Portugal. For more information, see SAP Note 771098.</p>\n<p>q) Input tax correction according to German sales/purchase tax law (UStG § 15a) (correction items)</p>\n<p>The function is generally available in the standard system as of November 2008 for Release ERP 6.0 and for all related Enhancement Packages. For details about the delivery, see SAP Note 1240658.</p>\n<p>Note that this solution only complies with German statutory requirements.</p>\n<p>According to German sales/purchase tax law (§15a UStG), the original input tax deduction must be corrected within the specified timeframe, in case the principles for the assumed input tax deduction opportunity are changed following an input tax deduction.</p>\n<p>The input tax correction is performed for the first time after the start of the actual usage of the fixed asset. The input tax is corrected depending on the changes in the option rate. This means that over a period of up to ten years, the pro rata difference is posted between the actual input tax deducted and the input tax that is liable for deduction according to the current option rate.</p>\n<p>Note that this solution only complies with German statutory requirements.</p>\n<p>r) Archiving enhancements</p>\n<p>You can now archive master data.</p>\n<p>4. Function scope of Flexible Real Estate in SAP Enhancement Package 2 for SAP ERP 6.0</p>\n<p>a) Long-term seating arrangements</p>\n<p>You can enter and plan long-term usage of rooms and other real estate objects by persons (for example, employees and contractors). You can settle the usage costs internally using Customizing or externally using the real estate contract.</p>\n<p>Note that no reversal function exists yet for the cost allocation in Enhancement Package 2.</p>\n<p>b) Move planning</p>\n<p>You can use this function to plan and activate moves of persons who reserve reservation objects on a long-term basis. You can also plan move-ins and move-outs of persons for whom no long-term arrangements have been made yet or who do no longer require a long-term arrangement. In addition, you can delegate moves of specific persons to other assignment planners.</p>\n<p>c) Room reservation</p>\n<p>You can use this process to make short-term reservations for rooms or other real estate objects or to reserve the rooms for single appointments or recurring appointments. In addition, you can place an order for additional services; for example, you can order lunches, additional chairs or equipment and settle the incurred costs accordingly. The cost allocation can be used for monitoring the costs.</p>\n<p>d) Master data: Graphical integration</p>\n<p>In addition to BAPIs, you can now use interfaces to integrate the display of graphics software (CAD, GIS).</p>\n<p>e) Rent adjustment according to cost efficiency analysis</p>\n<p>The rent adjustment according to cost efficiency analysis (CEA) is used to determine the cost rent for publicly funded accommodations. In this process, capital costs, current expenses and financing plans are taken into account. In addition, you can specify that the system considers specific amenities level factors (for example, advantages and disadvantages with regard to the location) for the cost efficiency analysis.</p>\n<p>f) Conditions in foreign currency</p>\n<p>You can define conditions for contracts, rental objects or contract offers that differ from the local currency of the company code. Depending on the translation rules and exchange rates, the system automatically carries out a translation within the different processes (for example, cash flow transaction, periodic postings, service charge settlement, and so on).</p>\n<p>g) Condition split</p>\n<p>You can distribute condition-based payments to several partners with a vendor account or partners with a customer account (for example, landlord, tenant and subsidizer). In particular, these requirements apply for the USA (key word: \"multiple vendors\").</p>\n<p>h) Enhancements for one-time conditions</p>\n<p>You can define a validity period for one-time conditions and manually enter a due date that is different from the frequency term. The validity period can also be used to carry out accruals and deferrals for one-time conditions.</p>\n<p>i) Posting of sales-based settlement using cash flow</p>\n<p>In the sales-based rent agreements of the real estate contract, you can specify that the receivables from the sales-based rent are also transferred to the cash flow and therefore posted using periodic postings.</p>\n<p>j) Accrual/deferral of service charges costs that have not been settled yet (unfinished services)</p>\n<p>This function to allows you to enter accruals/deferrals for the balance sheet of service charges that have not been settled yet. In addition to determining the amounts to be accrued, you can correct and post the amounts and dissolve the accruals/deferrals that were carried out.</p>\n<p>k) Contract: Different posting term for service charge settlement (changed)</p>\n<p>Unlike the posting rules of your service charge advance payments, this function allows you to use your own payment methods, dunning data, account determinations, and so on, when posting the results of a service charge settlement.</p>\n<p>l) Enhancements of CO integration for Funds Management (FM)</p>\n<p>When you transfer plan cash flows to cost element planning, the FM account assignments can now be applied. Using the relevant FM function, you can transfer the planned values from CO to the relevant FM account assignments.</p>\n<p>m) Integration to cash management and forecast</p>\n<p>Cash management and forecast provides a liquidity forecast of the receivables and payables resulting from real estate contracts. This forecast displays the planned items resulting from the cash flow. During periodic postings in RE-FX, these items are transferred to cash management and forecast.</p>\n<p>n) Accrual/deferral of tax amounts</p>\n<p>In addition to the net amounts, you can also accrue the tax amounts of conditions. Accrued amounts can be posted directly to tax accounts or clearing accounts.</p>\n<p>o) Info system: Item overview for several contracts</p>\n<p>Using the new item overview report, you can report open and cleared items for several customer contracts.</p>\n<p>p) Other enhancements of the real estate contract</p>\n<p>- Multiple assignment of objects: When you process real estate contracts, you can assign a rental object to several object groups so that the periods overlap.</p>\n<p>- Locking critical data for backdated changes: You can lock fields of the contract processing to avoid any backdated cash flow-relevant changes that would lead to subsequent receivables or credit memos.</p>\n<p>q) Country specifications for Austria: VAT calculation for condominium owners' associations (COA)</p>\n<p>This enhancement allows you to calculate the sales tax for expenses from the maintenance reserve of rental objects of a COA. You can pass on the amounts to the individual owners.</p>\n<p>5. Function scope of Flexible Real Estate in SAP Enhancement Package 3 for SAP ERP 6.0</p>\n<p>The Real Estate functions delivered with SAP Enhancement Package 3 for SAP ERP 6.0 contain enhancements that were implemented within the integration to Funds Management.</p>\n<p>a) Funds reservation with approval workflow</p>\n<p>You can use this approval workflow to display an approval procedure for funds that are fixed over a longer period of time using lease-ins.</p>\n<p>b) Creating plan values with regard to Funds Management (FM) account assignments from contracts</p>\n<p>The existing function for creating CO plan data from the cash flow of real estate contracts were enhanced so that FM account assignments (funds, grants and functional area) are transferred to CO plan data.</p>\n<p>c) Simulation of FM account assignment derivation</p>\n<p>In the overview for the contract, a new report is available that displays the simulated derivations of FM account assignments.</p>\n<p>6. Function scope of Flexible Real Estate in SAP Enhancement Package 4 for SAP ERP 6.0</p>\n<p>The Real Estate functions delivered with SAP Enhancement Package 4 for SAP ERP 6.0 are grouped into Commercial and Corporate Real Estate Management, which can be activated using the business functions RE_GEN_CI_2 and RE_CRE_MISC.</p>\n<p>Within Commercial Real Estate Management, the following enhancements have been made:</p>\n<p>a) Enhancements to land use management (country-specific)</p>\n<p>- New \"Function\" field in the parcel</p>\n<p>This field allows you - in the same way as the function for usage objects - to implement an additional attribute assignment of parcels with custom characteristics.</p>\n<p>- Simplified key for parcel</p>\n<p>An option is provided to use a simplified key for parcels that is not subject to cadastral classifications.</p>\n<p>- New reports</p>\n<p>Various new reports are available.</p>\n<p>b) Enhancements to the rent adjustment according to the cost efficiency analysis</p>\n<p>- Change management of expense items</p>\n<p>A transaction is provided for the mass change of current expenses of adjustment measures for the cost efficiency analysis.</p>\n<p>- Reporting</p>\n<p>Various reports are available so that you can execute general evaluations for several cost efficiency analyses.</p>\n<p>c) Enhancements to contract and conditions</p>\n<p>- Possession dates for rental objects </p>\n<p>In addition to the runtime definition of contracts and contract offers, you can define possession dates for contract objects. The possession dates define the period in which the object is released for usage. The start possession date may be before the contract start; the end possession date may be after the contract start.</p>\n<p>- Requested notice date</p>\n<p>If notice is given for contracts, you can enter a requested notice date that is before the expiration of the regular period of notice. This date is also taken into account in the real estate search.</p>\n<p>d) Enhancements to the service charge settlement</p>\n<p>- Rounding of receivable amount or credit balance amount</p>\n<p>For service charge settlements for which receivables and credit balances from advance payments are posted in an amount, you can define whether and how the result is to be rounded.</p>\n<p>- New status monitor for settlement periods of settlement units</p>\n<p>- Cross-settlement evaluations for service charge settlement</p>\n<p>e) Business Intelligence</p>\n<p>During the extraction of master data and flow data from flexible Real Estate Management to Business Intelligence, the system transfers additional master data attributes for usage objects (for example, the reason for vacancy of a rental object). New DataSources are also available for extracting data in Business Intelligence.</p>\n<p>f) Other enhancements</p>\n<p>- Periodic postings: Selection according to condition type and condition purpose</p>\n<p>- Evaluations for sales-based settlement</p>\n<p>The display has been adjusted to the general output of evaluations in RE-FX and therefore provides flexible enhancement options and options for the time slot method.</p>\n<p>- Reporting: Option rate data for usage objects</p>\n<p>A report is available for evaluating option rate relationships for real estate objects. This data can also be supplied for evaluations according to the German Principles of Data Access and Verifiability (GDPdU).</p>\n<p>- BAdI for option rate determination</p>\n<p>- Input tax correction (country-specific)</p>\n<p>If you change the principles for the originally assumed input tax deduction option after an input tax deduction, you can adjust the deducted value using the input tax correction. The input tax correction is performed at the earliest after the actual usage of the fixed asset and is dependent on the option rate change.</p>\n<p>- Filter in management of time slots</p>\n<p>In a report, regardless of the original selection, you can take different time intervals into account without starting the report again.</p>\n<p>- RE Navigator</p>\n<p>For a real estate object, the system now displays the existing registrations, and a copy function using drag and drop is available in the navigation bar.</p>\n<p>Within Corporate Real Estate Management, the following enhancements have been made:</p>\n<p>g) Degrees of occupancy for continuous occupancy</p>\n<p>For persons who occupy more or less than one location continuously (for example, part-time employees), you can specify a degree of occupancy for continuous occupancies. The degree of occupancy directly affects the utilization of reservation objects and is taken into account during the price calculation. If persons occupy more than one location, the BAdI BADI_REOR_PO_OCCQUOTE must be implemented.</p>\n<p>h) Occupancy evaluation and vacancy evaluation</p>\n<p>You can use this report to execute detailed evaluations for continuous occupancy. You can display an overview of the current and historical occupancy, the utilization of capacities and the existing vacancies.</p>\n<p>i) Different move dates</p>\n<p>In the case of a move, you can define individual move dates for all persons moving. This means that you no longer have to create a move plan for each move-out date or move-in date.</p>\n<p>j) Postings for continuous occupancies and reservations</p>\n<p>You can now use two separate transactions for the posting of reservations and continuous occupancies. In particular, transactions are provided for reversing costs of continuous occupancies and reservations. The new transactions can be called simultaneously for several reservation objects.</p>\n<p>7. Functional scope of Flexible Real Estate in SAP Enhancement Package 5 for SAP ERP 6.0</p>\n<p>a) Tax transfer in the service charge settlement</p>\n<p>For input tax opting tenants, in the service charge settlement, you can pass on the exact amount of input tax from the original document as value-added tax (VAT) - for example, full VAT rate for services, reduced rate for energy or none for property taxes and fees - proportionally in the service charge settlement.</p>\n<p>b) Surcharges based on contract conditions in the service charge settlement</p>\n<p>In the service charge settlement, you can levy surcharges that are determined as a percentage of any contract conditions you choose.</p>\n<p>c) Heating days for consumption-independent costs in the service charge settlement</p>\n<p>In the service charge settlement, you can now also distribute consumption-independent costs, such as those for the maintenance of heating, in a weighted manner according to heating days in the case of a tenant changeover.</p>\n<p>d) Direct posting from the real estate contract</p>\n<p>You can now create periodic postings for the current contract directly from the real estate contract in order to ensure prompt correction of the open items in the case of contract changes.</p>\n<p>e) Integrated posting</p>\n<p>To reduce the volume of documents, you can now group postings from the partner-related cash flow and from the object cash flow in one document.</p>\n<p>f) Accruals and deferrals by freely definable posting periods</p>\n<p>Accruals and deferrals were previously always performed on the basis of calendar months. You now have the option of accruing and deferring by posting periods that do not correspond to calendar months. This enhancement is available with SAP Note 1428133 for ERP 6.0 and higher and all available enhancement packages.</p>\n<p>g) Time-dependent assignment of rental objects to COA objects in the third-party management</p>\n<p>You can now make time-dependent assignments of rental objects from object mandates to COA objects. This enables you to assign more than one object from the manager company code (or from an object mandate) and from the condominium owner company code respectively in the case of rental objects. SAP Note 1396584 already provides this enhancement for Enhancement Package 4 for SAP ERP 6.0.</p>\n<p>h) Defining capping rules at the start of the rent adjustment</p>\n<p>In addition to the procedure-dependent capping rules, you can also define individual capping rules at the start of the adjustment run.</p>\n<p>8. Functional scope of flexible Real Estate in SAP Enhancement Package 6 for SAP ERP 6.0</p>\n<p>a) Integrated planning for real estate objects</p>\n<p>Planned activities for real estate objects can now be transferred directly to cost center planning as scheduled activities. In the process, the plan value is calculated from the activity type and the rate defined for the cost center.</p>\n<p>b) Indexed sales grading</p>\n<p>Sales grading in the sales-based rent agreement can now be coupled with an index rule and adjusted as part of the index-linked rent adjustment.</p>\n<p>c) SEPA mandates</p>\n<p>The SAP module for processing SEPA debit memos and SEPA bank transfers within the Single Euro Payments Area (SEPA) has now been enhanced for Real Estate requests.</p>\n<p>The following enhancements have been made for customer activities, in particular:</p>\n<ul>\n<ul>\n<li>Managing the SEPA mandate in the posting term in the contract</li>\n</ul>\n</ul>\n<ul>\n<ul>\n<li>Integrating the SEPA mandate into the payment data for a one-time posting</li>\n</ul>\n</ul>\n<ul>\n<ul>\n<li>Enhancing the bank details for the SAP business partner</li>\n</ul>\n</ul>\n<ul>\n<ul>\n<li>Storing the SEPA mandate in the RE document</li>\n</ul>\n</ul>\n<p>For more detailed information about this, see SAP Note 1678321.</p>\n<p>d) Enhancements for one-time postings (RERAOP)</p>\n<p>Postings with net amounts and statistical account assignments are now also possible for one-time postings.</p>\n<p>e) Contract-independent option rate</p>\n<p>Currently, all objects rented in a contract are subject to the same option rate. This means that the option rate of a rental object is basically determined depending on the occupancy contract.</p>\n<p>Since it can be useful in some cases not to pass on the option rate of the contract to the object, you can now control if the occupancy contract is not to be taken into account during the determination of the option rate of the rental object.</p>\n<p>For more detailed information about this, see SAP Note 1596008.</p>\n<p>f) Service charge group in reports and search helps for service charge settlement (SCS)</p>\n<p>The service charge group has been added to the reports for the SCS. In addition, the service charge group has also been integrated into the search help for the master data for the SCS.</p>\n<p>For more detailed information about this, see SAP Note 1529396.</p>\n<p>g) Implementation of the \"Date Until Which the Cash Flow Is Locked\" field</p>\n<p>By setting this date, you can prevent planned records from originating for past periods during cash flow generation.</p>\n<p>For more detailed information, see SAP Note 1635539.</p>\n<p>h) Performance improvements</p>\n<p>For the following time-intensive processes, parallel processing has been enabled:</p>\n<ul>\n<ul>\n<li>Periodic Posting: Contracts (RERAPP)</li>\n</ul>\n</ul>\n<ul>\n<ul>\n<li>Reverse Contract Postings (RERAPPRV)</li>\n</ul>\n</ul>\n<ul>\n<ul>\n<li>Periodic Posting: Rental Objects (Vacancies) (RERAVP)</li>\n</ul>\n</ul>\n<ul>\n<ul>\n<li>Reverse Vacancy Postings (RERAVPRV)</li>\n</ul>\n</ul>\n<ul>\n<ul>\n<li>Input Tax Distribution (REITDS)</li>\n</ul>\n</ul>\n<p>i) SAP Business Information Warehouse (BW)</p>\n<p>Migration of data flow under 3.x to SAP BW 7.0 technology</p>\n<p>j) Localization for France</p>\n<p>For more detailed information about this, see SAP Note 1561158.</p>\n<p>k) SAP Common Area Maintenance Expense Recovery (CAM)</p>\n<p>Mapping the service charge settlement according to the USA standard</p>\n<p>There is now an add-on solution for this. For more detailed information about this, see SAP Note 1610085.</p>\n<p>9. Functions that are not included</p>\n<p>In SAP ERP 6.0 and SAP Enhancement Packages for SAP ERP 6.0, the functions listed below are not provided in RE-FX. This list is not final. If functions are missing from this list, you cannot assume that they are contained in the delivery. This list does not mean that these functions will be available in subsequent releases.</p>\n<p>a) Rental forecast</p>\n<p>b) Country-specific processing</p>\n<p>Country-specific legal requirements in real estate law - if they are not mentioned individually.  For information about country-specific releases, languages and localization, see SAP Note 771098.</p>\n<p>c) Proportional clearing of split advance payments in the service charge settlement</p>\n<p>This is the pro rata distribution of the amount paid in advance if the calculation period of the advance payment exceeds a settlement period.</p>\n<p>d) Terms of use for multiple usage</p>\n<p>e) Completion of the PS-CD connection</p>\n<p>f) Mapping of the official or public law view of Real Estate Management, including the management of tasks of the survey office and land registry and the calculation of property taxes and other taxes and fees such as agricultural subsidies and so on.</p>\n<p>g)<strong></strong> Subsequent implementation of a further accounting principle. Note that implementation of a further (subsequent) accounting principle (as a separate ledger) is currently not supported for RE-FX using the <em>\"Subsequent Implementation of a Further Accounting Principle\"</em> tool. For more information, see the following documentation: <a href=\"https://help.sap.com/doc/57c2fc57bde70f70e10000000a44147b/3.6/en-US/frameset.htm\" target=\"_blank\">https://help.sap.com/doc/57c2fc57bde70f70e10000000a44147b/3.6/en-US/frameset.htm#</a></p>\n<p> </p>\n<p> </p>\n<p> </p>\n<p> </p>", "noteVersion": 82, "refer_note": [{"note": "1026487", "noteTitle": "1026487 - Solution Manager in RE-FX", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>You require information about the Solution Manager regarding the SAP real estate solution RE-FX.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>Real Estate, RE-FX, Solution Manager</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>None</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>The relevant information dating from February 2007 is contained in the attachment.</p></div>", "noteVersion": 5}, {"note": "1804012", "noteTitle": "1804012 - EHP7: Control of reports for service charge settlement", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>Enhancement Package 7 gives you the option of activating the new service charge settlement (SCS) report versions without having to activate the subfunction SC01.<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>RESCIS, SCS, service charge settlement, COA, condominium owners' association, COA settlement, owner settlement, accrual/deferral for operating costs, annual budget settlement, IMG, Implementation Guide</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>The relevant function is missing.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>The correction is delivered in Support Package 1 for Enhancement Package 7.<br/>IMG path for activation:<br/>\"Flexible Real Estate Management (RE-FX) -&gt; Service Charge Settlement -&gt; Define Accrual/Deferral Reporting\"</p></div>", "noteVersion": 1}, {"note": "1670535", "noteTitle": "1670535 - RES and REfx - facts and updates", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>You want additional clarity about RES, REM and RE-fx</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>Refer to the attachment for detailed content.</p></div>", "noteVersion": 1}, {"note": "894389", "noteTitle": "894389 - <PERSON><PERSON><PERSON>r.: SAP NW 7.0 - Adobe Document Services", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>Release restrictions of SAP NetWeaver 7.0 - Adobe document services</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>At the time of the release of SAP NetWeaver 7.0 limitations existed concerning the productive usage of certain functions of the Adobe document services. This note provides customer information on these restrictions.<br/><br/>As Interactive Forms is not released for all platforms that are released for SAP Netweaver Application Server, restrictions regarding the availability of the Adobe document services in SAP NetWeaver 7.0 on specific platforms are documented in the Product Availability Matrix.<br/>For details about supported operating systems for Adobe document services in SAP NetWeaver 7.0, go to the SAP Support Portal at http://service.sap.com/~form/sapnet?_SHORTKEY=01100035870000651812&amp;.<br/><br/></p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p></p> <b><b>Open Limitation(s)</b></b><br/> <p></p> <b><b>XX-PART-ADB</b></b><br/> <p></p> <b> <b>Release with approval of SAP or after consultation with SAP</b></b><br/> <p><b>Right to Left Language Support</b><br/>The direction of the text flow from right to left is supported for right-to-left languages such as Hebrew, Arabic etc as of NetWeaver 7.0 Support Package 6 (see Note 886572). However, the layout of a translated form is not automatically adapted in   the right to left direction i.e the form layout continues to flow from  left to right. No mirroring of the form is supported. A Right-to-left form can be designed from scratch in the appropriate language (e.g Hebrew, Arabic, etc). However, note that when designing forms for these languages, SubForms don't support the flow direction right to left.<br/>( Changed at 11.11.2008 )<br/><br/><b>Size of a single document for printing</b><br/>At this time, the maximum size of output for an individual document that  can be generated by SAP Interactive Forms by Adobe is approximately 1000 pages on a Microsoft Windows server and approximately 2000 pages on  a Unix or Linux server. Although a batch run may be much more than this  limit, no individual document within the batch run may exceed the limit . In general, SAP and Adobe recommend that you should not plan on more than 800 pages per output document on Microsoft Windows and 1800 pages on Unix/Linux. The actual limit will vary based on form complexity and data complexity. For example, form designs that use choice subforms will  be limited to a smaller number of pages than forms that do not use choice subforms. (For more information on choice subforms, see the Adobe  LiveCycle designer documentation.) Even the generation of large documents of several hundreds of pages may be an issue, because the data  stream is transmitted as part of a SOAP message causing possible memory bottlenecks during the parsing of the XML document.<br/>( Changed at 25.09.2008 )<br/><br/></p> <b> <b>Release with restrictions</b></b><br/> <p><b>Potential data security issue due to local copies of PDF files</b><br/>When you call Adobe Reader or Adobe Acrobat in SAP applications, Microsoft Windows stores the PDF file that the application displays in the Temporary Internet Files directory. After you exit the application, the PDF file is not necessarily deleted. If the PDF document contains confidential information, this may constitute a security risk.<br/>( Changed at 19.01.2009 )<br/><br/></p> <b> <b>No release</b></b><br/> <p><b>Special features of Smart Forms text modules and Smart Styles</b><br/>When you integrate Smart Forms text modules and Smart Styles into fields  on PDF forms based on SAP Interactive Forms (for print and interactive use), certain special features of Smart Forms are not supported. The unsupported features currently known are: 1. When you use  text modules to fill concatenated floating type text fields in your form template, page protection is not available. 2. It is not possible to set the indent for the numbering of paragraphs in an outline as used in the Smart Style Builder. Certain word-processing features are not available. The unsupported features currently known are: 1. In static texts in a form template there is no tab positioning, no paragraph numbering, and no indenting. For more information see Note 1009567.<br/>( Changed at 25.09.2008 )<br/><br/></p> <b><b>Fixed Limitation(s)</b></b><br/> <p></p> <b><b>XX-PART-ADB</b></b><br/> <p></p> <b> <b>Release with restrictions</b></b><br/> <p><b>Browser support</b><br/>When ActiveX components are used for the communication between Adobe  Reader and a Web Dynpro application running in a browser, integration of Interactive Forms in Web Dynpro is only supported for Microsoft Internet Explorer. When ZCI is used, integration of Interactive Forms in Web Dynpro for Java is supported for Microsoft Internet Explorer or Firefox. For Web Dynpro for ABAP, see Note 1098009. Limitation only valid for use with ACF</p> <b>This restriction is no longer valid.</b><br/> <p>Fixed with the use of ZCI instead of ACF<br/>( Changed at 25.09.2008 )<br/><br/></p> <b> <b>No release</b></b><br/> <p><b>No use of digital signatures and certification in dynamic PDF forms</b><br/>SAP strongly recommends not to use digital signatures and certification in dynamic PDF forms. Dynamic PDFs can be used as of Adobe Acrobat 7 or Adobe Reader 7. The layout of a form of this type has a dynamic structure. The layout of the form can change at runtime, while it is being displayed in Adobe Reader. Dynamic layout changes can include, for  example, table rows being added to or removed from a purchase order, or  fields being revealed or hidden, as specified by a script. It is not guaranteed that Adobe Reader displays the correct document version that was signed digitally or certified. Limitation is fixed with the use of Adobe Reader 8.1 or later</p> <b>This restriction is no longer valid.</b><br/> <p>Limitation is fixed with the use of Adobe Reader 8.1 or later<br/>( Changed at 25.09.2008 )<br/><br/><b>Language support</b><br/>Interactive Forms supports all languages supported by SAP NetWeaver Application Server.</p> <b>This restriction is no longer valid.</b><br/> <p>Refer to SAP Note 0000886572<br/>( Changed at 07.11.2007 )<br/></p></div>", "noteVersion": 106}, {"note": "909490", "noteTitle": "909490 - RE-FX Country Version Restrictions for Korea", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>Missing localization of Flexible Real Estate Management for Korea</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>Real Estate, Flexible Real Estate, RE-FX, Localization, Restriction, Korea</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>SAP Real Estate Management is not localized for Korea</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>SAP Real Estate Management is currently available in two versions: Classic Real Estate (old version) and Flexible Real Estate (new version).<br/><br/>Both versions are <b>not</b> localized for Korea. The consequences of a missing localization are described in detail in note 771098.<br/><br/>Classic Real Estate will not be localized and developed anymore. Therefore, it is <b>not</b> released for new implementation projects.<br/><br/>A localization of Flexible Real Estate is currently planned for future releases. However, it is currently not decided which release of mySAP ERP will include this localization, when it will be available and what will part of this localization.<br/><br/>Please note that by default localization enhancements made in higher releases can not be downgraded on earlier versions. (E.g. a localization feature in mySAP ERP 2007 can not be downgraded to mySAP ERP 2005.)<br/><br/>The consequences are as follows:<br/><br/>SAP will <b>not</b> support SAP Real Estate Management in the area of legal or other requirements of customers in Korea - <b>neither</b> via OSS-messages <b>nor</b> via another channel - until a localized version of the solution will be general available in the future.<br/><br/>Only generic standard problems will be supported by SAP. The decision whether a problem has to be categorized as a standard or as a localization issue will be made by SAP.<br/><br/>All implementations of SAP Real Estate Management in Korea are made on own responsibility and risk of the customer and their consultants. SAP will not provide any support and coaching for these implementations.<br/><br/>All questions regarding localization and consulting support can be addressed to the local product manager for SAP Real Estate Management in Korea, Mr. Kun Hong Choi (<EMAIL>).<br/></p></div>", "noteVersion": 2}, {"note": "944578", "noteTitle": "944578 - RE-FX Country Version Availability and Restrictions for PL", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>Missing standard localization of Flexible Real Estate Management for Poland</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>Real Estate, Flexible Real Estate, RE-FX, Localization, Restriction, Availability, Poland</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>SAP Real Estate Management is not localized in standard SAP for Poland<br/>Solution</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>SAP Real Estate Management is currently available in two versions:<br/>Classic Real Estate (old version) and Flexible Real Estate (new version).<br/>Both versions are not localized in the standard SAP solution for Poland.<br/>The consequences of a missing localization are described in detail in note 771098.<br/><br/>Classic Real Estate will not be localized and developed anymore.<br/>Therefore, it is not released for new implementation projects.<br/><br/>A localization of Flexible Real Estate is currently planned for future<br/>releases. However, it is currently not decided which release of SAP ERP will include this localization, when it will be available and what<br/>will part of this localization.<br/><br/>Please note that by default localization enhancements made in higher<br/>releases can not be downgraded to earlier versions.<br/><br/>If you would like to use RE-FX for Polish companies you can order consulting service. Contact person is Maciej Kwasiborski, Consulting Manager (<EMAIL>).<br/><br/>The consequences are as follows:<br/><br/>SAP will not support SAP Real Estate Management in the area of legal or other requirements of customers in Poland - neither via OSS-messages nor via another channel - until a localized version of the solution will be generally available in the future.<br/><br/><br/>Only generic standard problems will be supported by SAP. The decision<br/>whether a problem has to be categorized as a standard or as a<br/>localization issue will be made by SAP.<br/><br/>******* PROPONUJE PONIZSZY TEKST USUNAC*********<br/>All questions regarding localization and consulting support can be<br/>addressed to the local product manager for SAP Real Estate Management in Poland, Agnieszka Paslawska (<EMAIL>).<br/>*******kONIEC USUWANIA***************<br/></p></div>", "noteVersion": 10}, {"note": "690900", "noteTitle": "690900 - User-defined real estate master data fields as of 470x200", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>You want to define user-defined fields for the real estate contract or another RE-FX master data table and display and change them in the maintenance dialog.<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>RECN, REBDBE, REBDPR, REBDBU, REBDRO, REBDAO, RESCPG, RESCSU, REAJCG<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p><b>Note:</b><br/>To reproduce the example of this note, you must have imported Support Package 04 into your system. It contains the sample function group REGC_EXT_EXAMPLE specified below.<br/><br/>The master data dialogs are implemented with the Business Data Toolset (BDT). The Business Data Toolset provides a large number of extension options. <b>Take care that you only use the extension options of the application interface in the context of the real estate master data.</b> To implement user-defined checks or save additional data in user-defined tables, do not use BDT events but the specific enhancement method for the real estate master data (see IMG documentation for the activity \"Implement enhancements (BADI)\" for the respective object type). Compared to the pure BDT solution this has the advantage that you can execute, for example, additional checks not only within the interface but also during the legacy data transfer and in case of BAPI calls.<br/><br/>In this example, the procedure for adding user fields is described for the contract. For business entity (BE), property (PR), buildings (BU), rental object (RO), architectural object (AO), settlement unit (SU), participation group (PG) and comparative group of apartments (CG, as of SAP ECC 5.0), you have to proceed accordingly. In the technical names (transaction, function group, table, ...) you must replace xx by the corresponding object abbreviation specified here in parentheses.<br/><br/>The BDT application object for the contract is called REGC, and the one for the other objects is called RExx.<br/><br/>The respective table names are partially different in Release 470x200 and subsequent releases. The list below contains the objects including their tables for Release 470x200 and higher:<br/></p> <div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\"><tr><th align=\"LEFT\">Object</th><th align=\"LEFT\"> Up to 470x200</th><th align=\"LEFT\"> Higher release</th></tr> <tr><td>Business entity</td><td> VIOB01</td><td> VIBDBE</td></tr> <tr><td>Property</td><td> VIOB02</td><td> VIBDPR</td></tr> <tr><td>Building</td><td> VIOB03</td><td> VIBDBU</td></tr> <tr><td>Rental object</td><td> VIMI01</td><td> VIBDRO</td></tr> <tr><td>Architectural object</td><td> VIBDAO</td><td> VIBDAO</td></tr> <tr><td>RE contract</td><td> VICN01</td><td> VICNCN</td></tr> <tr><td>Settlement unit</td><td> VIAK03</td><td> VISCSU</td></tr> <tr><td>Participation group</td><td> VIAK23</td><td> VISCPG</td></tr> <tr><td>Comparative group of apartments</td><td> not available</td><td> REAJCG</td></tr> <tr><td></td></tr> </table><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>The following example shows the steps required to include a new user field in the real estate contract and to maintain it in the contract dialog. The example is based on the example documented in Note 632104 for Release 470x110.<br/><br/>First of all, make sure that Note 691511 has been implemented in your system. Function group REGC_EXT_EXAMPLE is imported into your system with Support Package 04.<br/><br/>The Business Data Toolset structures customer enhancements and partner enhancements according to applications. In the RE environment, the application delivered by SAP has the same name as the BDT object (that is, REGC in the case of the contract). However, there may be additional applications - for example, the fast entry of the contract was implemented as application GCFE. By encapsulating applications that are functionally related, you can activate or deactivate them as required (for example, for test purposes).<br/>User-defined applications must start with Y or Z (applications by a partner start with X). Come up with a name for your application. In the following, abbreviation ZZZZ is used for the application.<br/></p> <ol>1. To include one or several fields in the master data table, call the ABAP Dictionary (Transaction SE11) and display master data table VICN01 / VICNCN. In the field list, select .INCLUDE CI_VICNCN (or .INCLUDE CI_VICN01 for Release 470x200) and press F2.<br/>If the structure does not contain any fields yet, the system asks you whether you want to create these fields. Confirm this question with 'Yes'.  You reach the maintenance screen where you can enter the required customer fields into the field list. The field names have to be in the customer namespace (starting with YY or ZZ), for example field YYID with data element YYID (also create \"File number\"). Activate the structure.<br/>In general, the name of the Include in which you have to include the user-defined fields is always CI_YYY, YYY is the name of the master data table.<br/>In this structure, only include fields that must be stored in the database. If you need more additional fields, create another structure for these fields in the ABAP Dictionary (the name must start with Y or Z).<br/></ol> <ol>2. Display function group REGC_EXT_EXAMPLE in Transaction SE80. Select the function group with the right mouse button and choose 'Copy'. In the customer namespace, enter a function group as a \"New function group\", for example ZZZZ_REGC_EXT.<br/>Also select the names for the new function modules in such a way that they are in the customer namespace, for example:<br/> <div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\"><tr><th align=\"LEFT\">Old function module</th><th align=\"LEFT\"> New function module</th><th align=\"LEFT\"> </th></tr> <tr><td>REGC_REGC_EVENT_FMOD2_EXAMPLE</td><td> ZZZZ_REGC_EVENT_FMOD2</td><td> </td></tr> <tr><td>REGC_REGC_PBO_EXAMPLE</td><td> ZZZZ_REGC_PBO_Z901</td><td> </td></tr> <tr><td>REGC_REGC_PAI_EXAMPLE</td><td> ZZZZ_REGC_PAI_Z901</td><td> </td></tr> <tr><td></td></tr> <tr><td>The template function group is generally called RExx_EXT_EXAMPLE, the function modules start with RExx_RExx. Bear in mind that your function modules must start with ZZZZ_RExx_ according to the BDT naming conventions (ZZZZ is the user-defined application defined, RExx is the application object enhanced by this application).<br/></td></tr> </table></div></ol> <ol>3. When you copy the function group, the system also copies a screen (subscreen). Its number in the template is 901. Change the layout of this screen so that you can maintain the desired fields. The fields refer to structure RECN_CONTRACT_CI which is globally defined in the function group and contains the fields of the customer Include (the name of the structure is always issued in the only TABLES statement of the respective TOP include LRExx_EXT_EXAMPLETOP).<br/>The flow logic of the screen can remain unchanged. If you still need other screens, you can create them in the same function group by copying screen 901. In the same way you can insert, if required, other modules into the flow logic and include the source code of the corresponding modules and subroutines in the main program of the function group. For each additional screen you require an additional PBO and PAI function module in analogy to REGC_REGC_PBO_Z901_EXAMPLE and REGC_REGC_PAI_Z901_EXAMPLE.<br/></ol> <ol>4. Then you have to make sure that the system displays your subscreen when you maintain the contract and that the corresponding function modules are run.<br/></ol> <ol><ol>a) First of all, publish your application. For this purpose, call the following transaction for each application object:<br/> <div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\"><tr><th align=\"LEFT\">Transaction</th><th align=\"LEFT\"> Object</th></tr> <tr><td>REBDAO0001</td><td> Architectural object</td></tr> <tr><td>REBDBE0001</td><td> Business entity</td></tr> <tr><td>REBDBU0001</td><td> Building</td></tr> <tr><td>REAJCG0001</td><td> Comparative group of apartments</td></tr> <tr><td>REGC0001</td><td> RE contract</td></tr> <tr><td>RESCPG0001</td><td> Participation group</td></tr> <tr><td>REBDPR0001</td><td> Property</td></tr> <tr><td>REBDRO0001</td><td> Rental object</td></tr> <tr><td>RESCSU0001</td><td> Settlement unit</td></tr> <tr><td></td></tr> <tr><td>In the dialog, choose 'New Entries' and specify the name of your application and a text. To activate the application, select the 'Active' checkbox on the detail screen.<br/></td></tr> </table></div></ol></ol> <ol><ol>b) Then publish the screens you have defined to the Business Data Toolset. You can find the corresponding transactions in the Customizing menu, Transaction RECACUST. From there you can also call the transactions for the other objects. All settings are client-specific and must therefore be transported into all clients where they are needed.<br/><br/>For the maintenance of the other BDT Customizing entries, you find the corresponding entries in the IMG for every maintenance dialog in the 'Dialog' menu option. For example, the complete dialog for the contract is<br/>'Flexible Real Estate Management -&gt; Contract -&gt; Dialog'<br/><br/>Create a new field group for your fields by choosing IMG activity 'Dialog -&gt; Screen Layout -&gt; Field Groups -&gt; Field Groups'. Choose a number starting with 7, for example 701. In the 'General data' area, enter a mnemonic name in the 'Description' field for your new fields, for example 'File number'. In addition, enter the name of the function module from point 2 (ZZZZ_REGC_EVENT_FMOD2) in field 'FM for fld grouping'. You can leave the remaining fields empty. Double-click 'Field Group -&gt; Fields' in the dialog structure for the new field group and assign all fields of the subscreen to the field group, for example:<br/>Table: RECN_CONTRACT_CI<br/>Field name: YYID<br/>Input field: selected (This checkbox must be selected for all fields that are ready for input. In addition, these fields must be defined as input fields in the Screen Painter).<br/>When you save, the system displays a warning message which you can ignore.<br/></ol></ol> <ol><ol>c) Create a view for your fields by choosing 'Dialog -&gt; Screen Layout -&gt; Views'. For this purpose, copy an existing entry, for example REGC02. The name of the new view must begin with the application, for example ZZZZ01 with the text 'Contract: File number'.<br/>In 'Application' field, enter the name of your application.<br/>In the 'Subscreen' area, enter the program name (SAPLaaaaaa with aaaaaa being the name of your function group) and the screen number of the subscreen created before:<br/>Program name: SAPLY_REGC_EXT<br/>Screen number: 0901<br/><br/>In the 'Function module' area, enter the name of your PBO module in field 'Before Output' (for example, ZZZZ_REGC_PBO_Z901), and enter the name of your PAI module in the 'After Entry' field (for example, ZZZZ_REGC_PAI_Z901). Leave the values of the remaining fields for the view unchanged. NEVER use the function modules from the standard view in your own screens. If you do so, a program termination will occur because the subroutine to be called is determined dynamically by the standard module.<br/><br/>In the navigation tree, double-click 'View -&gt; Field Groups' and enter the field group (701) created before. Under 'Further checks, you do not have to make any entries.<br/></ol></ol> <ol><ol>d) In the next step, define a section by choosing 'Dialog -&gt; Screen Layout -&gt; Stages'. The name of the section must begin with the application (for example, ZZZZ01) with description 'File number' and title 'File number'. The title you enter here will be displayed as a group heading on the screen.<br/><br/>In the dialog structure, double-click 'Section -&gt; Views' to assign the view created before to the section. The last two digits of the line item number have to be different from '00', for example '9000050'.<br/></ol></ol> <ol><ol>e) Choose 'Dialog -&gt; Screen Layout -&gt; Screens' and define the screen on which the new fields are to be displayed. For example, if you want the file number to be displayed on the 'General Data' screen, select line REGC2 and double-click 'Screen -&gt; Sections' in the dialog structure. The line item number that you assign here to the newly defined section determines the position of the section on the screen. For example, if you want the file number to be displayed after the adjustment, choose a line item number between 300000 and 400000, for example 300050 for section Z901. This line item number is independent of the number that you have assigned to this section in 'Dialog -&gt; Screen Layout -&gt; Stages', but it must not end with '00' either. Ignore error message \"Maximum 10 views\" if it occurs.<br/>If you want to create a completely new screen with own sections note that always the first section in View -&gt; Section of BDT NICHT is displayed. For this reason you must enter an 'empty section' as first section in the list. See also the view-section assignment delivered by SAP (for example for contracts, see section REGC9X).<br/>Note that not all screens are always used. The system groups the screens in screen sequences (maintenance via 'Dialog -&gt; Screen Sequences') and assigns a screen sequence to the contract type in Customizing. If you use (create) a new screen, you have to include it in the screen sequence.<br/><br/>In the application, a screen corresponds to a tab page. You can also define a tab page. To do that, copy an existing screen to a new screen with another name (beginning with the name of the application, for example ZZZZ01) and assign the new screen to your new sections. In this case you MUST also define a new screen sequence for the new screen and make sure that it is displayed. For this purpose, refer to the documentation in IMG activity 'Screen Sequences'.<br/></ol></ol> <ol>5. Call Transaction RECN (in general: RExx) and check whether the added fields respond correctly when you change, display and create a contract.</ol></div></div>", "noteVersion": 14}, {"note": "1328779", "noteTitle": "1328779 - RE-FX Country Version for Netherlands", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>Localization of Flexible Real Estate Management for the Netherlands</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>Real Estate, Flexible Real Estate, RE-FX, Localization, Netherlands, Rent Adjustment, Fixtures/Fittings, Points, Subsidizable Rent</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>Flexible Real Estate Management is used by a company in the Netherlands.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><ol>1. Release strategy</ol> <p><br/>SAP Real Estate Management is currently available in two versions:<br/>Classic Real Estate (old version) and Flexible Real Estate (new     version). Please read more details about the solution strategy in<br/>SAP Note 443311.<br/><br/>Please note that Classic Real Estate will not be developed and localized anymore and mainly covers only the requirements of residential real estate. Therefore, Classic Real Estate is not released for new implementation projects since the general availability for the release SAP ERP 6.0.</p> <ol>2. Localization</ol> <p><br/>As of SAP ERP 5.0 (ERP 2004) Dutch residential and commercial real estate companies can use the standard Flexible Real Estate Management solution for the Netherlands without a standard localization functionality.<br/><br/>It is planned that as of EhP5 (Enhancement Package 5) of SAP ERP 6.0 Dutch social housing companies can use the localized Flexible Real Estate Management solution for the Netherlands with a standard localization functionality.<br/><br/>Please read more about localization of SAP Real Estate Management in general and the consequences of a missing localization in SAP Note 771098.<br/></p> <ol>3. Localization scope (Legal requirements and major business practices for social housing companies in the Netherlands)</ol> <p><br/>Planned to be available as of Enhancement Package 5 (EhP5) of SAP ERP 6.0.<br/></p> <b>Flexible maintenance of fixture/fitting points</b><br/> <p><br/>One of the frequent rent adjustment procedures in the Netherlands is the adjustment after the Representative List of Rents. This adjustment is applied for rental objects which are measured after points. Points are measurement units which can be defined in the standard similarly to area units. However the basis for the calculation of points in the Netherlands is the fixture/fitting characteristics of the rental object.<br/>Therefore fixtures/fittings have to be defined for each rental object which should participate in a rent adjustment after points.<br/><br/>The current localization allows:</p> <ul><li>a more flexible definition of the fixture/fitting characteristics, permitting decimal values in points, the definition of common fixtures/fittings for more than one rental object, individual rounding rules per characteristic,</li></ul> <ul><li>the automatic calculation of the total number of points per rental object</li></ul> <p></p> <b>Calculation and adjustment of the average in rent adjustments according to Dutch prescriptions (Huurbeleid)</b><br/> <p><br/>Social housing companies needs to have an overview of all their rent adjustments and to know the average of these adjustments because according to Dutch presciptions (Huurbeleid) this average is not allowed to be over a limit specified by the authorities.<br/>A special report shows all the rent adjustments of the company, regardless to the adjustment procedure and allows a manual correction of the individual rent adjustments, either in absolute value or in percentage, to be on average below the rent adjustment limit prescribed by the authorities.<br/>This report is written to cope with rent adjustments done for ten thousands of tenants. Optionally it can show also the subsidizable rents.<br/></p> <b>Automatic calculation of subsidizable rents (Huurtoeslag),</b><br/> <p><br/>Tenants of social housing with low income have the possibility to receive a subsidy for their rental costs. This subsidy includes the basic rent and the service charges up to certain limits of different subsidizable service charge categories as defined by the Dutch authorities.<br/><br/>The current localization allows for the social housing companies:</p> <ul><li>the customizing of the subsidy categories with their limits,</li></ul> <ul><li>the specification of the condition types which are subsidizable,</li></ul> <ul><li>the automatic calculation of the subsidizable rents in the contracts,</li></ul> <ul><li>the recalculation of the subsidizable rents in case of changes in the conditions</li></ul> <p></p> <b>Reporting of the subsidizable rents.</b><br/> <p><br/>The social housing company has to report periodically the amount of subsidizable rents for all its tenants to the tax authorities.<br/>A special report presents the subsidizable rent per contract and business partner, identified by their BSN numbers.<br/></p> <ol>1. Localization recommendations</ol> <p><br/>Please note that by default localization enhancements which affect databases or user interfaces made in higher releases cannot be downgraded on earlier versions.<br/><br/>If you have questions about the release of SAP Real Estate Management for the Netherlands including localization topics please refer to the local contact Leo Mommersteeg (<EMAIL>).<br/></p></div>", "noteVersion": 2}, {"note": "782947", "noteTitle": "782947 - Programming interfaces for RE-FX", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>In the context of additional developments, you want to use RE-FX interfaces to read or change Real Estate master data, for example.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>BAPI, Business Application Programming Interface, API, Application Programming Interface, BAdI, Business Add-In</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>The following RE-FX standard objects are provided for use in customer-specific programs or enhancements:</p>\n<ul>\n<li>API function modules for accessing master data and contracts</li>\n</ul>\n<p>           Find modules: Transaction SE37, search string API_RE_*<br/>You can also use these modules in BAdI implementations. For almost all of these modules, there is also a corresponding BAPI (same function module name: BAPI_RE_... instead of API_RE_ ...). The BAPI contains the documentation for the parameters. As of Release ERP 2004, there are also modules for mass access (*_GET_LIST).</p>\n<ul>\n<li>BAdI RECA_STORABLE_EXT or, as of Release ERP 2005, BAdI BADI_RECA_STORABLE_EXT for enhancing master data objects and contracts (additional fields, checks, and preassignments)</li>\n</ul>\n<p>           In the IMG, you find one IMG activity \"Implement Enhancements\" for each object type. The two enhancement methods for flat and tabular master data enhancements are described in the relevant IMG documentation (this corresponds to the BAdI documentation). For table-like enhancements, you have to use the released interface IF_RECA_STORABLE_EXT and the released class CL_RECA_STORABLE_EXT.</p>\n<ul>\n<li>Enhancements to the user interface for master data objects and the contract</li>\n</ul>\n<p>           SAP Note 690900 describes the procedure for enhancing the user interface. The content of this SAP Note is contained in the system as of ERP 2004 (part of the afore-mentioned IMG documentation \"Implement enhancements\").</p>\n<ul>\n<li>Additional BAdIs (for example, for service charge settlement, resubmission, and correspondence)</li>\n</ul>\n<p>           Find BAdIs: Transaction SE18, search string RE++_*.</p>\n<ul>\n<li>BAPI function modules for accessing master data and contracts</li>\n</ul>\n<p>           Find modules: Transaction SE37, search string BAPI_RE_*<br/>These BAPIs are provided for the legacy data transfer (transactions LSMW and SXDA). Since BAPIs are generally RFC-enabled, you can also use these modules for remote access from external systems, for example, to integrate external graphics systems.<br/>Also use the BAPIs, where applicable, even though they are not formally released yet.</p>\n<ul>\n<li>Global classes for accessing configuration data (C and S tables)</li>\n</ul>\n<p>           Find classes: Transaction SE24, search string CL_RE++C_*<br/>To determine the attributes of a certain contract type, for example, call the method CL_RECNC_CONTRACT_TYPE=&gt;GET_DETAIL.</p>\n<ul>\n<li>Logical databases:</li>\n</ul>\n<p>           REBD - Master data for usage objects<br/>REAO - Master data for architectural objects<br/>REBP - Business partner for Real Estate objects<br/>RECN - Real Estate Contracts<br/><br/>SAP will enhance these interfaces (compatible with BAdIs and released BAPIs) or will make incompatible changes only in exceptional cases (API function modules).<br/><br/>To minimize the effort required to make adjustments after a release upgrade, avoid the following usages:</p>\n<ul>\n<li>Do not execute any SELECTs in RE-FX tables, but instead use the API modules for accessing master data and transaction data or the aforementioned classes for accessing configuration data. Bear this in mind, in particular in the context of a table change from Release 470x200 to ERP 2004 (see SAP Note 681951).</li>\n</ul>\n<ul>\n<li>Do not change table data directly using INSERT, UPDATE or DELETE, but use the CREATE and CHANGE BAPI function modules or API function modules for the relevant object type. In the case of changes made directly, the system will not run the business logic, which means that some data will be inconsistent or additional data will be missing (for example, change documents).</li>\n</ul>\n<ul>\n<li>The direct issuing of messages using the statement MESSAGE (without the clause RAISING ...) is not permitted.</li>\n</ul>\n<ul>\n<li>For data declarations and for the parameterization of methods, function modules, form routines, and so on, do not use the table reference or the data element, but use the structure reference instead. Example for number of business entity:   DATA: ld_swenr TYPE bapi_re_bus_entity_int-swenr.  \"korrekt<br/>  DATA: ld_swenr TYPE vibdbe-swenr.  \"falsch<br/>  DATA: ld_swenr TYPE swenr.         \"falsch</li>\n</ul>\n<ul>\n<li>Do not use other interfaces, classes, function modules, or form routines from RE-FX if these objects or modules were not released or recommended for use by SAP.<br/>In particular, do not create a subclass for an RE-FX class and do not implement an RE-FX interface in a separate class or a separate interface. Exceptions are the aforementioned RE-FX class (CL_RECA_STORABLE_EXT) and RE-FX interface (IF_RECA_STORABLE_EXT).</li>\n</ul>\n<p>For information on using API or BAPI function modules to make data changes, read the following notes in connection with COMMIT WORK:</p>\n<ul>\n<li><strong>Within an BAdI implementation, you shall not execute a COMMIT WORK or a ROLLBACK WORK. The transaction control must only be in the surrounding application that calls the Business Add-In method. Otherwise data inconsistencies may arise.</strong></li>\n</ul>\n<ul>\n<li>Data changes using API and BAPI modules are always executed with IN UPDATE TASK. The modules never execute a COMMIT WORK or ROLLBACK WORK. To effectively write the changes to the database, you must execute COMMIT WORK after calling the data change module. To do so, call the function module BAPI_TRANSACTION_COMMIT.</li>\n</ul>\n<ul>\n<li>If possible, call BAPI_TRANSACTION_COMMIT <strong>each time</strong> a data change module is called successfully. This procedure is absolutely necessary if you want to change the same Real Estate object when you call data change modules twice in quick succession or if you want to create dependent Real Estate objects (for example, an architectural object and lower-level architectural object).</li>\n</ul>\n<ul>\n<li>If errors occur when you call a module, this means that the system could not make at least one of the changes you wanted. You do not have to carry out a ROLLBACK WORK. The update modules are not called internally until all changes are can be made. You do not have to perform a COMMIT WORK or call BAPI_TRANSACTION_COMMIT, which is time-consuming, but this cannot result in data inconsistencies or similar errors.</li>\n</ul>\n<ul>\n<li>If you only want to simulate changes, set the TEST_RUN parameter to \"X\". For this, update modules are generally not called so that neither a COMMIT WORK nor a ROLLBACK WORK is necessary or useful.</li>\n</ul>\n<p>For the Real Estate master data, BOR object types are available in the Business Object Repository. You require these object types to define workflows or other tools (such as document management). The BOR object types are:</p>\n<ul>\n<li>BUS1151    Architectural object</li>\n</ul>\n<ul>\n<li>BUS1501    Business entity</li>\n</ul>\n<ul>\n<li>BUS1502    Property</li>\n</ul>\n<ul>\n<li>BUS1503    Building</li>\n</ul>\n<ul>\n<li>BUS1504    Rental object</li>\n</ul>\n<ul>\n<li>BUS1505    Real estate contract</li>\n</ul>\n<ul>\n<li>BUS1506    Settlement unit</li>\n</ul>\n<ul>\n<li>BUS1507    Participation group</li>\n</ul>\n<ul>\n<li>BUS1508    Comparative group of apartments</li>\n</ul></div>", "noteVersion": 7}, {"note": "1003497", "noteTitle": "1003497 - Release note: RE-FX/PSCD integration", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You want to use PSCD (Collection and Disbursement as part of the industry solution for the Public Sector) integrated with RE-FX (Flexible Real Estate Management). PSCD is an industry-specific variant of FI-CA (Contract Accounts Receivable and Payable).</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>RE-FX, Flexible Real Estate, PSCD, Collection and Disbursement, FI-CA, Contract Accounts Receivable and Payable, PSM-FM, Funds Management, SAP Real Estate Management</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>As of Release SAP ERP 6.0, RE-FX offers an integration with PSCD. This option enables you to post from the real estate contract (RE-FX) directly into Contract Accounts Receivable and Payable (PSCD). You can use this integration instead of posting to Accounts Receivable (FI-AR).<br/><br/>By <strong>activating </strong>the Public Services business function set, you can select PSCD as the accounting system in the company code. This links the real estate contract (RE-FX) to PSCD.<br/><br/>The following are prerequisites:<br/>- SAP ECC 6.00, Financials Extension (EA-FIN 600) or higher and<br/>- Extension Public Services 6.0 (EA-PS 600) or higher.<br/><br/>This note describes the released functions and the existing restrictions.<br/><br/>Note 1003258 describes the required settings in the SAP Implementation Guide (IMG).<br/><br/>Note 751579 describes the basic options for integration into the various accounting systems.<br/><br/>Note 443311 describes the release strategy and Note 517673 describes the function scope of RE-FX.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>In PSCD, the real estate contract (RE-FX) is treated as a contract and can therefore be assigned to contract accounts. This enables you to use processes of Contract Accounts Receivable and Payable, such as, for example, paying and dunning. The following functions of the integration of RE-FX with PSCD are currently released:</p>\n<ul>\n<li><strong>Assignment of contract accounts</strong><br/>Depending on the real estate contract type (RE-FX) and the business partner role in the business partner administration (BP), either an existing contract account (PSCD) can be assigned to the real estate contract (RE-FX) or a new contract account can be created automatically and assigned to the real estate contract.</li>\n</ul>\n<ul>\n<li><strong>Partner management</strong><br/>Business partners that have the role category MKK (PSCD contract partner) can be assigned to a real estate contract (RE-FX).</li>\n</ul>\n<ul>\n<li><strong>Posting</strong><br/>Using the (new) indicator \"Subapplication\", you can specify whether the posting in PSCD refers to a real estate contract (RE-FX) or to a contract object (PSCD). You enter the number of the real estate contract (RE-FX) in the \"Contract\" field when you use RE-FX.</li>\n</ul>\n<ul>\n<li><strong>Subsequent processes</strong><br/>All subsequent processes of financial accounting, such as payment, dunning, invoice printout, dunning notice printout, transfer of the postings to the general ledger and so on, are not performed in RE-FX, but directly in PSCD or FI-CA.<br/><br/>Receivables for a real estate contract (RE-FX) can either be administered in a separate contract account (PSCD) or together with other receivables in a common contract account. If a common contract account is used, these processes can be performed for all items.</li>\n</ul>\n<ul>\n<li><strong>CO account assignments</strong><br/>By consistently using a special account assignment toolbar, you can create a link to the account assignment objects of RE-FX (real estate contract, business entity, property, and so on) at the G/L account item level.</li>\n</ul>\n<ul>\n<li><strong>Requests, document posting and cash desk</strong><br/>You can use both the contract connection and the account assignment toolbar in the special manual transactions of the Contract Accounts Receivable and Payable for the Public Sector, such as requests, document posting and cash desk.</li>\n</ul>\n<ul>\n<li><strong>Funds Management (PSM-FM)</strong><br/>If you use the account assignments of Funds Management, you can transfer the relevant account assignments from RE-FX using the posting interface to PSCD, and from there to Funds Management.</li>\n</ul>\n<p><strong>Restrictions</strong></p>\n<p>The following RE-FX functions are currently not released for the integrated use with PSCD:</p>\n<ul>\n<li>Processing of vendor contracts (RE-FX) using PSCD. This function is available as of 605 (Note <a href=\"/notes/1814243\" target=\"_blank\">1814243</a>).</li>\n</ul>\n<ul>\n<li>Using different subledger accounting systems (PSCD and FI-AR) for individual company codes in the same client</li>\n</ul>\n<p><br/>The following functions from RE-FX are currently not supported in the integration with PSCD:</p>\n<ul>\n<li>The system does not take advance payments into account according to the actual principle during the settlement of service charges and sales-based rents.</li>\n</ul>\n<ul>\n<li>During the settlement of service charges and sales-based rents, the system does not take into account advance payments that were posted as special G/L transactions.</li>\n</ul>\n<ul>\n<li>One-time postings</li>\n</ul>\n<ul>\n<li>Postings of the COA management</li>\n</ul>\n<ul>\n<li>Summarization of line items in the case of automatic postings</li>\n</ul>\n<ul>\n<li>Condition split in contracts (subfunction CDSP)</li>\n</ul>\n<ul>\n<li>Conditions in foreign currency</li>\n</ul>\n<ul>\n<li>Approval and earmarked funds (business function RE_FM_EARMARKED_FUND)</li>\n</ul>\n<p> </p></div>", "noteVersion": 12}, {"note": "448973", "noteTitle": "448973 - Add-Ons in Real Estate: Release Enablement", "noteText": "<div class=\"sapMMsgStrip sapMMsgStripWarning sapUiSmallMarginTop sapUiMediumMarginBottom\" id=\"DISCLAIMER\"><div class=\"sapMMsgStripMessage\"><span class=\"sapMText sapUiSelectable sapMTextMaxWidth\" dir=\"auto\" style=\"text-align: left;\"><strong>ATTENTION!</strong> This is a machine-translated document that has not been reviewed for accuracy. To provide feedback regarding the machine translation, please <a href=\"https://sapinsights.eu.qualtrics.com/jfe/form/SV_6nI2C1MOwEviVQG?NoteNumber=448973&amp;TargetLanguage=EN&amp;Component=XX-PROJ-RE-LUM&amp;SourceLanguage=DE&amp;Priority=06\" style=\"color:var(--sapLinkColor);\" target=\"_blank\">click here</a>.<br/>You can access the original document via the following link: <a href=\"/notes/448973/D\" style=\"color:var(--sapLinkColor);\" target=\"_blank\">/notes/448973/D</a>.</span></div></div><div><div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>Information requirements for the release strategy in the mySAP Financials Real Estate area with regard to the add-ons for land use management and condominium ownership management<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Weitere Begriffe\">Other Terms</h3><p>RE, IS-RE, WEG, LUM, add-on, Enterprise, Aldebaran, compatibility, release strategy, flexible real estate, classic real estate<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Ursache und Voraussetzungen\">Reason and Prerequisites</h3><p>You use the add-on for real estate (XRE-LUM) or the add-on for condominium ownership management (XRE-WEG). You plan the upgrade to SAP R/3 Enterprise or mySAP ERP. This SAP Note applies to you regardless of whether you use the old version of SAP Real Estate Management on mySAP ERP, that is: Classic Real Estate in SAP ECC Core, or the new version, that is, Flexible Real Estate in SAP ECC Financials Extension Set.<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Lösung\">Solution</h3><ol>1. Availability<br/><br/>SAP has developed functions that are comparable to the add-ons LUM and WEG in Flexible Real Estate on the basis of mySAP ERP 2005. Please note that these are complete new developments. On the one hand, this results in changes in the function and design of the solution and, on the other hand, the scope of the standard solution has partially changed (for example, in reporting and correspondence). Therefore, before an upgrade, SAP recommends that you determine which specific enhancements are to be made as part of the project as part of a deployment investigation.</ol> <ol>2. Restriction<br/><br/>The add-ons for land use management (\"LUM\") and for condominium ownership management (\"COA\") from SAP R/3 4.6C are <u>not</u> available on SAP R/3 Enterprise (SAP ECC 4.70), mySAP ERP 2004 (SAP ECC 5.00), or higher. In Releases R/3 Enterprise and mySAP ERP 2004, no comparable functions are available in Flexible Real Estate. For more information about the release strategy of SAP Real Estate Management, see SAP Note 443311.</ol> <ol>3. Release upgrade within Classic Real Estate<br/><br/>Therefore, if you use one of the add-ons WEG or LUM based on SAP R/3 4.6C, note that a conversion to mySAP ERP or SAP R/3 Enterprise 4.7 is only possible <u>without installing these add-ons</u>. As a result, the subject areas Land Use Management and Condominium Ownership Management are no longer functionally supported after the release upgrade.</ol> <ol>4. Migration to Flexible Real Estate<br/><br/>If you require the functions of the add-ons and want to convert to mySAP ERP, you can only carry out this conversion if a release upgrade to mySAP ERP 2005 or higher and a migration from Classic Real Estate to Flexible Real Estate take place at the same time. Note that the conversion of the data from the changeover from the LUM and COA add-ons for Classic Real Estate to the standard system is not supported by the standard migration tools. SAP therefore recommends that you request support from SAP Consulting via your SAP Account Manager.<br/></ol></div></div>", "noteVersion": 10}, {"note": "914067", "noteTitle": "914067 - Quarter Days", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>This note describes the use of quarter day functionality in Flexible Real Estate (RE-FX) and its limitations.<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>English quarter days, Scottish quarter days, term days<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>This note provides additional documentation and describes limitations. The solution is available in Flexible Real Estate as of release mySAP ERP 2004 and higher releases.<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><ol>1. Quarter Days in Flexible Real Estate</ol> <p>              The functionality quarter days is designed to define due days for contract conditions, e.g. due dates of rents in Real Estate Contracts. The Quarter Days are the days that mark the beginning of each quarter of the year. Traditionally, these have been days when accounts have been settled and typically with commercial properties these are designated in UK leases and contracts as the days when rent is due. <ol>2. Dates</ol> <p>              Please note that for frequencies, where the period lengths are multiples of months, like in the example of New Scottish or New English quarter days, the quarter day functionality is not required. These \"regular\" frequencies can be set up in all Flexible Real Estate versions. <p>              Where quarter days are specified in a Real Estate Contract, rent must be calculated and charged for each quarter and is due on the quarter day relating to that period. <p>              The dates are different in the various parts of the UK. The exact dates you find here: <ol><ol>a) England, Ireland and Wales</ol></ol> <p>                       In England, Ireland and Wales the quarter days fall on the following dates: <ul><ul><li>March 25</li></ul></ul> <ul><ul><li>June 24</li></ul></ul> <ul><ul><li>September 29</li></ul></ul> <ul><ul><li>December 25</li></ul></ul> <ol><ol>b) Scotland - traditional</ol></ol> <p>                       In Scotland the traditional quarter days may be referred to as \"term days\". These fall on the following dates: <ul><ul><li>February 2</li></ul></ul> <ul><ul><li>May 15</li></ul></ul> <ul><ul><li>August 1</li></ul></ul> <ul><ul><li>November 11</li></ul></ul> <ol><ol>c) Scotland - new</ol></ol> <p>                       However, recent legislation (Term &amp; Quarter Days (Scotland) Act 1990 c.22) has specified the new Scottish quarter days as: <ul><ul><li>February 28</li></ul></ul> <ul><ul><li>May 28</li></ul></ul> <ul><ul><li>August 28</li></ul></ul> <ul><ul><li>November 28</li></ul></ul> <p>                       These new quarter days will apply to more recent leases/contracts unless otherwise stated. <ol>3. Calculation Rules</ol> <p>              Flexible Real Estate applies the following calculation rules for contracts with conditions based on quarter days: <ul><li>The same amount is paid in each full period</li></ul> <ul><li>The above amount is determined by dividing the annual amount of the condition by the number of periods (as defined in customizing)</li></ul> <ul><li>Exactly one period can be flagged in customizing to which any rounding differences are added. If no period has been flagged the rounding differences are not taken into account which means the total of the amounts from each period may be slightly different from the annual condition amount.</li></ul> <ul><li>The due date is calculated on the basis of the total period. If periods are split (when, for example the rent amount changes within a quarter) the due date remains the same.</li></ul> <ol>4. Calculation of due dates for contract conditions</ol> <p>              Quarter days are applied to a condition by applying the appropriate frequency settings on the posting parameters. The calculation method applied to the fixed period calculation can either be on the basis of exact days or by year. <ol><ol>a) Calculation by exact days</ol></ol> <p>              Where Exact Days is used, the calculation base is always the number of days in the respective period. The calculation is made by dividing the total amount for the period by the number of days in the period multiplied by the number of days in partial period the condition applies to. <ol><ol>b) Calculation by year</ol></ol> <p>              Where the calculation method By Year is selected the calculation for any partial period is based on the annual amount. The annual total is divided by the number of days in the year and multiplied by the number of days in the partial period. If the partial periods are not completely in a leap year, two partial amounts are calculated. <ol>5. Usage in Service Charge Settlement and Sales Based Rent</ol> <p>              If advance payments are charged on quarter days, and the settlement variant is not based on quarter days (e.g. you settle at the end of the financial year or calendar year), the advance payment for the first and last quarter are distributed pro-rata into the settlement periods. This distribution is always necessary (independent if quarter days or another condition frequency is used), if advance payment frequency and settlement variant do not correspond. <ol>6. Limitations</ol> <ol><ol>a) Sales-Based Rent</ol></ol> <p>                       Where Sales-Based Rent is used, it is not currently possible to enter a sales report with a reporting rule for a quarter day period. Sales reports can be entered for multiples of days, months or years only. <ol><ol>b) Accruals and Deferrals</ol></ol> <p>                       When Accruals and Deferrals are calculated, the calculation is always based on Exact Days, independent of which calculation method has been chosen at condition type level. <p><br/></p></p></p></p></p></p></p></p></p></p></p></p></p></p></p></p></div>", "noteVersion": 1}, {"note": "663095", "noteTitle": "663095 - SAP Real Estate in R/3 Enterprise and mySAP ERP - Licenses", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>You plan to use SAP Real Estate Management based on mySAP ERP 2004, and you are already using SAP Real Estate Management based on SAP R/3 4.6C or lower, SAP R/3 Enterprise Core or SAP R/3 Enterprise Extension Set 1.10 or 2.00.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>Flexible Real Estate, RE-FX, upgrade, licensing, ERP</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><ol>1. General information<br/><br/>The functions of the new version of SAP Real Estate Management (that is, Flexible Real Estate) are enhanced in the Financials Extension Sets for SAP R/3 Enterprise or SAP ERP Core Component (SAP ECC).</ol><ol>2. Release strategy<br/><br/>As of 2004, the functions of SAP R/3 (including SAP R/3 Enterprise Extension Sets) have been developed in mySAP ERP. Therefore, Flexible Real Estate is developed in mySAP ERP based on the SAP R/3 Enterprise Extension Set 2.00. The next higher release for R/3 Enterprise is mySAP ERP 2004. Note 443311 contains details regarding the release strategy of SAP Real Estate Management.</ol> <ol>3. General license agreement<br/><br/>If you do not yet have a license agreement for SAP Business Suite or SAP ERP (that is, if you have SAP R/3 licensed), you have to upgrade your contract to one of these products so as to use SAP Real Estate Management in SAP ERP 2004 or higher. If you have questions regarding details or individual contract conditions, contact your SAP Account Manager.<br/></ol><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>For details and explanations of the individual contract conditions, contact your SAP Account Manager.<br/></p></div>", "noteVersion": 4}, {"note": "934831", "noteTitle": "934831 - Missing localization of SAP Real Estate Management for China", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>Missing localization of Flexible Real Estate Management for China</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>Real Estate, Flexible Real Estate, RE-FX, Localization, Restriction,<br/>China</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>SAP Real Estate Management is partly released for China</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>SAP Real Estate Management is currently available in two versions: Classic Real Estate (old version) and Flexible Real Estate (new version).<br/><br/>The old versions is not localized for China and not released for implementation anymore. Details on Classic Real Estate Management are described in SAP Note 443311.<br/><br/>Based on the feedback the customers deploying SAP Real Estate Mnaagement the Flexible Real Estate version is released for retail and office property management. For residential property management, industrial property management and land management it is not released yet.<br/><br/>The consequences of a missing localization are described in detail in note 771098.<br/></p></div>", "noteVersion": 2}, {"note": "953883", "noteTitle": "953883 - RE-FX Country Version for Portugal", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>Localization of Flexible Real Estate Management for Portugal</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Real Estate, Flexible Real Estate, RE-FX, Localization, Portugal, Municipal Property Tax, IMI, Imposto municipal sobre imóveis, Stamp Tax, IS, Imposto de Selo, New Urban Lease Act, NULA, NRAU, Novo Regime de Arrendamento Urbano), Conservation and Sewers Tax, CST, Taxa de Conservação e Esgotos.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>Flexible Real Estate is released and localized for Portugal. However, there are some restrictions.<br/>Classic Real Estate is not released anymore for new implementation projects.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<ol>1. Release strategy</ol>\n<p><br/>    SAP Real Estate Management is currently available in two versions:<br/>    Classic Real Estate (old version) and Flexible Real Estate (new<br/>    version). Please read more details about the solution strategy in<br/>    SAP Note 443311.<br/><br/>    Please note that Classic Real Estate will not be developed and<br/>    localized anymore and mainly covers only the requirements of<br/>    residential real estate. Therefore, Classic Real Estate is not<br/>    released for new implementation projects since the general<br/>    availability for the release SAP ERP 6.0.</p>\n<ol>2. Localization</ol>\n<p><br/>    As of EhP4 (Enhancement Package 4) of SAP ERP 6.0 Portuguese<br/>    companies can use the localized Flexible Real Estate Management<br/>    solution for Portugal under a controlled release.<br/><br/>    Please read more about localization of SAP Real Estate Management in general and the consequences of a missing localization in SAP Note 771098.</p>\n<ol>3. Localization scope (Legal requirements and major business practices in Portugal)</ol>\n<p><strong> Municipal Property Tax (IMI - Imposto municipal sobre imóveis)</strong></p>\n<ul>\n<li>Available as of Enhancement Package 3 (EhP3) of SAP ERP 6.0.</li>\n</ul>\n<ul>\n<li>Administration of the Municipal Property Tax (IMI) for your cadastral entries, enabling the fulfillment of the legal requirements for IMI on a yearly basis.</li>\n</ul>\n<ul>\n<li>IMI objects represented as parcels of the Land Use Management (LUM) functionality. <strong>Note that the usage of LUM is connected with separate country-specific license prices.</strong></li>\n</ul>\n<ul>\n<li>Special parcel screen for the IMI objects to display all relevant parameters of the cadastral entries over all calendar years. Assignment of usage objects, for example rental objects, buildings, properties (lands) or business entities.</li>\n</ul>\n<ul>\n<li>Calculation of the expected property tax for each year based on the legal property tax categories, municipal property tax rates or exemptions defined in customizing.</li>\n</ul>\n<ul>\n<li>Special contract type with the Ministry of Finance as business partner and with all property tax objects of the company. This contract sums up the amounts of all the property taxes of the cadastral objects belonging to the company.</li>\n</ul>\n<ul>\n<li>Accrual and posting of the calculated property tax every month during the periodic postings.</li>\n</ul>\n<ul>\n<li>Comparison and overwriting of the calculated property tax values with the liable property tax values received with the yearly payment request. Posting of the liable property tax values in one or two installments depending on the property tax amount at predefined installment dates.</li>\n</ul>\n<ul>\n<li>Distribution of the property tax amounts on cost objects if allocated to Municipal Property Tax objects.</li>\n</ul>\n<ul>\n<li>Mass administration (also historical) from an IMI Cockpit, like cash flow postings (accruals, reversals, liable values), yearly carry over, etc.</li>\n</ul>\n<p><strong> Stamp Tax (IS - Imposto de Selo)</strong></p>\n<ul>\n<li>Available as of Enhancement Package 4 (EhP4) of SAP ERP 6.0.</li>\n</ul>\n<ul>\n<li>Generation of stamp tax conditions for acquisition or lease contracts</li>\n</ul>\n<ul>\n<li>Automatic calculation of the stamp tax based on the official tax rates and on the:</li>\n</ul>\n<ul>\n<ul>\n<li>Property Taxable Value (VPT - Valor Patrimonial Tributario) (acquisition contracts),</li>\n</ul>\n</ul>\n<ul>\n<ul>\n<li>condition amounts in the contract subject to stamp tax (lease contracts).</li>\n</ul>\n</ul>\n<p><strong> New Urban Lease Act (NRAU - Novo Regime de Arrendamento Urbano) as of June 28th 2006</strong></p>\n<ul>\n<li>Available as of Enhancement Package 4 (EhP4) of SAP ERP 6.0.</li>\n</ul>\n<ul>\n<li>Gradually alignment of very low social rents to the market prices in a predefined number of years (2, 5 or 10) considering also the official rent adjustment limitations and the yearly price indexes</li>\n</ul>\n<ul>\n<li>Rent adjustment with the help of new master data in buildings, rental objects and contracts, like the conservation level or the Property Taxable Value (VPT).</li>\n</ul>\n<ol>1. Others</ol>\n<p><strong> Extended Withholding Tax (IRF - Imposto Retido na Fonte)</strong></p>\n<ul>\n<li>Generic functionality necessary for Portugal available as of SAP ERP 6.0.</li>\n</ul>\n<ul>\n<li>Successfully tested for Portugal. However, transactions with Special General Ledger Indicator such as instalments and down payments are not supported.</li>\n</ul>\n<p><span><strong>Digital Signature</strong></span></p>\n<ul>\n<li><span>Unique signature for all outgoing invoices</span></li>\n<li><span>Contry specific invoice template with QR </span><span>code</span></li>\n<li><span>Debit note/credit note handling</span></li>\n</ul>\n<p><strong><span>SAF-T</span></strong></p>\n<ul>\n<li><span>Integration of outgoing invoices</span></li>\n</ul>\n<p><strong> Missing localizations</strong></p>\n<ul>\n<li>Stamp Tax on contract changes</li>\n</ul>\n<ul>\n<li>Conservation and Sewers Tax (CST - Taxa de Conservação e Esgotos)</li>\n</ul>\n<p><br/> <strong>It is recommended that customers in Portugal should implement Flexible Real Estate as of Enhancement Package 4 of SAP ERP 6.0.</strong></p>\n<p><br/>Please note that by default localization enhancements which affect databases or user interfaces made in higher releases cannot be downgraded on earlier versions.</p></div>", "noteVersion": 11}, {"note": "872301", "noteTitle": "872301 - RE-FX Country Version for Italy", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>Localization of the Flexible Real Estate Management solution for Italy.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>RE-FX, Localization, ICI, IRE, Bollo</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>You need a Flexible Real Estate solution for Italy.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>As of mySAP ERP 2005, Italian companies can use the new Flexible Real Estate Management solution for Italy to calculate their taxes in accordance with Italian requirements. This new solution allows more flexibility than the classic real estate management solution. The following functions are supported:</p> <ul><ul><li>City tax (ICI),</li></ul></ul> <ul><ul><li>Registration tax (IRE),</li></ul></ul> <ul><ul><li>Stamp tax (bollo),</li></ul></ul> <ul><ul><li>VAT tax line summarization.</li></ul></ul> <p><br/></p> <b>City tax (Imposta comunale sugli immobili - ICI)</b><br/> <ul><li>Representation of the Italian cadastral objects subject to ICI by architectural objects with a special cadastral object type.</li></ul> <ul><li>Relationships between ICI cadastral object and objects of the usage view (buildings, rental objects, or properties). 1:n relationships between ICI cadastral object and objects of the usage view can be realised through participation groups.</li></ul> <ul><li>Maintenance and view of data related to the calculation of the ICI tax value depending on the calculation type (building, D-building, building area, agricultural land)</li></ul> <ul><li>Tax rate depending on the rental status (rented, not rented, related contract with reduced registration tax).</li></ul> <ul><li>History of cadastral object and ICI tax calculation data with different conditions to one object within one year.</li></ul> <ul><li>Possibility to use automatic or manual ICI tax rate determination with absolute or relative ICI amount detractions</li></ul><ul><li>Maintenance and view of first and second (final) installment data and amounts</li></ul> <ul><li>Possibility to lock the first installment amount and the entire ICI tax record</li></ul> <ul><li>Only one report (instead of four in RE Classic) to execute all steps relevant for ICI tax calculation (display data, carry forward, print: downpayment/balance, payment lock).</li></ul> <ul><li>Print of ICI tax payment forms per city/ICI collector.</li></ul> <p><br/></p> <b>Registration tax (Imposta di registro - IRE)</b><br/> <ul><li>View of IRE records for each contract status (creation, noticing, renewal) with date, official IRE tax code, and so on.</li></ul> <ul><li>Automatic IRE tax determination from the taxable amount with possibility to add registration fees.</li></ul> <ul><li>Possibility to calculate the payback value of the IRE tax to the contract counterpart.</li></ul> <ul><li>Possibility to lock the IRE tax record.</li></ul> <ul><li>One report (instead of four in RE Classic) to execute all steps relevant for IRE tax calculation (display data, carry forward, post payback payment, lock IRE tax record).</li></ul> <p></p> <b>Stamp Tax (Imposta di bollo)</b><br/> <ul><li>Stamp Tax customizing according to contract type and flow type.</li></ul> <ul><li>Automatic generation of stamp tax in each document as a separate tax line (provided that the posting parameters of the real estate contract do not specify sales tax)</li></ul> <p></p> <b>Value Added Tax (Imposta sul valore aggiunto - IVA)</b><br/> <ul><li>Tax Line summarizations on tax code, currency, and so on (for details see note 517673).</li></ul> <p></p></div>", "noteVersion": 1}, {"note": "751579", "noteTitle": "751579 - Accounts receivable accounting (SAP Real Estate/SAP for Public Sector)", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You want to commence integrated use of SAP Real Estate Management and SAP for Public Sector. SAP provides different integration options in the area of accounts receivable accounting.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Contract Accounts Receivable and Payable, RE-FX, Funds Management, IS-PS, PSCD, FI-CA</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>You require information.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>SAP Real Estate Management is available in the Classic RE version for all current releases. SAP Real Estate Management is available in the RE-FX version for SAP R/3 Enterprise Extension 2.00, mySAP ERP2004, and subsequent releases of mySAP ERP2004. For detailed information about the release strategy in the area of SAP Real Estate, see SAP Note 443311.<br/>Alternatively, SAP for Public Sector, as an accounts receivable accounting system, can connect FI-AR or PSCD (as a FI-CA implementation).<br/>In each case, SAP for Public Sector requires certain additional posting information from SAP Real Estate, some of which cannot be provided.</p>\n<ol>1. For Classic RE, the following applies:</ol><ol><ol>a) Up to now, integration with FI-AR was piloted in a customer project. For each customer, it is necessary to check whether the experiences and developments garnered in this project can be transferred to other projects.</ol></ol><ol><ol>b) Integration with FI-CA (PSCD) is not supported.</ol></ol><ol>2. For RE-FX, the following applies:</ol><ol><ol>a) Integration with FI-AR is implemented in functional terms. An integration test has not taken place yet. For those interested, SAP provides support during implementation. The same applies to transferring funds-relevant account assignments from SAP Real Estate Management.</ol></ol><ol><ol><ol><ol>b) Integration with FI-CA (PSCD) is possible only in accordance with SAP Notes1003497 and 1115688.</ol></ol></ol></ol></div>", "noteVersion": 5}, {"note": "681951", "noteTitle": "681951 - New master data tables as of ERP 2004 & subsequent releases", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>You prepare the upgrade to Release ERP 2004, ERP 2005 or a subsequent release.<br/>This SAP Note is also relevant if you perform enhancements or customer developments in the Release R/3 Enterprise Extension 2.0 (for example, if you use API function modules, implement Business Add-Ins, or copy and adjust Smart Forms). This SAP Note helps you to estimate and minimize any subsequent effort for the upgrade.<br/>This SAP Note is not relevant for customers who start using RE-FX with Release ERP 2004.<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>Migration<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>In Releases R/3 Enterprise Extension 1.1 and R/3 Enterprise Extension 2.0, some of the real estate master data is stored in tables that belong to Classic Real Estate.<br/>To enable a manageable migration to the new real estate solution (RE-FX) for customers of Classic RE, new master data tables and transaction data tables are used as of Release ERP 2004. This change affects all customers with Releases Enterprise Extension 1.1 and Enterprise Extension 2.0. As of Release ERP 2004, Classic RE and RE-FX no longer use master data tables and transaction data tables together.<br/>As of Release ERP 2004, we provide an upgrade program (see SAP Note 734981), which copies the data from the previously used tables into the new tables. Neither the definition of the previously used tables nor the data itself are changed or deleted. This means that no data is lost.<br/>Since all standard programs access the new tables as of Release ERP 2004, additional effort is required by you only if you access these tables as part of the customer developments mentioned above.<br/>No incompatible changes were made to Business Add-In definitions during the table change. However, as an exception, incompatible changes were made to the interface of the BAPI function modules and API function modules.   The adjustment effort for these changes is minimal. However, if you want to access the standard data from within customer-specific enhancements, we recommend that you use the API modules.<br/></p> <b>What are the differences between the new tables and the old tables?</b><br/> <p>General changes for all new tables are as follows:</p> <ul><li>The primary key is INTRENO instead of the semantic key.</li></ul> <ul><li>The standard field name for the object number is OBJNR (incompatible change of the ABAP Dictionary structure for parameters of the API modules).</li></ul> <ul><li>The standard field name for the authorization group is AUTHGRP (incompatible change of the ABAP Dictionary structure for parameters of the API modules).</li></ul> <ul><li>The name of the real estate object now has the standard length 60 (the field extension also applies for BAPI structures, API structures, and screens).</li></ul> <ul><li>Unnecessary fields from the Classic RE tables are not copied to the RE-FX tables.</li></ul> <ul><li>Various data elements, in particular for the semantic key fields (for example, number of the business entity), are replaced by new data elements.   The underlying technical data type and the field length remain unchanged in each case.</li></ul> <p><br/>The following overview contains the new tables with the changed field names.   However, only the fields from the Classic RE tables that are actually used in RE-FX are listed.<br/></p> <ul><li>Business entity: Old table name: VIOB01 =&gt; new: <b>VIBDBE</b></li><br/> <div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\"><tr><th align=\"LEFT\">Old</th><th align=\"LEFT\"> New</th><th align=\"LEFT\"> Short Description</th></tr> <tr><td>J_OBJNR</td><td> OBJNR</td><td> Object Number</td></tr> <tr><td>BERGRP</td><td> AUTHGRP</td><td> Authorization Group</td></tr> <tr><td>SMIETSP</td><td> RLRA</td><td> Representative List of Rents</td></tr> <tr><td>NUMKIGE</td><td> (separate tab.)</td><td> Building number range</td></tr> <tr><td>NUMKIGR</td><td> (separate tab.)</td><td> Number range property</td></tr> <tr><td>NUMKIME</td><td> (separate tab.)</td><td> Number range rental object</td></tr> <tr><td></td></tr> </table></div></ul> <p>           The current number levels for each business entity for the assigned buildings, properties, and rental objects are maintained in the table VIBDSUBOBJNUM.<br/></p> <ul><li>Property: Old table name: VIOB02 =&gt; new: <b>VIBDPR</b></li><br/> <div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\"><tr><th align=\"LEFT\">Old</th><th align=\"LEFT\"> New</th><th align=\"LEFT\"> Short Description</th></tr> <tr><td>J_OBJNR</td><td> OBJNR</td><td> Object Number</td></tr> <tr><td>BERGRP</td><td> AUTHGRP</td><td> Authorization Group</td></tr> </table></div></ul> <p></p> <ul><li>Building: Old table name: VIOB03 =&gt; new: <b>VIBDBU</b></li><br/> <div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\"><tr><th align=\"LEFT\">Old</th><th align=\"LEFT\"> New</th><th align=\"LEFT\"> Short Description</th></tr> <tr><td>J_OBJNR</td><td> OBJNR</td><td> Object Number</td></tr> <tr><td>BERGRP</td><td> AUTHGRP</td><td> Authorization Group</td></tr> <tr><td>SLAGK</td><td> RLRALOC</td><td> Location Class</td></tr> </table></div></ul> <p></p> <ul><li>Rental object: Old table name: VIMI01 =&gt; new: <b>VIBDRO</b></li><br/> <div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\"><tr><th align=\"LEFT\">Old</th><th align=\"LEFT\"> New</th><th align=\"LEFT\"> Short Description</th></tr> <tr><td>SNKS</td><td> OBJNR</td><td> Object Number</td></tr> <tr><td>BERGRP</td><td> AUTHGRP</td><td> Authorization Group</td></tr> <tr><td>ROTYPEEX</td><td> (obsolete)</td><td> Type of Rental Object (External)</td></tr> <tr><td>XLAGE</td><td> RLRALOC</td><td> Location Class</td></tr> <tr><td>XAUSTKL</td><td> RLRAFIXFITCATE</td><td> Fixtures and Fittings Category</td></tr> </table></div></ul> <p></p> <ul><li>Real estate contract: Old table name: VICN01 =&gt; new: <b>VICNCN</b></li><br/> <div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\"><tr><th align=\"LEFT\">Old</th><th align=\"LEFT\"> New</th><th align=\"LEFT\"> Short Description</th></tr> <tr><td>RECNOBJNR</td><td> OBJNR</td><td> Object Number</td></tr> <tr><td>RECNAUTHGR</td><td> AUTHGRP</td><td> Authorization Group</td></tr> <tr><td>RECNEND</td><td> RECNEND1ST</td><td> Date of First Contract End</td></tr> </table></div></ul> <p></p> <ul><li>Contract objects: Old table name: VIOBOV =&gt; new: <b>VIBDOBJASS</b></li><br/> <div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\"><tr><th align=\"LEFT\">Old</th><th align=\"LEFT\"> New</th><th align=\"LEFT\"> Short Description</th></tr> <tr><td>FOR_OBJNR</td><td> OBJNRSRC</td><td> Initial object</td></tr> <tr><td>RE_OBJNR</td><td> OBJNRTRG</td><td> Assigned object</td></tr> <tr><td>DATGAB</td><td> VALIDFROM</td><td> Assignment from</td></tr> <tr><td>DATBIS</td><td> VALIDTO</td><td> Assignment to</td></tr> <tr><td></td></tr> </table></div></ul> <p>           The table VIBDOBJASS is also used for other assignment types, for example, for the assets, functional locations, and so on, for a real estate object. The assignment type is stored in the field OBJASSTYPE. The assignment type for contract -&gt; object or object group is '10', and the assignment type for object group -&gt; object is '11'.<br/></p> <ul><li>Asset assignment: Old table name: VIOB37 =&gt; new: <b>VIBDOBJASS</b></li><br/> <div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\"><tr><th align=\"LEFT\">Old</th><th align=\"LEFT\"> New</th><th align=\"LEFT\"> Short Description</th></tr> <tr><td>RE_OBJNR</td><td> OBJNRSRC</td><td> Real estate object</td></tr> <tr><td>DATGAB</td><td> VALIDFROM</td><td> Assignment from</td></tr> <tr><td>DATBIS</td><td> VALIDTO</td><td> Assignment to</td></tr> <tr><td>XUNUTZA</td><td> ISMAINASSET</td><td> Asset Is Leading Asset</td></tr> <tr><td></td></tr> </table></div></ul> <p>           The link to the asset is made using the field OBJNRTRG instead of using the fields BUKRS, ANLA1, and ANL2. The object number for the field OBJNRTRG is generated by concatenating the values of BUKRS (4 characters), ANLA1 (12 characters), and ANL2 (4 characters).<br/></p> <ul><li>Secondary key: Old table name: VIZNRN =&gt; new: <b>VICAINTRENO </b></li></ul> <p>           No field names have been changed.  The primary key is now INTRENO instead of IMKEY. The keys of <b>all</b> real estate objects are now saved redundantly in the table. Up to now, an entry was written in the table VIZNRN only if the IMKEY was generated (in other words, only when the real estate object was released). When creating the real estate object, the entry is immediately written into the table VICAINTRENO.<br/></p> <ul><li>Settlement unit: Old table name: VIAK03 =&gt; new: <b>VISCSU</b></li><br/> <div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\"><tr><th align=\"LEFT\">Old</th><th align=\"LEFT\"> New</th><th align=\"LEFT\"> Short Description</th></tr> <tr><td>SNKS</td><td> OBJNR</td><td> Object Number</td></tr> <tr><td>BERGRP</td><td> AUTHGRP</td><td> Authorization Group</td></tr> <tr><td>DAEEND</td><td> VALIDTO</td><td> Valid To</td></tr> <tr><td>XTEXTAE</td><td> XSU</td><td> Name</td></tr> <tr><td>SMIETR</td><td> TENANCYLAW</td><td> Tenancy law</td></tr> <tr><td>HAS_COSTOBJECT</td><td> HASCOSTOBJECT</td><td> Has Cost Object SUs</td></tr> <tr><td>JNOTAXCORR</td><td> NOTAXCORR</td><td> No Input Tax Adjustment</td></tr> <tr><td>UEBERAE</td><td> see SUTYPE</td><td> Actual Master Settlement Unit</td></tr> </table></div></ul> <p></p> <ul><li>Settlement variant for each SU: Old table name: VIAK04 =&gt; new: <b>VISCSURH</b></li><br/> <div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\"><tr><th align=\"LEFT\">Old</th><th align=\"LEFT\"> New</th><th align=\"LEFT\"> Short Description</th></tr> <tr><td>DGULTAB</td><td> VALIDFROM</td><td> Valid From</td></tr> <tr><td>ABRVAR</td><td> SETTLVARIANT</td><td> Settlement Variant</td></tr> <tr><td></td></tr> </table></div></ul> <p>           Instead of the fields BUKRS, SWENR, SNKSL and SEMPSL, the field INTRENO is now used as a reference to the settlement unit.<br/></p> <ul><li>Cost collector: Old table name: VIAK25 =&gt; new: <b>VISCCC</b></li><br/> <div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\"><tr><th align=\"LEFT\">Old</th><th align=\"LEFT\"> New</th><th align=\"LEFT\"> Short Description</th></tr> <tr><td>DPERBEG</td><td> VALIDFROM</td><td> Start of Settlement Period</td></tr> <tr><td>DPEREND</td><td> VALIDTO</td><td> End of Settlement Period</td></tr> <tr><td>ABRVAR</td><td> SETTLVARIANT</td><td> Settlement Variant</td></tr> <tr><td>DPEROEFF</td><td> RELEASEDON</td><td> Opened on</td></tr> <tr><td>DPERSCHL</td><td> CLOSEDON</td><td> Closed on</td></tr> <tr><td>DABRECHN</td><td> SETTLEDON</td><td> Settled on</td></tr> <tr><td>VVABBLN</td><td> PROCESSID</td><td> Settlement ID</td></tr> <tr><td></td></tr> </table></div></ul> <p>           Instead of the fields BUKRS, SWENR, SNKSL and SEMPSL, the field INTRENO is now used as a reference to the relevant settlement unit.<br/></p> <ul><li>Settlement units for each master settlement unit: Old: VIAK26 =&gt; new: <b>VISCSUREL</b></li><br/> <div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\"><tr><th align=\"LEFT\">Old</th><th align=\"LEFT\"> New</th><th align=\"LEFT\"> Short Description</th></tr> <tr><td>DGUELEMP</td><td> VALIDFROM</td><td> Valid from</td></tr> <tr><td>EQUINUM</td><td> EQUIVNUM</td><td> Equivalence Number</td></tr> <tr><td>KBRART</td><td> FUELTYPE</td><td> Fuel type</td></tr> <tr><td></td></tr> </table></div></ul> <p>           Instead of the fields BUKRS, SWENR, SNKSL and SEMPSL, the field INTRENOSRC is now used as a reference to the master settlement unit. Instead of the fields TBUKRS, TSWENR, TSNKSL and TSEMPSL, the field INTRENOTRG is now used as a reference to the assigned settlement units.<br/></p> <b>Removed table from flexible Real Estate Management</b><br/> <ul><li>Assignment of functional locations:<br/>Old table name: VIBDPMFL =&gt; new: <b>VIBDOBJASS</b></li></ul> <p>           Instead of INTRENO, the real estate object is now stored in the field OBJNRSRC with its object number. Instead of using TPLNR, the reference to the functional location is now made using the relevant object number in the field OBJNRTRG.<br/>The assignment type for the functional locations is '61' (field OBJASSTYPE).<br/></p> <b>Further changes</b><br/> <p>The example function module RECN_NUMBER_GENERATE_DEFAULT for determining the contract number is now provided as an API module under the name API_RE_CN_NUM_GENERATE_BY_OBJ.<br/><br/>The Business Transaction Event (BTE) 00708001 is no longer executed to determine the contract number using a user exit. Use the method GET_NUMBER of the Business Add-In RECN_CONTRACT instead. For more information, see SAP Note 747506.<br/><br/>Redundant function modules have been deleted in the function groups for the Business Data Toolset (BDT) applications. This change is relevant for you only if you have made own enhancements on the user interface for the real estate master data. According to the example from SAP Note 690900, only the use of the function module RExx_GET_BUSOBJ of the relevant function group RExx is required and allowed. In particular, direct access to the basic data using the BDT modules is no longer supported. The module RExx_GET_BUSOBJ with the subsequent call of API_RE_xx_GET_DETAIL should be used instead (see example function groups RExx_EXT_EXAMPLE according to SAP Note 690900).<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><b>What must be taken into account for customer developments?</b><br/> <p>Do not access tables directly using SELECT. To read data, use the \"GET_DETAIL\" BAPI function module or API function module for the relevant object type instead. As of Release ERP 2004 with Support Package 2, the \"GET_LIST\" BAPI function modules or API function modules for mass access are also available. To obtain an overview of all BAPI function modules for RE-FX, search for function modules with the name BAPI_RE_* in the Function Builder (transaction SE37) or in the Repository Information System (transaction SE15) . For API modules, use the search variant API_RE_*. If you use direct SELECTs, the business logic, which specifically formats the data if necessary, is not executed. If there are changes to the table design - as in this case - the expected data is no longer found in this table.<br/>For the object selection within your reports, you can use one of the following logical databases:</p> <ul><li>REAO - Architectural Object</li></ul> <ul><li>REBD - master data of usage view</li></ul> <ul><li>REBP - business partner for real estate objects</li></ul> <ul><li>RECN - real estate contracts<br/></li></ul> <p>Do not change table data directly using INSERT, UPDATE or DELETE, but use the \"CREATE\" and \"CHANGE\" BAPI function modules or API function modules for the relevant object type. If direct changes are made, the business logic is not executed, which means that there may be inconsistent data or additional data may be missing (for example, change documents).<br/><br/>For data declarations and for the parameterization of methods, function modules, form routines, and so on, do not use the table reference or the data element, but use the structure reference instead. Example for number of business entity:<br/>  DATA: ld_swenr TYPE bapi_re_bus_entity_int-swenr.  \"korrekt<br/>  DATA: ld_swenr TYPE vibdbe-swenr.  \"falsch<br/>  DATA: ld_swenr TYPE swenr.         \"falsch<br/></p> <b>Which subfunctions that are relevant for customer-specific enhancements are affected by the changes?</b><br/> <p></p> <b>User-defined fields</b><br/> <p>Before executing the migration program, you must insert all user fields that exist in the old tables into the new tables. The migration program checks whether all required fields exist in the new tables and terminates the migration in the case of an error. For the definition of user fields, we recommend the following structuring: First create a separate ABAP Dictionary structure for the user fields of an object type (using transaction SE11). In this ABAP Dictionary structure, define all user fields and then activate the structure. Display the new database table in transaction SE11 and double-click the entry .INCLUDE CI_VI... in the field overview. Create the relevant CI include structure and insert the separate ABAP Dictionary structure with the user fields using .INCLUDE. Activate the CI include structure. As a result, the user fields are part of the master data table.<br/></p> <b>Customer-specific reports</b><br/> <p>If you have already created your own reports (or similar), check whether you are using the tables specified above directly. To do this, use the where-used list for the relevant tables. In the dialog box for the where-used list, restrict the search range as required, for example, to the packages Y* and Z*. Replace the usages as described above (for example, by calling an API module or adjusting the data declaration).<br/></p> <b>Use of API function modules</b><br/> <p>Some field names of the basic data structure for the relevant object type have been changed, for example, the field names for the object number and authorization group.   This incompatible change affects, for example, the parameter ES_BUS_ENTITY of the module API_RE_BE_GET_DETAIL for reading the data of a business entity. If you previously used the field ES_BUS_ENTITY-J_OBJNR, the source code must be changed to the field ES_BUS_ENTITY-OBJNR.<br/>You can easily identify all relevant parts of the source code by executing a mass syntax check for the customer-specific source code.   To do this, call transaction REDSRS01 and enter the customer-specific packages (for example, Y* and Z*) instead of the package RE_*. If errors are found, you can display the relevant program by double-clicking. Correct the syntax errors by adjusting the field names in the source code and then activate the program.<br/>For this reason, implementations of Business Add-Ins may also have syntax errors. These errors are displayed in the implementing class using the mass syntax check.  The field names are adjusted in the same way as for other callpoints of the API modules that are outside the Business Add-In implementations.<br/></p> <b>Correspondence with Smart Forms</b><br/> <p>During the setup of the Smart Form correspondence, the required forms are usually copied into the customer namespace and these copies are then adjusted.   We recommend that you regenerate the copied forms after upgrading to ERP 2004 or a subsequent release. The report RFRECPSFTLGEN is available for this. Execute the report using transaction SE38 and enter the customer-specific forms for RE-FX (for example, Y* and Z*) in the \"Form Name\" field.<br/>Any generation errors that occur are due to changed field names, which are listed in the following overview. Use the Form Builder (transaction SMARTFORMS) to change the field names. Switch to change mode of the relevant form and choose \"Check\". From the error messages, click the \"Node\" field to navigate directly to the incorrect text element.   Correct the field names (always use uppercase letters) and then activate the form.<br/></p> <ul><li>Master data summary - business entity, copies of the form <b>RE_BE_010</b></li><br/> <div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\"><tr><th align=\"LEFT\">Field:</th><th align=\"LEFT\"> Description of Representative List of Rents</th></tr> <tr><td>Old:</td><td> BUSINESS_ENTITY-XMIETSP</td></tr> <tr><td>New:</td><td> BUSINESS_ENTITY-XRLRA</td></tr> <tr><td></td></tr> </table></div></ul> <ul><li>Master data summary - contract, copies of the form <b>RE_CN_010</b></li><br/> <div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\"><tr><th align=\"LEFT\">Field:</th><th align=\"LEFT\"> Date of First Contract End</th></tr> <tr><td>Old:</td><td> TERM_PERIOD-RECNEND</td></tr> <tr><td>New:</td><td> TERM_PERIOD-RECNEND1ST</td></tr> <tr><td></td></tr> <tr><th>Field:</th><th> Name of Service Charge Key</th></tr> <tr><td>Old:</td><td> SETTL_CNPART_DETAIL-XTEXTSCKS</td></tr> <tr><td>New:</td><td> SETTL_CNPART_DETAIL-XSCKEY</td></tr> <tr><td></td></tr> </table></div></ul> <ul><li>Service charge settlement, copies of the form <b>RE_CN_150</b></li><br/> <div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\"><tr><th align=\"LEFT\">In each case, field:</th><th align=\"LEFT\"> Name of Service Charge Key</th></tr> <tr><td>Old:</td><td> SCR_RESULT-XTEXTSCKL</td></tr> <tr><td>New:</td><td> SCR_RESULT-XSCKEY</td></tr> <tr><td></td></tr> <tr><td>Old:</td><td> SCR_MSU-XTEXTSCKL</td></tr> <tr><td>New:</td><td> SCR_MSU-XSCKEY</td></tr> <tr><td></td></tr> <tr><th>In each case, field:</th><th> Name of settlement unit</th></tr> <tr><td>Old:</td><td> SCR_MSU-XTEXTSU</td></tr> <tr><td>New:</td><td> SCR_MSU-XSU</td></tr> <tr><td></td></tr> <tr><td>Old:</td><td> SCR_AJROSU-XTEXTSU</td></tr> <tr><td>New:</td><td> SCR_AJROSU-XSU</td></tr> </table></div></ul> <p></p> <b>Further interface changes to BAPIs and API modules</b><br/> <p>Release ERP 2004 Support Package 2 supports new compatible parameters and new data fields in existing parameters. For more information, see SAP Note 741557.<br/>The following incompatible change was also required for this: For the BAPI modules and API modules for all master data objects, access to assigned objects such as functional locations and assets is now executed using the common parameter OBJ_ASSIGN (object assignment). The previous separate parameters ASSET (assigned assets) and FUNC_LOC (assigned functional locations) do not apply. Change any calls for these modules accordingly.<br/></p> <b>New Customizing tables</b><br/> <p>In the same way as for application data, we do not recommend accessing Customizing tables directly. Unlike application data, Customizing data is rarely required within enhancements. For this reason, only the table names are mentioned in the following overview. Check whether you use tables from the column \"Old\" in your source code and replace any usages accordingly.</p> <ul><li>New Customizing tables</li><br/> <div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\"><tr><th align=\"LEFT\">Old</th><th align=\"LEFT\"> New</th><th align=\"LEFT\"> Short Description</th></tr> <tr><td>TPR5/TPR9</td><td> TIVBPOBJROLE</td><td> Assignment of Role to Object Type</td></tr> <tr><td>TIVCNCCTBPROLE</td><td> TIVBPOBJROLE</td><td> Assignment of Role to Object Type</td></tr> <tr><td>TIPZZ</td><td> TIVCACLSET</td><td> Client-specific settings</td></tr> <tr><td>TIPZB</td><td> TIVCACCSET</td><td> Company Code-Dependent Settings</td></tr> <tr><td>TIVCNOBJ</td><td> TIVBDOBJASS</td><td> Allowed Assignment Objects per Object Type</td></tr> <tr><td>TIV71</td><td> TIVBDCHARACT</td><td> Fixtures and Fittings Characteristic</td></tr> <tr><td>TIV7A</td><td> TIVBDCHARACTT</td><td> Fixtures and Fittings Characteristic (Text)</td></tr> <tr><td>TIVAJMAXINC</td><td> TIVAJMINMAXINC</td><td> Capping Provisions</td></tr> <tr><td>TIV05</td><td> TIVSCSCKEY</td><td> Service Charge Keys</td></tr> <tr><td>TIVT5</td><td> TIVSCSCKEYT</td><td> Service Charge Key (Text)</td></tr> <tr><td></td></tr> <tr><td></td></tr> </table></div></ul></div>", "noteVersion": 10}, {"note": "589389", "noteTitle": "589389 - Parallel usage RE - TR-TM - IS-B - CFM", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>As of Release 1.10 the Financial Services component (formerly TR-TM) uses SAP Business Partner mandatory. This note describes the aspects that are to be taken into account with the integration with the Real Estate components (RE, as of Release 4.7 Classic RE).<br/>The executions concerning the parallel update of Business Partners are also valid for other components which use SAP Business Partner.<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>Treasury BP, SAP BP, Business Partner<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>After a change from SAP R/3 Release 4.6B or Release 4.6C to SAP Enterprise R/3, you can still operate mySAP Financials Real Estate and Financial Services 1.10 parallel within the same system.<br/>However, the Financial Services 1.10 component uses the SAP Business Partner while mySAP Financials Real Estate uses the Treasury Business Partner. The interfaces for the partner maintenance differ correspondingly.<br/></p> <b>Procedure:</b><br/> <p>After the technical release upgrade, the Treasury Business Partner must be converted in SAP Business Partner before the start-up of the new release so that Financial Services can still use the Business Partners. The data of the Treasury Business Partner are retained at the conversion so that mySAP Financials Real Estate can still access this data.<br/>In order to use the same Business Partner in both components and to make changes at the Business Partner data only once, the option exists, to update the master records synchronously. See SAP Notes 351920 and 398888 for further information.<br/></p> <b>Outlook:</b><br/> <p>In the SAP R/3 Enterprise Extension, SAP currently develops a new Real Estate management (RE-FX). In a medium-term, this product will include all functions of mySAP Financials Real Estate in SAP R/3 Release 4.6C or SAP R/3 Enterprise Core. This new Real Estate management works with SAP Business Partner. In the course of the migration onto this new product the Business Partners used in RE are also converted. Then, Treasury Business Partner is not used anymore, the synchronous data update can be dropped.<br/>For further information on mySAP Financials Real Estate in SAP R/3 Enterprise Financials Extension see SAP Notes 443311 and 517673.</p></div>", "noteVersion": 3}, {"note": "460524", "noteTitle": "460524 - Activating the Real Estate Extension in SAP R/3 Enterprise", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>You want to activate \"SAP Real Estate Management based on the SAP R/3 Enterprise Financials Extension\" (called \"RE Extension\" in the following).<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p><PERSON><PERSON><PERSON><PERSON> (project name), Flexible RE, Flexible Real Estate Management<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>First you should carefully read SAP Notes 443311 and 517673.<br/>Before the activating the RE Extension, you should contact SAP, if this has not already been done, since this component is currently not released for general availability. Release 4.70 is required.<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>The functionality is part of the Financials Extension. In order to activate the RE Extension, you first have to activate the Financials Extension.<br/><br/>To do so, choose the menu point \"Activation Switch for SAP R/3 Enterprise Extension Set\" in in transaction SPRO (Standard Implementation Guide). Set the \"Active\" indicator for the application indicator \"EA-FIN\".<br/><br/>Call transaction RECACUST and choose the<br/>         Basic Settings -&gt; Activate Extension<br/>IMG activity.<br/>Within the \"Activate Real Estate Extension\" activity, set the \"Extension Active\" indicator.<br/><br/>Note:<br/>In order to be able to see the transactions of the RE Extension in the Easy Access Menu, you have to assign the role SAP_RE_APPL to the users, who then have to use the user menu. (Or define your own role using transaction PFCG. This role should be based on the RECAMENUAPPL area menu, and be a customer-specific, modified copy of this role.)<br/>The SAP standard menu does not contain the transactions for the RE Extension in Release 1.0 (even if the \"Extension Active\" indicator is set).<br/><br/>Implement Support Package 4, at the minimum. For more information on the support package, refer to SAP Note 565924.<br/></p></div>", "noteVersion": 4}, {"note": "1079514", "noteTitle": "1079514 - Customer bank procedure", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>The bank procedure is available in Classic Real Estate. Is there a similar function available in flexible Real Estate (RE-FX)?</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p><b>Solution in classic Real Estate</b><br/>The solution available in classic Real Estate (RE) is a customer bank procedure with the following functions:<br/><br/>You can define a virtual account number in lease-outs (field VIMIMV-KONTOBV). The system can fill the field using a user exit according to a custom procedure.<br/><br/>This field contains the account number that a tenant specified as the account number when using the automatic bank procedure (standing order, bank transfer). The procedure (also know as \"electronic rent collection\", \"bank procedure\") is used in Germany. It is supported, for example, by:</p> <ul><li>Aarealbank (BK01 procedure)</li></ul> <ul><li>Berliner Bank (BB01 procedure)</li></ul> <ul><li>Dresdner Bank (DR01 procedure)</li></ul> <ul><li>Sparkasse Berlin (SP01 procedure)</li></ul> <p><br/>In the house bank, the account number defined in the lease-out is assigned to the actual bank account of the landlord as a virtual account number. The \"Bank procedure account number\" is uniquely assigned to a lease-out. For incoming payments, the bank creates a data medium (DTAUS format) that contains the incoming payments for each account number (field C5). As a result of the 1:1 assignment, the system can use the automatic procedure (\"electronic bank statement\") to assign incoming payments to the relevant lease-out.<br/><br/>In classic Real Estate, there is no solution for a vendor bank procedure (assignment of outgoing payment, for example, automatic debits for account assignment objects).<br/><br/><b>Solution in flexible Real Estate</b><br/>For flexible Real Estate, the standard system does not contain a solution for the customer bank procedure or the vendor bank procedure.<br/><br/>To map the customer bank procedure in flexible Real Estate, we recommend a customer solution with the following elements:</p> <ul><li>Copy the field VIMIMV-KONTOBV from classic Real Estate to flexible Real Estate: Copy the field to a relevant customer field for the real estate contract and include it in the contract dialog (Note 690900). You can include the field (and, if necessary, include substitution procedures that preassign the field) without modifying the system.</li></ul> <ul><li>Intervene in the account statement entry by including a customer algorithm that uses the account number to find the lease-out and the relevant customer account. The account statement entry provides various exits that the customer can use to intervene without modifying the system.</li></ul> <p><br/><b>Alternative solution for flexible Real Estate</b><br/>Aareal First Financial, in collaboration with SAP Consulting, provides Aareal Bank customers with a solution whose functions exceed the functions of classic Real Estate. In particular, they also provide a solution for the vendor bank procedure. For more information about this solution, contact the following contact persons:<br/></p> <ul><li>Jörg Adolphs<br/>Senior Consultant<br/>Consulting Unit Real Estate I SAP Deutschland AG &lt;(&gt;&amp;&lt;)&gt; Co. KGTel.: +49 2102/868 365</li></ul> <ul><li>Dirk Forke Manager Strategy &lt;(&gt;&amp;&lt;)&gt; ProductsAareal First Financial Solutions AG<br/>Peter-Sander-Straße 30<br/>55252 Mainz-Kastel<br/>Tel.: +49 6134/560 107</li></ul> <p><br/></p></div>", "noteVersion": 4}, {"note": "870859", "noteTitle": "870859 - Homebuilding Real Estate Sales - Classical v.s. RE-FX", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>Information pertaining to the industry solution Homebuilding Real Estate Sales in relation to the new core Real Estate Management and   flexible functions also know as RE-FX</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>IS-ADEC-HBS, Homebuilding Solution, HBPS</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>Homebuilding IS-ADEC-HBS solution utilizes the core Real Estate Management data base tables which also known as classical RE release. As of Release 1.10 of SAP R/3 Enterprise Extension (release DIMP 4.71 and later), SAP delivers a new Real Estate Management with extended and more flexible functions (referred to as 'RE-FX'). The new RE-FX uses new data base tables for master and transactional data. Refer to notes 443311, 517673, 628208, or 681951...ect. for additional information. This new RE-FX component is a part of the Financials Extension. Therefore, activate the Financials Extension and activate these<br/>functions are required (note 628208) before using the new RE-FX application.<br/>The goal of this note is to warn the customers who are currently using IS Homebuilding functionality NOT to activate the RE-FX application. Activate RE-FX means de-activate classic RE which is the base of the IS solution Homebuilding functionality. Once the RE-FX switch is activated, classic RE (i.e. Homebuilding solution) is no longer work.<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>Customer who uses IS-ADEC-HBS should not activate RE-FX (core Real Estate Flexible Function)<br/></p></div>", "noteVersion": 2}, {"note": "915768", "noteTitle": "915768 - Sales-based rent agreements for lease-in contracts", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>Lease-in contract contains a sales-based rent agreement</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>Lease-in, Expense lease</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>When tenants manage in lease-in contracts (expense leases) with landlords sales-based rent agreements and execute related settlements, specific settings in customizing are required.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><ol>1. Customizing of condition types<br/><br/>In order to manage sales-based rent agreements in lease-in contracts (real estate contracts at vendor side), it is necessary to define special condition types. (This procedure is comparable to what has to be executed for lease-out contracts on the customer side.)</ol> <ol><ol>a) Sales-based rent<br/><br/>Please create this condition type with the attribute \"sales-based rent\" and assign it to a corresponding flow type.<br/><br/>Purpose of this condition is to be used as an \"anchor\" in the sales-based rent agreement without any amount. Although the \"sales-based rent\" condition type is not taken into account during periodic postings, the information about linked posting terms and objects is relevant for the sales-based rent settlement process.<br/><br/>The definition of this condition type is <b>mandatory</b> for the management of sales-based rent agreements in SAP Real Estate Management.</ol></ol> <ol><ol>b) Advance payments on sales-based rent<br/><br/>If advance payments are paid for sales-based rents, please create a condition \"advance-payments for sales-based rent\" with the attribute \"AP sales-based rent\" and select \"revenue\", and assign it to a corresponding flow type.<br/><br/>Please note: although this condition is an expense condition, the field \"revenue\" has to be selected, because it indicates that this condition is relevant for profit and lost accounting.<br/><br/>It is <b>not</b> recommended to use special general ledger indicators in the definition of the flow types that you assign to this condition type. Nevertheless, you could use special general ledger indicators. However, we assume that in business practice it does not make sense to apply special general ledgers in this area.<br/><br/>The definition of this condition type is optional for the management of sales-based rents.</ol></ol> <ol><ol>c) Minimum sales-based rent<br/><br/>If the customer has to pay a minimum rent, please create a condition with the attribute \"minimum sales-based rent\", and assign it to a corresponding flow type.<br/><br/>Sometimes, the periodical \"basic rent\" represents the \"minimum sales-based rent\". In this case, please customize the \"basic rent\" condition as described above for \"minimum sales-based rent\" condition types.<br/><br/>The definition of this condition type is optional for the management of sales-based rents.</ol></ol> <ol><ol>d) Maximum sales-based rent<br/><br/>If the sales-based rent is limited to a maximum amount, please create a condition type with the attribute \"maximum sales-based rent\", and assign it to a corresponding flow type.<br/><br/>The \"maximum rent\" is normally defined as a statistical condition. The reason is that the \"maximum rent\" will not be posted but represents only a limitation for the purpose of sales-based settlement (e.g. maximum is equal to two time of the basic rent). This condition is taken into account when the sales-based rent is calculated during the sales-based rent settlement.<br/><br/>The definition of this condition type is optional for the management of sales-based rents.</ol></ol> <ol>2. Customizing of condition purpose<br/><br/>Please maintain the condition purpose for the defined condition types. The recommended customizing is as follows:<br/><br/>- Sales-based rent: Actual rent<br/><br/>- Advance payments on sales-based rent: Actual rent<br/><br/>- Minimum sales-based rent: Actual rent<br/><br/>- Maximum sales-based rent: Statistical</ol> <ol>3. Customizing of flow types<br/><br/>Please define appropriate flow and reference flow types and assign them to the condition types.</ol> <ol>4. Customizing of condition groups<br/><br/>Please assign the sales-based rent conditions to the relevant condition group (e.g. commercial real estate contract) and make sure that the condition group is assigned to the right contract type.</ol> <ol>5. Customizing of sales rule<br/><br/>Please define the name of the sales rules. Please observe that this feature is not limited to turnover of companies, but you could also define other types of rules such as phone units, profit-based rents, etc.</ol> <ol>6. Customizing of reporting rules<br/><br/>Please define unique key and name for each sales type. When the sales type is for sales-based rent settlement based on quantitative sales, then you have to enter the unit of measurement for the quantity (such as gallons). For sales-based rent settlement based on monetary sales, you do not have to enter a unit of measure in this activity. The system automatically defaults the company code currency in the sales rule. Examples are Complete Assortment, Clothing, Tobacco products, Foodstuffs, and Beverages.</ol> <ol>7. Create sales-based rent agreements<br/><br/>When you create a new lease-in contract, please select<br/><br/>- select \"relevant for sales\" on the \"General Data\" tab<br/><br/>- and insert the relevant conditions on the \"Conditions\" tab<br/><br/>- enter sales and reporting rules on the \"Sales-Based Rent Agreement\"<br/><br/>- assign the sales rules to the relevant \"conditions\"</ol> <p></p></div>", "noteVersion": 3}, {"note": "1353606", "noteTitle": "1353606 - RE-FX Country Version for Hungary", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>Localization of Flexible Real Estate Management for Hungary.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>Real Estate, Flexible Real Estate, RE-FX, Localization, Correction invoice, Reversal Invoice, Fulfillment Date, Copy Numbering</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>Flexible Real Estate Management is used by a company in Hungary.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p></p> <ol>1. Release strategy</ol> <p><br/>SAP Real Estate Management is currently available in two versions:<br/>Classic Real Estate (old version) and Flexible Real Estate (new     version). Please read more details about the solution strategy in<br/>SAP Note 443311.<br/><br/>Please note that Classic Real Estate will not be developed and localized anymore and mainly covers only the requirements of residential real estate. Therefore, Classic Real Estate is not released for new implementation projects since the general availability for the release SAP ERP 6.0.</p> <ol>2. Localization</ol> <p><br/>It is planned that as of EhP5 (Enhancement Package 5) of SAP ERP 6.0 Hungarian residential and commercial real estate companies can use the localized Flexible Real Estate Management solution for Hungary with a standard localization functionality.<br/><br/>Please read more about localization of SAP Real Estate Management in general and the consequences of a missing localization in SAP Note 771098.</p> <ol>3. Localization scope (Legal requirements and major business practices in Hungary)</ol> <p><br/>Planned to be available as of Enhancement Package 5 (EhP5) of SAP ERP 6.0.<br/></p> <b><u><b>Correction Invoices / Reversal Invoices</b></u></b><br/> <p><br/>When you need to make a correction on an invoice, or you need to reverse it, in some countries like Hungary you are required to create a correction invoice or a reversal invoice, respectively.<br/><br/>A <u>Correction Invoice</u> is an invoice which contains the withdrawn original amount, the corrected amount and a reference to the original invoice item which was corrected.<br/><br/>A Reversal Invoice is an invoice which contains the reversed amount and a reference to the original invoice.<br/><br/>A new transaction enables you to determine for which financial documents created via periodic postings, Correction Invoices or Reversal Invoices should be created.<br/><br/>The print form of these invoices will respect the Hungarian prescriptions for Correction / Reversal Invoices.<br/><br/>You can also create a Correction / Reversal Invoices for a Correction Invoices.<br/><br/>The correction/reversal invoicing functionality is extensible for further countries.<br/></p> <b>Fulfillment Date</b><br/> <p><br/>Till note 1023317 (ERP 6.00 SP11, EhP2) there was no dedicated field for the Fulfillment Date. Customers used the Document Date or the Posting Date for this purpose. Since note 1023317 the financial documents have a 'Tax Reporting Date' on the header level. See also note 1232484 for how to use this date.<br/><br/>For Hungarian companies the Fulfillment Date of the financial documents created with real estate periodic postings is determined depending on the individual real estate contract conditions if they are categorized as continuous or discontinuous service.<br/><br/>In case of continuous services, the Fulfillment Date is the same as the payment due date, in case of non-continuous services, it could be different (the business practice in Hungary is to use the document date entered at the time of the periodic posting).<br/><br/>The system determines the Fulfillment Date at the time of the periodic posting and stores it in the Tax Reporting Date (because of historical reasons also in the Document Date) of the document header. It also splits the financial documents in case multiple fulfillment dates were determined in as many pieces as different fulfillment dates.<br/><br/>The Fulfillment Date on the header data of a real estate invoice is read from the Tax Reporting Date of the financial documents.<br/><br/>The Fulfillment Date is determined also for the correction invoices and for reversal invoices.<br/></p> <b>Copy Numbering</b><br/> <p><br/>When you print your original, correction, and reversal invoices for Hungarian companies, the system identifies each copy with a unique consecutive number as defined by law. You can apply the copy numbering for non-preprinted invoices for printing with separate copies or for printing with carbon copies.<br/></p> <ol>1. Others</ol> <b>Missing country-specific functions</b><br/> <b></b><br/> <ul><li>Correction Invoices and Reversal Invoices created from financial documents originating from 'One-time Posting' transactions -&gt; use the one-time conditions to have the Correction/Reversal Invoices originating from periodical postings.</li></ul> <ul><li>Correction Invoices and Reversal Invoices originating from sales-based rent postings or yearly service charge settlement postings.</li></ul> <ul><li>Registration of base amounts and tax amounts in the cash flow of credit-side contracts according to the amounts in the incoming invoices.</li></ul> <ol>1. Localization recommendations</ol> <p><br/>Please note that by default localization enhancements which affect databases or user interfaces made in higher releases cannot be downgraded on earlier versions.<br/><br/></p></div>", "noteVersion": 1}, {"note": "520965", "noteTitle": "520965 - Release restrictions R/3 Enterprise 4.70 / 1.10 Add-Ons", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p><br/>Information on release restrictions for Release R/3 4.70 Enterprise and 1.10 add-ons.<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p><br/>Release restriction, prerequisites<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p><br/>When R/3 Enterprise Core 4.70 and R/3 Enterprise Extensions/Add-ons 1.10 were released, there were still restrictions concerning the productive use of certain functions, and we want to give you information about this now.<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p><br/>Dear customer,<br/><br/>in this note we provide information regarding the following restrictions related to the productive use of R/3 Enterprise Core 4.70 as well as R/3 Enterprise Extensions/Add-ons 1.10. The status of the following information corresponds to the last change date of this note.<br/>           <b> If you have questions, suggestions or comments about the productive use of R/3 Enterprise Core 4.70 and R/3 Enterprise Extensions/Add-ons 1.10 or about the individual functions? We would be happy to receive your feedback.</b><br/><br/>E-mail the contact person (named in square brackets) for the relevant component , particularly in cases where SAP must be consulted before use. Enter problem messages, as usual, on SAP Service Marketplace or SAPNet - R/3 Frontend.<br/><br/><br/>Yours faithfully,<br/>Your Global Quality Management</p> <b>The following release-relevant themes are available in SAP notes or elsewhere:</b><br/> <ul><li>Unicode: SAP Note 540911 [<EMAIL>]</li></ul> <ul><li>Platforms: (SAP Service Marketplace, quick link /platforms) [<EMAIL>]</li></ul> <p><br/>As far as country-specific and language-specific releases are concerned, this SAP note only lists exceptions from the general releases for countries and languages as documented in SAP Service Marketplace (quick link /globalization).<br/><br/>If applications are used in countries for which they are not released, SAP cannot guarantee that country-specific legal requirements are met. In this case, contact the specified contact person (also see SAP Note 520842).<br/>           <br/></p> <b>In detail, the applications and functions are:</b><br/> <p></p> <b><b>SAP R/3 Enterprise Core 4.70</b></b><br/> <p></p> <ol>1. <b>Release only with SAP's approval or after consultation with SAP.</b></ol> <p></p> <ul><li>SCM: Cross-system flow of goods (CSFG)</li></ul> <p>           [<EMAIL>]<br/>           The \"Cross-system flow of goods\" scenario allows you to display the following processes.</p> <ul><ul><li>R/3 cross-system delivery</li></ul></ul> <ul><ul><li>R/3 cross-system stock transfer</li></ul></ul> <p>           Currently this is only released for use after consultation with SAP Development.<br/></p> <ul><li>HCM: Public Sector - HR Federal</li></ul> <p>           Application component:  PY-US-PS<br/>           [<EMAIL>]<br/>           If you want to use the \"HR Federal\" function, you must first consult SAP Development.<br/></p> <ul><li>Retail: Listing via layout (Transactions WLWB and WSM4L)</li></ul> <p>           Application components: LO-MD-RA and IS-R-BD-LST<br/>           Contact person: [<EMAIL>]<br/>           Currently, you must consult SAP and receive its approval before you can use the listing via layout function (in other words, the assortment creation via shelf modules). The listing functions of the Layout Workbench (Transaction WLWB) and the relisting by changes of the layout data (Transaction WSM4L) are affected by this.<br/>           The restriction is lifted with Support Package 21 of EA-Retail.<br/>(also see Notes 701234, 702531 and 704320)<br/></p> <ol>2. <b>Release with restrictions</b></ol> <p></p> <ul><li>SCM: Change Management</li></ul> <p>           Application component:  LE-IDW<br/>           [<EMAIL>]<br/>           The \"Change Management\" function is only released as of Support Package 06 of software component SAP_APPL. Refer to Note 576513 for further details about the implementation and the use of the Change Management function.<br/></p> <ul><li>HCM: Payroll Ireland</li></ul> <p>           Application component:  PY-IE<br/>           \"Payroll Ireland\" is only released with restrictions. The tax and social insurance (PRSI) function will be available with the first Support Package.<br/></p> <ul><li>HCM: Payroll South Africa</li></ul> <p>           Application component:  PY-ZA<br/>           \"Payroll South Africa\" is only released with restrictions. The latest legal change in the social insurance (UIF) is contained in the first Support Package.<br/></p> <ul><li>HCM: Payroll Great Britain</li></ul> <p>           Application component:  PY-GB<br/>           \"Payroll Great Britain\" is only released with restrictions. The latest legal change in the tax calculation (UIF) is contained in the first Support Package.<br/></p> <ul><li>HCM: 3CML (Third child maternity leave report)</li></ul> <p>           Application component:  PY-SG <br/>           Some results returned by the 3CML report are incorrect. The correct version of the report is delivered with the second Support Package for Release 4.70. <br/></p> <ul><li>HCM: Settlement of awards of the Compensation Management</li></ul> <p>           Application component:  PA-CM<br/>           Country-specific restriction<br/>           The function is only available for the following country versions:</p> <ul><ul><li>Germany</li></ul></ul> <ul><ul><li>Country grouping 99 (Other countries)</li></ul></ul> <ul><li>HCM: Public Sector</li></ul> <p>           Application component:  PY-XX-PS (XX = ISO code  of a country)<br/>[<EMAIL>]<br/>           The functions of the following components:</p> <ul><ul><li>PY-AT-PS</li></ul></ul> <ul><ul><li>PY-AU-PS</li></ul></ul> <ul><ul><li>PY-BE-PS</li></ul></ul> <ul><ul><li>PY-CH-PS</li></ul></ul> <ul><ul><li>PY-FR-PS</li></ul></ul> <ul><ul><li>PY-GB-PS</li></ul></ul> <ul><ul><li>PY-US-PS</li></ul></ul> <p>           were developed exclusively for the public services. Other customer groups cannot use these components.<br/></p> <ul><li>HCM: Public Sector - Process Workbench Engine (PWE)</li></ul> <p>           Application component:  PY-AU-PS<br/>           This function can only be used by customers in Australia in connection with the \"Termination Organizer\" function.</p> <ul><li>Retail: Interface between the product SAP StaffWorks and the SAP R/3 System</li></ul> <p>           Application component:  PA-PA-XX-ET<br/>           [<EMAIL>]<br/>           The interface between the product SAP StaffWorks and the SAP R/3 System consists of the following functions:</p> <ul><ul><li>Initial download of HR master data</li></ul></ul> <ul><ul><li>Upload of HR master data</li></ul></ul> <ul><ul><li>Upload of work schedules</li></ul></ul> <ul><ul><li>Upload of actual working times</li></ul></ul> <ul><ul><li>Download of time evaluation results</li></ul></ul> <p>           These functions are not generally released. These functions are only available for pilot customers. You can only use them after having consulted the responsible Product Manager.<br/>                                                                    )                                                                       n</p> <ol>3. <b>No release</b></ol> <p></p> <ul><li>Retail: POS Sales Audit - Editor</li></ul> <p>           Application component:  IS-R-IFC-AUD<br/>           The data change for the Sales Audit is released for productive use via the editor with Support Package 12.<br/></p> <ul><li>SCM: Partial goods receipt in decentralized WMS</li></ul> <p>           Application component:  LE-SHP-DL-LA<br/>           [<EMAIL>]<br/>           The \"Partial goods receipt in the decentralized WMS\" function is not delivered.<br/></p> <b><b>SAP R/3 Enterprise PLM Extension</b></b><br/> <p></p> <ol>1. <b>Release only with SAP's approval or after consultation with SAP.</b></ol> <p></p> <ul><li>Product and Process Modelling (PLM-PPM); ProductDesigner (PLM-PPM-PDN)</li></ul> <p>           Application components: PLM-PPM, PLM-PPM-PDN<br/>           [<EMAIL>]; [<EMAIL>]<br/>           Within the framework of the ProductDesigner function, only component LO-MD-PPE-CMP (Maintenance of product structures) is released for use within the iPPE.  Other functions of the iPPE, such as the components LO-MD-PPE-ACT (Maintenance of process structures) and LO-MD-PPE-FLO (Maintenance of factory layouts), can only be used with SAP's approval or after consultation with SAP and can only be used for documentation purposes.  Further process integration is not currently planned.<br/></p> <ul><li>iPPE</li></ul> <p>           Application component:  LO-MD-PPE<br/>           [<EMAIL>]; [<EMAIL>]<br/>           In the R/3 enterprise, the iPPE is only connected to the component PLM-PPM-PDN (ProductDesigner).  Only the component LO-MD-PPE-CMP (Maintenance of product structures) is used from the iPPE. All other components of the iPPE can only be used with SAP's approval or after consultation with SAP and can only be used for documentation purposes. Further process integration is not currently planned.<br/></p> <ul><li>Maintaining product structures.</li></ul> <p>           Application component:  LO-MD-PPE-CMP<br/>           [<EMAIL>]<br/>           Within the \"Maintenance of product structures\" function, the \"Creation of object dependencies using the new object dependencies editor\" and \"Focus in the iPPE\" functions are released only with SAP's approval or after consultation with SAP.<br/></p> <ul><li>Maintaining process structures</li></ul> <p>           Application component:  LO-MD-PPE-ACT<br/>           [<EMAIL>]; [<EMAIL>]<br/>           This function can only be used with SAP's approval or after consultation with SAP, and can only be used for documentation purposes.  Further process integration, or connection to other components is not currently planned.<br/></p> <ul><li>Maintaining factory layouts</li></ul> <p>           Application component:  LO-MD-PPE-FLO<br/>           [<EMAIL>]; [<EMAIL>]<br/>           This function can only be used with SAP's approval or after consultation with SAP, and can only be used for documentation purposes.  Further process integration, or connection to other components is not currently planned.<br/></p> <ol>2. <b>Release with restrictions</b></ol> <p></p> <ul><li>General restriction</li></ul> <p>           The EH&amp;S application is only available in German, English, Spanish, French, Italian, Japanese, Portuguese and Dutch. This applies only to the language of the application and is not a country restriction.<br/></p> <ol>3. <b>No release</b></ol> <p>- no entry -<br/></p> <b><b>SAP R/3 Enterprise SCM Extension</b></b><br/> <p>- no entry -<br/></p> <b><b>SAP R/3 Enterprise Financials Extension</b></b><br/> <p></p> <ol>1. <b>Release only with SAP's approval or after consultation with SAP.</b></ol> <p></p> <ul><li>Flexible Real Estate Management (Corporate and Commercial Real Estate)</li></ul> <p>           Application component: RE-FX<br/>           [<EMAIL>]<br/>           The restrictions are listed in detail in SAP Note 517673. <br/></p> <ol>2. <b>Release with restrictions</b></ol> <p></p> <ul><li>General restriction</li></ul> <p>           The RE application is only available in German and English. <br/></p> <ol>3. <b>No release</b></ol> <p>- no entry -<br/></p> <b><b>SAP R/3 Enterprise Joint Venture Accounting</b></b><br/> <p></p> <ol>1. <b>Release only with SAP's approval or after consultation with SAP.</b></ol> <p>- no entry -<br/></p> <ol>2. <b>Release with restrictions</b></ol> <p></p> <ul><li>Extended Withholding Tax</li></ul> <p>           Application component:  CA-JVA<br/>           [<EMAIL>]<br/>           You can activate JVA for company codes which use 'Country indicator Argentina' or 'Extended Withholding Tax'.  JVA can correctly process the special documents that can be generated in this context in the case of postings.  However, neither the JVA Integration Manager nor any other JVA application has a special handling for Extended Withholding Tax. Therefore, before this type of company code can be used, you must thoroughly test whether the function is sufficient for these special requirements.</p> <ul><li>Material Ledger and JVA function CRP</li></ul> <p>           Application component:  CA-JVA<br/>           [<EMAIL>]<br/>           Material Ledger (ML) and the JV function Current Replacement Prices (CRP) in Material Management (MM) do not function together. JVA without CRP does not present a problem.<br/> </p> <ul><li>Joint Venture Accounting - ALE</li></ul> <p>           Application component:  CA-JVA<br/>           [<EMAIL>]<br/>           ALE and JVA cannot be used together. The following are example of scenarios that are not supported:</p> <ul><ul><li>FI is separated and runs on different servers</li></ul></ul> <ul><ul><li>FI and MM are installed on different hosts</li></ul></ul> <ul><ul><li>FI and CO are installed on different hosts</li></ul></ul> <p>           This is not a complete list, but rather just some examples of ALE-scenarios that are not supported. JVA does not send any JVA documents to another host and cannot read documents of another host via ALE.  Once ALE is only used for master data, it should not be a problem for JVA. However, you should check whether any JVA information that is maintained is also transferred.<br/> </p> <ul><li>Joint Venture Accounting - Real Reversal</li></ul> <p>           Application component:  CA-JVA<br/>           [<EMAIL>]<br/>           The JVA Integration Manager does not support real reversals. All calling applications must, as before, create a reversal document and send it to JVA.<br/> </p> <ul><li>Joint Venture Accounting general</li></ul> <p>           Language restriction: JVA is only available in English. <br/>           [<EMAIL>]<br/>           </p> <ul><li>Production Sharing Agreement</li></ul> <p>           Application component:  CA-JVA-PSA<br/>           Language restriction: PSA is only available in English. <br/>           [<EMAIL>]<br/>           </p> <ol>1. <b>No release</b></ol> <p>           - no entry -<br/>           </p> <b><b>SAP R/3 Enterprise Incentive &amp; Comm. Management</b></b><br/> <p></p> <ol>1. <b>Release only with SAP's approval or after consultation with SAP.</b></ol> <p>- no entry -<br/></p> <ol>2. <b>Release with restrictions</b></ol> <ul><li>General restriction</li></ul> <p>           The application ICM (Commissions) is available only in German, English, French, Italian and Dutch.<br/></p> <ol>3. <b>No release</b></ol> <p>- no entry -<br/></p> <b><b>SAP R/3 Enterprise HR Extension</b></b><br/> <p></p> <ul><li>SAP Position Budgeting and Control</li></ul> <p>           Application component:  PA-PM*<br/>           Details about releases and ways of contacting SAP are given in separate SAP notes:</p> <ul><ul><li>SAP Note 606239 (EA-HR 1.10)</li></ul></ul> <ul><ul><li></li></ul></ul> <p></p> <ol>1. <b>Release only with SAP's approval or after consultation with SAP.</b></ol> <p></p> <ul><li>Concurrent employment </li></ul> <p>           Application components: PA-CE, PA-PA-CA-CE, PA-PA-US-CE, PY-US-CE, PY-CA-CE, PY-XX-CE, PT-CE, PA-BN-CE<br/>           [<EMAIL>]<br/>           The application \"Concurrent employment\" is only released with SAP's approval.</p> <ul><li>Management of/payroll for global employees </li></ul> <p>           Application components: PA-GE, PY-XX-GP<br/>           [<EMAIL>]<br/>           The application \"Management of/payroll for global employees\" is only released with SAP's approval.<br/>           <br/></p> <ol>2. <b>Release with restrictions</b></ol> <p>- no entry -<br/></p> <ol>3. <b>No release</b></ol> <p>- no entry -<br/></p> <b><b>SAP R/3 Enterprise Travel Extension</b></b><br/> <p>- no entry -<br/></p> <b><b>SAP R/3 Enterprise Retail</b></b><br/> <p></p> <ol>1. <b>Release only with SAP's approval or after consultation with SAP.</b></ol> <p>- no entry -<br/></p> <ol>2. <b>Release with restrictions</b></ol> <p></p> <ul><li>Sales order in SAP Retail Store in the frog design (Topic 99)</li></ul> <p>           Application component:  LO-SRS<br/>           The use of the sales order in the SAP Retail Store in the frog design (Topic 99) can only be used as of Support Package 02 without restrictions.<br/></p> <ul><li>MDE-WE Inbound in the SAP Retail Store when using deliveries as a reference document.</li></ul> <p>           Application component:  LO-SRS<br/>           You can only use \"MDE-WE Inbound in the SAP Retail Store with the use of deliveries as a reference document\" without restrictions as of Support Package 02.<br/> <br/></p> <ul><li>Prepack allocation planning (Transaction WSTN11, WSTN13, WSTN14, WSTN15)</li></ul> <p>           Application component:  IS-R-BD-ART<br/>           You can use the \"Prepack allocation planning\" function (Transactions  WSTN11, WSTN13, WSTN14 and WSTN15) with restrictions as of Support Package 10 of Release 4.70. The use of delivery phases is not supported during the planning of the allocation table and putaway quantities.<br/></p> <ol>3. <b>No release</b></ol> <p> <br/></p> <ul><li>Mass change purchase order</li></ul> <p>           Application component:  IS-R-PUR-MPO<br/>           The \"Mass change purchase order\" function is not released for productive use.<br/>The restriction is lifted with Support Package 06 of EA-Retail.<br/></p> <b><b>SAP R/3 Enterprise Global Trade </b></b><br/> <p></p> <ol>1. <b>Release only with SAP's approval or after consultation with SAP.</b></ol> <p></p> <ul><li>General restriction</li></ul> <p>           [<EMAIL>]; [<EMAIL>]<br/>           The Enterprise Extension Global Trade can generally only be used after consultation with SAP and is restricted to Germany (DE), Switzerland (CH), The Netherlands (NL), Canada (CA), China (CN), Indonesia, Hong Kong, Malaysia, Japan, The Philippines, South Korea, Singapore, Taiwan and Thailand.<br/></p> <ul><li>Trading Execution Workbench, Expenses Management</li></ul> <p>           Application components: LO-GT-TEW; LO-GT-TE<br/>           [<EMAIL>]; [<EMAIL>]<br/>           Currently, the following functions:</p> <ul><ul><li>LO-GT-TEW - Trading Execution Workbench</li></ul></ul> <ul><ul><li>LO-GT-TE - Expenses Management</li></ul></ul> <p>           can only be used after consultingSAP. These functions are not released unless the position management functions are required.<br/></p> <ol>2. <b>Release with restrictions</b></ol> <p>- no entry -<br/></p> <ol>3. <b>No release</b></ol> <p></p> <ul><li>Position Management</li></ul> <p>           Application component:  LO-GT-PM<br/>           Position Management is not released for productive use. <br/></p> <b><b>SAP R/3 Enterprise Public Services</b></b><br/> <p></p> <ol>1. <b>Release only with SAP's approval or after consultation with SAP.</b></ol> <p></p> <ul><li>SAP Grants Management</li></ul> <p>           Application component:  PSM-GM*<br/>           [<EMAIL>]<br/>           The new development \"SAP Grants Management\" can currently only be used by pilot customers after consultation with the product management of the IBU Public Services.<br/></p> <ul><li>\"Functions for US Federal Government\" and \"Specials functions for US Federal Government\"</li></ul> <p>           Application components: PSM-FG*; PSM-FM-US<br/>           [<EMAIL>]<br/>           The function is only generally released for the customer group US Federal Government. Other customers may only use these functions after consulting the product management of the IBU Public Services.<br/></p> <ul><li>Budget Control System</li></ul> <p>           Application component:  PSM-FM-BCS<br/>           [<EMAIL>]<br/>           The new development \"Budget Control System\" includes the redesign of the budget processor currently used in Funds Management. These functions can only be used by pilot customers after consulting the product management of the IBU Public Services.<br/><br/></p> <ol>2. <b>Release with restrictions</b></ol> <p></p> <ul><li>Special functions for Public Sector Germany</li></ul> <p>           Application component:  PSM-FM-DE<br/>           The additional functions for \"Public Sector Germany\" are only released for customers German Federal States and Local Authorities. These functions are:</p> <ul><ul><li>the master record objects</li></ul></ul> <ul><ul><li>the additional functions in the budgeting area (collective expenditure and net voting among others)</li></ul></ul> <ul><ul><li>the additional functions in the cash area (FMITPO, FMBELI, FMSHERLOCK, among others)</li></ul></ul> <ul><ul><li>other functions, for example the enterprises of the commercial type, cash posting day</li></ul></ul> <ul><ul><li>the specific reporting for this group of customers</li></ul></ul> <p>           Other customer groups cannot use these functions.<br/></p> <ul><li>Special functions for the customer European Union</li></ul> <p>           Application component:  PSM-FM*<br/>           The following functions are only released for customer \"European Union\":</p> <ul><ul><li>Within the collection expenditures, the distribution of the actual values (Transaction FMND).</li></ul></ul> <ul><ul><li>Recovery requests and payment requests (Transactions F802 - F816 as well as F832 - F836)</li></ul></ul> <ul><ul><li>Earmarked funds (Transactions FMW5, FMX5, FMY5, FMZ5, FMV5)</li></ul></ul> <ul><ul><li>Reassignment tool (Transactions FMD1, FMD2, FMDS, FMCG, FMCB, FMCT, FMCR, FMCD)</li></ul></ul> <ul><ul><li>The enhancements in the payment program (Transaction F111) as well as in the collective expenditures for the European Union.</li></ul></ul> <p></p> <ul><li>General restriction</li></ul> <p>           For productive customers who use an R/3 release prior to 4. 5, note that the conversion programs for transaction data (RFFMTRNS, etc.) will only be completely available for the upgrade to Release R/3 4.70 and EA-PS 1.10 as of 4.70 Support Package 01.<br/></p> <ol>3. <b>No release</b></ol> <p>- no entry -</p> <b><b>SAP R/3 Enterprise Financial Services</b></b><br/> <p></p> <ol>1. <b>Release only with SAP's approval or after consultation with SAP.</b></ol> <p></p> <ul><li>Financial product listed options</li></ul> <p>           Application component:  CFM-TM<br/>           [<EMAIL>]<br/>           For \"Transaction and position management for listed options\" that consists of the following subfunctions:</p> <ul><ul><li>Transaction entry and transaction processing including reversal.</li></ul></ul> <ul><ul><li>Correspondence</li></ul></ul> <ul><ul><li>Journal</li></ul></ul> <ul><ul><li>Posting movements</li></ul></ul> <ul><ul><li>Setting transactions</li></ul></ul> <ul><ul><li>Generation and setting of derived business transactions</li></ul></ul> <ul><ul><li>Valuation</li></ul></ul> <ul><ul><li>Generation and posting of margin movements</li></ul></ul> <ul><ul><li>Reporting</li></ul></ul> <p>           no functions are implemented that allow the user to perform listed options or to let them expire.  In addition, the transaction management for listed options is not connected to the correspondence function.</p> <ul><li>New development Statutory Reporting (Development class VVSRFISL)</li></ul> <p>           Application component:  FS-SR-DE<br/>           [<EMAIL>]<br/>           The new development \"Statutory Reporting\" consists of the following functions:</p> <ul><ul><li>RWIN adjustment</li></ul></ul> <ul><ul><li>Migration/follow-up posting</li></ul></ul> <ul><ul><li>Position management</li></ul></ul> <ul><ul><li>Master data entry</li></ul></ul> <ul><ul><li>Premium reserve fund transfer</li></ul></ul> <ul><ul><li>Layout control ALV/Smartforms</li></ul></ul> <ul><ul><li>Reporting</li></ul></ul> <p>           replaces a component that is already used productively and will only be completely released after our first pilot customer has successfully implemented the development.<br/>           If you use FS-SR-DE under Bank/CFM 2. 0, an upgrade to Enterprise SAP R/3 47x110 is currently not possible. In this case, contact SAP.<br/></p> <ol>2. <b>Release with restrictions</b></ol> <p></p> <ul><li>General restriction</li></ul> <p>           The translation into Swedish is not complete.</p> <ul><li>Financial product repurchase agreements</li></ul> <p>           Application component:  CFM-TM<br/>           Function \"Transaction and position management for repurchase agreements\" that consists of subfunctions.</p> <ul><ul><li>Transaction entry and transaction processing including reversal.</li></ul></ul> <ul><ul><li>Correspondence</li></ul></ul> <ul><ul><li>Journal</li></ul></ul> <ul><ul><li>Posting movements</li></ul></ul> <ul><ul><li>Setting transactions</li></ul></ul> <ul><ul><li>Generation and setting of derived business transactions</li></ul></ul> <ul><ul><li>Valuation</li></ul></ul> <ul><ul><li>Reporting</li></ul></ul> <p>           is only released for use in Denmark, because it does not contain the complete functions that are required for use in other countries. In addition, the transaction management for repurchase agreements is not connected to the correspondence function.</p> <ul><li>Financial product forward security transaction</li></ul> <p>           Application component:  CFM-TM<br/>           Function \"Transaction and position management for forward security transactions\" that consists of the following subfunctions:</p> <ul><ul><li>Transaction entry and transaction processing including reversal.</li></ul></ul> <ul><ul><li>Correspondence</li></ul></ul> <ul><ul><li>Journal</li></ul></ul> <ul><ul><li>Posting movements</li></ul></ul> <ul><ul><li>Setting transactions</li></ul></ul> <ul><ul><li>Generation and setting of derived business transactions</li></ul></ul> <ul><ul><li>Valuation</li></ul></ul> <ul><ul><li>Reporting</li></ul></ul> <p>           is only released for use in Denmark, because it does not contain the complete functions that are required for use in other countries. In addition, the transaction management for repurchase agreements is not connected to the correspondence function.<br/></p> <ul><li>Loans - Processing the business partner Financial Services from loan transactions</li></ul> <p>           Application component:  FS-CML<br/>           The functions to create and change the business partner (BP) in the contract administration and in the borrower change are not released. When a BP is assigned to a contract or to a borrower change, it has to exist in the required role. Generally, the function of the partner assignment in the contract administration is critical. The function will be released with one of the first Support Packages.<br/></p> <ol>3. <b>No release</b></ol> <p></p> <ul><li>Business partner - Navigate to the maintenance of a business partner from another object, in particular from the loan contract (CML).</li></ul> <p>           Component: FS-BP<br/>           The function will be released with one of the first Support Packages.<br/></p></div>", "noteVersion": 24}, {"note": "765810", "noteTitle": "765810 - Info text for the non-activation of RE-FX in Release 1.10", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>According to Note 517673, releases will no longer be issued in Release 1.10 for the use of the flexible Real Estate Management (RE-FX).<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>Use the flexible Real Estate Management as of Release 2.00 or higher.<br/>Due to the program corrections of this note, the documentation for the activation of RE-FX as well as the corresponding Customizing dialog are adjusted. Now refer to the circumstance described under \"Symptom\".</p></div>", "noteVersion": 2}, {"note": "1009567", "noteTitle": "1009567 - Functional differences between SAP Interactive Forms and Smart Forms", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>At the moment, several functional differences exist between the solutions Smart Forms and SAP Interactive Forms by Adobe.  This note lists the functional limitations of the SAP Interactive Forms by Adobe solution when compared to Smart Forms.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Adobe Document Services, Adobe LiveCycle Designer</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>When choosing a forms solution, ensure that you compare your requirements with the features offered by the respective forms solution.<br/><br/>The following functional differences exist at this time:</p>\n<ol>\n<li>Page numbering of the print job<br/><br/>There is no page counter for the numbers of pages in a print job, but only one for the pages of a form.<br/> </li>\n<li>Page numbering mode<br/><br/>Settings such as \"Initialize Counter\" or \"Increase Counter\" are set in a different way, scripting may be required.<br/> </li>\n<li>Copy window and copy counter<br/><br/>Not supported.<br/> </li>\n<li>Final window<br/><br/>Final windows as such are not supported.  Often, however, the concept can be realized using scripting and the different events.<br/> </li>\n<li>Styles<br/><br/>Styles such as the ones in Smart Forms cannot be defined in a central location and used in multiple forms.<br/> </li>\n<li>Large documents<br/><br/>At present, there is a limit for the document size: On Microsoft Windows platforms, this is around 1,000 pages, and on UNIX or Linux systems, it is around 2,000 pages. SAP and Adobe recommend to generate no more than 800 and 1,800 pages, respectively in one document.  This depends on the complexity of the form and the data. <br/><br/>Generating large documents can even cause problems with only a few hundred pages, because the data stream is transmitted as part of a SOAP message, which can result in memory bottlenecks during the parsing of the XML document.  <br/><br/>To prevent memory bottlenecks during transfer, you can change the type of data transfer as of Netweaver 7.0 Support Package 6 for a double stack installation or as of NetWeaver 7.0 Support Package 12 for a separate ABAP Java installation. For information about the roles and table entries to be used, see SAP Note 993612.<br/><br/>In either case, however, the limit of approx. 1,000 pages (up to 2,000 pages depending on the platform) per document will remain.<br/> </li>\n<li>Page protection<br/><br/>Among others, page protection can be set for subforms level.  Page protection based on paragraphs (\"Paragraph protected\" or \"Next paragraph same page\") in Smart Forms text modules or SAPscript Include texts is not supported.<br/> </li>\n<li>Outline paragraphs<br/><br/>Outline paragraphs in Smart Forms text modules or SAPscript Include texts are only partially supported. In particular, it is not possible to define the numbered margin independently of the left margin of the text.  The numbering is always part of the text.  It is not possible to initialize the numbering in the form context.<br/> </li>\n<li>Tabs<br/><br/>It is not possible to set tabs in static texts.  There is no support for all types of tab orientation. It may make sense to use tables.<br/><br/>The default tab step width is ignored.<br/> </li>\n<li>Using graphics stored in the SAPscript graphic administration (transaction SE78)<br/><br/>Not supported.<br/> </li>\n<li>Background graphics<br/><br/>Background graphics are not supported in the same way as Smart Forms.<br/> </li>\n<li>SAPscript Include texts and Smart Forms text modules<br/><br/>Not all SAPscript or Smart Forms functions are supported. See, for example, SAP Notes 1008550, 863893, 894389 and the online documentation. These are some of the limitations: SAPscript styles, control commands, page protection, SAP characters, symbols, orientation of fields (for decimal separators using tabs, for example).<br/><br/>Fields cannot be used if they originate from tables.<br/><br/>Note that the form output terminates during the output of unknown fields.<br/> </li>\n<li>Sorting, automatic calculation<br/><br/>Not supported. If possible, this should be included in the application program.  You must use scripting for calculations that must be made based on the page break (page subtotals, for example).<br/> </li>\n<li>Coding nodes in the context<br/><br/>Not supported.<br/> </li>\n<li>Explicit page break in the context<br/><br/>Not supported. It may be possible to realize a solution using scripting.<br/> </li>\n<li>Paper trays<br/><br/>Not supported in SAP NetWeaver 2004.<br/><br/>For SAP NetWeaver 7.0, the XDC scenario guide describes possible adjustments. The XDC files must be adjusted for this. Support Package 14 is required (ADS Version &gt;= 800.20070708051308.406522).<br/><br/>Printer tray selection is supported with additional printer options for PCL and PostScript device types. See SAP Note 1806471 for more information.<br/> </li>\n<li>Duplex printing<br/><br/>Supported in NetWeaver 2004 as of Support Package 18. For this and for releases of NetWeaver 7.0 older than Support Package 14, adjustment options are described in the XDC Scenario Guide. See Note 766410.<br/><br/>As of SAP NetWeaver 7.0 SP14 (ADS Version &gt; = 800.20070708051308.406522), a more flexible design is possible: You can adjust the duplex printing in Adobe LiveCycle Designer for the relevant form. Prerequisite is Adobe LiveCycle Designer as of Version 8.0.<br/><br/>Duplex printing is supported with additional printer options for PCL and PostScript device types. See SAP Note 1806471 for more information.<br/> </li>\n<li>Print controls<br/><br/>Not supported.<br/> </li>\n<li>Bar codes<br/><br/>For a list of supported bar codes, see the Adobe LiveCycle Designer documentation.<br/> </li>\n<li>Fonts OCR and MICR<br/><br/>Supported. However, the font is required for the design time and at runtime. An XDC adjustment is required.<br/><br/>The OCR and MICR fonts are delivered accordingly as of SAP NetWeaver 7.20 und SAP NetWeaver 7.31 ADS. See SAP Note 2018817 for more information.<br/> </li>\n<li>OTF output format<br/><br/>Not supported.<br/> </li>\n<li>ASCII output format<br/><br/>Not directly supported. In particular, dot matrix printers are not directly supported.<br/>Printing is possible using PDFPRINT (see Note 1444342).<br/> </li>\n<li>Other print formats: Prescribe, IGP<br/><br/>Not directly supported. Printing is possible using PDFPRINT (see SAP Note 1444342).<br/><br/>The supported formats are PostScript, PCL and ZPL.<br/>As of SAP NetWeaver 7.20 ADS, the formats IPL, TPCL, and DPL are also supported.<br/> </li>\n<li>HTML output<br/><br/>Not supported.<br/> </li>\n<li>XSF or XDF<br/><br/>Not supported. However, there is a similar interface called XFP. The certificate is \"BC-XFP 6.40\".<br/> </li>\n<li>Callbacks to other programs during form output<br/><br/>Not supported.<br/> </li>\n<li>Fixed user values<br/><br/>The decimal and date display in the user defaults is ignored.  To output a decimal figure or a date, the technology uses only the locale information passed to the form at runtime, e.g. de_DE, en_US etc.<br/> </li>\n<li>Partial printing<br/><br/>It is not possible to print only parts of a form (neither directly when calling the template nor later from the spool overview).<br/> </li>\n<li>Output medium (DEVICE) MAIL/TELEFAX<br/><br/>Not supported. Before you execute the application, you must request a PDF. You must call the send interface (BCS) separately.<br/> </li>\n<li>Grouping copies<br/><br/>You cannot group the output of copies, for example, first three times page 1, then page 2, and so on.<br/> </li>\n<li>Forms in languages in which the text flows from right to left.<br/><br/>The text flow direction from right to left is supported at field level for Hebrew and Arabic as of NetWeaver 7.0 Support Package 6 (see SAP Note 886572). Note that, for subforms, the text flow direction from right to left (RTL) cannot be specified. In addition, the layout is not automatically mirrored as in Smart Forms. The text flow direction RTL in subforms and layout mirroring is supported as of NetWeaver 7.31 and Adobe LiveCycle Designer 9.8. For more information, see Note 1539317.<br/> </li>\n<li>Sending and downloading ADS requests from SP01<br/><br/>Supported with restrictions. See SAP Notes 1291734 and 1717357.<br/> </li>\n<li>Cover page<br/><br/>Not supported.</li>\n</ol>", "noteVersion": 38}, {"note": "771098", "noteTitle": "771098 - Countries: Release restriction RE-FX", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>This SAP Note concerns the release limitations for SAP Real Estate Management.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Classic Real Estate, Flexible Real Estate, globalization, localization, countries, legal compliance</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>You are considering using the new SAP Real Estate Management (Flexible Real Estate). You do not know whether SAP has released SAP Real Estate Management for use in your country or whether it meets the legal requirements for Real Estate.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><span><strong>1. Localization requirement and release procedure.</strong></span></p>\n<p>SAP Real Estate Management is a solution with close references to legal standards. In particular, these include tenancy law, tax depreciation and commercial law. To some extent, standards differ greatly from country to country. Localization is particularly important for contract management and during integration into financial accounting.</p>\n<p>a) \"Classic\" versus \"Flexible\" Real Estate</p>\n<p>Note that localizations are only considered for the new Real Estate solution (Flexible Real Estate) and can no longer be provided for the old Real Estate solution (Classic Real Estate).</p>\n<p><strong>For this reason, all information in this SAP Note refers to the Flexible Real Estate version only (new solution).</strong><br/><br/><strong>For information about generic, that is, Flexible Real Estate features that are not localization-related, see SAP Note 517673.</strong></p>\n<p>b) Release process</p>\n<p>Before SAP releases Real Estate Management for implementation in a country, SAP local product management first checks whether the standard SAP Real Estate Management can already cover the national legal requirements.</p>\n<p>If the standard system lacks the functions to meet such local requirements, SAP checks whether and when this can be made available. SAP Real Estate Management is only released for the relevant country if SAP is sure that the relevant minimum legal requirements are met for the majority of customers.</p>\n<p>c) Scope of localization</p>\n<p>In general, the localization scope is limited to the legal requirements that are important for the majority of Real Estate managers in the country. In particular, this means that legal requirements for special industries (for example, for subsidized housing construction) are generally not developed for the standard system.</p>\n<p>The localization scope for the relevant country that is determined by the relevant SAP product management is described below in the country release and localization scope section. No further development of specific functions is planned for the relevant country.</p>\n<p>d) Restrictions</p>\n<p>Localization may be limited to certain subject areas or industries. For more information, see the individual notes about localization in the individual countries (if these are available).</p>\n<p>Certain topics are generally excluded from localization outside of Germany. These include the following functions that exist in the standard system as of SAP ERP 6.0:</p>\n<p>- Land use management</p>\n<p>- Condominium owners administration</p>\n<p>- Correction items</p>\n<p>- Integration into the industry solution for the Public Sector (PSCD)</p>\n<p>For more information, refer to the notes on localization in the individual countries, if they are available.</p>\n<p> </p>\n<p><span><strong>2. Language availability</strong></span></p>\n<p>Note that some of the languages in which SAP Real Estate Management (Flexible Real Estate) is available are not associated with a planned 'localization and release' or a 'localization and release' that has already taken place.</p>\n<p>Languages for the \"Office, Retail and Industrial Property Mgmt\" and \"Residential Property Management\" price list components</p>\n<p>These components are currently available in the following languages (the information in parentheses describes the first release and the associated Service Package (SP) or Service Release (SR) as of which the translation is available. If there is no additional information, this means that the next higher release already contains the translation as of the first Service Package or Service Release):</p>\n<p>- Arabic (SAP ERP 6.0 Enhancement Package 5 and higher releases)</p>\n<p>- (Simplified) Chinese/Mandarin (SAP ERP 2004 SP 12, SAP ERP 6.0 SR 2 and higher releases)</p>\n<p>- (Traditional) Chinese (SAP ERP 6.0 Enhancement Package 6 or higher)</p>\n<p>- Danish (SAP ERP 2004 SP 12, SAP ERP 6.0 SR 2 and higher releases)</p>\n<p>- German (R/3 Enterprise Financials Extension Set 2.00 SP 1 and higher releases)</p>\n<p>- English (R/3 Enterprise Financials Extension Set 2.00 SP 1 and higher releases)</p>\n<p>- Finnish (SAP ERP 2004 SP 12, SAP ERP 6.0 SR 2 and higher releases)</p>\n<p>- French (R/3 Enterprise Financials Extension Set 2.00 SP 10 and higher releases)</p>\n<p>- Hebrew (SAP ERP 2004 SP 8 and higher releases)</p>\n<p>- Italian (R/3 Enterprise Financials Extension Set 2.00 SP 1 and higher releases)</p>\n<p>- Japanese (R/3 Enterprise Financials Extension Set 2.00 SP 13, SAP ERP 2004 SP 1 and higher releases)</p>\n<p>- Korean (SAP ERP 2004 SP 12, SAP ERP 6.0 SR 2 and higher releases)</p>\n<p>- Croatian (SAP ERP 6.0 Enhancement Package 7 and higher releases)</p>\n<p>- Dutch (SAP ERP 2004 SP 1 and higher releases)</p>\n<p>- Norwegian (SAP ERP 6.0 SR 9 and higher releases)</p>\n<p>- Polish (R/3 Enterprise Financials Extension Set 2.00 SP 12, SAP ERP 2004 SP 14, SAP ERP 6.0 SR 2 and higher releases)</p>\n<p>- Portuguese (SAP ERP 2004 SP 1 and higher releases)</p>\n<p>- Romanian (SAP ERP 6.0 Enhancement Package 7 and higher releases)</p>\n<p>- Russian (SAP ERP 2004 SP 1 and higher releases)</p>\n<p>- Slovak (SAP ERP 6.0 Enhancement Package 3 and higher releases)</p>\n<p>- Spanish (R/3 Enterprise Financials Extension Set 2.00 SP 10, SAP ERP 2004 SP 7, SAP ERP 6.0 SP01 and higher releases)</p>\n<p>- Swedish (planned for SAP ERP 6.0 Enhancement Package 7, Service Package 5 and higher releases)</p>\n<p>- Czech (SAP ERP 6.0 SP 1 and higher releases)</p>\n<p>- Turkish (planned for SAP ERP 6.0 Enhancement Package 7, Service Package 12 and higher releases)</p>\n<p>- Hungarian (SAP ERP 2004 SP 15, SAP ERP 6.0 SR 8 and higher releases)</p>\n<p>Languages for the \"land use management\" price list component</p>\n<p>The following functions of this component are available in the same language as the price list components under a):</p>\n<p>- General land use management</p>\n<p>- Parcel management</p>\n<p>- Land register management</p>\n<p>The following functions:</p>\n<p>- Parcel - parcel update management</p>\n<p>- Joint liability in the land register</p>\n<p>- Other public registers</p>\n<p>Notice of assessment of the \"land use management\" price list component is available in the following languages:</p>\n<p>- German (R/3 Enterprise Financials Extension Set 2.00 SP 1 and higher releases)</p>\n<p>- English (R/3 Enterprise Financials Extension Set 2.00 SP 1 and higher releases)</p>\n<p>- Russian (SAP ERP 2004 SP 1 and higher releases)</p>\n<p> </p>\n<p><span><strong>3. Country release and scope of localization</strong></span></p>\n<p>The following is a list of countries for which SAP Real Estate Management was released in the new version (Flexible Real Estate), it informs you as of which release this is the case, and which country-specific functions are made available. Please also follow the documentation on the individual releases, enhancement packages and business functions that is to be found in the SAP Help Portal.</p>\n<p>a) <strong>Belgium</strong> (SAP ERP 2004 and higher releases): No country-specific functions are provided.</p>\n<p>b) <strong>China</strong> (SAP ERP 6.0 and higher releases): Released for the administration of office and retail real estate objects. No country-specific functions are provided. Not released for the administration of residential real estate objects, industrial plants, and real estates.</p>\n<p>c) <strong>Denmark</strong> (SAP ERP 2004 and higher releases): No country-specific functions are provided.</p>\n<p>d) <strong>Germany</strong> (R/3 Enterprise and higher releases)<br/><br/>SAP ERP 2004 and higher releases</p>\n<p>Rent increase up to the standard local comparative rent (adjustment based on representative list of rents) according to § 558 of the German Civil Code (BGB)</p>\n<p>- Condition adjustment according to comparative rent procedure</p>\n<p>SAP ERP 6.0 and higher releases:</p>\n<p>- Rent increase for modernization in accordance with § 559 of the German Civil Code (BGB)</p>\n<p>- Calculation of the apportionment loss risk in service charge settlement in accordance with the (German) second calculation regulations </p>\n<p>- Input tax distribution for CO objects based on option rates of assigned real estate objects</p>\n<p>- Administration of condominium owners' associations and condominium ownerships including annual budget and assessment settlement in accordance with the German Condominium Act (WEG)</p>\n<p>- Land use management including the administration of land registers, parcels from the real estate cadaster and other directories such as easements, site protection and contaminations of sites in accordance with German public law. <strong><strong>Land use management can be used in countries other than Germany only within the scope of functions provided. Local requirements and legal changes are not necessarily supported in this case. Therefore, before the implementation, the feasibility of an introduction must be investigated in consultation with the relevant SAP international subsidiary. </strong></strong></p>\n<p>- Data Retention Tool (DART) and Audit Information System (AIS) in accordance with the German Principles of Data Access and Verifiability of Digital Documentation (GDPdU)</p>\n<p>- As of Support Package 6 and higher for SAP ERP 6.0, the connection to regulatory reporting for insurance companies (BAFin connection) is available according to German requirements. </p>\n<p><span>Planned for later Releases</span><br/><br/>- Condition adjustment based on the cost efficiency analysis in accordance with the (German) second calculation regulations </p>\n<p>- Administration and processing of the correction items in accordance with § 15 a of the German sales/purchases tax law (UStG) (see also SAP Notes 964834 and 1240658)</p>\n<p>For more information, see SAP Note 517673.</p>\n<p>e) <strong>Finland</strong> (SAP ERP 2004 and higher releases): No country-specific functions are provided.</p>\n<p>f) <strong>India</strong> (Service Package 12 for SAP ERP 6.0 for pilot customers and higher releases)</p>\n<p>- Enhancements to the withholding tax area</p>\n<p>Enhancements are planned in the following area for future Releases:</p>\n<p>- Deferred tax</p>\n<p>For more information, see SAP Note 1029613.</p>\n<p>g) <strong>Israel</strong> (SAP ERP 2004 and higher releases)</p>\n<p>No country-specific functions are provided.</p>\n<p>h) <strong>Italy</strong> (SAP ERP 6.0 and higher releases)</p>\n<p>The following country-specific functions are provided:</p>\n<p>- Imposta di Bollo (stamp tax)</p>\n<p>- Imposto di Registro (register tax)</p>\n<p>- Imposta Comunale sugli Immobili (property taxes and fees)</p>\n<p>For more information, see SAP Note 872301.</p>\n<p>i) <strong>Japan</strong> (SAP ERP 6.0 Service Package 9 or Enhancement Package 2 or higher releases; only for pilot customers))</p>\n<p>- Accrual/deferral of taxes (only as of SAP ERP Enhancement Package 2 or higher)</p>\n<p>- Settlement of service charges by consumption at defined unit prices</p>\n<p>- Special reporting for real estate managers in Japan</p>\n<p>For more information, see SAP Note 928175.</p>\n<p>j) <strong>Canada</strong> (SAP ERP 2004 and higher releases): No country-specific functions are provided.</p>\n<p>k) <strong>Netherlands</strong> (SAP ERP 6.0 and higher releases)</p>\n<p>The following country-specific functions will be available as of SAP ERP 6.0 Enhancement Package 5 and higher releases.</p>\n<p>- Flexible management of fixtures and fittings characteristics points</p>\n<p>- Adjustment of rent average (Huurbeleid)</p>\n<p>- Rent subsidies (Huurtoeslag)</p>\n<p>In addition, the following is available as of Enhancement Package 5 for SAP ERP 6.0 and higher releases:</p>\n<p>- Report on rent subsidies</p>\n<p>For more information, see SAP Note 1328779.</p>\n<p>l) <strong>Austria</strong> (SAP ERP 6.0 and higher releases)</p>\n<p>The following country-specific functions are provided:</p>\n<p>- Condition adjustment: 15th adjustment</p>\n<p>- Service charge settlement: Current occupancy principle</p>\n<p>- Correspondence: Selected country-specific settings</p>\n<p>- Main rent settlement</p>\n<p>For more information, see SAP Note 890267.</p>\n<p>m) <strong>Poland</strong></p>\n<p>- No country-specific functions are provided.</p>\n<p>n) <strong>Portugal</strong> (for pilot customers as of SAP ERP 6.0 Enhancement Package 3 and higher releases)</p>\n<p>- Communal property tax (parcel management of the price list component \"land use management\" is released for this purpose for Portugal)<br/>- Stamp tax<br/>- Rent adjustment according to the new tenancy law that came into force in Portugal on June 28, 2006. Read SAP Note 953883 for details and restrictions of localization for Portugal.</p>\n<p>o) <strong>Switzerland</strong> (SAP ERP 6.0 and higher releases)<br/>- Rent adjustment according to Swiss Law (according to relative and absolute methods) - calculation of fuel consumption according to FiFo <br/>- External heating expenses settlement with data medium exchange for Switzerland<br/>- Lease-in with ESR reference<br/>- Correspondences: Selected country-specific settings<br/>Enhancements are planned in the following area for future releases:<br/>- Contract settlement and remuneration in residential rental. Read SAP Note 91229 for details.</p>\n<p>p) <strong>Slovakia</strong> (Enhancement Package 5 for SAP ERP 6.0 and higher releases)</p>\n<p>- Calculation of the property tax (including \"Gebäudesteuer (EN: building tax)\", \"Grundstückssteuer (EN: land tax)\" and \"Wohnungssteuer\" (EN: dwelling tax)\" and creation of the normal, supplementary and corrected tax return</p>\n<p>- Posting of the costs, payables and installation payments according to the local township rules</p>\n<p>- Year-end closing</p>\n<p>p) <strong>Czech Republic</strong> (Enhancement Package 5 for SAP ERP 6.0 and higher releases)</p>\n<p>- Downloading of key lists from the web page of the Ministry of Finance</p>\n<p>- Calculation of the property tax and creation of the normal, supplementary and corrected tax return</p>\n<p>- Posting of the monthly accruals and installation payments<br/><br/>For more information, see SAP Note 576345.</p>\n<p>r) <strong>United Kingdom</strong> (SAP ERP 2004 and later releases)</p>\n<p>The support of quarter days is available as a country-specific function. For more information, see SAP Note 914067.</p>\n<p>r) <strong>United States of America</strong> (SAP ERP 2004 and later releases)</p>\n<p>The tax jurisdiction code is available as a country-specific function.</p>\n<p>For information about displaying the requirements of the FASB 13 (Financial Accounting Standards Board) compliance in accordance with US GAAP (Generally Accepted Accounting Principles), see SAP Note 920772.</p>\n<p>To map the request according to the planned new IFRS for rents, read SAP Notes <a class=\"jive-link-external-small\" href=\"/notes/1682715\" target=\"_blank\">1682715</a> and <a class=\"jive-link-external-small\" href=\"/notes/1933458\" target=\"_blank\">1933458</a>.</p>\n<p>s) <strong>Hungary</strong> (Enhancement Package 5 for SAP ERP 6.0 and later releases)</p>\n<p>- Identification and creation of correction invoices and cancellation invoices</p>\n<p>- Monthly report for posting documents that were not yet invoiced and printed</p>\n<p>Additionally: Enhancement Package 5 Service Package 08 for SAP ERP 6.0 and later releases.</p>\n<p>- Determination of the service date in posting and invoice creation</p>\n<p>- Numbering of the invoice copies</p>\n<p>- Execution of one-time processes</p>\n<p>- Execution of service charge settlements</p>\n<p>For details and restrictions see SAP Note 1353606.</p>\n<p> </p>\n<p><span><strong>4. Cross-country functions.</strong></span></p>\n<p>The following cross-country functions exist for SAP Real Estate Management (Flexible Real Estate):</p>\n<p><strong>Withholding tax</strong><br/>This function is relevant for various countries. Currently, withholding tax is only released for India, Portugal and Spain. The following restrictions exist:</p>\n<p>- It is not possible to use this function in conjunction with the installment payment function</p>\n<p>- It is not possible to use this function in conjunction with special G/L indicators.</p>\n<p>The function will only be released for other countries within the framework of the relevant country-specific release.</p>\n<p><br/><strong>Deferred tax</strong><br/>This function is relevant for France, for example. Deferred tax can only be calculated if you post the advance payments as revenues. Special G/L indicators are not supported. For more information, see SAP Note 842785.</p>\n<p>Note that this list is not final.</p>\n<p> </p>\n<p><span><strong>5. Pilot customers</strong></span></p>\n<p>Pilot customer projects must be agreed with the local SAP product management before they begin. Generally, the number of pilot customer projects per country is restricted on a quantity basis. Pilot projects are only authorized with observance of particular conditions such as the support of experienced consultants and close cooperation with SAP product management. For more information, contact your local SAP product management.</p>\n<p> </p>\n<p><strong><span>6. Consequences of missing localization and release</span></strong></p>\n<p>If SAP Real Estate Management is not released for a country, SAP cannot guarantee support for the legal minimum requirements in the relevant country. Therefore, SAP has no obligation to make those changes in the software that would be necessary to adjust it to the legal requirements. Furthermore, as part of maintenance, SAP cannot offer any consulting support for adjusting the software to the legal requirements.</p>\n<p>If you intend to implement SAP Real Estate Management without localization and release in your country (at your own risk), you must make all of the adjustments to the local legal requirements within the framework of the project at your own expense. SAP cannot guarantee that such project-based solutions are possible in the standard system. To lower the risks associated with such an implementation, we recommend that SAP Consulting carries out a detailed feasibility study before the project starts, and that you that work closely with the local SAP Product Management and Real Estate Consulting in the relevant country as soon as you start the project.</p>\n<p> </p>\n<p><strong>7. Important note</strong></p>\n<p>Note that all of plans mentioned in this note are without obligation, are not exhaustive and can change at short notice. In particular, SAP cannot guarantee that a localized version of SAP Real Estate Management will be provided for the afore-mentioned countries, or that this will happen at a specific point in time.</p>\n<p>For inquiries on the state of localization planning, please contact your account team or Mr. Tom Anderson, <a href=\"mailto:<EMAIL>\" target=\"_blank\"><EMAIL></a> from Solution Management for SAP Real Estate Management.</p></div>", "noteVersion": 53}, {"note": "1575764", "noteTitle": "1575764 - Changing cash flow item status", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>This SAP Note provides information about the function to change the cash flow item status, which is available as of Enhancement Package 5.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>REBDRO, RECN, REISCDCF, REISCDCFOBJ, RECAMENUAPPL, RECACUST</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>The function <b>to change a cash flow item status </b>in the master data transactions RECN and REBDRO in change mode during the simulation of the cash flow and in cash flow reporting REISCDCF and REISCDCFOBJ, is made available with Enahncement Package 5 (\"Change Status\").<br/><br/>The function flags planned records of the cash flow as \"Posted\", and prevents a posting. The manually changed status is displayed in the lists by a relevant icon \"Entry Marked Manually\".<br/><br/><b>Important:</b><br/>For technical reasons, the changes are saved immediately and without query, when they are entered in the contract dialog. The authorizations, among other things, should be assigned as an <b>exception</b> and<b> to qualified users only</b>.</p> <ul><li>Prerequisites:</li></ul> <p>           The user has the change authorization for the relevant master data object (authorization object F_RECN for the contract or FI_ME1_BUK for the rental object). In addition, the authorization must exist for the activity CS (change cash flow).<br/>           The cash flow item to be changed must already exist on the database. The cash flow must be updated in advance if required.<br/>           The real estate object must not be displayed as changed (for example, due to calculation entries or distribution entries that are not current), or must not contain check errors. You must correct these errors in advance and save the object, as required.</p> <ul><li>Registration:</li></ul> <p>           The change of the cash flow item is noted in the table VICARGDONE with the registration reason CFSC (Status Change of Cash Flow). These entries can be displayed in the cash flow display by double-clicking the status icon.</p> <ul><li>Change from ACTUAL to PLAN:</li></ul> <p>           Entries set from PLAN to ACTUAL can be reset to PLAN using the method described. However, ACTUAL records actually posted cannot be reset to PLAN using this function.<br/></p></div>", "noteVersion": 1}, {"note": "928175", "noteTitle": "928175 - RE-FX Country Version for Japan", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>Localization of SAP Real Estate Management for Japan.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>Real Estate, Flexible Real Estate, RE-FX, Localization, Restriction, Japan, Service Charges, Fix unit price, Payment charge, Accrual, Deferral, Consumption tax, Business tax.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>You need a Flexible Real Estate solution for Japan.<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><ol>1. Release strategy<br/><br/>SAP Real Estate Management is currently available in two versions: Classic Real Estate (old version) and Flexible Real Estate (new version). Please read more details about the solution strategy in SAP Note 443311.<br/><br/>Please note that Classic Real Estate will not be developed and localized anymore and mainly covers only the requirements of residential real estate. Therefore, <b>Classic Real Estate is not released for new implementation projects since the general availability for the release SAP ERP 6.0.</b></ol> <ol>2. Localization<br/><br/>As of SP9 and EhP2 (Enhancement Package 2) of SAP ERP 6.0 Japanese companies can use the localized Flexible Real Estate Management solution for Japan. Please note that the solution is released to customers under restricted shipment.<br/><br/>Please read more about localization of SAP Real Estate Management in general and the consequences of a missing localization in SAP Note 771098.</ol> <ol>3. Localization scope</ol> <ul><li>Legal requirements and major business practices in Japan</li></ul> <b><br/>Service Charge Settlement based on fix unit prices</b><br/> <ul><li>Service charge key dependent and time-dependent maintenance of fix unit prices for business entities, buildings and participation groups,</li></ul> <ul><li>Calculation of service charge costs based on fixed unit prices,</li></ul> <ul><li>Distribution of fix unit price based service charges according to the standard Service Charge Settlement procedure,</li></ul> <ul><li>Print of the results of service charges based on fixed unit prices on the invoice for the tenants,</li></ul> <ul><ul><li>Print of the basic rent and of the non-consumption based components on the correspondence is out of the scope of this development and has to be added by the customer.</li></ul></ul> <b><br/>Payment report for Real Estate lease-in charges</b><br/> <ul><li>Reporting of real estate payments (rents, security deposits, reikin, premium charges, etc.) in Japan towards the authorities is mandatory when these payments exceed a specified yearly payment amount limit,</li></ul> <ul><li>Co-reporting of further real estate payments which don't count for the yearly payment, but are under them, like agent commissions,</li></ul> <ul><li>Collection of all relevant payments in an electronic file and printing them on a paper form.</li></ul> <ul><ul><li>The selection of payments posted in FI directly is out of scope of this development.</li></ul></ul> <b><br/>Report about leased-out offices in mega cities</b><br/> <ul><li>Companies which are leasing out the rental spaces for the companies eligible for business tax have to report the leased out offices as a measure of error prevention in the business office tax reports of the tenants in every big city declared by the Law as such,</li></ul> <ul><li>Specific forms of this reporting like the forms for the building (Tokyo template 179-1) and for the offices within the building (Tokyo template 179-2),</li></ul> <ul><ul><li>The forms for the business office tax reports (e.g. the Tokyo templates 44, 44-1, 44-4, etc.) are out of scope.</li></ul></ul> <p></p> <ol>1. Others</ol> <b></b><br/> <b>Accruals &amp; Deferrals for taxes</b><br/> <ul><li><u><b>This generic functionality necessary for Japan is available only in SAP_ERP 6.0 EhP2, not also in SP9 of SAP_ERP 6.0.</b></u></li></ul> <ul><li>The functionality has to be tested for Japan.<br/></li></ul> <b>Missing functionalities</b><br/> <ul><li>Invoices and Payments before contract start date and after contract end date (generic functionality necessary for Japan).</li></ul> <p></p> <ol>1. Implementation of not localized version<br/><br/>If customers implement Flexible Real Estate without localization, the customer has to execute the project on his own responsibility and risk. SAP Support for the SAP Real Estate Management will only be able to help customers with generic and localized standard problems under the license agreement executed between customer and SAP. The decision whether a problem has to be categorized as generic or as a localization issue will be made by SAP.<br/><br/>The consequences are as follows: SAP will not support SAP Real Estate Management in the area of legal or business requirements out of the scope for customers in Japan - neither via OSS-messages nor via another channel.<br/><br/>Please note that by default localization enhancements made in higher releases can not be downgraded on earlier versions. For example a localization feature in an enhancement package for SAP ERP 6.0 can not be downgraded to mySAP ERP 2004 or SAP ERP 6.0.<br/><br/>In case of inquiry for new implementations of SAP Real Estate Management in Japan, it is required to request to SAP Japan to execute this project as a pilot customer project with support of SAP Consulting. Support from SAP Consulting will be provided as a chargeable service.</ol> <ol>2. Local contact<br/><br/>If you have questions on this topic or need to apply for a pilot project, please contact the solution owner, Mr. Kentaro Tagawa (<EMAIL>) at SAP Japan.</ol> <p></p></div>", "noteVersion": 10}, {"note": "1682715", "noteTitle": "1682715 - IFRS compliant lease accounting", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>In a joint project, the <span class=\"SNO_DNT\" translate=\"no\">IFRS (International Financial Reporting Standards) Foundation</span> and the <span class=\"SNO_DNT\" translate=\"no\">FASB (Financial Accounting Standards Board)</span> have prepared a new standard for leases.<br/><br/></p>\n<p>\"<span class=\"SNO_DNT\" translate=\"no\">Leasing is an important activity for many entities. It is a means of gaining access to assets, of obtaining finance and of reducing an entity’s exposure to the risks of asset ownership. The prevalence of leasing means that it is important that users of financial statements have a complete and understandable picture of an entity’s leasing activities.</span></p>\n<p><span class=\"SNO_DNT\" translate=\"no\">The previous accounting model for leases required lessees and lessors to classify their leases as either finance leases or operating leases and account for those two types of leases differently. That model was criticised for failing to meet the needs of users of financial statements because it did not always provide a faithful representation of leasing transactions. In particular, it did not require lessees to recognise assets and liabilities arising from operating leases.</span></p>\n<p><span class=\"SNO_DNT\" translate=\"no\">The International Accounting Standards Board (IASB) and the US national standard-setter, the Financial Accounting Standards Board (FASB), initiated a joint project to develop a new approach to lease accounting that requires a lessee to recognise assets and liabilities for the rights and obligations created by leases. This approach will result in a more faithful representation of a lessee’s assets and liabilities and, together with enhanced disclosures, will provide greater transparency of a lessee’s financial leverage and capital employed.\"</span> (Source: <span class=\"SNO_DNT\" translate=\"no\">IFRS 16 Leases</span> - January 2016).</p>\n<p>Consequently, a lessee will have to create a right-of-Use (RoU) asset and a liability in the balance sheet. There are no changes required for the lessor compared to the existing reporting rules.</p>\n<p>More information is available on the related web pages of IFRS and FASB for leases.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>IFRS, IAS, FASB, FAS 13, lease accounting, capital leases, ﻿operational leases﻿, straight-lining, real estate, ﻿IAS16﻿, leased assets, right of use asset, <span class=\"SNO_DNT\" translate=\"no\">RoU</span></p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>On January 13, 2016 the IASB released a new pronouncement called “<span class=\"SNO_DNT\" translate=\"no\">IFRS 16 Leases</span>” with a similiar pronouncement for <span class=\"SNO_DNT\" translate=\"no\">US GAAP</span> being delivered on February 25, 2016.  For companies complying with the International Accounting Standard and Financial Accounting Standard, these important pronouncements will fundamentally change how organizations account for leases.  However, these new lease accounting standards represent more than just accounting changes - they will require significant business process and system changes as well.  Groups that traditionally worked in silos such as lease administration or asset accounting, must now be aligned over the entire leasing lifecycle.  SAP understands the importance of this topic for our customers and is working both internally and externally with its partners to deliver solutions that will help organizations manage their real estate and equipment leases under the new lease accounting standards.</p>\n<p> </p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Since 2002, customers have successfully used SAP Real Estate Management (RE-FX) to account for their real property leases.  SAP has enhanced this solution to leverage the lease data currently being captured within SAP Real Estate Management to calculate and generate financial postings in compliance with these new leasing standards.  This solution supports both the lease administrator and asset accountant with seperate authorizations and posting procedures but utilizes the abstracted lease agreement to ensure consistent and accurate lease valuations.  The first version of this solution was delivered for ECC 6.0, EHP6 on June 27, 2016 as part of support package 17.  Please refer to OSS Note 2255555 for the most current shipment information as well as solution details.</p>\n<p>To manage and account for equipment leases, SAP is partnering with Nakisa to deliver a comprehensive ﻿equipment﻿ leasing solution called <span class=\"SNO_DNT\" translate=\"no\">SAP Lease Administration by Nakisa</span>.  This will be a solution extension and is available both on on-premise and within the Hana Cloud Platform.  Nakisa and SAP are planning to enhance this solution to enable compliance for the new leasing standard with tight integration with SAP’s accounting solutions.  Nakisa and SAP have worked extensively with many customers from asset intensive industries to ensure this solution will help meet the special nuances of leasing equipment﻿.</p></div>", "noteVersion": 13}, {"note": "912290", "noteTitle": "912290 - Switzerland: Scope of localization RE-FX", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>This note concerns the scope of localization of the new real estate solution RE-FX for Switzerland. This is the end of maintenance for local legal requirements in Classic RE for Switzerland.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>RE-FX, localization, rent adjustment according to Swiss law, fuel consumption calculation, individual heating expenses settlement with data medium exchange, leases in with ISR reference, various forms and correspondence, end of maintenance for Classic RE</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>You are considering using new SAP Real Estate Management (Flexible Real Estate) in Switzerland. You want information about the scope of localization for Switzerland. You want to know when maintenance ends for Classic RE for Switzerland.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>The following functions are now available for Switzerland in RE-FX with SAP ECC Enterprise Extension Financials 6.0 (EA-FIN 600):<br/><br/>Rent Adjustment Based On Swiss Law<br/>With the rent adjustment based on Swiss law, you adjust net rent conditions based on the following changes:<br/><br/>Relative factors<br/>o Change to the mortgage rate<br/>A change to the rent based on a mortgage rate change is calculated on the basis of the legal change record of the difference between the mortgage rate level that underlies the current rent and the mortgage rate level that is to be implemented. The mortgage rate corresponds to the mortgage rates of the Kantonalbank in the canton in which the business entity is located.<br/><br/>o General operating and maintenance costs<br/>The system makes the calculation using only the flat rates applied in practice for each year on the grounds of cost increases. These flat rates correspond to local percentage rates that refer to one year in each case. These percentage rates are entered centrally in the system and are assigned to the relevant adjustment rule.<br/><br/>o General price increase (Purchasing power protection of the risk-bearing equity)<br/>The system first determines the country index of the consumer prices at the time of the last rent increase, then the standard index on which the new increase is based. The resulting difference in points is converted to percent and the result of this calculation is multiplied by 40%. The value 40% is defined as an arithmetical value in Customizing and is adjustable in accordance with the legal changes.<br/><br/>Absolute factors<br/>In accordance with BGE 108 II 137 E. 1a, the market rent (prevailing rent) is generally qualified as an absolute rent determination method that cannot be accessed by the ROI check. Consideration of a rent adjustment because break-even gross ROI has been reached is also accepted as an absolute method. However, the absolute method is mostly applied without restrictions during the revenue check in accordance with Art. 269 OR (Net ROI).<br/><br/>o Insufficient net ROI/gross ROI<br/>The revenue expressed in percent (= ROI) is the relationship between the investment and the income that it generates. For real estate, there is a distinction between the gross revenue (relationship between asset costs and rent income) and the net revenue (relationship between owner financing and income after deduction of all expenses). The values calculated based on these formulas outside of the system are maintained as conditions in the rental objects with the relevant condition purpose.<br/><br/>o Prevailing rent not achieved<br/>This value is also defined in the context of a condition for the rental object. The calculation of any possible adjustment based on the prevailing rent takes place analogously to the calculation for the reserve.<br/><br/>Fuel consumption calculation<br/>In Switzerland, the fuel costs are settled according to consumption and not according to purchase with regard to the tenants. A special fuel consumption calculation according to the FIFO valuation procedure has been implemented so that you can settle the fuel costs outside of materials management (MM).<br/><br/>Individual heating expenses settlement<br/>The data medium exchange has been adjusted to the country specifications in the context of the individual heating expenses settlement.  In Switzerland, the owner or their administration carries out the settlement (not the settlement company). Therefore, a separate data carrier has been implemented for Switzerland with measurement documents as its basis.<br/><br/>Tenant rental agreement with ISR reference<br/>Using this function, you can insert the ISR reference of the invoice (submitted by the customer) in the payment document from the periodic posting.<br/>For this purpose, there is an additional function available to assign the reference numbers from ISR to the relevant document from the tenant rental agreement.<br/><br/>Correspondence<br/>In Switzerland, correspondence is specified as mandatory by the tenancy law. For the localization, the legally required documents have been implemented in the standard system. The following correspondence cases are now available for Switzerland:<br/>o Rent change form<br/>o Rent change form for graduated rent<br/>o Rent change form for original rent<br/>o Notice form by landlord<br/>o Heating expenses settlement form/service charge settlement form<br/>o Sales-based rent settlement form<br/>o Invoice printout with ISR<br/>o Different reminder procedures<br/></p> <b>End of maintenance for Classic RE Switzerland<br/>The Swiss solution of Classic RE (all Switzerland-specific adjustments, including the above-mentioned correspondence topics) will be adjusted to meet the legal and local requirements regardless of the ERP releases until the end of 2009 only.      Technical maintenance takes place as described in Note 443311.</b><br/> <p>WW</p></div>", "noteVersion": 6}, {"note": "2255555", "noteTitle": "2255555 - Valuation of leasing contracts (SAP Contract and Lease Management based on SAP RE-FX)", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>New IASB and FASB leasing standard for the handling of leasing contracts (IFRS 16 and US-GAAP ASC 842)</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>SAP contract, IFRS 16, US-GAAP ASC 842, leasing, right of use, leasing liability, interest and repayment, contract valuation, IFRS, US GAAP, RE-FX, real estate, property, FASB 13, sublease, disclosure, SAP Contract and Lease Management</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>The enhancements of the real estate component RE-FX are available as soon as you have imported one of the following system versions (or higher):</p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"3\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td>Release</td>\n<td>SP</td>\n</tr>\n<tr>\n<td>S4CORE 101 (and higher)</td>\n<td>-</td>\n</tr>\n<tr>\n<td>S4CORE, 100</td>\n<td>3</td>\n</tr>\n<tr>\n<td>SAP_FIN 730</td>\n<td>4</td>\n</tr>\n<tr>\n<td>SAP_FIN 720</td>\n<td>5</td>\n</tr>\n<tr>\n<td>SAP_FIN 618</td>\n<td>3</td>\n</tr>\n<tr>\n<td>EA-FIN 700</td>\n<td>9</td>\n</tr>\n<tr>\n<td>EA-FIN 617</td>\n<td>12</td>\n</tr>\n<tr>\n<td>EA-APPL 616</td>\n<td>10</td>\n</tr>\n<tr>\n<td>EA-APPL 606</td>\n<td>17</td>\n</tr>\n</tbody>\n</table></div>\n<p><span><strong>Quick check:</strong></span> For a simple check to see whether you already have the required minimum release in your system landscape, proceed as follows:</p>\n<ul>\n<li>Call transaction SE11.</li>\n<li>Check whether the table VICERULE exists.</li>\n</ul>\n<p><span>If the table exists, you are on the required minimum release (or higher). If the table does not exist, a higher SP is required.</span></p>\n<p>It is not possible to implement the functions in advance (that is, without importing a Support Package).</p>\n<p>In addition to the Support Packages, various SAP Notes with functional enhancements and corrections are available. For this reason, always check for available SAP Notes for the component RE-FX-LA.</p>\n<p><strong>You must check at the start of the project whether all previously delivered SAP Notes for the component RE-FX-LA</strong>.<strong> (For more information, see related notes) have been fully implemented in your system. Implement all relevant SAP Notes straight away.</strong></p>\n<p>In this context, also refer to the transport-based correction instructions (TCI) for the component Real Estate Management RE-FX: SAP Note <a href=\"/notes/3189605\" target=\"_blank\">3189605 - \"Transport-based correction instructions (TCI) for Real Estate Management 2022\"</a>.</p>\n<p><strong>Always keep your project system or production system up-to-date with the relevant SAP Notes. </strong></p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong><span>Solution</span></strong></p>\n<p>The central element for the support of the requirements for the new IFRS 16 and US GAAP ASC 842 standard is the SAP contract within the component Real Estate Management RE-FX.</p>\n<p>This contract was previously able to map a wide range of contract constructions (lease-in, rental, services, purchases and sales, leasing of accessories, installations and so on) including the integration into accounting (automatic posting). Central functions of the contract include:</p>\n<ul>\n<li>Contract type concept (vendor or customer definition)</li>\n<li>Business partner integration</li>\n<li>Runtime management (options and cancellations)</li>\n<li>Resubmissions and reminders</li>\n<li>Direct Integration into FI and CO</li>\n<li>Time-slot-precise conditions and condition adjustment (for example consumer price index)</li>\n<li>Document management</li>\n<li>And many other things</li>\n</ul>\n<p>This SAP contract was enhanced by the following functions in the context of the adjustment for IFRS 16 and US GAAP ASC 842:</p>\n<ul>\n<li>Parallel valuation according to various accounting principles (IFRS, US-GAAP, local law) (see the scope of functions of the accounting principle)</li>\n<li>Expense and revenue valuation (for example, operate lease according to HGB in Germany, UGB in Austria, and so on) </li>\n<li>Linearized valuation (FASB13) </li>\n<li>Balance sheet valuation (for example, according to IFRS 16, US-GAAP ASC 842, finance lease out according to IFRS in sublease context)</li>\n<li>Calculation and display of valuation cash flows throughout the entire contract term</li>\n<li>Direct integration into asset accounting to manage the right of use asset</li>\n<li>Enhancement of FI integration for multi-GAAP postings</li>\n<li>New transactions for valuation and update.</li>\n</ul>\n<p>Details regarding the calculation of the new leasing valuation can be found in SAP Note 2555105.</p>\n<p> </p>\n<p><strong><span>Implementation</span></strong></p>\n<p> The new valuation functions are a pure functional enhancement of the existing RE-FX solution. They are available if the RE-FX subfunction CE01 ‘Contract Valuation’ is activated.</p>\n<p><span>Introduction to existing RE-FX implementations:</span></p>\n<p>The new functions can be added to an existing implementation. For this purpose, a separate valuation Customizing is required (see the Customizing documentation in the system, part of the delivery).  Existing contracts are thus enhanced by new tab pages and functions (see functions).</p>\n<p>Note that, due to the new functions for affected contracts (Customizing), account determination and posting logic changes. If you do not use the new functions, the system behavior does not change.</p>\n<p>Customer developments and customer-specific enhancements must be checked individually.</p>\n<p> For details of Customizing, see SAP Note <span 'times=\"\" ar-sa;\"=\"\" calibri',sans-serif;=\"\" en-us;=\"\" lang=\"EN-US\" mso-ansi-language:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" new=\"\" roman';=\"\">2580277.</span></p>\n<p><span>First introduction of RE-FX:</span></p>\n<p>For the implementation of IFRS/US GAAP specific contract management, it is not necessary to introduce and define the entire real estate component. It is also only possible to implement contract management with its accounting integration. You can subsequently add further RE-FX functions</p>\n<p> </p>\n<p><strong><span>Overview of scope of functions</span></strong></p>\n<p>In the following, the basic functions are described on the basis of the existing SAP contract. Existing functions of the contract are mentioned briefly here if required but are not listed in detail. The valuation function is provided only for credit-side or debit-side contracts.</p>\n<p> </p>\n<p><span>Contract types and accounting principles:</span></p>\n<p>Relevant valuation rules are defined in Customizing. Within these valuation rules, the following functions are available:</p>\n<ul>\n<li>Definition of the type of valuation with linearization, transfer posting or balance sheet valuation (lease in and ﻿lease out﻿)</li>\n<li>Assignment to the relevant FI accounting principle (instead of the ledger approach (newGL, SFIN, S/4 HANA Finance), an accounts approach without assignment to an FI accounting principle can also be mapped)</li>\n<li>Detail definitions with movement types (the basis for account determination)</li>\n</ul>\n<p>For more information about this, see the Customizing documentation.</p>\n<p>These valuation rules are assigned to contract types and company codes. For undefined assignments, valuation is not active.</p>\n<p>Based on the relevant definition of the contract types and account determination, both the vendor (lease in) as well as customer (lease out) contract relationships can be mapped.</p>\n<p>Linearization can also be used to support FASB 13 with the functions listed in this note. Requirements made of FASB 13 going beyond the functions listed in this note are not supported in the standard system. For customers with an existing FASB 13 implementation according to SAP Note 920772, SAP Note 2400460 describes a migration path.</p>\n<p> </p>\n<p><span>Contract objects:</span></p>\n<p>The valuation of contract contents is carried out on the basis of the contract objects assigned to the contract, that is, for each contract object, a separate right of use (RoU) can be entered and posted in the event of balance sheet valuation. If you perform balance sheet valuation according to several accounting principles in parallel, the relevant valuation areas of the RoU asset for the relevant object are posted to. Similarly, the additional ledgers (for example, HGB) are posted to separately.</p>\n<p>Possible contract objects are:</p>\n<ul>\n<li>Building</li>\n<li>Land</li>\n<li>Equipment</li>\n<li>Cost center</li>\n<li>WBS element</li>\n<li>Internal order</li>\n<li>PM order</li>\n<li>Functional location</li>\n<li>Business entity</li>\n<li>Rental object</li>\n<li>Architectural object</li>\n<li>Contracts</li>\n<li>New, freely-definable object “contract object” incl contract object type (see SAP Note 2326200 and 2428630)</li>\n</ul>\n<p>These objects can be assigned to the contract time-dependently, so contract valuation can be broken down into different valuations for each contract object.</p>\n<p> </p>\n<p><span>Valuation parameters:</span></p>\n<p>The valuation parameters in the contract (for each contract object and valuation rule) are a central element for valuation. The contents defined in Customizing for the valuation behavior can be entered in these valuation parameters. These are mainly as follows (depending on the accounting principle):</p>\n<ul>\n<li>Start of consideration</li>\n<li>Start of posting (see transitional rules)</li>\n<li>Fixed asset (only for FI-AA integration); see integration of fixed assets</li>\n<li>Different useful life of the asset (if required)</li>\n<li>Interest rate (manual or calculated)</li>\n<li>Probable contract end date (with default values based on the legal contract terms)</li>\n<li>Different frequency term (valuation frequency)</li>\n<li>Different account assignment element (for expenses/revenue from the valuation)</li>\n<li>Notes</li>\n<li>Classification of the conditions (see “Conditions”)</li>\n</ul>\n<p>Time slices can be created within the valuation parameters in order to enter valuation changes (condition increases, term changes, interest changes, scope changes and so on) and newly validate them for the key date on this basis.</p>\n<p> </p>\n<p><span>Conditions:</span></p>\n<p>In Customizing, you define which condition types are taken into account in which way in valuation.</p>\n<p>In particular the fact that valuation can be carried out on the basis of statistical conditions is new here.. Statistical conditions do not create a partner posting. In connection with valuation, however, they provide the option of specifying the contract in a more differentiated way from the valuation view. The relevant classification can be defined for the conditions (default setting in Customizing, but can be changed manually in the contract for each valuation rule, as specified in Customizing):</p>\n<ul>\n<li>Lease installment payment</li>\n<li>Initial costs</li>\n<li>Incentive</li>\n<li>Present value specification</li>\n<li>Net worth value specification</li>\n<li>Unscheduled depreciation</li>\n<li>Redemption value</li>\n<li>Residual value guarantee</li>\n<li>Asset retirement obligation (only for IFRS)</li>\n<li>Penalty payment</li>\n<li>Transfer posting / linearization</li>\n<li>Sublease</li>\n</ul>\n<p>The relevant consideration in valuation is performed depending on the classification (see the documentation for details).</p>\n<p>Parallel to classification, the value-based consideration of a condition can be defined. The following are possible:</p>\n<ul>\n<li>Full consideration</li>\n<li>Percentage consideration (for example 80%, and then 20% are taken into account as service costs)</li>\n<li>Absolute share (for example, EUR 10,000 are taken into account)</li>\n<li>Not taken into account</li>\n</ul>\n<p> </p>\n<p><span>Integration into asset accounting:</span></p>\n<p>In Customizing, you define whether an asset or a G/L account is to be used to map the RoU in the event of balance sheet valuation.</p>\n<p>In the case of an asset as a RoU, integration of the contract with asset accounting is available. The prerequisite for integration into Asset Accounting is that the new depreciation calculation (as described in SAP Note 965032) is active.</p>\n<p>Using this integration, assets are linked with the valuation parameters and updated. The link is carried out according to the following options:</p>\n<ul>\n<li>Automatic asset creation (specific asset class determination in Customizing)</li>\n<li>Manual selection of the asset class and creation of the asset from the contract on request</li>\n<li>Assignment of an existing asset to the contract (in this case, make sure that the asset has been given all the necessary settings)</li>\n</ul>\n<p>During the creation of the asset, the name (name of the valuation object), the account assignment object for the depreciation and the useful life are transferred and updated as necessary. The activation date is written to the asset in the case of post-capitalization. The special depreciation key comes from asset Customizing.</p>\n<p> </p>\n<p>The system determines the depreciation values using the integration of asset accounting with the SAP contract.. For this, you must create a new depreciation key developed exclusively for RE-FX (see SAP Note 2297363).</p>\n<p>Using this depreciation key, the RoU asset takes the planned depreciations from the cash flows of the SAP contract.</p>\n<p>The posting of the depreciation is then executed in the depreciation posting run of asset accounting for RoU assets, as for tangible assets.</p>\n<p>For detailed information, refer to SAP Note 2568784.</p>\n<p> A mass transaction is available for the final deactivation of the assets. For more information about this, see SAP Note 2528788.</p>\n<p> </p>\n<p><span>Integration into the general ledger:</span></p>\n<p>Make sure that SAP Notes 2657208 and 2661164 are implemented before productive posting. If the SAP Notes can be implemented only after the postings, you can use the correction reports from SAP Note 2661237 to subsequently fill the fields of the database tables.</p>\n<p>SAP Notes 2660070 and <span 'times=\"\" 11.0pt;\"=\"\" ar-sa;=\"\" arial',sans-serif;=\"\" calibri;=\"\" de;=\"\" en-us;=\"\" mso-ansi-language:=\"\" mso-bidi-font-family:=\"\" mso-bidi-font-size:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" new=\"\" roman';=\"\">2727558 </span> provide an overview of the closing operations (foreign currency valuation, liability sorting). This SAP Note is updated periodically.</p>\n<p> </p>\n<p> <span>Valuation:</span></p>\n<p>The valuation for a contract can be called directly from the contract dialog or triggered using a mass run. The valuation generates valuation cash flows on the basis of which the valuation postings are later created.</p>\n<p>The system generates a \"cash flow set\" for each valuation parameter (combination of valuation rule and valuation object). Depending on the valuation type, this cash flow set consists of:</p>\n<ul>\n<li>Changes to assets (for example, asset acquisition/retirement, leasing receivable)</li>\n<li>Depreciation (is made available to asset accounting)</li>\n<li>Clearing (of payments)</li>\n<li>Interest</li>\n<li>Transfer posting / linearization</li>\n<li>Special flows (for example, extraordinary loss from change, initial costs, retroactive changes, and so on)</li>\n</ul>\n<p>The net present value, RoU, depreciation, interest and repayment are calculated using the simulated payment cash flows based on the valuation conditions. In the process, the system uses the simulated due dates.</p>\n<p>With each valuation-relevant contract change, the user can create a new time slot in the valuation parameters. On the basis of this change, a new version of the \"cash flow set\" is created. The new set contains all cash flow records of the old time slices that have already been posted and the changed cash flow records for the new time slice.</p>\n<p>The old cash flow set is retained and can be called.</p>\n<p> </p>\n<p><span>Change processes (lease modification):</span></p>\n<p>A central requirement of the new leasing standards is the detailed processing of change processes. Within the valuation rule, you can enter, calculate and post (according to the specific accounting principle) the following modifications, among others:</p>\n<ul>\n<li>Condition changes (increase, reduction, scale, and so on)</li>\n<li>Index adjustments</li>\n<li>Term changes (shortened, extended)</li>\n<li>Notices, termination</li>\n<li>Scope changes (scope reduction or widened scope)</li>\n<li>Changes to basic principles, for example, interest</li>\n<li>New elements</li>\n<li>Switch between operate and finance lease</li>\n<li>Extraordinary depreciations</li>\n<li>Change of options to buy, residual value guarantees or asset retirement obligations</li>\n<li>Retroactive condition changes in already closed periods (as profit and loss posting)</li>\n<li>Changes to the account assignment elements</li>\n<li>Profit center or trading partner changes</li>\n<li>And many other things</li>\n</ul>\n<p> </p>\n<p><span>Partner posting (payment-relevant posting):</span></p>\n<p>The existing function of transaction RERAPP for the posting of the partner and object cash flow remains unchanged.</p>\n<p>For valuated contracts and conditions, the previous object cash flow is no longer applicable as of the start of valuation (or the start of posting). Its function is replaced by the valuation cash flows.</p>\n<p>For the valuation posting, it is not necessary for the partner cash flow to have been posted.</p>\n<p> </p>\n<p><span>Valuation posting:</span></p>\n<p>For the posting of valuation cash flows, a new posting transaction RECEEP is available.</p>\n<p>You can use this transaction to post the pending cash flows to the individual accounting principles.</p>\n<p>Using this posting, posting is carried out for each assigned accounting principle (see attached posting examples):</p>\n<ul>\n<li>Asset acquisition or retirement</li>\n<li>Leasing liability</li>\n<li>Interest</li>\n<li>Clearing (part of the repayment)</li>\n<li>Linearized service costs</li>\n<li>Transfer postings (for example, according to HGB)</li>\n<li>Retroactive changes</li>\n<li>Repayment from initial costs or incentive</li>\n<li>Owner financing differences</li>\n<li>Asset retirement obligations</li>\n</ul>\n<p>Assets depreciation posting is carried out using the Asset Accounting depreciation run.</p>\n<p> </p>\n<p><span>Sublease:</span></p>\n<p>Customer leasing contracts (lease out) can be managed both as an operate lease and as a finance lease out.</p>\n<p>For the finance lease out, a special classification of the balance sheet valuation rule is available. The calculation and display of the receivables (assets), the interest and revenue postings are carried out via the valuation.</p>\n<p>For the sublease, the following functions are also available:</p>\n<ul>\n<li>Linking the tenant rental agreement (head lease) with the lease-out (sublease)</li>\n<li>Option of adjusting the RoU on the head lease (see SAP Note 2406475).</li>\n</ul>\n<p> The RoU on the head lease can be adjusted in two ways:</p>\n<ul>\n<li>By means of absolute (amount) adjustment: The RoU is then changed as a credit memo posting</li>\n<li>By means of percentage adjustment (new calculation formula \"Sublet\"): The RoU is changed as a retirement</li>\n</ul>\n<p><span>Summaries for the contract: </span></p>\n<p>3 new reports are available in the contract summaries:</p>\n<ul>\n<li>Valuation cash flow – displays the relevant current cash flow set together with other valuation-relevant information (for example opening and closing stocks of the fixed asset and of the leasing liability)</li>\n<li>Valuation process – displays the status of the valuations.</li>\n<li>Valuation postings – displays the current posting summaries</li>\n</ul>\n<p> </p>\n<p><span>Additional transactions:</span></p>\n<p>For the processing of the valuation, some new transactions are also available:</p>\n<ul>\n<li>Edit RECECN Contract Valuation – This transaction allows you to call the contract for the maintenance of the valuation (you can define a different screen sequence compared to transaction RECN). Access to valuation data and \"normal\" contract data is controlled by authorizations.</li>\n<li>RECEISRULECN - Valuation Rules for Contracts – creating and changing valuation rules using mass maintenance (maintainable ALV)</li>\n<li>Perform RECEPR Contract Valuation – mass run for the creation of valuations on the basis of the valuation parameters</li>\n<li>Display RECESH Contract Valuation (cash flow and process) - summary report</li>\n<li>Post RECEEP Valuation for Contracts</li>\n<li>Cancel RECEEPRV Valuation for Contracts </li>\n<li>RECEISASSETCN - Leased Assets for Contracts</li>\n<li>RECEISRECLASSIFY - Reclassification</li>\n<li>RECEISLIABGRAD - Schedule of Liabilities</li>\n<li>RECEASSETDEACT - Deactivate Leased Assets</li>\n<li>RECEASSETDEACTRV - Deactivate Leased Assets: Reversal</li>\n<li>RECEISBALANCE - Stock Overview</li>\n</ul>\n<p> </p>\n<p><strong><span>Foreign currencies</span></strong></p>\n<p>The handling and the scope of functions of foreign currencies in connection with valuation is described in detail in the separate SAP Note 2474141.</p>\n<p> </p>\n<p><strong><span>Accrual / deferral postings</span></strong></p>\n<p>The functions of the accrual engine have been adjusted due to the new features. Here, you now have the option of:</p>\n<ul>\n<li>Taking valuation cash flow items from transfer posting valuation rules into account (instead of the object cash flow items).</li>\n<li>Calculating and posting accruals / deferrals on a ledger-specific basis (from a maximum of one transfer posting valuation rule, for example, HGB)</li>\n<li>Taking the various intermediate scenarios into account and the retransfer of accruals / deferrals before the effective date.</li>\n</ul>\n<p>For more information about this, see SAP Note 2441863</p>\n<p> </p>\n<p><strong><span>Maximum amounts</span></strong></p>\n<p>In the context of leasing valuations, there may be very large amounts for the right of use, the net present value or the contract value. The following maximum amounts are possible:</p>\n<ul>\n<li>The maximum amount for the contract value, the net present value and the RoU is 99,999,999,999.99 units for currencies <span>with 2</span> decimal places. For currencies with more than 2 decimal places, every additional decimal place reduces the maximum amount before the decimal point by a further decimal place. Currencies without decimal places increase the maximum amount to 9.999.999.999.999 units.</li>\n</ul>\n<p>Higher amounts cannot be mapped and posted. Note that SAP Note 2729378 is a necessary prerequisite for mapping these maximum amounts.</p>\n<p>Note that these maximum values also apply for potential additional local currencies. This means that the converted amounts in an additional local currency must not exceed this maximum amount either.</p>\n<p> </p>\n<p><strong><span>Transition period</span></strong></p>\n<p>For the definition of the transition, two central fields are available in the valuation parameters:</p>\n<ul>\n<li>Start of valuation: The system calculates the valuation from here onwards</li>\n<li>First Posting From: The system posts the valuation from here onwards</li>\n</ul>\n<p>As a result, both a retrospective approach (full or modified) as well as a start on the effective date are supported.</p>\n<p>In the case of a retrospective approach, an owner financing posting that may be required will be created and posted at the start of posting.</p>\n<p><span><strong>It is essential that you refer to the details described in consulting note 2544232 for each application scenario.</strong></span></p>\n<p> </p>\n<p><strong><span>Transition phase</span></strong></p>\n<p>During the transition phase, some important aspects must be taken into account:</p>\n<p>The object cash flow ends at the start of valuation (or start of posting of the valuation), and, depending on the completion period of the conditions, corrections (follow-up postings) may be required in the partner cash flow and the object cash flow.</p>\n<p>Therefore, we recommend executing the previous partner and object transfer postings (RERAPP) for the completion periods of the opening periods (for example, 01.01.19) with the following variants:</p>\n<p>(1) Initial valuation posting before periodic partner and object transfer postings</p>\n<p>(2) Only the partner postings first, then the valuation posting followed by the object transfer postings</p>\n<p> </p>\n<p>If you want to post the partner cash flow and the object cash flow before the initial valuation posting, you must post the RERAPP with the object cash flow again after the initial valuation posting (correction CO postings).</p>\n<p> </p>\n<p>In addition, for the transition period, the following functions are also available:</p>\n<ul>\n<li>The system automatically splits conditions with completion periods that are intersected by the effective date (for example, annual conditions and effective date 01.10.19) proportionally (into a previous operate lease share and a new RoU share). Note that this is only for periodic conditions, one-time conditions have to be adjusted manually</li>\n<li>Creation of owner financing postings with retrospective approach</li>\n<li>Corrections of accruals / deferrals created using the accrual engine</li>\n</ul>\n<p> </p>\n<p><strong><span>Go-live project and outlook</span></strong></p>\n<p>We plan to continuously enhance the functions pertaining to contract valuation. For new functions, SAP Notes are created in each case for the component RE-FX-LA. For this reason, always check for available SAP Notes for the component RE-FX-LA.</p>\n<p>The scope of functions of the enhancements is described in the relevant SAP Note.</p>\n<p> </p>\n<p><strong><span>FAQ</span></strong></p>\n<p>In the composite SAP Note 2662137, frequently-asked questions concerning the solution are compiled and continuously enhanced.</p>\n<p> </p>\n<p><strong>Independent audit for the leasing function</strong></p>\n<p>In June 2018, PwC conducted an \"Independent Practitioner’s Reasonable Assurance Report\" (ISAE) for SAP RE-FX (Contract and Lease Management).</p>\n<p>You can use the following access data to download the result of this audit from PwC:</p>\n<ul>\n<li>URL: <a href=\"https://swb.pwc.de/\" target=\"_blank\">https://swb.pwc.de</a></li>\n<li>Client: SAP_RE-FX-LA_ISAE3000_2018</li>\n<li>Key: P9QWj@_VZiN4!?H_5@yB</li>\n</ul>\n<p><span></span></p>\n<p> <strong><span>Training</span></strong></p>\n<p>SAP Education provides training courses on handling and implementation. You can find the courses on the SAP Education pages under S4F72 and S4F73.</p>\n<p> </p>", "noteVersion": 39}, {"note": "1561158", "noteTitle": "1561158 - RE-FX Country Version for France", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>Localization of Flexible Real Estate Management for France</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>Real Estate, Flexible Real Estate, RE-FX, Localization, France, Sales Grading Adjustment, Third-party management</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>Flexible Real Estate Management is used by a company in France.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><ol>1. Release strategy</ol> <p><br/>SAP Real Estate Management is currently available in two versions:<br/>Classic Real Estate (old version) and Flexible Real Estate (new     version). Please read more details about the solution strategy in<br/>SAP Note 443311.<br/><br/>Please note that Classic Real Estate will not be developed and localized anymore and mainly covers only the requirements of residential real estate. Therefore, Classic Real Estate is not released for new implementation projects since the general availability for the release SAP ERP 6.0.<br/></p> <ol>2. Localization</ol> <p><br/>As of SAP ERP 5.0 (ERP 2004) French residential and commercial real estate companies can use the standard Flexible Real Estate Management solution for France without a standard localization functionality.<br/><br/>It is planned that as of EhP6 (Enhancement Package 6) of SAP ERP 6.0 French customers can use the localized Flexible Real Estate Management solution for France with a standard localization functionality.<br/><br/>Please read more about localization of SAP Real Estate Management in general and the consequences of a missing localization in SAP Note 771098.<br/></p> <ol>3. Localization scope</ol> <p><br/>Planned to be available as of Enhancement Package 6 (EhP6) of SAP ERP 6.0.<br/></p> <ol><ol>a) Preparation of Annual Budget for Budgetary Periods</ol></ol> <p><br/>You can prepare the annual budget plan of your business entities for budgetary periods. The number of the budgetary periods depends on the length of the corresponding settlement period. Based on the length of the budgetary and settlement periods, the Annual Budget for Budgetary Periods program calculates the number of budgetary periods among which the program then equally distributes the annual budget amount. You can manually modify the budget amount or you can specify the percentage of the annual budget the program should consider when redistributing the annual budget. You can also modify the amounts or specify the percentage per budgetary period. You can then adjust the assessment that automatically updates the assessment condition amount.<br/><br/>After you modify the annual budget distribution again, you can rerun the assessment adjustment without needing to reverse the previous assessment adjustment that was run first.<br/><br/>When you modify the annual budget after an assessment adjustment, the program automatically saves the updated annual budget with a new version. This enables you to monitor the changes you make during the fiscal year.<br/></p> <ol><ol>b) Extraordinary Operations</ol></ol> <p><br/>When you prepare the annual budget, you plan a certain amount for extraordinary expenses that might occur during the year. In the system, you create settlement units for each extraordinary operation. In accordance with the accounting requirements, you must transfer all the amounts that relate to extraordinary operations to a transfer account. The amount of transfer posting depends on whether the company can deduct value-added tax (VAT) or not.<br/><br/>You can set the status of the extraordinary operations (that is the settlement units) in every settlement period.<br/><br/>At the end of the year, you must run year-end posting. During year-end posting, the system checks the status of the settlement units and compares the plan and actual expenses. Based on the status of the settlement unit, the Year-End Posting transaction posts the difference amount of plan and actual charges to the relevant G/L account. If the status is Not Finished, the transaction transfers the amounts to a transfer account that you have to carry forward at the beginning of the next fiscal year.<br/></p> <ol><ol>c) Adjustment of Sales Grading for Sales-Based Rents</ol></ol> <p><br/>This function enables you to calculate the sales-based rent amount in accordance with the legal requirements. The sales-based rent amount is based on the sales report that the tenant provides you at regular intervals, and on the sales-grading percentages. You can adjust the sales grading with free, index-based, and Business Add-In (BAdI)-based adjustment types for the sales-based commercial contracts. The system defines general sales-grading intervals and assigns a percentage to each sales grading as defined by law.<br/></p> <ol>4. Localization recommendations</ol> <p><br/>Please note that by default localization enhancements which affect databases or user interfaces made in higher releases cannot be downgraded on earlier versions.<br/><br/>If you have questions about the release of SAP Real Estate Management for France including localization topics please refer to the local contact Alexandra Pascal (<EMAIL>) or Quoc-Trung Lieu (<EMAIL>).<br/></p></div>", "noteVersion": 1}, {"note": "1240658", "noteTitle": "1240658 - InpTxCorr: Activating input tax correction", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>The function for the input tax correction in accordance with article 15a of the German Turnover Tax Act is generally released in November 2008 for the releases 600 (as of Support Package 6), Enhancement Package 2 and Enhancement Package 3.<br/>The function covers German legislation only.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>InpTxCorr, input tax correction</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>To activate the function, start the attached report RFRE_SWITCH_ON_TC (600, 602, 603). In Enhancement Package 4 (604), the function is activated using the subapplication IT01 (view V_TIVCAAPPLADDON).<br/>Also see Note 964834 (documents in the attachments of this note) and the FAQ Note 1013725.</p></div>", "noteVersion": 2}, {"note": "1013176", "noteTitle": "1013176 - Austria-specific correspondences", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>This note describes the procedure for using Austria-specific correspondences in RE-FX.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>Austria, correspondence, form</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>You want information about the options for using Austria-specific correspondences.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>SAP SmartForms are used to create correspondence form RE-FX. As of mySAP ERP 2005, you can use PDF-based forms in the Flexible Real Estate Management.<br/>With the SAP correspondence technology, it is possible to implement specific correspondence requirements and to create correspondences tailored to the needs of the customer.<br/><br/>In addition, samples are delivered for the following correspondence activities:<br/><br/> Contract form<br/> Notice and confirmation of notice<br/> Rent invoice<br/> Contract account sheet<br/> Stable value guarantee of rent (rent adjustment)<br/> Operating costs settlement<br/> Main rent settlement (Short and long version as per ÖNORM 4000)<br/><br/>Use the samples delivered as a template for your own correspondence forms and adjust these to suit your individual or country-specific requirements.</p></div>", "noteVersion": 2}, {"note": "766410", "noteTitle": "766410 - SAP Interactive Forms: XDC scenarios for printer control", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>In SAP NetWeaver AS ABAP you create PDF-based print forms using the form solution 'SAP Interactive Forms by Adobe' You want to specifically control the print output of the forms.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Interactive Forms, PDF-based forms, Adobe Document Services, print forms, form printing, duplex</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>SAP provides 'XDC files' for printing PDF-based print forms. You can adjust these files to control the document printing.<br/><br/>You can currently make the following adjustments:</p>\n<ul>\n<li>Configuration of device options on new printers</li>\n<li>The paper tray you use is not the default tray [with form paper].</li>\n<li>Printing documents on paper with a particular size</li>\n<li>Stapling each copy of a multi-page document</li>\n<li>Printing several copies of a document instance and controlling the number of copies at runtime</li>\n<li>Duplex printing<br/>As of SAP NetWeaver 2004 Support Package 18, and as of SAP NetWeaver 7.0 Support Package 05 to Support Package 13, this function is supported with the following restriction: You can only use duplex printing for the entire form.<br/>As of SAP NetWeaver 7.0 Support Package 14, duplex printing is fully supported. For a more flexible layout of the forms, you require Adobe LiveCycle Designer as of Version 8.0. You then no longer require adjustments as described in the XDC scenarios.</li>\n<li>Accessing different paper trays<br/>This function is supported as of SAP NetWeaver 7.0 Support Package 14. It is not supported in SAP NetWeaver 2004.</li>\n</ul>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>To edit the XDC files, you can download the XDC editor from the SAP Store at the Internet address <a href=\"https://www.sap.com/developer/trials-downloads/additional-downloads.html\" target=\"_blank\">https://www.sap.com/developer/trials-downloads/additional-downloads.html</a>. Enter \"XDC editor\" as the search term and download the trial version.<br/><br/>The documentation for the XDC scenarios is attached to this SAP Note.</p>\n<p><br/>An example for changing an XDC file for using different paper trays with step-by-step instructions is also attached to this SAP Note.<br/><br/>Also note the following tips:</p>\n<ol>1. Never change the original XDC files but create a customer-specific copy with a changed file name. Perform the adjustments only in the customer-specific XDC file.</ol><ol><ol>2. In the customer-specific file, change the contents of the attributes \"name\" and \"id\" of the node &lt;xdc&gt;.</ol></ol><ol><ol>Example:</ol></ol><ol><ol>&lt;xdc xmlns=\"http://www.xfa. org/schem....\" id=\"123\" name=\"postscript\"&gt;</ol></ol><ol><ol>To</ol></ol><ol>&lt;xdc xmlns=\"http://www.xfa.org/schem....\" id=\"124\" name=\"postscript1\"&gt;</ol><ol>3. Since the Adobe Document Services cache the XDC files, you must restart the XML Form Module Service after a change.</ol></div>", "noteVersion": 22}, {"note": "1678321", "noteTitle": "1678321 - EhP6: SEPA mandate management for SAP Real Estate Management", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>As part of the unification of the \"Single Euro Payments Area (SEPA)\" payment transactions throughout Europe, the previous debit memo procedure is replaced by the SEPA mandate.<br/>This affects Real Estate contractual relationships, in particular.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>SEPA mandate, mandate management, debit memos, Customer Connection, CustomerConnection, CustConn, focus topic, improvement, FAQ, 606, EhP6, Enhancement Package 6</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>The existing SAP SEPA mandate management (see SAP Note 1046199) should therefore also be integrated into SAP Real Estate Management.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>The SAP module for processing SEPA debit memos was enhanced for Real Estate requirements.<br/><br/>The following enhancements have been made for customer activities, in particular:<br/></p> <ul><li>Managing the SEPA mandate in the posting term for the Real Estate contract</li></ul> <p>           The posting term can now refer to one of the SEPA mandates that exist for the selected bank details.<br/>As a result, you can use a condition to control which mandate is supposed to be used to process the payments.</p> <ul><li>Integrating the SEPA mandate into the payment data for one-time postings</li></ul> <p>           This means that you can control which mandate is supposed to be used to process the payments individually for one-time postings.</p> <ul><li>Enhancing the bank details for the SAP business partner</li></ul> <p>           SEPA mandates are entered either for the customer or for the contract account (FI-CA). It is not possible to enter a SEPA mandate for the business partner in the standard system.<br/>Therefore, an enhancement was made to the maintenance of the business partner (in the same way as for FI-CA) for Real Estate. For business partners in customer roles or the role MKK (contract account), the relevant mandates can now be entered directly in the bank details.<br/>           The advantage of this is that the mandates can be defined as part of the contract entry.</p> <ul><li>Storing the SEPA mandate in the RE document</li></ul> <p>           During the execution of the periodic postings and the one-time postings, the system checks whether a mandate is defined for the condition to be posted. This mandate is then saved in the RE document.</p> <ul><li>Integration to the payment program</li></ul> <p>           For SEPA payment methods and documents that were generated from RE, the payment program checks whether a mandate is assigned in the contract. If this is the case, the mandate is used for entering the receivable. The executed payment is then assigned to the SEPA mandate. Therefore, you see all executed payments in the data for the SEPA mandate.<br/>           <br/>Note that RE-FX does not support the \"Contract Category\" (REF_TYPE) and \"Contract ID\" (REF_ID) fields of the SEPA mandate master record. SEPA mandates, in which these fields are set, cannot be defined in the posting term of the real estate contract and cannot be used for one-time postings (RERAOP). In particular, this applies if you use customer-specific contract categories as described in SAP Note 1835738.<br/><br/>For more information about SEPA mandate management in SAP Real Estate Management, see the attachments to this SAP Note.</p></div>", "noteVersion": 9}]}], "activities": [{"Activity": "Process Design / Blueprint", "Phase": "During conversion project", "Condition": "Mandatory", "Additional_Information": "Customers who use RE Classic need to migrate RE Classic to RE-FX"}]}