{"guid": "901B0E6D3EE91ED7AFE8CDC8493C20D7", "sitemId": "BW602: SEM API", "sitemTitle": "BW4SL - Strategic Enterprise Management (SEM) APIs", "note": 2526508, "noteTitle": "2526508 - BW4SL - Strategic Enterprise Management (SEM) APIs", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>The SEM APIs are not available in SAP BW/4HANA.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>$SEM, BAPI, RSSEM..., CL_RSSEM...</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>You can use the SAP BW ABAP Routine Analyzer to check if SEM APIs are used in custom code embedded in SAP BW objects (see SAP Note <a href=\"/notes/1847431\" target=\"_blank\">1847431</a>). However, there is no standard tool available to scan other custom developments.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Instead of SEM APIs, you should use standard APIs provided by SAP BW/4HANA. See SAP Note <a href=\"/notes/2462639\" target=\"_blank\">2462639</a> - BW4SL - Interfaces and Customer-Specific ABAP Development.</p>", "noteVersion": 1, "refer_note": [{"note": "2462639", "noteTitle": "2462639 - BW4SL - Interfaces and Customer-Specific ABAP Development", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>Converting from SAP BW to SAP BW/4HANA might have an impact on customer-specific ABAP development. Custom Code Migration might be required.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>$CUST, ABAP</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>This SAP Note lists changes to system software that could have an impact on custom code.</p>\n<p>Custom Code Migration describes the tools that help you with the migration of custom code – for example, if you want to migrate your current database to SAP HANA and/or convert your SAP Business Warehouse system to SAP BW/4HANA. In this SAP Note, you will find information about the tools that support you in this process.</p>\n<p>The SAP BW/4HANA Transfer Cockpit provides a Code Scan Tool to check custom code objects as well as code embedded in transformations and many other locations in SAP BW. For more details, see the <a href=\"https://help.sap.com/viewer/p/SAP_BW4HANA\" target=\"_blank\">Conversion Guide for SAP BW/4HANA</a>. A list of DDIC and ABAP objects that are <strong>not </strong>available in SAP BW/4HANA is included in the Code Scan Tool (see class CL_RS_B4HANA_CODE_SCAN method GET_BLACKLIST_WITH_TLOGO).</p>\n<p>Note: SAP Readiness Check for SAP BW/4HANA and the Pre-check Tool of the SAP BW/4HANA Transfer Cockpit do <strong>not </strong>scan or check any custom code.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong>Impact Due to Change of Database</strong> <br/><br/>When converting to SAP BW/4HANA running on SAP HANA from SAP BW running a different database platform, specific ABAP coding needs to be investigated, revisited, and possibly adapted. Functional adjustments and SQL performance tuning might be required.</p>\n<p><strong>Impact Due to Simplification of Application Functionality</strong></p>\n<p>As documented in other simplification items, SAP BW/4HANA provides many simplifications of application functionality compared to SAP BW. Therefore, many programs, function modules, and classes (millions of lines of standard code) and thousands of standard dictionary objects of SAP BW are not available in SAP BW/4HANA. Any use of such objects needs to be replaced in custom code. It's recommended to use only documented standard interfaces (API). See <a href=\"https://help.sap.com/viewer/107a6e8a38b74ede94c833ca3b7b6f51/1.0.2/en-US/a845dbe7e2704cde85da85411fb7a12d.html\" target=\"_blank\">SAP Documentation</a>.</p>\n<p><strong>Impact Due to Change of Application Technology</strong><br/><br/>SAP BW-specific customer enhancements (often called \"customer exits\", transaction CMOD) are not available in SAP BW/4HANA. For several SAP BW releases, SAP has offered corresponding enhancement spots. If customer enhancements are used in SAP BW, the code will have to be adjusted and converted to enhancement spots in SAP BW/4HANA. <br/><br/>The following customer exists are not available with SAP BW/4HANA:<br/><br/></p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"3\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td><em>Customer Exits</em></td>\n<td><em>Description</em></td>\n<td><em>Enhancement Spots</em></td>\n</tr>\n<tr>\n<td>AIBW0001, AIBW0002</td>\n<td>IM-BCT: Assignment of Actual Values to Budget Categories / Corporate IM: Settings for Group Currency</td>\n<td>n/a</td>\n</tr>\n<tr>\n<td>RSR00001, RSR00002</td>\n<td>BI: Enhancements for Global Variables in Reporting / BI: Virtual Characteristics and Key Figures in Reporting</td>\n<td>RSROA_VARIABLES_EXIT and RSROA, see SAP Note <a href=\"/notes/2458521\" target=\"_blank\">2458521</a> for details on how to switch to the enhancement spot</td>\n</tr>\n<tr>\n<td>SEMBPS01, SEMBPS02</td>\n<td>SEM-BPS: Enhancement for checking characteristic value combinations / Enhancement for characteristic derivation</td>\n<td>Corresponding functionality in Integrated Planning or SAP Business Planning and Consolidation (BPC)</td>\n</tr>\n</tbody>\n</table></div>\n<p><strong>Impact Due to Simplification of Object Types</strong><br/><br/>Several types of objects are not available in SAP BW/4HANA. Depending on the type of object, active versions of obsolete object types can be converted to objects that are compatible with SAP BW/4HANA. For example, the InfoCubes or classic DataStore objects can be converted to DataStore objects (advanced). If unavailable object types are used in custom code, the code might have to be adjusted. <br/><br/>For example, if custom code contains a lookup of a classic DataStore object (SELECT ... FROM /BIx/ATESTDSO00 ...), the lookup might fail after converting to an advanced DataStore object which has a different database representation (SELECT ... FROM /BIx/ATESTDSO2 ...). After implementing SAP Note 2539205 (or the equivalent support package), the system will generate compatibility views corresponding to the activation queue and the active data table of the classic DSO reducing the effort to adjust custom code significantly. Nevertheless, note that depending on the type of object, there might also be different technical fields, like \"Request TSN\" instead of \"Request ID\" in change logs and there is no compatibility view for the change log itself.<br/><br/>See the other object-specific simplification items, for recommendations on how to deal with custom code embedded in object types unavailable in SAP BW/4HANA (like Virtual InfoCubes).</p>\n<p><strong>Impact Due to Simplification of Content Objects</strong><br/><br/>Several BI Content and SAP BW Technical Content objects are not available in SAP BW/4HANA. Depending on the type of object, active versions of the content can be taken over or converted to objects that are compatible with SAP BW/4HANA (see \"simplification of object types\" above). If content that is used in custom code is not available in SAP BW/4HANA, the code will have to be adjusted.<br/><br/>For example, InfoObject \"Request ID\" (0REQUID) is not used in SAP BW/4HANA. Therefore, any custom code built around \"Request ID\" will not work with SAP BW/4HANA. Instead \"Request Transaction Sequence Number\" (0REQTSN) should be used.</p>\n<p>Another example is reusing logic provided by BI Content like SAP exit variables in custom code (CALL FUNCTION 'RSVAREXIT...'). Since some SAP exit variables are not available, custom code might have to be adjusted.</p>\n<p>For more details, see SAP Note <a href=\"/notes/2673734\" target=\"_blank\">2673734</a>.</p>\n<p><strong>Impact Due to Change of Personalization Objects</strong></p>\n<p>DataStore objects (0PERS_*) for persistency of personalization data are not available in SAP BW/4HANA. Instead the system uses transparent tables (RSPERS*). Custom code using these DataStore objects needs to be adjusted.</p>\n<p><strong>Impact Due to Simplification of Authorization Objects</strong><br/><br/>Several authorization objects, for example S_RS_ICUBE, are not available in SAP BW/4HANA. In case these objects are used for authorization checks in custom code, the code will have to be adjusted. See SAP Note <a href=\"/notes/2468657\" target=\"_blank\">2468657</a>, for details.</p>\n<p><strong>Impact Due to Changes of Application Interfaces</strong><br/><br/></p>\n<p>Usage of standard application interfaces in custom code or external applications needs to be evaluated and in some cases adjusted. The following table contains the most prominent changes that have been made in SAP BW/4HANA:</p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"3\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td><em>SAP BW Interface</em></td>\n<td><em>Description</em></td>\n<td><em>SAP BW/4HANA Interface</em></td>\n</tr>\n<tr>\n<td>\n<p>RSNDI_MD_ATTRIBUTES_UPDATE<br/>RSDMD_WRITE_ATTRIBUTES_TEXTS<br/>RSNDI_MD_TEXTS_UPDATE<br/>RSNDI_MD_...</p>\n</td>\n<td>APIs for Master Data</td>\n<td>\n<p>RSDMD_API_ATTRIBUTES_UPDATE<br/>RSDMD_API_ATTR_TEXTS_MAINTAIN<br/>RSDMD_API_DELETE<br/>RSDMD_API_TEXTS_UPDATE<br/>RSDMD_API_XXL_ATTR_UPDATE<br/><br/>See <a href=\"https://help.sap.com/viewer/107a6e8a38b74ede94c833ca3b7b6f51/1.0.5/en-US/4c1a1b8a54914c86e10000000a42189e.html\" target=\"_blank\">SAP Documentation</a> and SAP Note <a href=\"/notes/2362955\" target=\"_blank\">2362955</a> for details</p>\n</td>\n</tr>\n<tr>\n<td>RSNDI_SHIE_ACTIVATE<br/>RSNDI_SHIE_CATALOG_GET<br/>RSNDI_SHIE_DELETE<br/>RSNDI_SHIE_MAINTAIN<br/>RSNDI_SHIE_STRUCTURE_GET3<br/>RSNDI_SHIE_STRUCTURE_UPDATE3<br/>RSNDI_SHIE_SUBTREE_DELETE<br/>RSNDI_SHIE_...</td>\n<td>APIs for Hierarchies</td>\n<td>RSDMD_API_HI_ACTIVATE<br/>RSDMD_API_HI_BW_NODENMCREATE<br/>RSDMD_API_HI_DELETE<br/>RSDMD_API_HI_EX_NODENMCREATE<br/>RSDMD_API_HI_HEADER_GET_ALL<br/>RSDMD_API_HI_NODENAME_CREATE<br/>RSDMD_API_HI_STRUCTURE_GET<br/>RSDMD_API_HI_STRUCTURE_UPDATE<br/>RSDMD_API_HI_SUBTREE_DELETE<br/><br/>See <a href=\"https://help.sap.com/viewer/107a6e8a38b74ede94c833ca3b7b6f51/1.0.5/en-US/4c1a1b8d54914c86e10000000a42189e.html\" target=\"_blank\">SAP Documentation</a> and SAP Note <a href=\"/notes/2362955\" target=\"_blank\">2362955</a> for details</td>\n</tr>\n<tr>\n<td>\n<p>BAPI_ODSO_READ_DATA_UC<br/>RSDRI_ODSO_INSERT<br/>RSDRI_ODSO_INSERT_RFC<br/>RSDRI_ODSO_MODIFY<br/>RSDRI_ODSO_MODIFY_RFC<br/>RSDRI_ODSO_UPDATE<br/>RSDRI_ODSO_UPDATE_RFC<br/>RSDRI_ODSO_DELETE_RFC<br/>RSDRI_ODSO_ARRAY_DELETE<br/>RSDRI_ODSO_ARRAY_DELETE_RFC</p>\n</td>\n<td>APIs for DataStore Objects</td>\n<td>\n<p>RSDSO_WRITE_API<br/>RSDSO_WRITE_API_RFC<br/>RSDSO_ACTIVATE_REQ_API_RFC<br/>RSDSO_DU_WRITE_API<br/>RSDSO_DU_WRITE_API_RFC<br/>RSDSO_DU_DELETE_API_RFC<br/>RSDSO_DU_CLEANUP_API_RFC<br/><br/>See <a href=\"https://help.sap.com/viewer/107a6e8a38b74ede94c833ca3b7b6f51/1.0.5/en-US/72e16c936fb94cffb71ce90edd5f8f8e.html\" target=\"_blank\">SAP Documentation</a></p>\n</td>\n</tr>\n<tr>\n<td>BAPI_...</td>\n<td>APIs for other object types like InfoCube, <br/>MultiProvider, etc. which are not available <br/>in SAP BW/4HANA</td>\n<td>Functions still exist but will raise <br/>an error message when called in <br/>SAP BW/4HANA</td>\n</tr>\n<tr>\n<td>BAPI_REP...<br/>RSCRM...<br/>CL_RSCRM...</td>\n<td>CRM Tools and CRM BAPI are not available <br/>in SAP BW/4HANA</td>\n<td>n/a, see SAP Note <a href=\"/notes/2463800\" target=\"_blank\">2463800</a></td>\n</tr>\n<tr>\n<td>\n<p>RSDRI_DF_...<br/>RSDRI_INFOPROV_READ_DF</p>\n</td>\n<td>Data Federator Facade is not available <br/>in SAP BW/4HANA</td>\n<td>n/a, see SAP Note <a href=\"/notes/2444890\" target=\"_blank\">2444890</a></td>\n</tr>\n<tr>\n<td>\n<p>RSSEM_...<br/>CL_RSSEM...</p>\n</td>\n<td>SEM APIs are not available in <br/>SAP BW/4HANA</td>\n<td>n/a, see SAP Note <a href=\"/notes/2526508\" target=\"_blank\">2526508</a></td>\n</tr>\n<tr>\n<td>\n<p>RRW3_GET_QUERY_VIEW_DATA</p>\n</td>\n<td>Web Service API</td>\n<td>\n<p>Functions still exist but is not <br/>supported with SAP BW/4HANA.</p>\n<p>Related to the use case you have 2 options:</p>\n<p>The functionality can be replaced<br/>by the OData interface for BW<br/>Queries. (external access)</p>\n<p>See <a href=\"https://help.sap.com/viewer/107a6e8a38b74ede94c833ca3b7b6f51/1.0.6/en-US/33d6f5a17a3149c3907089059e484f61.html\" target=\"_blank\">SAP Documentation</a></p>\n<p><strong>or</strong></p>\n<p>starting with SAP BW/4 2021 we have the LBA Lightwaight BICS API availible. This can be used for replacment too. (internal access)</p>\n<p>See</p>\n<p><a href=\"https://community.sap.com/t5/technology-blogs-by-sap/how-to-consume-an-analytical-query-bex-query-in-abap/ba-p/13570280\" target=\"_blank\">https://community.sap.com/t5/technology-blogs-by-sap/how-to-consume-an-analytical-query-bex-query-in-abap/ba-p/13570280</a></p>\n</td>\n</tr>\n</tbody>\n</table></div>\n<p><strong>Impact Due to Enhanced Data Types for Characteristics and Constants</strong><br/><br/>When converting to SAP BW/4HANA from a SAP BW release below 7.4, data types and lengths for characteristics will be changed. Previously, the maximum length of characteristic values was 60 characters. The maximum length is now 250 characters, which corresponds to 500 bytes. Domain RSCHAVL has therefore been changed from CHAR60 to SSTRING.<br/><br/>For details, see <a href=\"https://help.sap.com/saphelp_nw74/helpdata/en/b1/26a42229e04101b7b8140b3c7af966/frameset.htm\" target=\"_blank\">SAP Documentation</a> and SAP Note <a href=\"/notes/1823174\" target=\"_blank\">1823174</a>.</p>\n<p><strong>Impact Due to Change of Front-end Technology</strong><br/><br/>SAP Business Explorer is not available with SAP BW/4HANA. Any custom code build on SAP BEx Web Templates or SAP BEx Workbooks (in 3.x as well as 7.x versions) will not work with SAP BW/4HANA.</p>\n<p>Data Federator Façade is not available in SAP BW/4HANA. Instead, generated SAP HANA views can be used. Easy Query is not available in SAP BW/4HANA. Instead, OData queries can be used. Custom code using either Data Federator Façade or Easy Query needs to be adjusted.</p>\n<p><strong>Impact Due to Changes in Other Software Components</strong></p>\n<p>SAP BW/4HANA 1.0 is delivered with the following software components and version:</p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"3\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td>Software Component</td>\n<td>Release</td>\n</tr>\n<tr>\n<td>SAP_BASIS</td>\n<td>750</td>\n</tr>\n<tr>\n<td>SAP_ABA</td>\n<td>75A</td>\n</tr>\n<tr>\n<td>SAP_GWFND</td>\n<td>750</td>\n</tr>\n<tr>\n<td>SAP_UI*</td>\n<td>750 / 752</td>\n</tr>\n<tr>\n<td>DW4CORE</td>\n<td>100</td>\n</tr>\n</tbody>\n</table></div>\n<p>* Starting with SAP BW/4HANA 1.0 SP 8, SAP_UI 7.52 is required.</p>\n<p>SAP BW/4HANA 2.0 is delivered with the following software components and version:</p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"3\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td>Software Component</td>\n<td>Release</td>\n</tr>\n<tr>\n<td>SAP_BASIS</td>\n<td>753</td>\n</tr>\n<tr>\n<td>SAP_ABA</td>\n<td>75D</td>\n</tr>\n<tr>\n<td>SAP_GWFND</td>\n<td>753</td>\n</tr>\n<tr>\n<td>SAP_UI</td>\n<td>753</td>\n</tr>\n<tr>\n<td>DW4CORE</td>\n<td>200</td>\n</tr>\n<tr>\n<td>UIBAS001</td>\n<td>400</td>\n</tr>\n</tbody>\n</table></div>\n<p>When converting to SAP BW/4HANA, these components can undergo a release upgrade, which could therefore impact custom code. SAP_ABA release 75A and higher, for example, supports long material numbers (CHAR 40 instead of CHAR 18). Custom code which uses 0MATERIAL - or variations thereof - should be checked for compatibility with long material numbers. See also SAP Note <a href=\"/notes/2635167\" target=\"_blank\">2635167</a> - Handling of Material InfoObjects with Conversion to SAP BW/4HANA.</p>\n<p>See the following SAP Notes for details:</p>\n<p>SAP Note <a href=\"/notes/2215424\" target=\"_blank\">2215424</a> - Material Number Field Length Extension - General Information<br/>SAP Note <a href=\"/notes/2215852\" target=\"_blank\">2215852</a> - Material Number Field Length Extension: Code Adaptions<br/>SAP Note <a href=\"/notes/2267140\" target=\"_blank\">2267140</a> - S4TWL - Material Number Field Length Extension<br/>SAP Note <a href=\"/notes/2272014\" target=\"_blank\">2272014</a> - Code Inspector check for field length extensions</p>\n<p><strong>Impact on System Modifications and Enhancements</strong></p>\n<p>Modifications and enhancements need to be adapted using the standard transactions SPDD, SPAU and SPAU_ENH. This is the same process as in previous upgrades of SAP BW, only the tools SPDD and SPAU have been renewed. Especially when moving from older system to SAP BW/4HANA many modifications and enhancements can be removed or set to SAP standard. For this purpose, the UI was invented for SPAU, which supports mass activities in order to adjust modifications and enhancements or reset objects to SAP standard more easily. The general recommendation is to reset as many objects as possible to SAP standard.</p>\n<p><strong>SAP Custom Development Projects (CDP)</strong></p>\n<p>If the SAP BW system contains any Custom Development Projects - customer-specific implementations done by SAP -, please contact SAP to ensure that the solution is properly evaluated and transitioned to SAP BW/4HANA.</p>\n<p><strong>Partner Development Projects</strong></p>\n<p>If the SAP BW system contains solutions developed by Partners, please contact the original vendor to see if the solution can be transitioned to SAP BW/4HANA.</p>\n<p><strong>Recommended Process for Custom Code Adjustments</strong></p>\n<p>The custom code adjustment process consists of two major phases. <strong>Before SAP BW/4HANA system conversion</strong> – during discovery and preparation phase – we recommend to get rid of old unused custom code (custom code evaluation) and then analyze remaining custom ABAP code (based on the Simplification List) and find out which objects need to be changed to get adapted to the SAP HANA and SAP BW/4HANA. <strong>After SAP BW/4HANA system conversion</strong> – during the realization phase – you need to adapt custom ABAP code to the new SAP BW/4HANA software (functional adaptation) and optimize performance for SAP HANA database (performance tuning).</p>\n<p><strong>Related Tools</strong></p>\n<p>The following table gives you an overview of how to detect potential functional, application, and performance-related issues during transition and of the relevant tools in the context of conversion to SAP BW/4HANA:</p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"3\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td>\n<p class=\"p\"><em>Use Case</em></p>\n</td>\n<td>\n<p class=\"p\"><em>Description</em></p>\n</td>\n<td>\n<p class=\"p\"><em>Tools</em></p>\n</td>\n</tr>\n<tr>\n<td>\n<p class=\"p\">Custom code evaluation</p>\n</td>\n<td>\n<p class=\"p\">For this purpose, we recommend to turn on the Usage Procedure Log (UPL) in your productive system to find out, which custom ABAP objects are used within your running business processes. You can also use this step for prioritization: to find out which objects are more important as the others.</p>\n<p class=\"p\">An alternative is to use the ABAP Call Monitor (SCMON). The advantage compared to the UPL is that using this tool you not only collect the usage data (how often a specific ABAP object was called), but also the information about the calling context.</p>\n<p class=\"p\">Solution Manager Custom Code Lifecycle Management (CCLM) can retrieve the usage data from your productive system and visualize them for your convenience. Based on the graphical presentation you get better transparency of the usage of your custom ABAP code and can easier analyze it then using only UPL or SCMON technical data.</p>\n</td>\n<td>\n<p class=\"p\"><a href=\"http://www.sap.com/documents/2017/01/a28f29f1-9f7c-0010-82c7-eda71af511fa.html\" target=\"_blank\">Usage and Procedure Logging (UPL)</a></p>\n<p class=\"p\"><a href=\"https://blogs.sap.com/2017/04/06/abap-call-monitor-scmon-analyze-usage-of-your-code/\" target=\"_blank\">ABAP Call Monitor (SCMON)</a></p>\n<p class=\"p\"><a href=\"http://www.sap.com/documents/2017/01/123695c2-a17c-0010-82c7-eda71af511fa.html\" target=\"_blank\">Custom Code Management Decommissioning</a></p>\n</td>\n</tr>\n<tr>\n<td>\n<p class=\"p\">Functional adaptations</p>\n</td>\n<td>\n<p class=\"p\">The ABAP Test Cockpit (ATC) and the Code Inspector provide static code checks that enable you to detect ABAP code which relies on database-specific features.</p>\n<p class=\"p\">Example: ABAP source code that relies on the sort order of the result of an SQL statement.</p>\n</td>\n<td>\n<p class=\"p\"><a class=\"xref\" href=\"https://help.sap.com/viewer/7bfe8cdcfbb040dcb6702dada8c3e2f0/7.5.7/en-US/2b99ce231e7e45e6a365608d63424336.html\" target=\"_blank\" title=\"\">ABAP Test Cockpit and Code Inspector in Context of HANA Migration</a></p>\n</td>\n</tr>\n<tr>\n<td>\n<p class=\"p\">SQL performance optimization</p>\n</td>\n<td>\n<p class=\"p\">The SQL Monitor allows you to monitor all SQL statements and operations that are executed by running ABAP applications. The collected SQL Monitor data then enables you to detect performance hot spots.</p>\n</td>\n<td>\n<p class=\"p\"><a class=\"xref\" href=\"https://help.sap.com/viewer/7bfe8cdcfbb040dcb6702dada8c3e2f0/7.5.7/en-US/f1be2e59f44d448180703d6a497ec1e2.html\" target=\"_blank\" title=\"\">SQL Monitor</a></p>\n</td>\n</tr>\n<tr>\n<td>\n<p class=\"p\">Runtime checks</p>\n</td>\n<td>\n<p class=\"p\">The Runtime Check Monitor allows you to execute a limited range of runtime checks in your productive system. You can use the check results to identify issues that lead to poor performance or high memory consumption.</p>\n</td>\n<td>\n<p class=\"p\"><a class=\"xref\" href=\"https://help.sap.com/viewer/7bfe8cdcfbb040dcb6702dada8c3e2f0/7.5.7/en-US/3e1365542d8041d9ac472f5496114428.html\" target=\"_blank\" title=\"\">Runtime Check Monitor</a></p>\n</td>\n</tr>\n<tr>\n<td>\n<p class=\"p\">Functional adaptation and optimization of <br/>custom code embedded in generated programs</p>\n</td>\n<td>\n<p class=\"p\">The SAP BW/4HANA Transfer Cockpit (since the March 2018 update) can be used to scan the system for custom code embedded in SAP BW configuration like update and transfer rules, transformations, planning functionality, analysis process designer, as well as SAP BW-specific customer exits, business add-ins, and enhancement spots. The Transfer Cockpit scans for usage of incompatible BW objects and obsolete ABAP code.</p>\n<p class=\"p\">In addition, you can use the SAP BW ABAP Routine Analyzer, which will perform a similar scan but focus on best practices and performance optimization.</p>\n</td>\n<td>\n<p class=\"p\"><a href=\"https://help.sap.com/viewer/p/SAP_BW4HANA\" target=\"_blank\">Conversion Guide for SAP BW/4HANA &gt; Custom Code Scan</a></p>\n<p class=\"p\"><a href=\"/notes/1847431\" target=\"_blank\">SAP BW ABAP Routine Analyzer</a> (part of <a href=\"/notes/1909597\" target=\"_blank\">SAP BW Migration Cockpit</a>)</p>\n</td>\n</tr>\n</tbody>\n</table></div>\n<p><span ar-sa;\"=\"\" calibri',sans-serif;=\"\" calibri;=\"\" en-gb;=\"\" en-us;=\"\" lang=\"EN-GB\" minor-latin;=\"\" mso-ansi-language:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" mso-fareast-theme-font:=\"\"> </span></p>", "noteVersion": 21, "refer_note": [{"note": "2719160", "noteTitle": "2719160 - Uninstallation of software component SAP_BW during the BW/4HANA 2.0 Conversion", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>The software component SAP_BW is explicitely uninstalled during the conversion process to product version BW/4HANA 2.0.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p id=\"\">SAP_BW uninstallation, BW/4HANA conversion, SUM</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>The software component SAP_BW is replaced by the software component DW4CORE and must be uninstalled to delete not longer needed objects and content.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>This SAP Note is updated regularly. Make sure you have the current version of this SAP Note before you start the BW/4HANA 2.0 conversion process.</p>\n<p><strong>Contents</strong></p>\n<p>1. Change history<br/>2. Prerequisites for uninstalling SAP_BW 750<br/>3. Manual preparation steps before uninstalling<br/>4. Performing the uninstallation as part of the BW/4HANA conversion process</p>\n<p><strong>1. Change history</strong></p>\n<p>20.08.2021: Extension of the Correction Instructions for the Plugin Class to handle the object R3TR TABU USRVETOTABLES<br/>04.06.2019: Update regarding prerequisites (SPAM/SAINT Version, BW Support Package)<br/>27.11.2018: Initial Version of the note</p>\n<p><strong>2. Prerequisites for uninstalling SAP_BW 750</strong></p>\n<p><strong>The uninstallation of SAP_BW 750 is only possible during the conversion process to the product version BW/4HANA 2.0. It is not possible to uninstall the software component SAP_BW with SAINT or during any other maintenance or upgrade process!</strong></p>\n<p>The following prerequisites must be fulfilled:</p>\n<ul>\n<li><strong>SPAM/SAINT Version 0072</strong> (or newer) is installed in the system.</li>\n<li>The corrections of this note are implemented with the Note Assistant.<br/>As an alternative SAP_BW Support Package <strong>SAPK-75016INSAPBW</strong> is implemented.</li>\n<li>The ACP <strong>SAP_BW====750</strong> is available in the system.</li>\n<li>The conversion to BW/4HANA 2.0 is already planned in the Maintanence Planner and a stack XML with the planning result is available.</li>\n</ul>\n<p><strong>3. Manual preparation steps before uninstalling</strong></p>\n<p>n/a</p>\n<p><strong>4. Performing the uninstallation as part of the BW/4HANA conversion process</strong></p>\n<p>There are no special actions neccessary, because the uninstallation of the component SAP_BW is mentioned in the Stack XML and is done automatically during the conversion process:</p>\n<ul>\n<li>Start the Software Update Manager (SUM) as described in the conversion guide.</li>\n<li>Provide the stack XML when SUM is asking for.</li>\n<li>The SUM tool will stop in phase UNINSTALL_PREPARE and will show that the software component SAP_BW is to be uninstalled.</li>\n<li>Continue the SUM process, there are no further special actions needed concerning the uninstallation of SAP_BW.</li>\n</ul>", "noteVersion": 2}, {"note": "2458521", "noteTitle": "2458521 - Conversion of obsolete customer exits (CMOD) to enhancement spots in SAP BW", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You run an SAP BW system and use the customer exit function for variables in transaction CMOD (function module EXIT_SAPLRRS0_001)</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>CMOD; EXIT_SAPLRRS0_001;</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>With SAP BW 7.3 a new enhancement spot - \"RSROA_VARIABLES_EXIT\" is introduced that has replaced the customer exit function. However, using the enhancement implementation CL_RSROA_VAR_SMOD and the BAdI implementation SMOD_EXIT_CALL, the function module EXIT_SAPLRRS0_001 used in the SMOD is called.</p>\n<p>With SAP BW/4HANA, the implementation using the function module EXIT_SAPLRRS 0_001 is no longer supported.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>In general, we recommend that in the conversion to the enhancement spots, that you classify the customer enhancements, for example, according to different application areas, and you create separate BAdI implementations. As a result, the clarity and maintenance is improved.</p>\n<p>If you are now in a situation, for example, that you are about to upgrade your system to SAP BW/4HANA and the customer exit implementation is mapped in CMOD using the function module EXIT_SAPLRRS0_001, and you want to perform the conversion quickly, the attached document contains a description for performing this simply. However, in this case it is strongly recommended that you subsequently revise this in order to optimize the clarity and maintainability.</p>\n<p>For SAP BW Releases as of 7.3, the enhancement implementation CL_RSROA_VAR_SMOD with the BAdI implementation SMOD_EXIT_CALL must then be temporarily deactivated after the conversion to the enhancement spot. In the case of SAP BW/4HANA, no manual deactivation is required.</p>\n<p>In the case of an inplace conversion, if the conversion takes place during an SAP BW/4 conversion, it might be the case that the customer exit is moved but is still listed as active in the SAP BW/4 check report. This is due to possible entries for EXIT_SAPLRRS0_001 in the table TFDIR and/or MODSAP. In this case, these must be deleted manually. In the case of an SAP BW/4 remote conversion, you can proceed in the same way as in the attached document. However, the enhancement spot is then in SAP BW/4.</p>", "noteVersion": 5}, {"note": "2362955", "noteTitle": "2362955 - Interface for data exchange of master data and hierarchies RSDMD_API", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>The interface for RFC access to master data and hierarchies has been revised in a BW4 system in comparison with a BW 7.x system.</p>\n<p>In a BW4 system, the data is always updated with RSPM request management with a unique request transaction ID, so each change to the data can be seen in transaction RSPM_MONITOR.</p>\n<p>In addition, the data is finalized by the system with a DB COMMIT after use of the function, so that a DB COMMIT always takes place if the data is updated successfully. This is an important difference from the RSNDI APIs, for which the COMMIT was set by the user (possibly after multiple calls of one or more APIs).</p>\n<p>The types and structures are still available in the package RSNDI, while the new functions are contained in the package RSDMD_API. In the case of the hierarchies, the type SSTRING with the length 1333 is used as the type for the node name. The actual length depends on the currently used maximum length for characteristics (currently 250). Parameters that were previously classified as table parameters are now export or import parameters. For a more detailed description, please refer to the documentation for the functions in the system.</p>\n<p>The old function modules and new equivalents are listed below. In the case of the RSNDI functions, the latest version 4 is always listed, RSNDI*4, but all versions of the relevant function can be replaced using the function specified on the right-hand side.</p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"3\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td>\n<p><strong>Name of function in 7.5x</strong></p>\n</td>\n<td><strong>Name of function in BW4</strong></td>\n<td>\n<p><strong>Description</strong></p>\n</td>\n</tr>\n<tr>\n<td>\n<p>RSNDI_SHIE_CATALOG_GET</p>\n</td>\n<td>RSDMD_API_HI_HEADER_GET_ALL</td>\n<td>\n<p>Reading of all hierarchy header entries for an InfoObject</p>\n</td>\n</tr>\n<tr>\n<td>RSNDI_SHIE_STRUCTURE_GET4</td>\n<td>RSDMD_API_HI_STRUCTURE_GET</td>\n<td>Reading of a hierarchy from the BW hierarchy repository</td>\n</tr>\n<tr>\n<td>RSNDI_SHIE_STRUCTURE_UPDATE4 </td>\n<td>RSDMD_API_HI_STRUCTURE_UPDATE</td>\n<td>Update of a hierarchy in the BW hierarchy repository</td>\n</tr>\n<tr>\n<td>RSNDI_SHIE_ACTIVATE </td>\n<td>RSDMD_API_HI_ACTIVATE</td>\n<td>Activation of a hierarchy</td>\n</tr>\n<tr>\n<td>RSNDI_SHIE_DELETE </td>\n<td>RSDMD_API_HI_DELETE</td>\n<td>Deletion of a hierarchy</td>\n</tr>\n<tr>\n<td>RSNDI_SHIE_SUBTREE_DELETE</td>\n<td>RSDMD_API_HI_SUBTREE_DELETE</td>\n<td>Deletion of a hierarchy subtree</td>\n</tr>\n<tr>\n<td>RSNDI_SHIE_MAINTAIN </td>\n<td>Use Web UI for external access</td>\n<td>Hierarchy maintenance</td>\n</tr>\n<tr>\n<td>RSNDI_SHIE_BW_NODENAMESCREATE4 </td>\n<td>RSDMD_API_HI_BW_NODENMCREATE</td>\n<td>Conversion of node name into the internal format</td>\n</tr>\n<tr>\n<td>RSNDI_SHIE_NDI_NODENAMECREATE4 </td>\n<td>RSDMD_API_HI_EX_NODENMCREATE</td>\n<td>Conversion of internal merged node name into the external format</td>\n</tr>\n<tr>\n<td>RSNDI_SHIE_NODENAME_CREATE4 </td>\n<td>RSDMD_API_HI_NODENAME_CREATE</td>\n<td>Creation of node name</td>\n</tr>\n<tr>\n<td>\n<p>RSNDI_MD_ATTRIBUTES_UPDATE and</p>\n<p>RSNDI_MD_ATTRIBUTES_UPDATE_4</p>\n</td>\n<td>RSDMD_API_ATTRIBUTES_UPDATE</td>\n<td>Update of master data attributes</td>\n</tr>\n<tr>\n<td>RSNDI_MD_ATTR_TEXTS_MAINTAIN</td>\n<td>RSDMD_API_ATTR_TEXTS_MAINTAIN</td>\n<td>Master data maintenance for attributes, texts, and XXL attributes</td>\n</tr>\n<tr>\n<td>\n<p>RSNDI_MD_DELETE</p>\n<p>RSNDI_MD_DELETE_4</p>\n</td>\n<td>RSDMD_API_DELETE</td>\n<td>Master data deletion</td>\n</tr>\n<tr>\n<td>\n<p>RSNDI_MD_TEXTS_UPDATE</p>\n<p>RSNDI_MD_TEXTS_UPDATE_4</p>\n</td>\n<td>RSDMD_API_TEXTS_UPDATE</td>\n<td>Update of master data texts</td>\n</tr>\n<tr>\n<td>\n<p>RSNDI_MD_XXL_ATTR_UPDATE_4</p>\n</td>\n<td>RSDMD_API_XXL_ATTR_UPDATE</td>\n<td>Update of master data XXL attributes<br/><br/></td>\n</tr>\n</tbody>\n</table></div>\n<p><strong>Handling with regard to migration of a system to BS4HANA</strong></p>\n<p>Since the new function modules are not available before the migration of the system itself or of the target system to BW4HANA, the calls of the function modules must be adjusted after the migration in accordance with the table above. If a function module is used very frequently in a system, it might make sense to create your own function/method with the interface of the old function module before the migration. This calls the actual function module, and should be used instead of the actual function module in all places so that you only have to adjust a single place for each function module centrally after the migration. This allows you to minimize the efforts required after the migration when the calls of the old function modules do not work and the calls have to be adjusted.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>RSNDI, RSNDI_SHIE, RSDMD_API_HI, RSDMD_API</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>Change of interface for master data and hierarchies in a BW4 system in comparison with a BW 7.x system.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<ul>\n<li><strong><span>BW/4HANA 1.0<br/></span></strong>Import Support Package 01 for BW/4HANA 1.0 (SAPK-10001INDW4CORE) into your BW4 system. The Support Package is available once <strong>SAP Note 2351337</strong> has been released for customers. </li>\n</ul>", "noteVersion": 3}, {"note": "2444890", "noteTitle": "2444890 - BW4SL - Data Federator Facade", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>The SAP BW Data Federator Facade is not available in SAP BW/4HANA.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>$DF, Data Federator, RSDRI_DF_..., RSDRI_INFOPROV_READ_DF, RSDRI_DF_GET_SYSTEM_INFO</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>There is currently no automated analysis available, if the Data Federator Facade is used or not.</p>\n<p>You may check your systems for usage of function module RSDRI_INFOPROV_READ_DF (RFC calls) and RSDRI_DF_GET_SYSTEM_INFO.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Instead of the Data Federator Facade, you can use the external SAP HANA views of the SAP BW/4HANA InfoProviders. This allows direct SQL access to SAP BW/4HANA data. The external SAP HANA views need to be generated for all objects where a SQL access is necessary.</p>\n<p><strong>Related Information</strong></p>\n<p><a href=\"https://uacp2.hana.ondemand.com/viewer/6f3d8c7c6c4b1014a4efa996db501300/7.01.18/en-US/48d7b2571371581de10000000a42189c.html\" target=\"_blank\">Data Federator</a></p>\n<p><a href=\"https://uacp2.hana.ondemand.com/viewer/107a6e8a38b74ede94c833ca3b7b6f51/1.0.3/en-US/6633d851345c4770bd4e523701b9f5b0.html\" target=\"_blank\">Generating SAP HANA Views from the BW System</a></p>", "noteVersion": 3}, {"note": "2468657", "noteTitle": "2468657 - BW4SL & BWbridgeSL - Standard Authorizations", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>Several standard authorizations objects used in SAP BW are not available in SAP BW/4HANA.</p>\n<p>Non of the standard authorization objects used in SAP BW or SAP BW/4HANA are available in SAP Datasphere, SAP BW bridge.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>$STDAUTH, Authorizations, Profiles, Roles, PFCG, SAP Datasphere, SAP Datasphere</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p><span><strong>Target SAP BW/HANA:</strong></span></p>\n<p>The simplification of object types in SAP BW/4HANA has an impact on authorization objects. When converting a SAP BW system to SAP BW/4HANA, authorizations for object types that are not available in SAP BW/4HANA (like InfoCubes) have to be replaced by authorizations for corresponding object types (like DataStore Objects (advanced)).</p>\n<p>The SAP BW/4HANA Transfer Cockpit includes a tool to automatically transfer roles and standard authorizations.</p>\n<p><span><strong>Target SAP Datasphere, SAP BW bridge:</strong></span></p>\n<p>In SAP Datasphere, SAP BW bridge the security of the business users is controlled by a role-based authorization management maintained via the Identity and Access Management Apps within the SAP BW bridge Cockpit. There is no automated transfer or conversion of the SAP BW or SAP BW/4HANA authorization into SAP Datasphere, SAP BW bridge.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><span><strong>Target SAP BW/HANA:</strong></span></p>\n<p>The attached file contains a list of authorization objects available in SAP BW and shows if they are available in SAP BW/4HANA or need to be replaced with a different authorization object.</p>\n<p>Adjust your security profiles and roles accordingly. To do so, you can use the Authorization Transfer Tools included in the SAP BW/4HANA Transfer Cockpit (programs RS_B4HTAU_*). You first perform an initial run to analyze the existing authorization usage. Then you convert the data model and flow objects. And finally, you perform a delta run to adjust the standard authorzations according to the attached rules. For more details, see <a href=\"https://help.sap.com/viewer/p/SAP_BW4HANA\" target=\"_blank\">Conversion Guide</a>.</p>\n<p>The column \"Treatment\" in the attached file has 4 values:</p>\n<ul>\n<li>Assume: <br/>Nothing do to. This authorization objects will work 1:1 after the SAP BW/4HANA conversion.</li>\n<li>Adjust: <br/>Check and adapt the values. If there are Cube/DSO/... names in use, please change it to the new ADSO name.</li>\n<li>Replace: <br/>A <strong>manual task</strong> is necessary and a new authorization object (\"Replacement\" column) instead of the old one (\"Technical Name\" column) must be included in the roles.</li>\n<li>Obsolete: <br/>The authorization object is not needed anymore. Please note, the authorisation objects that are marked as obsolete are still present in the system (In the transaction SU21, these are also displayed under the folder RS.). The reason for this is that the old objects are imported during remote transfer and converted during activation. There are also objects such as S_RS_BITM and S_RS_BTMP that can be used in SAP BW/4 due to a pilot programme for the use of the WAD. Deleting these objects is not possible for regression reasons. </li>\n</ul>\n<p>For Remote and Shell Conversion, you either maintain new roles in the SAP BW/4HANA target system (see SAP Note <a href=\"/notes/2468657\" target=\"_blank\">2468657</a>) or you transport the original roles to the target system and use the Authorization Transfer Tool (Report \"RS_B4HTAU_CREATE_RUN\") to make the required adjustments in the target system.</p>\n<p><span><strong>Target SAP Datasphere, SAP BW bridge:</strong></span></p>\n<p>In the <strong>Manage Business Roles</strong> app within the SAP BW bridge Cockpit it is possible to create business roles by combining predefined business catalogs. The business catalogs contain the actual authorization that grant users access to the apps for a particular business area. It is recommended that you use the templates delivered for SAP BW bridge. Based on the purpose of a business catalog it can be restricted on one or multiple restriction types:</p>\n<ul>\n<ul>\n<li>RSINFOAREA - Read/Write access to metadata of SAP BW bridge objects by InfoArea.</li>\n<li>RSLOGSYS - Restrict Access to source objects.</li>\n<li>RSAREASRC_RSAREATGT - Restrict access to transformation and to data traget processes for your staging tasks. Allows the control what data can be loaded by users into which objects.</li>\n</ul>\n</ul>\n<p><span>Once the business role is defined using a template it can be assigned to multiple business users who perform similar tasks.</span></p>\n<p><span>Related Information:</span></p>\n<ul>\n<li><a href=\"https://help.sap.com/docs/SAP_BW_BRIDGE/107a6e8a38b74ede94c833ca3b7b6f51/3205c74f5b1a4ababce3ed65165ac597.html\" target=\"_blank\">Identity and Access Management</a> </li>\n</ul>", "noteVersion": 15}, {"note": "1823174", "noteTitle": "1823174 - BW 7.4 changes and customer-specific programs", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>With BW Release 7.4 SPS2, a number of changes have been made that could have an effect on customer-specific programs. For this reason, this SAP Note is relevant for all systems for which an upgrade has been made from a release below 7.4 to a release equal to or above 7.4.</p>\n<ol>\n<li>In the BW Releases before 7.4, the maximum length of a characteristic value is limited to 60 characters. As of Release 7.4 SPS2, up to 250 characters are possible. To achieve this, the domain RSCHAVL was changed from CHAR 60 to SSTRING 1333. As a result, data elements that use the domain RSCHAVL are \"deep\" types in an ABAP context. Therefore, some ABAP language constructs are no longer possible (syntax errors) or they cause runtime errors in customer-specific programs. This SAP Note describes solution options for certain problem classes.</li>\n<li>Texts with a length of up to 1,333 characters are possible for characteristic values. For this, the structure RSTXTSMXL was created which is a \"deep\" type in an ABAP context. In the internal method interfaces and function module interfaces that handle the texts of characteristic values, the type RST<PERSON>SM<PERSON> was replaced with RSTXTSMXL. However, the RSRTXTSML structure remains unchanged and is required for the description of metadata.</li>\n<li>Internal metadata handling was optimized and redundant buffers were eliminated. In addition, some functions that have been obsolete for several releases have now been removed.</li>\n</ol>\n<p>The change should have little effect on your programs. You must expect problems where you operate on characteristic values that are assigned with a generic type (for example, variable exits) or where you call SAP-internal functions or methods whose interfaces were changed by SAP. BAPIs are not affected by this. Most programs can be changed schematically without the function having to be understood.<br/><br/>Most of the problems are syntax errors that result in a program termination. You can use the Code Inspector Tool (transaction SCI) to systematically detect and resolve these problems. You should, if possible, run a check both before and after the upgrade. With the attached program, you can then identify all problems that did not occur until after the upgrade. For a detailed description, refer to the attached document, \"CodeInspector_Pre_Post.docx\". If you are unable to run a check before the upgrade and are also unable to do so in a system with the old status (and subsequently import it into the system), you should run the check with a restricted check variant. You will find more details about this in the document \"CodeInspector_Post.docx\". For general information about using the Code Inspector Tool, see the SAP documentation (<a href=\"http://help.sap.com/saphelp_sm71_sp01/helpdata/en/49/205531d0fc14cfe10000000a42189b/frameset.htm\" target=\"_blank\">http://help.sap.com/saphelp_sm71_sp01/helpdata/en/49/205531d0fc14cfe10000000a42189b/frameset.htm</a>).</p>\n<p>To use the attached program, after the upgrade, create a local program named ZSAP_SCI_DELTA in your system and copy the contents of the text file \"CodeInspection_Delta.txt\" from the attachment into this program. Once you have run the Code Inspector before and after the upgrade, you can start the program. The *_PRE fields must be field with the identifiers of the run before the upgrade, while the *_PST fields must be filled with the identifiers of the run after the upgrade (INSP_* is the inspection name, VERS_* the version and USER_* the user). Enter the name of the new version in VERS_NEW. When you run the program, it finds all messages that occur in  *_PST but not in  *_PRE. The result is stored as a new version of the inspection run with the key of the *_PST run. You can then this view as normal with the Code Inspector Tool and proess the problems using this list/hierarchy (see also \"CodeInspector_Pre_Post.docx\").</p>\n<p>When you do this, note the following:</p>\n<ul>\n<li>There are checks that only find the first error in a program unit (class, function group, ABAP report). When you have eliminated the error and you try to activate the object, you are then alerted to further problems. However, syntax errors may also be displayed during activation, which the Code Inspector log displays under different headings. If this case, you will discover that an error has already been eliminated when you are processing the errors under this heading.</li>\n<li>Sometimes, the same problems recur regularly in a program section. In this case, you can avail of the options provided in the editor, such as \"Find and Replace\". In this way, you can quickly eliminate a very large number of errors of the same type very efficiently.</li>\n<li>Once you have processed the list of errors, you can start the Code Inspector again and once again determine the difference between the current status and the status before the upgrade. In this way, you can ensure that you have not overlooked any errors.</li>\n<li>The include ZXRSRU01 and the function module EXIT_SAPLRRS0_001 (CMOD enhancement RSR00001 for exit variables) are not analyzed by the Code Inspector. This include must be opened in the editor (transaction SE38) and its syntax checked there. You will find an efficient option for converting this source code under \"Solution\" below.</li>\n<li>The Code Inspector does not analyze generated programs. This may affect the following: Transfer rules, update rules, transformations. If a problem occurs in a generated program, you must adjust the relevant template program template. You will find details of the relevant object in the comment lines at the start of the generated ABAP program. Open these in the Datawarehouse Workbench and edit the ABAP routines. You can check these objects systematically by activating them. To do this, execute the program RSDG_TRFN_ACTIVATE (transformations; also see SAP Note 2108084), RS_COMSTRU_ACTIVATE_ALL (update rules via InfoSource) and RS_TRANSTRU_ACTIVATE_ALL (transfer rules via InfoSource).</li>\n<li>The generated hierarchy tables (/BI0/H... or /BIC/H...) are not adjusted during the upgrade or update. If you activate characteristic with a hierarchy in 7.40 again, however, the data element changes from field NODENAME to RSSHNODENAMESTR. As a result, the structure contains a data element with type SSTRING and one of the described problems can occur as a result.</li>\n<li>You cannot inspect any modifications to SAP programs with the Code Inspector Tool.  </li>\n</ul>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>RSCHAVL, hierarchy nodes</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>Enhancement of the SAP function</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>A list of potential errors and their solutions is provided below. In the description of the error, the message code is specified in brackets, and can be used to find the objects with an error of this type when analyzing the Code Inspector results. Where the solution mentions a CHAR type of a sufficient length, this refers primarily to a customer-specific, application-specific data type. However, the data elements RSCHAVL60 (a CHAR-60 type, which is identical to the RSCHAVL data elements in previous releases) and RSCHAVL_MAXLE are also available  RSCHAVL_MAXLEN is a CHAR type, which is exactly long enough to store every characteristic value. The length may be adjusted by SAP in subsequent releases.</p>\n<p><strong>ABAP Dictionary error</strong></p>\n<ul>\n<li>The total length of the key fields or of all fields of a database table is too long.</li>\n<ul>\n<li><em>Proposed solution</em>: The fields can be filled only with values of a length of up to 60 characters because the data element RSCHAVL was used. Therefore, you can replace the data elements of the type SSTRING with data elements of the type CHAR with a length &lt;= 60 without losing the existing function.</li>\n</ul>\n</ul>\n<p><strong>Syntax error</strong></p>\n<p>Below, <em>STRUC</em> is a structure that is defined either in the DDIC or in an ABAP type pool and contains at least one field that is of the STRING type from an ABAP point of view. This also applies, for example, to DDIC fields that use a data element of the SSTRING 1333 type. In addition, <em>VAR</em> is a variable that is of the STRING type from an ABAP point of view.</p>\n<ul>\n<li><strong>APPEND <em>STRUC</em> TO <em>ITAB</em>, </strong><strong>READ <em>ITAB</em> INTO <em>STRUC</em></strong> or <strong>LOOP AT <em>ITAB</em> INTO <em>STRUC </em></strong>results in a syntax error if <em>STRUC</em> does not correspond to the structure of <em>ITAB</em> (MESSAGEG@6, MESSAGEGXV)</li>\n<ul>\n<li><em>Proposed solution:</em> Define <em>STRUC</em> so that it has the same type as the structure of the internal table <em>ITAB</em>, or define an additional structure that is identical to the structure of <em>ITAB</em> and use it in the APPEND, READ, or LOOP INTO. Before the APPEND, fill the new structure from the old structure or, after the READ, fill the old structure from the new structure.</li>\n</ul>\n<li><strong><em>ITAB1</em> = <em>ITAB2</em> </strong>or<strong> MOVE <em>ITAB2</em> <em>TO </em>ITAB1</strong> results in a syntax error if the structure of <em>ITAB1</em> has fewer fields than the structure <em>ITAB2</em> and if they contain fields of the STRING type (MESSAGEGXD)</li>\n<ul>\n<li><em>Proposed solution: </em>Define the tables in such a way that they have the same structure, or fill the table<em> ITAB1</em> line by line ( LOOP <em>ITAB2</em>, MOVE or MOVE-CORRESPONDING from the structure of <em>ITAB2</em> to the structure of<em> ITAB1</em> and APPEND or INSERT for<em> ITAB1</em></li>\n</ul>\n<li><strong>WRITE INTO <em>VAR</em></strong> results in a syntax error (MESSAGEG@1)</li>\n<ul>\n<li><em>Proposed solution:</em> Define a variable of the type CHAR with sufficient length and replace the string variable in the WRITE command with the CHAR variable. After this, fill the CHAR variable back to the STRING variable.</li>\n</ul>\n<li><strong>TRANSFER <em>STRUC</em> TO </strong>results in a syntax error (MESSAGEG@8)</li>\n<ul>\n<li><em>Proposed solution:</em> Same as for WRITE INTO</li>\n</ul>\n<li><strong>SET / GET PARAMETER ... FIELD <em>VAR </em></strong>results in a syntax error (MESSAGE@E)</li>\n<ul>\n<li><em>Proposed solution: </em>Use an auxiliary variable of the type CHAR with a sufficient length as described in the proposed solution for the WRITE problem.</li>\n</ul>\n<li><strong>SELECT-OPTIONS ... FOR <em>VAR </em></strong>results in a syntax error (MESSAGEG@2)</li>\n<ul>\n<li><em>Proposed solution: </em>Make VAR into a CHAR type of a sufficient length or define a variable of the CHAR type of a sufficient length, use the new variable in the SELECT-OPTION, and (if necessary) copy the SELECT-OPTION variable to a RANGE-type structure with LOW and HIGH as the STRING type.</li>\n</ul>\n<li><strong><em>VAR</em> = <em>STRUC</em></strong> or <strong><em>VAR</em> = <em>STRUC</em>+offset(length)</strong> results in a syntax error (MESSAGEG\\J)</li>\n<ul>\n<li><em>Proposed solution: </em>Access the individual components of the structure and, if necessary, concatenate the values as described in the proposed solution for the MESSAGEG\\6 error type.</li>\n</ul>\n<li>Assume that <em>STRUC</em> is a structure defined in the ABAP Dictionary that contains a field of the type SSTRING. In this case, the following ABAP statements cause a syntax error (MESSAGEG?J):</li>\n<ul>\n<li><strong>DATA: VARIABLE LIKE <em>STRUC</em></strong> or <strong>DATA: VARIABLE LIKE <em>STRUC</em>-FELD</strong>, even if FELD is not of the type SSTRING.</li>\n<ul>\n<li><em>Proposed solution: </em> Replace LIKE with TYPE.</li>\n</ul>\n<li>In a type definition, <strong>INCLUDE STRUCTURE <em>STRUC</em></strong> causes a syntax error.</li>\n<ul>\n<li><em>Proposed solution:</em> INCLUDE TYPE <em>STRUC</em></li>\n</ul>\n<li><strong>TABLES<em> STRUC</em></strong> causes a syntax error.</li>\n<ul>\n<li><em>Proposed solution:</em> This statement creates a table with a header line. Check if the statement is still required. If it is still required, define a variable that uses a table type and a second variable that uses a structure type. In the program, you must then replace <em>STRUC</em> with the table variable or structure variable, depending on the usage (table or header line). You may also need to adjust a SELECT statement and use the table variable in the INTO clause.<br/>Example: DATA: G_T_TAB TYPE STANDARD TABLE OF STRUC,<br/>                       G_S_STRU TYPE STRUC.</li>\n</ul>\n<li><strong>TABLES</strong> <em><strong>STRUC</strong> </em>in the screen causes a syntax error.</li>\n<ul>\n<li><em>Proposed solution:</em> Copy <em>STRUC</em> and use data elements of the type CHAR with a length that is sufficient for your application instead of using the data elements that refer to a SSTRING.</li>\n</ul>\n</ul>\n<li><strong>ASSIGN <em>VAR</em>+OFFSET(LENGTH) TO &lt;FIELDSYMBOL&gt;</strong> or <strong><em>VAR</em>+OFFSET(LENGTH) = ...</strong> causes a syntax error (MESSAGEG\\6, MESSAGEG\\9)</li>\n<ul>\n<li><em>Proposed solution:</em>  Use an auxiliary variable of type CHAR with a sufficient length and, if necessary, fill the auxiliary variable before with the value of VAR. Then, fill the value of the auxiliary variables after <em>VAR</em> again. String templates represent an 'elegant' alternative.<br/>Example for a string template: The statement \"<em>VAR(OFFSET) = VAR1. VAR+OFFSET(*) = VAR2.\"</em> can be replaced by \"<em>VAR = |{ VAR1 WIDTH = OFFSET }| &amp;&amp; |{ VAR2 }|.\" </em></li>\n</ul>\n<li>The structure of a TABLES parameter of a function module is deep. This means that the structure contains a field of the type SSTRING (MESSAGEG8G).</li>\n<ul>\n<li><em>Proposed solution:</em> There are two alternative solution strategies for this:<br/>If the function module is not RFC-enabled, you can also convert the parameter into an importing, exporting, or changing parameter. To do this, you must define the parameter as a table type with the structure that was previously used. Since a TABLES parameter declares an internal table with header line, the header line is no longer available. Instead, you must create an additional variable that replaces the header line.<br/>Alternatively, you can also change the type. Replace the data elements of the type STRING with character types of sufficient length.<br/>Check which alternative is the more suitable solution for your application. Note that you must also adjust the call positions. If the function module is RFC-enabled, calls may also originate in other systems.</li>\n</ul>\n<li>A method, a function module, or a subroutine contains a parameter of the generic type C in the signature (MESSAGEGUQ).</li>\n<ul>\n<li><em>Proposed solution:</em> Replace the generic type C with CLIKE or CSEQUENCE (see ABAP documentation).</li>\n</ul>\n<li>SAP had to change the interface for some function modules that are not released (MESSAGEG)7).</li>\n<ul>\n<li>Since TABLES parameters of structures that contain a STRING are impossible, these were replaced with an IMPORTING, EXPORTING, or CHANGING parameter.</li>\n<li>The structure of a parameter (mainly TABLES) was changed. In most cases, the character type RSCHAVL_MAXLEN was used instead of the string type RSCHAVL in this case.</li>\n<ul>\n<li><em>Proposed solution:</em> Adjust the call accordingly.</li>\n</ul>\n</ul>\n<li>The constructor of the class CL_RSD_DTA is no longer public (MESSAGEG/Y)</li>\n<ul>\n<li><em>Proposed solution:</em> Use the static method CL_RSD_DTA=&gt;FACTORY to create an instance of CL_RSD_DTA.</li>\n</ul>\n<li>The class CL_RSDRV_DTA_BUFFER was deleted.</li>\n<ul>\n<li><em>Proposed solution:</em> Use the function module RSD_COB_PRO_ALL_GET instead of the method CL_RSDRV_DTA_BUFFER=&gt;GET_DTA_INFO.</li>\n</ul>\n<li>Some function groups were deleted:</li>\n<ul>\n<li>The \"old trace tool\" (transaction RSRCATTTRACE) has not been supported for the last few releases. The function groups RRX2 and RRX3, the type pool RRX2, and the include RSRTRACEMAKRO were deleted. (INCLUDE001, MESSAGEGTH, MESSAGEG$S)</li>\n<ul>\n<li><em>Proposed solution:</em> Function modules from these function groups (most of these modules have the prefix \"RRX_\" in their name) must be removed. They do not need to be replaced because they no longer had any function before the upgrade.</li>\n</ul>\n<li>The \"old reporting authorization concept\" is no longer supported as of BW 7.0. The function group RSSBR was deleted.</li>\n<ul>\n<li><em>Proposed solution:</em> You must replace the calls of these modules with an identical module from the function group RSEC_CHECKS. In general, you will find the relevant module by replacing the prefix \"RSSB_\" in the old name with \"RSEC_\".</li>\n</ul>\n</ul>\n</ul>\n<p><strong>Runtime error:</strong></p>\n<ul>\n<li>STRING_OFFSET_TOO_LARGE for a command <strong>... = VAR+OFFSET(LENGTH)</strong>, where VAR is a variable of the type STRING.</li>\n<ul>\n<li><em>Proposed solution:</em> Generally, offset operations and length operations are allowed when reading strings. However, you must ensure that the access is not performed beyond the end of the string. OFFSET + LENGTH &lt;= STRLEN( VAR ) must apply. Therefore, the reading access must be ensured with a suitable IF statement (with regard to the string length), or the string value must be copied to a variable of the type C with a sufficient length beforehand. The ABAP command SUBSTRING and suitable exception handling (see ABAP documentation) can also be used to solve the problem.</li>\n</ul>\n</ul>\n<p><strong>Efficient conversion of the variable exit program</strong></p>\n<p>The EXIT_SAPLRRS0_001 function module has the new optional importing parameter I_T_VAR_RANGE_C and the new exporting parameter E_T_RANGE_C. The parameter I_T_VAR_RANGE_C contains the same information as the parameter I_T_VAR_RANGE. The only difference is that the LOW and HIGH have the type CHAR 250 rather than the type SSTRING. The same applies to the parameter E_T_RANGE_C. By default, the function module is called using a SMOD_EXIT_CALL BAdI implementation of the RSROA_VARIABLES_EXIT_BADI BAdI delivered by SAP. The parameter I_T_VAR_RANGE is then filled and the exporting parameter E_T_RANGE has to be filled.</p>\n<p>If the function module is called using the CL_RSROA_VAR_SMOD_DIFF_TYPE implementation,  I_T_VAR_RANGE_C is then filled and E_T_RANGE_C has to be filled. If you have experience very many problems in this EXIT_SAPLRRS0_001 function module, you have the option of calling the function module with the CL_RSROA_VAR_SMOD_DIFF_TYPE implementation. To do this, you must set the CL_RSROA_VAR_SMOD_DIFF_TYPE implementation to active and the SMOD_EXIT_CALL to inactive in transaction SE18.</p>\n<p>To activate or deactivate the implementations, call transaction SE18, select the BAdI RSROA_VARIABLES_EXIT_BADI and go into display mode. Here, choose the tab page \"Enhancem. Implementations\" and double-click on the entry CL_RSROA_VAR_SMOD. Now, go into change mode and select the entry CL_RSROA_VAR_SMOD_DIFF_TYPE (by double-clicking on it). On the right side of the screen in the area \"Runtime Behavior\", select \"Implementation is active\". Then, select the entry SMOD_EXIT_CALL and delete the selection \"Implementation is active\" for this BAdI implementation. Save and activate your changes.</p>\n<p>In this case you can adjust the function module using the \"Find and Replace\" editor function and change the names as follows in the table:</p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"3\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td><strong>Old</strong></td>\n<td><strong>New</strong></td>\n</tr>\n<tr>\n<td>i_t_var_range</td>\n<td>i_t_var_range_c</td>\n</tr>\n<tr>\n<td>e_t_range</td>\n<td>e_t_range_c</td>\n</tr>\n<tr>\n<td>rrrangeexit</td>\n<td>rrs0_s_var_range_c</td>\n</tr>\n<tr>\n<td>rrs0_s_var_range</td>\n<td>\n<p>rrs0_s_var_range_c</p>\n</td>\n</tr>\n<tr>\n<td>rrrangesid</td>\n<td>\n<p>rrs0_s_range_c</p>\n</td>\n</tr>\n<tr>\n<td>rsr_s_rangesid </td>\n<td>\n<p>rrs0_s_range_c</p>\n</td>\n</tr>\n</tbody>\n</table></div>\n<p>Most syntax and runtime errors should be deleted as a result. For the remaining problems, check whether you are using any other (user-defined) types, which you can also convert systematically. You must process any remaining problems (for example, calls of other functions, use of LIKE) using the solutions proposed above.</p>\n<p>You must also check whether you have transferred the CMOD exit into one or more BAdI implementations of RSROA_VARIABLES_EXIT_BADI. This is the exit technology currently recommended by SAP, and it offers several benefits. However, the I_T_VAR_RANGE_C and E_T_RANGE_C parameters are not available in this case.</p>\n<p> </p></div>", "noteVersion": 8}, {"note": "2526508", "noteTitle": "2526508 - BW4SL - Strategic Enterprise Management (SEM) APIs", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>The SEM APIs are not available in SAP BW/4HANA.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>$SEM, BAPI, RSSEM..., CL_RSSEM...</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>You can use the SAP BW ABAP Routine Analyzer to check if SEM APIs are used in custom code embedded in SAP BW objects (see SAP Note <a href=\"/notes/1847431\" target=\"_blank\">1847431</a>). However, there is no standard tool available to scan other custom developments.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Instead of SEM APIs, you should use standard APIs provided by SAP BW/4HANA. See SAP Note <a href=\"/notes/2462639\" target=\"_blank\">2462639</a> - BW4SL - Interfaces and Customer-Specific ABAP Development.</p>", "noteVersion": 1}, {"note": "2749804", "noteTitle": "2749804 - Support Package levels of SAP BW/4HANA installations/upgrades", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>This note contains technical information about the ABAP Support Package levels contained in the following installations and release upgrades (including Support Releases):</p>\n<p>SAP BW/4HANA 2023</p>\n<p>SAP BW/4HANA 2021</p>\n<p>SAP BW/4HANA 2.0</p>\n<p>SAP BW/4HANA 1.0 SR1</p>\n<p>SAP BW/4HANA 1.0</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>CVERS, Support Package, SW component, ABAP SP, Support Package Stack, SAP BW/4HANA 2023, SAP BW/4HANA 2021, SAP BW/4HANA 2.0, SAP BW/4HANA 1.0 SR1, SAP BW/4HANA 1.0</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Note:</p>\n<p>The initial upgrade and installation shipment of a product version may contain a lower SP stack than required for productive usage. <br/>For more details please check the corresponding “Release and Information Note” (e.g. Note <a href=\"/notes/2347382\" target=\"_blank\">2347382</a> for SAP BW/4HANA 1.0, Note <a href=\"/notes/2733740\" target=\"_blank\">2733740</a> for SAP BW/4HANA 2.0, Note <a href=\"/notes/3100794\" target=\"_blank\">3100794</a> for SP BW/4HANA 2021 or Note <a href=\"/notes/3365187\" target=\"_blank\">3365187</a> for SAP BW/4HANA 2023 ).</p>\n<p>Required SP stacks must be included into the upgrade or installed after the installation.</p>\n<p><strong>ABAP Support Packages contained in SAP BW/4HANA 2023:</strong></p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"3\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td><strong>Component</strong></td>\n<td><strong>Release</strong></td>\n<td><strong>SP-Level</strong></td>\n</tr>\n<tr>\n<td>DW4CORE</td>\n<td>400</td>\n<td>0</td>\n</tr>\n<tr>\n<td>SAP_ABA</td>\n<td>75I</td>\n<td>0</td>\n</tr>\n<tr>\n<td>SAP_BASIS</td>\n<td>758</td>\n<td>0</td>\n</tr>\n<tr>\n<td>SAP_GWFND</td>\n<td>758</td>\n<td>0</td>\n</tr>\n<tr>\n<td>SAP_UI</td>\n<td>758</td>\n<td>0</td>\n</tr>\n<tr>\n<td>ST-PI</td>\n<td>740</td>\n<td>23</td>\n</tr>\n<tr>\n<td>UIBAS001</td>\n<td>758</td>\n<td>0</td>\n</tr>\n</tbody>\n</table></div>\n<p><strong>ABAP Support Packages contained in SAP BW/4HANA 2021:</strong></p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"3\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td><strong>Component</strong></td>\n<td><strong>Release</strong></td>\n<td><strong>SP-Level</strong></td>\n</tr>\n<tr>\n<td>DW4CORE</td>\n<td>300</td>\n<td>0</td>\n</tr>\n<tr>\n<td>SAP_ABA</td>\n<td>75G</td>\n<td>0</td>\n</tr>\n<tr>\n<td>SAP_BASIS</td>\n<td>756</td>\n<td>0</td>\n</tr>\n<tr>\n<td>SAP_GWFND</td>\n<td>756</td>\n<td>0</td>\n</tr>\n<tr>\n<td>SAP_UI</td>\n<td>756</td>\n<td>0</td>\n</tr>\n<tr>\n<td>ST-PI</td>\n<td>740</td>\n<td>15</td>\n</tr>\n<tr>\n<td>UIBAS001</td>\n<td>700</td>\n<td>0</td>\n</tr>\n</tbody>\n</table></div>\n<p><strong>ABAP Support Packages contained in SAP BW/4HANA 2.0:</strong></p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"3\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td><strong>Component</strong></td>\n<td><strong>Release</strong></td>\n<td><strong>SP-Level</strong></td>\n</tr>\n<tr>\n<td>DW4CORE</td>\n<td>200</td>\n<td>0</td>\n</tr>\n<tr>\n<td>SAP_ABA</td>\n<td>75D</td>\n<td>1</td>\n</tr>\n<tr>\n<td>SAP_BASIS</td>\n<td>753</td>\n<td>1</td>\n</tr>\n<tr>\n<td>SAP_GWFND</td>\n<td>753</td>\n<td>1</td>\n</tr>\n<tr>\n<td>SAP_UI</td>\n<td>753</td>\n<td>2</td>\n</tr>\n<tr>\n<td>ST-PI</td>\n<td>740</td>\n<td>9</td>\n</tr>\n<tr>\n<td>UIBAS001</td>\n<td>400</td>\n<td>1</td>\n</tr>\n</tbody>\n</table></div>\n<p><strong>ABAP Support Packages contained in SAP BW/4HANA 1.0 SR1:</strong></p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"3\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td><strong>Component</strong></td>\n<td><strong>Release</strong></td>\n<td><strong>SP-Level</strong></td>\n</tr>\n<tr>\n<td>BW4CONTB</td>\n<td>100</td>\n<td>8</td>\n</tr>\n<tr>\n<td>DW4CORE</td>\n<td>100</td>\n<td>8</td>\n</tr>\n<tr>\n<td>SAP_ABA</td>\n<td>75A</td>\n<td>10</td>\n</tr>\n<tr>\n<td>SAP_BASIS</td>\n<td>750</td>\n<td>10</td>\n</tr>\n<tr>\n<td>SAP_GWFND</td>\n<td>750</td>\n<td>10</td>\n</tr>\n<tr>\n<td>SAP_UI</td>\n<td>752</td>\n<td>2</td>\n</tr>\n<tr>\n<td>ST-PI</td>\n<td>740</td>\n<td>7</td>\n</tr>\n</tbody>\n</table></div>\n<p><strong>ABAP Support Packages contained in SAP BW/4HANA 1.0:</strong></p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"3\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td><strong>Component</strong></td>\n<td><strong>Release</strong></td>\n<td><strong>SP-Level</strong></td>\n</tr>\n<tr>\n<td>DW4CORE</td>\n<td>100</td>\n<td>0</td>\n</tr>\n<tr>\n<td>SAP_ABA</td>\n<td>75A</td>\n<td>4</td>\n</tr>\n<tr>\n<td>SAP_BASIS</td>\n<td>750</td>\n<td>4</td>\n</tr>\n<tr>\n<td>SAP_GWFND</td>\n<td>750</td>\n<td>4</td>\n</tr>\n<tr>\n<td>SAP_UI</td>\n<td>750</td>\n<td>4</td>\n</tr>\n<tr>\n<td>ST-PI</td>\n<td>740</td>\n<td>3</td>\n</tr>\n</tbody>\n</table></div>", "noteVersion": 4}, {"note": "2463800", "noteTitle": "2463800 - BW4SL - Customer Relationship Management (CRM) BAPI & Customer Segmentation", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>The CRM tools and CRM BAPI are not available in SAP BW/4HANA.</p>\n<p>The missing CRM BAPI and function modules of development class RSCRM are called by retraction use cases in SAP ERP e.g., plan data transfer to budget control system in PSM-FM-BCS (transaction FMCYCOPI_BW) and retraction of cost based CO-PA data (transaction KELR/KELM) and therefore not longer supported with SAP BW/4HANA.</p>\n<p>Furthermore, the following functionality to perform high data volume CRM customer segmentation is not available in SAP BW/4HANA:</p>\n<ul>\n<li><a href=\"https://help.sap.com/viewer/60f0c9d745b446b694566eef8fc46e14/********/en-US/4aa1f5f452b849da92d6b75c986f7ee8.html\" target=\"_blank\">Segmentation with High Data Volume from SAP NetWeaver BW</a></li>\n<li><a href=\"https://help.sap.com/viewer/60f0c9d745b446b694566eef8fc46e14/********/en-US/a654cb5198a79f55e10000000a423f68.html\" target=\"_blank\">Distinction Between BWA and BW High-Volume Segmentationwith HANA</a></li>\n</ul>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>$CRM, BAPI, BAPI_REP..., RSCRM..., RSDRCRM, RSDRCRM_SEG, KELR, Retraction</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>There is no analysis available, if the CRM tool, BAPI, or customer segmentation are used or not.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Instead of the CRM BAPI, you can use the external SAP HANA views of the SAP BW/4HANA InfoProviders. This allows direct SQL access to SAP BW/4HANA data. The external SAP HANA views need to be generated for all objects where a SQL access is necessary.</p>\n<p>There is currently no replacement for the other CRM-related features.</p>\n<p><strong>Related Information</strong></p>\n<p><a href=\"https://uacp2.hana.ondemand.com/viewer/107a6e8a38b74ede94c833ca3b7b6f51/1.0.3/en-US/6633d851345c4770bd4e523701b9f5b0.html\" target=\"_blank\">Generating SAP HANA Views from the BW System</a></p>", "noteVersion": 6}, {"note": "2635167", "noteTitle": "2635167 - Handling of Material InfoObjects with Conversion to SAP BW/4HANA", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You want to convert a SAP BW system (sender/original system) to SAP BW/4HANA (receiver/target system).</p>\n<p>This SAP Note explains how infoObjects with conversion exits MATN1 and MATNB - like 0MATERIAL - will be handled during conversion to SAP BW/4HANA. You can use the attached program ZBW_CONV_EXIT_MATN to find all impacted InfoObjects in your system.</p>\n<p>In the original system, conversion functionality in Material InfoObjects can be used with conversion exits MATN1 or MATNB (above SAP BW 7.4 SP10). These conversion exits use the below tables that can be maintained by the customer.</p>\n<ul>\n<li>TMCNV (transaction OMSL) - Conversion exit MATN1 uses customizing settings maintained in this table. Maximum material length can be 18 using conversion exit MATN1.</li>\n<li>RSTMCNV (Optional) – Conversion exit MATNB uses customizing settings maintained in this table.  Maximum material length can be 40 using conversion exit MATNB for above SAP BW releases. For numeric material, maximum length is 18.</li>\n</ul>\n<p>Starting from SAP BW 7.40 SP18, SAP BW 7.50 SP09, SAP BW 7.51 SP04 (see SAP Note <a href=\"/notes/2469073\" target=\"_blank\">2469073</a> for installation on lower SPs), it is possible to extract data containing material numbers longer than 18 characters into InfoObjects using the conversion exit MATNB (replacement of MATN1 for longer material numbers). Conversion exit MATNB is available as of BI CONT 7.47 SP 21 and 7.57 SP 14.</p>\n<p>In the target system, only one conversion exit MATN1 exists which uses customizing settings maintained in table TMCNV. Conversion exit MATNB and table RSTMCNV are not available in SAP BW/4HANA.</p>\n<p>In SAP BW/4HANA, maximum material length can be 40. With standard customizing (lexicographic flag not set, i.e. leading zeroes are not significant), purely numeric material numbers are still restricted to 18 characters and will be filled up in the database up to only 18 characters by leading zeroes.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>BW/4HANA, DMIS, BW/4 Remote Conversion, BW/4 Migration, <span>BW/4HANA Conversion Cockpit, Conversion to BW/4HANA, 0MATERIAL, 0MAT_PLANT, 0MAT_SALES</span></p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>Conversion to SAP BW/4HANA</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong>Customization Comparison Logic</strong></p>\n<p>Comparison of customization will happen:</p>\n<ul>\n<li>Within the original system between customization maintained in table RSTMCNV and TMCNV only if RSTMCNV customization exists</li>\n<li>Between customization maintained in table TMCNV in target system with one of the customization maintained in the original system in table TMCNV and RSTMCNV, whichever holds higher value of “Length of Material” (valid only for Remote Conversion)</li>\n</ul>\n<p>Logic is as follows:</p>\n<ol>\n<li>Comparison of <strong>Lexicographic indicator</strong>, if it is different, then process will end with an error.</li>\n<li>Comparison of <strong>Length of Material</strong>, among 2 customization settings which ever holds higher length of the material must also have higher value of “Significant” length of the material, if not then the process will end with an error.</li>\n</ol>\n<p><strong>Pre-Check executed in the Transfer Cockpit</strong></p>\n<p>Here, the checks are done within original system. There can be different scenarios based on the TMCNV and RSTMCNV customizations in original system. Scenarios are explained with its output in the Pre-Check report:</p>\n<ul>\n<li>Table RSTMCNV doesn’t exists OR No customization maintained in the table RSTMCNV in the original system.<br/>In this situation, process should end with success.</li>\n<li>Customization maintained in table RSTMCNV in the original system then comparison logic will be executed (mentioned above).</li>\n<ul>\n<ul>\n<li>If there are no difference, then process will end with success</li>\n<li>If there are differences, then process error will end with an error</li>\n</ul>\n</ul>\n</ul>\n<p><strong>Note</strong>: During data migration, if table TMCNV is already customized in target system then customization comparison of the original system will also be done with the target system.</p>\n<p><strong>For In-place Conversion</strong></p>\n<p>In case of In-place Conversion the conversion exit MATNB will be replaced automatically with MATN1 during the change of the operation mode to ‘Ready to Conversion’ (see task “Replace Conversion Exit MATNB with MATN1”).</p>\n<p>The customization in the table RSTMCNV must be manually removed and maintained only in the table TMCNV.</p>\n<p><strong><em>For Remote Conversion</em></strong><strong><br/><br/></strong>During import to the target system,</p>\n<ul>\n<li>the conversion exit of all InfoObjects currently using MATNB is replaced with MATN1.</li>\n<li>the length of all InfoObjects using MATN1 (including those from 1) is set to 40 or to a length greater than 40 if the currently active version is of a length greater than 40.</li>\n</ul>\n<p>There can be different scenarios based on TMCNV and RSTMCNV customizations in original and target system. These scenarios are explained below:</p>\n<ol>\n<li><strong>Scenario 1: </strong><em>Only TMCNV is configured in original system</em></li>\n<ol>\n<li><em>TMCNV is configured in target system</em>, then customization will be compared based on the logic mentioned above.</li>\n<ol>\n<li>If any setting is different and error can’t be removed by the relevant adjustment in the customizations, then please contact SAP consulting for support.<br/><br/><strong>Example: </strong>Lexicographical indicator is set in original system and it is not set in target system. Material will be stored in format as shown below:<br/><br/>Original System:<br/>      Defined length: 18 characters<br/>      Internally/externally assigned number: 12<br/>      Stored number: 123<br/><br/>Target System:<br/>      Defined length: 40 characters<br/>      Internally/externally assigned number: 123<br/>      Stored number: 000000000000000123<br/><br/>This difference in storing the same material, is a problem from data consistency perspective. This scenario needs to be analyzed and approach must be checked for data migration.</li>\n<li>If all settings are same, higher value of material length among original system and target system is used in target system settings.<br/><strong>Example<br/></strong>If material length is 18 in original system and 20 in target system, then length 20 is kept in target system.</li>\n</ol>\n<li>If TMCNV is not configured in target system, then customizing settings maintained in table TMCNV from original system are replicated in target system.</li>\n</ol>\n<li><strong>Scenario 2: </strong>Both TMCNV and RSTMCNV are configured in original system.</li>\n<ol>\n<li>If customization maintained in TMCNV and RSTMCNV in original system is same as per the comparison logic, then</li>\n<ol>\n<li>If customization in table TMCNV is not maintained in the target system then customizing settings from original system will be replicated in target system during data migration. Higher value of Material Length among TMCNV and RSTMCNV is used in target system.<br/><strong>Example:<br/></strong>If material length is 17 in TMCNV and 18 in RSTMCNV in original system, then length 18 is set in target system.</li>\n<li>If customization in table TMCNV is maintained in the target system, then target system customizing settings will be compared with original system and</li>\n<ul>\n<li>If there are no differences found in comparison then higher value of material length among 2 settings will be used in target system.<br/><strong>Example<br/></strong>If material length is 18 in original system and 17 in target system, then length 18 is set in target system.</li>\n<li>If there are differences found and error can’t be removed by the relevant adjustment in the customizations, then please contact SAP consulting for support.<br/><strong>Example<br/></strong>Lexicographical indicator is set in original system and it is not set in target system. Material will be stored in format as shown below: <br/><br/>Original System:<br/>      Defined length: 8 characters<br/>      Internally/externally assigned number: 123<br/>      Stored number: 123<br/><br/>Target System:<br/>      Defined length: 40 characters<br/>      Internally/externally assigned number: 123<br/>      Stored number: 000000000000000123</li>\n</ul>\n</ol>\n<li>If customization maintained in TMCNV and RSTMCNV in original system is different as per the comparison logic and if error can’t be removed by the relevant adjustment in the customizations, then please contact SAP consulting for support.<br/><strong>Example<br/></strong>Lexicographical indicator is not set in TMCNV and it is set in RSTMCNV.<br/><br/>InfoObjects with conversion exit MATN1:<br/>     Defined length: 18 characters<br/>     Internally/externally assigned number: 123<br/>     Stored number: 000000000000000123<br/><br/>InfoObjects with conversion exit MATNB:<br/>     Defined length: 40 characters<br/>     Internally/externally assigned number: 123<br/>     Stored number: 123<br/><br/>This creates a problem of consolidation of material InfoObjects with conversion exit MATN1 and MATNB in target system as the target system can only have one customization TMCNV for InfoObjects with conversion exit MATN1. This scenario needs to be analyzed and approach needs to be finalized for data migration.</li>\n</ol></ol>", "noteVersion": 1}]}, {"note": "2421930", "noteTitle": "2421930 - Simplification List for SAP BW/4HANA", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>Simplification List for SAP BW/4HANA is published as PDF document on the SAP Help Portal (<a href=\"http://help.sap.com/bw4hana20\" target=\"_blank\">http://help.sap.com/bw4hana20</a>). This SAP Note provides the Simplification List in spreadsheet format.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>SIMPL, BW4HANA</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>Planning to transition from SAP BW to SAP BW/4HANA.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>To enable our customers to better plan and estimate their path to SAP BW/4HANA, we have created the “Simplification List for SAP BW/4HANA”. In this list we describe in detail, on a functional level, what happens to individual object types and solution capabilities when transitioning to SAP BW/4HANA. Compared to the SAP Business Warehouse products, we have in some cases merged certain functionality with other elements or reflected it within a new solution or architecture.</p>\n<p><strong>A new, web-based UI for searching and displaying Simplification Items</strong></p>\n<p>In parallel to the PDF document and the spreadsheet available via this SAP Note, the <em>SAP Simplification Item Catalog</em> offers direct search and browse of SAP BW/4HANA Simplification Items in their current state via the SAP ONE Support Launchpad at <a href=\"https://launchpad.support.sap.com/#/sic/\" target=\"_blank\">https://launchpad.support.sap.com/#/sic/</a>.</p>", "noteVersion": 6, "refer_note": [{"note": "2347382", "noteTitle": "2347382 - SAP BW/4HANA – General Information (Installation, SAP HANA, security corrections…)", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You want to run or you are already running an SAP BW/4HANA system.<br/>You want to convert your SAP BW system to SAP BW/4HANA.<br/>You want to upgrade your system from SAP BW/4HANA 1.0 to SAP BW/4HANA 2.0</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Revision, BW4, BW/4HANA, BW, NetWeaver</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p><strong>This note contains information about the following topics:</strong></p>\n<ol>\n<li>General Information about SAP BW/4HANA and the conversion to SAP BW/4HANA</li>\n<li>Recommendations with regards to SAP BW/4HANA releases and SAP HANA revisions</li>\n<li>Information relevant for SAP BW/4HANA 2023</li>\n<li>Information relevant for SAP BW/4HANA 2021</li>\n<li>Information relevant for SAP BW/4HANA 2.0</li>\n<li>Information relevant for SAP BW/4HANA 1.0</li>\n</ol>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong><span>1. General information about SAP BW/4HANA and the conversion to SAP BW/4HANA</span></strong></p>\n<p><span>High Level information about BW/4HANA: <a href=\"http://www.sap.com/bw4hana\" target=\"_blank\">http://www.sap.com/bw4hana</a></span></p>\n<p><span>See SAP Note <a href=\"/notes/2347384 \" target=\"_blank\">2347384</a> - SAP BW/4HANA 1.0 Important Notes before you start</span></p>\n<p><span>When planning your system landscape(s) for the next release of SAP BW/4HANA (not release 1.00), you may want to take the available applicaton server platforms into account. Please refer to SAP note <a href=\"/notes/2620910\" target=\"_blank\">2620910</a> </span></p>\n<p><span>SAP changed the <a href=\"https://support.sap.com/en/my-support/knowledge-base/security-notes-news.html\" target=\"_blank\">SAP security strategy</a> to cover security corrections for Support Packages delivered during the past 24 months (formerly 18 months). BW/4HANA will not extend this period. We provide security corrections for Support Packages delivered in the last 18 months as before.</span></p>\n<p><span>See note </span><a href=\"/notes/2733740\" target=\"_blank\">https://launchpad.support.sap.com/#/notes/2733740</a> for Information/Restriction about Relase BW/4HANA 2.0</p>\n<p><strong>2. Recommendations with regards to SAP BW/4HANA releases and SAP HANA revisions</strong></p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"3\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td><strong>Productively used SAP BW/4HANA release</strong></td>\n<td><strong>Recommended minimum SAP HANA SP/revision</strong></td>\n</tr>\n<tr>\n<td>1.0</td>\n<td>Revision 122.12 or higher (HANA 1.0 SPS 12)<br/>Revision 12 or higher (HANA 2.0 SPS 1 or higher)</td>\n</tr>\n<tr>\n<td>2.0</td>\n<td>Revision 64 or higher (HANA 2.0 SPS 6 or higher)</td>\n</tr>\n<tr>\n<td>2021</td>\n<td>Revision 64 or higher (HANA 2.0 SPS 6 or higher)</td>\n</tr>\n<tr>\n<td>2023</td>\n<td>Revision 71 or higher (HANA 2.0 SPS 7 or higher)</td>\n</tr>\n</tbody>\n</table></div>\n<p>Please note that SAP strongly recommends to apply the <span>latest available</span> SAP HANA revision.</p>\n<p>For details about SAP HANA Maintenance Strategy see SAP Note <em><a href=\"/notes/2021789\" target=\"_blank\">2021789</a> - SAP HANA Revision and Maintenance Strategy</em>.</p>\n<p>Please keep the update paths for Maintenance Revisions in mind. Details can be found in SAP Note <em><a href=\"/notes/1948334\" target=\"_blank\">1948334</a> - SAP HANA Database Update Paths for Maintenance Revisions</em>.</p>\n<p>Notes with regards to SAP HANA Revisions:</p>\n<ul>\n<li>HANA SPS12: SAP Note <a href=\"/notes/2298750\" target=\"_blank\">2298750</a> - SAP HANA Platform SPS 12 Release Note</li>\n</ul>\n<p><span>SAP BW/4HANA and SAP HANA 2.0: </span></p>\n<p>SAP BW/4HANA 1.00 (including SAP_BASIS 7.50) has been released for HANA 2.0. Details can be found in note 2420699.<br/>SAP BW/4HANA 2.00 (including SAP_BASIS 7.53) and newer run on HANA 2.0 only</p>\n<p><strong> </strong></p>\n<p><strong>3. </strong><strong>Information relevant for SAP BW/4HANA 2023 (planned mid of Q4/2023)</strong></p>\n<p>You can find the detailed information in SAP Note: <a href=\"3365187\" target=\"_blank\">3365187 - Information/Restriction Note for Release SAP BW/4HANA 2023</a>.</p>\n<p><strong><strong>4. Information relevant for SAP BW/4HANA 2021</strong></strong></p>\n<p>You can find the detailed information in SAP Note: <a href=\"3100794\" target=\"_blank\">3100794 - Release Information/Restrictions Note for SAP BW/4HANA 2021</a>.</p>\n<p><strong><strong><strong>5. Information relevant for SAP BW/4HANA 2.0</strong></strong></strong></p>\n<p>You can find the detailed information in SAP Note: <a href=\"https://me.sap.com/notes/2733740\" target=\"_blank\">2733740 - Release Information/Restrictions Note for SAP BW/4HANA 2.0</a></p>\n<ul>\n<li><span>General</span></li>\n<ul>\n<li>See note 2733740 - Release Information/Restriction Note for SAP BW/4HANA 2.0 for restrictions in BW/4HANA 2.0</li>\n<li>Urgent! Implement note <a href=\"/notes/2770525\" target=\"_blank\" title=\"2770525  - Data Transfer Intermediate Storage (DTIS): Missing package assignment\">2770525 - Data Transfer Intermediate Storage (DTIS): Missing package assignment</a> <span><strong>before</strong></span> upgrading to BW/4HANA 2.0</li>\n<li><strong>Before </strong>you start the setup via tasklist (stc01) SAP_BW4_SETUP_SIMPLE check if all HANA authorizations are maintained like in SAP Note <a href=\"/notes/2362807\" target=\"_blank\">2362807</a> - BW Search/Value Help/Open in SAP BW Modeling Tools does not give a result.</li>\n<li>Introduction Video: <a href=\"https://youtu.be/bxH0EzAhSwQ\" target=\"_blank\">https://youtu.be/bxH0EzAhSwQ</a></li>\n</ul>\n<li><span>Upgrading BW/4HANA 1.0 to 2.0</span></li>\n<ul>\n<li>We now introduced tasklist SAP_BW4_BEFORE_UPGRADE and SAP_BW4_AFTER_UPGRADE  to support you upgrading your system. Please see note 2846537 - Taskliste for upgrade of BW/4HANA</li>\n<li>Afte the upgrade to BW/4HANA 2.0 the flag TADIR Popup f. Obj. in View RSADMINC gets initialized. Please see note 2884312 for details.</li>\n</ul>\n<li><span>ABAP Software Stack</span></li>\n<ul>\n<li>The initial Software stack for BW/4HANA 2.0 looks like this:</li>\n<ul>\n<li>SAP_BASIS 7.53 SP01</li>\n<li>SAP_ABA 7.5D SP01</li>\n<li>SAP_UI 7.53 SP02</li>\n<li>SAP_GWFND 7.53 SP01</li>\n<li>ST-PI 7.40 SP09</li>\n<li>DW4CORE 2.00 SP00</li>\n<li>UIBAS100 4.00 SP01</li>\n</ul>\n<li>This should dramatically reduces the effort for installation</li>\n<li>During the upgrade from BW/4HANA 1.0 the missing software components are installed automatically</li>\n<li></li>\n</ul>\n<li><span>BW/4HANA 2.0 SP01</span></li>\n<ul>\n<li>Please install following note on top of SP01 directly after update:</li>\n<ul>\n<li>2779827  Error starting task list SAP_BW4_LAUNCHPAD_SETUP from other task lists</li>\n</ul>\n</ul>\n</ul>\n<ul>\n<li><span>BW/4HANA 2.0 SP02 </span></li>\n<ul>\n<li>SAP_UI 7.54 SP1 as alternative UI version is released. Please check notes <strong>2903662 </strong>and<strong> 2908858</strong>; please note that \"SAP Quartz Light\" introduced with 1.65 is not yet supported but will be supported as of FP07</li>\n<li>please also take into consideration the Support Package Upgrade Strategy when running on higher UI Version described in note <strong>2618449</strong></li>\n</ul>\n</ul>\n<ul>\n<li><span>BW/4HANA 2.0 SP04</span></li>\n<ul>\n<li>SAP_UI 7.54 &gt;= SP2 as alternative UI version is released. lease check notes <strong>2903662 </strong>and<strong> 2908858</strong>; please note that \"SAP Quartz Light\" introduced with 1.65 is not yet supported but will be supported as of FP07. Also take into consideration the Support Paackage Strategy when running on higher UI version described in note <strong>2618449</strong></li>\n<li>NSE for DTO --&gt; available since HANA 2.0 SP04 + limitation see HANA Note <a href=\"/notes/2771956\" target=\"_blank\">https://launchpad.support.sap.com/#/notes/2771956</a></li>\n<li>Open Hub with SDA --&gt; available since HANA 2.0 SP04 Revision 46</li>\n<li>XDA --&gt; available since HANA 2.0 Sp04 Revision 40 </li>\n<li>Geo Support --&gt; available since HANA 2.0 SP04 Revision 45</li>\n<li>Treespec Partitioning in ADSO --&gt; available since Hana 2.0 SP04 </li>\n</ul>\n</ul>\n<p><strong>6. Information relevant for SAP BW/4HANA 1.0</strong></p>\n<p>Installation/Setup</p>\n<ul>\n<li><span>General</span></li>\n<ul>\n<li>Please see SAP Note<strong> </strong><a href=\"/notes/2354516\" target=\"_blank\">2354516</a> to enable client copy after installation.</li>\n<li><strong>Before </strong>you start the setup via tasklist (stc01) SAP_BW4_SETUP_SIMPLE check if all HANA authorizations are maintained like in SAP Note <a href=\"/notes/2362807\" target=\"_blank\">2362807</a> - BW Serach/Value Help/Open in SAP BW Modeling Tools does not give a result.</li>\n<li>Equivalent Support Packages<br/>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"3\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td>SAP BW 7.50 (SAP_BW)</td>\n<td>BW/4HANA 1.00(DW4CORE)</td>\n<td>BPC 11 (BPC4HANA)</td>\n<td>SAP UI 7.52 </td>\n</tr>\n<tr>\n<td>               4</td>\n<td>        Initial Installation</td>\n<td>           N.A.</td>\n<td> </td>\n</tr>\n<tr>\n<td>               5</td>\n<td>                   1</td>\n<td> </td>\n<td> </td>\n</tr>\n<tr>\n<td>               6</td>\n<td>                   &gt;= 2</td>\n<td> </td>\n<td> </td>\n</tr>\n<tr>\n<td>               7</td>\n<td>                   &gt;= 4</td>\n<td>  Initial Installation</td>\n<td> </td>\n</tr>\n<tr>\n<td>               8</td>\n<td>                   &gt;= 5</td>\n<td>             &gt;= 1</td>\n<td> </td>\n</tr>\n<tr>\n<td>               9</td>\n<td>                   &gt;= 6</td>\n<td>             &gt;= 2             </td>\n<td> </td>\n</tr>\n<tr>\n<td>             10</td>\n<td>                   &gt;= 7</td>\n<td>             &gt;= 3</td>\n<td> </td>\n</tr>\n<tr>\n<td>             11*</td>\n<td>                   &gt;=8</td>\n<td>             &gt;=4</td>\n<td>      &gt;=2</td>\n</tr>\n<tr>\n<td>             12*</td>\n<td>                   &gt;=9</td>\n<td>             &gt;=5</td>\n<td>      &gt;=3</td>\n</tr>\n</tbody>\n</table></div>\n</li>\n</ul>\n</ul>\n<p><span>*) Starting with BW/4HANA 1.00 SP08 SAP_UI 7.52 is required</span></p>\n<ul>\n<li><span>BW/4HANA SP08</span></li>\n<ul>\n<li><strong>Requirements:</strong></li>\n<ul>\n<li>SAP_BASIS 7.50 SP10</li>\n<li>SAP_GWFND 7.50 SP10</li>\n<li>SAP_ABA 7.5A SP10</li>\n<li>SAP_UI <strong>7.52 SP01 -- update required</strong></li>\n</ul>\n<li><strong>For new Installs:</strong></li>\n<ul>\n<li>For your convience we created a Support Release for BW/4HANA 1.00 including the following components:</li>\n<ul>\n<li>SAP_BASIS 7.50 SP10</li>\n<li>SAP_GWFND 7.50 SP10</li>\n<li>SAP_ABA 7.5A SP 10</li>\n<li>SAP_UI 7.52 SP02</li>\n<li>DW4CORE SP08</li>\n<li>BW4CONTB SP03</li>\n</ul>\n<li>this installation is based on Software Provisioning Manager 2.0 which requires HANA 2 SP2 Revision 23 see note 2610954.</li>\n<li>Release of the installation is planned for april (CW 17)</li>\n</ul>\n<li><strong>Enhancment in Tasklist</strong></li>\n<ul>\n<li>there are some enhancements made in the tasklists with regards to the new Web UI's: Please refert to note 2351381 for details.</li>\n</ul>\n</ul>\n<li><span>BW/4HANA SP03</span></li>\n<ul>\n<li>In some cases the execution of the tasklist SAP_BW4_SETUP_SIMPLE is dumping. Please have a look at 2351381 how to disable the invalid BAPI</li>\n</ul>\n<li><span>BW/4HANA SP02</span></li>\n<ul>\n<li>In some cases the execution of the tasklist SAP_BW4_SETUP_SIMPLE is dumping. Please have a look at 2351381 how to disable the invalid BAPI</li>\n<li>Please implement the following notes on top of SP02 to avoid issues during setup/tasklisk execution</li>\n<ul>\n<li>2410466                BW Workspace customizing: RSDDTREXADMIN_TO_RSWSPCUST dumps </li>\n</ul>\n</ul>\n<li><span>BW/4HANA SP01</span></li>\n<ul>\n<li>Please implement the following notes on top of SP01 to avoid issues during setup/later SP update</li>\n<ul>\n<li>2385382               Add SAPFSPOR to avoid strange DUMPS during SP update</li>\n<li>2385265               Check if essential objects are active after installation</li>\n<li>2381312               Task list SAP_BW4_SETUP_SIMPLE: Error in \"Check/Generate Packages for BW ODATA Services\" step</li>\n</ul>\n<li>General Information about tasklists and their usage in BW/4HANA: 2351381 SAP BW/4HANA task lists</li>\n</ul>\n</ul>\n<ul>\n<li><span>BW/4HANA SP00</span></li>\n<ul>\n<li>If job BI_WRITE_PROT_TO_APPLLOG is not started by the tasklist please see SAP Note <a href=\"/notes/2356498\" target=\"_blank\">2356498</a>.</li>\n<li>In SP0 the tasklist shows an error which can be ignored. See SAP Note <a href=\"/notes/2351344\" target=\"_blank\">2351344</a> for details.</li>\n</ul>\n</ul>", "noteVersion": 36}, {"note": "2383530", "noteTitle": "2383530 - Conversion from SAP BW to SAP BW/4HANA", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>This note describes the different paths to get from SAP BW to SAP BW/4HANA.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>SAP BW/4HANA, SAP BW, Migration, Conversion, Transfer, In-Place, Remote, Shell, Lifecycle Management</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>Conversion to BW/4HANA</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><span><strong>0 Disclaimer</strong></span></p>\n<p>Converting a SAP BW system to SAP BW/4HANA is not a simple task. There is no \"wizard\" that magically converts everything. Instead, SAP provides a well-defined process to guide you through the renovation of your data warehouse. We have developed tools to automate this renovation where applicable and feasible, but they are not built or meant to fix badly designed models or clean-up neglected systems. In any conversion there is a need for manual interaction and re-design. The amount of such manual tasks varies from customer to customer and depends on the configuration and state of the SAP BW system. For example, a conversion of newer systems that have been configured using SAP HANA-optimized objects and LSA++ from the beginning will be relatively easy. In contrast to that, a conversion of older systems that are running on non-SAP HANA databases, using out-of-date interfaces, legacy data models, and multiple redundant layers can become quite challenging.</p>\n<p>It is therefore essential that you understand the differences between SAP BW and SAP BW/4HANA and how to handle them, thoroughly analyze your existing system, estimate the complexity and duration of required tasks, properly plan all conversion activities, learn and practice to use the conversion tools, test the conversion processes ideally with a \"copy of production\", and actively manage changes to your business. It speaks for itself that good project management and skilled personnel will be required to execute a timely and efficient conversion of a complete SAP BW system. If done right, your conversion to SAP BW/4HANA will be very successful.</p>\n<p>SAP offers several way to help. To get to started run a <a href=\"https://help.sap.com/viewer/p/SAP_READINESS_CHECK\" target=\"_blank\">SAP Readiness Check</a> or book a <a href=\"http://sapsupport.info/offerings/sap-value-assurance/\" target=\"_blank\">SAP Value Assurance service </a><a href=\"http://sapsupport.info/offerings/sap-value-assurance/\" target=\"_blank\">for SAP BW/4HANA</a>.</p>\n<p>We also highly recommend the Class Room Training <strong>BW4HC</strong>. (<a href=\"https://training.sap.com/course/bw4hc-system-conversion-to-sap-bw4hana-classroom-017-de-en/\" target=\"_blank\">https://training.sap.com/course/bw4hc-system-conversion-to-sap-bw4hana-classroom-017-de-en/</a>?).</p>\n<p> </p>\n<p><span><strong>1 Overview</strong></span></p>\n<p><strong>1.1 What is SAP BW/4HANA</strong></p>\n<p>SAP BW/4HANA is a new, next generation data warehouse product from SAP that, like SAP S/4HANA, is optimized for the SAP HANA platform, including inheriting the high performance, simplicity, and agility of SAP HANA. SAP BW/4HANA delivers real time, enterprise wide analytics that minimize the movement of data and can connect all the data in an organization into a single, logical view, including new data types and sources.<strong><br/></strong></p>\n<p><strong>1.2 Related Information</strong></p>\n<p>Please find more information here:</p>\n<ul>\n<li>The Future of Data Warehousing - SAP BW/4HANA  <br/><a href=\"http://sap.com/bw4hana\" target=\"_blank\">http://sap.com/bw4hana</a></li>\n<li>SAP BW/4HANA Community (includes FAQ)<br/><a href=\"https://community.sap.com/topics/bw4-hana\" target=\"_blank\">https://community.sap.com/topics/bw4-hana</a><br/><a href=\"https://www.sap.com/documents/2016/08/c4458a08-877c-0010-82c7-eda71af511fa.html\" target=\"_blank\">https://www.sap.com/documents/2016/08/c4458a08-877c-0010-82c7-eda71af511fa.html</a></li>\n<li>Online Documentation<br/><a href=\"http://help.sap.com/bw4hana10\" target=\"_blank\">http://help.sap.com/bw4hana10</a></li>\n<li>Product Roadmap<br/><a href=\"https://roadmaps.sap.com/board?range=FIRST-LAST&amp;PRODUCT=73554900100800000681#Q1%202021\" target=\"_blank\">https://roadmaps.sap.com/board?range=FIRST-LAST&amp;PRODUCT=73554900100800000681#Q1%202021</a></li>\n<li>SAP BW/4HANA vs. SAP BW<br/><a href=\"https://www.sap.com/documents/2017/08/30aa5e11-cf7c-0010-82c7-eda71af511fa.html\" target=\"_blank\">https://www.sap.com/documents/2017/08/30aa5e11-cf7c-0010-82c7-eda71af511fa.html</a><br/><br/></li>\n</ul>\n<p>For information related to functionality available in SAP BW but replaced or deleted in SAP BW/4HANA please refer to the Simplification List: <br/><a href=\"https://help.sap.com/doc/590752e646cc4b15a9092f32353b209a/1.0/en-US/SAP_BW4HANA_10_Simplification_List.pdf\" target=\"_blank\">https://help.sap.com/doc/590752e646cc4b15a9092f32353b209a/1.0/en-US/SAP_BW4HANA_10_Simplification_List.pdf</a></p>\n<p>For a holistic view of the Conversion topic, see Conversion Guide for SAP BW/4HANA 1.0: <br/><a href=\"https://help.sap.com/doc/c3f4454877614bc7b9e85ae1f9d1d2c7/1.0/en-US/SAP_BW4HANA_10_Conversion_Guide.pdf\" target=\"_blank\">https://help.sap.com/doc/c3f4454877614bc7b9e85ae1f9d1d2c7/1.0/en-US/SAP_BW4HANA_10_Conversion_Guide.pdf</a></p>\n<p>For a holistic view of the Conversion topic, see Conversion Guide for SAP BW/4HANA 2.0:</p>\n<p><a href=\"https://help.sap.com/doc/999ae5f8c578402dab1fea94fa4599f9/2.0/en-US/SAP_BW4HANA_20_Conversion_Guide.pdf\" target=\"_blank\">https://help.sap.com/doc/999ae5f8c578402dab1fea94fa4599f9/2.0/en-US/SAP_BW4HANA_20_Conversion_Guide.pdf</a></p>\n<p>For a holistic view of the Conversion topic, see Conversion Guide for SAP BW/4HANA 2021 and higher:</p>\n<p><a href=\"https://help.sap.com/doc/c8e1ac37fee64e0f887a2d6d6af32a33/2021.00/en-US/SAP_BW4HANA_2021_Conversion_Guide.pdf\" target=\"_blank\">https://help.sap.com/doc/c8e1ac37fee64e0f887a2d6d6af32a33/2021.00/en-US/SAP_BW4HANA_2021_Conversion_Guide.pdf</a></p>\n<p> </p>\n<p><span><strong>2 How to Get to BW/4HANA</strong></span></p>\n<p><strong>2.1 Paths to SAP BW/4 HANA</strong></p>\n<p><strong><em>2.1.1 New implementation or fresh start</em></strong></p>\n<p>New implementations are the best choice for customers converting from a legacy system or building a system from scratch with new data model only.</p>\n<p><strong>Steps</strong>: Install SAP BW/4HANA, selective transport of BW objects (optional), implement HANA-optimized data models and flows, followed by data load and quality checks.</p>\n<p><strong>What to use</strong>: Software Provisioning Manager (SWPM), system connection (SAP Source), SAP BW/4HANA ETL, file upload and/or SAP HANA EIM (legacy sources)</p>\n<p>See SAP First Guidance: Complete functional scope (CFS) for SAP BW/4HANA: <a href=\"https://www.sap.com/documents/2016/09/b001a9de-8a7c-0010-82c7-eda71af511fa.html\" target=\"_blank\">https://www.sap.com/documents/2016/09/b001a9de-8a7c-0010-82c7-eda71af511fa.html<br/></a></p>\n<p><em><strong>2.1.2 System conversion</strong></em></p>\n<p>A system conversion addresses customers who want to change their current SAP BW system into a SAP BW/4HANA system. Using the Transfer Cockpit provided by SAP, the logical systemname of the system can be kept (in-place conversion) or a new  logical systemname can be used (remote conversion resp. shell conversion).</p>\n<p>A conversion project typically follows this sequence:</p>\n<ul>\n<li><strong>Discover / Prepare Phase</strong>: check system for BW/4HANA compliance (gather information about objects and code that needs to be transferred or changed), estimate effort for the conversion project</li>\n<li><strong>Explore / Realization Phase</strong>: Transfer legacy objects into HANA-optimized counterparts, system conversion, post conversion tasks</li>\n</ul>\n<p><em>*******. In-Place conversion</em></p>\n<p>Systems running on SAP BW 7.50 powered by SAP HANA can be converted in-place keeping their logical systemname. In the realization phase of the conversion project, classic objects have to be transferred into their HANA optimized replacements using the Transfer Cockpit. This transfer can be performed scenario-by-scenario. When all classic objects have been replaced, the system conversion to SAP BW/4HANA can be triggered.</p>\n<p>SAP highly recommends to use the latest support package for SAP BW/4HANA when performing the conversion. See note https://launchpad.support.sap.com/#/notes/2347382 regarding the equivalent support packages. Please keep the RTC Dates of the different Support Packages in mind when you plan your conversion project.</p>\n<p><strong>Steps</strong>: Migrate and/or upgrade to SAP BW 7.50 powered by SAP HANA (if necessary), install SAP BW/4HANA Starter Add-On (see “Installation” below), install Transfer Cockpit, transfer data models, adjust custom coding, perform system conversion</p>\n<p>To include the Data Comparison Functionality into InPlace-TaskList, please refer to the SAP Note <a href=\"/notes/2607742\" target=\"_blank\">2607742</a>.</p>\n<p><strong>Incident Component</strong>: BW-B4H-CNV-IPL</p>\n<p><strong>What to use</strong>: SAP BW/4HANA Starter Add-On, SAP BW/4HANA Transfer Cockpit, SPAM/SAINT<br/><em></em></p>\n<p><em></em><em>*******. Remote conversion</em></p>\n<p>For SAP BW systems on releases from 7.30 to 7.50 running on Any-DB, a remote conversion can be performed. For that conversion type, a green field installation (new  logical systemname) of SAP BW/4HANA is used. The Transfer Cockpit is able to transport selected data models into the new installation and to perform a remote data transfer.</p>\n<p><strong>Steps</strong>: Install SAP BW/4HANA, install DMIS Add-On, transfer data models, transport custom developments (might need adjustment to work with SAP BW/4HANA)</p>\n<p><strong>Incident Component</strong>: BW-B4H-CNV-RMT</p>\n<p><strong>What to use</strong>: Software Provisioning Manager (SWPM), SAP BW/4HANA Transfer Cockpit, DMIS Add-On</p>\n<p><em>*******. Shell conversion</em></p>\n<p>For SAP BW systems on releases from 7.00 to 7.50 running on Any-DB, a Shell conversion can be performed. For that conversion type, a green field installation (new  logical systemname) of SAP BW/4HANA is used. The Transfer Cockpit is able to transport selected data models without data into the new installation.</p>\n<p>Our recommendation for shell conversion is to use a sandbox as the sender system. The latest SPs should also be installed on this system. Depending on the version of the current system, this saves a lot of time and effort. If you still decide to import the hints, you should look at the wiki <a href=\"https://wiki.scn.sap.com/wiki/pages/viewpage.action?pageId=562729718\" target=\"_blank\" title=\"https://wiki.scn.sap.com/wiki/pages/viewpage.action?pageId=562729718\">https://wiki.scn.sap.com/wiki/pages/viewpage.action?pageId=562729718</a> beforehand and follow the information from the wiki when importing.</p>\n<p><em><strong>Steps</strong>: </em>Install SAP BW/4HANA, transfer data models without data using SAP BW/4HANA Transfer Cockpit, and transport custom developments (might need adjustments to work with SAP BW/4HANA).</p>\n<p><strong>Incident Component</strong>: BW-B4H-CNV-SHL</p>\n<p><strong>What to use: </strong>Software Provisioning Manager (SWPM), SAP BW/4HANA Transfer Cockpit</p>\n<p><strong><em>2.1.3. Landscape transformation</em></strong></p>\n<p>Landscape transformation is for customers who want to consolidate and optimize their complex SAP BW landscape (multiple production systems) into a single SAP BW/4HANA system or who want to carve-out selected data models or flows into a global SAP BW/4HANA system.</p>\n<p><strong>Steps</strong>: Install SAP BW/4HANA, implement consolidated and HANA-optimized data models and flows, followed by data load and quality checks</p>\n<p><strong>What to use</strong>: Selective data transfer, SAP Landscape Transformation (SLT), SAP Data Services, SAP BW/4HANA ETL or SAP HANA EIM<br/><em></em></p>\n<p>The following sections are focusing on the system conversion.</p>\n<p> </p>\n<p><strong>2.2 Availability</strong></p>\n<p><strong><em>2.2.1 Overview</em></strong></p>\n<p>The tools required to perform an In-Place, Remote or Shell conversion are general available now.</p>\n<p><strong><em>2.2.2 Releases</em></strong></p>\n<p>SAP highly recommends to use the latest support package for a release to start a conversion project with. It will massively reduce the effort for notes implementation and we cannot guarantee that all required notes can be imported from other areas.<em> </em>Nevertheless, the Transfer Cockpit was made available via SAP Notes for the following support packages.</p>\n<p><em>******* Pre-Checks (General Available)</em></p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"3\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td><strong>Release</strong></td>\n<td><strong>Recommended Support Package</strong></td>\n</tr>\n<tr>\n<td>SAP BW 7.00</td>\n<td>SP 25 and higher</td>\n</tr>\n<tr>\n<td>SAP BW 7.01</td>\n<td>SP 09 and higher</td>\n</tr>\n<tr>\n<td>SAP BW 7.02</td>\n<td>SP 07 and higher</td>\n</tr>\n<tr>\n<td>SAP BW 7.30</td>\n<td>SP 08 and higher</td>\n</tr>\n<tr>\n<td>SAP BW 7.31</td>\n<td>SP 05 and higher</td>\n</tr>\n<tr>\n<td>SAP BW 7.40</td>\n<td>SP 09 and higher</td>\n</tr>\n<tr>\n<td>SAP BW 7.50</td>\n<td>SP 05 and higher</td>\n</tr>\n<tr>\n<td>SAP BW 7.51</td>\n<td>SP 08 and higher</td>\n</tr>\n<tr>\n<td>SAP BW 7.58</td>\n<td>SP 00 and higher</td>\n</tr>\n</tbody>\n</table></div>\n<p><em>******* In-Place conversion</em></p>\n<p>SAP BW 7.50<strong> minimal </strong>requirement SP 05 ,<strong> recommended </strong>SP 12 and higher.</p>\n<p>An in-place conversion of SAP BW <strong>7.51</strong> or higher to SAP BW/4HANA 1.0 is <strong>not</strong> possible (since SAP BW/4HANA 1.0 is based on SAP Basis <strong>7.50</strong>).</p>\n<p>Starting from Release SAP BW <strong>7.50 SP16</strong> the conversion is only to SAP BW/4HANA <strong>2.0 SP&gt;=04</strong> possible!</p>\n<p>SAP highly recommends to update the systems before starting the conversion process. Support Package Implementation is possible until B4H Mode.</p>\n<p>Other components relevant for SAP BW Systems used for InPlace Conversion</p>\n<ul>\n<li>SAP HANA 1.0 SPS 12 and higher (Relevant for Conversion to Target Release SAP BW/4HANA <strong>1.0</strong>)</li>\n<li>SAP HANA 2.0 SPS 36 and higher (Relevant for Conversion to Target Release SAP BW/4HANA <strong>2.0</strong>)</li>\n<li>SAP Business Content 7.57 SP11 and higher</li>\n<li>SPAM version 72 and higher</li>\n<li>SAP UI 7.52 or lower (Relevant for Conversion to Target Release SAP BW/4HANA <strong>1.0</strong> . SAP BW/4HANA 1.0 does <strong>not</strong> support SAP_UI 7.53. See SAP Note <a href=\"/notes/2347382\" target=\"_blank\">2347382</a>)</li>\n<li>SAP UI 7.53 (Possible only by Conversion to Target Release SAP BW/4HANA <strong>2.0</strong>. See SAP Note <a href=\"/notes/2347382\" target=\"_blank\">2347382</a>)</li>\n</ul>\n<p> </p>\n<p><em>*******. Remote conversion </em></p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"3\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td><strong>Release</strong></td>\n<td><strong>Recommended Support Package</strong></td>\n<td><strong>Exceptional usage with increased manual implementation effort</strong></td>\n</tr>\n<tr>\n<td>SAP BW 7.30</td>\n<td colspan=\"2\">SP 10 and higher</td>\n</tr>\n<tr>\n<td>SAP BW 7.31</td>\n<td colspan=\"2\">SP 10 and higher</td>\n</tr>\n<tr>\n<td>SAP BW 7.40</td>\n<td>SP 12 and higher</td>\n<td>SP 09</td>\n</tr>\n<tr>\n<td>SAP BW 7.50</td>\n<td colspan=\"2\">SP 05 and higher</td>\n</tr>\n<tr>\n<td>SAP BW 7.51</td>\n<td colspan=\"2\">SP 08 and higher</td>\n</tr>\n<tr>\n<td>SAP BW 7.58</td>\n<td colspan=\"2\">SP 00 and higher</td>\n</tr>\n</tbody>\n</table></div>\n<p><em><em>******* Shell conversion</em></em></p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"3\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td><strong>Release </strong></td>\n<td><strong>Recommended Support Package</strong></td>\n<td><strong> <strong>Exceptional usage with increased manual implementation effort</strong></strong></td>\n</tr>\n<tr>\n<td>SAP BW 7.00</td>\n<td>SP 28 and higher</td>\n<td>SP 25</td>\n</tr>\n<tr>\n<td>SAP BW 7.01</td>\n<td>SP 12 and higher</td>\n<td>SP 09</td>\n</tr>\n<tr>\n<td>SAP BW 7.02</td>\n<td>SP 10 and higher</td>\n<td>SP 07</td>\n</tr>\n<tr>\n<td>SAP BW 7.30</td>\n<td colspan=\"2\">SP 10 and higher</td>\n</tr>\n<tr>\n<td>SAP BW 7.31</td>\n<td colspan=\"2\">SP 10 and higher</td>\n</tr>\n<tr>\n<td>SAP BW 7.40</td>\n<td>SP 12 and higher</td>\n<td>SP 09</td>\n</tr>\n<tr>\n<td>SAP BW 7.50</td>\n<td colspan=\"2\">SP 05 and higher</td>\n</tr>\n<tr>\n<td>SAP BW 7.51</td>\n<td colspan=\"2\">SP 08 and higher</td>\n</tr>\n<tr>\n<td>SAP BW 7.58</td>\n<td colspan=\"2\">SP 00 and higher</td>\n</tr>\n</tbody>\n</table></div>\n<p><em><em>******* SAP BW/4HANA required release and support package stack</em></em></p>\n<p><em><em></em></em>SAP BW/4HANA General Information see SAP Note <a href=\"/notes/2347382\" target=\"_blank\">2347382</a></p>\n<p>SAP BW/4HANA <strong>1.0</strong></p>\n<p>For In-Place / Shell:<strong> minimal</strong> requirement SP 08, <strong>recommended</strong> - latest released SP!</p>\n<p>For Remote: <strong>minimal</strong> requirement SP 09, <strong>recommended</strong> - latest released SP!</p>\n<p>SAP BW/4HANA <strong>2.0</strong></p>\n<p>For In-Place / Shell / Remote: <strong>minimal</strong> requirement SP 02 (released)</p>\n<p>For more information regading SAP BW/4HANA 2.0 Release Restictions see SAP Note <a href=\"/notes/2733740\" target=\"_blank\">2733740</a>.</p>\n<p>SAP BW/4HANA <strong>2021</strong></p>\n<p>For In-Place / Shell / Remote: <strong>minimal</strong> requirement SP 00 (released)</p>\n<p>For more information regading SAP BW/4HANA 2021 Release Restictions see SAP Note <a href=\"/notes/3100794\" target=\"_blank\">3100794</a>.<a href=\"/notes/2733740\" target=\"_blank\"><br/></a></p>\n<p>SAP BW/4HANA <strong>2023</strong></p>\n<p>For In-Place / Shell / Remote: <strong>minimal</strong> requirement SP 00 (released)</p>\n<p>For more information regading SAP BW/4HANA 2023 Release Restictions see SAP Note <a href=\"/notes/3365187\" target=\"_blank\">3365187</a>.</p>\n<p>For Conversion from SAP_BW 7.50 for HANA to SAP BW/4HANA 2023 (SAP_BASIS 7.58) please check the downloaded Upgrade Export according to SAP Note <a href=\"https://me.sap.com/notes/3454844\" target=\"_blank\">3454844</a>.</p>\n<p><span><em>2.2.2.6 Add-On's</em></span></p>\n<p><span>Please see SAP Note <a href=\"/notes/2189708\" target=\"_blank\">2189708</a> regarding usage of Add-Ons in </span><span>SAP BW/4HANA.</span></p>\n<p> </p>\n<p><em></em><em><strong>2.2.3 Installation</strong></em></p>\n<p><em>2.2.3.1 SAP BW/4HANA Starter Add-On</em></p>\n<p>For the In-Place Conversion, the installation of the SAP BW/4HANA Starter Add-on is required. Please follow the instruction of the Conversion Guide.</p>\n<p>For Shell or Remote Conversion, the SAP BW/4HANA Starter Add-On is not required.</p>\n<p><em>2.2.3.2 SAP BW/4HANA Transfer Cockpit</em></p>\n<p>To install the Transfer Cockpit for a transfer of scenarios (in-place, remote, and shell conversion), a set of SAP Notes needs to be applied to the corresponding systems. Please use the SAP BW Note Analyzer to analyze the system and to install the prerequisites. Create the SAP BW Note Analyzer program in each system for which notes have to be applied. Depending on your use case, the following systems needs to be processed:</p>\n<ul>\n<li><strong>SAP BW</strong> (in-place conversion or sending system for a remote/shell conversion)</li>\n<li><strong>Source systems</strong> connected to SAP BW (in-place and remote/shell conversion)</li>\n<li><strong>SAP BW/4HANA</strong> (post conversoin for in-place; receiving system for a remote/shell conversion)</li>\n</ul>\n<p>Run the SAP BW Note Analyzer in each system with the corresponding XML file.</p>\n<p><em>2.2.3.3 SAP BW Note Analyzer</em></p>\n<p>For a detailed documentation of the SAP BW Note Analyzer, please see <a href=\"https://help.sap.com/doc/b235b5bd94664d9986f721f049d72cbb/1.0/en-US/User_Guide_for_SAP_BW_Note_Analyzer.pdf\" target=\"_blank\">https://help.sap.com/doc/b235b5bd94664d9986f721f049d72cbb/1.0/en-US/User_Guide_for_SAP_BW_Note_Analyzer.pdf</a></p>\n<p>Attached to that note you’ll find a text file “Z_SAP_BW_NOTE_ANALYZER.txt”. Save the file on your computer and open it in an editor.</p>\n<p>In the corresponding system, use transaction SE38 to create a report called “Z_SAP_BW_NOTE_ANALYZER”. Copy and paste the content of the above-mentioned text file into the report and activate it.</p>\n<p>Attached to that note, you’ll find several XML files. Save those files that are related to your use-case:</p>\n<p>Please find more detailed informations under:</p>\n<p><a href=\"https://wiki.scn.sap.com/wiki/pages/viewpage.action?pageId=572523874\" target=\"_blank\">https://wiki.scn.sap.com/wiki/pages/viewpage.action?pageId=572523874</a>.</p>\n<p>You want to run the pre-checks in the discovery phase (RS_B4HANA_CONVERSION_CONTROL in SAP BW 7.0x and RS_B4HANA_RC from SAP BW 7.30 to 7.50):</p>\n<ul>\n<li>For SAP BW 7.0x, use <em>SAP_BW4HANA_<strong>Pre_Checks</strong>_[last_update].xml</em></li>\n<li>For SAP BW from 7.30 to 7.50, use XML<em> SAP_BW4HANA_<strong>Readiness_Check</strong>_<em>[last_update]</em>.xml</em> from note 2575059. </li>\n</ul>\n<p>You want to transfer scenarios and perform a system conversion in the realization phase:</p>\n<ul>\n<li>For SAP BW 7.50 (all cases),</li>\n<ul>\n<li>if you start now with the installation of the tool, use <em>SAP_BW4HANA_Conversion_<strong>SAP_BW_750</strong>_[last_update].xml </em></li>\n<li>if you have already installed most of the notes before and only want the latest updates, continue to use <em>SAP_BW4HANA_<strong>In-place_Conversion_(Original_System)</strong>_[last_update].xml</em></li>\n</ul>\n<li>For all systems connected to SAP BW as a data load source system, use <em><strong>Source_System</strong>_for_SAP_BW4HANA_[last_update].xml</em></li>\n<li>Remote Transfer</li>\n<ul>\n<li>For SAP BW from 7.30 to 7.40 acting as sending system for a remote conversion, use <em>SAP_BW4HANA_<strong>Remote_Conversion_(Original_System)</strong>_[last_update].xml</em></li>\n<li>For SAP BW/4HANA acting as receiving system for a remote conversion, use <em>SAP_BW4HANA_<strong>Remote_Conversion_(Target_System)</strong>_[last_update].xml</em></li>\n</ul>\n<li>Shell Transfer</li>\n<ul>\n<li>For SAP BW from 7.00 to 7.40 acting as sending system for a shell conversion, use <em>SAP_BW4HANA_<strong>Shell_Conversion_(Original_System)</strong>_[last_update].xml</em></li>\n<li>For SAP BW/4HANA acting as receiving system for a shell conversion, use <em>SAP_BW4HANA_<strong>Shell_Conversion_(Target_System)</strong>_[last_update].xml</em></li>\n</ul>\n</ul>\n<p>You have successfully converted the system (In-Place) to SAP BW/4HANA (see section 2.3.2.3 Post-Conversion phase)</p>\n<ul>\n<li>Use <em>SAP_BW4HANA_<strong>In-place_Conversion_(After_System_Conversion)</strong>_[last_update].xml</em></li>\n</ul>\n<p>Execute the SAP BW Note Analyzer in each relevant system. Click on the icon “Load XML file” and chose the system specific XML file.</p>\n<p>Select radio button “Check implementation state against information in XML file” and check checkbox “Download needed SAP Notes”. Select radio button “In background”.</p>\n<p>Run the report. A background process will be started which downloads the required notes.</p>\n<p>When the process finished successfully, execute the report again with a de-selected checkbox “Download needed SAP Notes”.</p>\n<p>A list of notes with its implementation status will be shown. You can now install the missing notes by clicking on the corresponding icon in the list.</p>\n<p><em>2.2.3.4 Transport-Enabled Correction Instructions (TCIs)</em></p>\n<p>When installing the SAP BW/4HANA Transfer Cockpit using the SAP BW Note Analyzer, you will be prompted for SAP Note <a href=\"/notes/2358953\" target=\"_blank\">2358953</a>, <a href=\"/notes/2371120\" target=\"_blank\">2371120</a>, and <a href=\"/notes/2735300\" target=\"_blank\">2735300</a> to install a TCI.</p>\n<p>For more information about TCI, see SAP Note <a href=\"/notes/2358953\" target=\"_blank\">2358953</a>.<a href=\"/notes/2358953\" target=\"_blank\"><br/></a></p>\n<p><em>******* DMIS Add-On</em></p>\n<p>For a remote conversion, the DMIS Add-On needs to be installed in SAP BW (sending system) and SAP BW/4HANA (receiving system). Follow the instructions in SAP Note <a href=\"/notes/2513088\" target=\"_blank\">2513088</a>.</p>\n<p> </p>\n<p><strong>2.3 Conversion Process</strong></p>\n<p>For a detailed description of the conversion process, see the <a href=\"https://help.sap.com/viewer/p/SAP_BW4HANA\" target=\"_blank\">Conversion Guide</a>. In additional we attached a excel file, \"<em><strong>SAP BW4HANA Conversion - Steps to consider.xlsx</strong></em>\", as a kind of check list/project plan.</p>\n<p>List of required authorization objects for conversion, see attachment \"<em><strong>Authorization_Objects_List_Conversion_2383530.xls</strong></em>\".</p>\n<p>Frequencly ask questions are collected in note <a href=\"/notes/2930058\" target=\"_blank\">2930058</a>.</p>\n<p>Each object can only be converted once. However, it can be collected again if it is used in another scenario. For example, an IOBJ that has been converted can be used in different objects such as ADSO, DSO, CUBE. Due to consistency, these IOBJs are also collected several times, but the metadata can only be transferred once.</p>\n<p><strong>2.4 New Features</strong></p>\n<p>Under link, you can find new developed features.</p>\n<p><a href=\"https://wiki.scn.sap.com/wiki/pages/viewpage.action?pageId=550043910\" target=\"_blank\">https://wiki.scn.sap.com/wiki/pages/viewpage.action?pageId=550043910</a>.</p>\n<p>The wiki is updated regularly.</p>", "noteVersion": 420}]}]}