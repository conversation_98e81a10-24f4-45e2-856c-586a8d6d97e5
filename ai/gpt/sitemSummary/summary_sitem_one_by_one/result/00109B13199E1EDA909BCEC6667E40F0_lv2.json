{"guid": "00109B13199E1EDA909BCEC6667E40F0", "sitemId": "SI5: SD_RRn", "sitemTitle": "S4TWL - ERP SD Revenue Recognition", "note": 2267342, "noteTitle": "2267342 - S4TWL - ERP SD Revenue Recognition", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are doing a system conversion to SAP S/4HANA. In this scenario, the following SAP S/4HANA Transition Worklist item applies.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong>Description</strong></p>\n<p>ERP SD Revenue Recognition (see SAP Help: <a href=\"http://help.sap.com/saphelp_46c/helpdata/en/46/74a073b4e411d199bc0000e8a5bd28/content.htm\" target=\"_blank\">Link</a>) is not available within SAP S/4HANA. The newly available SAP Revenue Accounting and Reporting functionality should be used instead.</p>\n<p>The new functionality supports the new revenue accounting standard as outlined in IFRS15 and adapted by local GAAPs. The migration to the new solution is required to comply with IFRS15, even if no upgrade to SAP S/4HANA is performed.</p>\n<p><strong>Business Process-Related Information</strong></p>\n<p>Sales and Distribution (SD) Revenue Recognition is not available in SAP S/4HANA. None of the SD Revenue Recognition functionality will work in SAP S/4HANA.</p>\n<p>For sales orders or contracts that were processed by SD Revenue Recognition and are <strong>not</strong> migrated to SAP Revenue Accounting and Reporting, this includes:</p>\n<ul>\n<li>Realize deferred revenue</li>\n<li>Realize revenue before invoice posting</li>\n<li>Defer revenue at invoice posting</li>\n<li>Check SD Revenue Recognition data consistency</li>\n<li>Cancel SD Revenue Recognition postings</li>\n</ul>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"0\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td valign=\"top\" width=\"182\">\n<p>Transaction not available in SAP S/4HANA</p>\n</td>\n<td valign=\"top\" width=\"414\">\n<p>OVACT \"Revenue Recognition: Activations\"<br/>OVEP \"Maintain Item Categories: Revenue Recognition\"<br/>VF42 \"Update Sales Documents\"<br/>VF43  \"Revenue Recognition: Posting Doc\"<br/>VF44 \"Revenue Recognition: Work List\"<br/>VF45 \"Revenue Recognition: Revenue Report\"<br/>VF46 \"Revenue Recognition: Cancellation\"<br/>VF47 \"Revenue Recognition: Consistency Check\"<br/>VF48 \"Revenue Recognition: Compare Report\"</p>\n</td>\n</tr>\n</tbody>\n</table></div>\n<p><strong>Required and Recommended Action(s)</strong></p>\n<p><strong>Prior to the Conversion to SAP S/4HANA,</strong> you need to migrate all sales order and contracts processed by SD Revenue Recognition to SAP Revenue Accounting and Reporting that are</p>\n<ul>\n<li>not fully delivered and invoiced</li>\n<li>have deferred revenue still to be realized</li>\n<li>and for which you expect follow-on activities like increase quantity, create credit memo or cancel invoice</li>\n</ul>\n<p>Please refer to note <a href=\"/notes/2591055\" target=\"_blank\">2591055</a> that contains information about known restrictions of SAP Sales Integration with SAP Revenue Accounting and Reporting (SAP SALES INTEGR SAP RAR 1.0) which is also known as the SD Integration Component.</p>\n<p><strong>If you are using SD Revenue Recognition, you need to evaluate whether a migration to SAP Revenue Accounting and Reporting is possible for your business before you decide to convert to SAP S/4HANA.</strong></p>\n<p>You can find more details about the migration to SAP Revenue Accounting and Reporting in the following notes:</p>\n<div><span><a href=\"/notes/2582784\" target=\"_blank\">2582784</a><br/>Revenue Accounting and Reporting with SAP RAR 1.3 and S/4HANA 1809 &amp; 1909 OP - FAQ's and Guidance</span></div>\n<div><span><a href=\"/notes/2733866\" target=\"_blank\">2733866</a>              <br/>Migration Guide for SD Revenue Recognition to Revenue Accounting and Reporting</span></div>\n<div></div>\n<p><strong>Related SAP Notes</strong></p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"0\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td valign=\"top\" width=\"302\">\n<p>Conversion pre-checks</p>\n</td>\n<td valign=\"top\" width=\"302\">\n<p>SAP Note: 2227824</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"302\">\n<p>Custom code-related information</p>\n</td>\n<td valign=\"top\" width=\"302\">\n<p>SAP Note: 2225170</p>\n</td>\n</tr>\n</tbody>\n</table></div>", "noteVersion": 6, "refer_note": [{"note": "2733866", "noteTitle": "2733866 - Migration Guide for SD Revenue Recognition to Revenue Accounting and Reporting", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>Detailed information and guidance for the migration from SD Revenue Recognition to Revenue Accounting and Reporting is missing.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>SD Revenue Recognition, Revenue Accounting and Reporting, Migration, FARRIC_OL, FARRIC_OL_EXPERT, FARRIC_IL_CLEANUP, FARRIC_RR_CORR, FARRIC_SD-REV_CORRECT, FARRIC_MIGRATED, FARR_MIGRATION_ED_CALC, ZFARR_CALC_REV_AMT, Operaional Load, Initial Load.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>Customers that use SD Revenue Recognition and plan to convert to S/4HANA, need to migrate to Revenue Accounting and Reporting <strong>before</strong> they convert to S/4HANA.</p>\n<p>The attached migration guide provides detailed information about this migration process. Technical restrictions, prerequisite activities, the supported POB fulfillment and event types that must be configured for the individual SD revenue recogntion processes are just a few of the topics covered by the migration guide.</p>\n<p>Additionally, the guide outlines the relevant migration tools and special scenarios that need to be considered for the migration.</p>\n<p class=\"pf0\">Please note that this migration guide is only valid if you want to migrate to 'Classic Contract Management' in Revenue Accounting. As of S/4HANA 2020 FPS1, S/4HANA 2021, S/4 HANA 2022 and higher releases with the migration capability of Optimized Contract Management (OCM) and Optimized Inbound Processing (OIP), you can migrate SD orders and SD contracts to Revenue Accounting, which have been previously processed by SD Revenue Recognition. The migration process from SD Revenue Recognition can be therefore performed <strong>after</strong> ECC has been updated to S/4HANA, as throughout the migration, RAR uses the subsequent follow-up documents related sales orders and contracts. They are still available after the upgrade to S/4HANA, therefore RAR OCM and OIP doesn’t rely on the SD Revenue Recognition programs and legacy data. For more information, see point 16 in the corresponding Release Information Note (e.g. SAP Note 3214045).</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>To avoid issues during and after migration from SD Revenue Recognition to Revenue Accounting and Reporting, please carefully read the attached guide. Ensure that you follow all of the prerequisite activities and set up configuration as described within the guide, so that your processes remain fully supported.</p>\n<p>Extended version of the guide released on 16.4.2019. This version includes additionaly information, for example cost handling during SD RR/RAR migration, Vf47 consistency checks during operational load, foreign currency handling during and after migration etc.</p>", "noteVersion": 6}, {"note": "2227824", "noteTitle": "2227824 - S4/HANA Pre-check for SD-Revenue Recognition", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>SAP S4/HANA Pre-check for SD-Revenue Recognition</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>S4TC</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>SAP S4/HANA Pre-check for SD-Revenue Recognition</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>SAP S4/HANA Pre-check for SD-Revenue Recognition</p>", "noteVersion": 5}, {"note": "2225170", "noteTitle": "2225170 - S/4 HANA SD-Revenue Recognition", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are using the SD-Revenue Recognition functionality (Application component <strong>SD-BIL-RR</strong>) and plan to upgrade to SAP S/4HANA.VF42</p>\n<p>The SD-Revenue Recognition functionality is replaced by the new application SAP Revenue Accounting and Reporting.</p>\n<p>Customer-specific code will therefore not work anymore.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>SD-BIL-RA, OVACT, OVEP, VF42, VF43, VF44, VF45, VF46, VF47, VF48</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>Upgrade to  SAP S/4HANA</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>SAP ERP SD Revenue Recognition is not available within SAP S/4HANA. Since the complete SD REVREC functionality is descoped in S4CORE, the processing of these documents with transactions VF44, VF45, VF46, VF47 and VF48 is no longer possible there. You should use the newly available SAP Revenue Accounting and Reporting functions instead.</p>\n<p>The new functions support the new revenue accounting standard as outlined in IFRS15 and adapted by local GAAPs. The migration to the new solution is required to comply with IFRS15 even if you do not carry out a conversion to SAP S/4HANA.</p>\n<p>If you migrate to Classical Contract Management (CCM), you need prior to the conversion to SAP S/4HANA to migrate all sales order and contracts processed by SD Revenue Recognition to SAP Revenue Accounting and Reporting that are:</p>\n<ul>\n<li>Not fully delivered and invoiced</li>\n<li>Have deferred revenue still to be realized</li>\n<li>For which you expect follow-up activities such as increase quantity, create credit memo, or cancel invoice</li>\n</ul>\n<p>Please refer to the migration chapter in the online help of “SAP Revenue Accounting and reporting” for details. For a migration to Optimized Contract managment (OCM) this is not required.</p>\n<p>All usages of SAP-Objects in customer objects for which the Custom Code Check refers to this SAP Note <a href=\"/notes/2225170\" target=\"_blank\">2225170</a> will not work anymore and need to be removed.</p>\n<p><strong>Please Note:</strong></p>\n<p>Please refer to note <a href=\"/notes/2591055\" target=\"_blank\">2591055</a> that contains information about known restrictions of SAP Sales Integration with SAP Revenue Accounting and Reporting (SAP SALES INTEGR SAP RAR 1.0) which is also known as the SD Integration Component.</p>\n<p>If you are using SD Revenue Recognition, you need to evaluate if a migration to SAP Revenue Accounting and Reporting is possible for your business before you decide to convert to SAP S/4HANA.</p>\n<p>You can find more details about the migration to SAP Revenue Accounting and Reporting in the following notes:</p>\n<div><span><a href=\"/notes/2582784\" target=\"_blank\">2582784</a><br/></span><span>Revenue Accounting and Reporting with SAP RAR 1.3 and S/4HANA 1809 &amp; 1909 OP - FAQ's and Guidance</span></div>\n<p><a href=\"/notes/2733866\" target=\"_blank\">2733866</a> <br/>Migration Guide for SD Revenue Recognition to Revenue Accounting and Reporting</p>", "noteVersion": 10}, {"note": "2582784", "noteTitle": "2582784 - Revenue Accounting and Reporting with SAP RAR 1.3 and S/4HANA 1809 & 1909 OP - FAQ's and Guidance", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>Handling of SAP Revenue Accounting implementation issues and FAQ's. The note lists FAQs and provides guidance for the add-on SAP Revenue Accounting and Reporting. Where not specifically listed the FAQs also apply for S/4HANA 1809 &amp; 1909 OP, where Revenue Accounting and Reporting has become an integral part of S/4HANA.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Consulting, FAQ, implementation support, Best Practises, SAP RAR, Migration</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>SAP Revenue Accounting and Reporting 1.3</p>\n<p>Revenue Accounting and Reporting as of S/4HANA 1809 &amp; 1909 OP</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>The note 2582784 provides additional guidance when implementing SAP Revenue Accounting and Reporting (SAP RAR) and covers the following areas:</p>\n<p>-        <a href=\"#Release_Information\" target=\"_self\">Release Information</a></p>\n<p>-        <a href=\"#Functional_Considerations\" target=\"_self\">Functional Considerations</a></p>\n<p>-       <a href=\"#Operations\" target=\"_self\"> Operations</a></p>\n<p>-        <a href=\"#Sizing_and_Performance\" target=\"_self\">Sizing and Performance</a></p>\n<p><a href=\"#HowTo\" target=\"_self\">-        Further how-to information</a></p>\n<p>The note will be frequently updated. Date of update will be added to new information. Please note that you may require dedicated users e.g. for application help or the SAP service marketplace.</p>\n<p> </p>\n<p><span><strong><a name=\"Release_Information\" target=\"_blank\"></a>﻿1.     </strong><strong>Release Information:</strong></span></p>\n<p><strong>1.1 Where can I find additional important information about the support packages for SAP RAR 1.3?</strong></p>\n<p>You can find additional information in note <a href=\"/notes/2382402\" target=\"_blank\">2382402 - SAP Revenue Accounting 1.3: Release Information Note</a>.</p>\n<p><strong>1.2 Where can I find information on supported software stack for SAP RAR 1.3?    </strong></p>\n<p>SAP RAR is an ABAP add-on to SAP ERP or SAP S/4HANA On-Premise. You can find additional information of supported product versions of SAP ERP or S/4HANA as well as required NetWeaver stacks in note <a href=\"/notes/2386978\" target=\"_blank\">2386978 - Release strategy for the ABAP add-on REVREC 130</a>.</p>\n<p><strong>1.3 I want to use the SD integration component to SAP RAR. Where can I find information about planning and upgrades of the SD integration component?</strong></p>\n<p>If you want to use the SD integration to SAP RAR you need to apply the SAP SALES INTEGR SAP RAR 1.0 add-on (REVRECSD 100). Note <a href=\"/notes/2254785\" target=\"_blank\">2254785 - Release strategy for the ABAP add-on REVRECSD 100</a> provides more information about planning the installation and upgrades of the ABAP add-on.</p>\n<p><strong>1.4 What corrections and features have been provided with SAP RAR 1.3 Feature Packs?</strong></p>\n<p>You can find the notes content of SAP RAR 1.3 Feature Packages here:</p>\n<p><a href=\"https://launchpad.support.sap.com/#/supportpackage/SAPK-13004INREVREC\" target=\"_blank\">https://launchpad.support.sap.com/#/supportpackage/SAPK-13004INREVREC</a></p>\n<p><a href=\"https://launchpad.support.sap.com/#/supportpackage/SAPK-13005INREVREC\" target=\"_blank\">https://launchpad.support.sap.com/#/supportpackage/SAPK-13005INREVREC</a></p>\n<p><a href=\"https://launchpad.support.sap.com/#/supportpackage/SAPK-13006INREVREC\" target=\"_blank\">https://launchpad.support.sap.com/#/supportpackage/SAPK-13006INREVREC</a></p>\n<p><a href=\"https://launchpad.support.sap.com/#/supportpackage/SAPK-13007INREVREC\" target=\"_blank\">https://launchpad.support.sap.com/#/supportpackage/SAPK-13007INREVREC</a></p>\n<p lang=\"de\"><a href=\"https://launchpad.support.sap.com/#/supportpackage/SAPK-13008INREVREC\" target=\"_blank\">https://launchpad.support.sap.com/#/supportpackage/SAPK-13008INREVREC</a></p>\n<p lang=\"de\"><a href=\"https://launchpad.support.sap.com/#/supportpackage/SAPK-13009INREVREC\" target=\"_blank\">https://launchpad.support.sap.com/#/supportpackage/SAPK-13009INREVREC</a></p>\n<p lang=\"de\"><a href=\"https://launchpad.support.sap.com/#/supportpackage/SAPK-13010INREVREC\" target=\"_blank\">https://launchpad.support.sap.com/#/supportpackage/SAPK-13010INREVREC</a></p>\n<p lang=\"de\"><a href=\"https://launchpad.support.sap.com/#/supportpackage/SAPK-13011INREVREC\" target=\"_blank\">https://launchpad.support.sap.com/#/supportpackage/SAPK-13011INREVREC</a></p>\n<p><a href=\"https://launchpad.support.sap.com/#/supportpackage/SAPK-13012INREVREC\" target=\"_blank\">https://launchpad.support.sap.com/#/supportpackage/SAPK-13012INREVREC</a></p>\n<p><a href=\"https://launchpad.support.sap.com/#/supportpackage/SAPK-13013INREVREC\" target=\"_blank\">https://launchpad.support.sap.com/#/supportpackage/SAPK-13013INREVREC</a></p>\n<p><a href=\"https://launchpad.support.sap.com/#/supportpackage/SAPK-13014INREVREC\" target=\"_blank\">https://launchpad.support.sap.com/#/supportpackage/SAPK-13014INREVREC</a></p>\n<p><a href=\"https://launchpad.support.sap.com/#/supportpackage/SAPK-13015INREVREC\" target=\"_blank\">https://launchpad.support.sap.com/#/supportpackage/SAPK-13015INREVREC</a></p>\n<p><a href=\"https://launchpad.support.sap.com/#/supportpackage/SAPK-13016INREVREC\" target=\"_blank\">https://launchpad.support.sap.com/#/supportpackage/SAPK-13016INREVREC</a></p>\n<p><a href=\"https://launchpad.support.sap.com/#/supportpackage/SAPK-13017INREVREC\" target=\"_blank\">https://launchpad.support.sap.com/#/supportpackage/SAPK-13017INREVREC</a></p>\n<p><a href=\"https://launchpad.support.sap.com/#/supportpackage/SAPK-13018INREVREC\" target=\"_blank\">https://launchpad.support.sap.com/#/supportpackage/SAPK-13018INREVREC</a></p>\n<p><strong>1.5 Why is support package 4, 5, 6 and 7 of SAP RAR 1.3 delivered as feature package and </strong><strong>what is the difference to support packages?</strong></p>\n<p>Support packages 4 to 7 of SAP Revenue Accounting and Reporting 1.3 are defined as Feature Packs to highlight the additional features being provided. Technically speaking, a <strong>feature pack stack is like a support package stack</strong>, but it may include nondisruptive, non-mandatory features. You will find the detailed definition of a Feature Pack Stack in the release strategy glossary: <a href=\"https://support.sap.com/release-upgrade-maintenance/release-strategy/glossary.html#f\" target=\"_blank\">https://support.sap.com/release-upgrade-maintenance/release-strategy/glossary.html#f</a>.</p>\n<p><strong>1.6 What are the additional features of RAR 1.3 FP04 and where can I find additional information?</strong></p>\n<p>Feature pack 4 provides the following <strong>innovations</strong>:</p>\n<ul>\n<li>Define rules for deriving values of fields for revenue contracts and performance obligations (POBs)</li>\n<li>Define customized logic to calculate contract liabilities and contract assets</li>\n</ul>\n<p>The feature <em>Define customized logic to calculate contract liabilities and contract assets</em> provides enhanced configuration as well as extensibility options.</p>\n<p>After the implementation of note 2560937 or application of SAP RAR 1.3 Feature Pack 04, in Customizing for <em>Configure Accounting Principle-specific Settings</em> you can define the <em>Default method to</em> <em>calculate contract liability and contract asset</em>. Please notice that the method <strong>Calculate Contract Asset and Liability Directly by Contract is defaulted</strong>. You can change this default method. Please DO carefully review the two algorithms mentioned in the note 2560937, and decide which one fits best to your requirements.</p>\n<p>If you want to keep the old standard logic available before the application of note 2560937 or application of SP4, please change the customizing to method <strong>Calculate Contract Asset and Liability per POB and Net by Contract</strong></p>\n<ul>\n<ul>\n<li>If you want to use the new standard logic, you don't need to change anything.</li>\n<li>If you want to implement your own logic, please implement the BADI with your logic.</li>\n<li>Calculate the remaining fulfillment percentage for a time-based performance obligation</li>\n</ul>\n</ul>\n<p> </p>\n<ul>\n<li>Clean up and reverse productive data coming from external sender components</li>\n<li>Perform an extended check before saving contracts to database</li>\n<li>Enable change of transactional currency in the sales order</li>\n<li>Deliver enhancements to POB cancellations for linked POBs</li>\n</ul>\n<p>In general, you can find additional information on innovations in the innovation discovery. For FP4 this is <a href=\"https://go.support.sap.com/innovationdiscovery/#/innovation/6EAE8B28C5D91ED7B886D83F3FF760C7\" target=\"_blank\">here</a>.</p>\n<p><strong>1.7 Why do I have to contact SAP before using SAP RAR 1.3?</strong></p>\n<p lang=\"en-GB\">The use of Revenue Accounting and Reporting requires a very conscientious and responsible approach to the subject. Thus SAP decided to release Revenue Accounting and Reporting individually to customers so that SAP can ensure customers intending to use this function understand and agree to the conditions and prerequisites that must be met. This process applies to all customers who have not used Revenue Accounting and Reporting in earlier releases.</p>\n<p lang=\"en-GB\">Despite this special release process, the responsibility for setting up, using and operating Revenue Accounting remains with the customer.</p>\n<p lang=\"en-GB\">You can find details on this activation process in the following note:</p>\n<p><a href=\"/notes/2750710\" target=\"_blank\">2750710</a> - Activation of Revenue Accounting and Reporting Add-On</p>\n<p><strong>1.8 Is SD Revenue Recognition (SD-BIL-RR) supported with SAP S/4HANA?</strong></p>\n<p>The SD-Revenue Recognition functionality is replaced by the new application SAP Revenue Accounting and Reporting - See note <a href=\"/notes/2225170\" target=\"_blank\">2225170 - S/4 HANA SD-Revenue Recognition</a>.</p>\n<p><strong>1.9 What is the recommended support package for the SD integration component (REVRECSD) when using SAP RAR 1.3?</strong></p>\n<p>In general, SAP recommends to always apply the latest support package of the SD integration component. As a minimum requirement however, <em>SAP Sales Integration with SAP Revenue Accounting and Reporting 1.0</em>  (SAP SALES INTEGR SAP RAR 1.0) must be installed with at least support package level 10 in order to integrate with Revenue Accounting and Reporting 1.3. For further details please refer to the latest administrator's guides for SAP RAR (Chapter Technical System Landscape) and for the SD Integration.</p>\n<p><strong>1.10 Can I download a release earlier than RAR 1.3?</strong></p>\n<p>Only RAR 1.3 can be downloaded. While RAR 1.3 is still in Early Adopter Care, this is the main release for Revenue Accounting and Reporting. Please also refer to 1.7.</p>\n<p><span><strong><a name=\"Functional_Considerations\" target=\"_blank\"></a>﻿2.     </strong><strong>Functional Considerations</strong></span></p>\n<p><strong>2.1 Where can I find information about supported processes with SAP RAR and the SD integration component?</strong></p>\n<p>You can find additional information in the application help for <a href=\"https://help.sap.com/viewer/17fec0d77e324d78b2a37af97bdef898/1.3.11/en-US\" target=\"_blank\">SAP RAR 1.3 SP11</a>.</p>\n<p>In addition, the attached whitepapers highlight supported processes and system settings. Please note that the whitepapers had been created under SAP RAR 1.2 and do not yet take into account the updates from SAP RAR 1.3.</p>\n<p>For the SD integration please also consider the following information:</p>\n<p>SD-BIL-RR            <a href=\"/notes/2733866\" target=\"_blank\">2733866</a>              Migration Guide for SD Revenue Recognition to Revenue Accounting and Reporting</p>\n<p>SD-BIL-RR            <a href=\"/notes/2225170\" target=\"_blank\">2225170 </a>             S/4 HANA SD-Revenue Recognition</p>\n<p>SD-BIL-RR            <a href=\"/notes/2341717\" target=\"_blank\">2341717</a>              FAQ: Future of SD Revenue Recognition after IFRS15 is released</p>\n<p>SD-BIL-RA            <a href=\"/notes/2610856\" target=\"_blank\">2610856</a>              FAQ: Condition handling in SD Integration Component</p>\n<p>SD-BIL-RA            <a href=\"/notes/2719185\" target=\"_blank\">2719185</a>              The redesigned SD Revenue Recognition type \"D\" migration is now available!</p>\n<p>SD-BIL-RA            <a href=\"/notes/2569950\" target=\"_blank\">2569950</a>              FAQ: Migration &amp; Operational Load in the SD Integration Component</p>\n<p>SD-BIL-RA            <a href=\"/notes/2703761\" target=\"_blank\">2703761</a>              SD Integration Component: Important core changes not included in Add-On REVRECSD</p>\n<p>SD-BIL-RA            <a href=\"/notes/2624932\" target=\"_blank\">2624932</a>              Consulting: Handling of bill of material scenarios in the SD integration component</p>\n<p>SD-BIL-RA            <a href=\"/notes/2591055\" target=\"_blank\">2591055</a>              Functional limitations in the SD Integration Component</p>\n<p>SD-BIL-RA            <a href=\"/notes/2650545\" target=\"_blank\">2650545</a>              Consulting: POB start date and end date determination in SDOI RAIs</p>\n<p>SD-BIL-RA            <a href=\"/notes/2634948\" target=\"_blank\">2634948</a>              Consulting: Manipulation of revenue accounting relevance</p>\n<p><strong>2.2 I want to integrate 3<sup>rd</sup> party billing and contract management systems to Revenue Accounting and Reporting. Where can I find additional information?</strong></p>\n<p><a href=\"/notes/2392956\" target=\"_blank\">Note 2392956 - SAP Revenue Accounting and Reporting 1.3: Integrating External (non-SAP) Sender Components</a> highlights key aspects of integrating external sender components to Revenue Accounting and Reporting.</p>\n<p><strong>2.3 Can I use the feature simplified invoice handling and finalization date?</strong></p>\n<p>As per note <a href=\"/notes/2537761\" target=\"_blank\">2537761</a> simplified invoice handling and the usage of the finalization date are not supported.</p>\n<p><strong>2.4 Why do I get message \"DataSource 0FARR_RA_10 does not exist in version A\" when using the Web Dynpro Sample Reports?</strong></p>\n<p>Please login into your cross client.</p>\n<p>1) Goto transaction RSA5.</p>\n<p>2) Expand the data source under the node of \"0FARR\". You can find the node under unassigned nodes.</p>\n<p>3) Select all and activate them.</p>\n<p>Then the data sources are activated. A warning message might be reported, but you can ignore this.</p>\n<p><strong>2.5 Can I combine 2 revenue contracts which have already posting in the previous closed period?</strong></p>\n<p>Yes, you are able to combine revenue contracts which have already postings in the previous closed period as contract combination functionality has been enhanced in SAP RAR 1.3.</p>\n<p><strong>2.6 Can I change a fiscal year variant for company codes which have revenue contracts created in RAR?  </strong></p>\n<p>You cannot change fiscal year variant for company codes that have revenue contracts created in RAR. RAR calculates revenue for the whole duration of time-based POBs in the period from the start date. RAR also creates reconciliation keys for the current and future periods. Reconciliation keys are generated based on the current assignment of the fiscal year variant. Therefore, if you change the assignment of the fiscal year variant, the period information for the reconciliation keys is incorrect. RAR currently cannot recalculate future revenue nor refresh reconciliation keys for future periods as this would negatively impact the processing of contract modifications, especially for perspective changes that occur in future. If you have this requirement please reach out to development support for further guidance.</p>\n<p><strong><strong>2.7 </strong>Why did I receive the “<em>Certain standalone selling prices are 0 unexpectedly (including &amp;1)” </em>message? </strong></p>\n<p>For a contract with more than one performance obligation, the contractual price needs to be allocated to each performance obligation (POB) on a relative stand-alone selling price (SSP) basis.</p>\n<p>The error message is raised when the total contractual price cannot be allocated to each POB based on the SSP.</p>\n<p> When following conditions are met, the error message is raised:</p>\n<ul>\n<li>All SSPs or remaining SSPs that require allocation to POBs have a value of 0.</li>\n<li>Total contractual price or total remaining contractual price of the relevant POBs is not equal to 0.</li>\n<li>There is no residual POB within the contract.</li>\n</ul>\n<p><strong>2.8 What do I have to consider if I want to add additional cost information in the contract? </strong></p>\n<p>Revenue Accounting manages cost recognition by using objects such as revenue accounting contracts and performance obligations (POBs). You can find more detailed information on the setup in the product assistance, e.g. for S/4HANA 1809 here: <a href=\"https://help.sap.com/viewer/f63dd39a28bb4b90adbf9e608aff58ea/1809.000/en-US/37bf1d57f3f91d62e10000000a44147b.html\" target=\"_blank\">https://help.sap.com/viewer/f63dd39a28bb4b90adbf9e608aff58ea/1809.000/en-US/37bf1d57f3f91d62e10000000a44147b.html</a>.</p>\n<p>Fulfillment cost scenarios are tightly related to the SD Integration Component. For additional information please refer to the respective scenarios under Revenue Accounting and Reporting à Integration of Sender Components à Sales and Distribution Integration with Revenue Accounting à Supported Processes.</p>\n<p>If you have additional requirements beyond the standard SD scenarios you may want to consider using the user exit: FARR_BADI_RAI0, e.g. to add additional RAIs with cost information.</p>\n<p>In this case however you must consider the following conditions:</p>\n<p>-              The cost currency must be the company code currency</p>\n<p>-              The cost currency must be filled in the 1st local currency</p>\n<p>-              The amount of the cost must be filled in the amount of 1st local currency.</p>\n<p>-              All cost currencies must be the same</p>\n<p>The column TC Amount and currency is not required to be filled at all. Please note that if the TC amount and currency is filled, it will be ignored and not used by Revenue Accounting.</p>\n<p><strong>2.9 Why are no Revenue Accounting Items created?</strong></p>\n<p>This is most likely related to missing configuration or authorization.</p>\n<p>SAP Sales and Distribution Integration with SAP Revenue Accounting and Reporting and SAP Revenue Accounting and Reporting communicate with RFC function calls. Please refer to chapter Configuration -&gt; Application Integration and Monitoring   -&gt; Background RFC Communication in the admin guide for the SD Integration Component</p>\n<p><a href=\"https://help.sap.com/docs/SAP_REVENUE_ACCOUNTING_AND_REPORTING?version=1.3%20Latest\" target=\"_blank\">Help files SAP_REVENUE_ACCOUNTING_AND_REPORTING</a>.</p>\n<p>In terms of authorization, please refer to the chapter <em>Security Information</em> in the admin guide for the <em>SD Integration Component</em> as well as the chapter <em>Security Information -&gt; User Administration</em> <em>and Authentication</em> and <em>Security Information -&gt; Authorizations</em> in the admin guide for <em>Revenue Accounting</em> (<a href=\"https://help.sap.com/docs/SAP_REVENUE_ACCOUNTING_AND_REPORTING/c61e906ebdf4434cae8c9cb13b57a141/c8abeb53bf7ca647e10000000a4450e5.html?locale=en-US&amp;version=1.3%20Latest\" target=\"_blank\">Admin Guide SAP Revenue Accounting and Reporting (latest)</a>).</p>\n<p>Without the correct authorization, the RFC call from SD to RAR can end up in the \" Asynchronous RFC Error Log\" (transaction SM58). You can search for example with input parameters TRFC Function: /1RA/SD01_RAI_CREATE_API, /1RA/SD02_RAI_CREATE_API and /1RA/SD03_RAI_CREATE_API.</p>\n<p>After adding the missing authorization to the user the RFC calls can be reprocessed (right mouse click on entry and select \"Execute LUW\" or F6). This will then create the missing RAIs and trigger RAR contract creation.</p>\n<p>The authorization needed is object F_RRRAI and e.g. role SAP_SR_FARR_REV_RFCUSER_A would be a good starting point. Of course you could also add F_RRRAI to the roles you already have.</p>\n<p>If the system is not set up correctly, you can have data inconsistencies like missing RAIs with missing authorizations.</p>\n<p>Please also refer to the following KBAs:</p>\n<p><a href=\"/notes/2571920\" target=\"_blank\">2571920</a> Check errors in RFC monitor</p>\n<p><a href=\"/notes/2496728\" target=\"_blank\">2496728</a> Customizing for RAI monitor</p>\n<p><a href=\"/notes/2496671\" target=\"_blank\">2496671</a> Error: No items found entering RAI monitor</p>\n<p><span><strong><a name=\"Operations\" target=\"_blank\"></a>﻿3.     </strong><strong>Operations</strong></span></p>\n<p><strong>3.1 Where can I find more information when upgrading or patching SAP RAR?</strong></p>\n<p>You can find additional information in note <a href=\"/notes/2580963\" target=\"_blank\">2580963 - RAR upgrade preparation - additional hints</a>. Please always consider the latest version of the administrator’s guide in the application help: Additional Information -&gt; Administrator’s guide for SAP RAR 1.3 SP16 you can find <a href=\"https://help.sap.com/docs/SAP_REVENUE_ACCOUNTING_AND_REPORTING/88a7e7624cb346ddb1709ab3dac29417/c8abeb53bf7ca647e10000000a4450e5.html?locale=en-US&amp;version=1.3.15\" target=\"_blank\">here</a>.</p>\n<p><strong>3.2 Where can I find more information on data migration to SAP RAR?</strong></p>\n<p>You can find additional information in note <a href=\"/notes/2580961\" target=\"_blank\">2580961 -  RAR Data Migration - additional hints</a> and in note <a href=\"/notes/2733866\" target=\"_blank\">2733866 - Migration Guide for SD Revenue Recognition to Revenue Accounting and Reporting</a> or in the application help of SAP RAR 1.3: SAP Library -&gt; Migration.</p>\n<p><strong>3.3 Where can I find more information on supported transition scenarios?</strong></p>\n<p>You can find additional information in the application help of SAP RAR 1.3: SAP Library -&gt; Transition..</p>\n<p><strong>3.4 What should I consider before a go-live of SAP RAR?</strong></p>\n<p>You can find additional information in note <a href=\"/notes/2580924\" target=\"_blank\">2580924 - RAR GoLive preparation - additional checks</a>.</p>\n<p><strong>3.5 When processing revenue accounting items (RAI) or changing a contract I get an inflight check error. What does that mean?</strong></p>\n<p>You can find additional information on inflight check errors in note <a href=\"/notes/2533254\" target=\"_blank\">2533254 - SAP Revenue Accounting and Reporting: Inflight Checks</a>.</p>\n<p><strong>3.6 I have run the data validation program as per administrator’s guide and the report has found data validation errors. What does that mean?</strong></p>\n<p>You can find additional information on data validation errors in note <a href=\"/notes/2463880\" target=\"_blank\">2463880 - SAP Revenue Accounting and Reporting: Data validation report</a>.</p>\n<p> </p>\n<p><strong><a name=\"Sizing_and_Performance\" target=\"_blank\"></a>﻿4.     </strong><strong>Sizing and Performance</strong></p>\n<p><strong>4.1 Where can I find more information on sizing Revenue Accounting and Reporting?</strong></p>\n<p>You can find the sizing guide for Revenue Accounting and Reporting and further details on sizing on CPU sizing and Disk sizing on the SAP Service Marketplace: Products -&gt; Performance and Scalability -&gt; Sizing -&gt; Sizing Guidelines -&gt; SAP ERP -&gt; Sizing Guideline for SAP Revenue Accounting and Reporting 1.3.</p>\n<p><strong>4.2 Are there any constraints on the number of POBs a single contract can contain?</strong></p>\n<p>Please consider additional information in note <a href=\"/notes/2551667\" target=\"_blank\">2551667 - SAP Revenue and Reporting: Special Considerations for Large Number of POBs in one Revenue Accounting Contract</a>.</p>\n<p><strong>4.3 Where can I find information on data partitioning for SAP RAR?</strong></p>\n<p>Note <a href=\"/notes/2546640\" target=\"_blank\">2546640 - Partitioning of fast growing tables in SAP Revenue Accounting and Reporting</a> provides recommendations how tables could be partitioned for HANA database systems in the context of SAP Revenue Accounting and Reporting.</p>\n<p><strong>4.4 Are there any constraints around the duration of a contract with time-based POBs?</strong></p>\n<p>There is no hard restriction with regards to the end date of a time-based POB. However, please keep in mind that upon creation or change of the contract the system will create records in the Revenue Accounting tables (e.g. deferral item table) for the entire duration of the contract. This will have a significant impact on data foot print as well as performance of operative transactions such as revenue schedule or RAI processing. SAP strongly advises customers to consider a restriction of the duration of a contract to just a number of years as supported by common practice. A contract stretching more than 10 years is not advisable.</p>\n<p><strong>4.5 For reporting can I use the WebDynpro reports for high volume scenarios?</strong></p>\n<p>The WebDynpro reports documented in the online documentation are just sample reports. As these reports are sample reports, SAP recommends to create your own reports for productive use or utilize the data sources and corresponding reporting tools (e.g. in BW). Please note that if you have a high volume of data and do not use appropriate selection criteria for the report (e.g. select by contract), the system may run out of memory or have significant performance issues. For high volume use cases please consider using the data sources provided by SAP with corresponding reporting clients.</p>\n<p><strong><a name=\"HowTo\" target=\"_blank\"></a>﻿﻿5. Further How-to Information</strong></p>\n<p>You can find further how-to information in the online support system: <a href=\"https://launchpad.support.sap.com/#/mynotes?tab=Search\" target=\"_blank\">https://launchpad.support.sap.com/#/mynotes?tab=Search</a>. Search by Components (Start with): FI-RA and Document Type: SAP Knowledge Base Articles:</p>\n<p> </p>\n<div class=\"table-responsive\"><table class=\"sapMListModeMultiSelect sapMListShowSeparatorsAll sapMListTbl sapMListUl\" id=\"__xmlview1--idResultTable-listUl\">\n<thead>\n<tr class=\"sapMListTblRow sapMLIBFocusable sapMListTblHeader\" id=\"__xmlview1--idResultTable-tblHeader\"><th class=\"sapMListTblCell sapMListTblHeaderCell\" id=\"__column20\"><span class=\"sapMText sapMTextMaxWidth sapMTextNoWrap sapUiSelectable\" id=\"__text52\"><span class=\"sapMText sapMTextMaxWidth sapMTextNoWrap sapUiSelectable\" id=\"__text51\"><span class=\"sapMText sapMTextMaxWidth sapMTextNoWrap sapUiSelectable\" id=\"__text50\">SAP Component</span></span></span></th><th class=\"sapMListTblCell sapMListTblHeaderCell\" id=\"__column21\"><span class=\"sapMText sapMTextMaxWidth sapMTextNoWrap sapUiSelectable\" id=\"__text53\"><span class=\"sapMText sapMTextMaxWidth sapMTextNoWrap sapUiSelectable\" id=\"__text51\">Number</span></span></th><th class=\"sapMListTblCell sapMListTblHeaderCell\" id=\"__column23\"><span class=\"sapMText sapMTextMaxWidth sapMTextNoWrap sapUiSelectable\" id=\"__text55\"><span class=\"sapMText sapMTextMaxWidth sapMTextNoWrap sapUiSelectable\" id=\"__text53\">Title</span></span></th><th class=\"sapMListTblCell sapMListTblHeaderCell\" id=\"__column24\"><span class=\"sapMText sapMTextMaxWidth sapUiSelectable\" id=\"__text56\"></span></th></tr>\n</thead>\n<tbody id=\"__xmlview1--idResultTable-tblBody\">\n<tr class=\"sapMLIB sapMLIB-CTX sapMLIBFocusable sapMLIBShowSeparator sapMLIBTypeInactive sapMLIBUnread sapMListTblRow\" id=\"__item73-__clone48\">\n<td class=\"sapMListTblCell\" headers=\"__text50\" id=\"__item73-__clone48_cell0\"><span class=\"sapMText sapMTextMaxWidth sapMTextNoWrap sapUiSelectable\" id=\"__text57-__clone48\" title=\"Revenue Accounting Inbound Processing\">FI-RA-IP</span></td>\n<td class=\"sapMListTblCell\" headers=\"__text51\" id=\"__item73-__clone48_cell1\"><span class=\"sapMText sapMTextMaxWidth sapMTextNoWrap sapUiSelectable\" id=\"__text58-__clone48\">2650878</span></td>\n<td class=\"sapMListTblCell\" headers=\"__text53\" id=\"__item73-__clone48_cell3\"><a class=\"sapMLnk sapMLnkMaxWidth sapMLnkWrapping\" href=\"/notes/**********\" id=\"__link2-__clone48\" tabindex=\"0\" target=\"_blank\">Contract combination failed during migration.</a></td>\n<td class=\"sapMListTblCell\" headers=\"__text54\" id=\"__item73-__clone48_cell4\"><span class=\"sapMText sapMTextBreakWord sapMTextMaxWidth sapUiSelectable\" id=\"__text60-__clone48\" title=\"Problem\">Problem</span></td>\n<td class=\"sapMListTblCell\" headers=\"__text56\" id=\"__item73-__clone48_cell6\"><span class=\"sapMText sapMTextMaxWidth sapMTextNoWrap sapUiSelectable\" id=\"__text62-__clone48\"></span></td>\n<td class=\"sapMListTblNavCol\"></td>\n</tr>\n<tr class=\"sapMLIB sapMLIB-CTX sapMLIBFocusable sapMLIBShowSeparator sapMLIBTypeInactive sapMLIBUnread sapMListTblRow\" id=\"__item73-__clone49\">\n<td class=\"sapMListTblCell\" headers=\"__text50\" id=\"__item73-__clone49_cell0\"><span class=\"sapMText sapMTextMaxWidth sapMTextNoWrap sapUiSelectable\" id=\"__text57-__clone49\" title=\"Revenue Accouting Period Closing\">FI-RA-PC</span></td>\n<td class=\"sapMListTblCell\" headers=\"__text51\" id=\"__item73-__clone49_cell1\"><span class=\"sapMText sapMTextMaxWidth sapMTextNoWrap sapUiSelectable\" id=\"__text58-__clone49\">2649776</span></td>\n<td class=\"sapMListTblCell\" headers=\"__text53\" id=\"__item73-__clone49_cell3\"><a class=\"sapMLnk sapMLnkMaxWidth sapMLnkWrapping\" href=\"/notes/**********\" id=\"__link2-__clone49\" tabindex=\"0\" target=\"_blank\">What is the transaction code for \"Open and close revenue accounting Periods\" under SPRO - FARR_IMG</a></td>\n<td class=\"sapMListTblCell\" headers=\"__text54\" id=\"__item73-__clone49_cell4\"><span class=\"sapMText sapMTextMaxWidth sapUiSelectable\" id=\"__text60-__clone49\" title=\"How To\">How To</span></td>\n<td class=\"sapMListTblCell\" headers=\"__text56\" id=\"__item73-__clone49_cell6\"><span class=\"sapMText sapMTextMaxWidth sapMTextNoWrap sapUiSelectable\" id=\"__text62-__clone49\"></span></td>\n<td class=\"sapMListTblNavCol\"></td>\n</tr>\n<tr class=\"sapMLIB sapMLIB-CTX sapMLIBFocusable sapMLIBShowSeparator sapMLIBTypeInactive sapMListTblRow\" id=\"__item73-__clone50\">\n<td class=\"sapMListTblCell\" headers=\"__text50\" id=\"__item73-__clone50_cell0\"><span class=\"sapMText sapMTextMaxWidth sapMTextNoWrap sapUiSelectable\" id=\"__text57-__clone50\" title=\"Revenue Accounting Inbound Processing\">FI-RA-IP</span></td>\n<td class=\"sapMListTblCell\" headers=\"__text51\" id=\"__item73-__clone50_cell1\"><span class=\"sapMText sapMTextMaxWidth sapMTextNoWrap sapUiSelectable\" id=\"__text58-__clone50\">2635906</span></td>\n<td class=\"sapMListTblCell\" headers=\"__text53\" id=\"__item73-__clone50_cell3\"><a class=\"sapMLnk sapMLnkMaxWidth sapMLnkWrapping\" href=\"/notes/**********\" id=\"__link2-__clone50\" tabindex=\"0\" target=\"_blank\">Not able to generate Revenue Accounting Classes</a></td>\n<td class=\"sapMListTblCell\" headers=\"__text54\" id=\"__item73-__clone50_cell4\"><span class=\"sapMText sapMTextMaxWidth sapUiSelectable\" id=\"__text60-__clone50\" title=\"How To\">How To</span></td>\n<td class=\"sapMListTblCell\" headers=\"__text56\" id=\"__item73-__clone50_cell6\"><span class=\"sapMText sapMTextMaxWidth sapMTextNoWrap sapUiSelectable\" id=\"__text62-__clone50\"></span></td>\n<td class=\"sapMListTblNavCol\"></td>\n</tr>\n<tr class=\"sapMLIB sapMLIB-CTX sapMLIBFocusable sapMLIBShowSeparator sapMLIBTypeInactive sapMLIBUnread sapMListTblRow\" id=\"__item73-__clone51\">\n<td class=\"sapMListTblCell\" headers=\"__text50\" id=\"__item73-__clone51_cell0\"><span class=\"sapMText sapMTextMaxWidth sapMTextNoWrap sapUiSelectable\" id=\"__text57-__clone51\" title=\"Revenue Accouting Period Closing\">FI-RA-PC</span></td>\n<td class=\"sapMListTblCell\" headers=\"__text51\" id=\"__item73-__clone51_cell1\"><span class=\"sapMText sapMTextMaxWidth sapMTextNoWrap sapUiSelectable\" id=\"__text58-__clone51\">2634222</span></td>\n<td class=\"sapMListTblCell\" headers=\"__text53\" id=\"__item73-__clone51_cell3\"><a class=\"sapMLnk sapMLnkMaxWidth sapMLnkWrapping\" href=\"/notes/**********\" id=\"__link2-__clone51\" tabindex=\"0\" target=\"_blank\">Transfer Account is not found message is thrown on Revenue Posting Run</a></td>\n<td class=\"sapMListTblCell\" headers=\"__text54\" id=\"__item73-__clone51_cell4\"><span class=\"sapMText sapMTextBreakWord sapMTextMaxWidth sapUiSelectable\" id=\"__text60-__clone51\" title=\"Problem\">Problem</span></td>\n<td class=\"sapMListTblCell\" headers=\"__text56\" id=\"__item73-__clone51_cell6\"><span class=\"sapMText sapMTextMaxWidth sapMTextNoWrap sapUiSelectable\" id=\"__text62-__clone51\"></span></td>\n<td class=\"sapMListTblNavCol\"></td>\n</tr>\n<tr class=\"sapMLIB sapMLIB-CTX sapMLIBFocusable sapMLIBShowSeparator sapMLIBTypeInactive sapMLIBUnread sapMListTblRow\" id=\"__item73-__clone52\">\n<td class=\"sapMListTblCell\" headers=\"__text50\" id=\"__item73-__clone52_cell0\"><span class=\"sapMText sapMTextMaxWidth sapMTextNoWrap sapUiSelectable\" id=\"__text57-__clone52\" title=\"Revenue Accounting Inbound Processing\">FI-RA-IP</span></td>\n<td class=\"sapMListTblCell\" headers=\"__text51\" id=\"__item73-__clone52_cell1\"><span class=\"sapMText sapMTextMaxWidth sapMTextNoWrap sapUiSelectable\" id=\"__text58-__clone52\">2633460</span></td>\n<td class=\"sapMListTblCell\" headers=\"__text53\" id=\"__item73-__clone52_cell3\"><a class=\"sapMLnk sapMLnkMaxWidth sapMLnkWrapping\" href=\"/notes/**********\" id=\"__link2-__clone52\" tabindex=\"0\" target=\"_blank\">Changes in SD document will produce new RAIs (with new Timestamp)</a></td>\n<td class=\"sapMListTblCell\" headers=\"__text54\" id=\"__item73-__clone52_cell4\"><span class=\"sapMText sapMTextMaxWidth sapUiSelectable\" id=\"__text60-__clone52\" title=\"How To\">How To</span></td>\n<td class=\"sapMListTblCell\" headers=\"__text56\" id=\"__item73-__clone52_cell6\"><span class=\"sapMText sapMTextMaxWidth sapMTextNoWrap sapUiSelectable\" id=\"__text62-__clone52\"></span></td>\n<td class=\"sapMListTblNavCol\"></td>\n</tr>\n<tr class=\"sapMLIB sapMLIB-CTX sapMLIBFocusable sapMLIBShowSeparator sapMLIBTypeInactive sapMLIBUnread sapMListTblRow\" id=\"__item73-__clone53\">\n<td class=\"sapMListTblCell\" headers=\"__text50\" id=\"__item73-__clone53_cell0\"><span class=\"sapMText sapMTextMaxWidth sapMTextNoWrap sapUiSelectable\" id=\"__text57-__clone53\" title=\"Revenue Accounting Inbound Processing\">FI-RA-IP</span></td>\n<td class=\"sapMListTblCell\" headers=\"__text51\" id=\"__item73-__clone53_cell1\"><span class=\"sapMText sapMTextMaxWidth sapMTextNoWrap sapUiSelectable\" id=\"__text58-__clone53\">2633256</span></td>\n<td class=\"sapMListTblCell\" headers=\"__text53\" id=\"__item73-__clone53_cell3\"><a class=\"sapMLnk sapMLnkMaxWidth sapMLnkWrapping\" href=\"/notes/**********\" id=\"__link2-__clone53\" tabindex=\"0\" target=\"_blank\">Custom Validations in Revenue Accounting (RAR)</a></td>\n<td class=\"sapMListTblCell\" headers=\"__text54\" id=\"__item73-__clone53_cell4\"><span class=\"sapMText sapMTextMaxWidth sapUiSelectable\" id=\"__text60-__clone53\" title=\"How To\">How To</span></td>\n<td class=\"sapMListTblCell\" headers=\"__text56\" id=\"__item73-__clone53_cell6\"><span class=\"sapMText sapMTextMaxWidth sapMTextNoWrap sapUiSelectable\" id=\"__text62-__clone53\"></span></td>\n<td class=\"sapMListTblNavCol\"></td>\n</tr>\n<tr class=\"sapMLIB sapMLIB-CTX sapMLIBFocusable sapMLIBShowSeparator sapMLIBTypeInactive sapMLIBUnread sapMListTblRow\" id=\"__item73-__clone54\">\n<td class=\"sapMListTblCell\" headers=\"__text50\" id=\"__item73-__clone54_cell0\"><span class=\"sapMText sapMTextMaxWidth sapMTextNoWrap sapUiSelectable\" id=\"__text57-__clone54\" title=\"Revenue Accounting\">FI-RA</span></td>\n<td class=\"sapMListTblCell\" headers=\"__text51\" id=\"__item73-__clone54_cell1\"><span class=\"sapMText sapMTextMaxWidth sapMTextNoWrap sapUiSelectable\" id=\"__text58-__clone54\">2627887</span></td>\n<td class=\"sapMListTblCell\" headers=\"__text53\" id=\"__item73-__clone54_cell3\"><a class=\"sapMLnk sapMLnkMaxWidth sapMLnkWrapping\" href=\"/notes/**********\" id=\"__link2-__clone54\" tabindex=\"0\" target=\"_blank\">Dump in CL_FARR_DB_UPDATE when maintaining customizing \"Assign Company Codes to Accounting Principles\"</a></td>\n<td class=\"sapMListTblCell\" headers=\"__text54\" id=\"__item73-__clone54_cell4\"><span class=\"sapMText sapMTextBreakWord sapMTextMaxWidth sapUiSelectable\" id=\"__text60-__clone54\" title=\"Problem\">Problem</span></td>\n<td class=\"sapMListTblCell\" headers=\"__text56\" id=\"__item73-__clone54_cell6\"><span class=\"sapMText sapMTextMaxWidth sapMTextNoWrap sapUiSelectable\" id=\"__text62-__clone54\"></span></td>\n<td class=\"sapMListTblNavCol\"></td>\n</tr>\n<tr class=\"sapMLIB sapMLIB-CTX sapMLIBFocusable sapMLIBShowSeparator sapMLIBTypeInactive sapMLIBUnread sapMListTblRow\" id=\"__item73-__clone55\">\n<td class=\"sapMListTblCell\" headers=\"__text50\" id=\"__item73-__clone55_cell0\"><span class=\"sapMText sapMTextMaxWidth sapMTextNoWrap sapUiSelectable\" id=\"__text57-__clone55\" title=\"Revenue Accounting\">FI-RA</span></td>\n<td class=\"sapMListTblCell\" headers=\"__text51\" id=\"__item73-__clone55_cell1\"><span class=\"sapMText sapMTextMaxWidth sapMTextNoWrap sapUiSelectable\" id=\"__text58-__clone55\">2623171</span></td>\n<td class=\"sapMListTblCell\" headers=\"__text53\" id=\"__item73-__clone55_cell3\"><a class=\"sapMLnk sapMLnkMaxWidth sapMLnkWrapping\" href=\"/notes/**********\" id=\"__link2-__clone55\" tabindex=\"0\" target=\"_blank\">Contract fulfilled progress is greater than 100%</a></td>\n<td class=\"sapMListTblCell\" headers=\"__text54\" id=\"__item73-__clone55_cell4\"><span class=\"sapMText sapMTextMaxWidth sapUiSelectable\" id=\"__text60-__clone55\" title=\"How To\">How To</span></td>\n<td class=\"sapMListTblCell\" headers=\"__text56\" id=\"__item73-__clone55_cell6\"><span class=\"sapMText sapMTextMaxWidth sapMTextNoWrap sapUiSelectable\" id=\"__text62-__clone55\"></span></td>\n<td class=\"sapMListTblNavCol\"></td>\n</tr>\n<tr class=\"sapMLIB sapMLIB-CTX sapMLIBFocusable sapMLIBShowSeparator sapMLIBTypeInactive sapMLIBUnread sapMListTblRow\" id=\"__item73-__clone56\">\n<td class=\"sapMListTblCell\" headers=\"__text50\" id=\"__item73-__clone56_cell0\"><span class=\"sapMText sapMTextMaxWidth sapMTextNoWrap sapUiSelectable\" id=\"__text57-__clone56\" title=\"Revenue Accounting Inbound Processing\">FI-RA-IP</span></td>\n<td class=\"sapMListTblCell\" headers=\"__text51\" id=\"__item73-__clone56_cell1\"><span class=\"sapMText sapMTextMaxWidth sapMTextNoWrap sapUiSelectable\" id=\"__text58-__clone56\">2618897</span></td>\n<td class=\"sapMListTblCell\" headers=\"__text53\" id=\"__item73-__clone56_cell3\"><a class=\"sapMLnk sapMLnkMaxWidth sapMLnkWrapping\" href=\"/notes/**********\" id=\"__link2-__clone56\" tabindex=\"0\" target=\"_blank\">Can not create a fulfillment revenue accounting item from a delivery document without performing post goods issue</a></td>\n<td class=\"sapMListTblCell\" headers=\"__text54\" id=\"__item73-__clone56_cell4\"><span class=\"sapMText sapMTextMaxWidth sapUiSelectable\" id=\"__text60-__clone56\" title=\"How To\">How To</span></td>\n<td class=\"sapMListTblCell\" headers=\"__text56\" id=\"__item73-__clone56_cell6\"><span class=\"sapMText sapMTextMaxWidth sapMTextNoWrap sapUiSelectable\" id=\"__text62-__clone56\"></span></td>\n<td class=\"sapMListTblNavCol\"></td>\n</tr>\n<tr class=\"sapMLIB sapMLIB-CTX sapMLIBFocusable sapMLIBShowSeparator sapMLIBTypeInactive sapMLIBUnread sapMListTblRow\" id=\"__item73-__clone57\">\n<td class=\"sapMListTblCell\" headers=\"__text50\" id=\"__item73-__clone57_cell0\"><span class=\"sapMText sapMTextMaxWidth sapMTextNoWrap sapUiSelectable\" id=\"__text57-__clone57\" title=\"Revenue Accounting\">FI-RA</span></td>\n<td class=\"sapMListTblCell\" headers=\"__text51\" id=\"__item73-__clone57_cell1\"><span class=\"sapMText sapMTextMaxWidth sapMTextNoWrap sapUiSelectable\" id=\"__text58-__clone57\">2615364</span></td>\n<td class=\"sapMListTblCell\" headers=\"__text53\" id=\"__item73-__clone57_cell3\"><a class=\"sapMLnk sapMLnkMaxWidth sapMLnkWrapping\" href=\"/notes/**********\" id=\"__link2-__clone57\" tabindex=\"0\" target=\"_blank\">Create enhancement for POB_DETAIL_UI</a></td>\n<td class=\"sapMListTblCell\" headers=\"__text54\" id=\"__item73-__clone57_cell4\"><span class=\"sapMText sapMTextMaxWidth sapUiSelectable\" id=\"__text60-__clone57\" title=\"How To\">How To</span></td>\n<td class=\"sapMListTblCell\" headers=\"__text56\" id=\"__item73-__clone57_cell6\"><span class=\"sapMText sapMTextMaxWidth sapMTextNoWrap sapUiSelectable\" id=\"__text62-__clone57\"></span></td>\n<td class=\"sapMListTblNavCol\"></td>\n</tr>\n<tr class=\"sapMLIB sapMLIB-CTX sapMLIBFocusable sapMLIBShowSeparator sapMLIBTypeInactive sapMLIBUnread sapMListTblRow\" id=\"__item73-__clone58\">\n<td class=\"sapMListTblCell\" headers=\"__text50\" id=\"__item73-__clone58_cell0\"><span class=\"sapMText sapMTextMaxWidth sapMTextNoWrap sapUiSelectable\" id=\"__text57-__clone58\" title=\"Revenue Accounting Inbound Processing\">FI-RA-IP</span></td>\n<td class=\"sapMListTblCell\" headers=\"__text51\" id=\"__item73-__clone58_cell1\"><span class=\"sapMText sapMTextMaxWidth sapMTextNoWrap sapUiSelectable\" id=\"__text58-__clone58\">2609055</span></td>\n<td class=\"sapMListTblCell\" headers=\"__text53\" id=\"__item73-__clone58_cell3\"><a class=\"sapMLnk sapMLnkMaxWidth sapMLnkWrapping\" href=\"/notes/**********\" id=\"__link2-__clone58\" tabindex=\"0\" target=\"_blank\">FARR_IL_CLEANUP issues error FARR_RAI463 (No values selected (selection or authorization failed) when deleting Header ID</a></td>\n<td class=\"sapMListTblCell\" headers=\"__text54\" id=\"__item73-__clone58_cell4\"><span class=\"sapMText sapMTextBreakWord sapMTextMaxWidth sapUiSelectable\" id=\"__text60-__clone58\" title=\"Problem\">Problem</span></td>\n<td class=\"sapMListTblCell\" headers=\"__text56\" id=\"__item73-__clone58_cell6\"><span class=\"sapMText sapMTextMaxWidth sapMTextNoWrap sapUiSelectable\" id=\"__text62-__clone58\"></span></td>\n<td class=\"sapMListTblNavCol\"></td>\n</tr>\n<tr class=\"sapMLIB sapMLIB-CTX sapMLIBFocusable sapMLIBShowSeparator sapMLIBTypeInactive sapMLIBUnread sapMListTblRow\" id=\"__item73-__clone59\">\n<td class=\"sapMListTblCell\" headers=\"__text50\" id=\"__item73-__clone59_cell0\"><span class=\"sapMText sapMTextMaxWidth sapMTextNoWrap sapUiSelectable\" id=\"__text57-__clone59\" title=\"Revenue Accounting Inbound Processing\">FI-RA-IP</span></td>\n<td class=\"sapMListTblCell\" headers=\"__text51\" id=\"__item73-__clone59_cell1\"><span class=\"sapMText sapMTextMaxWidth sapMTextNoWrap sapUiSelectable\" id=\"__text58-__clone59\">2600802</span></td>\n<td class=\"sapMListTblCell\" headers=\"__text53\" id=\"__item73-__clone59_cell3\"><a class=\"sapMLnk sapMLnkMaxWidth sapMLnkWrapping\" href=\"/notes/**********\" id=\"__link2-__clone59\" tabindex=\"0\" target=\"_blank\">Dump: SYSTEM_SHM_AREA_OBSOLETE using Revenue Accounting</a></td>\n<td class=\"sapMListTblCell\" headers=\"__text54\" id=\"__item73-__clone59_cell4\"><span class=\"sapMText sapMTextBreakWord sapMTextMaxWidth sapUiSelectable\" id=\"__text60-__clone59\" title=\"Problem\">Problem</span></td>\n<td class=\"sapMListTblCell\" headers=\"__text56\" id=\"__item73-__clone59_cell6\"><span class=\"sapMText sapMTextMaxWidth sapMTextNoWrap sapUiSelectable\" id=\"__text62-__clone59\"></span></td>\n<td class=\"sapMListTblNavCol\"></td>\n</tr>\n<tr class=\"sapMLIB sapMLIB-CTX sapMLIBFocusable sapMLIBShowSeparator sapMLIBTypeInactive sapMLIBUnread sapMListTblRow\" id=\"__item73-__clone60\">\n<td class=\"sapMListTblCell\" headers=\"__text50\" id=\"__item73-__clone60_cell0\"><span class=\"sapMText sapMTextMaxWidth sapMTextNoWrap sapUiSelectable\" id=\"__text57-__clone60\" title=\"Revenue Accounting Inbound Processing\">FI-RA-IP</span></td>\n<td class=\"sapMListTblCell\" headers=\"__text51\" id=\"__item73-__clone60_cell1\"><span class=\"sapMText sapMTextMaxWidth sapMTextNoWrap sapUiSelectable\" id=\"__text58-__clone60\">2600634</span></td>\n<td class=\"sapMListTblCell\" headers=\"__text53\" id=\"__item73-__clone60_cell3\"><a class=\"sapMLnk sapMLnkMaxWidth sapMLnkWrapping\" href=\"/notes/**********\" id=\"__link2-__clone60\" tabindex=\"0\" target=\"_blank\">Error: FARR_RAI707 processing Revenue Accounting items</a></td>\n<td class=\"sapMListTblCell\" headers=\"__text54\" id=\"__item73-__clone60_cell4\"><span class=\"sapMText sapMTextBreakWord sapMTextMaxWidth sapUiSelectable\" id=\"__text60-__clone60\" title=\"Problem\">Problem</span></td>\n<td class=\"sapMListTblCell\" headers=\"__text56\" id=\"__item73-__clone60_cell6\"><span class=\"sapMText sapMTextMaxWidth sapMTextNoWrap sapUiSelectable\" id=\"__text62-__clone60\"></span></td>\n<td class=\"sapMListTblNavCol\"></td>\n</tr>\n<tr class=\"sapMLIB sapMLIB-CTX sapMLIBFocusable sapMLIBShowSeparator sapMLIBTypeInactive sapMLIBUnread sapMListTblRow\" id=\"__item73-__clone61\">\n<td class=\"sapMListTblCell\" headers=\"__text50\" id=\"__item73-__clone61_cell0\"><span class=\"sapMText sapMTextMaxWidth sapMTextNoWrap sapUiSelectable\" id=\"__text57-__clone61\" title=\"Revenue Accounting Contract Processing\">FI-RA-CP</span></td>\n<td class=\"sapMListTblCell\" headers=\"__text51\" id=\"__item73-__clone61_cell1\"><span class=\"sapMText sapMTextMaxWidth sapMTextNoWrap sapUiSelectable\" id=\"__text58-__clone61\">2598387</span></td>\n<td class=\"sapMListTblCell\" headers=\"__text53\" id=\"__item73-__clone61_cell3\"><a class=\"sapMLnk sapMLnkMaxWidth sapMLnkWrapping\" href=\"/notes/**********\" id=\"__link2-__clone61\" tabindex=\"0\" target=\"_blank\">Inflight Check E2 / E02 error: Incorrect number after allocation</a></td>\n<td class=\"sapMListTblCell\" headers=\"__text54\" id=\"__item73-__clone61_cell4\"><span class=\"sapMText sapMTextBreakWord sapMTextMaxWidth sapUiSelectable\" id=\"__text60-__clone61\" title=\"Problem\">Problem</span></td>\n<td class=\"sapMListTblCell\" headers=\"__text56\" id=\"__item73-__clone61_cell6\"><span class=\"sapMText sapMTextMaxWidth sapMTextNoWrap sapUiSelectable\" id=\"__text62-__clone61\"></span></td>\n<td class=\"sapMListTblNavCol\"></td>\n</tr>\n<tr class=\"sapMLIB sapMLIB-CTX sapMLIBFocusable sapMLIBShowSeparator sapMLIBTypeInactive sapMLIBUnread sapMListTblRow\" id=\"__item73-__clone62\">\n<td class=\"sapMListTblCell\" headers=\"__text50\" id=\"__item73-__clone62_cell0\"><span class=\"sapMText sapMTextMaxWidth sapMTextNoWrap sapUiSelectable\" id=\"__text57-__clone62\" title=\"Revenue Accounting\">FI-RA</span></td>\n<td class=\"sapMListTblCell\" headers=\"__text51\" id=\"__item73-__clone62_cell1\"><span class=\"sapMText sapMTextMaxWidth sapMTextNoWrap sapUiSelectable\" id=\"__text58-__clone62\">2594984</span></td>\n<td class=\"sapMListTblCell\" headers=\"__text53\" id=\"__item73-__clone62_cell3\"><a class=\"sapMLnk sapMLnkMaxWidth sapMLnkWrapping\" href=\"/notes/**********\" id=\"__link2-__clone62\" tabindex=\"0\" target=\"_blank\">Trying to download RAR 1.3 from the download center and get message that you are not authorized to download</a></td>\n<td class=\"sapMListTblCell\" headers=\"__text54\" id=\"__item73-__clone62_cell4\"><span class=\"sapMText sapMTextBreakWord sapMTextMaxWidth sapUiSelectable\" id=\"__text60-__clone62\" title=\"Problem\">Problem</span></td>\n<td class=\"sapMListTblCell\" headers=\"__text56\" id=\"__item73-__clone62_cell6\"><span class=\"sapMText sapMTextMaxWidth sapMTextNoWrap sapUiSelectable\" id=\"__text62-__clone62\"></span></td>\n<td class=\"sapMListTblNavCol\"></td>\n</tr>\n<tr class=\"sapMLIB sapMLIB-CTX sapMLIBFocusable sapMLIBShowSeparator sapMLIBTypeInactive sapMLIBUnread sapMListTblRow\" id=\"__item73-__clone63\">\n<td class=\"sapMListTblCell\" headers=\"__text50\" id=\"__item73-__clone63_cell0\"><span class=\"sapMText sapMTextMaxWidth sapMTextNoWrap sapUiSelectable\" id=\"__text57-__clone63\" title=\"Revenue Accounting Contract Processing\">FI-RA-CP</span></td>\n<td class=\"sapMListTblCell\" headers=\"__text51\" id=\"__item73-__clone63_cell1\"><span class=\"sapMText sapMTextMaxWidth sapMTextNoWrap sapUiSelectable\" id=\"__text58-__clone63\">2593998</span></td>\n<td class=\"sapMListTblCell\" headers=\"__text53\" id=\"__item73-__clone63_cell3\"><a class=\"sapMLnk sapMLnkMaxWidth sapMLnkWrapping\" href=\"/notes/**********\" id=\"__link2-__clone63\" tabindex=\"0\" target=\"_blank\">TSV_TNEW_PAGE_ALLOC_FAILED  running report \"FI Documents by Contract\"</a></td>\n<td class=\"sapMListTblCell\" headers=\"__text54\" id=\"__item73-__clone63_cell4\"><span class=\"sapMText sapMTextBreakWord sapMTextMaxWidth sapUiSelectable\" id=\"__text60-__clone63\" title=\"Problem\">Problem</span></td>\n<td class=\"sapMListTblCell\" headers=\"__text56\" id=\"__item73-__clone63_cell6\"><span class=\"sapMText sapMTextMaxWidth sapMTextNoWrap sapUiSelectable\" id=\"__text62-__clone63\"></span></td>\n<td class=\"sapMListTblNavCol\"></td>\n</tr>\n<tr class=\"sapMLIB sapMLIB-CTX sapMLIBFocusable sapMLIBShowSeparator sapMLIBTypeInactive sapMLIBUnread sapMListTblRow\" id=\"__item73-__clone64\">\n<td class=\"sapMListTblCell\" headers=\"__text50\" id=\"__item73-__clone64_cell0\"><span class=\"sapMText sapMTextMaxWidth sapMTextNoWrap sapUiSelectable\" id=\"__text57-__clone64\" title=\"Revenue Accounting\">FI-RA</span></td>\n<td class=\"sapMListTblCell\" headers=\"__text51\" id=\"__item73-__clone64_cell1\"><span class=\"sapMText sapMTextMaxWidth sapMTextNoWrap sapUiSelectable\" id=\"__text58-__clone64\">2592905</span></td>\n<td class=\"sapMListTblCell\" headers=\"__text53\" id=\"__item73-__clone64_cell3\"><a class=\"sapMLnk sapMLnkMaxWidth sapMLnkWrapping\" href=\"/notes/**********\" id=\"__link2-__clone64\" tabindex=\"0\" target=\"_blank\">FARR_TRANS_CATCHUP does not display Recon Key if it is executed for the second time</a></td>\n<td class=\"sapMListTblCell\" headers=\"__text54\" id=\"__item73-__clone64_cell4\"><span class=\"sapMText sapMTextBreakWord sapMTextMaxWidth sapUiSelectable\" id=\"__text60-__clone64\" title=\"Problem\">Problem</span></td>\n<td class=\"sapMListTblCell\" headers=\"__text56\" id=\"__item73-__clone64_cell6\"><span class=\"sapMText sapMTextMaxWidth sapMTextNoWrap sapUiSelectable\" id=\"__text62-__clone64\"></span></td>\n<td class=\"sapMListTblNavCol\"></td>\n</tr>\n<tr class=\"sapMLIB sapMLIB-CTX sapMLIBFocusable sapMLIBShowSeparator sapMLIBTypeInactive sapMLIBUnread sapMListTblRow\" id=\"__item73-__clone65\">\n<td class=\"sapMListTblCell\" headers=\"__text50\" id=\"__item73-__clone65_cell0\"><span class=\"sapMText sapMTextMaxWidth sapMTextNoWrap sapUiSelectable\" id=\"__text57-__clone65\" title=\"Revenue Accounting Migration/Transition\">FI-RA-MIG</span></td>\n<td class=\"sapMListTblCell\" headers=\"__text51\" id=\"__item73-__clone65_cell1\"><span class=\"sapMText sapMTextMaxWidth sapMTextNoWrap sapUiSelectable\" id=\"__text58-__clone65\">2592015</span></td>\n<td class=\"sapMListTblCell\" headers=\"__text53\" id=\"__item73-__clone65_cell3\"><a class=\"sapMLnk sapMLnkMaxWidth sapMLnkWrapping\" href=\"/notes/**********\" id=\"__link2-__clone65\" tabindex=\"0\" target=\"_blank\">Document cannot be cleaned up with FARR_IL_CLEANUP</a></td>\n<td class=\"sapMListTblCell\" headers=\"__text54\" id=\"__item73-__clone65_cell4\"><span class=\"sapMText sapMTextBreakWord sapMTextMaxWidth sapUiSelectable\" id=\"__text60-__clone65\" title=\"Problem\">Problem</span></td>\n<td class=\"sapMListTblCell\" headers=\"__text56\" id=\"__item73-__clone65_cell6\"><span class=\"sapMText sapMTextMaxWidth sapMTextNoWrap sapUiSelectable\" id=\"__text62-__clone65\"></span></td>\n<td class=\"sapMListTblNavCol\"></td>\n</tr>\n<tr class=\"sapMLIB sapMLIB-CTX sapMLIBFocusable sapMLIBShowSeparator sapMLIBTypeInactive sapMLIBUnread sapMListTblRow\" id=\"__item73-__clone66\">\n<td class=\"sapMListTblCell\" headers=\"__text50\" id=\"__item73-__clone66_cell0\"><span class=\"sapMText sapMTextMaxWidth sapMTextNoWrap sapUiSelectable\" id=\"__text57-__clone66\" title=\"Revenue Accounting Inbound Processing\">FI-RA-IP</span></td>\n<td class=\"sapMListTblCell\" headers=\"__text51\" id=\"__item73-__clone66_cell1\"><span class=\"sapMText sapMTextMaxWidth sapMTextNoWrap sapUiSelectable\" id=\"__text58-__clone66\">2591974</span></td>\n<td class=\"sapMListTblCell\" headers=\"__text53\" id=\"__item73-__clone66_cell3\"><a class=\"sapMLnk sapMLnkMaxWidth sapMLnkWrapping\" href=\"/notes/**********\" id=\"__link2-__clone66\" tabindex=\"0\" target=\"_blank\">Revenue Accounting Items cannot be processed. Error \"There is only one main condition type allowed\"</a></td>\n<td class=\"sapMListTblCell\" headers=\"__text54\" id=\"__item73-__clone66_cell4\"><span class=\"sapMText sapMTextBreakWord sapMTextMaxWidth sapUiSelectable\" id=\"__text60-__clone66\" title=\"Problem\">Problem</span></td>\n<td class=\"sapMListTblCell\" headers=\"__text56\" id=\"__item73-__clone66_cell6\"><span class=\"sapMText sapMTextMaxWidth sapMTextNoWrap sapUiSelectable\" id=\"__text62-__clone66\"></span></td>\n<td class=\"sapMListTblNavCol\"></td>\n</tr>\n<tr class=\"sapMLIB sapMLIB-CTX sapMLIBFocusable sapMLIBShowSeparator sapMLIBTypeInactive sapMListTblRow\" id=\"__item73-__clone67\">\n<td class=\"sapMListTblCell\" headers=\"__text50\" id=\"__item73-__clone67_cell0\"><span class=\"sapMText sapMTextMaxWidth sapMTextNoWrap sapUiSelectable\" id=\"__text57-__clone67\" title=\"Revenue Accounting\">FI-RA</span></td>\n<td class=\"sapMListTblCell\" headers=\"__text51\" id=\"__item73-__clone67_cell1\"><span class=\"sapMText sapMTextMaxWidth sapMTextNoWrap sapUiSelectable\" id=\"__text58-__clone67\">2591455</span></td>\n<td class=\"sapMListTblCell\" headers=\"__text53\" id=\"__item73-__clone67_cell3\"><a class=\"sapMLnk sapMLnkMaxWidth sapMLnkWrapping\" href=\"/notes/**********\" id=\"__link2-__clone67\" tabindex=\"0\" target=\"_blank\">Error handling of the Revenue Accounting Item creation API</a></td>\n<td class=\"sapMListTblCell\" headers=\"__text54\" id=\"__item73-__clone67_cell4\"><span class=\"sapMText sapMTextMaxWidth sapUiSelectable\" id=\"__text60-__clone67\" title=\"How To\">How To</span></td>\n<td class=\"sapMListTblCell\" headers=\"__text56\" id=\"__item73-__clone67_cell6\"><span class=\"sapMText sapMTextMaxWidth sapMTextNoWrap sapUiSelectable\" id=\"__text62-__clone67\"></span></td>\n<td class=\"sapMListTblNavCol\"></td>\n</tr>\n<tr class=\"sapMLIB sapMLIB-CTX sapMLIBFocusable sapMLIBShowSeparator sapMLIBTypeInactive sapMLIBUnread sapMListTblRow\" id=\"__item73-__clone68\">\n<td class=\"sapMListTblCell\" headers=\"__text50\" id=\"__item73-__clone68_cell0\"><span class=\"sapMText sapMTextMaxWidth sapMTextNoWrap sapUiSelectable\" id=\"__text57-__clone68\" title=\"Revenue Accounting Inbound Processing\">FI-RA-IP</span></td>\n<td class=\"sapMListTblCell\" headers=\"__text51\" id=\"__item73-__clone68_cell1\"><span class=\"sapMText sapMTextMaxWidth sapMTextNoWrap sapUiSelectable\" id=\"__text58-__clone68\">2583778</span></td>\n<td class=\"sapMListTblCell\" headers=\"__text53\" id=\"__item73-__clone68_cell3\"><a class=\"sapMLnk sapMLnkMaxWidth sapMLnkWrapping\" href=\"/notes/**********\" id=\"__link2-__clone68\" tabindex=\"0\" target=\"_blank\">Currency code field XXXX must not be initial. Amount is not zero (Message no. FARR_RAI375)</a></td>\n<td class=\"sapMListTblCell\" headers=\"__text54\" id=\"__item73-__clone68_cell4\"><span class=\"sapMText sapMTextBreakWord sapMTextMaxWidth sapUiSelectable\" id=\"__text60-__clone68\" title=\"Problem\">Problem</span></td>\n<td class=\"sapMListTblCell\" headers=\"__text56\" id=\"__item73-__clone68_cell6\"><span class=\"sapMText sapMTextMaxWidth sapMTextNoWrap sapUiSelectable\" id=\"__text62-__clone68\"></span></td>\n<td class=\"sapMListTblNavCol\"></td>\n</tr>\n<tr class=\"sapMLIB sapMLIB-CTX sapMLIBFocusable sapMLIBShowSeparator sapMLIBTypeInactive sapMLIBUnread sapMListTblRow\" id=\"__item73-__clone69\">\n<td class=\"sapMListTblCell\" headers=\"__text50\" id=\"__item73-__clone69_cell0\"><span class=\"sapMText sapMTextMaxWidth sapMTextNoWrap sapUiSelectable\" id=\"__text57-__clone69\" title=\"Revenue Accounting Inbound Processing\">FI-RA-IP</span></td>\n<td class=\"sapMListTblCell\" headers=\"__text51\" id=\"__item73-__clone69_cell1\"><span class=\"sapMText sapMTextMaxWidth sapMTextNoWrap sapUiSelectable\" id=\"__text58-__clone69\">2581490</span></td>\n<td class=\"sapMListTblCell\" headers=\"__text53\" id=\"__item73-__clone69_cell3\"><a class=\"sapMLnk sapMLnkMaxWidth sapMLnkWrapping\" href=\"/notes/**********\" id=\"__link2-__clone69\" tabindex=\"0\" target=\"_blank\">BRF+ - General topics.</a></td>\n<td class=\"sapMListTblCell\" headers=\"__text54\" id=\"__item73-__clone69_cell4\"><span class=\"sapMText sapMTextMaxWidth sapUiSelectable\" id=\"__text60-__clone69\" title=\"How To\">How To</span></td>\n<td class=\"sapMListTblCell\" headers=\"__text56\" id=\"__item73-__clone69_cell6\"><span class=\"sapMText sapMTextMaxWidth sapMTextNoWrap sapUiSelectable\" id=\"__text62-__clone69\"></span></td>\n<td class=\"sapMListTblNavCol\"></td>\n</tr>\n<tr class=\"sapMLIB sapMLIB-CTX sapMLIBFocusable sapMLIBShowSeparator sapMLIBTypeInactive sapMListTblRow\" id=\"__item73-__clone70\">\n<td class=\"sapMListTblCell\" headers=\"__text50\" id=\"__item73-__clone70_cell0\"><span class=\"sapMText sapMTextMaxWidth sapMTextNoWrap sapUiSelectable\" id=\"__text57-__clone70\" title=\"Revenue Accounting\">FI-RA</span></td>\n<td class=\"sapMListTblCell\" headers=\"__text51\" id=\"__item73-__clone70_cell1\"><span class=\"sapMText sapMTextMaxWidth sapMTextNoWrap sapUiSelectable\" id=\"__text58-__clone70\">2581011</span></td>\n<td class=\"sapMListTblCell\" headers=\"__text53\" id=\"__item73-__clone70_cell3\"><a class=\"sapMLnk sapMLnkMaxWidth sapMLnkWrapping\" href=\"/notes/**********\" id=\"__link2-__clone70\" tabindex=\"0\" target=\"_blank\">DataSource 0FARR_RA_10 does not exist in version A</a></td>\n<td class=\"sapMListTblCell\" headers=\"__text54\" id=\"__item73-__clone70_cell4\"><span class=\"sapMText sapMTextMaxWidth sapUiSelectable\" id=\"__text60-__clone70\" title=\"How To\">How To</span></td>\n<td class=\"sapMListTblCell\" headers=\"__text56\" id=\"__item73-__clone70_cell6\"><span class=\"sapMText sapMTextMaxWidth sapMTextNoWrap sapUiSelectable\" id=\"__text62-__clone70\"></span></td>\n<td class=\"sapMListTblNavCol\"></td>\n</tr>\n<tr class=\"sapMLIB sapMLIB-CTX sapMLIBFocusable sapMLIBShowSeparator sapMLIBTypeInactive sapMLIBUnread sapMListTblRow\" id=\"__item73-__clone71\">\n<td class=\"sapMListTblCell\" headers=\"__text50\" id=\"__item73-__clone71_cell0\"><span class=\"sapMText sapMTextMaxWidth sapMTextNoWrap sapUiSelectable\" id=\"__text57-__clone71\" title=\"Revenue Accounting Inbound Processing\">FI-RA-IP</span></td>\n<td class=\"sapMListTblCell\" headers=\"__text51\" id=\"__item73-__clone71_cell1\"><span class=\"sapMText sapMTextMaxWidth sapMTextNoWrap sapUiSelectable\" id=\"__text58-__clone71\">2580723</span></td>\n<td class=\"sapMListTblCell\" headers=\"__text53\" id=\"__item73-__clone71_cell3\"><a class=\"sapMLnk sapMLnkMaxWidth sapMLnkWrapping\" href=\"/notes/**********\" id=\"__link2-__clone71\" tabindex=\"0\" target=\"_blank\">Transport error in SD01 Class Configuration</a></td>\n<td class=\"sapMListTblCell\" headers=\"__text54\" id=\"__item73-__clone71_cell4\"><span class=\"sapMText sapMTextMaxWidth sapUiSelectable\" id=\"__text60-__clone71\" title=\"How To\">How To</span></td>\n<td class=\"sapMListTblCell\" headers=\"__text56\" id=\"__item73-__clone71_cell6\"><span class=\"sapMText sapMTextMaxWidth sapMTextNoWrap sapUiSelectable\" id=\"__text62-__clone71\"></span></td>\n<td class=\"sapMListTblNavCol\"></td>\n</tr>\n<tr class=\"sapMLIB sapMLIB-CTX sapMLIBFocusable sapMLIBShowSeparator sapMLIBTypeInactive sapMLIBUnread sapMListTblRow\" id=\"__item73-__clone72\">\n<td class=\"sapMListTblCell\" headers=\"__text50\" id=\"__item73-__clone72_cell0\"><span class=\"sapMText sapMTextMaxWidth sapMTextNoWrap sapUiSelectable\" id=\"__text57-__clone72\" title=\"Revenue Accounting Inbound Processing\">FI-RA-IP</span></td>\n<td class=\"sapMListTblCell\" headers=\"__text51\" id=\"__item73-__clone72_cell1\"><span class=\"sapMText sapMTextMaxWidth sapMTextNoWrap sapUiSelectable\" id=\"__text58-__clone72\">2571920</span></td>\n<td class=\"sapMListTblCell\" headers=\"__text53\" id=\"__item73-__clone72_cell3\"><a class=\"sapMLnk sapMLnkMaxWidth sapMLnkWrapping\" href=\"/notes/**********\" id=\"__link2-__clone72\" tabindex=\"0\" target=\"_blank\">Check errors in RFC monitor</a></td>\n<td class=\"sapMListTblCell\" headers=\"__text54\" id=\"__item73-__clone72_cell4\"><span class=\"sapMText sapMTextMaxWidth sapUiSelectable\" id=\"__text60-__clone72\" title=\"How To\">How To</span></td>\n<td class=\"sapMListTblCell\" headers=\"__text56\" id=\"__item73-__clone72_cell6\"><span class=\"sapMText sapMTextMaxWidth sapMTextNoWrap sapUiSelectable\" id=\"__text62-__clone72\"></span></td>\n<td class=\"sapMListTblNavCol\"></td>\n</tr>\n<tr class=\"sapMLIB sapMLIB-CTX sapMLIBFocusable sapMLIBShowSeparator sapMLIBTypeInactive sapMLIBUnread sapMListTblRow\" id=\"__item73-__clone73\">\n<td class=\"sapMListTblCell\" headers=\"__text50\" id=\"__item73-__clone73_cell0\"><span class=\"sapMText sapMTextMaxWidth sapMTextNoWrap sapUiSelectable\" id=\"__text57-__clone73\" title=\"Revenue Accounting Inbound Processing\">FI-RA-IP</span></td>\n<td class=\"sapMListTblCell\" headers=\"__text51\" id=\"__item73-__clone73_cell1\"><span class=\"sapMText sapMTextMaxWidth sapMTextNoWrap sapUiSelectable\" id=\"__text58-__clone73\">2564316</span></td>\n<td class=\"sapMListTblCell\" headers=\"__text53\" id=\"__item73-__clone73_cell3\"><a class=\"sapMLnk sapMLnkMaxWidth sapMLnkWrapping\" href=\"/notes/**********\" id=\"__link2-__clone73\" tabindex=\"0\" target=\"_blank\">SLG1 does not provide Source Item ID for error - FARR_RAI_PROC_LOAD</a></td>\n<td class=\"sapMListTblCell\" headers=\"__text54\" id=\"__item73-__clone73_cell4\"><span class=\"sapMText sapMTextBreakWord sapMTextMaxWidth sapUiSelectable\" id=\"__text60-__clone73\" title=\"Problem\">Problem</span></td>\n<td class=\"sapMListTblCell\" headers=\"__text56\" id=\"__item73-__clone73_cell6\"><span class=\"sapMText sapMTextMaxWidth sapMTextNoWrap sapUiSelectable\" id=\"__text62-__clone73\"></span></td>\n<td class=\"sapMListTblNavCol\"></td>\n</tr>\n<tr class=\"sapMLIB sapMLIB-CTX sapMLIBFocusable sapMLIBShowSeparator sapMLIBTypeInactive sapMListTblRow\" id=\"__item73-__clone74\">\n<td class=\"sapMListTblCell\" headers=\"__text50\" id=\"__item73-__clone74_cell0\"><span class=\"sapMText sapMTextMaxWidth sapMTextNoWrap sapUiSelectable\" id=\"__text57-__clone74\" title=\"Revenue Accounting Inbound Processing\">FI-RA-IP</span></td>\n<td class=\"sapMListTblCell\" headers=\"__text51\" id=\"__item73-__clone74_cell1\"><span class=\"sapMText sapMTextMaxWidth sapMTextNoWrap sapUiSelectable\" id=\"__text58-__clone74\">2558190</span></td>\n<td class=\"sapMListTblCell\" headers=\"__text53\" id=\"__item73-__clone74_cell3\"><a class=\"sapMLnk sapMLnkMaxWidth sapMLnkWrapping\" href=\"/notes/**********\" id=\"__link2-__clone74\" tabindex=\"0\" target=\"_blank\">Message error: Ruleset activation</a></td>\n<td class=\"sapMListTblCell\" headers=\"__text54\" id=\"__item73-__clone74_cell4\"><span class=\"sapMText sapMTextMaxWidth sapUiSelectable\" id=\"__text60-__clone74\" title=\"How To\">How To</span></td>\n<td class=\"sapMListTblCell\" headers=\"__text56\" id=\"__item73-__clone74_cell6\"><span class=\"sapMText sapMTextMaxWidth sapMTextNoWrap sapUiSelectable\" id=\"__text62-__clone74\"></span></td>\n<td class=\"sapMListTblNavCol\"></td>\n</tr>\n<tr class=\"sapMLIB sapMLIB-CTX sapMLIBFocusable sapMLIBShowSeparator sapMLIBTypeInactive sapMLIBUnread sapMListTblRow\" id=\"__item73-__clone75\">\n<td class=\"sapMListTblCell\" headers=\"__text50\" id=\"__item73-__clone75_cell0\"><span class=\"sapMText sapMTextMaxWidth sapMTextNoWrap sapUiSelectable\" id=\"__text57-__clone75\" title=\"Revenue Accounting\">FI-RA</span></td>\n<td class=\"sapMListTblCell\" headers=\"__text51\" id=\"__item73-__clone75_cell1\"><span class=\"sapMText sapMTextMaxWidth sapMTextNoWrap sapUiSelectable\" id=\"__text58-__clone75\">2555502</span></td>\n<td class=\"sapMListTblCell\" headers=\"__text53\" id=\"__item73-__clone75_cell3\"><a class=\"sapMLnk sapMLnkMaxWidth sapMLnkWrapping\" href=\"/notes/**********\" id=\"__link2-__clone75\" tabindex=\"0\" target=\"_blank\">Calculation of Recognized Revenue for Time-Based POBs</a></td>\n<td class=\"sapMListTblCell\" headers=\"__text54\" id=\"__item73-__clone75_cell4\"><span class=\"sapMText sapMTextMaxWidth sapUiSelectable\" id=\"__text60-__clone75\" title=\"How To\">How To</span></td>\n<td class=\"sapMListTblCell\" headers=\"__text56\" id=\"__item73-__clone75_cell6\"><span class=\"sapMText sapMTextMaxWidth sapMTextNoWrap sapUiSelectable\" id=\"__text62-__clone75\"></span></td>\n<td class=\"sapMListTblNavCol\"></td>\n</tr>\n<tr class=\"sapMLIB sapMLIB-CTX sapMLIBFocusable sapMLIBShowSeparator sapMLIBTypeInactive sapMLIBUnread sapMListTblRow\" id=\"__item73-__clone76\">\n<td class=\"sapMListTblCell\" headers=\"__text50\" id=\"__item73-__clone76_cell0\"><span class=\"sapMText sapMTextMaxWidth sapMTextNoWrap sapUiSelectable\" id=\"__text57-__clone76\" title=\"Revenue Accounting\">FI-RA</span></td>\n<td class=\"sapMListTblCell\" headers=\"__text51\" id=\"__item73-__clone76_cell1\"><span class=\"sapMText sapMTextMaxWidth sapMTextNoWrap sapUiSelectable\" id=\"__text58-__clone76\">2548467</span></td>\n<td class=\"sapMListTblCell\" headers=\"__text53\" id=\"__item73-__clone76_cell3\"><a class=\"sapMLnk sapMLnkMaxWidth sapMLnkWrapping\" href=\"/notes/**********\" id=\"__link2-__clone76\" tabindex=\"0\" target=\"_blank\">Using price condition as SSP</a></td>\n<td class=\"sapMListTblCell\" headers=\"__text54\" id=\"__item73-__clone76_cell4\"><span class=\"sapMText sapMTextBreakWord sapMTextMaxWidth sapUiSelectable\" id=\"__text60-__clone76\" title=\"Problem\">Problem</span></td>\n<td class=\"sapMListTblCell\" headers=\"__text56\" id=\"__item73-__clone76_cell6\"><span class=\"sapMText sapMTextMaxWidth sapMTextNoWrap sapUiSelectable\" id=\"__text62-__clone76\"></span></td>\n<td class=\"sapMListTblNavCol\"></td>\n</tr>\n<tr class=\"sapMLIB sapMLIB-CTX sapMLIBFocusable sapMLIBShowSeparator sapMLIBTypeInactive sapMLIBUnread sapMListTblRow\" id=\"__item73-__clone77\">\n<td class=\"sapMListTblCell\" headers=\"__text50\" id=\"__item73-__clone77_cell0\"><span class=\"sapMText sapMTextMaxWidth sapMTextNoWrap sapUiSelectable\" id=\"__text57-__clone77\" title=\"Revenue Accounting\">FI-RA</span></td>\n<td class=\"sapMListTblCell\" headers=\"__text51\" id=\"__item73-__clone77_cell1\"><span class=\"sapMText sapMTextMaxWidth sapMTextNoWrap sapUiSelectable\" id=\"__text58-__clone77\">2531762</span></td>\n<td class=\"sapMListTblCell\" headers=\"__text53\" id=\"__item73-__clone77_cell3\"><a class=\"sapMLnk sapMLnkMaxWidth sapMLnkWrapping\" href=\"/notes/**********\" id=\"__link2-__clone77\" tabindex=\"0\" target=\"_blank\">Excel template for FARR_RAI_SAMPLE</a></td>\n<td class=\"sapMListTblCell\" headers=\"__text54\" id=\"__item73-__clone77_cell4\"><span class=\"sapMText sapMTextMaxWidth sapUiSelectable\" id=\"__text60-__clone77\" title=\"How To\">How To</span></td>\n<td class=\"sapMListTblCell\" headers=\"__text56\" id=\"__item73-__clone77_cell6\"><span class=\"sapMText sapMTextMaxWidth sapMTextNoWrap sapUiSelectable\" id=\"__text62-__clone77\"></span></td>\n<td class=\"sapMListTblNavCol\"></td>\n</tr>\n<tr class=\"sapMLIB sapMLIB-CTX sapMLIBFocusable sapMLIBShowSeparator sapMLIBTypeInactive sapMLIBUnread sapMListTblRow\" id=\"__item73-__clone78\">\n<td class=\"sapMListTblCell\" headers=\"__text50\" id=\"__item73-__clone78_cell0\"><span class=\"sapMText sapMTextMaxWidth sapMTextNoWrap sapUiSelectable\" id=\"__text57-__clone78\" title=\"Revenue Accounting\">FI-RA</span></td>\n<td class=\"sapMListTblCell\" headers=\"__text51\" id=\"__item73-__clone78_cell1\"><span class=\"sapMText sapMTextMaxWidth sapMTextNoWrap sapUiSelectable\" id=\"__text58-__clone78\">2529488</span></td>\n<td class=\"sapMListTblCell\" headers=\"__text53\" id=\"__item73-__clone78_cell3\"><a class=\"sapMLnk sapMLnkMaxWidth sapMLnkWrapping\" href=\"/notes/**********\" id=\"__link2-__clone78\" tabindex=\"0\" target=\"_blank\">Dump: MESSAGE_TYPE_X program RK2A1000_POST</a></td>\n<td class=\"sapMListTblCell\" headers=\"__text54\" id=\"__item73-__clone78_cell4\"><span class=\"sapMText sapMTextMaxWidth sapUiSelectable\" id=\"__text60-__clone78\" title=\"Bug Filed\">Bug Filed</span></td>\n<td class=\"sapMListTblCell\" headers=\"__text56\" id=\"__item73-__clone78_cell6\"><span class=\"sapMText sapMTextMaxWidth sapMTextNoWrap sapUiSelectable\" id=\"__text62-__clone78\"></span></td>\n<td class=\"sapMListTblNavCol\"></td>\n</tr>\n<tr class=\"sapMLIB sapMLIB-CTX sapMLIBFocusable sapMLIBShowSeparator sapMLIBTypeInactive sapMLIBUnread sapMListTblRow\" id=\"__item73-__clone79\">\n<td class=\"sapMListTblCell\" headers=\"__text50\" id=\"__item73-__clone79_cell0\"><span class=\"sapMText sapMTextMaxWidth sapMTextNoWrap sapUiSelectable\" id=\"__text57-__clone79\" title=\"Revenue Accounting\">FI-RA</span></td>\n<td class=\"sapMListTblCell\" headers=\"__text51\" id=\"__item73-__clone79_cell1\"><span class=\"sapMText sapMTextMaxWidth sapMTextNoWrap sapUiSelectable\" id=\"__text58-__clone79\">2529282</span></td>\n<td class=\"sapMListTblCell\" headers=\"__text53\" id=\"__item73-__clone79_cell3\"><a class=\"sapMLnk sapMLnkMaxWidth sapMLnkWrapping\" href=\"/notes/**********\" id=\"__link2-__clone79\" tabindex=\"0\" target=\"_blank\">CLEANUP process in Revenue Accounting (RA)</a></td>\n<td class=\"sapMListTblCell\" headers=\"__text54\" id=\"__item73-__clone79_cell4\"><span class=\"sapMText sapMTextMaxWidth sapUiSelectable\" id=\"__text60-__clone79\" title=\"How To\">How To</span></td>\n<td class=\"sapMListTblCell\" headers=\"__text56\" id=\"__item73-__clone79_cell6\"><span class=\"sapMText sapMTextMaxWidth sapMTextNoWrap sapUiSelectable\" id=\"__text62-__clone79\"></span></td>\n<td class=\"sapMListTblNavCol\"></td>\n</tr>\n<tr class=\"sapMLIB sapMLIB-CTX sapMLIBFocusable sapMLIBShowSeparator sapMLIBTypeInactive sapMLIBUnread sapMListTblRow\" id=\"__item73-__clone80\">\n<td class=\"sapMListTblCell\" headers=\"__text50\" id=\"__item73-__clone80_cell0\"><span class=\"sapMText sapMTextMaxWidth sapMTextNoWrap sapUiSelectable\" id=\"__text57-__clone80\" title=\"Revenue Accounting Inbound Processing\">FI-RA-IP</span></td>\n<td class=\"sapMListTblCell\" headers=\"__text51\" id=\"__item73-__clone80_cell1\"><span class=\"sapMText sapMTextMaxWidth sapMTextNoWrap sapUiSelectable\" id=\"__text58-__clone80\">2515328</span></td>\n<td class=\"sapMListTblCell\" headers=\"__text53\" id=\"__item73-__clone80_cell3\"><a class=\"sapMLnk sapMLnkMaxWidth sapMLnkWrapping\" href=\"/notes/**********\" id=\"__link2-__clone80\" tabindex=\"0\" target=\"_blank\">Modify RAI items that have already been posted in RAR</a></td>\n<td class=\"sapMListTblCell\" headers=\"__text54\" id=\"__item73-__clone80_cell4\"><span class=\"sapMText sapMTextMaxWidth sapUiSelectable\" id=\"__text60-__clone80\" title=\"How To\">How To</span></td>\n<td class=\"sapMListTblCell\" headers=\"__text56\" id=\"__item73-__clone80_cell6\"><span class=\"sapMText sapMTextMaxWidth sapMTextNoWrap sapUiSelectable\" id=\"__text62-__clone80\"></span></td>\n<td class=\"sapMListTblNavCol\"></td>\n</tr>\n<tr class=\"sapMLIB sapMLIB-CTX sapMLIBFocusable sapMLIBShowSeparator sapMLIBTypeInactive sapMLIBUnread sapMListTblRow\" id=\"__item73-__clone81\">\n<td class=\"sapMListTblCell\" headers=\"__text50\" id=\"__item73-__clone81_cell0\"><span class=\"sapMText sapMTextMaxWidth sapMTextNoWrap sapUiSelectable\" id=\"__text57-__clone81\" title=\"Revenue Accounting Inbound Processing\">FI-RA-IP</span></td>\n<td class=\"sapMListTblCell\" headers=\"__text51\" id=\"__item73-__clone81_cell1\"><span class=\"sapMText sapMTextMaxWidth sapMTextNoWrap sapUiSelectable\" id=\"__text58-__clone81\">2512661</span></td>\n<td class=\"sapMListTblCell\" headers=\"__text53\" id=\"__item73-__clone81_cell3\"><a class=\"sapMLnk sapMLnkMaxWidth sapMLnkWrapping\" href=\"/notes/**********\" id=\"__link2-__clone81\" tabindex=\"0\" target=\"_blank\">Error: Initial Load FARR_MSG_CUSTOM*</a></td>\n<td class=\"sapMListTblCell\" headers=\"__text54\" id=\"__item73-__clone81_cell4\"><span class=\"sapMText sapMTextBreakWord sapMTextMaxWidth sapUiSelectable\" id=\"__text60-__clone81\" title=\"Problem\">Problem</span></td>\n<td class=\"sapMListTblCell\" headers=\"__text56\" id=\"__item73-__clone81_cell6\"><span class=\"sapMText sapMTextMaxWidth sapMTextNoWrap sapUiSelectable\" id=\"__text62-__clone81\"></span></td>\n<td class=\"sapMListTblNavCol\"></td>\n</tr>\n<tr class=\"sapMLIB sapMLIB-CTX sapMLIBFocusable sapMLIBShowSeparator sapMLIBTypeInactive sapMLIBUnread sapMListTblRow\" id=\"__item73-__clone82\">\n<td class=\"sapMListTblCell\" headers=\"__text50\" id=\"__item73-__clone82_cell0\"><span class=\"sapMText sapMTextMaxWidth sapMTextNoWrap sapUiSelectable\" id=\"__text57-__clone82\" title=\"Revenue Accounting\">FI-RA</span></td>\n<td class=\"sapMListTblCell\" headers=\"__text51\" id=\"__item73-__clone82_cell1\"><span class=\"sapMText sapMTextMaxWidth sapMTextNoWrap sapUiSelectable\" id=\"__text58-__clone82\">2507379</span></td>\n<td class=\"sapMListTblCell\" headers=\"__text53\" id=\"__item73-__clone82_cell3\"><a class=\"sapMLnk sapMLnkMaxWidth sapMLnkWrapping\" href=\"/notes/**********\" id=\"__link2-__clone82\" tabindex=\"0\" target=\"_blank\">Netweaver business client. Calculate Time-Based Revenue 1.3 features</a></td>\n<td class=\"sapMListTblCell\" headers=\"__text54\" id=\"__item73-__clone82_cell4\"><span class=\"sapMText sapMTextMaxWidth sapUiSelectable\" id=\"__text60-__clone82\" title=\"How To\">How To</span></td>\n<td class=\"sapMListTblCell\" headers=\"__text56\" id=\"__item73-__clone82_cell6\"><span class=\"sapMText sapMTextMaxWidth sapMTextNoWrap sapUiSelectable\" id=\"__text62-__clone82\"></span></td>\n<td class=\"sapMListTblNavCol\"></td>\n</tr>\n<tr class=\"sapMLIB sapMLIB-CTX sapMLIBFocusable sapMLIBShowSeparator sapMLIBTypeInactive sapMLIBUnread sapMListTblRow\" id=\"__item73-__clone83\">\n<td class=\"sapMListTblCell\" headers=\"__text50\" id=\"__item73-__clone83_cell0\"><span class=\"sapMText sapMTextMaxWidth sapMTextNoWrap sapUiSelectable\" id=\"__text57-__clone83\" title=\"Revenue Accounting\">FI-RA</span></td>\n<td class=\"sapMListTblCell\" headers=\"__text51\" id=\"__item73-__clone83_cell1\"><span class=\"sapMText sapMTextMaxWidth sapMTextNoWrap sapUiSelectable\" id=\"__text58-__clone83\">2504924</span></td>\n<td class=\"sapMListTblCell\" headers=\"__text53\" id=\"__item73-__clone83_cell3\"><a class=\"sapMLnk sapMLnkMaxWidth sapMLnkWrapping\" href=\"/notes/**********\" id=\"__link2-__clone83\" tabindex=\"0\" target=\"_blank\">Flag \"Relevant for Revenue Accounting\" not activated</a></td>\n<td class=\"sapMListTblCell\" headers=\"__text54\" id=\"__item73-__clone83_cell4\"><span class=\"sapMText sapMTextBreakWord sapMTextMaxWidth sapUiSelectable\" id=\"__text60-__clone83\" title=\"Problem\">Problem</span></td>\n<td class=\"sapMListTblCell\" headers=\"__text56\" id=\"__item73-__clone83_cell6\"><span class=\"sapMText sapMTextMaxWidth sapMTextNoWrap sapUiSelectable\" id=\"__text62-__clone83\"></span></td>\n<td class=\"sapMListTblNavCol\"></td>\n</tr>\n<tr class=\"sapMLIB sapMLIB-CTX sapMLIBFocusable sapMLIBShowSeparator sapMLIBTypeInactive sapMLIBUnread sapMListTblRow\" id=\"__item73-__clone84\">\n<td class=\"sapMListTblCell\" headers=\"__text50\" id=\"__item73-__clone84_cell0\"><span class=\"sapMText sapMTextMaxWidth sapMTextNoWrap sapUiSelectable\" id=\"__text57-__clone84\" title=\"Revenue Accounting\">FI-RA</span></td>\n<td class=\"sapMListTblCell\" headers=\"__text51\" id=\"__item73-__clone84_cell1\"><span class=\"sapMText sapMTextMaxWidth sapMTextNoWrap sapUiSelectable\" id=\"__text58-__clone84\">2502593</span></td>\n<td class=\"sapMListTblCell\" headers=\"__text53\" id=\"__item73-__clone84_cell3\"><a class=\"sapMLnk sapMLnkMaxWidth sapMLnkWrapping\" href=\"/notes/**********\" id=\"__link2-__clone84\" tabindex=\"0\" target=\"_blank\">Revenue Accounting and Reporting - General topics</a></td>\n<td class=\"sapMListTblCell\" headers=\"__text54\" id=\"__item73-__clone84_cell4\"><span class=\"sapMText sapMTextMaxWidth sapUiSelectable\" id=\"__text60-__clone84\" title=\"How To\">How To</span></td>\n<td class=\"sapMListTblCell\" headers=\"__text56\" id=\"__item73-__clone84_cell6\"><span class=\"sapMText sapMTextMaxWidth sapMTextNoWrap sapUiSelectable\" id=\"__text62-__clone84\"></span></td>\n<td class=\"sapMListTblNavCol\"></td>\n</tr>\n<tr class=\"sapMLIB sapMLIB-CTX sapMLIBFocusable sapMLIBShowSeparator sapMLIBTypeInactive sapMLIBUnread sapMListTblRow\" id=\"__item73-__clone85\">\n<td class=\"sapMListTblCell\" headers=\"__text50\" id=\"__item73-__clone85_cell0\"><span class=\"sapMText sapMTextMaxWidth sapMTextNoWrap sapUiSelectable\" id=\"__text57-__clone85\" title=\"Revenue Accounting\">FI-RA</span></td>\n<td class=\"sapMListTblCell\" headers=\"__text51\" id=\"__item73-__clone85_cell1\"><span class=\"sapMText sapMTextMaxWidth sapMTextNoWrap sapUiSelectable\" id=\"__text58-__clone85\">2502585</span></td>\n<td class=\"sapMListTblCell\" headers=\"__text53\" id=\"__item73-__clone85_cell3\"><a class=\"sapMLnk sapMLnkMaxWidth sapMLnkWrapping\" href=\"/notes/**********\" id=\"__link2-__clone85\" tabindex=\"0\" target=\"_blank\">No company codes of the migration package are in status 'Migration'</a></td>\n<td class=\"sapMListTblCell\" headers=\"__text54\" id=\"__item73-__clone85_cell4\"><span class=\"sapMText sapMTextMaxWidth sapUiSelectable\" id=\"__text60-__clone85\" title=\"How To\">How To</span></td>\n<td class=\"sapMListTblCell\" headers=\"__text56\" id=\"__item73-__clone85_cell6\"><span class=\"sapMText sapMTextMaxWidth sapMTextNoWrap sapUiSelectable\" id=\"__text62-__clone85\"></span></td>\n<td class=\"sapMListTblNavCol\"></td>\n</tr>\n<tr class=\"sapMLIB sapMLIB-CTX sapMLIBFocusable sapMLIBShowSeparator sapMLIBTypeInactive sapMLIBUnread sapMListTblRow\" id=\"__item73-__clone86\">\n<td class=\"sapMListTblCell\" headers=\"__text50\" id=\"__item73-__clone86_cell0\"><span class=\"sapMText sapMTextMaxWidth sapMTextNoWrap sapUiSelectable\" id=\"__text57-__clone86\" title=\"Revenue Accounting Inbound Processing\">FI-RA-IP</span></td>\n<td class=\"sapMListTblCell\" headers=\"__text51\" id=\"__item73-__clone86_cell1\"><span class=\"sapMText sapMTextMaxWidth sapMTextNoWrap sapUiSelectable\" id=\"__text58-__clone86\">2501037</span></td>\n<td class=\"sapMListTblCell\" headers=\"__text53\" id=\"__item73-__clone86_cell3\"><a class=\"sapMLnk sapMLnkMaxWidth sapMLnkWrapping\" href=\"/notes/**********\" id=\"__link2-__clone86\" tabindex=\"0\" target=\"_blank\">ERROR: The SM59 alias \"FICA_SYSTEM_FOR_REVREC\"</a></td>\n<td class=\"sapMListTblCell\" headers=\"__text54\" id=\"__item73-__clone86_cell4\"><span class=\"sapMText sapMTextBreakWord sapMTextMaxWidth sapUiSelectable\" id=\"__text60-__clone86\" title=\"Problem\">Problem</span></td>\n<td class=\"sapMListTblCell\" headers=\"__text56\" id=\"__item73-__clone86_cell6\"><span class=\"sapMText sapMTextMaxWidth sapMTextNoWrap sapUiSelectable\" id=\"__text62-__clone86\"></span></td>\n<td class=\"sapMListTblNavCol\"></td>\n</tr>\n<tr class=\"sapMLIB sapMLIB-CTX sapMLIBFocusable sapMLIBShowSeparator sapMLIBTypeInactive sapMListTblRow\" id=\"__item73-__clone87\">\n<td class=\"sapMListTblCell\" headers=\"__text50\" id=\"__item73-__clone87_cell0\"><span class=\"sapMText sapMTextMaxWidth sapMTextNoWrap sapUiSelectable\" id=\"__text57-__clone87\" title=\"Revenue Accounting Inbound Processing\">FI-RA-IP</span></td>\n<td class=\"sapMListTblCell\" headers=\"__text51\" id=\"__item73-__clone87_cell1\"><span class=\"sapMText sapMTextMaxWidth sapMTextNoWrap sapUiSelectable\" id=\"__text58-__clone87\">2496728</span></td>\n<td class=\"sapMListTblCell\" headers=\"__text53\" id=\"__item73-__clone87_cell3\"><a class=\"sapMLnk sapMLnkMaxWidth sapMLnkWrapping\" href=\"/notes/**********\" id=\"__link2-__clone87\" tabindex=\"0\" target=\"_blank\">Customizing for RAI monitor</a></td>\n<td class=\"sapMListTblCell\" headers=\"__text54\" id=\"__item73-__clone87_cell4\"><span class=\"sapMText sapMTextMaxWidth sapUiSelectable\" id=\"__text60-__clone87\" title=\"How To\">How To</span></td>\n<td class=\"sapMListTblCell\" headers=\"__text56\" id=\"__item73-__clone87_cell6\"><span class=\"sapMText sapMTextMaxWidth sapMTextNoWrap sapUiSelectable\" id=\"__text62-__clone87\"></span></td>\n<td class=\"sapMListTblNavCol\"></td>\n</tr>\n<tr class=\"sapMLIB sapMLIB-CTX sapMLIBFocusable sapMLIBShowSeparator sapMLIBTypeInactive sapMListTblRow\" id=\"__item73-__clone88\">\n<td class=\"sapMListTblCell\" headers=\"__text50\" id=\"__item73-__clone88_cell0\"><span class=\"sapMText sapMTextMaxWidth sapMTextNoWrap sapUiSelectable\" id=\"__text57-__clone88\" title=\"Revenue Accounting Inbound Processing\">FI-RA-IP</span></td>\n<td class=\"sapMListTblCell\" headers=\"__text51\" id=\"__item73-__clone88_cell1\"><span class=\"sapMText sapMTextMaxWidth sapMTextNoWrap sapUiSelectable\" id=\"__text58-__clone88\">2496671</span></td>\n<td class=\"sapMListTblCell\" headers=\"__text53\" id=\"__item73-__clone88_cell3\"><a class=\"sapMLnk sapMLnkMaxWidth sapMLnkWrapping\" href=\"/notes/**********\" id=\"__link2-__clone88\" tabindex=\"0\" target=\"_blank\">Error: No items found entering RAI monitor</a></td>\n<td class=\"sapMListTblCell\" headers=\"__text54\" id=\"__item73-__clone88_cell4\"><span class=\"sapMText sapMTextBreakWord sapMTextMaxWidth sapUiSelectable\" id=\"__text60-__clone88\" title=\"Problem\">Problem</span></td>\n<td class=\"sapMListTblCell\" headers=\"__text56\" id=\"__item73-__clone88_cell6\"><span class=\"sapMText sapMTextMaxWidth sapMTextNoWrap sapUiSelectable\" id=\"__text62-__clone88\"></span></td>\n<td class=\"sapMListTblNavCol\"></td>\n</tr>\n<tr class=\"sapMLIB sapMLIB-CTX sapMLIBFocusable sapMLIBShowSeparator sapMLIBTypeInactive sapMLIBUnread sapMListTblRow\" id=\"__item73-__clone89\">\n<td class=\"sapMListTblCell\" headers=\"__text50\" id=\"__item73-__clone89_cell0\"><span class=\"sapMText sapMTextMaxWidth sapMTextNoWrap sapUiSelectable\" id=\"__text57-__clone89\" title=\"Revenue Accounting Inbound Processing\">FI-RA-IP</span></td>\n<td class=\"sapMListTblCell\" headers=\"__text51\" id=\"__item73-__clone89_cell1\"><span class=\"sapMText sapMTextMaxWidth sapMTextNoWrap sapUiSelectable\" id=\"__text58-__clone89\">2492885</span></td>\n<td class=\"sapMListTblCell\" headers=\"__text53\" id=\"__item73-__clone89_cell3\"><a class=\"sapMLnk sapMLnkMaxWidth sapMLnkWrapping\" href=\"/notes/**********\" id=\"__link2-__clone89\" tabindex=\"0\" target=\"_blank\">Delete information in the RAI monitor</a></td>\n<td class=\"sapMListTblCell\" headers=\"__text54\" id=\"__item73-__clone89_cell4\"><span class=\"sapMText sapMTextMaxWidth sapUiSelectable\" id=\"__text60-__clone89\" title=\"How To\">How To</span></td>\n<td class=\"sapMListTblCell\" headers=\"__text56\" id=\"__item73-__clone89_cell6\"><span class=\"sapMText sapMTextMaxWidth sapMTextNoWrap sapUiSelectable\" id=\"__text62-__clone89\"></span></td>\n<td class=\"sapMListTblNavCol\"></td>\n</tr>\n<tr class=\"sapMLIB sapMLIB-CTX sapMLIBFocusable sapMLIBShowSeparator sapMLIBTypeInactive sapMLIBUnread sapMListTblRow\" id=\"__item73-__clone90\">\n<td class=\"sapMListTblCell\" headers=\"__text50\" id=\"__item73-__clone90_cell0\"><span class=\"sapMText sapMTextMaxWidth sapMTextNoWrap sapUiSelectable\" id=\"__text57-__clone90\" title=\"Revenue Accounting\">FI-RA</span></td>\n<td class=\"sapMListTblCell\" headers=\"__text51\" id=\"__item73-__clone90_cell1\"><span class=\"sapMText sapMTextMaxWidth sapMTextNoWrap sapUiSelectable\" id=\"__text58-__clone90\">2483797</span></td>\n<td class=\"sapMListTblCell\" headers=\"__text53\" id=\"__item73-__clone90_cell3\"><a class=\"sapMLnk sapMLnkMaxWidth sapMLnkWrapping\" href=\"/notes/**********\" id=\"__link2-__clone90\" tabindex=\"0\" target=\"_blank\">Revenue posting on specific period of time basis</a></td>\n<td class=\"sapMListTblCell\" headers=\"__text54\" id=\"__item73-__clone90_cell4\"><span class=\"sapMText sapMTextBreakWord sapMTextMaxWidth sapUiSelectable\" id=\"__text60-__clone90\" title=\"Problem\">Problem</span></td>\n<td class=\"sapMListTblCell\" headers=\"__text56\" id=\"__item73-__clone90_cell6\"><span class=\"sapMText sapMTextMaxWidth sapMTextNoWrap sapUiSelectable\" id=\"__text62-__clone90\"></span></td>\n<td class=\"sapMListTblNavCol\"></td>\n</tr>\n<tr class=\"sapMLIB sapMLIB-CTX sapMLIBFocusable sapMLIBShowSeparator sapMLIBTypeInactive sapMLIBUnread sapMListTblRow\" id=\"__item73-__clone91\">\n<td class=\"sapMListTblCell\" headers=\"__text50\" id=\"__item73-__clone91_cell0\"><span class=\"sapMText sapMTextMaxWidth sapMTextNoWrap sapUiSelectable\" id=\"__text57-__clone91\" title=\"Revenue Accounting Inbound Processing\">FI-RA-IP</span></td>\n<td class=\"sapMListTblCell\" headers=\"__text51\" id=\"__item73-__clone91_cell1\"><span class=\"sapMText sapMTextMaxWidth sapMTextNoWrap sapUiSelectable\" id=\"__text58-__clone91\">2483334</span></td>\n<td class=\"sapMListTblCell\" headers=\"__text53\" id=\"__item73-__clone91_cell3\"><a class=\"sapMLnk sapMLnkMaxWidth sapMLnkWrapping\" href=\"/notes/**********\" id=\"__link2-__clone91\" tabindex=\"0\" target=\"_blank\">Error FARR_RAI088 during data migration and FARR_RAI651 during process a RAI</a></td>\n<td class=\"sapMListTblCell\" headers=\"__text54\" id=\"__item73-__clone91_cell4\"><span class=\"sapMText sapMTextMaxWidth sapUiSelectable\" id=\"__text60-__clone91\" title=\"How To\">How To</span></td>\n<td class=\"sapMListTblCell\" headers=\"__text56\" id=\"__item73-__clone91_cell6\"><span class=\"sapMText sapMTextMaxWidth sapMTextNoWrap sapUiSelectable\" id=\"__text62-__clone91\"></span></td>\n<td class=\"sapMListTblNavCol\"></td>\n</tr>\n<tr class=\"sapMLIB sapMLIB-CTX sapMLIBFocusable sapMLIBShowSeparator sapMLIBTypeInactive sapMListTblRow\" id=\"__item73-__clone92\">\n<td class=\"sapMListTblCell\" headers=\"__text50\" id=\"__item73-__clone92_cell0\"><span class=\"sapMText sapMTextMaxWidth sapMTextNoWrap sapUiSelectable\" id=\"__text57-__clone92\" title=\"SAP Support Portal - Software Distribution Center\">XX-SER-SAPSMP-SWC</span></td>\n<td class=\"sapMListTblCell\" headers=\"__text51\" id=\"__item73-__clone92_cell1\"><span class=\"sapMText sapMTextMaxWidth sapMTextNoWrap sapUiSelectable\" id=\"__text58-__clone92\">2380297</span></td>\n<td class=\"sapMListTblCell\" headers=\"__text53\" id=\"__item73-__clone92_cell3\"><a class=\"sapMLnk sapMLnkMaxWidth sapMLnkWrapping\" href=\"/notes/**********\" id=\"__link2-__clone92\" tabindex=\"0\" target=\"_blank\">Cannot Download SAP Revenue Accounting and Reporting 1.2 (REVREC120) add-on - SAP ONE Support Launchpad</a></td>\n<td class=\"sapMListTblCell\" headers=\"__text54\" id=\"__item73-__clone92_cell4\"><span class=\"sapMText sapMTextBreakWord sapMTextMaxWidth sapUiSelectable\" id=\"__text60-__clone92\" title=\"Problem\">Problem</span></td>\n<td class=\"sapMListTblCell\" headers=\"__text56\" id=\"__item73-__clone92_cell6\"><span class=\"sapMText sapMTextMaxWidth sapMTextNoWrap sapUiSelectable\" id=\"__text62-__clone92\"></span></td>\n</tr>\n</tbody>\n</table></div>", "noteVersion": 30, "refer_note": [{"note": "2703761", "noteTitle": "2703761 - SAP Notes for SD-BIL-RA (2021 September)", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are using the SAP Revenue Accounting and Reporting sales integration and you wish to be up-to-date about the latest corrections and new functionalities available for the integration.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>SD Revenue Accounting, SAP RAR, Core changes, SDIC, SD-IC, FARRIC, SD Core, notes, corrections, required notes, REVRECSD, consulting, core corrections</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>This SAP Note is meant as an additional guidance to help product experts, consultants and system administrators to keep track of the changes in the Revenue Accounting &amp; Reporting sales integration.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>The attached excel file contains a filterable list of SAP Notes which can be used to easily find the relevant corrections for your software release. It is highly recommended to install all SAP Notes listed in the excel as they ensure the stability of the sales integration.</p>\n<p>There are three tabs in the attached document:</p>\n<ol>\n<li><strong>Corrections</strong>: SAP Notes which contain source code changes for the REVRECSD software component<br/><br/></li>\n<li><strong>Core corrections</strong>: SAP Notes which contain source code changes for the SAP_APPL or S4CORE software component<br/><br/></li>\n<li><strong>Consulting</strong>: SAP Notes which contain additional generic information like limitations, FAQs or special consulting topics</li>\n</ol>\n<p>Using the \"<em>Note Category\"</em> columns you can search specifically for \"<em>New functionalities and logic changes</em>\" so you do not miss any important changes or new features during a Support Package upgrade. You can also look directly for \"<em>Performance optimizations</em>\" or use the \"<em>Operational load\"</em> filter to skip the migration-related corrections if your business has already completed the migration projects.</p>\n<p>You should keep using the SAP Support Portal as the main go-to portal for information about available corrections and news because this SAP Note is only updated at irregular intervals.</p>", "noteVersion": 7}, {"note": "2591055", "noteTitle": "2591055 - Functional limitations in the SD Integration Component", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>This SAP Note contains information about known restrictions of SAP Sales Integration with SAP Revenue Accounting and Reporting which is also known as the RAR SD Integration Component.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>SD Revenue Accounting, functional scope, restriction, limitation, not supported, unsupported, scenario, functionality, REVRECSD, negative list, industry solutions, IS-HT, IS-HT-SW-RR</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>The clear list of unsupported processes shall help you to better understand the impacts of a revenue accounting implementation project on your existing SD business processes. These processes cannot be implemented correctly with revenue accounting, therefore these must be either excluded from revenue accounting or changed to alternative processes that are supported.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<ul></ul>\n<p><span><strong>Obsolete restrictions:</strong></span></p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"3\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td><strong>Functionality</strong></td>\n<td><strong>SAP Note(s)</strong></td>\n<td><strong>SAP Help</strong></td>\n<td><strong>REVRECSD</strong></td>\n<td><strong>REVREC1.3</strong></td>\n<td><strong><strong>S4CORE1.3</strong></strong></td>\n<td><strong>Remarks</strong></td>\n</tr>\n<tr>\n<td>Milestone billing plan processes</td>\n<td>2325952</td>\n<td>-</td>\n<td>SP09 </td>\n<td>SP0</td>\n<td>SP0</td>\n<td>Must be activated in the customizing</td>\n</tr>\n<tr>\n<td>Service acceptance processes</td>\n<td>2371167</td>\n<td>-</td>\n<td>SP10</td>\n<td>SP0</td>\n<td>SP0</td>\n<td>Must be activated in the customizing</td>\n</tr>\n<tr>\n<td>Batch split processes</td>\n<td>2602146</td>\n<td>-</td>\n<td>SP16</td>\n<td>SP0</td>\n<td>SP0</td>\n<td>-</td>\n</tr>\n<tr>\n<td>Migration: SD Revenue Recognition with FASB52</td>\n<td>2607183</td>\n<td>-</td>\n<td>SP16</td>\n<td>SP0</td>\n<td>SP0</td>\n<td>-</td>\n</tr>\n<tr>\n<td>Third-Party / Drop shipment process</td>\n<td>2651782, 2649839, 2654138 </td>\n<td><a href=\"https://help.sap.com/viewer/de5c3a7ada6e41d482ce9ecdbd93aba1/1.3%20FP06/en-US/633ccf7a0c3648aa8c9544c7f7dee434.html\" target=\"_blank\">link</a></td>\n<td>SP17</td>\n<td>SP6</td>\n<td>SP1</td>\n<td>Must be activated in the customizing</td>\n</tr>\n<tr>\n<td>Internal cost in intercompany processes</td>\n<td>2651782, 2649839, 2654138</td>\n<td><a href=\"https://help.sap.com/viewer/de5c3a7ada6e41d482ce9ecdbd93aba1/1.3%20FP06/en-US/0d405c00f0f643119e2b7d7d92305350.html\" target=\"_blank\">link</a></td>\n<td>SP17</td>\n<td>SP6</td>\n<td>SP1</td>\n<td>Must be activated in the customizing; only 1 condition</td>\n</tr>\n<tr>\n<td>Intercompany billing documents </td>\n<td>2651782, 2649839, 2654138</td>\n<td><a href=\"https://help.sap.com/viewer/de5c3a7ada6e41d482ce9ecdbd93aba1/1.3%20FP06/en-US/0d405c00f0f643119e2b7d7d92305350.html\" target=\"_blank\">link</a></td>\n<td>SP17 </td>\n<td>SP6</td>\n<td>SP1</td>\n<td>Must be activated in the customizing</td>\n</tr>\n<tr>\n<td>Overfulfullment during returns processing</td>\n<td>2623625</td>\n<td>-</td>\n<td>-</td>\n<td>SP6</td>\n<td>SP1</td>\n<td>-</td>\n</tr>\n<tr>\n<td>Proof of Delivery</td>\n<td>2691932, 2698524, 2702194, 2715963, 2726341, 2694114</td>\n<td><a href=\"https://help.sap.com/viewer/de5c3a7ada6e41d482ce9ecdbd93aba1/1.3%20FP07/en-US/b1037294bcb748899b98377ac369f85a.html\" target=\"_blank\">link</a></td>\n<td>SP18</td>\n<td>SP7</td>\n<td>SP1</td>\n<td>Must be activated in the customizing</td>\n</tr>\n<tr>\n<td>Stock in Transit</td>\n<td>2691932</td>\n<td><a href=\"https://help.sap.com/viewer/de5c3a7ada6e41d482ce9ecdbd93aba1/1.3%20FP07/en-US/540652b8d6fa4005b4272059cc355989.html\" target=\"_blank\">link</a></td>\n<td>SP18</td>\n<td>SP7</td>\n<td>SP1</td>\n<td>Must be activated in the customizing</td>\n</tr>\n<tr>\n<td>\n<p>Contract Call-Offs</p>\n</td>\n<td>2711655, 2735272</td>\n<td><a href=\"https://help.sap.com/viewer/de5c3a7ada6e41d482ce9ecdbd93aba1/1.3%20FP07/en-US/1225fff336f341989254820ff84f4466.html\" target=\"_blank\">link</a></td>\n<td>SP18</td>\n<td>SP7</td>\n<td>SP1</td>\n<td>Must be activated in the customizing</td>\n</tr>\n</tbody>\n</table></div>\n<p><span><strong>Active restrictions:</strong></span></p>\n<p>1.) The business scenarios and/or processes in the following list are currently <strong>not supported</strong>:</p>\n<ul>\n<li>Rebate processes</li>\n<li>Self-billing and retroactive billing (in scheduling agreement processes)</li>\n<li>Consignment processes</li>\n<li>Cost split into material ledger categories (S/4HANA functionality)</li>\n<li>New invoice cancellation procedure in SD (SAP Note 1259505)</li>\n<li>Return processes with higher order quantity than the referenced performance obligation´s order quantity</li>\n<li>Revenue account determination using batch number in batch split processes is not supported as the reference account is required during the creation of the performance obligation where the batch number is not yet known</li>\n<li>Retrospective cost updates in billing documents with SDVPRSUPDATE cannot be sent to RAR (SAP Note 2716435)</li>\n<li>Subsequent credit and subsequent debit documents have no impact on the fulfillment costs in the third-party (drop-shipment) business scenario, these documents do not generate SDFI \"PI\" RAIs (<strong>obsolete restriction</strong> since SAP Note 2992437)</li>\n<li>Bill of Material scenarios are only available with restrictions (SAP Note 2624932)</li>\n<li>Inter-company stock transfer orders (STO)</li>\n<li>Performance obligations created by invoices (FARR_RELTYPE = G) cannot generate SDFI RAIs neither from deliveries nor from proof-of-deliveries (SAP Note 2719185)</li>\n<li>Fulfillment costs cannot be created by non-valuated goods movements. For example return processes utilizing movement type 651 cannot send negative cost conditions with their SDFI RAIs because these movements do not create an accounting document.</li>\n<li>Advanced Intercompany Sales processes in S/4HANA</li>\n<li>Intercompany Stock Transfer in S/4HANA</li>\n<li>Value Chain Monitoring Framework (VCM) for SAP S/4HANA</li>\n</ul>\n<p>2.) The documents and/or conditions in the following list are currently <strong>ignored</strong> in SD processes:</p>\n<ul>\n<li>Down payment documents</li>\n<li>Proforma invoices </li>\n<li>Transfer prices (KNTYP = b)</li>\n<li>Intercompany billing prices (KNTYP = I)</li>\n<li>Costing conditions (KNTYP = Q)</li>\n<li>Standard costs (KNTYP = S)</li>\n<li>Moving costs (KNTYP = T)</li>\n<li>Profic center costs (KNTYP = h)</li>\n<li>Taxes (KOAID = D)</li>\n</ul>\n<p><em>*As of SAP Note 2651782 costs marked with the \"intercompany billing\" condition flag can be processed regardless of their condition type (See obsolete restrictions section)</em></p>\n<p>3.) During operational load (migration) the scenarios, processes and documents that are listed in <strong>1.) and 2.) should generally not be migrated</strong> as these do not work with Revenue Accounting in the current release. In addition to those points there are further restrictions in place, due to which the following processes are also <strong>not migrated</strong>:</p>\n<ul>\n<li>Migration of any process other than time based &amp; billing related revenue recognition (type \"D\") into invoice-generated performance obligations (SDIG) is not possible</li>\n<li>Sales inquiries and quotations </li>\n<li>Processes where the billing document currency is different than the sales document currency</li>\n<li>SD Rev. Rec. specific: Manually completed contracts (transaction V_MACO)</li>\n<li>SD Rev. Rec. specific: Event-based processes (customer acceptance, customer-specific event)</li>\n<li>SD Rev. Rec. specific: SD Rev. Rec. items in S4CORE systems (These documents can only be migrated from a SAP_APPL system)</li>\n<li>SD Rev. Rec. specific: Performance-based processes where a price change results in less/more revenue realized than the actual contract value cannot be migrated correctly (RAR data can be corrected by SAP Support if an in-flight check error is encountered)</li>\n<li>SD Rev. Rec. specific: IS-Media specific processes</li>\n<li>SD Rev. Rec. specific: Processes containing non-view relevant revenue lines (Posted before 2011/2012)</li>\n<li>SD Rev. Rec. specific: Inconsistent processes (Transaction VF47)</li>\n</ul>\n<p>*<em>Starting with RAR 1.3 FP7 and corresponding support packages of the SD integration component, you can now also migrate sales orders or contracts from SD Revenue Recognition of Revenue Recognition Category D (Billing-related, time-related revenue recognition) to Revenue Accounting. This feature is available with note <a href=\"/notes/2719185\" target=\"_blank\">2719185</a> and the referenced notes therein.</em></p>\n<p>4.) Industry solutions which influence the standard Sales&amp;Distribution processes (e.g.: IS-OIL, IS-ADEC-BCQ) were not validated or released, therefore these are also <strong>not supported </strong>in the integration component. If you are planning to combine the SAP Sales Integration with SAP Revenue Accounting and Reporting function with other SAP industry solutions, direct your request to the SAP User Groups or to the responsible SAP Solution Manager.</p>", "noteVersion": 22}, {"note": "2551667", "noteTitle": "2551667 - SAP Revenue and Reporting: Special Considerations for Large Number of POBs/defitems in one Revenue Accounting Contract", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You implement SAP Revenue Accounting and Reporting and want to understand the sizing and performance impact of the add-on.</p>\n<p>Especially you have scenarios where one contract may contain more than 1,000 POBs or more than 100,000 deferral items.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Sizing, Performance, <span 'times=\"\" ar-sa;\"=\"\" calibri',sans-serif;=\"\" en-us;=\"\" lang=\"EN-US\" mso-ansi-language:=\"\" mso-bidi-font-family:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" new=\"\" roman';=\"\">TSV_TNEW_PAGE_ALLOC_FAILED, read_single_defitem, read_defitem_of_pob</span></p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>The solution has been tested in the SAP Labs environment with contracts containing <strong>up to 1,000 POBs</strong> each. It shows a linear behaviour with regards to n; n being the maximum number of POBs in a contract. This linear behaviour is also used in the formulas for disk and CPU sizing.</p>\n<p>Due to revenue allocations etc. to be performed as part the regular Revenue Accounting process, in tests the solution has been shown to no longer react linearly for contracts with more than 1,000 POBs in a single contract. In the region <strong>between 1,000 and 10,000 POBs</strong> per contract applying an n log n formula provides more accurate sizing results. If your scenario includes contracts with more than 1,000 POBs each, insert in the CPU sizing n log n instead of n as numbers for orders, invoices and fulfilments.</p>\n<p>For scenarios with contracts with <strong>more than 10,000 POBs</strong>, sufficient experience is not available. As a result, such scenarios need to be validated as part of the customer implementation to ensure performance requirements can be met. Depending on customer requirements, certain scenarios in this context may not be supported. SAP strongly suggests keeping the number of POBs in a single contract at a minimum and the solution has proven to perform best with contracts with up to 10 POBs.</p>\n<p>Customers are advised to perform <strong><span>focused tests on large contracts exceeding 1,000 POBs</span></strong>. In extreme cases large contracts may be necessary to be processed outside of the RAR solution.</p>\n<p>In case of an integrated solution where the data is migrated out of other systems it is recommended to enable control over the contract size and growth and in extreme cases avoid migrating these contracts into SAP RAR.</p>\n<p>Please keep in mind special considerations for the large contracts in the sizing guide. For further details on CPU sizing and Disk sizing please visit the SAP Help Portal:</p>\n<p><a href=\"https://www.sap.com/about/benchmark/sizing.html\" target=\"_blank\">https://www.sap.com/about/benchmark/sizing.html</a></p>\n<p>Sizing Guidelines -&gt; SAP Business Suite Applications -&gt; SAP ERP -&gt; Sizing Guideline for SAP Revenue Accounting and Reporting 1.3.</p>\n<p><span><strong>Special considerations for large number of deferral items</strong></span></p>\n<p>Additional attention should be paid to the number of deferral items in a contract. Focused performance tests have shown that, for contracts with more than 100,000 defitems, it can take many minutes to perform basic tasks - activities like displaying the contract's POB structure, editing the POBs in Comprehensive View, or combining contracts. The DB table farr_d_defitem is a central table in RAR, responsible for revenue recognition and schedule. As such, it is searched many times during any contract activity, oftentimes inside a POB loop. This slows down the system when the defitem table, in combination with the POB table, is too large.</p>\n<p>For these reasons it is recommended to keep the number of defitems low, and assess the expected size of the defitem table in advance. The defitem table will grow extra large in these situations:</p>\n<ol>\n<li>When there is a lot of time-based POBs with long durations in the contract.</li>\n<li>When there is a large amount of fulfillments for each POB.</li>\n<li>When posting programs (A/B/C) are run too often. These programs close the reconcilliation keys associated to defitems, so new defitems have to be created every time the programs are run. It needs to be noted that RAR was not created as a real-time system, so it is expected that generally these programs will be run only a few times a month.</li>\n</ol>\n<p> </p>", "noteVersion": 5}, {"note": "2650545", "noteTitle": "2650545 - Consulting: POB start date and end date determination in SDOI RAIs", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are using the SAP RAR SD integration component to generate performance obligations from sales document items. You wish to know how the POB start date and end date is determined during the SDOI RAI creation.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>SD Reveue Accounting, POB, start, end, date, BADI, billing plan, fplt, veda</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>Consulting</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong>Default logic</strong></p>\n<p><em>POB start and end dates</em> are per default populated during the SDOI RAI main item processing from the sales document´s contract data tab. If the sales document type does not allow the presence of contract data or the relevant fields are not filled out in the document, then the POB start and end date fields are not populated here.</p>\n<ul>\n<li>The <em>POB start date</em> is taken from the \"Contract start date\" field (VEDA-VBEGDAT)</li>\n<li>The <em>POB end date</em> is taken from the \"Contract end date\" field (VEDA-VENDDAT)</li>\n</ul>\n<p>Note: Customizing transaction VOV8 can be used to enable the contract data in a given sales document type. (Contract data allwd. = \"X\" or \"Y\")</p>\n<p><strong>Billing plan logic</strong></p>\n<p>Billing plans have a special logic when it comes to <em>POB start and end dates</em> which overrules the default start/end dates coming from the contract data. Each billing plan line gets in internal \"<em>start date</em>\" and \"<em>end date</em>\" assigned. In case the billing \"In Advance\" flag is set the earlier settlement deadline date is saved into the settlement deadline date FPLT-FKDAT and the later date into the settlement deadline date FPLT-NFDAT. Otherwise the other way around. The internal <em>start date</em> is always the earlier of these two dates, wheras the <em>end date</em> is always the later one.</p>\n<p><span>Formular representation:</span></p>\n<ul>\n<li>Billing plan item start date: <strong>min</strong>( settlement deadline date 1 (FPLT-NFDAT); settlement deadline date 2 (FPLT-FKDAT) )</li>\n<li>Billing plan item end date: <strong>max</strong>( settlement deadline date 1 (FPLT-NFDAT); settlement deadline date 2 (FPLT-FKDAT) )</li>\n</ul>\n<p>Then the earliest start date and the latest end date are determined based on all billing plan lines which are used to redetermine the <em>POB start date and end date</em> fields using the following logic. The <em>POB start date</em> is always set as the earliest start date of all billing plan items. The <em>POB end date</em> is however influenced by the automatic creation of correction dates setting used in the billing plan (FPLA-AUTKOR). If automatic corrections are not enabled, then the latest billing plan item end date is taken as <em>POB end date</em>. If automatic corrections are enabled then there are three possible dates to be set as end date using a complex logic.</p>\n<ol>\n<li>If the header level \"<em>dates until</em>\" field is filled (FPLA-TNDAT) and it is earlier than the header level \"<em>end date</em>\" (FPLA-ENDAT) then the \"dates until\" field is set as <em>POB end date</em> </li>\n<li>If the header level \"<em>dates until</em>\" field is not filled (FPLA-TNDAT) or if it is later than the header level \"<em>end date</em>\" (FPLA-ENDAT) then the \"end date\" field is set as <em>POB end date</em></li>\n<li>If the latest billing plan item level end date is earlier than the header level \"<em>dates until</em>\" (FPLA-TNDAT) or \"<em>end date</em>\"  (FPLA-ENDAT) fields it will overwrite the <em>POB end date</em></li>\n</ol>\n<p><span>Formular representation</span></p>\n<ul>\n<li>POB start date: <strong>min</strong>( billing plan item start dates )</li>\n<li>POB end date without automatic correction: <strong>max</strong>( billing plan item end dates ) </li>\n<li>POB end date with automatic correction: <strong>min</strong> ( <strong>min</strong>( dates until (FPLA-TNDAT); end date (FPLA-ENDAT) ); <strong>max</strong>( billing plan item end dates) )</li>\n</ul>\n<p>Note: The special logic for billing plans is only active if the SDPI processing of the sales document is triggered. In case a milestone billing plan is used the functionality for SDPI generation must be explicitly activated in the Customizing activity \"Activate Functions to Integrate with Revenue Accounting\", otherwise the logic above is not triggered.</p>\n<p><strong>Custom logic</strong></p>\n<p>It is possible to implement custom logic for the POB start date and end date determination using method ORDER_DATA_TO_ARL of BADI FARRIC_BADI_ORDER. The dates set in this BADI method overwrite both the \"Default\" and the \"Billing plan\" logic and have absolute priority.</p>\n<p>It is also possible to manipulate the POB start and end dates in the SAP RAR engine either using decision tables (e.g.: DT_PROCESS_POB) or BADI implementations, however these topics will not be a subject of this SAP Note as those functionalities belong to SAP RAR and not to the SD integration component.</p>", "noteVersion": 2}, {"note": "2463880", "noteTitle": "2463880 - SAP Revenue Accounting and Reporting: Data validation report", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>The following note documents functionality that can help you as a customer to spot potential issues in the results generated by the SAP Revenue Accounting and Reporting (RAR) solution. The functionality described is available to customers on release SAP RAR 1.2 and SAP RAR 1.3 and is recommended to be used in addition to reconciliation reports outlined in the SAP Administrator’s guide.</p>\n<p>Please refer to SAP note <a href=\"/notes/2567106\" target=\"_blank\">2567106</a> for an updated version of the consistency checks triggered by the data validation tool.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p><strong>FARR_CONTR_CHECK, <strong>FARR_CONTR_MON</strong></strong></p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>SAP Revenue Accounting and Reporting (RAR) provides two reports to perform currently 18 consistency checks (generating error categories E1 – E18) to identify potential issues based on other customer experiences. Potential issues identified by these reports can occur due to different reasons, such as errors in SAP RAR’s configuration, data errors in migration from a legacy system to SAP Revenue Accounting and Reporting (RAR), currently unsupported business processes, or other factors (software errors?).</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>SAP Revenue Accounting and Reporting (RAR) provides two reports to perform currently 18 consistency checks (generating error categories E1 – E18) to identify potential issues based on other customer experiences.</p>\n<p><strong>If issues are spotted, we recommend you to create an incident on component FI-RA.  </strong></p>\n<p>Please note that the performance of the reports has not yet been optimized. Therefore, the reports may take a lot of system resources and you may expect a long runtime in some cases.</p>\n<p>You can check revenue accounting data for specific accounting principles and company codes with transaction code <strong>FARR_CONTR_CHECK</strong>. The report performs 18 diagnostics on the selected contracts. For example, it checks whether the following situations are true:</p>\n<p>•             The quantity of fully fulfilled performance obligation is equal to the total quantity.</p>\n<p>•             The invoiced amount equals to the posted amount.</p>\n<p>You can monitor the result of the Revenue Accounting Contract Consistency Check with transaction code <strong>FARR_CONTR_MON</strong>. To display the result, you need to select the following modes:</p>\n<p>•             Read data online</p>\n<p>With this mode, SAP RAR performs the consistency checks for the selected contracts with revenue accounting data directly from the database. The result will not be saved in the error log.</p>\n<p>Note: By selecting this mode, you can also view revenue accounting data of selected contracts and performance obligations.</p>\n<p>•             Read data from error table</p>\n<p>With this mode, SAP RAR displays inconsistent contracts and performance obligations from the system log.</p>\n<p>The diagnostics currently do not support the feature of prospective changes due to contract modifications, contract asset and contract liability calculation logic and foreign currency exchange method 2.</p>\n<p>If required, in the future SAP will optimize the algorithm behind the existing error categories as well as provide additional error categories based on customer feedback.</p>\n<p>In the following pages we briefly describe and outline the relevant error categories. Note that you can find these explanations also in attached document, the report documentation per error category (or RAR’s on-line help).</p>\n<p><strong>E1: </strong><strong>Incorrect balance of allocation effects</strong></p>\n<p><strong>E2: </strong><strong>Difference between allocated amount and revenue schedule of a POB</strong></p>\n<p><strong>E3: </strong><strong>Difference between POB effective quantity and fulfilled quantity for completely fulfilled POB</strong></p>\n<p><strong>E4: </strong><strong>Difference between fulfilled, unsuspended quantity and fulfilled deferral item quantity for a time-based POB</strong></p>\n<p><strong>E5: Incorrect/Missing latest deferral item flag</strong></p>\n<p><strong>E6: </strong><strong>Difference between scheduled revenue amount and posted revenue amount</strong></p>\n<p><strong>E7: Difference between scheduled invoice amount and posted invoice amount</strong></p>\n<p><strong>E8: </strong><strong>Incorrect/missing special indicator flag</strong></p>\n<p><strong>E9: Inconsistency between transaction price, allocation amount and allocation effect for a POB</strong></p>\n<p><strong>E10: </strong><strong>Incorrect balance on receivable adjustment account in transaction currency</strong></p>\n<p><strong>E11: </strong><strong>Calculated deferred revenue/contract liability amount based on deferral items not equal with corresponding posted amounts</strong></p>\n<p><strong>E12: </strong><strong>Calculated unbilled receivable/contract asset amount based on deferral items not equal with corresponding posted amounts</strong></p>\n<p><strong>E13: </strong><strong>Transferred invoice amount not equal with posted invoice amount in transaction currency</strong></p>\n<p><strong>E14: Incorrect balance on receivable adjustment account in local currencies</strong></p>\n<p><strong>E15: POB without deferral item</strong></p>\n<p><strong>E16: </strong><strong>Transferred invoice amount not equal with posted invoice amount in local currencies</strong></p>\n<p><strong>E17: Incorrect cumulated posted quantities in deferral items</strong></p>\n<p><strong>E18: Check consistency and validity of accounting objects</strong></p>", "noteVersion": 5}, {"note": "2580961", "noteTitle": "2580961 - RAR Data Migration and Transition - Additional Information", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p><strong>Data Migration</strong> from operational systems to SAP Revenue Accounting is a critical part in the implementation and operation of SAP Revenue Accounting and Reporting.</p>\n<p>In addition, customers may want to switch from an existing accounting standard to a new one, for example, IFRS15. This process is referred to as <strong>Transition</strong>.</p>\n<p>The following note provides additional information in the context of migration and transition.</p>\n<p>Please also consider the list of SAP notes mentioned in the reference attached to this SAP note and ensure, that the listed notes are implemented before you start your migration or transition phase.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p><span 'times=\"\" 107%;=\"\" ar-sa;\"=\"\" arial',sans-serif;=\"\" en-gb;=\"\" line-height:=\"\" mso-ansi-language:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" new=\"\" roman';=\"\">Migration, Transition, Migration Packages, Switch-off old accounting principle, FARR_RAI784, FARR_CONTRACT_MAIN866, FARR_CONTRACT_MAIN864</span></p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>The following note provides additional information in the context of migration and transition.</p>\n<p>For migration, you can find additional information in the SAP Help Portal for SAP Revenue Accounting and Reporting in the chapter <strong><em>Migration</em></strong> as well as in the Administrator’s Guide (SD Integration).</p>\n<p><strong>Note:</strong></p>\n<p>Please carefully follow the instructions in the application help specific to the implementation scenario, e.g. integration with SAP SD or Third Party Sender integration.</p>\n<p>For transition, you can find additional information in the SAP Help Portal for SAP Revenue Accounting and Reporting in the chapter <em><strong>Transition</strong></em>.</p>\n<p>The note covers the following topics:</p>\n<p><strong><a href=\"#Additional_Recommendations\" target=\"_self\">1. Additional Recommendations</a></strong></p>\n<p><strong><a href=\"#Verification_of_the_migration_and_data_validation\" target=\"_self\">2. Verification of the migration and data validation</a></strong></p>\n<p><strong><strong><a href=\"#Runtime_of_the_data_migration\" target=\"_self\">3. Runtime of the Data Migration</a></strong></strong></p>\n<p><strong><strong><strong><a href=\"#Further_recommendations\" target=\"_self\">4. Further recommendations</a></strong></strong></strong></p>\n<p><strong><strong><strong><a href=\"#Restrictions\" target=\"_self\">5. Restrictions</a></strong></strong></strong></p>\n<p> </p>\n<p><strong><a name=\"Additional_Recommendations\" target=\"_blank\"></a>﻿1. Additional Recommendations</strong></p>\n<p>For the migration of data from Sales and Distribution, please use the latest version of the software component REVRECSD and implement all relevant SAP Notes.</p>\n<p>The details for the migration of data using a provided API can be found in the SAP Note 2392956</p>\n<p><strong>2. <a name=\"Verification_of_the_migration_and_data_validation\" target=\"_blank\"></a>﻿Verification of the migration and data validation</strong></p>\n<p>In addition to individual checks of the migration for smaller data sets, SAP recommends to verify the migration procedure for the full data set. This procedure should be performed on a dedicated environment, which in case of severe failures can be restored. As the business rules can be different for different company code and accounting principle combinations, the entire data set (or at least a significant portion representing all business scenarios) should be verified and validated in the migration procedure. The verification should include the reconciliation of data between the data source and the RAR system. For more details on data reconciliation, please refer to chapter Reconciliation in the SAP RAR Administrator's Guide.</p>\n<p><strong>Note:</strong> In many cases, the migrated data (initial load) can be reset on the application level – please, refer to the SAP Library in the help for SAP RAR Chapter Testing Migration for details.</p>\n<p> </p>\n<p><strong><a name=\"Runtime_of_the_data_migration\" target=\"_blank\"></a>﻿3. Runtime of the Data Migration</strong></p>\n<p>The performance and overall duration of the data migration procedure should be tested. The full migration procedure for a company code/accounting principle combination should fit within a single posting period (1 Month). The planned duration of the data migration contains the time for loading of data as well as the time for verification of data (typical 2-5 days). This procedure should be ideally completed 1-2 days before the End of Month closing starts. This additional contingency would allow correction of data in case of unexpected errors.</p>\n<p> </p>\n<p><strong><a name=\"Further_recommendations\" target=\"_blank\"></a>﻿4. Further recommendations</strong></p>\n<p>Whenever possible, data migration should be performed during black-out period for creation of new orders. In most cases, it would not be possible. Then, to minimize the impact of the data migration on the daily operations: existing (old) contracts should not be modified.</p>\n<p>For high data volumes (e.g. telecommunication), the loading of data into the RAR system, both initial data loading as well as daily data transfer, should be technically optimized. It is recommended to test the performance of the data loading and in case of insufficient runtimes to perform the technical bottle-neck analysis and optimize the process. Here, the possible tuning handles are: the overall number of batch work processes on the system, the assignment of migration jobs to the corresponding servers or the size of intervals – see chapter Parallel Processing Framework for more details.</p>\n<p>For migration and transition please note the following important information.</p>\n<p>- <strong>Migration Packages:</strong> If you have used migration packages already as part of your migration efforts, do NOT delete these migration packages from configuration.</p>\n<p>- <strong>Switch-off old accounting principle:</strong> You can switch off an accounting principle e.g. after the transition phase considering the advance development provided with note 2557098 (https://launchpad.support.sap.com/#/notes/2557098).</p>\n<p>- <strong>Error message Comp.code &amp;1/acct.pr. &amp;2: Events at/after date of adoption exist (FARR_RAI784):</strong> You execute transaction FARR_RAI_PROC_NEWACP to create contracts under the new accounting principle. You try to change the status of company code-accounting principle from Migration to Transition and get error message FARR_RAI784 “Comp.code &amp;1/acct.pr. &amp;2: Events at/after date of adoption exist”.</p>\n<p>Reason for this message could be that you have reprocessed some RAI4s (Processed RAIs) in the target accounting principle of the new accounting standard, where at that point in time no adoption date was maintained yet in the customizing. Afterwards, it was too late to set the desired adoption date, as some RAIs, which should have been parked instead of being processed according to the adoption date in question, were already in status “Processed”.</p>\n<p>Please ensure, to first maintain the customizing attributes “Adoption Date”, “Source Accounting Principle” and “External Source Accounting Principle” accordingly, to avoid such issues in the future and test transition properly.</p>\n<p>If this situation has happened please execute the following steps, to have a clean setup for the target accounting principle according to the new accounting standard and the corresponding company code:</p>\n<p>o Execute transaction FARR_NEWACP_CLEANUP to clean up the target accounting principle of the corresponding company code.</p>\n<p>o Set the status of the corresponding company code/accounting principle back to “Migration” and save your changes</p>\n<p>o For customizing entries for the new accounting principle, maintain an appropriate value for the attributes “Adoption Date”, “Source Accounting Principle” and “External Source Accounting Principle” from the beginning on and save your changes</p>\n<p>o Start with regular execution of transition steps like reprocessing of RAI4s etc.</p>\n<p> </p>\n<p><strong><a name=\"Restrictions\" target=\"_blank\"></a></strong>﻿<strong>5. Restrictions</strong></p>\n<p>For migration please note the following restrictions:</p>\n<p>- A <strong>migration of cost</strong> is currently not supported. Please remove the cost condition type from the legacy data to avoid error messages such as Time-based performance obligation &amp;1 cannot have open revenue (FARR_CONTRACT_MAIN864) during initial load.</p>\n<p>- A <strong>migration of linked POBs</strong> is currently not supported. Customers are asked not to utilize the additional POB customizing during migration (Decision Table DT_PROCESS_POB_ADD). As a workaround, you could create an additional POB, either through providing an additional order item RAI via custom report, or in case of using the integration with Hybris Billing by creating an additional provider contract item representing the additional POB within the main provider contract, or by creating an additional provider contract. In this scenario, the initial item would be of transaction price = 0 and a corresponding SSP.</p>\n<p>- During migration, the <strong>invoiced amount must not exceed contractual price</strong>: You want to migrate data from a legacy system to Revenue Accounting and switch the combination of company code and accounting principle directly from status 'Migration' to 'Production' without using status 'Transition' to calculate the cumulative catch-up. For this, in Customizing you did not specify a source accounting principle or mark the accounting principle as external accounting principle. As a result, the legacy revenue accounting items will have initial load indicator '1' (Initial Load Due to New Co. Code or Migr. Package). In this scenario, RAR has the constraint that for each item which creates a performance obligation, the invoiced amount must not exceed contractual price on the item. When you process such an item from the RAI monitor, you will get error message FARR_CONTRACT_MAIN 866 \"Perf. obl. &amp;1's invoice amount must not exceed its contractual price\".</p>\n<p>Reason: As the invoiced amount is higher than the contractual price, this requires an adjustment to the contract price of the performance obligation in Revenue Accounting. However, changing the contract price during initial load leads to a retrospective change and cumulative catch-up. Such cumulative catch-up can only be triggered by cumulative catch-up program \"FARR_CALC_TRANS_CATCHUP\" after the accounting principle is set to 'Transition'.</p>\n<p>Procedure: Such items shall not be migrated to Revenue Accounting.</p>\n<p>You may want to consider the following workarounds:</p>\n<p>o Change the contractual price in your legacy system according to the invoice price.</p>\n<p>o Exclude this item from initial load</p>\n<p>o Exempt the legacy RAI item</p>", "noteVersion": 5}, {"note": "2634948", "noteTitle": "2634948 - Consulting: Manipulation of revenue accounting relevance", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are using the SAP RAR SD integration component and exploring how flexible a sales document item´s revenue accounting relevance can be configured.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>SD Revenue Accounting, FARR_RELTYPE, CLEAR FARR_RELTYPE, DELETE, remove, set, change, item category, relevancy</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>This SAP Note explains the current possibilities and limitations in changing and determining the revenue accounting relevance.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>The revenue accounting relevance of a sales &amp; distribution process is defined on item category level either in the sales document or in the billing document.</p>\n<p><strong>Configuration is possible in the customizing:</strong></p>\n<p>SAP Customizing Implementation Guide<br/>└─Sales and Distribution<br/>    └─Revenue Accounting and Reporting<br/>        └─Maintain Revenue Accounting Item Settings (Sales item category)<br/>        └─Create Performance Obligation for SD Billing Item (Billing item category)</p>\n<p><strong>Sales document items can be customized based on:</strong></p>\n<ul>\n<li>Sales organization</li>\n<li>Sales document type</li>\n<li>Item category</li>\n<li>Migration package</li>\n</ul>\n<p><strong>Billing document items can be customized based on:</strong></p>\n<ul>\n<li>Sales organization</li>\n<li>Billing document type</li>\n<li>Item category</li>\n<li>Migration package</li>\n</ul>\n<p><strong>Dynamic revenue accounting relevance:</strong></p>\n<p>The relevance can also be changed by ABAP implementations of enhancement spot FARRIC_SD, where FARRIC_BADI_ORDER and FARRIC_BADI_INVOICE both offer a method called \"CLEAR_RELTYPE_FLAG\". As the method´s name also indicates it can only be used to remove the relevance flag of certain items, so it is not capable of setting it to \"X\", \"M\" or \"G\".</p>\n<ul>\n<li>FARRIC_BADI_ORDER~CLEAR_RELTYPE_FLAG can delete FARR_RELTYPE \"X\" or \"M\" read from the customizing</li>\n<li>FARRIC_BADI_INVOICE~CLEAR_RELTYPE_FLAG can delete FARR_RELTYPE \"G\" read from the customizing, but cannot delete \"X\" or \"M\" inherited from the reference document</li>\n</ul>\n<p><strong><br/>BADI for sales document relevance </strong>(FARRIC_BADI_ORDER~CLEAR_RELTYPE_FLAG)<br/><br/>This method is called in the SD Core application during sales document item creation. (form VBKD_FUELLEN_TVAP include FV45KFKD_VBKD_FUELLEN_TVAP) The coding is only called in the SD Core when the corresponding sales document item is created, or if the item category or the material is changed subsequently. The input parameters for the BADI method are the sales document type, the sales area, the billing company code, the item category and the material. Unfortunately no other fields can be added to the determination logic, because changing any other field would not trigger the revenue accounting relevance re-determination which would leave an inconsistent state. The sales document type, sales area and billing company code fields are set before building the individual sales document items and these cannot be changed without restarting the VA01/VA41 transaction. These fields can therefore also be used in the BADI method in addition to the material and item category whose changes do trigger the RAR relevance redetermination.</p>\n<p>The BADI method has been implemented in this spot for two major reasons:</p>\n<p>1.) There is significant business logic that may be executed differently in the SD Core application based on the revenue accounting relevance. In case the revenue accounting relevance was changed/cleared during e.g. IF_EX_BADI_SD_SALES~DOCUMENT_SAVE or at any later point then the necessary SD Core data enrichments may have never happened, or be invalid during save. This would result in inconsistent SDOI RAIs being sent to the revenue accounting engine.</p>\n<p>2.) Customer-specific BADI implementations can be changed even in live environments. A sales document update after changing the custom RAR-relevance determination logic may result in items losing and gaining RAR-relevance. Such changes may lead to inconsistencies, unwanted FI adjustment postings or processing errors if follow-on postings exist in the corresponding POB. In the SD Core application the material and the item category fields can only be changed if no follow-on documents exist. The fact that the revenue accounting relevance redetermination can also only be triggered by changing either of these fields ensures that only documents without subsequent SD documents can send POB deletion requests or create new POBs.</p>\n<p><strong>Example mistakes:</strong><br/>#1: Adding new fields to the relevance determination logic via enhancement/modification: <br/><em><em>The sales document item´s project number (VBAP-PS_PSP_PNR) is used during a <span>modified</span> RAR relevance determination. Later on the item is assigned to a different project and a new PS_PSP_PNR field is entered. You expected the sales document item to lose its RAR-relevance, but it remains relevant for RAR because form VBKD_FUELLEN_TVAP include FV45KFKD_VBKD_FUELLEN_TVAP is not called in the SD core.</em></em></p>\n<p>#2: Changing the RAR relevance via enhancement/modification in IF_EX_BADI_SD_SALES~DOCUMENT_SAVE:<em><br/>Revenue accounting relevant document items must generate a profitability segment number, even if cost based profitability analysis is not used normally. If the revenue accounting relevance is not set during item creation/processing only during saving the document, then the SD Core coding is not be able to derive a profitability segment number. As all RAR relevant items must have a profitability segment number this results in an inconsistent SDOI RAI being sent to SAP RAR. A subsequent editing of the sales document would even result in an incompleteness log entry due to the missing profitability segment number.</em></p>\n<p>#3: Changing the RAR relevance via enhancement/modification in IF_EX_BADI_SD_SALES~DOCUMENT_SAVE:<em><br/>The payment terms are changed in a completely processed RAR-relevant sales document. The corresponding POBs are also completely fulfilled and invoiced in the revenue accounting engine. The change results in new SDOI RAIs being sent to SAP RAR. The custom coding which deemed these items RAR-relevant back then was changed, so the items are not set relevant for RAR this time. The SD Integration Component marks the corresponding POBs for deletion in SAP RAR, where errors occur due to the existing follow-on documents.</em></p>\n<p>#4: Changing the RAR relevance via enhancement/modification in IF_EX_BADI_SD_SALES~DOCUMENT_SAVE:<br/><em>An item level field is changed in a partially invoiced and completely delivered sales document that is not relevant for RAR. A user-exit sets the item relevant for revenue accounting and a performance obligation is created for the order in SAP RAR. Since billing documents and deliveries were created before this change, those documents did not inherit the revenue accounting relevance from the source document and will therefore never generate SDII or SDFI RAIs. This results in an inconsistency between the SD system and SAP RAR.</em></p>\n<p><strong><br/>BADI for b</strong><strong>illing document relevance </strong>(FARRIC_BADI_INVOICE~CLEAR_RELTYPE_FLAG)</p>\n<p>FARRIC_BADI_INVOICE method CLEAR_RELTYPE_FLAG is called in the SD Integration Component while processing billing document RAIs. (class CL_FARRIC_SD_INVOICE method IF_FARRIC_SD_INVOICE-&gt;GET_INVOICE_RELEVANT_INDICATOR). <span>The input parameters for the BADI method are the sales document type, the sales area, the billing company code, the item category and the material. There are no technical restrictions in place to limit the parameters to these fields like in the sales document case, the field set was taken over from the sales BADI for simplicity´s sake.</span></p>\n<p><strong><br/>Revenue Accounting relevance (FARR_RELTYPE) VS. Revenue Recognition relevance (RRREL)</strong></p>\n<p>If both SD Revenue Recognition (RevRec) and the SD Revenue Accounting Integration (RA) are used in a system parellely then only one of these functionalities may get triggered for a given item. During the sales document item category determination the system prioritizes the revenue accounting configuration over revenue recognition. If the given sales item category is marked with any RA-relevance in the customizing and it is not cleared in the sales BADI method, then the RevRec-relevance is ignored and the process is only transferred to revenue accounting. Without RA-relevance the item is processed in RevRec if the customizing is set so.</p>\n<p>If the special invoice-generated revenue accounting process is used, then the corresponding sales document item category cannot be marked relevant for RA per definition. Therefore, if RevRec customizing exists for the item category the process gets marked relevant for RevRec which results in the suppression of the invoice-based POB generation for RA even if its customizing is maintained.</p>", "noteVersion": 3}, {"note": "2569950", "noteTitle": "2569950 - FAQ: Migration & Operational Load in the SD Integration Component", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You have recently started or planning to start the implementation of the SAP Revenue Accounting and Reporting (SAP RAR) functionality and you are about to migrate your existing Sales &amp; Distribution processes into this new solution. In this SAP Note you can find the frequently asked questions and answers about this process.</p>\n<p><a name=\"TOC\" target=\"_blank\"></a>﻿Table of contents:</p>\n<ol>\n<li><a href=\"#FAQ1\" target=\"_self\">Is there any general information available for Customers about migration and operational load?</a></li>\n<ol>\n<li><a href=\"#FAQ1a\" target=\"_self\">Can you explain us on a high level how the operational load program works and what is happening during migration?</a></li>\n</ol>\n<li><a href=\"#FAQ2\" target=\"_self\">What tools are provided by SAP to support the operational load/migration of SD documents?</a></li>\n<li><a href=\"#FAQ3\" target=\"_self\">Do I have to migrate all documents related to my sales process manually, like orders, invoices, deliveries and financial documents?</a></li>\n<li><a href=\"#FAQ4\" target=\"_self\">Can I migrate selected items of a sales document?</a></li>\n<li><a href=\"#FAQ5\" target=\"_self\">What does it mean that a sales document item is \"excluded\" or \"skipped\" from migration?</a></li>\n<li><a href=\"#FAQ6\" target=\"_self\">What happens if an existing item category is marked with revenue accounting relevance but some of the sales documents where this is used have already been migrated?</a></li>\n<li><a href=\"#FAQ7\" target=\"_self\">Can I migrate all standard SD processes to SAP RAR?</a></li>\n<ol>\n<li><a href=\"#FAQ7a\" target=\"_self\">Do I have to migrate completed processes?</a></li>\n<li><a href=\"#FAQ7b\" target=\"_self\">What can I do if some of my documents were archived in the to-be-migrated process?</a></li>\n<li><a href=\"#FAQ7c\" target=\"_self\">Can I migrate proof-of-delivery relevant processes?</a></li>\n<li><a href=\"#FAQ7d\" target=\"_self\">Can I migrate processes which have different billing currency than order currency?</a></li>\n<li><a href=\"#FAQ7e\" target=\"_self\">Can I migrate proforma invoices?</a></li>\n<li><a href=\"#FAQ7f\" target=\"_self\">Can I migrate cross-company sales processes?</a></li>\n</ol>\n<li><a href=\"#FAQ8\" target=\"_self\">Can I migrate SD Revenue Recognition processes to SAP RAR?</a></li>\n<ol>\n<li><a href=\"#FAQ8a\" target=\"_self\">Which SD Revenue Recognition processes do I have to migrate?</a></li>\n<li><a href=\"#FAQ8b\" target=\"_self\">What happens to open SD Revenue Recognition processes during migration?</a></li>\n<li><a href=\"#FAQ8c\" target=\"_self\">Can I migrate completed SD Revenue Recognition processes?</a></li>\n<li><a href=\"#FAQ8d\" target=\"_self\">Can I migrate cancelled revenue lines?</a></li>\n<li><a href=\"#FAQ8e\" target=\"_self\">Can I migrate manually completed documents?</a></li>\n<li><a href=\"#FAQ8f\" target=\"_self\">Can I migrate all event-based processes?</a></li>\n<li><a href=\"#FAQ8g\" target=\"_self\">Can I migrate the revenue schedule which was forecasted by SD Revenue Recognition?</a></li>\n<li><a href=\"#FAQ8h\" target=\"_self\">Can I migrate FASB 52 relevant documents?</a></li>\n<li><a href=\"#FAQ8i\" target=\"_self\">What happens if a billing document which was relevant for SD Revenue Recognition gets cancelled after its migration?</a></li>\n</ol>\n<li><a href=\"#FAQ9\" target=\"_self\">What is the revenue correction report?</a></li>\n<ol>\n<li><a href=\"#FAQ9a\" target=\"_self\">How do I know if I should use the revenue correction report?</a></li>\n</ol>\n<li><a href=\"#FAQ10\" target=\"_self\">I have received an error during operational load, can I undo and restart it?</a></li>\n<li><a href=\"#FAQ11\" target=\"_self\">Are cancelled billing documents migrated?</a></li>\n<li><a href=\"#FAQ12\" target=\"_self\">What happens to incomplete sales documents during migration?</a></li>\n<li><a href=\"#FAQ13\" target=\"_self\">What is a migration package and how can I utilize it?</a></li>\n<li><a href=\"#FAQ14\" target=\"_self\">Why does the operational load generate RAIs without initial load indicator?</a></li>\n<li><a href=\"#FAQ15\" target=\"_self\">Is it possible to migrate costs?</a></li>\n</ol>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>SD Revenue Accounting, SD IC, Integration component, SD Revenue Recognition, migration, FAQ, frequently asked questions, answers</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong><a name=\"FAQ1\" target=\"_blank\"></a>﻿1. Is there any general information available for Customers about migration and operational load?</strong></p>\n<p>Yes there are documentations available on the SAP help ... :</p>\n<ul>\n<li><a href=\"https://help.sap.com/viewer/product/SAP_REVENUE_ACCOUNTING_AND_REPORTING/1.3.3/en-US\" target=\"_blank\">https://help.sap.com/viewer/product/SAP_REVENUE_ACCOUNTING_AND_REPORTING/1.3.3/en-US</a></li>\n<li><a href=\"https://help.sap.com/viewer/de5c3a7ada6e41d482ce9ecdbd93aba1/1.3.3/en-US/565e63540a7b8f4ce10000000a4450e5.html\" target=\"_blank\">https://help.sap.com/viewer/de5c3a7ada6e41d482ce9ecdbd93aba1/1.3.3/en-US/565e63540a7b8f4ce10000000a4450e5.html</a></li>\n</ul>\n<p>... and FAQ Notes in the Revenue Accounting Engine:</p>\n<ul>\n<li><a href=\"/notes/2580961\" target=\"_blank\">2580961</a> - RAR Data Migration and Transition - Additional Information</li>\n<li><a href=\"/notes/2616387\" target=\"_blank\">2616387</a> - Important Performance Considerations when implementing SAP Revenue Accounting and Reporting</li>\n</ul>\n<p>... and the Migration Guide for SD Revenue Recognition Customers attached to the following Note:</p>\n<ul>\n<li>\n<p><a href=\"/notes/2733866\" target=\"_blank\">2733866</a> -  Migration Guide for SD Revenue Recognition to Revenue Accounting and Reporting</p>\n</li>\n</ul>\n<p><em><a href=\"#TOC\" target=\"_self\">[Back to the table of contents]</a></em></p>\n<p><strong><a name=\"FAQ1a\" target=\"_blank\"></a>﻿1.a Can you explain us on a high level how the operational load program works and what is happening during migration?</strong></p>\n<p>The migration is a quite complex sequence of actions during which sales documents, deliveries and billing documents are marked with the revenue account relevance and their legacy information is extracted from the corresponding posting entries from financials. During this transformation SD01, SD02 and SD03 class revenue account items (RAIs) are sent to SAP RAR along with the special migration specific legacy RAIs used for the cumulative catchup.</p>\n<p>In the first step the operational load program collects all sales documents from the system based on the input parameters of the FARRIC_OL report. This initial worklist is then extended using the document flow of the sales documents found in the first step. The documents are sorted into three sub-categories: <strong>orders</strong> <em>(sales order, sales contract, credit memo request, debit memo request, free order, scheduling agreement, return order)</em>, <strong>deliveries</strong> <em>(outbound delivery, return delivery)</em> and <strong>billing documents</strong> <em>(invoice, invoice cancellation, credit memo, debit memo, credit memo cancellation, intercompany billing document, intercompany credit memo)</em>.</p>\n<p>Each order item is validated with a number of consistency checks and the items which are not excluded (see point 5) are migrated to revenue accounting. The migration is performed using a sales document change BAPI call (VA02) where each item gets marked with the revenue accounting relevance from the customizing (VBKD-FARR_RELTYPE). If the process was relevant for SD Revenue Recognition that obsolete relevance flag is initialized (VBKD-RRREL). This change in the revenue accounting relevance indicator also impacts some core Sales areas, for example pricing must derive a G/L account for each pricing condition for revenue accounting relevant items and each item must have a profitability segment number assigned to it. These changes are all automatically performed &amp; saved in the corresponding sales document. This means that the migration process may change your existing sales documents to be able to provide all the required data to revenue accounting. Once these changes are saved and committed to the database the SDOI (&amp; SDPI) RAIs are sent to the RAI monitor. Depending on which functionalities you have activated the order processing may result in additional acceptance date fulfillment RAIs (SDFI AD), incoming vendor invoice fulfillment RAIs (SDFI PI) or release order fulfillment RAIs (SDFI RO).</p>\n<p>After the orders are completed each delivery is processed one by one. The delivery items are marked with the same revenue accounting relevance which was saved in their referenced sales process in the first processing block (LIPS-FARR_RELTYPE). Other than this there is no change in existing deliveries. Once these changes are saved and committed to the database the SDFI RAIs are sent to the RAI monitor. Depeding on which functionalities you have activated the delivery processing may also result in additional proof of delivery fulfillment RAIs (SDFI PD)</p>\n<p>After the deliveries each billing document is processed one by one. The billing document items are also marked with the same revenue accounting relevance which was saved in their referenced sales process in the first processing block (VBRP-FARR_RELTYPE). If the process was relevant for SD Revenue Recognition that relevance flag is initializd (VBRP-RRREL). Other than this there is no change in the existing billing documents. Once these changes are saved and committed to the database the SDII RAIs are sent to the RAI monitor. Depending on which functionalities you have activated the invoice processing may result in intercompany billing fulfillment RAIs (SDFI IB) instead of SDII RAIs.</p>\n<p>During order, delivery and billing document processing the operational load program collects the total realized revenue amount for each process. In regular sales processes this is the total billed amount up to the transfer date, in SD Revenue Recognition processes this is calculated from the realized revenue lines up to the transfer date (VBREVE). These realized legacy amounts are consolidated into legacy condition items (LEGACYC). The general process information is consolidated into legacy main items (LEGACY), whereas the revenue schedules from time-based SD Revenue Recognition processes can be converted to legacy revenue schedule (LEGACYSF).</p>\n<p>Once all orders, deliveries and billing documents are processed these three types of legacy data are passed to the revenue accounting initial load API. They are then included into the corresponding performance obligations and contracts and the migration is completed.</p>\n<p>In specific SD Revenue Recognition processes a post-migration activity may be required - the execution of the revenue correction report. (See 9 and 9a)</p>\n<p><em><a href=\"#TOC\" target=\"_self\">[Back to the table of contents]</a></em></p>\n<p><strong><a name=\"FAQ2\" target=\"_blank\"></a>﻿2. </strong><strong>﻿</strong><strong>What tools are provided by SAP to support the operational load/migration of SD documents?</strong></p>\n<p>The Operational Load report<br/> Transaction FARRIC_OL<br/> <br/> The Operational Load report´s expert mode<br/> Transaction FARRIC_OL_EXPERT<br/> <br/> The Operational Load report´s cleanup (SAP RAR tool)<br/> Transaction FARRIC_IL_CLEANUP</p>\n<p>The Correction report<br/>Transaction FARRIC_RR_CORR</p>\n<p>The Correction report´s expert mode<br/>Transaction FARRIC_RR_CORR_EXP</p>\n<p><em><a href=\"#TOC\" target=\"_self\">[Back to the table of contents]</a></em></p>\n<p><strong><a name=\"FAQ3\" target=\"_blank\"></a>﻿3. Do I have to migrate all documents related to my sales process manually, like orders, invoices, deliveries and financial documents?</strong></p>\n<p>No, the migration can only be triggered on sales document level and the operational load program takes care of the collecting and processing all relevant follow-on documents.</p>\n<p><em><a href=\"#TOC\" target=\"_self\">[Back to the table of contents]</a></em></p>\n<p><strong><a name=\"FAQ4\" target=\"_blank\"></a>﻿4. Can I migrate selected items of a sales document?</strong></p>\n<p>Generally the migration is performed on sales document level so you cannot enter a specific sales document item for migration. The standard operational load program first performs validations on each sales document (item) and the processes which are not supported are excluded from the migration. It is possible to implement customer-specific logic to exclude further documents or items using BADI FARRIC_OL_VALIDATION (enhancement spot FARRIC_SD_OL).<strong><br/></strong></p>\n<p>The sales document items that were not excluded by the validations are cross-checked with the customizing settings from SPRO activity \"Maintain Revenue Accounting Item Settings\". The sales document items with revenue accounting relevant item categories are processed by the operational load.</p>\n<p><em><a href=\"#TOC\" target=\"_self\">[Back to the table of contents]</a></em></p>\n<p><strong><a name=\"FAQ5\" target=\"_blank\"></a>﻿5. What does it mean that a sales document item is \"excluded\" or \"skipped\" from migration?</strong></p>\n<p>There are several validation checks implemented in the operational load tool to ensure consistent migration. If any of these checks fail the impacted sales document or sales document item is excluded from the current operational load run.</p>\n<p>This means that sales document (item) is not updated with the revenue accounting relevance flag, there are no revenue accounting items generated and the follow-on documents related to the given sales document (item) are also skipped from the migration. The processing of such sales processes remains fully in the SD application. In other words, the migration tool ignores the sales document (item) entirely.</p>\n<p>There are special cases where documents or items are excluded due to resolvable issues e.g.: the document must be updated first, correction postings must be done first, the document must be unlocked. In these cases, once the corresponding root cause is resolved the operational load can be repeated (see point 6.). If the issues are not resolvable though (see points 7. and 8.) then the item in question cannot be consistently transferred to revenue accounting with the Standard tools offered by SAP.</p>\n<p><em><a href=\"#TOC\" target=\"_self\">[Back to the table of contents]</a></em></p>\n<p><strong><a name=\"FAQ6\" target=\"_blank\"></a>﻿6. What happens if an existing item category is marked with revenue accounting relevance but some of the sales documents where this is used have already been migrated?</strong></p>\n<p>Sales documents can be reprocessed with the operational load tool without the need for resetting the previous operational load runs. Already migrated document items are not changed, however the remaining items and their follow-on documents which have become relevant for revenue accounting since the latest run are migrated. A re-run can be executed with a new migration package ID, so there is no need to worry if the original migration package has already been changed to productive status.</p>\n<p><em><a href=\"#TOC\" target=\"_self\">[Back to the table of contents]</a></em></p>\n<p><strong><a name=\"FAQ7\" target=\"_blank\"></a>﻿7. Can I migrate all standard SD processes to SAP RAR?</strong></p>\n<p>No, some SD processes are currently not supported by the Revenue Accounting Engine. See further sub-questions and SAP Note <a href=\"/notes/2591055\" target=\"_blank\">2591055</a>.</p>\n<p><em><a href=\"#TOC\" target=\"_self\">[Back to the table of contents]</a></em></p>\n<p><strong>      <a name=\"FAQ7a\" target=\"_blank\"></a>﻿7.a Do I have to migrate completed processes?</strong></p>\n<p>No, in the expert version of the operational load report the screen parameter \"Skip completed Sales Documents\" controls whether completed sales documents are processed or not.</p>\n<p><em><a href=\"#TOC\" target=\"_self\">[Back to the table of contents]</a></em></p>\n<p><strong><a name=\"FAQ7b\" target=\"_blank\"></a>﻿7.b What can I do if some of my documents were archived in the to-be-migrated process?</strong></p>\n<p>As of SAP Note <a href=\"/notes/2364641\" target=\"_blank\">2364641</a>, in the expert version of the operational load report the screen parameter \"Read from Archive\" can enable the reading &amp; evaluation of archived documents during operational load.</p>\n<p><em><a href=\"#TOC\" target=\"_self\">[Back to the table of contents]</a></em></p>\n<p><strong><a name=\"FAQ7c\" target=\"_blank\"></a>﻿7.c Can I migrate proof-of-delivery relevant processes?</strong></p>\n<p>Yes, sales processes that are relevant for proof-of-delivery can be migrated as part of new functionality delivered in SP18/SP19. (Notes 2691932, 2698524, 2702194, 2715963, 2726341, 2694114)<br/><br/> <em>The technical field for this check is: VBKD-PDSTA.</em></p>\n<p><em><em><a href=\"#TOC\" target=\"_self\">[Back to the table of contents]</a></em></em></p>\n<p><strong><a name=\"FAQ7d\" target=\"_blank\"></a>﻿7.d Can I migrate processes which have different billing currency than order currency?</strong></p>\n<p>No, these types of documents cannot be handled correctly by the Revenue Accounting Engine, therefore they are excluded from migration.</p>\n<p><em><a href=\"#TOC\" target=\"_self\">[Back to the table of contents]</a></em></p>\n<p><strong><a name=\"FAQ7e\" target=\"_blank\"></a>﻿7.e Can I migrate proforma invoices?</strong></p>\n<p>No, processes with only proforma invoices do not receive real invoices, therefore they cannot generate revenue. Individual proforma invoices created in actually billing-relevant processes are also excluded as they never generate a financials posting. Therefore, it makes no sense to transfer such documents to Revenue Accounting.</p>\n<p><em><a href=\"#TOC\" target=\"_self\">[Back to the table of contents]</a></em></p>\n<p><strong><a name=\"FAQ7f\" target=\"_blank\"></a>﻿7.f Can I migrate cross-company sales processes?</strong></p>\n<p>Yes, as of SAP Note <a href=\"/notes/2338218\" target=\"_blank\">2338218</a>, the migration of Cross-Company scenarios is also supported. <br/>Costs of intercompany sales processes can also be processed in the new intercompany billing functionality delivered in SP18/SP19. (Note 2651782, 2649839, 2654138)</p>\n<p><em><a href=\"#TOC\" target=\"_self\">[Back to the table of contents]</a></em></p>\n<p><strong><a name=\"FAQ8\" target=\"_blank\"></a>﻿8. Can I migrate SD Revenue Recognition processes to SAP RAR?</strong></p>\n<p>Yes, the standard Operational Load report also supports the migration of SD Revenue Recognition processes.</p>\n<p><strong>Type \"A\" - Time-based revenue recognition processes</strong> <br/> These document items are migrated into SDOI RAIs with fulfillment type = T (time-based) and deferral method = S (linear distribution)</p>\n<p><strong>Type \"B\" - Service-based revenue recognition processes with delivery</strong> <br/> These document items are migrated into SDOI RAIs with fulfillment type = E (event) and event type = GI (goods issue) <br/> <br/> <strong>Type \"B\" - Service-based revenue recognition processes without delivery </strong><br/> These document items are migrated into SDOI RAIs with fulfillment type = T (time-based) and deferral method = S (linear distribution)<br/> <br/> <strong>Type \"D\" - Time-based and invoice-based revenue recognition processes</strong> <br/>These documents items are migrated into SDIG RAIs with fulfillment type = T (time-based) and deferral method = S (linear distribution)<br/>Migration details are explained in <strong>SAP Note <a href=\"/notes/2719185\" target=\"_blank\">2719185</a></strong><br/> <br/> <strong>Type \"F\" - Credit/debit memo processing with reference to preceding document <br/> </strong>These document items must <strong>NOT</strong> be migrated directly into new POBs, however using revenue accounting type \"M\" instead of \"X\" their reference processes should get updated with their value adjustments.</p>\n<p>The POB attribues like fulfillment type, event type or deferral method are hard-coded in the operational load to ensure the compatibility of the legacy SD Revenue Recognition processes with the new RAR processes. The BRF+ customizing does not redetermine these attributes. However, enhancement spot FARRIC_SD_OL offers a BADI called FARRIC_OL_LEGACY which can be used to modify these attributes in case of Customer-specific requirements. <span>Any custom attributes set here are considered a process modification and SAP reserves the right to reject support requests if non-Standard attributes are found in migrated POBs.</span></p>\n<p>There are numerous limitations and restrictions for the migration of SD Revenue Recognition processes.</p>\n<p><em><a href=\"#TOC\" target=\"_self\">[Back to the table of contents]</a></em></p>\n<p><strong><a name=\"FAQ8a\" target=\"_blank\"></a>﻿8.a Which SD Revenue Recognition processes do I have to migrate?</strong></p>\n<p>The SD Revenue Recognition functionality is no longer available in S/4HANA and since the migration relies on this functionality the operational load can no longer be executed after the upgrade to S/4HANA. Therefore, if you are about to upgrade to S/4HANA you must first migrate all SD Revenue Recognition processes in your system which are:</p>\n<ul>\n<ul>\n<li>Not fully delivered and invoiced</li>\n<li>Have deferred revenue still to be realized</li>\n<li>For which you expect follow-up activities such as increase quantity, create credit memo, or cancel invoice (only migrated processes can get follow-on documents in S/4HANA)</li>\n</ul>\n</ul>\n<p>If you are <strong>not</strong> about to migrate to S/4HANA you can perform partial migration of selected sales processes using the revenue accounting item category relevance to control which documents are to be migrated. You can even keep the SD Revenue Recognition processes in your system and finish them with the legacy application and only create the new documents with revenue accounting relevance. (SD Revenue Recognition does not support price allocations or other IFRS15 requirements in the Standard, therefore if you need these features the migration must be performed nevertheless but this may vary in your individual processes)</p>\n<p><em><a href=\"#TOC\" target=\"_self\">[Back to the table of contents]</a></em></p>\n<p><strong><a name=\"FAQ8b\" target=\"_blank\"></a>﻿8.b What happens to open SD <strong>Revenue Recognition </strong>processes during migration?</strong></p>\n<p>During migration, the sales document items and billing document items lose their SD Revenue Recognition relevance flag, whereas the existing revenue lines get marked as \"migrated\". Follow-on documents like invoices or deliveries created after the migration for these document items will not be processed by the standard SD Revenue Recognition logic anymore. Migrated revenue lines are <strong>not</strong> proposed for processing by the standard Revenue Recognition transactions like VF44/VF46. Therefore, further processing in the classical SD Revenue Recognition application is no longer possible.</p>\n<p>Revenue lines which were realized in VF44 with a posting date after the transfer date, are marked for correction posting by the migration report. With the execution of the corresponding correction report, these entries are reverted similarly like the postings made via transaction VF46.</p>\n<p>Revenue lines which were realized in VF44 with a posted date before/on the transfer date are accumulated into legacy data and considered as cumulative catch-up.</p>\n<p>Revenue lines which were cancelled or are cancellation lines themselves are ignored completely.</p>\n<p>The revenue recognition type used in the migrated document is saved into database table FARRIC_MIGRATED.<br/> <br/> <em>The technical fields for these are: VBKD-RRREL = space, VBRP-RRREL = space, VBREVE-REVFIX = M</em></p>\n<p><em><em><a href=\"#TOC\" target=\"_self\">[Back to the table of contents]</a></em></em></p>\n<p><strong><a name=\"FAQ8c\" target=\"_blank\"></a>﻿8.c Can I migrate completed SD Revenue Recognition processes?</strong></p>\n<p>Yes, in the expert version of the operational load report the screen parameter \"Incl Rev status cmpl items\" controls whether completed SD Revenue Recognition processes are migrated or not.<br/> <br/> <em>The technical field for this check is: VBUP-RRSTA</em></p>\n<p><em><em><a href=\"#TOC\" target=\"_self\">[Back to the table of contents]</a></em></em></p>\n<p><strong><a name=\"FAQ8d\" target=\"_blank\"></a>﻿8.d Can I migrate cancelled revenue lines?</strong></p>\n<p>No, revenue lines that are cancelled or are cancellations themselves are not migrated. These lines are also not part of the legacy data regardless of their posting date.<br/> <br/> <em>The technical field for this check is: VBREVE-REVFIX = \"A\" or \"B\".</em></p>\n<p><em><em><a href=\"#TOC\" target=\"_self\">[Back to the table of contents]</a></em></em></p>\n<p><strong><a name=\"FAQ8e\" target=\"_blank\"></a>﻿8.e Can I migrate manually completed documents?</strong></p>\n<p>No, sales processes that have been manually completed are not migrated.<br/> <br/> <em>The technical field for this check is: VBUK-MANEK.</em></p>\n<p><em><em><a href=\"#TOC\" target=\"_self\">[Back to the table of contents]</a></em></em></p>\n<p><strong><a name=\"FAQ8f\" target=\"_blank\"></a>﻿8.f Can I migrate event-based processes?</strong></p>\n<p><strong>Yes:</strong></p>\n<ul>\n<ul>\n<li>Event type \"_ - Standard POD\" can be migrated into a proof of delivery event-based process (Note 2691932)</li>\n<li>Event type \"N - Not POD-relevant\" can be migrated into a goods issue event-based process (Note 2691932)</li>\n<li>Event type \"A - Incoming invoice\" can be migrated into an incoming vendor invoice event-based process (Note 2696483)</li>\n<li>Event type \"B - Acceptance Date\" can be migrated into an acceptance date event-based process (Note  2768928, 2737585)</li>\n</ul>\n</ul>\n<p><strong><span>No:</span></strong></p>\n<ul>\n<ul>\n<li>Event type \"X\", \"Y\", \"Z\" - Customer-Specific events <strong>cannot be migrated in the Standard</strong></li>\n</ul>\n</ul>\n<p><em>The technical field for this check is: VBKD-REVEVTYP &amp; VBKD-PODKZ</em></p>\n<p><em><em><a href=\"#TOC\" target=\"_self\">[Back to the table of contents]</a></em></em></p>\n<p><strong><a name=\"FAQ8g\" target=\"_blank\"></a>﻿8.g Can I migrate the revenue schedule which was forecasted by SD Revenue Recognition?</strong></p>\n<p>Yes, in the expert version of the operational load report the screen parameter \"Transfer Revenue Schedule\" enables the transfer of existing forecast VBREVE lines to Revenue Accounting. This is only supported for time-based processes.</p>\n<p><em>Technical field for this check is: VBKD-RRREL = A or D</em></p>\n<p><em><em><a href=\"#TOC\" target=\"_self\">[Back to the table of contents]</a></em></em></p>\n<p><strong><a name=\"FAQ8h\" target=\"_blank\"></a>﻿8.h Can I migrate FASB 52 relevant documents?</strong></p>\n<p>Yes, FASB 52 relevant documents can be migrated once the redesigned migration logic is installed in your system. (Note 2725702)</p>\n<p><em><a href=\"#TOC\" target=\"_self\">[Back to the table of contents]</a></em></p>\n<p><strong><a name=\"FAQ8i\" target=\"_blank\"></a>﻿8.i What happens if a billing document which was relevant for SD Revenue Recognition gets cancelled after its migration?</strong></p>\n<p>Cancellations created after the migration post between <span>different G/L accounts</span>, than the original billing documents.<br/><span><br/></span>Details: If we take an invoice item as an example where no revenue has been realized in transaction VF44 yet:</p>\n<ul>\n<ul>\n<li>The invoice posts a debit entry (-) to the <em>customer receivables</em> account and a credit entry (+) to the <em>deferred revenue </em>account<em> </em>(in the source system)</li>\n<li><span>Before</span> the migration, an invoice cancellation posts a debit entry (-) to the <span><em>deferred revenue</em></span> account and a credit entry (+) to the <em>customer receivables</em> account (in the source system)</li>\n<li><span>Af<span>ter</span></span> the migration, an invoice cancellation posts a debit entry (-) to the <span><em>revenue</em></span> account and a credit entry (+) to the <em>customer receivables </em>account (in the source system)</li>\n</ul>\n</ul>\n<p>This is all intended and works as designed, because the invoice cancellation item created after the migration is transferred to the Revenue Accounting engine as a productive SDII RAI. As such it gets assigned to a productive reconciliation key and triggers an invoice correction posting which gets transferred to G/L upon processing. (Unlike the initial load SDIIs which are assigned to the migration reconciliation key.) The invoice correction posts a credit entry (+) to the <em><span>revenue</span> </em>account and a debit entry (-) to the<em> receivables adjustment </em>account<em> (</em>equivalent of the<em> deferred</em> <em>revenue </em>account in SAP RAR).<em><span><br/></span></em></p>\n<p>If the invoice cancellation posted <span>after</span> the migration triggered a true reversal of the original invoice item, the Revenue Accounting engine's invoice correction posting would result in a duplicate accrual posting which cannot be automatically cleared in SAP RAR by the end of the process. This is also the reason why the \"new\" cancellation procedure cannot be used in revenue accounting relevant billing documents.</p>\n<p><em><a href=\"#TOC\" target=\"_self\">[Back to the table of contents]</a></em></p>\n<p><strong><a name=\"FAQ9\" target=\"_blank\"></a>﻿9. What is the revenue correction report?</strong></p>\n<p>The revenue correction report (transaction FARRIC_RR_CORR) is a correction tool meant for migrated SD Revenue Recognition processes which contain billing documents or revenue lines posted after the company code´s or migration package´s transfer date. Documents posted after the transfer date will be processed in the revenue accounting engine once the given company code or migration package is set to productive status. These will then result in postings to the RAR sub-ledger and ultimately to the general ledger, where the Standard RAR posting logic applies.</p>\n<p>If a billing document is posted after the transfer date the Standard RAR posting logic expects its credit side of the posting on the revenue account. However, if the document was still processed with the legacy SD Revenue Recognition logic its credit side was posted to an accruals account instead. The correction report releases the accrued invoiced amount to the revenue account to avoid duplicate accruals postings in the Engine.</p>\n<p>If a revenue line is posted after the transfer date it is not part of the legacy data generated during the migration and hence it is not considered when the cumulative catch-up is calculated in the Standard RAR posting logic. Such revenue lines must therefore be cancelled in the source system to avoid duplicate revenue postings in the Engine.</p>\n<p><em><a href=\"#TOC\" target=\"_self\">[Back to the table of contents]</a></em></p>\n<p><strong><a name=\"FAQ9a\" target=\"_blank\"></a>﻿9.a How do I know if I should use the revenue correction report?</strong></p>\n<p>If your legacy processes were not relevant for SD Revenue Recognition you will never have to run this report.</p>\n<p>If your legacy processes were relevant for SD Revenue Recognition and there were billing documents or revenue lines posted after the transfer date, there is a message in the application log of the migration run stating this and the item impacted must be processed with the correction report before the migration is completed. Transaction FARRIC_RR_CORR / FARRIC_RR_CORR_EXP has to be executed to perform the correction posting(s).</p>\n<p>If you have missed the application log you can also check database table FARRIC_MIGRATED for entries where the INV_CORR flag is not completed (\"C\") or irrelevant (\"SPACE\").</p>\n<p><span>If there is any correction posting required make sure you have installed the redesigned correction tool in your system:</span></p>\n<ul>\n<li>SAP Notes 2715378 &amp; 2702035</li>\n</ul>\n<p><em>Technical fields for this check: VBREVR database entries where BUDAT &gt; transfer date, VBREVE database entries where BUDAT &gt; transfer date and REVFIX is initial</em></p>\n<p><em><em><a href=\"#TOC\" target=\"_self\">[Back to the table of contents]</a></em></em></p>\n<p><strong><a name=\"FAQ10\" target=\"_blank\"></a>﻿10. I have received an error during operational load, can I undo and restart it?</strong></p>\n<p>Yes, as long as the corresponding company code or migration package is still in \"migration\" status, the operational load can be repeated even for individual documents. This makes sense if incorrect or inconsistent RAIs/legacy data were/was generated during the previous operational load.<strong><br/></strong></p>\n<ol>\n<li>Clean up the generated inconsistent / incorrect RAIs using transaction FARRIC_IL_CLEANUP<br/>Deletes the relevant RAIs and their additional data from the RAR inbound processing (IP / ARL).<br/><br/></li>\n<li>Execute transaction FARRIC_OL or FARRIC_OL_EXPERT with processing mode \"RESET\"<br/>Rolls back the changes performed during the original load run.<br/><br/></li>\n<li>Execute transaction FARRIC_OL or FARRIC_OL_EXPERT with processing mode \"LOAD\"<br/>Performs the operational load of the selected documents again.</li>\n</ol>\n<p><em><a href=\"#TOC\" target=\"_self\">[Back to the table of contents]</a></em></p>\n<p><strong><a name=\"FAQ11\" target=\"_blank\"></a>﻿11. Are cancelled billing documents migrated?</strong></p>\n<p>By default, the cancelled SD billing documents are <strong>not</strong> migrated if both the original document and its cancellation were created before the transfer date. This is because the two documents offset each other completely in Financials, so in sum they do not influence the legacy data.</p>\n<p>In productive use, there is no information available at the time of billing about a possible future cancellation, so the SDII RAIs are always generated automatically upon the release of billing document to Financials. As the document´s SDII RAIs are always transferred to the Revenue Accounting Engine in productive mode, cancellation RAIs must also be processed and sent otherwise the two systems were not consistent anymore.</p>\n<p>If the cancellation document´s billing date falls after the transfer date of the migration package, then both the cancelled document´s and the cancellation document´s RAIs are transferred to the Revenue Accounting Engine during migration.</p>\n<p>Another exception are the billing-related &amp; time-based SD Revenue Recognition processes (RRREL = D) . These billing documents are only excluded from the operational load if both the cancelled document and its cancellation are fully realized in transaction VF44. If either of these processes still has open revenue lines then the two documents do not cancel out each other completely and their exact legacy amounts must be accumulated and transferred so that the open revenue is scheduled accordingly in SAP RAR.</p>\n<p><em><a href=\"#TOC\" target=\"_self\">[Back to the table of contents]</a></em></p>\n<p><strong><a name=\"FAQ12\" target=\"_blank\"></a>﻿12. What happens to incomplete sales documents during migration?</strong></p>\n<p>If the migrated sales document has an incompleteness log entry for delivery data, invoice data or pricing then the entire sales document &amp; its document flow is skipped during migration. The document´s missing data must be completed in order to migrate the process.</p>\n<p>Incomplete sales documents with missing delivery/invoice/pricing data never create SDOI RAIs, neither during migration nor during productive use. As normally sales documents are the leading objects for POB creation in case their RAIs are not generated, the RAIs of follow-on documents would not have their parent performance obligation. Due to this, migrating the entire document chain makes no sense as long as the leading document cannot be migrated and the POB is not available.</p>\n<p><em><a href=\"#TOC\" target=\"_self\">[Back to the table of contents]</a></em></p>\n<p><strong><a name=\"FAQ13\" target=\"_blank\"></a>﻿13. What is a migration package and how can I utilize it?</strong></p>\n<p>Migration packages are the units for performing granular migration in a given company code in several steps. Individual migration packages may be configured differently than their company code level settings - migration packages have their own status and transfer date. A typical use case is to assign new migration package in \"Migration\" status to an already \"Productive\" company code. This lets you create and process new documents in the company code immediately in SAP RAR, whereas migrating legacy documents using the migration package is still possible.</p>\n<p>More information: <a href=\"https://help.sap.com/viewer/de5c3a7ada6e41d482ce9ecdbd93aba1/1.3.3/en-US/28394e5754fd0f4be10000000a4450e5.html\" target=\"_blank\">https://help.sap.com/viewer/de5c3a7ada6e41d482ce9ecdbd93aba1/1.3.3/en-US/28394e5754fd0f4be10000000a4450e5.html</a></p>\n<p><em><a href=\"#TOC\" target=\"_self\">[Back to the table of contents]</a></em></p>\n<p><strong><a name=\"FAQ14\" target=\"_blank\"></a>﻿14. Why does the operational load generate RAIs without initial load indicator?</strong></p>\n<p>The operational load migrates an entire sales process to revenue accounting including the documents created both before and after the transfer date defined for the company code or migration package.</p>\n<ul>\n<li>Sales documents with a \"Document date (VBAK-AUDAT)\" before or on the transfer date are considered \"legacy\" orders and their SDOI RAIs are created with the initial load flag</li>\n<li>Billing documents with a \"Billing date (VBRK-FKDAT)\" before or on the tranfer date are considered \"legacy\" invoices and their SDII RAIs are creatd with the initial load flag</li>\n<li>In the fulfillment items the SDFI RAI´s event date field decides whether the initial load flag is set or not; the event date is determined differently for each fulfillment event type:</li>\n<ul>\n<li>Event type \"GI\" - goods issue date (MKPF-BUDAT, LIKP-WADAT_IST)</li>\n<li>Event type \"AD\" - acceptance date (VEDA-VABNDAT)</li>\n<li>Event type \"RO\" - release order document date (VBAK-AUDAT)</li>\n<li>Event type \"PI\" - incoming vendor invoice posting date (BKPF-BUDAT)</li>\n<li>Event type \"IB\" - billing date (VBRK-FKDAT)</li>\n</ul>\n</ul>\n<p>If a RAI is marked with the initial load flag it can only be processed while the given company code is still in migration status, whereas RAIs without the initial load flag can only be processed when the company code has already been switched to productive status.</p>\n<p><em><a href=\"#TOC\" target=\"_self\">[Back to the table of contents]</a></em></p>\n<p><strong><a name=\"FAQ15\" target=\"_blank\"></a>﻿15. Is it possible to migrate costs?</strong></p>\n<p>Yes, as of SAP Note 2754952 (REVRECSD SP20) it is also possible to send legacy conditions for costs realized before the migration.</p>\n<p><em><a href=\"#TOC\" target=\"_self\">[Back to the table of contents]</a></em></p>", "noteVersion": 14}, {"note": "2719185", "noteTitle": "2719185 - The redesigned SD Revenue Recognition type \"D\" migration is now available!", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>Due to design issues with the original migration logic of type \"D\" SD Revenue Recognition processes in the operational load tool these time-based &amp; billing-related processes were completely excluded from the migration with SAP Note 2690763 until a new solution could be rolled out. The new redesigned solution is now available for download via SAP Notes and therefore the limitation of type \"D\" processes not being supported is herewith lifted. This SAP Note contains detailed information about prerequisite correction notes, the new redesigned migration logic, the required configuration and other special topics related to migration.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>SD Revenue Accounting, SD Revenue Recognition, operational load, FARRIC_OL, FARRIC_OL_EXPERT, FARRIC_RR_CORR, revenue correction, invoice correction, invoice based, type \"D\", typ D, RRREL=D, new logic, redesign, migration</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>Customers using SD Revenue Recognition type \"D\" legacy processes can now migrate their documents to SAP Revenue Accounting and Reporting.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p id=\"\"><span><strong>Prerequisite correction notes:</strong></span></p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"1\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td><strong>2715378 </strong></td>\n<td><strong>SP18</strong> / SAPK-10018INREVRECSD</td>\n<td>Operational load: SD Revenue Recognition correction report redesign</td>\n</tr>\n<tr>\n<td><strong>2711499 </strong></td>\n<td><strong>SP18</strong> / SAPK-10018INREVRECSD</td>\n<td>Operational load: Incorrect correction report related messages in the job log</td>\n</tr>\n<tr>\n<td><strong>2708957 </strong></td>\n<td><strong>SP18</strong> / SAPK-10018INREVRECSD</td>\n<td>Enablement of Migration for RRREL = D</td>\n</tr>\n<tr>\n<td><strong>2708045 </strong></td>\n<td><strong>SP18</strong> / SAPK-10018INREVRECSD</td>\n<td>Operational load: Revenue lines posted after take-over date should be part of LEGACY_SF</td>\n</tr>\n<tr>\n<td><strong>2707729 </strong></td>\n<td><strong>SP18</strong> / SAPK-10018INREVRECSD</td>\n<td>Incorrect SDIG start date and end date determination</td>\n</tr>\n<tr>\n<td><strong>2706329 </strong></td>\n<td><strong>SP18</strong> / SAPK-10018INREVRECSD</td>\n<td>Revenue Accountig Type '\"G\" for Order Items</td>\n</tr>\n<tr>\n<td><strong>2705261 </strong></td>\n<td><strong>SP18</strong> / SAPK-10018INREVRECSD</td>\n<td>Revenue accounting relevance not saved correctly into billing document items</td>\n</tr>\n<tr>\n<td><strong>2703852 </strong></td>\n<td><strong>SP18</strong> / SAPK-10018INREVRECSD</td>\n<td>Simulation returns unexpected invoice RAIs and omits invoice-generated POB RAIs</td>\n</tr>\n<tr>\n<td><strong>2700614 </strong></td>\n<td><strong>SP18</strong> / SAPK-10018INREVRECSD</td>\n<td>Operational load: Revenue correction report results in short dump or changes non-migrated processes</td>\n</tr>\n<tr>\n<td><strong>2700103 </strong></td>\n<td><strong>SP18</strong> / SAPK-10018INREVRECSD</td>\n<td>Operational load: Improved log handling for failed legacy data creation</td>\n</tr>\n<tr>\n<td><strong>2677479 </strong></td>\n<td><strong>SP17</strong> / SAPK-10017INREVRECSD</td>\n<td>Operational load: LOAD and RESET of SD Revenue Recognition documents deactivated in S4CORE systems</td>\n</tr>\n<tr>\n<td><strong>2653276 </strong></td>\n<td><strong>SP17</strong> / SAPK-10017INREVRECSD</td>\n<td>Operational load: Open revenue lines are getting lost from the revenue schedule</td>\n</tr>\n<tr>\n<td><strong>2645301 </strong></td>\n<td><strong>SP17</strong> / SAPK-10017INREVRECSD</td>\n<td>Inconsistencies caused by invoice-generated POB configuration</td>\n</tr>\n<tr>\n<td><strong>2702035 </strong></td>\n<td><strong>SAP_APPL</strong> core correction (!)</td>\n<td>Operational load: Correction revenue line deleted during RESET</td>\n</tr>\n</tbody>\n</table></div>\n<p><span><strong>Redesigned migration logic:</strong></span></p>\n<p>The redesigned migration utilizes the invoice-generated POB functionality, which has been extended and enhanced for these scenarios in the correction notes listed above. The core difference is that as of these SAP Notes it is possible to configure sales item categories for revenue accounting relevance \"G\" which up to now was only possible for billing documents using customizing activity \"Create Performance Obligation for SD Billing Item\". This type \"G\" revenue accounting relevance will result in the creation of invoice-generated POBs using special SDIG RAIs.</p>\n<p>General behavior of type \"G\" processes:</p>\n<p><span>RAI generation:</span></p>\n<ul>\n<li>Sales documents of relevance type \"G\" create no RAIs</li>\n<li>Delivery documents of relevance type \"G\" create no RAIs</li>\n<li>Billing documents of relevance type \"G\" create SD01/SDIG RAIs as well as SD03/SDII RAIs</li>\n</ul>\n<p><span>Predecessor determination:</span></p>\n<ul>\n<li>In legacy type \"D\" processes every single billing document creates its own POB to migrate the independent billing-related legacy processes into independent RAR processes with their own legacy data, legacy revenue schedule and contract details.<br/><br/></li>\n<li>In new/productive processes invoices are the leading objects in type \"G\" processes, which means that invoice cancellations, credit memos, credit memo cancellations, debit memos and their cancellations are all linked to the closest invoice´s POB &amp; contract. The only exceptions are type \"G\" credit/debit memos created with reference to type \"G\" credit/debit memo requests since these are considered standalone processes which generate their own POBs. </li>\n</ul>\n<p><span>Legacy data:</span></p>\n<ul>\n<li>LEGACY: Type \"D\" processes are migrated into time-based (fulfillment type = \"T\") POBs using linear distribution (deferral method = \"S\")</li>\n<li>LEGACY_C: Type \"D\" legacy revenues for the cumulative catch-up are accumulated from process´ revenue lines posted before transfer date</li>\n<li>LEGACY_SF: It is also possible to transfer the forecasted revenue schedule from the legacy process using the operational load tool´s expert mode. The legacy revenue schedule of type \"D\" processes is a list of open revenue lines and revenue lines posted after the transfer date. Open revenue lines due before transfer date are shifted to the first day after transfer date.</li>\n</ul>\n<p><span>POB start date and end date determination:</span></p>\n<ul>\n<li>The POB start date and end date of legacy type \"D\" processes is determined based on the revenue lines of the process. The start date is set as the first day of the first accrual period, whereas the end date is set as the last day of the last accrual period. <br/><br/></li>\n<li>In new/productive type \"G\" revenue accounting processes the rules for start date and end date determination is explained in SAP Note <strong>2707729</strong>.</li>\n</ul>\n<p><span><strong>Required configuration:</strong></span></p>\n<ul>\n<li><strong>Legacy SD Revenue Recognition type \"D\" processes can only be migrated into type \"G\" revenue accounting</strong></li>\n<li><strong>Legacy SD Revenue Recognition type \"A\", \"B\" and \"F\" processes cannot be migrated into type \"G\" revenue accounting</strong></li>\n<li><strong>Legacy SD Standard processes cannot be migrated into type \"G\" revenue accounting</strong></li>\n</ul>\n<p>This configuration must be maintained on sales item category level under the following customizing activity:</p>\n<p>SAP Customizing Implementation Guide (SPRO)<br/>       Sales and Distribution<br/>            Revenue Accounting and Reporting<br/>                  Maintain Revenue Accounting Item Settings</p>\n<p><span><strong>Correction report:</strong></span></p>\n<p>In case a legacy type \"D\" process has revenue recognition documents or billing documents posted after the transfer date the revenue correction report must be executed to offset the superfluous financials entries generated by these documents. Superfluous revenue recognition documents must be cancelled using transaction VF46, whereas the deferral postings of billing documents must be released to the revenue accounts directly using transaction VF44 in case the document was posted after transfer date. This is required to avoid duplicate revenue postings and invoice correction postings in RAR.</p>\n<p>In case a given process is marked for revenue correction or invoice correction the operational load tool generates an entry into database table FARRIC_MIGRATED with \"INV_CORR\" flag set to \"X\" and the following messages are written into the job log:</p>\n<ul>\n<li>FARRIC_OL / 045 : Revenue posted after take-over date (SD doc. &amp;1 needs correction)</li>\n<li>FARRIC_OL / 043 : Billing doc. &amp;1 posted after take-over date (SD doc. &amp;2 needs correction)</li>\n</ul>\n<p>These documents (listed in parenthesis) must be processed using transaction FARRIC_REV_CORR to trigger the automated revenue corrections and invoice corrections. These correction postings can only be executed till the the corresponding company code or migration package is still in \"Migration\" status. This step must be executed before switching over to \"Productive\" status!</p>\n<p><span><strong>Reset and cleanup:</strong></span></p>\n<ul>\n<li>Reset﻿ works just like in type \"A\" and \"B\" SD Revenue Recognition processes. The root sales document must be entered for processingmode \"RESET\" in transaction FARRIC_OL. The operational load tool selects all related billing documents and resets them to their original revenue recognition relevance.<br/><br/></li>\n<li>Cleanup works slightly differently than in type \"A\" and \"B\" SD Revenue Recognition processes. In revenue accounting the deletion of operational load data is triggered on performance obligation (POB) level and in the invoice-generated POB functionality each legacy type \"D\" billing document creates its own POB. Therefore, the initial load cleanup must be executed for each type \"D\" billing document individually in FARR_IL_CLEANUP!<br/><br/></li>\n<li>If the initial load cleanup is not executed properly after a reset the error message \"FARRIC_OL 021 Legacy data creation for document ID &amp;1 failed (accounting principle &amp;2)\" may get triggered during the next operational load.</li>\n</ul>\n<p><span><strong>Restrictions:</strong></span></p>\n<ul>\n<li><strong>The migration of SD Revenue Recognition data is not supported from S4CORE source systems</strong></li>\n</ul>", "noteVersion": 3}, {"note": "2580963", "noteTitle": "2580963 - RAR upgrade preparation - additional hints", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p id=\"\"> Preparation for upgrade of the SAP Revenue Accounting and Reporting</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>For upgrading the SAP RAR to higher release, please follow instructions contained in the SAP RAR Administrator’s Guide and read release notes thoroughly. Please use always the most recent version of the Guide.</p>\n<p>1  Testing scope for RAR release upgrade</p>\n<p>As in the case of the release upgrade of other SAP software components, when upgrading SAP RAR all relevant functions of RAR used in the specific implementation should be validated in the test environment before the new release is deployed in the production system.</p>\n<p>Same recommendations apply here as for the GoLive case (end-to-end process testing including Inflight Checks, Data Validation) – see <a href=\"/notes/2580924\" target=\"_blank\">SAP Note 2580924</a>.</p>\n<p>In addition, customizations and modifications require special attention. In general, all customizations and modifications should follow rules as described here: <br/> <a href=\"https://wiki.scn.sap.com/wiki/pages/viewpage.action?pageId=448470908\" target=\"_blank\">Development Guidelines for Greenfield Implementation in sync with SAP Custom Code Management</a></p>\n<p>2   Updating the Support Package stack</p>\n<p>Updating a Support Package stack is a regular maintenance activity, which should be performed regularly. Keeping the software components on the recent level for the entire stack (e.g. SAP_BASIS, SAP_ABA, etc.) enables an appropriate maintenance of the system and specifically of SAP RAR. Keeping the SP level up-to-date will provide pro-actively corrections to all known errors found for the product.</p>\n<p>Implementation of a Support Package requires a thorough testing of critical RAR functions. Even, if a new Support Package shouldn’t impact the existing solution, the testing would minimize the risk of destabilization of RAR.</p>", "noteVersion": 1}, {"note": "2610856", "noteTitle": "2610856 - FAQ: Condition handling in SD Integration Component", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p id=\"\">In this SAP Note, you can find the frequently asked questions and answers about the condition handling during RAIs preparation in SD Integration Component.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Revenue Accounting, SD IC, Integration component, SD Revenue Recognition, conditions, main condition, VPRS, FAQ, frequently asked questions, answers</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong>1. Which condition classes are transferred from SD Integration Component to SAP RAR?</strong></p>\n<p>Only condition class 'Discount and surcharge' (KOAID = A), 'Prices' (KOAID = B) and 'Expense reimbursement' (KOAID = C) are transferred from SD Integration Component to SAP RAR. All other condition classes are not transferred.</p>\n<p>Exception:</p>\n<ul>\n<li>The condition was added via customer-specific BADI implementation (FARRIC_BADI_ORDER method INCLUDE_CONDITIONS)</li>\n</ul>\n<p><strong>2. Which condition categories are transferred to SAP RAR?</strong></p>\n<p>The condition category 'Tax' (KNTYP = D), 'Cash discount' (KNTYP = E) and 'Down Payment' (KNTYP = e) are excluded from the transfer to SAP RAR. All other conditions categories are transferred to SAP RAR as per standard.</p>\n<p>Exception:</p>\n<ul>\n<li>The condition was added via customer-specific BADI implementation (FARRIC_BADI_ORDER method INCLUDE_CONDITIONS)</li>\n</ul>\n<p><strong>3. Are the inactive conditions transferred to SAP RAR?</strong></p>\n<p>The inactive conditions (KINAK &lt;&gt; space) are normally not transferred to SAP RAR.</p>\n<p>Exceptions:</p>\n<ul>\n<li>The condition was added via customer-specific BADI implementation (FARRIC_BADI_ORDER method INCLUDE_CONDITIONS)</li>\n<ul>\n<li>Statistical conditions are sent with their original condition value, whereas non-statistical conditions are sent with zero amount</li>\n</ul>\n<li>The condition was added in an attempt to find a main price condition (see <strong>6.</strong>)</li>\n<ul>\n<li>Inactive main price conditions are sent with zero amount</li>\n</ul>\n<li>The condition is an inactive statistical internal price condition in a BOM sub-item (SAP Note 2626711)</li>\n<ul>\n<li>Inactive internal price conditions are sent with their original condition value</li>\n</ul>\n</ul>\n<p><strong>4. Which conditions are considered 'Price' (P) conditions during RAIs creation as per standard?</strong></p>\n<p>As per standard, all conditions which are transferred to SAP RAR (see <strong>1.</strong>, <strong>2.</strong> and <strong>3.</strong>) except for cost conditions (see <strong>5.</strong>) are considered 'Price' (P) conditions. For each of these conditions 'Main' condition flag is set as per standard (see <strong>6.</strong>).</p>\n<p><strong>5. Which conditions are considered 'Cost' (C) conditions during RAIs creation as per standard?</strong></p>\n<p>As per standard, only statistical internal price conditions (KSTAT = X, KOAID = B, KNTYP = G) are considered 'Cost' (C) conditions during RAIs creation. In cross-company (inter-company) sales processes special rules apply, see <strong>13.</strong></p>\n<p>The RAR Sales integration only supports fulfillment costs (cost of goods sold), contract acquisition costs (capitalized costs) are not integrated in the Standard. Whenever 'Cost' is mentioned in this document we refer to <strong>fulfillment costs</strong>. In case your business requires the implementation of contract acquisition costs this must be implemented within a customer-specific solution (consulting).</p>\n<p>The following condition categories are not considered 'Cost' (C) conditions and therefore, are excluded from the transfer to SAP RAR:</p>\n<ul>\n<li>Intercompany billing prices                     (KNTYP = I)</li>\n<li>Standard costs                                       (KNTYP = S)</li>\n<li>Moving costs                                          (KNTYP = T)</li>\n<li>Costing conditions                                  (KNTYP = Q)</li>\n<li>Transfer prices                                       (KNTYP = b)</li>\n<li>Profit center costs                                  (KNTYP = h)</li>\n</ul>\n<p><strong>6. Which conditions are marked as 'Main' conditions during RAIs creation as per standard?</strong></p>\n<p>As per standard, all 'Price' (P) conditions (KOAID = B) are considered 'Main' price conditions. All statistical internal price conditions (KSTAT = X, KOAID = B, KNTYP = G) are considered 'Main' cost conditions. All main conditions (including zero conditions) are transferred to SAP RAR. Zero non-main conditions are not transferred to SAP RAR.</p>\n<p>In case no main price condition is found among the active conditions, the fallback scenario checks all inactive pricing conditions. All non-statistical conditions (KSTAT = space) of condition class 'Prices' (KOAID = B) are marked as main price conditions and hence transferred to SAP RAR.</p>\n<p><strong>7. Are 'accruals' relevant conditions transferred to SAP RAR?</strong></p>\n<p>Accruals relevant 'Cost' (C) conditions (KRUEK = X) are generally not relevant for SAP RAR, so they are ignored during the RAIs creation.</p>\n<p><strong>8. Does SAP RAR accept more than one main price condition per RAI?</strong></p>\n<p>No, the Adapter Reuse Layer and the Revenue Accounting Engine only accepts one condition marked as main price condition per RAI. If you are using business scenarios where several non-statistical price conditions are passed or all conditions may be inactive, it can lead to multiple main conditions. In this case, you may have to implement the BAdI FARRIC_BADI_ORDER to remove unwanted main condition. Make sure that <strong><span>only one</span></strong> of these conditions is passed to the Engine as main condition even if multiple conditions are proposed. Otherwise you may get the error \"FARR_RAI 350: There is only one main condition type allowed for item ID &amp;1/category &amp;2\" during RAI processing.</p>\n<p><strong>9. Which method can be used to completely remove the individual unwanted conditions?</strong></p>\n<p>FARRIC_BADI_ORDER method \"IF_FARRIC_ORDER~EXCLUDE_CONDITION\" and FARRIC_BADI_INVOICE method \"IF_FARRIC_INVOICE~EXCLUDE_CONDITION\"can be used to completely remove the individual unwanted conditions.</p>\n<p><strong>10. Which method can be used to manipulate the main condition flag of RAI conditions?</strong></p>\n<p>FARRIC_BADI_ORDER method \"IF_FARRIC_ORDER~ORDER_DATA_TO_ARL\" and FARRIC_BADI_INVOICE method \"IF_FARRIC_ORDER~INVOICE_DATA_TO_ARL\" can be used to set or clear the \"MAIN_COND_TYPE\" flag of the individual conditions. Keep in mind that there must be exactly one price condition marked as main condition.</p>\n<p><strong>11. Why a more specific SAP Standard main condition filter cannot be provided?</strong></p>\n<p>A more specific SAP Standard main condition filter cannot be provided because the SD Integration Component cannot decide which condition the given Customer-specific business scenario would require to be marked as main condition, and hence to be used for the account determination in the Revenue Accounting Engine.</p>\n<p><strong>12. How are the RAI conditions determined in milestone billing plan, periodic billing plan and non-billing plan scenarios?</strong></p>\n<p><span>a) Periodic billing plans (FKREL = I)<br/></span>The SDOI RAI conditions are generated based on the SDPI RAI conditions which belong to the given sales document item. This means that the system first generates each SDPI RAI condition based on the corresponding billing plan item. Each condition type (KSCHL) of each billing plan item is accumulated and collected into an SDOI RAI condition under the same condition type (KSCHL). Therefore, the SDOI RAI condition´s amounts are equal to the sum of all corresponding SDPI RAI conditions. <em>(Except for permanently blocked billing plan items where the statistical conditions are accumulated even without an existing SDPI RAI, see SAP Note 2601924)</em></p>\n<p>The SDPI RAI conditions are generated differently depending on the billing status of the corresponding billing plan item. In case the billing plan item has already been billed (FPLT-FKSAF = C), the corresponding invoice's pricing conditions are used as a basis for the SDPI RAI conditions. However, in the open billing plan items (FPLT-FKSAF = A) the pricing conditions of the corresponding billing plan item are used as a basis.</p>\n<p><span>b) Milestone billing plans (FKREL = I)</span><br/>The SDOI RAI conditions are generated based on the sales order item's pricing conditions. Then the system distributes the SDOI RAI condition's amounts between the SDPI RAI conditions based on the corresponding billing plan item´s milestone percentage (%). Unlike in the periodic billing plan in milestone billing plans the billing status (FPLT-FKSAF) does not influence which pricing conditions are used as a basis for the RAI conditions. (This was changed in REVRECSD SP16 / SAP Note 2603935)</p>\n<p><br/><span>c) All other non-billing plan scenarios</span><br/>The SDOI RAI conditions are generated based on the sales order item's pricing conditions, regardless of the billing status of the given item.</p>\n<p><strong>13. What is the current behavior of cost conditions in the intercompany sales process?</strong></p>\n<p>The statistical internal price conditions are not passed to SAP RAR even if they are present in the inter-company sales document. The internal price condition represents the costs of the delivering company code, whereas the actual costs of the billing company code are the amounts in the intercompany billing document which is paid by the billing company code to the delivering company code. Since the POB and the RAR contract are generated for the billing company code, the costs must be taken from the intercompany billing document to represent the actual fulfillment costs for the right organizational unit.</p>\n<ol start=\"1\">\n<li>Sales documents: The statistical internal price is not sent as cost condition to SAP RAR (SAP Note <a href=\"/notes/2430664\" target=\"_blank\">2430664</a>). Instead, the condition  with the highest step number marked with the \"inter-company billing condition\" flag (KFKIV) is passed to SAP RAR as main 'Cost' (C) condition. (SAP Note <a href=\"/notes/2651782\" target=\"_blank\">2651782</a>)</li>\n<li>Delivery/Goods issue: The statistical internal price is not sent as cost condition to SAP RAR. See SAP Note <a href=\"/notes/2397880\" target=\"_blank\">2397880</a>.</li>\n<li>Intercompany billing document: As of SAP Note <a href=\"/notes/2537192\" target=\"_blank\">2537192</a>, the intercompany invoice RAI's are not sent to RAR as SDII RAIs, however as of REVRECSD 100 SP17 and SAP RAR 1.3 Feature Package Stack 06 (07/2018) the intercompany billing documents are capable of generating SDFI RAIs which can carry the actual fulfillment costs to SAP RAR. The implementation of the following SAP Notes is required for this:</li>\n</ol>\n<ul>\n<ul>\n<li><a href=\"/notes/2651782\" target=\"_blank\">2651782</a> - Intercompany Invoice and Incoming Vendor Invoice create Fulfillment Event</li>\n<li><a href=\"/notes/2649839\" target=\"_blank\">2649839</a> - New Event Types And Cost Correction Related Posting</li>\n<li><a href=\"/notes/2654138\" target=\"_blank\">2654138</a> - Data elements for new Event Types</li>\n</ul>\n</ul>\n<p>Refer to the link for more information: <a href=\"https://help.sap.com/viewer/de5c3a7ada6e41d482ce9ecdbd93aba1/1.3%20FP06/en-US/2004a2699bfe404fa5b47e8064f1ae02.html\" target=\"_blank\">https://help.sap.com/viewer/de5c3a7ada6e41d482ce9ecdbd93aba1/1.3%20FP06/en-US/2004a2699bfe404fa5b47e8064f1ae02.html</a></p>\n<p><strong>14. How does the account determination for cost conditions work?</strong></p>\n<p>The main conditions must always have a reference account which can be used for the BRF+ account determination. Therefore a special coding was added to the sales account determination logic (FV45PF0K_KONTENFINDUNG), which triggers the revenue account determination for sales document conditions. However, even if this is triggered the SD Core does not derive an account in some scenarios:</p>\n<ul>\n<li>if the condition is statistical</li>\n<li>if the condition is inactive</li>\n</ul>\n<p>In these cases the SD integration component simulates an account determination using the C* tables (VKOA) for all cost conditions and inactive conditions. During simulation the condition is switched to 'active' and 'non-statistical' and the function module 'RV_INVOICE_ACCOUNT_DETERM' is therefore capable of deriving the G/L accounts. You can therefore change the the pricing procedure configuration in V/08 and assign a specific account key for the cost condition. The G/L account for the cost condition (P&amp;L) should then be maintained in VKOA for the corresponding access sequence with the same account key.</p>\n<p><strong>15. How to populate the \"Standalone Selling Price\" (SSP) conditions in SAP RAR in case of inactive and/or statistical SD conditions?</strong></p>\n<p>There are two standard ways to populate the \"Standalone Selling Price\" (SSP) conditions in Revenue Accounting &amp; Reporting (RAR). Either the BRF+ configuration is used to set/calculate the SSP conditions or the conditions are transferred from the sender components like SD, given the \"SSP\" condition is defined in the corresponding RAR customizing (transaction SPRO -&gt; Financial Accounting (New) / Revenue Accounting -&gt; Define Condition Types for SSP &amp; ROR).</p>\n<p>However, the RAR customizing information is not available in the SD sender component. Therefore, creating the SSP RAI condition is not always trivial even if your company defined a statistical condition to represent the SSP in SD Pricing procedure. There are scenarios where the conditions become inactive in sales orders (e.g. having an empty billing plan, setting a rejection reason), and inactive conditions are normally not sent to RAR. The SSP condition may still be required.<br/><br/>To resolve such issues there are BAdI-methods available where even inactive and/or statistical conditions can be included to the RAI conditions. For example for empty billing plans the SD integration generates a zero-value placeholder condition based on the sales order conditions and as of SAP Note 2930908 it is possible to use BAdI FARRIC_BADI_ORDER method IF_FARRIC_ ORDER~INCLUDE_SDPI_CONDITIONS to include the inactive, statistical conditions as well. Inactive statistical conditions can be included with their real net value, unlike other inactive conditions which are included with zero-value. Note that internal price (KSTAT = X, KOAID = B, KNTYP = G) is not considered statistical condition in RAR context, so it can also only be included with zero-value.</p>\n<p>To avoid errors caused by missing SSP conditions BAdI FARRIC_BADI_ORDER method IF_FARRIC_ORDER~INCLUDE_SDPI_CONDITIONS can be implemented.</p>", "noteVersion": 31}, {"note": "2392956", "noteTitle": "2392956 - Revenue Accounting and Reporting: Integrating External (non-SAP) Sender Components", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>A non-SAP operational system is not integrated with the S<em>AP Revenue Accounting and Reporting 1.3 </em>add-on<em> or Revenue Accounting in S/4HANA 1809.</em></p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>SAP Revenue Accounting and Reporting, Revenue Accounting, revenue accounting item, RAI, integration of 3rd party systems to SAP Revenue Accounting and Reporting</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>You use the <em>SAP Revenue Accounting and Reporting 1.3 </em>add-on or Revenue Accounting in S/4HANA 1809<em>. </em>You want to integrate a non-SAP operational system to <em>SAP Revenue Accounting and Reporting.</em></p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>See attached PDF.</p>", "noteVersion": 6}, {"note": "2382402", "noteTitle": "2382402 - SAP Revenue Accounting 1.3: Release Information Note", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>This Release Information Note (RIN) contains information and references about the product version 'SAP Revenue Accounting 1.3'.<br/>Note: This SAP Note is subject to change.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Revenue Accounting and Reporting 1.3</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p><strong><span>General Information</span></strong></p>\n<p>Add-on product version SAP Revenue Accounting 130 consists of one software component version:</p>\n<ul>\n<li>REVREC 130 (instance Revenue Accounting).</li>\n</ul>\n<p>SAP Revenue Accounting 1.3 bases on SAP enhancement package 5 for SAP ERP 6.0.</p>\n<p>Furter installations are also released and supported:</p>\n<ul>\n<li>SAP Enhancement Package 6 for SAP ERP 6.0</li>\n<li>SAP Enhancement Package 7 for SAP ERP 6.0</li>\n<li>SAP Enhancement Package 8 for SAP ERP 6.0</li>\n<li>SAP S/4HANA ON-PREMISE 1511</li>\n<li>SAP S/4HANA 1610</li>\n<li>SAP SFINANCIALS 1.0</li>\n<li>SAP SFINANCIALS 1503</li>\n<li>SAP S/4HANA FINANCE 1605</li>\n<li>SAP S/4HANA FINANCE 1610</li>\n<li>SAP S/4HANA FINANCE 1709</li>\n</ul>\n<p>Further information about the installation of REVREC 130 could be found in note <a href=\"/notes/2386978\" target=\"_blank\">2386978</a> (Release Strategy Note for REVREC 130). <br/><br/>Integration component REVRECSD 100 was removed from SAP Revenue Accounting with version 1.1 and is now shipped with the separate product version \"SAP SALES INTEGR SAP RAR 1.0 \" (SAP Sales Integration with SAP Revenue Accounting and Reporting 1.0)</p>\n<p>In chapter 'Solution' of this note, you will get additional information about the specifics of the initial delivery and the different support package stacks as well (incl. additional required notes).<br/><br/>The SP stacks of product version 'SAP Revenue Accounting 1.3' contain new SPs of the included software component version REVREC 130. The product version is released to customers with minimum SP stack 00 of SAP Revenue Accounting 1.3. However, the use of Revenue Accounting and Reporting requires a very conscientious and responsible approach to the subject. Thus SAP strongly recommends to implement the latest Support Package as soon as it is available.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>In this note you can find information for the following support and feature packages:</p>\n<ul>\n<li><a href=\"#SP0_Initial_Shipment\" target=\"_self\">Initial Shipment Stack (SP00)</a></li>\n<li><a href=\"#SP3_Support_Package\" target=\"_self\">Support Package Stack 03 (08/2017)</a></li>\n<li><a href=\"#FP4_Feature_Package\" target=\"_self\">Feature Package Stack 04 (12/2017)</a></li>\n<li><a href=\"#FP5_Feature_Package\" target=\"_self\">Feature Package Stack 05 (03/2018)</a></li>\n<li><a href=\"#FP6\" target=\"_self\">Feature Package Stack 06 (07/2018)</a></li>\n<li><a href=\"#FP7\" target=\"_self\">Feature Package Stack 07 (11/2018)</a></li>\n<li><a href=\"#SP8\" target=\"_self\">Support Package Stack 08 (03/2019)</a></li>\n<li><a href=\"#SP9\" target=\"_self\">Support Package Stack 09 (06/2019)</a></li>\n<li><a href=\"#SP10\" target=\"_self\">Support Package Stack 10 (09/2019)</a></li>\n<li><a href=\"#SP11\" target=\"_self\">Support Package Stack 11 (11/2019)</a></li>\n<li><a href=\"#SP12\" target=\"_self\">Support Package Stack 12 (06/2020)</a></li>\n<li><a href=\"#SP13\" target=\"_self\">Support Package Stack 13 (11/2020)</a></li>\n<li><a href=\"#SP14\" target=\"_self\">Support Package Stack 14 (05/2021)</a></li>\n<li><a href=\"#SP15\" target=\"_blank\">Support Package Stack 15 (03/2022)</a></li>\n<li><a href=\"#SP16\" target=\"_self\">Support Package Stack 16 (10/2022)</a></li>\n<li><a href=\"#SP17\" target=\"_blank\">Support Package Stack 17 (04/2023)</a></li>\n<li><a href=\"#SP18\" target=\"_blank\">Support Package Stack 18 (10/2023)</a></li>\n<li><a href=\"#SP19\" target=\"_blank\">Support Package Stack 19 (07/2024)</a></li>\n</ul>\n<p><span><strong><a name=\"DOC\" target=\"_blank\"></a>﻿Documentation of SAP Revenue Accounting and Reporting 1.3</strong></span></p>\n<p><a href=\"https://help.sap.com/docs/SAP_REVENUE_ACCOUNTING_AND_REPORTING?version=1.3%20Latest\" target=\"_blank\">Here</a>, you can find the latest documentation of SAP Revenue Accounting and Reporting 1.3.</p>\n<p>It contains references to the application help, the administration guide, the migration guide, information about important new functions of various support packages, and documentation for SD integration.</p>\n<p><strong><span><a name=\"SP0_Initial_Shipment\" target=\"_blank\"></a></span></strong><strong><span>Initial Shipment</span><strong><span> Stack (SP00)</span></strong></strong></p>\n<p><span>Upgrade from SAP Revenue Accounting 1.1 and 1.2:</span></p>\n<p>Mandatory pre-check for upgrade described in SAP note <a href=\"/notes/2360465\" target=\"_blank\">2360465</a>.</p>\n<p>When Upgrading to REVREC 1.3 SP 1 or SP2 the note 2344993 is implemented in the system. In order to avoid any data inconsistencies in Revenue Accounting, the dependant note 2345085 needs to be implemented before any RAI processing has been started, if not yet already part of the relevant release of the FI system. Starting with the REVREC 1.3 SP3 this dependency is obsolete.</p>\n<p> </p>\n<p><span>Important Notes to read:</span></p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"3\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td><strong>Note ID</strong></td>\n<td><strong>Description</strong></td>\n</tr>\n<tr>\n<td><span class=\"sapUxAPObjectPageHeaderIdentifierContainer\" id=\"__xmlview2--idObjectPageHeader-identifierLineContainer\">2463880 </span></td>\n<td>SAP Revenue Accounting and Reporting: Data validation report</td>\n</tr>\n<tr>\n<td><span class=\"sapUxAPObjectPageHeaderIdentifierContainer\">2463528</span></td>\n<td>Revenue Accounting and Reporting 1.3: Important Notes Delivered with Support Package 1 and Higher</td>\n</tr>\n</tbody>\n</table></div>\n<p><span>Additional notes to be applied (as part of this Stack):</span></p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"3\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td><strong>Note ID</strong></td>\n<td><strong>Description</strong></td>\n<td><strong>Manual Activity Required</strong></td>\n</tr>\n<tr>\n<td><span ar-sa;\"=\"\" arial',sans-serif;=\"\" calibri;=\"\" de;=\"\" en-us;=\"\" lang=\"EN-US\" minor-latin;=\"\" mso-ansi-language:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" mso-fareast-theme-font:=\"\">2309514</span></td>\n<td>Internal error: INCOMPLETE_MESSAGE, Message no. SCWN200</td>\n<td> </td>\n</tr>\n<tr>\n<td>2402860</td>\n<td>Fix issue for update posting account determination issue for 1.3</td>\n<td> </td>\n</tr>\n<tr>\n<td>\n<p>2402895</p>\n</td>\n<td>Fix issue for handling Purely new contract in Program B</td>\n<td>\n<p> </p>\n</td>\n</tr>\n<tr>\n<td>\n<p>2456711</p>\n</td>\n<td>Inflight check to prevent data inconsistency</td>\n<td>Yes</td>\n</tr>\n<tr>\n<td>\n<p>2460096</p>\n</td>\n<td>Inflight check: further checks to prevent data inconsistency</td>\n<td>Yes</td>\n</tr>\n<tr>\n<td>\n<p>2459695</p>\n</td>\n<td>RA-consistency check - liability/asste calculation E10, E11, E12, E14</td>\n<td>Yes</td>\n</tr>\n</tbody>\n</table></div>\n<p><strong><span><a name=\"SP3_Support_Package\" target=\"_blank\"></a>﻿Support Package </span><strong><span>Stack 03 (08/2017)</span></strong></strong></p>\n<p><strong><span>Additional notes to be applied (as part of this Stack):<br/></span></strong>If you use the integration to SAP Results Analysis you also have to implement note <strong>2504568</strong>.</p>\n<p>If you use the  integration to SAP Hybris Billing you also have to implement note <strong>2515905</strong>.</p>\n<p>Please also make sure that notes <strong>2223641</strong> and<strong> 1910924</strong> are implemented or your Netweaver installation is current.</p>\n<p>If you are on basis release 740, SP &lt; 8 please consider note <strong>2528958.</strong></p>\n<p>Note that SAP had to restrict the scenario of contract modifications with value relevant POBs and event type Customer Invoice (CI) as per note <strong>2485732</strong>. As a result the determination of change type <em>contract modification</em> in such a scenario was disabled and <em>change of estimates</em> is determined instead (with the corresponding accounting treatment). This is to avoid issues in the calculation of the remaining SSP. Functionality can be enabled again through a pilot note. If this scenario is relevant to you, please contact SAP through an incident under component FI-RA.</p>\n<p>SAP Revenue Accounting assists you in your efforts to manage the revenue recognition process along the five steps outlined by IFRS15 and ASC606. You can transform revenue recognition related events such as order inceptions or changes, fulfilments and invoices from various sender components (e.g. SD, CRM Service etc.) into revenue accounting items (RAI). When revenue accounting items (RAI) are processed, contracts and performance obligations are created or updated. The system also updates other database tables such as for deferral items, fulfilments and invoices etc.</p>\n<p>In some specific and rare situations it can happen that the data in these database tables is not stored consistently. This could be due to a system dump in conjunction with implicit database commits in custom enhancements like BAdI implementations, implicit enhancements or modifications.  The implicit database commits can be caused by RFC calls, wait statements, sending Error, Warning, Information messages etc.</p>\n<p>To ensure overall data consistency the logic to update Revenue Accounting database tables along the revenue accounting process has been changed with this support package. The system will assure that the database is only updated once all steps of a process performed in Revenue Accounting are finished. To assure transactional consistency, (local) update tasks will be used instead of directly updating the database. Update tasks are a proven technology that also allow a better compatibility with components and services external to Revenue Accounting. As update tasks need an explicit COMMIT WORK some of the select statements in SAP Revenue Accounting have been changed to allow placing COMMIT WORK statements after each package processed. Existing explicit DB commits and also some implicit DB commits (after dialog steps, e.g. in RAI Monitor) have been replaced with COMMIT WORK calls. With the changes in this note new update functions are provided in function group FARR_DB_UPDATE. Class CL_FARR_DB_UPDATE provides convenience methods to register inserts/updates/deletes for the revenue accounting tables.</p>\n<p>Changes to the user interface, system behaviour for the end user or negative impact on performance are not expected. Nevertheless we would advise to perform a thorough regression test after applying the support package. Customers are also advised to check custom enhancements, especially those not utilizing existing standard enhancement capabilities such as BAdIs:</p>\n<ul>\n<li>With the new update behaviour the data will not exist on the database before the COMMIT WORK happened. If your custom enhancement reads data of the currently processed contract (or contracts which are processed in the same package) from the database, this code will not work any longer. You can use method GET_UPDATE_BUFFER of class CL_FARR_DB_UPDATE to retrieve data that is already registered for the update task. Please call method REGISTER_TABLE_FOR_BUFFERING beforehand for those tables you need to get updated data from.</li>\n<li>If you store data to custom tables you should use FUNCTION … IN UPDATE TASK or PERFORM … ON COMMIT to follow the same update logic and ensure data consistency. </li>\n</ul>\n<p><span><strong><a name=\"FP4_Feature_Package\" target=\"_blank\"></a>﻿Feature Package Stack 04 (12/2017)</strong></span></p>\n<p>Support package 4 of SAP Revenue Accounting and Reporting 1.3 is defined as Feature Pack. Technically speaking, a <strong>feature pack stack is like a support package stack</strong>, but it may include nondisruptive, non-mandatory features. You will find the detailed definition of a Feature Pack Stack in the release strategy glossary: <a href=\"https://support.sap.com/release-upgrade-maintenance/release-strategy/glossary.html#f\" target=\"_blank\">https://support.sap.com/release-upgrade-maintenance/release-strategy/glossary.html#f</a>.</p>\n<p>Feature pack 4 provides the following <strong>innovations</strong>:</p>\n<ul>\n<li>Define rules for deriving values of fields for revenue contracts and performance obligations (POBs)</li>\n<li>Define customized logic to calculate contract liabilities and contract assets</li>\n</ul>\n<p>The feature <em>Define customized logic to calculate contract liabilities and contract assets</em> provides enhanced configuration as well as extensibility options.</p>\n<p>After the implementation of note 2560937 or application of SAP RAR 1.3 Feature Pack 04, in Customizing for <em>Configure Accounting Principle-specific Settings</em> you can define the <em>Default method to</em> <em>calculate contract liability and contract asset</em>. Please notice that the method <strong>Calculate Contract Asset and Liability Directly by Contract is defaulted</strong>. You can change this default method. Please DO carefully review the two algorithms mentioned in the note 2560937, and decide which one fits best to your requirements.</p>\n<ul>\n<ul>\n<ul>\n<li>If you want to keep the old standard logic available before the application of note 2560937 or application of SP4, please change the customizing to method <strong>Calculate Contract Asset and Liability per POB and Net by Contract</strong></li>\n<li>If you want to use the new standard logic, you don't need to change anything.</li>\n</ul>\n<ul>\n<li>If you want to implement your own logic, please implement the BADI with your logic.</li>\n</ul>\n</ul>\n</ul>\n<p> </p>\n<ul>\n<li>Calculate the remaining fulfillment percentage for a time-based performance obligation</li>\n<li>Clean up and reverse productive data coming from external sender components</li>\n<li>Perform an extended check before saving contracts to database</li>\n<li>Enable change of transactional currency in the sales order</li>\n<li>Deliver enhancements to POB cancellations for linked POBs</li>\n</ul>\n<p>You can find additional information here:</p>\n<p><strong>SAP Online Help</strong> for SAP Revenue Accounting 1.3 FP04: <a href=\"https://help.sap.com/viewer/de5c3a7ada6e41d482ce9ecdbd93aba1/1.3%20FP04/en-US\" target=\"_blank\">https://help.sap.com/viewer/de5c3a7ada6e41d482ce9ecdbd93aba1/1.3%20FP04/en-US</a></p>\n<p><strong>Notes Content SAP RAR 1.3 </strong>Feature Package FP04: <a href=\"https://launchpad.support.sap.com/#/supportpackage/SAPK-13004INREVREC\" target=\"_blank\">https://launchpad.support.sap.com/#/supportpackage/SAPK-13004INREVREC</a></p>\n<p><strong><a name=\"FP5_Feature_Package\" target=\"_blank\"></a></strong>﻿<span><strong>Feature Package Stack 05 (03/2018) </strong></span></p>\n<p>Support package 5 of SAP Revenue Accounting and Reporting 1.3 is defined as Feature Pack. Feature Pack 5 of SAP Revenue Accounting 1.3 has been released 23.03.2018. Feature Pack 5 contains various enhancements in functionality and performance (Note category: Advance Development, Performance) as well as program corrections with major focus on migration and transition. A full list of available support notes you can find <a href=\"https://launchpad.support.sap.com/#/supportpackage/SAPK-13005INREVREC\" target=\"_blank\">here</a>.</p>\n<p>For details please refer to the pdf attached.</p>\n<p> </p>\n<p><span><strong>Important Information after Patching to FP5:</strong></span></p>\n<p>With FP5 based on customer request, a program correction was introduced that omits the cost center in revenue line items (SAP Note <em><span #44546a;=\"\" 'times=\"\" ar-sa;=\"\" calibri',sans-serif;=\"\" color:=\"\" en-gb;=\"\" en-us;=\"\" lang=\"EN-US\" minor-latin;=\"\" mso-ansi-language:=\"\" mso-ascii-theme-font:=\"\" mso-bidi-font-family:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" mso-hansi-font-family:=\"\" new=\"\" roman';=\"\" roman';\"=\"\"><a href=\"/notes/2607953\" target=\"_blank\"><span>2607953</span></a></span></em>). This correction has been revoked with a second iteration of this note. If you have a requirement to post revenue statistcally to a cost center, please apply the second version of above SAP Note after the patching to FP5.</p>\n<p>If you have a requirement to <span><strong>not</strong></span> post revenues against a cost center, please get in touch with SAP through OSS, where we will provide you with further updates.</p>\n<p> </p>\n<p>Feature pack 5 provides the following <strong>product features</strong>:</p>\n<ul>\n<li><strong>General performance improvements</strong></li>\n</ul>\n<p>Performance and memory consumption is an important topic, especially if you have to process millions of Revenue Accounting contracts. With RAR 1.3 FP5 significant performance improvements in RAI processing, Contract Lifecycle Management, Accruals Management and Data Provisioning of Revenue Accounting are available.</p>\n<p>Generally the following support notes should be considered:<br/><a href=\"/notes/2551667\" target=\"_blank\">2551667</a> - SAP Revenue and Reporting: Special Considerations for Large Number of POBs in one Revenue Accounting Contract<br/><a href=\"/notes/2616387\" target=\"_blank\">2616387</a> - Important Performance Considerations when implementing SAP Revenue Accounting and Reporting</p>\n<p>You can find the sizing guide for SAP RAR 1.3 and further details on sizing on CPU sizing and Disk sizing on the SAP Help Portal: <a href=\"https://www.sap.com/about/benchmark/sizing.html\" target=\"_blank\">https://www.sap.com/about/benchmark/sizing.html</a></p>\n<p>Sizing Guidelines -&gt; SAP Business Suite Applications -&gt; SAP ERP -&gt; Sizing Guideline for SAP Revenue Accounting and Reporting 1.3</p>\n<ul>\n<li><strong>Additional operations support</strong></li>\n<ul>\n<li>Reprocess contracts in batch</li>\n<li>Reprocess account determination in batch</li>\n<li>Performance improvements for archiving of contracts and revenue accounting items</li>\n<li>Calculate and post rounding differences in batch for fixed exchange rate method</li>\n<li>Improved reconciliation between sender components and inbound processing</li>\n<li>Enhanced validation checks and inflight checks</li>\n</ul>\n</ul>\n<p><strong>Inflight Checks</strong> are proactive runtime checks that are implemented in the solution to validate data before committing it to the database. You can find a detailed chapter on Inflight Checks in the Administrator’s guide for SAP Revenue Accounting or in support note <a href=\"https://launchpad.support.sap.com/\" target=\"_blank\">2533254</a>.</p>\n<p>The software also contains post database commit checks which validate data that is already written to the database tables. This part of the solution is called <strong>Data Validation Check</strong> which implements verifications of equivalent error categories. You can find additional information concerning the data validation check in the administrator’s guide for SAP Revenue Accounting or in support note <a href=\"https://launchpad.support.sap.com/\" target=\"_blank\">2567106</a>.</p>\n<ul>\n<li><strong>Improvements in migration and transition process to better support implementation</strong></li>\n<ul>\n<li>Support notes for migration and transition:\r\n<p>Data Migration from operational systems to SAP Revenue Accounting is a critical part in the implementation and operation of SAP Revenue Accounting and Reporting. In addition, customers may want to switch from an existing accounting standard to a new one, for example, IFRS15. This process is referred to as Transition.</p>\n<p>For migration, you can find additional information in the SAP Help Portal for SAP Revenue Accounting and Reporting in the chapter Migration as well as in the Administrator’s Guide (SD Integration).<br/>Please carefully follow the instructions in the application help specific to the implementation scenario, e.g. integration with SAP SD or Third Party Sender integration.<br/>For transition, you can find additional information in the SAP Help Portal for SAP Revenue Accounting and Reporting in the chapter Transition.</p>\n<p>The following notes provide additional information in the context of migration and transition.<br/>Please also consider the list of SAP notes mentioned in the reference attached to support note 2580961. <br/>Please ensure, that the listed notes are implemented before you start your migration or transition phase.</p>\n<p><a href=\"/notes/2580961\" target=\"_blank\">2580961</a> - RAR Data Migration and Transition - Additional Information<br/><a href=\"/notes/2569950\" target=\"_blank\">2569950</a> - FAQ: Migration &amp; Operational Load in the SD Integration Componen</p>\n</li>\n<li>\n<p>Re-run the cumulative catch-up program during transition to a new accounting standard</p>\n</li>\n<li>End of usage of an accounting principle in Revenue Accounting</li>\n</ul>\n</ul>\n<ul>\n<li><strong>Added flexibility in contract management</strong></li>\n<ul>\n<li>Define rules for deriving values of fields for revenue contracts and performance obligations (POBs)</li>\n<li>Navigate from the Financials documents to the original documents in Revenue Accounting</li>\n<li>Change the revenue spreading using an API</li>\n<li>Improved price allocation default BAdI implementation</li>\n</ul>\n</ul>\n<p> </p>\n<ul>\n<li><strong>Improved flexibility for implementing contract modifications</strong></li>\n<ul>\n<li>Calculate the remaining stand-alone selling price (SSP) for value-relevant performance obligations (POB) fulfilled by event type customer invoice</li>\n<li>Apply the remaining fulfillment percentage to the stand-alone selling price tolerance</li>\n</ul>\n</ul>\n<p> </p>\n<ul>\n<li><strong>Improved support for disclosures</strong></li>\n<ul>\n<li>Use the Revenue Accounting data sources with the Operational Data Provisioning Framework (ODP)</li>\n</ul>\n</ul>\n<p>If you use SD (Sales and Distribution) as sender component to integrate with RAR 1.3, please also consider the latest support package for REVRECSD 100 SP15. Support note <a href=\"/notes/2254785\" target=\"_blank\">2254785</a> (Release strategy for the ABAP add-on REVRECSD 100) contains important information about planning the installation and upgrades of the ABAP add-on REVRECSD 100. This includes supported product versions and required NetWeaver stacks.</p>\n<p>Support Package 15 of the SD integration component has been released 09.03.2018. A full list of available support notes you can find <a href=\"https://launchpad.support.sap.com/#/supportpackage/SAPK-10015INREVRECSD\" target=\"_blank\">here</a>.</p>\n<p>Please also refer to the following notes, that are constantly updated:</p>\n<p><a href=\"/notes/2225170\" target=\"_blank\">2225170</a> - S/4 HANA SD-Revenue Recognition<br/><a href=\"/notes/2341717\" target=\"_blank\">2341717</a> - FAQ: Future of SD Revenue Recognition after IFRS15 is released<br/><a href=\"/notes/2591055\" target=\"_blank\">2591055</a> - Functional limitations in the SD Integration Component</p>\n<p><strong>SAP Online Help</strong> for SAP Revenue Accounting 1.3 FP05: <a href=\"https://help.sap.com/viewer/de5c3a7ada6e41d482ce9ecdbd93aba1/1.3%20FP04/en-US\" target=\"_blank\">https://help.sap.com/viewer/de5c3a7ada6e41d482ce9ecdbd93aba1/1.3%20FP05/en-US</a></p>\n<p> </p>\n<p><a name=\"FP6\" target=\"_blank\"></a>﻿﻿<span><strong>Feature Package Stack 06 (07/2018) </strong></span></p>\n<p>Feature Pack 6 of SAP Revenue Accounting 1.3 has been released 13.07.2018. Feature Pack 6 contains various enhancements in functionality for integrating with the SD sender component and in contract management (Note category: Advance Development) as well as program corrections. A full list of available support notes you can find <a href=\"https://launchpad.support.sap.com/#/supportpackage/SAPK-13006INREVREC\" target=\"_blank\">here</a>.</p>\n<p>Feature pack 6 provides the following <strong>product features </strong>(Please refer to attachment for Feature Pack 6):</p>\n<p><strong>Additional SD process support</strong></p>\n<ul>\n<li>Intercompany sales process (Requires additional notes for the SD integration component -&gt; Please refer to additional information in the attachment)</li>\n<li>Drop shipment process (Requires additional notes for the SD integration component -&gt; Please refer to additional information in the attachment)</li>\n<li>Reconciling operational data with Revenue Accounting</li>\n</ul>\n<p><strong>Added flexibility in contract management</strong></p>\n<ul>\n<li>Enable change documents for account assignments</li>\n<li>Allow performance obligation cancellation of a compound structure separately</li>\n<li>POB cancellation and conflict management</li>\n<li>BAdI: Log POB Data</li>\n<li>Set customized fields as selection criteria on worklist</li>\n<li>Comprehensive view enhancement: Enable filter button</li>\n<li>Archived change history of Revenue Accounting contract</li>\n<li>Additional contract shift scenarios</li>\n<li>Overfulfillment during returns processing</li>\n<li>Set multiple Revenue Accounting periods status to 'In Closing' report</li>\n</ul>\n<p><strong>Improved flexibility for implementing contract modifications</strong></p>\n<ul>\n<li>Apply contract change to earliest open period</li>\n<li>Improve remaining standalone selling price calculation flexibility</li>\n<li>Improve remaining amount calculation formula for allocation difference condition type</li>\n<li>Manual spreading retained for POB when contract modification occurs</li>\n<li>Perform standalone selling price range validation during contract modification</li>\n</ul>\n<p><strong>Improved support for disclosures</strong></p>\n<ul>\n<li>Enable report ‘G/L Accounts Between Revenue Accounting and General Ledger’ for S/4 HANA</li>\n<li>Enable report ‘Reconciliation Between Revenue Postings and General Ledger’ for S/4HANA</li>\n<li>Column Name Change from 'Receivable' to 'Billable' for Posted Amount Reports</li>\n</ul>\n<p><strong>Improvements in migration and transition process to better support implementation</strong></p>\n<ul>\n<li>Exchange rate difference (actual rate) calculation report for contract initial load</li>\n<li>Change transaction price during initial load</li>\n</ul>\n<p><strong>Additional operations support</strong></p>\n<ul>\n<li>Parallelization of 'Clean-Up and Reverse the Productive Data' report</li>\n<li>Enhanced inflight checks</li>\n<li>Enhanced data validation checks</li>\n</ul>\n<p>Below you can find the relevant links for the documentation:</p>\n<p><strong>App Help (and release notes) EN</strong></p>\n<p><a href=\"https://help.sap.com/viewer/de5c3a7ada6e41d482ce9ecdbd93aba1/1.3%20FP06/en-US/c7ce61533be7ff4fe10000000a44176d.html\" target=\"_blank\">https://help.sap.com/viewer/de5c3a7ada6e41d482ce9ecdbd93aba1/1.3%20FP06/en-US/c7ce61533be7ff4fe10000000a44176d.html</a></p>\n<p><strong>App Help (and release notes) DE</strong></p>\n<p><a href=\"https://help.sap.com/viewer/de5c3a7ada6e41d482ce9ecdbd93aba1/1.3%20FP06/de-DE\" target=\"_blank\">https://help.sap.com/viewer/de5c3a7ada6e41d482ce9ecdbd93aba1/1.3%20FP06/de-DE</a></p>\n<p><strong>Admin Guide</strong></p>\n<p><a href=\"https://help.sap.com/viewer/0d4e9a84977548c9ac3d98c04eaa276e/1.3%20FP06/en-US\" target=\"_blank\">https://help.sap.com/viewer/0d4e9a84977548c9ac3d98c04eaa276e/1.3%20FP06/en-US</a></p>\n<p> </p>\n<p><a name=\"FP7\" target=\"_blank\"></a>﻿﻿<span><strong>Feature Package Stack 07 (11/2018) </strong></span></p>\n<p>Feature Pack 7 of SAP Revenue Accounting 1.3 has been released 30.11.2018. Feature Pack 7 contains various enhancements in functionality for integrating with the SD sender component and in contract management (Note category: Advance Development) as well as program corrections. A full list of available support notes you can find <a href=\"https://launchpad.support.sap.com/#/supportpackage/SAPK-13007INREVREC\" target=\"_blank\">here</a>.</p>\n<p>For SD related processes, please also consider the corresponding support packages for the SD integration component. A full list of available support notes for support package <a href=\"https://launchpad.support.sap.com/#/supportpackage/SAPK-10016INREVRECSD\" target=\"_blank\">16</a>, <a href=\"https://launchpad.support.sap.com/#/supportpackage/SAPK-10017INREVRECSD\" target=\"_blank\">17</a> and <a href=\"https://launchpad.support.sap.com/#/supportpackage/SAPK-10018INREVRECSD\" target=\"_blank\">18</a> you can find in the corresponding links.</p>\n<p>Feature pack 7 provides the following <strong>product features </strong>(Please refer to attachment for Feature Pack 7):</p>\n<p><strong>Additional SD process support</strong></p>\n<ul>\n<li>Proof of delivery</li>\n<li>Stock in transit</li>\n<li>Call-off order</li>\n<li>Migration of RevRelType = D</li>\n</ul>\n<p><strong>Added flexibility in contract management</strong></p>\n<ul>\n<li>Improved check when closing revenue accounting period</li>\n<li>Search helps for accounting principles added to backend reports</li>\n<li>Allow VALUE_RELEVANT flag to be changed if invoice is not billed yet</li>\n<li>Reprocess account determination for invoice condition types</li>\n<li>Adjust search criteria on Search Revenue Accounting Contracts user interface</li>\n<li>Edit customer fields in comprehensive view</li>\n<li>Change type explanations for POB change type reasons</li>\n<li>Document Relationship Browser activated for the revenue accounting FI document</li>\n<li>Allow compound structure with only one non-distinct POB</li>\n<li>Positive and negative allocated price in price allocation</li>\n</ul>\n<p><strong>Additional operations support</strong></p>\n<ul>\n<li>BAdI of POB validation supports error check for a Customizing field</li>\n<li>Data validation check E22 for manual spreading</li>\n<li>Call inflight checks during processing of fulfillment RAIs</li>\n<li>Data validation: BAdI implementation for transaction price (TCV check)</li>\n</ul>\n<p><strong>Improved support for disclosures</strong></p>\n<ul>\n<li>Contract balance for deferred costs</li>\n<li>Adjust reports for disaggregation of revenues</li>\n</ul>\n<p><strong>Improved flexibility for implementing contract modifications</strong></p>\n<ul>\n<li>Consider calendar specific settings for time-based performance obligations</li>\n<li>Control allocation of negative revenue</li>\n</ul>\n<p>Below you can find the relevant links for the documentation:</p>\n<p><strong>App Help (and release notes) EN</strong></p>\n<p><a href=\"https://help.sap.com/viewer/de5c3a7ada6e41d482ce9ecdbd93aba1/1.3%20FP07/en-US/c7ce61533be7ff4fe10000000a44176d.html\" target=\"_blank\">https://help.sap.com/viewer/de5c3a7ada6e41d482ce9ecdbd93aba1/1.3%20FP07/en-US/c7ce61533be7ff4fe10000000a44176d.html</a></p>\n<p><strong>App Help (and release notes) DE</strong></p>\n<p><a href=\"https://help.sap.com/viewer/de5c3a7ada6e41d482ce9ecdbd93aba1/1.3%20FP07/de-DE\" target=\"_blank\">https://help.sap.com/viewer/de5c3a7ada6e41d482ce9ecdbd93aba1/1.3%20FP07/de-DE</a></p>\n<p><strong>Admin Guide</strong></p>\n<p><a href=\"https://help.sap.com/viewer/0d4e9a84977548c9ac3d98c04eaa276e/1.3%20FP07/en-US\" target=\"_blank\">https://help.sap.com/viewer/0d4e9a84977548c9ac3d98c04eaa276e</a><a href=\"https://help.sap.com/viewer/0d4e9a84977548c9ac3d98c04eaa276e/1.3%20FP07/en-US\" target=\"_blank\">/</a><a href=\"https://help.sap.com/viewer/0d4e9a84977548c9ac3d98c04eaa276e/1.3%20FP07/en-US\" target=\"_blank\">1.3%20FP07/en-US</a></p>\n<p> </p>\n<p><a name=\"SP8\" target=\"_blank\"></a>﻿﻿<strong>Support Package Stack 08 (03/2019)</strong></p>\n<p>Support Pack 8 of SAP Revenue Accounting 1.3 has been released 11.03.2019. Support Pack 8 contains mainly enhancements  program corrections. A full list of available support notes you can find <a href=\"https://launchpad.support.sap.com/#/supportpackage/SAPK-13008INREVREC\" target=\"_blank\">here</a>.</p>\n<p>For SD related processes, please also consider the corresponding support packages for the SD integration component. A full list of available support notes for support package <a href=\"https://launchpad.support.sap.com/#/supportpackage/SAPK-10016INREVRECSD\" target=\"_blank\">16</a>, <a href=\"https://launchpad.support.sap.com/#/supportpackage/SAPK-10017INREVRECSD\" target=\"_blank\">17</a>, <a href=\"https://launchpad.support.sap.com/#/supportpackage/SAPK-10018INREVRECSD\" target=\"_blank\">18</a> and <a href=\"https://launchpad.support.sap.com/#/supportpackage/SAPK-10018INREVRECSD\" target=\"_blank\">19</a> you can find in the corresponding links.</p>\n<p><strong>App Help (and release notes) EN</strong></p>\n<p><a href=\"https://help.sap.com/viewer/de5c3a7ada6e41d482ce9ecdbd93aba1/1.3%20SP08/en-US/c7ce61533be7ff4fe10000000a44176d.html\" target=\"_blank\">https://help.sap.com/viewer/de5c3a7ada6e41d482ce9ecdbd93aba1/1.3%20SP08/en-US/c7ce61533be7ff4fe10000000a44176d.html</a></p>\n<p><strong>App Help (and release notes) DE</strong></p>\n<p><a href=\"https://help.sap.com/viewer/de5c3a7ada6e41d482ce9ecdbd93aba1/1.3%20SP08/de-DE/c7ce61533be7ff4fe10000000a44176d.html\" target=\"_blank\">https://help.sap.com/viewer/de5c3a7ada6e41d482ce9ecdbd93aba1/1.3%20SP08/de-DE/c7ce61533be7ff4fe10000000a44176d.html</a></p>\n<p><strong>Admin Guide</strong></p>\n<p><a href=\"https://help.sap.com/viewer/0d4e9a84977548c9ac3d98c04eaa276e/1.3%20SP08/en-US/c8abeb53bf7ca647e10000000a4450e5.html\" target=\"_blank\">https://help.sap.com/viewer/0d4e9a84977548c9ac3d98c04eaa276e/1.3%20SP08/en-US/c8abeb53bf7ca647e10000000a4450e5.html</a></p>\n<p> </p>\n<p lang=\"de\"><span><strong><a name=\"SP9\" target=\"_blank\"></a>﻿Support Package Stack 09 (06/2019)</strong></span></p>\n<p lang=\"de\">Support Pack 9 of SAP Revenue Accounting 1.3 has been released 10.06.2019. Support Pack 9 contains mainly program corrections. A full list of available support notes you can find <a href=\"https://launchpad.support.sap.com/#/supportpackage/SAPK-13009INREVREC\" target=\"_blank\">here</a>.</p>\n<p lang=\"de\">For SD related processes, please also consider the corresponding support packages for the SD integration component. A full list of available support notes for support package <a href=\"https://launchpad.support.sap.com/#/supportpackage/SAPK-10016INREVRECSD\" target=\"_blank\">16</a>, <a href=\"https://launchpad.support.sap.com/#/supportpackage/SAPK-10017INREVRECSD\" target=\"_blank\">17</a>, <a href=\"https://launchpad.support.sap.com/#/supportpackage/SAPK-10018INREVRECSD\" target=\"_blank\">18</a>, <a href=\"https://launchpad.support.sap.com/#/supportpackage/SAPK-10019INREVRECSD\" target=\"_blank\">19</a> and <a href=\"https://launchpad.support.sap.com/#/supportpackage/SAPK-10020INREVRECSD\" target=\"_blank\">20</a> you can find in the corresponding links.</p>\n<p lang=\"de\">There are also some enhancements you may want to consider:</p>\n<p lang=\"de\"><strong>New tool for shift any contract to next period</strong></p>\n<ul>\n<li>\n<div lang=\"de\">Previously, only erroneous Revenue Accounting contracts could be moved to the next period after the revenue posting run. Now you can move any contract in errornous status to enable period close.</div>\n</li>\n</ul>\n<p lang=\"de\">See note: <a href=\"/notes/2749859\" target=\"_blank\">https://launchpad.support.sap.com/#/notes/2749859</a></p>\n<p lang=\"de\"><strong>New BAdI allowing more flexibility in the account determination for invoice correction postings</strong></p>\n<ul>\n<li>\n<div lang=\"de\">This BAdI allows for flexible account determination when standard account determination does not suit your requirements.</div>\n</li>\n</ul>\n<p lang=\"de\">See note: <a href=\"/notes/2749786\" target=\"_blank\">https://launchpad.support.sap.com/#/notes/2749786</a></p>\n<p lang=\"de\"><strong>App Help (and release notes) EN</strong></p>\n<p lang=\"de\"><a href=\"https://help.sap.com/viewer/de5c3a7ada6e41d482ce9ecdbd93aba1/1.3%20SP09/en-US/c7ce61533be7ff4fe10000000a44176d.html\" target=\"_blank\">https://help.sap.com/viewer/de5c3a7ada6e41d482ce9ecdbd93aba1/1.3%20SP09/en-US/c7ce61533be7ff4fe10000000a44176d.html</a></p>\n<p lang=\"de\"><strong>App Help (and release notes) DE</strong></p>\n<p lang=\"de\"><a href=\"https://help.sap.com/viewer/de5c3a7ada6e41d482ce9ecdbd93aba1/1.3%20SP09/de-DE/c7ce61533be7ff4fe10000000a44176d.html\" target=\"_blank\">https://help.sap.com/viewer/de5c3a7ada6e41d482ce9ecdbd93aba1/1.3%20SP09/de-DE/c7ce61533be7ff4fe10000000a44176d.html</a></p>\n<p lang=\"de\"><strong>Admin Guide</strong></p>\n<p lang=\"de\"><a href=\"https://help.sap.com/viewer/0d4e9a84977548c9ac3d98c04eaa276e/1.3%20SP09/en-US/c8abeb53bf7ca647e10000000a4450e5.html\" target=\"_blank\">https://help.sap.com/viewer/0d4e9a84977548c9ac3d98c04eaa276e/1.3%20SP09/en-US/c8abeb53bf7ca647e10000000a4450e5.html</a></p>\n<p lang=\"de\">.</p>\n<p><strong><a name=\"SP10\" target=\"_blank\"></a>﻿Support Package Stack 10 (09/2019)</strong></p>\n<p>Support Pack 10 of SAP Revenue Accounting 1.3 has been released 11.09.2019. Support Pack 10 contains mainly program corrections. A full list of available support notes you can find <a href=\"https://launchpad.support.sap.com/#/supportpackage/SAPK-13010INREVREC\" target=\"_blank\">here</a>.</p>\n<p>For SD related processes, please also consider the corresponding support packages for the SD integration component. A full list of available support notes for support package <a href=\"https://launchpad.support.sap.com/#/supportpackage/SAPK-10016INREVRECSD\" target=\"_blank\">16</a>, <a href=\"https://launchpad.support.sap.com/#/supportpackage/SAPK-10017INREVRECSD\" target=\"_blank\">17</a>, <a href=\"https://launchpad.support.sap.com/#/supportpackage/SAPK-10018INREVRECSD\" target=\"_blank\">18</a>, <a href=\"https://launchpad.support.sap.com/#/supportpackage/SAPK-10019INREVRECSD\" target=\"_blank\">19</a>, <a href=\"https://launchpad.support.sap.com/#/supportpackage/SAPK-10020INREVRECSD\" target=\"_blank\">20</a> and <a href=\"https://launchpad.support.sap.com/#/supportpackage/SAPK-10021INREVRECSD\" target=\"_blank\">21 </a>you can find in the corresponding links.</p>\n<p>There are also some enhancements you may want to consider:</p>\n<p><strong>Performance improvements</strong></p>\n<p>New extraction method of a data source avoid runtime error while processing large data volumes.</p>\n<p>See note: <a href=\"/notes/2796749\" target=\"_blank\">https://launchpad.support.sap.com/#/notes/2796749</a></p>\n<p><strong>Enhancements for Data Privacy Protection</strong></p>\n<p>Implementation of simplified blocking and deletion of customer master integrated to RAR based on regulations of Data Privacy Protection requirements.</p>\n<p>See notes: <a href=\"/notes/2798400\" target=\"_blank\">https://launchpad.support.sap.com/#/notes/2798400</a> and <a href=\"/notes/2805208\" target=\"_blank\">https://launchpad.support.sap.com/#/notes/2805208</a></p>\n<p><strong>Enable BRF+ Trace Viewer</strong></p>\n<p>This development allows to visualize the full decision path of the BRF+ function calls during creation of a POB from source RAI data.  The BRF+ trace can be used by customer to explain own customization as well as an support engineer during problem analysis. Note: this trace is only available for BRF+ usage in Inbound Processing.</p>\n<p>See note: <a href=\"/notes/2651604\" target=\"_blank\">https://launchpad.support.sap.com/#/notes/2651604</a></p>\n<p><strong>Allow Manual Performance Obligation to Be Created/Changed Through Contract Creation and Change</strong></p>\n<p>Manual POB can now be created or changed during RAI processing, which originally was only allowed during transition.</p>\n<p>See note: <a href=\"/notes/2651604\" target=\"_blank\">https://launchpad.support.sap.com/#/notes/2651604</a></p>\n<p><strong>RAI Monitor: enhanced selection criteria</strong></p>\n<p>A new selection criteria has been added to  FARR_RAI_MON which allows now to display “Partially Processed” or “Processed and Partially Processed” RAIs together.</p>\n<p>See note: <a href=\"/notes/2659215\" target=\"_blank\">https://launchpad.support.sap.com/#/notes/2659215</a></p>\n<p> </p>\n<p><strong>App Help (and release notes) EN</strong></p>\n<p><a href=\"https://help.sap.com/viewer/17fec0d77e324d78b2a37af97bdef898/1.3%20SP10/en-US/c7ce61533be7ff4fe10000000a44176d.html\" target=\"_blank\">https://help.sap.com/viewer/17fec0d77e324d78b2a37af97bdef898/1.3%20SP10/en-US/c7ce61533be7ff4fe10000000a44176d.html</a></p>\n<p><strong>App Help (and release notes) DE</strong></p>\n<p><a href=\"https://help.sap.com/viewer/17fec0d77e324d78b2a37af97bdef898/1.3%20SP10/de-DE/c7ce61533be7ff4fe10000000a44176d.html\" target=\"_blank\">https://help.sap.com/viewer/17fec0d77e324d78b2a37af97bdef898/1.3%20SP10/de-DE/c7ce61533be7ff4fe10000000a44176d.html</a></p>\n<p><strong>Admin Guide</strong></p>\n<p><a href=\"https://help.sap.com/viewer/88a7e7624cb346ddb1709ab3dac29417/1.3%20SP10/en-US\" target=\"_blank\">https://help.sap.com/viewer/88a7e7624cb346ddb1709ab3dac29417/1.3%20SP10/en-US</a></p>\n<p> </p>\n<p><strong><a name=\"SP11\" target=\"_blank\"></a>﻿﻿﻿Support Package Stack 11 (12/2019)</strong></p>\n<p>Support Pack 11 of SAP Revenue Accounting 1.3 has been released 16.12.2019. Support Pack 11 contains mainly program corrections. A full list of available support notes you can find <a href=\"https://launchpad.support.sap.com/#/supportpackage/SAPK-13011INREVREC\" target=\"_blank\">here</a>.</p>\n<p>For SD related processes, please also consider the corresponding support packages for the SD integration component. A full list of available support notes for support package <a href=\"https://launchpad.support.sap.com/#/supportpackage/SAPK-10016INREVRECSD\" target=\"_blank\">16</a>, <a href=\"https://launchpad.support.sap.com/#/supportpackage/SAPK-10017INREVRECSD\" target=\"_blank\">17</a>, <a href=\"https://launchpad.support.sap.com/#/supportpackage/SAPK-10018INREVRECSD\" target=\"_blank\">18</a>, <a href=\"https://launchpad.support.sap.com/#/supportpackage/SAPK-10019INREVRECSD\" target=\"_blank\">19</a>, <a href=\"https://launchpad.support.sap.com/#/supportpackage/SAPK-10020INREVRECSD\" target=\"_blank\">20</a>, <a href=\"https://launchpad.support.sap.com/#/supportpackage/SAPK-10021INREVRECSD\" target=\"_blank\">21 </a>and <a href=\"https://launchpad.support.sap.com/#/supportpackage/SAPK-10022INREVRECSD\" target=\"_blank\">22</a> you can find in the corresponding links.</p>\n<p> </p>\n<p><span>To be considered:</span></p>\n<p>SAP has further enhanced the Revenue Accounting and Reporting Rel. 1.3 functionality to meet <strong>data protection requirements</strong> in a more standardized and automated manner.</p>\n<p>This contains simplified blocking, archiving and deletion functionality of partner master data when there is no longer a relevant purpose of this data (performing a so-called “end of purpose” check).</p>\n<p>See Note 2812171 - RAR: Simplified Blocking and Deletion of Partner Master Data for comprehensive information of this topic: <a href=\"/notes/2812171\" target=\"_blank\">https://launchpad.support.sap.com/#/notes/2812171</a></p>\n<p>      .</p>\n<p><strong>App Help (and release notes) EN</strong></p>\n<p><a href=\"https://help.sap.com/viewer/17fec0d77e324d78b2a37af97bdef898/1.3.11/en-US/c7ce61533be7ff4fe10000000a44176d.html\" target=\"_blank\">https://help.sap.com/viewer/17fec0d77e324d78b2a37af97bdef898/1.3.11/en-US/c7ce61533be7ff4fe10000000a44176d.html</a></p>\n<p><strong>Admin Guide EN</strong></p>\n<p><a href=\"https://help.sap.com/viewer/88a7e7624cb346ddb1709ab3dac29417/1.3.11/en-US/c8abeb53bf7ca647e10000000a4450e5.html\" target=\"_blank\">https://help.sap.com/viewer/88a7e7624cb346ddb1709ab3dac29417/1.3.11/en-US/c8abeb53bf7ca647e10000000a4450e5.html</a></p>\n<p><strong><a name=\"SP12\" target=\"_blank\"></a>﻿Support Package Stack 12 (06/2020)</strong></p>\n<p>Support Pack 12 of SAP Revenue Accounting 1.3 has been released 08.06.2020. Support Pack 12 contains mainly program corrections. A full list of available support notes you can find <a href=\"https://launchpad.support.sap.com/#/supportpackage/SAPK-13012INREVREC\" target=\"_blank\">here</a>.</p>\n<p>For SD related processes, please also consider the corresponding support packages for the SD integration component. A full list of available support notes for support package <a href=\"https://launchpad.support.sap.com/#/supportpackage/SAPK-10016INREVRECSD\" target=\"_blank\">16</a>, <a href=\"https://launchpad.support.sap.com/#/supportpackage/SAPK-10017INREVRECSD\" target=\"_blank\">17</a>, <a href=\"https://launchpad.support.sap.com/#/supportpackage/SAPK-10018INREVRECSD\" target=\"_blank\">18</a>, <a href=\"https://launchpad.support.sap.com/#/supportpackage/SAPK-10019INREVRECSD\" target=\"_blank\">19</a>, <a href=\"https://launchpad.support.sap.com/#/supportpackage/SAPK-10020INREVRECSD\" target=\"_blank\">20</a>, <a href=\"https://launchpad.support.sap.com/#/supportpackage/SAPK-10021INREVRECSD\" target=\"_blank\">21 </a>, <a href=\"https://launchpad.support.sap.com/#/supportpackage/SAPK-10022INREVRECSD\" target=\"_blank\">22</a>, <a href=\"https://launchpad.support.sap.com/#/supportpackage/SAPK-100223INREVRECSD\" target=\"_blank\">23</a> and <a href=\"https://launchpad.support.sap.com/#/supportpackage/SAPK-10024INREVRECSD\" target=\"_blank\">24</a> you can find in the corresponding links.</p>\n<p> </p>\n<p><span>To be considered:</span></p>\n<ul>\n<li>New reports available to better track and monitor changes:</li>\n</ul>\n<p><a href=\"/notes/2835924\" target=\"_blank\" title=\"2835924  - Report To Search And Display Changes History In  Revenue Accounting Contract and Performance Obligations\">2835924 - Report To Search And Display Changes History In Revenue Accounting Contract and Performance Obligations</a></p>\n<p><a href=\"/notes/2844377\" target=\"_blank\" title=\"2844377  - Report To Search And Display Changes in Allocated Amounts and Prices of Performance Obligations At Period Level.\">2844377 - Report To Search And Display Changes in Allocated Amounts and Prices of Performance Obligations At Period Level.</a></p>\n<ul>\n<li>Better support for auditing processes</li>\n</ul>\n<p><a href=\"/notes/2823583\" target=\"_blank\" title=\"2823583  - Business Reconciliation report with multiple Sender Components and Multiple Source systems\">2823583 - Business Reconciliation report with multiple Sender Components and Multiple Source systems</a></p>\n<ul>\n<li>Enhancement for account assignment logic for BOM’s</li>\n</ul>\n<p><a href=\"/notes/2887935\" target=\"_blank\" title=\"2887935  - Account Assignment for BOM POB Members\">2887935 - Account Assignment for BOM POB Members</a></p>\n<p> </p>\n<p><strong>App Help (and release notes) EN</strong></p>\n<p><a href=\"https://help.sap.com/viewer/17fec0d77e324d78b2a37af97bdef898/1.3.12/en-US/c7ce61533be7ff4fe10000000a44176d.html\" target=\"_blank\">https://help.sap.com/viewer/17fec0d77e324d78b2a37af97bdef898/1.3.12/en-US/c7ce61533be7ff4fe10000000a44176d.html</a></p>\n<p><strong>Admin Guide EN</strong></p>\n<p><a href=\"https://help.sap.com/viewer/88a7e7624cb346ddb1709ab3dac29417/1.3.12/en-US\" target=\"_blank\">https://help.sap.com/viewer/88a7e7624cb346ddb1709ab3dac29417/1.3.12/en-US</a></p>\n<p>.</p>\n<p><strong><a name=\"SP13\" target=\"_blank\"></a>﻿﻿Support Package Stack 13 (11/2020)</strong></p>\n<p>Support Pack 13 of SAP Revenue Accounting 1.3 has been released 13.11.2020. Support Pack 13 contains mainly program corrections. A full list of available support notes you can find <a href=\"https://launchpad.support.sap.com/#/supportpackage/SAPK-13013INREVREC\" target=\"_blank\">here</a>.</p>\n<p>For SD related processes, please also consider the corresponding support packages for the SD integration component. A full list of available support notes for support package <a href=\"https://launchpad.support.sap.com/#/supportpackage/SAPK-10016INREVRECSD\" target=\"_blank\">16</a>, <a href=\"https://launchpad.support.sap.com/#/supportpackage/SAPK-10017INREVRECSD\" target=\"_blank\">17</a>, <a href=\"https://launchpad.support.sap.com/#/supportpackage/SAPK-10018INREVRECSD\" target=\"_blank\">18</a>, <a href=\"https://launchpad.support.sap.com/#/supportpackage/SAPK-10019INREVRECSD\" target=\"_blank\">19</a>, <a href=\"https://launchpad.support.sap.com/#/supportpackage/SAPK-10020INREVRECSD\" target=\"_blank\">20</a>, <a href=\"https://launchpad.support.sap.com/#/supportpackage/SAPK-10021INREVRECSD\" target=\"_blank\">21 </a>, <a href=\"https://launchpad.support.sap.com/#/supportpackage/SAPK-10022INREVRECSD\" target=\"_blank\">22</a>, <a href=\"https://launchpad.support.sap.com/#/supportpackage/SAPK-100223INREVRECSD\" target=\"_blank\">23</a> , <a href=\"https://launchpad.support.sap.com/#/supportpackage/SAPK-10024INREVRECSD\" target=\"_blank\">24</a> and <a href=\"https://launchpad.support.sap.com/#/supportpackage/SAPK-10025INREVRECSD\" target=\"_blank\">25</a> you can find in the corresponding links.</p>\n<p> </p>\n<p>To be considered:</p>\n<ul>\n<li>New functionality to manage Migration</li>\n<ul>\n<li>This new functionality provides a guided procedure for migrating legacy data to Revenue Accounting and Reporting with special focus on migrating data from SAP SD Revenue Recognition. It leverages the existing migration programs. It streamlines and safeguard the execution of this critical process.</li>\n</ul>\n</ul>\n<ul>\n<ul>\n<li>Notes: </li>\n<ul>\n<li><a href=\"/notes/2897314\" target=\"_blank\">2897314 - Migration Cockpit</a></li>\n<li><a href=\"/notes/2900941\" target=\"_blank\">2900941 - RA Migration cockpit text objects</a></li>\n</ul>\n</ul>\n</ul>\n<ul>\n<li>Performance improvements for processing of large contracts</li>\n<ul>\n<li>There are certain limitations for running RAR1.3 with contracts of very large sizes. These limitations are described in notes 2616387 and 2551667. This can often lead to timeouts when performing basic tasks, such as opening the contracts in the GUI, or combining them. Within SP13 certain technical changes had been introduced to improve performance to allow users to process larger contracts.</li>\n</ul>\n</ul>\n<ul>\n<ul>\n<li>Notes:</li>\n<ul>\n<li><a href=\"/notes/2928584\" target=\"_blank\">2928584 - Performance improvement for large contracts</a></li>\n<li><a href=\"/notes/2976149\" target=\"_blank\">2976149 - Performance improvement for large contracts in Revenue Schedule</a></li>\n</ul>\n</ul>\n<li>Allow condition types with negative values</li>\n<ul>\n<li>\n<p>RAR provides the functionality to check whether a condition type of a particular POB has the same +/- sign. If sign is different, an error message is raised. This functionality was introduced to prevent errors during posting run in case customer was using cost-based or combined CO-PA. For certian custom specific processes (e.g. managing of rounding differences) this could cause an issue. We now deliver a functionality to allow configuration to specify which condition types can have both +/- sign for the same POB.</p>\n</li>\n<li>\n<p>Notes:</p>\n</li>\n<ul>\n<li><a href=\"/notes/2918711\" target=\"_blank\">2918711 - Message FARR_MSG_CUSTOM 034 for Rounding Conditions from SD</a></li>\n<li><a href=\"/notes/2922153\" target=\"_blank\">2922153 - Message FARR_MSG_CUSTOM 034 for Rounding Conditions from SD - UDO Report</a></li>\n</ul>\n</ul>\n</ul>\n<p><strong>App Help (and release notes) EN</strong></p>\n<p><a href=\"https://help.sap.com/viewer/17fec0d77e324d78b2a37af97bdef898/1.3.13/en-US\" target=\"_blank\">https://help.sap.com/viewer/17fec0d77e324d78b2a37af97bdef898/1.3.13/en-US</a></p>\n<p><strong>Admin Guide EN</strong></p>\n<p><a href=\"https://help.sap.com/viewer/88a7e7624cb346ddb1709ab3dac29417/1.3.13/en-US\" target=\"_blank\">https://help.sap.com/viewer/88a7e7624cb346ddb1709ab3dac29417/1.3.13/en-US</a></p>\n<p>.</p>\n<p><strong><a name=\"SP14\" target=\"_blank\"></a>﻿Support Package Stack 14 (05/2021)</strong></p>\n<p>Support Pack 14 of SAP Revenue Accounting 1.3 has been released 21.05.2021. Support Pack 14 contains mainly program corrections. A full list of available support notes you can find <a href=\"https://launchpad.support.sap.com/#/supportpackage/SAPK-13014INREVREC\" target=\"_blank\">here</a>.</p>\n<p>For SD related processes, please also consider the corresponding support packages for the SD integration component. A full list of available support notes for support package <a href=\"https://launchpad.support.sap.com/#/supportpackage/SAPK-10016INREVRECSD\" target=\"_blank\">16</a>, <a href=\"https://launchpad.support.sap.com/#/supportpackage/SAPK-10017INREVRECSD\" target=\"_blank\">17</a>, <a href=\"https://launchpad.support.sap.com/#/supportpackage/SAPK-10018INREVRECSD\" target=\"_blank\">18</a>, <a href=\"https://launchpad.support.sap.com/#/supportpackage/SAPK-10019INREVRECSD\" target=\"_blank\">19</a>, <a href=\"https://launchpad.support.sap.com/#/supportpackage/SAPK-10020INREVRECSD\" target=\"_blank\">20</a>, <a href=\"https://launchpad.support.sap.com/#/supportpackage/SAPK-10021INREVRECSD\" target=\"_blank\">21 </a>, <a href=\"https://launchpad.support.sap.com/#/supportpackage/SAPK-10022INREVRECSD\" target=\"_blank\">22</a>, <a href=\"https://launchpad.support.sap.com/#/supportpackage/SAPK-100223INREVRECSD\" target=\"_blank\">23</a> , <a href=\"https://launchpad.support.sap.com/#/supportpackage/SAPK-10024INREVRECSD\" target=\"_blank\">24</a>, <a href=\"https://launchpad.support.sap.com/#/supportpackage/SAPK-10025INREVRECSD\" target=\"_blank\">25</a>, <a href=\"https://launchpad.support.sap.com/#/supportpackage/SAPK-10026INREVRECSD\" target=\"_blank\">26</a> and <a href=\"https://launchpad.support.sap.com/#/supportpackage/SAPK-10027INREVRECSD\" target=\"_blank\">27</a> you can find in the corresponding links.</p>\n<p>To be considered:</p>\n<ul>\n<li>New functionality to read processed RAIs from archive in Revenue Accounting Item Monitor</li>\n<ul>\n<li>This new functionality provides with the ability to view also archived Revenue Accounting Items (RAI) in the Revenue Accounting Monitor (transaction FARR_RAI_MON)</li>\n<li>Notes: </li>\n</ul>\n</ul>\n<ul>\n<ul>\n<li><a href=\"/notes/3012763\" target=\"_blank\" title=\"3012763  - FARR_RAI_MON: reading archived RAIs\">3012763 - FARR_RAI_MON: reading archived RAIs</a></li>\n</ul>\n</ul>\n<ul>\n<ul></ul>\n</ul>\n<p><strong>App Help (and release notes) EN</strong></p>\n<p><a href=\"https://help.sap.com/viewer/17fec0d77e324d78b2a37af97bdef898/1.3.14/en-US\" target=\"_blank\">https://help.sap.com/viewer/17fec0d77e324d78b2a37af97bdef898/1.3.14/en-US</a></p>\n<p><strong>Admin Guide EN</strong></p>\n<p><a href=\"https://help.sap.com/viewer/88a7e7624cb346ddb1709ab3dac29417/1.3.14/en-US\" target=\"_blank\">https://help.sap.com/viewer/88a7e7624cb346ddb1709ab3dac29417/1.3.14/en-US</a></p>\n<p><strong>﻿</strong></p>\n<p><strong><a name=\"SP15\" target=\"_blank\"></a>﻿Support Package Stack 15 (03/2022)</strong></p>\n<p>Support Pack 15 of SAP Revenue Accounting 1.3 has been released 28.03.2022. Support Pack 15 contains mainly program corrections. A full list of available support notes you can find <a href=\"https://launchpad.support.sap.com/#/supportpackage/SAPK-13015INREVREC\" target=\"_blank\">here</a>.</p>\n<p>For SD related processes, please also consider the corresponding support packages for the SD integration component. A full list of available support notes for support package <a href=\"https://launchpad.support.sap.com/#/supportpackage/SAPK-10016INREVRECSD\" target=\"_blank\">16</a>, <a href=\"https://launchpad.support.sap.com/#/supportpackage/SAPK-10017INREVRECSD\" target=\"_blank\">17</a>, <a href=\"https://launchpad.support.sap.com/#/supportpackage/SAPK-10018INREVRECSD\" target=\"_blank\">18</a>, <a href=\"https://launchpad.support.sap.com/#/supportpackage/SAPK-10019INREVRECSD\" target=\"_blank\">19</a>, <a href=\"https://launchpad.support.sap.com/#/supportpackage/SAPK-10020INREVRECSD\" target=\"_blank\">20</a>, <a href=\"https://launchpad.support.sap.com/#/supportpackage/SAPK-10021INREVRECSD\" target=\"_blank\">21 </a>, <a href=\"https://launchpad.support.sap.com/#/supportpackage/SAPK-10022INREVRECSD\" target=\"_blank\">22</a>, <a href=\"https://launchpad.support.sap.com/#/supportpackage/SAPK-100223INREVRECSD\" target=\"_blank\">23</a> , <a href=\"https://launchpad.support.sap.com/#/supportpackage/SAPK-10024INREVRECSD\" target=\"_blank\">24</a>, <a href=\"https://launchpad.support.sap.com/#/supportpackage/SAPK-10025INREVRECSD\" target=\"_blank\">25</a>, <a href=\"https://launchpad.support.sap.com/#/supportpackage/SAPK-10026INREVRECSD\" target=\"_blank\">26</a>, <a href=\"https://launchpad.support.sap.com/#/supportpackage/SAPK-10027INREVRECSD\" target=\"_blank\">27</a>, <a href=\"https://launchpad.support.sap.com/#/supportpackage/SAPK-10028INREVRECSD\" target=\"_blank\">28</a> and <a href=\"https://launchpad.support.sap.com/#/supportpackage/SAPK-10029INREVRECSD\" target=\"_blank\">29</a> you can find in the corresponding links.</p>\n<p>To be considered:</p>\n<ul>\n<li>Improvements in BOM handling</li>\n<ul>\n<li>Adding ability to create BOMs out of previously flat POBs, on a living contract</li>\n<li><a href=\"/notes/3127362\" target=\"_blank\">3127362 - BOM creation out of previously flat POB structure</a></li>\n</ul>\n<li>\n<div class=\"O1\">Technical optimization for SSP rebuilding</div>\n</li>\n<ul>\n<li>Introduction of a new helper class</li>\n<li><a href=\"/notes/3104411\" target=\"_blank\">3104411 - SSP Helper Class</a></li>\n</ul>\n<li>Performance improvements </li>\n<ul>\n<li>Performance improvements for reversal of postings</li>\n<li><a href=\"/notes/3057404\" target=\"_blank\">3057404 - FARR_PROD_CLEANUP: reversal posting performance optimization</a></li>\n<li><a href=\"/notes/3042166\" target=\"_blank\">3042166 - FARR_PROD_CLEANUP: reversal posting performance optimization - prerequisite SAP Note</a></li>\n</ul>\n<li>Improved selection for shifting contracts</li>\n<ul>\n<li>Introduce ability to shift failed contracts only</li>\n<li><a href=\"/notes/3107367\" target=\"_blank\">3107367 - Shift contract report update</a></li>\n</ul>\n</ul>\n<p><strong>App Help (and release notes) EN</strong></p>\n<p><a href=\"https://help.sap.com/docs/SAP_REVENUE_ACCOUNTING_AND_REPORTING?version=1.3.15&amp;locale=en-US\" target=\"_blank\">Help SAP Revenue Accounting and Reporting SP15</a></p>\n<p><strong>Admin Guide EN</strong></p>\n<p><a href=\"https://help.sap.com/docs/SAP_REVENUE_ACCOUNTING_AND_REPORTING/88a7e7624cb346ddb1709ab3dac29417/c8abeb53bf7ca647e10000000a4450e5.html?locale=en-US&amp;version=1.3.15\" target=\"_blank\">Admin Guide SAp Revenue Accounting and Reporting 1.3 SP15</a></p>\n<p> </p>\n<p><strong><a name=\"SP16\" target=\"_blank\"></a>﻿Support Package Stack 16 (10/2022)</strong></p>\n<p>Support Pack 16 of SAP Revenue Accounting 1.3 has been released 10.10.2022. Support Pack 16 contains mainly program corrections. A full list of available support notes you can find <a href=\"https://launchpad.support.sap.com/#/supportpackage/SAPK-13016INREVREC\" target=\"_blank\">here</a>.</p>\n<p>For SD related processes, please also consider the corresponding support packages for the SD integration component. A full list of available support notes for support package <a href=\"https://launchpad.support.sap.com/#/supportpackage/SAPK-10016INREVRECSD\" target=\"_blank\">16</a>, <a href=\"https://launchpad.support.sap.com/#/supportpackage/SAPK-10017INREVRECSD\" target=\"_blank\">17</a>, <a href=\"https://launchpad.support.sap.com/#/supportpackage/SAPK-10018INREVRECSD\" target=\"_blank\">18</a>, <a href=\"https://launchpad.support.sap.com/#/supportpackage/SAPK-10019INREVRECSD\" target=\"_blank\">19</a>, <a href=\"https://launchpad.support.sap.com/#/supportpackage/SAPK-10020INREVRECSD\" target=\"_blank\">20</a>, <a href=\"https://launchpad.support.sap.com/#/supportpackage/SAPK-10021INREVRECSD\" target=\"_blank\">21 </a>, <a href=\"https://launchpad.support.sap.com/#/supportpackage/SAPK-10022INREVRECSD\" target=\"_blank\">22</a>, <a href=\"https://launchpad.support.sap.com/#/supportpackage/SAPK-100223INREVRECSD\" target=\"_blank\">23</a> , <a href=\"https://launchpad.support.sap.com/#/supportpackage/SAPK-10024INREVRECSD\" target=\"_blank\">24</a>, <a href=\"https://launchpad.support.sap.com/#/supportpackage/SAPK-10025INREVRECSD\" target=\"_blank\">25</a>, <a href=\"https://launchpad.support.sap.com/#/supportpackage/SAPK-10026INREVRECSD\" target=\"_blank\">26</a>, <a href=\"https://launchpad.support.sap.com/#/supportpackage/SAPK-10027INREVRECSD\" target=\"_blank\">27</a>, <a href=\"https://launchpad.support.sap.com/#/supportpackage/SAPK-10028INREVRECSD\" target=\"_blank\">28</a>, <a href=\"https://launchpad.support.sap.com/#/supportpackage/SAPK-10029INREVRECSD\" target=\"_blank\">29</a>, <a href=\"https://launchpad.support.sap.com/#/supportpackage/SAPK-10030INREVRECSD\" target=\"_blank\">3</a>0 you can find in the corresponding links.</p>\n<p>To be considered:</p>\n<ul>\n<li>Improvements in functionality for assumed invoices</li>\n<ul>\n<li>Better handling for large volumes</li>\n<li><a href=\"/notes/3125274\" target=\"_blank\">3125274 - RAR assumed invoice RAIs - new BAdI methods of BAdI FARR_BADI_RAI4</a></li>\n</ul>\n<li>Curing issues in error log for cost-related CO-PA-problems during posting run C</li>\n<ul>\n<li>Detailed info in log to be displayed for analyzing issues</li>\n<li><a href=\"/notes/3207423\" target=\"_blank\">3207423 - Revenue accounting: Revenue posting run ends with error message KE/AD 243</a></li>\n</ul>\n<li>Further integration into Financial Closing Cockpit</li>\n<ul>\n<li>New functionality adds the possibility to run any of Revenue Accounting Posting reports using standard Closing Cockpit functionality</li>\n<li><a href=\"/notes/3209396\" target=\"_blank\">3209396 - Revenue Accounting Closing Cockpit integration</a></li>\n</ul>\n</ul>\n<p><a href=\"#DOC\" target=\"_blank\">For more information, see the documentation.</a></p>\n<p> </p>\n<p><strong><a name=\"SP17\" target=\"_blank\"></a>﻿﻿﻿Support Package Stack 17 (04/2023)</strong></p>\n<p>Support Pack 17 of SAP Revenue Accounting 1.3 has been released 17.04.2023. Support Pack 17 contains mainly program corrections. A full list of available support notes you can find <a href=\"https://launchpad.support.sap.com/#/supportpackage/SAPK-13017INREVREC\" target=\"_blank\">here</a>.</p>\n<p>For SD related processes, please also consider the corresponding support packages for the SD integration component. A full list of available support notes for support package <a href=\"https://launchpad.support.sap.com/#/supportpackage/SAPK-10016INREVRECSD\" target=\"_blank\">16</a>, <a href=\"https://launchpad.support.sap.com/#/supportpackage/SAPK-10017INREVRECSD\" target=\"_blank\">17</a>, <a href=\"https://launchpad.support.sap.com/#/supportpackage/SAPK-10018INREVRECSD\" target=\"_blank\">18</a>, <a href=\"https://launchpad.support.sap.com/#/supportpackage/SAPK-10019INREVRECSD\" target=\"_blank\">19</a>, <a href=\"https://launchpad.support.sap.com/#/supportpackage/SAPK-10020INREVRECSD\" target=\"_blank\">20</a>, <a href=\"https://launchpad.support.sap.com/#/supportpackage/SAPK-10021INREVRECSD\" target=\"_blank\">21 </a>, <a href=\"https://launchpad.support.sap.com/#/supportpackage/SAPK-10022INREVRECSD\" target=\"_blank\">22</a>, <a href=\"https://launchpad.support.sap.com/#/supportpackage/SAPK-100223INREVRECSD\" target=\"_blank\">23</a> , <a href=\"https://launchpad.support.sap.com/#/supportpackage/SAPK-10024INREVRECSD\" target=\"_blank\">24</a>, <a href=\"https://launchpad.support.sap.com/#/supportpackage/SAPK-10025INREVRECSD\" target=\"_blank\">25</a>, <a href=\"https://launchpad.support.sap.com/#/supportpackage/SAPK-10026INREVRECSD\" target=\"_blank\">26</a>, <a href=\"https://launchpad.support.sap.com/#/supportpackage/SAPK-10027INREVRECSD\" target=\"_blank\">27</a>, <a href=\"https://launchpad.support.sap.com/#/supportpackage/SAPK-10028INREVRECSD\" target=\"_blank\">28</a>, <a href=\"https://launchpad.support.sap.com/#/supportpackage/SAPK-10029INREVRECSD\" target=\"_blank\">29</a>, <a href=\"https://launchpad.support.sap.com/#/supportpackage/SAPK-10030INREVRECSD\" target=\"_blank\">3</a>0, <a href=\"https://launchpad.support.sap.com/#/supportpackage/SAPK-10031INREVRECSD\" target=\"_blank\">31</a>, <a href=\"https://launchpad.support.sap.com/#/supportpackage/SAPK-10032INREVRECSD\" target=\"_blank\">32</a> you can find in the corresponding links.</p>\n<p>To be considered:</p>\n<ul>\n<li>Introducing new data sources for revenue and posting items</li>\n<ul>\n<li>improved functionality</li>\n<li><a href=\"/notes/3206324\" target=\"_blank\">3206324 - New DataSources for Revenue and Posting Items</a></li>\n</ul>\n</ul>\n<p><a href=\"#DOC\" target=\"_blank\">For more information, see the documentation.</a></p>\n<p><strong> ﻿</strong></p>\n<p><strong>﻿<a name=\"SP18\" target=\"_blank\"></a>﻿Support Package Stack 18 (10/2023)</strong></p>\n<p>Support Pack 18 of SAP Revenue Accounting 1.3 has been released 17.04.2023. Support Pack 18 contains mainly program corrections. A full list of available support notes you can find <a href=\"https://launchpad.support.sap.com/#/supportpackage/SAPK-13018INREVREC\" target=\"_blank\">here</a>.</p>\n<p>For SD related processes, please also consider the corresponding support packages for the SD integration component. A full list of available support notes for support package <a href=\"https://launchpad.support.sap.com/#/supportpackage/SAPK-10016INREVRECSD\" target=\"_blank\">16</a>, <a href=\"https://launchpad.support.sap.com/#/supportpackage/SAPK-10017INREVRECSD\" target=\"_blank\">17</a>, <a href=\"https://launchpad.support.sap.com/#/supportpackage/SAPK-10018INREVRECSD\" target=\"_blank\">18</a>, <a href=\"https://launchpad.support.sap.com/#/supportpackage/SAPK-10019INREVRECSD\" target=\"_blank\">19</a>, <a href=\"https://launchpad.support.sap.com/#/supportpackage/SAPK-10020INREVRECSD\" target=\"_blank\">20</a>, <a href=\"https://launchpad.support.sap.com/#/supportpackage/SAPK-10021INREVRECSD\" target=\"_blank\">21 </a>, <a href=\"https://launchpad.support.sap.com/#/supportpackage/SAPK-10022INREVRECSD\" target=\"_blank\">22</a>, <a href=\"https://launchpad.support.sap.com/#/supportpackage/SAPK-100223INREVRECSD\" target=\"_blank\">23</a> , <a href=\"https://launchpad.support.sap.com/#/supportpackage/SAPK-10024INREVRECSD\" target=\"_blank\">24</a>, <a href=\"https://launchpad.support.sap.com/#/supportpackage/SAPK-10025INREVRECSD\" target=\"_blank\">25</a>, <a href=\"https://launchpad.support.sap.com/#/supportpackage/SAPK-10026INREVRECSD\" target=\"_blank\">26</a>, <a href=\"https://launchpad.support.sap.com/#/supportpackage/SAPK-10027INREVRECSD\" target=\"_blank\">27</a>, <a href=\"https://launchpad.support.sap.com/#/supportpackage/SAPK-10028INREVRECSD\" target=\"_blank\">28</a>, <a href=\"https://launchpad.support.sap.com/#/supportpackage/SAPK-10029INREVRECSD\" target=\"_blank\">29</a>, <a href=\"https://launchpad.support.sap.com/#/supportpackage/SAPK-10030INREVRECSD\" target=\"_blank\">3</a>0, <a href=\"https://launchpad.support.sap.com/#/supportpackage/SAPK-10031INREVRECSD\" target=\"_blank\">31</a>, <a href=\"https://launchpad.support.sap.com/#/supportpackage/SAPK-10032INREVRECSD\" target=\"_blank\">32</a>, <a href=\"https://launchpad.support.sap.com/#/supportpackage/SAPK-10033INREVRECSD\" target=\"_blank\">33</a> you can find in the corresponding links.</p>\n<p>To be considered:</p>\n<ul>\n<li>Integration into ECC-Tool for Local Currency Changeover</li>\n<ul>\n<li>Customers in countries where a currency reform is performed are asking to automatically adjust the currencies in their system (e.g. new countries planning to changeover to EURO: Croatia in 2023, Bulgaria in 2024, Romania in 2028)</li>\n<li>In ERP there are already tools available to convert currencies. They‘ve been developed in the course of the Euro introduction.</li>\n<li>In the past RAR 1.3 add-on was not capable to use the existing ERP tools.</li>\n<li>Enhancements had now been developed to integrate RAR into existing ECC conversion tool. </li>\n<li>In order to use this functionality involvement of SAP Data Management and Landscape Transformation (DMLT) is required.</li>\n</ul>\n</ul>\n<ul>\n<ul>\n<li>Please read note <a href=\"/notes/3369008\" target=\"_blank\">3369008 - Local Currency Conversion for Revenue Accounting and Reporting ECC 130</a></li>\n</ul>\n</ul>\n<p><a href=\"#DOC\" target=\"_blank\">For more information, see the documentation.</a></p>\n<p><strong><a name=\"SP19\" target=\"_blank\"></a>﻿Support Package Stack 19 (07/2024)</strong></p>\n<p>Support Pack 19 of SAP Revenue Accounting 1.3 has been released 08.07.2024. Support Pack 19 contains mainly program corrections. A full list of available support notes you can find <a href=\"https://launchpad.support.sap.com/#/supportpackage/SAPK-13019INREVREC\" target=\"_blank\">here</a>.</p>\n<p>For SD related processes, please also consider the corresponding support packages for the SD integration component. A full list of available support notes for support package <a href=\"https://launchpad.support.sap.com/#/supportpackage/SAPK-10016INREVRECSD\" target=\"_blank\">16</a>, <a href=\"https://launchpad.support.sap.com/#/supportpackage/SAPK-10017INREVRECSD\" target=\"_blank\">17</a>, <a href=\"https://launchpad.support.sap.com/#/supportpackage/SAPK-10018INREVRECSD\" target=\"_blank\">18</a>, <a href=\"https://launchpad.support.sap.com/#/supportpackage/SAPK-10019INREVRECSD\" target=\"_blank\">19</a>, <a href=\"https://launchpad.support.sap.com/#/supportpackage/SAPK-10020INREVRECSD\" target=\"_blank\">20</a>, <a href=\"https://launchpad.support.sap.com/#/supportpackage/SAPK-10021INREVRECSD\" target=\"_blank\">21 </a>, <a href=\"https://launchpad.support.sap.com/#/supportpackage/SAPK-10022INREVRECSD\" target=\"_blank\">22</a>, <a href=\"https://launchpad.support.sap.com/#/supportpackage/SAPK-100223INREVRECSD\" target=\"_blank\">23</a> , <a href=\"https://launchpad.support.sap.com/#/supportpackage/SAPK-10024INREVRECSD\" target=\"_blank\">24</a>, <a href=\"https://launchpad.support.sap.com/#/supportpackage/SAPK-10025INREVRECSD\" target=\"_blank\">25</a>, <a href=\"https://launchpad.support.sap.com/#/supportpackage/SAPK-10026INREVRECSD\" target=\"_blank\">26</a>, <a href=\"https://launchpad.support.sap.com/#/supportpackage/SAPK-10027INREVRECSD\" target=\"_blank\">27</a>, <a href=\"https://launchpad.support.sap.com/#/supportpackage/SAPK-10028INREVRECSD\" target=\"_blank\">28</a>, <a href=\"https://launchpad.support.sap.com/#/supportpackage/SAPK-10029INREVRECSD\" target=\"_blank\">29</a>, <a href=\"https://launchpad.support.sap.com/#/supportpackage/SAPK-10030INREVRECSD\" target=\"_blank\">3</a>0, <a href=\"https://launchpad.support.sap.com/#/supportpackage/SAPK-10031INREVRECSD\" target=\"_blank\">31</a>, <a href=\"https://launchpad.support.sap.com/#/supportpackage/SAPK-10032INREVRECSD\" target=\"_blank\">32</a>, <a href=\"https://launchpad.support.sap.com/#/supportpackage/SAPK-10033INREVRECSD\" target=\"_blank\">33</a>, <a href=\"https://launchpad.support.sap.com/#/supportpackage/SAPK-10034INREVRECSD\" target=\"_blank\">34</a> you can find in the corresponding links.</p>\n<p>To be considered:</p>\n<ul>\n<li>It is recommended implementing the latest SP as it contains the latest bug fixing and, thus, avoids cumbersome correction efforts.</li>\n</ul>\n<p><a href=\"#DOC\" target=\"_blank\">For more information, see the documentation.</a></p>", "noteVersion": 42}, {"note": "2254785", "noteTitle": "2254785 - Release strategy for the ABAP add-on REVRECSD 100", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>This SAP note contains information about planning the installation and upgrades of the ABAP add-on REVRECSD 100</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Release strategy, REVRECSD 100, Revenue Accounting, SAP Sales Integration with SAP Revenue Accounting and Reporting</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<ol>\n<li>General information</li>\n<li>Overview</li>\n<ol>\n<li>SAP Product Availability Matrix (PAM)</li>\n<li>Download</li>\n<li>Modifications</li>\n</ol>\n<li>REVRECSD 100</li>\n<ol>\n<li>Installation</li>\n<li>Delta Upgrade</li>\n<li>Supplement Upgrade</li>\n<li>Support Package</li>\n<li>Conflict Resolution Transport </li>\n</ol></ol>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<ol>\n<li>General information</li>\n</ol>\n<ul>\n<li>See the general information from SAP Note <a href=\"/notes/70228\" target=\"_blank\">70228</a>.</li>\n<li>In general it is not possible to uninstall ABAP add-ons. Exceptions see Note <a href=\"/notes/2011192\" target=\"_blank\">2011192</a>.</li>\n<li>You can install ABAP add-ons only on certain SAP releases.</li>\n<li>Upgrades with ABAP add-ons</li>\n</ul>\n<ol start=\"2\"><ol>\n<li>In systems in which an ABAP add-on is installed, you can upgrade only to SAP releases that are supported for this add-on.</li>\n<li>Note that there is a delay between the delivery of the SAP standard releases and the release of the corresponding add-on releases.</li>\n<li>If an add-on upgrade is connected with a change of the SAP release, it is integrated into the SAP upgrade (repository or system switch). To carry out this upgrade, you require an additional add-on upgrade CD or DVD in addition to the SAP standard upgrade CDs or DVDs. If the SAP standard upgrade is carried out without this additional CD or DVD, the add-on will no longer work after the upgrade. The entire SAP system is inconsistent.<br/>For more information about this problem, see SAP Note <a href=\"/notes/33040\" target=\"_blank\">33040</a>.</li>\n<li>If you are scheduling an upgrade or the import of an enhancement package, note that you should not import the latest Support Package Stack in the source release directly before the upgrade to ensure that the required, equivalent Support Package is already available in the target release.<br/>For more information about this, see SAP Note <a href=\"/notes/832594\" target=\"_blank\">832594</a> and the following information on SAP Service Marketplace.<br/><a href=\"http://support.sap.com/sp-stacks\" target=\"_blank\">http://support.sap.com/sp-stacks</a> -&gt; SP Stack Information -&gt; SP Stack Strategy</li>\n</ol></ol>\n<p>Please refer to the note 1841471 - Release strategy for ABAP add-ons for interface components for general information on ABAP add-ons for interface components</p>\n<p><strong>For upgrades to SAP S/4HANA 1809 or later:</strong><br/>Please note that starting with SAP S/4HANA 1809, the former Revenue Accounting and Reporting add-on including the add-on SAP Sales Integration with SAP Revenue Accounting and Reporting 1.0 have become an integral part of SAP S/4HANA. For more details please refer to the corresponding Release Information Notes for Revenue Accounting. For a smooth upgrade, the support package applied for your ABAP add-on REVRECSD 100 as well as Revenue Accounting 1.3 must be aligned with the corresponding support package of the SAP S/4HANA release you plan to upgrade to. For example, the support package of the ABAP add-ons must not be more current than the planned S/4HANA support package. Please ensure that your latest support packages of the Revenue Accounting 1.3 add-on and the add-on SAP Sales Integration with SAP Revenue Accounting and Reporting 1.0 are at least 6 months older (released) than the support package of the corresponding S/4HANA stack you plan to upgrade to. You can find the release date information of all support packages in the Product Availability Matrix (PAM).</p>\n<ol start=\"2\">\n<li>Overview</li>\n<ol>\n<li>SAP Product Availability Matrix<br/>The following information about the add-on in the SAP Product Availability Matrix is available on SAP Service Marketplace at <a href=\"https://service.sap.com/sap/support/pam\" target=\"_blank\">https://service.sap.com/sap/support/pam</a><br/>- Availability <br/>- End of maintenance <br/>- Release for products with enhancement packages <br/>- Language support</li>\n<li>Download<br/>The software is available on SAP Service Marketplace at <a href=\"https://support.sap.com/software.html\" target=\"_blank\">https://support.sap.com/software.html</a><br/>  -&gt; Search for Software Downloads<br/>      -&gt; REVRECSD 100<br/><br/>Search options: Only Software Components and Software Component Versions</li>\n<li>The add-on does not contain any modifications.<br/>You can also perform the installation if you have already imported more Support Packages into your system than are specified in the section \"Required Support Packages\".</li>\n</ol></ol>\n<p> </p>\n<ol start=\"3\">\n<li>REVRECSD 100</li>\n</ol>\n<p> SAP SALES INTEGR SAP RAR 1.0 ( REVRECSD 100 ) is supported on the Product Versions :</p>\n<p>SAP ERP 6.0</p>\n<p>EHP2 FOR SAP ERP 6.0</p>\n<p>EHP3 FOR SAP ERP 6.0</p>\n<p>EHP4 FOR SAP ERP 6.0</p>\n<p>EHP5 FOR SAP ERP 6.0</p>\n<p>EHP6 FOR SAP ERP 6.0</p>\n<p>EHP6 FOR SAP ERP 6.0 ON HANA</p>\n<p>EHP7 FOR SAP ERP 6.0</p>\n<p>EHP8 FOR SAP ERP 6.0</p>\n<p>SAP S/4HANA ON-PREMISE 1511</p>\n<p>SAP S/4HANA 1610</p>\n<p>SAP S/4HANA 1709</p>\n<p>SAP SFINANCIALS 1.0</p>\n<p>SAP SFINANCIALS 1503</p>\n<p>SAP S/4HANA Finance 1605</p>\n<p> </p>\n<p>Please make sure to download the latest version of the ACP for REVRECSD 100 before planning to install/upgrade to REVRECSD 100 on any of the above mentioned Product Versions .</p>\n<p>ACP file name: REVRECSD====100</p>\n<p> a. Installation</p>\n<p>The Software component version REVRECSD 100 is part of two different Product versions : SAP Sales Integration RAR 1.0 and SAP REVENUE RECOGNITION 1.0</p>\n<p>If you are installing REVRECSD 100 as a part of the Product Version SAP REVENUE RECOGNITION 1.0, then the material number is : 51048006</p>\n<p>If you are installing REVRECSD 100 as a part of the Product Version SAP Sales Integration RAR 1.0 , then the material number is : 51050389</p>\n<p>The software can be found in the above Material number as required        -&gt; DATA_UNITS -&gt; REVRECSD_100</p>\n<p><br/>Name of the Installation Package (AOI):</p>\n<p>SAPK-100AGINREVRECSD</p>\n<p>The Required Support Packages for installing REVRECSD 100 are mentioned below in a separate table for each of the supported EHP releases:</p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"3\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td>Component</td>\n<td>Release</td>\n<td>Support Packages</td>\n</tr>\n<tr>\n<td>EA-APPL</td>\n<td>600</td>\n<td>SAPK-60015INEAAPPL</td>\n</tr>\n<tr>\n<td>SAP_BASIS</td>\n<td>700</td>\n<td> </td>\n</tr>\n</tbody>\n</table></div>\n<p>Or</p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"3\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td>Component</td>\n<td>Release</td>\n<td>Support Packages</td>\n</tr>\n<tr>\n<td>EA-APPL</td>\n<td>602</td>\n<td>SAPK-60206INEAAPPL</td>\n</tr>\n<tr>\n<td>SAP_BASIS</td>\n<td>700</td>\n<td> </td>\n</tr>\n</tbody>\n</table></div>\n<p>Or</p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"3\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td>Component</td>\n<td>Release</td>\n<td>Support Packages</td>\n</tr>\n<tr>\n<td>EA-APPL</td>\n<td>603</td>\n<td>SAPK-60305INEAAPPL</td>\n</tr>\n<tr>\n<td>SAP_BASIS</td>\n<td>700</td>\n<td> </td>\n</tr>\n</tbody>\n</table></div>\n<p>Or</p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"3\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td>Component</td>\n<td>Release</td>\n<td>Support Packages</td>\n</tr>\n<tr>\n<td>EA-APPL</td>\n<td>604</td>\n<td>SAPK-60405INEAAPPL</td>\n</tr>\n<tr>\n<td>SAP_BASIS</td>\n<td>701</td>\n<td> </td>\n</tr>\n</tbody>\n</table></div>\n<p>Or</p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"3\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td>Component</td>\n<td>Release</td>\n<td>Support Packages</td>\n</tr>\n<tr>\n<td>EA-APPL</td>\n<td>605</td>\n<td>SAPK-60510INEAAPPL</td>\n</tr>\n<tr>\n<td>SAP_BASIS</td>\n<td>702</td>\n<td> </td>\n</tr>\n</tbody>\n</table></div>\n<p>Or</p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"3\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td>Component</td>\n<td>Release</td>\n<td>Support Packages</td>\n</tr>\n<tr>\n<td>EA-APPL</td>\n<td>606</td>\n<td>SAPK-60603INEAAPPL</td>\n</tr>\n<tr>\n<td>SAP_BASIS</td>\n<td>731</td>\n<td> </td>\n</tr>\n</tbody>\n</table></div>\n<p>Or</p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"3\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td>Component</td>\n<td>Release</td>\n<td>Support Packages</td>\n</tr>\n<tr>\n<td>EA-APPL</td>\n<td>616</td>\n<td>SAPK-61601INEAAPPL</td>\n</tr>\n<tr>\n<td>SAP_BASIS</td>\n<td>702</td>\n<td> </td>\n</tr>\n</tbody>\n</table></div>\n<p>Or</p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"3\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td>Component</td>\n<td>Release</td>\n<td>Support Packages</td>\n</tr>\n<tr>\n<td>EA-APPL</td>\n<td>617</td>\n<td>SAPK-61701INEAAPPL</td>\n</tr>\n<tr>\n<td>EA-FIN</td>\n<td>617</td>\n<td>SAPK-61701INEAFIN</td>\n</tr>\n<tr>\n<td>SAP_BASIS</td>\n<td>740</td>\n<td> </td>\n</tr>\n</tbody>\n</table></div>\n<p>Or</p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"3\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td>Component</td>\n<td>Release</td>\n<td>Support Packages</td>\n</tr>\n<tr>\n<td>EA-APPL</td>\n<td>617</td>\n<td>SAPK-61701INEAAPPL</td>\n</tr>\n<tr>\n<td>EA-FIN</td>\n<td>700</td>\n<td> </td>\n</tr>\n<tr>\n<td>SAP_BASIS</td>\n<td>740</td>\n<td> </td>\n</tr>\n</tbody>\n</table></div>\n<p>Or</p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"3\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td>Component</td>\n<td>Release</td>\n<td>Support Packages</td>\n</tr>\n<tr>\n<td>EA-APPL</td>\n<td>617</td>\n<td>SAPK-61701INEAAPPL</td>\n</tr>\n<tr>\n<td>EA-FIN</td>\n<td>720</td>\n<td> </td>\n</tr>\n<tr>\n<td>SAP_BASIS</td>\n<td>740</td>\n<td> </td>\n</tr>\n</tbody>\n</table></div>\n<p> Or</p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"3\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td>Component</td>\n<td>Release</td>\n<td>Support Packages</td>\n</tr>\n<tr>\n<td>SAP_APPL</td>\n<td>618</td>\n<td> </td>\n</tr>\n<tr>\n<td>SAP_FIN</td>\n<td>618</td>\n<td> </td>\n</tr>\n<tr>\n<td>SAP_BASIS</td>\n<td>750</td>\n<td> </td>\n</tr>\n</tbody>\n</table></div>\n<p>Or [For SAP S/4HANA ON-PREMISE 1511]</p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"3\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td>Component</td>\n<td>Release</td>\n<td>Support Packages</td>\n</tr>\n<tr>\n<td>S4CORE</td>\n<td>100</td>\n<td> </td>\n</tr>\n<tr>\n<td>SAP_BASIS</td>\n<td>750</td>\n<td> </td>\n</tr>\n</tbody>\n</table></div>\n<p>Or [For SAP S/4HANA 1610]</p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"3\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td>Component</td>\n<td>Release</td>\n<td>Support Packages</td>\n</tr>\n<tr>\n<td>S4CORE</td>\n<td>101</td>\n<td> </td>\n</tr>\n<tr>\n<td>SAP_BASIS</td>\n<td>751</td>\n<td> </td>\n</tr>\n</tbody>\n</table></div>\n<p>Or [For SAP S/4HANA 1709]</p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"3\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td>Component</td>\n<td>Release</td>\n<td>Support Packages</td>\n</tr>\n<tr>\n<td>S4CORE</td>\n<td>102</td>\n<td> </td>\n</tr>\n<tr>\n<td>SAP_BASIS</td>\n<td>752</td>\n<td> </td>\n</tr>\n</tbody>\n</table></div>\n<p>Or [For SAP SFINANCIALS 1503]</p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"3\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td>Component</td>\n<td>Release</td>\n<td>Support Packages</td>\n</tr>\n<tr>\n<td>\n<p>EA-FIN</p>\n</td>\n<td>720</td>\n<td> </td>\n</tr>\n<tr>\n<td>SAP_BASIS</td>\n<td>747</td>\n<td> </td>\n</tr>\n</tbody>\n</table></div>\n<p>Or [For SAP SAP S/4HANA Finance 1605]</p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"3\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td>Component</td>\n<td>Release</td>\n<td>Support Packages</td>\n</tr>\n<tr>\n<td>SAP_FIN</td>\n<td>730</td>\n<td> </td>\n</tr>\n<tr>\n<td>SAP_BASIS</td>\n<td>750</td>\n<td> </td>\n</tr>\n</tbody>\n</table></div>\n<p> </p>\n<p>b. Delta Upgrade<br/>Name of the delta upgrade package (AOU):</p>\n<p>SAPK-110BGINREVREC - REVREC 110: Add-On Upgrade Prototype -  CSN0120061532 0096391 PAT</p>\n<p>The Required Support Packages for upgrading to REVREC 110 are mentioned below in a separate table for each of the supported EHP releases:</p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"3\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td>Component</td>\n<td>Release</td>\n<td>Support Packages</td>\n</tr>\n<tr>\n<td>EA-APPL</td>\n<td>605</td>\n<td>SAPK-60510INEAAPPL</td>\n</tr>\n<tr>\n<td>SAP_BASIS</td>\n<td>702</td>\n<td> </td>\n</tr>\n<tr>\n<td>REVREC</td>\n<td>100</td>\n<td> </td>\n</tr>\n</tbody>\n</table></div>\n<p>Or</p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"3\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td>Component</td>\n<td>Release</td>\n<td>Support Packages</td>\n</tr>\n<tr>\n<td>EA-APPL</td>\n<td>606</td>\n<td>SAPK-60606INEAAPPL</td>\n</tr>\n<tr>\n<td>SAP_BASIS</td>\n<td>731</td>\n<td> </td>\n</tr>\n<tr>\n<td>REVREC</td>\n<td>100</td>\n<td> </td>\n</tr>\n</tbody>\n</table></div>\n<p>Or</p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"3\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td>Component</td>\n<td>Release</td>\n<td>Support Packages</td>\n</tr>\n<tr>\n<td>EA-APPL</td>\n<td>616</td>\n<td>SAPK-61601INEAAPPL</td>\n</tr>\n<tr>\n<td>SAP_BASIS</td>\n<td>702</td>\n<td> </td>\n</tr>\n<tr>\n<td>REVREC</td>\n<td>100</td>\n<td> </td>\n</tr>\n</tbody>\n</table></div>\n<p>Or</p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"3\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td>Component</td>\n<td>Release</td>\n<td>Support Packages</td>\n</tr>\n<tr>\n<td>EA-APPL</td>\n<td>617</td>\n<td>SAPK-61701INEAAPPL</td>\n</tr>\n<tr>\n<td>EA-FIN</td>\n<td>617</td>\n<td>SAPK-61701INEAFIN</td>\n</tr>\n<tr>\n<td>SAP_BASIS</td>\n<td>740</td>\n<td> </td>\n</tr>\n<tr>\n<td>REVREC</td>\n<td>100</td>\n<td> </td>\n</tr>\n</tbody>\n</table></div>\n<p>Or</p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"3\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td>Component</td>\n<td>Release</td>\n<td>Support Packages</td>\n</tr>\n<tr>\n<td>EA-APPL</td>\n<td>617</td>\n<td>SAPK-61701INEAAPPL</td>\n</tr>\n<tr>\n<td>EA-FIN</td>\n<td>700</td>\n<td> </td>\n</tr>\n<tr>\n<td>SAP_BASIS</td>\n<td>740</td>\n<td> </td>\n</tr>\n<tr>\n<td>REVREC</td>\n<td>100</td>\n<td> </td>\n</tr>\n</tbody>\n</table></div>\n<p>Or</p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"3\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td>Component</td>\n<td>Release</td>\n<td>Support Packages</td>\n</tr>\n<tr>\n<td>EA-APPL</td>\n<td>617</td>\n<td>SAPK-61701INEAAPPL</td>\n</tr>\n<tr>\n<td>EA-FIN</td>\n<td>720</td>\n<td> </td>\n</tr>\n<tr>\n<td>SAP_BASIS</td>\n<td>740</td>\n<td> </td>\n</tr>\n<tr>\n<td>REVREC</td>\n<td>100</td>\n<td> </td>\n</tr>\n</tbody>\n</table></div>\n<p> Or</p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"3\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td>Component</td>\n<td>Release</td>\n<td>Support Packages</td>\n</tr>\n<tr>\n<td>SAP_APPL</td>\n<td>618</td>\n<td> </td>\n</tr>\n<tr>\n<td>SAP_FIN</td>\n<td>618</td>\n<td> </td>\n</tr>\n<tr>\n<td>SAP_BASIS</td>\n<td>750</td>\n<td> </td>\n</tr>\n<tr>\n<td>REVREC</td>\n<td>100</td>\n<td> </td>\n</tr>\n</tbody>\n</table></div>\n<p>Or [For SAP S/4HANA ON-PREMISE 1511]</p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"3\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td>Component</td>\n<td>Release</td>\n<td>Support Packages</td>\n</tr>\n<tr>\n<td>S4CORE</td>\n<td>100</td>\n<td> </td>\n</tr>\n<tr>\n<td>SAP_BASIS</td>\n<td>750</td>\n<td> </td>\n</tr>\n<tr>\n<td>REVREC</td>\n<td>100</td>\n<td> </td>\n</tr>\n</tbody>\n</table></div>\n<p> </p>\n<p><strong><span>Activities after installaing / upgrading to REVREC 110:</span></strong></p>\n<p><strong>c.Supplement Upgrade</strong></p>\n<p>No supplement upgrade package available for REVRECSD 100.</p>\n<p><strong>d.Support Package</strong></p>\n<p>SAP SALES INTEGR SAP RAR 1.0 is initially shipped with SP05 of software component version REVRECSD 100.</p>\n<p><strong>e.Conflict Resolution Transport</strong></p>\n<p> No Conflict Resolution Transports available for REVRECSD 100.</p>", "noteVersion": 5}, {"note": "2386978", "noteTitle": "2386978 - Release strategy for the ABAP add-on REVREC 130", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>This SAP Note contains information about planning the installation and upgrades of the ABAP add-on REVREC 130.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Release strategy, REVREC 130</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<ol>\n<li>General information</li>\n<li>Overview</li>\n<ol>\n<li>SAP Product Availability Matrix (PAM)</li>\n<li>Download</li>\n<li>Modifications</li>\n</ol>\n<li>REVREC 130</li>\n<ol>\n<li>Installation</li>\n<li>Delta Upgrade</li>\n<li>Supplement Upgrade</li>\n<li>Support Package</li>\n</ol></ol>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<ol>\n<li>General information</li>\n</ol>\n<ul>\n<li>See the general information from SAP Note <a href=\"/notes/70228\" target=\"_blank\">70228</a>.</li>\n<li>In general it is not possible to uninstall ABAP add-ons. Exceptions see Note <a href=\"/notes/2011192\" target=\"_blank\">2011192</a>.</li>\n<li>You can install ABAP add-ons only on certain SAP releases.</li>\n<li>Upgrades with ABAP add-ons</li>\n</ul>\n<ol start=\"2\"><ol>\n<li>Please follow the steps in the upgrade chapter of the Administrator’s Guide. You can visit help.sap.com/revacc_130 to download the guide.</li>\n<li>In systems in which an ABAP add-on is installed, you can upgrade only to SAP releases that are supported for this add-on.</li>\n<li>Note that there is a delay between the delivery of the SAP standard releases and the release of the corresponding add-on releases.</li>\n<li>If an add-on upgrade is connected with a change of the SAP release, it is integrated into the SAP upgrade (repository or system switch). To carry out this upgrade, you require an additional add-on exchange package in addition to the SAP standard upgrade packages (CDs or DVDs). If the SAP standard upgrade is carried out without this additional exchange package, the add-on will no longer work after the upgrade. The entire SAP system is inconsistent.<br/>For more information about this problem, see SAP Note <a href=\"/notes/33040\" target=\"_blank\">33040</a>.</li>\n<li>If you are scheduling an upgrade or the import of an enhancement package, note that you should not import the latest Support Package Stack in the source release directly before the upgrade to ensure that the required, equivalent Support Package is already available in the target release.<br/>For more information about this, see SAP Note <a href=\"/notes/832594\" target=\"_blank\">832594</a> and the following information on SAP Support Portal.<br/><a href=\"http://support.sap.com/sp-stacks\" target=\"_blank\">http://support.sap.com/sp-stacks</a> -&gt; SP Stack Information -&gt; SP Stack Strategy.     </li>\n</ol></ol>\n<ul>\n<li><span><strong>For upgrades to SAP S/4HANA 1809 or later:</strong><br/>Please note that starting with SAP S/4HANA 1809, the former Revenue Accounting and Reporting add-on including the add-on SAP Sales Integration with SAP Revenue Accounting and Reporting 1.0 have become an integral part of SAP S/4HANA. For more details please refer to the corresponding Release Information Notes for Revenue Accounting. For a smooth upgrade, the support package applied for your ABAP add-on REVRECSD 100 as well as Revenue Accounting 1.3 must be aligned with the corresponding support package of the SAP S/4HANA release you plan to upgrade to. For example, the support package of the ABAP add-ons must not be more current than the planned S/4HANA support package. Please ensure that your latest support packages of the Revenue Accounting 1.3 add-on and the add-on SAP Sales Integration with SAP Revenue Accounting and Reporting 1.0 are at least 6 months older (released) than the support package of the corresponding S/4HANA stack you plan to upgrade to. You can find the release date information of all support packages in the Product Availability Matrix (PAM).</span></li>\n<li>\n<p>When installing SP03 or upgrading to SP03 or higher<em>, NW702 SP18 or corresponding NW equivalent SP stack</em> is required. Refer to the below table for equivalents. Make sure you upgrade to the required NW support package level to run add-on properly</p>\n</li>\n</ul>\n<p> </p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"3\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td>Component</td>\n<td>Release</td>\n<td>Support Packages</td>\n</tr>\n<tr>\n<td>Netweaver</td>\n<td>702</td>\n<td>SP18</td>\n</tr>\n<tr>\n<td>Netweaver</td>\n<td>731</td>\n<td>SP18</td>\n</tr>\n<tr>\n<td>Netweaver</td>\n<td>740</td>\n<td>SP15</td>\n</tr>\n<tr>\n<td>Netweaver</td>\n<td>750</td>\n<td>SP03</td>\n</tr>\n</tbody>\n</table></div>\n<ol start=\"2\">\n<li>Overview</li>\n<ol>\n<li>SAP Product Availability Matrix<br/>The following information about the add-on in the SAP Product Availability Matrix is available on SAP Support Portal at <a href=\"https://service.sap.com/sap/support/pam\" target=\"_blank\">https://support.sap.com/pam</a><br/>- Availability <br/>- End of maintenance <br/>- Release for products with enhancement packages <br/>- Language support</li>\n<li>Download<br/>The software is available on SAP Support Portal at <a href=\"https://support.sap.com/software.html\" target=\"_blank\">https://support.sap.com/software.html</a><br/>  -&gt; Search for Software Downloads<br/>      -&gt; REVREC 130</li>\n<li>The add-on does not contain any modifications.<br/>You can also perform the installation if you have already imported more Support Packages into your system than are specified in the section \"Required Support Packages\".</li>\n</ol></ol>\n<p> </p>\n<ol start=\"3\">\n<li>REVREC 130</li>\n</ol>\n<p>           SAP Revenue Accounting and Reporting 1.3 ( REVREC 130 ) is supported on the Product Versions :</p>\n<p> </p>\n<p>EHP5 FOR SAP ERP 6.0</p>\n<p>EHP6 FOR SAP ERP 6.0</p>\n<p>EHP6 FOR SAP ERP 6.0 ON HANA</p>\n<p>EHP7 FOR SAP ERP 6.0</p>\n<p>EHP8 FOR SAP ERP 6.0</p>\n<p>SAP S/4HANA ON-PREMISE 1511</p>\n<p>SAP SFINANCIALS 1.0</p>\n<p>SAP SFINANCIALS 1503</p>\n<p>SAP SFINANCIALS 1605</p>\n<p>SAP S/4HANA 1610</p>\n<p>SAP S/4HANA 1709</p>\n<p>a.          Installation</p>\n<p><br/>Name of the Installation Package (AOI): SAPK-130AGINREVREC</p>\n<p>Material Number :********     -&gt;DATA_UNITS-&gt;REVREC_130</p>\n<p>EPS File Name :I710020751258_0108083.PAT<br/>The password is: 812E313959<br/>          <br/><br/>Required Support Packages  for installing REVREC 130 are mentioned below in a separate table for each of the supported EHP releases:</p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"3\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td>Component</td>\n<td>Release</td>\n<td>Support Packages</td>\n</tr>\n<tr>\n<td>SAP_BASIS</td>\n<td>702</td>\n<td> </td>\n</tr>\n<tr>\n<td>EA-APPL</td>\n<td>605</td>\n<td>SAPK-60510INEAAPPL</td>\n</tr>\n</tbody>\n</table></div>\n<p>                OR</p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"3\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td>Component</td>\n<td>Release</td>\n<td>Support Packages</td>\n</tr>\n<tr>\n<td>SAP_BASIS</td>\n<td>731</td>\n<td> </td>\n</tr>\n<tr>\n<td>EA-APPL</td>\n<td>606</td>\n<td>SAPK-60606INEAAPPL</td>\n</tr>\n</tbody>\n</table></div>\n<p>OR</p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"3\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td>Component</td>\n<td>Release</td>\n<td>Support Packages</td>\n</tr>\n<tr>\n<td>SAP_BASIS</td>\n<td>702</td>\n<td> </td>\n</tr>\n<tr>\n<td>EA-APPL</td>\n<td>616</td>\n<td>SAPK-61601INEAAPPL</td>\n</tr>\n</tbody>\n</table></div>\n<p>OR</p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"3\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td>Component</td>\n<td>Release</td>\n<td>Support Packages</td>\n</tr>\n<tr>\n<td>SAP_BASIS</td>\n<td>740</td>\n<td> </td>\n</tr>\n<tr>\n<td>EA-APPL</td>\n<td>617</td>\n<td>SAPK-61701INEAAPPL</td>\n</tr>\n<tr>\n<td>\n<p>SAP_APPL</p>\n</td>\n<td>617</td>\n<td>SAPKH61701</td>\n</tr>\n<tr>\n<td>\n<p>SAP_FIN</p>\n</td>\n<td>617</td>\n<td>SAPK-61701INSAPFIN</td>\n</tr>\n<tr>\n<td>\n<p>EA-FIN</p>\n</td>\n<td>617</td>\n<td>SAPK-61701INEAFIN</td>\n</tr>\n</tbody>\n</table></div>\n<p>OR</p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"3\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td>Component</td>\n<td>Release</td>\n<td>Support Packages</td>\n</tr>\n<tr>\n<td>SAP_BASIS</td>\n<td>740</td>\n<td> </td>\n</tr>\n<tr>\n<td>EA-APPL</td>\n<td>617</td>\n<td>SAPK-61701INEAAPPL</td>\n</tr>\n<tr>\n<td>\n<p>SAP_APPL</p>\n</td>\n<td>617</td>\n<td>SAPKH61701</td>\n</tr>\n<tr>\n<td>\n<p>SAP_FIN</p>\n</td>\n<td>700</td>\n<td>SAPK-70005INSAPFIN</td>\n</tr>\n<tr>\n<td>\n<p>EA-FIN</p>\n</td>\n<td>700</td>\n<td>SAPK-70005INEAFIN</td>\n</tr>\n</tbody>\n</table></div>\n<p>OR</p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"3\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td>Component</td>\n<td>Release</td>\n<td>Support Packages</td>\n</tr>\n<tr>\n<td>SAP_BASIS</td>\n<td>740</td>\n<td> </td>\n</tr>\n<tr>\n<td>EA-APPL</td>\n<td>617</td>\n<td>SAPK-61701INEAAPPL</td>\n</tr>\n<tr>\n<td>\n<p>SAP_APPL</p>\n</td>\n<td>617</td>\n<td>SAPKH61701</td>\n</tr>\n<tr>\n<td>\n<p>SAP_FIN</p>\n</td>\n<td>720</td>\n<td> </td>\n</tr>\n<tr>\n<td>\n<p>EA-FIN</p>\n</td>\n<td>720</td>\n<td> </td>\n</tr>\n</tbody>\n</table></div>\n<p> OR</p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"3\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td>Component</td>\n<td>Release</td>\n<td>Support Packages</td>\n</tr>\n<tr>\n<td>SAP_BASIS</td>\n<td>750</td>\n<td>SAPK-75001INSAPBASIS</td>\n</tr>\n<tr>\n<td>\n<p>SAP_APPL</p>\n</td>\n<td>618</td>\n<td>SAPK-61801INSAPAPPL</td>\n</tr>\n<tr>\n<td>\n<p>SAP_FIN</p>\n</td>\n<td>618</td>\n<td>SAPK-61801INSAPFIN</td>\n</tr>\n</tbody>\n</table></div>\n<p>OR</p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"3\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td>Component</td>\n<td>Release</td>\n<td>Support Packages</td>\n</tr>\n<tr>\n<td>SAP_BASIS</td>\n<td>750</td>\n<td>SAPK-75001INSAPBASIS</td>\n</tr>\n<tr>\n<td>\n<p>SAP_APPL</p>\n</td>\n<td>618</td>\n<td>SAPK-61801INSAPAPPL</td>\n</tr>\n<tr>\n<td>\n<p>SAP_FIN</p>\n</td>\n<td>730</td>\n<td> </td>\n</tr>\n</tbody>\n</table></div>\n<p>OR</p>\n<p>[For SAP S/4HANA ON-PREMISE 1511]</p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"3\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td>Component</td>\n<td>Release</td>\n<td>Support Packages</td>\n</tr>\n<tr>\n<td>SAP_BASIS</td>\n<td>750</td>\n<td> </td>\n</tr>\n<tr>\n<td>S4CORE</td>\n<td>100</td>\n<td> </td>\n</tr>\n</tbody>\n</table></div>\n<p>OR</p>\n<p>[For SAP S/4HANA 1610]</p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"3\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td>Component</td>\n<td>Release</td>\n<td>Support Package</td>\n</tr>\n<tr>\n<td>SAP_BASIS</td>\n<td>751</td>\n<td> </td>\n</tr>\n<tr>\n<td>S4CORE</td>\n<td>101</td>\n<td> </td>\n</tr>\n</tbody>\n</table></div>\n<p> </p>\n<p>OR</p>\n<p>[For SAP S/4HANA 1709]</p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"3\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td>Component</td>\n<td>Release</td>\n<td>Support Package</td>\n</tr>\n<tr>\n<td>SAP_BASIS</td>\n<td>752</td>\n<td> </td>\n</tr>\n<tr>\n<td>S4CORE</td>\n<td>102</td>\n<td> </td>\n</tr>\n</tbody>\n</table></div>\n<p>b.      Delta Upgrade</p>\n<p><br/>Name of the delta upgrade package (AOI): SAPK-130AGINREVREC</p>\n<p><br/>EPS File Name :I710020751258_0108083.PAT<br/>The password is: 812E313959<br/>          <br/><br/>Required Support Packages for installing REVREC 130 are mentioned below in a separate table for each of the supported EHP releases:</p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"3\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td>Component</td>\n<td>Release</td>\n<td>Support Packages</td>\n</tr>\n<tr>\n<td>SAP_BASIS</td>\n<td>702</td>\n<td> </td>\n</tr>\n<tr>\n<td>EA-APPL</td>\n<td>605</td>\n<td>SAPK-60510INEAAPPL</td>\n</tr>\n<tr>\n<td>REVREC</td>\n<td>100/110/120</td>\n<td> </td>\n</tr>\n</tbody>\n</table></div>\n<p>OR</p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"3\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td>Component</td>\n<td>Release</td>\n<td>Support Packages</td>\n</tr>\n<tr>\n<td>SAP_BASIS</td>\n<td>731</td>\n<td> </td>\n</tr>\n<tr>\n<td>EA-APPL</td>\n<td>606</td>\n<td>SAPK-60606INEAAPPL</td>\n</tr>\n<tr>\n<td>REVREC</td>\n<td>100/110/120</td>\n<td> </td>\n</tr>\n</tbody>\n</table></div>\n<p>OR</p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"3\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td>Component</td>\n<td>Release</td>\n<td>Support Packages</td>\n</tr>\n<tr>\n<td>SAP_BASIS</td>\n<td>702</td>\n<td> </td>\n</tr>\n<tr>\n<td>EA-APPL</td>\n<td>616</td>\n<td>SAPK-61601INEAAPPL</td>\n</tr>\n<tr>\n<td>REVREC</td>\n<td>100/110/120</td>\n<td> </td>\n</tr>\n</tbody>\n</table></div>\n<p>OR</p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"3\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td>Component</td>\n<td>Release</td>\n<td>Support Packages</td>\n</tr>\n<tr>\n<td>SAP_BASIS</td>\n<td>740</td>\n<td> </td>\n</tr>\n<tr>\n<td>EA-APPL</td>\n<td>617</td>\n<td>SAPK-61701INEAAPPL</td>\n</tr>\n<tr>\n<td>\n<p>SAP_APPL</p>\n</td>\n<td>617</td>\n<td>SAPKH61701</td>\n</tr>\n<tr>\n<td>\n<p>SAP_FIN</p>\n</td>\n<td>617</td>\n<td>SAPK-61701INSAPFIN</td>\n</tr>\n<tr>\n<td>\n<p>EA-FIN</p>\n</td>\n<td>617</td>\n<td>SAPK-61701INEAFIN</td>\n</tr>\n<tr>\n<td>\n<p>REVREC</p>\n</td>\n<td>100/110/120</td>\n<td> </td>\n</tr>\n</tbody>\n</table></div>\n<p>OR</p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"3\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td>Component</td>\n<td>Release</td>\n<td>Support Packages</td>\n</tr>\n<tr>\n<td>SAP_BASIS</td>\n<td>740</td>\n<td> </td>\n</tr>\n<tr>\n<td>EA-APPL</td>\n<td>617</td>\n<td>SAPK-61701INEAAPPL</td>\n</tr>\n<tr>\n<td>\n<p>SAP_APPL</p>\n</td>\n<td>617</td>\n<td>SAPKH61701</td>\n</tr>\n<tr>\n<td>\n<p>SAP_FIN</p>\n</td>\n<td>700</td>\n<td>SAPK-70005INSAPFIN</td>\n</tr>\n<tr>\n<td>\n<p>EA-FIN</p>\n</td>\n<td>700</td>\n<td>SAPK-70005INEAFIN</td>\n</tr>\n<tr>\n<td>\n<p>REVREC</p>\n</td>\n<td>100/110/120</td>\n<td> </td>\n</tr>\n</tbody>\n</table></div>\n<p>OR</p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"3\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td>Components</td>\n<td>Release</td>\n<td>Support Packages</td>\n</tr>\n<tr>\n<td>SAP_BASIS</td>\n<td>740</td>\n<td> </td>\n</tr>\n<tr>\n<td>EA-APPL</td>\n<td>617</td>\n<td>SAPK-61701INEAAPPL</td>\n</tr>\n<tr>\n<td>SAP_APPL</td>\n<td>617</td>\n<td>SAPKH61701</td>\n</tr>\n<tr>\n<td>SAP_FIN</td>\n<td>720</td>\n<td> </td>\n</tr>\n<tr>\n<td>EA_FIN</td>\n<td>720</td>\n<td> </td>\n</tr>\n<tr>\n<td>REVREC</td>\n<td>100/110/120</td>\n<td> </td>\n</tr>\n</tbody>\n</table></div>\n<p>OR</p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"3\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td>Component</td>\n<td>Release</td>\n<td>Support Packages</td>\n</tr>\n<tr>\n<td>SAP_BASIS</td>\n<td>750</td>\n<td>SAPK-75001INSAPBASIS</td>\n</tr>\n<tr>\n<td>\n<p>SAP_APPL</p>\n</td>\n<td>618</td>\n<td>SAPK-61801INSAPAPPL</td>\n</tr>\n<tr>\n<td>\n<p>SAP_FIN</p>\n</td>\n<td>618</td>\n<td>SAPK-61801INSAPFIN</td>\n</tr>\n<tr>\n<td>\n<p>REVREC</p>\n</td>\n<td>100/110/120</td>\n<td> </td>\n</tr>\n</tbody>\n</table></div>\n<p>OR</p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"3\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td>Component</td>\n<td>Release</td>\n<td>Support Packages</td>\n</tr>\n<tr>\n<td>SAP_BASIS</td>\n<td>750</td>\n<td>SAPK-75001INSAPBASIS</td>\n</tr>\n<tr>\n<td>SAP_APPL</td>\n<td>618</td>\n<td>SAPK-61801INSAPAPPL</td>\n</tr>\n<tr>\n<td>SAP_FIN</td>\n<td>730</td>\n<td> </td>\n</tr>\n<tr>\n<td>REVREC</td>\n<td>100/110/120</td>\n<td> </td>\n</tr>\n</tbody>\n</table></div>\n<p>OR</p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"3\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td>Components</td>\n<td>Release</td>\n<td>Support packages</td>\n</tr>\n<tr>\n<td>SAP_BASIS</td>\n<td>751</td>\n<td> </td>\n</tr>\n<tr>\n<td>S4CORE</td>\n<td>101</td>\n<td> </td>\n</tr>\n<tr>\n<td>REVREC</td>\n<td>100/110/120</td>\n<td> </td>\n</tr>\n</tbody>\n</table></div>\n<p> </p>\n<p>OR</p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"3\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td>Component</td>\n<td>Release</td>\n<td>Support Package</td>\n</tr>\n<tr>\n<td>SAP_BASIS</td>\n<td>750</td>\n<td> </td>\n</tr>\n<tr>\n<td>S4CORE</td>\n<td>100</td>\n<td> </td>\n</tr>\n<tr>\n<td>REVREC</td>\n<td>100/110/120</td>\n<td> </td>\n</tr>\n</tbody>\n</table></div>\n<p> OR</p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"3\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td>Component</td>\n<td>Release</td>\n<td>Support Package</td>\n</tr>\n<tr>\n<td>SAP_BASIS</td>\n<td>752</td>\n<td> </td>\n</tr>\n<tr>\n<td>S4CORE</td>\n<td>102</td>\n<td> </td>\n</tr>\n<tr>\n<td>REVREC</td>\n<td>100/110/120</td>\n<td> </td>\n</tr>\n</tbody>\n</table></div>\n<p>c.        Supplement Upgrade</p>\n<p><br/>No supplement upgrade pacakge available for REVREC 130.</p>\n<p><br/>d.         Available Support Packages<br/><br/>  Support Package                    Availability Date:</p>\n<p><br/>SAPK-13001INREVREC               24.03.2017</p>\n<p>SAPK-13002INREVREC               19.05.2017</p>\n<p>SAPK-13003INREVREC               11.08.2017</p>\n<p>SAPK-13004INREVREC               15.12.2017</p>\n<p>SAPK-13005INREVREC               23.03.2018</p>\n<p>SAPK-13006INREVREC               13.07.2018</p>\n<p>SAPK-13007INREVREC               30.11.2018</p>\n<p>SAPK-13008INREVREC               11.03.2019</p>\n<p>SAPK-13009INREVREC               10.06.2019</p>\n<p>SAPK-13010INREVREC               11.09.2019</p>\n<p>SAPK-13011INREVREC               16.12.2019</p>\n<p>SAPK-13012INREVREC               08.06.2020</p>\n<p>SAPK-13013INREVREC               13.11.2020</p>\n<p>SAPK-13014INREVREC               21.05.2021</p>\n<p>SAPK-13015INREVREC               28.03.2022</p>\n<p>SAPK-13016INREVREC               10.10.2022</p>\n<p>SAPK-13017INREVREC               17.04.2023</p>\n<p>SAPK-13018INREVREC               09.10.2023</p>\n<p>SAPK-13019INREVREC               08.07.2024</p>\n<p>e. Known Errors during Support Packages Import:</p>\n<p>While applying REVREC 130 SP03 or higher in a system with SAP_BASIS 702 lower than SP18 (or equivalent higher SAP_BASIS release - see above); you might get an ABAP Runtime error for program <strong>CL_FARR_DB_UPDATE=============CP, </strong></p>\n<p>Include<strong> <strong>CL_FARR_DB_UPDATE=============CM003,</strong></strong> Method: <strong>CHECK_WHITELIST</strong></p>\n<p>Table /1RA/0SD010MI is not allowed (whitelisted).</p>\n<p>Solution: Apply SAP BASIS Note 2223641 or update SAP_BASIS 702 to SP18 (or equivalent).</p>\n<p> </p>\n<p><br/><br/></p>", "noteVersion": 26}, {"note": "2341717", "noteTitle": "2341717 - FAQ: Future of SD Revenue Recognition after IFRS15 is released", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>This note meant to answer frequently asked questions about the future of SAP ERP SD Revenue Recognition solution (SD RevRec) when the new accounting standards such as \"Revenues from Contracts with Customers\" (IFRS15) are released.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>SD Revenue Recognition, SD RR, SD RevRec, SAP Revenue Accounting and Reporting, SAP RAR, SFIN, SFINANCIALS, S/4HANA</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<div class=\"WordSection1\">\n<p>Please note that the information provided here may be incomplete and that there is no warranty for the correctness and up-to-dateness of the contents.</p>\n</div>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong>1. Does SD Revenue Recognition (SD-BIL-RR) support new accounting standards such as IFRS15?</strong></p>\n<p>The existing SD Revenue Recognition functionality has limitations and therefore cannot be supported by the standard solution for the new accounting standards, such as \"Revenues from Contracts with Customers\" (IFRS15).</p>\n<p><strong>2. Is there a new SAP solution already available which can fullfill the new standard?</strong></p>\n<p>As of SAP ERP 6.0 EHP5 and SAP S/4HANA, a new <a href=\"https://help.sap.com/viewer/product/DRAFT/SAP_REVENUE_ACCOUNTING_AND_REPORTING/1.3.3/en-US\" target=\"_blank\">SAP Revenue Accounting and Reporting 1.3</a> (SAP RAR) solution is available which can fulfill the new standard. The new requirements include price allocations between separate performance obligations or the combination of performance obligations to the compound groups. For more information, see SAP Note <a href=\"/notes/2582784\" target=\"_blank\">2582784</a>.</p>\n<p><strong>3. Does a new SAP solution (SAP RAR) include the full functional capabilities of SD Revenue Recognition?</strong></p>\n<p>Please refer to SAP Note <a href=\"/notes/2591055\" target=\"_blank\">2591055</a>.</p>\n<p><strong>4. How is SD integration with SAP RAR realized?</strong></p>\n<p>The SD integration with the new SAP RAR solution is realized via \"Integration Component\" (software component REVRECSD) as an Add-on which can be installed on SAP ERP 6.00 SPS15 systems as minimum required release level. For more information see the following documentation <a href=\"https://websmp102.sap-ag.de/~sapidb/012002523100019017592016E/AG_REVACC_SD_13.pdf\" target=\"_blank\">SD Integration with SAP Revenue Accounting and Reporting 1.0.</a></p>\n<p><strong>5. How can the new SAP RAR solution be activated?</strong></p>\n<p><em>The use of Revenue Accounting and Reporting requires a very conscientious and responsible approach to the subject. Thus SAP decided <strong>to release Revenue Accounting and Reporting individually to customers</strong> so that SAP can ensure customers intending to use this function understand and agree to the conditions and prerequisites that must be met. This process applies to all customers who have not used Revenue Accounting and Reporting in earlier releases. </em></p>\n<p><em>Despite this special release process, the responsibility for setting up, using and operating Revenue Accounting remains with the customer.</em></p>\n<p><em>You can find details on this activation process in the following note:</em></p>\n<p><a href=\"/notes/2750710\" target=\"_blank\"><em>2750710</em></a><em> - <strong>Activation of Revenue Accounting and Reporting</strong> Add-On</em></p>\n<p><strong>6. Is there a special SAP approval for SD Revenue Recognition required?</strong></p>\n<p>The SD Revenue Recognition solution still stays as not generally available. The customers who want to activate SD Revenue Recognition solution still have to undergo an assessment which then includes only the validation and release of the solution's customizing and processes. For more information, see SAP Note <a href=\"/notes/820417\" target=\"_blank\">820417</a>.</p>\n<p><strong>7. Can an SD sales document combine the items relevant for SD Revenue Recognition with the items relevant for SAP RAR?</strong></p>\n<p>The current logic allows to combine the line items relevant for SD Revenue Recognition with the line items relevant for SAP RAR within the same sales document. Especially during data migration such a scenario needs to be supported.</p>\n<p><strong>8. Is SD Revenue Recognition functionality supported in SAP S/4HANA?</strong></p>\n<p>No, the SD Revenue Recognition is not supported in SAP S/4HANA. None of the SD RevRec functionality will work in SAP S/4HANA. The SD RevRec functionality is replaced via SAP RAR. See SAP Note <a href=\"/notes/2225170\" target=\"_blank\">2225170</a> and <a href=\"/notes/2267342\" target=\"_blank\">2267342</a>.</p>\n<p><strong>9. If SD Revenue Recognition data migration to SAP RAR is necessary, where can I read more about it?</strong></p>\n<p>More information about data migration from SD to SAP RAR you can find in the migration chapter of the online help:<strong> <a class=\"external-link\" href=\"https://help.sap.com/viewer/de5c3a7ada6e41d482ce9ecdbd93aba1/1.3.3/en-US/356163540a7b8f4ce10000000a4450e5.html\" target=\"_blank\">RevAcc Help</a> </strong>and in the administrator guide for product \"SAP SD Integration with SAP Revenue Accounting and Reporting\": <strong><a class=\"external-link\" href=\"https://help.sap.com/doc/5019e289c5a0486c87c1cebf3c872331/1.3%20FP04/en-US/loio79dde25341d61e4ee10000000a423f68.pdf\" target=\"_blank\">Admin Guide Integration SD</a></strong>. See also SAP Note <a href=\"/notes/2569950\" target=\"_blank\">2569950 - FAQ: Migration &amp; Operational Load</a>.</p>", "noteVersion": 14}, {"note": "2580924", "noteTitle": "2580924 - RAR GoLive preparation - additional checks", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>Preparation for GoLive of the component SAP Revenue Accounting and Reporting</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>This note provides some useful hints setting live the RAR solution as a whole as well as for GoLive of additional groups of contracts, e.g. additional company codes/market units.<br/>Preparing the GoLive, please follow the steps described in detail in the chapter <em>Migration</em> and <em>Transition</em> of the SAP Library in the application help for SAP Revenue Accounting and Reporting.</p>\n<h3 data-toc-skip=\"\">Further recommendations</h3>\n<p>It is important to verify the end-to-end processing of data within RAR. The process starts from the sender component (SD, CRM Service, Hybris Billing or 3rd party sender components), which create or changes revenue accounting items (Order, Fulfilment, Invoice) and should cover the regular period end processes such as programs Transfer Revenue, Calculate Contract Liabilities and Assets and Post Revenues. For the testing create events, such as create sales orders are important. Even more important are the various change events that may happen, e.g. order change or cancellation processes.</p>\n<p>The focus of the testing activities should also contain:</p>\n<p>-        Inflight Checks – see <a href=\"/notes/2533254\" target=\"_blank\">SAP Note 2533254</a><br/>Inflight checks should be active during the testing of the RAR solution to get an early alert about data inconsistencies. <br/>The relevant SAP Notes can be found searching for the term FARR_INFLIGHT_CHECK for the corresponding software components (e.g. REVREC/130/SAPK-13003INREVREC).<br/>Checks could be limited to dedicated users within the BADI FARR_BADI_EXTENDED_CHECK– see more details in the <a href=\"/notes/2476987\" target=\"_blank\">SAP Note 2476987</a>.<br/>On the production system the Inflight Checks must be active.</p>\n<p>-        Data Validation report – see <a href=\"/notes/2463880\" target=\"_blank\">SAP Note 2463880</a><br/>The Data Validation report will help to identify data inconsistencies within RAR for data which has already been created. During the testing phase and the preparation for the GoLive phase, the data validation check should be performed regularly – see  the Chapter Operation Information / Data Validation in the SAP RAR Administrator’s Guide.<br/>The relevant SAP Notes can be found searching for the term FARR_CONTR_CHECK for the corresponding software components (e.g. REVREC/130/SAPK-13003INREVREC). Please, check for new notes related to the data validation regularly.</p>\n<p>Note:</p>\n<p>The Data Validation report would identify inconsistent data. Validation of data on systems with uncertain data quality (e.g. sandbox) might result in the identification of a high number of findings with no relevance for the implementation project. Therefore, the Data Validation report provides most significant results if executed on near-production systems like a copy of the production system or as part of an integration test on a copy of the production systen.</p>\n<p>The results of the testing should in addition provide detailed information regarding the exact timing for the cut-over procedure.</p>", "noteVersion": 1}, {"note": "2624932", "noteTitle": "2624932 - Consulting: Handling of bill of material scenarios in the SD integration component", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are working with revenue accounting relevant sales documents that contain subordinate items for example through bill of material (BOM) explosion. The handling of internal costs does not work as you expect, or you receive errors during RAI processing in the revenue accounting engine.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>SD Revenue Accounting, SDOI, SD01, BOM, bill of material, UEPOS, VBAP, cost, cost recognition, internal price</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>There are various bill of material scenarios available in the Sales standard, however not all these process variations are supported by SAP RAR.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>This SAP Note explains the requirements and restrictions in place for using BOMs and in general subordinate sales document items in revenue accounting.</p>\n<p><strong>Example sales document structure:</strong></p>\n<p>Item 1<br/>└─Item 11<br/>└─Item 12<br/>     └─Item 121<br/>     └─Item 122</p>\n<p><strong>Restrictions for billing relevance:</strong></p>\n<ul>\n<li>Only one order item on a path from lowest level to root can be billing relevant</li>\n<ul>\n<li>Example 1: If Item 1 is relevant for billing, no other item can be billing relevant</li>\n<li>Example 2: If Item 12 is relevant for billing, Item 1, Item 121 and Item 122 cannot be billing relevant, however Item 11 can be billing relevant<br/><br/></li>\n</ul>\n<li>Periodic billing plans are <strong>NOT supported </strong>on lower level items</li>\n</ul>\n<p><strong><span>Restrictions depending on delivery relevance:</span></strong></p>\n<ul>\n<li>If the BOM header is billing and delivery relevant the subordinate order items do not create performance obligations even if they are marked as relevant for RAR</li>\n<ul>\n<li>Example 1: If Item 12 is billing and delivery relevant performance obligations are <strong>NOT created</strong> for Item 121 and Item 122<br/><br/></li>\n</ul>\n<li>If the BOM is distinct the fulfillment needs to be on lower level</li>\n<ul>\n<li>Example 1: A distinct BOM fulfillment of Item 1 is <strong>NOT supported</strong></li>\n</ul>\n</ul>\n<p><strong><span>Restrictions for conditions:</span></strong></p>\n<ul>\n<li>Cost conditions (internal price \"G\") are <strong>NOT passed</strong> for BOM headers, even if the order item is delivery relevant and has a cost condition assigned<br/><br/></li>\n<li>Cost conditions (internal price \"G\") are <strong>NOT</strong> meant to be added for BOM headers via BADI FARRIC_BADI_ORDER method INCLUDE_CONDITIONS. The corresponding RAIs will be generated but will not be processable (unless the BOM hierarchy is dissolved, see last chapter of the Note)</li>\n</ul>\n<ul>\n<li>Cumulated cost (internal price \"G\") on BOM header are <strong>NOT supported</strong></li>\n<ul>\n<li>Example 1: If Item 1 is not delivery relevant but has a cost condition for the cumulated cost of Item 11 and Item 12 this cost condition is not passed to RAR (see previous point)</li>\n<li>Example 2: It is expected that Item 11 and Item 12 are relevant for RAR, have a cost condition and statistical item category. For a statistical item the cost conditions will be <strong>inactive </strong>but will be passed to RAR regardless (only for BOM lower level items)<strong><br/></strong></li>\n<li>Example 3: If there is more than one inactive cost condition the BADI FARRIC_BADI_ORDER method EXCLUDE_CONDITIONS must be used to ensure only one cost condition is passed to RAR<br/><br/></li>\n</ul>\n<li>Inactive standalone selling price conditions (SSP) are not passed automatically</li>\n<ul>\n<li>Example 1: Typically in this scenario Item 11 and Item 12 have a statistical item category and the standalone selling price will be inactive. BADI FARRIC_BADI_ORDER method INCLUDE_CONDITIONS can be used to pass the standalone selling price condition</li>\n</ul>\n</ul>\n<p><strong>Work around for BOM´s not relevant for RAR:</strong></p>\n<ul>\n<li>If a BOM is not relevant for RAR and the BOM header is not needed in RAR, it can be marked as \"not relevant for RAR\" and the BOM structure is automatically removed<br/><br/></li>\n<li>If the BOM header is needed in RAR the BOM structure can be removed in BADI FARR_BADI_RAI0 method ENRICH. In this case the VPRS condition can be included for the item in BADI FARRIC_BADI_ORDER method INCLUDE_CONDITIONS because it is technically no longer a BOM header, so the restriction does not apply either (SAP Note <strong>2696156</strong> is required for this workaround)<br/><br/></li>\n<li>The following fields need to be cleared to remove the hierarchy: HIERARCHY_ROOT, HILDOC_COMP, HILDOC_LOGSYS, HILDOC_TYPE, HILDOC_ID<br/><br/></li>\n<li>If the BOM is \"only\" used to bundle items sold together and has billing on multiple levels the hierarchy must be removed in the BADI because RAR cannot handle billing on multiple levels</li>\n</ul>", "noteVersion": 3}, {"note": "2533254", "noteTitle": "2533254 - SAP Revenue Accounting and Reporting: Inflight Checks", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>This note gives an overview of the current existing <em>Inflight Checks</em> which can be used by customers to preserve data integrity during business activities. With <em>Inflight Checks</em>, data inconsistencies are detected on the spot – before they are written to the database.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>FARR, Revenue Accounting, INFLIGHT CHECK, EXTENDED CHECK</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>Until now, no comprehensive documentation regarding <em>Inflight Checks</em> was available to customers. This note aims to provide customers who have implemented <em>Inflight Checks</em> (SAP Notes 2476987 and 2485621) with the necessary information to use and understand the <em>Inflight Check</em> functionality.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>The PDF attached contains the comprehensive <em>Inflight Check</em> documentation.</p>", "noteVersion": 5}]}, {"note": "2777486", "noteTitle": "2777486 - S4/HANA Simplification Item Check (SIC) for SD-Revenue Recognition", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>This note introduces an SD-Revenue Recognition data check for the SAP S4/HANA Simplification Item Check (SIC) framework.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>S4TC,  S4TWL, FARRIC_OL. FARRIC_OL_EXPERT, SAP RAR, SD RevRec, S4HANA, S/4</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>The ERP SD Revenue Recognition (see SAP Help: <a href=\"https://help.sap.com/viewer/a2d139d094f04ad6812f613fa64640d4/6.18.latest/en-US/2b6fb6535fe6b74ce10000000a174cb4.html\" target=\"_blank\">Link</a>) is not available within SAP S/4HANA and the new SAP Revenue Accounting and Reporting functionality has been used instead. To migrate SD Sales orders and their subsequent documents to the Revenue Accounting solution the operational load process has been <a href=\"https://help.sap.com/viewer/de5c3a7ada6e41d482ce9ecdbd93aba1/1.3%20FP05/en-US/4e411857b5cd5c38e10000000a44147b.html\" target=\"_blank\">introduced </a></p>\n<p>The need for a migration to the Revenue Accounting solution was described previously in note 2267342 (S4TWL - ERP SD Revenue Recognition) and a pre-check with a warning if SD Revenue Recognition data exists and is documented in Simplification Item SI5: SD_RR (RP SD Revenue Recognition).</p>\n<p>This note now introduces a more detailed check with error messages if no operational load process has been started or it is incomplete.</p>\n<p>One reason for these new checks is that the FARRIC_OL transaction has been disabled in S4CORE for order items that are relevant for SD Revenue Recognition by note 2677479. For these items an operational load is not possible after migration from an ERP 6.0 system.</p>\n<p>This means that it must be assured that the migration from SD Revenue Recognition to Revenue Accounting with FARRIC_OL is executed before a system conversion to SAP S/4HANA or the open SD Revenue Recognition items have to be closed. The creation of follow-on documents with reference to closed SD Revenue Recognition processes is strictly forbidden in SAP S/4HANA systems.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>So far only a pre-check for a migration was implemented (see note 2227824) that did not prevent a migration in case the operational load process had not been executed. <br/>This note introduces an additional S4/HANA Simplification Item Check (SIC) for SD-Revenue Recognition with class CLS4SIC_SD_BIL_RA.</p>\n<p>This Simplification Item Check (SIC) for SD-Revenue Recognition will trigger an error message if no migration to Revenue Accounting has been started or the transition is not complete. These error messages can be exempted if there are technical or business reasons why an SD Revenue Recognition item can't be completed for a specific sales order. This should only be done if no other data exists that is still relevant for migration.</p>\n<p><strong>The complete migration guide for SD Revenue Recognition processes has been released for Customers in SAP Note 2733866.</strong></p>\n<p>Depending on the  SD-Revenue Recognition data the following messages are possible:</p>\n<ol>\n<li><strong>Check Sub ID: SAP_SD_BIL_RA_CHECK_REVFIX (Skippable Error)</strong><br/><br/>Error Message: Migration from SD Revenue Recognition to Revenue Accounting required. See SAP note 2777486 <br/><br/>No SD Revenue Recognition Lines (VBREVE) with REVFIX = M have been found. Because the flag VBREVE-REVFIX (Fixed Revenue Line Indicator) is set to \"M\" during the migration process with FARRIC_OL this means that no data has been migrated.<br/><br/></li>\n<li><strong>Check Sub ID: SAP_SD_BIL_RA_CHECK_VBKD (Skippable Error)</strong><br/><br/>Error Message: Migration from SD Revenue Recognition to Revenue Accounting not complete. See SAP note 2777486<br/><br/>For an order item table entries with VBKD-RRREL = \"A\" or VBKD-RRREL = \"B\" were found that have revenue recognition status VBUP-RRSTA = \"A\" (Open) or revenue recognition status VBUP-RRSTA = \"B\" (Partially Completed). Since the field VBKD-RRREL is getting cleared during the migration process with FARRIC_OL this is an order item that has not been migrated.<br/><br/>If detailed error messages are requested the first sales order that was found will be displayed.</li>\n<li><strong>Check Sub ID: SAP_SD_BIL_RA_CHECK_VBRP (Skippable Error)</strong><br/><br/>Error Message: Migration from SD Revenue Recognition to Revenue Accounting not complete. See SAP note 2777486<br/><br/>For an invoice item that has revenue recognition status VBREVK-RRSTA = \"A\" (Open) or revenue recognition status VBREVK-RRSTA = \"B\" (Partially Completed) table entry with VBRP-RRREL = \"D\" has been found. Since the field VBRP-RRREL is getting cleared during the migration process with FARRIC_OL this is an invoice item that has not been migrated.<br/><br/>If detailed error messages are requested the first invoice that was found will be displayed.</li>\n<li><strong>Check Sub ID:  SAP_SD_BIL_RA_CHECK (Success Message)</strong><br/><br/>Message: Migration from SD Revenue Recognition to Revenue Accounting is complete.<br/><br/>All relevant SD Revenue Recognition data has been migrated.</li>\n</ol>\n<p>An error message that is skippable can be disabled by creating a new entry in the parameter table <strong>S4SIC_PARAM </strong>with the value <strong>SKIP </strong>for the Check Sub ID.</p>", "noteVersion": 5}], "activities": [{"Activity": "Business Decision", "Phase": "Before conversion project", "Condition": "Mandatory", "Additional_Information": "Decide if SAP Revenue Accounting and Reporting (RAR) shall be implemented as successor."}, {"Activity": "Implementation project required", "Phase": "Before conversion project", "Condition": "Mandatory", "Additional_Information": "Implement SAP RAR as successor solution for SAP Revenue Accounting."}, {"Activity": "Custom Code Adaption", "Phase": "Before or during conversion project", "Condition": "Mandatory", "Additional_Information": "Adapt custom code to the new SAP RAR solution and remove any obsolete objects only used in SAP Revenue Recognition."}, {"Activity": "Business Operations", "Phase": "During conversion project", "Condition": "Mandatory", "Additional_Information": "Run SAP S/4HANA pre-checks in SAP ERP to make sure that really all migration relevant SAP Revenue Recognition documents have been migrated to SAP RAR."}, {"Activity": "User Training", "Phase": "Before conversion project", "Condition": "Mandatory", "Additional_Information": "Provide training for new SAP RAR solution."}, {"Activity": "Software Upgrade / Maintenance", "Phase": "Before conversion project", "Condition": "Conditional", "Additional_Information": "If ERP release is less than EHP 5 for SAP ERP 6.0, upgrade to higher SAP ERP version as prerequisite for SAP RAR 1.3"}, {"Activity": "Implementation project required", "Phase": "Before conversion project", "Condition": "Mandatory", "Additional_Information": "Implement SAP RAR as successor solution for SAP Revenue Recognition. Refer to SAP note 2733866 providing a migration guide for the move from SAP Revenue Recognition to SAP RAR."}]}