{"guid": "6CAE8B3E8C3B1ED792D9EB9E8D0DA0C6", "sitemId": "BW151: SAP Source Systems", "sitemTitle": "BW4SL - SAP Source Systems", "note": 2473145, "noteTitle": "2473145 - BW4SL & BWbridgeSL - SAP and BW Source Systems", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You use the SAP or BW Source Systems based on Service API (S-API) and want to migrate to SAP BW/4HANA or SAP Datasphere, SAP BW bridge.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>LSYS, RSDS</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>Run program RS_B4HANA_RC to determine which objects are available in or can be converted to SAP BW/4HANA or SAP Datasphere, SAP BW bridge.</p>\n<p>See node: Manual Redesign --&gt; Replace by HANA or ODP Source System</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>SAP BW/4HANA and SAP Datasphere, SAP BW bridge do not connect to other SAP systems using the SAP Source System as this are no longer available. If you want to transfer data from Extractors / DataSources (Service API) of an SAP system to SAP BW/4HANA or SAP Datasphere, SAP BW bridge, then you can configure a ODP Source System with the \"SAP (Extractors) Context\". Note: Check for ODP compatibility of DataSources in SAP Note <a href=\"/notes/2232584\" target=\"_blank\">2232584</a> - Release of SAP extractors for operational data provisioning (ODP). Alternatively, you can transfer data from SAP source systems using other ODP contexts, like \"BW\" (also applies to SAP BW/4HANA source systems), \"SLT Queue\" (for real-time replication), or \"ABAP CDS Views\" (for SAP S/4HANA source systems).</p>\n<p>For further information please also refer to SAP Note <a href=\"/notes/2481315\" target=\"_blank\">2481315</a> - Operational Data Provisioning (ODP): Extracting from SAP Systems to SAP BW or SAP BW/4HANA – Availability and Limitations and the <a href=\"https://blogs.sap.com/2017/07/20/operational-data-provisioning-odp-faq/\" target=\"_blank\">Operational Data Provisioning (ODP) FAQ</a>.</p>\n<p>The Transfer Tool will automatically copy your DataSources which load from as SAPI or BW source system to a corresponding ODP source system of context SAPI or BW, respectively. It will also seamlessly clone the delta queues, so that no delta data is lost or loaded double, and no downtime in the source systems is required. This however requires the implementation of a few notes in the corresponding source system, as given by the BW/4HANA Transfer Tool Note Analyzer.</p>\n<p>If you cannot install these notes in your source system for organizational reasons, then you will have to stop any operations in the source system which push data into the Delta Queue (Push extractors), and perform an Init Simulation in the BW after the DataSource has been copied.</p>\n<p>If you cannot install the notes in your source system due to technical reasons (system is too old), then you will have to upgrade your source system or search for an individual solution.</p>\n<p>For the in-place conversion to SAP BW/4HANA any SAP Source Systems left, when executing the switch to \"Ready for conversion mode\", will be automatically deleted.</p>\n<p><span>Related Information</span></p>\n<ul>\n<li><a href=\"https://help.sap.com/viewer/107a6e8a38b74ede94c833ca3b7b6f51/1.0.3/en-US/46b9297716c94e509fcbe62d3c795e85.html\" target=\"_blank\">Creating an ODP Source System</a></li>\n<li><a href=\"/notes/2464541/E\" target=\"_blank\">SAP Note 2464541 - BW4SL - Data Transfer Processes</a></li>\n</ul>", "noteVersion": 13, "refer_note": [{"note": "2470315", "noteTitle": "2470315 - BW4SL & BWbridgeSL - Generic and Export DataSources", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You use Service API for DataSources (S-API) and want to migrate to SAP BW/4HANA or SAP Datasphere, SAP BW bridge.</p>\n<p>Following types of DataSources are not available:</p>\n<ul>\n<li>Generic DataSources (maintained in transaction RSO2)</li>\n<li>Export DataSources (8*, generated from transaction RSA1)</li>\n<li>Technical Content DataSources (0TCT*, activated via transaction RSA1)</li>\n</ul>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>OSOA, OSOD</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>Run program RS_B4HANA_RC to determine which objects are available or can be converted to SAP BW/4HANA or SAP Datasphere, SAP BW bridge.</p>\n<p>See node: Manual Redesign --&gt; Check Service API target systems</p>\n<p>See node: Automated Cleanup --&gt; Deletion of SAP Delivered Technical Content</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><span><strong>Target SAP BW/4HANA</strong>:</span></p>\n<p><span>Generic Data Sources</span><span><br/></span></p>\n<ul>\n<ul>\n<li>To support generic extraction from tables or views, we recommend creating corresponding ABAP CDS views and using source system type ODP with ABAP-CDS context in the target system. During the conversion it is possible to convert generic datasources from the myself connection to ODP ABAP-CDS or ODP-SAPI.</li>\n</ul>\n</ul>\n<p><span>Export Data Sources</span><span><br/></span></p>\n<ul>\n<ul>\n<li><span>If you want to load data from a SAP BW/4HANA system into another SAP BW/4HANA or SAP BW system, you need to choose source system type ODP with BW context in the target system.</span></li>\n<li>Extraction via 3.x Export DataSources (prefixed with 8) into the same BW: Such scenarios cannot be transferred with the transfer tool. These scenarios are deprecated already from BW 7.00 and should be replaced by DTP loads from InfoProvider to InfoProvider. From BW 7.30 transaction RSMIGRATE helps with migrating such scenarios to DTP loads.</li>\n</ul>\n</ul>\n<p><span>Technical Content DataSources</span><span> </span></p>\n<ul>\n<ul>\n<li>All relevant technical content DataSources have been replaced with ABAP CDS views (namespace RV*) in SAP BW/4HANA. These CDS views can be used for real-time monitoring or extraction of system statistics.</li>\n</ul>\n</ul>\n<p><span>Related Information</span></p>\n<ul>\n<li><a href=\"https://help.sap.com/viewer/08ad0e9c40b8480e9e0e0e35d0681530/7.5.7/en-US/45ddff380bd65591e10000000a1553f7.html\" target=\"_blank\">Export DataSource --&gt; Data Transfer Process</a></li>\n<li><a href=\"https://help.sap.com/viewer/107a6e8a38b74ede94c833ca3b7b6f51/1.0.4/en-US/af11a5cb6d2e4d4f90d344f58fa0fb1d.html\" target=\"_blank\">Transferring Data from SAP Systems via ODP (ABAP CDS Views)</a></li>\n<li><a href=\"https://help.sap.com/viewer/107a6e8a38b74ede94c833ca3b7b6f51/1.0.3/en-US/ef3341bc65c747fba1509fdfb250b452.html\" target=\"_blank\">Transferring Data from SAP BW or SAP BW∕4HANA Systems Using ODP (InfoProviders, Export DataSources)</a></li>\n<li><a href=\"https://help.sap.com/viewer/107a6e8a38b74ede94c833ca3b7b6f51/1.0.4/en-US/1e596b288f494f5d815c86cf94c3fbbb.html\" target=\"_blank\">Statistical Analyses of the Data Warehouse Infrastructure</a></li>\n</ul>\n<p><span><strong>Target SAP Datasphere, SAP BW Bridge:</strong></span></p>\n<p><span><span>Generic Data Sources</span></span></p>\n<ul>\n<ul>\n<li><span>To support generic extraction from tables or views, we recommend creating corresponding ABAP CDS views and using source system type ODP with ABAP-CDS context in the target system. During the conversion it is possible to convert generic datasources from the myself connection to ODP ABAP-CDS or ODP-SAPI.</span></li>\n</ul>\n</ul>\n<p><span><span>Export Data Sources</span></span></p>\n<ul>\n<ul>\n<li><span>To load data from Datasphere, SAP BW Bridge to another SAP BW/4HANA or SAP BW system you need to expose the data to Datasphere and process further from there.</span></li>\n<li>Extraction via 3.x Export DataSources (prefixed with 8) into the same BW: Such scenarios cannot be transferred with the transfer tool. These scenarios are deprecated already from BW 7.00 and should be replaced by DTP loads from InfoProvider to InfoProvider. From BW 7.30 transaction RSMIGRATE helps with migrating such scenarios to DTP loads.</li>\n</ul>\n</ul>\n<p><span><span>Technical Content DataSources</span></span></p>\n<ul>\n<ul>\n<li><span>All technical content DataSources are not available and also have not been replaced with other artifacts in SAP Datasphere, SAP BW bridge.</span></li>\n</ul>\n</ul>\n<p><span>Related Information</span></p>\n<ul>\n<li><a href=\"https://help.sap.com/viewer/08ad0e9c40b8480e9e0e0e35d0681530/7.5.7/en-US/45ddff380bd65591e10000000a1553f7.html\" target=\"_blank\">Export DataSource --&gt; Data Transfer Process</a></li>\n<li><a href=\"https://help.sap.com/viewer/107a6e8a38b74ede94c833ca3b7b6f51/1.0.4/en-US/af11a5cb6d2e4d4f90d344f58fa0fb1d.html\" target=\"_blank\">Transferring Data from SAP Systems via ODP (ABAP CDS Views)</a></li>\n<li><a href=\"https://help.sap.com/viewer/107a6e8a38b74ede94c833ca3b7b6f51/1.0.3/en-US/ef3341bc65c747fba1509fdfb250b452.html\" target=\"_blank\">Transferring Data from SAP BW or SAP BW∕4HANA Systems Using ODP (InfoProviders, Export DataSources)</a></li>\n</ul>", "noteVersion": 8}, {"note": "2479674", "noteTitle": "2479674 - BW4SL & BWbridgeSL - Myself Source System", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You use the \"Myself\" Source System and want to migrate to SAP BW/4HANA or SAP Datasphere, SAP BW bridge.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>LSYS, RSDS</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>Run program RS_B4HANA_RC to determine which objects are available in or can be converted to SAP BW/4HANA or SAP Datasphere, SAP BW bridge.</p>\n<p>See node: Automated Cleanup --&gt; Deletions</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><span><strong>Target SAP BW/4HANA:</strong></span></p>\n<p>The \"Myself\" Source System serves two data load scenarios and will not be available with SAP BW/4HANA:</p>\n<ol><ol>\n<li>Extraction via 3.x Export DataSources (prefixed with 8) into the same BW: Such scenarios cannot be transferred to SAP BW/4HANA with the transfer tool. These scenarios are deprecated already from BW 7.00 and should be replaced by DTP loads from InfoProvider to InfoProvider. From BW 7.30 transaction RSMIGRATE helps with migrating such scenarios to DTP loads.</li>\n<li>Extraction from customer-owned (prefixed with Z) or other application DataSources into the same BW: These scenarios need to be replaced by ODP extraction. Since SAP BW/4HANA does not allow to create load scenarios with the SAPI-Context, it is recommended to transfer them to the ODP-CDS. However for convenience transfer to ODP-SAPI is supported as well. Still, no new such ODP-SAPI DataSources can be created in BW/4 but ODP-CDS must be used. The transfer of the BW-DataSources (RSDS) requires that the corresponding SAPI-DataSources (OSOA) in the source system (which in myself case is the BW resp. BW/4 itself) are already available. These you have to manually transport from the sender to the receiver system prior to the transfer. Report RS_B4HANA_CREATE_OSOA_TRANSP is available to simplify the collection of the required objects on that transport.</li>\n</ol></ol>\n<p>When executing the switch to \"Ready for conversion mode\" the Myself-Source System will be automatically deleted, provided that there are no more DataSources existing for it. Else the deletion gives error and subsequently the conversion cannot be executed. This means that all Myself-DataSources must either be deleted or transferred like explained above.</p>\n<p><span>Related Information</span></p>\n<ul>\n<li><a href=\"/notes/2473145\" target=\"_blank\">2473145</a> - BW4SL &amp; BWbridgeSL - BW &amp; SAP Source Systems</li>\n<li><a href=\"/notes/2483299\" target=\"_blank\">2483299</a> - BW4SL &amp; BWbridgeSL - Export DataSources (SAP BW/4HANA as Source)</li>\n<li><a href=\"/notes/2443863\" target=\"_blank\">2443863</a> - BW4SL &amp; BWbridgeSL - S-API DataSources (SAP BW/4HANA as Source)</li>\n<li><a href=\"https://help.sap.com/viewer/107a6e8a38b74ede94c833ca3b7b6f51/1.0.3/en-US/46b9297716c94e509fcbe62d3c795e85.html\" target=\"_blank\">Creating an ODP Source System</a></li>\n</ul>\n<p><span><strong>Target Datasphere, SAP BW bridge:</strong></span></p>\n<p>The \"Myself\" Source System serves two data load scenarios and will not be available with SAP Datasphere, SAP BW bridge:</p>\n<ol><ol>\n<li>Extraction via 3.x Export DataSources (prefixed with 8) into the same BW: Such scenarios cannot be transferred to SAP Datasphere, SAP BW bridge with the transfer tool. These scenarios are deprecated already from BW 7.00 and should be replaced by DTP loads from InfoProvider to InfoProvider. From BW 7.30 transaction RSMIGRATE helps with migrating such scenarios to DTP loads.</li>\n<li>Extraction from customer-owned (prefixed with Z) or other application DataSources into the same BW: These scenarios are not supported anymore with SAP Datasphere, SAP BW bridge.</li>\n</ol></ol>\n<p><span>Related Information</span></p>\n<ul>\n<li><a href=\"/notes/2473145\" target=\"_blank\">2473145</a> - BW4SL &amp; BWbridgeSL - BW &amp; SAP Source Systems</li>\n<li><a href=\"/notes/2483299\" target=\"_blank\">2483299</a> - BW4SL &amp; BWbridgeSL - Export DataSources (SAP BW/4HANA as Source)</li>\n<li><a href=\"/notes/2443863\" target=\"_blank\">2443863</a> - BW4SL &amp; BWbridgeSL - S-API DataSources (SAP BW/4HANA as Source)</li>\n</ul>", "noteVersion": 8, "refer_note": [{"note": "2473145", "noteTitle": "2473145 - BW4SL & BWbridgeSL - SAP and BW Source Systems", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You use the SAP or BW Source Systems based on Service API (S-API) and want to migrate to SAP BW/4HANA or SAP Datasphere, SAP BW bridge.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>LSYS, RSDS</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>Run program RS_B4HANA_RC to determine which objects are available in or can be converted to SAP BW/4HANA or SAP Datasphere, SAP BW bridge.</p>\n<p>See node: Manual Redesign --&gt; Replace by HANA or ODP Source System</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>SAP BW/4HANA and SAP Datasphere, SAP BW bridge do not connect to other SAP systems using the SAP Source System as this are no longer available. If you want to transfer data from Extractors / DataSources (Service API) of an SAP system to SAP BW/4HANA or SAP Datasphere, SAP BW bridge, then you can configure a ODP Source System with the \"SAP (Extractors) Context\". Note: Check for ODP compatibility of DataSources in SAP Note <a href=\"/notes/2232584\" target=\"_blank\">2232584</a> - Release of SAP extractors for operational data provisioning (ODP). Alternatively, you can transfer data from SAP source systems using other ODP contexts, like \"BW\" (also applies to SAP BW/4HANA source systems), \"SLT Queue\" (for real-time replication), or \"ABAP CDS Views\" (for SAP S/4HANA source systems).</p>\n<p>For further information please also refer to SAP Note <a href=\"/notes/2481315\" target=\"_blank\">2481315</a> - Operational Data Provisioning (ODP): Extracting from SAP Systems to SAP BW or SAP BW/4HANA – Availability and Limitations and the <a href=\"https://blogs.sap.com/2017/07/20/operational-data-provisioning-odp-faq/\" target=\"_blank\">Operational Data Provisioning (ODP) FAQ</a>.</p>\n<p>The Transfer Tool will automatically copy your DataSources which load from as SAPI or BW source system to a corresponding ODP source system of context SAPI or BW, respectively. It will also seamlessly clone the delta queues, so that no delta data is lost or loaded double, and no downtime in the source systems is required. This however requires the implementation of a few notes in the corresponding source system, as given by the BW/4HANA Transfer Tool Note Analyzer.</p>\n<p>If you cannot install these notes in your source system for organizational reasons, then you will have to stop any operations in the source system which push data into the Delta Queue (Push extractors), and perform an Init Simulation in the BW after the DataSource has been copied.</p>\n<p>If you cannot install the notes in your source system due to technical reasons (system is too old), then you will have to upgrade your source system or search for an individual solution.</p>\n<p>For the in-place conversion to SAP BW/4HANA any SAP Source Systems left, when executing the switch to \"Ready for conversion mode\", will be automatically deleted.</p>\n<p><span>Related Information</span></p>\n<ul>\n<li><a href=\"https://help.sap.com/viewer/107a6e8a38b74ede94c833ca3b7b6f51/1.0.3/en-US/46b9297716c94e509fcbe62d3c795e85.html\" target=\"_blank\">Creating an ODP Source System</a></li>\n<li><a href=\"/notes/2464541/E\" target=\"_blank\">SAP Note 2464541 - BW4SL - Data Transfer Processes</a></li>\n</ul>", "noteVersion": 13}]}, {"note": "2483299", "noteTitle": "2483299 - BW4SL & BWbridgeSL - Export DataSources (SAP BW/4HANA or SAP Datasphere, BW bridge as Source)", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You consume BW Export DataSources from another SAP BW System and you want to convert the SAP BW Source System to SAP BW/4HANA or SAP Datasphere, BW bridge.</p>\n<p>Following constraints apply on the BW source system when it gets converted to SAP BW/4HANA or SAP Datasphere, BW bridge and might have an impact on consumers.</p>\n<ul>\n<li>The creation of Export DataSources (8*) within SAP BW/4HANA is not supported.</li>\n<li>Data Transfer out of InfoProviders in SAP BW/4HANA (SAP BW/4HANA as source system) to other Consumers (target systems) such as BW systems or ETL Tools is only possible using ODP-BW. </li>\n<li>InfoCubes and classic DataStore Objects get converted to Advanced DataStore Objects (ADSO), the resulting ADSO will not have an Export DataSource.</li>\n<li>InfoObjects in a SAP BW/4HANA source system will get extracted to target systems using the ODP-BW context.</li>\n<li>SAP Datasphere, BW bridge cannot act as a source system for other SAP BW or SAP BW/4HANA systems.</li>\n</ul>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Data Mart</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>Run program RS_B4HANA_RC to determine which objects are supported with or can be converted to SAP BW/4HANA or SAP Datasphere, BW bridge.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><span><strong>Source system conversion to SAP BW/4HANA:</strong></span></p>\n<p>Conversion of Export DataSources in a SAP BW source system which shall be converted into SAP BW/4HANA has to happen from the target system (preferrably also the target system is converted to SAP BW/4HANA):</p>\n<ul>\n<li>For SAP BW 7.5 target systems a conversion of Export DataSources in the SAP BW source system to ODP-BW Providers in the SAP BW source system is possible with the SAP BW/4HANA conversion tools when converting associated data flows in the target system</li>\n<li>For SAP BW 7.3x and 7.4x target system changing data flows based on Export DataSources (\"BW Source System\") to data flows based on ODP-BW (\"ODP-BW Source System\") has to happen manually on project basis</li>\n<li>For SAP BW target systems &lt; SAP BW 7.3x no ODP-BW based extraction out of a SAP BW/4HANA is possible as the ODP source system type is only available with SAP BW &gt;= 7.3x. Hence, no manual or tool based conversion is possible.</li>\n<li>For non SAP BW target systems (e.g. SAP DataServices) changing data flows based on Export DataSources to data flows based on ODP-BW has to happen manually on project basis</li>\n</ul>\n<p>Please note that the opposite direction (conversion of a SAP BW target system to SAP BW/4HANA) is described in SAP Note <a href=\"/notes/2473145\" target=\"_blank\">2473145</a> - BW4SL - SAP and BW Source Systems</p>\n<p><span>Related Information</span></p>\n<ul>\n<li>SAP Note <a href=\"/notes/2481315\" target=\"_blank\">2481315</a> - Operational Data Provisioning (ODP): Extracting from SAP Systems to SAP BW or SAP BW/4HANA – Availability and Limitations</li>\n<li><a href=\"https://blogs.sap.com/2017/07/20/operational-data-provisioning-odp-faq/\" target=\"_blank\">Operational Data Provisioning (ODP) FAQ</a></li>\n</ul>\n<p><span><strong>Source system conversion to SAP Datasphere, BW bridge:</strong></span></p>\n<p>SAP Datasphere, BW bridge systems cannot act as source systems for other SAP BW or SAP BW/4HANA systems.</p>", "noteVersion": 9}, {"note": "2443863", "noteTitle": "2443863 - BW4SL - S-API DataSources (SAP BW/4HANA as Source)", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>Creation of Service API DataSource within SAP BW/4HANA is not supported. This means that no Generic DataSources can be maintained in Transaction RSO2 and no Technical Content DataSources (0TCT* DataSources) can be activated in SAP BW/4HANA. Hence, SAP BW/4HANA can't serve as source system to other SAP BW oder SAP BW/4HANA system via the Service API Interface.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>LSYS, SAPI</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>Run program RS_B4HANA_CONVERSION_CONTROL to determine which objects are supported with or can be converted to SAP BW/4HANA.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>For SAP BW/4HANA InfoProviders we recommend using the available ODP-BW provider capability that allows transferring data to other SAP BW or SAP BW/4HANA via ODP.</p>\n<p>For other tables residing on your SAP BW/4HANA system you have to use ABAP CDS Views for data transfer and then the <strong>ODP-CDS Source System </strong>in your connected target system instead. Exceptions exist only for Inplace-, Remote- or Shell-Transfer of Myself-DataSources, which will be mapped to ODP-SAPI source systems, however these transferred DataSources might run out of maintenance in releases greater than 2.00.</p>\n<p>Former Technical Content DataSource (0TCT_DS* namespace) are replaced by ABAP CDS Views that allow for statistics data extraction and monitoring. For further information on this topic see SAP Note <a href=\"/notes/2467074/E\" target=\"_blank\">2467074</a>.</p>\n<p><strong>Related Information</strong></p>\n<p>For further information please see:</p>\n<p>- SAP Note <a href=\"/notes/2481315/E\" target=\"_blank\">2481315</a> - Operational Data Provisioning (ODP): Extracting from SAP Systems to SAP BW or SAP BW/4HANA – Availability and Limitations</p>\n<p>- <a href=\"https://blogs.sap.com/2017/07/20/operational-data-provisioning-odp-faq/\" target=\"_blank\">Operational Data Provisioning (ODP) FAQ</a></p>", "noteVersion": 5}, {"note": "2421930", "noteTitle": "2421930 - Simplification List for SAP BW/4HANA", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>Simplification List for SAP BW/4HANA is published as PDF document on the SAP Help Portal (<a href=\"http://help.sap.com/bw4hana20\" target=\"_blank\">http://help.sap.com/bw4hana20</a>). This SAP Note provides the Simplification List in spreadsheet format.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>SIMPL, BW4HANA</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>Planning to transition from SAP BW to SAP BW/4HANA.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>To enable our customers to better plan and estimate their path to SAP BW/4HANA, we have created the “Simplification List for SAP BW/4HANA”. In this list we describe in detail, on a functional level, what happens to individual object types and solution capabilities when transitioning to SAP BW/4HANA. Compared to the SAP Business Warehouse products, we have in some cases merged certain functionality with other elements or reflected it within a new solution or architecture.</p>\n<p><strong>A new, web-based UI for searching and displaying Simplification Items</strong></p>\n<p>In parallel to the PDF document and the spreadsheet available via this SAP Note, the <em>SAP Simplification Item Catalog</em> offers direct search and browse of SAP BW/4HANA Simplification Items in their current state via the SAP ONE Support Launchpad at <a href=\"https://launchpad.support.sap.com/#/sic/\" target=\"_blank\">https://launchpad.support.sap.com/#/sic/</a>.</p>", "noteVersion": 6, "refer_note": [{"note": "2347382", "noteTitle": "2347382 - SAP BW/4HANA – General Information (Installation, SAP HANA, security corrections…)", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You want to run or you are already running an SAP BW/4HANA system.<br/>You want to convert your SAP BW system to SAP BW/4HANA.<br/>You want to upgrade your system from SAP BW/4HANA 1.0 to SAP BW/4HANA 2.0</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Revision, BW4, BW/4HANA, BW, NetWeaver</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p><strong>This note contains information about the following topics:</strong></p>\n<ol>\n<li>General Information about SAP BW/4HANA and the conversion to SAP BW/4HANA</li>\n<li>Recommendations with regards to SAP BW/4HANA releases and SAP HANA revisions</li>\n<li>Information relevant for SAP BW/4HANA 2023</li>\n<li>Information relevant for SAP BW/4HANA 2021</li>\n<li>Information relevant for SAP BW/4HANA 2.0</li>\n<li>Information relevant for SAP BW/4HANA 1.0</li>\n</ol>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong><span>1. General information about SAP BW/4HANA and the conversion to SAP BW/4HANA</span></strong></p>\n<p><span>High Level information about BW/4HANA: <a href=\"http://www.sap.com/bw4hana\" target=\"_blank\">http://www.sap.com/bw4hana</a></span></p>\n<p><span>See SAP Note <a href=\"/notes/2347384 \" target=\"_blank\">2347384</a> - SAP BW/4HANA 1.0 Important Notes before you start</span></p>\n<p><span>When planning your system landscape(s) for the next release of SAP BW/4HANA (not release 1.00), you may want to take the available applicaton server platforms into account. Please refer to SAP note <a href=\"/notes/2620910\" target=\"_blank\">2620910</a> </span></p>\n<p><span>SAP changed the <a href=\"https://support.sap.com/en/my-support/knowledge-base/security-notes-news.html\" target=\"_blank\">SAP security strategy</a> to cover security corrections for Support Packages delivered during the past 24 months (formerly 18 months). BW/4HANA will not extend this period. We provide security corrections for Support Packages delivered in the last 18 months as before.</span></p>\n<p><span>See note </span><a href=\"/notes/2733740\" target=\"_blank\">https://launchpad.support.sap.com/#/notes/2733740</a> for Information/Restriction about Relase BW/4HANA 2.0</p>\n<p><strong>2. Recommendations with regards to SAP BW/4HANA releases and SAP HANA revisions</strong></p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"3\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td><strong>Productively used SAP BW/4HANA release</strong></td>\n<td><strong>Recommended minimum SAP HANA SP/revision</strong></td>\n</tr>\n<tr>\n<td>1.0</td>\n<td>Revision 122.12 or higher (HANA 1.0 SPS 12)<br/>Revision 12 or higher (HANA 2.0 SPS 1 or higher)</td>\n</tr>\n<tr>\n<td>2.0</td>\n<td>Revision 64 or higher (HANA 2.0 SPS 6 or higher)</td>\n</tr>\n<tr>\n<td>2021</td>\n<td>Revision 64 or higher (HANA 2.0 SPS 6 or higher)</td>\n</tr>\n<tr>\n<td>2023</td>\n<td>Revision 71 or higher (HANA 2.0 SPS 7 or higher)</td>\n</tr>\n</tbody>\n</table></div>\n<p>Please note that SAP strongly recommends to apply the <span>latest available</span> SAP HANA revision.</p>\n<p>For details about SAP HANA Maintenance Strategy see SAP Note <em><a href=\"/notes/2021789\" target=\"_blank\">2021789</a> - SAP HANA Revision and Maintenance Strategy</em>.</p>\n<p>Please keep the update paths for Maintenance Revisions in mind. Details can be found in SAP Note <em><a href=\"/notes/1948334\" target=\"_blank\">1948334</a> - SAP HANA Database Update Paths for Maintenance Revisions</em>.</p>\n<p>Notes with regards to SAP HANA Revisions:</p>\n<ul>\n<li>HANA SPS12: SAP Note <a href=\"/notes/2298750\" target=\"_blank\">2298750</a> - SAP HANA Platform SPS 12 Release Note</li>\n</ul>\n<p><span>SAP BW/4HANA and SAP HANA 2.0: </span></p>\n<p>SAP BW/4HANA 1.00 (including SAP_BASIS 7.50) has been released for HANA 2.0. Details can be found in note 2420699.<br/>SAP BW/4HANA 2.00 (including SAP_BASIS 7.53) and newer run on HANA 2.0 only</p>\n<p><strong> </strong></p>\n<p><strong>3. </strong><strong>Information relevant for SAP BW/4HANA 2023 (planned mid of Q4/2023)</strong></p>\n<p>You can find the detailed information in SAP Note: <a href=\"3365187\" target=\"_blank\">3365187 - Information/Restriction Note for Release SAP BW/4HANA 2023</a>.</p>\n<p><strong><strong>4. Information relevant for SAP BW/4HANA 2021</strong></strong></p>\n<p>You can find the detailed information in SAP Note: <a href=\"3100794\" target=\"_blank\">3100794 - Release Information/Restrictions Note for SAP BW/4HANA 2021</a>.</p>\n<p><strong><strong><strong>5. Information relevant for SAP BW/4HANA 2.0</strong></strong></strong></p>\n<p>You can find the detailed information in SAP Note: <a href=\"https://me.sap.com/notes/2733740\" target=\"_blank\">2733740 - Release Information/Restrictions Note for SAP BW/4HANA 2.0</a></p>\n<ul>\n<li><span>General</span></li>\n<ul>\n<li>See note 2733740 - Release Information/Restriction Note for SAP BW/4HANA 2.0 for restrictions in BW/4HANA 2.0</li>\n<li>Urgent! Implement note <a href=\"/notes/2770525\" target=\"_blank\" title=\"2770525  - Data Transfer Intermediate Storage (DTIS): Missing package assignment\">2770525 - Data Transfer Intermediate Storage (DTIS): Missing package assignment</a> <span><strong>before</strong></span> upgrading to BW/4HANA 2.0</li>\n<li><strong>Before </strong>you start the setup via tasklist (stc01) SAP_BW4_SETUP_SIMPLE check if all HANA authorizations are maintained like in SAP Note <a href=\"/notes/2362807\" target=\"_blank\">2362807</a> - BW Search/Value Help/Open in SAP BW Modeling Tools does not give a result.</li>\n<li>Introduction Video: <a href=\"https://youtu.be/bxH0EzAhSwQ\" target=\"_blank\">https://youtu.be/bxH0EzAhSwQ</a></li>\n</ul>\n<li><span>Upgrading BW/4HANA 1.0 to 2.0</span></li>\n<ul>\n<li>We now introduced tasklist SAP_BW4_BEFORE_UPGRADE and SAP_BW4_AFTER_UPGRADE  to support you upgrading your system. Please see note 2846537 - Taskliste for upgrade of BW/4HANA</li>\n<li>Afte the upgrade to BW/4HANA 2.0 the flag TADIR Popup f. Obj. in View RSADMINC gets initialized. Please see note 2884312 for details.</li>\n</ul>\n<li><span>ABAP Software Stack</span></li>\n<ul>\n<li>The initial Software stack for BW/4HANA 2.0 looks like this:</li>\n<ul>\n<li>SAP_BASIS 7.53 SP01</li>\n<li>SAP_ABA 7.5D SP01</li>\n<li>SAP_UI 7.53 SP02</li>\n<li>SAP_GWFND 7.53 SP01</li>\n<li>ST-PI 7.40 SP09</li>\n<li>DW4CORE 2.00 SP00</li>\n<li>UIBAS100 4.00 SP01</li>\n</ul>\n<li>This should dramatically reduces the effort for installation</li>\n<li>During the upgrade from BW/4HANA 1.0 the missing software components are installed automatically</li>\n<li></li>\n</ul>\n<li><span>BW/4HANA 2.0 SP01</span></li>\n<ul>\n<li>Please install following note on top of SP01 directly after update:</li>\n<ul>\n<li>2779827  Error starting task list SAP_BW4_LAUNCHPAD_SETUP from other task lists</li>\n</ul>\n</ul>\n</ul>\n<ul>\n<li><span>BW/4HANA 2.0 SP02 </span></li>\n<ul>\n<li>SAP_UI 7.54 SP1 as alternative UI version is released. Please check notes <strong>2903662 </strong>and<strong> 2908858</strong>; please note that \"SAP Quartz Light\" introduced with 1.65 is not yet supported but will be supported as of FP07</li>\n<li>please also take into consideration the Support Package Upgrade Strategy when running on higher UI Version described in note <strong>2618449</strong></li>\n</ul>\n</ul>\n<ul>\n<li><span>BW/4HANA 2.0 SP04</span></li>\n<ul>\n<li>SAP_UI 7.54 &gt;= SP2 as alternative UI version is released. lease check notes <strong>2903662 </strong>and<strong> 2908858</strong>; please note that \"SAP Quartz Light\" introduced with 1.65 is not yet supported but will be supported as of FP07. Also take into consideration the Support Paackage Strategy when running on higher UI version described in note <strong>2618449</strong></li>\n<li>NSE for DTO --&gt; available since HANA 2.0 SP04 + limitation see HANA Note <a href=\"/notes/2771956\" target=\"_blank\">https://launchpad.support.sap.com/#/notes/2771956</a></li>\n<li>Open Hub with SDA --&gt; available since HANA 2.0 SP04 Revision 46</li>\n<li>XDA --&gt; available since HANA 2.0 Sp04 Revision 40 </li>\n<li>Geo Support --&gt; available since HANA 2.0 SP04 Revision 45</li>\n<li>Treespec Partitioning in ADSO --&gt; available since Hana 2.0 SP04 </li>\n</ul>\n</ul>\n<p><strong>6. Information relevant for SAP BW/4HANA 1.0</strong></p>\n<p>Installation/Setup</p>\n<ul>\n<li><span>General</span></li>\n<ul>\n<li>Please see SAP Note<strong> </strong><a href=\"/notes/2354516\" target=\"_blank\">2354516</a> to enable client copy after installation.</li>\n<li><strong>Before </strong>you start the setup via tasklist (stc01) SAP_BW4_SETUP_SIMPLE check if all HANA authorizations are maintained like in SAP Note <a href=\"/notes/2362807\" target=\"_blank\">2362807</a> - BW Serach/Value Help/Open in SAP BW Modeling Tools does not give a result.</li>\n<li>Equivalent Support Packages<br/>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"3\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td>SAP BW 7.50 (SAP_BW)</td>\n<td>BW/4HANA 1.00(DW4CORE)</td>\n<td>BPC 11 (BPC4HANA)</td>\n<td>SAP UI 7.52 </td>\n</tr>\n<tr>\n<td>               4</td>\n<td>        Initial Installation</td>\n<td>           N.A.</td>\n<td> </td>\n</tr>\n<tr>\n<td>               5</td>\n<td>                   1</td>\n<td> </td>\n<td> </td>\n</tr>\n<tr>\n<td>               6</td>\n<td>                   &gt;= 2</td>\n<td> </td>\n<td> </td>\n</tr>\n<tr>\n<td>               7</td>\n<td>                   &gt;= 4</td>\n<td>  Initial Installation</td>\n<td> </td>\n</tr>\n<tr>\n<td>               8</td>\n<td>                   &gt;= 5</td>\n<td>             &gt;= 1</td>\n<td> </td>\n</tr>\n<tr>\n<td>               9</td>\n<td>                   &gt;= 6</td>\n<td>             &gt;= 2             </td>\n<td> </td>\n</tr>\n<tr>\n<td>             10</td>\n<td>                   &gt;= 7</td>\n<td>             &gt;= 3</td>\n<td> </td>\n</tr>\n<tr>\n<td>             11*</td>\n<td>                   &gt;=8</td>\n<td>             &gt;=4</td>\n<td>      &gt;=2</td>\n</tr>\n<tr>\n<td>             12*</td>\n<td>                   &gt;=9</td>\n<td>             &gt;=5</td>\n<td>      &gt;=3</td>\n</tr>\n</tbody>\n</table></div>\n</li>\n</ul>\n</ul>\n<p><span>*) Starting with BW/4HANA 1.00 SP08 SAP_UI 7.52 is required</span></p>\n<ul>\n<li><span>BW/4HANA SP08</span></li>\n<ul>\n<li><strong>Requirements:</strong></li>\n<ul>\n<li>SAP_BASIS 7.50 SP10</li>\n<li>SAP_GWFND 7.50 SP10</li>\n<li>SAP_ABA 7.5A SP10</li>\n<li>SAP_UI <strong>7.52 SP01 -- update required</strong></li>\n</ul>\n<li><strong>For new Installs:</strong></li>\n<ul>\n<li>For your convience we created a Support Release for BW/4HANA 1.00 including the following components:</li>\n<ul>\n<li>SAP_BASIS 7.50 SP10</li>\n<li>SAP_GWFND 7.50 SP10</li>\n<li>SAP_ABA 7.5A SP 10</li>\n<li>SAP_UI 7.52 SP02</li>\n<li>DW4CORE SP08</li>\n<li>BW4CONTB SP03</li>\n</ul>\n<li>this installation is based on Software Provisioning Manager 2.0 which requires HANA 2 SP2 Revision 23 see note 2610954.</li>\n<li>Release of the installation is planned for april (CW 17)</li>\n</ul>\n<li><strong>Enhancment in Tasklist</strong></li>\n<ul>\n<li>there are some enhancements made in the tasklists with regards to the new Web UI's: Please refert to note 2351381 for details.</li>\n</ul>\n</ul>\n<li><span>BW/4HANA SP03</span></li>\n<ul>\n<li>In some cases the execution of the tasklist SAP_BW4_SETUP_SIMPLE is dumping. Please have a look at 2351381 how to disable the invalid BAPI</li>\n</ul>\n<li><span>BW/4HANA SP02</span></li>\n<ul>\n<li>In some cases the execution of the tasklist SAP_BW4_SETUP_SIMPLE is dumping. Please have a look at 2351381 how to disable the invalid BAPI</li>\n<li>Please implement the following notes on top of SP02 to avoid issues during setup/tasklisk execution</li>\n<ul>\n<li>2410466                BW Workspace customizing: RSDDTREXADMIN_TO_RSWSPCUST dumps </li>\n</ul>\n</ul>\n<li><span>BW/4HANA SP01</span></li>\n<ul>\n<li>Please implement the following notes on top of SP01 to avoid issues during setup/later SP update</li>\n<ul>\n<li>2385382               Add SAPFSPOR to avoid strange DUMPS during SP update</li>\n<li>2385265               Check if essential objects are active after installation</li>\n<li>2381312               Task list SAP_BW4_SETUP_SIMPLE: Error in \"Check/Generate Packages for BW ODATA Services\" step</li>\n</ul>\n<li>General Information about tasklists and their usage in BW/4HANA: 2351381 SAP BW/4HANA task lists</li>\n</ul>\n</ul>\n<ul>\n<li><span>BW/4HANA SP00</span></li>\n<ul>\n<li>If job BI_WRITE_PROT_TO_APPLLOG is not started by the tasklist please see SAP Note <a href=\"/notes/2356498\" target=\"_blank\">2356498</a>.</li>\n<li>In SP0 the tasklist shows an error which can be ignored. See SAP Note <a href=\"/notes/2351344\" target=\"_blank\">2351344</a> for details.</li>\n</ul>\n</ul>", "noteVersion": 36}, {"note": "2383530", "noteTitle": "2383530 - Conversion from SAP BW to SAP BW/4HANA", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>This note describes the different paths to get from SAP BW to SAP BW/4HANA.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>SAP BW/4HANA, SAP BW, Migration, Conversion, Transfer, In-Place, Remote, Shell, Lifecycle Management</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>Conversion to BW/4HANA</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><span><strong>0 Disclaimer</strong></span></p>\n<p>Converting a SAP BW system to SAP BW/4HANA is not a simple task. There is no \"wizard\" that magically converts everything. Instead, SAP provides a well-defined process to guide you through the renovation of your data warehouse. We have developed tools to automate this renovation where applicable and feasible, but they are not built or meant to fix badly designed models or clean-up neglected systems. In any conversion there is a need for manual interaction and re-design. The amount of such manual tasks varies from customer to customer and depends on the configuration and state of the SAP BW system. For example, a conversion of newer systems that have been configured using SAP HANA-optimized objects and LSA++ from the beginning will be relatively easy. In contrast to that, a conversion of older systems that are running on non-SAP HANA databases, using out-of-date interfaces, legacy data models, and multiple redundant layers can become quite challenging.</p>\n<p>It is therefore essential that you understand the differences between SAP BW and SAP BW/4HANA and how to handle them, thoroughly analyze your existing system, estimate the complexity and duration of required tasks, properly plan all conversion activities, learn and practice to use the conversion tools, test the conversion processes ideally with a \"copy of production\", and actively manage changes to your business. It speaks for itself that good project management and skilled personnel will be required to execute a timely and efficient conversion of a complete SAP BW system. If done right, your conversion to SAP BW/4HANA will be very successful.</p>\n<p>SAP offers several way to help. To get to started run a <a href=\"https://help.sap.com/viewer/p/SAP_READINESS_CHECK\" target=\"_blank\">SAP Readiness Check</a> or book a <a href=\"http://sapsupport.info/offerings/sap-value-assurance/\" target=\"_blank\">SAP Value Assurance service </a><a href=\"http://sapsupport.info/offerings/sap-value-assurance/\" target=\"_blank\">for SAP BW/4HANA</a>.</p>\n<p>We also highly recommend the Class Room Training <strong>BW4HC</strong>. (<a href=\"https://training.sap.com/course/bw4hc-system-conversion-to-sap-bw4hana-classroom-017-de-en/\" target=\"_blank\">https://training.sap.com/course/bw4hc-system-conversion-to-sap-bw4hana-classroom-017-de-en/</a>?).</p>\n<p> </p>\n<p><span><strong>1 Overview</strong></span></p>\n<p><strong>1.1 What is SAP BW/4HANA</strong></p>\n<p>SAP BW/4HANA is a new, next generation data warehouse product from SAP that, like SAP S/4HANA, is optimized for the SAP HANA platform, including inheriting the high performance, simplicity, and agility of SAP HANA. SAP BW/4HANA delivers real time, enterprise wide analytics that minimize the movement of data and can connect all the data in an organization into a single, logical view, including new data types and sources.<strong><br/></strong></p>\n<p><strong>1.2 Related Information</strong></p>\n<p>Please find more information here:</p>\n<ul>\n<li>The Future of Data Warehousing - SAP BW/4HANA  <br/><a href=\"http://sap.com/bw4hana\" target=\"_blank\">http://sap.com/bw4hana</a></li>\n<li>SAP BW/4HANA Community (includes FAQ)<br/><a href=\"https://community.sap.com/topics/bw4-hana\" target=\"_blank\">https://community.sap.com/topics/bw4-hana</a><br/><a href=\"https://www.sap.com/documents/2016/08/c4458a08-877c-0010-82c7-eda71af511fa.html\" target=\"_blank\">https://www.sap.com/documents/2016/08/c4458a08-877c-0010-82c7-eda71af511fa.html</a></li>\n<li>Online Documentation<br/><a href=\"http://help.sap.com/bw4hana10\" target=\"_blank\">http://help.sap.com/bw4hana10</a></li>\n<li>Product Roadmap<br/><a href=\"https://roadmaps.sap.com/board?range=FIRST-LAST&amp;PRODUCT=73554900100800000681#Q1%202021\" target=\"_blank\">https://roadmaps.sap.com/board?range=FIRST-LAST&amp;PRODUCT=73554900100800000681#Q1%202021</a></li>\n<li>SAP BW/4HANA vs. SAP BW<br/><a href=\"https://www.sap.com/documents/2017/08/30aa5e11-cf7c-0010-82c7-eda71af511fa.html\" target=\"_blank\">https://www.sap.com/documents/2017/08/30aa5e11-cf7c-0010-82c7-eda71af511fa.html</a><br/><br/></li>\n</ul>\n<p>For information related to functionality available in SAP BW but replaced or deleted in SAP BW/4HANA please refer to the Simplification List: <br/><a href=\"https://help.sap.com/doc/590752e646cc4b15a9092f32353b209a/1.0/en-US/SAP_BW4HANA_10_Simplification_List.pdf\" target=\"_blank\">https://help.sap.com/doc/590752e646cc4b15a9092f32353b209a/1.0/en-US/SAP_BW4HANA_10_Simplification_List.pdf</a></p>\n<p>For a holistic view of the Conversion topic, see Conversion Guide for SAP BW/4HANA 1.0: <br/><a href=\"https://help.sap.com/doc/c3f4454877614bc7b9e85ae1f9d1d2c7/1.0/en-US/SAP_BW4HANA_10_Conversion_Guide.pdf\" target=\"_blank\">https://help.sap.com/doc/c3f4454877614bc7b9e85ae1f9d1d2c7/1.0/en-US/SAP_BW4HANA_10_Conversion_Guide.pdf</a></p>\n<p>For a holistic view of the Conversion topic, see Conversion Guide for SAP BW/4HANA 2.0:</p>\n<p><a href=\"https://help.sap.com/doc/999ae5f8c578402dab1fea94fa4599f9/2.0/en-US/SAP_BW4HANA_20_Conversion_Guide.pdf\" target=\"_blank\">https://help.sap.com/doc/999ae5f8c578402dab1fea94fa4599f9/2.0/en-US/SAP_BW4HANA_20_Conversion_Guide.pdf</a></p>\n<p>For a holistic view of the Conversion topic, see Conversion Guide for SAP BW/4HANA 2021 and higher:</p>\n<p><a href=\"https://help.sap.com/doc/c8e1ac37fee64e0f887a2d6d6af32a33/2021.00/en-US/SAP_BW4HANA_2021_Conversion_Guide.pdf\" target=\"_blank\">https://help.sap.com/doc/c8e1ac37fee64e0f887a2d6d6af32a33/2021.00/en-US/SAP_BW4HANA_2021_Conversion_Guide.pdf</a></p>\n<p> </p>\n<p><span><strong>2 How to Get to BW/4HANA</strong></span></p>\n<p><strong>2.1 Paths to SAP BW/4 HANA</strong></p>\n<p><strong><em>2.1.1 New implementation or fresh start</em></strong></p>\n<p>New implementations are the best choice for customers converting from a legacy system or building a system from scratch with new data model only.</p>\n<p><strong>Steps</strong>: Install SAP BW/4HANA, selective transport of BW objects (optional), implement HANA-optimized data models and flows, followed by data load and quality checks.</p>\n<p><strong>What to use</strong>: Software Provisioning Manager (SWPM), system connection (SAP Source), SAP BW/4HANA ETL, file upload and/or SAP HANA EIM (legacy sources)</p>\n<p>See SAP First Guidance: Complete functional scope (CFS) for SAP BW/4HANA: <a href=\"https://www.sap.com/documents/2016/09/b001a9de-8a7c-0010-82c7-eda71af511fa.html\" target=\"_blank\">https://www.sap.com/documents/2016/09/b001a9de-8a7c-0010-82c7-eda71af511fa.html<br/></a></p>\n<p><em><strong>2.1.2 System conversion</strong></em></p>\n<p>A system conversion addresses customers who want to change their current SAP BW system into a SAP BW/4HANA system. Using the Transfer Cockpit provided by SAP, the logical systemname of the system can be kept (in-place conversion) or a new  logical systemname can be used (remote conversion resp. shell conversion).</p>\n<p>A conversion project typically follows this sequence:</p>\n<ul>\n<li><strong>Discover / Prepare Phase</strong>: check system for BW/4HANA compliance (gather information about objects and code that needs to be transferred or changed), estimate effort for the conversion project</li>\n<li><strong>Explore / Realization Phase</strong>: Transfer legacy objects into HANA-optimized counterparts, system conversion, post conversion tasks</li>\n</ul>\n<p><em>*******. In-Place conversion</em></p>\n<p>Systems running on SAP BW 7.50 powered by SAP HANA can be converted in-place keeping their logical systemname. In the realization phase of the conversion project, classic objects have to be transferred into their HANA optimized replacements using the Transfer Cockpit. This transfer can be performed scenario-by-scenario. When all classic objects have been replaced, the system conversion to SAP BW/4HANA can be triggered.</p>\n<p>SAP highly recommends to use the latest support package for SAP BW/4HANA when performing the conversion. See note https://launchpad.support.sap.com/#/notes/2347382 regarding the equivalent support packages. Please keep the RTC Dates of the different Support Packages in mind when you plan your conversion project.</p>\n<p><strong>Steps</strong>: Migrate and/or upgrade to SAP BW 7.50 powered by SAP HANA (if necessary), install SAP BW/4HANA Starter Add-On (see “Installation” below), install Transfer Cockpit, transfer data models, adjust custom coding, perform system conversion</p>\n<p>To include the Data Comparison Functionality into InPlace-TaskList, please refer to the SAP Note <a href=\"/notes/2607742\" target=\"_blank\">2607742</a>.</p>\n<p><strong>Incident Component</strong>: BW-B4H-CNV-IPL</p>\n<p><strong>What to use</strong>: SAP BW/4HANA Starter Add-On, SAP BW/4HANA Transfer Cockpit, SPAM/SAINT<br/><em></em></p>\n<p><em></em><em>*******. Remote conversion</em></p>\n<p>For SAP BW systems on releases from 7.30 to 7.50 running on Any-DB, a remote conversion can be performed. For that conversion type, a green field installation (new  logical systemname) of SAP BW/4HANA is used. The Transfer Cockpit is able to transport selected data models into the new installation and to perform a remote data transfer.</p>\n<p><strong>Steps</strong>: Install SAP BW/4HANA, install DMIS Add-On, transfer data models, transport custom developments (might need adjustment to work with SAP BW/4HANA)</p>\n<p><strong>Incident Component</strong>: BW-B4H-CNV-RMT</p>\n<p><strong>What to use</strong>: Software Provisioning Manager (SWPM), SAP BW/4HANA Transfer Cockpit, DMIS Add-On</p>\n<p><em>*******. Shell conversion</em></p>\n<p>For SAP BW systems on releases from 7.00 to 7.50 running on Any-DB, a Shell conversion can be performed. For that conversion type, a green field installation (new  logical systemname) of SAP BW/4HANA is used. The Transfer Cockpit is able to transport selected data models without data into the new installation.</p>\n<p>Our recommendation for shell conversion is to use a sandbox as the sender system. The latest SPs should also be installed on this system. Depending on the version of the current system, this saves a lot of time and effort. If you still decide to import the hints, you should look at the wiki <a href=\"https://wiki.scn.sap.com/wiki/pages/viewpage.action?pageId=562729718\" target=\"_blank\" title=\"https://wiki.scn.sap.com/wiki/pages/viewpage.action?pageId=562729718\">https://wiki.scn.sap.com/wiki/pages/viewpage.action?pageId=562729718</a> beforehand and follow the information from the wiki when importing.</p>\n<p><em><strong>Steps</strong>: </em>Install SAP BW/4HANA, transfer data models without data using SAP BW/4HANA Transfer Cockpit, and transport custom developments (might need adjustments to work with SAP BW/4HANA).</p>\n<p><strong>Incident Component</strong>: BW-B4H-CNV-SHL</p>\n<p><strong>What to use: </strong>Software Provisioning Manager (SWPM), SAP BW/4HANA Transfer Cockpit</p>\n<p><strong><em>2.1.3. Landscape transformation</em></strong></p>\n<p>Landscape transformation is for customers who want to consolidate and optimize their complex SAP BW landscape (multiple production systems) into a single SAP BW/4HANA system or who want to carve-out selected data models or flows into a global SAP BW/4HANA system.</p>\n<p><strong>Steps</strong>: Install SAP BW/4HANA, implement consolidated and HANA-optimized data models and flows, followed by data load and quality checks</p>\n<p><strong>What to use</strong>: Selective data transfer, SAP Landscape Transformation (SLT), SAP Data Services, SAP BW/4HANA ETL or SAP HANA EIM<br/><em></em></p>\n<p>The following sections are focusing on the system conversion.</p>\n<p> </p>\n<p><strong>2.2 Availability</strong></p>\n<p><strong><em>2.2.1 Overview</em></strong></p>\n<p>The tools required to perform an In-Place, Remote or Shell conversion are general available now.</p>\n<p><strong><em>2.2.2 Releases</em></strong></p>\n<p>SAP highly recommends to use the latest support package for a release to start a conversion project with. It will massively reduce the effort for notes implementation and we cannot guarantee that all required notes can be imported from other areas.<em> </em>Nevertheless, the Transfer Cockpit was made available via SAP Notes for the following support packages.</p>\n<p><em>******* Pre-Checks (General Available)</em></p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"3\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td><strong>Release</strong></td>\n<td><strong>Recommended Support Package</strong></td>\n</tr>\n<tr>\n<td>SAP BW 7.00</td>\n<td>SP 25 and higher</td>\n</tr>\n<tr>\n<td>SAP BW 7.01</td>\n<td>SP 09 and higher</td>\n</tr>\n<tr>\n<td>SAP BW 7.02</td>\n<td>SP 07 and higher</td>\n</tr>\n<tr>\n<td>SAP BW 7.30</td>\n<td>SP 08 and higher</td>\n</tr>\n<tr>\n<td>SAP BW 7.31</td>\n<td>SP 05 and higher</td>\n</tr>\n<tr>\n<td>SAP BW 7.40</td>\n<td>SP 09 and higher</td>\n</tr>\n<tr>\n<td>SAP BW 7.50</td>\n<td>SP 05 and higher</td>\n</tr>\n<tr>\n<td>SAP BW 7.51</td>\n<td>SP 08 and higher</td>\n</tr>\n<tr>\n<td>SAP BW 7.58</td>\n<td>SP 00 and higher</td>\n</tr>\n</tbody>\n</table></div>\n<p><em>******* In-Place conversion</em></p>\n<p>SAP BW 7.50<strong> minimal </strong>requirement SP 05 ,<strong> recommended </strong>SP 12 and higher.</p>\n<p>An in-place conversion of SAP BW <strong>7.51</strong> or higher to SAP BW/4HANA 1.0 is <strong>not</strong> possible (since SAP BW/4HANA 1.0 is based on SAP Basis <strong>7.50</strong>).</p>\n<p>Starting from Release SAP BW <strong>7.50 SP16</strong> the conversion is only to SAP BW/4HANA <strong>2.0 SP&gt;=04</strong> possible!</p>\n<p>SAP highly recommends to update the systems before starting the conversion process. Support Package Implementation is possible until B4H Mode.</p>\n<p>Other components relevant for SAP BW Systems used for InPlace Conversion</p>\n<ul>\n<li>SAP HANA 1.0 SPS 12 and higher (Relevant for Conversion to Target Release SAP BW/4HANA <strong>1.0</strong>)</li>\n<li>SAP HANA 2.0 SPS 36 and higher (Relevant for Conversion to Target Release SAP BW/4HANA <strong>2.0</strong>)</li>\n<li>SAP Business Content 7.57 SP11 and higher</li>\n<li>SPAM version 72 and higher</li>\n<li>SAP UI 7.52 or lower (Relevant for Conversion to Target Release SAP BW/4HANA <strong>1.0</strong> . SAP BW/4HANA 1.0 does <strong>not</strong> support SAP_UI 7.53. See SAP Note <a href=\"/notes/2347382\" target=\"_blank\">2347382</a>)</li>\n<li>SAP UI 7.53 (Possible only by Conversion to Target Release SAP BW/4HANA <strong>2.0</strong>. See SAP Note <a href=\"/notes/2347382\" target=\"_blank\">2347382</a>)</li>\n</ul>\n<p> </p>\n<p><em>*******. Remote conversion </em></p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"3\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td><strong>Release</strong></td>\n<td><strong>Recommended Support Package</strong></td>\n<td><strong>Exceptional usage with increased manual implementation effort</strong></td>\n</tr>\n<tr>\n<td>SAP BW 7.30</td>\n<td colspan=\"2\">SP 10 and higher</td>\n</tr>\n<tr>\n<td>SAP BW 7.31</td>\n<td colspan=\"2\">SP 10 and higher</td>\n</tr>\n<tr>\n<td>SAP BW 7.40</td>\n<td>SP 12 and higher</td>\n<td>SP 09</td>\n</tr>\n<tr>\n<td>SAP BW 7.50</td>\n<td colspan=\"2\">SP 05 and higher</td>\n</tr>\n<tr>\n<td>SAP BW 7.51</td>\n<td colspan=\"2\">SP 08 and higher</td>\n</tr>\n<tr>\n<td>SAP BW 7.58</td>\n<td colspan=\"2\">SP 00 and higher</td>\n</tr>\n</tbody>\n</table></div>\n<p><em><em>******* Shell conversion</em></em></p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"3\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td><strong>Release </strong></td>\n<td><strong>Recommended Support Package</strong></td>\n<td><strong> <strong>Exceptional usage with increased manual implementation effort</strong></strong></td>\n</tr>\n<tr>\n<td>SAP BW 7.00</td>\n<td>SP 28 and higher</td>\n<td>SP 25</td>\n</tr>\n<tr>\n<td>SAP BW 7.01</td>\n<td>SP 12 and higher</td>\n<td>SP 09</td>\n</tr>\n<tr>\n<td>SAP BW 7.02</td>\n<td>SP 10 and higher</td>\n<td>SP 07</td>\n</tr>\n<tr>\n<td>SAP BW 7.30</td>\n<td colspan=\"2\">SP 10 and higher</td>\n</tr>\n<tr>\n<td>SAP BW 7.31</td>\n<td colspan=\"2\">SP 10 and higher</td>\n</tr>\n<tr>\n<td>SAP BW 7.40</td>\n<td>SP 12 and higher</td>\n<td>SP 09</td>\n</tr>\n<tr>\n<td>SAP BW 7.50</td>\n<td colspan=\"2\">SP 05 and higher</td>\n</tr>\n<tr>\n<td>SAP BW 7.51</td>\n<td colspan=\"2\">SP 08 and higher</td>\n</tr>\n<tr>\n<td>SAP BW 7.58</td>\n<td colspan=\"2\">SP 00 and higher</td>\n</tr>\n</tbody>\n</table></div>\n<p><em><em>******* SAP BW/4HANA required release and support package stack</em></em></p>\n<p><em><em></em></em>SAP BW/4HANA General Information see SAP Note <a href=\"/notes/2347382\" target=\"_blank\">2347382</a></p>\n<p>SAP BW/4HANA <strong>1.0</strong></p>\n<p>For In-Place / Shell:<strong> minimal</strong> requirement SP 08, <strong>recommended</strong> - latest released SP!</p>\n<p>For Remote: <strong>minimal</strong> requirement SP 09, <strong>recommended</strong> - latest released SP!</p>\n<p>SAP BW/4HANA <strong>2.0</strong></p>\n<p>For In-Place / Shell / Remote: <strong>minimal</strong> requirement SP 02 (released)</p>\n<p>For more information regading SAP BW/4HANA 2.0 Release Restictions see SAP Note <a href=\"/notes/2733740\" target=\"_blank\">2733740</a>.</p>\n<p>SAP BW/4HANA <strong>2021</strong></p>\n<p>For In-Place / Shell / Remote: <strong>minimal</strong> requirement SP 00 (released)</p>\n<p>For more information regading SAP BW/4HANA 2021 Release Restictions see SAP Note <a href=\"/notes/3100794\" target=\"_blank\">3100794</a>.<a href=\"/notes/2733740\" target=\"_blank\"><br/></a></p>\n<p>SAP BW/4HANA <strong>2023</strong></p>\n<p>For In-Place / Shell / Remote: <strong>minimal</strong> requirement SP 00 (released)</p>\n<p>For more information regading SAP BW/4HANA 2023 Release Restictions see SAP Note <a href=\"/notes/3365187\" target=\"_blank\">3365187</a>.</p>\n<p>For Conversion from SAP_BW 7.50 for HANA to SAP BW/4HANA 2023 (SAP_BASIS 7.58) please check the downloaded Upgrade Export according to SAP Note <a href=\"https://me.sap.com/notes/3454844\" target=\"_blank\">3454844</a>.</p>\n<p><span><em>2.2.2.6 Add-On's</em></span></p>\n<p><span>Please see SAP Note <a href=\"/notes/2189708\" target=\"_blank\">2189708</a> regarding usage of Add-Ons in </span><span>SAP BW/4HANA.</span></p>\n<p> </p>\n<p><em></em><em><strong>2.2.3 Installation</strong></em></p>\n<p><em>2.2.3.1 SAP BW/4HANA Starter Add-On</em></p>\n<p>For the In-Place Conversion, the installation of the SAP BW/4HANA Starter Add-on is required. Please follow the instruction of the Conversion Guide.</p>\n<p>For Shell or Remote Conversion, the SAP BW/4HANA Starter Add-On is not required.</p>\n<p><em>2.2.3.2 SAP BW/4HANA Transfer Cockpit</em></p>\n<p>To install the Transfer Cockpit for a transfer of scenarios (in-place, remote, and shell conversion), a set of SAP Notes needs to be applied to the corresponding systems. Please use the SAP BW Note Analyzer to analyze the system and to install the prerequisites. Create the SAP BW Note Analyzer program in each system for which notes have to be applied. Depending on your use case, the following systems needs to be processed:</p>\n<ul>\n<li><strong>SAP BW</strong> (in-place conversion or sending system for a remote/shell conversion)</li>\n<li><strong>Source systems</strong> connected to SAP BW (in-place and remote/shell conversion)</li>\n<li><strong>SAP BW/4HANA</strong> (post conversoin for in-place; receiving system for a remote/shell conversion)</li>\n</ul>\n<p>Run the SAP BW Note Analyzer in each system with the corresponding XML file.</p>\n<p><em>2.2.3.3 SAP BW Note Analyzer</em></p>\n<p>For a detailed documentation of the SAP BW Note Analyzer, please see <a href=\"https://help.sap.com/doc/b235b5bd94664d9986f721f049d72cbb/1.0/en-US/User_Guide_for_SAP_BW_Note_Analyzer.pdf\" target=\"_blank\">https://help.sap.com/doc/b235b5bd94664d9986f721f049d72cbb/1.0/en-US/User_Guide_for_SAP_BW_Note_Analyzer.pdf</a></p>\n<p>Attached to that note you’ll find a text file “Z_SAP_BW_NOTE_ANALYZER.txt”. Save the file on your computer and open it in an editor.</p>\n<p>In the corresponding system, use transaction SE38 to create a report called “Z_SAP_BW_NOTE_ANALYZER”. Copy and paste the content of the above-mentioned text file into the report and activate it.</p>\n<p>Attached to that note, you’ll find several XML files. Save those files that are related to your use-case:</p>\n<p>Please find more detailed informations under:</p>\n<p><a href=\"https://wiki.scn.sap.com/wiki/pages/viewpage.action?pageId=572523874\" target=\"_blank\">https://wiki.scn.sap.com/wiki/pages/viewpage.action?pageId=572523874</a>.</p>\n<p>You want to run the pre-checks in the discovery phase (RS_B4HANA_CONVERSION_CONTROL in SAP BW 7.0x and RS_B4HANA_RC from SAP BW 7.30 to 7.50):</p>\n<ul>\n<li>For SAP BW 7.0x, use <em>SAP_BW4HANA_<strong>Pre_Checks</strong>_[last_update].xml</em></li>\n<li>For SAP BW from 7.30 to 7.50, use XML<em> SAP_BW4HANA_<strong>Readiness_Check</strong>_<em>[last_update]</em>.xml</em> from note 2575059. </li>\n</ul>\n<p>You want to transfer scenarios and perform a system conversion in the realization phase:</p>\n<ul>\n<li>For SAP BW 7.50 (all cases),</li>\n<ul>\n<li>if you start now with the installation of the tool, use <em>SAP_BW4HANA_Conversion_<strong>SAP_BW_750</strong>_[last_update].xml </em></li>\n<li>if you have already installed most of the notes before and only want the latest updates, continue to use <em>SAP_BW4HANA_<strong>In-place_Conversion_(Original_System)</strong>_[last_update].xml</em></li>\n</ul>\n<li>For all systems connected to SAP BW as a data load source system, use <em><strong>Source_System</strong>_for_SAP_BW4HANA_[last_update].xml</em></li>\n<li>Remote Transfer</li>\n<ul>\n<li>For SAP BW from 7.30 to 7.40 acting as sending system for a remote conversion, use <em>SAP_BW4HANA_<strong>Remote_Conversion_(Original_System)</strong>_[last_update].xml</em></li>\n<li>For SAP BW/4HANA acting as receiving system for a remote conversion, use <em>SAP_BW4HANA_<strong>Remote_Conversion_(Target_System)</strong>_[last_update].xml</em></li>\n</ul>\n<li>Shell Transfer</li>\n<ul>\n<li>For SAP BW from 7.00 to 7.40 acting as sending system for a shell conversion, use <em>SAP_BW4HANA_<strong>Shell_Conversion_(Original_System)</strong>_[last_update].xml</em></li>\n<li>For SAP BW/4HANA acting as receiving system for a shell conversion, use <em>SAP_BW4HANA_<strong>Shell_Conversion_(Target_System)</strong>_[last_update].xml</em></li>\n</ul>\n</ul>\n<p>You have successfully converted the system (In-Place) to SAP BW/4HANA (see section 2.3.2.3 Post-Conversion phase)</p>\n<ul>\n<li>Use <em>SAP_BW4HANA_<strong>In-place_Conversion_(After_System_Conversion)</strong>_[last_update].xml</em></li>\n</ul>\n<p>Execute the SAP BW Note Analyzer in each relevant system. Click on the icon “Load XML file” and chose the system specific XML file.</p>\n<p>Select radio button “Check implementation state against information in XML file” and check checkbox “Download needed SAP Notes”. Select radio button “In background”.</p>\n<p>Run the report. A background process will be started which downloads the required notes.</p>\n<p>When the process finished successfully, execute the report again with a de-selected checkbox “Download needed SAP Notes”.</p>\n<p>A list of notes with its implementation status will be shown. You can now install the missing notes by clicking on the corresponding icon in the list.</p>\n<p><em>2.2.3.4 Transport-Enabled Correction Instructions (TCIs)</em></p>\n<p>When installing the SAP BW/4HANA Transfer Cockpit using the SAP BW Note Analyzer, you will be prompted for SAP Note <a href=\"/notes/2358953\" target=\"_blank\">2358953</a>, <a href=\"/notes/2371120\" target=\"_blank\">2371120</a>, and <a href=\"/notes/2735300\" target=\"_blank\">2735300</a> to install a TCI.</p>\n<p>For more information about TCI, see SAP Note <a href=\"/notes/2358953\" target=\"_blank\">2358953</a>.<a href=\"/notes/2358953\" target=\"_blank\"><br/></a></p>\n<p><em>******* DMIS Add-On</em></p>\n<p>For a remote conversion, the DMIS Add-On needs to be installed in SAP BW (sending system) and SAP BW/4HANA (receiving system). Follow the instructions in SAP Note <a href=\"/notes/2513088\" target=\"_blank\">2513088</a>.</p>\n<p> </p>\n<p><strong>2.3 Conversion Process</strong></p>\n<p>For a detailed description of the conversion process, see the <a href=\"https://help.sap.com/viewer/p/SAP_BW4HANA\" target=\"_blank\">Conversion Guide</a>. In additional we attached a excel file, \"<em><strong>SAP BW4HANA Conversion - Steps to consider.xlsx</strong></em>\", as a kind of check list/project plan.</p>\n<p>List of required authorization objects for conversion, see attachment \"<em><strong>Authorization_Objects_List_Conversion_2383530.xls</strong></em>\".</p>\n<p>Frequencly ask questions are collected in note <a href=\"/notes/2930058\" target=\"_blank\">2930058</a>.</p>\n<p>Each object can only be converted once. However, it can be collected again if it is used in another scenario. For example, an IOBJ that has been converted can be used in different objects such as ADSO, DSO, CUBE. Due to consistency, these IOBJs are also collected several times, but the metadata can only be transferred once.</p>\n<p><strong>2.4 New Features</strong></p>\n<p>Under link, you can find new developed features.</p>\n<p><a href=\"https://wiki.scn.sap.com/wiki/pages/viewpage.action?pageId=550043910\" target=\"_blank\">https://wiki.scn.sap.com/wiki/pages/viewpage.action?pageId=550043910</a>.</p>\n<p>The wiki is updated regularly.</p>", "noteVersion": 420}]}, {"note": "2480284", "noteTitle": "2480284 - BW4SL - Hierarchy DataSources", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>Loading hierarchy DataSources via Operational Data Provisioning (ODP) is not available in a SAP NetWeaver 7.0x to 7.3x Source System. This affects the following ODP Provider Contexts:</p>\n<p>ODP-BW (Hierarchies from InfoObjects in SAP BW Source Systems) in SAP BW as Source System for releases in between SAP BW 7.0x and SAP BW 7.3x</p>\n<p>ODP-SAPI (Hierarchy DataSources / Extractors in any SAP Source System) in an SAP Source System (ERP, CRM, SCM, etc.) that only uses the ODP Data Replication API 1.0 or the ODP Replication API 2.0 in conjunction with PI_BASIS 7.30:</p>\n<ul>\n<ul>\n<li>Information on the availability of the ODP Replication API 1.0: See SAP Note <a href=\"/notes/1521883\" target=\"_blank\">1521883</a></li>\n<li>Information on the availability of the ODP Replication API 2.0: See SAP Note <a href=\"/notes/1931427\" target=\"_blank\">1931427</a></li>\n</ul>\n</ul>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>RSDS, DataSource *_HIER not released, program RODPS_OS_EXPOSE</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>There is currently no automated analysis available.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>For ODP-BW (Hierarchies from InfoObjects in SAP BW as Source System)</p>\n<ul>\n<li>SAP BW starting from 7.0x but lower than 7.3x:</li>\n<ul>\n<li>Please implement SAP Note <a href=\"/notes/2418250\" target=\"_blank\">2418250 - Extraction of hierarchies via ODP in NetWeaver &lt; 7.30</a> <strong>on your SAP BW source system <span>and</span> your target SAP BW and SAP BW/4HANA system</strong> in order to extract InfoObject hierarchies via ODP.</li>\n</ul>\n<li>SAP BW 7.3x:</li>\n<ul>\n<li>Please implement SAP Note <a href=\"/notes/2469120\" target=\"_blank\">2469120 - ODP &amp; Hierarchies in PI_BASIS 7.30 / BW 7.3x</a> on your SAP BW source system in order to extract InfoObject hierarchies via ODP.</li>\n</ul>\n</ul>\n<p>For ODP-SAPI (Hierarchy DataSources / Extractors in any SAP Source System)</p>\n<ul>\n<li>SAP Source Systems (ERP, CRM, SCM, etc.) that only use the ODP Data Replication API 1.0: </li>\n<ul>\n<li>Please implement SAP Note <a href=\"/notes/2418250\" target=\"_blank\">2418250 - Extraction of hierarchies via ODP in NetWeaver &lt; 7.30</a> <strong>on your source system <span>and</span> your target SAP BW and SAP BW/4HANA system</strong> in order to extract Hierarchy DataSources / Extractors via ODP. </li>\n<li>Please also note the mandatory manual pre-step required for SAP Note 2418250 in a PI_BASIS 7.0-7.11 source system: SAP Note <a href=\"/notes/2768527\" target=\"_blank\">2768527 - ODP API 1.5 Hierarchie Support UDO</a> (referenced in SAP Note 2418250).</li>\n</ul>\n<li>SAP Source Systems (ERP, CRM, SCM, etc.) with ODP Replication API 2.0 and PI_BASIS 7.30:</li>\n<ul>\n<li>Please implement SAP Note <a href=\"/notes/2469120\" target=\"_blank\">2469120 - ODP &amp; Hierarchies in PI_BASIS 7.30 / BW 7.3x</a> on your source system in order to extract Hierarchy DataSources / Extractors via ODP.</li>\n</ul>\n</ul>\n<p>As long as the above limitations apply (= the mentioned SAP Notes are not implemented on your SAP Source System or your SAP BW Source System), you can (instead of ODP) use the SAP BW Source System type for loading InfoObject hierarchies or the SAP Source System type for loading Hierarchy DataSources. This applies only for SAP BW target systems on a release 7.3x, 7.4x or 7.5x. In SAP BW/4HANA there is no alternative as ODP is the only source system type for extracting data from SAP Source Systems and SAP BW Source Systems.</p>\n<p>As a alternative workaround, the following is possible:</p>\n<ul>\n<li>Load your hierarchy from your SAP Source System to a Hierarchy InfoObject in a SAP BW &gt;= 740 SP5 using the SAP Source System (instead ODP). From this Hierarchy InfoObject, you can load your hierarchy to SAP BW/4HANA using the ODP-BW Source System in SAP BW/4HANA.</li>\n<li>Load your hierarchy to the Embedded BW of you SAP Source System. From there, use an Open Hub Destination to save the hierarchy to a file or DB table and load it from there to SAP BW/4HANA.</li>\n</ul>\n<p>Please finally note the following for SAP BW/4HANA remote conversion scenarios from SAP BW &gt;= 7.00 but &lt; SAP BW 7.30: Business Content Hierarchy DataSources that only support the transfer method 'IDoc' (ROOSOURCE-TFMETHODS = '1') can only use the \"old\" SAP BW 3.x data flow (transfer rules and update rules) in SAP BW target releases &gt;= 7.00 but &lt; 7.30. The \"new\" data flow (transformations) for such hierarchies is only available with SAP BW &gt;= 7.30. Hence, the respective data flows on SAP BW side need to be removed before conversion to SAP BW/4HANA. After conversion to SAP BW/4HANA, the respective Hierarchy DataSource can get connected again to their target InfoObjects in SAP BW/4HANA using a transformation and the ODP-SAPI Source System (in case the restriction under \"Symptom\" does not apply).</p>\n<p>For general information about availability and functionality of ODP based extractions from SAP Systems to SAP BW or SAP BW/4HANA see SAP Note <a href=\"/notes/2481315/E\" target=\"_blank\">2481315</a>.</p>\n<p>General information on ODP is available in the <a href=\"https://blogs.sap.com/2017/07/20/operational-data-provisioning-odp-faq/\" target=\"_blank\">Operational Data Provisioning FAQ</a>.</p>", "noteVersion": 7, "refer_note": [{"note": "1931427", "noteTitle": "1931427 - ODP Data Replication API 2.0", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>The \"ODP Data Replication API 2.0\" is used for the internal connection of SAP BW/4HANA or SAP BW &gt;= 7.3x, SAP BusinessObjects Data Services 4.2 and SAP HANA Smart Data Integration (ABAP Adapter) to different data provider types such as DataSources/Extractors (ODP-SAPI Context), ABAP CDS Views (ODP-CDS Context) or  InfoProviders of a SAP BW/4HANA or SAP BW &gt;= 7.4. (ODP-BW Context).</p>\n<p>The \"ODP Data Replication API 2.0\" is an functional enhancement to the first version of this interface released with SAP Note 1521883. But it does not deactivate or invalidate the first version of the interface which can still be used to connect SAP BusinessObjects Data Services 4.0.</p>\n<p>For more information on ODP 1.0 and 2.0 compatibility and availability see <a href=\"/notes/2481315\" target=\"_blank\">SAP Note 2481315</a> and the <a href=\"https://blogs.sap.com/2017/07/20/operational-data-provisioning-odp-faq/\" target=\"_blank\">Operational Data Provisioning FAQ</a>.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>BW, ETL, extraction, transformation and loading, ETL interface, ODP, operational data provider, RSAR032, RSAR 032</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>The complete \"ODP Data Replication API 2.0\" is available with the following Support Packages (SP) for the applicable component (PI_BASIS, SAP_BW, or DW4CORE). We strongly recommend to import these Support Packages (SP) that contain all relevant enhancements:</p>\n<ul>\n<li>PI_BASIS 730 SP 14 (part of SAP NetWeaver 7.30 SP 14)</li>\n<li>PI_BASIS 731 SP 16 (part of SAP NetWeaver 7.03 SP 16 and 7.31 SP 16)</li>\n<li>PI_BASIS 740 SP 11 (part of SAP NetWeaver 7.40 SP 11)</li>\n<li>SAP_BW 750 SP 0 (incl. former PI_BASIS packages)</li>\n<li>DW4CORE 100 SP 0 (incl. former PI_BASIS packages)</li>\n<li>DW4CORE 200 SP 0 (incl. former PI_BASIS packages)</li>\n</ul>\n<p>This is the only recommended way to update your system to ODP API 2.0 and to a consistent state.</p>\n<p>On an exceptional project basis (this is <span>not</span> the recommended way), the implementation of ODP API 2.0 is also possible via advance corrections with at least the following minimum Support Packages (SP) for the applicable component PI_BASIS:</p>\n<ul>\n<li>PI_BASIS 730 SP 10 (part of SAP NetWeaver 7.30 SP 10)</li>\n<li>PI_BASIS 731 SP 8 (part of SAP NetWeaver 7.03 SP 8 and 7.31 SP 8)</li>\n<li>PI_BASIS 740 SP 4 (part of SAP NetWeaver 7.40 SP 4)</li>\n</ul>\n<p>For this, the attached list of SAP Notes with advance corrections has to be analyzed on custom project basis and all relevant SAP Notes (for the specific release and SP level) must get implemented.</p>\n<p>In case you need and want to implement the complete list, you can use the attached ABAP program with the attached SAP Note list in the following way:</p>\n<ol>1. Ensure that you have implemented SAP Note 1734555.</ol><ol>2. Download the latest version of this SAP Note (1931427) from SAP Service Marketplace.</ol><ol>3. Unpack the attached file \"ZNOTE_ANALYZER_1931427.zip\" and copy the contained files \"ZNOTE_ANALYZER_1931427.txt\" and \"ODP Data Replication API 2.0 Notes.csv\" to your computer.</ol><ol>4. Use transaction SE38 \"ABAP Editor\" to create the program ZNOTE_ANALYZER_1931427.</ol><ol>5. Enter a description in the dialog box and select \"Executable program\".</ol><ol>6. Save the program as a local, temporary object.</ol><ol>7. In the menu, choose \"Utilities -&gt; Upload/Download -&gt; Upload\".</ol><ol>8. Select the path for the file \"ZNOTE_ANALYZER_1931427.txt\" on your computer.</ol><ol>9. Save and activate the program.</ol><ol>10. Execute the program.</ol><ol>11. Select the path for the file \"ODP Data Replication API 2.0 Notes.csv\" on your computer.</ol><ol>12. Select the option \"Parallel Download\".</ol><ol>13. Choose \"Execute (F8)\". The program starts several background jobs to load the latest versions of the listed SAP Notes from SAP Service Marketplace.</ol><ol>14. Execute the program again when these background jobs finish.</ol><ol>15. Select the path for the file \"ODP Data Replication API 2.0 Notes.csv\" on your computer.</ol><ol>16. Do not select the option \"Parallel Download\".</ol><ol>17. Choose \"Execute (F8)\" and confirm the dialog box. The program lists the SAP Notes concerning known errors that you have not yet implemented in your system or the latest versions of these SAP Notes.</ol><ol>18. Scroll through the list from top to bottom and double-click the SAP Note number to navigate to transaction SNOTE in order to implement the SAP Notes.</ol>\n<p>If an error occurs when you use the interface, repeat the above procedure to ensure that the latest corrections are implemented in your system.</p></div>", "noteVersion": 11}, {"note": "2440562", "noteTitle": "2440562 - ODP: Hierarchy extraction", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>Extraction of hierarchies from ODP source system (<strong>Release/Support Package level: lower than 731 SP 10</strong>) via ODP API does not work correctly.</p>\n<p>Symtoms:</p>\n<p>- only the first segment of the hierarhcy will be extracted<br/>- in InfoPackage maintanance no hierarchy can be choosen<br/>- in DTP filter in case of direct update from the source system, no hierarchy will be found<br/>- in ODP source system, RODPS_REPL_TEST program does not extract all fields of a hierarchy</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Environment\">Environment</h3>\n<ul>\n<li>SAP enhancement package 1 for SAP NetWeaver 7.3</li>\n</ul>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reproducing the Issue\">Reproducing the Issue</h3>\n<p>Extract data from hierarchy via ODP API.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Cause\">Cause</h3>\n<p>Extraction from a hierarchy works correctly as of PI_BASIS 731 Support Package 10 (SAPK-73110INPIBASIS).</p>\n<p>The minimum requirement for hierarchies is SAP NetWeaver 731 SP10.</p>\n<p>SAP will not downport the solution due to many program interdependencies to older Support Packages.</p>\n<p>SAP recommends at least a Support Package update once per year.</p>\n<p>See for the strategy also attached SAP Note <a href=\"/notes/375631\" target=\"_blank\">375631</a>.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Resolution\">Resolution</h3>\n<ol>\n<li>Apply at least PI_BASIS 731 Support Package 10 (SAPK-73110INPIBASIS). In addition read and follow steps of SAP Consulting Note <a href=\"/notes/1931427\" target=\"_blank\">1931427</a> in the ODP source system.</li>\n<li>Apply the note <a href=\"/notes/2346942\" target=\"_blank\">2346942</a> in BW.</li>\n<li>Apply the note <a href=\"/notes/2087361\" target=\"_blank\">2087361</a> in the source system.</li>\n</ol>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"See Also\">See Also</h3>\n<ul>\n<li><a href=\"/notes/2794303\" target=\"_blank\">2794303</a> - Error while transferring time dependent hierarchy data via ODP from 7.0* source system to BW</li>\n<li><a href=\"/notes/2863408\" target=\"_blank\">2863408</a> - DATEFROM not transferred for time dependent ODP hierarchy load from 7.0x source system</li>\n</ul>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Keywords\">Keywords</h3>\n<p>rsm383, dh801, segment, hierarchy, odp, sapi, bods, business objects data services, source system, fields, bw, sap, business warehouse, rodps_repl_test, odqmon, Hierarchieselektion, InfoPackage, BW, ODP, Verbindung, hierarchy selection, infopackage</p>", "noteVersion": 5}, {"note": "1521883", "noteTitle": "1521883 - ODP Data Replication API 1.0", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>The ODP Data Replication API 1.0 is used for the internal connection of SAP BusinessObjects Data Services to application extractors. This interface is delivered with an SAP NetWeaver Support Package whose specific level depends on the release.</p>\n<p>For more information on ODP 1.0 and 2.0 compatibility and availability see <a href=\"/notes/2481315\" target=\"_blank\">SAP Note 2481315</a> and the <a href=\"https://blogs.sap.com/2017/07/20/operational-data-provisioning-odp-faq/\" target=\"_blank\">operational data provisioning FAQ</a></p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>ETL, extraction, transformation and loading, ETL interface, ODP, operational data provider</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>This interface cannot be made available in advance. It must be implemented as part of a Support Package. As of which Support Package (SP) the interface is available depends on the release of the PI_BASIS component:<br/><br/>To use of the ODP Data Replication API 1.0 productively, you must install at least one of the following releases of component PI_BASIS:</p>\n<ul>\n<li>PI_BASIS 2006_1_700 Support Package 14</li>\n</ul>\n<ul>\n<li>PI_BASIS 701 Support Package 9 (part of SAP NetWeaver 7.01 Support Package 9)</li>\n</ul>\n<ul>\n<li>PI_BASIS 702 Support Package 8 (part of SAP NetWeaver 7.02 Support Package 8)</li>\n</ul>\n<ul>\n<li>PI_BASIS 730 Support Package 3 (part of SAP NetWeaver 7.30 Support Package 3)</li>\n</ul>\n<ul>\n<li>PI_BASIS 731 Support Package 1 (part of SAP NetWeaver 7.03 Support Package 1 and 7.31 Support Package 1)</li>\n</ul>\n<p>Release PI_BASIS 2005_1_700 SP24 (part of SAP NetWeaver 7.00 SP24) already enabled the use of ODP Data Replication API 1.0. However, we advise against productive use, since this release is no longer maintained. If errors occur, no advance corrections and support packages can be provided. We therefore strongly recommend that you upgrade to a higher PI_BASIS release before productive use.</p>\n<p>From a technical point of view, the interface can also be used with the preceding Support Package. However, important corrections and the data preview (Note 1559727) are missing, which cannot be implemented using transaction SNOTE.<br/><br/>In addition, we recommend that you use transaction SNOTE to implement all SAP Notes concerning known errors. As of the Support Package levels required for productive use mentioned above, you can use the attached ABAP program with the attached list of SAP Notes as follows:</p>\n<ol>1. Ensure that you have implemented Note 1734555.</ol><ol>2. Download the latest version of this SAP Note (1521883) from SAP Service Marketplace.</ol><ol>3. Unpack the attached file \"ZSAP_ODP_NOTE_ANALYZER.zip\" and copy the contained files \"ZSAP_ODP_NOTE_ANALYZER.txt\" and \"ODP Data Replication API Notes.csv\" to your computer.</ol><ol>4. Use the ABAP Editor (transaction SE38) to create program ZSAP_ODP_NOTE_ANALYZER.</ol><ol>5. Enter a description in the dialog box and select \"Executable program\".</ol><ol>6. Save the program as a local temporary object.</ol><ol>7. Choose 'Utilities --&gt; More Utilities --&gt; Upload/Download --&gt; Upload'.</ol><ol>8. Select the path for the file \"ZSAP_ODP_NOTE_ANALYZER.txt\" on your computer.</ol><ol>9. Save and activate the program.</ol><ol>10. Execute the program.</ol><ol>11. Select the path for the file \"ODP Data Replication API Notes.csv\" on your computer.</ol><ol>12. Select the option \"Parallel Download\".</ol><ol>13. Select \"Execute\" (F8). The program starts several background jobs to download the latest versions of the listed SAP Notes from SAP Service Marketplace.</ol><ol>14. Execute the program again when these background jobs have finished.</ol><ol>15. Select the path for the file \"ODP Data Replication API Notes.csv\" on your computer.</ol><ol>16. Do not select the option \"Parallel Download\".</ol><ol>17. Choose \"Execute (F8)\" and confirm the dialog box. The program lists the SAP Notes concerning known errors that you have not yet implemented in your system or where a more recent version exists.</ol><ol>18. Check the list from top to bottom and double-click the note number to navigate to transaction SNOTE to implement the SAP Notes.</ol>\n<p><br/>If an error occurs while using the interface, repeat the above procedure to ensure that the latest corrections are implemented in your system.<br/><br/>If you do not want to implement all SAP Notes concerning known errors using the attached ABAP program, implement at least the following SAP Notes:</p>\n<ul>\n<li>1660122 - ODQ: Process hangs during insert statement on table ODQ_TSN</li>\n</ul>\n<ul>\n<li>1674442 - Data throughput unsatisfactory during transfer via ODP API</li>\n</ul>\n<ul>\n<li>1704569 - ODQ: Delta unit exceeds memory limit in case of pushed delta </li>\n</ul>\n<ul>\n<li>1746264 - Previous delta confirmed too soon during recovery</li>\n</ul></div>", "noteVersion": 14}, {"note": "2469120", "noteTitle": "2469120 - ODP & Hierarchies in PI_BASIS 7.30 / BW 7.3x", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>The ODP BW context does not show InfoObject hierarchies even though the corresponding export DataSource exists.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>ODP BW HIER</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>Hierarchies are extracted in a special, segmented format. This format was not yet supported by the ODP replication framework (2.0) in PI_BASIS 7.30.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<ul>\n<li><strong><span>SAP BW 7.30</span></strong><br/>Implement Support Package 18 for SAP BW 7.30 (SAPKW73018) into your BW system. The Support Package will be available as soon as <strong>SAP Note </strong><strong>2435787 </strong>with the short text \"SAPBWNews 7.30 BW ABAP SP18\", which describes this Support Package in more detail, is released for customers.</li>\n</ul>\n<ul>\n<li><strong><span>SAP BW 7.31 (SAP NW BW 7.3 EHP 1)</span></strong><br/>Implement Support Package 21 for SAP BW 7.31 (SAPKW73121) into your BW system. The Support Package will be available as soon as <strong>SAP Note </strong><strong>2443574 </strong>with the short text \"SAPBWNews 7.31 BW ABAP SP21\", which describes this Support Package in more detail, is released for customers.</li>\n</ul>\n<p>In urgent cases you can use the correction instructions.</p>\n<p>Before you use the correction instructions, make sure that you check <strong>SAP Note 1668882 and SAP Note 2248091</strong> for transaction SNOTE.</p>\n<p>This SAP Note might already be available before the Support Package is released. In this case, however, the short text still contains the term \"preliminary version\".</p>", "noteVersion": 2}]}, {"note": "2481315", "noteTitle": "2481315 - Operational Data Provisioning (ODP): Extracting from SAP Systems to SAP BW or SAP BW/4HANA – Availability and Limitations", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You want to connect a SAP system (Service-API DataSources / Extractors), a SAP BW or SAP BW/4HANA as source system to a SAP BW or SAP BW/4HANA target system. You want to check whether <span>on system level</span> your source and target system meet the prerequisites for ODP-based data extraction. In addition, you need information on potential limitations and whether they can get addressed.</p>\n<p>In order to assess the ODP Availability <span>on DataSource (extractor) level</span> please refer to SAP Note <a href=\"/notes/2232584\" target=\"_blank\">2232584</a> \"Release of SAP extractors for operational data provisioning (ODP)\". Note that the vast majority of Business Content and custom DataSources can get easily released for ODP.</p>\n<p>For more general questions about ODP please have a look at our <a href=\"https://blogs.sap.com/2017/07/20/operational-data-provisioning-odp-faq/\" target=\"_blank\">Operational Data Provisioning (ODP) FAQ</a>. In there, you will also find specific information on conversion support from SAP Source System to ODP Source System.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>S-API, DataSources, ODP-BW, ODP-SAPI, RSDS, LSYS, RSBASIDOC, ODQ</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>In order to use ODP for data extraction to your SAP BW or SAP BW/4HANA system please see the following SAP Notes regarding the correct support packages and SAP Notes to be implemented on the respective source system.</p>\n<ul>\n<li><em>SAP_BASIS &lt; 730 SAP Note <a href=\"/notes/1521883\" target=\"_blank\">1521883</a> (ODP Replication API 1.0)</em></li>\n<li><em>SAP_BASIS &gt;= 730 SAP Note <a href=\"/notes/1931427\" target=\"_blank\">1931427</a> (ODP Replication API 2.0)</em></li>\n</ul>\n<p>In case your source system only supports the ODP Replication API 1.0 please note the following functional differences in comparison to the ODP Replication API 2.0:</p>\n<ul>\n<li>No support for realtime loading for Service API DataSources (Extractors)</li>\n<ul>\n<li>This affects however only few DataSources in the SAP delivered Business Content. For a detailed check you may use table ROOSOURCE in your source system where REALTIME = ‘X’</li>\n</ul>\n<li>No remote Content Activation (DataSource Activation in Source triggered from BW Content Activation)\r\n<ul>\n<li>This means that DataSources / Extractors have to be first activated in the SAP Source System and then replicated to BW before activating respective Business Content Data Flow on SAP BW or SAP BW/4HANA side (see next point).</li>\n</ul>\n</li>\n<li>No Application Component Tree Replication (from Source System to SAP BW or SAP BW/4HANA)</li>\n<ul>\n<li>From ODP 1.0 based source systems, the application component tree from the source system can’t be replicated to SAP BW or SAP BW/4HANA. Hence, the generic application component tree as defined in target SAP BW or SAP BW/4HANA is used (that is the application component tree that is for example used for Flat File Source Systems).</li>\n<li>Replication of (new) DataSources from an ODP 1.0 source system can therefore only happen from the source system (top node) itself. At that time, a pop up will display all (new) DataSources that haven’t been replicated so far to the target system. The user is then able to select only specific DataSources for replication.</li>\n<li>All newly replicated DataSource will then typically get assigned to the application component “Unassigned Nodes”. From within the DataSource Maintenance, a user can then change this (default) application component to another application component and activate the DataSource on BW Side.</li>\n<li>Tip: In order to ease the structuring of DataSources on SAP BW or SAP BW/4HANA side in that case you can for sure maintain the generic application component tree in a way that it contains the same application components as in the connected source systems (p.eg. creating manually application components such as LO, SD, CO or similar).</li>\n</ul>\n<li>Enhanced parallel extraction (Prefetch)</li>\n<ul>\n<li>Parallel extraction is supported in ODP 1.0 but ODP 2.0 has a refined packaging mechanism compared to ODP 1.0.</li>\n</ul>\n</ul>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Here is an overview on available integration scenarios between SAP systems and SAP BW or SAP BW/4HANA target systems:</p>\n<p><strong>1) SAP Source Systems to SAP BW or SAP BW/4HANA Target System</strong></p>\n<p><strong>1a) DataSources (Extractors)</strong></p>\n<p>The following table lists available the scenarios for extracting DataSources from SAP systems (other than SAP BW or SAP BW/4HANA). This also includes custom (generic) DataSources you might have created in your SAP system. In case of limitations with ODP, the SAP Source System may be used instead (if available).</p>\n<div class=\"table-responsive\"><table border=\"2\" cellpadding=\"3\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td> </td>\n<td colspan=\"2\"><strong>SAP Source Systems</strong></td>\n</tr>\n<tr>\n<td><strong>Target System</strong></td>\n<td><em>ODP Data Replication API 2.0</em></td>\n<td><em>ODP Data Replication API 1.0 (*1)</em></td>\n</tr>\n<tr>\n<td>SAP BW/4HANA</td>\n<td>ODP-SAP  </td>\n<td>ODP-SAP </td>\n</tr>\n<tr>\n<td>SAP BW 7.5x</td>\n<td>\n<p>ODP-SAP or<br/>SAP Source System</p>\n</td>\n<td>ODP-SAP or<br/>SAP Source System</td>\n</tr>\n<tr>\n<td>SAP BW 7.4x</td>\n<td>\n<p>ODP-SAP or<br/>SAP Source System</p>\n</td>\n<td>ODP-SAP or<br/>SAP Source System</td>\n</tr>\n<tr>\n<td>SAP BW 7.3x (*3)</td>\n<td>\n<p>ODP-SAP or<br/>SAP Source System</p>\n</td>\n<td>SAP Source System</td>\n</tr>\n<tr>\n<td>SAP BW 7.0x</td>\n<td>SAP Source System</td>\n<td>SAP Source System</td>\n</tr>\n</tbody>\n</table></div>\n<p>Note that DataSources (extractors) in the SAP system need to be <strong>specifically released for ODP</strong>. For more information see SAP Note <a href=\"/notes/2232584\" target=\"_blank\">2232584</a> - Release of SAP extractors for operational data provisioning (ODP).</p>\n<p><strong>1b) Other Data</strong></p>\n<p>As an alternative to DataSources, for example to extract data from other database tables that are not available as a DataSource, you can use ABAP CDS views (delivered by SAP or custom). See the <a href=\"https://www.sap.com/documents/2017/06/50b7535e-bf7c-0010-82c7-eda71af511fa.html\" target=\"_blank\">How To Guide ABAP CDS Data Provisioning to SAP BW</a> for more information.</p>\n<p>This scenario is available in the following cases:</p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"3\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td> </td>\n<td colspan=\"5\"><strong>SAP Source Systems</strong></td>\n</tr>\n<tr>\n<td> </td>\n<td colspan=\"4\"><em>ODP Data Replication API 2.0</em></td>\n<td><em>ODP Data Replication API 1.0</em></td>\n</tr>\n<tr>\n<td><strong>Target System</strong></td>\n<td>SAP BW/4HANA</td>\n<td>SAP BW 7.5x</td>\n<td>SAP BW 7.4x</td>\n<td>SAP BW 7.3x</td>\n<td>SAP BW 7.0x</td>\n</tr>\n<tr>\n<td>SAP BW/4HANA</td>\n<td>ODP-CDS </td>\n<td>ODP-CDS </td>\n<td>n/a</td>\n<td>n/a</td>\n<td>n/a</td>\n</tr>\n<tr>\n<td>SAP BW 7.5x</td>\n<td>\n<p>ODP-CDS</p>\n</td>\n<td>\n<p>ODP-CDS</p>\n</td>\n<td>\n<p>n/a</p>\n</td>\n<td>n/a</td>\n<td>\n<p>n/a</p>\n</td>\n</tr>\n<tr>\n<td>SAP BW 7.4x</td>\n<td>ODP-CDS </td>\n<td>\n<p>ODP-CDS</p>\n</td>\n<td>\n<p>n/a</p>\n</td>\n<td>n/a</td>\n<td>n/a</td>\n</tr>\n<tr>\n<td>SAP BW 7.3x (*3)</td>\n<td>ODP-CDS </td>\n<td>\n<p>ODP-CDS</p>\n</td>\n<td>\n<p>n/a</p>\n</td>\n<td>n/a</td>\n<td>n/a</td>\n</tr>\n<tr>\n<td>SAP BW 7.0x</td>\n<td>n/a</td>\n<td>n/a</td>\n<td>n/a</td>\n<td>n/a</td>\n<td>n/a</td>\n</tr>\n</tbody>\n</table></div>\n<p><strong>2) SAP BW or SAP BW/4HANA Source Systems to SAP BW or SAP BW/4HANA Target System</strong></p>\n<p><strong>2a) InfoProvider Data</strong></p>\n<p>SAP BW and SAP BW/4HANA <em>InfoProvider data</em> is extracted using the <strong>ODP-BW Source System</strong> in your connected SAP BW or SAP BW/4HANA target system. See also <a href=\"http://help.sap.com/saphelp_nw75/helpdata/en/ef/3341bc65c747fba1509fdfb250b452/content.htm?frameset=/en/ef/3341bc65c747fba1509fdfb250b452/frameset.htm&amp;current_toc=/en/a3/fe1140d72dc442e10000000a1550b0/plain.htm&amp;node_id=387\" target=\"_blank\">Exchanging Data Between BW Systems Using the ODP Source System</a>. In case of limitations with ODP-BW the former BW Source System may be used instead of ODP-BW (indicated if applicable).</p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"3\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td> </td>\n<td colspan=\"5\"><strong>SAP BW or SAP BW/4HANA Source Systems</strong></td>\n</tr>\n<tr>\n<td> </td>\n<td colspan=\"4\"><em>ODP Data Replication API 2.0</em></td>\n<td><em>ODP Data Replication API 1.0</em></td>\n</tr>\n<tr>\n<td><strong>Target System</strong></td>\n<td>SAP BW/4HANA</td>\n<td>SAP BW 7.5x</td>\n<td>SAP BW 7.4x (*2)</td>\n<td>SAP BW 7.3x (*4)</td>\n<td>SAP BW 7.0x (*4)</td>\n</tr>\n<tr>\n<td>SAP BW/4HANA</td>\n<td>ODP-BW</td>\n<td>ODP-BW</td>\n<td>ODP-BW</td>\n<td>ODP-BW</td>\n<td>ODP-BW</td>\n</tr>\n<tr>\n<td>SAP BW 7.5x</td>\n<td>ODP-BW</td>\n<td>\n<p>ODP-BW or<br/>BW Source System</p>\n</td>\n<td>\n<p>ODP-BW or<br/>BW Source System</p>\n</td>\n<td>\n<p>ODP-BW or<br/>BW Source System</p>\n</td>\n<td>\n<p>ODP-BW or<br/>BW Source System</p>\n</td>\n</tr>\n<tr>\n<td>SAP BW 7.4x</td>\n<td>ODP-BW</td>\n<td>\n<p>ODP-BW or<br/>BW Source System</p>\n</td>\n<td>\n<p>ODP-BW or<br/>BW Source System</p>\n</td>\n<td>BW Source System</td>\n<td>BW Source System</td>\n</tr>\n<tr>\n<td>SAP BW 7.3x (*3)</td>\n<td>ODP-BW </td>\n<td>\n<p>ODP-BW or<br/>BW Source System</p>\n</td>\n<td>\n<p>ODP-BW or<br/>BW Source System</p>\n</td>\n<td>BW Source System</td>\n<td>BW Source System</td>\n</tr>\n<tr>\n<td>SAP BW 7.0x</td>\n<td>n/a</td>\n<td>BW Source System</td>\n<td>BW Source System</td>\n<td>BW Source System</td>\n<td>BW Source System</td>\n</tr>\n</tbody>\n</table></div>\n<p>In case two options are available, it's recommended to use an ODP Source System.</p>\n<p><strong>2b) Other Data</strong></p>\n<p>In case you want to also transfer <em>data out of other tables</em> of a SAP BW (e.g. technical content or generic (custom) DataSources), you can use the <strong>ODP-SAP Source System </strong>in your connected target system to load those DataSources. The above availability matrix for 1a) applies.</p>\n<p>In SAP BW 7.5x, you may alternatively use ABAP CDS views for data transfer out of other tables of your system and then the <strong>ODP-CDS Source System </strong>in your connected target system instead<strong>. </strong>In SAP BW/4HANA based source systems, ABAP CDS views are the only option for custom (generic) and statistics data (Technical Content) extractions out of SAP BW/4HANA. See more information on <a href=\"https://help.sap.com/viewer/dd104a87ab9249968e6279e61378ff66/11.0.6/en-US/1e596b288f494f5d815c86cf94c3fbbb.html\" target=\"_blank\">Technical Content coverage with ABAP CDS in SAP BW/4HANA</a> in the SAP Help Portal. Therefore, you have to use an <strong>OPD-CDS Source System</strong> in your target system. The above availability matrix for 1b) applies.</p>\n<p><strong>3) Replication</strong></p>\n<p>As a general alternative to (batch) extraction, you can consider real-time replication via SAP Landscape Transformation (SLT). In this case, you use the ODP-SLT Source System in the target SAP BW or SAP BW/4HANA system.</p>\n<p><strong>4) Recommendations</strong></p>\n<p>In case two or more options are available, it's recommended to use an ODP Source System (and ODP-CDS rather than ODP-SAP). To check the readiness of your source systems for ODP, we recommend to use the SAP BW Note Analyzer with the corresponding XML, which you can both find attached to SAP Note <a href=\"/notes/2383530\" target=\"_blank\">2383530</a>.</p>\n<p><strong>Footnotes</strong></p>\n<p>(*1) For hierarchy support in ODP-SAPI (DataSources / Extractors) Source Systems (ERP, CRM, SCM, etc.) that only use the ODP Data Replication API 1.0 please implement SAP Note <a href=\"/notes/2418250\" target=\"_blank\">2418250</a>. For more information see also SAP Note <a href=\"/notes/2480284\" target=\"_blank\">2480284</a>.</p>\n<p>(*2) Minimum SP 5 required in SAP BW 7.40 source system.</p>\n<p>(*3) For creating and using ODP Source Systems in SAP BW 7.3x target systems (ODP consumer) certain SAP Notes are required (see SAP Notes <a href=\"/notes/1935357\" target=\"_blank\">1935357</a> and <a href=\"/notes/1780912\" target=\"_blank\">1780912</a>).</p>\n<p>(*4) With the following SAP Notes it is possible to connect source SAP BW systems between release 7.00 and 7.4 SP 5 (initial shipment of the ODP-BW context) via ODP to SAP BW 7.5 and SAP BW/4HANA. Already available generated Export DataSources (8*) for InfoProviders and InfoObjects will then expose themselves via the ODP-BW context to the target SAP BW 7.5 or SAP BW/4HANA system.</p>\n<p>The following SAP Notes are required in the source SAP BW system</p>\n<ul>\n<li>ODP API 1.5 (PI_BASIS): <a href=\"/notes/2415021\" target=\"_blank\">2415021</a> with <a href=\"/notes/2403202\" target=\"_blank\">2403202</a> as prerequisite</li>\n<li>SAP_BW: <a href=\"/notes/2414955\" target=\"_blank\">2414955</a> with <a href=\"/notes/2404671 \" target=\"_blank\">2404671 </a>as prerequisite</li>\n<li>SAP_BW: <a href=\"/notes/2425281\" target=\"_blank\">2425281</a>; with <a href=\"/notes/2404671 \" target=\"_blank\">2404671 </a>as prerequisite</li>\n<li>Only in SAP BW 7.0x and 7.1x: <a href=\"/notes/2445660\" target=\"_blank\">2445660</a></li>\n<li>Only for hierarchy extraction from InfoObjects in SAP BW 7.3x: <a href=\"/notes/2469120\" target=\"_blank\">2469120</a>.</li>\n<li>Only for hierarchy extraction from InfoObjects in SAP BW starting from 7.0x but lower than 7.3x: <a href=\"/notes/2418250\" target=\"_blank\">2418250</a></li>\n<li>For further information on hierarchy support see also SAP Note <a href=\"/notes/2480284\" target=\"_blank\">2480284</a>.</li>\n</ul>\n<p>The following SAP Note is required in a target SAP BW 7.5x or SAP BW/4HANA system for connecting a source SAP BW system with release 7.0 to 7.3x.</p>\n<ul>\n<li><a href=\"/notes/2404673\" target=\"_blank\">2404673</a> ODP API v1.5 in BW DataSource</li>\n</ul>\n<p>In general, ODP corrections follow BW’s maintenance strategy which is documented in note <a href=\"/notes/375631\" target=\"_blank\">375631</a>. Please follow the maintenance window for security patches and therefore will provide fixes for the last two years at least.</p>", "noteVersion": 15}]}