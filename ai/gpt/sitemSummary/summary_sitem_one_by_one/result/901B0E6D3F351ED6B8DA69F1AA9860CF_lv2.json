{"guid": "901B0E6D3F351ED6B8DA69F1AA9860CF", "sitemId": "SI18: Oil_UOM_Data_Model", "sitemTitle": "S4TWL - Data Model in Upstream Oil Management", "note": 2419408, "noteTitle": "2419408 - Upstream Operations Management: Change of data model in S/4HANA", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You want to install SAP S/4HANA and require additional information to adjust your enhancements, modifications and own functionalities to the new simplified data model.</p>\n<p>You want to know the difference in Upstream Operations Management on S/4HANA compared to R/3.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Well Test, Well Test Header, Proxy Object, CDS View</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>You have customer enhancements, modifications in the area of maintaining well test in Upstream Operations Management which are built on SAP ERP 6.0.</p>\n<p>The SAP ERP 6.0 Upstream Operations Management consists of two tables (gho_well_prior and gho_well_std_h) for well test header information. Table gho_well_prior has more fields as compared to gho_well_std_h. Deletion flag field exists only in gho_well_prior hence, the deletion of well test can be tracked only through table gho_well_prior.</p>\n<p>As all the operations on well test performed through gho_well_std_h can also be performed through gho_well_prior and deletion flag can only be stored and tracked through gho_well_prior. So, in order to remove redundancey a proxy CDS view has been set for gho_well_std_h.</p>\n<p>Table gho_well_std_h from SAP ERP 6.0 do still exist in S/4HANA as DDIC definition as well as database object. For compatability reasons there is a Core Data Service (CDS) View assigned as a proxy object to table gho_well_std_h ensuring that each read access to this table returns the data as before in SAP ERP 6.0. Hence all customer coding reading data from gho_well_std_h will work as before because each read access to gho_well_std_h will get redirected to the database interface layer of NetWeaver to the assigned CDS Views created on table gho_well_prior. However if there is any custom coding which writes only to table gho_well_std_h that should be adjusted to now write in gho_well_prior.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Core Data Service(CDS) View NSDM_E_GHOWLSTDH on the top of table gho_well_prior has been created and set as a proxy object on table gho_well_std_h. The NetWeaver redirect capability requires that DB table and assigned proxy view is compatible in the structure: number of fields, their sequence and their type.</p>\n<p><strong>SQL View Name</strong>: NSDM_V_GHOWLSTDH</p>\n<p><strong>CDS View Name</strong>: NSDM_E_GHOWLSTDH</p>\n<p><strong>DDL Source</strong>: NSDM_DDL_GHO_WELL_STD_H</p>", "noteVersion": 2, "refer_note": [{"note": "2444372", "noteTitle": "2444372 - Release Information Note for Upstream Operations Management (UOM) - Upstream Oil & Gas 05/2017", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>This note provides information regarding Upstream Operations Management (UOM) in the area of SAP Upstream Oil &amp; Gas.</p>\n<p>Upstream Operations Management enables companies, for example, oil and gas companies, to carry out their exploration and production operations in the most cost-efficient way to maximize profitability.</p>\n<p>Upstream Operations Management for SAP Oil &amp; Gas consists of the following apps:</p>\n<ul>\n<ul>\n<li>Capture Field Data</li>\n<li>Analyze Production Allocation</li>\n<li>Allocate Production</li>\n<li>View Deferment Events</li>\n<li>Manage Work Order Deferment Events</li>\n<li>Analyze Deferment</li>\n<li>Fix Errors</li>\n</ul>\n<ul>\n<li>Manage Forecast Projects</li>\n<li>Manage Forecast Access</li>\n<li>Gather Forecast Data</li>\n<li>Calculate Forecast</li>\n<li>View Forecast Results</li>\n<li>Approve and Publish Forecast</li>\n<li>Upload Production Data</li>\n<li>Manage Hierarchy</li>\n<li>Analyze and Compare Results</li>\n</ul>\n</ul>\n<p>Please see the references section for release information notes of individual applications and features. For more details refer the attachments section.</p>\n<p>To access the FPM Applications of UOM, the NWBC Role is \"SAP_SR_UOM_S4\".</p>\n<div class=\"WordSection1\">\n<p>This note is subject to change. Ensure you check it on a regular basis.</p>\n</div>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>SAP Oil &amp; Gas, UOM, Upstream Operations Management, SAP Oil &amp; Gas, UOM, Upstream Operations Management, NWBC, Net Weaver Business Client</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<div class=\"longtext\">\n<p>Please check the release information note 2346431 for product version 'S/4HANA OP 1610' and 2356208  for product version 'Fiori for S/4HANA OP 1610' for general installation and infrastructure topics.</p>\n</div>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Apply the Stack FPS2  of product version S/4HANA OP 1610 on your Frontend Server system</p>\n<p>Apply the Stack FPS2  of product version S/4HANA OP 1610 on the assigned  Backend systems</p>", "noteVersion": 3, "refer_note": [{"note": "2443851", "noteTitle": "2443851 - Release Information Note for the Fiori application View Forecast Results 05/2017", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>This note provides additional information regarding the Fiori application View Forecast Results in the area of Oil &amp; Gas Upstream Operations Management (UOM). This note contains the minimum required and recommended fixes that should be installed to make the app functional either in the frontend server or in the related backend server.</p>\n<div class=\"sapUiRespGridSpanL8 sapUiRespGridSpanM8 sapUiRespGridSpanS12 sapUiRespGridSpanXL8\">\n<div id=\"__html2\">\n<p class=\"- topic/p\">With the transactional app View Forecast Results you, in your role as a Forecast Analyst - Production (OG) or Forecast Specialist (OG) or Forecast Manager (OG), can view forecast results of forecast projects. You can filter your authorized forecast projects by name, effective dates, and forecast date.</p>\n<p class=\"- topic/p\">Key Features</p>\n<ul>\n<li>View forecast results of forecast projects</li>\n<li>View aggregated results at Scenarios and Production Networks</li>\n<li>Enter comments for scenarios and networks</li>\n</ul>\n<p>This note is subject to change. Ensure you check it on a regular basis.</p>\n<p>New or signficant enhancements of delivered Fiori applications for area View Forecast Results are delivered with Stack FPS2 of productversion S/4HANA OP 1610</p>\n<p>Following Fiori apps are affected:</p>\n<p><strong>Fiori application                 Status</strong></p>\n<p>View Forecast Results         New; OData service UPS_FC_VIEW_RES has to be registered.</p>\n<p>Gateway Role                    SAP_BR_FC_ANALYST_PROD_IOG has to be added</p>\n<p>Additional information about the Fiori application can be found in the <a href=\"https://fioriappslibrary.hana.ondemand.com/sap/fix/externalViewer/\" target=\"_blank\">SAP Fiori apps reference library</a></p>\n</div>\n</div>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Fiori, Design, UI, APP, UPS_FC_RESULTS1, SAP Oil &amp; Gas, UOM, Upstream Operations Management, Forecasting, View Forecast Results</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>Please check the release information note 2346431 for product version 'S/4HANA OP 1610' and 2356208  for product version 'Fiori for S/4HANA OP 1610' for general installation and infrastructure topics.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Apply the Stack FPS2 of productversion S/4HANA OP 1610 on your Frontend Server system</p>\n<div class=\"longtext\">\n<div class=\"longtext\">\n<div class=\"longtext\">\n<p>Apply the Stack FPS2 of productversion S/4HANA OP 1610 on the assigned  Backend systems</p>\n</div>\n</div>\n</div>", "noteVersion": 2}, {"note": "2356208", "noteTitle": "2356208 - SAP FIORI FOR SAP S/4HANA 1610: Release Information Note", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>Release information for ‘SAP FIORI FOR SAP S/4HANA 1610’ (SAP Fiori 2.0 for SAP S/4HANA). This note is the entry point for central notes and application-specific notes.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>FIORI, Design, UI, APP, ‘SAP FIORI FOR SAP S/4HANA 1610’, ‘SAP Fiori 2.0 for SAP S/4HANA’</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>You want to run the ‘SAP FIORI FOR SAP S/4HANA 1610’ (SAP Fiori 2.0 for SAP S/4HANA) and look for overall information.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>This note gives you additional information about the new UI for the product ‘SAP Fiori 2.0 for SAP S/4HANA’. It is a central entry point and includes central notes, plus area &amp; application specific notes.</p>\n<p><strong>Note:</strong> This SAP Note is subject to change. All important changes made after release of SAP FIORI FOR SAP S/4HANA 1610 are documented in section \"Changes made after Release of SAP FIORI FOR SAP S/4HANA 1610.</p>\n<p><strong>Supported SAP FIORI front-end server</strong></p>\n<ul>\n<li>SAP Fiori front-end server 3.0 is Out of Maintenance since 31.12.2019</li>\n<li>SAP Fiori front-end server 4.0 is Out of Maintenance since 31.12.2020</li>\n<li>SAP Fiori front-end server 5.0 (Starting from SP05)</li>\n<li>SAP Fiori front-end server 6.0 (Starting from SP08)</li>\n<li>Upgrading the SAP Fiori front-end server does not require the underlying Application Server ABAP (hub deployment) or SAP S/4HANA 1610 back-end system (embedded deployment) to be updated if the Support Package Stack level is already on Feature Package Stack 01 or higher.</li>\n</ul>\n<p><strong><span>Supported Databases</span></strong></p>\n<ul>\n<li>Running SAP FIORI Fiori front-end server as an embedded deployment on your S/4HANA back-end will be based on SAP HANA Database.</li>\n<li>Running SAP FIORI Fiori front-end server hub deployment, SAP MaxDB, Sybase ASE and SAP HANA Database are supported database systems.</li>\n</ul>\n<p><strong><span>Supported Browser</span></strong></p>\n<ul>\n<li>Browser Support details can be found in the <a href=\"https://support.sap.com/content/dam/launchpad/en_us/pam/pam-essentials/TIP/S4HANA1610Cont.pdf\" target=\"_blank\">Product Planning Matrix for SAP S/4HANA 1610 and SAP S/4HANA FOUNDATION 1610</a>.</li>\n<li>Microsoft Internet Explorer and Legacy Edge support changes:Support of IE11 for SAPUI5, Fiori Launchpad, Web Dynpro ABAP and SAPGUI for HTML will end. Details on affected components and product releases are described in SAP note <a href=\"/notes/1672812\" target=\"_blank\">1672817</a> (Browser: Microsoft Legacy Edge and Internet Explorer Support Policy Note).</li>\n</ul>\n<p><strong>General information</strong></p>\n<ul>\n<li>Support Package Stack 01 or higher of SAP Fiori For SAP S/4HANA 1610 on the frontend require Feature Package Stack 01 or higher of SAP S/4HANA 1610 in the backend (and vice versa).</li>\n<li>Support Package Stack 00 of SAP Fiori For SAP S/4HANA 1610 on the frontend requires Feature Package Stack 00 of SAP S/4HANA 1610 in the backend (and vice versa).</li>\n<li>There is a strict (1:1) dependency between SAP Fiori for SAP S/4HANA version (containing the Fiori UIs) and the SAP S/4HANA back-end version. SAP FIORI FOR SAP S/4HANA 1610 is released for SAP S/4HANA 1610 only.</li>\n<li>We recommend you implement at least <strong>SAP FIORI FOR SAP S/4HANA 1610 Support Package Stack 01</strong> (SAPUI5 version 1.44 with Maintenance Phase \"Maintenance\") in your productive system in order to avoid disruption as this version is maintained for an extended period. The minimum UI-level for product version <strong>SAP Fiori For SAP S/4HANA 1610 </strong>is SAP FIORI FRONTEND SERVER 5.0 Support Package Stack01.</li>\n</ul>\n<p><strong><span>Important Notes</span></strong></p>\n<ul>\n<li>SAP Fiori front-end server 6.0 - General Information (SAP note <a href=\"/notes/2775163\" target=\"_blank\">2775163</a>)</li>\n<li>SAP Fiori front-end server 5.0 - General Information (SAP note <a href=\"/notes/2618605\" target=\"_blank\">2618605</a>)</li>\n<li>General Information: FIORI UI Infrastructure Components for products on SAP Fiori front-end server 5.0 (S4H) (SAP note <a href=\"/notes/2662732\" target=\"_blank\">2662732</a>)</li>\n<li>FIORI UI Infrastructure Components for products on SAP Frontend Server 4.0 (S4H) (SAP note <a href=\"/notes/2524632\" target=\"_blank\">2524632</a>) </li>\n<li>SAP Fiori front-end server deployment for SAP S/4HANA (SAP note <a href=\"/notes/2590653\" target=\"_blank\">2590653</a>)</li>\n<li>Telecommunication Network Prerequisites for SAP Fiori (SAP note <a href=\"/notes/0001958586\" target=\"_blank\">1958586</a>)</li>\n<li>General Fiori Browser Information: <a href=\"/notes/1935915\" target=\"_blank\">1935915 - Fiori for Business Suite: Browser / Devices / OS Information</a></li>\n<li>Please refer to note <a href=\"/notes/2217489\" target=\"_blank\">2217489</a> (Technology (SAPUI5) in SAP AS ABAP/Fiori Front-End Server)</li>\n<li>SAP Enterprise Search in SAP Fiori 2358053 <a href=\"/notes/2358053\" target=\"_blank\">Release Information for SAP Enterprise Search 7.51 for SAP Fiori</a></li>\n<li>Integration with SAP Launchpad service - Restrictions (SAP note <a href=\"/notes/3042853\" target=\"_blank\">3042853</a>).</li>\n</ul>\n<p><strong>Area &amp; application specific notes</strong></p>\n<p>Regarding application area or app specific deviations please check for additional notes documented in the installation chapter from the <a href=\"http://www.sap.com/fiori-apps-library\" target=\"_blank\">Fiori Apps Reference Library</a>.</p>\n<p>For information on special restrictions regarding Finance Fiori apps refer to SAP Note <a href=\"/notes/2344977\" target=\"_blank\">2344977</a> and regarding Sales apps refer to SAP Note <a href=\"/notes/2348936\" target=\"_blank\" title=\"2348936  - SAP S/4HANA 1610 Sales: Restriction Note\">2348936</a>.</p>\n<p>Customers using MDG for consolidation and mass processing and implementing S/4HANA 1610 Feature Package Stack 02 or higher need also to implement SAP note <a href=\"/notes/2442897\" target=\"_blank\">2442897</a> in the respective frontend system.</p>\n<p>In addition check the reference section of this note for additional information.</p>\n<p><strong>Information on Upgrading Fiori Apps from previous SAP S/4HANA Versions</strong></p>\n<p>For general information on the SAP Fiori Upgrade within SAP S/4HANA please refer to the <a href=\"https://uacp.hana.ondemand.com/http.svc/rc/PRODUCTION/pdfd61b9f57e5146b10e10000000a441470/1610%20001/en-US/UPGR_OP1610_FPS01.pdf\" target=\"_blank\">Upgrade Guide</a>, the section “Follow-On Activities for SAP Fiori”.<br/>After performing the steps described in the guide, the deployed Fiori Apps from SAP S/4HANA 1511 should generally be working again. However, depending on the apps set-up in your scenario, there may be certain cases, where an app is not running anymore.</p>\n<ul>\n<li>Some apps require additional manual application customizing after the upgrade from SAP S/4HANA 1511 to 1610 Feature Package Stack 01. Please check the reference section of this note for additional information, if you have deployed these apps in SAP S/4HANA 1511.</li>\n<li>Another possible cause for an app not working, may be a change of configuration requirements for an app that is introduced with the new product version or Feature Package Stack of SAP Fiori for SAP S/4HANA (like required ICF services or oDATA services). To identify these changes, we recommend that you check the configuration details in the entry of the latest app version in the <a href=\"https://fioriappslibrary.hana.ondemand.com/sap/fix/externalViewer/#/detail/Apps('F0717')/S6OP\" target=\"_blank\">SAP Fiori apps reference library</a>.</li>\n<li>A third cause may be changes in the delivered standard content (e.g. Fiori Catalogs), introduced with the new SAP Fiori for SAP S/4HANA product version, that are (by definition) not reflected in objects created in the customization layer built on top the standard content. Hence, if you have created such customer specific content, you should check for affected objects in the Launchpad designer.</li>\n<li>Lastly, we recommend that you explore the <a href=\"https://fioriappslibrary.hana.ondemand.com/sap/fix/externalViewer/#/detail/Apps('F0717')/S6OP\" target=\"_blank\">SAP Fiori apps reference library</a> with regards to newly added apps for the business roles you are using that would make sense to set-up and use in addition.</li>\n</ul>\n<p><strong>Changes made after Release of SAP FIORI FOR SAP S/4HANA 1610</strong></p>\n<ul>\n<li><sub><span>2021-06-18: Added Note for Launchpad Service</span></sub></li>\n<li><sub>2021-02-11: Inserted Microsoft Internet Explorer and Legacy Edge support changes.</sub></li>\n<li><sub>2017-05-09: Inserted Area &amp; application specific notes</sub></li>\n</ul>", "noteVersion": 27}, {"note": "2443857", "noteTitle": "2443857 - Release Information Note for the Fiori application View Deferment Events 05/2017", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>This note provides additional information regarding the Fiori application View Deferment Events in the area of Oil &amp; Gas Upstream Operations Management (UOM). This note contains the minimum required and recommended fixes that should be installed to make the app functional either in the frontend server or in the related backend server.</p>\n<p>With the transactional app View Deferment Events a Deferment Analyst can view the existing deferment events created for the relevant network objects. The screeen also displays event attributes such as event period, deferment code and tags. You can also view valid and invalid events. You can navigate to the deferment event screen to edit or delete deferment event if required.</p>\n<p>Key Features:</p>\n<ul>\n<li>View deferment events</li>\n<li>Maintain Deferment Events</li>\n</ul>\n<p>This note is subject to change. Ensure you check it on a regular basis.</p>\n<p>New or signficant enhancements of delivered Fiori applications for area View Deferment Events are delivered with Stack FPS2 of productversion S/4HANA OP 1610</p>\n<p>Following Fiori apps are affected:</p>\n<p><strong>Fiori application                  Status</strong></p>\n<p>View Deferment Events          New; OData service UPS_DEF_EVENT has to be registered</p>\n<p>Gateway Role                        SAP_BR_DEFERMENT_ANALYST_IOG has to be added</p>\n<p>Additional information about the Fiori application can be found in the <a href=\"https://fioriappslibrary.hana.ondemand.com/sap/fix/externalViewer/\" target=\"_blank\">SAP Fiori apps reference library</a></p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Fiori, Design, UI, APP, UPS_DEFER_EVTS1, SAP Oil &amp; Gas, UOM, Upstream Operations Management, View Deferment Events.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>Please check the release information note 2346431 for product version 'S/4HANA OP 1610' and 2356208  for product version 'Fiori for S/4HANA OP 1610' for general installation and infrastructure topics.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Apply the Stack FPS2  of productversion S/4HANA OP 1610 on your Frontend Server system</p>\n<div class=\"longtext\">\n<div class=\"longtext\">\n<p>Apply the Stack FPS2  of productversion S/4HANA OP 1610 on the assigned  Backend systems</p>\n</div>\n</div>", "noteVersion": 2}, {"note": "2443853", "noteTitle": "2443853 - Release Information Note for the Fiori application Approve and Publish Forecast 05/2017", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>This note provides additional information regarding the Fiori application Approve and Publish Forecast in the area of Oil &amp; Gas Upstream Operations Management (UOM). This note contains the minimum required and recommended fixes that should be installed to make the app functional either in the frontend server or in the related backend server.</p>\n<div class=\"sapUiRespGridSpanL8 sapUiRespGridSpanM8 sapUiRespGridSpanS12 sapUiRespGridSpanXL8\">\n<div id=\"__html2\">\n<p class=\"- topic/p\">With the transactional app Approve and Publish Forecast you, in your role as a Forecast Manager (OG), can approve or reject forecast results of forecast projects. You can filter your authorized forecast projects by name, effective dates, scenario status, forecast type, and forecast date.</p>\n<p class=\"- topic/p\">Key Features</p>\n<ul>\n<li>View Forecast results of forecast projects and entering comments for scenarios and networks</li>\n<li>Approve or reject forecast results of forecast projects with final comment</li>\n</ul>\n<p>This note is subject to change. Ensure you check it on a regular basis.</p>\n<p>New or signficant enhancements of delivered Fiori applications for area Approve and Publish Forecast are delivered with Stack FPS2 of productversion S/4HANA OP 1610</p>\n<p>Following Fiori apps are affected:</p>\n<p><strong>Fiori application                              Status</strong></p>\n<p>Approve and Publish Forecast           New; OData service UPS_FC_APPR_PUB has to be registered.</p>\n<p>Gateway Role                                 SAP_BR_FORECAST_MANAGER_IOG has to be added</p>\n<p>Additional information about the Fiori application can be found in the <a href=\"https://fioriappslibrary.hana.ondemand.com/sap/fix/externalViewer/\" target=\"_blank\">SAP Fiori apps reference library</a></p>\n</div>\n</div>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Fiori, Design, UI, APP, UPS_FC_APFCSTS1, SAP Oil &amp; Gas, UOM, Upstream Operations Management, Forecasting, Approve and Publish Forecast</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>Please check the release information note 2346431 for product version 'S/4HANA OP 1610' and 2356208  for product version 'Fiori for S/4HANA OP 1610' for general installation and infrastructure topics.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<div class=\"longtext\">\n<p>Apply the Stack FPS2 of productversion S/4HANA OP 1610 on your Frontend Server system</p>\n<div class=\"longtext\">\n<div class=\"longtext\">\n<div class=\"longtext\">\n<p>Apply the Stack FPS2 of productversion S/4HANA OP 1610 on the assigned  Backend systems</p>\n</div>\n</div>\n</div>\n</div>", "noteVersion": 2}, {"note": "2443852", "noteTitle": "2443852 - Release Information Note for the Fiori application Calculate Forecast 05/2017", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>This note provides additional information regarding the Fiori application Calculate Forecast in the area of Oil &amp; Gas Upstream Operations Management (UOM). This note contains the minimum required and recommended fixes that should be installed to make the app functional either in the frontend server or in the related backend server.</p>\n<div class=\"sapUiRespGridSpanL8 sapUiRespGridSpanM8 sapUiRespGridSpanS12 sapUiRespGridSpanXL8\">\n<div id=\"__html2\">\n<p class=\"- topic/p\">With the transactional app Calculate Forecast you, in your role as a Forecast Specialist (OG), can calculate forecast results for scenarios in forecast projects, view forecast results, and submit forecast projects for further approval. You can filter your authorized forecast projects by name and effective dates.</p>\n<p class=\"- topic/p\">Key Features:</p>\n<ul>\n<li>Determine readiness (that is, Pre-Processing) for scenarios of forecast projects</li>\n<li>Calculate forecast for scenarios</li>\n<li>View forecast results for scenarios</li>\n<li>Submit forecast results of projects for approval</li>\n</ul>\n<div class=\"longtext\">\n<div class=\"sapUiRespGridSpanL8 sapUiRespGridSpanM8 sapUiRespGridSpanS12 sapUiRespGridSpanXL8\">\n<div id=\"__html2\">\n<p>This note is subject to change. Ensure you check it on a regular basis.</p>\n<p>New or signficant enhancements of delivered Fiori applications for area Calculate Forecast are delivered with Stack FPS2 of productversion S/4HANA OP 1610</p>\n<p>Following Fiori apps are affected:</p>\n<p><strong>Fiori application             Status</strong></p>\n<p>Calculate Forecast          New; OData service UPS_FC_CALC_FCST has to be registered.</p>\n<p>Gateway Role                SAP_BR_FORECAST_SPECIALIST_IOG has to be added</p>\n<p>Additional information about the Fiori application can be found in the <a href=\"https://fioriappslibrary.hana.ondemand.com/sap/fix/externalViewer/\" target=\"_blank\">SAP Fiori apps reference library</a></p>\n</div>\n</div>\n</div>\n</div>\n</div>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Fiori, Design, UI, APP, UPS_FC_CALFCTS1, SAP Oil &amp; Gas, UOM, Upstream Operations Management, Forecasting, Calculate Forecast.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>Please check the release information note 2346431 for product version 'S/4HANA OP 1610' and 2356208  for product version 'Fiori for S/4HANA OP 1610' for general installation and infrastructure topics.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Apply the Stack FPS2 of productversion S/4HANA OP 1610 on your Frontend Server system</p>\n<div class=\"longtext\">\n<div class=\"longtext\">\n<div class=\"longtext\">\n<p>Apply the Stack FPS2 of productversion S/4HANA OP 1610 on the assigned  Backend systems</p>\n</div>\n</div>\n</div>", "noteVersion": 2}, {"note": "2443854", "noteTitle": "2443854 - Release Information Note for the Fiori application Upload Production Data 05/2017", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>This note provides additional information regarding the Fiori application Upload Production Data in the area of Oil &amp; Gas Upstream Operations Management (UOM). This note contains the minimum required and recommended fixes that should be installed to make the app functional either in the frontend server or in the related backend server.</p>\n<div class=\"sapUiRespGridSpanL8 sapUiRespGridSpanM8 sapUiRespGridSpanS12 sapUiRespGridSpanXL8\">\n<div id=\"__html2\">\n<p class=\"- topic/p\">With the transactional app Upload Production Data you, in your role as a Production Data Specialist (OG), can upload production data (for example forecast project, forecast scenario, forecast constraints, forecast results and so on) for Upstream Operations Management. You can also filter your batch job logs by run ID, run date, object type, and file processing status.</p>\n<p class=\"- topic/p\">Key Features:</p>\n<ul>\n<li>Upload files of different object types (for example forecast projects, forecast scenarios and so on) from presentation server to application server</li>\n<li>Import files from application server to upload production data</li>\n<li>Filter batch job logs by run ID, run date, object type, and file processing status</li>\n<li>View job logs of files</li>\n</ul>\n<p>This note is subject to change. Ensure you check it on a regular basis.</p>\n<p>New or signficant enhancements of delivered Fiori applications for area Upload Production Data are delivered with Stack FPS2 of productversion S/4HANA OP 1610</p>\n<p>Following Fiori apps are affected:</p>\n<p><strong>Fiori application                      Status</strong></p>\n<p>Upload Production Data              New; OData service UPS_BULK_UPLD has to be registered.</p>\n<p>Gateway Role                            SAP_BR_PROD_DATA_SPEC_IOG has to be added</p>\n<p>Additional information about the Fiori application can be found in the <a href=\"https://fioriappslibrary.hana.ondemand.com/sap/fix/externalViewer/\" target=\"_blank\">SAP Fiori apps reference library</a></p>\n</div>\n</div>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Fiori, Design, UI, APP, UPS_BLKUPLOADS1, SAP Oil &amp; Gas, UOM, Upstream Operations Management, Forecasting, Upload Production Data</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>Please check the release information note 2346431 for product version 'S/4HANA OP 1610' and 2356208  for product version 'Fiori for S/4HANA OP 1610' for general installation and infrastructure topics.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Apply the Stack FPS2 of productversion S/4HANA OP 1610 on your Frontend Server system</p>\n<div class=\"longtext\">\n<div class=\"longtext\">\n<div class=\"longtext\">\n<p>Apply the Stack FPS2 of productversion S/4HANA OP 1610 on the assigned  Backend systems</p>\n</div>\n</div>\n</div>", "noteVersion": 2}, {"note": "2443808", "noteTitle": "2443808 - Release Information Note for the Fiori application Manage Forecast Access 05/2017", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>This note provides additional information regarding the Fiori application Manage Forecast Access in the area of Oil &amp; Gas Upstream Operations Management (UOM). This note contains the minimum required and recommended fixes that should be installed to make the app functional either in the frontend server or in the related backend server.</p>\n<div class=\"sapUiRespGridSpanL8 sapUiRespGridSpanM8 sapUiRespGridSpanS12 sapUiRespGridSpanXL8\">\n<div id=\"__html2\">\n<p class=\"- topic/p\">With the transactional app Manage Forecast Access you, in your role as a Forecast Specialist, can manage access (that is, grant or revoke access) of users to different processes (that is, delegation, data gathering, viewing, and analysis of forecast results and forecast approval) of forecast for forecast projects. You can filter your authorized forecast projects by project name, forecast type and frequency.</p>\n<p class=\"- topic/p\">Key Features</p>\n<ul>\n<li>Maintain User Groups in Forecast Projects</li>\n<li>Assign Users to User Groups</li>\n<li>Assign sub-user Groups to User Groups</li>\n<li>Maintain Object Groups in Forecast Projects</li>\n<li>Assign Objects to Object groups</li>\n<li>Assign sub-object Groups to Object Groups</li>\n<li>Assign Users and sub-groups to special User Groups 'Forecast Coordinators' and 'Forecast Stakeholder'</li>\n<li>Maintain Access Rights for Data Gathering and Forecast Approval</li>\n</ul>\n<p>This note is subject to change. Ensure you check it on a regular basis.</p>\n<p>New or signficant enhancements of delivered Fiori applications for area Manage Forecast Access are delivered with Stack FPS2 of productversion S/4HANA OP 1610</p>\n<p>Following Fiori apps are affected:</p>\n<p>Fiori application                      Status</p>\n<p>Manage Forecast Access          New; OData service UPS_FC_MNG_ACCESS has to be registered.</p>\n<p>Gateway Role                         SAP_BR_FORECAST_SPECIALIST_IOG has to be added</p>\n<p>Additional information about the Fiori application can be found in the <a href=\"https://fioriappslibrary.hana.ondemand.com/sap/fix/externalViewer/\" target=\"_blank\">SAP Fiori apps reference library</a></p>\n</div>\n</div>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Fiori, Design, UI, APP, UPS_FC_ACCESSS1, SAP Oil &amp; Gas, UOM, Upstream Operations Management, Forecasting, Manage Forecast Access.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>Please check the release information note 2346431 for product version 'S/4HANA OP 1610' and 2356208  for product version 'Fiori for S/4HANA OP 1610' for general installation and infrastructure topics.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Apply the Stack FPS2 of productversion S/4HANA OP 1610 on your Frontend Server system</p>\n<div class=\"longtext\">\n<div class=\"longtext\">\n<div class=\"longtext\">\n<p>Apply the Stack FPS2 of productversion S/4HANA OP 1610 on the assigned  Backend systems</p>\n</div>\n</div>\n</div>", "noteVersion": 2}, {"note": "2471863", "noteTitle": "2471863 - Missing Alerts and Application log objects control data after system conversion", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>After the conversion to S4HANA 1610 SP02, you are facing issues in Allocate Production application. The error alerts are not shown by the Fiori app. Also the preprocessing and allocation cannot be run successfully and invalid errors generated message appears.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>UOM, S4HANA, OP1610, FPS2, Allocate Production, Alerts</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>Due to missing control data in tables BALOBJ, BALSUB, SALRTCAT and SALRTCAT the application is not able to work as expected.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Please implement the attached correction and then perform the manual instructions from this SAP note.</p>", "noteVersion": 1}, {"note": "2471826", "noteTitle": "2471826 - BC set inconsistency", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<div class=\"WordSection1\">\n<p>During the conversion to S4HANA 1610 SP02 onwards or S4HANA 1709, you are facing BC set inconsistency issue in phase MAIN_NEWBAS/XPRAS_AIMMRG for BC sets GHO_SWBC_ALERT_CAT_CI3 or EAMS_KPI_T_EAMS_KPIMETADATA. Or after a new installation in Fix Errors app for fixing well test error you are not able to navigate to the Field Data Capture app.</p>\n</div>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>GHO_SWBC_ALERT_CAT_CI3, EAMS_KPI_T_EAMS_KPIMETADATA, S4HANA, OP1610, OP1709, UOM</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>For OP1610 you get the error because release validity of the BC set GHO_SWBC_ALERT_CAT_CI3 is IS-PRA 802 and above, whereas for OP1610 the software component version is 801. During conversion to 1610 or 1709 you might get an error in BC set EAMS_KPI_T_EAMS_KPIMETADATA because of inconsistency.</p>\n<p>The navigation issue from Fix Error to Field Data Capture app is because of missing records in BC set GHO_SWBC_ALERT_CAT_CI3.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<div class=\"WordSection1\">\n<p>If the above error occurs during SUM-run in phase XPRAS_AIMMRG, you may ignore this error and fix the issue after the conversion.</p>\n<p>After the upgrade please implement the attached correction and perform the manual instructions from this SAP note.</p>\n</div>", "noteVersion": 7}, {"note": "2443806", "noteTitle": "2443806 - Release Information Note for the Fiori application Gather Forecast Data 05/2017", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>This note provides additional information regarding the Fiori application Gather Forecast Data in the area of Oil &amp; Gas Upstream Operations Management (UOM). This note contains the minimum required and recommended fixes that should be installed to make the app functional either in the frontend server or in the related backend server.</p>\n<div class=\"sapUiRespGridSpanL8 sapUiRespGridSpanM8 sapUiRespGridSpanS12 sapUiRespGridSpanXL8\">\n<div id=\"__html2\">\n<p class=\"- topic/p\">With the transactional app Gather Forecast Data you, in your role as a Field Operator, Deferment Analyst or Hydrocarbon Analyst can maintain constraints (that is, potential, capacity, risk, opportunity and events) at network objects of scenarios of forercast projects. You can filter your authorized forecast projects by name and effective dates.</p>\n<p class=\"- topic/p\">Key Features</p>\n<ul>\n<li>Maintain Potential Constraints</li>\n<li>Maintain Capacity Constraints</li>\n<li>Maintain Risk Constraints</li>\n<li>Maintain Opportunity Constraints</li>\n<li>Maintain Event Constraints</li>\n<li>Complete Data Gathering for Scenarios</li>\n</ul>\n<p>This note is subject to change. Ensure you check it on a regular basis.</p>\n<p>New or signficant enhancements of delivered Fiori applications for area Gather Forecast Data are delivered with Stack FPS2 of productversion S/4HANA OP 1610</p>\n<p>Following Fiori apps are affected:</p>\n<p><strong>Fiori application                  Status</strong></p>\n<p>Gather Forecast Data              New; OData service UPS_FC_GATHERDATA has to be registered.</p>\n<p>Gateway Role                         SAP_BR_FORECAST_SPECIALIST_IOG has to be added</p>\n<p>Additional information about the Fiori application can be found in the <a href=\"https://fioriappslibrary.hana.ondemand.com/sap/fix/externalViewer/\" target=\"_blank\">SAP Fiori apps reference library</a></p>\n</div>\n</div>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Fiori, Design, UI, APP, UPS_FC_GHDATAS1, SAP Oil &amp; Gas, UOM, Upstream Operations Management, Forecasting, Gather Forecast Data</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>Please check the release information note 2346431 for product version 'S/4HANA OP 1610' and 2356208  for product version 'Fiori for S/4HANA OP 1610' for general installation and infrastructure topics.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Apply the Stack FPS2 of productversion S/4HANA OP 1610 on your Frontend Server system</p>\n<div class=\"longtext\">\n<div class=\"longtext\">\n<div class=\"longtext\">\n<p>Apply the Stack FPS2 of productversion S/4HANA OP 1610 on the assigned  Backend systems</p>\n</div>\n</div>\n</div>", "noteVersion": 2}, {"note": "2443807", "noteTitle": "2443807 - Release Information Note for the Fiori application Analyze Allocation Production 05/2017", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>This note provides additional information regarding the Fiori application Analyze Allocation Production in the area of Oil &amp; Gas Upstream Operations Management (UOM). This note contains the minimum required and recommended fixes that should be installed to make the app functional either in the frontend server or in the related backend server.</p>\n<p>With Analyze Production Allocation app, a Hydrocarbon Analyst can view the aggregated and the node level allocation results of the production networks. The allocation results are displayed based on the different mediums. The results display the Theoretical and Allocated volumes for the selected mediums. You can perform the approval actions using this app. This app displays the results in tabular and graphical views.</p>\n<p>Key Features:</p>\n<ul>\n<li>View the aggregated Allocation Results</li>\n<li>View the Nodel level Allocation Results</li>\n<li>Approve Allocation Results</li>\n<li>View in Tabular and Graphical view</li>\n<li>View the allocation factors at node level</li>\n</ul>\n<p>This note is subject to change. Ensure you check it on a regular basis.</p>\n<p>New or signficant enhancements of delivered Fiori applications for area Analyze Allocation Production are delivered with Stack FPS2 of productversion S/4HANA OP 1610</p>\n<p>Following Fiori apps are affected:</p>\n<p><strong>Fiori application                            Status</strong></p>\n<p>Analyze Allocation Production         New; OData service UPS_HCA_RESULT has to be registered</p>\n<p>Gateway Role                                SAP_BR_FORECAST_SPECIALIST_IOG has to be added</p>\n<p>Additional information about the FIORI application can be found in the <a href=\"https://fioriappslibrary.hana.ondemand.com/sap/fix/externalViewer/\" target=\"_blank\">SAP Fiori apps reference library</a></p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Fiori, Design, UI, APP, UPS_ALLOC_RESS1, SAP Oil &amp; Gas, UOM, Upstream Operations Management, Analyze Allocation Production.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>Please check the release information note 2346431 for product version 'S/4HANA OP 1610' and 2356208  for product version 'Fiori for S/4HANA OP 1610' for general installation and infrastructure topics.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Apply the Stack FPS2 of productversion S/4HANA OP 1610 on your Frontend Server system</p>\n<div class=\"longtext\">\n<div class=\"longtext\">\n<div class=\"longtext\">\n<p>Apply the Stack FPS2 of productversion S/4HANA OP 1610 on the assigned  Backend systems</p>\n</div>\n</div>\n</div>", "noteVersion": 2}, {"note": "2443855", "noteTitle": "2443855 - Release Information Note for the Fiori application Analyze and Compare Results 05/2017", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>This note provides additional information regarding the Fiori application Analyze and Compare Results in the area of Oil &amp; Gas Upstream Operations Management (UOM). This note contains the minimum required and recommended fixes that should be installed to make the app functional either in the frontend server or in the related backend server.</p>\n<p>With the analytical app 'Analyze and Compare Results' you, in your role as a Forecast Analyst - Production (IOG) or Forecast Specialist (IOG) or Forecast Manager (IOG), can analyze forecast results. You can use this app to perform step-by-step analysis of production results by looking at various measures and dimensions from different perspectives.</p>\n<div class=\"sapUiRespGridSpanL8 sapUiRespGridSpanM8 sapUiRespGridSpanS12 sapUiRespGridSpanXL8\">\n<div id=\"__html2\">\n<p>Key Features:</p>\n<ul>\n<li>Creating an Analysis Path</li>\n<li>Working with Analysis Path</li>\n<li>Making Selections in Charts</li>\n<li>Visualize and compare results across Project Scenarios, Networks and Network Objects</li>\n<li>Enhancing the Application using the APF Configuration Modeler</li>\n</ul>\n<p>This note is subject to change. Ensure you check it on a regular basis.</p>\n<p>New or signficant enhancements of delivered Fiori applications for area Analyze and Compare Results are delivered with Stack FPS2 of productversion S/4HANA OP 1610</p>\n<p>Following Fiori apps are affected:</p>\n<p><strong>Fiori application                              Status</strong></p>\n<p>Analyze and Compare Results             New; OData services C_GHOAPFFILTER_VH_CDS, C_GHOFCPROJRESACT_CDS, C_GHOFCPROJRESACTNWLVL_CDS has to be registered.</p>\n<p>Gateway Role                                   SAP_BR_FC_ANALYST_PROD_IOG has to be added</p>\n<p>Additional information about the Fiori application can be found in the <a href=\"https://fioriappslibrary.hana.ondemand.com/sap/fix/externalViewer/\" target=\"_blank\">SAP Fiori apps reference library</a></p>\n</div>\n</div>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Fiori, Design, UI, APP, UPS_FC_CMPRESS1, SAP Oil &amp; Gas, UOM, Upstream Operations Management, Forecasting, Analyze and Compare Results</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>Please check the release information note 2346431 for product version 'S/4HANA OP 1610' and 2356208  for product version 'Fiori for S/4HANA OP 1610' for general installation and infrastructure topics.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Apply the Stack FPS2 of productversion S/4HANA OP 1610 on your Frontend Server system</p>\n<div class=\"longtext\">\n<div class=\"longtext\">\n<div class=\"longtext\">\n<p>Apply the Stack FPS2 of productversion S/4HANA OP 1610 on the assigned  Backend systems</p>\n</div>\n</div>\n</div>", "noteVersion": 2}, {"note": "2346431", "noteTitle": "2346431 - SAP S/4HANA 1610: Release Information Note", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>This Release Information Note (RIN) contains information and references to notes for applying Feature Package (FP)/Support Package (SP) Stacks of product version 'SAP S/4HANA 1610'.</p>\n<p><strong><strong>Note</strong>:</strong> This SAP Note is subject to change. Check this note for changes on a regular basis. All important changes made after release of a Feature Package (FP)/Support Package (SP) Stack are documented in section \"Changes made after Release of FP/SP Stack &lt;xx&gt;\".</p>\n<p><br/><strong>GENERAL INFORMATION</strong></p>\n<p>SAP S/4HANA 1610 will be in Maintenance until 31.12.2021. For further information about the maintenance strategy please refer to SAP note <a href=\"/notes/52505\" target=\"_blank\">52505</a> and SAP note <a href=\"/notes/2311392\" target=\"_blank\">2900388</a>.<a href=\"https://i7p.wdf.sap.corp/sap(bD1lbiZjPTAwMQ==)/bc/bsp/sno/ui_entry/entry.htm?view=bsp&amp;param=69765F6D6F64653D3030312669765F7361706E6F7465735F6B65793D30313130303033353837303030303932373037303230303126766965773D627370\" target=\"_blank\"><br/></a></p>\n<p>We strongly recommend to upgrade your system to the latest SAP S/4HANA on-premise release.</p>\n<p><strong>General / Important Considerations</strong></p>\n<ul>\n<li>\n<p>Please refer to the specific documentation under section <a href=\"https://help.sap.com/viewer/product/SAP_S4HANA_ON-PREMISE/1909/en-US\" target=\"_blank\">product documentation</a>. We recommend setting up your system to display a dynamic documentation, which reflects the latest Feature or Support Package Stack of this release. If you want to stay on Feature Package Stack 00 to 02 for a longer period, and if you also want to display the corresponding documentation for one of these Feature Package Stacks, you will find all necessary information in SAP note <a href=\"/notes/2904428\" target=\"_blank\">2904428</a>.</p>\n</li>\n</ul>\n<ul>\n<li>The supported Kernel versions are: SAP KERNEL 7.49 64-BIT UNICODE and SAP KERNEL 7.53 64-BIT UNICODE.</li>\n<li>For general restrictions as well as restrictions for the conversion to SAP S/4HANA 1610, please refer to SAP note <a href=\"/notes/2333141\" target=\"_blank\">2333141</a>.</li>\n<li>For release information and restrictions related to country-specific localization features, please refer to SAP note <a href=\"/notes/2349004\" target=\"_blank\">2349004</a>.</li>\n<li>For the specific Front-end/UI for SAP S/4HANA 1610, please refer to SAP note <a href=\"/notes/2356208\" target=\"_blank\">2356208</a> which is the release information note for product version \"SAP FIORI FOR SAP S/4HANA 1610\".</li>\n<li>If you have add-ons installed on your system and/or want to use them on product version SAP S/4HANA 1610, please refer to SAP note <a href=\"/notes/2214409\" target=\"_blank\">2214409</a>.</li>\n<li>For process integration capabilities with other SAP on-premise solutions, please refer to SAP note <a href=\"/notes/2376061\" target=\"_blank\">2376061</a>.</li>\n<li>\n<p>Please be aware that SAP S/4HANA 1610 is an Unicode-only release. Non-Unicode systems are not supported anymore. Hence upgrades of non-Unicode systems without prior Unicode conversion is not possible. For details see <a href=\"https://service.sap.com/~sapidb/012002523100009958832014E/\" target=\"_blank\">Upgrade of non Unicode systems</a> or <a href=\"https://service.sap.com/Unicode\" target=\"_blank\">service.sap.com/Unicode </a>and SAP note <a href=\"/notes/2033243\" target=\"_blank\">2033243</a>.</p>\n</li>\n<li>A new inventory data model will be delivered. This new inventory data model has a lot of advantages at S4. With the S4 new inventory data model delta stock quantities instead of total stock quantities are transferred to APO. At APO code changes need to be implemented to get this delta stock quantities processed. SAP Note 2816388 contains a list of all SAP Notes to be implemented at APO to realize these code changes.</li>\n<li><strong>United Kingdom leaving the EU</strong>: For information on how a “hard Brexit” (= a no-deal scenario) would impact your <em>SAP S/4HANA </em>system, please see SAP Note <strong><a href=\"/notes/2749671\" target=\"_blank\">2749671</a>.</strong></li>\n<li>For the implementation of ELSTER modules using the ERiC libraries and the corresponding impact on platform support, please see notes <a href=\"/notes/2745249\" target=\"_blank\">2745249</a> and <a href=\"/notes/2558316\" target=\"_blank\">2558316</a>.</li>\n<li>\n<p>For more information regarding LEGAL VAT TAX CHANGE in Germany<strong> , please see SAP note </strong><span><a href=\"/notes/2934992\" target=\"_blank\">2934992</a></span></p>\n</li>\n</ul>\n<p><strong><strong>Installation Information</strong></strong></p>\n<ul>\n<li>For the system installation, please refer to the <a href=\"https://uacp.hana.ondemand.com/http.svc/rc/PRODUCTION/pdf3be8f85500f17b43e10000000a4450e5/1610%20000/en-US/INST_OP1610.pdf\" target=\"_blank\">installation guide</a>.</li>\n<li>You need at least Software Provisioning Manager (SWPM) Support Package 20 for the installation. Please make sure that you always use the latest patch level of SWPM available at <a href=\"https://support.sap.com/software/sltoolset.html\" target=\"_blank\">support.sap.com/software/sltoolset.html</a>.</li>\n</ul>\n<p><strong><strong><strong><strong>Upgrade Information</strong></strong></strong></strong></p>\n<ul>\n<li>You can upgrade from SAP S/4HANA, on-premise edition 1511 to SAP S/4HANA 1610.</li>\n<li>For the system upgrade, please refer to the <a href=\"https://uacp.hana.ondemand.com/http.svc/rc/PRODUCTION/pdfd61b9f57e5146b10e10000000a441470/1610%20000/en-US/UPGR_OP1610.pdf\" target=\"_blank\">upgrade guide</a>.</li>\n<li>You need at least Software Update Manager (SUM) Support Package 20 for the upgrade. Please make sure that you always use the latest patch level of SUM available at <a href=\"https://support.sap.com/software/sltoolset.html\" target=\"_blank\">support.sap.com/software/sltoolset.html</a>.</li>\n</ul>\n<p><strong>SAP HANA database requirements</strong></p>\n<ul>\n<li>The minimum required revision is defined in the respective Support or Feature Package Stack chapter.</li>\n<li>Detailed information about SAP HANA 2.0 Revision and Maintenance Strategy can be found in SAP note <a href=\"/notes/2378962\" target=\"_blank\">2378962</a>.</li>\n<li>If you plan to upgrade your SAP HANA database to a newer revision or a newer available SPS level,</li>\n<ul>\n<li>Refer to SAP note <a href=\"/notes/1906576\" target=\"_blank\">1906576</a> for SAP HANA client and server cross-version compatibility and</li>\n<li>Refer to SAP note <a href=\"/notes/2655761\" target=\"_blank\">2655761</a> for restrictions and recommendations regarding specific revisions of SAP HANA database for use in SAP S/4HANA.</li>\n</ul>\n</ul>\n<p><strong><strong>Conversion Information</strong></strong></p>\n<ul>\n<li>\n<p><em>Please note that a system conversion to SAP S/4HANA 1511 and 1610 is no longer possible or supported</em><em></em></p>\n<ul>\n<li><em>for newly starting </em><em>system conversion </em><em>projects – independently of the Enhancement Package or SP level of the </em><em>SAP ERP source system.</em></li>\n<li>for any system which is below the following SAP ERP SP levels and have to go to S/4HANA 1511 and 1610, open a message on CA-TRS-PRCK</li>\n<li><em>for any system which is on the following SAP ERP SP levels or higher: 600 SP30, 602 SP20, 603 SP19, 604 SP20, 605 SP17, 606 SP20, 616 SP12, 617 SP15, 618 SP09</em></li>\n</ul>\n<p><em>You can convert to the successor product versions of SAP S/4HANA.</em></p>\n</li>\n</ul>\n<p><strong>Feature Package Update (via Software Update Manager (SUM))</strong></p>\n<ul>\n<li>It is recommended to use SUM to apply Feature Package Stacks.</li>\n</ul>\n<p><strong><strong>Support Package Stacks on SAP Support Portal</strong></strong></p>\n<p>You will find general information about Support Package Stacks on SAP Support Portal at <a href=\"https://support.sap.com/software/patches/stacks.html\" target=\"_blank\">support.sap.com/software/patches/stacks.html</a>. The Schedule for Support Package Stacks is available at <a href=\"http://support.sap.com/maintenance-schedule\" target=\"_blank\">support.sap.com/maintenance-schedule</a>.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>S/4HANA 1610</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>You want to get additional information about product version SAP S/4HANA 1610.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong>SUPPORT PACKAGE STACK 11 (10/2021)</strong></p>\n<p><strong>General / Important Considerations</strong></p>\n<ul>\n<li>Please refer to the specific documentation under section <a href=\"https://help.sap.com/viewer/product/SAP_S4HANA_ON-PREMISE/1610%20002/en-US\" target=\"_blank\">product documentation</a>.</li>\n<li>SAP HANA 1.0</li>\n<ul>\n<li>The minimum required revision of SAP HANA 1.0 is Revision 122.27 SAP in general recommends customers to implement the highest SAP HANA 1.0 revision available. For more details, please refer to note <a href=\"/notes/2021789\" target=\"_blank\">2021789</a> (SAP HANA 1.0 Revision and Maintenance Strategy).</li>\n</ul>\n<li>SAP HANA 2.0</li>\n<ul>\n<li>The minimum required revision of SAP HANA 2.0 is SPS02 Revision 24. SAP in general recommends customers to implement the highest SAP HANA 2.0 revision available. Please take in account that the minimum required revision is out of maintenance. For more details, please refer to note <a href=\"/notes/2378962\" target=\"_blank\">2378962</a> (SAP HANA 2.0 Revision and Maintenance Strategy).</li>\n<li>Please refer to note <a href=\"/notes/2426477\" target=\"_blank\" title=\"2426477  - Support for SAP HANA 2 in SAP S/4HANA 1610 - Status of application support for specific functionalities of SAP HANA 2\">2426477</a> which describes new features of SAP HANA 2.0 already been adopted by SAP S/4HANA 1610 and note <a href=\"/notes/2426339\" target=\"_blank\" title=\"2426339  - Support for SAP HANA 2 in SAP S/4HANA - Technical Information Regarding SAP HANA Requirements\">2426339 </a>which informs about technical requirements to the database system (hardware, operating system, etc.) introduced with SAP HANA 2.0 that may be relevant if you plan to operate your SAP S/4HANA 1610 system on SAP HANA 2.0.</li>\n<li>Furthermore, refer to the general restriction note <a href=\"/notes/2333141\" target=\"_blank\">2333141</a> also for restrictions relevant for SAP S/4HANA 1610 on HANA 2.0.</li>\n<li>Please refer to note <a href=\"/notes/2487855\" target=\"_blank\">2487855</a> (Possible Table Inconsistencies on SAP HANA 2.0 SPS01 due to Internal Persistence Structure Migration) if you are on SAP HANA 2.0 Revision 010.00 or 011.00 (SPS01) or if you plan to upgrade to these SPS01 revisions. </li>\n<li>SAP HANA 2.0 SPS 02 Revision 20: Please check SAP note <a href=\"/notes/2527538\" target=\"_blank\">2527538</a> (AMDP Procedure Execution Fails With Column Store Error) and SAP note <a href=\"/notes/2527648\" target=\"_blank\">2527648</a> (Column Value Equals Null Cannot be Detected by IS NULL in Debug Mode).</li>\n</ul>\n<li>For the specific Front-end/UI for SAP S/4HANA 1610, please refer to SAP note <a href=\"/notes/2356208\" target=\"_blank\">2356208</a> which is the release information note for product version SAP FIORI FOR SAP S/4HANA 1610. Support Package Stack 03 of SAP S/4HANA 1610 in the backend requires Support Package Stack 01 or higher of SAP Fiori 2.0 for SAP S/4HANA on the frontend (and vice versa).</li>\n<li>Please refer to note <a href=\"/notes/2539548\" target=\"_blank\">2539548</a> (SAP S/4HANA, on-premise edition 1610 - SAP S/4HANA ON-PREMISE 1610 SP stack 03 (10/2017) content activation note) when you are using the SAP Best Practices for your SAP S/4HANA on premise implementation and you need current information for the tool and the content.</li>\n<li>Visual theme “Belize” for SAP GUI for Windows”:<br/>For the usage of SAP S/4HANA 1610 Support Package Stack 03 with SAP GUI for Windows there are few prerequisites:</li>\n<ul>\n<li>Installation of SAP GUI for Windows 7.50 or higher (desktop client)</li>\n<li>Installation of SAP Kernel 7.49 Patch 210 or higher</li>\n</ul>\n<li>If you would like to use a Fiori Launchpad (FLP) with integrated SAP GUI for Windows applications, please install SAP Business Client 6.5. Please refer to note <a href=\"/notes/2348661\" target=\"_blank\">2348661</a> - Restrictions of SAP Fiori visual theme for classic applications.</li>\n</ul>\n<p><strong>Installation Requirements</strong></p>\n<ul>\n<li>For the system installation, please refer to the <a href=\"https://help.sap.com/doc/14f9236f8e234dccbb195259a8eaf616/1610%20002/en-US/INST_OP1610_FPS02.pdf\" target=\"_blank\">installation guide</a>.</li>\n<li>You need at least Software Provisioning Manager (SWPM) Support Package 21 for the installation. Please make sure that you always use the latest patch level of SWPM available at <a href=\"https://support.sap.com/software/sltoolset.html\" target=\"_blank\">support.sap.com/software/sltoolset.html</a>.</li>\n<li>Multi-Node (Scale-Out) Support possibilities are described in note <a href=\"/notes/2408419\" target=\"_blank\">2408419</a>.</li>\n<li>\n<p><em>Please refer to SAP note <a href=\"/notes/306695\" target=\"_blank\">306695</a> ('Field EMDST13 is unknown' after installing IS-U/CCS) after installation.</em></p>\n</li>\n</ul>\n<p><strong>Upgrade Requirements</strong></p>\n<ul>\n<li>You can upgrade from SAP S/4HANA, on-premise edition 1511 to SAP S/4HANA 1610. <br/>Please be aware that your software level is not higher than Support Package Stack 11.</li>\n<li>For the system upgrade, please refer to the <a href=\"https://help.sap.com/doc/31afbde9e69047298073148662e8aeb2/1610%20002/en-US/UPGR_OP1610_FPS02.pdf\" target=\"_blank\">upgrade guide</a>.</li>\n<li>You need at least Software Update Manager (SUM) 2.0 for the upgrade. Please make sure that you always use the latest patch level of SUM available at <a href=\"https://support.sap.com/software/sltoolset.html\" target=\"_blank\">support.sap.com/software/sltoolset.html</a>.</li>\n<li>If you encounter sequence errors during DDIC_ACTIVATION, please repeat the phase.</li>\n</ul>\n<p><strong>Support Package Update (via Software Update Manager (SUM)):</strong></p>\n<ul>\n<li>If you are using SPAM (instead of SUM) for the SP update to SAP S/4HANA 1610 Support Package Stack 04, please apply note 2603860 (Data Dictionary: Wrong calculation of RC in mass activation) to avoid errors during DDIC_ACTIVATION, before you start the update.</li>\n<li>If the SP-import with SPAM stops in import phase AUTO_MOD_SPAU and if a short dump of type “LOAD_PROGRAM_INTF_MISMATCH” is present in ST22 at the respective import time, please repeat the import phase once. In case repeating the phase does not solve the issue, please contact the SAP Support.</li>\n<li>If you encounter sequence errors during DDIC_ACTIVATION of type “specify reference table AND reference field”, please repeat the import phase once. In case repeating the phase does not solve the issue, please contact the SAP Support.</li>\n<li>Specific errors which might occur if certain Business Function (Sets) are activated</li>\n<li>If IS-Business Function Sets for \"TELCO\" is activated and if you use the generation option in SPAM for the SP update, you might encounter generation errors during SP import of type  “The data object \"LS_xxx\" does not have a component called \"/SAPCE/x\". In this case, please use “Ignore generation errors”-option in SPAM and restart the SP update.</li>\n</ul>\n<p><strong>Notes to be applied on top of Support Package 11:</strong></p>\n<p><strong>SUPPORT PACKAGE STACK 10 (04/2021)</strong></p>\n<p><strong>General / Important Considerations</strong></p>\n<ul>\n<li>Please refer to the specific documentation under section <a href=\"https://help.sap.com/viewer/product/SAP_S4HANA_ON-PREMISE/1610%20002/en-US\" target=\"_blank\">product documentation</a>.</li>\n<li>SAP HANA 1.0</li>\n<ul>\n<li>The minimum required revision of SAP HANA 1.0 is Revision 122.27 SAP in general recommends customers to implement the highest SAP HANA 1.0 revision available. For more details, please refer to note <a href=\"/notes/2021789\" target=\"_blank\">2021789</a> (SAP HANA 1.0 Revision and Maintenance Strategy).</li>\n</ul>\n<li>SAP HANA 2.0</li>\n<ul>\n<li>The minimum required revision of SAP HANA 2.0 is SPS02 Revision 24. SAP in general recommends customers to implement the highest SAP HANA 2.0 revision available. Please take in account that the minimum required revision is out of maintenance. For more details, please refer to note <a href=\"/notes/2378962\" target=\"_blank\">2378962</a> (SAP HANA 2.0 Revision and Maintenance Strategy).</li>\n<li>Please refer to note <a href=\"/notes/2426477\" target=\"_blank\" title=\"2426477  - Support for SAP HANA 2 in SAP S/4HANA 1610 - Status of application support for specific functionalities of SAP HANA 2\">2426477</a> which describes new features of SAP HANA 2.0 already been adopted by SAP S/4HANA 1610 and note <a href=\"/notes/2426339\" target=\"_blank\" title=\"2426339  - Support for SAP HANA 2 in SAP S/4HANA - Technical Information Regarding SAP HANA Requirements\">2426339 </a>which informs about technical requirements to the database system (hardware, operating system, etc.) introduced with SAP HANA 2.0 that may be relevant if you plan to operate your SAP S/4HANA 1610 system on SAP HANA 2.0.</li>\n<li>Furthermore, refer to the general restriction note <a href=\"/notes/2333141\" target=\"_blank\">2333141</a> also for restrictions relevant for SAP S/4HANA 1610 on HANA 2.0.</li>\n<li>Please refer to note <a href=\"/notes/2487855\" target=\"_blank\">2487855</a> (Possible Table Inconsistencies on SAP HANA 2.0 SPS01 due to Internal Persistence Structure Migration) if you are on SAP HANA 2.0 Revision 010.00 or 011.00 (SPS01) or if you plan to upgrade to these SPS01 revisions. </li>\n<li>SAP HANA 2.0 SPS 02 Revision 20: Please check SAP note <a href=\"/notes/2527538\" target=\"_blank\">2527538</a> (AMDP Procedure Execution Fails With Column Store Error) and SAP note <a href=\"/notes/2527648\" target=\"_blank\">2527648</a> (Column Value Equals Null Cannot be Detected by IS NULL in Debug Mode).</li>\n</ul>\n<li>For the specific Front-end/UI for SAP S/4HANA 1610, please refer to SAP note <a href=\"/notes/2356208\" target=\"_blank\">2356208</a> which is the release information note for product version SAP FIORI FOR SAP S/4HANA 1610. Support Package Stack 03 of SAP S/4HANA 1610 in the backend requires Support Package Stack 01 or higher of SAP Fiori 2.0 for SAP S/4HANA on the frontend (and vice versa).</li>\n<li>Please refer to note <a href=\"/notes/2539548\" target=\"_blank\">2539548</a> (SAP S/4HANA, on-premise edition 1610 - SAP S/4HANA ON-PREMISE 1610 SP stack 03 (10/2017) content activation note) when you are using the SAP Best Practices for your SAP S/4HANA on premise implementation and you need current information for the tool and the content.</li>\n<li>Visual theme “Belize” for SAP GUI for Windows”:<br/>For the usage of SAP S/4HANA 1610 Support Package Stack 03 with SAP GUI for Windows there are few prerequisites:</li>\n<ul>\n<li>Installation of SAP GUI for Windows 7.50 or higher (desktop client)</li>\n<li>Installation of SAP Kernel 7.49 Patch 210 or higher</li>\n</ul>\n<li>If you would like to use a Fiori Launchpad (FLP) with integrated SAP GUI for Windows applications, please install SAP Business Client 6.5. Please refer to note <a href=\"/notes/2348661\" target=\"_blank\">2348661</a> - Restrictions of SAP Fiori visual theme for classic applications.</li>\n</ul>\n<p><strong>Installation Requirements</strong></p>\n<ul>\n<li>For the system installation, please refer to the <a href=\"https://help.sap.com/doc/14f9236f8e234dccbb195259a8eaf616/1610%20002/en-US/INST_OP1610_FPS02.pdf\" target=\"_blank\">installation guide</a>.</li>\n<li>You need at least Software Provisioning Manager (SWPM) Support Package 21 for the installation. Please make sure that you always use the latest patch level of SWPM available at <a href=\"https://support.sap.com/software/sltoolset.html\" target=\"_blank\">support.sap.com/software/sltoolset.html</a>.</li>\n<li>Multi-Node (Scale-Out) Support possibilities are described in note <a href=\"/notes/2408419\" target=\"_blank\">2408419</a>.</li>\n<li>\n<p><em>Please refer to SAP note <a href=\"/notes/306695\" target=\"_blank\">306695</a> ('Field EMDST13 is unknown' after installing IS-U/CCS) after installation.</em></p>\n</li>\n</ul>\n<p><strong>Upgrade Requirements</strong></p>\n<ul>\n<li>You can upgrade from SAP S/4HANA, on-premise edition 1511 to SAP S/4HANA 1610. <br/>Please be aware that your software level is not higher than Support Package Stack 11.</li>\n<li>For the system upgrade, please refer to the <a href=\"https://help.sap.com/doc/31afbde9e69047298073148662e8aeb2/1610%20002/en-US/UPGR_OP1610_FPS02.pdf\" target=\"_blank\">upgrade guide</a>.</li>\n<li>You need at least Software Update Manager (SUM) 2.0 for the upgrade. Please make sure that you always use the latest patch level of SUM available at <a href=\"https://support.sap.com/software/sltoolset.html\" target=\"_blank\">support.sap.com/software/sltoolset.html</a>.</li>\n<li>If you encounter sequence errors during DDIC_ACTIVATION, please repeat the phase.</li>\n</ul>\n<p><strong>Support Package Update (via Software Update Manager (SUM)):</strong></p>\n<ul>\n<li>If you are using SPAM (instead of SUM) for the SP update to SAP S/4HANA 1610 Support Package Stack 04, please apply note 2603860 (Data Dictionary: Wrong calculation of RC in mass activation) to avoid errors during DDIC_ACTIVATION, before you start the update.</li>\n<li>If the SP-import with SPAM stops in import phase AUTO_MOD_SPAU and if a short dump of type “LOAD_PROGRAM_INTF_MISMATCH” is present in ST22 at the respective import time, please repeat the import phase once. In case repeating the phase does not solve the issue, please contact the SAP Support.</li>\n<li>If you encounter sequence errors during DDIC_ACTIVATION of type “specify reference table AND reference field”, please repeat the import phase once. In case repeating the phase does not solve the issue, please contact the SAP Support.</li>\n<li>Specific errors which might occur if certain Business Function (Sets) are activated</li>\n<li>If IS-Business Function Sets for \"TELCO\" is activated and if you use the generation option in SPAM for the SP update, you might encounter generation errors during SP import of type  “The data object \"LS_xxx\" does not have a component called \"/SAPCE/x\". In this case, please use “Ignore generation errors”-option in SPAM and restart the SP update.</li>\n</ul>\n<p><strong>Notes to be applied on top of Support Package 10:</strong></p>\n<p><strong>SUPPORT PACKAGE STACK 09 (10/2020)</strong></p>\n<p><strong>General / Important Considerations</strong></p>\n<ul>\n<li>Please refer to the specific documentation under section <a href=\"https://help.sap.com/viewer/product/SAP_S4HANA_ON-PREMISE/1610%20002/en-US\" target=\"_blank\">product documentation</a>.</li>\n<li>SAP HANA 1.0</li>\n<ul>\n<li>The minimum required revision of SAP HANA 1.0 is Revision 122.27 SAP in general recommends customers to implement the highest SAP HANA 1.0 revision available. For more details, please refer to note <a href=\"/notes/2021789\" target=\"_blank\">2021789</a> (SAP HANA 1.0 Revision and Maintenance Strategy).</li>\n</ul>\n<li>SAP HANA 2.0</li>\n<ul>\n<li>The minimum required revision of SAP HANA 2.0 is SPS02 Revision 24. SAP in general recommends customers to implement the highest SAP HANA 2.0 revision available. Please take in account that the minimum required revision is out of maintenance. For more details, please refer to note <a href=\"/notes/2378962\" target=\"_blank\">2378962</a> (SAP HANA 2.0 Revision and Maintenance Strategy).</li>\n<li>Please refer to note <a href=\"/notes/2426477\" target=\"_blank\" title=\"2426477  - Support for SAP HANA 2 in SAP S/4HANA 1610 - Status of application support for specific functionalities of SAP HANA 2\">2426477</a> which describes new features of SAP HANA 2.0 already been adopted by SAP S/4HANA 1610 and note <a href=\"/notes/2426339\" target=\"_blank\" title=\"2426339  - Support for SAP HANA 2 in SAP S/4HANA - Technical Information Regarding SAP HANA Requirements\">2426339 </a>which informs about technical requirements to the database system (hardware, operating system, etc.) introduced with SAP HANA 2.0 that may be relevant if you plan to operate your SAP S/4HANA 1610 system on SAP HANA 2.0.</li>\n<li>Furthermore, refer to the general restriction note <a href=\"/notes/2333141\" target=\"_blank\">2333141</a> also for restrictions relevant for SAP S/4HANA 1610 on HANA 2.0.</li>\n<li>Please refer to note <a href=\"/notes/2487855\" target=\"_blank\">2487855</a> (Possible Table Inconsistencies on SAP HANA 2.0 SPS01 due to Internal Persistence Structure Migration) if you are on SAP HANA 2.0 Revision 010.00 or 011.00 (SPS01) or if you plan to upgrade to these SPS01 revisions. </li>\n<li>SAP HANA 2.0 SPS 02 Revision 20: Please check SAP note <a href=\"/notes/2527538\" target=\"_blank\">2527538</a> (AMDP Procedure Execution Fails With Column Store Error) and SAP note <a href=\"/notes/2527648\" target=\"_blank\">2527648</a> (Column Value Equals Null Cannot be Detected by IS NULL in Debug Mode).</li>\n</ul>\n<li>For the specific Front-end/UI for SAP S/4HANA 1610, please refer to SAP note <a href=\"/notes/2356208\" target=\"_blank\">2356208</a> which is the release information note for product version SAP FIORI FOR SAP S/4HANA 1610. Support Package Stack 03 of SAP S/4HANA 1610 in the backend requires Support Package Stack 01 or higher of SAP Fiori 2.0 for SAP S/4HANA on the frontend (and vice versa).</li>\n<li>Please refer to note <a href=\"/notes/2539548\" target=\"_blank\">2539548</a> (SAP S/4HANA, on-premise edition 1610 - SAP S/4HANA ON-PREMISE 1610 SP stack 03 (10/2017) content activation note) when you are using the SAP Best Practices for your SAP S/4HANA on premise implementation and you need current information for the tool and the content.</li>\n<li>Visual theme “Belize” for SAP GUI for Windows”:<br/>For the usage of SAP S/4HANA 1610 Support Package Stack 03 with SAP GUI for Windows there are few prerequisites:</li>\n<ul>\n<li>Installation of SAP GUI for Windows 7.50 or higher (desktop client)</li>\n<li>Installation of SAP Kernel 7.49 Patch 210 or higher</li>\n</ul>\n<li>If you would like to use a Fiori Launchpad (FLP) with integrated SAP GUI for Windows applications, please install SAP Business Client 6.5. Please refer to note <a href=\"/notes/2348661\" target=\"_blank\">2348661</a> - Restrictions of SAP Fiori visual theme for classic applications.</li>\n</ul>\n<p><strong>Installation Requirements</strong></p>\n<ul>\n<li>For the system installation, please refer to the <a href=\"https://help.sap.com/doc/14f9236f8e234dccbb195259a8eaf616/1610%20002/en-US/INST_OP1610_FPS02.pdf\" target=\"_blank\">installation guide</a>.</li>\n<li>You need at least Software Provisioning Manager (SWPM) Support Package 21 for the installation. Please make sure that you always use the latest patch level of SWPM available at <a href=\"https://support.sap.com/software/sltoolset.html\" target=\"_blank\">support.sap.com/software/sltoolset.html</a>.</li>\n<li>Multi-Node (Scale-Out) Support possibilities are described in note <a href=\"/notes/2408419\" target=\"_blank\">2408419</a>.</li>\n<li>\n<p><em>Please refer to SAP note <a href=\"/notes/306695\" target=\"_blank\">306695</a> ('Field EMDST13 is unknown' after installing IS-U/CCS) after installation.</em></p>\n</li>\n</ul>\n<p><strong>Upgrade Requirements</strong></p>\n<ul>\n<li>You can upgrade from SAP S/4HANA, on-premise edition 1511 to SAP S/4HANA 1610. <br/>Please be aware that your software level is not higher than Support Package Stack 11.</li>\n<li>For the system upgrade, please refer to the <a href=\"https://help.sap.com/doc/31afbde9e69047298073148662e8aeb2/1610%20002/en-US/UPGR_OP1610_FPS02.pdf\" target=\"_blank\">upgrade guide</a>.</li>\n<li>You need at least Software Update Manager (SUM) 2.0 for the upgrade. Please make sure that you always use the latest patch level of SUM available at <a href=\"https://support.sap.com/software/sltoolset.html\" target=\"_blank\">support.sap.com/software/sltoolset.html</a>.</li>\n<li>If you encounter sequence errors during DDIC_ACTIVATION, please repeat the phase.</li>\n</ul>\n<p><strong>Support Package Update (via Software Update Manager (SUM)):</strong></p>\n<ul>\n<li>If you are using SPAM (instead of SUM) for the SP update to SAP S/4HANA 1610 Support Package Stack 04, please apply note 2603860 (Data Dictionary: Wrong calculation of RC in mass activation) to avoid errors during DDIC_ACTIVATION, before you start the update.</li>\n<li>If the SP-import with SPAM stops in import phase AUTO_MOD_SPAU and if a short dump of type “LOAD_PROGRAM_INTF_MISMATCH” is present in ST22 at the respective import time, please repeat the import phase once. In case repeating the phase does not solve the issue, please contact the SAP Support.</li>\n<li>If you encounter sequence errors during DDIC_ACTIVATION of type “specify reference table AND reference field”, please repeat the import phase once. In case repeating the phase does not solve the issue, please contact the SAP Support.</li>\n<li>Specific errors which might occur if certain Business Function (Sets) are activated</li>\n<li>If IS-Business Function Sets for \"TELCO\" is activated and if you use the generation option in SPAM for the SP update, you might encounter generation errors during SP import of type  “The data object \"LS_xxx\" does not have a component called \"/SAPCE/x\". In this case, please use “Ignore generation errors”-option in SPAM and restart the SP update.</li>\n</ul>\n<p><strong>Notes to be applied on top of Support Package 09:</strong></p>\n<p><strong>SUPPORT PACKAGE STACK 08 (04/2020)</strong></p>\n<p><strong>General / Important Considerations</strong></p>\n<ul>\n<li>Please refer to the specific documentation under section <a href=\"https://help.sap.com/viewer/product/SAP_S4HANA_ON-PREMISE/1610%20002/en-US\" target=\"_blank\">product documentation</a>.</li>\n<li>SAP HANA 1.0</li>\n<ul>\n<li>The minimum required revision of SAP HANA 1.0 is Revision 122.27 SAP in general recommends customers to implement the highest SAP HANA 1.0 revision available. For more details, please refer to note <a href=\"/notes/2021789\" target=\"_blank\">2021789</a> (SAP HANA 1.0 Revision and Maintenance Strategy).</li>\n</ul>\n<li>SAP HANA 2.0</li>\n<ul>\n<li>The minimum required revision of SAP HANA 2.0 is SPS02 Revision 24. SAP in general recommends customers to implement the highest SAP HANA 2.0 revision available. For more details, please refer to note <a href=\"/notes/2378962\" target=\"_blank\">2378962</a> (SAP HANA 2.0 Revision and Maintenance Strategy).</li>\n<li>Please refer to note <a href=\"/notes/2426477\" target=\"_blank\" title=\"2426477  - Support for SAP HANA 2 in SAP S/4HANA 1610 - Status of application support for specific functionalities of SAP HANA 2\">2426477</a> which describes new features of SAP HANA 2.0 already been adopted by SAP S/4HANA 1610 and note <a href=\"/notes/2426339\" target=\"_blank\" title=\"2426339  - Support for SAP HANA 2 in SAP S/4HANA - Technical Information Regarding SAP HANA Requirements\">2426339 </a>which informs about technical requirements to the database system (hardware, operating system, etc.) introduced with SAP HANA 2.0 that may be relevant if you plan to operate your SAP S/4HANA 1610 system on SAP HANA 2.0.</li>\n<li>Furthermore, refer to the general restriction note <a href=\"/notes/2333141\" target=\"_blank\">2333141</a> also for restrictions relevant for SAP S/4HANA 1610 on HANA 2.0.</li>\n<li>Please refer to note <a href=\"/notes/2487855\" target=\"_blank\">2487855</a> (Possible Table Inconsistencies on SAP HANA 2.0 SPS01 due to Internal Persistence Structure Migration) if you are on SAP HANA 2.0 Revision 010.00 or 011.00 (SPS01) or if you plan to upgrade to these SPS01 revisions. </li>\n<li>SAP HANA 2.0 SPS 02 Revision 20: Please check SAP note <a href=\"/notes/2527538\" target=\"_blank\">2527538</a> (AMDP Procedure Execution Fails With Column Store Error) and SAP note <a href=\"/notes/2527648\" target=\"_blank\">2527648</a> (Column Value Equals Null Cannot be Detected by IS NULL in Debug Mode).</li>\n</ul>\n<li>For the specific Front-end/UI for SAP S/4HANA 1610, please refer to SAP note <a href=\"/notes/2356208\" target=\"_blank\">2356208</a> which is the release information note for product version SAP FIORI FOR SAP S/4HANA 1610. Support Package Stack 03 of SAP S/4HANA 1610 in the backend requires Support Package Stack 01 or higher of SAP Fiori 2.0 for SAP S/4HANA on the frontend (and vice versa).</li>\n<li>Please refer to note <a href=\"/notes/2539548\" target=\"_blank\">2539548</a> (SAP S/4HANA, on-premise edition 1610 - SAP S/4HANA ON-PREMISE 1610 SP stack 03 (10/2017) content activation note) when you are using the SAP Best Practices for your SAP S/4HANA on premise implementation and you need current information for the tool and the content.</li>\n<li>Visual theme “Belize” for SAP GUI for Windows”:<br/>For the usage of SAP S/4HANA 1610 Support Package Stack 03 with SAP GUI for Windows there are few prerequisites:</li>\n<ul>\n<li>Installation of SAP GUI for Windows 7.50 or higher (desktop client)</li>\n<li>Installation of SAP Kernel 7.49 Patch 210 or higher</li>\n</ul>\n<li>If you would like to use a Fiori Launchpad (FLP) with integrated SAP GUI for Windows applications, please install SAP Business Client 6.5. Please refer to note <a href=\"/notes/2348661\" target=\"_blank\">2348661</a> - Restrictions of SAP Fiori visual theme for classic applications.</li>\n</ul>\n<p><strong>Installation Requirements</strong></p>\n<ul>\n<li>For the system installation, please refer to the <a href=\"https://help.sap.com/doc/14f9236f8e234dccbb195259a8eaf616/1610%20002/en-US/INST_OP1610_FPS02.pdf\" target=\"_blank\">installation guide</a>.</li>\n<li>You need at least Software Provisioning Manager (SWPM) Support Package 21 for the installation. Please make sure that you always use the latest patch level of SWPM available at <a href=\"https://support.sap.com/software/sltoolset.html\" target=\"_blank\">support.sap.com/software/sltoolset.html</a>.</li>\n<li>Multi-Node (Scale-Out) Support possibilities are described in note <a href=\"/notes/2408419\" target=\"_blank\">2408419</a>.</li>\n<li>\n<p><em>Please refer to SAP note <a href=\"/notes/306695\" target=\"_blank\">306695</a> ('Field EMDST13 is unknown' after installing IS-U/CCS) after installation.</em></p>\n</li>\n</ul>\n<p><strong>Upgrade Requirements</strong></p>\n<ul>\n<li>You can upgrade from SAP S/4HANA, on-premise edition 1511 to SAP S/4HANA 1610. <br/>Please be aware that your software level is not higher than Support Package Stack 10.</li>\n<li>For the system upgrade, please refer to the <a href=\"https://help.sap.com/doc/31afbde9e69047298073148662e8aeb2/1610%20002/en-US/UPGR_OP1610_FPS02.pdf\" target=\"_blank\">upgrade guide</a>.</li>\n<li>You need at least Software Update Manager (SUM) 2.0 for the upgrade. Please make sure that you always use the latest patch level of SUM available at <a href=\"https://support.sap.com/software/sltoolset.html\" target=\"_blank\">support.sap.com/software/sltoolset.html</a>.</li>\n<li>If you encounter sequence errors during DDIC_ACTIVATION, please repeat the phase.</li>\n</ul>\n<p><strong>Support Package Update (via Software Update Manager (SUM)):</strong></p>\n<ul>\n<li>If you are using SPAM (instead of SUM) for the SP update to SAP S/4HANA 1610 Support Package Stack 04, please apply note 2603860 (Data Dictionary: Wrong calculation of RC in mass activation) to avoid errors during DDIC_ACTIVATION, before you start the update.</li>\n<li>If the SP-import with SPAM stops in import phase AUTO_MOD_SPAU and if a short dump of type “LOAD_PROGRAM_INTF_MISMATCH” is present in ST22 at the respective import time, please repeat the import phase once. In case repeating the phase does not solve the issue, please contact the SAP Support.</li>\n<li>If you encounter sequence errors during DDIC_ACTIVATION of type “specify reference table AND reference field”, please repeat the import phase once. In case repeating the phase does not solve the issue, please contact the SAP Support.</li>\n<li>Specific errors which might occur if certain Business Function (Sets) are activated</li>\n<li>If IS-Business Function Sets for \"TELCO\" is activated and if you use the generation option in SPAM for the SP update, you might encounter generation errors during SP import of type  “The data object \"LS_xxx\" does not have a component called \"/SAPCE/x\". In this case, please use “Ignore generation errors”-option in SPAM and restart the SP update.</li>\n</ul>\n<p><strong>Notes to be applied on top of Support Package 08:</strong></p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"0\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td width=\"82\">\n<p><strong>SAP Note</strong></p>\n</td>\n<td valign=\"top\" width=\"349\">\n<p><strong>Description</strong></p>\n</td>\n<td valign=\"top\" width=\"154\">\n<p><strong>Software Component</strong></p>\n</td>\n<td valign=\"top\" width=\"147\">\n<p><strong>Manual Activities required</strong></p>\n</td>\n<td valign=\"top\" width=\"142\">\n<p><strong> Added on</strong></p>\n</td>\n</tr>\n<tr>\n<td width=\"82\">\n<p><a href=\"/notes/2880761\" target=\"_blank\">2880761</a></p>\n</td>\n<td valign=\"top\" width=\"349\">\n<p>Syntax error \"VBHDR_G does not exist\" in program /SSA/EBT</p>\n</td>\n<td valign=\"top\" width=\"154\">\n<p>ST-A/PI</p>\n</td>\n<td valign=\"top\" width=\"147\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"142\">\n<p>2020-04-08</p>\n</td>\n</tr>\n<tr>\n<td width=\"82\">\n<p><a href=\"/notes/2880801\" target=\"_blank\">2880801</a></p>\n</td>\n<td valign=\"top\" width=\"349\">\n<p>Syntax error \"VBHDR_G does not exist\" in program /SSA/EBP</p>\n</td>\n<td valign=\"top\" width=\"154\">\n<p>ST-A/PI</p>\n</td>\n<td valign=\"top\" width=\"147\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"142\">\n<p>2020-04-08</p>\n</td>\n</tr>\n<tr>\n<td width=\"82\">\n<p><a href=\"/notes/2886195\" target=\"_blank\">2886195</a></p>\n</td>\n<td valign=\"top\" width=\"349\">Syntax error \"VBHDR_G does not exist\" in program /SDF/SAPLEWA</td>\n<td valign=\"top\" width=\"154\">ST-A/PI</td>\n<td valign=\"top\" width=\"147\">No</td>\n<td valign=\"top\" width=\"142\">2020-04-08</td>\n</tr>\n</tbody>\n</table></div>\n<p><strong> </strong></p>\n<p><strong><strong>Important changes made after release of <strong>Support Package Stack 08</strong></strong></strong></p>\n<p><strong>SUPPORT PACKAGE STACK 07 (10/2019)</strong></p>\n<p><strong>General / Important Considerations</strong></p>\n<ul>\n<li>Please refer to the specific documentation under section <a href=\"https://help.sap.com/viewer/product/SAP_S4HANA_ON-PREMISE/1610%20002/en-US\" target=\"_blank\">product documentation</a>.</li>\n<li>SAP HANA 1.0</li>\n<ul>\n<li>The minimum required revision of SAP HANA 1.0 is Revision 122.23 SAP in general recommends customers to implement the highest SAP HANA 1.0 revision available. For more details, please refer to note <a href=\"/notes/2021789\" target=\"_blank\">2021789</a> (SAP HANA 1.0 Revision and Maintenance Strategy).</li>\n</ul>\n<li>SAP HANA 2.0</li>\n<ul>\n<li>The minimum required revision of SAP HANA 2.0 is SPS02 Revision 24. SAP in general recommends customers to implement the highest SAP HANA 2.0 revision available. For more details, please refer to note <a href=\"/notes/2378962\" target=\"_blank\">2378962</a> (SAP HANA 2.0 Revision and Maintenance Strategy).</li>\n<li>Please refer to note <a href=\"/notes/2426477\" target=\"_blank\" title=\"2426477  - Support for SAP HANA 2 in SAP S/4HANA 1610 - Status of application support for specific functionalities of SAP HANA 2\">2426477</a> which describes new features of SAP HANA 2.0 already been adopted by SAP S/4HANA 1610 and note <a href=\"/notes/2426339\" target=\"_blank\" title=\"2426339  - Support for SAP HANA 2 in SAP S/4HANA - Technical Information Regarding SAP HANA Requirements\">2426339 </a>which informs about technical requirements to the database system (hardware, operating system, etc.) introduced with SAP HANA 2.0 that may be relevant if you plan to operate your SAP S/4HANA 1610 system on SAP HANA 2.0.</li>\n<li>Furthermore, refer to the general restriction note <a href=\"/notes/2333141\" target=\"_blank\">2333141</a> also for restrictions relevant for SAP S/4HANA 1610 on HANA 2.0.</li>\n<li>Please refer to note <a href=\"/notes/2487855\" target=\"_blank\">2487855</a> (Possible Table Inconsistencies on SAP HANA 2.0 SPS01 due to Internal Persistence Structure Migration) if you are on SAP HANA 2.0 Revision 010.00 or 011.00 (SPS01) or if you plan to upgrade to these SPS01 revisions. </li>\n<li>SAP HANA 2.0 SPS 02 Revision 20: Please check SAP note <a href=\"/notes/2527538\" target=\"_blank\">2527538</a> (AMDP Procedure Execution Fails With Column Store Error) and SAP note <a href=\"/notes/2527648\" target=\"_blank\">2527648</a> (Column Value Equals Null Cannot be Detected by IS NULL in Debug Mode).</li>\n</ul>\n<li>For the specific Front-end/UI for SAP S/4HANA 1610, please refer to SAP note <a href=\"/notes/2356208\" target=\"_blank\">2356208</a> which is the release information note for product version SAP FIORI FOR SAP S/4HANA 1610. Support Package Stack 03 of SAP S/4HANA 1610 in the backend requires Support Package Stack 01 or higher of SAP Fiori 2.0 for SAP S/4HANA on the frontend (and vice versa).</li>\n<li>Please refer to note <a href=\"/notes/2539548\" target=\"_blank\">2539548</a> (SAP S/4HANA, on-premise edition 1610 - SAP S/4HANA ON-PREMISE 1610 SP stack 03 (10/2017) content activation note) when you are using the SAP Best Practices for your SAP S/4HANA on premise implementation and you need current information for the tool and the content.</li>\n<li>Visual theme “Belize” for SAP GUI for Windows”:<br/>For the usage of SAP S/4HANA 1610 Support Package Stack 03 with SAP GUI for Windows there are few prerequisites:</li>\n<ul>\n<li>Installation of SAP GUI for Windows 7.50 or higher (desktop client)</li>\n<li>Installation of SAP Kernel 7.49 Patch 210 or higher</li>\n</ul>\n<li>If you would like to use a Fiori Launchpad (FLP) with integrated SAP GUI for Windows applications, please install SAP Business Client 6.5. Please refer to note <a href=\"/notes/2348661\" target=\"_blank\">2348661</a> - Restrictions of SAP Fiori visual theme for classic applications.</li>\n</ul>\n<p><strong>Installation Requirements</strong></p>\n<ul>\n<li>For the system installation, please refer to the <a href=\"https://help.sap.com/doc/14f9236f8e234dccbb195259a8eaf616/1610%20002/en-US/INST_OP1610_FPS02.pdf\" target=\"_blank\">installation guide</a>.</li>\n<li>You need at least Software Provisioning Manager (SWPM) Support Package 21 for the installation. Please make sure that you always use the latest patch level of SWPM available at <a href=\"https://support.sap.com/software/sltoolset.html\" target=\"_blank\">support.sap.com/software/sltoolset.html</a>.</li>\n<li>Multi-Node (Scale-Out) Support possibilities are described in note <a href=\"/notes/2408419\" target=\"_blank\">2408419</a>.</li>\n<li>\n<p><em>Please refer to SAP note <a href=\"/notes/306695\" target=\"_blank\">306695</a> ('Field EMDST13 is unknown' after installing IS-U/CCS) after installation.</em></p>\n</li>\n</ul>\n<p><strong>Upgrade Requirements</strong></p>\n<ul>\n<li>You can upgrade from SAP S/4HANA, on-premise edition 1511 to SAP S/4HANA 1610. <br/>Please be aware that your software level is not higher than Support Package Stack 09.</li>\n<li>For the system upgrade, please refer to the <a href=\"https://help.sap.com/doc/31afbde9e69047298073148662e8aeb2/1610%20002/en-US/UPGR_OP1610_FPS02.pdf\" target=\"_blank\">upgrade guide</a>.</li>\n<li>You need at least Software Update Manager (SUM) 2.0 for the upgrade. Please make sure that you always use the latest patch level of SUM available at <a href=\"https://support.sap.com/software/sltoolset.html\" target=\"_blank\">support.sap.com/software/sltoolset.html</a>.</li>\n<li>If you encounter sequence errors during DDIC_ACTIVATION, please repeat the phase.</li>\n</ul>\n<p><strong>Support Package Update (via Software Update Manager (SUM)):</strong></p>\n<ul>\n<li>If you are using SPAM (instead of SUM) for the SP update to SAP S/4HANA 1610 Support Package Stack 04, please apply note 2603860 (Data Dictionary: Wrong calculation of RC in mass activation) to avoid errors during DDIC_ACTIVATION, before you start the update.</li>\n<li>If the SP-import with SPAM stops in import phase AUTO_MOD_SPAU and if a short dump of type “LOAD_PROGRAM_INTF_MISMATCH” is present in ST22 at the respective import time, please repeat the import phase once. In case repeating the phase does not solve the issue, please contact the SAP Support.</li>\n<li>If you encounter sequence errors during DDIC_ACTIVATION of type “specify reference table AND reference field”, please repeat the import phase once. In case repeating the phase does not solve the issue, please contact the SAP Support.</li>\n<li>Specific errors which might occur if certain Business Function (Sets) are activated</li>\n<li>If IS-Business Function Sets for \"TELCO\" is activated and if you use the generation option in SPAM for the SP update, you might encounter generation errors during SP import of type  “The data object \"LS_xxx\" does not have a component called \"/SAPCE/x\". In this case, please use “Ignore generation errors”-option in SPAM and restart the SP update.</li>\n</ul>\n<p><strong> </strong></p>\n<p><strong><strong>Important changes made after release of <strong>Support Package Stack 07</strong></strong></strong></p>\n<p><strong>2020-01-31: </strong><strong>United Kingdom leaving the EU</strong>:</p>\n<p>• For information about the United Kingdom leaving the EU with the Withdrawal Bill and the transition period, please see SAP note <a href=\"/notes/2885225\" target=\"_blank\">2885225</a>.</p>\n<p>• For information about how a “hard Brexit” (= a no-deal scenario) would impact your SAP S/4HANA system, please see SAP note <a href=\"/notes/2749671\" target=\"_blank\">2749671</a>.</p>\n<p><strong>SUPPORT PACKAGE STACK 06 (04/2019)</strong></p>\n<p><strong>General / Important Considerations</strong></p>\n<ul>\n<li>Please refer to the specific documentation under section <a href=\"https://help.sap.com/viewer/product/SAP_S4HANA_ON-PREMISE/1610%20002/en-US\" target=\"_blank\">product documentation</a>.</li>\n<li>SAP HANA 1.0</li>\n<ul>\n<li>The minimum required revision of SAP HANA 1.0 is Revision 122.21. SAP in general recommends customers to implement the highest SAP HANA 1.0 revision available. For more details, please refer to note <a href=\"/notes/2021789\" target=\"_blank\">2021789</a> (SAP HANA 1.0 Revision and Maintenance Strategy).</li>\n</ul>\n<li>SAP HANA 2.0</li>\n<ul>\n<li>The minimum required revision of SAP HANA 2.0 is SP01 Revision 12.04 or SPS02 Revision 23. SAP in general recommends customers to implement the highest SAP HANA 2.0 revision available. For more details, please refer to note <a href=\"/notes/2378962\" target=\"_blank\">2378962</a> (SAP HANA 2.0 Revision and Maintenance Strategy).</li>\n<li>Please refer to note <a href=\"/notes/2426477\" target=\"_blank\" title=\"2426477  - Support for SAP HANA 2 in SAP S/4HANA 1610 - Status of application support for specific functionalities of SAP HANA 2\">2426477</a> which describes new features of SAP HANA 2.0 already been adopted by SAP S/4HANA 1610 and note <a href=\"/notes/2426339\" target=\"_blank\" title=\"2426339  - Support for SAP HANA 2 in SAP S/4HANA - Technical Information Regarding SAP HANA Requirements\">2426339 </a>which informs about technical requirements to the database system (hardware, operating system, etc.) introduced with SAP HANA 2.0 that may be relevant if you plan to operate your SAP S/4HANA 1610 system on SAP HANA 2.0.</li>\n<li>Furthermore, refer to the general restriction note <a href=\"/notes/2333141\" target=\"_blank\">2333141</a> also for restrictions relevant for SAP S/4HANA 1610 on HANA 2.0.</li>\n<li>Please refer to note <a href=\"/notes/2487855\" target=\"_blank\">2487855</a> (Possible Table Inconsistencies on SAP HANA 2.0 SPS01 due to Internal Persistence Structure Migration) if you are on SAP HANA 2.0 Revision 010.00 or 011.00 (SPS01) or if you plan to upgrade to these SPS01 revisions. </li>\n<li>SAP HANA 2.0 SPS 02 Revision 20: Please check SAP note <a href=\"/notes/2527538\" target=\"_blank\">2527538</a> (AMDP Procedure Execution Fails With Column Store Error) and SAP note <a href=\"/notes/2527648\" target=\"_blank\">2527648</a> (Column Value Equals Null Cannot be Detected by IS NULL in Debug Mode).</li>\n</ul>\n<li>For the specific Front-end/UI for SAP S/4HANA 1610, please refer to SAP note <a href=\"/notes/2356208\" target=\"_blank\">2356208</a> which is the release information note for product version SAP FIORI FOR SAP S/4HANA 1610. Support Package Stack 03 of SAP S/4HANA 1610 in the backend requires Support Package Stack 01 or higher of SAP Fiori 2.0 for SAP S/4HANA on the frontend (and vice versa).</li>\n<li>Please refer to note <a href=\"/notes/2539548\" target=\"_blank\">2539548</a> (SAP S/4HANA, on-premise edition 1610 - SAP S/4HANA ON-PREMISE 1610 SP stack 03 (10/2017) content activation note) when you are using the SAP Best Practices for your SAP S/4HANA on premise implementation and you need current information for the tool and the content.</li>\n<li>Visual theme “Belize” for SAP GUI for Windows”:<br/>For the usage of SAP S/4HANA 1610 Support Package Stack 03 with SAP GUI for Windows there are few prerequisites:</li>\n<ul>\n<li>Installation of SAP GUI for Windows 7.50 or higher (desktop client)</li>\n<li>Installation of SAP Kernel 7.49 Patch 210 or higher</li>\n</ul>\n<li>If you would like to use a Fiori Launchpad (FLP) with integrated SAP GUI for Windows applications, please install SAP Business Client 6.5. Please refer to note <a href=\"/notes/2348661\" target=\"_blank\">2348661</a> - Restrictions of SAP Fiori visual theme for classic applications.</li>\n</ul>\n<p><strong>Installation Requirements</strong></p>\n<ul>\n<li>For the system installation, please refer to the <a href=\"https://help.sap.com/doc/14f9236f8e234dccbb195259a8eaf616/1610%20002/en-US/INST_OP1610_FPS02.pdf\" target=\"_blank\">installation guide</a>.</li>\n<li>You need at least Software Provisioning Manager (SWPM) Support Package 21 for the installation. Please make sure that you always use the latest patch level of SWPM available at <a href=\"https://support.sap.com/software/sltoolset.html\" target=\"_blank\">support.sap.com/software/sltoolset.html</a>.</li>\n<li>Multi-Node (Scale-Out) Support possibilities are described in note <a href=\"/notes/2408419\" target=\"_blank\">2408419</a>.</li>\n<li>\n<p><em>Please refer to SAP note <a href=\"/notes/306695\" target=\"_blank\">306695</a> ('Field EMDST13 is unknown' after installing IS-U/CCS) after installation.</em></p>\n</li>\n</ul>\n<p><strong>Upgrade Requirements</strong></p>\n<ul>\n<li>You can upgrade from SAP S/4HANA, on-premise edition 1511 to SAP S/4HANA 1610. <br/>Please be aware that your software level is not higher than Support Package Stack 08.</li>\n<li>For the system upgrade, please refer to the <a href=\"https://help.sap.com/doc/31afbde9e69047298073148662e8aeb2/1610%20002/en-US/UPGR_OP1610_FPS02.pdf\" target=\"_blank\">upgrade guide</a>.</li>\n<li>You need at least Software Update Manager (SUM) 2.0 for the upgrade. Please make sure that you always use the latest patch level of SUM available at <a href=\"https://support.sap.com/software/sltoolset.html\" target=\"_blank\">support.sap.com/software/sltoolset.html</a>.</li>\n<li>If you encounter sequence errors during DDIC_ACTIVATION, please repeat the phase.</li>\n</ul>\n<p><strong>Support Package Update (via Software Update Manager (SUM)):</strong></p>\n<ul>\n<li>If you are using SPAM (instead of SUM) for the SP update to SAP S/4HANA 1610 Support Package Stack 04, please apply note 2603860 (Data Dictionary: Wrong calculation of RC in mass activation) to avoid errors during DDIC_ACTIVATION, before you start the update.</li>\n<li>If the SP-import with SPAM stops in import phase AUTO_MOD_SPAU and if a short dump of type “LOAD_PROGRAM_INTF_MISMATCH” is present in ST22 at the respective import time, please repeat the import phase once. In case repeating the phase does not solve the issue, please contact the SAP Support.</li>\n<li>If you encounter sequence errors during DDIC_ACTIVATION of type “specify reference table AND reference field”, please repeat the import phase once. In case repeating the phase does not solve the issue, please contact the SAP Support.</li>\n<li>Specific errors which might occur if certain Business Function (Sets) are activated</li>\n<li>If IS-Business Function Sets for \"TELCO\" is activated and if you use the generation option in SPAM for the SP update, you might encounter generation errors during SP import of type  “The data object \"LS_xxx\" does not have a component called \"/SAPCE/x\". In this case, please use “Ignore generation errors”-option in SPAM and restart the SP update.</li>\n</ul>\n<p><strong>Notes to be applied on top of Support Package Stack 06</strong></p>\n<p> </p>\n<p><strong>SUPPORT PACKAGE STACK 05 (11/2018)</strong></p>\n<p><strong>General / Important Considerations</strong></p>\n<ul>\n<li>Please refer to the specific documentation under section <a href=\"https://help.sap.com/viewer/product/SAP_S4HANA_ON-PREMISE/1610%20002/en-US\" target=\"_blank\">product documentation</a>.</li>\n<li>SAP HANA 1.0</li>\n<ul>\n<li>The minimum required revision of SAP HANA 1.0 is Revision 122.05. SAP in general recommends customers to implement the highest SAP HANA 1.0 revision available. For more details, please refer to note <a href=\"/notes/2021789\" target=\"_blank\">2021789</a> (SAP HANA 1.0 Revision and Maintenance Strategy).</li>\n</ul>\n<li>SAP HANA 2.0</li>\n<ul>\n<li>The minimum required revision of SAP HANA 2.0 is SP0 Revision 002. SAP in general recommends customers to implement the highest SAP HANA 2.0 revision available. For more details, please refer to note <a href=\"/notes/2378962\" target=\"_blank\">2378962</a> (SAP HANA 2.0 Revision and Maintenance Strategy).</li>\n<li>Please refer to note <a href=\"/notes/2426477\" target=\"_blank\" title=\"2426477  - Support for SAP HANA 2 in SAP S/4HANA 1610 - Status of application support for specific functionalities of SAP HANA 2\">2426477</a> which describes new features of SAP HANA 2.0 already been adopted by SAP S/4HANA 1610 and note <a href=\"/notes/2426339\" target=\"_blank\" title=\"2426339  - Support for SAP HANA 2 in SAP S/4HANA - Technical Information Regarding SAP HANA Requirements\">2426339 </a>which informs about technical requirements to the database system (hardware, operating system, etc.) introduced with SAP HANA 2.0 that may be relevant if you plan to operate your SAP S/4HANA 1610 system on SAP HANA 2.0.</li>\n<li>Furthermore, refer to the general restriction note <a href=\"/notes/2333141\" target=\"_blank\">2333141</a> also for restrictions relevant for SAP S/4HANA 1610 on HANA 2.0.</li>\n<li>Please refer to note <a href=\"/notes/2487855\" target=\"_blank\">2487855</a> (Possible Table Inconsistencies on SAP HANA 2.0 SPS01 due to Internal Persistence Structure Migration) if you are on SAP HANA 2.0 Revision 010.00 or 011.00 (SPS01) or if you plan to upgrade to these SPS01 revisions. </li>\n<li>SAP HANA 2.0 SPS 02 Revision 20: Please check SAP note <a href=\"/notes/2527538\" target=\"_blank\">2527538</a> (AMDP Procedure Execution Fails With Column Store Error) and SAP note <a href=\"/notes/2527648\" target=\"_blank\">2527648</a> (Column Value Equals Null Cannot be Detected by IS NULL in Debug Mode).</li>\n</ul>\n<li>For the specific Front-end/UI for SAP S/4HANA 1610, please refer to SAP note <a href=\"/notes/2356208\" target=\"_blank\">2356208</a> which is the release information note for product version SAP FIORI FOR SAP S/4HANA 1610. Support Package Stack 03 of SAP S/4HANA 1610 in the backend requires Support Package Stack 01 or higher of SAP Fiori 2.0 for SAP S/4HANA on the frontend (and vice versa).</li>\n<li>Please refer to note <a href=\"/notes/2539548\" target=\"_blank\">2539548</a> (SAP S/4HANA, on-premise edition 1610 - SAP S/4HANA ON-PREMISE 1610 SP stack 03 (10/2017) content activation note) when you are using the SAP Best Practices for your SAP S/4HANA on premise implementation and you need current information for the tool and the content.</li>\n<li>Visual theme “Belize” for SAP GUI for Windows”:<br/>For the usage of SAP S/4HANA 1610 Support Package Stack 03 with SAP GUI for Windows there are few prerequisites:</li>\n<ul>\n<li>Installation of SAP GUI for Windows 7.50 or higher (desktop client)</li>\n<li>Installation of SAP Kernel 7.49 Patch 210 or higher</li>\n</ul>\n<li>If you would like to use a Fiori Launchpad (FLP) with integrated SAP GUI for Windows applications, please install SAP Business Client 6.5. Please refer to note <a href=\"/notes/2348661\" target=\"_blank\">2348661</a> - Restrictions of SAP Fiori visual theme for classic applications.</li>\n</ul>\n<p><strong>Installation Requirements</strong></p>\n<ul>\n<li>For the system installation, please refer to the <a href=\"https://help.sap.com/doc/14f9236f8e234dccbb195259a8eaf616/1610%20002/en-US/INST_OP1610_FPS02.pdf\" target=\"_blank\">installation guide</a>.</li>\n<li>You need at least Software Provisioning Manager (SWPM) Support Package 21 for the installation. Please make sure that you always use the latest patch level of SWPM available at <a href=\"https://support.sap.com/software/sltoolset.html\" target=\"_blank\">support.sap.com/software/sltoolset.html</a>.</li>\n<li>Multi-Node (Scale-Out) Support possibilities are described in note <a href=\"/notes/2408419\" target=\"_blank\">2408419</a>.</li>\n<li>\n<p><em>Please refer to SAP note <a href=\"/notes/306695\" target=\"_blank\">306695</a> ('Field EMDST13 is unknown' after installing IS-U/CCS) after installation.</em></p>\n</li>\n</ul>\n<p><strong>Upgrade Requirements</strong></p>\n<ul>\n<li>You can upgrade from SAP S/4HANA, on-premise edition 1511 to SAP S/4HANA 1610. <br/>Please be aware that your software level is not higher than Support Package Stack 07.</li>\n<li>For the system upgrade, please refer to the <a href=\"https://help.sap.com/doc/31afbde9e69047298073148662e8aeb2/1610%20002/en-US/UPGR_OP1610_FPS02.pdf\" target=\"_blank\">upgrade guide</a>.</li>\n<li>You need at least Software Update Manager (SUM) 2.0 for the upgrade. Please make sure that you always use the latest patch level of SUM available at <a href=\"https://support.sap.com/software/sltoolset.html\" target=\"_blank\">support.sap.com/software/sltoolset.html</a>.</li>\n<li>If you encounter sequence errors during DDIC_ACTIVATION, please repeat the phase.</li>\n</ul>\n<p><strong>Support Package Update (via Software Update Manager (SUM)):</strong></p>\n<ul>\n<li>If you are using SPAM (instead of SUM) for the SP update to SAP S/4HANA 1610 Support Package Stack 04, please apply note 2603860 (Data Dictionary: Wrong calculation of RC in mass activation) to avoid errors during DDIC_ACTIVATION, before you start the update.</li>\n<li>If the SP-import with SPAM stops in import phase AUTO_MOD_SPAU and if a short dump of type “LOAD_PROGRAM_INTF_MISMATCH” is present in ST22 at the respective import time, please repeat the import phase once. In case repeating the phase does not solve the issue, please contact the SAP Support.</li>\n<li>If you encounter sequence errors during DDIC_ACTIVATION of type “specify reference table AND reference field”, please repeat the import phase once. In case repeating the phase does not solve the issue, please contact the SAP Support.</li>\n<li>Specific errors which might occur if certain Business Function (Sets) are activated</li>\n<li>If IS-Business Function Sets for \"TELCO\" is activated and if you use the generation option in SPAM for the SP update, you might encounter generation errors during SP import of type  “The data object \"LS_xxx\" does not have a component called \"/SAPCE/x\". In this case, please use “Ignore generation errors”-option in SPAM and restart the SP update.</li>\n</ul>\n<p><strong>Notes to be applied on top of Support Package Stack 05</strong></p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"0\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td width=\"82\">\n<p><strong>SAP Note</strong></p>\n</td>\n<td width=\"349\">\n<p><strong>Description</strong></p>\n</td>\n<td valign=\"top\" width=\"126\">\n<p><strong>Software Component</strong></p>\n</td>\n<td valign=\"top\" width=\"175\">\n<p><strong>Manual Activities required</strong></p>\n</td>\n<td valign=\"top\" width=\"142\">\n<p><strong> Added on</strong></p>\n</td>\n</tr>\n<tr>\n<td width=\"82\"><span ar-sa;\"=\"\" calibri',sans-serif;=\"\" calibri;=\"\" de;=\"\" minor-latin;=\"\" mso-ansi-language:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" mso-fareast-theme-font:=\"\"><a href=\"/notes/2694011\" target=\"_blank\">2694011</a></span></td>\n<td valign=\"top\" width=\"349\">\n<p><span ar-sa;\"=\"\" calibri',sans-serif;=\"\" calibri;=\"\" de;=\"\" minor-latin;=\"\" mso-ansi-language:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" mso-fareast-theme-font:=\"\">WTY: Dump \"CALL_FUNCTION_CONFLICT_LENG\" occurs on account document posting</span></p>\n</td>\n<td valign=\"top\" width=\"126\">\n<p> <span ar-sa;\"=\"\" calibri',sans-serif;=\"\" calibri;=\"\" de;=\"\" minor-latin;=\"\" mso-ansi-language:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" mso-fareast-theme-font:=\"\">S4CORE</span></p>\n</td>\n<td valign=\"top\" width=\"175\">\n<p> No</p>\n</td>\n<td valign=\"top\" width=\"142\">\n<p> 22.11.2018</p>\n</td>\n</tr>\n<tr>\n<td width=\"82\">\n<p><span ar-sa;\"=\"\" calibri',sans-serif;=\"\" calibri;=\"\" de;=\"\" minor-latin;=\"\" mso-ansi-language:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" mso-fareast-theme-font:=\"\"><a href=\"/notes/2697405\" target=\"_blank\">2697405</a></span></p>\n</td>\n<td valign=\"top\" width=\"349\">\n<p><span ar-sa;\"=\"\" calibri',sans-serif;=\"\" calibri;=\"\" de;=\"\" minor-latin;=\"\" mso-ansi-language:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" mso-fareast-theme-font:=\"\">TrexViaDbsl: wrong schema name is set to temporary objects in TREX_EXT_AGGREGATE</span></p>\n</td>\n<td valign=\"top\" width=\"126\">\n<p> <span ar-sa;\"=\"\" calibri',sans-serif;=\"\" calibri;=\"\" de;=\"\" minor-latin;=\"\" mso-ansi-language:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" mso-fareast-theme-font:=\"\">SAP_BASIS</span></p>\n</td>\n<td valign=\"top\" width=\"175\">\n<p> Yes</p>\n</td>\n<td valign=\"top\" width=\"142\">\n<p><strong> </strong>22.11.2018</p>\n</td>\n</tr>\n</tbody>\n</table></div>\n<p> </p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"0\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td width=\"82\">\n<p><a href=\"/notes/2722552\" target=\"_blank\">2722552</a></p>\n</td>\n<td valign=\"top\" width=\"349\">\n<p>Runtime error SYSTEM_DATA_ALREADY_FREE during update of classifications</p>\n</td>\n<td valign=\"top\" width=\"126\">\n<p>SAP_ABA</p>\n</td>\n<td valign=\"top\" width=\"175\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"142\">\n<p> 2018-11-28</p>\n</td>\n</tr>\n<tr>\n<td width=\"82\">\n<p><a href=\"/notes/2724147\" target=\"_blank\">2724147</a></p>\n</td>\n<td valign=\"top\" width=\"349\">\n<p>Termination in the update of the classification</p>\n</td>\n<td valign=\"top\" width=\"126\">\n<p>SAP_ABA</p>\n</td>\n<td valign=\"top\" width=\"175\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"142\">\n<p> 2018-11-28</p>\n</td>\n</tr>\n<tr>\n<td width=\"82\">\n<p><a href=\"/notes/2726975\" target=\"_blank\">2726975</a></p>\n</td>\n<td valign=\"top\" width=\"349\">\n<p>Support of CLSD in SNOTE: Ignore all Changed by and Changed on data in the CI</p>\n</td>\n<td valign=\"top\" width=\"126\">\n<p>SAP_BASIS</p>\n</td>\n<td valign=\"top\" width=\"175\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"142\">\n<p> 2018-12-11</p>\n</td>\n</tr>\n</tbody>\n</table></div>\n<p> </p>\n<p><strong>Important Changes made after Release of Feature Package Stack 05</strong></p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"0\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td width=\"82\">\n<p><strong>SAP Note</strong></p>\n</td>\n<td width=\"349\">\n<p><strong>Description</strong></p>\n</td>\n<td valign=\"top\" width=\"126\">\n<p><strong>Software Component</strong></p>\n</td>\n<td valign=\"top\" width=\"175\">\n<p><strong>Manual Activities required</strong></p>\n</td>\n<td valign=\"top\" width=\"142\">\n<p><strong> Added on</strong></p>\n</td>\n</tr>\n<tr>\n<td width=\"82\">\n<p> <a href=\"/notes/273869\" target=\"_blank\">2738698</a></p>\n</td>\n<td valign=\"top\" width=\"349\">\n<p>Bank chains: determination of intermediary banks with unexpected results</p>\n</td>\n<td valign=\"top\" width=\"126\">\n<p> S4CORE</p>\n</td>\n<td valign=\"top\" width=\"175\">\n<p> No</p>\n</td>\n<td valign=\"top\" width=\"142\">\n<p> 2019-01-11</p>\n</td>\n</tr>\n<tr>\n<td width=\"82\">\n<p> <a href=\"/notes/2324448\" target=\"_blank\">2324448</a></p>\n</td>\n<td valign=\"top\" width=\"349\">\n<p>S4 Release 1610: New inventory data model -&gt; Adjustments at APO to use the new locking free stock upate at liveCache</p>\n</td>\n<td valign=\"top\" width=\"126\">\n<p> SCMAPO</p>\n</td>\n<td valign=\"top\" width=\"175\">\n<p> No</p>\n</td>\n<td valign=\"top\" width=\"142\">\n<p> 2019-01-31</p>\n</td>\n</tr>\n<tr>\n<td width=\"82\">\n<p><a href=\"/notes/2767385\" target=\"_blank\">2767385</a></p>\n</td>\n<td valign=\"top\" width=\"349\">\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"0\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td valign=\"top\" width=\"349\">\n<p>Melted variables showing initial values by mistake</p>\n</td>\n</tr>\n</tbody>\n</table></div>\n</td>\n<td valign=\"top\" width=\"126\">SAP_BW</td>\n<td valign=\"top\" width=\"175\">No</td>\n<td valign=\"top\" width=\"142\">2019-07-12</td>\n</tr>\n</tbody>\n</table></div>\n<p> </p>\n<p> </p>\n<p><strong>SUPPORT PACKAGE STACK 04 (05/2018)</strong></p>\n<p><strong>General / Important Considerations</strong></p>\n<ul>\n<li>Please refer to the specific documentation under section <a href=\"https://help.sap.com/viewer/product/SAP_S4HANA_ON-PREMISE/1610%20002/en-US\" target=\"_blank\">product documentation</a>.</li>\n<li>SAP HANA 1.0</li>\n<ul>\n<li>The minimum required revision of SAP HANA 1.0 is Revision 122.05. SAP in general recommends customers to implement the highest SAP HANA 1.0 revision available. For more details, please refer to note <a href=\"/notes/2021789\" target=\"_blank\">2021789</a> (SAP HANA 1.0 Revision and Maintenance Strategy).</li>\n</ul>\n<li>SAP HANA 2.0</li>\n<ul>\n<li>The minimum required revision of SAP HANA 2.0 is SP0 Revision 002. SAP in general recommends customers to implement the highest SAP HANA 2.0 revision available. For more details, please refer to note <a href=\"/notes/2378962\" target=\"_blank\">2378962</a> (SAP HANA 2.0 Revision and Maintenance Strategy).</li>\n<li>Please refer to note <a href=\"/notes/2426477\" target=\"_blank\" title=\"2426477  - Support for SAP HANA 2 in SAP S/4HANA 1610 - Status of application support for specific functionalities of SAP HANA 2\">2426477</a> which describes new features of SAP HANA 2.0 already been adopted by SAP S/4HANA 1610 and note <a href=\"/notes/2426339\" target=\"_blank\" title=\"2426339  - Support for SAP HANA 2 in SAP S/4HANA - Technical Information Regarding SAP HANA Requirements\">2426339 </a>which informs about technical requirements to the database system (hardware, operating system, etc.) introduced with SAP HANA 2.0 that may be relevant if you plan to operate your SAP S/4HANA 1610 system on SAP HANA 2.0.</li>\n<li>Furthermore, refer to the general restriction note <a href=\"/notes/2333141\" target=\"_blank\">2333141</a> also for restrictions relevant for SAP S/4HANA 1610 on HANA 2.0.</li>\n<li>Please refer to note <a href=\"/notes/2487855\" target=\"_blank\">2487855</a> (Possible Table Inconsistencies on SAP HANA 2.0 SPS01 due to Internal Persistence Structure Migration) if you are on SAP HANA 2.0 Revision 010.00 or 011.00 (SPS01) or if you plan to upgrade to these SPS01 revisions. </li>\n<li>SAP HANA 2.0 SPS 02 Revision 20: Please check SAP note <a href=\"/notes/2527538\" target=\"_blank\">2527538</a> (AMDP Procedure Execution Fails With Column Store Error) and SAP note <a href=\"/notes/2527648\" target=\"_blank\">2527648</a> (Column Value Equals Null Cannot be Detected by IS NULL in Debug Mode).</li>\n</ul>\n<li>For the specific Front-end/UI for SAP S/4HANA 1610, please refer to SAP note <a href=\"/notes/2356208\" target=\"_blank\">2356208</a> which is the release information note for product version SAP FIORI FOR SAP S/4HANA 1610. Support Package Stack 03 of SAP S/4HANA 1610 in the backend requires Support Package Stack 01 or higher of SAP Fiori 2.0 for SAP S/4HANA on the frontend (and vice versa).</li>\n<li>Please refer to note <a href=\"/notes/2539548\" target=\"_blank\">2539548</a> (SAP S/4HANA, on-premise edition 1610 - SAP S/4HANA ON-PREMISE 1610 SP stack 03 (10/2017) content activation note) when you are using the SAP Best Practices for your SAP S/4HANA on premise implementation and you need current information for the tool and the content.</li>\n<li>Visual theme “Belize” for SAP GUI for Windows”:<br/>For the usage of SAP S/4HANA 1610 Support Package Stack 03 with SAP GUI for Windows there are few prerequisites:</li>\n<ul>\n<li>Installation of SAP GUI for Windows 7.50 or higher (desktop client)</li>\n<li>Installation of SAP Kernel 7.49 Patch 210 or higher</li>\n</ul>\n<li>If you would like to use a Fiori Launchpad (FLP) with integrated SAP GUI for Windows applications, please install SAP Business Client 6.5. Please refer to note <a href=\"/notes/2348661\" target=\"_blank\">2348661</a> - Restrictions of SAP Fiori visual theme for classic applications.</li>\n</ul>\n<p><strong>Installation Requirements</strong></p>\n<ul>\n<li>For the system installation, please refer to the <a href=\"https://help.sap.com/doc/14f9236f8e234dccbb195259a8eaf616/1610%20002/en-US/INST_OP1610_FPS02.pdf\" target=\"_blank\">installation guide</a>.</li>\n<li>You need at least Software Provisioning Manager (SWPM) Support Package 21 for the installation. Please make sure that you always use the latest patch level of SWPM available at <a href=\"https://support.sap.com/software/sltoolset.html\" target=\"_blank\">support.sap.com/software/sltoolset.html</a>.</li>\n<li>Multi-Node (Scale-Out) Support possibilities are described in note <a href=\"/notes/2408419\" target=\"_blank\">2408419</a>.</li>\n<li>\n<p><em>Please refer to SAP note <a href=\"/notes/306695\" target=\"_blank\">306695</a> ('Field EMDST13 is unknown' after installing IS-U/CCS) after installation.</em></p>\n</li>\n</ul>\n<p><strong>Upgrade Requirements</strong></p>\n<ul>\n<li>You can upgrade from SAP S/4HANA, on-premise edition 1511 to SAP S/4HANA 1610. <br/>Please be aware that your software level is not higher than Support Package Stack 06.</li>\n<li>For the system upgrade, please refer to the <a href=\"https://help.sap.com/doc/31afbde9e69047298073148662e8aeb2/1610%20002/en-US/UPGR_OP1610_FPS02.pdf\" target=\"_blank\">upgrade guide</a>.</li>\n<li>You need at least Software Update Manager (SUM) 2.0 for the upgrade. Please make sure that you always use the latest patch level of SUM available at <a href=\"https://support.sap.com/software/sltoolset.html\" target=\"_blank\">support.sap.com/software/sltoolset.html</a>.</li>\n<li>If you encounter sequence errors during DDIC_ACTIVATION, please repeat the phase.</li>\n</ul>\n<p><strong>Support Package Update (via Software Update Manager (SUM)):</strong></p>\n<ul>\n<li>If you are using SPAM (instead of SUM) for the SP update to SAP S/4HANA 1610 Support Package Stack 04, please apply note 2603860 (Data Dictionary: Wrong calculation of RC in mass activation) to avoid errors during DDIC_ACTIVATION, before you start the update.</li>\n<li>If the SP-import with SPAM stops in import phase AUTO_MOD_SPAU and if a short dump of type “LOAD_PROGRAM_INTF_MISMATCH” is present in ST22 at the respective import time, please repeat the import phase once. In case repeating the phase does not solve the issue, please contact the SAP Support.</li>\n<li>If you encounter sequence errors during DDIC_ACTIVATION of type “specify reference table AND reference field”, please repeat the import phase once. In case repeating the phase does not solve the issue, please contact the SAP Support.</li>\n<li>Specific errors which might occur if certain Business Function (Sets) are activated</li>\n<li>If IS-Business Function Sets for \"TELCO\" is activated and if you use the generation option in SPAM for the SP update, you might encounter generation errors during SP import of type  “The data object \"LS_xxx\" does not have a component called \"/SAPCE/x\". In this case, please use “Ignore generation errors”-option in SPAM and restart the SP update.</li>\n</ul>\n<p><strong>Notes to be applied on top of Support Package Stack 04</strong></p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"0\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td width=\"82\">\n<p><strong>SAP Note</strong></p>\n</td>\n<td width=\"349\">\n<p><strong>Description</strong></p>\n</td>\n<td valign=\"top\" width=\"126\">\n<p><strong>Software Component</strong></p>\n</td>\n<td valign=\"top\" width=\"175\">\n<p><strong>Manual Activities required</strong></p>\n</td>\n<td valign=\"top\" width=\"142\">\n<p><strong> Added on</strong></p>\n</td>\n</tr>\n<tr>\n<td width=\"82\">\n<p><span ar-sa;\"=\"\" calibri',sans-serif;=\"\" calibri;=\"\" de;=\"\" minor-latin;=\"\" mso-ansi-language:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" mso-fareast-theme-font:=\"\"><a href=\"/notes/2618103\" target=\"_blank\">2618103</a></span></p>\n</td>\n<td valign=\"top\" width=\"349\">\n<p><span ar-sa;\"=\"\" calibri',sans-serif;=\"\" calibri;=\"\" de;=\"\" minor-latin;=\"\" mso-ansi-language:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" mso-fareast-theme-font:=\"\">ALV layout: Layouts cannot be saved from the 'Change Layout' dialog after an error message</span></p>\n</td>\n<td valign=\"top\" width=\"126\">\n<p>SAP_BASIS 7.51</p>\n</td>\n<td valign=\"top\" width=\"175\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"142\">\n<p> 2018-05-11</p>\n</td>\n</tr>\n<tr>\n<td width=\"82\">\n<p><span ar-sa;\"=\"\" calibri',sans-serif;=\"\" calibri;=\"\" de;=\"\" minor-latin;=\"\" mso-ansi-language:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" mso-fareast-theme-font:=\"\"><a href=\"/notes/2624170\" target=\"_blank\">2624170</a></span></p>\n</td>\n<td valign=\"top\" width=\"349\">\n<p><span ar-sa;\"=\"\" calibri',sans-serif;=\"\" calibri;=\"\" de;=\"\" minor-latin;=\"\" mso-ansi-language:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" mso-fareast-theme-font:=\"\">Conversion routine for DATUM data element in BC Set</span></p>\n</td>\n<td valign=\"top\" width=\"126\">\n<p>SAP_BASIS 7.51</p>\n</td>\n<td valign=\"top\" width=\"175\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"142\">\n<p><strong> </strong>2018-05-11</p>\n</td>\n</tr>\n<tr>\n<td width=\"82\">\n<p><span ar-sa;\"=\"\" calibri',sans-serif;=\"\" calibri;=\"\" de;=\"\" minor-latin;=\"\" mso-ansi-language:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" mso-fareast-theme-font:=\"\"><span ar-sa;\"=\"\" calibri',sans-serif;=\"\" calibri;=\"\" de;=\"\" en-us;=\"\" minor-latin;=\"\" mso-ansi-language:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" mso-fareast-theme-font:=\"\"><span ar-sa;\"=\"\" calibri',sans-serif;=\"\" calibri;=\"\" de;=\"\" en-us;=\"\" minor-latin;=\"\" mso-ansi-language:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" mso-fareast-theme-font:=\"\"><a href=\"/notes/2441447\" target=\"_blank\">2441447</a></span></span></span></p>\n</td>\n<td valign=\"top\" width=\"349\">\n<p><span ar-sa;\"=\"\" calibri',sans-serif;=\"\" calibri;=\"\" de;=\"\" minor-latin;=\"\" mso-ansi-language:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" mso-fareast-theme-font:=\"\"> <span ar-sa;\"=\"\" calibri',sans-serif;=\"\" calibri;=\"\" de;=\"\" en-us;=\"\" minor-latin;=\"\" mso-ansi-language:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" mso-fareast-theme-font:=\"\">Authorization check enablement in Business Partner F4 search help</span></span></p>\n</td>\n<td valign=\"top\" width=\"126\">\n<p>SAP_BASIS 7.51</p>\n</td>\n<td valign=\"top\" width=\"175\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"142\">\n<p><span ar-sa;\"=\"\" calibri',sans-serif;=\"\" calibri;=\"\" de;=\"\" en-us;=\"\" minor-latin;=\"\" mso-ansi-language:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" mso-fareast-theme-font:=\"\"> <span ar-sa;\"=\"\" calibri',sans-serif;=\"\" calibri;=\"\" de;=\"\" en-us;=\"\" minor-latin;=\"\" mso-ansi-language:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" mso-fareast-theme-font:=\"\">2018-07-10</span></span></p>\n</td>\n</tr>\n<tr>\n<td width=\"82\">\n<p><span ar-sa;\"=\"\" calibri',sans-serif;=\"\" calibri;=\"\" de;=\"\" minor-latin;=\"\" mso-ansi-language:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" mso-fareast-theme-font:=\"\"><span ar-sa;\"=\"\" calibri',sans-serif;=\"\" calibri;=\"\" de;=\"\" en-us;=\"\" minor-latin;=\"\" mso-ansi-language:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" mso-fareast-theme-font:=\"\"><span ar-sa;\"=\"\" calibri',sans-serif;=\"\" calibri;=\"\" de;=\"\" en-us;=\"\" minor-latin;=\"\" mso-ansi-language:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" mso-fareast-theme-font:=\"\"><a href=\"/notes/2658952\" target=\"_blank\">2658952</a></span></span></span></p>\n</td>\n<td valign=\"top\" width=\"349\">\n<p><span ar-sa;\"=\"\" calibri',sans-serif;=\"\" calibri;=\"\" de;=\"\" minor-latin;=\"\" mso-ansi-language:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" mso-fareast-theme-font:=\"\"><span ar-sa;\"=\"\" calibri',sans-serif;=\"\" calibri;=\"\" de;=\"\" en-us;=\"\" minor-latin;=\"\" mso-ansi-language:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" mso-fareast-theme-font:=\"\">ESH - initial authorization index filling - error: \"Feature not supported\"/OLAP VIEW on SAP HANA 3.1</span></span></p>\n</td>\n<td valign=\"top\" width=\"126\">\n<p>SAP_BASIS 7.51</p>\n</td>\n<td valign=\"top\" width=\"175\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"142\">\n<p><span ar-sa;\"=\"\" calibri',sans-serif;=\"\" calibri;=\"\" de;=\"\" en-us;=\"\" minor-latin;=\"\" mso-ansi-language:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" mso-fareast-theme-font:=\"\"> 2018-07-10</span></p>\n</td>\n</tr>\n<tr>\n<td width=\"82\">\n<p><span ar-sa;\"=\"\" calibri',sans-serif;=\"\" calibri;=\"\" de;=\"\" minor-latin;=\"\" mso-ansi-language:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" mso-fareast-theme-font:=\"\"><span ar-sa;\"=\"\" calibri',sans-serif;=\"\" calibri;=\"\" de;=\"\" en-us;=\"\" minor-latin;=\"\" mso-ansi-language:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" mso-fareast-theme-font:=\"\"><span ar-sa;\"=\"\" calibri',sans-serif;=\"\" calibri;=\"\" de;=\"\" en-us;=\"\" minor-latin;=\"\" mso-ansi-language:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" mso-fareast-theme-font:=\"\"><a href=\"/notes/2660883\" target=\"_blank\">2660883</a></span></span></span></p>\n</td>\n<td valign=\"top\" width=\"349\">\n<p><span ar-sa;\"=\"\" calibri',sans-serif;=\"\" calibri;=\"\" de;=\"\" minor-latin;=\"\" mso-ansi-language:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" mso-fareast-theme-font:=\"\"><span ar-sa;\"=\"\" calibri',sans-serif;=\"\" calibri;=\"\" de;=\"\" en-us;=\"\" minor-latin;=\"\" mso-ansi-language:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" mso-fareast-theme-font:=\"\">BP_EOP: Success Message is not displayed properly</span></span></p>\n</td>\n<td valign=\"top\" width=\"126\">\n<p>SAP_BASIS 7.51</p>\n</td>\n<td valign=\"top\" width=\"175\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"142\">\n<p><span ar-sa;\"=\"\" calibri',sans-serif;=\"\" calibri;=\"\" de;=\"\" en-us;=\"\" minor-latin;=\"\" mso-ansi-language:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" mso-fareast-theme-font:=\"\"> 2018-07-10</span></p>\n</td>\n</tr>\n<tr>\n<td width=\"82\">\n<p><span ar-sa;\"=\"\" calibri',sans-serif;=\"\" calibri;=\"\" de;=\"\" minor-latin;=\"\" mso-ansi-language:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" mso-fareast-theme-font:=\"\"><span ar-sa;\"=\"\" calibri',sans-serif;=\"\" calibri;=\"\" de;=\"\" en-us;=\"\" minor-latin;=\"\" mso-ansi-language:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" mso-fareast-theme-font:=\"\"><a href=\"/notes/2652897\" target=\"_blank\">2652897</a></span></span></p>\n</td>\n<td valign=\"top\" width=\"349\">\n<p><span ar-sa;\"=\"\" calibri',sans-serif;=\"\" calibri;=\"\" de;=\"\" minor-latin;=\"\" mso-ansi-language:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" mso-fareast-theme-font:=\"\"> <span ar-sa;\"=\"\" calibri',sans-serif;=\"\" calibri;=\"\" de;=\"\" en-us;=\"\" minor-latin;=\"\" mso-ansi-language:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" mso-fareast-theme-font:=\"\">List ATS UIBB and Tree UIBB: API for choosing selection eventing not working correctly</span></span></p>\n</td>\n<td valign=\"top\" width=\"126\">\n<p> S<span ar-sa;\"=\"\" calibri',sans-serif;=\"\" calibri;=\"\" de;=\"\" en-us;=\"\" minor-latin;=\"\" mso-ansi-language:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" mso-fareast-theme-font:=\"\">AP_UI</span></p>\n</td>\n<td valign=\"top\" width=\"175\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"142\">\n<p><span ar-sa;\"=\"\" calibri',sans-serif;=\"\" calibri;=\"\" de;=\"\" en-us;=\"\" minor-latin;=\"\" mso-ansi-language:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" mso-fareast-theme-font:=\"\">  2018-07-23</span></p>\n</td>\n</tr>\n<tr>\n<td width=\"82\">\n<p><span ar-sa;\"=\"\" calibri',sans-serif;=\"\" calibri;=\"\" de;=\"\" minor-latin;=\"\" mso-ansi-language:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" mso-fareast-theme-font:=\"\"><span ar-sa;\"=\"\" calibri',sans-serif;=\"\" calibri;=\"\" de;=\"\" en-us;=\"\" minor-latin;=\"\" mso-ansi-language:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" mso-fareast-theme-font:=\"\"> <span ar-sa;\"=\"\" calibri',sans-serif;=\"\" calibri;=\"\" de;=\"\" en-us;=\"\" minor-latin;=\"\" mso-ansi-language:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" mso-fareast-theme-font:=\"\"><a href=\"/notes/2655756\" target=\"_blank\">2655756</a></span></span></span></p>\n</td>\n<td valign=\"top\" width=\"349\">\n<p><span ar-sa;\"=\"\" calibri',sans-serif;=\"\" calibri;=\"\" de;=\"\" minor-latin;=\"\" mso-ansi-language:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" mso-fareast-theme-font:=\"\"> <span ar-sa;\"=\"\" calibri',sans-serif;=\"\" calibri;=\"\" de;=\"\" en-us;=\"\" minor-latin;=\"\" mso-ansi-language:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" mso-fareast-theme-font:=\"\">Tree UIBB: Conditional Formatting when master column has display type image</span></span></p>\n</td>\n<td valign=\"top\" width=\"126\">\n<p> SAP_UI</p>\n</td>\n<td valign=\"top\" width=\"175\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"142\">\n<p><span ar-sa;\"=\"\" calibri',sans-serif;=\"\" calibri;=\"\" de;=\"\" en-us;=\"\" minor-latin;=\"\" mso-ansi-language:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" mso-fareast-theme-font:=\"\"> 2018-07-23</span></p>\n</td>\n</tr>\n</tbody>\n</table></div>\n<p> </p>\n<p><strong>Important Changes made after Release of Feature Package Stack 04</strong></p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"0\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td width=\"82\">\n<p><strong>SAP Note</strong></p>\n</td>\n<td width=\"349\">\n<p><strong>Description</strong></p>\n</td>\n<td valign=\"top\" width=\"126\">\n<p><strong>Software Component</strong></p>\n</td>\n<td valign=\"top\" width=\"175\">\n<p><strong>Manual Activities required</strong></p>\n</td>\n<td valign=\"top\" width=\"142\">\n<p><strong> Added on</strong></p>\n</td>\n</tr>\n<tr>\n<td width=\"82\">\n<p> <a href=\"/notes/273869\" target=\"_blank\">2738698</a></p>\n</td>\n<td valign=\"top\" width=\"349\">\n<p>Bank chains: determination of intermediary banks with unexpected results</p>\n</td>\n<td valign=\"top\" width=\"126\">\n<p> S4CORE</p>\n</td>\n<td valign=\"top\" width=\"175\">\n<p> No</p>\n</td>\n<td valign=\"top\" width=\"142\">\n<p> 11.01.2019</p>\n</td>\n</tr>\n</tbody>\n</table></div>\n<p> </p>\n<p><strong>SUPPORT PACKAGE STACK 03 (10/2017)</strong></p>\n<p><strong>General / Important Considerations</strong></p>\n<ul>\n<li>Please refer to the specific documentation under section <a href=\"https://help.sap.com/viewer/product/SAP_S4HANA_ON-PREMISE/1610%20002/en-US\" target=\"_blank\">product documentation</a>.</li>\n<li>SAP HANA 1.0</li>\n<ul>\n<li>The minimum required revision of SAP HANA 1.0 is Revision 122.05. SAP in general recommends customers to implement the highest SAP HANA 1.0 revision available. For more details, please refer to note <a href=\"/notes/2021789\" target=\"_blank\">2021789</a> (SAP HANA 1.0 Revision and Maintenance Strategy).</li>\n</ul>\n<li>SAP HANA 2.0</li>\n<ul>\n<li>The minimum required revision of SAP HANA 2.0 is SP0 Revision 002. SAP in general recommends customers to implement the highest SAP HANA 2.0 revision available. For more details, please refer to note <a href=\"/notes/2378962\" target=\"_blank\">2378962</a> (SAP HANA 2.0 Revision and Maintenance Strategy).</li>\n<li>Please refer to note <a href=\"/notes/2426477\" target=\"_blank\" title=\"2426477  - Support for SAP HANA 2 in SAP S/4HANA 1610 - Status of application support for specific functionalities of SAP HANA 2\">2426477</a> which describes new features of SAP HANA 2.0 already been adopted by SAP S/4HANA 1610 and note <a href=\"/notes/2426339\" target=\"_blank\" title=\"2426339  - Support for SAP HANA 2 in SAP S/4HANA - Technical Information Regarding SAP HANA Requirements\">2426339 </a>which informs about technical requirements to the database system (hardware, operating system, etc.) introduced with SAP HANA 2.0 that may be relevant if you plan to operate your SAP S/4HANA 1610 system on SAP HANA 2.0.</li>\n<li>Furthermore, refer to the general restriction note <a href=\"/notes/2333141\" target=\"_blank\">2333141</a> also for restrictions relevant for SAP S/4HANA 1610 on HANA 2.0.</li>\n<li>Please refer to note <a href=\"/notes/2487855\" target=\"_blank\">2487855</a> (Possible Table Inconsistencies on SAP HANA 2.0 SPS01 due to Internal Persistence Structure Migration) if you are on SAP HANA 2.0 Revision 010.00 or 011.00 (SPS01) or if you plan to upgrade to these SPS01 revisions. </li>\n<li>SAP HANA 2.0 SPS 02 Revision 20: Please check SAP note <a href=\"/notes/2527538\" target=\"_blank\">2527538</a> (AMDP Procedure Execution Fails With Column Store Error) and SAP note <a href=\"/notes/2527648\" target=\"_blank\">2527648</a> (Column Value Equals Null Cannot be Detected by IS NULL in Debug Mode).</li>\n</ul>\n<li>For the specific Front-end/UI for SAP S/4HANA 1610, please refer to SAP note <a href=\"/notes/2356208\" target=\"_blank\">2356208</a> which is the release information note for product version SAP FIORI FOR SAP S/4HANA 1610. Support Package Stack 03 of SAP S/4HANA 1610 in the backend requires Support Package Stack 01 or higher of SAP Fiori 2.0 for SAP S/4HANA on the frontend (and vice versa).</li>\n<li>Please refer to note <a href=\"/notes/2539548\" target=\"_blank\">2539548</a> (SAP S/4HANA, on-premise edition 1610 - SAP S/4HANA ON-PREMISE 1610 SP stack 03 (10/2017) content activation note) when you are using the SAP Best Practices for your SAP S/4HANA on premise implementation and you need current information for the tool and the content.</li>\n<li>Visual theme “Belize” for SAP GUI for Windows”:<br/>For the usage of SAP S/4HANA 1610 Support Package Stack 03 with SAP GUI for Windows there are few prerequisites:</li>\n<ul>\n<li>Installation of SAP GUI for Windows 7.50 or higher (desktop client)</li>\n<li>Installation of SAP Kernel 7.49 Patch 210 or higher</li>\n</ul>\n<li>If you would like to use a Fiori Launchpad (FLP) with integrated SAP GUI for Windows applications, please install SAP Business Client 6.5. Please refer to note <a href=\"/notes/2348661\" target=\"_blank\">2348661</a> - Restrictions of SAP Fiori visual theme for classic applications.</li>\n</ul>\n<p><strong>Installation Requirements</strong></p>\n<ul>\n<li>For the system installation, please refer to the <a href=\"https://help.sap.com/doc/14f9236f8e234dccbb195259a8eaf616/1610%20002/en-US/INST_OP1610_FPS02.pdf\" target=\"_blank\">installation guide</a>.</li>\n<li>You need at least Software Provisioning Manager (SWPM) Support Package 21 for the installation. Please make sure that you always use the latest patch level of SWPM available at <a href=\"https://support.sap.com/software/sltoolset.html\" target=\"_blank\">support.sap.com/software/sltoolset.html</a>.</li>\n<li>Multi-Node (Scale-Out) Support possibilities are described in note <a href=\"/notes/2408419\" target=\"_blank\">2408419</a>.</li>\n<li>\n<p><em>Please refer to SAP note <a href=\"/notes/306695\" target=\"_blank\">306695</a> ('Field EMDST13 is unknown' after installing IS-U/CCS) after installation.</em></p>\n</li>\n</ul>\n<p><strong>Upgrade Requirements</strong></p>\n<ul>\n<li>You can upgrade from SAP S/4HANA, on-premise edition 1511 to SAP S/4HANA 1610. <br/>Please be aware that your software level is not higher than Support Package Stack 05.</li>\n<li>For the system upgrade, please refer to the <a href=\"https://help.sap.com/doc/31afbde9e69047298073148662e8aeb2/1610%20002/en-US/UPGR_OP1610_FPS02.pdf\" target=\"_blank\">upgrade guide</a>.</li>\n<li>You need at least Software Update Manager (SUM) 2.0 for the upgrade. Please make sure that you always use the latest patch level of SUM available at <a href=\"https://support.sap.com/software/sltoolset.html\" target=\"_blank\">support.sap.com/software/sltoolset.html</a>.</li>\n<li>If you encounter sequence errors during DDIC_ACTIVATION, please repeat the phase.</li>\n</ul>\n<p> </p>\n<p><strong>Support Package Update (via Software Update Manager (SUM)):</strong></p>\n<ul>\n<li>If you are using SPAM for the SP update (instead of SUM), please apply note <a href=\"/notes/2474810\" target=\"_blank\">2474810</a> before you start the SP update to SAP S/4HANA 1610 Support Package Stack 03 to avoid errors during  DDIC_ACTIVATION of type “Key fields are not together at the beginning of the view\".</li>\n<li>If you encounter sequence errors during DDIC_ACTIVATION of type “specify reference table AND reference field”, please repeat the import phase once. In case repeating the phase does not solve the issue, please contact the SAP Support.</li>\n<li>Specific errors which might occur if certain Business Function (Sets) are activated</li>\n<li>If IS-Business Function Sets for \"TELCO\" is activated and if you use the generation option in SPAM for the SP update, you might encounter generation errors during SP import of type  “The data object \"LS_xxx\" does not have a component called \"/SAPCE/x\". In this case, please use “Ignore generation errors”-option in SPAM and restart the SP update.</li>\n<li>If switch GLO_REP_EAPS_SFWS_03 is activated, you might encounter errors during AFTER_IMPORT_METHOD SCPR_SCP2_AFTER_IMPORT for the object R3TR SCP2 GLO_REP_SK_03_IDREPFW_SELPA_C. In this case, please repeat the phase up to 2 times. In case repeating the phase twice does not solve the issue, please contact the SAP Support.</li>\n</ul>\n<p><strong>Notes to be applied on top of Support Package Stack 03</strong></p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"0\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td width=\"82\">\n<p><strong>SAP Note</strong></p>\n</td>\n<td width=\"349\">\n<p><strong>Description</strong></p>\n</td>\n<td valign=\"top\" width=\"126\">\n<p><strong>Software Component</strong></p>\n</td>\n<td valign=\"top\" width=\"175\">\n<p><strong>Manual Activities required</strong></p>\n</td>\n<td valign=\"top\" width=\"142\">\n<p><strong> Added on</strong></p>\n</td>\n</tr>\n<tr>\n<td width=\"82\">\n<p><a href=\"https://i7p.wdf.sap.corp/sap(bD1lbiZjPTAwMQ==)/bc/bsp/sno/ui_entry/entry.htm?param=69765F6D6F64653D3030312669765F7361706E6F7465735F6E756D6265723D3235373030323926\" target=\"_blank\">2570029</a></p>\n</td>\n<td valign=\"top\" width=\"349\">\n<p>Connector creation: Dump due to memory overflow</p>\n</td>\n<td valign=\"top\" width=\"126\">\n<p>SAP_BASIS 7.51</p>\n</td>\n<td valign=\"top\" width=\"175\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"142\">\n<p> 2018-02-02</p>\n</td>\n</tr>\n<tr>\n<td width=\"82\">\n<p><a href=\"/notes/2566812\" target=\"_blank\">2566812</a></p>\n</td>\n<td valign=\"top\" width=\"349\">\n<p>Application area TM/Transportation Management</p>\n</td>\n<td valign=\"top\" width=\"126\">\n<p>SAP_BASIS 7.51</p>\n</td>\n<td valign=\"top\" width=\"175\">\n<p>Yes</p>\n</td>\n<td valign=\"top\" width=\"142\">\n<p><strong> </strong>2018-02-02</p>\n</td>\n</tr>\n<tr>\n<td width=\"82\">\n<p><a href=\"/notes/2537567\" target=\"_blank\">2537567</a></p>\n</td>\n<td valign=\"top\" width=\"349\">\n<p>EHP8_SP08: Issue with workforce viewer application</p>\n</td>\n<td valign=\"top\" width=\"126\">\n<p>EA-HRRXX 608<br/>(valid to SP46)</p>\n</td>\n<td valign=\"top\" width=\"175\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"142\">\n<p><strong> </strong>2017-10-24</p>\n</td>\n</tr>\n<tr>\n<td width=\"82\">\n<p><a href=\"/notes/2352024\" target=\"_blank\">2352024</a></p>\n</td>\n<td valign=\"top\" width=\"349\">\n<p>Time dependant promotion data has not been deleted when transferring the data in parallel mode</p>\n</td>\n<td valign=\"top\" width=\"126\">\n<p>S4CORE <br/>(SCM-FRE-ERP)</p>\n</td>\n<td valign=\"top\" width=\"175\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"142\">\n<p> 2017-10-24</p>\n</td>\n</tr>\n<tr>\n<td width=\"82\">\n<p><a href=\"/notes/2340156\" target=\"_blank\">2340156</a></p>\n</td>\n<td valign=\"top\" width=\"349\">\n<p>WRF_DISP_CON does not delete TD procurement data from F&amp;R</p>\n</td>\n<td valign=\"top\" width=\"126\">\n<p>S4CORE <br/>(SCM-FRE-ERP)</p>\n</td>\n<td valign=\"top\" width=\"175\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"142\">\n<p> 2017-10-24</p>\n</td>\n</tr>\n</tbody>\n</table></div>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"0\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td width=\"82\">\n<p><a href=\"/notes/2652897\" target=\"_blank\">2652897</a></p>\n</td>\n<td valign=\"top\" width=\"349\">\n<p> List ATS UIBB and Tree UIBB: API for choosing selection eventing not working correctly</p>\n</td>\n<td valign=\"top\" width=\"126\">\n<p> SAP_UI</p>\n</td>\n<td valign=\"top\" width=\"175\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"142\">\n<p>  2018-07-23</p>\n</td>\n</tr>\n<tr>\n<td width=\"82\">\n<p> <a href=\"/notes/2655756\" target=\"_blank\">2655756</a></p>\n</td>\n<td valign=\"top\" width=\"349\">\n<p> Tree UIBB: Conditional Formatting when master column has display type image</p>\n</td>\n<td valign=\"top\" width=\"126\">\n<p> SAP_UI</p>\n</td>\n<td valign=\"top\" width=\"175\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"142\">\n<p> 2018-07-23</p>\n</td>\n</tr>\n</tbody>\n</table></div>\n<p> </p>\n<p><strong>Important Changes made after Release of Feature Package Stack 03</strong></p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"0\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td width=\"82\">\n<p><strong>SAP Note</strong></p>\n</td>\n<td width=\"349\">\n<p><strong>Description</strong></p>\n</td>\n<td valign=\"top\" width=\"126\">\n<p><strong>Software Component</strong></p>\n</td>\n<td valign=\"top\" width=\"175\">\n<p><strong>Manual Activities required</strong></p>\n</td>\n<td valign=\"top\" width=\"142\">\n<p><strong> Added on</strong></p>\n</td>\n</tr>\n<tr>\n<td width=\"82\">\n<p> <a href=\"/notes/273869\" target=\"_blank\">2738698</a></p>\n</td>\n<td valign=\"top\" width=\"349\">\n<p>Bank chains: determination of intermediary banks with unexpected results</p>\n</td>\n<td valign=\"top\" width=\"126\">\n<p> S4CORE</p>\n</td>\n<td valign=\"top\" width=\"175\">\n<p> No</p>\n</td>\n<td valign=\"top\" width=\"142\">\n<p> 11.01.2019</p>\n</td>\n</tr>\n</tbody>\n</table></div>\n<p> </p>\n<p><strong><span>FEATURE PACKAGE STACK 02 (05/2017)</span></strong></p>\n<p><strong>General / Important Considerations</strong></p>\n<ul>\n<li>Please refer to the specific documentation under section <a href=\"https://help.sap.com/viewer/product/SAP_S4HANA_ON-PREMISE/1610%20002/en-US\" target=\"_blank\">product documentation</a>.</li>\n<li>SAP HANA 1.0</li>\n<ul>\n<li>The minimum required revision of SAP HANA 1.0 is Revision 122.05. SAP in general recommends customers to implement the highest SAP HANA 1.0 revision available. For more details, please refer to note <a href=\"/notes/2021789\" target=\"_blank\">2021789</a> (SAP HANA 1.0 Revision and Maintenance Strategy).</li>\n</ul>\n<li>SAP HANA 2.0</li>\n<ul>\n<li>The minimum required revision of SAP HANA 2.0 is SP0 Revision 002. SAP in general recommends customers to implement the highest SAP HANA 2.0 revision available. For more details, please refer to note <a href=\"/notes/2378962\" target=\"_blank\">2378962</a> (SAP HANA 2.0 Revision and Maintenance Strategy).</li>\n<li>Please refer to note <a href=\"/notes/2426477\" target=\"_blank\" title=\"2426477  - Support for SAP HANA 2 in SAP S/4HANA 1610 - Status of application support for specific functionalities of SAP HANA 2\">2426477</a> which describes new features of SAP HANA 2.0 already been adopted by SAP S/4HANA 1610 and note <a href=\"/notes/2426339\" target=\"_blank\" title=\"2426339  - Support for SAP HANA 2 in SAP S/4HANA - Technical Information Regarding SAP HANA Requirements\">2426339 </a>which informs about technical requirements to the database system (hardware, operating system, etc.) introduced with SAP HANA 2.0 that may be relevant if you plan to operate your SAP S/4HANA 1610 system on SAP HANA 2.0.</li>\n<li>Furthermore, refer to the general restriction note <a href=\"/notes/2333141\" target=\"_blank\">2333141</a> also for restrictions relevant for SAP S/4HANA 1610 on HANA 2.0.</li>\n<li>Please refer to note <a href=\"/notes/2487855\" target=\"_blank\">2487855</a> (Possible Table Inconsistencies on SAP HANA 2.0 SPS01 due to Internal Persistence Structure Migration) if you are on SAP HANA 2.0 Revision 010.00 or 011.00 (SPS01) or if you plan to upgrade to these SPS01 revisions. </li>\n<li>\n<p>SAP HANA 2.0 SPS 02 Revision 20: Please check SAP note <a href=\"/notes/2527538\" target=\"_blank\">2527538</a> (AMDP Procedure Execution Fails With Column Store Error) and SAP note <a href=\"/notes/2527648\" target=\"_blank\">2527648</a> (Column Value Equals Null Cannot be Detected by IS NULL in Debug Mode).</p>\n</li>\n</ul>\n<li>For the specific Front-end/UI for SAP S/4HANA 1610, please refer to SAP note <a href=\"/notes/2356208\" target=\"_blank\">2356208</a> which is the release information note for product version SAP FIORI FOR SAP S/4HANA 1610. Feature Package Stack 02 of SAP S/4HANA 1610 in the backend requires Support Package Stack 01 or higher of SAP Fiori 2.0 for SAP S/4HANA on the frontend (and vice versa).</li>\n<li>\n<p>Please refer to note <a href=\"/notes/2400710\" target=\"_blank\">2400710</a> (SAP S/4HANA, on-premise edition 1610 - SAP S/4HANA ON-PREMISE 1610 FP stack 01 (02/2017) content activation note) when you are using the SAP Best Practices for your SAP S/4HANA on premise implementation and you need current information for the tool and the content.</p>\n</li>\n<li>Visual theme “Belize” for SAP GUI for Windows”:<br/>For the usage of SAP S/4HANA 1610 Feature Package Stack 02 with SAP GUI for Windows there are few prerequisites:</li>\n<ul>\n<li>Installation of SAP GUI for Windows 7.50 or higher (desktop client)</li>\n<li>Installation of SAP Kernel 7.49 Patch 210 or higher</li>\n</ul>\n<li>If you would like to use a Fiori Launchpad (FLP) with integrated SAP GUI for Windows applications, please install SAP Business Client 6.5. Please refer to note <a href=\"/notes/2348661\" target=\"_blank\">2348661</a> - Restrictions of SAP Fiori visual theme for classic applications.</li>\n<li>In case you are using roles from <strong>Retail</strong> in FIORI Launchpad and plan to upload the backend app descriptors for area S4RFM please check the instructions of note <a href=\"/notes/2550359\" target=\"_blank\">2550359</a>.</li>\n</ul>\n<p>Installation Requirements</p>\n<ul>\n<li>For the system installation, please refer to the <a href=\"https://help.sap.com/doc/14f9236f8e234dccbb195259a8eaf616/1610%20002/en-US/INST_OP1610_FPS02.pdf\" target=\"_blank\">installation guide</a>.</li>\n<li>You need at least Software Provisioning Manager (SWPM) Support Package 20 for the installation. Please make sure that you always use the latest patch level of SWPM available at <a href=\"https://support.sap.com/software/sltoolset.html\" target=\"_blank\">support.sap.com/software/sltoolset.html</a>.</li>\n<li>Multi-Node (Scale-Out) Support possibilities are described in note <a href=\"/notes/2408419\" target=\"_blank\">2408419</a>.</li>\n<li>\n<p><em>Please refer to SAP note <a href=\"/notes/306695\" target=\"_blank\">306695</a> ('Field EMDST13 is unknown' after installing IS-U/CCS) after installation.</em></p>\n</li>\n</ul>\n<p><strong>Upgrade Requirements</strong></p>\n<ul>\n<li>You can upgrade from SAP S/4HANA, on-premise edition 1511 to SAP S/4HANA 1610. <br/>Please be aware that your software level is not higher than Support Package Stack 04.</li>\n<li>For the system upgrade, please refer to the <a href=\"https://help.sap.com/doc/31afbde9e69047298073148662e8aeb2/1610%20002/en-US/UPGR_OP1610_FPS02.pdf\" target=\"_blank\">upgrade guide</a>.</li>\n<li>You need at least Software Update Manager (SUM) Support Package 20 for the upgrade. Please make sure that you always use the latest patch level of SUM available at <a href=\"https://support.sap.com/software/sltoolset.html\" target=\"_blank\">support.sap.com/software/sltoolset.html</a>.</li>\n</ul>\n<p> </p>\n<p><strong>Feature Package Update (via Software Update Manager (SUM)):</strong></p>\n<ul>\n<li>It is recommended to use SUM to apply Feature Package Stacks.</li>\n</ul>\n<p><strong>Notes to be applied on top of Feature Package Stack 02</strong></p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"0\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td width=\"82\">\n<p><strong>SAP Note</strong></p>\n</td>\n<td width=\"349\">\n<p><strong>Description</strong></p>\n</td>\n<td valign=\"top\" width=\"126\">\n<p><strong>Software Component</strong></p>\n</td>\n<td valign=\"top\" width=\"175\">\n<p><strong>Manual Activities required</strong></p>\n</td>\n<td valign=\"top\" width=\"142\">\n<p><strong> Added on</strong></p>\n</td>\n</tr>\n<tr>\n<td width=\"82\">\n<p><a href=\"/notes/2372221\" target=\"_blank\">2372221</a></p>\n</td>\n<td valign=\"top\" width=\"349\">\n<p>Performance improvement on GT_ADDR_XPCPT</p>\n</td>\n<td valign=\"top\" width=\"126\">\n<p>SAP_BASIS</p>\n</td>\n<td valign=\"top\" width=\"175\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"142\">\n<p><strong> </strong>2017-05-09</p>\n</td>\n</tr>\n<tr>\n<td width=\"82\">\n<p><a href=\"/notes/2436731\" target=\"_blank\">2436731</a></p>\n</td>\n<td valign=\"top\" width=\"349\">\n<p>Job scheduling failed due to invalid date</p>\n</td>\n<td valign=\"top\" width=\"126\">\n<p>SAP_BASIS</p>\n</td>\n<td valign=\"top\" width=\"175\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"142\">\n<p> 2017-05-09</p>\n</td>\n</tr>\n<tr>\n<td width=\"82\">\n<p><a href=\"/notes/2445210\" target=\"_blank\">2445210</a></p>\n</td>\n<td valign=\"top\" width=\"349\">\n<p>SAP GUI for HTML: Incorrect handling of transaction codes with a dash '-'</p>\n</td>\n<td valign=\"top\" width=\"126\">\n<p>SAP_BASIS</p>\n</td>\n<td valign=\"top\" width=\"175\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"142\">\n<p> 2017-05-09</p>\n</td>\n</tr>\n<tr>\n<td width=\"82\">\n<p><a href=\"/notes/2461676\" target=\"_blank\">2461676</a></p>\n</td>\n<td valign=\"top\" width=\"349\">\n<p>New MM-GPD stock sync validation to consider legacy MM</p>\n</td>\n<td valign=\"top\" width=\"126\">\n<p>S4CORE</p>\n</td>\n<td valign=\"top\" width=\"175\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"142\">\n<p> 2017-05-09</p>\n</td>\n</tr>\n<tr>\n<td width=\"82\">\n<p><a href=\"/notes/2467650\" target=\"_blank\">2467650</a></p>\n</td>\n<td valign=\"top\" width=\"349\">\n<p>Authorization issues in viewing Document Info Record</p>\n</td>\n<td valign=\"top\" width=\"126\">\n<p>S4CORE</p>\n</td>\n<td valign=\"top\" width=\"175\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"142\">\n<p> 2017-05-09</p>\n</td>\n</tr>\n<tr>\n<td width=\"82\">\n<p><a href=\"/notes/2463740\" target=\"_blank\">2463740</a></p>\n</td>\n<td valign=\"top\" width=\"349\">\n<p>Error message FINS_ACDOC_CUST 215 during postings</p>\n</td>\n<td valign=\"top\" width=\"126\">\n<p>S4CORE</p>\n</td>\n<td valign=\"top\" width=\"175\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"142\">\n<p> 2017-05-19</p>\n</td>\n</tr>\n<tr>\n<td width=\"82\">\n<p><span class=\"sapUxAPObjectPageHeaderIdentifierContainer\" id=\"__xmlview2--idObjectPageHeader-identifierLineContainer\"><a href=\"/notes/2477735\" target=\"_blank\">2477735</a></span></p>\n</td>\n<td valign=\"top\" width=\"349\">\n<p>Annotation API: enable cache usage for get_annos_mass</p>\n</td>\n<td valign=\"top\" width=\"126\">\n<p>SAP_BASIS</p>\n</td>\n<td valign=\"top\" width=\"175\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"142\">\n<p> 2017-07-10</p>\n</td>\n</tr>\n<tr>\n<td width=\"82\">\n<p><span class=\"sapUxAPObjectPageHeaderIdentifierContainer\"><a href=\"/notes/2450514\" target=\"_blank\">2450514</a></span></p>\n</td>\n<td valign=\"top\" width=\"349\">\n<p>BRFplus: Numeric Comparison doesn't work within IF-FORMULA</p>\n</td>\n<td valign=\"top\" width=\"126\">\n<p>SAP_BASIS</p>\n</td>\n<td valign=\"top\" width=\"175\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"142\">\n<p> 2017-07-12</p>\n</td>\n</tr>\n<tr>\n<td width=\"82\">\n<p><span class=\"sapUxAPObjectPageHeaderIdentifierContainer\"><a href=\"/notes/2489305\" target=\"_blank\">2489305</a></span></p>\n</td>\n<td valign=\"top\" width=\"349\">\n<p>Dump while inserting international address version for a person</p>\n</td>\n<td valign=\"top\" width=\"126\">\n<p>SAP_BASIS</p>\n</td>\n<td valign=\"top\" width=\"175\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"142\">\n<p> 2017-07-12</p>\n</td>\n</tr>\n<tr>\n<td width=\"82\">\n<p><span class=\"sapUxAPObjectPageHeaderIdentifierContainer\"><a href=\"/notes/2485784\" target=\"_blank\">2485784</a></span></p>\n</td>\n<td valign=\"top\" width=\"349\">\n<p>ALV export: Cannot save to clipboard in browser</p>\n</td>\n<td valign=\"top\" width=\"126\">\n<p>SAP_BASIS</p>\n</td>\n<td valign=\"top\" width=\"175\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"142\">\n<p> 2017-07-12</p>\n</td>\n</tr>\n<tr>\n<td width=\"82\">\n<p><span class=\"sapUxAPObjectPageHeaderIdentifierContainer\"><a href=\"/notes/2490652\" target=\"_blank\">2490652</a></span></p>\n</td>\n<td valign=\"top\" width=\"349\">\n<p>Legacy DAC maps business key to initial BOPF key</p>\n</td>\n<td valign=\"top\" width=\"126\">\n<p>SAP_BASIS</p>\n</td>\n<td valign=\"top\" width=\"175\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"142\">\n<p> 2017-07-12</p>\n</td>\n</tr>\n<tr>\n<td width=\"82\">\n<p><span class=\"sapUxAPObjectPageHeaderIdentifierContainer\"><a href=\"/notes/2491892\" target=\"_blank\">2491892</a></span></p>\n</td>\n<td valign=\"top\" width=\"349\">\n<p>DD: keep switched off objects in gentab for DD_WORKLIST_ACT</p>\n</td>\n<td valign=\"top\" width=\"126\">\n<p>SAP_BASIS</p>\n</td>\n<td valign=\"top\" width=\"175\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"142\">\n<p> 2017-07-27</p>\n</td>\n</tr>\n<tr>\n<td width=\"82\">\n<p><a href=\"/notes/2500159\" target=\"_blank\">2500159</a></p>\n</td>\n<td valign=\"top\" width=\"349\">\n<p>MESSAGE_TYPE_X issued during delivery creation</p>\n</td>\n<td valign=\"top\" width=\"126\">\n<p>S4CORE</p>\n</td>\n<td valign=\"top\" width=\"175\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"142\">\n<p> 2017-08-28</p>\n</td>\n</tr>\n<tr>\n<td width=\"82\">\n<p><a href=\"/notes/2520306\" target=\"_blank\">2520306</a></p>\n</td>\n<td valign=\"top\" width=\"349\">\n<p>Condition table index not activated after upgrade</p>\n</td>\n<td valign=\"top\" width=\"126\">\n<p>S4CORE</p>\n</td>\n<td valign=\"top\" width=\"175\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"142\">\n<p> 2017-09-06</p>\n</td>\n</tr>\n<tr>\n<td width=\"82\">\n<p><a href=\"/notes/2527709\" target=\"_blank\">2527709</a></p>\n</td>\n<td valign=\"top\" width=\"349\">\n<p>Program Dump after activating Business Function LOG_PP_EWM_MAN_2</p>\n</td>\n<td valign=\"top\" width=\"126\">\n<p>S4CORE</p>\n</td>\n<td valign=\"top\" width=\"175\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"142\">\n<p> 2017-09-06</p>\n</td>\n</tr>\n<tr>\n<td width=\"82\">\n<p><a href=\"/notes/2485570\" target=\"_blank\">2485570</a></p>\n</td>\n<td valign=\"top\" width=\"349\">\n<p>Downport of bugfix for CDS Metadata Extensions Runtime Data Provider</p>\n</td>\n<td valign=\"top\" width=\"126\">\n<p>SAP_BASIS</p>\n</td>\n<td valign=\"top\" width=\"175\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"142\">\n<p> 2017-09-07</p>\n</td>\n</tr>\n</tbody>\n</table></div>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"0\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td width=\"82\">\n<p><a href=\"/notes/2652897\" target=\"_blank\">2652897</a></p>\n</td>\n<td valign=\"top\" width=\"349\">\n<p> List ATS UIBB and Tree UIBB: API for choosing selection eventing not working correctly</p>\n</td>\n<td valign=\"top\" width=\"126\">\n<p> SAP_UI</p>\n</td>\n<td valign=\"top\" width=\"175\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"142\">\n<p>  2018-07-23</p>\n</td>\n</tr>\n<tr>\n<td width=\"82\">\n<p> <a href=\"/notes/2655756\" target=\"_blank\">2655756</a></p>\n</td>\n<td valign=\"top\" width=\"349\">\n<p> Tree UIBB: Conditional Formatting when master column has display type image</p>\n</td>\n<td valign=\"top\" width=\"126\">\n<p> SAP_UI</p>\n</td>\n<td valign=\"top\" width=\"175\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"142\">\n<p> 2018-07-23</p>\n</td>\n</tr>\n</tbody>\n</table></div>\n<p><strong>Important Changes made after Release of Feature Package Stack 02</strong></p>\n<p><strong><strong><strong><strong><strong>2017-10-19: </strong></strong></strong></strong></strong>Inserted:In case you are using roles from <strong>Retail</strong> in FIORI Launchpad and plan to upload the backend app descriptors for area S4RFM please check the instructions of note <a href=\"/notes/2550359\" target=\"_blank\">2550359</a>.<br/><strong><strong><strong><strong>2017-09-04: </strong></strong></strong></strong>SAP HANA 2.0 SPS 02 Revision 20: SAP note <a href=\"/notes/2527538\" target=\"_blank\">2527538</a> (AMDP Procedure Execution Fails With Column Store Error) and SAP note <a href=\"/notes/2527648\" target=\"_blank\">2527648</a> (Column Value Equals Null Cannot be Detected by IS NULL in Debug Mode) inserted.<strong><strong><strong><strong><strong><strong><br/></strong></strong>2017-08-30</strong>: </strong></strong></strong>SAP HANA 1.0 minimum required revision changed from Revision 122.03 to 122.05 to synchronize with requirements in SUM tool. <strong><strong><br/></strong></strong><strong><strong>2017-06-19</strong>: </strong>SAP HANA 2.0 related note <a href=\"/notes/2487855\" target=\"_blank\">2487855</a> (Possible Table Inconsistencies on SAP HANA 2.0 SPS01 due to Internal Persistence Structure Migration) inserted.<strong> <br/></strong><strong>2017-06-12</strong>: SWPM and SUM Support Package Level increased to 20.</p>\n<p><strong><span>FEATURE PACKAGE STACK 01 (02/2017)</span></strong></p>\n<p><strong>General / Important Considerations</strong></p>\n<ul>\n<li>Please refer to the specific documentation under section <a href=\"https://help.sap.com/viewer/product/SAP_S4HANA_ON-PREMISE/1610%20001/en-US\" target=\"_blank\">product documentation</a>.</li>\n<li>Feature Package Stack 01 is released on SAP HANA 1.0 and SAP HANA 2.0.</li>\n<ul>\n<li>SAP HANA 1.0</li>\n<ul>\n<li>The minimum required revision of SAP HANA 1.0 is Revision 122.05. SAP in general recommends customers to implement the highest SAP HANA 1.0 revision available. For more details, please refer to note <a href=\"/notes/2021789\" target=\"_blank\">2021789</a> (SAP HANA 1.0 Revision and Maintenance Strategy).</li>\n</ul>\n<li>SAP HANA 2.0</li>\n<ul>\n<li>The minimum required revision of SAP HANA 2.0 is SP0 Revision 002. SAP in general recommends customers to implement the highest SAP HANA 2.0 revision available. For more details, please refer to note <a href=\"/notes/2378962\" target=\"_blank\">2378962</a> (SAP HANA 2.0 Revision and Maintenance Strategy).</li>\n<li>Please refer to note <a href=\"/notes/2426477\" target=\"_blank\" title=\"2426477  - Support for SAP HANA 2 in SAP S/4HANA 1610 - Status of application support for specific functionalities of SAP HANA 2\">2426477</a> which describes new features of SAP HANA 2.0 already been adopted by SAP S/4HANA 1610 and note <a href=\"/notes/2426339\" target=\"_blank\" title=\"2426339  - Support for SAP HANA 2 in SAP S/4HANA - Technical Information Regarding SAP HANA Requirements\">2426339 </a>which informs about technical requirements to the database system (hardware, operating system, etc.) introduced with SAP HANA 2.0 that may be relevant if you plan to operate your SAP S/4HANA 1610 system on SAP HANA 2.0.</li>\n<li>Furthermore, refer to the general restriction note <a href=\"/notes/2333141\" target=\"_blank\">2333141</a> also for restrictions relevant for SAP S/4HANA 1610 on HANA 2.0.</li>\n<li>Please refer to note<a href=\"/notes/2429281\" target=\"_blank\"> 2429281</a> (S4H:SUM:XPRAS_AIMMRG:HANA deadlock dumps) when you upgrade your system with Software Update Manager (SUM) or apply Feature Package Stack 01 and your system is on HANA 2.0.</li>\n</ul>\n<ul>\n<li>Please refer to note <a href=\"/notes/2487855\" target=\"_blank\">2487855</a> (Possible Table Inconsistencies on SAP HANA 2.0 SPS01 due to Internal Persistence Structure Migration) if you are on SAP HANA 2.0 Revision 010.00 or 011.00 (SPS01) or if you plan to upgrade to these SPS01 revisions. </li>\n<li>\n<p>SAP HANA 2.0 SPS 02 Revision 20: Please check SAP note <a href=\"/notes/2527538\" target=\"_blank\">2527538</a> (AMDP Procedure Execution Fails With Column Store Error) and SAP note <a href=\"/notes/2527648\" target=\"_blank\">2527648</a> (Column Value Equals Null Cannot be Detected by IS NULL in Debug Mode).</p>\n</li>\n</ul>\n</ul>\n<li>For the specific Front-end/UI for SAP S/4HANA 1610, please refer to SAP note <a href=\"/notes/2356208\" target=\"_blank\">2356208</a> which is the release information note for product version SAP FIORI FOR SAP S/4HANA 1610.<br/>Support Package Stack 01 of SAP Fiori 2.0 for SAP S/4HANA on the frontend requires Feature Package Stack 01 of SAP S/4HANA 1610 in the backend (and vice versa).</li>\n<li>Please refer to note <a href=\"/notes/2400710\" target=\"_blank\">2400710</a> (SAP S/4HANA, on-premise edition 1610 - SAP S/4HANA ON-PREMISE 1610 FP stack 01 (02/2017) content activation note) when you are using the SAP Best Practices for your SAP S/4HANA on premise implementation and you need current information for the tool and the content.</li>\n<li>Please ensure to apply note <a href=\"/notes/2344014\" target=\"_blank\">2344014</a> on your S/4HANA system before starting further lifecycle management processes such as a Support Package update or an Add-on installation via SPAM or SAINT.</li>\n<li>For systems with an active BW client, the implementation of Feature Package Stack 01 could abort due to issues with activation of queries REP_LUECSL and REP_BE_STRPWHLDGTAXITEM. In this case, please proceed as described in note <a href=\"/notes/2429774\" target=\"_blank\">2429774</a> and note <a href=\"/notes/2436577\" target=\"_blank\">2436577</a>.</li>\n</ul>\n<p><strong>Installation Requirements</strong></p>\n<ul>\n<li>For the system installation, please refer to the <a href=\"https://uacp.hana.ondemand.com/http.svc/rc/PRODUCTION/pdf3be8f85500f17b43e10000000a4450e5/1610%20001/en-US/INST_OP1610_FPS01.pdf\" target=\"_blank\">installation guide</a>.</li>\n<li>You need at least Software Provisioning Manager (SWPM) Support Package 20 for the installation. Please make sure that you always use the latest patch level of SWPM available at <a href=\"https://support.sap.com/software/sltoolset.html\" target=\"_blank\">support.sap.com/software/sltoolset.html</a>.</li>\n<li>Multi-Node (Scale-Out) Support possibilities are described in note <a href=\"/notes/2408419\" target=\"_blank\">2408419</a>.</li>\n<li>\n<p><em>Please refer to SAP note <a href=\"/notes/306695\" target=\"_blank\">306695</a> ('Field EMDST13 is unknown' after installing IS-U/CCS) after installation.</em></p>\n</li>\n</ul>\n<p><strong>Upgrade Requirements</strong></p>\n<ul>\n<li>You can upgrade from SAP S/4HANA, on-premise edition 1511 to SAP S/4HANA 1610. <br/>Please be aware that your software level is not higher than Support Package Stack 03.</li>\n<li>For the system upgrade, please refer to the <a href=\"https://uacp.hana.ondemand.com/http.svc/rc/PRODUCTION/pdfd61b9f57e5146b10e10000000a441470/1610%20001/en-US/UPGR_OP1610_FPS01.pdf\" target=\"_blank\">upgrade guide</a>.</li>\n<li>You need at least Software Update Manager (SUM) Support Package 20 for the upgrade. Please make sure that you always use the latest patch level of SUM available at <a href=\"https://support.sap.com/software/sltoolset.html\" target=\"_blank\">support.sap.com/software/sltoolset.html</a>.</li>\n</ul>\n<p><strong>Conversion Requirements</strong></p>\n<ul>\n<li>For the system conversion, please refer to the <a href=\"https://uacp.hana.ondemand.com/http.svc/rc/PRODUCTION/pdfe68bfa55e988410ee10000000a441470/1610%20001/en-US/CONV_OP1610_FPS01.pdf\" target=\"_blank\">conversion guide </a>and SAP notes <a href=\"/notes/2389794\" target=\"_blank\">2389794</a>, <a href=\"/notes/2389807\" target=\"_blank\">2389807</a> and <a href=\"/notes/2377310\" target=\"_blank\"><span class=\"sapUxAPObjectPageHeaderIdentifierContainer\" id=\"__xmlview2--idObjectPageHeader-identifierLineContainer\">2377310</span> </a>(conversion-related information).</li>\n<li>You need at least Software Update Manager (SUM) Support Package 20 for the conversion. Please make sure that you always use the latest patch level of SUM available at <a href=\"https://support.sap.com/software/sltoolset.html\" target=\"_blank\">support.sap.com/software/sltoolset.html</a>.</li>\n<li>For system conversions from an existing source release, please be aware that your software level is not higher than</li>\n<ul>\n<li>SAP ERP 6.0 SP Stack 28</li>\n<li>SAP enhancement package 2 for SAP ERP 6.0 SP Stack 18</li>\n<li>SAP enhancement package 3 for SAP ERP 6.0 SP Stack 17</li>\n<li>SAP enhancement package 4 for SAP ERP 6.0 SP Stack 18</li>\n<li>SAP enhancement package 5 for SAP ERP 6.0 SP Stack 15</li>\n<li>SAP enhancement package 6 for SAP ERP 6.0 SP Stack 18</li>\n<li>SAP enhancement package 6 for SAP ERP 6.0, version for SAP HANA SP Stack 10</li>\n<li>SAP enhancement package 7 for SAP ERP 6.0 SP Stack 13</li>\n<li>SAP enhancement package 8 for SAP ERP 6.0 SP Stack 05</li>\n<li>SAP Simple Finance add-on 1.0 for SAP Business Suite powered by SAP HANA SP Stack 10</li>\n<li>SAP Simple Finance, on-premise edition 1503 SP Stack 06</li>\n<li>SAP S/4HANA Finance 1605 SP Stack 05</li>\n</ul>\n</ul>\n<p><strong>Feature Package Update (via Software Update Manager (SUM)):</strong></p>\n<ul>\n<li>It is recommended to use SUM to apply Feature Package Stacks.</li>\n<li>Please apply the correction in note <a href=\"/notes/2423846\" target=\"_blank\">2423846</a> on your SAP S/4HANA 1610 Feature Package Stack 00 system before starting a Support Package update to Feature Package Stack 01.</li>\n<li>\n<p>In case you import SAP NetWeaver 7.51 Support Package Stack 01 and SAP S/4HANA 1610 Support Package Stack 01 including generation in SPAM, the Support Package Stack import stops with an RC 8 in the ABAP generation phase of the SAP_BASIS 751 Support Package 01, showing generation issues due to syntax errors for classes CL_IVE_INVOICEERPCRTRQ1_MAP and CL_IVE_INVOICEERPCRTRQ1_VAL (Type ‘CL_SAPPLCO_ACTION_CODE1’ is unknown). This import issue can be overcome by restarting the import in SPAM.</p>\n</li>\n</ul>\n<p><strong>Notes to be applied on top of Feature Package Stack 01</strong></p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"0\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td width=\"102\">\n<p><strong>SAP Note</strong></p>\n</td>\n<td valign=\"top\" width=\"436\">\n<p><strong>Description</strong></p>\n</td>\n<td valign=\"top\" width=\"192\">\n<p><strong>Software Component</strong></p>\n</td>\n<td valign=\"top\" width=\"184\">\n<p><strong>Manual Activities required</strong></p>\n</td>\n<td valign=\"top\" width=\"178\">\n<p><strong> Added on</strong></p>\n</td>\n</tr>\n<tr>\n<td width=\"102\">\n<p><a href=\"/notes/2399423\" target=\"_blank\">2399423</a></p>\n</td>\n<td valign=\"top\" width=\"436\">\n<p>Dump in characteristic value assignment</p>\n</td>\n<td valign=\"top\" width=\"192\">\n<p>SAP_ABA</p>\n</td>\n<td valign=\"top\" width=\"184\">\n<p>Yes</p>\n</td>\n<td valign=\"top\" width=\"178\">\n<p> 2017-02-21</p>\n</td>\n</tr>\n<tr>\n<td width=\"102\">\n<p><a href=\"/notes/2405390\" target=\"_blank\">2405390</a></p>\n</td>\n<td valign=\"top\" width=\"436\">\n<p>MFLE: Short version in output conversion calculated incorrectly</p>\n</td>\n<td valign=\"top\" width=\"192\">\n<p>SAP_ABA</p>\n</td>\n<td valign=\"top\" width=\"184\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"178\">\n<p><strong> </strong>2017-02-21</p>\n</td>\n</tr>\n<tr>\n<td width=\"102\">\n<p><a href=\"/notes/2396398\" target=\"_blank\">2396398</a></p>\n</td>\n<td valign=\"top\" width=\"436\">\n<p>After activating the draft, the draft UUID field of the returned active instance data is not initial</p>\n</td>\n<td valign=\"top\" width=\"192\">\n<p>SAP_BASIS</p>\n</td>\n<td valign=\"top\" width=\"184\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"178\">\n<p><strong> </strong>2017-02-21</p>\n</td>\n</tr>\n<tr>\n<td width=\"102\">\n<p><a href=\"/notes/2399372\" target=\"_blank\">2399372</a></p>\n</td>\n<td valign=\"top\" width=\"436\">\n<p>Authorization check failed for SRT_SR_P</p>\n</td>\n<td valign=\"top\" width=\"192\">\n<p>SAP_BASIS</p>\n</td>\n<td valign=\"top\" width=\"184\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"178\">\n<p><strong> </strong>2017-02-21</p>\n</td>\n</tr>\n<tr>\n<td width=\"102\">\n<p><a href=\"/notes/2399477\" target=\"_blank\">2399477</a></p>\n</td>\n<td valign=\"top\" width=\"436\">\n<p>SAP GUI for HTML: ~singletransaction=3 allows /nTX</p>\n</td>\n<td valign=\"top\" width=\"192\">\n<p>SAP_BASIS</p>\n</td>\n<td valign=\"top\" width=\"184\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"178\">\n<p><strong> </strong>2017-02-21</p>\n</td>\n</tr>\n<tr>\n<td width=\"102\">\n<p><a href=\"/notes/2400809\" target=\"_blank\">2400809</a></p>\n</td>\n<td valign=\"top\" width=\"436\">\n<p>Dump POSTING_ILLEGAL_STATEMENT (TSTR_GENERATE, DB_COMMIT)</p>\n</td>\n<td valign=\"top\" width=\"192\">\n<p>SAP_BASIS</p>\n</td>\n<td valign=\"top\" width=\"184\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"178\">\n<p><strong> </strong>2017-02-21</p>\n</td>\n</tr>\n<tr>\n<td width=\"102\">\n<p><a href=\"/notes/2402373\" target=\"_blank\">2402373</a></p>\n</td>\n<td valign=\"top\" width=\"436\">\n<p>OBJECTS_OBJREF_NOT_ASSIGNED in class CL_SWF_UTL_WAPI_SERVICES</p>\n</td>\n<td valign=\"top\" width=\"192\">\n<p>SAP_BASIS</p>\n</td>\n<td valign=\"top\" width=\"184\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"178\">\n<p><strong> </strong>2017-02-21</p>\n</td>\n</tr>\n<tr>\n<td width=\"102\">\n<p><a href=\"/notes/2404762\" target=\"_blank\">2404762</a></p>\n</td>\n<td valign=\"top\" width=\"436\">\n<p>Performance: SADL Metadata Load Is Calculated in Each Request</p>\n</td>\n<td valign=\"top\" width=\"192\">\n<p>SAP_BASIS</p>\n</td>\n<td valign=\"top\" width=\"184\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"178\">\n<p><strong> </strong>2017-02-21</p>\n</td>\n</tr>\n<tr>\n<td width=\"102\">\n<p><a href=\"/notes/2424813\" target=\"_blank\">2424813</a></p>\n</td>\n<td valign=\"top\" width=\"436\">\n<p>Runtime error COMMIT_IN_POSTING in class CL_FDT_BACKGROUND</p>\n</td>\n<td valign=\"top\" width=\"192\">\n<p>SAP_BASIS</p>\n</td>\n<td valign=\"top\" width=\"184\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"178\">\n<p><strong> </strong>2017-02-21</p>\n</td>\n</tr>\n<tr>\n<td width=\"102\">\n<p><a href=\"/notes/2428086\" target=\"_blank\">2428086</a></p>\n</td>\n<td valign=\"top\" width=\"436\">\n<p>Unable to Create New Line in Empty DataGrid</p>\n</td>\n<td valign=\"top\" width=\"192\">\n<p>SAP_BW</p>\n</td>\n<td valign=\"top\" width=\"184\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"178\">\n<p><strong> </strong>2017-02-21</p>\n</td>\n</tr>\n<tr>\n<td width=\"102\">\n<p><a href=\"/notes/2280748\" target=\"_blank\">2280748</a></p>\n</td>\n<td valign=\"top\" width=\"436\">\n<p>ED: MIGO, dump due to incompletely removed MM-IM-ED LIS S465 &amp; S466</p>\n</td>\n<td valign=\"top\" width=\"192\">\n<p>S4CORE</p>\n</td>\n<td valign=\"top\" width=\"184\">\n<p>Yes</p>\n</td>\n<td valign=\"top\" width=\"178\">\n<p><strong> </strong>2017-02-21</p>\n</td>\n</tr>\n<tr>\n<td width=\"102\">\n<p><a href=\"/notes/2415736\" target=\"_blank\">2415736</a></p>\n</td>\n<td valign=\"top\" width=\"436\">\n<p>Warranty : Post Versions to Reimburser fails via action A042 in transaction WTY</p>\n</td>\n<td valign=\"top\" width=\"192\">\n<p>S4CORE</p>\n</td>\n<td valign=\"top\" width=\"184\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"178\">\n<p><strong> </strong>2017-02-21</p>\n</td>\n</tr>\n<tr>\n<td width=\"102\">\n<p><a href=\"/notes/2427850\" target=\"_blank\">2427850</a></p>\n</td>\n<td valign=\"top\" width=\"436\">\n<p>WTY : Character field entry for RECNT dumps during method call VALUE_CHANGE</p>\n</td>\n<td valign=\"top\" width=\"192\">\n<p>S4CORE</p>\n</td>\n<td valign=\"top\" width=\"184\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"178\">\n<p><strong> </strong>2017-02-21</p>\n</td>\n</tr>\n<tr>\n<td width=\"102\">\n<p><a href=\"/notes/2429609\" target=\"_blank\">2429609</a></p>\n</td>\n<td valign=\"top\" width=\"436\">\n<p>S/4 Hana: Technical update of view V_PEG_MSEG2</p>\n</td>\n<td valign=\"top\" width=\"192\">\n<p>S4CORE</p>\n</td>\n<td valign=\"top\" width=\"184\">\n<p>Yes</p>\n</td>\n<td valign=\"top\" width=\"178\">\n<p> 2017-02-21</p>\n</td>\n</tr>\n<tr>\n<td width=\"102\">\n<p><a href=\"/notes/2372221\" target=\"_blank\">2372221</a></p>\n</td>\n<td valign=\"top\" width=\"436\">\n<p>Performance improvement on GT_ADDR_XPCPT</p>\n</td>\n<td valign=\"top\" width=\"192\">\n<p>SAP_BASIS</p>\n</td>\n<td valign=\"top\" width=\"184\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"178\">\n<p> 2017-04-12</p>\n</td>\n</tr>\n<tr>\n<td width=\"102\">\n<p><a href=\"/notes/2461676\" target=\"_blank\">2461676</a></p>\n</td>\n<td valign=\"top\" width=\"436\">\n<p>New MM-GPD stock sync validation to consider legacy MM</p>\n</td>\n<td valign=\"top\" width=\"192\">\n<p>S4CORE</p>\n</td>\n<td valign=\"top\" width=\"184\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"178\">\n<p> 2017-04-24</p>\n</td>\n</tr>\n<tr>\n<td width=\"102\">\n<p><a href=\"/notes/2445210\" target=\"_blank\">2445210</a></p>\n</td>\n<td valign=\"top\" width=\"436\">\n<p>SAP GUI for HTML: Incorrect handling of transaction codes with a dash '-'</p>\n</td>\n<td valign=\"top\" width=\"192\">\n<p>SAP_BASIS</p>\n</td>\n<td valign=\"top\" width=\"184\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"178\">\n<p> 2017-05-08</p>\n</td>\n</tr>\n<tr>\n<td width=\"102\">\n<p><a href=\"/notes/2467650\" target=\"_blank\">2467650</a></p>\n</td>\n<td valign=\"top\" width=\"436\">\n<p>Authorization issues in viewing Document Info Record</p>\n</td>\n<td valign=\"top\" width=\"192\">\n<p>S4CORE</p>\n</td>\n<td valign=\"top\" width=\"184\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"178\">\n<p> 2017-05-08</p>\n</td>\n</tr>\n<tr>\n<td width=\"102\">\n<p><a href=\"/notes/2463740\" target=\"_blank\">2463740</a></p>\n</td>\n<td valign=\"top\" width=\"436\">\n<p>Error message FINS_ACDOC_CUST 215 during postings</p>\n</td>\n<td valign=\"top\" width=\"192\">\n<p>S4CORE</p>\n</td>\n<td valign=\"top\" width=\"184\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"178\">\n<p> 2017-05-19</p>\n</td>\n</tr>\n<tr>\n<td width=\"102\">\n<p><span class=\"sapUxAPObjectPageHeaderIdentifierContainer\" id=\"__xmlview2--idObjectPageHeader-identifierLineContainer\"><a href=\"/notes/2477735\" target=\"_blank\">2477735</a></span></p>\n</td>\n<td valign=\"top\" width=\"436\">\n<p>Annotation API: enable cache usage for get_annos_mass</p>\n</td>\n<td valign=\"top\" width=\"192\">\n<p>SAP_BASIS</p>\n</td>\n<td valign=\"top\" width=\"184\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"178\">\n<p> 2017-07-10</p>\n</td>\n</tr>\n<tr>\n<td width=\"102\">\n<p><span class=\"sapUxAPObjectPageHeaderIdentifierContainer\"><a href=\"/notes/2450514\" target=\"_blank\">2450514</a></span></p>\n</td>\n<td valign=\"top\" width=\"436\">\n<p>BRFplus: Numeric Comparison doesn't work within IF-FORMULA</p>\n</td>\n<td valign=\"top\" width=\"192\">\n<p>SAP_BASIS</p>\n</td>\n<td valign=\"top\" width=\"184\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"178\">\n<p> 2017-07-12</p>\n</td>\n</tr>\n<tr>\n<td width=\"102\">\n<p><span class=\"sapUxAPObjectPageHeaderIdentifierContainer\"><a href=\"/notes/2489305\" target=\"_blank\">2489305</a></span></p>\n</td>\n<td valign=\"top\" width=\"436\">\n<p>Dump while inserting international address version for a person</p>\n</td>\n<td valign=\"top\" width=\"192\">\n<p>SAP_BASIS</p>\n</td>\n<td valign=\"top\" width=\"184\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"178\">\n<p> 2017-07-12</p>\n</td>\n</tr>\n<tr>\n<td width=\"102\">\n<p><span class=\"sapUxAPObjectPageHeaderIdentifierContainer\"><a href=\"/notes/2485784\" target=\"_blank\">2485784</a></span></p>\n</td>\n<td valign=\"top\" width=\"436\">\n<p>ALV export: Cannot save to clipboard in browser</p>\n</td>\n<td valign=\"top\" width=\"192\">\n<p>SAP_BASIS</p>\n</td>\n<td valign=\"top\" width=\"184\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"178\">\n<p> 2017-07-12</p>\n</td>\n</tr>\n<tr>\n<td width=\"102\">\n<p><span class=\"sapUxAPObjectPageHeaderIdentifierContainer\"><a href=\"/notes/2490652\" target=\"_blank\">2490652</a></span></p>\n</td>\n<td valign=\"top\" width=\"436\">\n<p>Legacy DAC maps business key to initial BOPF key</p>\n</td>\n<td valign=\"top\" width=\"192\">\n<p>SAP_BASIS</p>\n</td>\n<td valign=\"top\" width=\"184\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"178\">\n<p> 2017-07-12</p>\n</td>\n</tr>\n<tr>\n<td width=\"102\">\n<p><span class=\"sapUxAPObjectPageHeaderIdentifierContainer\"><a href=\"/notes/2491892\" target=\"_blank\">2491892</a></span></p>\n</td>\n<td valign=\"top\" width=\"436\">\n<p>DD: keep switched off objects in gentab for DD_WORKLIST_ACT</p>\n</td>\n<td valign=\"top\" width=\"192\">\n<p>SAP_BASIS</p>\n</td>\n<td valign=\"top\" width=\"184\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"178\">\n<p> 2017-07-27</p>\n</td>\n</tr>\n<tr>\n<td width=\"102\">\n<p><a href=\"/notes/2500159\" target=\"_blank\">2500159</a></p>\n</td>\n<td valign=\"top\" width=\"436\">\n<p>MESSAGE_TYPE_X issued during delivery creation</p>\n</td>\n<td valign=\"top\" width=\"192\">\n<p>S4CORE</p>\n</td>\n<td valign=\"top\" width=\"184\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"178\">\n<p> 2017-08-28</p>\n</td>\n</tr>\n<tr>\n<td width=\"102\">\n<p><a href=\"/notes/2520306\" target=\"_blank\">2520306</a></p>\n</td>\n<td valign=\"top\" width=\"436\">\n<p>Condition table index not activated after upgrade</p>\n</td>\n<td valign=\"top\" width=\"192\">\n<p>S4CORE</p>\n</td>\n<td valign=\"top\" width=\"184\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"178\">\n<p> 2017-09-06</p>\n</td>\n</tr>\n<tr>\n<td width=\"102\">\n<p><a href=\"/notes/2527709\" target=\"_blank\">2527709</a></p>\n</td>\n<td valign=\"top\" width=\"436\">\n<p>Program Dump after activating Business Function LOG_PP_EWM_MAN_2</p>\n</td>\n<td valign=\"top\" width=\"192\">\n<p>S4CORE</p>\n</td>\n<td valign=\"top\" width=\"184\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"178\">\n<p> 2017-09-06</p>\n</td>\n</tr>\n<tr>\n<td width=\"102\">\n<p><a href=\"/notes/2485570\" target=\"_blank\">2485570</a></p>\n</td>\n<td valign=\"top\" width=\"436\">\n<p>Downport of bugfix for CDS Metadata Extensions Runtime Data Provider</p>\n</td>\n<td valign=\"top\" width=\"192\">\n<p>SAP_BASIS</p>\n</td>\n<td valign=\"top\" width=\"184\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"178\">\n<p> 2017-09-07</p>\n</td>\n</tr>\n</tbody>\n</table></div>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"0\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td width=\"82\">\n<p><a href=\"/notes/2652897\" target=\"_blank\">2652897</a></p>\n</td>\n<td valign=\"top\" width=\"349\">\n<p> List ATS UIBB and Tree UIBB: API for choosing selection eventing not working correctly</p>\n</td>\n<td valign=\"top\" width=\"126\">\n<p> SAP_UI</p>\n</td>\n<td valign=\"top\" width=\"175\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"142\">\n<p>  2018-07-23</p>\n</td>\n</tr>\n<tr>\n<td width=\"82\">\n<p> <a href=\"/notes/2655756\" target=\"_blank\">2655756</a></p>\n</td>\n<td valign=\"top\" width=\"349\">\n<p> Tree UIBB: Conditional Formatting when master column has display type image</p>\n</td>\n<td valign=\"top\" width=\"126\">\n<p> SAP_UI</p>\n</td>\n<td valign=\"top\" width=\"175\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"142\">\n<p> 2018-07-23</p>\n</td>\n</tr>\n</tbody>\n</table></div>\n<p><strong>Important Changes made after Release of Feature Package Stack 01</strong></p>\n<p><strong><strong><strong><strong><strong><strong><strong><strong><strong><strong>2018-07-03:</strong> </strong></strong></strong></strong></strong></strong></strong></strong></strong>SAP note <a href=\"/notes/2655761\" target=\"_blank\">2655761</a> (SAP S/4HANA - unrecommended revisions of SAP HANA database for use in SAP S/4HANA ) when you plan to upgrade to SAP HANA 2.0 SPS03 Revisions 3x.</p>\n<p><strong><strong><strong><strong><strong><strong><strong><strong><strong>2017-09-04: </strong></strong></strong></strong></strong></strong></strong></strong></strong>SAP HANA 2.0 SPS 02 Revision 20: SAP note <a href=\"/notes/2527538\" target=\"_blank\">2527538</a> (AMDP Procedure Execution Fails With Column Store Error) and SAP note <a href=\"/notes/2527648\" target=\"_blank\">2527648</a> (Column Value Equals Null Cannot be Detected by IS NULL in Debug Mode) inserted.<br/><strong><strong><strong><strong><strong>2017-08-30</strong>: </strong></strong></strong></strong>SAP HANA 1.0 minimum required revision changed from Revision 122.03 to 122.05 to synchronize with requirements in SUM tool.<br/><strong>2017-03-13: </strong>Inserted under General Considerations: Refer to the general restriction note <a href=\"/notes/2333141\" target=\"_blank\">2333141</a> also for restrictions relevant for SAP S/4HANA 1610 on HANA 2.0.<br/><strong>2017-03-16: </strong>Note <a href=\"/notes/2436577\" target=\"_blank\">2436577</a> inserted under General Conditions in section issues with active BW client.<br/><strong>2017-03-24: </strong>Deleted in General Considerations: According to the Product Availability Matrix for supported Database versions for the products SAP ERP 6.0 EHP 6 (or higher) and SAP S/4HANA 1511 the upgrade of an underlying SAP HANA Database from Release 1.0 to 2.0 can only be executed after the successfully completed technical conversion or upgrade process to S/HANA 1610 Feature Package Stack 01.<br/><strong>2017-03-29: </strong>SAP HANA 2.0 Minimum version changed to SP0 Revision 002.<br/><strong>2017-05-08: </strong>Inserted under Feature Package Update: 'It is recommed to use SUM to apply Feature Package Stacks.'<br/><strong>2017-06-12</strong>: SWPM and SUM Support Package Level increased to 20.<br/><strong><strong>2017-06-19</strong>: </strong>SAP HANA 2.0 related note <a href=\"/notes/2487855\" target=\"_blank\">2487855</a> (Possible Table Inconsistencies on SAP HANA 2.0 SPS01 due to Internal Persistence Structure Migration) inserted.</p>\n<p><strong><span>FEATURE PACKAGE STACK 00 (10/2016)</span></strong></p>\n<p><strong>General / Important Considerations</strong></p>\n<ul>\n<li>Please refer to the specific documentation under section <a href=\"http://help.sap.com/s4hana_op_1610?current=s4hana_op_1610\" target=\"_blank\">product documentation</a>.</li>\n<li>Feature Package Stack 00 is released on SAP HANA 1.0 and SAP HANA 2.0.</li>\n<ul>\n<li>SAP HANA 1.0</li>\n<ul>\n<li>The minimum required revision of SAP HANA 1.0 is Revision 122.05. SAP in general recommends customers to implement the highest SAP HANA 1.0 revision available. For more details, please refer to note <a href=\"/notes/2021789\" target=\"_blank\">2021789</a> (SAP HANA Revision and Maintenance Strategy).</li>\n</ul>\n<li>SAP HANA 2.0</li>\n<ul>\n<li>The minimum required revision of SAP HANA 2.0 is SP0 Revision 002. SAP in general recommends customers to implement the highest SAP HANA 2.0 revision available. For more details, please refer to note <a href=\"/notes/2378962\" target=\"_blank\">2378962</a> (SAP HANA 2.0 Revision and Maintenance Strategy).</li>\n<li>Please refer to note <a href=\"/notes/2426477\" target=\"_blank\" title=\"2426477  - Support for SAP HANA 2 in SAP S/4HANA 1610 - Status of application support for specific functionalities of SAP HANA 2\">2426477</a> which describes new features of SAP HANA 2.0 already been adopted by SAP S/4HANA 1610 and note <a href=\"/notes/2426339\" target=\"_blank\" title=\"2426339  - Support for SAP HANA 2 in SAP S/4HANA - Technical Information Regarding SAP HANA Requirements\">2426339 </a>which informs about technical requirements to the database system (hardware, operating system, etc.) introduced with SAP HANA 2.0 that may be relevant if you plan to operate your SAP S/4HANA 1610 system on SAP HANA 2.0.</li>\n<li>Furthermore, refer to the general restriction note <a href=\"/notes/2333141\" target=\"_blank\">2333141</a> also for restrictions relevant for SAP S/4HANA 1610 on HANA 2.0.</li>\n<li>Please refer to note<a href=\"/notes/2429281\" target=\"_blank\"> 2429281</a> (S4H:SUM:XPRAS_AIMMRG:HANA deadlock dumps) when you upgrade your system with Software Update Manager (SUM) and your system is on HANA 2.0.</li>\n<li>Please refer to note <a href=\"/notes/2487855\" target=\"_blank\">2487855</a> (Possible Table Inconsistencies on SAP HANA 2.0 SPS01 due to Internal Persistence Structure Migration) if you are on SAP HANA 2.0 Revision 010.00 or 011.00 (SPS01) or if you plan to upgrade to these SPS01 revisions. </li>\n<li>\n<p>SAP HANA 2.0 SPS 02 Revision 20: Please check SAP note <a href=\"/notes/2527538\" target=\"_blank\">2527538</a> (AMDP Procedure Execution Fails With Column Store Error) and SAP note <a href=\"/notes/2527648\" target=\"_blank\">2527648</a> (Column Value Equals Null Cannot be Detected by IS NULL in Debug Mode).</p>\n</li>\n<li>\n<p>Please refer to SAP note <a href=\"/notes/2655761\" target=\"_blank\">2655761</a> (SAP S/4HANA - unrecommended revisions of SAP HANA database for use in SAP S/4HANA ) when you plan to upgrade to SAP HANA 2.0 SPS03 Revisions 3x.</p>\n</li>\n</ul>\n<li>\n<p>Please refer to SAP note <a href=\"/notes/1906576\" target=\"_blank\">1906576</a> (HANA client and server cross-version compatibility) if you would like to upgrade your SAP HANA Database to a newer revision or a newer available SPS level.</p>\n</li>\n</ul>\n<li>For the specific Front-end/UI for SAP S/4HANA 1610, please refer to SAP note <a href=\"/notes/2356208\" target=\"_blank\">2356208</a> which is the release information note for product version \"SAP FIORI FOR SAP S/4HANA 1610\".<br/>Support Package Stack 00 of SAP Fiori 2.0 for SAP S/4HANA on the frontend requires Feature Package Stack 00 of SAP S/4HANA 1610 in the backend (and vice versa).</li>\n<li>Please refer to note <a href=\"/notes/2328546\" target=\"_blank\">2328546</a> (SAP S/4HANA, on-premise edition 1610 - SAP S/4HANA ON-PREMISE 1610 FP stack 00 (10/2016) content activation note<strong><span 'times=\"\" ar-sa;=\"\" calibri',sans-serif;=\"\" calibri;=\"\" en-us;=\"\" lang=\"EN-US\" minor-latin;=\"\" mso-ansi-language:=\"\" mso-bidi-font-family:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" mso-fareast-theme-font:=\"\" new=\"\" roman';\"=\"\">)</span></strong> for using the core configurator for your SAP S/4HANA on premise implementation and you need current installation and configuration data.</li>\n<li>Please ensure to apply note <a href=\"/notes/2344014\" target=\"_blank\">2344014</a> on your S/4HANA system before starting further lifecycle management processes such as a Support Package update or an Add-on installation via SPAM or SAINT.</li>\n<li>If you are planning to update the underlying NetWeaver Support Package level in your system independently from the application stack, you could run into an issue with duplicate field names. This error might occur if you are on SAP S/4HANA 1610 Feature Package Stack 00 and the target is SAP NetWeaver Support Package 01 or higher. In this case, please implement note <a href=\"/notes/2391758\" target=\"_blank\">2391758</a>.</li>\n</ul>\n<p><strong>Installation Requirements</strong></p>\n<ul>\n<li>For the system installation, please refer to the <a href=\"https://uacp.hana.ondemand.com/http.svc/rc/PRODUCTION/pdf3be8f85500f17b43e10000000a4450e5/1610%20000/en-US/INST_OP1610.pdf\" target=\"_blank\">installation guide</a>.</li>\n<li>You need at least Software Provisioning Manager (SWPM) Support Package 20 for the installation. Please make sure that you always use the latest patch level of SWPM available at <a href=\"https://support.sap.com/software/sltoolset.html\" target=\"_blank\">support.sap.com/software/sltoolset.html</a>.</li>\n<li>\n<p><em>Please refer to SAP note <a href=\"/notes/306695\" target=\"_blank\">306695</a> ('Field EMDST13 is unknown' after installing IS-U/CCS) after installation.</em></p>\n</li>\n</ul>\n<p><strong>Upgrade Requirements</strong></p>\n<ul>\n<li>You can upgrade from SAP S/4HANA, on-premise edition 1511 to SAP S/4HANA 1610. <br/>Please be aware that your software level is not higher than Feature Package Stack 02.</li>\n<li>For the system upgrade, please refer to the <a href=\"https://uacp.hana.ondemand.com/http.svc/rc/PRODUCTION/pdfd61b9f57e5146b10e10000000a441470/1610%20000/en-US/UPGR_OP1610.pdf\" target=\"_blank\">upgrade guide</a>.</li>\n<li>You need at least Software Update Manager (SUM) Support Package 20 for the upgrade. Please make sure that you always use the latest patch level of SUM available at <a href=\"https://support.sap.com/software/sltoolset.html\" target=\"_blank\">support.sap.com/software/sltoolset.html</a>.</li>\n</ul>\n<p><strong>Notes to be applied on top of <strong>Feature Package Stack 00</strong></strong></p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"0\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td width=\"118\">\n<p><strong>SAP Note</strong></p>\n</td>\n<td valign=\"top\" width=\"518\">\n<p><strong>Description</strong></p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p><strong>Software Component</strong></p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p><strong>Manual Activities required</strong></p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p><strong> Added on</strong></p>\n</td>\n</tr>\n<tr>\n<td width=\"118\">\n<p><a href=\"/notes/2338721\" target=\"_blank\">2338721</a></p>\n</td>\n<td valign=\"top\" width=\"518\">\n<p>Program termination \"ASSIGN to a substring is not allowed\"</p>\n</td>\n<td valign=\"top\" width=\"238\">SAP_ABA</td>\n<td valign=\"top\" width=\"238\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p> 2016-10-31</p>\n</td>\n</tr>\n<tr>\n<td width=\"118\">\n<p><a href=\"/notes/2344368\" target=\"_blank\">2344368</a></p>\n</td>\n<td valign=\"top\" width=\"518\">\n<p>ECN IDoc processing fails because of missing data parts</p>\n</td>\n<td valign=\"top\" width=\"238\">SAP_ABA</td>\n<td valign=\"top\" width=\"238\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p> 2016-10-31</p>\n</td>\n</tr>\n<tr>\n<td width=\"118\">\n<p><a href=\"/notes/2345087\" target=\"_blank\">2345087</a></p>\n</td>\n<td valign=\"top\" width=\"518\">\n<p>BP_BAP: Missing values in required entry fields cause posting termination in mass processing</p>\n</td>\n<td valign=\"top\" width=\"238\">SAP_ABA</td>\n<td valign=\"top\" width=\"238\">\n<p>Yes</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p> 2016-10-31</p>\n</td>\n</tr>\n<tr>\n<td width=\"118\">\n<p><a href=\"/notes/2345102\" target=\"_blank\">2345102</a></p>\n</td>\n<td valign=\"top\" width=\"518\">\n<p>No valuation dialog box for entering effectivity parameter values</p>\n</td>\n<td valign=\"top\" width=\"238\">SAP_ABA</td>\n<td valign=\"top\" width=\"238\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p> 2016-10-31</p>\n</td>\n</tr>\n<tr>\n<td width=\"118\">\n<p><a href=\"/notes/2322771\" target=\"_blank\">2322771</a></p>\n</td>\n<td valign=\"top\" width=\"518\">\n<p>S4HANA SuccessFactors &amp; personnel number and the user name fields behavior in S4HANA</p>\n</td>\n<td valign=\"top\" width=\"238\">SAP_ABA, SAP_BASIS</td>\n<td valign=\"top\" width=\"238\">\n<p>Yes</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p> 2016-10-31</p>\n</td>\n</tr>\n<tr>\n<td width=\"118\">\n<p><a href=\"/notes/2284857\" target=\"_blank\">2284857</a></p>\n</td>\n<td valign=\"top\" width=\"518\">\n<p>Number ranges - trace</p>\n</td>\n<td valign=\"top\" width=\"238\">SAP_BASIS</td>\n<td valign=\"top\" width=\"238\">\n<p>Yes</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p> 2016-10-31</p>\n</td>\n</tr>\n<tr>\n<td width=\"118\">\n<p><a href=\"/notes/2342658\" target=\"_blank\">2342658</a></p>\n</td>\n<td valign=\"top\" width=\"518\">\n<p>Note Implementation Failure due to Technical Languages(1Q,2Q,3Q,4Q,etc)</p>\n</td>\n<td valign=\"top\" width=\"238\">SAP_BASIS</td>\n<td valign=\"top\" width=\"238\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p> 2016-10-31</p>\n</td>\n</tr>\n<tr>\n<td width=\"118\">\n<p><a href=\"/notes/2344014\" target=\"_blank\">2344014</a></p>\n</td>\n<td valign=\"top\" width=\"518\">\n<p>SPAU Adjustment for R3TR CLAS deliveries - adjustment of obsolete SAP notes deletes classes</p>\n</td>\n<td valign=\"top\" width=\"238\">SAP_BASIS</td>\n<td valign=\"top\" width=\"238\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p> 2016-10-31</p>\n</td>\n</tr>\n<tr>\n<td width=\"118\">\n<p><a href=\"/notes/2344436\" target=\"_blank\">2344436</a></p>\n</td>\n<td valign=\"top\" width=\"518\">\n<p>ATC: Table SATC_AC_OBJ_CTXT gets extremely large</p>\n</td>\n<td valign=\"top\" width=\"238\">SAP_BASIS</td>\n<td valign=\"top\" width=\"238\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p> 2016-10-31</p>\n</td>\n</tr>\n<tr>\n<td width=\"118\">\n<p><a href=\"/notes/2345697\" target=\"_blank\">2345697</a></p>\n</td>\n<td valign=\"top\" width=\"518\">\n<p>BRF+ analytical decision table check Call CL_FDT_XS=&gt;GET_INSTANCE with RFC destination</p>\n</td>\n<td valign=\"top\" width=\"238\">SAP_BASIS</td>\n<td valign=\"top\" width=\"238\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p> 2016-10-31</p>\n</td>\n</tr>\n<tr>\n<td width=\"118\">\n<p><a href=\"/notes/2345795\" target=\"_blank\">2345795</a></p>\n</td>\n<td valign=\"top\" width=\"518\">\n<p>Workflow runtime ends with error WL821</p>\n</td>\n<td valign=\"top\" width=\"238\">SAP_BASIS</td>\n<td valign=\"top\" width=\"238\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p> 2016-10-31</p>\n</td>\n</tr>\n<tr>\n<td width=\"118\">\n<p><a href=\"/notes/2346044\" target=\"_blank\">2346044</a></p>\n</td>\n<td valign=\"top\" width=\"518\">\n<p>Exceptions were raised for unblocked addresses</p>\n</td>\n<td valign=\"top\" width=\"238\">SAP_BASIS</td>\n<td valign=\"top\" width=\"238\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p> 2016-10-31</p>\n</td>\n</tr>\n<tr>\n<td width=\"118\">\n<p><a href=\"/notes/2346821\" target=\"_blank\">2346821</a></p>\n</td>\n<td valign=\"top\" width=\"518\">\n<p>SCWB - TLOGO Language(1Q,2Q,3Q,4Q etc) Filtering Fix</p>\n</td>\n<td valign=\"top\" width=\"238\">SAP_BASIS</td>\n<td valign=\"top\" width=\"238\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p> 2016-10-31</p>\n</td>\n</tr>\n<tr>\n<td width=\"118\">\n<p><a href=\"/notes/2350429\" target=\"_blank\">2350429</a></p>\n</td>\n<td valign=\"top\" width=\"518\">\n<p>External view with more than 255 fields</p>\n</td>\n<td valign=\"top\" width=\"238\">SAP_BASIS</td>\n<td valign=\"top\" width=\"238\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p> 2016-10-31</p>\n</td>\n</tr>\n<tr>\n<td width=\"118\">\n<p><a href=\"/notes/2351188\" target=\"_blank\">2351188</a></p>\n</td>\n<td valign=\"top\" width=\"518\">\n<p>BRF+ Anlytical function generation - Derive default schema dynamically</p>\n</td>\n<td valign=\"top\" width=\"238\">SAP_BASIS</td>\n<td valign=\"top\" width=\"238\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p> 2016-10-31</p>\n</td>\n</tr>\n<tr>\n<td width=\"118\">\n<p><a href=\"/notes/2285661\" target=\"_blank\">2285661</a></p>\n</td>\n<td valign=\"top\" width=\"518\">\n<p>Record of a time management infotype is exited even though system issues an error message</p>\n</td>\n<td valign=\"top\" width=\"238\">EA-HRRXX</td>\n<td valign=\"top\" width=\"238\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p> 2016-10-31</p>\n</td>\n</tr>\n<tr>\n<td width=\"118\">\n<p><a href=\"/notes/2311339\" target=\"_blank\">2311339</a></p>\n</td>\n<td valign=\"top\" width=\"518\">\n<p>HR PAO: IT0019</p>\n</td>\n<td valign=\"top\" width=\"238\">EA-HRRXX</td>\n<td valign=\"top\" width=\"238\">\n<p>Yes</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p> 2016-10-31</p>\n</td>\n</tr>\n<tr>\n<td width=\"118\">\n<p><a href=\"/notes/2313878\" target=\"_blank\">2313878</a></p>\n</td>\n<td valign=\"top\" width=\"518\">\n<p>HR PAO - validity period &amp;lt ;&gt; \"for all data\": Creation of new data record does not function correctly</p>\n</td>\n<td valign=\"top\" width=\"238\">EA-HRRXX</td>\n<td valign=\"top\" width=\"238\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p> 2016-10-31</p>\n</td>\n</tr>\n<tr>\n<td width=\"118\">\n<p><a href=\"/notes/2335641\" target=\"_blank\">2335641</a></p>\n</td>\n<td valign=\"top\" width=\"518\">\n<p>HR PAO: Field PERID (IT 002) is not selected in the event of an input error</p>\n</td>\n<td valign=\"top\" width=\"238\">EA-HRRXX</td>\n<td valign=\"top\" width=\"238\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p> 2016-10-31</p>\n</td>\n</tr>\n<tr>\n<td width=\"118\">\n<p><a href=\"/notes/2336154\" target=\"_blank\">2336154</a></p>\n</td>\n<td valign=\"top\" width=\"518\">\n<p>HR PAO: ‘Back’ button (navigation to the overview page) is inactive</p>\n</td>\n<td valign=\"top\" width=\"238\">EA-HRRXX</td>\n<td valign=\"top\" width=\"238\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p> 2016-10-31</p>\n</td>\n</tr>\n<tr>\n<td width=\"118\">\n<p><a href=\"/notes/2333704\" target=\"_blank\">2333704</a></p>\n</td>\n<td valign=\"top\" width=\"518\">\n<p>HR PAO: Program termination when you save infotype</p>\n</td>\n<td valign=\"top\" width=\"238\">EA-HRRXX</td>\n<td valign=\"top\" width=\"238\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p> 2016-10-31</p>\n</td>\n</tr>\n<tr>\n<td width=\"118\">\n<p><a href=\"/notes/2340847\" target=\"_blank\">2340847</a></p>\n</td>\n<td valign=\"top\" width=\"518\">\n<p>HR PAO: Program termination when you save infotype</p>\n</td>\n<td valign=\"top\" width=\"238\">EA-HRRXX</td>\n<td valign=\"top\" width=\"238\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p> 2016-10-31</p>\n</td>\n</tr>\n<tr>\n<td width=\"118\">\n<p><a href=\"/notes/2343342\" target=\"_blank\">2343342</a></p>\n</td>\n<td valign=\"top\" width=\"518\">\n<p>Orders in cost distribution (IT0027/IT1018)</p>\n</td>\n<td valign=\"top\" width=\"238\">SAP_HRRXX</td>\n<td valign=\"top\" width=\"238\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p> 2016-10-31</p>\n</td>\n</tr>\n<tr>\n<td width=\"118\">\n<p><a href=\"/notes/2071826\" target=\"_blank\">2071826</a></p>\n</td>\n<td valign=\"top\" width=\"518\">\n<p>Client copy for integrated SAP HANA liveCache</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p>S4CORE</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p> 2016-10-31</p>\n</td>\n</tr>\n<tr>\n<td width=\"118\">\n<p><a href=\"/notes/2353319\" target=\"_blank\">2353319</a></p>\n</td>\n<td valign=\"top\" width=\"518\">\n<p>Customizing Consistency Check for TAK01 in OKKP</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p>S4CORE</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p> 2016-10-31</p>\n</td>\n</tr>\n<tr>\n<td width=\"118\">\n<p><a href=\"/notes/2354979\" target=\"_blank\">2354979</a></p>\n</td>\n<td valign=\"top\" width=\"518\">\n<p>SRFV_RPG_CAT2: Delete last record in Assign Report Categories to a Reporting Entity</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p>S4CORE</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p> 2016-10-31</p>\n</td>\n</tr>\n<tr>\n<td width=\"118\">\n<p><a href=\"/notes/2355000\" target=\"_blank\">2355000</a></p>\n</td>\n<td valign=\"top\" width=\"518\">\n<p>Parameter for ledger groups of underlying ledgers</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p>S4CORE</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p> 2016-10-31</p>\n</td>\n</tr>\n<tr>\n<td width=\"118\">\n<p><a href=\"/notes/2359435\" target=\"_blank\">2359435</a></p>\n</td>\n<td valign=\"top\" width=\"518\">\n<p>Client copy: Syntax error in program FINS_UPD_FINSC_001A_REP</p>\n</td>\n<td valign=\"top\" width=\"238\">S4CORE</td>\n<td valign=\"top\" width=\"238\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p> 2016-10-31</p>\n</td>\n</tr>\n<tr>\n<td width=\"118\">\n<p><a href=\"/notes/2362815\" target=\"_blank\">2362815</a></p>\n</td>\n<td valign=\"top\" width=\"518\">\n<p>Material Ledger (ML) and Retail: tied empties: error CKMLMV 009 in LCKMLMVQUANTF06</p>\n</td>\n<td valign=\"top\" width=\"238\">S4CORE</td>\n<td valign=\"top\" width=\"238\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p> 2016-10-31</p>\n</td>\n</tr>\n<tr>\n<td width=\"118\">\n<p><a href=\"/notes/2364253\" target=\"_blank\">2364253</a></p>\n</td>\n<td valign=\"top\" width=\"518\">\n<p>Syntax errors due to missing development package assignment</p>\n</td>\n<td valign=\"top\" width=\"238\">S4CORE</td>\n<td valign=\"top\" width=\"238\">\n<p>Yes</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p> 2016-10-31</p>\n</td>\n</tr>\n<tr>\n<td width=\"118\">\n<p><a href=\"/notes/2366738\" target=\"_blank\">2366738</a></p>\n</td>\n<td valign=\"top\" width=\"518\">\n<p> S/4HANA dump in CL_FAA_CFG_SERVICE-&gt;GET_LOCAL_CURR_TYPE_FROM_LDGRP if a ledger is not maintained correctly</p>\n</td>\n<td valign=\"top\" width=\"238\">S4CORE</td>\n<td valign=\"top\" width=\"238\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p> 2016-10-31</p>\n</td>\n</tr>\n<tr>\n<td width=\"118\">\n<p><a href=\"/notes/2367508\" target=\"_blank\">2367508</a></p>\n</td>\n<td valign=\"top\" width=\"518\">\n<p>Correction for selection of changedocs in DIMP system</p>\n</td>\n<td valign=\"top\" width=\"238\">S4CORE</td>\n<td valign=\"top\" width=\"238\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p> 2016-10-31</p>\n</td>\n</tr>\n<tr>\n<td width=\"118\">\n<p><a href=\"/notes/2369405\" target=\"_blank\">2369405</a></p>\n</td>\n<td valign=\"top\" width=\"518\">\n<p>Issue in Document Flow (SAP S/4HANA)</p>\n</td>\n<td valign=\"top\" width=\"238\">S4CORE</td>\n<td valign=\"top\" width=\"238\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p> 2016-10-31</p>\n</td>\n</tr>\n<tr>\n<td width=\"118\">\n<p><a href=\"/notes/2369962\" target=\"_blank\">2369962</a></p>\n</td>\n<td valign=\"top\" width=\"518\">\n<p>Authorization issue and G/L not defaulting from PPOMA corrections</p>\n</td>\n<td valign=\"top\" width=\"238\">S4CORE</td>\n<td valign=\"top\" width=\"238\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p> 2016-10-31</p>\n</td>\n</tr>\n<tr>\n<td width=\"118\">\n<p><a href=\"/notes/2371490\" target=\"_blank\">2371490</a></p>\n</td>\n<td valign=\"top\" width=\"518\">\n<p>Delivery date validation</p>\n</td>\n<td valign=\"top\" width=\"238\">S4CORE</td>\n<td valign=\"top\" width=\"238\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p> 2016-10-31</p>\n</td>\n</tr>\n<tr>\n<td width=\"118\">\n<p><a href=\"/notes/2371666\" target=\"_blank\">2371666</a></p>\n</td>\n<td valign=\"top\" width=\"518\">\n<p>MD01N: Setup of MRP-Records via PPH_SETUP_MRPRECORDS or PPH_SETUP_MRPRECORDS_SIMU incomplete</p>\n</td>\n<td valign=\"top\" width=\"238\">S4CORE</td>\n<td valign=\"top\" width=\"238\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p> 2016-10-31</p>\n</td>\n</tr>\n<tr>\n<td width=\"118\">\n<p><a href=\"/notes/2372008\" target=\"_blank\">2372008</a></p>\n</td>\n<td valign=\"top\" width=\"518\">\n<p>Switch from DB_COMMIT to COMMIT WORK AND WAIT for consolidation of Data Aging carry forward records and archive representatives</p>\n</td>\n<td valign=\"top\" width=\"238\">S4CORE</td>\n<td valign=\"top\" width=\"238\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p> 2016-10-31</p>\n</td>\n</tr>\n<tr>\n<td width=\"118\">\n<p><a href=\"/notes/2372230\" target=\"_blank\">2372230</a></p>\n</td>\n<td valign=\"top\" width=\"518\">\n<p>Fill BWTAR in data aging carry forward records and archive representatives</p>\n</td>\n<td valign=\"top\" width=\"238\">S4CORE</td>\n<td valign=\"top\" width=\"238\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p> 2016-10-31</p>\n</td>\n</tr>\n<tr>\n<td width=\"118\">\n<p><a href=\"/notes/2372605\" target=\"_blank\">2372605</a></p>\n</td>\n<td valign=\"top\" width=\"518\">\n<p>Issue while creating BP role as a supplier in transaction BP</p>\n</td>\n<td valign=\"top\" width=\"238\">S4CORE</td>\n<td valign=\"top\" width=\"238\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p> 2016-10-31</p>\n</td>\n</tr>\n<tr>\n<td width=\"118\">\n<p><a href=\"/notes/2373940\" target=\"_blank\">2373940</a></p>\n</td>\n<td valign=\"top\" width=\"518\">\n<p>Corrective measure for changes to the BOM Maintenance application</p>\n</td>\n<td valign=\"top\" width=\"238\">S4CORE</td>\n<td valign=\"top\" width=\"238\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p> 2016-10-31</p>\n</td>\n</tr>\n<tr>\n<td width=\"118\">\n<p><a href=\"/notes/2376505\" target=\"_blank\">2376505</a></p>\n</td>\n<td valign=\"top\" width=\"518\">\n<p>Revise Payment Proposals: Cannot Mass Block Items for Payment</p>\n</td>\n<td valign=\"top\" width=\"238\">S4CORE</td>\n<td valign=\"top\" width=\"238\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p> 2016-10-31</p>\n</td>\n</tr>\n<tr>\n<td width=\"118\">\n<p><a href=\"/notes/2376747\" target=\"_blank\">2376747</a></p>\n</td>\n<td valign=\"top\" width=\"518\">\n<p>Moving average price is not changed by goods receipt</p>\n</td>\n<td valign=\"top\" width=\"238\">S4CORE</td>\n<td valign=\"top\" width=\"238\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p> 2016-10-31</p>\n</td>\n</tr>\n<tr>\n<td width=\"118\">\n<p><a href=\"/notes/2377529\" target=\"_blank\">2377529</a></p>\n</td>\n<td valign=\"top\" width=\"518\">\n<p>V_T012 maintenance dialog does not exist</p>\n</td>\n<td valign=\"top\" width=\"238\">S4CORE</td>\n<td valign=\"top\" width=\"238\">\n<p>Yes</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p> 2016-10-31</p>\n</td>\n</tr>\n<tr>\n<td width=\"118\">\n<p><a href=\"/notes/2378915\" target=\"_blank\">2378915</a></p>\n</td>\n<td valign=\"top\" width=\"518\">\n<p>Manage PIRs redirect of non active versions</p>\n</td>\n<td valign=\"top\" width=\"238\">S4CORE</td>\n<td valign=\"top\" width=\"238\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p> 2016-10-31</p>\n</td>\n</tr>\n<tr>\n<td width=\"118\">\n<p><a href=\"/notes/2379565\" target=\"_blank\">2379565</a></p>\n</td>\n<td valign=\"top\" width=\"518\">\n<p>The Incoterm 2 field is converted incorrectly</p>\n</td>\n<td valign=\"top\" width=\"238\">S4CORE</td>\n<td valign=\"top\" width=\"238\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p> 2016-10-31</p>\n</td>\n</tr>\n<tr>\n<td width=\"118\">\n<p><a href=\"/notes/2379790\" target=\"_blank\">2379790</a></p>\n</td>\n<td valign=\"top\" width=\"518\">\n<p>Reading Purchase Order for multiple items in a PR</p>\n</td>\n<td valign=\"top\" width=\"238\">S4CORE</td>\n<td valign=\"top\" width=\"238\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p> 2016-10-31</p>\n</td>\n</tr>\n<tr>\n<td width=\"118\">\n<p><a href=\"/notes/2380548\" target=\"_blank\">2380548</a></p>\n</td>\n<td valign=\"top\" width=\"518\">\n<p>Error during assignment to investment program item, maintenance order</p>\n</td>\n<td valign=\"top\" width=\"238\">S4CORE</td>\n<td valign=\"top\" width=\"238\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p> 2016-10-31</p>\n</td>\n</tr>\n<tr>\n<td width=\"118\">\n<p><a href=\"/notes/2381346\" target=\"_blank\">2381346</a></p>\n</td>\n<td valign=\"top\" width=\"518\">\n<p>Characteristic based planning aborts for long material numbers</p>\n</td>\n<td valign=\"top\" width=\"238\">S4CORE</td>\n<td valign=\"top\" width=\"238\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p> 2016-10-31</p>\n</td>\n</tr>\n<tr>\n<td width=\"118\">\n<p><a href=\"/notes/2381849\" target=\"_blank\">2381849</a></p>\n</td>\n<td valign=\"top\" width=\"518\">\n<p>Missing condition tables B082 and B083</p>\n</td>\n<td valign=\"top\" width=\"238\">S4CORE</td>\n<td valign=\"top\" width=\"238\">\n<p>Yes</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p> 2016-10-31</p>\n</td>\n</tr>\n<tr>\n<td width=\"118\">\n<p><a href=\"/notes/2382748\" target=\"_blank\">2382748</a></p>\n</td>\n<td valign=\"top\" width=\"518\">\n<p>KB11N: Short dump when ledger group filled</p>\n</td>\n<td valign=\"top\" width=\"238\">S4CORE</td>\n<td valign=\"top\" width=\"238\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p> 2016-10-31</p>\n</td>\n</tr>\n<tr>\n<td width=\"118\">\n<p><a href=\"/notes/2327999\" target=\"_blank\">2327999</a></p>\n</td>\n<td valign=\"top\" width=\"518\">\n<p>QuantityWare CDS Extensions For IS-OIL</p>\n</td>\n<td valign=\"top\" width=\"238\">IS-OIL</td>\n<td valign=\"top\" width=\"238\">\n<p>Yes</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p> 2016-10-31</p>\n</td>\n</tr>\n<tr>\n<td width=\"118\">\n<p><a href=\"/notes/2379816\" target=\"_blank\">2379816</a></p>\n</td>\n<td valign=\"top\" width=\"518\">\n<p>Unicode conversion dump related to KONV</p>\n</td>\n<td valign=\"top\" width=\"238\">IS-OIL</td>\n<td valign=\"top\" width=\"238\">\n<p>Yes</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p> 2016-10-31</p>\n</td>\n</tr>\n<tr>\n<td width=\"118\">\n<p><a href=\"/notes/2172384\" target=\"_blank\">2172384</a></p>\n</td>\n<td valign=\"top\" width=\"518\">\n<p>J1INCHLN and J1INCHLC: Multiple section legal change</p>\n</td>\n<td valign=\"top\" width=\"238\">FI-CA</td>\n<td valign=\"top\" width=\"238\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p> 2016-11-03</p>\n</td>\n</tr>\n<tr>\n<td width=\"118\">\n<p><a href=\"/notes/2364845\" target=\"_blank\">2364845</a></p>\n</td>\n<td valign=\"top\" width=\"518\">\n<p>Migration: E543(FINS_RECON) in RC3</p>\n</td>\n<td valign=\"top\" width=\"238\">S4CORE</td>\n<td valign=\"top\" width=\"238\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p> 2016-11-08</p>\n</td>\n</tr>\n<tr>\n<td width=\"118\">\n<p><a href=\"/notes/2384182\" target=\"_blank\">2384182</a></p>\n</td>\n<td valign=\"top\" width=\"518\">\n<p>S/4HANA 1511+1610: Material master maintenance: Dumps when using screen sequence DI</p>\n</td>\n<td valign=\"top\" width=\"238\">S4CORE</td>\n<td valign=\"top\" width=\"238\">\n<p>Yes</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p> 2017-01-09</p>\n</td>\n</tr>\n<tr>\n<td width=\"118\">\n<p><a href=\"/notes/2399372\" target=\"_blank\">2399372</a></p>\n</td>\n<td valign=\"top\" width=\"518\">\n<p>Authorization check failed for SRT_SR_P</p>\n</td>\n<td valign=\"top\" width=\"238\">SAP_BASIS</td>\n<td valign=\"top\" width=\"238\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p> 2017-01-09</p>\n</td>\n</tr>\n<tr>\n<td width=\"118\">\n<p><a href=\"/notes/2399423\" target=\"_blank\">2399423</a></p>\n</td>\n<td valign=\"top\" width=\"518\">\n<p>Dump in characteristic value assignment</p>\n</td>\n<td valign=\"top\" width=\"238\">SAP_ABA</td>\n<td valign=\"top\" width=\"238\">\n<p>Yes</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p> 2017-01-16</p>\n</td>\n</tr>\n<tr>\n<td width=\"118\">\n<p><a href=\"/notes/2400809\" target=\"_blank\">2400809</a></p>\n</td>\n<td valign=\"top\" width=\"518\">\n<p>Dump POSTING_ILLEGAL_STATEMENT (TSTR_GENERATE, DB_COMMIT)</p>\n</td>\n<td valign=\"top\" width=\"238\">SAP_BASIS</td>\n<td valign=\"top\" width=\"238\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p> 2017-01-16</p>\n</td>\n</tr>\n<tr>\n<td width=\"118\">\n<p><a href=\"/notes/2402373\" target=\"_blank\">2402373</a></p>\n</td>\n<td valign=\"top\" width=\"518\">\n<p>OBJECTS_OBJREF_NOT_ASSIGNED in class CL_SWF_UTL_WAPI_SERVICES</p>\n</td>\n<td valign=\"top\" width=\"238\">SAP_BASIS</td>\n<td valign=\"top\" width=\"238\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p> 2017-01-16</p>\n</td>\n</tr>\n<tr>\n<td width=\"118\">\n<p><a href=\"/notes/2405390\" target=\"_blank\">2405390</a></p>\n</td>\n<td valign=\"top\" width=\"518\">\n<p>MFLE: Short version in output conversion calculated incorrectly</p>\n</td>\n<td valign=\"top\" width=\"238\">SAP_ABA</td>\n<td valign=\"top\" width=\"238\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p> 2017-01-16</p>\n</td>\n</tr>\n<tr>\n<td width=\"118\">\n<p><a href=\"/notes/2415736\" target=\"_blank\">2415736</a></p>\n</td>\n<td valign=\"top\" width=\"518\">\n<p>Warranty : Post Versions to Reimburser fails via action A042 in transaction WTY</p>\n</td>\n<td valign=\"top\" width=\"238\">S4CORE</td>\n<td valign=\"top\" width=\"238\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p> 2017-01-23</p>\n</td>\n</tr>\n<tr>\n<td width=\"118\">\n<p><a href=\"/notes/2280748\" target=\"_blank\">2280748</a></p>\n</td>\n<td valign=\"top\" width=\"518\">\n<p>ED: MIGO, dump due to incompletely removed MM-IM-ED LIS S465 &amp; S466</p>\n</td>\n<td valign=\"top\" width=\"238\">S4CORE</td>\n<td valign=\"top\" width=\"238\">\n<p>Yes</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p> 2017-01-31</p>\n</td>\n</tr>\n<tr>\n<td width=\"118\">\n<p><a href=\"/notes/2427850\" target=\"_blank\">2427850</a></p>\n</td>\n<td valign=\"top\" width=\"518\">\n<p>WTY : Character field entry for RECNT dumps during method call VALUE_CHANGE</p>\n</td>\n<td valign=\"top\" width=\"238\">S4CORE</td>\n<td valign=\"top\" width=\"238\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p> 2017-02-16</p>\n</td>\n</tr>\n<tr>\n<td width=\"118\">\n<p><a href=\"/notes/2429609\" target=\"_blank\">2429609</a></p>\n</td>\n<td valign=\"top\" width=\"518\">\n<p>S/4 Hana: Technical update of view V_PEG_MSEG2</p>\n</td>\n<td valign=\"top\" width=\"238\">S4CORE</td>\n<td valign=\"top\" width=\"238\">\n<p>Yes</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p> 2017-02-17</p>\n</td>\n</tr>\n<tr>\n<td width=\"118\">\n<p><a href=\"/notes/2399477\" target=\"_blank\">2399477</a></p>\n</td>\n<td valign=\"top\" width=\"518\">\n<p>SAP GUI for HTML: ~singletransaction=3 allows /nTX</p>\n</td>\n<td valign=\"top\" width=\"238\">SAP_BASIS</td>\n<td valign=\"top\" width=\"238\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p> 2017-02-20</p>\n</td>\n</tr>\n<tr>\n<td width=\"118\">\n<p><a href=\"/notes/2372221\" target=\"_blank\">2372221</a></p>\n</td>\n<td valign=\"top\" width=\"518\">\n<p>Performance improvement on GT_ADDR_XPCPT</p>\n</td>\n<td valign=\"top\" width=\"238\">SAP_BASIS</td>\n<td valign=\"top\" width=\"238\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p> 2017-04-12</p>\n</td>\n</tr>\n<tr>\n<td width=\"118\">\n<p><a href=\"/notes/2461676\" target=\"_blank\">2461676</a></p>\n</td>\n<td valign=\"top\" width=\"518\">\n<p>New MM-GPD stock sync validation to consider legacy MM</p>\n</td>\n<td valign=\"top\" width=\"238\">S4CORE</td>\n<td valign=\"top\" width=\"238\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p> 2017-04-24</p>\n</td>\n</tr>\n<tr>\n<td width=\"118\">\n<p><a href=\"/notes/2467650\" target=\"_blank\">2467650</a></p>\n</td>\n<td valign=\"top\" width=\"518\">\n<p>Authorization issues in viewing Document Info Record</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p>S4CORE</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p> 2017-05-08</p>\n</td>\n</tr>\n<tr>\n<td width=\"118\">\n<p><a href=\"/notes/2463740\" target=\"_blank\">2463740</a></p>\n</td>\n<td valign=\"top\" width=\"518\">\n<p>Error message FINS_ACDOC_CUST 215 during postings</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p>S4CORE</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p> 2017-05-19</p>\n</td>\n</tr>\n<tr>\n<td width=\"118\">\n<p><span class=\"sapUxAPObjectPageHeaderIdentifierContainer\" id=\"__xmlview2--idObjectPageHeader-identifierLineContainer\"><a href=\"/notes/2477735\" target=\"_blank\">2477735</a></span></p>\n</td>\n<td valign=\"top\" width=\"518\">\n<p>Annotation API: enable cache usage for get_annos_mass</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p>SAP_BASIS</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p> 2017-07-10</p>\n</td>\n</tr>\n<tr>\n<td width=\"118\">\n<p><span class=\"sapUxAPObjectPageHeaderIdentifierContainer\"><a href=\"/notes/2450514\" target=\"_blank\">2450514</a></span></p>\n</td>\n<td valign=\"top\" width=\"518\">\n<p>BRFplus: Numeric Comparison doesn't work within IF-FORMULA</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p>SAP_BASIS</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p> 2017-07-12</p>\n</td>\n</tr>\n<tr>\n<td width=\"118\">\n<p><span class=\"sapUxAPObjectPageHeaderIdentifierContainer\"><a href=\"/notes/2489305\" target=\"_blank\">2489305</a></span></p>\n</td>\n<td valign=\"top\" width=\"518\">\n<p>Dump while inserting international address version for a person</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p>SAP_BASIS</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p> 2017-07-12</p>\n</td>\n</tr>\n<tr>\n<td width=\"118\">\n<p><span class=\"sapUxAPObjectPageHeaderIdentifierContainer\"><a href=\"/notes/2485784\" target=\"_blank\">2485784</a></span></p>\n</td>\n<td valign=\"top\" width=\"518\">\n<p>ALV export: Cannot save to clipboard in browser</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p>SAP_BASIS</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p> 2017-07-12</p>\n</td>\n</tr>\n<tr>\n<td width=\"118\">\n<p><span class=\"sapUxAPObjectPageHeaderIdentifierContainer\"><a href=\"/notes/2490652\" target=\"_blank\">2490652</a></span></p>\n</td>\n<td valign=\"top\" width=\"518\">\n<p>Legacy DAC maps business key to initial BOPF key</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p>SAP_BASIS</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p> 2017-07-12</p>\n</td>\n</tr>\n<tr>\n<td width=\"118\">\n<p><span class=\"sapUxAPObjectPageHeaderIdentifierContainer\"><a href=\"/notes/2491892\" target=\"_blank\">2491892</a></span></p>\n</td>\n<td valign=\"top\" width=\"518\">\n<p>DD: keep switched off objects in gentab for DD_WORKLIST_ACT</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p>SAP_BASIS</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p> 2017-07-27</p>\n</td>\n</tr>\n<tr>\n<td width=\"118\">\n<p><a href=\"/notes/2500159\" target=\"_blank\">2500159</a></p>\n</td>\n<td valign=\"top\" width=\"518\">\n<p>MESSAGE_TYPE_X issued during delivery creation</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p>S4CORE</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p> 2017-08-28</p>\n</td>\n</tr>\n<tr>\n<td width=\"118\">\n<p><a href=\"/notes/2520306\" target=\"_blank\">2520306</a></p>\n</td>\n<td valign=\"top\" width=\"518\">\n<p>Condition table index not activated after upgrade</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p>S4CORE</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p> 2017-09-06</p>\n</td>\n</tr>\n<tr>\n<td width=\"118\">\n<p><a href=\"/notes/2527709\" target=\"_blank\">2527709</a></p>\n</td>\n<td valign=\"top\" width=\"518\">\n<p>Program Dump after activating Business Function LOG_PP_EWM_MAN_2</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p>S4CORE</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p> 2017-09-06</p>\n</td>\n</tr>\n<tr>\n<td width=\"118\">\n<p><a href=\"/notes/2485570\" target=\"_blank\">2485570</a></p>\n</td>\n<td valign=\"top\" width=\"518\">\n<p>Downport of bugfix for CDS Metadata Extensions Runtime Data Provider</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p>SAP_BASIS</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p> 2017-09-07</p>\n</td>\n</tr>\n</tbody>\n</table></div>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"0\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td width=\"82\">\n<p><a href=\"/notes/2652897\" target=\"_blank\">2652897</a></p>\n</td>\n<td valign=\"top\" width=\"349\">\n<p> List ATS UIBB and Tree UIBB: API for choosing selection eventing not working correctly</p>\n</td>\n<td valign=\"top\" width=\"126\">\n<p> SAP_UI</p>\n</td>\n<td valign=\"top\" width=\"175\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"142\">\n<p>  2018-07-23</p>\n</td>\n</tr>\n<tr>\n<td width=\"82\">\n<p> <a href=\"/notes/2655756\" target=\"_blank\">2655756</a></p>\n</td>\n<td valign=\"top\" width=\"349\">\n<p> Tree UIBB: Conditional Formatting when master column has display type image</p>\n</td>\n<td valign=\"top\" width=\"126\">\n<p> SAP_UI</p>\n</td>\n<td valign=\"top\" width=\"175\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"142\">\n<p> 2018-07-23</p>\n</td>\n</tr>\n</tbody>\n</table></div>\n<p><strong><strong>Notes to be applied on top of <strong>Feature Package Stack 00 (only relevant for customers using SEM-BCS)</strong></strong></strong></p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"0\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td width=\"118\">\n<p><strong>SAP Note</strong></p>\n</td>\n<td valign=\"top\" width=\"518\">\n<p><strong>Description</strong></p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p><strong>Software Component</strong></p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p><strong>Manual Activities required</strong></p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p><strong> Added on</strong></p>\n</td>\n</tr>\n<tr>\n<td width=\"118\">\n<p><a href=\"/notes/2352766\" target=\"_blank\">2352766</a></p>\n</td>\n<td valign=\"top\" width=\"518\">\n<p>Enhancement of MDF metadata for S/4 1610</p>\n</td>\n<td valign=\"top\" width=\"238\">S4CORE</td>\n<td valign=\"top\" width=\"238\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p> 2016-10-31</p>\n</td>\n</tr>\n<tr>\n<td width=\"118\">\n<p><a href=\"/notes/2367523\" target=\"_blank\">2367523</a></p>\n</td>\n<td valign=\"top\" width=\"518\">\n<p>Enhancement of the SEM-BCS data model for S/4 HANA OP</p>\n</td>\n<td valign=\"top\" width=\"238\">S4CORE</td>\n<td valign=\"top\" width=\"238\">\n<p>No</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p> 2016-11-09</p>\n</td>\n</tr>\n</tbody>\n</table></div>\n<p><strong><strong>Notes to be applied on top of <strong>Feature Package Stack 00 (only relevant for customers using </strong></strong>IS-RETAIL<strong><strong>)</strong></strong></strong></p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"0\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td width=\"118\">\n<p><strong>SAP Note</strong></p>\n</td>\n<td valign=\"top\" width=\"518\">\n<p><strong>Description</strong></p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p><strong>Software Component</strong></p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p><strong>Manual Activities required</strong></p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p><strong> Added on</strong></p>\n</td>\n</tr>\n<tr>\n<td width=\"118\">\n<p><a href=\"/notes/2224330\" target=\"_blank\">2224330</a></p>\n</td>\n<td valign=\"top\" width=\"518\">\n<p>Parameter list of append search help \"WRF_BETR_WHSH_APPEND\" differs from appending one</p>\n</td>\n<td valign=\"top\" width=\"238\">S4CORE</td>\n<td valign=\"top\" width=\"238\">\n<p>Yes</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p> 2016-10-31</p>\n</td>\n</tr>\n<tr>\n<td width=\"118\">\n<p><a href=\"/notes/2393010\" target=\"_blank\">2393010</a></p>\n</td>\n<td valign=\"top\" width=\"518\">\n<p>Negative stock for empties after stock transfer</p>\n</td>\n<td valign=\"top\" width=\"238\">S4CORE</td>\n<td valign=\"top\" width=\"238\">\n<p>Yes</p>\n</td>\n<td valign=\"top\" width=\"238\">\n<p> 2016-11-21</p>\n</td>\n</tr>\n</tbody>\n</table></div>\n<p><strong>Important Changes made after Release of Feature Package Stack 00</strong></p>\n<p><strong><strong><strong><strong><strong><strong><strong><strong><strong>2020-05-11: </strong></strong></strong></strong></strong></strong></strong></strong></strong>Removed incorrect supported Kernel version SAP KERNEL 7.73 64-BIT UNICODE.</p>\n<p><strong><strong><strong><strong><strong><strong><strong><strong><strong>2019-04-01:</strong></strong></strong></strong></strong></strong></strong></strong></strong> Inserted: United Kingdom leaving the EU: For information on how a “hard Brexit” (= a no-deal scenario) would impact your <em>SAP S/4HANA </em>system, please see SAP Note <a href=\"/notes/2749671\" target=\"_blank\">2749671</a>.<br/><br/><strong><strong><strong><strong><strong><strong><strong><strong><strong>2017-09-04: </strong></strong></strong></strong></strong></strong></strong></strong></strong>SAP HANA 2.0 SPS 02 Revision 20: SP note <a href=\"/notes/2527538\" target=\"_blank\">2527538</a> (AMDP Procedure Execution Fails With Column Store Error) and SAP note <a href=\"/notes/2527648\" target=\"_blank\">2527648</a> (Column Value Equals Null Cannot be Detected by IS NULL in Debug Mode) inserted.</p>\n<p><strong><strong><strong><strong><strong>2017-08-30</strong>: </strong></strong></strong></strong>SAP HANA 1.0 minimum required revision changed from Revision 122.03 to 122.05 to synchronize with requirements in SUM tool.<br/><strong>2016-11-18</strong>: Note 2224330 moved to list of notes which is only relevant for customers using IS-RETAIL.<br/><strong>2016-11-21: </strong>Note <a href=\"/notes/2356364\" target=\"_blank\">2356364</a> deleted in list of notes to be implemented on top of Feature Package Stack 00.<br/><strong>2016-12-08: </strong>Section<strong> '</strong>Please ensure to apply note <a href=\"/notes/2344014\" target=\"_blank\">2344014</a> on your S/4HANA system before starting further lifecycle management processes such as a Support Package update or an Add-on installation via SPAM or SAINT.' inserted under 'General / Important Considerations'.<br/><strong>2016-12-20</strong>: 'SAP HANA 2.0 is not released for Feature Package Stack 00.' inserted under 'General / Important Considerations'.<br/><strong>2017-03-31: </strong>HANA 2.0 chapter inserted under 'General / Important Considerations'.<br/><strong>2017-06-12</strong>: SWPM and SUM Support Package Level increased to 20.<br/><strong><strong>2017-06-19</strong>: </strong>SAP HANA 2.0 related note <a href=\"/notes/2487855\" target=\"_blank\">2487855</a> (Possible Table Inconsistencies on SAP HANA 2.0 SPS01 due to Internal Persistence Structure Migration) inserted.</p>", "noteVersion": 107}, {"note": "2443860", "noteTitle": "2443860 - Release Information Note for the Fiori application Fix Errors 05/2017", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>This note provides additional information regarding the Fiori application Fix Errors in the area of Oil &amp; Gas Upstream Operations Management (UOM). This note contains the minimum required and recommended fixes that should be installed to make the app functional either in the frontend server or in the related backend server.</p>\n<p>With the transactional app Fix Errors, you can view errors present in the selected Production Network and allocation period and then can perform subsequent fix. The app's landing page also provides a detailed description of the errors and you can navigate to relevant app to fix the error. This also helps you group and filter the type of errors. The fixed errors are removed from the list and the app displays the updated list.</p>\n<p>Key Features:</p>\n<ul>\n<li>View Errors</li>\n<li>Navigate to the relevant app from where the errors can be fixed</li>\n<li>Automatic navigation back to the Fix Error app with the updated error list once an error is fixed</li>\n</ul>\n<p>This note is subject to change. Ensure you check it on a regular basis.</p>\n<p>New or signficant enhancements of delivered Fiori applications for area Fix Errors are delivered with Stack FPS2 of productversion S/4HANA OP 1610</p>\n<p>Following Fiori apps are affected:</p>\n<p><strong>Fiori application         Status</strong></p>\n<p>Fix Errors                     New; OData service UPS_FIX_ERROR has to be registered</p>\n<p>Gateway Role               SAP_BR_FIELD_OPERATOR_IOG has to be added</p>\n<p>Additional information about the FIORI application can be found in the <a href=\"https://fioriappslibrary.hana.ondemand.com/sap/fix/externalViewer/\" target=\"_blank\">SAP Fiori apps reference library</a></p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>FIORI, Design, UI, APP, UPS_FIXERRORSS1, SAP Oil &amp; Gas, UOM, Upstream Operations Management, Fix Errors</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>Please check the release information note 2346431 for product version 'S/4HANA OP 1610' and 2356208  for product version 'Fiori for S/4HANA OP 1610' for general installation and infrastructure topics.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Apply the Stack FPS2  of productversion S/4HANA OP 1610 on your Frontend Server system</p>\n<p>Apply the Stack FPS2  of productversion S/4HANA OP 1610 on the assigned Backend systems</p>", "noteVersion": 3}, {"note": "2443804", "noteTitle": "2443804 - Release Information Notes for Common Reuse Library 05/2017", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>This note provides additional information regarding the Fiori reuse library Reusable Component for UOM in the area of Oil &amp; Gas Upstream Operations Management (UOM). This note contains the minimum required and recommended fixes that should be installed to make the app functional either in the frontend server or in the related backend server.</p>\n<p>The Fiori reuse component: Reusable Component for UOM consists of the following features:</p>\n<ul>\n<li>Primary Selector</li>\n<li>Variant Management</li>\n<li>Date &amp; Frequency Selectors</li>\n</ul>\n<p>This note is subject to change. Ensure you check it on a regular basis.</p>\n<p>New or signficant enhancements of delivered Fiori reuse library Reusable Component for UOM in the area of Oil &amp; Gas Upstream Operations Management (UOM) are delivered with Stack FPS2 of productversion S/4HANA OP 1610</p>\n<p>Following Fiori apps are affected:</p>\n<p><strong>Fiori Reuse Library              Status</strong></p>\n<p>Common Reuse Library          New; OData service UPS_COMMON has to be registered</p>\n<p>Additional information about the Fiori application can be found in the <a href=\"https://fioriappslibrary.hana.ondemand.com/sap/fix/externalViewer/\" target=\"_blank\">SAP Fiori apps reference library</a></p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Fiori, Design, UI, APP, UPS_COMMONSS1, SAP Oil &amp; Gas, UOM, Upstream Operations Management, Reusable Component for UOM.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>Please check the release information note 2346431 for product version 'S/4HANA OP 1610' and 2356208  for product version 'Fiori for S/4HANA OP 1610' for general installation and infrastructure topics.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Apply the Stack FPS2 of productversion S/4HANA OP 1610 on your Frontend Server system</p>\n<div class=\"longtext\">\n<div class=\"longtext\">\n<div class=\"longtext\">\n<p>Apply the Stack FPS2 of productversion S/4HANA OP 1610 on the assigned  Backend systems</p>\n</div>\n</div>\n</div>", "noteVersion": 2}, {"note": "2443858", "noteTitle": "2443858 - Release Information Note for the Fiori application Manage Work Order Deferment Events 05/2017", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>This note provides additional information regarding the Fiori application Manage Work Order Deferment Events in the area of Oil &amp; Gas Upstream Operations Management (UOM). This note contains the minimum required and recommended fixes that should be installed to make the app functional either in the frontend server or in the related backend server.</p>\n<p>With the transactional app Manage Work Order Deferment Events, a Deferment Analyst can analyze whether deferment events are linked to available work orders. You can view existing events linked to work orders, link the events where they are missing or link multiple events to an existing work order.</p>\n<p>Key Features:</p>\n<ul>\n<li>View work orders with deferment events</li>\n<li>View work orders without deferment events</li>\n<li>Link deferment events to work orders</li>\n</ul>\n<p>This note is subject to change. Ensure you check it on a regular basis.</p>\n<p>New or signficant enhancements of delivered Fiori applications for area Manage Work Order Deferment Events are delivered with Stack FPS2 of productversion S/4HANA OP 1610</p>\n<p>Following Fiori apps are affected:</p>\n<p><strong>Fiori application                                            Status</strong></p>\n<p>Manage Work Order Deferment Events            New; OData service UPS_DEF_WORK_ORDER has to be registered</p>\n<p>Gateway Role                                               SAP_BR_DEFERMENT_ANALYST_IOG has to be added</p>\n<p>Additional information about the Fiori application can be found in the <a href=\"https://fioriappslibrary.hana.ondemand.com/sap/fix/externalViewer/\" target=\"_blank\">SAP Fiori apps reference library</a></p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Fiori, Design, UI, APP, UPS_DEF_WOEVTS1, SAP Oil &amp; Gas, UOM, Upstream Operations Management, Manage Work Order Deferment Events.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>Please check the release information note 2346431 for product version 'S/4HANA OP 1610' and 2356208  for product version 'Fiori for S/4HANA OP 1610' for general installation and infrastructure topics.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<div class=\"longtext\">\n<p>Apply the Stack FPS2  of productversion S/4HANA OP 1610 on your Frontend Server system</p>\n<p>Apply the Stack FPS2  of productversion S/4HANA OP 1610 on the assigned  Backend systems</p>\n</div>", "noteVersion": 2}, {"note": "2443809", "noteTitle": "2443809 - Release Information Note for the Fiori application Manage Forecast Projects 05/2017", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>This note provides additional information regarding the Fiori application Manage Forecast Projects in the area of Oil &amp; Gas Upstream Operations Management (UOM). This note contains the minimum required and recommended fixes that should be installed to make the app functional either in the frontend server or in the related backend server.</p>\n<div class=\"sapUiRespGridSpanL8 sapUiRespGridSpanM8 sapUiRespGridSpanS12 sapUiRespGridSpanXL8\">\n<div id=\"__html2\">\n<p class=\"- topic/p\">With the transactional app Manage Forecast Projects you, in your role as a Forecast Specialist, can maintain the structure of forecast projects and trigger the 'Start Data Gathering' process. You can filter your authorized forecast projects by name, forecast type, effective dates, and forecast date.</p>\n<p class=\"- topic/p\">Key Features</p>\n<ul>\n<li>Maintain forecast projects, their header details, mediums, conversion factors, equations, and hierarchy mappings</li>\n<li>Copy forecast projects to new forecast projects</li>\n<li>Maintain scenarios in forecast projects and their header details, conversion factors, and equations</li>\n<li>Copy scenarios to new scenarios in forecast projects</li>\n<li>Maintain networks in what-if scenarios and their header details, mediums, conversion factors, equations, and hierarchy mappings</li>\n<li>View network objects and links of networks in what-if-scenarios</li>\n<li>Start data gathering for forecast projects</li>\n</ul>\n<div class=\"longtext\">\n<div class=\"sapUiRespGridSpanL8 sapUiRespGridSpanM8 sapUiRespGridSpanS12 sapUiRespGridSpanXL8\">\n<div id=\"__html2\">\n<p>This note is subject to change. Ensure you check it on a regular basis.</p>\n<p>New or signficant enhancements of delivered Fiori applications for area Manage Forecast Projects are delivered with Stack FPS2 of productversion S/4HANA OP 1610</p>\n<p>Following Fiori apps are affected:</p>\n<p><strong>Fiori application                      Status</strong></p>\n<p>Manage Forecast Projects         New; OData service UPS_FC_MNG_PROJECT has to be registered.</p>\n<p>Gateway Role                         SAP_BR_FORECAST_SPECIALIST_IOG has to be added</p>\n<p>Additional information about the Fiori application can be found in the <a href=\"https://fioriappslibrary.hana.ondemand.com/sap/fix/externalViewer/\" target=\"_blank\">SAP Fiori apps reference library</a></p>\n</div>\n</div>\n</div>\n</div>\n</div>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Fiori, Design, UI, APP, UPS_FC_MNGPROS1, SAP Oil &amp; Gas, UOM, Upstream Operations Management, Forecasting, Manage Forecast Projects.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<div class=\"longtext\">\n<p>Please check the release information note 2346431 for product version 'S/4HANA OP 1610' and 2356208  for product version 'Fiori for S/4HANA OP 1610' for general installation and infrastructure topics.</p>\n</div>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Apply the Stack FPS2 of productversion S/4HANA OP 1610 on your Frontend Server system</p>\n<div class=\"longtext\">\n<div class=\"longtext\">\n<div class=\"longtext\">\n<p>Apply the Stack FPS2 of productversion S/4HANA OP 1610 on the assigned  Backend systems</p>\n</div>\n</div>\n</div>", "noteVersion": 2}, {"note": "2443856", "noteTitle": "2443856 - Release Information Note for the Fiori application Manage Hierarchy 05/2017", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>This note provides additional information regarding the Fiori application Manage Hierarchy in the area of Oil &amp; Gas Upstream Operations Management (UOM). This note contains the minimum required and recommended fixes that should be installed to make the app functional either in the frontend server or in the related backend server.</p>\n<p>With the transactional app Manage Hierarchy you, in your role as a Business Analyst (OG), can manage custom hierarchies (for example geographical, organizational and so on) for Upstream Operations Management. You can also filter your custom hierarchies by free search on name.</p>\n<p><strong>Key Features:</strong></p>\n<ul>\n<li>Manage custom hierarchies (for example geographical, organizational and so on)</li>\n<li>Manage nodes (for example continents, countries, states, counties, cities and so on) in custom hierarchies (for example, geographical hierarchy) in parent-child relationships</li>\n</ul>\n<p>This note is subject to change. Ensure you check it on a regular basis.</p>\n<p>New or signficant enhancements of delivered Fiori applications for area Manage Hierarchy are delivered with Stack FPS2 of productversion S/4HANA OP 1610</p>\n<p>Following Fiori apps are affected:</p>\n<p><strong>Fiori application            Status</strong></p>\n<p>Manage Hierarchy           New; OData service UPS_MNG_HIERARCHY has to be registered</p>\n<p>Gateway Role                  SAP_BR_BUSINESS_ANALYST_IOG has to be added</p>\n<p>Additional information about the Fiori application can be found in the <a href=\"https://fioriappslibrary.hana.ondemand.com/sap/fix/externalViewer/\" target=\"_blank\">SAP Fiori apps reference library</a></p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Fiori, Design, UI, APP, UPS_MNGHIERS1, SAP Oil &amp; Gas, UOM, Upstream Operations Management, Forecasting, Manage Hierarchy</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>Please check the release information note 2346431 for product version 'S/4HANA OP 1610' and 2356208  for product version 'Fiori for S/4HANA OP 1610' for general installation and infrastructure topics.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Apply the Stack FPS2 of productversion S/4HANA OP 1610 on your Frontend Server system</p>\n<div class=\"longtext\">\n<div class=\"longtext\">\n<div class=\"longtext\">\n<p>Apply the Stack FPS2 of productversion S/4HANA OP 1610 on the assigned  Backend systems</p>\n</div>\n</div>\n</div>", "noteVersion": 2}, {"note": "2452347", "noteTitle": "2452347 - Unable to update Business partner role", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>Unable to update business partner role.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p><PERSON><PERSON><PERSON>, UOM, Business partner Role.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>Program error.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Kindly implement the correction instruction attached with the SAP Note.</p>", "noteVersion": 3}, {"note": "2443859", "noteTitle": "2443859 - Release Information Note for the Fiori application Analyze Deferment 05/2017", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>This note provides additional information regarding the Fiori application Analyze Deferment in the area of Oil &amp; Gas Upstream Operations Management (UOM). This note contains the minimum required and recommended fixes that should be installed to make the app functional either in the frontend server or in the related backend server.</p>\n<p>With the Analyze Deferment app, a Deferment Analyst can view aggregated and node level deferment results. It displays the system capability, allocation values, and deferment values. You can reduce the unexplained deferment values within the tolerance limit by viewing the unexplained and explained deferment values separately and creating deferment events to minimize the unexplained deferment result. The app provides tabular and chart views of deferment results, which you can display by quantity or by percentage.</p>\n<p>Key Features:</p>\n<ul>\n<li>View aggregated deferment results</li>\n<li>View node level deferment results</li>\n<li>Create or edit deferment events</li>\n</ul>\n<p>This note is subject to change. Ensure you check it on a regular basis.</p>\n<p>New or signficant enhancements of delivered Fiori applications for area  Analyze Deferment are delivered with Stack FPS2  of productversion S/4HANA OP 1610</p>\n<p>Following FIORI apps are affected:</p>\n<p><strong>Fiori application         Status</strong></p>\n<p>Analyze Deferment     New; OData service UPS_DEF_RESULT has to be registered</p>\n<p>Gateway Role               SAP_BR_DEFERMENT_ANALYST_IOG has to be added</p>\n<p>Additional information about the FIORI application can be found in the <a href=\"https://fioriappslibrary.hana.ondemand.com/sap/fix/externalViewer/\" target=\"_blank\">SAP Fiori apps reference library</a></p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Fiori, Design, UI, APP, UPS_DEFER_RESS1, SAP Oil &amp; Gas, UOM, Upstream Operations Management, Analyze Deferment</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<div class=\"longtext\">\n<p>Please check the release information note 2346431 for product version 'S/4HANA OP 1610' and 2356208  for product version 'Fiori for S/4HANA OP 1610' for general installation and infrastructure topics.</p>\n</div>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Apply the Stack FPS2  of productversion S/4HANA OP 1610 on your Frontend Server system</p>\n<p>Apply the Stack FPS2  of productversion S/4HANA OP 1610 on the assigned  Backend systems</p>", "noteVersion": 2}, {"note": "2443871", "noteTitle": "2443871 - Release Information Note for the Fiori application Capture Field Data 05/2017", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>This note provides additional information regarding the Fiori application Capture Field Data in the area of Oil &amp; Gas Upstream Operations Management (UOM). This note contains the minimum required and recommended fixes that should be installed to make the app functional either in the frontend server or in the related backend.</p>\n<p>With the transactional app Capture Field Data, a Field Data Operator can manually maintain measurements for various Objects in a Production Network for a particular time range. The measurements can vary from volumetric flow rates to pressure etc, from components of a gas to deferment events and so on. Based on the type of data captured, the app displays only the relevant data fields that are configured.</p>\n<p>Key Features:</p>\n<ul>\n<li>Maintain volumetric meter readings</li>\n<li>Maintain volumetric tank readings</li>\n<li>Maintain well and well completion test data</li>\n<li>Maintain well and well completion theoreticals</li>\n<li>Maintain component analysis for a gas medium</li>\n<li>Maintain zonal contribution factor</li>\n<li>Maintain well reading data (non-volumetric)</li>\n<li>Create or view deferment events for a network object</li>\n</ul>\n<p>This note is subject to change. Ensure that you check on a regular basis.</p>\n<p>New or signficant enhancements of delivered Fiori application for area Capture Field Data are delivered with Stack FPS2  of product version S/4HANA OP 1610</p>\n<p>Following Fiori apps are affected:</p>\n<p><strong>Fiori application          Status</strong></p>\n<p>Capture Field data          New; OData service UPS_FIELD_DATA_CAPTURE has to be registered</p>\n<p>Gateway Role                 SAP_BR_FIELD_OPERATOR_IOG has to be added</p>\n<p>Additional information about the Fiori application can be found in the <a href=\"https://fioriappslibrary.hana.ondemand.com/sap/fix/externalViewer/\" target=\"_blank\">SAP Fiori apps reference library</a></p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>FIORI, Design, UI, APP, UPS_FDCS1, SAP Oil &amp; Gas, UOM, Upstream Operations Management, Capture Field Data, Measurement, Well Test, Well Completion Test, Well Reading, Theoretical Override, Deferment Events, Component Analysis.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>Please check the release information note 2346431 for product version 'S/4HANA OP 1610' and 2356208  for product version 'Fiori for S/4HANA OP 1610' for general installation and infrastructure topics.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Apply the Stack FPS2  of product version S/4HANA OP 1610 on your Frontend Server system</p>\n<p>Apply the Stack FPS2  of product version S/4HANA OP 1610 on the assigned  Backend systems</p>", "noteVersion": 4}]}], "activities": [{"Activity": "Custom Code Adaption", "Phase": "Before or during conversion project", "Condition": "Conditional", "Additional_Information": "You have customer enhancements, modifications in the area of maintaining well test in Upstream Operations Management that are built on SAP ERP 6.0, which  consists of two tables (gho_well_prior and gho_well_std_h) for well test header information. Redundancies have been removed and a proxy CDS view has been set for gho_well_std_h, whereas the table do still exist in S/4HANA as DDIC definition as well as database object. Customer code reading data from gho_well_std_h will work as before as redirection via CDS view. However, if there is any custom code which writes only to table gho_well_std_h that should be adjusted to now write in gho_well_prior. Core Data Service(CDS) View NSDM_E_GHOWLSTDH on the top of table gho_well_prior has been created and set as a proxy object on table gho_well_std_h.  Please, see SAP Notes 2419408 and 2444372 for more details."}, {"Activity": "Custom Code Adaption", "Phase": "During conversion project", "Condition": "Mandatory", "Additional_Information": "Adjustments to change of Upstream Operations Management data model in SAP S/4HANA"}]}