{"guid": "6CAE8B3EA9EC1ED79F89CB391DE740CA", "sitemId": "SI11: Logistics_MM-IM", "sitemTitle": "S4TWL - Blocked customer or supplier in Inventory Management", "note": 2516223, "noteTitle": "2516223 - S4TWL - Blocked customer or supplier in Inventory Management", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are doing a system conversion to SAP S/4HANA. The following SAP S/4HANA Transition Worklist item is applicable in this case.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong>Description</strong></p>\n<p>The end of purpose check for customer and supplier master data has changed in Inventory Management. From SAP S/4HANA 1709 onwards the following requirements must be fulfilled for blocking customer or supplier master data:</p>\n<ul>\n<li>Material documents that are related to the customer or supplier to be blocked must be archived.</li>\n<li>Special stocks that are related to the customer or supplier to be blocked must be archived or deleted.</li>\n<li>Physical inventory documents that are related to the customer or supplier to be blocked must be archived.</li>\n<li>Open reservations that are related to the customer or supplier to be blocked must be deleted.</li>\n<li>The customer or supplier to be blocked must be removed from plant and storage location data.</li>\n<li>The condition tables used to control the sending of receipt request messages to the Ariba Network must not contain records related to the supplier to be blocked.</li>\n<li>Batch input test data that is related to the supplier to be blocked must be deleted (or changed).</li>\n<li>Short documents (as substitutes for archived material documents) that are related to the supplier to be blocked must be deleted.</li>\n<li>Correction data (contained in database table MMINKON_UP) that is related to the customer or supplier to be blocked must be deleted.</li>\n<li>Consistency check results of aged material documents that are related to the customer or supplier to be blocked must be deleted.</li>\n<li>In case of an upgrade from 1511/1610/1709 to 1709-FPS01 or higher, please read note 2629400</li>\n</ul>\n<p><strong>Required and Recommended Action(s)</strong></p>\n<p>Material documents, special stocks, physical inventory documents and open reservations that are related to blocked customer or supplier master data shall be archived or deleted before converting or upgrading to SAP S/4HANA 1709 (and above).</p>\n<p>Open reservations can be deleted (or flagged for deletion) using transactions MB22 and MBVR. Material documents, special stocks and physical inventory documents can be archived using archiving objects MM_MATBEL, MM_SPSTOCK and MM_INVBEL. Special stocks can be deleted using data destruction objects MM_STO_CONSI_DEST and MM_STO_SOBES_DEST.</p>\n<p>Furthermore, blocked customers and suppliers shall be removed from plant and storage location data:</p>\n<ul>\n<li>The assignment of a blocked customer to a plant can be removed via SAP Reference IMG / SAP Customizing Implementation Guide (transaction SPRO): Materials Management -&gt; Purchasing -&gt; Purchase Order -&gt; Set up Stock Transport Order -&gt; Define Shipping Data for Plants.</li>\n<li>The assignment of a blocked supplier to a plant can be removed via transaction XK02 -&gt; Purchasing data -&gt; Extras -&gt; Additional Purchasing Data.</li>\n<li>The assignment of a blocked customer or supplier to a storage location can be removed via SAP Reference IMG / SAP Customizing Implementation Guide (transaction SPRO): Materials Management -&gt; Purchasing -&gt; Purchase Order -&gt; Set up Stock Transport Order -&gt; Set Up Stock Transfer Between Storage Locations -&gt; Define Shipping Data for Stock Transfers Between Storage Locations.</li>\n</ul>\n<p>Condition tables can be changed using transaction NACE:</p>\n<ol>\n<li>Select application ME (Inventory Management) and enter “Condition records (F8)”</li>\n<li>Select Output Type ARIB (Ariba Rcpt Request)</li>\n<li>Select key combination (\"Supplier/CoCode\" or \"Supplier\")</li>\n<li>In the selection screen for the condition records enter the supplier for that you want to create or display conditions and press execute (F8).</li>\n<li>Enter the conditions (supplier, respectively company code; the other attributes can be ignored).</li>\n<li>Save the changes.</li>\n</ol>\n<p>Moreover, batch input test data, short documents, correction data and consistency check results shall be deleted:</p>\n<ul>\n<li>Batch input test data can be deleted (or changed) using maintenance view V_159A.</li>\n<li>Short documents (as substitutes for archived material documents) can be deleted using report RM07MAID.</li>\n<li>Correction data (contained in database table MMINKON_UP) can only be deleted manually.</li>\n<li>Consistency check results of aged material documents can be deleted using report NSDM_MATDOC_CHECK_AGED_DATA.</li>\n</ul>\n<p><strong>How to Determine Relevancy</strong></p>\n<p>With notes 2502552, 2524011, 2536568 and 2585925 SAP offers a Simplification Item Check to identify blocked customers and suppliers in Inventory Management. Alternatively, the following steps can be performed:</p>\n<ol>\n<li>Identify blocked customer master data in database table KNB1. The customer is blocked when the field CVP_XBLCK_B is set. Plants assigned to the company code of the customer master record can be identified via transaction OX18.</li>\n<li>Identify blocked supplier master data in database table LFB1. The supplier is blocked when the field CVP_XBLCK_B is set. Plants assigned to the company code of the supplier master record can be identified via transaction OX18.</li>\n<li>Identify material documents, special stocks, physical inventory documents, open reservations, plants and storage locations that are related to blocked customer or supplier master data in the following database tables: ISEG, MKOL, MSEG, MSKU, MSLB, MSSL, RESB, RKPF, T001W and T001L. For Discrete Industries and Mill Products (DIMP) the following database tables are relevant in addition: MCSD, MCSS, MSCD, MSCS, MSFD, MSFS, MSID, MSIS, MSRD and MSRS. Filter these database tables by the plant (field WERKS) and by the customer or supplier. The customer can be contained in the field KUNNR or VPTNR. The supplier can be contained in the field DISUB_OWNER, EMLIF, LIFNR, LLIEF or OWNER.</li>\n<li>Identify condition records used to control the sending of receipt request messages to the Ariba Network that are related to blocked supplier master data in the database tables B082 (\"Supplier/CoCode\") and B083 (\"Supplier\"). In both tables the supplier can be contained in the field ARRLIFNR.</li>\n<li>Identify batch input test data that is related to blocked supplier master data by filtering the database table T159A by the plant (field WERKS) and by the supplier (field LIFNR).</li>\n<li>Identify short documents that are related to blocked supplier master data by filtering the database table MARI by the plant (field WERKS) and by the supplier (field LIFNR).</li>\n<li>Identify correction data that is related to blocked customer or supplier master data by filtering the database table MMINKON_UP by the plant (field WERKS) and by the customer or supplier. The customer can be contained in the field KUNNR. The supplier can be contained in the field LIFNR or OWNER.</li>\n<li>Identify consistency check results that are related to blocked customer or supplier master data by filtering the database table NSDM_AGING_CHECK by the plant (field WERKS) and by the customer or supplier. The customer can be contained in the field KUNNR_SID. The supplier can be contained in the field LIFNR_SID or DISUB_OWNER_SID.</li>\n</ol>", "noteVersion": 9, "refer_note": [{"note": "2629400", "noteTitle": "2629400 - DPP: Archiving and Masking after migration", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>If a customer is migrating from 1511/1610/1709 to 1709-FPS01 or higher and in his system are Business Partners with EOP-Status and for the BP are entries in MATDOC/MATDOC_EXTRACT and MARI the BP fields in that tables have to be masked.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p id=\"\">Blocked Customer in MM-IM, Inventory management, System conversion, Migration, blocked supplier</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p id=\"\"> Upgrade from 1511/1610/1709 and blocked customers</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>There are some elements shown which can't be delivered by note and should be created manually. But this can be ignored. The Objects are:</p>\n<p>AVAS 42F2E9AFBE7F1EE891F5D49DDA4F5F0D --&gt; Classification if accessible or not. Is not absolute needed.</p>\n<p>CUAD NSDM_MATDOC_DPP_MASK --&gt; Control of a Dynpro. This report doesn't have a Dynpro</p>\n<p>REPT CL_MMIM_MASK_MATDOC_DATA --&gt; Report text for class. For the class no report texts exists.</p>\n<p>REPT NSDM_MATDOC_DPP_MASK --&gt; Report text for Report. This Report doesn't have text elements</p>\n<p>DOCU RENSDM_MATDOC_DPP_MASK --&gt; Documentation for the report. There is no docu for this report.</p>\n<p>Program flow of this functionallity (Remark: all selections are system-wide over all clients):</p>\n<p>With transaction SE38 you can start the masking with program NSDM_MATDOC_DPP_MASK. The program collects all company codes and collects for every company code blocked customers and blocked suppliers in a partner table. If there are blocked customers/suppliers in a company code, Method Process in class CL_MMIM_MASK_MATDOC_DATA is called with the partner table as API. Method process only calls function Module MMIM_MASK_MATDOC_ARCH in NEW TASK, so that every company code is running in an own task. The function Module calls the updates of BP masking in tables MATDOC, MATDOC_EXTRACT and MARI.</p>", "noteVersion": 1, "refer_note": [{"note": "2516223", "noteTitle": "2516223 - S4TWL - Blocked customer or supplier in Inventory Management", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are doing a system conversion to SAP S/4HANA. The following SAP S/4HANA Transition Worklist item is applicable in this case.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong>Description</strong></p>\n<p>The end of purpose check for customer and supplier master data has changed in Inventory Management. From SAP S/4HANA 1709 onwards the following requirements must be fulfilled for blocking customer or supplier master data:</p>\n<ul>\n<li>Material documents that are related to the customer or supplier to be blocked must be archived.</li>\n<li>Special stocks that are related to the customer or supplier to be blocked must be archived or deleted.</li>\n<li>Physical inventory documents that are related to the customer or supplier to be blocked must be archived.</li>\n<li>Open reservations that are related to the customer or supplier to be blocked must be deleted.</li>\n<li>The customer or supplier to be blocked must be removed from plant and storage location data.</li>\n<li>The condition tables used to control the sending of receipt request messages to the Ariba Network must not contain records related to the supplier to be blocked.</li>\n<li>Batch input test data that is related to the supplier to be blocked must be deleted (or changed).</li>\n<li>Short documents (as substitutes for archived material documents) that are related to the supplier to be blocked must be deleted.</li>\n<li>Correction data (contained in database table MMINKON_UP) that is related to the customer or supplier to be blocked must be deleted.</li>\n<li>Consistency check results of aged material documents that are related to the customer or supplier to be blocked must be deleted.</li>\n<li>In case of an upgrade from 1511/1610/1709 to 1709-FPS01 or higher, please read note 2629400</li>\n</ul>\n<p><strong>Required and Recommended Action(s)</strong></p>\n<p>Material documents, special stocks, physical inventory documents and open reservations that are related to blocked customer or supplier master data shall be archived or deleted before converting or upgrading to SAP S/4HANA 1709 (and above).</p>\n<p>Open reservations can be deleted (or flagged for deletion) using transactions MB22 and MBVR. Material documents, special stocks and physical inventory documents can be archived using archiving objects MM_MATBEL, MM_SPSTOCK and MM_INVBEL. Special stocks can be deleted using data destruction objects MM_STO_CONSI_DEST and MM_STO_SOBES_DEST.</p>\n<p>Furthermore, blocked customers and suppliers shall be removed from plant and storage location data:</p>\n<ul>\n<li>The assignment of a blocked customer to a plant can be removed via SAP Reference IMG / SAP Customizing Implementation Guide (transaction SPRO): Materials Management -&gt; Purchasing -&gt; Purchase Order -&gt; Set up Stock Transport Order -&gt; Define Shipping Data for Plants.</li>\n<li>The assignment of a blocked supplier to a plant can be removed via transaction XK02 -&gt; Purchasing data -&gt; Extras -&gt; Additional Purchasing Data.</li>\n<li>The assignment of a blocked customer or supplier to a storage location can be removed via SAP Reference IMG / SAP Customizing Implementation Guide (transaction SPRO): Materials Management -&gt; Purchasing -&gt; Purchase Order -&gt; Set up Stock Transport Order -&gt; Set Up Stock Transfer Between Storage Locations -&gt; Define Shipping Data for Stock Transfers Between Storage Locations.</li>\n</ul>\n<p>Condition tables can be changed using transaction NACE:</p>\n<ol>\n<li>Select application ME (Inventory Management) and enter “Condition records (F8)”</li>\n<li>Select Output Type ARIB (Ariba Rcpt Request)</li>\n<li>Select key combination (\"Supplier/CoCode\" or \"Supplier\")</li>\n<li>In the selection screen for the condition records enter the supplier for that you want to create or display conditions and press execute (F8).</li>\n<li>Enter the conditions (supplier, respectively company code; the other attributes can be ignored).</li>\n<li>Save the changes.</li>\n</ol>\n<p>Moreover, batch input test data, short documents, correction data and consistency check results shall be deleted:</p>\n<ul>\n<li>Batch input test data can be deleted (or changed) using maintenance view V_159A.</li>\n<li>Short documents (as substitutes for archived material documents) can be deleted using report RM07MAID.</li>\n<li>Correction data (contained in database table MMINKON_UP) can only be deleted manually.</li>\n<li>Consistency check results of aged material documents can be deleted using report NSDM_MATDOC_CHECK_AGED_DATA.</li>\n</ul>\n<p><strong>How to Determine Relevancy</strong></p>\n<p>With notes 2502552, 2524011, 2536568 and 2585925 SAP offers a Simplification Item Check to identify blocked customers and suppliers in Inventory Management. Alternatively, the following steps can be performed:</p>\n<ol>\n<li>Identify blocked customer master data in database table KNB1. The customer is blocked when the field CVP_XBLCK_B is set. Plants assigned to the company code of the customer master record can be identified via transaction OX18.</li>\n<li>Identify blocked supplier master data in database table LFB1. The supplier is blocked when the field CVP_XBLCK_B is set. Plants assigned to the company code of the supplier master record can be identified via transaction OX18.</li>\n<li>Identify material documents, special stocks, physical inventory documents, open reservations, plants and storage locations that are related to blocked customer or supplier master data in the following database tables: ISEG, MKOL, MSEG, MSKU, MSLB, MSSL, RESB, RKPF, T001W and T001L. For Discrete Industries and Mill Products (DIMP) the following database tables are relevant in addition: MCSD, MCSS, MSCD, MSCS, MSFD, MSFS, MSID, MSIS, MSRD and MSRS. Filter these database tables by the plant (field WERKS) and by the customer or supplier. The customer can be contained in the field KUNNR or VPTNR. The supplier can be contained in the field DISUB_OWNER, EMLIF, LIFNR, LLIEF or OWNER.</li>\n<li>Identify condition records used to control the sending of receipt request messages to the Ariba Network that are related to blocked supplier master data in the database tables B082 (\"Supplier/CoCode\") and B083 (\"Supplier\"). In both tables the supplier can be contained in the field ARRLIFNR.</li>\n<li>Identify batch input test data that is related to blocked supplier master data by filtering the database table T159A by the plant (field WERKS) and by the supplier (field LIFNR).</li>\n<li>Identify short documents that are related to blocked supplier master data by filtering the database table MARI by the plant (field WERKS) and by the supplier (field LIFNR).</li>\n<li>Identify correction data that is related to blocked customer or supplier master data by filtering the database table MMINKON_UP by the plant (field WERKS) and by the customer or supplier. The customer can be contained in the field KUNNR. The supplier can be contained in the field LIFNR or OWNER.</li>\n<li>Identify consistency check results that are related to blocked customer or supplier master data by filtering the database table NSDM_AGING_CHECK by the plant (field WERKS) and by the customer or supplier. The customer can be contained in the field KUNNR_SID. The supplier can be contained in the field LIFNR_SID or DISUB_OWNER_SID.</li>\n</ol>", "noteVersion": 9}]}, {"note": "2007926", "noteTitle": "2007926 - Simplified Blocking and Deletion of Customer / Vendor Master Data", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>The SAP Business Partner (LO-MD-BP) component manages business partner of the types customer, vendor or contact person that are relevant in business processes.</p>\n<p>SAP software supports the simplified blocking and deletion of personal data. The SAP Note <a href=\"/notes/1825544\" target=\"_blank\">1825544 </a>describes how the use of SAP Information Lifecycle Management (ILM) supports the simplified blocking and deletion of personal data in SAP Business Suite.</p>\n<p>Transactional data related to a customer or vendor might exist in several applications. Some data might be related to closed business, other data to open or new business. As long as business activities are in progress, most data will be neither blocked nor deleted. As soon as business activities related to the customer or vendor are completed, the data has to be blocked. After the data reaches the longest retention period, the data has to be deleted.</p>\n<p>The customer or vendor needs to be blocked in the leading system and the connected systems after considering the application`s consumption of the customer or vendor. The relevant applications, including the customer or vendor master application itself needs to provide permission for blocking the customer or vendor based on the residence rule calculation defined in the ILM framework. After the customer or vendor is blocked, business users must no longer be able to display or process this data. Only authorized users such as an auditor should view the customer or vendor.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Data Privacy, Information Lifecycle Management, ILM, Deletion, Blocking, Personal Data, Business Partner, Customer, Vendor, FI_ACCRECV, FI_ACCPAYB, FI_ACCKNVK, EOP</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>See SAP Note <a href=\"/notes/1825544\" target=\"_blank\">1825544</a></p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>The solution is available as of SAP_APPL 617, support package 5 and contains of the following main building blocks:</p>\n<ul>\n<li>\n<div>The following database tables have been enhanced with a new field as a blocking indicator:</div>\n</li>\n</ul>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"3\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td><strong>Database Table</strong></td>\n<td><strong>Name of Blocking Indicator</strong></td>\n</tr>\n<tr>\n<td>KNA1</td>\n<td>CVP_XBLCK</td>\n</tr>\n<tr>\n<td>KNB1</td>\n<td>CVP_XBLCK_B</td>\n</tr>\n<tr>\n<td>LFA1</td>\n<td>CVP_XBLCK</td>\n</tr>\n<tr>\n<td>LFB1</td>\n<td>CVP_XBLCK_B</td>\n</tr>\n<tr>\n<td>KNVK</td>\n<td>CVP_XBLCK_K</td>\n</tr>\n</tbody>\n</table></div>\n<ul>\n<li>\n<p>Adapting the existing customer and vendor functionalities according to the new fields.</p>\n</li>\n<li>\n<p>Database table CVP_SORT has been created for storing the retention periods for a customer, vendor or contact person from all using applications.</p>\n</li>\n<li>\n<p>Report CVP_PREPARE_EOP (transaction CVP_PRE_EOP) has been created for blocking customer and vendor data.</p>\n</li>\n<li>\n<p>Report BUPA_REQ_UNBLOCK (transaction BUP_REQ_UNBLK) has been created for the creation of an unblocking request for a customer or vendor.</p>\n</li>\n<li>\n<p>Report CVP_UNBLOCK_MD (transaction CVP_UNBLOCK_MD) has been created for unblocking customer and vendor data.</p>\n</li>\n<li>\n<p>Report CVP_DISPLAY_LOG (transaction CVP_DISPLAY_LOG) has been created to read the stored application logs.</p>\n</li>\n<li>\n<p>Replication of blocking / unblocking information (blocking indicators, table CVP_SORT) via data exchange (ALE or Enterprise Services)</p>\n</li>\n<li>The authorization object B_BUP_PCPT is available to maintain the authorization for the following activity types. You can copy the PFCG role SAP_CA_BP_DP_ADMIN as template to assign the corresponding authorization to specific users.</li>\n<ul>\n<li>05 Lock (execute the blocking/end of purpose check for a customer or vendor)</li>\n<li>95 Unlock (execute the unblocking check for a customer or vendor)</li>\n</ul>\n<li>The following authorization objects are used to maintain the authorization for the activity type 03 (Display) of blocked data for a customer or vendor. <br/>You need to define in the Customizing activity <em>“Define Authorization Group Indicating Blocked Master Data”</em> an authorization group, which is assigned to blocked customer and vendor master data. The display of blocked data is afterwards only possible for users to which the display authority for the this customized authorization group is assigned.</li>\n<ul>\n<li>\n<p>F_KNA1_BED - Customer: Account Authorization</p>\n</li>\n<li>\n<p>F_LFA1_BEK - Vendor: Account Authorization</p>\n</li>\n<li>\n<p>F_BKPF_BED - Accounting Document: Account Authorization for Customers</p>\n</li>\n<li>\n<p>F_BKPF_BEK - Accounting Document: Account Authorization for Vendors</p>\n</li>\n<li>\n<p>V_KNA1_BRG - Customer: Account Authorization for Sales Areas</p>\n</li>\n<li>\n<p>F_KNKK_BED - Credit Management: Account Authorization</p>\n</li>\n<li>\n<p>B_BUP_PCPT (to read to address data)</p>\n</li>\n</ul>\n<li>The ILM objects FI_ACCRECV (Customer) and FI_ACCPAYB (Vendor) were enhanced and the ILM object FI_ACCKNVK (Contact Person) was created to enable:</li>\n<ul>\n<li>\n<p>Residence rules in relation to consuming applications to control blocking of the customer, vendor or contact person can be maintained</p>\n</li>\n<li>\n<p>Deletion of a customer, vendor or contact person considering retention rules in relation to consuming application is possible.</p>\n</li>\n</ul>\n<li>Code adaptions have been carried out to prevent unauthorized viewing of a blocked customer, vendor or contact person and to prevent changes being made to a blocked customer, vendor or contact person.</li>\n</ul>\n<p>The ILM enabled methodology of blocking and deleting a customer or vendor is controlled by the SAP Business Function ERP_CVP_ILM_1.</p>\n<p>Prior SAP Note <a href=\"/notes/2075111\" target=\"_blank\">2075111</a> or corresponding support package, SAP Business Function BUPA_ILM_BF has to be active for the blocking of related address data of a customer or vendor. After SAP Note <a href=\"/notes/2075111\" target=\"_blank\">2075111</a> or corresponding support package, SAP Business Function ERP_CVP_ILM_1 can be activated alone.</p>\n<p><strong>To prevent any data loss, only activate the business function ERP_CVP_ILM_1 if all applications that use customer or vendor data support the deletion of customer or vendor data using SAP Information Lifecycle Management. </strong>Refer to the attached notes being implemented either manually, either via the corresponding support package or release.</p>\n<p>Define the settings for blocking in Customizing under <em>Logistics - General </em><em>-&gt;</em><em> Business Partner </em><em>-&gt;</em><em> Deletion of Customer and Vendor Master Data</em>.<strong><br/></strong></p>\n<p>For more information about using SAP Information Lifecycle Management (ILM) to control the blocking and deletion of business partner data, see SAP Library for SAP ERP on SAP Help Portal at <a href=\"http://help.sap.com/erp\" target=\"_blank\">http://help.sap.com/erp</a>. Choose a release and then<em> Application Help. </em>In SAP Library, choose<em> SAP ERP Cross-Application Functions</em> -&gt; <em>Cross-Application Components -&gt; Data Protection -&gt; Deletion of Customer Master Data respectively Deletion of Vendor Master Data</em>. Alternatively you can use the following link: <a href=\"http://help.sap.com/erp2005_ehp_07/helpdata/en/f0/b55853b2dc7425e10000000a44176d/frameset.htm\" target=\"_blank\">http://help.sap.com/erp2005_ehp_07/helpdata/en/f0/b55853b2dc7425e10000000a44176d/frameset.htm</a></p>\n<p><strong>Current Scope for the Simplified Blocking and Deletion:</strong></p>\n<p>Only applications named in referenced Notes are enabled for the simplified blocking and deletion. This is valid for SAP delivered functionality, any custom own enhancement must get checked properly.  This includes also Partner Products listed on SAPs price list. In case of questions get in contact with SAP Partner Management. All other third party software processing personal data from the SAP Business Suite would have to be enabled individually either from the third party vendor or from the customer. The guide attached to note <a href=\"/notes/2103639\" target=\"_blank\">2103639 </a>contains the necessary information how to do this.</p>\n<p><em>                          </em></p>", "noteVersion": 5, "refer_note": [{"note": "2018854", "noteTitle": "2018854 - Check management: Locking customer and vendor", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>Check management contains data with relation to a customer or vendor. This SAP Note describes the changes in check management that enable the simplified deletion and locking of customer master data or vendor master data based on SAP Information Lifecycle Management (ILM). SAP Note 2007926 contains the description for this.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Includes, data protection, ILM, SAP Information Lifecycle Management, delete, deletion, lock, personal data, person-related data, customer, vendor, FI_ACCPAYB, FI_ACCRECV, EoP, end of purpose, FS679, FS680, FS681, FS682</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>See SAP Note 2007926.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>The new functions for check management are available as of: <br/>SAP_FIN, Release 617, Support Package 5<br/><br/>Check management offers an end of purpose check for the customer or vendor. This check consists of the following main characteristics:</p>\n<p>If a customer or vendor is locked, the application does not display any personal data of this customer or vendor including the customer number or vendor number. The system informs you about this via corresponding messages.</p>\n<p>Maintain the following ILM Customizing (transactions ILMARA and ILMPOL):<br/>Policy Category: Residence Rules<br/>Object Category: SAP Business Suite<br/>Audit Area: BUPA_DP<br/>ILM object: FI_SCHECK</p>\n<p>The simplified deletion is implemented via the archiving object FI_SCHECK with the corresponding ILM connection (ILM object FI_SCHECK).</p>", "noteVersion": 1}, {"note": "2018575", "noteTitle": "2018575 - Deletion and locking of customers and vendors in FI", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>FI <em></em>contains data with relation to a customer or vendor. This SAP Note describes the changes in the FI system that enable the simplified deletion and locking of customer master data or vendor master data based on SAP Information Lifecycle Management (ILM). SAP Note 2007926 contains the description for this.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Data protection, ILM, SAP Information Lifecycle Management, delete, deletion, lock, personal data, person-related data, customer, vendor, FI_ACCPAYB, FI_ACCRECV, FI_ACCKNVK, EoP, end of purpose, customer, vendor, one-time</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>See SAP Note 2007926.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<div class=\"longtext\">\n<p>The new functions for the FI system are available as of ERP 6 EHP 7 Support Package 05.</p>\n<p>The FI system offers an end of purpose check for the customer or vendor. This check consists of the following main characteristics:</p>\n<ol>\n<li>In the standard system, the following application names are provided for the FI system:<br/><div class=\"table-responsive\"><table border=\"1\" cellpadding=\"0\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td>\n<p>ID type</p>\n</td>\n<td>\n<p>Application name</p>\n</td>\n<td>\n<p>Application description</p>\n</td>\n</tr>\n<tr>\n<td>\n<p>1 - customer master data</p>\n</td>\n<td>\n<p>ERP_FI</p>\n</td>\n<td>\n<p>Master data of the customer</p>\n</td>\n</tr>\n<tr>\n<td>\n<p>2 - vendor master data</p>\n</td>\n<td>\n<p>ERP_FI</p>\n</td>\n<td>\n<p>Master data of the vendor</p>\n</td>\n</tr>\n</tbody>\n</table></div>\n</li>\n<ol>\n<li></li>\n</ol>\n<li>The application name ERP_FI supports the use of application rule variants. This allows you to enter and change residence times and retention periods for the ILM object FI_ACCPAYB or FI_ACCRECV according to the objects that belong to the application ERP_FI. <br/><strong><em><br/></em></strong>To assign application-specific condition fields to the application rule variants, assign application rule variants to the ILM rule groups. You can use transaction IRM_CUST_CSS to define ILM rule groups for ILM objects of the FI system. In addition, you can use transaction IRMPOL to assign residence rules that are specified for these ILM objects to application-specific condition fields. The following ILM objects are relevant for the application ERP_FI:</li>\n<ul>\n<li><em>FI_ACCRECV</em></li>\n<li><em>FI_ACCPAYB</em></li>\n<li><em>FI_DOCUMNT</em></li>\n</ul>\n<ol>\n<li><em></em></li>\n</ol>\n<li>The application ERP_FI provides the following classes that are registered for the check of the purpose of the customer master and the vendor master.<br/><div class=\"table-responsive\"><table border=\"1\" cellpadding=\"0\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td>\n<p>ID type</p>\n</td>\n<td>\n<p>Application name</p>\n</td>\n<td>\n<p>Class registered for end of purpose checks</p>\n</td>\n<td>\n<p>General</p>\n</td>\n<td>\n<p>Company code</p>\n</td>\n</tr>\n<tr>\n<td>\n<p>1 - customer master data</p>\n</td>\n<td>\n<p>ERP_FI</p>\n</td>\n<td>\n<p>CL_FIN_EOP_CHECK_CUST</p>\n</td>\n<td>\n<p> </p>\n</td>\n<td>\n<p>X</p>\n</td>\n</tr>\n<tr>\n<td>\n<p>2 - vendor master data</p>\n</td>\n<td>\n<p>ERP_FI</p>\n</td>\n<td>\n<p>CL_FIN_EOP_CHECK_VEND</p>\n</td>\n<td>\n<p> </p>\n</td>\n<td>\n<p>X</p>\n</td>\n</tr>\n</tbody>\n</table></div>\n<ul>\n<li>Class CL_FIN_EOP_CHECK_CUST<em> - </em>EOP check for customers in FI<br/>The following points are checked in the system:</li>\n<ul>\n<li>Does an open item of the customer exist?</li>\n<li>Does at least one parked document or fully saved document exist for this customer?</li>\n<li>Does a recurring entry original document exist that uses the customer?</li>\n<li>Is a link to a vendor maintained in the master record of the customer, and is the clearing between the customer and vendor active?</li>\n<li>Is the customer used as an alternative payer and does the customer for which the checked customer is the alternative payer still have open items?</li>\n<li>Does at least one saved document for this customer exist in the cash journal?</li>\n</ul>\n</ul>\n<blockquote dir=\"ltr\">\n<p>If one of the points mentioned applies, the system does not lock the customer. If these points do not apply, the system determines the last payment that was made for this customer and uses it as a reference date for the retention rules that are defined in ILM Customizing.</p>\n<p>For one-time customers, the system determines all documents of the customer and groups them into individual posting processes. If a process is fully completed, the system copies the authorization group of the company code that is defined in ILM Customizing to all documents of this process.</p>\n</blockquote>\n</li>\n<ul>\n<li>Class CL_FIN_EOP_CHECK_VEND<em> -</em> EOP check for vendor in FI<br/>The check logic is the same as for the class CL_FIN_EOP_CHECK_CUST.</li>\n<ul>\n<li><em></em></li>\n</ul>\n</ul>\n<li>If a customer, vendor, or contact person is locked, the application does not display any personal data of this customer, vendor, or contact person including the customer number, vendor number, or contact person number. You cannot display or change any data. You can also not create any new business with this customer, vendor or contact person.</li>\n</ol></div>", "noteVersion": 2}, {"note": "2012217", "noteTitle": "2012217 - Retail Store related where-used-check before blocking of customer data", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>The application Retail Store or In Store Merchendise and Inventory Management application contains data, which is related to the customer. This note describes the changes, which will prevent blocking of a customer, if this customer is still used by the application.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Deletion, Blocking, Personal Data, EOP, WUC, Customer</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>See note 2007926</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>The new functionality for application Retail Store or In Store MIM will be available with release EA-RETAIL 617 and support package SP05.</p>\n<p>Retail Store or In Store MIM application provides a where-used check for customer data.<br/><br/>The functionality consists of the following main features:</p>\n<ol>\n<li>In customizing \"Customer Master /Vendor Master Deletion\" the following entries are added:<ol>\n<li>In the activity \"Define and Store Application Names for EoP Check\" the following entry was added:<br/>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"3\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td>ID Type</td>\n<td>Application Name</td>\n<td>Application Description</td>\n</tr>\n<tr>\n<td>1 - Customer Master Data</td>\n<td>SRS</td>\n<td>SAP Retail Store</td>\n</tr>\n</tbody>\n</table></div>\n</li>\n</ol><ol start=\"2\">\n<li>In the activity \"Define Application Classes Registered for EoP Check\" the following entry was added:<br/>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"3\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td>ID Type</td>\n<td>Application Name</td>\n<td>Registered Class for EoP Checks</td>\n<td>General</td>\n<td>Copm. Code</td>\n</tr>\n<tr>\n<td>1- Customer Master Data</td>\n<td>SRS</td>\n<td>CL_WSRS_TOOLS_CV_EOP_CHECK</td>\n<td>X</td>\n<td>X</td>\n</tr>\n</tbody>\n</table></div>\n</li>\n</ol></li>\n<li>\n<p>The application Retail Store or In Store MIM delivers the following class registered for the end of purpose check of the customer:<br/> CL_WSRS_TOOLS_CV_EOP_CHECK- \"Retail Store EOP Check for CV\"</p>\n</li>\n</ol>", "noteVersion": 3}, {"note": "2014522", "noteTitle": "2014522 - DIMP industry related where used checks before blocking of customer master", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>The DIMP solution uses the customer master in various industry applications . This note describes the changes which will prevent the blocking of customer data if it is still used by the DIMP industry applications.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Deletion, Blocking, Personal Data, EOP, Customer, KUNNR, Recipient , DIMP</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>Reason : See note 2007926</p>\n<p>Pre-requisite : Switch BC sets DIMP_CVP_EOPAPP_V , DIMP_CVP_EOPCLASS_VC has to be activated .</p>\n<p> </p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>The new functionality for application customer master relevant to the DIMP industry Where-used-check is available with release ECC-DIMP 617 and support package SP05.</p>", "noteVersion": 1}, {"note": "2028117", "noteTitle": "2028117 - Incentives and commissions management (ICM) related where-used-check before blocking of customer and vendor data", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>The application Incentives and Commissions Management (ICM) can contain data, which is related to a customer or vendor. This note describes the changes, which will prevent blocking of a customer / vendor, if this customer / vendor is still assigned as payee to the comission contract.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Deletion, Blocking, Personal Data, EOP, Customer, Vendor, Payee, Commission contract, KUNNR, LIFNR,  CACS_STMTYPE_ID,  CACS_STMRU,  TRIGID,  PAYEE_FI</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>WUC for customer/vendor master in ICM</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>The new functionality for application Incentives and Commissions Mangagement (ICM) will be available with release EA_APPL 617 and support package SP05.<br/><br/>The functionality consists of the following main features:</p>\n<ol>\n<li>In customizing \"Customer Master /Vendor Master Deletion\" the following entries are added:<ol>\n<li>In the activity \"Define and Store Application Names for EoP Check\" the following entry was added:<br/>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"3\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td>ID Type</td>\n<td>Application Name</td>\n<td>Application Description</td>\n</tr>\n<tr>\n<td>1 - Customer Master Data</td>\n<td>CACS_CUSTVEND</td>\n<td>ICM application for customer/Vendor</td>\n</tr>\n<tr>\n<td>2 - Vendor Master Data</td>\n<td>CACS_CUSTVEND</td>\n<td>ICM application for customer/Vendor</td>\n</tr>\n</tbody>\n</table></div>\n</li>\n<li>In the activity \"Define Application Classes Registered for EoP Check\" the following entry was added:<br/>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"3\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td>ID Type</td>\n<td>Application Name</td>\n<td>Registered Class for EoP Checks</td>\n<td>General</td>\n<td>Comp.Code</td>\n</tr>\n<tr>\n<td>1 - Customer Master Data</td>\n<td>CACS_CUSTVEND</td>\n<td>Cl_CACS_WUC_CHECK_CUST</td>\n<td>'X'</td>\n<td>'X'</td>\n</tr>\n<tr>\n<td>2 - Vendor Master Data</td>\n<td>CACS_CUSTVEND</td>\n<td>Cl_CACS_WUC_CHECK_VEND</td>\n<td>'X'</td>\n<td>'X'</td>\n</tr>\n</tbody>\n</table></div>\n</li>\n</ol></li>\n<li>\n<p>The application Incentives and Commissions Mangagement (ICM) delivers the following class registered for the WHERE USED check of the customer and the vendor:<br/> Cl_CACS_WUC_CHECK_CUST - \"Where used check for customer in ICM\"</p>\n</li>\n</ol>\n<p>         CL_CACS_WUC_CHECK_VEND - \"WUC check for vendor in ICM\"</p>", "noteVersion": 1}, {"note": "2021665", "noteTitle": "2021665 - FSCM Collection Management related where-used-check before blocking Business Partner and Contact Person.", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>The application FSCM Collection Management contains data, which is related to central Business Partner (cBP) or the customer contact person. This note describes the changes in FSCM Collection Managementthat enable the simplified blocking and deletion of the central business partner or customer contact person based on the use of SAP Information Lifecycle Management (ILM) as described in SAP Note 1825608.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Data Privacy, Information Lifecycle Management, ILM, Deletion, Blocking, Personal Data, Business Partner, cBP, CA_BUPA, EoP, PARNR, contact person</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>See SAP Note 1825608</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>The new functionality for application FSCM Collection Management will be available with release SAP_APPL 617 and support package SP05. FSCM Collection Management provides a where-used check for the central business partner and customer contact person.<br/><br/>The functionality consists of the following main features:</p>\n<ol>\n<li>In customizing \"Customer Master /Vendor Master Deletion\" the following entries are added:<ol>\n<li>In the activity \"Define and Store Application Names for EoP Check\" the following entry was added:<br/>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"3\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td>ID Type</td>\n<td>Application Name</td>\n<td>Application Description</td>\n</tr>\n<tr>\n<td>3 - Contact Person </td>\n<td>FI-CM</td>\n<td>SAP Collection Management</td>\n</tr>\n</tbody>\n</table></div>\n</li>\n<li>In the activity \"Define Application Classes Registered for EoP Check\" the following entry was added:<br/>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"3\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td>ID Type</td>\n<td>Application Name</td>\n<td>Registered Class for EoP Checks</td>\n<td>General</td>\n<td>Comp.Code</td>\n</tr>\n<tr>\n<td>3 - Contact Person </td>\n<td>FI-CM</td>\n<td>CL_FDM_WUC_CP_EOP_CHECK</td>\n<td>'X'</td>\n<td>'X'</td>\n</tr>\n</tbody>\n</table></div>\n</li>\n</ol></li>\n<li>\n<p>The application FSCM Collection Management delivers the following class registered for the end of purpose check of the Contact Person:<br/> CL_FDM_WUC_CP_EOP_CHECK - \"Where-Used-Check Contact Person\"</p>\n</li>\n<li>In customizing \"Blocking and Unblocking of Data\" the following entries are added:<ol>\n<li>In the activity \"Define and Store Application Names for EoP Check\" the following entry was added:<br/>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"3\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td>Appl Name</td>\n<td>Item</td>\n<td>Application Description</td>\n</tr>\n<tr>\n<td>FI-CM</td>\n<td>1000000</td>\n<td>SAP Collection Management</td>\n</tr>\n</tbody>\n</table></div>\n</li>\n<li>In the activity \"Define Application Function Modules Registered for EoP Check\" the following entry was added:<br/>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"3\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td>Appl Name</td>\n<td>item</td>\n<td>Function Module</td>\n</tr>\n<tr>\n<td>FI-CM</td>\n<td>1000000</td>\n<td>UDM_WUC_COL_EOP_CHECK</td>\n</tr>\n</tbody>\n</table></div>\n</li>\n</ol></li>\n<li>\n<p>The application FSCM Collection Management delivers the following funtion module registered for the end of purpose check of the Business Partner:<br/> UDM_WUC_COL_EOP_CHECK- \"Where-Used-Check Business Partner\". This module is used to set the end of purpose flag based on the business partner entry in the table UDM_COLL_ITEM.</p>\n</li>\n<li>\n<p>When you run the EoP for a contact person the application FI-CM has a \"where use check\" for the following tables:</p>\n</li>\n<ul>\n<li>UDM_CCT_ATTR (Customer Contact)</li>\n<li>UDMCASEATTR00 (UDM_CASE FSCM Dispute Management - Case / Dispute Case Attributes)</li>\n<li>UDM_P2P_ATTR (Promise to Pay)</li>\n</ul>\n</ol>\n<p>        To be able to block the contact person you need to configure the destruction process of the ILM object SCMG. While the object handles the deletion of UDMCASEATTR00 and UDM_P2P_ATTR for UDM_CCT_ATTR you need ro            run the deletion reports UDM_REORG_WORKLIST followed by  UDM_DELETE_CONTACT_DETAILS.</p>", "noteVersion": 2}, {"note": "2012520", "noteTitle": "2012520 - FI line item reporting: Locking customers and vendors", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>The FI system contains documents that have a relation to a customer or vendor (that is, customer documents or vendor documents).</p>\n<p>This SAP Note describes the changes that enable the simplified deletion and locking of customer master data or vendor master data based on SAP Information Lifecycle Management (ILM). SAP Note 2007926 contains the description for this.</p>\n<p>This SAP Note is also provided as a technical prerequisite for other corrections. It does not deliver any function.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Data protection, ILM, SAP Information Lifecycle Management, delete, deletion, lock, personal data, person-related data, customer, vendor, FI_ACCPAYB, FI_ACCRECV, FI_ACCKNVK, EoP, end of purpose, RFITEMGL, RFITEMAP, RFITEMAR, SAPDBSDF, SAPDBDDF, SAPDBKDF, SAPDBBRF</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>See SAP Note 2007926.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>The new functions are available as of: <br/>SAP_FIN, Release 617, Support Package 5<br/><br/>The FI system offers an end of purpose check for the customer or vendor. This check consists of the following main characteristics:</p>\n<p>If a customer or vendor is locked, the application does not display any personal data of this customer or vendor including the customer number or vendor number. You cannot display or change any data. This also applies to one-time documents.</p>", "noteVersion": 4}, {"note": "2030203", "noteTitle": "2030203 - IS OIL related where-used-check Changes", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>The application IS OIL can contain data, which is related to a customer or vendor. This note describes the changes, which will prevent blocking of a customer, if this customer is still in use. In addition these changes will prevent blocking of a vendor, if this vendor is still in use.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Deletion, Blocking, Personal Data, EOP, Customer, Vendor, KUNNR</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>See note 2007926</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>The new functionality for application IS OIL will be available with release SAP_APPL 617 and support package SP05.</p>\n<p>IS-OIL application provides a where used check for the customer or vendor.</p>\n<p>The check functionality consists of the following main features:</p>\n<ol>\n<li>In customizing \"Customer Master /Vendor Master Deletion\" the following entries are added:</li>\n<ol>\n<li>In the activity \"Define and Store Application Names for EoP Check\" the following entry was added:<br/>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"3\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td>ID Type</td>\n<td>Application Name</td>\n<td>Application Description</td>\n</tr>\n<tr>\n<td>1 - Customer Master Data</td>\n<td>IS-OIL</td>\n<td>Oil Downstream Customer</td>\n</tr>\n<tr>\n<td>2 - Vendor Master Data</td>\n<td>IS-OIL</td>\n<td>Oil Downstream Vendor</td>\n</tr>\n</tbody>\n</table></div>\n</li>\n<li>In the activity \"Define Application Classes Registered for EoP Check\" the following entry was added:<br/>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"3\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td>ID Type</td>\n<td>Application Name</td>\n<td>Registered Class for EoP Checks</td>\n<td>General</td>\n<td>Comp.Code</td>\n</tr>\n<tr>\n<td>1 - Customer Master Data</td>\n<td>IS-OIL</td>\n<td>CVP_OIL_EOP_CHECK</td>\n<td>'X'</td>\n<td>'X'</td>\n</tr>\n<tr>\n<td>2 - Vendor Master Data</td>\n<td>IS-OIL</td>\n<td>CVP_OIL_EOP_CHECK</td>\n<td>'X'</td>\n<td>'X'</td>\n</tr>\n</tbody>\n</table></div>\n</li>\n</ol>\n<li>\n<p>The application IS OIL delivers the following class registered for the end of purpose check of the customer and the vendor:<br/> CVP_OIL_EOP_CHECK</p>\n</li>\n</ol>", "noteVersion": 1}, {"note": "2019485", "noteTitle": "2019485 - QM-basis where-used-checks before blocking of partner", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<div class=\"longtext\">\n<p>The application Quality Managment can contain data, which is related to a customer,  vendor or contact person. This note describes the changes, which will prevent blocking of a customer, vendor or contact person, if this customer, vendor or contact person is still assigned to QM data.</p>\n</div>\n<p>\n<script language=\"javascript\" type=\"text/javascript\">// <![CDATA[\r\nfunction get_tst( ){\r\n    var min = 5;\r\n    var max = 10;\r\n    var x = (Math.random() * (max - min)) + min;\r\n    x = x * 10000;\r\n    x = Math.round(x);\r\n    return x;\r\n}\r\nfunction change_cm(pointer) {\r\nx = get_tst();\r\n  window.open('/sap/bc/bsp/spn/sno_corr/NNF_Gui_show_cm_overview.sap?tst='+x+'&pointer='+pointer,'Launch_CWB','location=0,status=0,scrollbars=0, width=100,height=100');\r\n}\r\nfunction launch_msg(pointer) {\r\nx = get_tst();\r\n  window.open('/sap/bc/bsp/spn/sno_corr/NNF_Gui_launch_msg.sap?tst='+x+'&pointer='+pointer,'Launch_CWB','location=0,status=0,scrollbars=0, width=100,height=100');\r\n}\r\n// ]]></script>\n</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Deletion, Blocking, Personal Data, EOP, Customer, Vendor, Contact person, Manufacturer, KUNNR, LIFNR, PARNR, QALS-LIFNR, QALS-HERSTELLER, QALS-KUNNR, QALS-SELLIFNR, QALS-SELKUNNR, QALS-SELHERST, QASH-LSLIFNR, QASH-LSHERST, QASH-LSKUNNR, QCEM-PARNR, QCEP-PARNR, QCPR-LIFNR, QCPR-HERSTELLER, QDQL-LIFNR, QDQL-HERSTELLER, QDQL-KUNNR, QINF-LIEFERANT, QIWL-LIFNR, QIWL-KUNNR, QPRN-KUNNR, QPRN-LIFNR, QPRN-HRSTL, QDVM-KUNNR, QMSM-PARNR, QMUR-PARNR</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>See note 2007926</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>The new functionality for application Quality Management will be available with release SAP_APPL 617 and support package SP05.<br/><br/>The functionality consists of the following main features:</p>\n<p>In customizing \"Customer Master /Vendor Master Deletion\" the following entries are added:</p>\n<p>In the activity \"Define and Store Application Names for EoP Check\" the following entry was added:</p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"3\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td>\n<p dir=\"ltr\">ID Type</p>\n</td>\n<td>\n<p dir=\"ltr\">Application Name</p>\n</td>\n<td>\n<p dir=\"ltr\">Application Description</p>\n</td>\n</tr>\n<tr>\n<td>\n<p dir=\"ltr\">1 - Customer Master Data</p>\n</td>\n<td>\n<p dir=\"ltr\">ERP_QM_IM</p>\n</td>\n<td>\n<p dir=\"ltr\">Quality Management without notification</p>\n</td>\n</tr>\n<tr>\n<td>\n<p dir=\"ltr\">1 - Customer Master Data</p>\n</td>\n<td>\n<p dir=\"ltr\">ERP_QM_QN</p>\n</td>\n<td>\n<p dir=\"ltr\">Quality notification</p>\n</td>\n</tr>\n<tr>\n<td>\n<p dir=\"ltr\">2 - Vendor Master Data</p>\n</td>\n<td>\n<p dir=\"ltr\">ERP_QM_IM</p>\n</td>\n<td>\n<p dir=\"ltr\">Quality Management without notification</p>\n</td>\n</tr>\n<tr>\n<td>\n<p dir=\"ltr\">2 - Vendor Master Data</p>\n</td>\n<td>\n<p dir=\"ltr\">ERP_QM_QN</p>\n</td>\n<td>\n<p dir=\"ltr\">Quality notification</p>\n</td>\n</tr>\n<tr>\n<td>\n<p dir=\"ltr\">3 - Contact Person</p>\n</td>\n<td>\n<p dir=\"ltr\">ERP_QM_QN</p>\n</td>\n<td>\n<p dir=\"ltr\">Quality notification</p>\n</td>\n</tr>\n</tbody>\n</table></div>\n<p>In the activity \"Define Application Classes Registered for EoP Check\" the following entry was added:</p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"3\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td>ID Type</td>\n<td>Application Name</td>\n<td>\n<p>Item number</p>\n</td>\n<td>Registered Class for EoP Checks</td>\n<td>General</td>\n<td>Comp.Code</td>\n</tr>\n<tr>\n<td>1 - Customer Master Data</td>\n<td>\n<p dir=\"ltr\">ERP_QM_IM</p>\n</td>\n<td>\n<p dir=\"ltr\"> 0</p>\n</td>\n<td>CL_WUC_QM_IM_EOP_CHECK</td>\n<td>'X'</td>\n<td>'X'</td>\n</tr>\n<tr>\n<td>1 - Customer Master Data</td>\n<td>\n<p dir=\"ltr\">ERP_QM_QN</p>\n</td>\n<td>\n<p dir=\"ltr\"> 0</p>\n</td>\n<td>CL_WUC_QM_QN_EOP_CHECK</td>\n<td>'X'</td>\n<td>'X'</td>\n</tr>\n<tr>\n<td>1 - Customer Master Data</td>\n<td>\n<p dir=\"ltr\">ERP_QM_QN</p>\n</td>\n<td>\n<p dir=\"ltr\"> 1</p>\n</td>\n<td>CL_WUC_PM_WOC_MN_EOP_CHECK</td>\n<td>'X'</td>\n<td>'X'</td>\n</tr>\n<tr>\n<td>1 - Customer Master Data</td>\n<td>\n<p dir=\"ltr\">ERP_QM_QN</p>\n</td>\n<td>\n<p dir=\"ltr\"> 2</p>\n</td>\n<td>CL_WUC_PM_EQM_FL_EOP_CHECK</td>\n<td>'X'</td>\n<td>'X'</td>\n</tr>\n<tr>\n<td>2 - Vendor Master Data</td>\n<td>\n<p dir=\"ltr\">ERP_QM_IM</p>\n</td>\n<td>\n<p dir=\"ltr\"> 0</p>\n</td>\n<td>CL_WUC_QM_IM_EOP_CHECK</td>\n<td>'X'</td>\n<td>'X'</td>\n</tr>\n<tr>\n<td>2 - Vendor Master Data</td>\n<td>\n<p dir=\"ltr\">ERP_QM_QN</p>\n</td>\n<td>\n<p dir=\"ltr\"> 0</p>\n</td>\n<td>CL_WUC_QM_QN_EOP_CHECK</td>\n<td>'X'</td>\n<td>'X'</td>\n</tr>\n<tr>\n<td>2 - Vendor Master Data</td>\n<td>\n<p dir=\"ltr\">ERP_QM_QN</p>\n</td>\n<td>\n<p dir=\"ltr\"> 1</p>\n</td>\n<td>CL_WUC_PM_WOC_MN_EOP_CHECK</td>\n<td>'X'</td>\n<td>'X'</td>\n</tr>\n<tr>\n<td>2 - Vendor Master Data</td>\n<td>\n<p dir=\"ltr\">ERP_QM_QN</p>\n</td>\n<td>\n<p dir=\"ltr\"> 2</p>\n</td>\n<td>CL_WUC_PM_EQM_FL_EOP_CHECK</td>\n<td>'X'</td>\n<td>'X'</td>\n</tr>\n<tr>\n<td>\n<p dir=\"ltr\">3 - Contact Person</p>\n</td>\n<td>\n<p dir=\"ltr\">ERP_QM_QN</p>\n</td>\n<td> 0</td>\n<td>CL_WUC_QM_QN_EOP_CHECK</td>\n<td>'X'</td>\n<td>'X'</td>\n</tr>\n<tr>\n<td>\n<p dir=\"ltr\">3 - Contact Person</p>\n</td>\n<td>\n<p dir=\"ltr\">ERP_QM_QN</p>\n</td>\n<td> 1</td>\n<td>CL_WUC_PM_EQM_FL_EOP_CHECK</td>\n<td>'X'</td>\n<td>'X'</td>\n</tr>\n</tbody>\n</table></div>\n<p>The application Quality Management delivers the following class registered for the end of purpose check of customer, vendor and contact person:</p>\n<p> CL_WUC_QM_IM_EOP_CHECK  -'Where-Used-Check QM (without notification)'<br/> CL_WUC_QM_QN_EOP_CHECK - 'Where-Used-Check QM (notification)'</p>\n<p> </p>", "noteVersion": 1}, {"note": "2010512", "noteTitle": "2010512 - Material Master related where-used-check before blocking of customer and vendor data", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>The application Material Master can contain data, which is related to a customer or vendor. This note describes the changes, which will prevent blocking of a customer, if this customer is still assigned as competitor to materials. In addition these changes will prevent blocking of a vendor, if this vendor is still assigned as manufacturer to materials.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Deletion, Blocking, Personal Data, EOP, Customer, Vendor, Competitor, Manufacturer, KUNNR, MARA-KUNNR, MFRNR, MARA-MFRNR</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>See SAP note 2007926.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>The new functionality for application Material Master will be available with release SAP_APPL 617 and support package SP05.<br/><br/>The functionality consists of the following main features:</p>\n<ol>\n<li>In customizing \"Customer Master /Vendor Master Deletion\" the following entries are added:</li>\n<ol>\n<li>In the activity \"Define and Store Application Names for EoP Check\" the following entry was added:<br/>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"3\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td>ID Type</td>\n<td>Application Name</td>\n<td>Application Description</td>\n</tr>\n<tr>\n<td>1 - Customer Master Data</td>\n<td>ERP_LO_MD_MM</td>\n<td>Material Master</td>\n</tr>\n<tr>\n<td>2 - Vendor Master Data</td>\n<td>ERP_LO_MD_MM</td>\n<td>Material Master</td>\n</tr>\n</tbody>\n</table></div>\n</li>\n<li>In the activity \"Define Application Classes Registered for EoP Check\" the following entry was added:<br/>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"3\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td>ID Type</td>\n<td>Application Name</td>\n<td>Registered Class for EoP Checks</td>\n<td>General</td>\n<td>Comp.Code</td>\n</tr>\n<tr>\n<td>1 - Customer Master Data</td>\n<td>ERP_LO_MD_MM</td>\n<td>CL_WUC_LO_MD_MM_EOP_CHECK</td>\n<td>'X'</td>\n<td>'X'</td>\n</tr>\n<tr>\n<td>2 - Vendor Master Data</td>\n<td>ERP_LO_MD_MM</td>\n<td>CL_WUC_LO_MD_MM_EOP_CHECK</td>\n<td>'X'</td>\n<td>'X'</td>\n</tr>\n</tbody>\n</table></div>\n</li>\n</ol>\n<li>\n<p>The application Material Master delivers the following class registered for the end of purpose check of the customer and the vendor:<br/> CL_WUC_LO_MD_MM_EOP_CHECK - \"Where-Used-Check Material Master\"</p>\n</li>\n</ol>\n<p> </p>\n<p>As of release SAP ERP 600 Enhancement Package 8, Support package 06, a dedicated handling of blocked customers and vendors has been implemented for the Material Master and Article Master (see SAP note 2419991 for further details). This handling no longer requires to prevent the blocking of a customer or vendor in case the material or article contains data related to this customer or vendor.<br/>For this reason, the functionality described above is disabled as of SAP ERP 600 Enhancement Package 8, Support package 06.</p>", "noteVersion": 3}, {"note": "2066995", "noteTitle": "2066995 - Deletion and blocking of customer in ERP ATP application", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>ERP ATP contains data that is related to a customer or a vendor. This note describes the changes in the ERP ATP application that enable the simplified blocking and deletion of customer or vendor master based on SAP Information Lifecycle Management (ILM) as described in SAP Note 2007926.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Data Privacy, Information Lifecycle Management, ILM, Deletion, Blocking, Personal Data, Customer</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>See SAP Note 2007926</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>The new functionality ensures that:</p>\n<p>1) the validation of the customer number in transaction CO09 takes into account blocked customers. For blocked customers, the error message CO845 \"Enter Valid customer number\" is displayed and no availability data gets displayed.</p>\n<p>2) the route schedule transactions (VL51, VL52, VL53) have been enhanced to prevent the display the personal data referring to blocked sold-to-party's customers, in particular the customer name. An exception to this rule concerns users with auditor rights who are still authorized to display blocked data. When a route schedule contains one or several records referring to blocked or deleted customers, no user is allowed to edit existing records referring to blocked customers. However it is still possible to delete records referring to blocked or deleted sold-to-partys. No automatic deletion of the route schedule records take place when a corresponding sold-to-party is blocked or deleted in the customer / vendor application. The reason for this is that a manual adjustment of the route schedule times for the remaining records could be necessary and requires human decision.</p>\n<p>For product allocation, characteristics containing personal data can be used. These characteristics can be picked from various field catalogues in transaction mc21 when creating the infostructure. The values of these characteristics are then maintained in transaction mc62. The deletion of personal data is done as per the instructions in SAP note 1788312. LO-LIS provides no automatic mechanism for deletion of personal data and should be done manually by following the instructions in the note.</p>\n<p> </p>", "noteVersion": 2}, {"note": "2014905", "noteTitle": "2014905 - PM related where-used-checks before blocking of customer / vendor data", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>The application Plant Maintenance contains data which is related to customer / vendor. This note describes the changes which will prevent blocking of a customer or vendor if this customer or vendor is still assigned as manufacturer to materials.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Deletion, Blocking, Personal Data, EOP, Customer, KUNNR, Vendor, LIFNR, AFIH-KUNUM, AUFM-LIFNR, TPMUS-QLIFNUM, TPMUS-QQKUNUM, TPMUS-QQLIFNR, HIKO-KUNUM, HIMA-LIFNR, HIVG-LIFNR, MCIPMIS-ELIEF, QMEL-HERSTELLER, QMEL-KUNUM, QMEL-LIFNUM, IHPA-PARNR</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>See note 2007926.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>The new functionality for application Plant Maintenance will be available with release SAP_APPL 617 and support package SP05.</p>\n<p>The functionality consists of the following main features:</p>\n<ol>\n<li>In the customizing \"Customer Master / Vendor Master Deletion\", the following entries are added:</li>\n<ol>\n<li>In the activity \"Define and Store Application Names for EoP Check\" the following entry was added:<br/>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"3\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td>ID Type</td>\n<td>Application Name</td>\n<td>Application Description</td>\n</tr>\n<tr>\n<td>1 - Customer Master Data</td>\n<td>ERP_PM_EQM_FL</td>\n<td>\n<p>Plant Maintenance Partner</p>\n</td>\n</tr>\n<tr>\n<td>1 - Customer Master Data</td>\n<td>ERP_PM_WOC_MH</td>\n<td>\n<p>Plant Maintenance History</p>\n</td>\n</tr>\n<tr>\n<td>1 - Customer Master Data</td>\n<td>ERP_PM_WOC_MN</td>\n<td>\n<p>Plant Maintenance Notification</p>\n</td>\n</tr>\n<tr>\n<td>1 - Customer Master Data</td>\n<td>ERP_PM_WOC_MO</td>\n<td>\n<p>Plant Maintenance Order</p>\n</td>\n</tr>\n<tr>\n<td>2 - Vendor Master Data</td>\n<td>ERP_PM_EQM_FL</td>\n<td>\n<p> Plant Maintenance Partner</p>\n</td>\n</tr>\n<tr>\n<td>2 - Vendor Master Data</td>\n<td>ERP_PM_IS</td>\n<td>\n<p>Plant Maintenance Information System</p>\n</td>\n</tr>\n<tr>\n<td>2 - Vendor Master Data</td>\n<td>ERP_PM_WOC_MH</td>\n<td>\n<p>Plant Maintenance History</p>\n</td>\n</tr>\n<tr>\n<td>2 - Vendor Master Data</td>\n<td>ERP_PM_WOC_MN</td>\n<td>\n<p>Plant Maintenance Notification</p>\n</td>\n</tr>\n<tr>\n<td>2 - Vendor Master Data</td>\n<td>ERP_PM_WOC_MO</td>\n<td>\n<p>Plant Maintenance Order</p>\n</td>\n</tr>\n<tr>\n<td>3 - Contact Person</td>\n<td>ERP_PM_WOC_MN</td>\n<td>\n<p>Plant Maintenance Notification</p>\n</td>\n</tr>\n<tr>\n<td>3 - Contact Person</td>\n<td>ERP_PM_EQM_FL</td>\n<td>\n<p>Plant Maintenance Partner</p>\n</td>\n</tr>\n</tbody>\n</table></div>\n</li>\n<li>In the activity \"Define Application Classes Registered for EoP Check\", the following entry was added:<br/>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"3\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td>ID Type</td>\n<td>Application Name</td>\n<td>Item Number</td>\n<td>Registered Class for EoP Checks</td>\n<td>General</td>\n<td>Comp.Code</td>\n</tr>\n<tr>\n<td>1 - Customer Master Data</td>\n<td>ERP_PM_EQM_FL</td>\n<td>0</td>\n<td>CL_WUC_PM_EQM_FL_EOP_CHECK</td>\n<td>'X'</td>\n<td>'X'</td>\n</tr>\n<tr>\n<td>1 - Customer Master Data</td>\n<td>ERP_PM_WOC_MH</td>\n<td>0</td>\n<td>CL_WUC_PM_WOC_MH_EOP_CHECK</td>\n<td>'X'</td>\n<td>'X'</td>\n</tr>\n<tr>\n<td>1 - Customer Master Data</td>\n<td>ERP_PM_WOC_MN</td>\n<td>0</td>\n<td>CL_WUC_PM_WOC_MN_EOP_CHECK</td>\n<td>'X'</td>\n<td>'X'</td>\n</tr>\n<tr>\n<td>1 - Customer Master Data</td>\n<td>ERP_PM_WOC_MN</td>\n<td>1</td>\n<td>CL_WUC_QM_QN_EOP_CHECK</td>\n<td>'X'</td>\n<td>'X'</td>\n</tr>\n<tr>\n<td>1 - Customer Master Data</td>\n<td>ERP_PM_WOC_MO</td>\n<td>0</td>\n<td>CL_WUC_PM_WOC_MO_EOP_CHECK</td>\n<td>'X'</td>\n<td>'X'</td>\n</tr>\n<tr>\n<td>2 - Vendor Master Data</td>\n<td>ERP_PM_EQM_FL</td>\n<td>0</td>\n<td>CL_WUC_PM_EQM_FL_EOP_CHECK</td>\n<td>'X'</td>\n<td>'X'</td>\n</tr>\n<tr>\n<td>2 - Vendor Master Data</td>\n<td>ERP_PM_IS</td>\n<td>0</td>\n<td>CL_WUC_PM_IS_EOP_CHECK</td>\n<td>'X'</td>\n<td>'X'</td>\n</tr>\n<tr>\n<td>2 - Vendor Master Data</td>\n<td>ERP_PM_WOC_MH</td>\n<td>0</td>\n<td>CL_WUC_PM_WOC_MH_EOP_CHECK</td>\n<td>'X'</td>\n<td>'X'</td>\n</tr>\n<tr>\n<td>2 - Vendor Master Data</td>\n<td>ERP_PM_WOC_MN</td>\n<td>0</td>\n<td>CL_WUC_PM_WOC_MN_EOP_CHECK</td>\n<td>'X'</td>\n<td>'X'</td>\n</tr>\n<tr>\n<td>2 - Vendor Master Data</td>\n<td>ERP_PM_WOC_MN</td>\n<td>1</td>\n<td>CL_WUC_QM_QN_EOP_CHECK</td>\n<td>'X'</td>\n<td>'X'</td>\n</tr>\n<tr>\n<td>2 - Vendor Master Data</td>\n<td>ERP_PM_WOC_MO</td>\n<td>0</td>\n<td>CL_WUC_PM_WOC_MO_EOP_CHECK</td>\n<td>'X'</td>\n<td>'X'</td>\n</tr>\n<tr>\n<td>3 - Contact Person</td>\n<td>ERP_PM_WOC_MN</td>\n<td>1</td>\n<td>CL_WUC_QM_QN_EOP_CHECK</td>\n<td>'X'</td>\n<td>'X'</td>\n</tr>\n<tr>\n<td>3 - Contact Person</td>\n<td>ERP_PM_EQM_FL</td>\n<td>0</td>\n<td>CL_WUC_PM_EQM_FL_EOP_CHECK</td>\n<td>'X'</td>\n<td>'X'</td>\n</tr>\n</tbody>\n</table></div>\n</li>\n</ol>\n<li>\n<p>The application Plant Maintenance delivers the following class registered for the end of purpose check of the vendor:<br/>CL_WUC_PM_EQM_FL_EOP_CHECK - \"Where-Used-Check Plant Maintenance Partner\"<br/>CL_WUC_PM_IS_EOP_CHECK - \"Where-Used-Check Plant Maintenance Information System\"<br/>CL_WUC_PM_WOC_MH_EOP_CHECK - \"Where-Used-Check Plant Maintenance History\"<br/>CL_WUC_PM_WOC_MN_EOP_CHECK - \"Where-Used-Check Plant Maintenance Notification\"<br/>CL_WUC_PM_WOC_MO_EOP_CHECK - \"Where-Used-Check Plant Maintenance Order\"</p>\n</li>\n</ol>\n<p> </p>\n<p>Please note that the provided method CVP_IF_APPL_EOP_CHECK~CHECK_PARTNERS of the new classes contains database accesses to the different tables without index. For this purpose, the following indexs are pre-defined and can be activated in the ABAP Data Dictionary (as index on all database systems or as index for selected database systems) to improve the performance:</p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"3\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td>AUFM~CV1</td>\n<td>Table AUFM, fields MANDT and LIFNR (Vendor)</td>\n</tr>\n<tr>\n<td>TPMUS~CV1</td>\n<td>Table TPMUS, fields MANDT and QLIFNUM (Vendor)</td>\n</tr>\n<tr>\n<td>TPMUS~CV2</td>\n<td>Table TPMUS, fields MANDT and QQKUNUM (Customer)</td>\n</tr>\n<tr>\n<td>TPMUS~CV3</td>\n<td>Table TPMUS, fields MANDT and QQLIFNR (Vendor)</td>\n</tr>\n<tr>\n<td>\n<p>HIKO~CV1</p>\n</td>\n<td>Table HIKO, fields MANDT and KUNUM (Customer)</td>\n</tr>\n<tr>\n<td>HIMA~CV1</td>\n<td>Table HIMA, fields MANDT and LIFNR (Vendor)</td>\n</tr>\n<tr>\n<td>HIVG~CV1</td>\n<td>Table HIVG, fields MANDT and LIFNR (Vendor)</td>\n</tr>\n<tr>\n<td>MCIPMIS~CV1</td>\n<td>Table MCIPMIS, fields MANDT and ELIEF (Vendor)</td>\n</tr>\n<tr>\n<td>QMEL~CV1</td>\n<td>Table QMEL, fields MANDT and HERSTELLER (Vendor)</td>\n</tr>\n</tbody>\n</table></div>", "noteVersion": 5}, {"note": "2140138", "noteTitle": "2140138 - Decoupled TSW related End Of Purpose Check before blocking of customer and vendor data", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>The application Decoupled TSW can contain data, which is related to a customer or vendor. This note describes the changes, which will prevent blocking of a customer, if this customer is still in use. In addition these changes will prevent blocking of a vendor, if this vendor is still in use.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Deletion, Blocking, Personal Data, EOP, Customer, Vendor, KUNNR , TSW for ECC</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>See note 2007926</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>The new functionality for <em>Decoupled TSW </em> is available as of 617 release support package SP08.</p>\n<p><strong><em>1.LE-TSW</em> provides <em>a End of Purpose Check </em> for the customer or vendor. This check consists of the following main features:</strong></p>\n<p><br/>The functionality consists of the following main features:</p>\n<p>In customizing \"Customer Master /Vendor Master Deletion\" the following entries are added:</p>\n<ol><ol>\n<li>In the activity \"Define and Store Application Names for EoP Check\" the following entry was added:<br/>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"3\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td>ID Type</td>\n<td>Application Name</td>\n<td>Application Description</td>\n</tr>\n<tr>\n<td>1 - Customer Master Data</td>\n<td>TSW_ECC</td>\n<td>Decoupled TSW Customer</td>\n</tr>\n<tr>\n<td>2 - Vendor Master Data</td>\n<td>TSW_ECC</td>\n<td>Decoupled TSW Vendor</td>\n</tr>\n</tbody>\n</table></div>\n</li>\n<li>In the activity \"Define Application Classes Registered for EoP Check\" the following entry was added:<br/>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"3\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td>ID Type</td>\n<td>Application Name</td>\n<td>Registered Class for EoP Checks</td>\n<td>General</td>\n<td>Comp.Code</td>\n</tr>\n<tr>\n<td>1 - Customer Master Data</td>\n<td>TSW_ECC</td>\n<td>CVP_TSW_ECC_EOP_CHECK</td>\n<td>'X'</td>\n<td>'X'</td>\n</tr>\n<tr>\n<td>2 - Vendor Master Data</td>\n<td>TSW_ECC</td>\n<td>CVP_TSW_ECC_EOP_CHECK</td>\n<td>'X'</td>\n<td>'X'</td>\n</tr>\n</tbody>\n</table></div>\r\nThe application TSW delivers the following class registered for the end of purpose check of the customer and the vendor:<br/> CVP_TSW_ECC_EOP_CHECK</li>\n</ol></ol>\n<p><strong>2. The new Business function ILM_TSW_ECC is provided for ILM enablement of the following archiving objects-</strong></p>\n<ul>\n<li>OIG_TPUNIT</li>\n<li>OIG_VEHCLE</li>\n<li>OIJ_NOMIN</li>\n<li>OIJ_TICKET</li>\n<li>IS_OIFSPBL</li>\n</ul>\n<p><strong>3. Handling of blocked customer/vendor</strong></p>\n<p>No further business activity like creation or changing of nominations will be allowed on a blocked customer or vendor.Details of blocked customer or vendor can be viewed only by authorized user.</p>", "noteVersion": 1}, {"note": "2011558", "noteTitle": "2011558 - Work order related where-used-checks before blocking of customer / vendor data", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>The application Production Order (Production Planning - Shop Floor Control) contains data which is related to customer / vendor. This note describes the changes which will prevent blocking of a customer or vendor if this customer or vendor is still assigned to a goods movement to be processed or to an externally processed operation.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Deletion, Blocking, Personal Data, EOP, Customer, KUNNR, AFFW-KUNNR, Vendor, LIFNR, AFVC-LIFNR, AFFW-LIFNR</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>See note 2007926.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>The new functionality for application Production Order (Production Planning - Shop Floor Control) will be available with release SAP_APPL 617 and support package SP05.<br/><br/>The functionality consists of the following main features:</p>\n<ol>\n<li>In the customizing \"Customer Master / Vendor Master Deletion\", the following entries are added:</li>\n<ol>\n<li>In the activity \"Define and Store Application Names for EoP Check\" the following entry was added:<br/>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"3\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td>ID Type</td>\n<td>Application Name</td>\n<td>Application Description</td>\n</tr>\n<tr>\n<td>1 - Customer Master Data</td>\n<td>ERP_PP_SFC</td>\n<td>Production Order</td>\n</tr>\n<tr>\n<td>2 - Vendor Master Data</td>\n<td>ERP_PP_SFC</td>\n<td>Production Order</td>\n</tr>\n</tbody>\n</table></div>\n</li>\n<li>In the activity \"Define Application Classes Registered for EoP Check\", the following entry was added:<br/>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"3\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td>ID Type</td>\n<td>Application Name</td>\n<td>Registered Class for EoP Checks</td>\n<td>General</td>\n<td>Comp.Code</td>\n</tr>\n<tr>\n<td>1 - Customer Master Data</td>\n<td>ERP_PP_SFC</td>\n<td>CL_WUC_PP_SFC_EOP_CHECK</td>\n<td>'X'</td>\n<td>'X'</td>\n</tr>\n<tr>\n<td>2 - Vendor Master Data</td>\n<td>ERP_PP_SFC</td>\n<td>CL_WUC_PP_SFC_EOP_CHECK</td>\n<td>'X'</td>\n<td>'X'</td>\n</tr>\n</tbody>\n</table></div>\n</li>\n</ol>\n<li>\n<p>The application Production Order (Production Planning - Shop Floor Control) delivers the following class registered for the end of purpose check of the vendor:<br/> CL_WUC_PP_SFC_EOP_CHECK - \"Where-Used-Check Production Order\"</p>\n</li>\n</ol>\n<p>Please note that the provided method CVP_IF_APPL_EOP_CHECK~CHECK_PARTNERS of the new class CL_WUC_PP_SFC_EOP_CHECK contains database accesses to the tables AFFW and AFVC without index. For this purpose, the following indexs are pre-defined and can be activated in the ABAP Data Dictionary (as index on all database systems or as index for selected database systems) to improve the performance:</p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"3\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td>AFFW~CV1</td>\n<td>Table AFFW, fields MANDT and KUNNR (Customer)</td>\n</tr>\n<tr>\n<td>AFFW~CV2</td>\n<td>Table AFFW, fields MANDT and LIFNR (Vendor)</td>\n</tr>\n<tr>\n<td>AFVC~CV1</td>\n<td>Table AFVC, fields MANDT and LIFNR (Vendor)</td>\n</tr>\n</tbody>\n</table></div>", "noteVersion": 2}, {"note": "2021007", "noteTitle": "2021007 - Where-used check for empties management in sales", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>The empties management application in sales contains data that refers to customers. This SAP Note delivers an implementation that prevents the blocking of a customer if the customer is still being used by the empties management application in sales.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>End of purpose, where-used check, delete, block, customer, vendor, partner</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>See SAP Note 2007926.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>The new function is available in the empties management application in sales as of Release EA-APPL 617 Support Package SP05.</p>\n<p>It is delivered with the class /BEV1/CL_WUC_SD_EM_EOP_CHECK.</p>\n<p>Maintenance views CVP_EOPAPP_V and CVP_EOPCLASS_V contain the relevant customizing.</p>", "noteVersion": 3}, {"note": "2101387", "noteTitle": "2101387 - Deletion and Blocking of Customer in Spain VAT reporting", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>The application Spain VAT reporting contains data that is related to a customer. This SAP Note describes the changes in the Spain VAT reporting application that enable the simplified blocking and deletion of customer master data based on SAP Information Lifecycle Management (ILM) as described in SAP Note 2007926.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Information Lifecycle Management, ILM, Blocking, Personal Data, Customer, Vendor, FI_ACCPAYB, FI_ACCRECV, FI_ACCKNVK, End of Purpose check, EoP, ES VAT, Spain</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>See SAP Note 2007926</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>The new functionality for application Spain VAT reporting is available as of SAP_FIN 617, Support Package 07.</p>\n<p>Spain VAT reporting application provides an end of purpose check for the customer. This check consists of the following main features:</p>\n<ul>\n<li>In the standard system, the following application names are delivered for Spain VAT reporting:</li>\n</ul>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"0\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td>\n<p>ID Type</p>\n</td>\n<td>\n<p>Application Name</p>\n</td>\n<td>\n<p>Application Description</p>\n</td>\n</tr>\n<tr>\n<td>\n<p>1 - Customer Master Data</p>\n</td>\n<td>\n<p>ERP_FI_LOC_ES_VOC</p>\n</td>\n<td>\n<p>Spain VAT on Cash</p>\n</td>\n</tr>\n</tbody>\n</table></div>\n<p> </p>\n<ul>\n<li>Spain VAT reporting application delivers the following class registered for the end of purpose check of customer master:</li>\n</ul>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"0\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td>\n<p>ID Type</p>\n</td>\n<td>\n<p>Application Name</p>\n</td>\n<td>\n<p>Registered Class for EoP Checks</p>\n</td>\n<td>\n<p>General</p>\n</td>\n<td>\n<p>Comp.Code</p>\n</td>\n</tr>\n<tr>\n<td>\n<p>1 - Customer Master Data</p>\n</td>\n<td>\n<p>ERP_FI_LOC_ES_VOC</p>\n</td>\n<td>\n<p>CL_FIN_LOC_ES_VOC_EOP_CUST</p>\n</td>\n<td>\n<p>' '</p>\n</td>\n<td>\n<p>'X'</p>\n</td>\n</tr>\n</tbody>\n</table></div>\n<p> The EoP class checks for the Spain VAT transaction of the customer with respect to the company code for the current fiscal year. If there are no transactions, then the status is updated as 'no Business made with partner at all' for the partners. Additionally, for all other customers being checked, if any already reported transactions exist then the status is updated as 'Business is complete and Residence time is over' along with the SORT date if there are no references of the customer by other customers.</p>\n<p>The status 'No (Business is ongoing with partner)' is set for the customer when this customer is referred by other customers.</p>\n<ul>\n<li>When a customer is blocked, the application does not display any personal data related to this customer including the number of the customer. You cannot display or change any information. You cannot create new business with this customer. </li>\n</ul>", "noteVersion": 2}, {"note": "2010348", "noteTitle": "2010348 - RFDABL00/RFKABL00: Locking customers and vendors", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p><em>RFDABL00 (\"Display Changes to Customers\") and RFKABL00 (\"Display Changes to Vendors\")</em> display data with relation to a customer or vendor.</p>\n<p>This SAP Note describes the changes in these programs that enable the simplified deletion and locking of customer master data and vendor master data based on SAP Information Lifecycle Management (ILM). SAP Note 2007926 contains the description for this.</p>\n<p>This SAP Note is also provided as a technical prerequisite for other corrections. It does not deliver any function.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Data protection, ILM, SAP Information Lifecycle Management, delete, deletion, lock, personal data, person-related data, customer, vendor, FI_ACCPAYB, FI_ACCRECV, FI_ACCKNVK, EoP, end of purpose</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>See SAP Note 2007926.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>The new functions are available as of: <br/>SAP_FIN, Release 617, Support Package 5<br/><br/>The programs offer an end of purpose check for the customer and vendor. This check consists of the following main characteristics:</p>\n<p>If a customer or vendor is locked, the application does not display any personal data of this customer or vendor including the customer number or vendor number.</p>\n<p> </p>", "noteVersion": 3}, {"note": "2023030", "noteTitle": "2023030 - Deletion & Blocking of Customer and Vendor in PSM", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p><em>PSM</em> contains data, which is related to a Customer or Vendor. This note describes the changes in PSM for the adaption to the simplified blocking and deletion of the Customer or Vendor based on SAP Information Lifecycle Management (ILM) as described by note 2007926.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Data Privacy, Information Lifecycle Management, ILM, Deletion, Blocking, Personal Data, Customer, Vendor, FI_ACCPAYB, FI_ACCRECV, EoP, ERP_PSM, FMFINCODE-BOSSID</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>See note 2007926</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>The new functionality for PSM will be available with release:</p>\n<ul>\n<li>SAP_FIN 617 SP 05</li>\n<li>SAP_FIN 700 SP 02</li>\n<li>EA-PS 617 SP 05</li>\n</ul>\n<p>For PSM an end of purpose check for the Customer or Vendor is provided:</p>\n<div class=\"longtext\">\n<ul>\n<li>Application names introduced for the end of purpose check:<br/>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"0\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td>\n<p>ID Type</p>\n</td>\n<td>\n<p>Application Name</p>\n</td>\n<td>\n<p>Application Description</p>\n</td>\n</tr>\n<tr>\n<td>\n<p>Customer Master Data</p>\n</td>\n<td>\n<p>ERP_EF</p>\n</td>\n<td>\n<p>Earmarked Documents</p>\n</td>\n</tr>\n<tr>\n<td>\n<p>Vendor Master Data</p>\n</td>\n<td>\n<p>ERP_EF</p>\n</td>\n<td>\n<p>Earmarked Documents</p>\n</td>\n</tr>\n<tr>\n<td>\n<p>Customer Master Data</p>\n</td>\n<td>\n<p>ERP_PSM</p>\n</td>\n<td>Public Sector Management</td>\n</tr>\n<tr>\n<td>\n<p>Vendor Master Data</p>\n</td>\n<td>ERP_PSM</td>\n<td>\n<p>Public Sector Management</p>\n</td>\n</tr>\n</tbody>\n</table></div>\n<em></em></li>\n</ul>\n</div>\n<ul>\n<li>The application PSM supports the usage of application rule variants to enable the maintenance of residence and retention periods for ILM objects FI_ACCPAYB and FI_ACCRECV according to the objects of the application PSM.<br/>For the mapping of application specific condition fields to application rule variants you need to assign ILM rule groups to application rule variants. ILM rule groups can be defined for the ILM objects of PSM in transaction IRM_CUST_CSS. You assigned them also to the retention rules maintained for these ILM objects in transaction IRMPOL. Relevant for PSM are the following ILM objects:</li>\n<ul>\n<li><em>FM_DOC_FI</em></li>\n<li><em>FM_DOC_OI</em></li>\n<li><em>FM_CASHDSK</em></li>\n<li><em>FM_FUNR</em></li>\n</ul>\n</ul>\n<ul>\n<li>The application PSM delivers the following classes registered for the end of purpose check of Customer and Vendor:<br/>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"0\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td>\n<p>ID Type</p>\n</td>\n<td>\n<p>Application Name</p>\n</td>\n<td>\n<p>Registered Class for EoP Checks</p>\n</td>\n<td>\n<p>General</p>\n</td>\n<td>\n<p>Comp. Code</p>\n</td>\n</tr>\n<tr>\n<td>\n<p>Customer Master Data</p>\n</td>\n<td>\n<p>ERP_EF</p>\n</td>\n<td>\n<p>CVP_CL_APPL_EOP_CHECK_EF</p>\n</td>\n<td>\n<p> </p>\n</td>\n<td>\n<p>'X'</p>\n</td>\n</tr>\n<tr>\n<td>\n<p>Vendor Master Data</p>\n</td>\n<td>\n<p>ERP_EF</p>\n</td>\n<td>\n<p>CVP_CL_APPL_EOP_CHECK_EF</p>\n</td>\n<td>\n<p> </p>\n</td>\n<td>\n<p>'X'</p>\n</td>\n</tr>\n<tr>\n<td>\n<p>Customer Master Data</p>\n</td>\n<td>\n<p>ERP_PSM</p>\n</td>\n<td>\n<p>CVP_CL_APPL_EOP_CHECK_PSM</p>\n</td>\n<td>\n<p> </p>\n</td>\n<td>\n<p>'X'</p>\n</td>\n</tr>\n<tr>\n<td>\n<p>Vendor Master Data</p>\n</td>\n<td>\n<p>ERP_PSM</p>\n</td>\n<td>\n<p>CVP_CL_APPL_EOP_CHECK_PSM</p>\n</td>\n<td>\n<p> </p>\n</td>\n<td>\n<p>'X'</p>\n</td>\n</tr>\n</tbody>\n</table></div>\n</li>\n<ul>\n<li>Class CVP_CL_APPL_EOP_CHECK_EF checks whether an Earmarked Document is set to completed</li>\n<li>Class CVP_CL_APPL_EOP_CHECK_PSM checks the updating of FI documents, updating of commitment documents,cash desk documents and Fund. For more information please see the online documentation. </li>\n</ul>\n</ul>\n<ul>\n<li>Once a Customer, Vendor or Contact Person is blocked, the application will not show any personal data related to this Customer, Vendor or Contact Person including the number of the Customer, Vendor or Contact Person. You will not be able to display and or maintain any information. You will not be able create new Business with this Customer, Vendor or Contact Person. In PSM this is implemented for:</li>\n<ul>\n<li>Reauests</li>\n<li>Earmarked documents</li>\n<li>Reporting</li>\n</ul>\n</ul>", "noteVersion": 2}, {"note": "1825544", "noteTitle": "1825544 - Simplified Deletion and Blocking of Personal Data in SAP Business Suite", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>Data protection is associated with numerous legal requirements and privacy concerns. In addition to compliance with general data privacy acts, it is necessary to consider compliance with industry-specific legislation in different countries.</p>\n<p>A typical potential scenario in certain countries is that personal data shall be deleted after the specified, explicit, and legitimate purpose of the processing has ended but only as long as no other retention periods are defined in legislation, for example, retention periods for financial documents.</p>\n<p>Legal requirements in certain scenarios or countries also often require blocking of data in cases where the specified, explicit, and legitimate purposes of the processing has ended but the data is retained in the database due to other legally defined retention periods.</p>\n<p>In some scenarios, personal data also includes referenced data. Therefore, the challenge for destruction and blocking is to handle first referenced data and finally data such as business partner data.</p>\n<p>To enable even complex scenarios, SAP simplifies existing deletion functionality to cover data objects that are personal data by default. For this purpose, SAP uses SAP Information Lifecycle Management (ILM) to help you set up a compliant information lifecycle management process in an efficient and flexible manner.</p>\n<p>The functions that support the simplified blocking and deletion of personal data are not delivered as a big bang implementation but in several waves. Scenarios or products that are not specified in the notes 1825608 (central Business Partner) and 2007926 (ERP Customer and Vendor) are so far not subject of simplified blocking and deletion. Nevertheless, it is also possible to destroy personal data for these scenarios or products. In these cases, you have to use existing archiving or deletion functionality or implement an individual retention management of relevant business data along its entire lifecycle.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Information Lifecycle Management, ILM, Deletion, Blocking, Personal Data</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>SAP Information Lifecycle Management enhances the SAP standard delivery with the ability to manage the lifecycle of live and archived data based on rules. SAP Information Lifecycle Management (ILM) is an important building block that helps SAP customers comply with the various laws and regulations governing the retention of information.</p>\n<p>Information lifecycle management involves managing a company's business data along its entire life cycle - from the time of its creation in the application system to its long-term storage in a storage system through to its final destruction at the end of its life cycle.</p>\n<p><strong>Preparation steps:</strong></p>\n<p>You must activate the business function ILM once. This is the prerequisite with which you can use the options to delete the data.<br/>Check in addition the implementation status of the prerequisite SAP notes as listed in SAP note 2039087 - \"Release Information for Simplified Data Deletion based on SAP ILM\".</p>\n<p>For more information about SAP Information Lifecycle Management, see <br/>http://help.sap.com/erp2005_ehp_07/helpdata/en/35/aaef16745644c397ec0024b31fb191/frameset.htm<br/><br/></p>\n<p><strong>Current Scope for the Simplified Blocking and Deletion:</strong></p>\n<p>Only applications named in referenced Notes are enabled for the simplified blocking and deletion. This is valid for SAP delivered functionality, any custom own enhancement must get checked properly.  This includes also Partner Products listed on SAPs price list. In case of questions get in contact with SAP Partner Management. All other third party software processing personal data from the SAP Business Suite would have to be enabled individually either from the third party vendor or from the customer. The guide attached to note 2103639 contains the necessary information how to do this.</p>\n<p>An exemplary overview with links to further related information or documentation can be found in the attached document \"Quick-Guide Simplified Blocking and Deletion in SAP Business Suite based on SAP ILM\".</p>", "noteVersion": 7}, {"note": "2020890", "noteTitle": "2020890 - Transportation related where-used-check before blocking of customer and vendor data", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>The application Transportation can contain data, which is related to a customer or vendor. This note describes the changes, which will prevent blocking of a customer, if this customer is still assigned to a shipment document. In addition these changes will prevent blocking of a vendor, if this vendor is still assigned to a shipment document.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Deletion, Blocking, Personal Data, EOP, Customer, Vendor, KUNNR</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>See note 2007926</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>The new functionality for application Transprotation (Material Master) will be available with release SAP_APPL 617 and support package SP05.</p>\n<p>Application Tranportation (Material Master) provides <em>an end of purpose check|a where-used check&gt;</em> for the customer or vendor.<br/><br/>The functionality consists of the following main features:</p>\n<ol>\n<li>In customizing \"Customer Master /Vendor Master Deletion\" the following entries are added:</li>\n<ol>\n<li>In the activity \"Define and Store Application Names for EoP Check\" the following entry was added:<br/>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"3\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td>ID Type</td>\n<td>Application Name</td>\n<td>Application Description</td>\n</tr>\n<tr>\n<td>1 - Customer Master Data</td>\n<td>ERP_LE_TRA</td>\n<td>Customer Master</td>\n</tr>\n<tr>\n<td>2 - Vendor Master Data</td>\n<td>ERP_LE_TRA</td>\n<td>Vendor Master</td>\n</tr>\n</tbody>\n</table></div>\n</li>\n<li>In the activity \"Define Application Classes Registered for EoP Check\" the following entry was added:<br/>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"3\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td>ID Type</td>\n<td>Application Name</td>\n<td>Registered Class for EoP Checks</td>\n<td>General</td>\n<td>Comp.Code</td>\n</tr>\n<tr>\n<td>1 - Customer Master Data</td>\n<td>ERP_LO_MD_MM</td>\n<td>CVP_IF_EOP_LE_TRA_CHECK</td>\n<td>'X'</td>\n<td>'X'</td>\n</tr>\n<tr>\n<td>2 - Vendor Master Data</td>\n<td>ERP_LO_MD_MM</td>\n<td>CVP_IF_EOP_LE_TRA_CHECK</td>\n<td>'X'</td>\n<td>'X'</td>\n</tr>\n</tbody>\n</table></div>\n</li>\n</ol>\n<li>\n<p>The application Material Master delivers the following class registered for the end of purpose check of the customer and the vendor:<br/> CVP_IF_EOP_LE_TRA_CHECK - \"Where-Used-Check Material Master\"</p>\n</li>\n</ol>", "noteVersion": 2}, {"note": "2015394", "noteTitle": "2015394 - End of Purpose check to block ECC vendor replicated to SRM", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>This note describes the changes, which will prevent blocking of a ECC Vendor, if vendor is used in any of the Business Documents in SRM.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Deletion, Blocking, Personal Data, EOP, Vendor,Business Partner,CRMD_PARTNER,ERPSRM_VEND_EOP_CHECK</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>See note 1825608 , 2007926 &amp; 2011539</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>This new functionality will be available from ECC 6.0(Ehp7) SP05 and SRM 7.03 SP05</p>\n<p> Following Changes have been done as part of customizing.</p>\n<p> (a).  In the view CVP_EOPAPP_V(Define and Store Application Names for EOP Check )  .</p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"3\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td><strong>ID Type</strong></td>\n<td><strong><strong>Appl Name  </strong></strong></td>\n<td><strong>Application Description</strong></td>\n</tr>\n<tr>\n<td>2 Vendor master data</td>\n<td>ERP_SRM</td>\n<td>Supplier Relationship Management</td>\n</tr>\n</tbody>\n</table></div>\n<p>(b).  In the view CVP_EOPCLASS_V(Define Application Classes Registered for EoP Check).</p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"3\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td><strong>ID Type </strong></td>\n<td><strong>Appl Name  </strong></td>\n<td><strong><strong>Application Description</strong></strong></td>\n<td><strong>Registered Class for EoP Checks</strong></td>\n<td><strong>General</strong></td>\n<td><strong>Comp.Code </strong></td>\n</tr>\n<tr>\n<td>2 Vendor master data</td>\n<td>ERP_SRM</td>\n<td>Supplier Relationship Management</td>\n<td>CL_MMSRM_EOP_VEND_CHECK</td>\n<td>    X</td>\n<td> </td>\n</tr>\n</tbody>\n</table></div>\n<p>ERP system RFC information (RFC name) need to be maintained in the below path:</p>\n<p>SAP Customizing Implementation Guide -&gt;<br/>Financial Accounting (New) -&gt; Accounts Receivable and Accounts Payable<br/>-&gt;Deletion of Customer and Vendor Master Data -&gt;<br/>Register RFC Connection of Master System</p>\n<p><strong><span>Deletion of Personal Data</span></strong></p>\n<p>The SAP_APPL,ERP_SRM and SRM standalone, SRM_SERVER, BBP might process data (personal data) that is subject to the data protection laws applicable in specific countries as described in SAP Note 1825544 The SAP Information Lifecycle Management (ILM) component supports the entire software lifecycle including the storage, retention, blocking, and deletion of data. SAP_APPL, ERP_SRM and SRM standalone, SRM_SERVER , BBP uses SAP ILM to support the deletion of personal data as described in the following sections</p>\n<p>SAP delivers an end of purpose check for the</p>\n<p>SAP_APPL, ERP_SRM and SRM standalone, SRM_SERVER, BBP SAP delivers a where-used check (WUC) for the SAP_APPL, ERP_SRM and SRM standalone, SRM_SERVER, BBP.</p>\n<p>All applications register either an end of purpose check (EoP) in the Customizing settings for the blocking and deletion of vendor master / the business partner or a WUC. For information about the Customizing of blocking and deletion for MM-SRM, SAP_APPL, ERP_SRM and SRM standalone, SRM_SERVER, BBP see Configuration: Simplified Blocking and Deletion.</p>\n<p><span><strong>Requirement for EoP checks &amp; vendor blocking in SRM:</strong></span></p>\n<p>In order to block vendor, for ILM object CA_BUPA residence rules need to be maintained under audit area BUPA_DP</p>\n<p>Residence rules need to be maintained for applications (Application Name)   BBP, BUP &amp; ERP_SRM</p>\n<p>Note : Avoid blank lines while creating policies</p>\n<p><strong><span>End of Purpose Check (EoP)</span></strong></p>\n<p>Phase one: The relevant data is actively used.</p>\n<p>Phase two: The relevant data is actively available in the system.</p>\n<p>Phase three: The relevant data needs to be retained for other reasons.</p>\n<p>For example, processing of data is no longer required for the primary business purpose, but to comply with legal rules for retention, the data must still be available. In phase three, the relevant data is blocked. Blocking of data prevents the business users of SAP applications from displaying and using data that may include personal data and is no longer relevant for business activities.</p>\n<p>Blocking of data can impact system behavior in the following ways:　</p>\n<p>● Display: The system does not display blocked data.</p>\n<p>● Change: It is not possible to change a business object that contains blocked data.</p>\n<p> </p>", "noteVersion": 5}, {"note": "2016499", "noteTitle": "2016499 - WUC for Direct Store Delivery", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>The Direct Store Delivery application contains data that refers to customers and partners. This SAP Note delivers an implementation that prevents the blocking of a customer or partner if the customer or partner is still being used by the DSD application.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>End of purpose, where-used check, delete, block, customer, vendor, partner</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>See SAP Note 2007926.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>The new function is available in the Direct Store Delivery application as of Release EA-APPL 617 Support Package 05.</p>\n<p>It is delivered for DSD with the class /DSD/CL_WUC_LE_DSD_EOP_CHECK.</p>\n<p>The relevant Customizing is delivered with the switch BC Sets /DSD/VAL_CUST_WUC and /DSD/VAL_CUST_WUC_CL.</p>\n<p> </p>\n<p> </p>", "noteVersion": 3}, {"note": "2029295", "noteTitle": "2029295 - Deletion and Blocking of Customer and Vendor in SAP ERP Travel Management", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p><br/> SAP ERP Travel Managementcontains data that is related to a customer or a vendor. This note describes the changes in SAP ERP Travel Management that enable the simplified blocking and deletion of customer or vendor master based on SAP Information Lifecycle Management (ILM) as described in SAP Note 2007926.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Data Privacy, Information Lifecycle Management, ILM, Deletion, Blocking, Personal Data, Customer, Vendor, FI_ACCPAYB, FI_ACCRECV, FI_ACCKNVK, EoP, <em>&lt;Application Specific Keywords&gt;</em></p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>See SAP Note 2007926</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<div class=\"longtext\">\n<p>The new functionality for <em>&lt;application description</em><em>&gt;</em> is available as of <em>&lt;product release&gt;</em>, support package &lt;nn&gt;.</p>\n<p><em>&lt;application description&gt;</em> provides <em>&lt;</em><em>an end of purpose check |a where-used check&gt;</em> for the customer or vendor. This check consists of the following main features:</p>\n<ol>\n<li><strong><em>&lt;&lt;List here all application names delivered for your application and provide a description about the intention (e.g. which application objects are covered)&gt;&gt;<br/></em></strong>In the standard system, the following application names are delivered for <em>&lt;application description&gt;</em>:<br/>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"0\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td>\n<p>ID Type</p>\n</td>\n<td>\n<p>Application Name</p>\n</td>\n<td>\n<p>Application Description</p>\n</td>\n</tr>\n<tr>\n<td>\n<p>&lt;1 - Customer Master Data&gt;</p>\n</td>\n<td>\n<p>&lt;ZZ1&gt;</p>\n</td>\n<td>\n<p>&lt;short description of application name &lt;ZZ1&gt;&gt;</p>\n</td>\n</tr>\n<tr>\n<td>\n<p>&lt;2 - Vendor Master Data&gt;</p>\n</td>\n<td>\n<p>&lt;ZZ2&gt;</p>\n</td>\n<td>\n<p>&lt;short description of application name &lt;ZZ2&gt;&gt;</p>\n</td>\n</tr>\n<tr>\n<td>\n<p>&lt;3 – Contact Person&gt;</p>\n</td>\n<td>\n<p>&lt;ZZ3&gt;</p>\n</td>\n<td>\n<p>&lt;short description of application name &lt;ZZ3&gt;&gt;</p>\n</td>\n</tr>\n</tbody>\n</table></div>\n<p>&lt;Description of application name &lt;ZZn&gt; including covered application objects&gt;</p>\n</li>\n<li><strong><em>&lt;&lt;Describe here for each application name if and how the application rule variant concept is supported. Choose the template for the relevant variant 1 or 2. &gt;&gt;</em></strong><br/>The application name <em>&lt;ZZn&gt;</em> supports the use of application rule variants to enable the maintenance of residence and retention periods for ILM objects <em>&lt;FI_ACCPAYB&gt;, &lt;FI_ACCRECV&gt; or &lt;FI_ACCKNVK&gt;</em> according to the objects that belong to application <em>&lt;ZZn&gt;</em>. <br/><br/><strong><em>&lt;&lt;Variant 1 using an application-specific mapping customizing: &gt;&gt;<br/></em></strong>You can mapapplication-specific condition fields to application rule variants in Customizing for &lt;….&gt; under <em>&lt;describe the IMG path&gt;</em>.<br/><strong><em><br/>&lt;&lt;Variant 2 using ILM rule groups: &gt;&gt;<br/></em></strong>To map application-specific condition fields to application rule variants, you assign ILM rule groups to application rule variants. You can define ILM rule groups for the ILM objects of <em>&lt;application description&gt;</em> in transaction IRM_CUST_CSS. You also assign them to the retention rules maintained for these ILM objects in transaction IRMPOL. The following ILM objects are relevant for the application <em>&lt;ZZn&gt;</em>:<br/>\n<ul>\n<li><em>&lt;ILM object 1&gt;</em></li>\n<li><em>&lt;ILM object 2&gt;</em></li>\n</ul>\n</li>\n<li><strong><em>&lt;&lt;Describe here for each application name which EoP check classes are delivered including the check logic for blocking a Customer or Vendor or Contact Person. &gt;&gt;</em></strong><br/>The application <em>&lt;ZZ&gt;</em> delivers the following classes registered for the end of purpose check of customer and vendor master:<br/>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"0\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td>\n<p>ID Type</p>\n</td>\n<td>\n<p>Application Name</p>\n</td>\n<td>\n<p>Registered Class for EoP Checks</p>\n</td>\n<td>\n<p>General</p>\n</td>\n<td>\n<p>Comp. Code</p>\n</td>\n</tr>\n<tr>\n<td>\n<p>&lt;1 - Customer Master Data&gt;</p>\n</td>\n<td>\n<p>&lt;ZZ1&gt;</p>\n</td>\n<td>\n<p>&lt;ZZ1 class&gt;</p>\n</td>\n<td>\n<p>&lt;'X'&gt;</p>\n</td>\n<td>\n<p>&lt;'X'&gt;</p>\n</td>\n</tr>\n<tr>\n<td>\n<p>&lt;2 - Vendor Master Data&gt;</p>\n</td>\n<td>\n<p>&lt;ZZ2&gt;</p>\n</td>\n<td>\n<p>&lt;ZZ2 class &gt;</p>\n</td>\n<td>\n<p>&lt;'X'&gt;</p>\n</td>\n<td>\n<p>&lt;'X'&gt;</p>\n</td>\n</tr>\n<tr>\n<td>\n<p>&lt;3 – Contact Person&gt;</p>\n</td>\n<td>\n<p>&lt;ZZ3&gt;</p>\n</td>\n<td>\n<p>&lt;ZZ3 class &gt;</p>\n</td>\n<td>\n<p>&lt;'X'&gt;</p>\n</td>\n<td>\n<p>&lt;'X'&gt;</p>\n</td>\n</tr>\n</tbody>\n</table></div>\n<ul>\n<li>Class <em>&lt;ZZ1 class&gt; -</em> <em>&lt;short description&gt;</em><br/><em>&lt;description of &lt;ZZ1 class&gt; including the check logic &gt;</em></li>\n<li>Class <em>&lt;ZZ2 class&gt; -</em> <em>&lt;short description&gt;</em><br/><em>&lt;description of &lt;ZZ2 class&gt; including the check logic &gt;</em></li>\n<li><em>&lt;…&gt;</em></li>\n</ul>\n</li>\n<li><strong><strong><em>&lt;&lt;Describe here generically how the application reacts on a blocked Customer or Vendor. &gt;&gt;<br/></em></strong></strong><em>When a customer, vendor or contact person is blocked, the application does not display any personal data related to this customer, vendor or contact person including the number of the customer, vendor or contact person. You cannot display or change any information. You cannot create new business with this customer, vendor or contact person.</em></li>\n</ol></div>", "noteVersion": 1}, {"note": "2103639", "noteTitle": "2103639 - End of Purpose Check Adaption for Business Partner Consuming Applications – Guide for Partners and Customers", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>SAP software supports the simplified blocking and deletion of personal data. Note 1825544 describes how the use of SAP Information Lifecycle Management (ILM) supports the simplified blocking and deletion of personal data in SAP Business Suite.</p>\n<p>Simplified blocking and deletion of central business partner is described in note 1825608, of customer / vendor master data in note 2007926 and of SCM locations in note 2249093.</p>\n<p><span 12pt;=\"\" ar-sa;\"=\"\" arial','sans-serif';=\"\" calibri;=\"\" de;=\"\" en-us;=\"\" font-size:=\"\" lang=\"EN-US\" minor-latin;=\"\" mso-ansi-language:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" mso-fareast-theme-font:=\"\">Any Application using customer / vendor master data, SCM location or central business partner must be enabled for the End of Purpose Check. SAP Partner and Customers with own ABAP applications have to implement in own responsibility. The attached guide is dedicated to provide all required information to implement.</span></p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>EOP, Information Lifecycle Management, ILM, Data Privacy, Deletion, Blocking, Personal Data, Business Partner, BP, cBP, CA_BUPA, BUPA_DP, Customer, Vendor, Supplier, FI_ACCRECV, FI_ACCPAYB, FI_ACCKNVK, SCMB_LOC</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>See SAP Note 1825544</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>The solution is available by the attached guide for partners and customers: END OF PURPOSE CHECK ADAPTION FOR BUSINESS PARTNER CONSUMING APPLICATIONS<br/>The guide is valid starting with release SAP ERP 6.0 EhP7 SP05, SCM_BASIS 7.0 EhP4 SP03 and SAP CRM EhP3 SP05 based on SAP NetWeaver 7.40.</p>\n<p>The guide provides a technical overview of the available business partner master data blocking functionality and the interfaces to be considered for the creation of an end of purpose check for an application. It includes information about the necessary adaptions in the corresponding functionality for handling blocked business partners.</p>\n<p>An S/4HANA specific version of the guide starting with release <span>SAP S/4HANA 1610 is linked in the product assistance information as part of the data protection chapter and can be accessed via the following link: <a href=\"https://uacp2.hana.ondemand.com/doc/5eb4c4832d054542b7a426271bbd34a3/1610%20000/en-US/End%20of%20Purpose%20Check%20Adaption%20for%20Applications%20Consuming%20SAP%20Business%20Partner%20in%20SAP%20S4HANA.pdf\" target=\"_blank\">https://uacp2.hana.ondemand.com/doc/5eb4c4832d054542b7a426271bbd34a3/1610%20000/en-US/End%20of%20Purpose%20Check%20Adaption%20for%20Applications%20Consuming%20SAP%20Business%20Partner%20in%20SAP%20S4HANA.pdf</a></span></p>", "noteVersion": 6}, {"note": "2013770", "noteTitle": "2013770 - Plant and Site Master related where-used-check before blocking of customer or vendor data", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>Site master or plant master data may be related to a vendor or customer. This note describes the changes preventing blocking of customers or vendors which are still assigned to sites or plants.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Deletion, Blocking, Personal Data, EOP</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<div class=\"longtext\">\n<p>See note 2007926</p>\n</div>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>The new functionality for applications Site- and Plant Master will be available with release SAP_APPL 617 and support package SP05.<br/><br/></p>\n<p>The new functionality for application Material Master will be available with release SAP_APPL 617 and support package SP05.<br/><br/>The functionality consists of the following main features:</p>\n<ol>\n<li>In customizing \"Customer Master /Vendor Master Deletion\" the following entries are added:</li>\n<ol>\n<li>In the activity \"Define and Store Application Names for EoP Check\" the following entry was added:<br/>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"3\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td>ID Type</td>\n<td>Application Name</td>\n<td>Application Description</td>\n</tr>\n<tr>\n<td>1 - Customer Master Data</td>\n<td>ERP_PLANT_SITE</td>\n<td>Plant- and Sitemaster</td>\n</tr>\n<tr>\n<td>2 - Vendor Master Data</td>\n<td>ERP_PLANT_SITE</td>\n<td>Plant- and Sitemaster</td>\n</tr>\n</tbody>\n</table></div>\n</li>\n<li>In the activity \"Define Application Classes Registered for EoP Check\" the following entry was added:<br/>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"3\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td>ID Type </td>\n<td>Application Name</td>\n<td>Registeres Class for EoP Checks</td>\n<td>General</td>\n<td>Comp. Code</td>\n</tr>\n<tr>\n<td>1 - Customer Master Data</td>\n<td>ERP_PLANT_SITE</td>\n<td>CL_T001W_WUC</td>\n<td>X</td>\n<td>X</td>\n</tr>\n<tr>\n<td>2 - Vendor Master Data</td>\n<td>ERP_PLANT_SITE</td>\n<td>CL_T001W_WUC</td>\n<td>X</td>\n<td>X</td>\n</tr>\n</tbody>\n</table></div>\n</li>\n</ol>\n<li>\n<p>The application Material Master delivers the following class registered for the end of purpose check of the vendor:<br/> CL_T001W_WUC - \"Where-Used-Check Material Master\"</p>\n</li>\n</ol>", "noteVersion": 2}, {"note": "2011592", "noteTitle": "2011592 - Batch Master related where-used-check before blocking of vendor data", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>The Batch Master contains data, which is related to the vendor. This note describes the changes, which will prevent blocking of a vendor, if this vendor is still assigned as manufacturer to batch master.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Deletion, Blocking, Personal Data, EOP, Vendor, MCHA, MCH1, WUC, MCHA-LIFNR, MCH1-LIFNR</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>See note 2007926</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>The new functionality for application Batch Master will be available with release SAP_APPL 617 and support package SP05.<br/><br/>The functionality consists of the following main features:</p>\n<ol>\n<li>In customizing \"Customer Master /Vendor Master Deletion\" the following entries are added:</li>\n<ol>\n<li>In the activity \"Define and Store Application Names for EoP Check\" the following entry was added:<br/>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"3\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td>ID Type</td>\n<td>Application Name</td>\n<td>Application Description</td>\n</tr>\n<tr>\n<td>2 - Vendor Master Data</td>\n<td>ERP_LO_BM_MD</td>\n<td>Batch Master</td>\n</tr>\n</tbody>\n</table></div>\n</li>\n<li>In the activity \"Define Application Classes Registered for EoP Check\" the following entry was added:<br/>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"3\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td>ID Type</td>\n<td>Application Name</td>\n<td>Registered Class for EoP Checks</td>\n<td>General</td>\n<td>Comp.Code</td>\n</tr>\n<tr>\n<td>2 - Vendor Master Data</td>\n<td>ERP_LO_BM_MD</td>\n<td>CL_WUC_LO_BM_MD_EOP_CHECK</td>\n<td>'X'</td>\n<td>'X'</td>\n</tr>\n</tbody>\n</table></div>\n</li>\n</ol>\n<li>\n<p>The application Material Master delivers the following class registered for the end of purpose check of the vendor:<br/> CL_WUC_LO_BM_MD_EOP_CHECK - \"Where-Used-Check Batch Master\"</p>\n</li>\n<li>In case the performance of this where-used-check needs to be improved the Index CV of table MCHA and MCH1 can be activated.</li>\n</ol>", "noteVersion": 2}, {"note": "2018774", "noteTitle": "2018774 - ED & PL: WUC Where-used check for KUNNR, LIFNR, PARNR", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>The excise duty (MM-IM-ED) and pendulum list (SD-BIL-RB-PL) applications contain data that refers to customers, vendors or partners.</p>\n<p>For each application, an implementation is now delivered that prevents the blocking of a customer, vendor or partner, as long as they are still used in relevant tables of the excise duty and/or pendulum list applications.</p>\n<p> </p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Beverage, excise duty, pendulum list; <br/>deletion, blocking, master data, personal data, EOP end-of-purpose; <br/>customer INKDN KUNNR, sold-to party, ship-to party WEMPF KUNWE, bill-to party KUNRE, payer KUNRG, wholesaler distributor VERL /BEV4/PLVERL, object OBJN /BEV4/PLOBJN, vendor LIFNR, partner PARNR .</p>\n<p> </p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>See Note 2007926.</p>\n<p> </p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Delivery is via Support Package EA-APPL 617 SP05 (= SAPK-61705INEAAPPL), not as correction instructions in the SAP Note.</p>\n<p>The functionality consists of the following main elements:</p>\n<ol>\n<li>In Customizing \"Customer/Vendor Master Data Deletion\", the following entries are added:</li>\n<ol>\n<li>In the IMG activity \"Definition and Saving of Application Names for the EOP Check\", the following three entries were added:<br/>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"3\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td><span>ID type</span></td>\n<td><span>Application name</span></td>\n<td><span>Application description</span></td>\n</tr>\n<tr>\n<td>1 - Customer Master Data</td>\n<td>ERP_MM_IM_ED</td>\n<td>ERP Excise Duties Master data Consignee</td>\n</tr>\n<tr>\n<td>2 - Vendor Master Data</td>\n<td>ERP_MM_IM_ED</td>\n<td>ERP Excise Duties Master data Vendor</td>\n</tr>\n<tr>\n<td>1 - Customer Master Data</td>\n<td>ERP_SD_BIL_RB_PL</td>\n<td>ERP Pendulum List Master data Wholesaler/Object/Customer</td>\n</tr>\n</tbody>\n</table></div>\n</li>\n<li>In the IMG activity \"Registration of Application Classes for the EOP Check\", the following three entries were added:<br/>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"3\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td><span>ID type</span></td>\n<td><span>Application name</span></td>\n<td><span>Registered class for the EOP check</span></td>\n<td><span>General</span></td>\n<td><span>Comp.code</span></td>\n</tr>\n<tr>\n<td>1 - Customer Master Data</td>\n<td>ERP_MM_IM_ED</td>\n<td>/BEV2/CL_IM_ED_WUC_EOP_CHECK  </td>\n<td>'X'</td>\n<td>'X'</td>\n</tr>\n<tr>\n<td>2 - Vendor Master Data</td>\n<td>ERP_MM_IM_ED</td>\n<td>/BEV2/CL_IM_ED_WUC_EOP_CHECK  </td>\n<td>'X'</td>\n<td>'X'</td>\n</tr>\n<tr>\n<td>1 - Customer Master Data</td>\n<td>ERP_SD_BIL_RB_PL</td>\n<td>/BEV4/CL_IM_PL_WUC_EOP_CHECK  </td>\n<td>'X'</td>\n<td>'X'</td>\n</tr>\n</tbody>\n</table></div>\n</li>\n</ol>\n<li>\n<p>The excise duty and pendulum list applications return the following implementing classes that are registered for the EOP check for customer and vendor:<br/>/BEV2/CL_IM_ED_WUC_EOP_CHECK - \"Imp. class for interf. CVP_IF_APPL_EOP_CHECK (WUC)\" <br/>/BEV4/CL_IM_PL_WUC_EOP_CHECK - \"Imp. class for interf. CVP_IF_APPL_EOP_CHECK (WUC)\"</p>\n</li>\n</ol>", "noteVersion": 2}, {"note": "2027223", "noteTitle": "2027223 - Do not use data blocking for vendor master data if you are using HCM payroll localizations of US, Canada, Russia or Kazakhstan", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>This note describes the behavior in HCM related to simplified blocking and deletion of customer or vendor master based on SAP Information Lifecycle Management (ILM) as described in SAP Note 2007926.</p>\n<p>You are using SAP solutions where you would like to block information for those vendors for whom end of purpose conditions are met as per standard data privacy guidelines. You are using SAP HCM payroll localization solution for one of these countries as well: US, Canada, Russia, Kazakhstan.</p>\n<p>These HCM payroll localization solutions use vendor information for some of the country specific funtionalities (Garnishments, for example). But they do not provide end-of-purpose (EOP) check and where-used-check (WUC). So if you opt to block certain vendor information using data blocking solution, these functionalities may not work correctly after data blocking. These functionalities will not be able to stop the data blocking of vendor information even if this information is required for their correct working.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Data blocking, WUC, EOP, where used check, end of purpose check, garnishment, RHC4, HRCA_CREDITOR_GETDETAIL, HRCA_DEBTOR_GETDETAIL, vendor blocking</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>Technical limitations in implementing WuC checks.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Till the time SAP HCM payroll localization solution is enabled for WUC or EOP, you cannot safely use data blocking solution for vendor information. This Note shall be updated as and when there is an update on this topic.</p>", "noteVersion": 2}, {"note": "2011507", "noteTitle": "2011507 - Where used check for the customer and vendor data in ICM", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>When a customer or a vendor is to be blocked, the blocking report will check if the customer / vendor is used by any of the applications. The WUC will check if the particular customer/Vendor number resides in any of the ICM table.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>CACS_STMRU, CACS00_DOCFI_P, PAYEE_FI, LIFNR, WUC, Blocking.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>Blocking of customer/vendor. Refer note 2007926 and 2022899.</p>\n<div dir=\"ltr\" id=\"imcontent\"></div>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>The new functionality for application ICM is available as of EhP7, Support package 05. ICM provides CL_CACS_WUC_CHECK_VEND and CL_CACS_WUC_CHECK_CUST for the customer vendor. The check consists of the following main features.</p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"3\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td>ID type</td>\n<td>Application Name</td>\n<td>Application description</td>\n</tr>\n<tr>\n<td>Customer master data</td>\n<td>CACS_CUSTVEND</td>\n<td>ICM Application for customer</td>\n</tr>\n<tr>\n<td>Vendor Master data</td>\n<td>CACS_CUSTVEND</td>\n<td>ICM Application for vendor</td>\n</tr>\n</tbody>\n</table></div>", "noteVersion": 2}, {"note": "2011073", "noteTitle": "2011073 - FI documents: Blocking customers and vendors", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>The FI system contains documents that have a relation to a customer or vendor (that is, customer documents or vendor documents).</p>\n<p>This SAP Note describes the changes that enable the simplified deletion and blocking of customer master data or vendor master data based on SAP Information Lifecycle Management (ILM). SAP Note 2007926 contains the description for this.</p>\n<p>This SAP Note is also provided as a technical prerequisite for other corrections. It does not deliver any function.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Data protection, ILM, SAP Information Lifecycle Management, delete, deletion, block, personal data, person-related data, customer, vendor, SAPMF05A, SAPMF05R, SAPMF05L, SAPLFCPD, FI_ACCPAYB, FI_ACCRECV, FB03, FB02, FBRA, FBR2, EoP, end of purpose</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>See SAP Note 2007926.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>The new functions are available as of: <br/>SAP_FIN, Release 617, Support Package 5<br/><br/>The FI system offers an end of purpose check for the customer or vendor. This check consists of the following main characteristics:</p>\n<p>If a customer or vendor is blocked, the application does not display any personal data of this customer or vendor including the customer number or vendor number. You cannot display or change any data. In addition, you cannot post any new documents with this customer or vendor. When you post a document with reference, the system ignores lines of such a blocked customer or vendor and does not copy them from the reference document.</p>\n<p>The lock logic applies to normal master records, one-time documents and documents for alternative payers and alternative payees. You can also not reset clearing documents that have been made for accounts that have been blocked in the meantime. You cannot use blocked master data in one-time account items as a copy template. The system issues corresponding messages (F5A451, F5351, F5A452).</p>\n<p>Release 617: The simplified deletion is implemented via the archiving object FI_DOCUMNT with the corresponding ILM connection (ILM object FI_DOCUMNT).</p>", "noteVersion": 1}, {"note": "2139142", "noteTitle": "2139142 - Deletion and Blocking of Customer in Spain VAT(M347) reporting", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>The application Spain VAT(M347) reporting contains data that is related to a customer. This SAP Note describes the changes in the Spain VAT reporting application that enable the simplified blocking and deletion of customer master data based on SAP Information Lifecycle Management (ILM) as described in SAP Note 2007926.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Data Privacy, Information Lifecycle Management, ILM, Deletion, Blocking, Personal Data, Customer, Vendor, FI_ACCPAYB, FI_ACCRECV, FI_ACCKNVK, End of Purpose check, EoP, ES VAT, Spain M347, Incash transaction.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>See SAP Note 2007926.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>The new functionality for application Spain VAT(M347) reporting is available as of SAP_FIN 617, Support Package 08.</p>\n<p>Spain VAT(M347) reporting application provides an end of purpose check for the customer. This check consists of the following main features:</p>\n<ul>\n<li>In the standard system, the following application names are delivered for Spain VAT reporting:</li>\n</ul>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"0\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td>\n<p>ID Type</p>\n</td>\n<td>\n<p>Application Name</p>\n</td>\n<td>\n<p>Application Description</p>\n</td>\n</tr>\n<tr>\n<td>\n<p>1 - Customer Master Data</p>\n</td>\n<td>\n<p>ERP_FI_LOC_ES_CASH</p>\n</td>\n<td>\n<p>Spain 347 Incash Reporting</p>\n</td>\n</tr>\n</tbody>\n</table></div>\n<p> </p>\n<ul>\n<li>Spain VAT reporting application delivers the following class registered for the end of purpose check of customer master:</li>\n</ul>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"0\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td>\n<p>ID Type</p>\n</td>\n<td>\n<p>Application Name</p>\n</td>\n<td>\n<p>Registered Class for EoP Checks</p>\n</td>\n<td>\n<p>General</p>\n</td>\n<td>\n<p>Comp.Code</p>\n</td>\n</tr>\n<tr>\n<td>\n<p>1 - Customer Master Data</p>\n</td>\n<td>\n<p>ERP_FI_LOC_ES_CASH</p>\n</td>\n<td>\n<p>CL_FIN_LOC_ES_CASH_EOP_CUST</p>\n</td>\n<td>\n<p>' '</p>\n</td>\n<td>\n<p>'X'</p>\n</td>\n</tr>\n</tbody>\n</table></div>\n<p> The EoP class checks for the Spain VAT(M347) transaction of the customer with respect to the company code for the current fiscal year. If there are no transactions, then the status is updated as 'no Business made with partner at all' for the partners. Additionally, for all other customers being checked, if any already reported transactions exist then the status is updated as 'Business is complete and Residence time is over' along with the SORT date if there are no references of the customer by other customers.</p>\n<p>The status 'No (Business is ongoing with partner)' is set for the customer when this customer is referred by other customers.</p>\n<ul>\n<li>When a customer is blocked, the application does not display any personal data related to this customer including the number of the customer. You cannot display or change any information. You cannot create new business with this customer. </li>\n</ul>", "noteVersion": 1}, {"note": "2013944", "noteTitle": "2013944 - Requests: Restrict access to personal data", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>With this note the request transactions supports the restrict access to personal data.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>data protection, personal data</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>Please implement the prerequisite notes 2007926 and 2004024.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Please implement the attached correction instruction.</p>", "noteVersion": 2}, {"note": "2012303", "noteTitle": "2012303 - Price Catalogue related where-used-check before blocking of vendor / customer data", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>The Price Catalogue application contains data, which is related to the vendor or customer. This note describes the changes, which will prevent blocking of a vendor or customer, if this vendor or customer is still used by the application.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Deletion, Blocking, Personal Data, WUC, Vendor, Customer</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>See note 2007926</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>The new functionality for application Price Catalogue will be available with release EA-RETAIL 617 and support package SP05.</p>\n<p>The Price Catalogue application provides a where-used check for the customer / vendor data.<br/><br/>The functionality consists of the following main features:</p>\n<ol>\n<li>In customizing \"Customer Master /Vendor Master Deletion\" the following entries are added:<ol>\n<li>In the activity \"Define and Store Application Names for EoP Check\" the following entry was added:<br/>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"3\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td>ID Type</td>\n<td>Application Name</td>\n<td>Application Description</td>\n</tr>\n<tr>\n<td>1 - Customer Master Data</td>\n<td>PRICAT</td>\n<td>Price Catalogue</td>\n</tr>\n<tr>\n<td>2 - Vendor Master Data</td>\n<td>PRICAT</td>\n<td>Price Catalogue</td>\n</tr>\n</tbody>\n</table></div>\n</li>\n</ol><ol start=\"2\">\n<li>In the activity \"Define Application Classes Registered for EoP Check\" the following entry was added:<br/>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"3\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td>ID Type</td>\n<td>Application Name</td>\n<td>Registered Class for EoP Checks</td>\n<td>General</td>\n<td>Copm. Code</td>\n</tr>\n<tr>\n<td>1- Customer Master Data</td>\n<td>PRICAT</td>\n<td>CL_PRICAT_EOP_CHECK_CV</td>\n<td>X</td>\n<td>X</td>\n</tr>\n<tr>\n<td>2 - Vendor Master Data</td>\n<td>PRICAT</td>\n<td>CL_PRICAT_EOP_CHECK_CV</td>\n<td>X</td>\n<td>X</td>\n</tr>\n</tbody>\n</table></div>\n</li>\n</ol></li>\n<li>\n<p>The application Price Catalogue delivers the following class registered for the end of purpose check of the vendor and customer:<br/>CL_PRICAT_EOP_CHECK_CV- \"Price Catalogue EOP check for CV\"</p>\n</li>\n</ol>", "noteVersion": 2}, {"note": "2100243", "noteTitle": "2100243 - Deletion and Blocking of Customer and Vendor in Slovakia VAT reporting", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>The application Slovakia VAT reporting contains data that is related to a customer or a vendor. This SAP Note describes the changes in the Slovakia VAT reporting application that enable the simplified blocking and deletion of customer or vendor master data based on SAP Information Lifecycle Management (ILM) as described in SAP Note 2007926.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Data Privacy, Information Lifecycle Management, ILM, Deletion, Blocking, Personal Data, Customer, Vendor, FI_ACCPAYB, FI_ACCRECV, FI_ACCKNVK, End of Purpose check, EoP, SK VAT, Slovakia</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>See SAP Note 2007926</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>The new functionality for application Slovakia VAT reporting is available as of SAP_FIN 617, Support Package 07.</p>\n<p>Slovakia VAT reporting application provides an end of purpose check for the customer or vendor. This check consists of the following main features:</p>\n<ul>\n<li>In the standard system, the following application names are delivered for Slovakia VAT reporting:</li>\n</ul>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"0\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td>\n<p>ID Type</p>\n</td>\n<td>\n<p>Application Name</p>\n</td>\n<td>\n<p>Application Description</p>\n</td>\n</tr>\n<tr>\n<td>\n<p>1 - Customer Master Data</p>\n</td>\n<td>\n<p>ERP_FI_LOC_SK_VAT</p>\n</td>\n<td>\n<p>Slovakia VAT Reporting</p>\n</td>\n</tr>\n<tr>\n<td>\n<p>2 - Vendor Master Data</p>\n</td>\n<td>\n<p>ERP_FI_LOC_SK_VAT</p>\n</td>\n<td>\n<p>Slovakia VAT Reporting</p>\n</td>\n</tr>\n</tbody>\n</table></div>\n<p> </p>\n<ul>\n<li>Slovakia VAT reporting application delivers the following classes registered for the end of purpose check of customer and vendor master:</li>\n</ul>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"0\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td>\n<p>ID Type</p>\n</td>\n<td>\n<p>Application Name</p>\n</td>\n<td>\n<p>Registered Class for EoP Checks</p>\n</td>\n<td>\n<p>General</p>\n</td>\n<td>\n<p>Comp.Code</p>\n</td>\n</tr>\n<tr>\n<td>\n<p>1 - Customer Master Data</p>\n</td>\n<td>\n<p>ERP_FI_LOC_SK_VAT</p>\n</td>\n<td>\n<p>CL_FIN_LOC_SK_EOP_CUST_CHECK</p>\n</td>\n<td>\n<p>' '</p>\n</td>\n<td>\n<p>'X'</p>\n</td>\n</tr>\n<tr>\n<td>\n<p>2 - Vendor Master Data</p>\n</td>\n<td>\n<p>ERP_FI_LOC_SK_VAT</p>\n</td>\n<td>\n<p>CL_FIN_LOC_SK_EOP_VEND_CHECK</p>\n</td>\n<td>\n<p>' '</p>\n</td>\n<td>\n<p>'X'</p>\n</td>\n</tr>\n</tbody>\n</table></div>\n<p> The EoP class's checks for the Slovakia VAT transactions of the customer/vendor of the company code for the current fiscal year. If there are transactions, then the status is updated as 'no Business made with partner at all' for the partners. Additionally, for all other customer and vendors being checked, if any already reported transactions exist then the status is updated as 'Business is complete and Residence time is over' along with the SORT date.</p>\n<ul>\n<li>When a customer or vendor is blocked, the application does not display any personal data related to this customer or vendor including the number of the customer or vendor. You cannot display or change any information. You cannot create new business with this customer or vendor.</li>\n</ul>", "noteVersion": 1}, {"note": "2131018", "noteTitle": "2131018 - Deletion and Blocking of Customer and Vendor in the FI Localization for Saudi Arabia", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>The FI Localization for Saudi Arabia contains data that is related to a customer or a vendor. This note describes the changes in the FI Localization for Saudi Arabia that enable the simplified blocking and deletion of customer or vendor master data based on SAP Information Lifecycle Management (ILM) as described in SAP Note 2007926.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Data Privacy, Information Lifecycle Management, ILM, Deletion, Blocking, Personal Data, Customer, Vendor, FI_ACCPAYB, EoP</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>See SAP Note 2007926</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>The new functionality for the FI Localization for Saudi Arabia is available as of SAP_FIN 617, Support Package 08.</p>\n<p>The FI Localization for Saudi Arabia provides an end of purpose check for the vendor. This check consists of the following main features:</p>\n<ul>\n<li>In the standard system, the following application names are delivered for the FI Localization for Saudi Arabia :</li>\n</ul>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"0\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td>\n<p>ID Type</p>\n</td>\n<td>\n<p>Application Name</p>\n</td>\n<td>\n<p>Application Description</p>\n</td>\n</tr>\n<tr>\n<td>\n<p>2 - Vendor Master Data</p>\n</td>\n<td>\n<p>ERP_FI_LOC_SAU_WT</p>\n</td>\n<td>\n<p>Vendor certificate (SAU)</p>\n</td>\n</tr>\n</tbody>\n</table></div>\n<ul>\n<li>The FI Localization for Saudi Arabia delivers the following classes registered for the end of purpose check of the vendor master data:</li>\n</ul>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"0\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td>\n<p>ID Type</p>\n</td>\n<td>\n<p>Application Name</p>\n</td>\n<td>\n<p>Registered Class for EoP Checks</p>\n</td>\n<td>\n<p>General</p>\n</td>\n<td>\n<p>Comp.Code</p>\n</td>\n</tr>\n<tr>\n<td>\n<p>2 - Vendor Master Data</p>\n</td>\n<td>\n<p>ERP_FI_LOC_SAU_WT</p>\n</td>\n<td>\n<p>CL_FIN_LOC_SAWTCERT_EOP</p>\n</td>\n<td>\n<p>'X'</p>\n</td>\n<td>\n<p>'X'</p>\n</td>\n</tr>\n</tbody>\n</table></div>\n<ul>\n<li>When a customer or vendor is blocked, the application does not display any personal data related to this customer or vendor, including the number of the customer or vendor. You cannot display or change any information. You cannot create new business with this customer or vendor.</li>\n</ul>", "noteVersion": 2}, {"note": "2014950", "noteTitle": "2014950 - Credit management: Locking of customers", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p><em>Credit management</em> contains data with relation to a customer or vendor.</p>\n<p>This SAP Note describes the changes in credit management that enable the simplified deletion and locking of customer master data based on SAP Information Lifecycle Management (ILM). SAP Note 2007926 contains the description for this.</p>\n<p>This SAP Note is also provided as a technical prerequisite for other corrections. It does not deliver any function.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Data protection, ILM, SAP Information Lifecycle Management, delete, deletion, lock, personal data, person-related data, customer, vendor, FI_ACCPAYB, FI_ACCRECV, FI_ACCKNVK, EoP, end of purpose</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>See SAP Note 2007926.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>The new functions for credit management are available as of: <br/>SAP_FIN, Release 617, Support Package 5<br/><br/>Credit management offers an end of purpose check for the customer. This check consists of the following main characteristics:</p>\n<p>If a customer is locked, the application does not display any personal data of this customer including the customer number. You cannot display or change any data. You can also not create any new business with this customer.</p>", "noteVersion": 2}, {"note": "2008603", "noteTitle": "2008603 - Central functions: Locking customer and vendor", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>The FI system contains data with relation to a customer or vendor.<br/>This SAP Note summarizes central FI functions that are used by FI (but also by other applications).<br/><br/>See SAP Note 2007926 for a description of the general functions.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Central includes, SAP_FIN</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>See SAP Note 2007926.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Import the relevant Support Package.<br/>The corrections are delivered only via SP (EHP7, 617 =&gt; SP05).</p>", "noteVersion": 1}, {"note": "2018350", "noteTitle": "2018350 - IS OIL related where-used-check before blocking of customer and vendor data", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>The application IS OIL can contain data, which is related to a customer or vendor. This note describes the changes, which will prevent blocking of a customer, if this customer is still in use. In addition these changes will prevent blocking of a vendor, if this vendor is still in use.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Deletion, Blocking, Personal Data, EOP, Customer, Vendor, KUNNR</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>See note 2007926</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>The new functionality for application Material Master will be available with release SAP_APPL 617 and support package SP05.<br/><br/>The functionality consists of the following main features:</p>\n<ol>\n<li>In customizing \"Customer Master /Vendor Master Deletion\" the following entries are added:</li>\n<ol>\n<li>In the activity \"Define and Store Application Names for EoP Check\" the following entry was added:<br/>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"3\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td>ID Type</td>\n<td>Application Name</td>\n<td>Application Description</td>\n</tr>\n<tr>\n<td>1 - Customer Master Data</td>\n<td>IS-OIL</td>\n<td>Oil Downstream Customer</td>\n</tr>\n<tr>\n<td>2 - Vendor Master Data</td>\n<td>IS-OIL</td>\n<td>Oil Downstream Vendor</td>\n</tr>\n</tbody>\n</table></div>\n</li>\n<li>In the activity \"Define Application Classes Registered for EoP Check\" the following entry was added:<br/>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"3\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td>ID Type</td>\n<td>Application Name</td>\n<td>Registered Class for EoP Checks</td>\n<td>General</td>\n<td>Comp.Code</td>\n</tr>\n<tr>\n<td>1 - Customer Master Data</td>\n<td>IS-OIL</td>\n<td>CVP_OIL_EOP_CHECK</td>\n<td>'X'</td>\n<td>'X'</td>\n</tr>\n<tr>\n<td>2 - Vendor Master Data</td>\n<td>IS-OIL</td>\n<td>CVP_OIL_EOP_CHECK</td>\n<td>'X'</td>\n<td>'X'</td>\n</tr>\n</tbody>\n</table></div>\n</li>\n</ol>\n<li>\n<p>The application IS OIL delivers the following class registered for the end of purpose check of the customer and the vendor:<br/> CVP_OIL_EOP_CHECK</p>\n</li>\n</ol>", "noteVersion": 1}, {"note": "2015907", "noteTitle": "2015907 - EH&S related where-used-check before blocking of central business partner, customer or vendor data", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>The following applications can contain data, which is related to a central business partner, customer or vendor:</p>\n<p>Product Safety (EHS-PS)</p>\n<ul>\n<li>Report Shipping</li>\n<li>Substance Volume Tracking</li>\n</ul>\n<p>Waste Management (EHS-WA)</p>\n<ul>\n<li>Master Data, Partner</li>\n</ul>\n<p>Industrial Hygiene and Safety (EHS-IHS)</p>\n<ul>\n<li>\n<p>Incident Management</p>\n</li>\n<li>\n<p>Measurement Management</p>\n</li>\n<li>\n<p>Risk Assessment</p>\n</li>\n<li>\n<p>Work Area Management</p>\n</li>\n</ul>\n<p>Occupational Health (EHS-HEA)</p>\n<ul>\n<li>\n<p>Injury/Illness Log</p>\n</li>\n<li>\n<p>Medical Service</p>\n</li>\n</ul>\n<p>This note describes the changes, which will prevent blocking of central business partner, customer or vendor, if this central business partner, customer or vendor is still assigned in one of the listed applications.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Deletion, Blocking, Personal Data, EOP, Central Business Partner, Customer, Vendor, CBRC01, WUC, CG50, CG54,</p>\n<p>CCRCT_OR-VENDOR_EXT, CCRCT_OR-OR_EXT, CCRCT_CU-OR_EXT, CCRCT_CUPOS-KUNNR, CCRCT_SO-OR_EXT, CCRCT_SOPL-OR_EXT</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>Customer/vendor: see note 2007926</p>\n<p>Central business partner: see note 1825608</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>For the previous listed applicantions the new functionality will be available with release EA-APPL 617 and support package SP05:<br/><br/>The functionality consists of the following main features:</p>\n<p><span>Prevent blocking of a customer or vendor if this customer or vendor is still used.</span></p>\n<ol>\n<li>In customizing \"Customer Master /Vendor Master Deletion\" the following entries were added:</li>\n<ol>\n<li>In the activity \"Define and Store Application Names for EoP Check\" the following entries were added:<br/>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"3\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td>ID Type                              </td>\n<td>Application Name   </td>\n<td>Application Description</td>\n</tr>\n<tr>\n<td>1 - Customer Master Data</td>\n<td>EHS_PS_REP</td>\n<td>EHS Product Safety, Report Shipping</td>\n</tr>\n<tr>\n<td>1 - Customer Master Data</td>\n<td>EHS_WA_MD</td>\n<td>EHS Waste Management – Master Data (Partners)</td>\n</tr>\n<tr>\n<td>2 - Vendor Master Data</td>\n<td>EHS_PS_SVT</td>\n<td>EHS Product Safety, Substance Volume Tracking</td>\n</tr>\n<tr>\n<td>2 - Vendor Master Data</td>\n<td>EHS_WA_MD</td>\n<td>EHS Waste Management – Master Data (Partners)</td>\n</tr>\n</tbody>\n</table></div>\n</li>\n<li>In the activity \"Define Application Classes Registered for EoP Check\" the following entries were added:<br/>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"3\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td>ID Type                               </td>\n<td>Application Name    </td>\n<td>Registered Class for EoP Checks</td>\n<td>General </td>\n<td>Comp.Code </td>\n</tr>\n<tr>\n<td>1 - Customer Master Data</td>\n<td>EHS_PS_REP</td>\n<td>CL_EHS_REPSHIP_CUST_EOP_CHECK  </td>\n<td>'X'</td>\n<td>'X'</td>\n</tr>\n<tr>\n<td>1 - Customer Master Data</td>\n<td>EHS_WA_MD</td>\n<td>CL_EHSW_BUPT_EOP_CHECK_MD</td>\n<td>'X'</td>\n<td>'X'</td>\n</tr>\n<tr>\n<td>2 - Vendor Master Data</td>\n<td>EHS_PS_SVT</td>\n<td>CL_EHS_SVT_VENDOR_EOP_CHECK</td>\n<td>'X'</td>\n<td>'X'</td>\n</tr>\n<tr>\n<td>2 - Vendor Master Data</td>\n<td>EHS_WA_MD</td>\n<td>CL_EHSW_BUPT_EOP_CHECK_MD</td>\n<td>'X'</td>\n<td>'X'</td>\n</tr>\n</tbody>\n</table></div>\n</li>\n</ol>\n<li>\n<p>EH&amp;S applications deliver the following classes registered for the end of purpose check of the customer and the vendor:<br/>- CL_EHS_REPSHIP_CUST_EOP_CHECK<br/>- CL_EHS_SVT_VENDOR_EOP_CHECK<br/>- CL_EHSW_BUPT_EOP_CHECK_MD</p>\n</li>\n</ol>\n<p><span>Prevent blocking of a central business partner if this central business partner is still used.</span></p>\n<ol>\n<li>In customizing <em>Cross-Application Components &gt; Data Protection-&gt; Blocking and Unblocking of Data &gt; Business Partner </em>the following entries were added:</li>\n</ol>\n<ul>\n<li>In the activity “Define and Store Application Names for EoP Check” the following entries were added:</li>\n</ul>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"0\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td>\n<p>Application Name</p>\n</td>\n<td>\n<p>Application Description</p>\n</td>\n</tr>\n<tr>\n<td>\n<p>EHS_IHS_IA</p>\n</td>\n<td>\n<p>EHS INDUSTRIAL HYGIENE AND SAFETY: INCIDENT/ACCIDENT MANAGEMENT</p>\n</td>\n</tr>\n<tr>\n<td>\n<p>EHS_IHS_MM</p>\n</td>\n<td>\n<p>EHS INDUSTRIAL HYGIENE AND SAFETY: MEASUREMENT MANAGEMENT</p>\n</td>\n</tr>\n<tr>\n<td>\n<p>EHS_IHS_RA</p>\n</td>\n<td>\n<p>EHS INDUSTRIAL HYGIENE AND SAFETY: RISK ASSESSMENT</p>\n</td>\n</tr>\n<tr>\n<td>\n<p>EHS_IHS_WA</p>\n</td>\n<td>\n<p>EHS INDUSTRIAL HYGIENE AND SAFETY: WORK AREA MANAGEMENT</p>\n</td>\n</tr>\n<tr>\n<td>\n<p>EHS_IHS_SM</p>\n</td>\n<td>\n<p>EHS INDUSTRIAL HYGIENE AND SAFETY: SAFETY MEASURES</p>\n</td>\n</tr>\n<tr>\n<td>\n<p>EHS_HEA_IL</p>\n</td>\n<td>\n<p>EHS OCCUPATIONAL HEALTH: INJURY/ILLNESS LOG</p>\n</td>\n</tr>\n<tr>\n<td>\n<p>EHS_HEA_SV</p>\n</td>\n<td>\n<p>EHS OCCUPATIONAL HEALTH: MEDICAL SERVICE</p>\n</td>\n</tr>\n</tbody>\n</table></div>\n<ul>\n<li> In the activity “Define Application Classes Registered for EoP<em> </em>Check” the following entries were added:</li>\n</ul>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"0\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td>\n<p>Application Name</p>\n</td>\n<td>\n<p>Registered Function Module for EoP Checks</p>\n</td>\n</tr>\n<tr>\n<td>\n<p>EHS_IHS_IA</p>\n</td>\n<td valign=\"top\">\n<p>CBIH_BUPT_EOP_CHECK_IAL</p>\n</td>\n</tr>\n<tr>\n<td>\n<p>EHS_IHS_MM</p>\n</td>\n<td valign=\"top\">\n<p>CBIH_BUPT_EOP_CHECK_MP</p>\n</td>\n</tr>\n<tr>\n<td>\n<p>EHS_IHS_RA</p>\n</td>\n<td valign=\"top\">\n<p>CBIH_BUPT_EOP_CHECK_ER</p>\n</td>\n</tr>\n<tr>\n<td>\n<p>EHS_IHS_WA</p>\n</td>\n<td valign=\"top\">\n<p>CBIH_BUPT_EOP_CHECK_WA</p>\n</td>\n</tr>\n<tr>\n<td>\n<p>EHS_IHS_SM</p>\n</td>\n<td valign=\"top\">\n<p>CBIH_BUPT_EOP_CHECK_SM</p>\n</td>\n</tr>\n<tr>\n<td>\n<p>EHS_HEA_IL</p>\n</td>\n<td valign=\"top\">\n<p>CBIH_BUPT_EOP_CHECK_FAL</p>\n</td>\n</tr>\n<tr>\n<td>\n<p>EHS_HEA_SV</p>\n</td>\n<td valign=\"top\">\n<p>EHS00_BUPT_EOP_CHECK_MED_DATA</p>\n</td>\n</tr>\n</tbody>\n</table></div>\n<ol start=\"2\">\n<li>EH&amp;S applications deliver the following function modules registered for the end of purpose check of the central business partner:</li>\n</ol>\n<ul>\n<li>CBIH_BUPT_EOP_CHECK_IAL</li>\n<li>CBIH_BUPT_EOP_CHECK_MP</li>\n<li>CBIH_BUPT_EOP_CHECK_ER</li>\n<li>CBIH_BUPT_EOP_CHECK_WA</li>\n<li>CBIH_BUPT_EOP_CHECK_SM</li>\n<li>CBIH_BUPT_EOP_CHECK_FAL</li>\n<li>EHS00_BUPT_EOP_CHECK_MED_DATA</li>\n</ul>\n<p> </p>\n<p> </p>\n<p><span><span>How to increase performance during check execution:</span></span></p>\n<p>If you want to speed up the performance of the check you can create indexes on several tables. Which table depends on amount of data you are using in each. Please check numbers of entries in the tables before creating an index.</p>\n<p>Tables and relevant fields:</p>\n<p><span>Product Safety (EHS-PS):</span></p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"0\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td>Application name                       </td>\n<td>Table                       </td>\n<td>\n<p>Relevant Index Fields</p>\n</td>\n</tr>\n<tr>\n<td>\n<p>EHS_PS_REP</p>\n<p>(Report Shipping)</p>\n</td>\n<td>CVDDH</td>\n<td>RECADR_C</td>\n</tr>\n<tr>\n<td rowspan=\"5\">\n<p>EHS_PS_SVT</p>\n<p>(Substance Volume Tracking)</p>\n</td>\n<td>CCRCT_OR</td>\n<td>\n<p>VENDOR_EXT</p>\n</td>\n</tr>\n<tr>\n<td>\n<p>CCRCT_OR</p>\n</td>\n<td>\n<p>OR_EXT</p>\n</td>\n</tr>\n<tr>\n<td>CCRCT_CU</td>\n<td>OR_EXT</td>\n</tr>\n<tr>\n<td>\n<p>CCRCT_SO</p>\n</td>\n<td>OR_EXT</td>\n</tr>\n<tr>\n<td>\n<p>CCRCT_SOPL</p>\n</td>\n<td>OR_EXT</td>\n</tr>\n</tbody>\n</table></div>\n<p><span>Waste Management (EHS-WA)</span></p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"0\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td>\n<p>Application name</p>\n</td>\n<td>\n<p>Table</p>\n</td>\n<td>\n<p>Relevant Index Fields</p>\n</td>\n</tr>\n<tr>\n<td>\n<p>EHS_WA_MD</p>\n<p>(Master Data, Partners)</p>\n</td>\n<td>\n<p>EHSWAT_BPDEFINIT</p>\n</td>\n<td>\n<p>OBJNUM<br/>OBJART</p>\n</td>\n</tr>\n</tbody>\n</table></div>\n<p><span>Industrial Hygiene and Safety (EHS-IHS)</span></p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"0\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td>\n<p>Application name</p>\n</td>\n<td>\n<p>Table</p>\n</td>\n<td>\n<p>Relevant Index Fields</p>\n</td>\n</tr>\n<tr>\n<td rowspan=\"4\">\n<p>EHS_IHS_IA</p>\n<p>(Incident/Accident Management)</p>\n</td>\n<td>\n<p>CCIHT_IP</p>\n</td>\n<td>\n<p>IPPERS<br/>IPPERSGRP</p>\n</td>\n</tr>\n<tr>\n<td>\n<p>CCIHT_IPEVA</p>\n</td>\n<td>\n<p>FRTRMT<br/>FRTRMTGRP</p>\n</td>\n</tr>\n<tr>\n<td>\n<p>CCIHT_IPEVA</p>\n</td>\n<td>\n<p>FAPHYS<br/>FAPHYSGRP</p>\n</td>\n</tr>\n<tr>\n<td>\n<p>CCIHT_IPEVA</p>\n</td>\n<td>\n<p>IALPHOSP</p>\n</td>\n</tr>\n<tr>\n<td rowspan=\"2\">\n<p>EHS_IHS_MM</p>\n<p>(Measurement Management)</p>\n</td>\n<td>\n<p>CCIHT_MP</p>\n</td>\n<td>\n<p>MPERSON<br/>MPERSONGRP</p>\n</td>\n</tr>\n<tr>\n<td>\n<p>CCIHT_MJ</p>\n</td>\n<td>\n<p>MPERSON<br/>MPERSONGRP</p>\n</td>\n</tr>\n<tr>\n<td rowspan=\"5\">\n<p>EHS_IHS_RA</p>\n<p>(Risk Assessment)</p>\n</td>\n<td>\n<p>CCIHT_ERH</p>\n</td>\n<td>\n<p>ASSCTRPER<br/>ASSCTRPERGRP</p>\n</td>\n</tr>\n<tr>\n<td>\n<p>CCIHT_ERQT</p>\n</td>\n<td>\n<p>PROBAND<br/>PROBANDGRP</p>\n</td>\n</tr>\n<tr>\n<td>\n<p>CCIHT_ANML</p>\n</td>\n<td>\n<p>PROBAND<br/>PROBANDGRP</p>\n</td>\n</tr>\n<tr>\n<td>\n<p>CCIHT_ANNC</p>\n</td>\n<td>\n<p>PERSRESP<br/>PERSRESPGRP</p>\n</td>\n</tr>\n<tr>\n<td>\n<p>CCIHT_ANSE</p>\n</td>\n<td>\n<p>PERSRESP<br/>PERSRESPGRP</p>\n</td>\n</tr>\n<tr>\n<td rowspan=\"3\">\n<p>EHS_IHS_SM</p>\n<p>(Safety Measures)</p>\n</td>\n<td>\n<p>CCIHT_ACH</p>\n</td>\n<td>\n<p>PERSRESP<br/>PERSRESPGRP</p>\n</td>\n</tr>\n<tr>\n<td>\n<p>CCIHT_ACH</p>\n</td>\n<td>\n<p>PROCPERS<br/>PROCPERSGRP</p>\n</td>\n</tr>\n<tr>\n<td>\n<p>CCIHT_ACH</p>\n</td>\n<td>\n<p>RESPPCONTR<br/>RESPPCONTRGRP</p>\n</td>\n</tr>\n</tbody>\n</table></div>\n<p><span>Occupational Health (EHS-HEA):</span></p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"0\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td>\n<p>Application name</p>\n</td>\n<td>\n<p>Table</p>\n</td>\n<td>\n<p>Relevant Index Fields</p>\n</td>\n</tr>\n<tr>\n<td rowspan=\"2\">\n<p>EHS_HEA_IL</p>\n<p>(Injury/Illness Log)</p>\n</td>\n<td>\n<p>CCIHT_FAL</p>\n</td>\n<td>\n<p>FAHELPER<br/>FAHELPERGRP</p>\n</td>\n</tr>\n<tr>\n<td>\n<p>CCIHT_IP</p>\n</td>\n<td>\n<p>IPPERS<br/>IPPERSGRP</p>\n</td>\n</tr>\n<tr>\n<td rowspan=\"7\">\n<p>EHS_HEA_SV</p>\n<p>(Medical Service)</p>\n<p> </p>\n<p> </p>\n<p> </p>\n<p> </p>\n<p> </p>\n<p> </p>\n</td>\n<td>\n<p>T7EHS00_SERVICE</p>\n</td>\n<td>\n<p>SRV_PHYSICIAN<br/>PHYSFLAG</p>\n</td>\n</tr>\n<tr>\n<td>\n<p>T7EHS00_SRV_PROT</p>\n</td>\n<td>\n<p>SRV_PHYSICIAN<br/>PHYSFLAG</p>\n</td>\n</tr>\n<tr>\n<td>\n<p>T7EHS00_VACCH</p>\n</td>\n<td>\n<p>VACC_PHYSICIAN<br/>PHYSFLAG</p>\n</td>\n</tr>\n<tr>\n<td>\n<p>T7EHS00_SRV_DIA</p>\n</td>\n<td>\n<p>SRV_PHYSICIAN<br/>PHYSFLAG</p>\n</td>\n</tr>\n<tr>\n<td>\n<p>T7EHS00_SRV_SCH</p>\n</td>\n<td>\n<p>SRV_PHYSICIAN<br/>SRV_PHYSFLAG_INT</p>\n</td>\n</tr>\n<tr>\n<td>\n<p>T7EHS00_SRV_EXA</p>\n</td>\n<td>\n<p>EXAM_EXTERNAL</p>\n</td>\n</tr>\n<tr>\n<td>\n<p>T7EHS00_SRV_EXA</p>\n</td>\n<td>\n<p>EXAM_PROVIDER</p>\n</td>\n</tr>\n</tbody>\n</table></div>\n<p>How-To create an index:</p>\n<ul>\n<li>Open transaction SE11 (ABAP Dictionary Maintenance). Fill into the field \"Database table\" the name of the table. Press the \"Change\" button and you'll be directed to the database table.</li>\n<li>Click on button \"Indexes...\".</li>\n<li>Click on the button \"Create\" and then click on the item \"Create Index\".</li>\n<li>In the popup for \"Index Name\" type the relevant field names. Press \"OK\".</li>\n<li>Write for the \"Short Description\" following text: \"Index for EoP check\".</li>\n<li>Press the button \"Table Fields\" and in the popup tick checkboxes for \"MANDT\" and relevant field and then press the button \"Copy\".</li>\n<li>Make sure that the new index is \"Non-unique index\" and \"For selected database systems\". Click on button \"Database Systems\". Select now 'Exclusion List' and add 'HDB' (SAP HANA Database).</li>\n<li>Press the icon \"Save\" and then the icon \"Activate\".</li>\n<li>Close transaction se11.</li>\n</ul>\n<p> </p>", "noteVersion": 4}, {"note": "2012421", "noteTitle": "2012421 - Allocation Table related where-used-check before blocking of vendor / customer data", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>The allocation table application contains data, which is related to the vendor or customer. This note describes the changes, which will prevent blocking of a vendor or customer, if this vendor or customer is still used by the application.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Deletion, Blocking, Personal Data, WUC, Vendor, Customer</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>See note 2007926</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>The new functionality for Allocation table will be available with release SAP_APPL 617 and support package SP05.</p>\n<p>This notes provides a where-used check for the customer or vendor data in the application.<br/><br/>The functionality consists of the following main features:</p>\n<ol>\n<li>In customizing \"Customer Master /Vendor Master Deletion\" the following entries are added:<ol>\n<li>In the activity \"Define and Store Application Names for EoP Check\" the following entry was added:<br/>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"3\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td>ID Type</td>\n<td>Application Name</td>\n<td>Application Description</td>\n</tr>\n<tr>\n<td>1 - Customer Master Data</td>\n<td>ERP_ALLOC</td>\n<td>Allocation Table</td>\n</tr>\n<tr>\n<td>2 - Vendor Master Data</td>\n<td>ERP_ALLOC</td>\n<td>Allocation Table</td>\n</tr>\n</tbody>\n</table></div>\n</li>\n</ol><ol start=\"2\">\n<li>In the activity \"Define Application Classes Registered for EoP Check\" the following entry was added:<br/>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"3\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td>ID Type</td>\n<td>Application Name</td>\n<td>Registered Class for EoP Checks</td>\n<td>General</td>\n<td>Copm. Code</td>\n</tr>\n<tr>\n<td>1- Customer Master Data</td>\n<td>ERP_ALLOC</td>\n<td>CL_ALLOCATION_CV_EOP_CHECK</td>\n<td>X</td>\n<td>X</td>\n</tr>\n<tr>\n<td>2 - Vendor Master Data</td>\n<td>ERP_ALLOC</td>\n<td>CL_ALLOCATION_CV_EOP_CHECK</td>\n<td>X</td>\n<td>X</td>\n</tr>\n</tbody>\n</table></div>\n</li>\n</ol></li>\n<li>\n<p>The application Allocation Table delivers the following class registered for the end of purpose check or where-used check of the vendor and customer:<br/>CL_ALLOCATION_CV_EOP_CHECK- \"Allocation Table EOP Check for CV\"</p>\n</li>\n</ol>", "noteVersion": 2}, {"note": "2118673", "noteTitle": "2118673 - IS OIL related End Of Purpose Check for customer and vendor data", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>The application IS OIL can contain data, which is related to a customer or vendor. This note describes the changes, which will prevent blocking of a customer, if this customer is still in use. In addition these changes will prevent blocking of a vendor, if this vendor is still in use.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Deletion, Blocking, Personal Data, EOP, Customer, Vendor, KUNNR ,ILM</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>See note 2007926</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>The new functionality for application IS-OIL downstream and  will be applicable EhP7 SP08<br/><br/><strong>1.End of Purpose Check</strong></p>\n<p>The functionality consists of the following main features:</p>\n<p>In customizing \"Customer Master /Vendor Master Deletion\" the following entries are added:</p>\n<ol><ol>\n<li>In the activity \"Define and Store Application Names for EoP Check\" the following entry was added:<br/>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"3\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td>ID Type</td>\n<td>Application Name</td>\n<td>Application Description</td>\n</tr>\n<tr>\n<td>1 - Customer Master Data</td>\n<td>IS-OIL</td>\n<td>Oil Downstream Customer</td>\n</tr>\n<tr>\n<td>2 - Vendor Master Data</td>\n<td>IS-OIL</td>\n<td>Oil Downstream Vendor</td>\n</tr>\n</tbody>\n</table></div>\n</li>\n<li>In the activity \"Define Application Classes Registered for EoP Check\" the following entry was added:</li>\n</ol></ol>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"3\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td>ID Type</td>\n<td>Application Name</td>\n<td>Registered Class for EoP Checks</td>\n<td>General</td>\n<td>Comp.Code</td>\n</tr>\n<tr>\n<td>1 - Customer Master Data</td>\n<td>IS-OIL</td>\n<td>CVP_OIL_EOP_CHECK</td>\n<td>'X'</td>\n<td>'X'</td>\n</tr>\n<tr>\n<td>2 - Vendor Master Data</td>\n<td>IS-OIL</td>\n<td>CVP_OIL_EOP_CHECK</td>\n<td>'X'</td>\n<td>'X'</td>\n</tr>\n</tbody>\n</table></div>\n<p>The application IS OIL delivers the following class registered for the end of purpose check of the customer and the vendor  CVP_OIL_EOP_CHECK</p>\n<p> </p>\n<p><strong>2. ILM Enablement of the following Archiving objects-</strong></p>\n<p><strong>New Business Function - ILM_IS_OIL_02 </strong> is provided for ILM enablement of the following archiving objects</p>\n<ul>\n<li>OIG_DRIVER</li>\n<li>OIG_VEHCLE</li>\n<li>OIG_TPUNIT</li>\n<li>OIJ_NOMIN</li>\n<li>OIJ_TICKET</li>\n<li>IS_OIFSPBL</li>\n</ul>\n<p>The BDRP tables  OIISCPKN, OIISCPLKN, OIIOTWSIKN can be archived with customer arvhiving object  FI_ACCRECV .</p>\n<p>The BADI implementation OILBDRP_FI_ACCRECV is provided for this purpose.</p>\n<p><strong>3. Handling of blocked Customer/Vendor</strong></p>\n<p>If a customer or vendor is blocked no further business activity will be allowed on the blocked customer/Vendor.The details of blocked customer /vendor will be visible only for authorized user.</p>\n<p> </p>\n<p> </p>\n<p> </p>\n<p> </p>", "noteVersion": 1}, {"note": "2015309", "noteTitle": "2015309 - FI document change interface: locking customer and vendor", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>Using the FI document change interface, data that is related to a customer can be changed.</p>\n<p>This SAP Note describes the changes in the document change interface that enable the simplified deletion and locking of customer master data based on SAP Information Lifecycle Management (ILM). SAP Note 2007926 contains the description for this.</p>\n<p>This SAP Note is also provided as a technical prerequisite for other corrections. It does not deliver any function.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Data protection, ILM, SAP Information Lifecycle Management, delete, deletion, lock, personal data, person-related data, customer, FI_DOCUMENT_CHANGE, FI_ITEMS_CHANGE, EoP, end of purpose</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>See SAP Note 2007926.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>The new functions for the FI document change interface are available as of: <br/>SAP_FIN, Release 617, Support Package 5<br/><br/>The FI document change interface offers an end of purpose check for the customer. This check consists of the following main characteristics:</p>\n<p>If a customer is locked, no further changes can be made to the document data of this customer.</p>", "noteVersion": 2}, {"note": "2026216", "noteTitle": "2026216 - OIL_&_GAS: Follow Up Note 2007926", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>***********************************************************************<br/>* WARNING: This is an IS-OIL / IS-MINE specific note. If you          *<br/>* DON'T have IS-OIL / IS-MINE  installed on your system, this         *<br/>* note does not apply to you. If this note is applied and you do not  *<br/>* have IS-OIL / IS-MINE, you could cause serious damage to your       *<br/>* system.                                                             *<br/>***********************************************************************<br/><br/>Customer implements CORE Note 2007926 and also needs to check with the side effects on OIL_&amp;_GAS related coding.</p>\n<p>Symptom in note 2007926:</p>\n<p>The SAP Business Partner (LO-MD-BP) component manages business partner of the types customer, vendor or contact person that are relevant in business processes.</p>\n<p>\"SAP software supports the simplified blocking and deletion of personal data. The SAP Note 1825544 describes how the use of SAP Information Lifecycle Management (ILM) supports the simplified blocking and deletion of personal data in SAP Business Suite.</p>\n<p>Transactional data related to a customer or vendor might exist in several applications. Some data might be related to closed business, other data to open or new business. As long as business activities are in progress, most data will be neither blocked nor deleted. As soon as business activities related to the customer or vendor are completed, the data has to be blocked. After the data reaches the longest retention period, the data has to be deleted.</p>\n<p>The customer or vendor needs to be blocked in the leading system and the connected systems after considering the application`s consumption of the customer or vendor. The relevant applications, including the customer or vendor master application itself needs to provide permission for blocking the customer or vendor based on the residence rule calculation defined in the ILM framework. After the customer or vendor is blocked, business users must no longer be able to display or process\"</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<ul>\n<li>General keywords: add-on, IS-Oil, industry solution.</li>\n</ul>\n<ul>\n<li>Problem-specific key words: Data Privacy, Information Lifecycle Management, ILM, Deletion, Blocking, Personal Data, Business Partner, Customer, Vendor, FI_ACCRECV, FI_ACCPAYB, FI_ACCKNVK, EOP</li>\n</ul>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>You are using OIL_&amp;_GAS and are about to implement CORE note 2007926.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Please apply note 2007926 before applying this note.<br/><br/></p>", "noteVersion": 3}, {"note": "2011623", "noteTitle": "2011623 - LO-ARM Where-used-check (WUC)  before blocking of customer/vendor data", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>Advanced Returns Management contains data which is related to customers or vendors. This note describes the changes in Advanced Returns Management which will prevent the blocking of customers or vendors if they are found in ARM returns processes.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Deletion, Blocking, Personal Data, EOP, Customer, Vendor, LIFNR, KUNNR, WUC, MSR_D_HEAD, MSR_D_EXECUTED</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>See SAP Note 2007926.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>The new functionality for Advanced Returns Management is available as of release SAP_APPL 617 and support package 05. SAP Note <span class=\"urTxtStd urVt1\">2035204 should be implemented afterwards.</span></p>\n<p>Advanced Returns Management provides a where-used check for the customer or vendor. This check consists of the following main features:</p>\n<ol>\n<li>In customizing \"Customer Master /Vendor Master Deletion\" the following entries are added:</li>\n<ol>\n<li>In the activity \"Define and Store Application Names for EoP Check\" the following entry was added:<br/>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"3\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td>ID Type</td>\n<td>Application Name</td>\n<td>Application Description</td>\n</tr>\n<tr>\n<td>1 - Customer Master Data</td>\n<td>ERP_LO_ARM</td>\n<td>\n<p>Advanced Returns Management</p>\n</td>\n</tr>\n<tr>\n<td>2 - Vendor Master Data</td>\n<td>ERP_LO_ARM</td>\n<td>\n<p>Advanced Returns Management</p>\n</td>\n</tr>\n</tbody>\n</table></div>\n</li>\n<li>In the activity \"Define Application Classes Registered for EoP Check\" the following entry was added:<br/>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"3\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td>ID Type</td>\n<td>Application Name</td>\n<td>Registered Class for EoP Checks</td>\n<td>General</td>\n<td>Comp.Code</td>\n</tr>\n<tr>\n<td>1 - Customer Master Data</td>\n<td>ERP_LO_ARM</td>\n<td>CL_WUC_LO_ARM_EOP_CHECK</td>\n<td>'X'</td>\n<td>'X'</td>\n</tr>\n<tr>\n<td>2 - Vendor Master Data</td>\n<td>ERP_LO_ARM</td>\n<td>CL_WUC_LO_ARM_EOP_CHECK</td>\n<td>'X'</td>\n<td>'X'</td>\n</tr>\n</tbody>\n</table></div>\n</li>\n</ol>\n<li>\n<p>The application Advanced Returns Management delivers the following class registered for the end of purpose check of the customer and vendor:<br/>CL_WUC_LO_ARM_EOP_CHECK - \"Where-Used-Check Advanced Returns Management\"</p>\n</li>\n</ol>", "noteVersion": 1}, {"note": "2021710", "noteTitle": "2021710 - BOM/ Routing / Inspection Plan : related where-used-check before blocking <PERSON><PERSON><PERSON>.", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>The application bill of Material / Routing / Inspection Plan can contain data, which is related to Business Partner or Vendor information. This note describes the changes, which will prevent blocking of a Business Partner or Contact Person, if it is still in use by the Collection Management.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Deletion, Blocking, Personal Data, EOP, Business Partner, PARNR, contact person</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>See note 2007926</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>The new functionality for application Material Master will be available with release SAP_APPL 617 and support package SP05.<br/><br/>The functionality consists of the following main features:</p>\n<p>In customizing transaction CVP_CUST \"Define and Store Application Names for EoP Check\" the following entries are added:</p>\n<ol>\n<li>In the activity \"Define and Store Application Names for EoP Check\" the following entry was added:<br/>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"3\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td>ID Type</td>\n<td>APPL Name</td>\n<td>Application Description</td>\n</tr>\n<tr>\n<td>Vendor Master Data</td>\n<td>ERP_LO_MD_BOM</td>\n<td>Bill of Material</td>\n</tr>\n<tr>\n<td>Vendor Master Data</td>\n<td>ERP_PP_BD_RTG</td>\n<td>Routing / Tasklist / Inspection Plan Master Data</td>\n</tr>\n</tbody>\n</table></div>\n</li>\n<li>In the activity \"Define Application Classes Registered for EoP Check\" the following entry was added:<br/>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"3\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td>ID Type</td>\n<td>Appl Name</td>\n<td>APPl Description</td>\n<td>Registered class for EOP check</td>\n</tr>\n<tr>\n<td>Vendor Master Data</td>\n<td>ERP_LO_MD_BOM</td>\n<td>Bill of Material</td>\n<td>CL_WUC_LO_MD_BOM_EOP_CHECK</td>\n</tr>\n<tr>\n<td>Vendor Master Data</td>\n<td>ERP_PP_BD_RTG</td>\n<td>Routing / Tasklist / Inspection Plan Master Data</td>\n<td>CL_WUC_PP_BD_RTG_EOP_CHECK</td>\n</tr>\n</tbody>\n</table></div>\n</li>\n</ol>\n<p> </p>", "noteVersion": 1}, {"note": "2012374", "noteTitle": "2012374 - Retail Replenishment related where-used-check before blocking of vendor / customer data", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>The application for retail puchasing replenishment (replenishment and vendor managed inventory) contains data, which is related to the vendor or customer. This note describes the changes, which will prevent blocking of a vendor or customer, if this vendor or customer is still used by the application</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Deletion, Blocking, Personal Data, WUC, Vendor, Customer</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>See note 2007926</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>The new functionality for application Retail Replenishment will be available with release SAP_APPL617 and support package SP05.</p>\n<p>This note provides a where-used check for customer and vendor data in the application.<br/><br/>The functionality consists of the following main features:</p>\n<ol>\n<li>In customizing \"Customer Master /Vendor Master Deletion\" the following entries are added:<ol>\n<li>In the activity \"Define and Store Application Names for EoP Check\" the following entry was added:<br/>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"3\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td>ID Type</td>\n<td>Application Name</td>\n<td>Application Description</td>\n</tr>\n<tr>\n<td>1 - Customer Master Data</td>\n<td>RET_PUR_RP</td>\n<td>Retail Replenishment</td>\n</tr>\n<tr>\n<td>2 - Vendor Master Data</td>\n<td>RET_PUR_RP</td>\n<td>Retail Replenishment</td>\n</tr>\n</tbody>\n</table></div>\n</li>\n</ol><ol start=\"2\">\n<li>In the activity \"Define Application Classes Registered for EoP Check\" the following entry was added:<br/>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"3\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td>ID Type</td>\n<td>Application Name</td>\n<td>Registered Class for EoP Checks</td>\n<td>General</td>\n<td>Copm. Code</td>\n</tr>\n<tr>\n<td>1- Customer Master Data</td>\n<td>RET_PUR_RP</td>\n<td>CL_PUR_RETAIL_CV_EOP_CHECK</td>\n<td>X</td>\n<td>X</td>\n</tr>\n<tr>\n<td>2 - Vendor Master Data</td>\n<td>RET_PUR_RP</td>\n<td>CL_PUR_RETAIL_CV_EOP_CHECK</td>\n<td>X</td>\n<td>X</td>\n</tr>\n</tbody>\n</table></div>\n</li>\n</ol></li>\n<li>\n<p>The application Retail Replenishment delivers the following class registered for the end of purpose check of the vendor and customer:<br/>CL_PUR_RETAIL_CV_EOP_CHECK- \"Retail Purchasing EOP check for CV\"</p>\n</li>\n</ol>", "noteVersion": 2}, {"note": "2075111", "noteTitle": "2075111 - Customer/Vendor master data Destruction: Various correction 2 Part1", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>This note correct several errors and introduce some new advanced functionalities:</p>\n<ul>\n<li>Business Function ERP_CVP_ILM_1<br/>Now includes switch from address management ADDR_BLK_SWITCH</li>\n<li>End of Purpose blocking report</li>\n<ul>\n<li>Data elements long text documentation CVP_MODE_PROD and CVP_MODE_TEST,<br/>now have original language English</li>\n<li>Application log display new context column ( icons for No Business,<br/>Ongoing Business, Business Completed)</li>\n<li>SoRT table now contains information about creation and last change<br/>(Date, Time, User, Program, Transaction)</li>\n</ul>\n<li>Unblocking report</li>\n<ul>\n<li>Data elements long text documentation CVP_MODE_PROD and CVP_MODE_TEST,<br/>now have original language English</li>\n</ul>\n<li>Display Application log</li>\n<ul>\n<li>Application log display new context column ( icons for No Business,<br/>Ongoing Business, Business Completed)</li>\n</ul>\n</ul>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>CVP_PREPARE_EOP, CVP_PRE_EOP, CVP_DISPLAY_LOG, CVP_UNBLOCK_MD</p>\n<p>ERP_CVP_ILM_1</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>Advance development</p>\n<p>SAP Notes <a href=\"/notes/2066108\" target=\"_blank\">2066108</a> and <a href=\"/notes/2020058\" target=\"_blank\">2020058</a> are pre-requisites</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Implement correction as described</p>", "noteVersion": 1}, {"note": "2019402", "noteTitle": "2019402 - Decoupled TSW related where-used-check before blocking of customer and vendor data", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>The application Decoupled TSW can contain data, which is related to a customer or vendor. This note describes the changes, which will prevent blocking of a customer, if this customer is still in use. In addition these changes will prevent blocking of a vendor, if this vendor is still in use.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Deletion, Blocking, Personal Data, EOP, Customer, Vendor, KUNNR , TSW for ECC</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>See note 2007926</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>The new functionality for <em>Decoupled TSW </em> is available as of 617 release support package SP05.</p>\n<p><em>LE-TSW</em> provides <em>a where-used check </em> for the customer or vendor. This check consists of the following main features:</p>\n<p><br/>The functionality consists of the following main features:</p>\n<ol>\n<li>In customizing \"Customer Master /Vendor Master Deletion\" the following entries are added:</li>\n<ol>\n<li>In the activity \"Define and Store Application Names for EoP Check\" the following entry was added:<br/>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"3\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td>ID Type</td>\n<td>Application Name</td>\n<td>Application Description</td>\n</tr>\n<tr>\n<td>1 - Customer Master Data</td>\n<td>TSW_ECC</td>\n<td>Decoupled TSW Customer</td>\n</tr>\n<tr>\n<td>2 - Vendor Master Data</td>\n<td>TSW_ECC</td>\n<td>Decoupled TSW Vendor</td>\n</tr>\n</tbody>\n</table></div>\n</li>\n<li>In the activity \"Define Application Classes Registered for EoP Check\" the following entry was added:<br/>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"3\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td>ID Type</td>\n<td>Application Name</td>\n<td>Registered Class for EoP Checks</td>\n<td>General</td>\n<td>Comp.Code</td>\n</tr>\n<tr>\n<td>1 - Customer Master Data</td>\n<td>TSW_ECC</td>\n<td>CVP_TSW_ECC_EOP_CHECK</td>\n<td>'X'</td>\n<td>'X'</td>\n</tr>\n<tr>\n<td>2 - Vendor Master Data</td>\n<td>TSW_ECC</td>\n<td>CVP_TSW_ECC_EOP_CHECK</td>\n<td>'X'</td>\n<td>'X'</td>\n</tr>\n</tbody>\n</table></div>\n</li>\n</ol>\n<li>\n<p>The application TSW delivers the following class registered for the end of purpose check of the customer and the vendor:<br/> CVP_TSW_ECC_EOP_CHECK</p>\n</li>\n</ol>", "noteVersion": 1}, {"note": "2140563", "noteTitle": "2140563 - QA: Deletion and Blocking of Vendor in the FI Localization for Qatar", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>The FI Localization for Qatar includes vendor-related data that may require blocking and/or deletion. This SAP Note describes the changes in the FI Localization for Qatar that enable the simplified blocking and deletion of vendor master data based on SAP Information Lifecycle Management (ILM) as described in SAP Note 2007926.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Information Lifecycle Management, ILM, Deletion, Blocking, Personal Data, Vendor, FI_ACCPAYB, EoP</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>See SAP Note 2007926</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>The new functionality for the FI Localization for Qatar is available as of EAPPLGLO 607, Support Package 12.</p>\n<p>The FI Localization for Qatar includes an End of Purpose check for the vendor master data. This check consists of the following main features:</p>\n<p>1. In the standard system, the following application names have to be customized for the FI Localization for Qatar. Run transaction CVP_CUST, choose <em>Customer Master/Vendor Master Deletion -&gt; Registration of Applications -&gt; Register Application with Different Software Layer</em>:</p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"0\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td>\n<p>ID Type</p>\n</td>\n<td>\n<p>Application Name</p>\n</td>\n<td>\n<p>Applic.Component</p>\n</td>\n<td>\n<p>General</p>\n</td>\n<td>\n<p>Comp.Code</p>\n</td>\n<td>\n<p>EoP Priority</p>\n</td>\n<td>\n<p>Application Description</p>\n</td>\n</tr>\n<tr>\n<td>\n<p>2 - Vendor Master Data</p>\n</td>\n<td>\n<p>ERP_FI_LOC_QA_WT</p>\n</td>\n<td>\n<p>FI-LOC-FI-QA</p>\n</td>\n<td> </td>\n<td>X</td>\n<td>0</td>\n<td>\n<p>Withholding tax certificates for Qatar</p>\n</td>\n</tr>\n</tbody>\n</table></div>\n<p>2. The FI Localization for Qatar includes the following functional modules for the end of purpose check of the vendor master data. These must be customized in the node <em>Register Functional Module</em>:</p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"0\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td>\n<p>ID Type</p>\n</td>\n<td>\n<p>Application Name</p>\n</td>\n<td>\n<p>FM Type</p>\n</td>\n<td>\n<p>Registered Func. Modules</p>\n</td>\n</tr>\n<tr>\n<td>\n<p>2 Withholding tax certificates for Qatar</p>\n</td>\n<td>\n<p>ERP_FI_LOC_QA_WT</p>\n</td>\n<td>\n<p>EoP Check: Check Partner</p>\n</td>\n<td>\n<p>FIAP_WTQA_EOP_CHECK_PARTNERS</p>\n</td>\n</tr>\n<tr>\n<td> </td>\n<td> </td>\n<td>\n<p>EoP Check: Finalize EoP Check</p>\n</td>\n<td>\n<p>FIAP_WTQA_EOP_FINALIZE</p>\n</td>\n</tr>\n<tr>\n<td> </td>\n<td> </td>\n<td>\n<p>EoP Check: Initialize EoP Check</p>\n</td>\n<td>\n<p>FIAP_WTQA_EOP_INITIALIZE</p>\n</td>\n</tr>\n</tbody>\n</table></div>\n<p>3.  This solution differs from the standard solution described in SAP Notes 2007926 and 2112710. For this solution, you are required customize your residence period(s) for the application-specific ILM object FILOC_QA_WT_CERT. The policies you create must be assigned to the Audit Area BUPA_DP.</p>\n<p>4. When a vendor is blocked, the application does not display any personal data related to this vendor, including the number of the vendor. You cannot display or change any information. You cannot create new business with this vendor.</p>", "noteVersion": 4}, {"note": "2011851", "noteTitle": "2011851 - Batch where-used list related where-used-check before blocking of customer/vendor data", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>The Batch where-use list contains data, which is related to the customer or/and vendor. This note describes the changes, which will prevent blocking of a customer/vendor, if this customer/vendor is still assigned to a batch where-use entry.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Deletion, Blocking, Personal Data, EOP, Vendor, Customer, CHVW</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>See note 2007926</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>The new functionality for application Batch Master will be available with release SAP_APPL 617 and support package SP05.<br/><br/>The functionality consists of the following main features:</p>\n<ol>\n<li>In customizing \"Customer Master /Vendor Master Deletion\" the following entries are added:</li>\n<ol>\n<li>In the activity \"Define and Store Application Names for EoP Check\" the following entry was added:<br/>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"3\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td>ID Type</td>\n<td>Application Name</td>\n<td>Application Description</td>\n</tr>\n<tr>\n<td>1 - Customer master data</td>\n<td>ERP_LO_BM_WUL</td>\n<td>\n<p>Batch where-Used list</p>\n</td>\n</tr>\n<tr>\n<td>\n<p>2 - Vendor master data</p>\n</td>\n<td>ERP_LO_BM_WUL</td>\n<td>\n<p>Batch where-Used list</p>\n</td>\n</tr>\n</tbody>\n</table></div>\n</li>\n<li>In the activity \"Define Application Classes Registered for EoP Check\" the following entry was added:<br/>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"3\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td>ID Type</td>\n<td>Application Name</td>\n<td>Registered Class for EoP Checks</td>\n<td>General</td>\n<td>Comp.Code</td>\n</tr>\n<tr>\n<td>1 - Customer master data</td>\n<td>ERP_LO_BM_WUL</td>\n<td>CL_WUC_LO_BM_WUL_EOP_CHECK</td>\n<td>'X'</td>\n<td>'X'</td>\n</tr>\n<tr>\n<td>\n<p>2 - Vendor master data</p>\n</td>\n<td>ERP_LO_BM_WUL</td>\n<td>CL_WUC_LO_BM_WUL_EOP_CHECK</td>\n<td>'X'</td>\n<td>'X'</td>\n</tr>\n</tbody>\n</table></div>\n</li>\n</ol>\n<li>The application Batch where-used list delivers the following class registered for the end of purpose check of the customer/vendor:<br/> CL_WUC_LO_BM_WUL_EOP_CHECK - \"Where-Used-Check Batch Master\"</li>\n</ol>", "noteVersion": 1}, {"note": "2021002", "noteTitle": "2021002 - WUC for empties management in Purchasing", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>The application of empties management in Purchasing includes data that refers to vendors. This SAP Note provides an implementation that prevents the blocking of vendors when they are still involved in the application of empties management in Purchasing.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>End of purpose, where used check, deletion, block, customer, vendor, partner</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>See SAP Note 2007926.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Thsi new function is available in the application of empties management in Purchasing as of Release EA-APPL 617 Support Package SP05.</p>\n<p>It is delivered together with class /BEV1/CL_WUC_MM_NE_EOP_CHECK.</p>\n<p>The relevant customizing is delivered with the switch BC Sets /BEV1/NE_VAL_CUST_WUC and /BEV1/NE_VAL_CUST_WUC_CL.</p>\n<p> </p>", "noteVersion": 4}]}], "activities": [{"Activity": "Process Design / Blueprint", "Phase": "Before or during conversion project", "Condition": "Mandatory", "Additional_Information": "In Inventory Management the end of purpose check for customer and supplier master data has changed since SAP S/4 HANA 1709, see details in SAP Note 2516223"}, {"Activity": "Data cleanup / archiving", "Phase": "Before or during conversion project", "Condition": "Mandatory", "Additional_Information": "Blocked customer or supplier master data related attributes shall be archived or deleted, details in SAP Note 2516223"}, {"Activity": "Customizing / Configuration", "Phase": "Before or during conversion project", "Condition": "Mandatory", "Additional_Information": "Remove blocked customers and suppliers from plant and storage location data"}, {"Activity": "Data correction", "Phase": "Before or during conversion project", "Condition": "Mandatory", "Additional_Information": "Batch input test data, short documents, correction data and consistency check results shall be deleted, see reparts details in SAP Note 2516223"}, {"Activity": "Data correction", "Phase": "Before or during conversion project", "Condition": "Conditional", "Additional_Information": "If you upgrade from 1511/1610/1709 to 1709-FPS01 or higher -> please read SAP Note 2629400"}, {"Activity": "User Training", "Phase": "During or after conversion project", "Condition": "Mandatory", "Additional_Information": ""}]}