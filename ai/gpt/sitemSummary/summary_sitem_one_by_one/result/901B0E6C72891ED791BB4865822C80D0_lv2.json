{"guid": "901B0E6C72891ED791BB4865822C80D0", "sitemId": "SI50: Logistics_General", "sitemTitle": "S4TWL - Segmentation", "note": 2465612, "noteTitle": "2465612 - S4TWL - Segmentation", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are doing a system conversion to SAP S/4HANA 1709, and you are using segmentation related busines applications. The following SAP S/4HANA Transition Worklist item is applicable in this case</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Segmentation, Fashion, Retail.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong><strong>Description</strong></strong></p>\n<p>In SAP S/4HANA segmentation is available, but has some diiferences compared to SAP ERP which need to be considered.</p>\n<p><strong><strong><strong><strong>Business Process related information</strong></strong></strong></strong></p>\n<p>The differences in segmentaion between SAP ERP and SAP S/4HANA are the following:</p>\n<ul>\n<li>In SAP S/4HANA segmentation strategies 1:n and n:m are not available anymore.</li>\n<li>Stock protection in SAP ERP is implemented via segmentation. In SAP S/4HANA stock protection can be achieved via product allocation (PAL).</li>\n<li>Segmentation is considered only in the advanced ATP (aATP). The classic ATP is not supported anymore.</li>\n<li>In SAP S/4HANA, the default segmentation can be maitained via a specific transaction (SGT_DEFAULT).</li>\n<li>In SAP S/4HANA transactions to configure segmentation structures and strategies (SGTS, SGTC) are now available in the application (vs. customizing in SAP ERP).</li>\n<li>In SAP S/4HANA MRP related article/material master data on segment level are not available anymore.</li>\n<li>In SAP S/4HANA segmentation maintenance in the article/material master has been simplified compared to SAP ERP (e.g. automatic fixing of the segmentation strategy during article and listing maintenance).</li>\n<li>In SAP S/4HANA the transaction LS24 doesn’t support  the “Refresh” button anymore</li>\n<li>PRC planning level (SGT_PRPL) and Consecutive Number for Segmentation of Equal Status (SGT_CNES) fields are not supported anymore</li>\n<li>Safety stock is supported only at Default stock segment level (vs stock segment level in SAP ERP)</li>\n<li>Segmentation is considered only in MRP live (MD01N). The classic MRP is not supported. </li>\n<li>In SAP S/4HANA segmentation is not supported in the following applications:</li>\n<ul>\n<li>Quality Management (QM)</li>\n<li>Production Planning - Detailed Scheduling (PP-DS)</li>\n<li>Process Order</li>\n<li>Demand-Driven MRP (DDMRP)</li>\n<li>Predictive MRP (pMRP)</li>\n<li>Segmentation structure and strategy are not considered in EWM</li>\n<li>Spare Parts Planning (SPP) </li>\n</ul>\n<li>In SAP S/4HANA, the following fields are not available:</li>\n<ul>\n<li>Discrete Batch Number (SGT_CHINT)</li>\n<li>Stock Protection Indicator (SGT_STK_PRT)  </li>\n<li>Consumption Priority (SGT_PRCM)  </li>\n<li>ATP/MRP Status for Material and Segment (SGT_MRP_ATP_STATUS)  </li>\n<li>Sort Stock based on Segment (SGT_MRPSI)  </li>\n<li>Date from which the plant-specific material status is valid (SGT_MMSTD)</li>\n</ul>\n<li>In SAP S/4HANA, the following transactions are not available:</li>\n<ul>\n<li>SGT_CONVERT_MATERIAL</li>\n<li>SGT_MAINTAIN_VALT</li>\n<li>SGT_MASS_EXTD_MAT</li>\n</ul>\n</ul>\n<p><strong><strong><strong><strong>Required and Recommended Action</strong></strong></strong></strong></p>\n<p>Part of the conversion from SAP ERP to SAP S/4HANA, article need to be resasigned from \"old\" to \"new\" segmentation, please see referenced note.</p>\n<p>Implemented business processes need to be adjusted according to the changes listed above.</p>\n<p><strong>How to Determine Relevancy</strong></p>\n<p>This Simplification Item is relevant if segmentation is used. <br/>This can be checked via transaction SE16N. Enter table <span class=\"sapMText sapMTextMaxWidth sapUiSelectable sapUiTinyMarginTopBottom tdbCheckListFont\" id=\"__text36-__list6-0\">SGT_COVS_T </span>and check whether there are entries.<br/>There is also a pre-check class <span class=\"sapMText sapMTextMaxWidth sapUiSelectable sapUiTinyMarginTopBottom tdbCheckListFont\" id=\"__text39-__list7-0\">CLS4SIC_SGT_MASTER_DATA, please see attached note.</span></p>", "noteVersion": 12, "refer_note": [{"note": "2471098", "noteTitle": "2471098 - Conversion of Default Segment Values Data", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are doing a system conversion to SAP S/4HANA 1709 (S4CORE 102), and you are using segmentation and default segment values in SAP Fashion Management.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>SAP Fashion Management, SAP ERP, Segmentation</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>In SAP Fashion Management, system was equipped with two ways to maintain default segments that can be consumed by different documents. Few applications use the data in the table SGT_CATDYN for the determination of default segment value. Few applications use the data in the tables SGT_SEGVAL and SGT_SEGVAL_STG. Both of them have different data models and data maintenance tools.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>In S/4HANA, single data model combines the functionality and advantages of both the data model and single data maintenance tool to handle the data maintenance. The data present in the above mentioned tables will be converted and stored in the tables SGT_DYNCAT and SGT_DYNVAL under a single data model. Applications will start determining the default segment by using the new data model.</p>\n<p>During the conversion, the following report is executed automatically R_SGT_DEFAULT_MIG.<br/>In case of problems during the conversion, you can execute these reports manually.</p>", "noteVersion": 3}, {"note": "2502552", "noteTitle": "2502552 - S4TC - SAP S/4HANA Conversion & Upgrade new Simplification Item Checks", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are planning to perform a system conversion from SAP ERP 6.0 (or higher) to SAP S/4HANA 1709 (or higher), or you are planning to perform an upgrade on SAP S/4HANA and your upgrade target is SAP S/4HANA 1709 or higher.<br/>This note currently covers the following <span>target</span> releases:<br/>- <strong>SAP S/4HANA 1709</strong> all SP-levels<br/>- <strong>SAP S/4HANA 1809 </strong>all SP-levels<br/>- <strong>SAP S/4HANA 1909 </strong>all SP-levels<br/>- <strong>SAP S/4HANA 2020 </strong>all SP-levels <br/>- <strong>SAP S/4HANA 2021</strong> all SP-levels<br/>- <strong>SAP S/4HANA 2022</strong> all SP-levels<br/>- <strong>SAP S/4HANA 2023</strong> until FPS01</p>\n<p><em>Please note that the checks are implemented on the source release, but relevancy is determined by the desired target release.</em></p>\n<p><em>If your target release is SAP S/4HANA 1511 or SAP S/4HANA 1610 please refer to note 2182725.<br/></em></p>\n<p><em><strong>Please note:</strong> This note is <span>not</span> relevant if your upgrade target release is only a SP-Upgrade <br/>This SAP note is subject to change. Changes are logged in this notes text.</em></p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>S4PC, Conversion, S/4HANA, Pre-Check, Simplification Item, TCI, SIC, SI-Check, S4TC, S4SI, 1809, 1909, 2020, 2021, 2022, 2023</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>In order to identify all simplifications that apply to your SAP system, you can run the Simplification Item Checks (SI-Checks). The results will tell you all affected Simplification Items and ensures your system is in a consistent state so the system conversion or upgrade will succeed.<br/>The new check report is delivered with note 2399707. The report calls the check classes that are delivered with this note.</p>\n<p>This note uses the advanced transport-based correction instructions called TCI. As a prerequisite to enable your system to install these TCI notes please follow the below instructions.</p>\n<p>This note is not a TCI note, cause it acts as a master note. All check classes will be delivered by the prerequisite notes of this one. <br/>One of these prerequisite notes is the TCI note. Please note that the TCI (it is also mentioned in the text below) should be implemented <span>before</span> implementing 2502552.</p>\n<p>This note is not relevant if you are in an pure SP-upgrade scenario (e.g. 1709 SP00 -&gt; 1709 SP01). It is relevant for conversions (ERP 6.0 -&gt; S/4HANA) and release upgrades (e.g. 1709 -&gt; 1809).</p>\n<p><span>Prerequisites:</span></p>\n<p>Follow the descriptions in the attachment \"TCI_for_Customer.pdf\" from note 2187425.<br/>It describes how to enable SNOTE for implementation of TCI's, as well as how to enable SNOTE for deimplementation of TCI's.</p>\n<p>Please note that the TCI enablement needs to be done in every system before a TCI can be implemented. This applies also to systems were the TCI gets implemented via transport (like the Q or P system).</p>\n<p><span>SAP Download Service (new feature):</span></p>\n<p>The SAP Download Service is a feature for the SNOTE that allows to download the notes as digitally signed notes via SNOTE. It also allows to download TCI-files from TCI notes automatically.<br/>This means that the points 5. and 6. (from below) are not required anymore as they will be handled by the SAP Download service once it is activated.<br/>As a prerequisite to activate the download service please perform the TCI enablement before.<br/>See notes <a href=\"/notes/2576306\" target=\"_blank\">2576306</a> and <a href=\"/notes/2554853\" target=\"_blank\">2554853</a> for more information about the SAP Download Service.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Implement the note to get the S/4HANA Simplification Item Checks.</p>\n<p><span>Process to implement this note and the TCI's:</span><br/>(Please note that this is a step by step approach - the correct order is important)</p>\n<ol type=\"1\">\n<li>Ensure the SPAM version is 71 or above.</li>\n<li>a) Perform the preparation tasks described in the attachment \"TCI_for_Customer.pdf\" from note 2187425.<br/>b) Please also activate the rollback functionality of the TCI's (also described in the attachment \"TCI_for_Customer.pdf\").</li>\n<li>Please read this point carefully: <br/>-If the system has no direct access to SAPOSS (this is the case if you cannot download notes in SNOTE directly and you need to upload notes manually) <br/>-<span>and</span> if your SAP_BASIS  version is lower than 731 (SAP_BASIS &lt; 731): <br/>Only if both above conditions are true then <span>download</span> (and do <span>not</span> implement) note 1995550 and note 2408383 after TCI enablement (this solves an error that can happen during the conversion and needs to be done independently to the TCI enablement). Please no not implement the notes 1995550 and 2408383 in SAP_BASIS &lt; 731, <span>only</span> download it. <br/>Ignore this step if any of the both conditions from this step 3 do not fit.</li>\n<li>Implement note 2499199 if applicable after TCI enablement.</li>\n<li>If SAP Download Service is enabled you can skip the points 5. and 6. and continue with 7.<br/>Otherwise download the correction instructions of note 3304656 that are relevant for your corresponding SAP_APPL and ECC-DIMP or S4CORE release as a local file to your PC (you'll find either S4CORE alone or SAP_APPL and ECC-DIMP component in your system). Required are the .SAR files e.g.: \"K61800C<strong>CP</strong>SAPAPPL.SAR\"</li>\n<li>Upload the .SAR files from 5. to your system via the transaction SPAM (from front end server) or via SNOTE-&gt;GoTo-&gt;Upload TCI. Then continue with step 7 (cause the files will be implemented via the SAP note). </li>\n<li>De-Implement SAP note 2502552 (if already implemented).<br/><strong>Download and implement the TCI 3304656 afterwards via SNOTE.</strong></li>\n<li><strong>Implement SAP note 2502552 now (after 3304656).</strong></li>\n<li>Afterwards you can execute the report \"/SDF/RC_START_CHECK\" to get the check results of the SAP S/4HANA conversion checks for SAP S/4HANA 1809 and above. The report is delivered with note 2399707. <br/>For the most accurate results please execute the report in <strong>client 000</strong>.</li>\n</ol>\n<p><span><strong>Please note:</strong></span> -Steps 1.-2a. need to be done in every system separately (consider step 3 as well). Steps 2b, 4.-9. can be transported but they require Steps 1.-2a. before transport implementation.<br/>                     -Note: From SPAM version 70 on also the TCI enablement (steps 1.-2a) is transportable.<br/>                     -Steps 1.-3. should be done in client 000, steps 4.-9. can be done in client 000 or in the customer client.<br/>                     -This note 2502552 implements only notes for the Simplification Item Checks. It is not required that you read every note that is being implemented <span>by</span> this note. <br/>                      The SI-Check Framework will point you to the notes that are necessary to be read.<br/><br/></p>\n<p>Info: There are additional notes that are delivered as prerequisites of this note. They should be pulled automatically into your system if they are applicable. The list is just for checking the completeness. These notes are:</p>\n<div class=\"table-responsive\"><table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\"><colgroup> <col width=\"96\"/> <col width=\"782\"/></colgroup>\n<tbody>\n<tr>\n<td class=\"xl65\" height=\"20\" width=\"96\">2314696</td>\n<td class=\"xl65\" width=\"782\">S4TC CEEISUT Master Check for S/4 System Conversion Checks</td>\n</tr>\n<tr>\n<td class=\"xl65\" height=\"20\" width=\"96\">2441434</td>\n<td class=\"xl65\" width=\"782\">S4TC SAP Supplier Lifecycle Management Transition Prechecks (SIC)</td>\n</tr>\n<tr>\n<td class=\"xl65\" height=\"20\" width=\"96\">2445069</td>\n<td class=\"xl65\" width=\"782\">S4TC CPRXRPM Master Check for S/4 System Conversion Checks  (new Simplification Item Checks)</td>\n</tr>\n<tr>\n<td class=\"xl65\" height=\"20\" width=\"96\">2450623</td>\n<td class=\"xl65\" width=\"782\">S4TC OGSD Simplification Item Checks</td>\n</tr>\n<tr>\n<td class=\"xl65\" height=\"20\" width=\"96\">2455430</td>\n<td class=\"xl65\" width=\"782\">S4TC Ariba BS Add-On Master Check for S/4 System Conversion Checks (SIC)</td>\n</tr>\n<tr>\n<td class=\"xl65\" height=\"20\" width=\"96\">2455870</td>\n<td class=\"xl65\" width=\"782\">S4TC IS-M Transition to S/4HANA Simplification Item Check of Industry Solution Media</td>\n</tr>\n<tr>\n<td class=\"xl65\" height=\"20\" width=\"96\">2456502</td>\n<td class=\"xl65\" width=\"782\">S4TC IS-UT Master Check for S/4 System Conversion Checks (New Simplification Item Checks)</td>\n</tr>\n<tr>\n<td class=\"xl65\" height=\"20\" width=\"96\">2470117</td>\n<td class=\"xl65\" width=\"782\">S4TC OTAS Transition to S/4HANA Simplification Item Checks</td>\n</tr>\n<tr>\n<td class=\"xl65\" height=\"20\" width=\"96\">2477311</td>\n<td class=\"xl65\" width=\"782\">S4TC MDG_MDC Master Data Consolidation check for S/4 upgrade</td>\n</tr>\n<tr>\n<td class=\"xl65\" height=\"20\" width=\"96\">2477318</td>\n<td class=\"xl65\" width=\"782\">S4TC MDG_APPL Master Data Governance check for S/4 upgrade</td>\n</tr>\n<tr>\n<td class=\"xl65\" height=\"20\" width=\"96\">2478308</td>\n<td class=\"xl65\" width=\"782\">S4TC SRM_SERVER pre-check class for S/4 system conversion checks (SIC)</td>\n</tr>\n<tr>\n<td class=\"xl65\" height=\"20\" width=\"96\">2481172</td>\n<td class=\"xl65\" width=\"782\">S4TC IS-OIL Master Check for S/4 System Conversion Checks (New Simplification Item Checks)</td>\n</tr>\n<tr>\n<td class=\"xl65\" height=\"20\" width=\"96\">2481713</td>\n<td class=\"xl65\" width=\"782\">S4TC ACM Pre-Checks for Data Migration</td>\n</tr>\n<tr>\n<td class=\"xl65\" height=\"20\" width=\"96\">2482812</td>\n<td class=\"xl65\" width=\"782\">S4TC EA-PS Migration to the new pre-check framework</td>\n</tr>\n<tr>\n<td class=\"xl65\" height=\"20\" width=\"96\">2528547</td>\n<td class=\"xl65\" width=\"782\">S4TC CML - Pre-transition check for RE Classic</td>\n</tr>\n<tr>\n<td class=\"xl65\" height=\"20\" width=\"96\">2599127</td>\n<td class=\"xl65\" width=\"782\">CL_FVD_S4TC_CHK - error in pre_check routine</td>\n</tr>\n<tr>\n<td class=\"xl65\" height=\"20\" width=\"96\">2965337</td>\n<td class=\"xl65\" width=\"782\">S4TWL - Advanced Track &amp; Trace</td>\n</tr>\n<tr>\n<td class=\"xl65\" height=\"20\" width=\"96\">2665372</td>\n<td class=\"xl65\" width=\"782\">ERECRUIT Check</td>\n</tr>\n<tr>\n<td class=\"xl65\" height=\"20\" width=\"96\">2998453</td>\n<td class=\"xl65\" width=\"782\">Check LSO activation status in S4 landscape</td>\n</tr>\n<tr>\n<td class=\"xl65\" height=\"20\" width=\"96\">2689990</td>\n<td class=\"xl65\" width=\"782\">S4TC Commercial Project Management Master Check for S/4 System Conversion</td>\n</tr>\n<tr>\n<td class=\"xl65\" height=\"20\" width=\"96\">2690079</td>\n<td class=\"xl65\" width=\"782\">S4TC Project Issue And Change Management Master Check for S/4 System Conversion</td>\n</tr>\n<tr>\n<td class=\"xl65\" height=\"20\" width=\"96\">2820270</td>\n<td class=\"xl65\" width=\"782\">Pre-Check class system conversion for CTS_PLUG</td>\n</tr>\n<tr>\n<td class=\"xl65\" height=\"20\" width=\"96\">3009161</td>\n<td class=\"xl65\" width=\"782\">SEPA Mandate for Vendors</td>\n</tr>\n<tr>\n<td class=\"xl65\" height=\"20\" width=\"96\">3009162</td>\n<td class=\"xl65\" width=\"782\">Simplification Check in BD001 and BC001 Tables</td>\n</tr>\n<tr>\n<td class=\"xl65\" height=\"20\" width=\"96\">3323640</td>\n<td class=\"xl65\" width=\"782\">SAP HCM Simplification Item Check</td>\n</tr>\n<tr>\n<td class=\"xl65\" height=\"20\" width=\"96\">3394900</td>\n<td class=\"xl65\" width=\"782\">Delivery of check class CLS4SIC_HR_ECPAYROLL_BADI for SI35: HCM_EC_PAYROLL_BAdIs</td>\n</tr>\n</tbody>\n</table></div>\n<p><span>Additional corrections for the TCI (also pulled automatically):</span></p>\n<div class=\"table-responsive\"><table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\"><colgroup> <col width=\"164\"/></colgroup>\n<tbody>\n<tr>\n<td class=\"xl65\" height=\"21\" width=\"164\">3071413</td>\n</tr>\n<tr>\n<td class=\"xl65\" height=\"21\" width=\"164\">2332472</td>\n</tr>\n<tr>\n<td class=\"xl65\" height=\"21\" width=\"164\">3194465</td>\n</tr>\n<tr>\n<td class=\"xl65\" height=\"21\" width=\"164\">3106485</td>\n</tr>\n</tbody>\n</table></div>\n<p><span>Review the change log to see whether it contains relevant changes.Process to update this note if a more recent version is available:</span></p>\n<ol type=\"1\">\n<li>Follow the steps 1.-9. (they might be updated in case of a new TCI note). Skip the points that are done already.</li>\n<li>Download this note again and implement it via transaction SNOTE.</li>\n<li>If you face issues during note re-implementation please make sure that you de-implement the note in a first step and implement it again afterwards as the second steps.</li>\n</ol>\n<p><span>Most common complications/issues and their solution:</span></p>\n<ul>\n<li>The most common issues arise from incomplete TCI-Enablement in the system. Please make sure you have followed the instructions step by step.<br/>The TCI-Enablement is complete if you have followed the instructions completely and afterwards you can see the option \"Upload TCI\" in transaction SNOTE-&gt;GoTo-&gt;\"Upload TCI\".</li>\n<li>SNOTE or SPAM might dump during TCI enablementation cause of changed objects at runtime. Simply start the last action again.</li>\n<li>You get the error that check classes are missing, but the associated note is already implemented: There was a problem during activation of the objects. Go to the note in SNOTE and choose right-click-&gt;\"Activate SAP Note Manually\". </li>\n<li>While releasing your transport you get the error that for certain objects no object directory entries exist: This error can be ignored. You can release the transports anyway. Please do not delete the objects from the transport request as this will result in subsequent errors. The error is the result of the deletion of an object that does not exist on the system and therefore no directory entry exists. Implement SAP note 2569813 and see SAP note 2515366.</li>\n<li>The SPAU comes up while implementing the TCI note: Related MFLE objects from previous pre-check implementations can be reset to original.<br/>Please re-implement notes with corrections to the SI-Check classes if they come up in SPAU.</li>\n<li>If you have implemented a previous version of this note with one of the previous TCI notes (2418800, 2503309, 2538018, 2598422 or 2617167, 2708143, 2741379, 2761703, 2910131, 3028788, 3143951) please make sure that you do implement the transports in the correct order. Please do not implement one of the previous TCI notes after the latest TCI. Latest is 3304656.</li>\n<li>If you have old kernel releases please set the ABAP screen generation to \"never\". (SPAM-&gt;Extras-&gt;Settings-&gt;ABAP/Screen Generation-&gt;Never). This might avoid syntax errors during TCI implementation that can occur due to low kernel releases. </li>\n<li>The following SAP blog may help: https://blogs.sap.com/2018/03/26/sap-s4hana-simplification-item-check-how-to-do-it-right./</li>\n<li>The SI-Check Framework is not required if you perform a pure SP-upgrade (like SP01 -&gt; SP02) - only for major release upgrades.</li>\n<li>During TCI implementation you get the error \"Data for SAP note [...] incomplete. Solution: Implement the SAP note 2627665 and try again.</li>\n<li>During TCI implementation you get the error \"Delivery event [...] is not in your system\" or \"Unable to find delivery event [...]\", Solution: Implement the SAP note 2671774 and try again.</li>\n<li>Runtime error in class CLS4SIC_LSO_ACTIVE_STATUS due to missing function module LSO_ACTIVATION_CONSISTENCY_CHK, Solution: Implement note 1812655.</li>\n<li>In report /SDF/RC_START_CHECK you cannot choose a target release: There is no transition database content in the system. Use the button to download from the report or download and upload it manually from the Simplification Item database https://launchpad.support.sap.com/#/sic/overview. <br/>Please note that the report is only relevant if there is a higher SAP S/4HANA release available. If the system is on the highest available release no upgrade target can be selected. Wait until the next release is available.</li>\n<li>The note 2502552 remains with status \"Incompletely implemented\": The status of the note is not updated correctly. Implement the note 2755890 and 3196697. Afterwards \"check\" the SAP note 2502552 again in SNOTE. Then the status will be updated correctly.</li>\n<li>You get the error \"Not all required corrections were implemented for note\" when trying to implement the note. Try to implement note 3196697 / (3236917) for a correction.<br/>In addition please open the note 2502552 from your SNOTE. Then use the evaluations on the right side to show and display all prerequisite notes. Please try to implement these note manually one by one. </li>\n<li>If the mentioned TCI is not valid for your system then it is most likely cause it is already contained in the current SP level. In this case the TCI does not need to be implemented.</li>\n<li>You did the TCI enablement but you are not able to see the \"Upload TCI\" option in SNOTE-&gt;GoTo. Please see note 2682266.</li>\n<li>The check DO_SFWPARAM_CHECK might come up again during SUM execution despite the corresponding fix was done already.<br/>To prevent this please implement note 1808606 and 2035728 before the upgrade. If this occurs again simply the fix need to be done again.</li>\n<li>If you are already on a very high SAP_APPL / S4CORE SP level then it might be that the TCI note is not implementable (\"Cannot be implemented\") in SNOTE. Then this is perfectly fine and you can continue to implement note 2502552.</li>\n</ul>\n<p><span>Change log:<br/></span>Version 110: Only a small adjustment in the note text to implement prerequisite notes one by one if the implementation of 2502552 aborts with error.<br/>Version 109: Only a small adjustment in the note text.</p>\n<p>Version 107: Added SAP ECPAYROLL Check class 3394900 and other notes corrections (3392898, 3327713, 3055691)<br/>Version 106: Only a small adjustment in the note text.<br/>Version 105: Adjustments for SAP S/4HANA 2023. Please use TCI note 3304656 (TCI #12)<br/>Version 104: Solution for \"Not all required corrections were implemented..\" message /Incompletely implemented\"-status -&gt; 3196697 / (alternative 3236917)<br/>Version 103: Added SAP HCM check class (3323640)<br/>Version 102: Added some correction notes (2478308, 3255919, 3335646, 3336805)<br/>Version 100: Only text adjustments. SAP S/4HANA 2022 FPS01 target release can be choosen with at least version 97.<br/>Version 99: Removed 1 hard dependency to note 3094714.<br/>Version 98: Only text adjustments<br/>Version 97: Adjustments for SAP S/4HANA 2022. Please use TCI note 3143951<br/>Version 96: Adjustments to the ERECRUIT check<br/>Version 95: Added corrections; now also target SAP S/4HANA 2021 FPS02 is supported<br/>Version 94: Added 3151094 for SI10: FIN_MISC_FSBP_BP001_HR; now also target SAP S/4HANA 2021 FPS01 is supported<br/>Version 93: Added 3141545 for SI02: TM_CUSTOMIZING_2<br/>Version 92: Added small syntax correction for low EHP7 releases and S13: MasterData<br/>Version 91: Added small corrections for SI28: CT_CHK_ITEM_DEVICE with note 3106485.<br/>Version 90: Adjustments for SAP S/4HANA 2021. Please use TCI note 3028788.<br/>Version 89: Minor adjustment (removed 2850333 from prerequisites)<br/>Version 88: Minor adjustment in the dependencies on start release S4CORE. (Note 2665372 added as dependent)<br/>Version 87: This note can be used for SAP S/4HANA 2020 SP02, added note 2850333.<br/>Version 86: Minor correction of prerequisite for swc ERECRUIT<br/>Version 85: Correction added 3011425<br/>Version 84: Corrections added 3009161 and 3009162<br/>Version 83: Correction added 2998453 - EA-HRGXX<br/>Version 82: Correction added 2994093<br/>Version 81: Correction added to note 2965544 - EA-HRGXX<br/>Version 80: Added correction 2982137 and 2984456 and 2982157<br/>Version 79: New TCI note 2910131 added. This TCI is required for target release SAP S/4HANA 2020 Initial Shipment Stack.<br/>Version 78: Removed prerequisite for STTPEC due to tech. issues. Implement manually if necessary -&gt; note 2965337.<br/>Version 75: New STTPEC correction 2903240<br/>Version 73: Added correction 2894923<br/>Version 71: only text adjustments<br/>Version 70: Added <em>3</em> <em>additional corrections</em>.<br/>Version 69: Added new corrections.<br/>Version 68: New TCI note 2761703 added. This TCI is required for target release SAP S/4HANA 1909 Initial Shipment Stack.<br/>Version 66: Added SAP_BASIS corrections to avoid implementation errors (no need to update if already implemented).<br/>Version 65: Added additional corrections.<br/>Version 63: New TCI note 2741379 added. This TCI is required for target release SAP S/4HANA 1809 SP02, text adjustments.<br/>Version 61: Added new corrections for check classes for SI2: Logistics_PP-MRP, SI2: MasterData_BP, SI1: Logistics_MM-IM, SI1_FIN_GL, SI1:_Logistics_General_ATT<br/>Version 60: Changes to validity on SAP S/4HANA 1809. Only necessary if you have problems implementing this note on 1809.<br/>Version 59: New generic check added, corrections for DIMP and BP-Check<br/>Version 58: Requirements for target release SAP S/4HANA 1809 SP01 with necessary dependencies. New TCI Note #6: 2708143; add. necessary adjustments<br/>Version 55: Added check class for item \"SI2: Logistics_EHS - Occupational Health\" and corrections for \"SI59: Logistics_General\"<br/>Version 54: Added correction for checks of SI25: CMM_MTM_ACCOUNTING, SI2: FIN_AA, ECC-DIMP, SI01: TM_CUSTOMIZING<br/>Version 53: Only text adjustments, no need to update.<br/>Version 51: Only adjustments to manual activity to reflect the right releases.<br/>Version 47: Fixed possible runtime error in SI22: GENERIC_CHECKS. <br/>Version 46: Requirements for SAP S/4HANA 1809 with necessary dependencies. New TCI Note: 2617167<br/>Version 45: Added note 2677603 (correction for SI04: CM_ROUTINES), adjustments to chapter \"Complications/issues and their solution:\" <br/>Version 44: No need to upgrade compared to version 41. Only internal adjustments.<br/>Version 41: Added note 2666169 (correction for SI25: CMM_MTM_ACCOUNTING).<br/>Version 39: Only text adjustments, no need to implement version 39 if you are on 38 already.<br/>Version 38: Latest TCI note is 2598422. This note will replace all previous TCI notes for the SI-Check Framework.<br/>Version 36: Added CI for S4CORE 102, text adjustments.<br/>Version 35: Added notes 2603883 (memory optimization for SI1: Logistics_MM-IM), 2609584 (SI04: CM_ROUTINES), 2612303 (SI50: Logistics_General).<br/>Version 34: Added notes 2597446 (Speedboost for SI1: Logistics_MM-IM), 2597136 (DIMP), 2599168 (SI3: GSFIN_LOCPTFM) &amp; 2599127 (RE Classic (EA-FINSERV)).<br/>Version 33: Only text adjustments.<br/>Version 32: Added note 2597310 (SI3: GSFIN_LOCPTFM), Solves an syntax error during implementation. Adjusted implementation procedure if SAP Download Service is activated.<br/>Version 29: Added notes 2589568 (SI2: Logistics_PP-MRP), 2590563 (SI1: Logistics_General) and 2581131 (SI1: Logistics_MM-IM) to the automatic correction instruction; minor text changes.<br/>Version 28: Added note 2585925 (MM_IM_SI11) to the automatic correction instruction.<br/>Version 27: Added note 2585500 (DIMP) to the automatic correction instruction.<br/>Version 26: Added notes 2575751 (SI2: Logistics_PP-MRP) and 2578735 (SI1: Logistics_General) to the automatic correction instruction.<br/>Version 25: Added note 2572185 (SI26: OIL_ITSW) to the automatic correction instruction.<br/>Version 24: Only text adjustments.<br/>Version 23: Changed TCI note to 2538018. This note will replace all previous TCI notes for the SI-Check Framework.<br/>                  SPAM version 66 is now required. TCI's now allow rollback. Check SPAM-&gt;Extras-&gt;Settings-&gt;\"Create backups for Transport based Correction Instruction\" is marked.<br/>                  No more post-implementation activity required - automatic now.<br/>Version 22: Added note 2570699 to the post implementation activity for SAP_APPL; note text adjustments.<br/>Version 21: Note 2528547 added for RE Classic (EA-FINSERV), added note 2568861 into post implementation activity for SAP_APPL.<br/>Version 20: Note 2558904 is not needed until release of SAP S/4HANA 1709 FPS01.<br/>Version 19: Added note 2558904 to the post implementation activity, note 2546795 is now relevant for all releases of SAP_APPL.<br/>Version 18: Added notes 2554342, 2555240 and 2555266 to the post implementation activity of SAP_APPL. (corrections and performance improvements for long running checks)<br/>Version 17: Added notes 2539490, 2544894, 2546795 and 2552657 to the post implementation activities.<br/>Version 16: Added note 2540865 and 2542716 to the post implementation activities. <br/>Version 15: Added corrections to the post implementation activity of SAP_APPL and S4CORE. Added \"Info\" in note description.<br/>Version 14: Only adjusted the post implementation activity for S4CORE.<br/>Version 13: Added note 2534355 to the post implementation activity. Please apply if possible. Changed Status to \"Released for Customer\".<br/>Version 12: Added note 2515316 and 2531113 to the post implementation activity. Please apply them if possible.<br/>Version 11: Removed note 2516686 from post-impl. activity. Please de-implement it. (if note remains there might be unnecassary tasks pointed out by the SI-Checks).<br/>Version 10: Added note 2524011 and 2523644 into the post implementation activity for their available releases - please apply the notes via SNOTE.<br/>Version 9: Added note 2468295 and 2525628 into the post implementation activity for their available releases - please apply the notes via SNOTE.<br/>Version 8: Added note 2523159 into the post implementation activity for all available releases - please apply the note via SNOTE.</p>", "noteVersion": 110, "refer_note": [{"note": "2399707", "noteTitle": "2399707 - Simplification Item Check", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>As part of your system’s SAP Readiness Check assessment, you are interested in performing a simplification item relevance check, compatibility scope relevancy check, or a simplification item consistency check. Your intent in conducting this analysis is to support the scoping and planning of either the conversion from SAP ERP to SAP S/4HANA or the upgrade from one SAP S/4HANA product version to another SAP S/4HANA product version.</p>\n<p>Alternatively, you are interested in manually performing a simplification item relevancy or consistency check outside SAP Readiness Check.</p>\n<p>To learn more about how to perform simplification item checks and the relation to SAP Readiness Check, you can explore the following links:</p>\n<p><a href=\"https://blogs.sap.com/2018/03/26/sap-s4hana-simplification-item-check-how-to-do-it-right./\" target=\"_blank\">https://blogs.sap.com/2018/03/26/sap-s4hana-simplification-item-check-how-to-do-it-right./</a></p>\n<p><a href=\"https://blogs.sap.com/2020/01/02/simplification-item-catalog-simplification-item-check-and-sap-readiness-check-for-sap-s-4hana/\" target=\"_blank\">https://blogs.sap.com/2020/01/02/simplification-item-catalog-simplification-item-check-and-sap-readiness-check-for-sap-s-4hana/</a></p>\n<p><strong>We strongly recommend analyzing a production system; otherwise, the results might be incomplete or misleading. </strong>If you choose to use another environment, for instance, a copy of a production system, implement and follow SAP Note <a href=\"/notes/2568736\" target=\"_blank\">2568736</a> (in both the productive and non-production systems) to capture and upload the necessary ST03N data.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>SAP Readiness Check, SAP S/4HANA Conversion; SAP S/4HANA Upgrade; Simplification Item, Simplification List, Compatibility Scope</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>Before implementing this SAP Note (2399707), we strongly recommend implementing the latest version of SAP Note <a href=\"/notes/1668882\" target=\"_blank\">1668882</a>.</p>\n<p>In addition, before executing the simplification item analysis in your system, we strongly recommend implementing SAP Note <a href=\"/notes/2502552\" target=\"_blank\">2502552</a>. This note delivers check classes used to refine the list of relevant simplification items.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong>Contents<br/></strong>  1.  Introduction<br/>  2.  Enabling the Simplification Item Checks<br/>       2.1. Note Implementation<br/>       2.2. Simplification Item Catalog Maintenance<br/>  3.  Executing the Simplification Item Checks<br/>       3.1. Using the Data Collection Framework for SAP Readiness Check<br/>       3.2. Executing the Checks Manually<br/>  4.  Reviewing the Results in /SDF/RC_START_CHECK<br/>       4.1. Interpreting the Check Results<br/>       4.2. Administrative Functions within the Check Results View<br/>  5.  Additional Information<br/>  6.  Frequently Asked Questions</p>\n<p><strong>  1. Introduction</strong></p>\n<p>While scoping and planning the conversion of your SAP ERP system to SAP S/4HANA, or the upgrade of an SAP S/4HANA system, we recommend analyzing the impact on your system based on the list of documented simplification items. This note provides the simplification item check capabilities to analyze your system. There are two types of checks integrated into this solution:</p>\n<ul>\n<li><strong>Relevance Check:</strong> Produces a customized list of relevant simplification items for your system. The relevance is determined based on rules maintained in the <a href=\"https://launchpad.support.sap.com/#sic\" target=\"_blank\">simplification item catalog</a>. </li>\n</ul>\n<ul>\n<ul>\n<li>If the rule is not maintained or cannot be processed, the item has the status <em>Relevance to Be Checked</em>.</li>\n<li>For simplification items where the rule evaluates database table content, the analysis evaluates table entries across all clients within the system.</li>\n</ul>\n</ul>\n<p>The results of the relevance check are presented in the <em>Simplification Items</em> tile and the <em>Compatibility Scope Analysis</em> tile within SAP Readiness Check for SAP S/4HANA and SAP Readiness Check for SAP S/4HANA upgrades.</p>\n<ul>\n<li><strong>Consistency Check:</strong> When initiated in client 000, this check analyzes the consistency of the system in preparation for the conversion or upgrade using Software Update Manager. The consistency check only evaluates those simplification items marked as relevant or potentially relevant to the system.</li>\n</ul>\n<p>We recommend resolving all identified inconsistencies before the downtime in Software Update Manager. Otherwise, when Software Update Manager encounters unresolved consistency issues, it will need to be reset or restarted. The check also warns you about critical changes during the conversion or upgrade, for example, potential data loss. To continue, you must confirm that you have understood the warning.</p>\n<p>Individual check classes, developed by the associated application area, are leveraged during the consistency check evaluation. The implementation of SAP Note <a href=\"/notes/2502552\" target=\"_blank\">2502552</a> delivers these classes to your system.</p>\n<p>To initiate the consistency check, you can either activate the option within the SAP Readiness Check selection screen (report RC_COLLECT_ANALYSIS_DATA) or use the simplification item check report (/SDF/RC_START_CHECK).</p>\n<p>The results of the consistency check, when initiated by SAP Readiness Check (report RC_COLLECT_ANALYSIS_DATA) in client 000, can be uploaded to an existing analysis for the productive client. The results are then visible within the detailed view of the <em>Simplification Items</em> tile.</p>\n<p>Detailed results are available in the simplification item check report (/SDF/RC_START_CHECK) by selecting <em>Display Last Check Result</em>. The results are only visible in the client used to perform the analysis (for instance, client 000).</p>\n<p><strong>Note</strong>: The check results include references to SAP Notes that describe how to resolve identified issues.</p>\n<p>To identify and resolve possible issues within time, you must <strong>correctly implement and run the simplification item checks before</strong> starting the technical conversion or upgrade. To allocate sufficient time and resources to resolve any potential issues, we recommend initially performing the analysis as part of the scoping and planning phase of the project within SAP Readiness Check. The check is performed one additional time by the Software Update Manager toolset shortly before the technical downtime of the conversion or upgrade of a system. But do not wait to implement and execute the check just before starting Software Update Manager; otherwise, there is a potential risk to the project timeline.</p>\n<p><strong>  2. Enabling the Simplification Item Checks</strong></p>\n<p>2.1. Note Implementation</p>\n<p>Depending on the target software level of your system conversion or release upgrade, specific <strong>minimum versions</strong> of this SAP Note (2399707) and SAP Note <a href=\"/notes/2502552\" target=\"_blank\">2502552</a> are required. Otherwise, the check may be incomplete or inaccurate.</p>\n<p>The minimum versions are (If the Support Package Stack is not specifically listed, then its version requirement defaults to match that of the closest Support Package Stack.):</p>\n<ul>\n<ul>\n<ul>\n<li>SAP S/4HANA 2021 Initial Shipment</li>\n<ul>\n<li>2399707: Minimum recommended version 147 (Minimum technical version 82)</li>\n<li>2502552: version 97</li>\n</ul>\n<li>SAP S/4HANA 2022 Initial Shipment</li>\n<ul>\n<li>2399707: Minimum recommended version 154 (Minimum technical version 82)</li>\n<li>2502552: version 97</li>\n</ul>\n<li>SAP S/4HANA 2022 Feature Package 1</li>\n<ul>\n<li>2399707: Minimum recommended version 155 (Minimum technical version 82)</li>\n<li>2502552: version 97</li>\n</ul>\n<li>SAP S/4HANA 2023 Initial Shipment</li>\n<ul>\n<li>2399707: Minimum recommended version 161 (Minimum technical version 82)</li>\n<li>2502552: version 105</li>\n</ul>\n</ul>\n</ul>\n</ul>\n<p>While the minimum technical version of SAP Note 2399707 enables the conversion process to proceed, the minimum version may not reveal all application-level issues. As a result, we advise using at least the recommended version referenced above.</p>\n<p>Regardless of the minimum note version, when starting your project, it is recommended to <strong>use the most recent version of SAP Notes 2399707 and 2502552</strong>. When you reach the hard-freeze phase in your project, you should also freeze the version of these notes.</p>\n<p>2.2. Simplification Item Catalog Maintenance</p>\n<p>The simplification item catalog contains a list of generally available target product versions and documented simplification items. A local replica is imported into the system to support the execution of the simplification item checks.</p>\n<p>The initial implementation of the check report (/SDF/RC_START_CHECK) includes the most recent version of the simplification item catalog. While continuing to scope and plan the project, we recommend using an up-to-date version of the simplification item catalog. Since conversion projects can take some time, you should consider and plan catalog updates during the project.</p>\n<p>While we advise you to use the most recent simplification item catalog content in early project phases, you should freeze and synchronize this content as part of the hard-freeze phase in your project. As you prepare to enter the hard-freeze phase of your project, <strong>download the local replica of the simplification item catalog </strong>from the system where you plan to perform your final test conversion. You can download the catalog using the corresponding button on the /SDF/RC_START_CHECK selection screen. We recommend downloading the content before converting the system. Similarly, you can then upload this content using report /SDF/RC_START_CHECK to synchronize the version before performing checks in any subsequent systems.</p>\n<p>2.2.1. Updating the Simplification Item Catalog</p>\n<p>The SAP hosted version of the simplification item catalog is updated periodically to accommodate new product versions, introduce new simplification items, and integrate lessons learned from project experience. By default, the report /SDF/RC_START_CHECK does not automatically update the local replica of the simplification item catalog from SAP servers. If you want to update the content from the SAP servers, you explicitly need to trigger this via the <em>Update catalog with latest version from SAP</em> button in /SDF/RC_START_CHECK.</p>\n<p>If no connection exists between the system and the SAP support backbone, you could download the content as an archive directly from the <a href=\"https://me.sap.com/sic\" target=\"_blank\">simplification item catalog</a> site. Once you download the archive to your local machine, you can manually upload it in /SDF/RC_START_CHECK. The <em>Local Version</em> will be marked with a watch icon when the local replica is over 30 days old.</p>\n<p>The available functions for managing the local replica in /SDF/RC_START_CHECK are:</p>\n<ul>\n<ul>\n<ul>\n<li><em>Update Catalog with latest version from SAP: </em>Used to directly download the latest catalog version from SAP to update the local replica.</li>\n<li><em>Upload Simplification Item Catalog from file</em>: Used to upload a version of the catalog stored on your local machine. This option supports environments without direct connectivity to the SAP support backbone. Additionally, it supports synchronizing the catalog version across systems. </li>\n<li><em>Download Current Simplification Item Catalog</em>: Used to download a copy of the local replica version to your local machine as a backup before updating the catalog. It also supports synchronizing the catalog version across systems.</li>\n</ul>\n</ul>\n</ul>\n<p><strong>  3. Executing the Simplification Item Checks<br/></strong></p>\n<p>As stated previously, the simplification item checks (that is, the relevance check and the consistency check) are available via both the data collection framework for SAP Readiness Check (using report RC_COLLECT_ANALYSIS_DATA) and manually (using report /SDF/RC_START_CHECK).</p>\n<p>When running the simplification item checks close to the technical execution of the conversion from SAP ERP to SAP S/4HANA or the upgrade to a higher SAP S/4HANA product version, it is essential to follow the guidance below. <strong>Otherwise</strong>, the technical conversion or upgrade could end with an error, and you may have to <strong>reset and</strong> <strong>repeat the Software Update Manager procedure.</strong> If forced to reset Software Update Manager, update this SAP Note (2399707) and SAP Note <a href=\"/notes/2502552\" target=\"_blank\">2502552</a>, repeat the analysis, and resolve any new issues found before restarting the Software Update Manager procedure.</p>\n<p>3.1. Using the Data Collection Framework for SAP Readiness Check</p>\n<p>To enable the data collection framework to analyze the simplification item checks, enable the related options within the selection screen of report RC_COLLECT_ANALYSIS_DATA. The data collection framework includes the following options related to simplification items:</p>\n<ul>\n<ul>\n<li><em>Simplification Item and Compatibility Scope relevance</em>: By default, this check is active. We recommend performing this analysis on each of the system’s productive clients. Note that each client requires a separate analysis on the landing page for SAP Readiness Check.</li>\n<li><em>Simplification Item Effort Drivers</em>: This check is not enabled by default as it takes some additional time to collect the related information. However, we recommend enabling this analysis for clarity on the effort required to remediate simplification items that are ranked<em> potentially high</em>. Additionally, by analyzing the effort drivers, the default effort ranking for some <em>potentially high</em> simplification items may be reclassified, further supporting the project’s scoping and planning.</li>\n<li><em>Simplification Item Consistency</em>: This check should be performed within client 000, as this is the same client where Software Update Manager will analyze the system. Log on to client 000 and clear all other options within RC_COLLECT_ANALYSIS_DATA to enable this check. The relevance check will be automatically added to the scope when enabling the consistency check. You can then extend the analysis of the production client by updating it with the archive produced by the consistency check in client 000. Select the <em>Update Analysis</em> button on the dashboard view to add the archive.</li>\n</ul>\n</ul>\n<p>3.2. Executing the Checks Manually</p>\n<p>These are the steps required to execute the simplification item checks manually:</p>\n<ol><ol>\n<li>Start report /SDF/RC_START_CHECK in transaction SA38.</li>\n<li>In the <em>Simplification Item Check Options</em> section, choose the target SAP S/4HANA product version.</li>\n</ol></ol>\n<p>Note: If the target product version is not available in the list, follow the steps above to update the local replica of the simplification item catalog. Only released-to-customer product versions are visible in the list.</p>\n<ol><ol start=\"3\">\n<li>Choose the mode you want to perform the check:</li>\n</ol></ol><ol>\n<ul>\n<ul>\n<li><em>New relevance check in Online mode</em>: The result will be displayed immediately after the check completes.</li>\n<li><em>New relevance &amp; consistency check as background job</em>: We recommend this option when you expect a long runtime or you are uncertain of the runtime.</li>\n</ul>\n</ul>\n</ol><ol><ol start=\"4\">\n<li>Execute the check to receive the simplification item list.</li>\n<li>Review the results (see below).</li>\n</ol></ol>\n<p><strong>  4. Reviewing the Results in /SDF/RC_START_CHECK</strong></p>\n<p>When using the data collection framework for SAP Readiness Check to execute the simplification item checks, the results are collected in the generated archive and can be uploaded to the landing page for SAP Readiness Check: <a href=\"https://me.sap.com/readinesscheck\" target=\"_blank\">https://me.sap.com/readinesscheck</a>.</p>\n<p>Independently of how you initiated the simplification items evaluation, /SDF/RC_START_CHECK can be used to review the detailed results. To find the results, log on to client 000, start report /SDF/RC_START_CHECK, select the targeted SAP S/4HANA product version, then select the <em>Display Last Check Result</em> option from the <em>Simplification Item Check Options </em>section<em>,</em> and then choose <em>Execute</em>.</p>\n<p>Solve any errors identified by the consistency check, as this is <strong>mandatory</strong> for any conversion or upgrade to SAP S/4HANA 1809 or higher.</p>\n<p>4.1. Interpreting the Check Results</p>\n<p>Within the check result view, you will find the following:</p>\n<ul>\n<ul>\n<li>Basic information about a simplification item, including application area, ID, title, application component, category, and business impact note.</li>\n<li>Indication of whether the simplification item is relevant to the system or not.</li>\n<li>Results of the most recent consistency check, with an indication of whether the system is consistent, related to the simplification item, or not. Select the simplification item and choose <em>Display Consistency Check Log</em> to find the latest result and learn how to resolve the inconsistency.</li>\n<li>Indication of whether an exemption is possible for those simplification items ending in error. Some simplification items only require the acknowledgment of the customer. You need only apply an exemption to the simplification item in such cases. In doing so, you acknowledge that you have read and understood the message. Once an item is exempt, it will no longer block the conversion to the corresponding target SAP S/4HANA product version.</li>\n<li>A summary of the relevancy assessment of the simplification item.</li>\n</ul>\n</ul>\n<p>4.2. Administrative Functions within the Check Results View</p>\n<p>From the check results view, various operations regarding the consistency check are possible, including:</p>\n<ul>\n<ul>\n<li>Initiate a consistency check for all relevant items.</li>\n<li>Perform a detailed consistency check for a selected item.</li>\n<li>Display the consistency check log.</li>\n<li>Apply or revoke exemption for consistency check errors able to be skipped.</li>\n<li>Display exemption log.</li>\n</ul>\n</ul>\n<p><strong>  5. Additional Information</strong></p>\n<p>5.1. Executing simplification item checks for a newly released SAP S/4HANA Product Version, Feature Package Stack, or Support Package Stack</p>\n<p>Suppose you encounter the issue where you cannot see the newly released SAP S/4HANA feature package stack or support package stack in the target list. In that case, you need to update the simplification item catalog to the latest version available from SAP. See section 2.2.1 for more information.</p>\n<p>5.2. Framework for storing and managing the simplification item check logs</p>\n<p>The simplification item checks use the application log to retain the check logs. These logs are client-specific; therefore, you must ensure you are in the correct client when analyzing the check logs.</p>\n<p>The check logs produced by Software Update Manager are only available in client 000.</p>\n<p>5.3. Adding an exemption for an inconsistent simplification item</p>\n<p>The simplification item check only supports the exemption of an item when the consistency check return code is 7.</p>\n<p>When creating the exemption, you must make sure the selected target version in /SDF/RC_START_CHECK is the same as the target product version processed by Software Update Manager.</p>\n<p>5.4. Using a central download service system to download simplification item catalog updates</p>\n<p>If you use a central download service system as a communication server and the ST-PI version in the landscape is less than or equal to ST-PI 2008_1_700 SP27, ST-PI 2008_1_710 SP27, or ST-PI 740 SP17, you need to implement this SAP Note (2399707) in the central download service system.</p>\n<p>Additionally, you need to implement SAP Note <a href=\"/notes/2945785\" target=\"_blank\">2945785</a> in the central download service system.</p>\n<p>5.5. Determining the relevant application component to use when creating an SAP incident related to a simplification item</p>\n<p>If you encounter an issue with a specific simplification item, it is best to create the customer support incident under the item’s relevant application component.</p>\n<p>The correct component can be identified by following these steps:</p>\n<ol><ol>\n<li>Open the URL <a href=\"https://me.sap.com/sic\" target=\"_blank\">https://me.sap.com/sic</a> with your S-User.</li>\n<li>Search the item with the Check item ID.</li>\n<li>Open the item.</li>\n<li>Go to the <em>Application Component</em> tab on the detail page.</li>\n</ol></ol>\n<p><strong>  6. Frequently Asked Questions</strong></p>\n<p>6.1. What do I need to check before implementing this SAP Note (2399707)?</p>\n<ul>\n<ul>\n<li>We <strong>strongly recommend </strong>checking that the latest version of SAP Note <a href=\"/notes/1668882\" target=\"_blank\">1668882</a> is “completely implemented” before you implement this SAP Note.</li>\n</ul>\n</ul>\n<p>If you encounter an issue while installing or updating this SAP Note (2399707), for example, syntax error after implementation, perform the following steps:</p>\n<ul>\n<ul>\n<ul>\n<li>Reset the implementation of this SAP Note (2399707) using transaction SNOTE.</li>\n<li>Implement the latest version of SAP Note <a href=\"/notes/1668882\" target=\"_blank\">1668882</a>.</li>\n<li>Exit the transaction SNOTE.</li>\n<li>Open SNOTE and reimplement this SAP Note (2399707).</li>\n</ul>\n</ul>\n</ul>\n<ul>\n<ul>\n<li>When encountering the runtime error DDIC_TYPELENG_INCONSISTENT with report /SDF/RC_START_CHECK, first check to see whether SAP Note <a href=\"/notes/1229062\" target=\"_blank\">1229062</a> is valid for the system. If the SAP Note is not applicable, manually activate the following DDIC structures: SWNCAGGTASKTYPE, SWNCAGGUSERTCODE, SWNCAGGUSERWORKLOAD, and SWNCAGGTASKTIMES.</li>\n</ul>\n</ul>\n<p>6.2. Why do I get syntax errors when activating the objects in this SAP Note (2399707)?</p>\n<p>Usually, this is because the code context for the correction instruction is inconsistent. You can reset the implementation of this SAP Note (2399707) and then reimplement it.</p>\n<p>If this process does not resolve the issue, create a customer support incident under the component CA-TRS-PRCK.</p>\n<p>6.3. Where can I find the SAP Note for performing the simplification item check for SAP Readiness Check for SAP BW/4HANA?</p>\n<p>The SAP Readiness Check for SAP BW/4HANA approach evolved from analyzing a list of simplification items to evaluating the objects within the SAP BW system. Now, simplification item-related SAP Notes are available in the <em>Learn More</em> text and the tables provided within the detailed views.</p>\n<p>6.4. Is it possible to skip a simplification item consistency check?</p>\n<p>Yes, it is technically possible to skip a simplification item consistency check. However, you must create a customer support incident under the component CA-TRS-TDBT requesting access to SAP Note <a href=\"/notes/2641675\" target=\"_blank\">2641675</a>.</p>\n<p>6.5. How can I resolve the warning in /SDF/RC_START_CHECK that I cannot fetch the simplification item catalog through the SAP-SUPPORT_PORTAL HTTP connection?</p>\n<p>This issue arises when the SAP-SUPPORT_PORTAL RFC destination is not working. Reference the steps in SAP Note <a href=\"/notes/91488\" target=\"_blank\">91488</a> to resolve the communication issue.</p>\n<p>Alternatively, you can use the manual download and upload process described above to update the local simplification item catalog replica.</p>\n<p>6.6. In which client should I perform the simplification item checks?</p>\n<ul>\n<ul>\n<li><strong>Relevance Check</strong>: We recommend analyzing each of the system’s productive clients. If there is more than one productive client in the system, we recommend evaluating each client. Note that results from each client will require a separate analysis to display the results. </li>\n<li><strong>Consistency Check</strong>: Client 000 is the recommended client, as this is the same client the Software Update Manager will evaluate during the technical conversion. These results can enhance a system’s analysis when you upload them to the analysis results of the productive client.</li>\n</ul>\n</ul>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"3\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td><strong>Version</strong></td>\n<td><strong>Change</strong></td>\n</tr>\n<tr>\n<td>167</td>\n<td>\n<p>Text change for specific minimum versions</p>\n</td>\n</tr>\n<tr>\n<td>166</td>\n<td>\n<p>Enhance application log when item is relevant but no consistency check defined.</p>\n</td>\n</tr>\n<tr>\n<td>165</td>\n<td>\n<p>Bugfix: The results from the first check didn't automatically sort when clicking \"Display Last Check Result\".</p>\n</td>\n</tr>\n<tr>\n<td>164</td>\n<td>\n<p>Includes ST-PI 740 SP25 and ST-PI 2008_1_700 SP35 into validation component.</p>\n<p>You don't need update to this version if your SP is smaller than above.</p>\n</td>\n</tr>\n<tr>\n<td>162/163</td>\n<td>\n<p>Bugfix: check class is available in the system but got error 'not found'.</p>\n</td>\n</tr>\n<tr>\n<td>161</td>\n<td>\n<p>Includes ST-PI 740 SP24 and ST-PI 2008_1_700 SP34 into validation component.</p>\n<p>You don't need update to this version if your SP is smaller than above.</p>\n</td>\n</tr>\n<tr>\n<td>160</td>\n<td>\n<p>Add the stack into source release adjustment.</p>\n</td>\n</tr>\n<tr>\n<td>158/159</td>\n<td>\n<p>Bugfix: when relevant simple check results contains MISS_ST03N or RULE_ISSUE, the relevent check result may not correct.</p>\n</td>\n</tr>\n<tr>\n<td>157</td>\n<td>\n<p>ST-PI 740 SP21 and ST-PI 2008_1_700/2008_1_710 SP31 will include the code of this version.</p>\n</td>\n</tr>\n<tr>\n<td>156</td>\n<td>\n<p>Enhance the SUM log if the target product version does not exist in Simplification Item check framework.</p>\n</td>\n</tr>\n<tr>\n<td>155</td>\n<td>\n<p>Enhance note check text for SAP Note 2399707. ST-PI 740 SP20 and ST-PI 2008_1_700/2008_1_710 SP30 will include the code of this version.</p>\n</td>\n</tr>\n<tr>\n<td>153/154</td>\n<td>\n<p>Enhance the application log text for note check of consistency check.</p>\n</td>\n</tr>\n<tr>\n<td>152</td>\n<td>\n<p>Bugfix: if \"New relevance &amp; consistency check as background job\" selected and click \"Execute in Background\" (F9) in the menu, there's  too much batch jobs \"RC_NEW_CHECK_IN_JOB\" created.</p>\n<p>Change the note description.</p>\n</td>\n</tr>\n<tr>\n<td>151</td>\n<td>\n<p>Bugfix: replace the SY-DATUM with the date value when checking DB.</p>\n</td>\n</tr>\n<tr>\n<td>150</td>\n<td>\n<p>Fix the bug which from download service server.</p>\n</td>\n</tr>\n<tr>\n<td>148/149</td>\n<td>\n<p>Enhance the IDoc relevance check performance issue. The client number is added in the select SQL statement to avoid performance issue in some system environments.</p>\n</td>\n</tr>\n<tr>\n<td>146/147</td>\n<td>\n<p>Fix the IDoc relevance check bug that the check result is not correct when exectued in different client.</p>\n</td>\n</tr>\n<tr>\n<td>145</td>\n<td>\n<p>Remove the SAP S/4HANA ON-PREMISE 1511 from target version list since it's out of maintenance.</p>\n<p>Add the application log \"commit work\" statement for performance optimization.</p>\n</td>\n</tr>\n<tr>\n<td>144</td>\n<td>\n<p>Fix bug: the xml format error when update catalog with latest version from SAP.</p>\n</td>\n</tr>\n<tr>\n<td>143</td>\n<td>\n<p>Fix bug: Class /SDF/CL_RC_CHK_UTILITY, Public Section, Field \"ICON_CHECKED\" is unknown.</p>\n</td>\n</tr>\n<tr>\n<td>142</td>\n<td>\n<p>Fix bug: avoid creating too many work processes when download service is active</p>\n</td>\n</tr>\n<tr>\n<td>141</td>\n<td>\n<p>Update the note description to add the minimum recommendation for SAP S/4HANA 2020 Feature Package 1.</p>\n<p>No code updated, so for customers who have already implemented the SAP Note, which fulfills the minimum recommendation, it is not required to update.</p>\n</td>\n</tr>\n<tr>\n<td>140</td>\n<td>\n<p>Bugfix: SAP S/4HANA 1709 is still supported for a system upgrade, do not show error message when upgrade to 1709.</p>\n</td>\n</tr>\n<tr>\n<td>139</td>\n<td>\n<p>Includes ST-PI 740 SP14 and ST-PI 2008_1_700 SP24 into validation component.</p>\n<p>You don't need update to this version if your SP is smaller than above.</p>\n</td>\n</tr>\n<tr>\n<td>138</td>\n<td>\n<p>Enhance error message for consistency check since that the target release SAP S/4HANA 1709 is no longer supported for a system conversion.</p>\n</td>\n</tr>\n<tr>\n<td>137</td>\n<td>\n<p>Update the note description to add the minimum recommendation for SAP S/4HANA 1909 Feature Package 2 and SAP S/4HANA 2020 Initial Shipment.</p>\n<p>No code updated, so for customers who have already implemented the SAP Note, which fulfills the minimum recommendation, it is not required to update.</p>\n</td>\n</tr>\n<tr>\n<td>136</td>\n<td>\n<p>Fix issue: Type \"CL_ABAP_DYN_PRG\" is unknown.</p>\n<p>You can ignore this version if you don't have this issue.</p>\n</td>\n</tr>\n<tr>\n<td>134/135</td>\n<td>\n<p>Support Download Service to update the Simplification Item Catalog. Please check more description of this issue in SAP Note <a href=\"/notes/2882166\" target=\"_blank\">2882166</a>.</p>\n<p>You can ignore this version if you are not using Download Service to connect to the SAP Support Backbone.</p>\n</td>\n</tr>\n<tr>\n<td>133</td>\n<td>\n<p>Fix bug: do not block the process when ICM is unavailable.</p>\n<p>You can ignore this version if ICM service is active.</p>\n</td>\n</tr>\n<tr>\n<td>132</td>\n<td>\n<p>Fix bug: the consistency result can not show the latest result for items with exemption applied.</p>\n</td>\n</tr>\n<tr>\n<td>131</td>\n<td>\n<p>Improve the performance of cluster table checking if the database is Sybase.</p>\n<p>You can ignore this version if your database isn't Sybase.</p>\n</td>\n</tr>\n<tr>\n<td>130</td>\n<td>\n<p>Fix bug: The program is blocked when updating BALSUB.</p>\n</td>\n</tr>\n<tr>\n<td>129</td>\n<td>\n<p>Use SAP-SUPPORT_PORTAL HTTP connection instead of SAPOSS RFC connection. Note requirement check works and Simplification Item Catalog content can automatically update from SAP servers in this version. Please check more description of this issue in SAP Note <a href=\"/notes/2882166\" target=\"_blank\">2882166</a>.</p>\n</td>\n</tr>\n<tr>\n<td>128</td>\n<td>\n<p>Remove remote note requirement check because SAPOSS does not work anymore.</p>\n</td>\n</tr>\n<tr>\n<td>127</td>\n<td>\n<p>Includes ST-PI 740 SP level 12 into validation component. You don't need update to this version if your ST-PI 740 SP is smaller than SP12.</p>\n</td>\n</tr>\n<tr>\n<td>126</td>\n<td>\n<p>Update Note description and enhance ST03N data collection.</p>\n</td>\n</tr>\n<tr>\n<td>125</td>\n<td>\n<p>Add new fields \"LoB/Technology\" and \"Business Area\" into check result; Remove usage of CL_ABAP_DYN_PRG.</p>\n</td>\n</tr>\n<tr>\n<td>123/124</td>\n<td>\n<p>Includes ST-PI 740 SP level 11 into validation component. You don't need update to this version if your ST-PI 740 SP is smaller than SP11.</p>\n</td>\n</tr>\n<tr>\n<td>122</td>\n<td>\n<p>Enhance error message for consistency check; Update note text for minimum version requirement; Fix bug: filter lost when resizing check result</p>\n</td>\n</tr>\n<tr>\n<td>120-121</td>\n<td>\n<p>Fix bug: Stop <em>SUM-Phase: PREP_EXTENSION/RUN_S4H_SIF_CHECK_INIT even if the return code is 4.</em></p>\n</td>\n</tr>\n<tr>\n<td>118/119</td>\n<td>\n<p>Add Simplification Item Consistency support for Readiness Check 2.0.</p>\n<p>Add SUM return code support and write the SUM log when error occurs in SUM mode.</p>\n</td>\n</tr>\n<tr>\n<td>114/115/116/117</td>\n<td>\n<p>Change ST03N data check: Do the entry point check if the ST03N entries are available.</p>\n</td>\n</tr>\n<tr>\n<td>113</td>\n<td>\n<p>Enhance performance issue for IDoc relevance check.</p>\n</td>\n</tr>\n<tr>\n<td>109~110</td>\n<td>\n<p>Bugfix:</p>\n<ul>\n<li>Right propagation of highest return code (8 + 7 + applied exemption is still rc=8)</li>\n<li>Check for the missing putstatus “I” to get the correct SUM phase</li>\n</ul>\n</td>\n</tr>\n<tr>\n<td>106~107</td>\n<td>\n<p>Bugfix: the check result is wrong when check the table with DATS or TIMS data type relevant condition.</p>\n</td>\n</tr>\n<tr>\n<td>104~105</td>\n<td>\n<p>Bugfix for relevance check.</p>\n</td>\n</tr>\n<tr>\n<td>103</td>\n<td>\n<p>Update the note title and description. <strong>SAP Note 2502552 is not required for SAP Readiness Check</strong>. It is only for consistency check.</p>\n</td>\n</tr>\n<tr>\n<td>100</td>\n<td>\n<p>Update the note description to add the minimum recommendation for SAP S/4HANA 1809 Initial Shipment.</p>\n<p>No code updated, so for customers who have already implemented the SAP Note, which fulfills the minimum recommendation, it is not required to update.</p>\n</td>\n</tr>\n</tbody>\n</table></div>", "noteVersion": 167}, {"note": "2602071", "noteTitle": "2602071 - Internal Workaround SI-Check requests minimum note version", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are doing a system conversion or an upgrade to SAP S/4HANA 1709 or above.</p>\n<p>Suddenly the SUM procedure stops and requests a higher note version of one of the existing notes.</p>\n<p>For example:<br/>\"Minimum required note version of 2502552 is 29. Implemented version is 28.\"<br/>\"Minimum required note version of 2399707 is 62. Implemented version is 60.\"</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>SI-Check, Simplification Item Check, S4TC, S4SIC, S/4HANA 1709,</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>We continuously update the SI-Checks with new checks for the next S/4HANA release or with corrected checks, cause a customer reported an issue that was not yet covered by a check.</p>\n<p>In the backend we maintain a minimum note version for each S/4HANA 1709+ release.</p>\n<p>For example:<br/>Minimum note version for SAP S/4HANA 1709 Initial Shipment Stack:<br/>  - 2399707  -&gt; Version 31<br/>  - 2502552 -&gt;  Version 1</p>\n<p>Minimum note version for SAP S/4HANA 1709 FPS01<br/>  - 2399707  -&gt; Version 62 (not forceable right now)<br/>  - 2502552 -&gt;  Version 29</p>\n<p>With this we ensure that a customer has all necessary check classes that are required for the related target release.</p>\n<p>Please note: We only set the minimum note version once for every release. So even if there are additional changes for the same release we do not change the minimum note version again. We only highly recommend to use the most recent available version.</p>\n<p><strong>The internal problem:</strong></p>\n<p>The backend feature allows us to only maintain these minimum note versions at the RTC of the related S/4HANA release. This could have a negative impact on internal tests which have been started before that date and are still ongoing.<br/>We will set the minimum note version to the current version number of those notes by RTC. As the customer can only choose the new target release with RTC, this is not a problem for him.</p>\n<p>With the BCOS_CUST option RC_SYS_TYPE / SAP_TEST internal projects can also choose the non-released versions before RTC. If in the middle of the conversion the minimum note version changes (cause of RTC date), this is a problem, cause it will block the internal conversion from that moment on.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>The minimum note version comes to the system on 2 ways:<br/>a. Via the direct connection in the report /SDF/RC_START_CHECK (SAPOSS or SAPSNOTE connection)<br/>b. Via the Transition Database file from the report /SDF/RC_START_CHECK</p>\n<p>1. Please download the Transition Database file before you start the conversion in the system and change the \"Auto-update\" to use the local version in the report. This way the content will stay stable and the minimum note version is not included.</p>\n<p>2. Additionally you need to disable the SAPOSS connection. Then the minimum note version cannot be retrieved from our backend. Even with the local version of the TDB-Content the new minimum note version would be received if the connection is active.</p>\n<p>3. Keep the RTC in mind. Only on RTC we will change the minimum note version for the <span>new release</span>. Already released product versions are not affected.</p>", "noteVersion": 3}]}], "activities": [{"Activity": "Process Design / Blueprint", "Phase": "Before or during conversion project", "Condition": "Mandatory", "Additional_Information": "Business processes will have to be adapted as per the cheanges listed in SAP Note 2465612"}, {"Activity": "User Training", "Phase": "During conversion project", "Condition": "Mandatory", "Additional_Information": "Users must ne trained in the new process designed"}, {"Activity": "Data migration", "Phase": "During conversion project", "Condition": "Mandatory", "Additional_Information": "The report R_SGT_DEFAULT_MIG is executed automatically during conversion.In case of problems during the conversion, you can execute these reports manually."}]}