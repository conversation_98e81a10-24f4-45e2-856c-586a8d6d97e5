<html lang="en-us" dir="ltr">
<head><title>Integration Between SAP Readiness Check and SAP Cloud ALM</title>
    <meta charset="utf-8">
    <link rel="stylesheet" type="text/css" media="screen,projection" href="/resource/css/style-v2.css">
    \x3Cscript type="text/javascript" src="js/sap-strings.js">\x3C/script>
</head>
<body class="en-us sap-light centered navigation-default enumeration-disabled collapsify-enabled">
<div id="d4h5-main-container" class="container_12" role="application">
    <div id="d4h5-section-container" class="grid_12">
        <div id="d4h5-main-content" class="grid_8 alpha omega">
            <section>
                <div class="page concept  - topic-topic concept-concept " id="loioabc6ba54f3be455a9f5ff02069e37edb"><h1
                        class="title topictitle1">Integration Between SAP Readiness Check and SAP Cloud ALM</h1>
                    <div class="body conbody"><p class="shortdesc"></p>\n<p class="p">SAP Readiness Check offers a set
                        of self-service tools to evaluate your existing landscape in\npreparation for a transformation
                        journey. The destination could be an upgrade to a new\nproduct version or the transformation to
                        a new SAP solution. Overall, it provides a\ncomprehensive overview of various topics to be
                        considered, based on the analysis of your\nexisting landscape. As a result, the tools help to
                        identify required preparative\nactivities and even possible pre-projects for your system well
                        before the actual project\nstarts.</p>\n
                        <div class="section collapsible" id="loioabc6ba54f3be455a9f5ff02069e37edb__section_agp_tzz_jzb">
                            <section class="section collapsible" type="SAP Readiness Check on SAP Cloud ALM"><h2
                                    class="section_title">SAP Readiness Check on SAP Cloud ALM</h2>\n\n<p class="p">The
                                integration with SAP Cloud ALM enables you to set up references between SAP\nReadiness
                                Check findings and SAP Cloud ALM projects. This functionality allows you\nto thoroughly
                                oversee and administrate your SAP Cloud ALM projects, providing an\nend-to-end
                                traceability.</p>\n<p class="p">On SAP Cloud ALM, the integration functionality enables
                                you to create new and assign\nexisting follow-ups (that is, requirements and user
                                stories) to the findings within\nyour SAP Readiness Check analyses, and manage these
                                follow-ups in your SAP Cloud ALM\nprojects.</p>\n
                                <div class="p">The integration functionality replaced the <span class="ph uicontrol">Transfer Tasks to SAP Cloud\nALM</span>
                                    function in the SAP Readiness Check for SAP S/4HANA dashboard\nand is available for
                                    the following checks within SAP Readiness Check for SAP S/4HANA\nand SAP Readiness
                                    Check for SAP S/4HANA upgrades:
                                    <ul class="ul" id="loioabc6ba54f3be455a9f5ff02069e37edb__ul_uwq_c11_kzb">\n
                                        <li class="li">\n<p class="p">SAP Readiness Check for SAP S/4HANA:</p>\n
                                            <ul class="ul" id="loioabc6ba54f3be455a9f5ff02069e37edb__ul_frx_211_kzb">\n
                                                <li class="li">\n<p class="p">Simplification Items</p>\n</li>
                                                \n
                                                <li class="li">\n<p class="p">Compatibility Scope Analysis</p>\n</li>
                                                \n
                                                <li class="li">\n<p class="p">Activities Related to Simplification
                                                    Items</p>\n
                                                </li>
                                                \n
                                                <li class="li">\n<p class="p">Add-On Compatibility</p>\n</li>
                                                \n
                                                <li class="li">\n<p class="p">Active Business Functions</p>\n</li>
                                                \n
                                                <li class="li">\n<p class="p">Custom Code Analysis</p>\n</li>
                                                \n
                                                <li class="li">\n<p class="p">Integration</p>\n</li>
                                                \n
                                                <li class="li">\n<p class="p">Recommended SAP Fiori Apps</p>\n</li>
                                                \n
                                                <li class="li">\n<p class="p">SAP Innovate Business Solutions</p>\n</li>
                                                \n
                                                <li class="li">\n<p class="p">Business Process Discovery</p>\n</li>
                                                \n
                                            </ul>
                                            \n
                                        </li>
                                        \n
                                        <li class="li">\n<p class="p">SAP Readiness Check for SAP S/4HANA upgrades:</p>
                                            \n
                                            <ul class="ul" id="loioabc6ba54f3be455a9f5ff02069e37edb__ul_s2y_m11_kzb">\n
                                                <li class="li">\n<p class="p">Simplification Items</p>\n</li>
                                                \n
                                                <li class="li">\n<p class="p">Compatibility Scope Analysis</p>\n</li>
                                                \n
                                                <li class="li">\n<p class="p">Activities Related to Simplification
                                                    Items</p>\n
                                                </li>
                                                \n
                                                <li class="li">\n<p class="p">Integration</p>\n</li>
                                                \n
                                                <li class="li">\n<p class="p">Custom Code Analysis</p>\n</li>
                                                \n
                                                <li class="li">\n<p class="p">Recommended SAP Fiori Apps</p>\n</li>
                                                \n
                                                <li class="li">\n<p class="p">SAP Innovate Business Solutions</p>\n</li>
                                                \n
                                                <li class="li">\n<p class="p">Add-On Compatibility</p>\n</li>
                                                \n
                                            </ul>
                                            \n
                                        </li>
                                        \n
                                    </ul>
                                </div>
                                \n
                                <div class="p">To activate and use the integration functionality for your SAP Readiness
                                    Check\nanalyses, proceed as follows:
                                    <ol class="ol" id="loioabc6ba54f3be455a9f5ff02069e37edb__ol_njf_v11_kzb">\n
                                        <li class="li">\n<p class="p">Prerequisite: Before activating and using the
                                            integration functionality\nin SAP Cloud ALM, check your authorizations for
                                            the SAP Cloud ALM\nprojects and setup app. You need to have the role <span
                                                    class="ph uicontrol">Readiness\nCheck Analysis Administrator</span>
                                            assigned to activate the\nintegration with SAP Cloud ALM and to
                                            create/assign follow-up items in\nSAP Cloud ALM. In addition, it is checked
                                            whether you are allowed to\ncreate or assign SAP Readiness Check items to
                                            the target project. Your\nuser needs at least the role <span
                                                    class="ph uicontrol">Project Member</span>, and\ndependent on the
                                            access level of the project your user may need to be\nassigned explicitly to
                                            this project. For more information, see <a class="extlink"
                                                                                       href="/docs/link-disclaimer?site=https%3A%2F%2Fblogs.sap.com%2F2022%2F12%2F15%2Fhow-access-restrictions-work-in-a-project-in-sap-cloud-alm%2F"
                                                                                       target="_blank" rel="noopener"
                                                                                       alt="https://blogs.sap.com/2022/12/15/how-access-restrictions-work-in-a-project-in-sap-cloud-alm/"
                                                                                       title="https://blogs.sap.com/2022/12/15/how-access-restrictions-work-in-a-project-in-sap-cloud-alm/">How
                                                access restrictions work in a Project in SAP\nCloud ALM<img
                                                        src="themes/sap-light/img/sap_link.png" class="link-sap"
                                                        alt="Information published on SAP site"
                                                        title="Information published on SAP site" border="0"></a> and <a
                                                    class="extlink"
                                                    href="/docs/link-disclaimer?site=https%3A%2F%2Fsupport.sap.com%2Fen%2Falm%2Fsap-cloud-alm%2Fimplementation%2Fsap-cloud-alm-implementation-expert-portal%2Fproject-setup.html%3FanchorId%3Dsection"
                                                    target="_blank" rel="noopener"
                                                    alt="https://support.sap.com/en/alm/sap-cloud-alm/implementation/sap-cloud-alm-implementation-expert-portal/project-setup.html?anchorId=section"
                                                    title="https://support.sap.com/en/alm/sap-cloud-alm/implementation/sap-cloud-alm-implementation-expert-portal/project-setup.html?anchorId=section">Project
                                                Setup<img src="themes/sap-light/img/sap_link.png" class="link-sap"
                                                          alt="Information published on SAP site"
                                                          title="Information published on SAP site" border="0"></a>.</p>
                                            \n
                                        </li>
                                        \n
                                        <li class="li">\n<p class="p">On the SAP Cloud ALM launchpad, go to the <span
                                                class="ph uicontrol">SAP Readiness Check\nIntegration</span> app.</p>\n
                                            <div class="fig fignone"
                                                 id="loioabc6ba54f3be455a9f5ff02069e37edb__fig_xj4_zb1_kzb">
                                                <div class="figbody">\n<img class="inline minimized-small"
                                                                            id="loioabc6ba54f3be455a9f5ff02069e37edb__image_ydk_4c1_kzb"
                                                                            src="loio336bc15b923f43a9bf8511c1b8426c93_LowRes.png">\n
                                                </div>
                                            </div>
                                            \n
                                        </li>
                                        \n
                                        <li class="li">\n<p class="p">In the <span class="ph uicontrol">Scenario Integration Settings</span>
                                            section,\nactivate the integration for SAP Readiness Check for SAP S/4HANA
                                            and/or\nSAP Readiness Check for SAP S/4HANA upgrades by using the toggles.
                                        </p>\n<p class="p">You can change the settings anytime within this app.</p>\n
                                        </li>
                                        \n
                                        <li class="li">\n<p class="p">In the <span class="ph uicontrol">Analysis Integration Settings</span>
                                            section,\nactivate the integration for your SAP Readiness Check analyses by
                                            using\nthe <span class="ph uicontrol">Requirements</span> and/or <span
                                                    class="ph uicontrol">User\nStories</span> toggles.</p>\n<p
                                                class="p">You can change the settings anytime within this app.</p>\n
                                            <div class="p">\n
                                                <aside class="note" aria-labelledby="n0t">
                                                    <div class="title" id="n0t"><strong>Note</strong></div>
                                                    \n<p class="p">If one or more follow-ups (that is, requirements or
                                                    user stories)\nhave been created for an analysis in SAP Readiness
                                                    Check, the\nrelated toggle in the <span class="ph uicontrol">Analysis Integration\nSettings</span>
                                                    section within this app cannot be\nswitched off.</p></aside>
                                                \n
                                            </div>
                                            \n
                                        </li>
                                        \n
                                        <li class="li">\n<p class="p">On SAP Cloud ALM, access the project-related
                                            checks in SAP Readiness\nCheck for SAP S/4HANA or SAP Readiness Check for
                                            SAP S/4HANA\nupgrades.</p>\n
                                        </li>
                                        \n
                                        <li class="li">\n<p class="p">Within the checks, select one or more findings in
                                            the\n<span class="ph uicontrol">Items</span> table and choose\n<span
                                                    class="ph uicontrol">Follow-Up</span> above the table.</p>\n
                                            <div class="fig fignone"
                                                 id="loioabc6ba54f3be455a9f5ff02069e37edb__fig_zyp_c21_kzb">
                                                <div class="figbody">\n<img class="inline minimized-small"
                                                                            id="loioabc6ba54f3be455a9f5ff02069e37edb__image_ulf_d21_kzb"
                                                                            src="loioe5b3c6a4d7a6474fa7f036a8dd665063_LowRes.png">\n
                                                </div>
                                            </div>
                                            \n
                                        </li>
                                        \n
                                        <li class="li">\n<p class="p">By choosing <span
                                                class="ph uicontrol">Create New</span> or <span class="ph uicontrol">Assign to\nExisting</span>
                                            in the <span class="ph uicontrol">Follow-Up</span>\ndropdown list, the
                                            subsequent popup windows guide you through the\nprocess of creating one or
                                            more follow-ups for the selected items or\nassigning the selected items to
                                            an existing follow-up.</p>\n<p class="p">Note that new follow-ups can only
                                            be created for findings in the\n<span class="ph uicontrol">Items</span>
                                            table if no follow-ups have been\nassigned yet.</p>\n
                                        </li>
                                        \n
                                        <li class="li">\n<p class="p">Once you have created one or more follow-ups for
                                            the selected items or\nassigned the selected items to an existing follow-up,
                                            the\n<span class="ph uicontrol">Follow-Up Status</span> column indicates
                                            the\nprogress of the creation and assignment of follow-ups:</p>\n
                                            <ul class="ul" id="loioabc6ba54f3be455a9f5ff02069e37edb__ul_tng_m21_kzb">\n
                                                <li class="li">\n<p class="p"><span class="ph uicontrol">Assigned</span>
                                                    (in all checks except the\n<span class="ph uicontrol">Simplification Items</span>
                                                    check): You can\ncreate one follow-up for each SAP Readiness Check
                                                    finding. If a\nfinding is already assigned to a follow-up, the
                                                    follow-up status\nis <span class="ph uicontrol">Assigned</span>.</p>
                                                    \n
                                                </li>
                                                \n
                                                <li class="li">\n<p class="p"><span
                                                        class="ph uicontrol">Partially</span> (only in the\n<span
                                                        class="ph uicontrol">Simplification Items</span> check): You
                                                    can\ncreate one or more follow-ups for activities and/or
                                                    consistency\nchecks belonging to one simplification item. If there
                                                    are any\nactivities and/or consistency checks related to a
                                                    simplification\nitem that are not assigned to follow-ups yet, the
                                                    follow-up\nstatus is <span class="ph uicontrol">Partially</span>.
                                                </p>\n
                                                </li>
                                                \n
                                                <li class="li">\n<p class="p"><span
                                                        class="ph uicontrol">All Assigned</span> (only in the\n<span
                                                        class="ph uicontrol">Simplification Items</span> check): You
                                                    can\ncreate one or more follow-ups for activities and/or
                                                    consistency\nchecks belonging to one simplification item. If all
                                                    activities\nand/or consistency checks related to a simplification
                                                    item are\nassigned to follow-ups, the follow-up status is <span
                                                            class="ph uicontrol">All\nAssigned</span>.</p>\n
                                                    <div class="fig fignone"
                                                         id="loioabc6ba54f3be455a9f5ff02069e37edb__fig_ycb_w21_kzb">
                                                        <div class="figbody">\n<img class="inline minimized-small"
                                                                                    id="loioabc6ba54f3be455a9f5ff02069e37edb__image_yyh_z21_kzb"
                                                                                    src="loiobe40c21cec064715b51a54e946fb47ca_LowRes.png">\n
                                                        </div>
                                                    </div>
                                                    \n
                                                </li>
                                                \n
                                            </ul>
                                            \n
                                        </li>
                                        \n
                                        <li class="li">\n<p class="p">To view follow-ups of your SAP Readiness Check
                                            findings in SAP Cloud ALM,\nchoose the status of an item in the <span
                                                    class="ph uicontrol">Follow-Up\nStatus</span> column. The follow-up
                                            title will take you to the\nrelated requirement or user story in the SAP
                                            Cloud ALM tasks or\nrequirements app.</p>\n
                                            <div class="fig fignone"
                                                 id="loioabc6ba54f3be455a9f5ff02069e37edb__fig_us3_ff1_kzb">
                                                <div class="figbody">\n<img class="inline minimized-small"
                                                                            id="loioabc6ba54f3be455a9f5ff02069e37edb__image_agh_3f1_kzb"
                                                                            src="loio839610cc09c548628ae0cba785af9f36_LowRes.png">\n
                                                </div>
                                            </div>
                                            \n
                                        </li>
                                        \n
                                        <li class="li">\n<p class="p">You can now manage your follow-ups in SAP Cloud
                                            ALM.</p>\n
                                        </li>
                                        \n
                                    </ol>
                                </div>
                                \n<p class="p">To change the integration settings for your SAP Readiness Check analyses,
                                    go to the\n<span class="ph uicontrol">SAP Readiness Check Integration</span> app on
                                    the SAP Cloud ALM\nlaunchpad.</p>\n
                            </section>
                        </div>
                        \n
                        <div class="section collapsible" id="loioabc6ba54f3be455a9f5ff02069e37edb__section_bvj_2p3_dxb">
                            <section class="section collapsible" type="SAP Readiness Check on SAP for Me"><h2
                                    class="section_title">SAP Readiness Check on SAP for Me</h2>\n\n<p class="p">
                                Alternatively to the above-mentioned integration functionality in SAP Readiness
                                Check\nfor SAP S/4HANA and SAP Readiness Check for SAP S/4HANA upgrades on SAP Cloud
                                ALM,\nyou can instead generate and upload tasks from SAP Readiness Check for SAP
                                S/4HANA\non SAP for Me to SAP Cloud ALM.</p>\n
                                <div class="p"> Proceed as follows:
                                    <ol class="ol" id="loioabc6ba54f3be455a9f5ff02069e37edb__ol_kcv_d1m_2yb">\n
                                        <li class="li">\n<p class="p">On SAP for Me, access the project-related check in
                                            SAP Readiness Check\nfor SAP S/4HANA.</p>\n
                                        </li>
                                        \n
                                        <li class="li">\n<p class="p">Choose <span class="SAP-icons-V5"></span> <span
                                                class="ph sap-icon-font-description sap-icon-font-description"> (Download)</span>
                                            and <span class="ph uicontrol">Generate Extract for SAP Cloud\nALM</span> in
                                            the header area of the dashboard.</p>\n
                                            <div class="fig fignone"
                                                 id="loioabc6ba54f3be455a9f5ff02069e37edb__fig_pjc_kds_2yb">
                                                <div class="figbody">\n<img class="inline minimized-small"
                                                                            id="loioabc6ba54f3be455a9f5ff02069e37edb__image_ixk_4ds_2yb"
                                                                            src="loiod8ff54e9a3704715bef2371be5db7696_LowRes.png">\n
                                                </div>
                                            </div>
                                            \n
                                        </li>
                                        \n
                                        <li class="li">\n<p class="p">Select which checks are to be included in the
                                            download.</p>\n
                                            <div class="fig fignone"
                                                 id="loioabc6ba54f3be455a9f5ff02069e37edb__fig_npd_qcs_2yb">
                                                <div class="figbody">\n<img class="inline minimized-small"
                                                                            id="loioabc6ba54f3be455a9f5ff02069e37edb__image_gyt_qcs_2yb"
                                                                            src="loio0a5f048176164c409ac08490ac8d1f9d_LowRes.png">\n
                                                </div>
                                            </div>
                                            \n
                                        </li>
                                        \n
                                        <li class="li">\n<p class="p">Choose <span class="ph uicontrol">Generate</span>
                                            to create the upload file\ncontaining your tasks (that is, sub-tasks and
                                            user stories) for the SAP\nCloud ALM tasks app.</p>\n<p class="p">A popup
                                            window informs you about the number of generated tasks in the SAP\nCloud ALM
                                            tasks app.</p>\n
                                        </li>
                                        \n
                                        <li class="li">\n<p class="p">In the SAP Cloud ALM tasks app, choose <span
                                                class="SAP-icons-V5"></span> <span
                                                class="ph sap-icon-font-description sap-icon-font-description"> (Upload)</span>
                                            above the <span class="ph uicontrol">Items</span> table. </p>\n
                                        </li>
                                        \n
                                    </ol>
                                </div>
                                \n
                            </section>
                        </div>
                        \n
                        <div class="section" id="loioabc6ba54f3be455a9f5ff02069e37edb__section_wgx_hh3_dxb">
                            <section class="section" type="More"><h2 class="section_title">More</h2>\n\n<p class="p"><a
                                    class="xref" href="https://help.sap.com/docs/SAP_READINESS_CHECK" target="_blank"
                                    rel="noopener" alt="https://help.sap.com/docs/SAP_READINESS_CHECK"
                                    title="https://help.sap.com/docs/SAP_READINESS_CHECK">SAP Readiness Check</a> (SAP
                                Help Portal)</p>\n
                            </section>
                        </div>
                        \n
                    </div>
                </div>
            </section>
            <div class="clear"></div>
            \n
        </div>
    </div>
</div>
</body>
</html>