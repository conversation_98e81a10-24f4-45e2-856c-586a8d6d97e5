<html lang="en-us" dir="ltr">
<head><title>Availability of SAP Readiness Check</title>
    <meta charset="utf-8">
    <meta name="abstract"
          content="SAP Readiness Check is available to you in the public cloud and on your private SAP Cloud ALM tenant.">
    <meta name="description"
          content="SAP Readiness Check is available to you in the public cloud and on your private SAP Cloud ALM tenant.">
    <link rel="stylesheet" type="text/css" media="screen,projection" href="/resource/css/style-v2.css">
    \x3Cscript type="text/javascript" src="js/sap-strings.js">\x3C/script>
</head>
<body class="en-us sap-light centered navigation-default enumeration-disabled">
<div id="d4h5-main-container" class="container_12" role="application">
    <div id="d4h5-section-container" class="grid_12">
        <div id="d4h5-main-content" class="grid_8 alpha omega">
            <section>
                <div class="page concept  - topic-topic concept-concept " id="loio461e257406594e4f98dacc261e609145"><h1
                        class="title topictitle1">Availability of SAP Readiness Check</h1>
                    <div class="body conbody"><p class="shortdesc">SAP Readiness Check is available to you in the public
                        cloud and on your private SAP\nCloud ALM tenant.</p>\n
                        <div class="section" id="loio461e257406594e4f98dacc261e609145__section_ary_svx_5zb">
                            <section class="section" type="Public Usage of SAP Readiness Check"><h2
                                    class="section_title">Public Usage of SAP Readiness Check</h2>\n\n<p class="p">You
                                can launch SAP Readiness Check in public mode from SAP for Me: <a class="extlink"
                                                                                                  href="/docs/link-disclaimer?site=https%3A%2F%2Fme.sap.com%2Freadinesscheck"
                                                                                                  target="_blank"
                                                                                                  rel="noopener"
                                                                                                  alt="https://me.sap.com/readinesscheck"
                                                                                                  title="https://me.sap.com/readinesscheck">https://me.sap.com/readinesscheck<img
                                        src="themes/sap-light/img/sap_link.png" class="link-sap"
                                        alt="Information published on SAP site"
                                        title="Information published on SAP site" border="0"></a>.</p>\n<p class="p">
                                From here, you can use all available check scenarios (see <a class="xref"
                                                                                             href="https://help.sap.com/docs/r/a281af437b3e4ef4a187c7f35a9093e9/latest/en-US"
                                                                                             target="_blank"
                                                                                             rel="noopener"
                                                                                             alt="https://help.sap.com/docs/r/a281af437b3e4ef4a187c7f35a9093e9/latest/en-US"
                                                                                             title="https://help.sap.com/docs/r/a281af437b3e4ef4a187c7f35a9093e9/latest/en-US">Feature
                                Scope Description</a>).</p>\n<p class="p">You can also manually upload all tasks
                                identified by the checks to SAP Cloud ALM. For\nmore information, see <a class="xref"
                                                                                                         href="192e1b87bc5a4f049b0deb01d5b8abad.html"
                                                                                                         title="You can mass edit the complete task information in one go in SAP Cloud ALM by uploading a spreadsheet.">Mass
                                    Update by Upload</a>.</p>\n<p class="p">More under <a class="xref"
                                                                                          href="https://help.sap.com/docs/cloud-alm/applicationhelp/uploading-readiness-check-tasks?q=SAP%20Readiness%20Check#sap-readiness-check-on-sap-for-me"
                                                                                          target="_blank" rel="noopener"
                                                                                          alt="https://help.sap.com/docs/cloud-alm/applicationhelp/uploading-readiness-check-tasks?q=SAP%20Readiness%20Check#sap-readiness-check-on-sap-for-me"
                                                                                          title="https://help.sap.com/docs/cloud-alm/applicationhelp/uploading-readiness-check-tasks?q=SAP%20Readiness%20Check#sap-readiness-check-on-sap-for-me">SAP
                                Readiness Check on SAP for Me</a>.</p>\n
                            </section>
                        </div>
                        \n
                        <div class="section" id="loio461e257406594e4f98dacc261e609145__section_bq1_svx_5zb">
                            <section class="section" type="Integration with SAP Cloud ALM"><h2 class="section_title">
                                Integration with SAP Cloud ALM</h2>\n\n<p class="p"> SAP Readiness Check, with all check
                                scenarios, is also available on the launchpad of\nSAP Cloud ALM. From here, you can run
                                SAP Readiness Check on your own, private SAP\nCloud ALM tenant.</p>\n<p class="p">In SAP
                                Cloud ALM, you can set up project references to findings from the SAP\nReadiness Check
                                for SAP S/4HANA and SAP Readiness Check for SAP S/4HANA upgrades.\nThe integration with
                                SAP Cloud ALM enables you to create new and assign existing\nfollow-ups to the findings
                                within your SAP Readiness Check analysis, and manage\nthese follow-ups in your SAP Cloud
                                ALM project.</p>\n<p class="p">More under <a class="xref"
                                                                             href="https://help.sap.com/docs/cloud-alm/applicationhelp/uploading-readiness-check-tasks?q=SAP%20Readiness%20Check#sap-readiness-check-on-sap-cloud-alm"
                                                                             target="_blank" rel="noopener"
                                                                             alt="https://help.sap.com/docs/cloud-alm/applicationhelp/uploading-readiness-check-tasks?q=SAP%20Readiness%20Check#sap-readiness-check-on-sap-cloud-alm"
                                                                             title="https://help.sap.com/docs/cloud-alm/applicationhelp/uploading-readiness-check-tasks?q=SAP%20Readiness%20Check#sap-readiness-check-on-sap-cloud-alm">SAP
                                Readiness Check on SAP Cloud ALM</a>.</p>\n
                            </section>
                        </div>
                        \n
                    </div>
                </div>
            </section>
            <div class="clear"></div>
            \n
        </div>
    </div>
</div>
</body>
</html>