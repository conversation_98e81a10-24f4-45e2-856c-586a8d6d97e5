{"title": "Learn More About Active Business Functions", "next": "\n          \n          \n          <p>For <cite>Compatible</cite> business functions, no further action is required.</p>\n\n          <p>\n            For more information about the displayed business functions, create a case under the application component of the business function.\n          </p>\n\n          <p>\n            Additional information about business functions and SAP S/4HANA can be found in the following SAP Notes:\n            <ul>\n              <li>SAP Note <a rel='noopener noreferrer' href=\"https://me.sap.com/notes/2240359\">2240359</a> (SAP S/4HANA: always-off business functions)</li>\n              <li>SAP Note <a rel='noopener noreferrer' href=\"https://me.sap.com/notes/2240360\">2240360</a> (SAP S/4HANA: always-on business functions)</li>\n              <li>For additional information, see the release restriction note, which appears on the <cite>Discover</cite> tab on SAP Help Portal for your target SAP S/4HANA release. The documentation for the current release is accessible via this link: <a rel='noopener noreferrer' href='https://help.sap.com/viewer/product/SAP_S4HANA_ON-PREMISE/latest/'>https://help.sap.com/viewer/product/SAP_S4HANA_ON-PREMISE/latest/</a></li>\n            </ul>\n          </p>\n        ", "info": "\n            <p>\n              Before you can start a conversion, your active business functions need to be validated for compatibility with your target SAP S/4HANA release. In general, business functions can have one out of three statuses in SAP S/4HANA (always_on, customer_switchable, or always_off), which has an impact on the conversion.\n            </p>\n            <p>\n              This section lists the active business functions in your system and shows if they are compatible with SAP S/4HANA. The identified business functions are assigned to one of the following categories, according to their compatibility status with SAP S/4HANA:\n              <ul>\n                <li><cite>Incompatible</cite>: The business function activates a functionality that is not supported by SAP S/4HANA. It is switched on in the start release but defined as always_off in the target release. The system conversion will be blocked and is not possible to proceed with the selected target release.</li>\n                <li><cite>Will Be Deleted</cite>: The business function is no longer available in SAP S/4HANA. It may have been assigned to different business functions in SAP S/4HANA or could have been deleted. The conversion will not be blocked.</li>\n                <li>\n                  <cite>Compatible</cite>: The business function is\n                  <ul>\n                    <li>switched on in the start release and defined as always_on in the target release.</li>\n                    <li>switched off* in the start release and defined as always_off in the target release.</li>\n                    <li>switched off* in the start release but defined as always_on in the target release.</li>\n                    <li>switched on or off in the start release system but defined as customer_switchable in the target release. In the target release, it will have the same status as defined in the start release.</li>\n                  </ul>\n                  In these scenarios, the business functions are compatible with SAP S/4HANA. They will be activated or deactivated automatically during the conversion, according to the corresponding scenario.\n                </li>\n              </ul>\n            </p>\n          ", "disclaim": "\n          <p>\n            The compatibility statements are dynamic and may change, because some business functions might become compatible with SAP S/4HANA. In addition, the compatibility statements may vary across SAP S/4HANA releases.\n          </p>\n          <p>\n            * For simplicity purposes, SAP Readiness Check does not include business functions that are switched off.\n          </p>\n          <p>\n            The data collected for the current check is client independent.\n          </p>\n        "}