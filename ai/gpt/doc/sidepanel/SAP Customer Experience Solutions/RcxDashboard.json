{"next": "\n              <p>To see your check results in more detail, choose a check title.</p>\n              <p>The available checks support you to plan and organize your transition project. To explore the check results, you can take the following steps:</p>\n              <p>\n                  <ul>\n                      <li>Contact SAP for an advisory service for SAP Customer Experience solutions, or your preferred implementation partner.</li>\n                      <li>Learn more about the <a rel='noopener noreferrer' href='https://support.sap.com/content/dam/support/en_us/library/ssp/offerings-and-programs/sap-enterprise-support/enterprise-support-academy/value-maps/overview-sap-customer-experience-solutions.pdf'>value maps</a> for SAP Customer Experience solutions.</li>\n                      <li>Learn more about <a rel='noopener noreferrer' href='https://www.asug.com/events/asug-express-customer-experience-101-what-you-need-to-know-about-sap-cx?utm_campaign=EXP%2021_UX&utm_medium=email&_hsmi=113969079&_hsenc=p2ANqtz-_YQbtR7JQ4_-y-LzMB3Ff7mYVg9m2vrQLrDITJL03QCBd5j3DFKHHxn2IoNJ2T9F9Y-jBTXhBVLbOEGfqZpWFGP_sp-Q&utm_content=113969079&utm_source=hs_email'>SAP Customer Experience solutions</a>.</li>\n                      <li>Explore the <a rel='noopener noreferrer' href='https://roadmaps.sap.com/board?range=CURRENT-LAST&PRODUCT=089E017A62AB1EDAAFDD016CAED780E7#Q1%202021'>road maps for SAP CRM and SAP Customer Experience</a>.</li>\n                  </ul>\n              </p>\n              <p>Please also consider that beyond the checks provided in this tool, additional restrictions and landscape requirements might be relevant for your system.</p>\n          ", "info": "\n              <p>With the SAP Readiness Check tool for SAP Customer Experience solutions, SAP provides a self-service tool to check how your productive SAP CRM system is used. It helps to identify required preparations and possible preprojects for your system well before the transition project starts. This early insight means that you can scope and plan your transition project with a higher degree of accuracy. For useful transactional data and history, we recommend running SAP Readiness Check for SAP Customer Experience solutions on a production system (or a recent copy of one).</p>\n              <p>Based on the analysis of your source system, the tool provides a comprehensive overview of specific parameters throughout various checks:</p>\n              <p>\n                  <ul>\n                      <li><cite>SAP CRM Usage Profile</cite>: Summary of active SAP CRM components and their dependent levels that were identified in the analyzed system. The displayed findings were collected via a set of checks, which measured the data volumes of key master data and transactional data tables in your system. The tile also shows the industry-specific functionality that is used in your system. The font size of each topic indicates the degree to which the functionality has been used. </li>\n                      <li><cite>Initial Data Migration Profile</cite>: Compilation of volume statistics for a selected set of objects, providing an initial insight into the analyzed system.</li>\n                      <li><cite>Custom Code - BSP Enhancements</cite>: Overview of Business Server Page (BSP) enhancements that are implemented at a UI level in your SAP CRM system, grouped by application area. </li>\n                      <li><cite>Custom Code - ABAP Enhancements</cite>: Overview of ABAP enhancements that are implemented in your SAP CRM system, grouped by enhancement type. In the detailed view, you can provide information about the main area of the solution that is related to each development package or individual enhancement. Once this information has been provided, the tile also shows the identified ABAP enhancements by main area of the SAP CRM solution. </li>\n                      <li><cite>Interfaces</cite>: Evaluation of inbound and outbound interfaces that were found in your SAP CRM system. The check includes standard and non-standard interfaces, considering the interface types IDoc, BDoc, custom Web service, RFC and BAPI, BW extractor, OData service, SLT replication, and flat file.</li>\n                      <li><cite>Enhanced Business Structures And Custom Tables</cite>: Identification of custom fields in your system, grouped by the object to which they are related. </li>\n                      <li><cite>Add-Ons</cite>: Analysis of SAP and non-SAP add-ons that were found in your SAP CRM system.</li>\n                      <li><cite>Active Business Functions</cite>: Summary of active business functions that were found in your SAP CRM system.</li>\n                  </ul>\n              </p>\n          ", "disclaim": "<p>The findings presented by SAP Readiness Check include a mix of both client-dependent and client-independent results. If the analyzed system contains more than one active client, it is recommended to collect and upload the results as separate analyses. More information about the nature of each check is available within the respective <cite>Learn More</cite> side panel content.</p>", "title": "Learn More About Customer Experience Solutions"}