<!DOCTYPE html>
<html class="no-js" dir="ltr" lang="en" prefix="og: http://ogp.me/ns#">
 <head>
  <title>
   SAP S/4HANA Simplification Item Check - How to do ... - SAP Community
  </title>
  <script type="text/javascript">
   function DataContainer(){
        var data = {
            "loginStatus": "no",
            "pageName": "SAP S/4HANA Simplification Item Check - How to do it right.",
            "pageTemplate": "BlogArticlePage",
            "country": "glo",
            "productIDs": "73554900100800000266,1647bf27-5e10-4efd-89e1-a59efaf4e250",
            "nodeId": "750",
            "messageId": "13386669",
            "messageTitle": "SAP S/4HANA Simplification Item Check - How to do it right.",
            "messageType": "blog",
            "messageCreateDate": "2018-03-26 15:28:13",
            "user_tags": [ "S4HANA System Conversion",   "SAP S4HANA Release Upgrade",   "simplification item check" ],
            "labels": [ "Technology Updates" ],
            "pageId": "1/317/747/750",
        }
        this.getProp = function(name){ return data[name]; }
    }
  </script>
  <script type="text/javascript">
   window.adobeDataLayer=window.adobeDataLayer||[];function CommunityEDDLCore(){this.sendEvent=function(event){window.adobeDataLayer.push(event)}}function CommunityEDDLLoginState(pageData){var hasUserLoggedIn=LITHIUM.CommunityJsonObject.Activity.Results.filter(item=>item.name==='UserSignedOn').length>0;var currentState=pageData.getProp("loginStatus");var prevState=localStorage.getItem("userLoggedIn")||currentState;localStorage.setItem("userLoggedIn",currentState);this.isLogin=function(){return hasUserLoggedIn&&currentState==="yes"};this.isLogout=function(){return prevState!=currentState&&currentState==="no"};this.isAuthenticationEvent=function(){return this.isLogin()||this.isLogout()}}function CommunityEDDLEvents(){var eddl=new CommunityEDDLCore();var pageData=new DataContainer();this.global=function(){eddl.sendEvent({'event':'globalDL','site':{'name':'scn-groups','country':pageData.getProp('country')},'user':{'loginStatus':pageData.getProp('loginStatus')}})};this.pageView=function(){eddl.sendEvent({'event':'pageView','page':{'pageId':pageData.getProp('pageId'),'name':location.pathname.slice(1)||'/','title':pageData.getProp('pageName'),'template':pageData.getProp('pageTemplate'),'section':location.pathname.split('/')[2]||'/','language':'en','country':pageData.getProp('country'),'url':window.location.href,'referrer':document.referrer,'semaphoreID':pageData.getProp('productIDs')},'user':{'loginStatus':pageData.getProp('loginStatus')}})};this.siteContent=function(){if(!pageData.getProp('messageId')){return}eddl.sendEvent({'event':'siteContentView','siteContent':{'contentid':pageData.getProp('messageId'),'type':pageData.getProp('messageType'),'title':pageData.getProp('messageTitle'),'tag':pageData.getProp('user_tags'),'language':'en','createDate':pageData.getProp('messageCreateDate'),'nodeID':pageData.getProp('nodeId'),'label':pageData.getProp('labels')},'user':{'loginStatus':pageData.getProp('loginStatus')}})};this.login=function(){var loginState=new CommunityEDDLLoginState(pageData);if(!loginState.isAuthenticationEvent()){return}eddl.sendEvent({'event':loginState.isLogin()?'userLogin':'userLogout','user':{'loginStatus':pageData.getProp('loginStatus')}})};this.beacon=function(){eddl.sendEvent({'event':'stBeaconReady'})}}function CommunityScenarios(){var events=new CommunityEDDLEvents();this.pageView=function(){events.global();events.login();events.pageView();events.siteContent();events.beacon()};this.kudo=function(){};this.subscription=function(){}}window.addEventListener('load',()=>{if(LITHIUM?.CommunityJsonObject?.Activity?.Results==undefined){console.error("SAP EDDL: Tracking is disabled due to LITHIUM.CommunityJsonObject.Activity.Results is undefined");return}var eddlScenarios=new CommunityScenarios();eddlScenarios.pageView()});
  </script>
  <meta content="This is not a one-shot blog post, but will be regularly updated with the most recent information concerning SAP S/4HANA Simplification Item Checks. Date Change May 30th" name="description"/>
  <meta content="width=device-width, initial-scale=1.0, user-scalable=yes" name="viewport"/>
  <meta content="2023-01-17T17:15:16+01:00" itemprop="dateModified"/>
  <meta content="text/html; charset=utf-8" http-equiv="Content-Type"/>
  <link href="https://community.sap.com/t5/enterprise-resource-planning-blogs-by-sap/sap-s-4hana-simplification-item-check-how-to-do-it-right/ba-p/13386669" rel="canonical"/>
  <meta content="https://groups.community.sap.com/html/assets/SAP_R_grad_200x200.png" property="og:image"/>
  <meta content="https://community.sap.com/t5/user/viewprofilepage/user-id/282993" property="article:author"/>
  <meta content="SAP Community" property="og:site_name"/>
  <meta content="article" property="og:type"/>
  <meta content="https://community.sap.com/t5/enterprise-resource-planning-blogs-by-sap/sap-s-4hana-simplification-item-check-how-to-do-it-right/ba-p/13386669" property="og:url"/>
  <meta content="SAP S4HANA Release Upgrade" property="article:tag"/>
  <meta content="S4HANA System Conversion" property="article:tag"/>
  <meta content="Technology Updates" property="article:tag"/>
  <meta content="simplification item check" property="article:tag"/>
  <meta content="Enterprise Resource Planning Blogs by SAP" property="article:section"/>
  <meta content="This is not a one-shot blog post, but will be regularly updated with the most recent information concerning SAP S/4HANA Simplification Item Checks.    Date   Change      May 30th 2022    - Added information on SAP S/4HANA 2021 FPS 1 + 2.       October 21st 2021    - Added an explanation on runtime d..." property="og:description"/>
  <meta content="2018-03-26T13:28:13.000Z" property="article:published_time"/>
  <meta content="2023-01-17T17:15:16+01:00" property="article:modified_time"/>
  <meta content="SAP S/4HANA Simplification Item Check - How to do it right." property="og:title"/>
  <link class="lia-link-navigation hidden live-links" href="/khhcw49343/rss/message?board.id=erp-blog-sap&amp;message.id=26183" id="link" rel="alternate" title="article SAP S/4HANA Simplification Item Check - How to do it right. in Enterprise Resource Planning Blogs by SAP" type="application/rss+xml"/>
  <link href="/skins/1907178/30d6a525796f5d5fef22afc1a0238606/sap2023.css" rel="stylesheet" type="text/css"/>
  <link href="https://community.sap.com/html/@28E64AF715869863C9097AAA9C123156/assets/favicon.ico" rel="shortcut icon"/>
  <script src="https://assets.adobedtm.com/ccc66c06b30b/2a75032df81e/launch-dfaf5c383cc4.min.js">
  </script>
  <link href="https://community.sap.com/html/@0695D6660F84CABE78A4151F0A127FA9/assets/prism-kh.css" rel="stylesheet" type="text/css"/>
  <script src="https://community.sap.com/html/@2778250842E3D1984686289CD77E55B9/assets/prism-kh.js" type="text/javascript">
  </script>
  <link href="https://contextualnavigation.api.community.sap.com/static/1.31.0/cxs-designsystem/cxs-designsystem.css" rel="stylesheet"/>
  <script src="https://contextualnavigation.api.community.sap.com/static/1.31.0/cxs-designsystem/cxs-designsystem.esm.js" type="module">
  </script>
  <script language="javascript" type="text/javascript">
   <!--
if("undefined"==typeof LITHIUM)var LITHIUM={};LITHIUM.Loader=function(){var d=[],b=[],a=!1,c=!1;return{onLoad:function(b){"function"===typeof b&&(!0===a?b():d.push(b))},onJsAttached:function(a){"function"===typeof a&&(!0===c?a():b.push(a))},runJsAttached:function(){c=!0;for(var a=0;a<b.length;a++)b[a]()},getOnLoadFunctions:function(){return d},setLoaded:function(){a=!0},isLoaded:function(){return a},isJsAttached:function(){return c}}}();"undefined"===typeof LITHIUM.Components&&(LITHIUM.Components={});LITHIUM.Components.render=function(d,b,a){LITHIUM.Loader.onLoad(function(){var c=LITHIUM.Components.renderUrl(d),h={type:"GET",dataType:"json"};LITHIUM.jQuery.extend(h,a||{});h.hasOwnProperty("url")||LITHIUM.jQuery.extend(h,{url:c});h.data=b;if("object"!==typeof h.data||null===h.data)h.data={};h.data.originalPageName=LITHIUM.Components.ORIGINAL_PAGE_NAME;h.data.originalPageContext=LITHIUM.Components.ORIGINAL_PAGE_CONTEXT;LITHIUM.jQuery.ajax(h)}.bind(this))};
LITHIUM.Components.renderUrl=function(d,b){var a=LITHIUM.Components.RENDER_URL;LITHIUM.jQuery.each({"component-id":d},function(b,d){a=a.replace(new RegExp("#{"+b+"}","g"),d)});"undefined"!==typeof b&&(a+="?"+LITHIUM.jQuery.param(b));return a};
LITHIUM.Components.renderInPlace=function(d,b,a,c){function h(a){var b=document.createElement("div"),d=(new Date).getTime()+Math.floor(1E7*Math.random()+1);b.setAttribute("id",d);a.parentNode.insertBefore(b,a);return d}if(c)var q=c;else!1===LITHIUM.Loader.isLoaded()&&(document.currentScript?q=h(document.currentScript):(c=document.querySelectorAll("script"),1<c.length&&(q=h(c[c.length-1]))));LITHIUM.Loader.onLoad(function(){var c=LITHIUM.jQuery,h=b||{},m=a||{},k=c("#"+q);c.extend(h,{renderedScripts:LITHIUM.RenderedScripts.toString(),
"component-id":d});c.extend(m,{success:function(a){var b=a.content;LITHIUM.AngularSupport.isAngularEnabled()&&(b=LITHIUM.AngularSupport.compile(b));k.replaceWith(b);LITHIUM.AjaxSupport.ScriptsProcessor.handleScriptEvaluation(a);(a=LITHIUM.jQuery(b).attr("id"))&&LITHIUM.jQuery("#"+a).trigger("LITHIUM:ajaxSuccess:renderInPlace",{componentId:d})},error:function(b,c,d){0===b.readyState||0===b.status?k.html(""):k.html('\x3cspan class\x3d"lia-ajax-error-text"\x3e'+a.errorMessage+"\x3c/span\x3e");k.removeClass(LITHIUM.Css.BASE_LAZY_LOAD).removeClass("lia-fa-spin")}});
k&&LITHIUM.Components.render(d,h,m)}.bind(this))};/*
 modernizr v3.3.1
 Build https://modernizr.com/download?-exiforientation-filereader-flash-setclasses-dontmin

 Copyright (c)
  Faruk Ates
  Paul Irish
  Alex Sexton
  Ryan Seddon
  Patrick Kettner
  Stu Cox
  Richard Herrera

 MIT License
 {
      "name": "EXIF Orientation",
      "property": "exiforientation",
      "tags": ["image"],
      "builderAliases": ["exif_orientation"],
      "async": true,
      "authors": ["Paul Sayre"],
      "notes": [{
        "name": "Article by Dave Perrett",
        "href": "http://recursive-design.com/blog/2012/07/28/exif-orientation-handling-is-a-ghetto/"
      },{
        "name": "Article by Calvin Hass",
        "href": "http://www.impulseadventure.com/photo/exif-orientation.html"
      }]
    }
    ! {
      "name": "Flash",
      "property": "flash",
      "tags": ["flash"],
      "polyfills": ["shumway"]
      }
      ! {
      "name": "File API",
      "property": "filereader",
      "caniuse": "fileapi",
      "notes": [{
        "name": "W3C Working Draft",
        "href": "https://www.w3.org/TR/FileAPI/"
      }],
      "tags": ["file"],
      "builderAliases": ["file_api"],
      "knownBugs": ["Will fail in Safari 5 due to its lack of support for the standards defined FileReader object"]
    }
    !*/
LITHIUM.LiModernizr=function(){(function(d,b,a){function c(g){var a=n.className,b=e._config.classPrefix||"";r&&(a=a.baseVal);e._config.enableJSClass&&(a=a.replace(new RegExp("(^|\\s)"+b+"no-js(\\s|$)"),"$1"+b+"js$2"));e._config.enableClasses&&(a+=" "+b+g.join(" "+b),r?n.className.baseVal=a:n.className=a)}function h(){return"function"!==typeof b.createElement?b.createElement(arguments[0]):r?b.createElementNS.call(b,"http://www.w3.org/2000/svg",arguments[0]):b.createElement.apply(b,arguments)}function q(){var a=
b.body;a||(a=h(r?"svg":"body"),a.fake=!0);return a}function p(a,b){if("object"==typeof a)for(var g in a)u(a,g)&&p(g,a[g]);else{a=a.toLowerCase();g=a.split(".");var l=e[g[0]];2==g.length&&(l=l[g[1]]);if("undefined"!=typeof l)return e;b="function"==typeof b?b():b;1==g.length?e[g[0]]=b:(!e[g[0]]||e[g[0]]instanceof Boolean||(e[g[0]]=new Boolean(e[g[0]])),e[g[0]][g[1]]=b);c([(b&&0!=b?"":"no-")+g.join("-")]);e._trigger(a,b)}return e}var t=[],m=[],k={_version:"3.3.1",_config:{classPrefix:"",enableClasses:!0,
enableJSClass:!0,usePrefixes:!0},_q:[],on:function(a,b){var g=this;setTimeout(function(){b(g[a])},0)},addTest:function(a,b,c){m.push({name:a,fn:b,options:c})},addAsyncTest:function(a){m.push({name:null,fn:a})}},e=function(){};e.prototype=k;e=new e;var n=b.documentElement,r="svg"===n.nodeName.toLowerCase(),u;(function(){var a={}.hasOwnProperty;u="undefined"!==typeof a&&"undefined"!==typeof a.call?function(b,g){return a.call(b,g)}:function(a,b){return b in a&&"undefined"===typeof a.constructor.prototype[b]}})();
k._l={};k.on=function(a,b){this._l[a]||(this._l[a]=[]);this._l[a].push(b);e.hasOwnProperty(a)&&setTimeout(function(){e._trigger(a,e[a])},0)};k._trigger=function(a,b){if(this._l[a]){var g=this._l[a];setTimeout(function(){var a;for(a=0;a<g.length;a++){var c=g[a];c(b)}},0);delete this._l[a]}};e._q.push(function(){k.addTest=p});e.addAsyncTest(function(){LITHIUM.Loader.onLoad(function(){var a=b.createElement("img");a.onerror=function(){p("exiforientation",!1,{aliases:["exif-orientation"]});b.body.removeChild(a)};
a.onload=function(){p("exiforientation",2!==a.width,{aliases:["exif-orientation"]});b.body.removeChild(a)};a.src="data:image/jpeg;base64,/9j/4AAQSkZJRgABAQEAYABgAAD/4QAiRXhpZgAASUkqAAgAAAABABIBAwABAAAABgASAAAAAAD/2wBDAAEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQH/2wBDAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQH/wAARCAABAAIDASIAAhEBAxEB/8QAHwAAAQUBAQEBAQEAAAAAAAAAAAECAwQFBgcICQoL/8QAtRAAAgEDAwIEAwUFBAQAAAF9AQIDAAQRBRIhMUEGE1FhByJxFDKBkaEII0KxwRVS0fAkM2JyggkKFhcYGRolJicoKSo0NTY3ODk6Q0RFRkdISUpTVFVWV1hZWmNkZWZnaGlqc3R1dnd4eXqDhIWGh4iJipKTlJWWl5iZmqKjpKWmp6ipqrKztLW2t7i5usLDxMXGx8jJytLT1NXW19jZ2uHi4+Tl5ufo6erx8vP09fb3+Pn6/8QAHwEAAwEBAQEBAQEBAQAAAAAAAAECAwQFBgcICQoL/8QAtREAAgECBAQDBAcFBAQAAQJ3AAECAxEEBSExBhJBUQdhcRMiMoEIFEKRobHBCSMzUvAVYnLRChYkNOEl8RcYGRomJygpKjU2Nzg5OkNERUZHSElKU1RVVldYWVpjZGVmZ2hpanN0dXZ3eHl6goOEhYaHiImKkpOUlZaXmJmaoqOkpaanqKmqsrO0tba3uLm6wsPExcbHyMnK0tPU1dbX2Nna4uPk5ebn6Onq8vP09fb3+Pn6/9oADAMBAAIRAxEAPwD+/iiiigD/2Q\x3d\x3d";
b.body&&(a.setAttribute("style","position: absolute; left: -9999;"),b.body.appendChild(a))})});e.addAsyncTest(function(){var a=function(a){n.contains(a)||n.appendChild(a)},c=function(a,b){var c=!!a;c&&(c=new Boolean(c),c.blocked="blocked"===a);p("flash",function(){return c});if(b&&f.contains(b)){for(;b.parentNode!==f;)b=b.parentNode;f.removeChild(b)}};try{var e="ActiveXObject"in d&&"Pan"in new d.ActiveXObject("ShockwaveFlash.ShockwaveFlash")}catch(v){}if(!("plugins"in navigator&&"Shockwave Flash"in
navigator.plugins||e)||r)c(!1);else{var l=h("embed"),f=q(),k;l.type="application/x-shockwave-flash";f.appendChild(l);if("Pan"in l||e){var m=function(){a(f);if(!n.contains(f))return f=b.body||f,l=h("embed"),l.type="application/x-shockwave-flash",f.appendChild(l),setTimeout(m,1E3);n.contains(l)?(k=l.style.cssText,""!==k?c("blocked",l):c(!0,l)):c("blocked");f.fake&&f.parentNode&&f.parentNode.removeChild(f)};setTimeout(m,10)}else a(f),c("blocked",l),f.fake&&f.parentNode&&f.parentNode.removeChild(f)}});
e.addTest("filereader",!!(d.File&&d.FileList&&d.FileReader));(function(){var a,b;for(b in m)if(m.hasOwnProperty(b)){var c=[];var d=m[b];if(d.name&&(c.push(d.name.toLowerCase()),d.options&&d.options.aliases&&d.options.aliases.length))for(a=0;a<d.options.aliases.length;a++)c.push(d.options.aliases[a].toLowerCase());d="function"===typeof d.fn?d.fn():d.fn;for(a=0;a<c.length;a++){var f=c[a];f=f.split(".");1===f.length?e[f[0]]=d:(!e[f[0]]||e[f[0]]instanceof Boolean||(e[f[0]]=new Boolean(e[f[0]])),e[f[0]][f[1]]=
d);t.push((d?"":"no-")+f.join("-"))}}})();c(t);delete k.addTest;delete k.addAsyncTest;for(a=0;a<e._q.length;a++)e._q[a]();LITHIUM.Modernizr=e})(window,document)}();(function(){LITHIUM.Globals=function(){var d={};return{preventGlobals:function(b){for(var a=0;a<b.length;a++){var c=b[a];c in window&&void 0!==window[c]&&(d[c]=window[c],window[c]=void 0)}},restoreGlobals:function(b){for(var a=0;a<b.length;a++){var c=b[a];d.hasOwnProperty(c)&&(window[c]=d[c])}}}}()})();(function(){LITHIUM.EarlyEventCapture=function(d,b,a){if(void 0===LITHIUM.jQuery||!0!==LITHIUM.jQuery.isReady||!LITHIUM.Loader.isJsAttached()){var c=d.getAttribute("data-lia-early-event-captured");if(!0===a&&!0!==c||!0===a)d.setAttribute("data-lia-early-event-captured",!0),LITHIUM.Loader.onJsAttached(function(){var a=LITHIUM.jQuery;a(function(){a(d).trigger(b)})});return!1}return!0}})();(function(d){Element.prototype.matches||(Element.prototype.matches=Element.prototype.msMatchesSelector||Element.prototype.webkitMatchesSelector);Element.prototype.closest||(Element.prototype.closest=function(b){var a=this;do{if(a.matches(b))return a;a=a.parentElement||a.parentNode}while(null!==a&&1===a.nodeType);return null})})(LITHIUM.jQuery);window.FileAPI = { jsPath: '/html/assets/js/vendor/ng-file-upload-shim/' };
LITHIUM.PrefetchData = {"Components":{},"commonResults":{}};
LITHIUM.DEBUG = false;
LITHIUM.CommunityJsonObject = {
  "Validation" : {
    "image.description" : {
      "min" : 0,
      "max" : 1000,
      "isoneof" : [ ],
      "type" : "string"
    },
    "tkb.toc_maximum_heading_level" : {
      "min" : 1,
      "max" : 6,
      "isoneof" : [ ],
      "type" : "integer"
    },
    "tkb.toc_heading_list_style" : {
      "min" : 0,
      "max" : 50,
      "isoneof" : [
        "disc",
        "circle",
        "square",
        "none"
      ],
      "type" : "string"
    },
    "blog.toc_maximum_heading_level" : {
      "min" : 1,
      "max" : 6,
      "isoneof" : [ ],
      "type" : "integer"
    },
    "tkb.toc_heading_indent" : {
      "min" : 5,
      "max" : 50,
      "isoneof" : [ ],
      "type" : "integer"
    },
    "blog.toc_heading_indent" : {
      "min" : 5,
      "max" : 50,
      "isoneof" : [ ],
      "type" : "integer"
    },
    "blog.toc_heading_list_style" : {
      "min" : 0,
      "max" : 50,
      "isoneof" : [
        "disc",
        "circle",
        "square",
        "none"
      ],
      "type" : "string"
    }
  },
  "User" : {
    "settings" : {
      "imageupload.legal_file_extensions" : "*.jpg;*.JPG;*.jpeg;*.JPEG;*.gif;*.GIF;*.png;*.PNG;*.webm;*.svg;*.SVG",
      "config.enable_avatar" : true,
      "integratedprofile.show_klout_score" : true,
      "layout.sort_view_by_last_post_date" : true,
      "layout.friendly_dates_enabled" : true,
      "profileplus.allow.anonymous.scorebox" : false,
      "tkb.message_sort_default" : "topicPublishDate",
      "layout.format_pattern_date" : "MM-dd-yyyy",
      "config.require_search_before_post" : "off",
      "isUserLinked" : false,
      "integratedprofile.cta_add_topics_dismissal_timestamp" : -1,
      "layout.message_body_image_max_size" : 1000,
      "profileplus.everyone" : false,
      "integratedprofile.cta_connect_wide_dismissal_timestamp" : -1,
      "blog.toc_maximum_heading_level" : "",
      "integratedprofile.hide_social_networks" : false,
      "blog.toc_heading_indent" : "",
      "contest.entries_per_page_num" : 20,
      "layout.messages_per_page_linear" : 15,
      "integratedprofile.cta_manage_topics_dismissal_timestamp" : -1,
      "profile.shared_profile_test_group" : false,
      "integratedprofile.cta_personalized_feed_dismissal_timestamp" : -1,
      "integratedprofile.curated_feed_size" : 10,
      "contest.one_kudo_per_contest" : false,
      "integratedprofile.enable_social_networks" : false,
      "integratedprofile.my_interests_dismissal_timestamp" : -1,
      "profile.language" : "en",
      "layout.friendly_dates_max_age_days" : 31,
      "layout.threading_order" : "thread_ascending",
      "blog.toc_heading_list_style" : "disc",
      "useRecService" : false,
      "layout.module_welcome" : "<h2>Welcome to SAP Community<\/h2>\r\n<p>Connect and engage with our community to get answers, discuss best practices, \r\nand continually learn more about SAP solutions.<\/p>",
      "imageupload.max_uploaded_images_per_upload" : 100,
      "imageupload.max_uploaded_images_per_user" : 1000,
      "integratedprofile.connect_mode" : "",
      "tkb.toc_maximum_heading_level" : "",
      "tkb.toc_heading_list_style" : "disc",
      "sharedprofile.show_hovercard_score" : true,
      "config.search_before_post_scope" : "community",
      "tkb.toc_heading_indent" : "",
      "p13n.cta.recommendations_feed_dismissal_timestamp" : -1,
      "imageupload.max_file_size" : 9216,
      "layout.show_batch_checkboxes" : false,
      "integratedprofile.cta_connect_slim_dismissal_timestamp" : -1
    },
    "isAnonymous" : true,
    "policies" : {
      "image-upload.process-and-remove-exif-metadata" : false
    },
    "registered" : false,
    "emailRef" : "",
    "id" : -1,
    "login" : "Former Member"
  },
  "Server" : {
    "communityPrefix" : "/khhcw49343",
    "nodeChangeTimeStamp" : 1707286177370,
    "tapestryPrefix" : "/t5",
    "deviceMode" : "DESKTOP",
    "responsiveDeviceMode" : "DESKTOP",
    "membershipChangeTimeStamp" : "0",
    "version" : "23.12",
    "branch" : "23.12-release",
    "showTextKeys" : false
  },
  "Config" : {
    "phase" : "prod",
    "integratedprofile.cta.reprompt.delay" : 30,
    "profileplus.tracking" : {
      "profileplus.tracking.enable" : false,
      "profileplus.tracking.click.enable" : false,
      "profileplus.tracking.impression.enable" : false
    },
    "app.revision" : "2402020916-s44cf0fb5af-b79",
    "navigation.manager.community.structure.limit" : "1000"
  },
  "Activity" : {
    "Results" : [ ]
  },
  "NodeContainer" : {
    "viewHref" : "https://community.sap.com/t5/enterprise-resource-planning/ct-p/erp",
    "description" : "Get support, share your expertise, and engage with the community about SAP S/4HANA, SAP S/4HANA Cloud, and other enterprise resource planning (ERP) software.",
    "id" : "erp",
    "shortTitle" : "Enterprise Resource Planning",
    "title" : "Enterprise Resource Planning",
    "nodeType" : "category"
  },
  "Page" : {
    "skins" : [
      "sap2023",
      "theme_hermes",
      "responsive_peak"
    ],
    "authUrls" : {
      "loginUrl" : "/plugins/common/feature/oidcss/sso_login_redirect/providerid/sap_ids?referer=https%3A%2F%2Fcommunity.sap.com%2Ft5%2Fenterprise-resource-planning-blogs-by-sap%2Fsap-s-4hana-simplification-item-check-how-to-do-it-right%2Fba-p%2F13386669",
      "loginUrlNotRegistered" : "/plugins/common/feature/oidcss/sso_login_redirect/providerid/sap_ids?redirectreason=notregistered&referer=https%3A%2F%2Fcommunity.sap.com%2Ft5%2Fenterprise-resource-planning-blogs-by-sap%2Fsap-s-4hana-simplification-item-check-how-to-do-it-right%2Fba-p%2F13386669",
      "loginUrlNotRegisteredDestTpl" : "/plugins/common/feature/oidcss/sso_login_redirect/providerid/sap_ids?redirectreason=notregistered&referer=%7B%7BdestUrl%7D%7D"
    },
    "name" : "BlogArticlePage",
    "rtl" : false,
    "object" : {
      "viewHref" : "/t5/enterprise-resource-planning-blogs-by-sap/sap-s-4hana-simplification-item-check-how-to-do-it-right/ba-p/13386669",
      "subject" : "SAP S/4HANA Simplification Item Check - How to do it right.",
      "id" : 13386669,
      "page" : "BlogArticlePage",
      "type" : "Thread"
    }
  },
  "WebTracking" : {
    "Activities" : { },
    "path" : "Community:SAP Community/Category:Products and Technology/Category:Enterprise Resource Planning/Blog:ERP Blogs by SAP/Article:SAP S\\/4HANA Simplification Item Check - How to do it right."
  },
  "Feedback" : {
    "targeted" : { }
  },
  "Seo" : {
    "markerEscaping" : {
      "pathElement" : {
        "prefix" : "@",
        "match" : "^[0-9][0-9]$"
      },
      "enabled" : false
    }
  },
  "TopLevelNode" : {
    "viewHref" : "https://community.sap.com/",
    "description" : "",
    "id" : "khhcw49343",
    "shortTitle" : "SAP Community",
    "title" : "SAP Community",
    "nodeType" : "Community"
  },
  "Community" : {
    "viewHref" : "https://community.sap.com/",
    "integratedprofile.lang_code" : "en",
    "integratedprofile.country_code" : "US",
    "id" : "khhcw49343",
    "shortTitle" : "SAP Community",
    "title" : "SAP Community"
  },
  "CoreNode" : {
    "conversationStyle" : "blog",
    "viewHref" : "https://community.sap.com/t5/enterprise-resource-planning-blogs-by-sap/bg-p/erp-blog-sap",
    "settings" : { },
    "description" : "Get insights and updates about cloud ERP and RISE with SAP, SAP S/4HANA and SAP S/4HANA Cloud, and more enterprise management capabilities with SAP blog posts.",
    "id" : "erp-blog-sap",
    "shortTitle" : "ERP Blogs by SAP",
    "title" : "Enterprise Resource Planning Blogs by SAP",
    "nodeType" : "Board",
    "ancestors" : [
      {
        "viewHref" : "https://community.sap.com/t5/enterprise-resource-planning/ct-p/erp",
        "description" : "Get support, share your expertise, and engage with the community about SAP S/4HANA, SAP S/4HANA Cloud, and other enterprise resource planning (ERP) software.",
        "id" : "erp",
        "shortTitle" : "Enterprise Resource Planning",
        "title" : "Enterprise Resource Planning",
        "nodeType" : "category"
      },
      {
        "viewHref" : "https://community.sap.com/t5/products-and-technology/ct-p/products",
        "description" : "",
        "id" : "products",
        "shortTitle" : "Products and Technology",
        "title" : "Products and Technology",
        "nodeType" : "category"
      },
      {
        "viewHref" : "https://community.sap.com/",
        "description" : "",
        "id" : "khhcw49343",
        "shortTitle" : "SAP Community",
        "title" : "SAP Community",
        "nodeType" : "Community"
      }
    ]
  }
};
LITHIUM.Components.RENDER_URL = "/t5/util/componentrenderpage/component-id/#{component-id}?render_behavior=raw";
LITHIUM.Components.ORIGINAL_PAGE_NAME = 'blogs/v2/BlogArticlePage';
LITHIUM.Components.ORIGINAL_PAGE_ID = 'BlogArticlePage';
LITHIUM.Components.ORIGINAL_PAGE_CONTEXT = 'b5Uqhruv2wqmain0FJ4Lb4zRhLaVgyvfeTQq8OIR1FGFqlAYlo5ahYWyYkQzaysGm92sku7J35eBAg2UH2vRJe_wvQRPCwPxQdsr1bsoUubfsPaUDEJCBSwWRIF3Nrf_NR-rfZTolKYhaNMrkrw1IoZVxHHkyB_kEaGeF7T_yZicrvBS4V0JPIZP6H842qZ5EiytSsRbnb-aJapXz405bCu-Yt8dYYeWAe3OztPy23k_FHvsiDwatbydpZ337qB9LYAe0YVLwfYhHchZUyaTNx6MXHqSe-HafjNdkj8_JQXz4F_Dcx-bD4Wc-kvagxBwmlOBd9DP_oqSBgyA3ilRwBGThu1KjKDgVMa__d8keCQ.';
LITHIUM.Css = {
  "BASE_DEFERRED_IMAGE" : "lia-deferred-image",
  "BASE_BUTTON" : "lia-button",
  "BASE_SPOILER_CONTAINER" : "lia-spoiler-container",
  "BASE_TABS_INACTIVE" : "lia-tabs-inactive",
  "BASE_TABS_ACTIVE" : "lia-tabs-active",
  "BASE_AJAX_REMOVE_HIGHLIGHT" : "lia-ajax-remove-highlight",
  "BASE_FEEDBACK_SCROLL_TO" : "lia-feedback-scroll-to",
  "BASE_FORM_FIELD_VALIDATING" : "lia-form-field-validating",
  "BASE_FORM_ERROR_TEXT" : "lia-form-error-text",
  "BASE_FEEDBACK_INLINE_ALERT" : "lia-panel-feedback-inline-alert",
  "BASE_BUTTON_OVERLAY" : "lia-button-overlay",
  "BASE_TABS_STANDARD" : "lia-tabs-standard",
  "BASE_AJAX_INDETERMINATE_LOADER_BAR" : "lia-ajax-indeterminate-loader-bar",
  "BASE_AJAX_SUCCESS_HIGHLIGHT" : "lia-ajax-success-highlight",
  "BASE_CONTENT" : "lia-content",
  "BASE_JS_HIDDEN" : "lia-js-hidden",
  "BASE_AJAX_LOADER_CONTENT_OVERLAY" : "lia-ajax-loader-content-overlay",
  "BASE_FORM_FIELD_SUCCESS" : "lia-form-field-success",
  "BASE_FORM_WARNING_TEXT" : "lia-form-warning-text",
  "BASE_FORM_FIELDSET_CONTENT_WRAPPER" : "lia-form-fieldset-content-wrapper",
  "BASE_AJAX_LOADER_OVERLAY_TYPE" : "lia-ajax-overlay-loader",
  "BASE_FORM_FIELD_ERROR" : "lia-form-field-error",
  "BASE_SPOILER_CONTENT" : "lia-spoiler-content",
  "BASE_FORM_SUBMITTING" : "lia-form-submitting",
  "BASE_EFFECT_HIGHLIGHT_START" : "lia-effect-highlight-start",
  "BASE_FORM_FIELD_ERROR_NO_FOCUS" : "lia-form-field-error-no-focus",
  "BASE_EFFECT_HIGHLIGHT_END" : "lia-effect-highlight-end",
  "BASE_SPOILER_LINK" : "lia-spoiler-link",
  "BASE_DISABLED" : "lia-link-disabled",
  "FACEBOOK_LOGOUT" : "lia-component-users-action-logout",
  "FACEBOOK_SWITCH_USER" : "lia-component-admin-action-switch-user",
  "BASE_FORM_FIELD_WARNING" : "lia-form-field-warning",
  "BASE_AJAX_LOADER_FEEDBACK" : "lia-ajax-loader-feedback",
  "BASE_AJAX_LOADER_OVERLAY" : "lia-ajax-loader-overlay",
  "BASE_LAZY_LOAD" : "lia-lazy-load"
};
LITHIUM.noConflict = true;
LITHIUM.useCheckOnline = false;
LITHIUM.RenderedScripts = [
  "SpoilerToggle.js",
  "json2.js",
  "Loader.js",
  "jquery.autocomplete.js",
  "prism.js",
  "jquery.ui.position.js",
  "jquery.tmpl-1.1.1.js",
  "jquery.ui.mouse.js",
  "jquery.ui.draggable.js",
  "InformationBox.js",
  "DropDownMenuVisibilityHandler.js",
  "jquery.function-utils-1.0.js",
  "jquery.effects.core.js",
  "Auth.js",
  "jquery.ui.widget.js",
  "Globals.js",
  "jquery.js",
  "Placeholder.js",
  "Link.js",
  "DropDownMenu.js",
  "jquery.css-data-1.0.js",
  "AjaxFeedback.js",
  "SearchAutoCompleteToggle.js",
  "UserListActual.js",
  "jquery.ui.dialog.js",
  "jquery.effects.slide.js",
  "MessageBodyDisplay.js",
  "PolyfillsAll.js",
  "SearchForm.js",
  "AjaxSupport.js",
  "Throttle.js",
  "Namespace.js",
  "LiModernizr.js",
  "jquery.ui.core.js",
  "jquery.scrollTo.js",
  "jquery.fileupload.js",
  "jquery.ui.resizable.js",
  "ForceLithiumJQuery.js",
  "jquery.json-2.6.0.js",
  "CustomEvent.js",
  "jquery.iframe-transport.js",
  "jquery.iframe-shim-1.0.js",
  "jquery.delayToggle-1.0.js",
  "Cache.js",
  "jquery.blockui.js",
  "Events.js",
  "DataHandler.js",
  "EarlyEventCapture.js",
  "jquery.ajax-cache-response-1.0.js",
  "Sandbox.js",
  "jquery.placeholder-2.0.7.js",
  "jquery.appear-1.1.1.js",
  "jquery.clone-position-1.0.js",
  "jquery.position-toggle-1.0.js",
  "Components.js",
  "HelpIcon.js",
  "ActiveCast3.js",
  "Video.js",
  "Forms.js",
  "DeferredImages.js",
  "AutoComplete.js",
  "Text.js",
  "jquery.viewport-1.0.js",
  "NoConflict.js",
  "ElementQueries.js",
  "jquery.lithium-selector-extensions.js",
  "Lithium.js",
  "PartialRenderProxy.js",
  "ElementMethods.js",
  "Tooltip.js",
  "ResizeSensor.js",
  "jquery.hoverIntent-r6.js",
  "jquery.tools.tooltip-1.2.6.js"
];(function(){LITHIUM.AngularSupport=function(){function g(a,c){a=a||{};for(var b in c)"[object object]"===Object.prototype.toString.call(c[b])?a[b]=g(a[b],c[b]):a[b]=c[b];return a}var d,f,b={coreModule:"li.community",coreModuleDeps:[],noConflict:!0,bootstrapElementSelector:".lia-page .min-width .lia-content",bootstrapApp:!0,debugEnabled:!1,useCsp:!0,useNg2:!1},k=function(){var a;return function(b){a||(a=document.createElement("a"));a.href=b;return a.href}}();LITHIUM.Angular={};return{preventGlobals:LITHIUM.Globals.preventGlobals,
restoreGlobals:LITHIUM.Globals.restoreGlobals,init:function(){var a=[],c=document.querySelector(b.bootstrapElementSelector);a.push(b.coreModule);b.customerModules&&0<b.customerModules.length&&a.concat(b.customerModules);b.useCsp&&(c.setAttribute("ng-csp","no-unsafe-eval"),c.setAttribute("li-common-non-bindable",""));d=LITHIUM.angular.module(b.coreModule,b.coreModuleDeps);d.config(["$locationProvider","$provide","$injector","$logProvider","$compileProvider","$qProvider","$anchorScrollProvider",function(a,
c,e,d,f,g,h){h.disableAutoScrolling();h=document.createElement("base");h.setAttribute("href",k(location));document.getElementsByTagName("head")[0].appendChild(h);window.history&&window.history.pushState&&a.html5Mode({enabled:!0,requireBase:!0,rewriteLinks:!1}).hashPrefix("!");d.debugEnabled(b.debugEnabled);f.debugInfoEnabled(b.debugEnabled);e.has("$uibModal")&&c.decorator("$uibModal",["$delegate",function(a){var b=a.open;a.open=function(a){a.backdropClass=(a.backdropClass?a.backdropClass+" ":"")+
"lia-modal-backdrop";a.windowClass=(a.windowClass?a.windowClass+" ":"")+"lia-modal-window";return b(a)};return a}]);e.has("uibDropdownConfig")&&(e.get("uibDropdownConfig").openClass="lia-dropdown-open");e.has("uibButtonConfig")&&(e.get("uibButtonConfig").activeClass="lia-link-active");g.errorOnUnhandledRejections(!1)}]);if(b.bootstrapApp)f=b.useNg2?LITHIUM.Angular.upgradeAdapter.bootstrap(c,a):LITHIUM.angular.bootstrap(c,a);else LITHIUM.Loader.onLoad(function(){f=LITHIUM.angular.element(c).injector()});
LITHIUM.Angular.app=d},compile:function(a){void 0===a&&(a=document.querySelector(b.bootstrapElementSelector));var c;if(void 0===a||""===a)return a;f.invoke(["$rootScope","$compile",function(b,d){try{var e=LITHIUM.angular.element(a)}catch(l){e=LITHIUM.angular.element("\x3cli:safe-wrapper\x3e"+a+"\x3c/li:safe-wrapper\x3e")}e.attr("li-common-non-bindable","");c=d(e)(b);b.$digest()}]);return c},isAngularEnabled:function(){return void 0!==d},updateLocationUrl:function(a,b){f.invoke(["$location","$rootScope",
"$browser",function(c,d,e){a=""===a?"?":a;c.url(a,b);d.$apply()}])},setOptions:function(a){return g(b,a)},getOptions:function(){return b},initGlobal:function(a){LITHIUM.angular=a;b.useNg2&&(LITHIUM.Angular.upgradeAdapter=new ng.upgrade.UpgradeAdapter)}}}()})();(function(){LITHIUM.ScriptLoader=function(){function d(a){a in e||(e[a]={loaded:!1});return e[a]}function f(){g.filter(function(a){return!a.loaded}).forEach(function(a){var b=!0;a.labels.forEach(function(a){!1===d(a).loaded&&(b=!1)});b&&(a.loaded=!0,a.callback())})}var e={},g=[];return{load:function(a){a.forEach(function(a){var b=document.getElementsByTagName("head")[0]||document.documentElement,c=document.createElement("script");c.src=a.url;c.async=!1;a.crossorigin&&a.integrity&&(c.setAttribute("crossorigin",
a.crossorigin),c.setAttribute("integrity",a.integrity));b.insertBefore(c,b.firstChild);d(a.label)})},setLoaded:function(a){d(a).loaded=!0;f()},ready:function(a,b){g.push({labels:a,callback:b,loaded:!1});f()}}}()})();LITHIUM.ScriptLoader.load([{"name":"lia-scripts-common-min.js","label":"common","url":"/t5/scripts/AEDAE865B0CFD50743A695EDB8D1125C/lia-scripts-common-min.js"},{"name":"lia-scripts-body-min.js","label":"body","url":"/t5/scripts/795AC2DDAACCCFDDF501127C8CBD2004/lia-scripts-body-min.js"},{"name":"lia-scripts-angularjs-min.js","label":"angularjs","url":"/t5/scripts/B2E81B1ECCE0975FEC768641B9390130/lia-scripts-angularjs-min.js"},{"name":"lia-scripts-angularjsModules-min.js","label":"angularjsModules","url":"/t5/scripts/951858BAD326D0669C2BAF489130C8F0/lia-scripts-angularjsModules-min.js"}]);
// -->
  </script>
 </head>
 <body class="lia-blog lia-user-status-anonymous BlogArticlePage lia-body lia-a11y" id="lia-body">
  <div class="ServiceNodeInfoHeader" id="3EA-119-8">
  </div>
  <div class="lia-page">
   <center>
    <div style="height: 64px; background: black">
     <ds-header identifier="community" locale="en-us" s3-bucket="https://contextualnavigation.api.community.sap.com">
     </ds-header>
    </div>
    <script type="module">
     // Listen to header custom events
    document.addEventListener('login', () => {
        window.location.href = "/plugins/common/feature/oidcss/sso_login_redirect/providerid/sap_ids?referer=https%3A%2F%2Fcommunity.sap.com%2Ft5%2Fenterprise-resource-planning-blogs-by-sap%2Fsap-s-4hana-simplification-item-check-how-to-do-it-right%2Fba-p%2F13386669";
    });
    document.addEventListener('logout', () => {
        window.location.href = "https://community.sap.com/t5/community/page.logoutpage?t:cp=authentication/contributions/unticketedauthenticationactions&dest_url=https%3A%2F%2Fcommunity.sap.com%2F&lia-action-token=CLcWIIhjPQZHxEq5-iEpbcsEd4K0ftvk9gytoy7pigM.&lia-action-token-id=logoff";
    });
    </script>
    <div class="MinimumWidthContainer">
     <div class="min-width-wrapper">
      <div class="min-width">
       <div class="lia-content">
        <div class="lia-quilt lia-quilt-blog-article-page lia-quilt-layout-two-column-main-side lia-top-quilt">
         <div class="lia-quilt-row lia-quilt-row-header">
          <div class="lia-quilt-column lia-quilt-column-24 lia-quilt-column-single lia-quilt-column-common-header">
           <div class="lia-quilt-column-alley lia-quilt-column-alley-single">
            <div class="lia-quilt lia-quilt-header lia-quilt-layout-custom-community-header lia-component-quilt-header">
             <div class="lia-quilt-row lia-quilt-row-header-top">
              <div class="lia-quilt-column lia-quilt-column-24 lia-quilt-column-single lia-quilt-column-header-top-content lia-mark-empty">
              </div>
             </div>
             <div class="lia-quilt-row lia-quilt-row-header-navigation">
              <div class="lia-quilt-column lia-quilt-column-24 lia-quilt-column-single lia-quilt-column-header-navigation-content">
               <div class="lia-quilt-column-alley lia-quilt-column-alley-single">
                <div class="custom-community-header-left">
                 <div aria-label="breadcrumbs" class="BreadCrumb crumb-line lia-breadcrumb lia-component-common-widget-breadcrumb" role="navigation">
                  <ul class="lia-list-standard-inline" id="list" role="list">
                   <li class="lia-breadcrumb-node crumb">
                    <a class="lia-link-navigation crumb-community lia-breadcrumb-community lia-breadcrumb-forum" href="/" id="link_0">
                     SAP Community
                    </a>
                   </li>
                   <li aria-hidden="true" class="lia-breadcrumb-seperator crumb-community lia-breadcrumb-community lia-breadcrumb-forum">
                    <span>
                     <span alt="" aria-label="" class="lia-img-icon-list-separator-breadcrumb lia-fa-icon lia-fa-list lia-fa-separator lia-fa-breadcrumb lia-fa" id="display" role="img">
                     </span>
                    </span>
                   </li>
                   <li class="lia-breadcrumb-node crumb">
                    <a class="lia-link-navigation crumb-category lia-breadcrumb-category lia-breadcrumb-forum" href="/t5/products-and-technology/ct-p/products" id="link_1">
                     Products and Technology
                    </a>
                   </li>
                   <li aria-hidden="true" class="lia-breadcrumb-seperator crumb-category lia-breadcrumb-category lia-breadcrumb-forum">
                    <span>
                     <span alt="" aria-label="" class="lia-img-icon-list-separator-breadcrumb lia-fa-icon lia-fa-list lia-fa-separator lia-fa-breadcrumb lia-fa" id="display_0" role="img">
                     </span>
                    </span>
                   </li>
                   <li class="lia-breadcrumb-node crumb">
                    <a class="lia-link-navigation crumb-category lia-breadcrumb-category lia-breadcrumb-forum" href="/t5/enterprise-resource-planning/ct-p/erp" id="link_2">
                     Enterprise Resource Planning
                    </a>
                   </li>
                   <li aria-hidden="true" class="lia-breadcrumb-seperator crumb-category lia-breadcrumb-category lia-breadcrumb-forum">
                    <span>
                     <span alt="" aria-label="" class="lia-img-icon-list-separator-breadcrumb lia-fa-icon lia-fa-list lia-fa-separator lia-fa-breadcrumb lia-fa" id="display_1" role="img">
                     </span>
                    </span>
                   </li>
                   <li class="lia-breadcrumb-node crumb">
                    <a class="lia-link-navigation crumb-board lia-breadcrumb-board lia-breadcrumb-forum" href="/t5/enterprise-resource-planning-blogs-by-sap/bg-p/erp-blog-sap" id="link_3">
                     ERP Blogs by SAP
                    </a>
                   </li>
                   <li aria-hidden="true" class="lia-breadcrumb-seperator crumb-board lia-breadcrumb-board lia-breadcrumb-forum">
                    <span>
                     <span alt="" aria-label="" class="lia-img-icon-list-separator-breadcrumb lia-fa-icon lia-fa-list lia-fa-separator lia-fa-breadcrumb lia-fa" id="display_2" role="img">
                     </span>
                    </span>
                   </li>
                   <li class="lia-breadcrumb-node crumb final-crumb">
                    <span aria-disabled="true" aria-label="SAP S/4HANA Simplification Item Check - How to do it right." class="lia-link-navigation child-thread lia-link-disabled" disabled="true" id="link_4" role="link">
                     SAP S/4HANA Simplification Item Check - How to do ...
                    </span>
                   </li>
                  </ul>
                 </div>
                </div>
                <div class="custom-community-header-right lia-mark-empty">
                </div>
               </div>
              </div>
             </div>
             <div class="lia-quilt-row lia-quilt-row-header-hero">
              <div class="lia-quilt-column lia-quilt-column-24 lia-quilt-column-single lia-quilt-column-header-hero-content">
               <div class="lia-quilt-column-alley lia-quilt-column-alley-single">
                <div class="header-hero-wrapper">
                 <div class="lia-node-header-info lia-component-common-widget-node-information" id="nodeInformation">
                  <div class="lia-node-header-title">
                   Enterprise Resource Planning Blogs by SAP
                  </div>
                  <div class="lia-node-header-description">
                   Get insights and updates about cloud ERP and RISE with SAP, SAP S/4HANA and SAP S/4HANA Cloud, and more enterprise management capabilities with SAP blog posts.
                  </div>
                 </div>
                 <div class="SearchForm lia-search-form-wrapper lia-mode-default lia-component-common-widget-search-form" id="lia-searchformV32">
                  <div class="lia-inline-ajax-feedback">
                   <div class="AjaxFeedback" id="ajaxfeedback">
                   </div>
                  </div>
                  <div id="searchautocompletetoggle">
                   <div class="lia-inline-ajax-feedback">
                    <div class="AjaxFeedback" id="ajaxfeedback_0">
                    </div>
                   </div>
                   <form action="https://community.sap.com/t5/blogs/v2/blogarticlepage.searchformv32.form.form" class="lia-form lia-form-inline SearchForm" enctype="multipart/form-data" id="form" method="post" name="form">
                    <div class="t-invisible">
                     <input name="t:ac" type="hidden" value="blog-id/erp-blog-sap/article-id/26183"/>
                     <input name="t:cp" type="hidden" value="search/contributions/page"/>
                     <input name="lia-form-context" type="hidden" value="Yx17FPaKZ3xgdAm5aat1mDYdl9ujBdPLLyK8br5xKLh8x090HijeStuYG2ORv1zHUPWYxJey-52MkeR22YkZTHYHUN7hyQVRMsMHuxN1mi2AxQU7W6VLdLO94GmAe2F6ua4kzw1_JzvkWpd_xBELxV0p2Bm5ge-nqNuOmXpF-cU2txohAJosBTdby-h6wxZhJx_QOE2rPqV40AVT5hLofKVXAUy4CmrdGs1cNtKMeewQ82-O93rjQU3x5ZW9xCJ5mT-3RrbEn_tedERElCmPNdWd4iJGjIwWOrbzqcpqnLXys6KlvsM92VvhOxZcU-7QA9YPk8sG_FjfFS9_1QOBIQzyCQucQ6EySvZJfX6Fc03EMrRenulLcdsqiM4y42QJTPh1oOaFht7cgs_wpL-uDQQk_B79MNChog1IDc2idoxlrHpmnNoIayrRLMLT4nMsrwlnfBwYN_5Cm__4HFxm0P6OH41ronNEbQ3m-Q7eLpVFXsMR5_zoVCVVULZBFCQWdtmkKOK64r85G8nRA1X8_86bSoYcjE8K-G2uuThPhpWLMZbC4kJ29Bz10kyBX9gs09j9iZY73NwYLrbUiZG3C-_iXs21wpNFUgNaQuVZFmf68IBTJQH-MYGcjbAGyhGusqWj7bNhVG4mI9OLi8hhUYtNhhpfQaE_QWv02YZ4AXvlKBNTW6MY9y0XuQyqlWIsYMHIanVZH55-l51acFrCy2IZkiG7-10yq8tjo7DVLdC7AoYaHXegqNcE-DUcrZvtfjogweOgRsi7onvz3pR8xlB9TvgwqZqAynBvSPKMQz0fD-W_iwOTDPaQbh7eeWA-WVRBgWI-po1hSQc6d8gAGcvV0CFP5BaSY3LXyYRo_1G1LYfARM13fMt0afto_ewO7D_zrD9i6u-hvEAdhEYNzBnaTvucN8oaFMKvwvQxjSZmSnP0eNEoymXJC-o-AgIr03HQUvGZEXLZm4lRkmSv6mECTLWiUyrQVLW-K531il7CCojYoiJd_fBgQQdyiExEfCdcJFkl0dBbW8FEMrXFbd_UDBpOyj7RwuD6JlEUb9k8X8j9GlNBppbGHunDFKJlBs2V4tMyBbf7otzF1uVUTQ9gvNo0ICmAWV2QwDuMhZNG1bKz0ddRkrYjcmsiJN5GXRzsjXe4sVDyFkpkHd1Ln-INZbuzC_j4eVJWruHeIGi7ZBGykzRROrGDn7o9-_IS-CNjCryByxj3TF7pQdSuy3wP5HAZRiSqJY1z5nS0yQ4."/>
                     <input name="liaFormContentKey" type="hidden" value="BlogArticlePage:blog-id/erp-blog-sap/article-id/26183:searchformv32.form:"/>
                     <input name="t:formdata" type="hidden" value="5DI9GWMef1Esyz275vuiiOExwpQ=:H4sIAAAAAAAAALVSTU7CQBR+krAixkj0BrptjcpCMSbERGKCSmxcm+kwlGrbqTOvFDYexRMYL8HCnXfwAG5dubDtFKxgYgu4mrzvm3w/M+/pHcphHQ4kI4L2dMo9FLYZoM09qbeJxQ4V0+XC7e/tamqyBPEChwgbh1JAjQtLIz6hPaYh8ZlEMaxplAvm2KZmEsm0hhmBhOKpzZzOlsEw8LevR5W3zZfPEqy0oJIYc+eCuAyh2rolfaI7xLN0I8rjWfWBj7CuzJvf5osmbxRN3hacMimNwHRtKSOr0XNnv/vx+FoCGPjhMRzljhNLYHrEt9kA5T08ACCsKvREoYuqxqLl8BLO84q4UcMITcG49y/QOGs1pYyESl5p6V6qwRW086rinVmoxMZsiZud/zBUTc6gmVc4kExkJafmcYG1GM9+wfIsCkf2OP54hal5EjnG54z8h0XhjfcF7wQUs5Kz0GTjU2rOjc/llTT4Au07pDOcBQAA"/>
                    </div>
                    <div class="lia-inline-ajax-feedback">
                     <div class="AjaxFeedback" id="feedback">
                     </div>
                    </div>
                    <input name="lia-action-token" type="hidden" value="nBeTtNz7WL-MWLtq2jHdFR1Vuk0oFPvc1TuE8KwMEj0."/>
                    <input id="form_UIDform" name="form_UID" type="hidden" value="form"/>
                    <input id="form_instance_keyform" name="form_instance_key" type="hidden" value=""/>
                    <span class="lia-search-granularity-wrapper">
                     <select aria-label="Search Granularity" class="lia-search-form-granularity search-granularity" id="searchGranularity" name="searchGranularity" title="Search Granularity">
                      <option selected="selected" title="All community" value="khhcw49343|community">
                       All community
                      </option>
                      <option title="This category" value="erp|category">
                       This category
                      </option>
                      <option title="Blog" value="erp-blog-sap|blog-board">
                       Blog
                      </option>
                      <option title="Knowledge base" value="tkb|tkb">
                       Knowledge base
                      </option>
                      <option title="Users" value="user|user">
                       Users
                      </option>
                      <option title="Managed tags" value="product|product">
                       Managed tags
                      </option>
                     </select>
                    </span>
                    <span class="lia-search-input-wrapper">
                     <span class="lia-search-input-field">
                      <span class="lia-button-wrapper lia-button-wrapper-secondary lia-button-wrapper-searchForm-action">
                       <input name="submitContextX" type="hidden" value="searchForm"/>
                       <input class="lia-button lia-button-secondary lia-button-searchForm-action" id="submitContext" name="submitContext" type="submit" value="Search"/>
                      </span>
                      <span class="lia-hidden-aria-visibile" id="autocompleteInstructionsText">
                      </span>
                      <input aria-label="Search" class="lia-form-type-text lia-autocomplete-input search-input lia-search-input-message" id="messageSearchField_0" name="messageSearchField" placeholder="What are you looking for today?" title="Search" type="text" value=""/>
                      <span class="lia-hidden-aria-visibile" id="autocompleteInstructionsText_0">
                      </span>
                      <input aria-label="Search" class="lia-form-type-text lia-autocomplete-input search-input lia-search-input-tkb-article lia-js-hidden" id="messageSearchField_1" name="messageSearchField_0" placeholder="What are you looking for today?" title="Search" type="text" value=""/>
                      <span class="lia-hidden-aria-visibile" id="autocompleteInstructionsText_1">
                      </span>
                      <input aria-label="Enter a user name or rank" class="lia-form-type-text UserSearchField lia-search-input-user search-input lia-js-hidden lia-autocomplete-input" id="userSearchField" name="userSearchField" ng-non-bindable="" placeholder="Enter a keyword to search within the users" title="Enter a user name or rank" type="text" value=""/>
                      <span class="lia-hidden-aria-visibile" id="autocompleteInstructionsText_2">
                      </span>
                      <input aria-label="Enter a search word" class="lia-form-type-text NoteSearchField lia-search-input-note search-input lia-js-hidden lia-autocomplete-input" id="noteSearchField_0" name="noteSearchField" placeholder="Enter a keyword to search within the private messages" title="Enter a search word" type="text" value=""/>
                      <span class="lia-hidden-aria-visibile" id="autocompleteInstructionsText_3">
                      </span>
                      <input aria-label="Enter a search word" class="lia-form-type-text ProductSearchField lia-search-input-product search-input lia-js-hidden lia-autocomplete-input" id="productSearchField" name="productSearchField" title="Enter a search word" type="text" value=""/>
                      <input class="lia-as-search-action-id" name="as-search-action-id" type="hidden"/>
                     </span>
                    </span>
                    <span class="lia-cancel-search">
                     cancel
                    </span>
                   </form>
                   <div class="search-autocomplete-toggle-link lia-js-hidden">
                    <span>
                     <a class="lia-link-navigation auto-complete-toggle-on lia-link-ticket-post-action lia-component-search-action-enable-auto-complete" data-lia-action-token="WxHMmjiBphj7y4Zfu3ysILq90yO7n85gpqSzr7MlRQc." href="https://community.sap.com/t5/blogs/v2/blogarticlepage.enableautocomplete:enableautocomplete?t:ac=blog-id/erp-blog-sap/article-id/26183&amp;t:cp=action/contributions/searchactions" id="enableAutoComplete" rel="nofollow">
                      Turn on suggestions
                     </a>
                     <span class="HelpIcon">
                      <a aria-label="Help Icon" class="lia-link-navigation help-icon lia-tooltip-trigger" href="#" id="link_5" role="button">
                       <span alt="Auto-suggest helps you quickly narrow down your search results by suggesting possible matches as you type." aria-label="Help Icon" class="lia-img-icon-help lia-fa-icon lia-fa-help lia-fa" id="display_3" role="img">
                       </span>
                      </a>
                      <div class="lia-content lia-tooltip-pos-bottom-left lia-panel-tooltip-wrapper" id="link_6-tooltip-element" role="alertdialog">
                       <div class="lia-tooltip-arrow">
                       </div>
                       <div class="lia-panel-tooltip">
                        <div class="content">
                         Auto-suggest helps you quickly narrow down your search results by suggesting possible matches as you type.
                        </div>
                       </div>
                      </div>
                     </span>
                    </span>
                   </div>
                  </div>
                  <div class="spell-check-showing-result">
                   Showing results for
                   <span aria-disabled="true" class="lia-link-navigation show-results-for-link lia-link-disabled" id="showingResult">
                   </span>
                  </div>
                  <div>
                   <span class="spell-check-search-instead">
                    Search instead for
                    <a class="lia-link-navigation search-instead-for-link" href="#" id="searchInstead" rel="nofollow">
                    </a>
                   </span>
                  </div>
                  <div class="spell-check-do-you-mean lia-component-search-widget-spellcheck">
                   Did you mean:
                   <a class="lia-link-navigation do-you-mean-link" href="#" id="doYouMean" rel="nofollow">
                   </a>
                  </div>
                 </div>
                </div>
               </div>
              </div>
             </div>
             <div class="lia-quilt-row lia-quilt-row-header-bottom">
              <div class="lia-quilt-column lia-quilt-column-24 lia-quilt-column-single lia-quilt-column-header-bottom-content">
               <div class="lia-quilt-column-alley lia-quilt-column-alley-single lia-mark-empty">
               </div>
              </div>
             </div>
             <div class="lia-quilt-row lia-quilt-row-header-feedback">
              <div class="lia-quilt-column lia-quilt-column-24 lia-quilt-column-single lia-quilt-column-feedback">
               <div class="lia-quilt-column-alley lia-quilt-column-alley-single">
               </div>
              </div>
             </div>
            </div>
           </div>
          </div>
         </div>
         <div class="lia-quilt-row lia-quilt-row-main">
          <div class="lia-quilt-column lia-quilt-column-16 lia-quilt-column-left lia-quilt-column-main-content">
           <div class="lia-quilt-column-alley lia-quilt-column-alley-left">
            <div class="lia-panel-message message-uid-13386669 lia-component-article" data-lia-message-uid="13386669" id="messageview">
             <div class="lia-message-view-wrapper lia-js-data-messageUid-13386669 lia-component-forums-widget-message-view-two" data-lia-message-uid="13386669" id="messageView2_1">
              <span id="U13386669">
              </span>
              <span id="M26183">
              </span>
              <div class="lia-inline-ajax-feedback">
               <div class="AjaxFeedback" id="ajaxfeedback_1">
               </div>
              </div>
              <div class="MessageView lia-message-view-blog-topic-message lia-message-view-display lia-row-standard-unread lia-thread-topic">
               <span class="lia-message-state-indicator">
               </span>
               <div class="lia-quilt lia-quilt-blog-topic-message lia-quilt-layout-custom-message">
                <div class="lia-quilt-row lia-quilt-row-message-header-top">
                 <div class="lia-quilt-column lia-quilt-column-24 lia-quilt-column-single lia-quilt-column-message-header-top-content">
                  <div class="lia-quilt-column-alley lia-quilt-column-alley-single">
                   <div class="lia-message-subject lia-component-message-view-widget-subject">
                    <div class="MessageSubject">
                     <div class="MessageSubjectIcons">
                      <h2 class="message-subject" itemprop="name">
                       <span class="lia-message-unread">
                        <a class="page-link lia-link-navigation lia-custom-event" href="/t5/enterprise-resource-planning-blogs-by-sap/sap-s-4hana-simplification-item-check-how-to-do-it-right/ba-p/13386669" id="link_7">
                         SAP S/4HANA Simplification Item Check - How to do it right.
                        </a>
                       </span>
                      </h2>
                      <span alt="Message contains a hyperlink" aria-label="Contains a hyperlink" class="lia-img-message-has-url lia-fa-message lia-fa-has lia-fa-url lia-fa" id="display_4" role="img" title="Contains a hyperlink">
                      </span>
                      <span alt="Message contains an image" aria-label="Contains an image" class="lia-img-message-has-image lia-fa-message lia-fa-has lia-fa-image lia-fa" id="display_5" role="img" title="Contains an image">
                      </span>
                     </div>
                    </div>
                   </div>
                  </div>
                 </div>
                </div>
                <div class="lia-quilt-row lia-quilt-row-message-header-main">
                 <div class="lia-quilt-column lia-quilt-column-24 lia-quilt-column-single lia-quilt-column-message-header-main-content lia-mark-empty">
                 </div>
                </div>
                <div class="lia-quilt-row lia-quilt-row-message-header-bottom">
                 <div class="lia-quilt-column lia-quilt-column-16 lia-quilt-column-left lia-quilt-column-message-header-bottom-left">
                  <div class="lia-quilt-column-alley lia-quilt-column-alley-left">
                   <div class="lia-message-author-avatar">
                    <div class="UserAvatar lia-user-avatar lia-component-common-widget-user-avatar lia-component-message-view-widget-author-avatar">
                     <img alt="markus_goebel" class="lia-user-avatar-profile" id="imagedisplay" src="https://avatars.profile.sap.com/4/7/id4728db278cdc80186e86b2f89ab73a1adc544a5a7410b2b6c4e146989a1df354_small.jpeg" title="markus_goebel"/>
                    </div>
                   </div>
                   <div class="lia-message-author-with-avatar">
                    <span class="UserName lia-user-name lia-user-rank-Explorer lia-component-message-view-widget-author-username">
                     <a aria-label="View Profile of markus_goebel" class="lia-link-navigation lia-page-link lia-user-name-link" href="https://community.sap.com/t5/user/viewprofilepage/user-id/282993" id="link_8" itemprop="url" style="" target="_self">
                      <span class="">
                       markus_goebel
                      </span>
                     </a>
                    </span>
                    <div class="lia-message-author-rank lia-component-author-rank lia-component-message-view-widget-author-rank">
                     Explorer
                    </div>
                   </div>
                  </div>
                 </div>
                 <div class="lia-quilt-column lia-quilt-column-08 lia-quilt-column-right lia-quilt-column-message-header-bottom-right">
                  <div class="lia-quilt-column-alley lia-quilt-column-alley-right">
                   <div class="lia-menu-navigation-wrapper lia-js-hidden lia-menu-action lia-component-message-view-widget-action-menu" id="actionMenuDropDown">
                    <div class="lia-menu-navigation">
                     <div class="dropdown-default-item">
                      <a aria-expanded="false" aria-label="Show SAP S/4HANA Simplification Item Check - How to do it right. post option menu" class="lia-js-menu-opener default-menu-option lia-js-mouseover-menu lia-link-navigation" href="#" id="dropDownLink" role="button" title="Show option menu">
                       Options
                      </a>
                      <div class="dropdown-positioning">
                       <div class="dropdown-positioning-static">
                        <ul aria-label="Dropdown menu items" class="lia-menu-dropdown-items" id="dropdownmenuitems" role="list">
                         <li role="listitem">
                          <a class="lia-link-navigation rss-thread-link lia-component-rss-action-thread" href="/khhcw49343/rss/message?board.id=erp-blog-sap&amp;message.id=26183" id="rssThread" rel="nofollow noopener noreferrer">
                           Subscribe to RSS Feed
                          </a>
                         </li>
                         <li aria-hidden="true">
                          <span class="lia-separator lia-component-common-widget-link-separator">
                           <span class="lia-separator-post">
                           </span>
                           <span class="lia-separator-pre">
                           </span>
                          </span>
                         </li>
                         <li role="listitem">
                          <span aria-disabled="true" class="lia-link-navigation mark-thread-unread lia-link-disabled lia-component-forums-action-mark-thread-unread" id="markThreadUnread">
                           Mark as New
                          </span>
                         </li>
                         <li role="listitem">
                          <span aria-disabled="true" class="lia-link-navigation mark-thread-read lia-link-disabled lia-component-forums-action-mark-thread-read" id="markThreadRead">
                           Mark as Read
                          </span>
                         </li>
                         <li aria-hidden="true">
                          <span class="lia-separator lia-component-common-widget-link-separator">
                           <span class="lia-separator-post">
                           </span>
                           <span class="lia-separator-pre">
                           </span>
                          </span>
                         </li>
                         <li role="listitem">
                          <span aria-disabled="true" class="lia-link-navigation addThreadUserBookmark lia-link-disabled lia-component-subscriptions-action-add-thread-user-bookmark" id="addThreadUserBookmark">
                           Bookmark
                          </span>
                         </li>
                         <li role="listitem">
                          <span aria-disabled="true" class="lia-link-navigation addThreadUserEmailSubscription lia-link-disabled lia-component-subscriptions-action-add-thread-user-email" id="addThreadUserEmailSubscription">
                           Subscribe
                          </span>
                         </li>
                         <li aria-hidden="true">
                          <span class="lia-separator lia-component-common-widget-link-separator">
                           <span class="lia-separator-post">
                           </span>
                           <span class="lia-separator-pre">
                           </span>
                          </span>
                         </li>
                         <li role="listitem">
                          <a class="lia-link-navigation print-article lia-component-forums-action-print-thread" href="/t5/blogs/blogarticleprintpage/blog-id/erp-blog-sap/article-id/26183" id="printThread" rel="nofollow">
                           Printer Friendly Page
                          </a>
                         </li>
                         <li role="listitem">
                          <a class="lia-link-navigation report-abuse-link lia-component-forums-action-report-abuse" href="/t5/notifications/notifymoderatorpage/message-uid/13386669" id="reportAbuse" rel="nofollow">
                           Report Inappropriate Content
                          </a>
                         </li>
                        </ul>
                       </div>
                      </div>
                     </div>
                    </div>
                   </div>
                   <div class="post-info">
                    <div class="lia-message-post-date lia-component-post-date lia-component-message-view-widget-post-date" title="Posted on">
                     <span class="DateTime">
                      <span class="local-date">
                       ‎03-26-2018
                      </span>
                      <span class="local-time">
                       2:28 PM
                      </span>
                     </span>
                    </div>
                   </div>
                  </div>
                 </div>
                </div>
                <div class="lia-quilt-row lia-quilt-row-message-main">
                 <div class="lia-quilt-column lia-quilt-column-24 lia-quilt-column-single lia-quilt-column-message-main-content">
                  <div class="lia-quilt-column-alley lia-quilt-column-alley-single">
                   <div class="KudosButton lia-button-image-kudos-wrapper lia-component-kudos-widget-button-version-3 lia-component-kudos-widget-button-horizontal lia-component-kudos-widget-button lia-component-kudos-action lia-component-message-view-widget-kudos-action" data-lia-kudos-id="13386669" id="kudosButtonV2">
                    <div class="lia-button-image-kudos lia-button-image-kudos-horizontal lia-button-image-kudos-enabled lia-button-image-kudos-not-kudoed lia-button-image-kudos-has-kudoes lia-button-image-kudos-has-kudos lia-button">
                     <div class="lia-button-image-kudos-count">
                      <a class="lia-link-navigation kudos-count-link" href="/t5/kudos/messagepage/board-id/erp-blog-sap/message-id/26183/tab/all-users" id="link_9" title="Click here to see who gave kudos to this post.">
                       <span class="MessageKudosCount lia-component-kudos-widget-message-kudos-count" id="messageKudosCount_3dfd1fd4c7348" itemprop="upvoteCount">
                        94
                       </span>
                       <span class="lia-button-image-kudos-label lia-component-kudos-widget-kudos-count-label">
                        Kudos
                       </span>
                      </a>
                     </div>
                     <div class="lia-button-image-kudos-give">
                      <a aria-label="Click here to give kudos to this post." class="lia-link-navigation kudos-link lia-link-ticket-post-action" data-lia-action-token="8YBi-jbDTu6oSw7XkreZB8OnGeXp6rwVasaCAJWo1bg." data-lia-kudos-entity-uid="13386669" href="https://community.sap.com/t5/blogs/v2/blogarticlepage.kudosbuttonv2.kudoentity:kudoentity/kudosable-gid/13386669?t:ac=blog-id/erp-blog-sap/article-id/26183&amp;t:cp=kudos/contributions/tapletcontributionspage" id="kudoEntity" onclick="return LITHIUM.EarlyEventCapture(this, 'click', true)" rel="nofollow" role="button" title="Click here to give kudos to this post.">
                      </a>
                     </div>
                    </div>
                   </div>
                   <div class="lia-message-body-wrapper lia-component-message-view-widget-body">
                    <div class="lia-message-body" id="bodyDisplay" itemprop="text">
                     <div class="lia-message-body-content">
                      <div style="color: #000000;background: #eeeeee;margin: 10px;padding: 5px;border-style: solid;border-color: #000000;border-width: 1px">
                       <p>
                        This is not a one-shot blog post, but will be regularly updated with the most recent information concerning SAP S/4HANA Simplification Item Checks.
                       </p>
                       <br/>
                       <table>
                        <br/>
                        <tbody>
                         <tr>
                          <br/>
                          <th>
                           Date
                          </th>
                          <br/>
                          <th>
                           Change
                          </th>
                          <br/>
                         </tr>
                         <br/>
                         <tr>
                          <br/>
                          <td>
                           May 30th 2022
                          </td>
                          <br/>
                          <td>
                           <br/>
                           - Added information on SAP S/4HANA 2021 FPS 1 + 2.
                           <br/>
                          </td>
                          <br/>
                         </tr>
                         <br/>
                         <tr>
                          <br/>
                          <td>
                           October 21st 2021
                          </td>
                          <br/>
                          <td>
                           <br/>
                           - Added an explanation on runtime differences between RUN_S4H_SIF_CHECK_INIT and RUN_S4H_SIF_CHECK_EXEC.
                           <br/>
                           - Added a more detailed explanation on the status icons from the consistency check.
                           <br/>
                          </td>
                          <br/>
                         </tr>
                         <br/>
                         <tr>
                          <br/>
                          <td>
                           October 13th 2021
                          </td>
                          <br/>
                          <td>
                           <br/>
                           - Updated release information. Added information on SAP S/4HANA 2021.
                           <br/>
                          </td>
                          <br/>
                         </tr>
                         <br/>
                         <tr>
                          <br/>
                          <td>
                           April 15th 2021
                          </td>
                          <br/>
                          <td>
                           <br/>
                           - Added details on the SAP Readiness Check usecases vs. Simplification Item Check usecases.
                           <br/>
                          </td>
                          <br/>
                         </tr>
                         <br/>
                         <tr>
                          <br/>
                          <td>
                           March 30th 2021
                          </td>
                          <br/>
                          <td>
                           <br/>
                           - Information on additional BP/CVI related checks added.
                           <br/>
                          </td>
                          <br/>
                         </tr>
                         <br/>
                         <tr>
                          <br/>
                          <td>
                           March 3rd 2021
                          </td>
                          <br/>
                          <td>
                           <br/>
                           - Added information on SAP S/4HANA 2020 FPS1.
                           <br/>
                          </td>
                          <br/>
                         </tr>
                         <br/>
                        </tbody>
                       </table>
                       <br/>
                      </div>
                      <br/>
                      <br/>
                      While a system conversion to SAP S/4HANA or a release upgrade within SAP S/4HANA is technically similar to a release upgrade within SAP ERP, there are some steps and tools which are new and specific to SAP S/4HANA system conversions or release upgrades. One of these SAP S/4HANA specific tools is the so called “Simplification Item Check” which is available as of target release SAP S/4HANA 1709.
                      <br/>
                      <br/>
                      The Simplification Item Check consists of a report /SDF/RC_START_CHECK and a related check framework which you shall run
                      <br/>
                      <ul>
                       <li>
                        on your SAP ERP system as preparation for a system conversion to SAP S/4HANA.
                       </li>
                       <br/>
                       <li>
                        on your SAP S/4HANA system as preparation for a release upgrade to a higher SAP S/4HANA release.
                       </li>
                      </ul>
                      <br/>
                      Though the Simplification Item Check is not required or applicable when doing a Support Package or Feature Package update within a SAP S/4HANA release.
                      <br/>
                      <br/>
                      The Simplification Item Check serves two purposes:
                      <br/>
                      <ul>
                       <br/>
                       <li>
                        <strong>
                         Relevance check
                        </strong>
                        : Determine which Simplification Items are relevant for the specific system in which you are running the Simplification Item Check. This shall help you to assess the functional and technical impact of the system conversion on your system.
                       </li>
                       <br/>
                       <li>
                        <strong>
                         Consistency check
                        </strong>
                        : During the conversion process your system will be migrated to the new data structures and new processes. The conversion routines rely on consistent data in the system in order for this to happen automatically. If the Simplification Item Check identifies data inconsistencies or missing mandatory preparation activities which could cause the system conversion to fail, it will make you aware of these issues so you can correct or exempt them before the actual system conversion starts.
                       </li>
                       <br/>
                      </ul>
                      <br/>
                      <img class="migrated-image" src="/legacyfs/online/storage/blog_attachments/2018/03/rel_vs_cons.png" width="500"/>
                      <br/>
                      <br/>
                      The Simplification Item Check report /SDF/RC_START_CHECK supersedes the report R_S4_PRE_TRANSITION_CHECKS which was available in SAP S/4HANA 1511 and 1610 with a similar, though much more limited functionality.
                      <br/>
                      <br/>
                      For a quick comparison between Readiness Check and Simplification Item Check please see the following table.
                      <br/>
                      <br/>
                      <table>
                       <br/>
                       <tbody>
                        <tr style="color: #000000;background: #eeeeee">
                         <br/>
                         <th>
                         </th>
                         <br/>
                         <th>
                          SAP Readiness Check for SAP S/4HANA
                         </th>
                         <br/>
                         <th>
                          Simplification Item Check
                         </th>
                         <br/>
                        </tr>
                        <br/>
                        <tr>
                         <br/>
                         <td style="color: #000000;background: #eeeeee">
                          Implementation
                         </td>
                         <br/>
                         <td>
                          Cloud based customer self service provided by SAP Digital Business Services in the SAP Support Portal.
                         </td>
                         <br/>
                         <td>
                          ABAP Report in the customer system.
                         </td>
                         <br/>
                        </tr>
                        <br/>
                        <tr>
                         <br/>
                         <td style="color: #000000;background: #eeeeee">
                          Included Checks
                         </td>
                         <br/>
                         <td>
                          <br/>
                          - Simplification Item Relevance
                          <br/>
                          - Simplification Item Consistency
                          <b>
                           NEW as of Readiness Check 2.0
                          </b>
                          <br/>
                          - Custom code
                          <br/>
                          - Recommended Fiori apps
                          <br/>
                          - Add-on compatibility
                          <br/>
                          - SAP Custom Development projects
                          <br/>
                          - SAP S/4HANA sizing
                          <br/>
                          - Business Function compatibility
                          <br/>
                         </td>
                         <br/>
                         <td>
                          <br/>
                          - Simplification Item Relevance
                          <br/>
                          - Simplification Item Consistency
                          <br/>
                         </td>
                         <br/>
                        </tr>
                        <br/>
                        <tr>
                         <br/>
                         <td style="color: #000000;background: #eeeeee">
                          Called by SUM
                         </td>
                         <br/>
                         <td>
                          no
                         </td>
                         <br/>
                         <td>
                          yes
                         </td>
                         <br/>
                        </tr>
                        <br/>
                        <tr>
                         <br/>
                         <td style="color: #000000;background: #eeeeee">
                          Mandatory
                         </td>
                         <br/>
                         <td>
                          no, but highly recommended
                         </td>
                         <br/>
                         <td>
                          yes
                         </td>
                         <br/>
                        </tr>
                        <br/>
                        <tr>
                         <br/>
                         <td style="color: #000000;background: #eeeeee">
                          Granularity
                         </td>
                         <br/>
                         <td>
                          High level
                         </td>
                         <br/>
                         <td>
                          Detailed
                         </td>
                         <br/>
                        </tr>
                        <br/>
                        <tr>
                         <br/>
                         <td style="color: #000000;background: #eeeeee">
                          Use cases
                         </td>
                         <br/>
                         <td>
                          <br/>
                          - Conversion to SAP S/4HANA
                          <br/>
                         </td>
                         <br/>
                         <td>
                          <br/>
                          - Conversion to SAP S/4HANA
                          <br/>
                          - Upgrade to a higher SAP S/4HANA release
                          <br/>
                         </td>
                         <br/>
                        </tr>
                        <br/>
                        <tr>
                         <br/>
                         <td style="color: #000000;background: #eeeeee">
                          Available as of target release
                         </td>
                         <br/>
                         <td>
                          SAP S/4HANA, on-premise edition 1511 or higher
                         </td>
                         <br/>
                         <td>
                          SAP S/4HANA 1709 or higher
                         </td>
                         <br/>
                        </tr>
                        <br/>
                       </tbody>
                      </table>
                      <br/>
                      <br/>
                      While both, SAP Readiness Check and Simplification Item Check are able to display information on Simplification Item relevance and consistency, they serve different purposes and are both required.
                      <br/>
                      <ul>
                       <br/>
                       <li>
                        SAP Readiness Check is mainly a planning tool which you should run already early in a system conversion project in order to get an overview on the required activities – including Simplification Item related tasks. It is also useful for tracking these activities during the project e.g. via the burndown chart of Simplification Item consistency errors found.
                       </li>
                       <br/>
                       <li>
                        The Simplification Item Check on the other hand is used on operational level during cleanup of the Simplification Item consistency errors, up to the downtime, before which the SUM tool is doing the final executions of the Simplification Item Check. Hence the Simplification Item Check offers some more fine granular control over running the checks. This includes the option to re-run checks for individual Simplification Items instead of running all checks like SAP Readiness Check does, which can significantly reduce the check runtime, in case you just want to re-check if a specific error which you have just fixed is really gone.
                        <br/>
                        In addition the Simplification Item Check offers the option to exempt certain type of errors (details see below).
                       </li>
                       <br/>
                      </ul>
                      <br/>
                      <br/>
                      <br/>
                      For additional details on how the Simplification Item Check relates to the Simplification Item Catalog and the SAP Readiness Check for SAP S/4HANA please refer to the blog post
                      <a href="https://blogs.sap.com/2020/01/02/simplification-item-catalog-simplification-item-check-and-sap-readiness-check-for-sap-s-4hana/" rel="noopener noreferrer" target="_blank">
                       Simplification Item Catalog, Simplification Item Check and SAP Readiness Check for SAP S/4HANA
                      </a>
                      .
                      <br/>
                      <br/>
                      In order to understand, what is in scope of the Simplification Item Check and what is out of scope, it's also important to understand what the purpose of a Simplification Item is. A Simplification Item describes a major, potentially incompatible change between SAP ERP and SAP S/4HANA or between different SAP S/4HANA releases, which might require customers to do some preparation or follow on activities in a system conversion to SAP S/4HANA or in a SAP S/4HANA upgrade.
                      <br/>
                      <br/>
                      Therefore the Simplification Item Check is not the right tool if you are looking for new features and innovations in SAP S/4HANA. Please refer to
                      <a href="https://go.support.sap.com/innovationdiscovery/" rel="noopener noreferrer" target="_blank">
                       SAP Innovation Discovery
                      </a>
                      and the
                      <a href="https://help.sap.com/viewer/product/SAP_S4HANA_ON-PREMISE/2020/en-US?task=whats_new_task" rel="noopener noreferrer" target="_blank">
                       What's New Viewer
                      </a>
                      for this purpose.
                      <br/>
                      <br/>
                      Also custom code checks - even for changes of potentially incompatible nature - are not in the scope of the Simplification Item Check. For this purpose there is the
                      <a href="https://blogs.sap.com/2016/12/12/remote-code-analysis-in-atc-one-central-check-system-for-multiple-systems-on-various-releases/" rel="noopener noreferrer" target="_blank">
                       ABAP Test Cockpit (ATC)
                      </a>
                      as a dedicated custom code check tool.
                      <br/>
                      <br/>
                      In this blog post I will focus on the consistency check part of the Simplification Item Check. How to successfully prepare and run the consistency checks and how to avoid common mistakes in execution.
                      <br/>
                      <br/>
                      <h1 id="toc-hId-778600075">
                       Implementing the Simplification Item Check
                      </h1>
                      <br/>
                      <br/>
                      On the source system of the system conversion the Simplification Item Check can be implemented and run on any SAP ERP system, no matter which Enhancement Package, down to SAP ERP 6.0 (ECC600). And on the source system for a release upgrade on any SAP S/4HANA system starting with SAP S/4HANA 1511.
                      <br/>
                      Though if the Support Package Stack installed on your system is too old, this might lead to issues with the the Simplification Item Checks, as it cannot be guaranteed that all prerequisites of the Simplification Item Checks are fulfilled. Hence it's recommended to have a rather recent Support Package Stack in the source system.
                      <br/>
                      (Please note that in general
                      <a href="https://support.sap.com/en/my-support/software-downloads/support-package-stacks.html" rel="noopener noreferrer" target="_blank">
                       SAP recommends the application of support package stacks at least once a year
                      </a>
                      .)
                      <br/>
                      <br/>
                      While for the system conversion itself three main technical prerequisites are, that the system needs to be on SAP ERP 6.0 or higher, on unicode and single-stack (only ABAP, no Java stack), for the Readiness Check and Simplification Item Check it's sufficient if the system is on SAP ERP 6.0. Even if your system is still non-Unicode or dual-stack, you can already run Readiness Check and Simplification Item Check. Just keep in mind to later on do the dual-stack split and Unicode conversion before the actual system conversion to SAP S/4HANA.
                      <br/>
                      <br/>
                      How to implement the Simplification Item Check is described in SAP notes 2399707 and 2502552. In order to minimize the number of notes customers have to implement for the Simplification Item Check, the individual, application specific check classes are not delivered as individual SAP notes. Instead they are delivered as a new type of note/correction called “Transport Based Correction Instruction” (TCI).
                      <br/>
                      <br/>
                      In order to be able to implement a TCI, customers on older support package levels have to do a one-time enablement for TCIs.
                      <strong>
                       The easiest way to enable a system for TCI implementation is to follow the automated, guided steps in SAP note
                       <a href="https://launchpad.support.sap.com/#/notes/2836302" rel="noopener noreferrer" target="_blank">
                        2836302
                       </a>
                      </strong>
                      (this note is unrelated to the Simplification Item Checks, but it also includes the TCI enablement and due to support infrastructure changes anyway all customer have to implement this note until January 1st 2020). Alternatively it's still possible to do the TCI enablement by following the guide in note 2187425, which was the previous recommendation. Though this involves more manual steps and effort.
                      <br/>
                      <br/>
                      If your system is already on SAP S/4HANA 1709 (SAP_BASIS 7.52) or higher, the TCI functionality is fully available out of the box and you don't need to do a TCI enablement.
                      <br/>
                      <br/>
                      Though irrespective of how you did do the TCI enablement, or if your system was already on a TCI enabled support package level, you need to follow the steps in SAP note 2502552 from step 5 onward, to actually implement the most recent TCI.
                      <br/>
                      <br/>
                      The Simplification Item Check is exclusively intended to be implemented and run on your SAP ERP / SAP S/4HANA backend system. There is no need or possibility to run it on any other system that might be connected to your SAP ERP / SAP S/4HANA backend (e.g. SAP Fiori Frontend Server, SAP Portal, SAP PI...).
                      <br/>
                      <br/>
                      <h2 id="toc-hId-711169289">
                       Common Issues and Solutions for Implementing the Simplification Item Check
                      </h2>
                      <br/>
                      <br/>
                      <br/>
                      <h3 id="toc-hId-643738503">
                       Issues related to the framework note 2399707
                      </h3>
                      <br/>
                      <br/>
                      <ul>
                       <br/>
                       <br/>
                       <li style="color: #FF0000">
                        For common issues and solutions related to implementation of SAP Note 2399707 please refer to
                        <a href="https://blogs.sap.com/2019/11/10/sap-note-simplification-item-check-implementation-problem/" rel="noopener noreferrer">
                         this separate blog post
                        </a>
                        by the Simplification Item Check framework development team.
                       </li>
                       <br/>
                       <br/>
                      </ul>
                      <br/>
                      <br/>
                      <h3 id="toc-hId-447224998">
                       Issues related to TCI implementation / note 2502552
                      </h3>
                      <br/>
                      <br/>
                      <ul>
                       <br/>
                       <li>
                        A common mistake is to do the TCI enablement not correctly - as described in SAP notes 2502552 and 2187425 - or not at all and just implement these notes like regular SAP notes. This will lead to further issues when trying to implement the Simplification Item Check.
                       </li>
                       <br/>
                       <br/>
                       <li>
                        In case you have changed this setting, restore the SPAM setting for "ABAP/Dynpro Generation" to it's default value of "Never",
                        <strong>
                         before
                        </strong>
                        importing the TCI. You can do this in transaction SPAM =&gt; menu "Extras" =&gt; "Settings". The reason for this is, that there are additional SAP notes containing corrections for objects in the TCI (as listed in SAP note 2502552).
                        <br/>
                        If you have ABAP/Dynpro Generation activated in SPAM you might run into syntax errors while installing the TCI, which can only be fixed by the additional SAP notes with the TCI corrections on top. Though these notes cannot be installed before the TCI itself is installed, resulting in a deadlock situation.
                       </li>
                       <br/>
                       <br/>
                       <li>
                        When implementing a TCI, SPAM will create a backup transport of the objects within the TCI. This requires that STMS is properly setup in the system, so creating and releasing of workbench requests is possible. Hence, please ensure a proper STMS setup before implementing the TCI, otherwise TCI implementation will fail.
                       </li>
                       <br/>
                       <br/>
                       <li>
                        Also ensure to implement the latest corrections for the SNOTE tool (SAP note 1668882)
                        <strong>
                         before
                        </strong>
                        implementing SAP notes 2399707 and 2502552.
                       </li>
                       <br/>
                       <br/>
                       <li>
                        In case you experience issues when transporting the implemented TCIs to follow-on systems (error message: "Object ... requires a directory entry"), have a look at and implement SAP note 2569813.
                       </li>
                       <br/>
                       <br/>
                       <li>
                        If you run into errors like "Delivery event [...] is not in your system" or "Unable to find delivery event [...]" during TCI implementation, implement SAP note 2671774 and try again.
                       </li>
                       <br/>
                      </ul>
                      <br/>
                      <br/>
                      <h3 id="toc-hId-250711493">
                       Issues related to individual check classes / Simplification Items
                      </h3>
                      <br/>
                      <br/>
                      <br/>
                      <h4 id="toc-hId-183280707">
                       CLS4SIC_MM_IM_SI1 / SI1: Logistics_MM-IM
                      </h4>
                      <br/>
                      <br/>
                      <ul>
                       <br/>
                       <br/>
                       <li>
                        In case you experience longs runtimes or performance issues with check class CLS4SIC_MM_IM_SI1 please refer to the "Runtime of MM-IM checks" section further down in this blog post.
                       </li>
                       <br/>
                       <br/>
                       <li>
                        If you experience a CX_SY_OPEN_SQL_DB short dump in check class CLS4SIC_MM_IM_SI1 and increasing the memory does not fix the issues, please open a support incident on support component MM-IM-GF-MIG and request access to pilot note 2761731.
                       </li>
                       <br/>
                       <br/>
                       <li>
                        If you experience a CX_SY_DYNAMIC_OSQL_SEMANTICS short dump in check class CLS4SIC_MM_IM_SI1, please refer to note 2894923.
                       </li>
                       <br/>
                       <br/>
                      </ul>
                      <br/>
                      <br/>
                      <h1 id="toc-hId--400480955">
                       Simplification Item Check in the freeze period of your project
                      </h1>
                      <br/>
                      The Simplification Item Check technically consists of three parts
                      <br/>
                      <ul>
                       <br/>
                       <li>
                        The check framework (SAP note 2399707)
                       </li>
                       <br/>
                       <li>
                        Application specific check classes (SAP note 2502552)
                       </li>
                       <br/>
                       <li>
                        The check content from the
                        <a href="https://launchpad.support.sap.com/#/sic/overview" rel="noopener noreferrer">
                         Simplification Item Catalog
                        </a>
                        . The check framework will download the content automatically from SAP servers.
                       </li>
                       <br/>
                      </ul>
                      <br/>
                      SAP will regularly deliver corrections and improvements on the check framework, check classes and check content. E.g. in order to reduce false positives in the checks, to add new features or to support new Simplification Items. Though it’s important not to negatively affect already ongoing SAP S/4HANA projects by changing or introducing new checks.
                      <br/>
                      <br/>
                      Therefore each SAP S/4HANA release or feature package/support package has a specific minimum version of SAP notes 2399707 and 2502552 which need to be installed in order to do the Simplification Item Check and the system conversion. This minimum SAP note versions are defined upon general availability of each SAP S/4HANA release or feature package/support package and will afterwards not be changed anymore.
                      <br/>
                      <br/>
                      Please refer to SAP note 2399707 for the list of minimum note versions for each SAP S/4HANA release and feature package to which a system conversion is supported. (System conversions to a given SAP S/4HANA release are supported until availability of Feature Package 2 of the next but one release, so around 2.5 years.)
                      <br/>
                      <br/>
                      Corrections of these notes will still be released. However, to allow for a smooth conversion or upgrade the following update strategy is recommended for these two notes.
                      <br/>
                      <br/>
                      While you are still in an early phase of your project don't stay on the minimum note versions, but always use the most recent versions of SAP notes 2399707 and 2502552 as well as the most recent version of the check content.
                      <br/>
                      <br/>
                      Though
                      <strong>
                       after the final conversion of your DEV system, it absolutely crucial to freeze the versions of these notes as well as the version of the check content
                      </strong>
                      on the level which was used for the final DEV conversion.
                      <br/>
                      <br/>
                      For your follow on systems like QAS and PRD use the transport of the final note implementations 2399707 and 2502552 done in your DEV system.
                      <br/>
                      <br/>
                      In order to
                      <strong>
                       save the check content used for your DEV conversion
                      </strong>
                      , download it via the “Download Local Simplification Item Catalog” button in report /SDF/RC_START_CHECK.
                      <strong>
                       Download it before your start the conversion of the DEV system
                      </strong>
                      . It’s general good practice for every system conversion you do, to download the check content before the conversion and keep track of which system was converted with which version of the content.
                      <br/>
                      <img class="migrated-image" src="/legacyfs/online/storage/blog_attachments/2018/03/download_content.png" width="500"/>
                      <br/>
                      <br/>
                      The downloaded content from your last DEV conversion you can then upload with /SDF/RC_START_CHECK for conversion of all your follow on systems like QAS and PRD. In order to do this, on the start screen of /SDF/RC_START_CHECK switch to “Local version uploaded manually” and upload the content via button “Upload Simplification Item Catalog”.
                      <br/>
                      <img class="migrated-image" src="/legacyfs/online/storage/blog_attachments/2018/03/upload_content.png" width="500"/>
                      <br/>
                      <h1 id="toc-hId--596994460">
                       When to run the Simplification Item Check
                      </h1>
                      <br/>
                      There are two modes in which you can run the consistency checks of the the Simplification Item Check.
                      <br/>
                      <h2 id="toc-hId--664425246">
                       Manually running report /SDF/RC_START_CHECK
                      </h2>
                      <br/>
                      You can do this any time in your SAP ERP system when planning a system conversion to SAP S/4HANA (or in your SAP S/4HANA system when planning a release upgrade).
                      <strong>
                       Run the checks as early as possible in your project
                      </strong>
                      in order to get an overview early in advance, which inconsistencies you need to fix before the system conversion/upgrade. You can also run the checks long before the actual conversion project - long before starting SUM.
                      <br/>
                      <br/>
                      When manually running the checks, ensure that you select the correct target stack for your system conversion / upgrade. Per default the /SDF/RC_START_CHECK report always has the most recent target stack pre-selected. As the check results are target stack dependent, when running the check against the wrong target stack you might miss some important Simplification Items and issues. Or see additional ones which are actually not relevant for your target stack.
                      <br/>
                      <br/>
                      When starting report /SDF/RC_START_CHECK manually, only the relevance checks are run automatically in order to determine which Simplification Items are probably relevant and which are not. In order to reduce the runtime of the report in cases where it's not required to run all consistency checks, the consistency checks will not be started automatically. From the result screen of /SDF/RC_START_CHECK you can then start the consistency checks for all relevant Simplification Items with the "Check Consistency for all" button.
                      <br/>
                      <br/>
                      Depending on the number of inconsistencies found, fixing them might take some time or might even need a pre-project for data-cleanup. Also even when you have initially fixed all errors found, re-run the checks during the duration of your project in order to ensure that no new inconsistencies come up.
                      <br/>
                      <br/>
                      Though experience from SAP S/4HANA projects shows, that most of the inconsistencies found by the checks did not arise recently, but are of historic nature (e.g. inconsistent archiving, failed data conversions, wrong configurations, incomplete data maintenance, error in application logic… which did occur many years ago). Once you have initially cleanup up all issues found by the checks, it’s therefore not expected that many new issues will be found during the duration of your project, but you should check this.
                      <br/>
                      <br/>
                      Be careful when doing further archiving during your project and ensure that the archiving is done consistently. One category of inconsistencies which need to be fixed is caused by wrong archiving, breaking foreign key relations. E.g. archiving a material for which still material documents exists. If archiving is done improperly during your project, you could easily introduce new inconsistencies even after the initial cleanup.
                      <br/>
                      <h2 id="toc-hId--860938751">
                       Software Update Manager calling /SDF/RC_START_CHECK
                      </h2>
                      <br/>
                      The SUM tool will run the relevance and consistency checks before starting the actual system conversion/upgrade and before going into the downtime (phases RUN_S4H_SIF_CHECK_INIT of the "extraction" step and RUN_S4H_SIF_CHECK_EXEC of the "preprocessing" step). This is the last time the checks are run and if you did properly fix all issues before, here no new issues should come up. This check execution is mandatory and cannot be skipped.
                      <br/>
                      <br/>
                      When run via the SUM tool, the report will automatically get the correct target stack from SUM, based on the stack.xml file you did upload to SUM.
                      <br/>
                      <br/>
                      <h1 id="toc-hId--416794892">
                       Where to run the Simplification Item Check
                      </h1>
                      <br/>
                      <br/>
                      The SUM tool will run the Simplification Item Check individually in every system that is being converted to SAP S/4HANA, irrespective of the system type (SBX, DEV, QAS, PRD...). Hence as preparation of each of these system conversions, you should also run the Simplification Item Check manually in every system.
                      <br/>
                      <br/>
                      The SUM tool is always running the Simplification Item Check in client 000 of a system. Only running the checks in client 000 will ensure, that data in all clients is checked for inconsistencies. If you run the check in any other client, some of the checks will only return check results for this specific client.
                      <br/>
                      <br/>
                      Therefore it’s crucial that, when manually running the checks during the preparation phase of your project, that you
                      <strong>
                       run the Simplification Item Check in client 000
                      </strong>
                      .
                      <br/>
                      <br/>
                      <h2 id="toc-hId--906711404">
                       Differences in check results
                      </h2>
                      <br/>
                      <br/>
                      When running the Simplification Item Check in different systems of the same landscape or even repeatedly in the same system, the check results can differ. The most common (expected) reasons for this are:
                      <br/>
                      <br/>
                      <ul>
                       <br/>
                       <li>
                        The DEV, QAS, PRD systems in a customer landscape are usually not 100% identical (e.g. different data in the systems, different transactions being used, different business functions being active...). Therefore you will most likely also get different check results and issues to be solved for each system.
                       </li>
                       <br/>
                       <li>
                        When re-running the Simplification Item Checks in the same system but with more recent versions of the check framework and content (Simplification Item Catalog), you will most likely get additional results as new checks might have been added in the meantime or existing ones have been improved.
                       </li>
                       <br/>
                       <li>
                        When doing a system copy of an existing system (e.g. production to sandbox), the usage (ST03N) data will we not be copied and adapted to the new system. This is per design how ST03N stores it's data. Hence it often happens, that in a sandbox system copied from production many of those simplification items, which rely on ST03N data in their relevance check, don't show up as relevant anymore. While they did show up as relevant in the production system. One solution for this is the special report in SAP note 2568736 which copies ST03N data between systems.
                       </li>
                       <br/>
                       <li>
                        When running the Simplification Item Check for the first time for a given target stack, there will only be a relevance check result but no consistency check result yet. So don't forget to press the "Check Consistency for all" button, before comparing this to the check result from some other system or target stack.
                       </li>
                       <br/>
                       <li>
                        And last but not least, ongoing operation of a system might lead to new errors. So if you did fix all errors of a certain type some weeks or months before go-live, this does not mean that no new errors might show up. This is the reason why it's recommended to run the Simplification Item Checks repeatedly per system during the course of your project.
                       </li>
                       <br/>
                      </ul>
                      <br/>
                      <br/>
                      <h1 id="toc-hId--809821902">
                       Simplification Item Check runtime
                      </h1>
                      <br/>
                      Most of the consistency checks verify customizing settings and run quite fast. However, a few of the consistency checks also proof transaction data. The runtime of such checks depends mostly on the amount of data in the system. Specifically in the areas of inventory management (MM-IM) and material ledger (CO-PC-ACT) some customers have rather large amounts of historic transactional data which could result in longer check runtimes.
                      <br/>
                      <br/>
                      Therefore it’s recommended to
                      <strong>
                       do a strict archiving
                      </strong>
                      , especially in the application areas mentioned above, before starting your system conversion project and before running the Simplification Item Check. This will not only help to reduce the runtime of the checks, but also to reduce the effort for cleanup up of possible inconsistencies.
                      <br/>
                      <br/>
                      So you should consider, whether you really need the last 15 years of operational data in the system, requiring cleanup of inconsistencies in this data. Or if it is sufficient to keep the last 2-3 years of data and archive the rest.
                      <br/>
                      <br/>
                      <h2 id="toc-hId--1299738414">
                       Runtime of MM-IM checks
                      </h2>
                      <br/>
                      <br/>
                      For customers with huge amounts of data in the material master related tables (MARD, MARC, MBEW, MKPF, MSEG...) the related consistency checks ( CLS4SIC_MM_IM_SI1 / SI1: Logistics_MM-IM ) will contribute significantly to the overall check runtime. The runtime of the MM-IM checks can in such cases be several days. Most of this time is spent checking KALNR (cost estimate number) data. As this is not strictly required in order to do a technically successful system conversion and as many customers don't need the KALNR data for reporting purposes, please check SAP note 2753888, in case you experience unacceptably long runtimes of the MM-IM checks. With this note you can skip the KALNR checks - at the cost of probably not having full KALNR data later on.
                      <br/>
                      <br/>
                      <h2 id="toc-hId--1496251919">
                       Deleting unused clients
                      </h2>
                      <br/>
                      <br/>
                      Another way of reducing the check runtime (and of course the effort for fixing and cleaning up inconsistencies) is to delete clients which are no longer required. This applies to obsolete SAP standard clients like 001 or 066 as well as to customer specific clients which are no longer used. Here it's important to properly delete the clients. An often made mistake is, to just delete the T000 entry of a client, but not the actual data of the client. This way the client will only be hidden but not deleted. The Simplification Item Checks as well as the data migration logic later on will still run on such hidden clients! Which also means that you are required to fix any data inconsistencies in these clients, whether they are hidden or not.
                      <br/>
                      <br/>
                      The same holds true for other partitions of data in the system like company codes, sales organizations etc. Even if you are not actively using data (anymore), it still has to be consistent in order to be migrated into the SAP S/4HANA data structures. Hence most check classes analyze all related data in the system and don't offer the option to exclude certain data in advance from the checks.
                      <br/>
                      <br/>
                      Please refer to SAP note
                      <a href="https://launchpad.support.sap.com/#/notes/1749142" rel="noopener noreferrer">
                       1749142
                      </a>
                      for how to properly delete a client.
                      <br/>
                      <br/>
                      <h1 id="toc-hId--1399362417">
                       Handling of the check results
                      </h1>
                      <br/>
                      <br/>
                      Before running the consistency checks for the first time for a given stack, the "Last consistency check result" column in the result overview of /SDF/RC_START_CHECK will show grey icons for all Simplification Items.
                      <br/>
                      After running all consistency checks the icons in this column will
                      <br/>
                      <ul>
                       <br/>
                       <li>
                        Stay grey for all Simplification Items which don't need and hence don't have a consistency check. This is the majority of all Simplification Items
                       </li>
                       <br/>
                       <li>
                        Turn into a red RC=12 icon, if the check class of the corresponding Simplification Items has not been implemented yet and hence the check cannot run. In the log you will find details, which SAP note needs to be implemented in order to implement the check class.
                       </li>
                       <br/>
                       <li>
                        Turn into the icon of the most critical status the check class did return.
                       </li>
                       <br/>
                      </ul>
                      <br/>
                      <br/>
                      Details on the check results can be found in the log file created by the checks ("Display Consistency Check Log" button in /SDF/RC_START_CHECK).
                      <br/>
                      <br/>
                      The following three categories of log entries are important:
                      <br/>
                      <ul>
                       <br/>
                       <li>
                        Yellow warning messages (return code 4)
                       </li>
                       <br/>
                       <ul>
                        <br/>
                        <li>
                         These you should be aware of and read the corresponding SAP note, but they are
                         <strong>
                          no show stoppers
                         </strong>
                         for the system conversion/upgrade. Therefore no mandatory action is required to solve these before the conversion. Though there might be activities required after the conversion, or activities where you can decide whether you do them before or after the conversion.
                        </li>
                        <br/>
                       </ul>
                       <br/>
                       <br/>
                       <li>
                        Red error messages (return code 7):
                        <br/>
                        <ul>
                         <br/>
                         <li>
                          Messages with return code 7 will block the system conversion. So you need to take some action to solve them.
                          <strong>
                           One possibility to address return code 7 errors is, to set an exemption for the erro
                          </strong>
                          r. This can be done in the report /SDF/RC_START_CHECK
                          <strong>
                           in client 000
                          </strong>
                          . In the ALV with the overview of all Simplification Items see the column “Exemption Possible” to see items which are exemptable or which have already been exempted.
                         </li>
                         <br/>
                         <li>
                          Exemptable error before setting the exemption:
                          <br/>
                          <img class="migrated-image" src="/legacyfs/online/storage/blog_attachments/2018/03/exemption_before.png" width="500"/>
                         </li>
                         <br/>
                         <li>
                          Exemptable error after setting the exemption:
                          <br/>
                          <img class="migrated-image" src="/legacyfs/online/storage/blog_attachments/2018/03/exemption_after.png" width="500"/>
                         </li>
                         <br/>
                         <li>
                          Before exempting a return code 7 error please be sure to understand the ramifications this has. The details are described in the corresponding, application specific SAP note in the log of the Simplification Item Check. While return code 7 errors will not cause the system conversion to fail, they can have a serious impact on the system, which you accept by exempting the error.
                         </li>
                         <br/>
                         <li>
                          Please note that setting exemptions is target stack specific. So if you have exempted a return code 7 error for a given target stack (e.g. SAP S/4HANA 1709) and decide during the duration of your project to change to a higher target stack (e.g. SAP S/4HANA 1809) the previously exempted return code 7 errors will come up again. and you need to exempt them again, if appropriate. this is intentional, as the ramification of exempting an error be different, depending on the target stack of your system conversion or upgrade.
                         </li>
                         <br/>
                        </ul>
                        <br/>
                       </li>
                       <br/>
                       <li>
                        Red error messages (return code 8 or 12):
                        <br/>
                        <ul>
                         <br/>
                         <li>
                          Messages with return code 8 or 12 will block the system conversion.
                          <strong>
                           It’s mandatory that you fix these return code 8 or 12 errors before starting a system conversion
                          </strong>
                          . How exactly the errors can be solved is highly application specific and is described in the corresponding, application specific SAP note in the log of the Simplification Item Check or in the business impact note of the corresponding Simplification Item. For many of these errors there are further, application specific tools for checking the issue in more details or for fixing the error. In case you need further information or have doubts regarding a specific error in the logs of the Simplification Item Check, open an incident on the support component to which the corresponding SAP note (business impact note) is assigned.
                          <br/>
                          <img class="migrated-image" src="/legacyfs/online/storage/blog_attachments/2018/03/check_errors.png" width="500"/>
                          <br/>
                         </li>
                         <br/>
                        </ul>
                        <br/>
                       </li>
                       <br/>
                      </ul>
                      <br/>
                      <br/>
                      <h2 id="toc-hId--1889278929">
                       Detailing and updating the check results
                      </h2>
                      <br/>
                      The consistency checks can be run in two modes:
                      <br/>
                      <ul>
                       <br/>
                       <li>
                        A quick mode, which checks if there are any critical issues for a given Simplification Item. This is sufficient for determining whether there are any blockers for the system conversion / upgrade. But it will not give you a detailed list of issues per Simplification Item. In this mode most check classes only search for the first 1 - 100 issues and then stop analysis in order to reduce runtime for the overall check execution.
                        <br/>
                        <br/>
                        This mode is used when running the consistency checks for all Simplification Items, either manually or via SUM.
                        <br/>
                        The main advantage of this mode is, that it can have an overall shorter run-time than the detailed mode explained next.
                        <br/>
                       </li>
                       <br/>
                       <li>
                        <br/>
                        A detailed mode, which is doing a more in-depth analysis for individual Simplification Items. This will give you a list of all issues found per Simplification Item as well as more details on the type of issue and how to solve them. If and to what extent the level of detail differs between the quick mode and the detailed mode varies between Simplification Items. This mode is useful when building a work-list for solving all consistency issues related to a specific Simplification Item. This mode can have a longer run-time compared to the quick mode.
                        <br/>
                        <br/>
                        You can trigger a detailed check for one or more Simplification Items by selecting the Simplification Items you are interested in in the ALV result view of /SDF/RC_START_CHECK and then pressing the "Check Consistency Details" button.
                        <br/>
                        <img class="migrated-image" src="/legacyfs/online/storage/blog_attachments/2018/03/check_details.png" width="500"/>
                        <br/>
                       </li>
                       <br/>
                      </ul>
                      <br/>
                      <br/>
                      After fixing a specific error, this will not immediately reflect in the consistency check results shown by /SDF/RC_START_CHECK. In order to see the updated check results, re-run "Check Consistency Details" for the specific Simplification Item where you did fix the inconsistencies.
                      <br/>
                      <br/>
                      When manually running the consistency checks from within the result view of /SDF/RC_START_CHECK, they will currently run in dialog mode. Alternatively you can also use report /SDF/RC_TROUBLE_SHOOT for (re)running individual checks in background.
                      <br/>
                      <br/>
                      If you have set the timeout value for dialog processes (
                      <em>
                       rdisp/scheduler/prio_[high|normal|low]/max_runtime
                      </em>
                      or
                      <em>
                       rdisp/max_wprun_time
                      </em>
                      ) to a very low value or have a high data volume in your system which might lead to longer run-times of this specific check, you need to increase the timeout value in dialog mode.
                      <br/>
                      <br/>
                      Up to and including version 88 of SAP note 2399707, even when starting the checks in background, under certain circumstances follow on processes could be spawned running in dialog work processes. Thereby making the timeout mentioned above also applicable in this case. This was improved with version 89 of SAP note 2399707, where all related processes are run in background work processes when starting the checks in background.
                      <br/>
                      <br/>
                      When repeatedly running the consistency checks, the results are written continuously to log file /usr/sap/SID/SUM/abap/tmp/S4_SIF_TRANSITION_CHECKS.SID.
                      <br/>
                      <br/>
                      So the results from more than one check execution might be included in this log file. Therefore it's important to always look at the latest check execution in this log, in order to see which errors are actually left and which need to be fixed before the conversion. You can search for "BEGIN OF RUN_S4H_SIF_CHECK" in the log file in order to identify the start of a check execution.
                      <br/>
                      <br/>
                      <h2 id="toc-hId--2085792434">
                       Different SUM behavior in different phases of check execution
                      </h2>
                      <br/>
                      <br/>
                      While the check framework knows the return codes 0, 4, 7, 8 ,12, as explained above, the SUM tool only knows the return codes 0 (information), 4 (warning) and 8 (error = SUM will stop). Therefore a mapping between these return codes takes place which is different, depending on whether the framework is called by SUM in phase RUN_S4H_SIF_CHECK_INIT or by SUM in phase RUN_S4H_SIF_CHECK_EXEC.
                      <br/>
                      <br/>
                      During first SUM check execution (RUN_S4H_SIF_CHECK_INIT):
                      <br/>
                      <table>
                       <br/>
                       <tbody>
                        <tr style="color: #000000;background: #ffffff">
                         <br/>
                         <th>
                          Actual return code
                         </th>
                         <br/>
                         <th>
                          Return code displayed in the check report and in application log
                         </th>
                         <br/>
                         <th>
                          Return code delivered to SUM and shown in SUM log file*
                         </th>
                         <br/>
                        </tr>
                        <br/>
                        <tr>
                         <br/>
                         <td>
                          0
                         </td>
                         <br/>
                         <td>
                          0
                         </td>
                         <br/>
                         <td>
                          0
                         </td>
                         <br/>
                        </tr>
                        <br/>
                        <tr>
                         <br/>
                         <td>
                          4
                         </td>
                         <br/>
                         <td>
                          4
                         </td>
                         <br/>
                         <td>
                          4
                         </td>
                         <br/>
                        </tr>
                        <br/>
                        <tr>
                         <br/>
                         <td>
                          7
                         </td>
                         <br/>
                         <td>
                          7 (or 4 if it has been exempted)
                         </td>
                         <br/>
                         <td>
                          4 in any case, whether it’s exempted or not
                         </td>
                         <br/>
                        </tr>
                        <br/>
                        <tr>
                         <br/>
                         <td>
                          8
                         </td>
                         <br/>
                         <td>
                          8
                         </td>
                         <br/>
                         <td>
                          4
                         </td>
                         <br/>
                        </tr>
                        <br/>
                        <tr>
                         <br/>
                         <td>
                          12
                         </td>
                         <br/>
                         <td>
                          12
                         </td>
                         <br/>
                         <td style="color: #FF0000;background: #ffffff">
                          8 (= SUM stops)
                         </td>
                         <br/>
                        </tr>
                        <br/>
                       </tbody>
                      </table>
                      <br/>
                      <br/>
                      During second SUM check execution (RUN_S4H_SIF_CHECK_EXEC):
                      <br/>
                      <table>
                       <br/>
                       <tbody>
                        <tr style="color: #000000;background: #ffffff">
                         <br/>
                         <th>
                          Actual return code
                         </th>
                         <br/>
                         <th>
                          Return code displayed in the check report and in application log
                         </th>
                         <br/>
                         <th>
                          Return code delivered to SUM and shown in SUM log file*
                         </th>
                         <br/>
                        </tr>
                        <br/>
                        <tr>
                         <br/>
                         <td>
                          0
                         </td>
                         <br/>
                         <td>
                          0
                         </td>
                         <br/>
                         <td>
                          0
                         </td>
                         <br/>
                        </tr>
                        <br/>
                        <tr>
                         <br/>
                         <td>
                          4
                         </td>
                         <br/>
                         <td>
                          4
                         </td>
                         <br/>
                         <td>
                          4
                         </td>
                         <br/>
                        </tr>
                        <br/>
                        <tr>
                         <br/>
                         <td>
                          7
                         </td>
                         <br/>
                         <td>
                          7 (or 4 if it has been exempted)
                         </td>
                         <br/>
                         <td>
                          4 if exempted or
                          <div style="color: #FF0000;background: #ffffff">
                           8 (=SUM stops) if not exempted
                          </div>
                         </td>
                         <br/>
                        </tr>
                        <br/>
                        <tr>
                         <br/>
                         <td>
                          8
                         </td>
                         <br/>
                         <td>
                          8
                         </td>
                         <br/>
                         <td style="color: #FF0000;background: #ffffff">
                          8 (= SUM stops)
                         </td>
                         <br/>
                        </tr>
                        <br/>
                        <tr>
                         <br/>
                         <td>
                          12
                         </td>
                         <br/>
                         <td>
                          12
                         </td>
                         <br/>
                         <td style="color: #FF0000;background: #ffffff">
                          8 (= SUM stops)
                         </td>
                         <br/>
                        </tr>
                        <br/>
                       </tbody>
                      </table>
                      <br/>
                      <br/>
                      Please pay special attention to the two cases marked in bold above, where the SUM does not stop in the first phase (RUN_S4H_SIF_CHECK_INIT) for yet unexempted (RC=7) errors and less critical (RC=8) errors. But SUM will stop for these errors in the second phase (RUN_S4H_SIF_CHECK_EXEC)! This is in order not to block SUM and therefore the whole conversion already in such an early phase, which can happen many days or even weeks before the start of downtime and where there is still plenty of time to exempt of fix these issues. In the first phase (RUN_S4H_SIF_CHECK_INIT) SUM will only stop for the most critical errors (RC=12).
                      <br/>
                      <br/>
                      Therefore please always check in the application log of the check runs or the check report itself, if there are any remaining RC=8 to be fixed or RC=7 to be exempted. And don't just assume that if the check report did not stop SUM in the RUN_S4H_SIF_CHECK_INIT phase, that this will also be the case in the RUN_S4H_SIF_CHECK_EXEC phase.
                      <br/>
                      <br/>
                      (* For better transparency in one of the next updates of the framework note this will be changed, so that the message text in the SUM log will also display the real return code from the check framework - so column 2 in the tables above - which is already shown in the application log and the check report, instead of the return code passed to SUM.)
                      <br/>
                      <br/>
                      Please be aware that RUN_S4H_SIF_CHECK_INIT and RUN_S4H_SIF_CHECK_EXEC can have different runtimes due to the way the "quick check" mode (see previous paragraph) works, which both phases are using. Example:
                      <br/>
                      <ul>
                       <br/>
                       <li>
                        A check class is scanning a table and the scan is quite expensive in terms of runtime (e.g. full table scan).
                       </li>
                       <br/>
                       <li>
                        The check class is implemented in a way, that in "quick mode" it only looks for the first 100 data inconsistencies found in the data.
                       </li>
                       <br/>
                       <li>
                        In a given customer system this table has 1.000.000.000 records and at the time RUN_S4H_SIF_CHECK_INIT is executed the first 100 data inconsistencies are located within the first 1.000.000 records of the table.
                       </li>
                       <br/>
                       <li>
                        After RUN_S4H_SIF_CHECK_INIT the customer is manually running the detailed check and fixing all data inconsistencies.
                       </li>
                       <br/>
                       <li>
                        At the time RUN_S4H_SIF_CHECK_EXEC is executed, no data inconsistenciesare left in the table.
                       </li>
                       <br/>
                      </ul>
                      <br/>
                      In this example the check class is only checking the first 1.000.000 records during RUN_S4H_SIF_CHECK_INIT but needs to check all 1.000.000.000 records during RUN_S4H_SIF_CHECK_EXEC in order to verify that no new errors have come up, which surely does have an impact on the runtime of the check.
                      <br/>
                      <br/>
                      Though how RUN_S4H_SIF_CHECK_INIT and RUN_S4H_SIF_CHECK_EXEC runtime differ in a specific customer system depends on many factors. Including the number of records and data inconsistencies in the scanned tables, how these data inconsistencies are distributed across the data, delta mechanisms and optimizations used by individual check classes etc.
                      <br/>
                      <br/>
                      Hence for better predictability of runtimes it's recommended to clean up most errors already in the (repeated) manual check runs before RUN_S4H_SIF_CHECK_INIT. Then RUN_S4H_SIF_CHECK_INIT and RUN_S4H_SIF_CHECK_EXEC runtimes will be quite similar.
                      <br/>
                      <br/>
                      <h1 id="toc-hId--1988902932">
                       Checks not included in the Simplification Item Check
                      </h1>
                      <br/>
                      Any new consistency checks related to SAP S/4HANA conversions or upgrades are build directly in the Simplification Item Check framework. Also most previously existing standalone consistency checks have been moved into the Simplification Item Check framework.
                      <br/>
                      <br/>
                      Though some consistency checks in FIN area are not yet included in the Simplification Item Check framework.
                      <br/>
                      <br/>
                      <ul>
                       <br/>
                       <li>
                        FIN configuration consistency checks
                        <br/>
                        <ul>
                         <br/>
                         <li>
                          GL: Are included in the Simplification Item Checks as of SAP S/4HANA 1709. See SAP note 2245333 for details.
                         </li>
                         <br/>
                         <li>
                          AA: Are included in the Simplification Item Checks as of SAP S/4HANA 1809. In SAP S/4HANA releases &lt; 1809 the FI-AA configuration consistency checks are handled via the separate report RASFIN_MIGR_PRECHECK. As described in SAP note 2333236. See also the guide attached to SAP note 2332030 for details.
                         </li>
                         <br/>
                        </ul>
                        <br/>
                       </li>
                       <br/>
                       <li>
                        FIN transactional data consistency checks
                        <br/>
                        <ul>
                         <br/>
                         <li>
                          GL: Are still handled outside of the Simplification Item Checks.
                         </li>
                         <br/>
                         <li>
                          AA: Are still handled outside of the Simplification Item Checks. These checks can already run on the source ERP system on anyDB. Please refer to SAP note 2755360 for details.
                         </li>
                         <br/>
                        </ul>
                        <br/>
                       </li>
                       <br/>
                       <li>
                        These FIN consistency checks for configuration and transactional data are only relevant if you are doing a conversion from SAP ERP to SAP S/4HANA. They are not relevant for release upgrades within SAP S/4HANA (e.g. upgrade from 1709 to 1909). Because once a system is on SAP S/4HANA it's already using the new FI data model.
                       </li>
                       <br/>
                       <li>
                        For more details on the Finance consistency checks please refer to the blog series
                        <a href="https://blogs.sap.com/2018/08/13/conversion-to-sap-s4hana-consistency-checks-in-finance-part-1/" rel="noopener noreferrer" target="_blank">
                         Conversion to SAP S/4HANA - Consistency checks in Finance
                        </a>
                        from Martin Schmidt.
                       </li>
                       <br/>
                      </ul>
                      <br/>
                      <br/>
                      Also in the area of Business Partner / Customer Vendor Integration additional checks exist, which go beyond the scope of the Simplification Item checks. For further information please see
                      <br/>
                      <ul>
                       <br/>
                       <li>
                        <font color="red">
                         NEW:
                        </font>
                        <a href="https://blogs.sap.com/2021/03/29/new-customer-vendor-integration-analysis-cvia-check-in-sap-readiness-check-is-now-live/" rel="noopener noreferrer" target="_blank">
                         Customer Vendor Integration Analysis (CVIA) check in SAP Readiness Check
                        </a>
                       </li>
                       <br/>
                       <li>
                        <a href="https://support.sap.com/content/dam/SAAP/SAP_Activate/S4H.0781%20SAP%20S4HANA%20Cookbook%20Customer%20Vendor%20Integration.pdf" rel="noopener noreferrer" target="_blank">
                         CVI Cookbook
                        </a>
                       </li>
                       <br/>
                      </ul>
                      <br/>
                      <br/>
                      <br/>
                      Don't forget to run these checks in addition to the Simplification Item Check!
                      <br/>
                      <br/>
                      <h1 id="toc-hId-2109550859">
                       Conclusion
                      </h1>
                      <br/>
                      <br/>
                      In order to successfully use the Simplification Item Check as preparation for your system conversion/upgrade project
                      <br/>
                      <ul>
                       <br/>
                       <li>
                        Start running the Simplification Item Check and fixing the issues found by the checks as early as possible in your project.
                       </li>
                       <br/>
                       <li>
                        Stay up-to-date with the latest check and check content versions early in your project. But don't miss to freeze the check notes and check content versions after converting your DEV system.
                       </li>
                       <br/>
                       <li>
                        Archive any data which you don't strictly need in your SAP S/4HANA system in order to minimize the check runtime and the data cleanup effort.
                       </li>
                       <br/>
                       <li>
                        Don't forget to run those Finance specific checks which are not included in the consistency checks of the Simplification Item Check yet.
                       </li>
                       <br/>
                      </ul>
                     </div>
                    </div>
                   </div>
                   <div class="custom-view-associated-products">
                    <ul class="lia-list-standard-inline" id="list_0" role="list">
                     <li aria-level="5" class="custom-labels-title" role="heading">
                      SAP Managed Tags:
                     </li>
                     <li class="lia-link-navigation lia-custom-event">
                      <a href="https://community.sap.com/t5/c-khhcw49343/SAP%2520S%252F4HANA/pd-p/73554900100800000266">
                       SAP S/4HANA
                      </a>
                      ,
                     </li>
                     <li class="lia-link-navigation lia-custom-event">
                      <a href="https://community.sap.com/t5/c-khhcw49343/SAP%2520Readiness%2520Check/pd-p/1647bf27-5e10-4efd-89e1-a59efaf4e250">
                       SAP Readiness Check
                      </a>
                     </li>
                    </ul>
                   </div>
                   <div class="LabelsForArticle lia-component-labels lia-component-message-view-widget-labels-with-event" id="labelsWithEvent">
                    <span aria-level="5" class="article-labels-title" role="heading">
                     Labels:
                    </span>
                    <div class="LabelsList">
                     <ul class="lia-list-standard-inline" id="list_0" role="list">
                      <li class="label">
                       <a class="label-link lia-link-navigation lia-custom-event" href="/t5/enterprise-resource-planning-blogs-by-sap/bg-p/erp-blog-sap/label-name/technology%20updates" id="link_10" rel="tag">
                        Technology Updates
                        <wbr/>
                       </a>
                      </li>
                     </ul>
                    </div>
                   </div>
                   <div class="TagList lia-message-tags lia-component-message-view-widget-tags" id="taglist">
                    <ul aria-label="User Tags" class="lia-list-standard-inline" id="list_1" role="list">
                     <li class="tag-123718 lia-tag-list-item">
                      <a class="lia-link-navigation lia-tag tag tag-123718 lia-js-data-tagUid-123718" href="/t5/tag/S4HANA%20System%20Conversion/tg-p/board-id/erp-blog-sap" id="link_11" rel="tag">
                       S4HANA System Conversion
                      </a>
                      <div class="tag-list-js-confirmation hidden">
                      </div>
                     </li>
                     <li class="tag-131652 lia-tag-list-item">
                      <a class="lia-link-navigation lia-tag tag tag-131652 lia-js-data-tagUid-131652" href="/t5/tag/SAP%20S4HANA%20Release%20Upgrade/tg-p/board-id/erp-blog-sap" id="link_12" rel="tag">
                       SAP S4HANA Release Upgrade
                      </a>
                      <div class="tag-list-js-confirmation hidden">
                      </div>
                     </li>
                     <li class="tag-77554 lia-tag-list-item">
                      <a class="lia-link-navigation lia-tag tag tag-77554 lia-js-data-tagUid-77554" href="/t5/tag/simplification%20item%20check/tg-p/board-id/erp-blog-sap" id="link_13" rel="tag">
                       simplification item check
                      </a>
                      <div class="tag-list-js-confirmation hidden">
                      </div>
                     </li>
                    </ul>
                    <div class="lia-inline-ajax-feedback">
                     <div class="AjaxFeedback" id="ajaxFeedback">
                     </div>
                    </div>
                   </div>
                  </div>
                 </div>
                </div>
                <div class="lia-quilt-row lia-quilt-row-message-footer">
                 <div class="lia-quilt-column lia-quilt-column-08 lia-quilt-column-left lia-quilt-column-message-footer-left lia-mark-empty">
                 </div>
                 <div class="lia-quilt-column lia-quilt-column-16 lia-quilt-column-right lia-quilt-column-message-footer-right">
                  <div class="lia-quilt-column-alley lia-quilt-column-alley-right">
                   <div class="footer-top">
                    <div class="lia-button-group lia-component-comment-button lia-component-message-view-widget-comment-button">
                    </div>
                   </div>
                  </div>
                 </div>
                </div>
                <div class="lia-quilt-row lia-quilt-row-message-moderation">
                 <div class="lia-quilt-column lia-quilt-column-24 lia-quilt-column-single lia-quilt-column-message-moderation-content">
                  <div class="lia-quilt-column-alley lia-quilt-column-alley-single">
                   <div class="footer-bottom lia-mark-empty">
                   </div>
                  </div>
                 </div>
                </div>
               </div>
              </div>
             </div>
             <div class="lia-progress lia-js-hidden" id="progressBar">
              <div class="lia-progress-indeterminate">
              </div>
             </div>
            </div>
            <div class="lia-text lia-blog-article-page-comment-count lia-discussion-page-sub-section-header lia-component-comment-count-conditional">
             51 Comments
            </div>
            <a name="comments">
            </a>
            <div class="lia-component-blogs-page-comment-list-loader lia-component-lazy-loader lia-lazy-load lia-component-comment-list" id="lazyload">
            </div>
            <div aria-label="Use the previous and next links to move between pages. Use the page number links to go directly to a page." class="lia-paging-full-wrapper lia-paging-pager lia-paging-full-left-position lia-discussion-page-message-pager lia-component-comment-pager" id="pager" role="navigation">
             <div class="lia-inline-ajax-feedback">
              <div class="AjaxFeedback" id="ajaxFeedback_0">
              </div>
             </div>
             <ul class="lia-paging-full lia-paging-full-left">
              <li class="lia-paging-page-previous lia-component-previous">
               <span aria-disabled="true" class="lia-link-navigation lia-js-data-pageNum-1 lia-link-disabled" id="link_14">
                <span>
                 <span aria-hidden="true" class="lia-paging-page-arrow">
                  «
                 </span>
                 <span class="lia-paging-page-link">
                  Previous
                 </span>
                </span>
               </span>
              </li>
              <li class="lia-component-pagesnumbered">
               <ul class="lia-paging-full-pages">
                <li class="lia-paging-page-first lia-js-data-pageNum-1">
                 <span aria-current="page" aria-disabled="true" class="lia-js-data-pageNum-1 lia-link-navigation lia-link-disabled" id="link_15">
                  1
                 </span>
                </li>
                <li class="lia-paging-page-last lia-js-data-pageNum-2">
                 <a aria-label="Page 2" class="lia-js-data-pageNum-2 lia-link-navigation lia-custom-event" href="https://community.sap.com/t5/enterprise-resource-planning-blogs-by-sap/sap-s-4hana-simplification-item-check-how-to-do-it-right/ba-p/13386669/page/2#comments" id="link_16">
                  2
                 </a>
                </li>
               </ul>
              </li>
              <li class="lia-paging-page-next lia-component-next">
               <a aria-label="Next Page" class="lia-link-navigation lia-js-data-pageNum-2 lia-custom-event" href="https://community.sap.com/t5/enterprise-resource-planning-blogs-by-sap/sap-s-4hana-simplification-item-check-how-to-do-it-right/ba-p/13386669/page/2#comments" id="link_17" rel="next">
                <span class="lia-paging-page-link">
                 Next
                </span>
                <span aria-hidden="true" class="lia-paging-page-arrow">
                 »
                </span>
               </a>
              </li>
             </ul>
            </div>
            <a name="comment-on-this">
            </a>
            <span id="feedback-successinformationbox_8">
            </span>
            <div class="InfoMessage lia-panel-feedback-banner-note lia-component-comment-editor" id="informationbox_8">
             <div class="lia-text" role="alert">
              <p ng-non-bindable="" tabindex="0">
               You must be a registered user to add a comment. If you've already registered, sign in. Otherwise, register and sign in.
              </p>
              <ul class="lia-list-standard" id="list_2" role="list">
               <li>
                <a class="lia-link-navigation blog-link lia-message-comment-post" href="/plugins/common/feature/oidcss/sso_login_redirect/providerid/sap_ids?redirectreason=permissiondenied&amp;referer=https%3A%2F%2Fcommunity.sap.com%2Ft5%2Fenterprise-resource-planning-blogs-by-sap%2Fsap-s-4hana-simplification-item-check-how-to-do-it-right%2Fba-p%2F13386669%23comment-on-this" id="link_18" rel="nofollow">
                 Comment
                </a>
               </li>
              </ul>
             </div>
            </div>
           </div>
          </div>
          <div class="lia-quilt-column lia-quilt-column-08 lia-quilt-column-right lia-quilt-column-side-content">
           <div class="lia-quilt-column-alley lia-quilt-column-alley-right">
            <div class="lia-panel lia-panel-standard LabelsTaplet Chrome lia-component-labels-widget-labels-list">
             <div class="lia-decoration-border">
              <div class="lia-decoration-border-top">
               <div>
               </div>
              </div>
              <div class="lia-decoration-border-content">
               <div>
                <div class="lia-panel-heading-bar-wrapper">
                 <div class="lia-panel-heading-bar">
                  <span aria-level="3" class="lia-panel-heading-bar-title" role="heading">
                   Labels in this area
                  </span>
                 </div>
                </div>
                <div class="lia-panel-content-wrapper">
                 <div class="lia-panel-content">
                  <div id="labelsTaplet">
                   <div class="BlogLabelsTaplet">
                    <div class="LabelsList" id="list_3">
                     <ul class="lia-list-standard" id="list_4" role="list">
                      <li class="label even-row">
                       <a aria-label="Business Trends" class="label-link lia-link-navigation" href="/t5/enterprise-resource-planning-blogs-by-sap/bg-p/erp-blog-sap/label-name/business%20trends" id="link_19">
                        Business Trends
                        <wbr/>
                       </a>
                       <span class="label-count">
                        363
                       </span>
                      </li>
                      <li class="label odd-row">
                       <a aria-label="Business Trends​" class="label-link lia-link-navigation" href="/t5/enterprise-resource-planning-blogs-by-sap/bg-p/erp-blog-sap/label-name/business%20trends%E2%80%8B" id="link_20">
                        Business Trends​
                        <wbr/>
                       </a>
                       <span class="label-count">
                        1
                       </span>
                      </li>
                      <li class="label even-row">
                       <a aria-label="Event Information" class="label-link lia-link-navigation" href="/t5/enterprise-resource-planning-blogs-by-sap/bg-p/erp-blog-sap/label-name/event%20information" id="link_21">
                        Event Information
                        <wbr/>
                       </a>
                       <span class="label-count">
                        462
                       </span>
                      </li>
                      <li class="label odd-row">
                       <a aria-label="Event Information​" class="label-link lia-link-navigation" href="/t5/enterprise-resource-planning-blogs-by-sap/bg-p/erp-blog-sap/label-name/event%20information%E2%80%8B" id="link_22">
                        Event Information​
                        <wbr/>
                       </a>
                       <span class="label-count">
                        9
                       </span>
                      </li>
                      <li class="label even-row">
                       <a aria-label="Expert Insights" class="label-link lia-link-navigation" href="/t5/enterprise-resource-planning-blogs-by-sap/bg-p/erp-blog-sap/label-name/expert%20insights" id="link_23">
                        Expert Insights
                        <wbr/>
                       </a>
                       <span class="label-count">
                        114
                       </span>
                      </li>
                      <li class="label odd-row">
                       <a aria-label="Expert Insights​" class="label-link lia-link-navigation" href="/t5/enterprise-resource-planning-blogs-by-sap/bg-p/erp-blog-sap/label-name/expert%20insights%E2%80%8B" id="link_24">
                        Expert Insights​
                        <wbr/>
                       </a>
                       <span class="label-count">
                        38
                       </span>
                      </li>
                      <li class="label even-row">
                       <a aria-label="Life at SAP" class="label-link lia-link-navigation" href="/t5/enterprise-resource-planning-blogs-by-sap/bg-p/erp-blog-sap/label-name/life%20at%20sap" id="link_25">
                        Life at SAP
                        <wbr/>
                       </a>
                       <span class="label-count">
                        420
                       </span>
                      </li>
                      <li class="label odd-row">
                       <a aria-label="Life at SAP​" class="label-link lia-link-navigation" href="/t5/enterprise-resource-planning-blogs-by-sap/bg-p/erp-blog-sap/label-name/life%20at%20sap%E2%80%8B" id="link_26">
                        Life at SAP​
                        <wbr/>
                       </a>
                       <span class="label-count">
                        1
                       </span>
                      </li>
                      <li class="label even-row">
                       <a aria-label="Product Updates" class="label-link lia-link-navigation" href="/t5/enterprise-resource-planning-blogs-by-sap/bg-p/erp-blog-sap/label-name/product%20updates" id="link_27">
                        Product Updates
                        <wbr/>
                       </a>
                       <span class="label-count">
                        4,691
                       </span>
                      </li>
                      <li class="label odd-row">
                       <a aria-label="Product Updates​" class="label-link lia-link-navigation" href="/t5/enterprise-resource-planning-blogs-by-sap/bg-p/erp-blog-sap/label-name/product%20updates%E2%80%8B" id="link_28">
                        Product Updates​
                        <wbr/>
                       </a>
                       <span class="label-count">
                        74
                       </span>
                      </li>
                      <li class="label even-row">
                       <a aria-label="Technology Updates" class="label-link lia-link-navigation" href="/t5/enterprise-resource-planning-blogs-by-sap/bg-p/erp-blog-sap/label-name/technology%20updates" id="link_29">
                        Technology Updates
                        <wbr/>
                       </a>
                       <span class="label-count">
                        1,505
                       </span>
                      </li>
                      <li class="label odd-row">
                       <a aria-label="Technology Updates​" class="label-link lia-link-navigation" href="/t5/enterprise-resource-planning-blogs-by-sap/bg-p/erp-blog-sap/label-name/technology%20updates%E2%80%8B" id="link_30">
                        Technology Updates​
                        <wbr/>
                       </a>
                       <span class="label-count">
                        31
                       </span>
                      </li>
                     </ul>
                    </div>
                   </div>
                  </div>
                 </div>
                </div>
               </div>
              </div>
              <div class="lia-decoration-border-bottom">
               <div>
               </div>
              </div>
             </div>
            </div>
            <div class="lia-panel lia-panel-standard custom-related-content">
             <div class="lia-decoration-border">
              <div class="lia-decoration-border-top">
               <div>
               </div>
              </div>
              <div class="lia-decoration-border-content">
               <div>
                <div class="lia-panel-heading-bar-wrapper">
                 <div class="lia-panel-heading-bar">
                  <span class="lia-panel-heading-bar-title">
                   Related Content
                  </span>
                 </div>
                </div>
                <div class="lia-panel-content-wrapper">
                 <div class="lia-panel-content">
                  <ul class="lia-list-standard">
                   <li>
                    <a class="lia-link-navigation" href="/t5/enterprise-resource-planning-q-a/how-to-add-auto-increase-number-custom-field-to-sales-order-header/qaq-p/13597111">
                     How to add auto increase number custom field to Sales Order Header
                    </a>
                    <small>
                     in
                     <a href="/t5/enterprise-resource-planning-q-a/qa-p/erp-questions">
                      Enterprise Resource Planning Q&amp;A
                     </a>
                     <time>
                      3 hours ago
                     </time>
                    </small>
                   </li>
                   <li>
                    <a class="lia-link-navigation" href="/t5/enterprise-resource-planning-blogs-by-sap/manufacturing-in-sap-s-4hana-cloud-public-edition-2402/ba-p/13590769">
                     Manufacturing in SAP S/4HANA Cloud Public Edition 2402
                    </a>
                    <small>
                     in
                     <a href="/t5/enterprise-resource-planning-blogs-by-sap/bg-p/erp-blog-sap">
                      Enterprise Resource Planning Blogs by SAP
                     </a>
                     <time>
                      yesterday
                     </time>
                    </small>
                   </li>
                   <li>
                    <a class="lia-link-navigation" href="/t5/enterprise-resource-planning-blogs-by-sap/discrete-industries-in-sap-s-4hana-cloud-public-edition-2402/ba-p/13585141">
                     Discrete Industries in SAP S/4HANA Cloud Public Edition 2402
                    </a>
                    <small>
                     in
                     <a href="/t5/enterprise-resource-planning-blogs-by-sap/bg-p/erp-blog-sap">
                      Enterprise Resource Planning Blogs by SAP
                     </a>
                     <time>
                      yesterday
                     </time>
                    </small>
                   </li>
                   <li>
                    <a class="lia-link-navigation" href="/t5/enterprise-resource-planning-blogs-by-sap/sap-s-4hana-business-partner-address-management-at-business-user/ba-p/13595157">
                     SAP S/4HANA Business Partner – Address Management at Business User
                    </a>
                    <small>
                     in
                     <a href="/t5/enterprise-resource-planning-blogs-by-sap/bg-p/erp-blog-sap">
                      Enterprise Resource Planning Blogs by SAP
                     </a>
                     <time>
                      yesterday
                     </time>
                    </small>
                   </li>
                   <li>
                    <a class="lia-link-navigation" href="/t5/enterprise-resource-planning-blogs-by-sap/sap-s-4hana-cloud-public-edition-2402-%E4%BA%AE%E7%82%B9%E5%89%8D%E7%9E%BB/ba-p/13594195">
                     SAP S/4HANA Cloud Public Edition 2402 亮点前瞻
                    </a>
                    <small>
                     in
                     <a href="/t5/enterprise-resource-planning-blogs-by-sap/bg-p/erp-blog-sap">
                      Enterprise Resource Planning Blogs by SAP
                     </a>
                     <time>
                      yesterday
                     </time>
                    </small>
                   </li>
                  </ul>
                 </div>
                </div>
               </div>
              </div>
              <div class="lia-decoration-border-bottom">
               <div>
               </div>
              </div>
             </div>
            </div>
            <div class="lia-panel lia-panel-standard custom-popular-blog-articles">
             <div class="lia-decoration-border">
              <div class="lia-decoration-border-top">
               <div>
               </div>
              </div>
              <div class="lia-decoration-border-content">
               <div>
                <div class="lia-panel-heading-bar-wrapper">
                 <div class="lia-panel-heading-bar">
                  <span class="lia-panel-heading-bar-title">
                   Popular Blog Posts
                  </span>
                 </div>
                </div>
                <div class="lia-panel-content-wrapper">
                 <div class="lia-panel-content">
                  <section>
                   <article class="custom-popular-article-tile">
                    <a href="/t5/enterprise-resource-planning-blogs-by-sap/useful-documents-on-scn/ba-p/13101633" title="View article">
                     <img alt="" src="https://community.sap.com/html/assets/img_tile-default.png"/>
                    </a>
                    <h3>
                     <a href="/t5/enterprise-resource-planning-blogs-by-sap/useful-documents-on-scn/ba-p/13101633">
                      Useful documents on SCN
                     </a>
                    </h3>
                    <aside>
                     <a class="UserAvatar lia-link-navigation" href="/t5/user/viewprofilepage/user-id/10611" title="View profile">
                      <img alt="theme-lib.general.user-avatar" class="lia-user-avatar-message" src="https://avatars.profile.sap.com/b/6/idb649706629af2724d98fb26f5d1428fb257cf2d216340afe52cd31da26978bc0_small.jpeg"/>
                     </a>
                     <strong>
                      <span>
                       by
                      </span>
                      <a href="/t5/user/viewprofilepage/user-id/10611" rel="author" title="View profile">
                       <span class="">
                        Nancy
                       </span>
                      </a>
                     </strong>
                     <small>
                      •
                     </small>
                     <em>
                      Product and Topic Expert
                     </em>
                    </aside>
                    <footer>
                     <ul class="custom-tile-statistics">
                      <li class="custom-tile-views">
                       <b>
                        132504
                       </b>
                       Views
                      </li>
                      <li class="custom-tile-replies">
                       <b>
                        123
                       </b>
                       comments
                      </li>
                      <li class="custom-tile-kudos">
                       <b>
                        219
                       </b>
                       kudos
                      </li>
                     </ul>
                     <div class="post-time">
                      <time>
                       01-06-2015
                      </time>
                     </div>
                    </footer>
                   </article>
                   <article class="custom-popular-article-tile">
                    <a href="/t5/enterprise-resource-planning-blogs-by-sap/evolution-of-abap/ba-p/13522761" title="View article">
                     <img alt="" src="https://community.sap.com/html/assets/img_tile-default.png"/>
                    </a>
                    <h3>
                     <a href="/t5/enterprise-resource-planning-blogs-by-sap/evolution-of-abap/ba-p/13522761">
                      Evolution of ABAP
                     </a>
                    </h3>
                    <aside>
                     <a class="UserAvatar lia-link-navigation" href="/t5/user/viewprofilepage/user-id/533" title="View profile">
                      <img alt="theme-lib.general.user-avatar" class="lia-user-avatar-message" src="https://avatars.profile.sap.com/e/0/ide03ed0fa93f30abfeac07c402c516ddc1e247fb3fb2774a62c9624e0a47fc785_small.jpeg"/>
                     </a>
                     <strong>
                      <span>
                       by
                      </span>
                      <a href="/t5/user/viewprofilepage/user-id/533" rel="author" title="View profile">
                       <span class="">
                        KARLKESSLER
                       </span>
                      </a>
                     </strong>
                     <small>
                      •
                     </small>
                     <em>
                      Advisor
                     </em>
                    </aside>
                    <footer>
                     <ul class="custom-tile-statistics">
                      <li class="custom-tile-views">
                       <b>
                        24452
                       </b>
                       Views
                      </li>
                      <li class="custom-tile-replies">
                       <b>
                        42
                       </b>
                       comments
                      </li>
                      <li class="custom-tile-kudos">
                       <b>
                        192
                       </b>
                       kudos
                      </li>
                     </ul>
                     <div class="post-time">
                      <time>
                       09-01-2022
                      </time>
                     </div>
                    </footer>
                   </article>
                   <article class="custom-popular-article-tile">
                    <a href="/t5/enterprise-resource-planning-blogs-by-sap/analytics-in-s-4hana-real-shape-of-embedded-analytics-and-beyond-embedded/ba-p/13403536" title="View article">
                     <img alt="" src="https://community.sap.com/html/assets/img_tile-default.png"/>
                    </a>
                    <h3>
                     <a href="/t5/enterprise-resource-planning-blogs-by-sap/analytics-in-s-4hana-real-shape-of-embedded-analytics-and-beyond-embedded/ba-p/13403536">
                      Analytics in S/4HANA - real shape of embedded analytics and beyond embedded analytics
                     </a>
                    </h3>
                    <aside>
                     <a class="UserAvatar lia-link-navigation" href="/t5/user/viewprofilepage/user-id/131707" title="View profile">
                      <img alt="theme-lib.general.user-avatar" class="lia-user-avatar-message" src="https://avatars.profile.sap.com/d/c/iddc47e4382f12bb063df8959eaa11212843bb1f4922da76e911867d0a173536f6_small.jpeg"/>
                     </a>
                     <strong>
                      <span>
                       by
                      </span>
                      <a href="/t5/user/viewprofilepage/user-id/131707" rel="author" title="View profile">
                       <span class="">
                        Masaaki
                       </span>
                      </a>
                     </strong>
                     <small>
                      •
                     </small>
                     <em>
                      Advisor
                     </em>
                    </aside>
                    <footer>
                     <ul class="custom-tile-statistics">
                      <li class="custom-tile-views">
                       <b>
                        96748
                       </b>
                       Views
                      </li>
                      <li class="custom-tile-replies">
                       <b>
                        32
                       </b>
                       comments
                      </li>
                      <li class="custom-tile-kudos">
                       <b>
                        182
                       </b>
                       kudos
                      </li>
                     </ul>
                     <div class="post-time">
                      <time>
                       06-08-2019
                      </time>
                     </div>
                    </footer>
                   </article>
                  </section>
                 </div>
                </div>
               </div>
              </div>
              <div class="lia-decoration-border-bottom">
               <div>
               </div>
              </div>
             </div>
            </div>
            <div class="lia-panel lia-panel-standard KudoedAuthorsLeaderboardTaplet Chrome lia-component-kudos-widget-authors-leaderboard">
             <div class="lia-decoration-border">
              <div class="lia-decoration-border-top">
               <div>
               </div>
              </div>
              <div class="lia-decoration-border-content">
               <div>
                <div class="lia-panel-heading-bar-wrapper">
                 <div class="lia-panel-heading-bar">
                  <span aria-level="3" class="lia-panel-heading-bar-title" role="heading">
                   Top kudoed authors
                  </span>
                 </div>
                </div>
                <div class="lia-panel-content-wrapper">
                 <div class="lia-panel-content">
                  <div class="UserList lia-component-users-widget-user-list">
                   <span id="user-listuserList">
                   </span>
                   <div class="t-data-grid" id="grid">
                    <table class="lia-list-slim" role="presentation">
                     <thead class="lia-table-head" id="columns">
                      <tr>
                       <th class="userAvatarNameColumn lia-data-cell-primary lia-data-cell-text t-first" scope="col">
                        <span aria-disabled="true" class="lia-view-filter lia-link-disabled" id="link_31">
                         User
                        </span>
                       </th>
                       <th class="kudosCountColumn lia-data-cell-tertiary lia-data-cell-integer t-last" scope="col">
                        Count
                       </th>
                      </tr>
                     </thead>
                     <tbody>
                      <tr class="lia-list-row lia-row-odd t-first">
                       <td class="userAvatarNameColumn lia-data-cell-primary lia-data-cell-text">
                        <div class="UserProfileSummary lia-user-item lia-js-data-userId-131558 lia-user-info-group">
                         <div class="lia-message-author-avatar-username">
                          <a class="UserAvatarName lia-link-navigation" href="/t5/user/viewprofilepage/user-id/131558" id="link_32">
                           <div class="UserAvatar lia-user-avatar lia-component-common-widget-user-avatar">
                            <img alt="SDenecken" class="lia-user-avatar-message" id="imagedisplay_0" src="https://avatars.profile.sap.com/0/b/id0bee1c5eb86ded2e6b290e7e400cafce88fd51258967fecdea226f48f1affbf1_small.jpeg" title="SDenecken"/>
                           </div>
                           <div class="lia-user-attributes">
                            <div class="lia-user-name">
                             <span class="UserName lia-user-name lia-user-rank-Employee">
                              <img alt="Employee" class="lia-user-rank-icon lia-user-rank-icon-left" id="display_6" src="/html/@C6690A74F301515E6881523D52BDF6AA/rank_icons/sap-logo-small-14px.png" title="Employee"/>
                              <span class="">
                               SDenecken
                              </span>
                             </span>
                            </div>
                           </div>
                          </a>
                         </div>
                         <div class="lia-user-attributes">
                         </div>
                        </div>
                       </td>
                       <td aria-label="Number of kudos: 2,029" class="kudosCountColumn lia-data-cell-tertiary lia-data-cell-integer">
                        2029
                       </td>
                      </tr>
                      <tr class="lia-list-row lia-row-even">
                       <td class="userAvatarNameColumn lia-data-cell-primary lia-data-cell-text">
                        <div class="UserProfileSummary lia-user-item lia-js-data-userId-121014 lia-user-info-group">
                         <div class="lia-message-author-avatar-username">
                          <a class="UserAvatarName lia-link-navigation" href="/t5/user/viewprofilepage/user-id/121014" id="link_33">
                           <div class="UserAvatar lia-user-avatar lia-component-common-widget-user-avatar">
                            <img alt="former_member121014" class="lia-user-avatar-message" id="imagedisplay_1" src="https://avatars.profile.sap.com/former_member_small.jpeg" title="former_member121014"/>
                           </div>
                           <div class="lia-user-attributes">
                            <div class="lia-user-name">
                             <span class="UserName lia-user-name lia-user-rank-Advisor">
                              <img alt="Advisor" class="lia-user-rank-icon lia-user-rank-icon-left" id="display_7" src="/html/@C6690A74F301515E6881523D52BDF6AA/rank_icons/sap-logo-small-14px.png" title="Advisor"/>
                              <span class="">
                               former_member12
                               <wbr/>
                               1014
                              </span>
                             </span>
                            </div>
                           </div>
                          </a>
                         </div>
                         <div class="lia-user-attributes">
                         </div>
                        </div>
                       </td>
                       <td aria-label="Number of kudos: 1,203" class="kudosCountColumn lia-data-cell-tertiary lia-data-cell-integer">
                        1203
                       </td>
                      </tr>
                      <tr class="lia-list-row lia-row-odd">
                       <td class="userAvatarNameColumn lia-data-cell-primary lia-data-cell-text">
                        <div class="UserProfileSummary lia-user-item lia-js-data-userId-131461 lia-user-info-group">
                         <div class="lia-message-author-avatar-username">
                          <a class="UserAvatarName lia-link-navigation" href="/t5/user/viewprofilepage/user-id/131461" id="link_34">
                           <div class="UserAvatar lia-user-avatar lia-component-common-widget-user-avatar">
                            <img alt="Gerhard_Welker" class="lia-user-avatar-message" id="imagedisplay_2" src="https://avatars.profile.sap.com/3/a/id3ad0f2da55e04fe9ba2e7851cf31b9406e23ca17cf5f0b21225b4be1f67254af_small.jpeg" title="Gerhard_Welker"/>
                           </div>
                           <div class="lia-user-attributes">
                            <div class="lia-user-name">
                             <span class="UserName lia-user-name lia-user-rank-Product-and-Topic-Expert">
                              <img alt="Product and Topic Expert" class="lia-user-rank-icon lia-user-rank-icon-left" id="display_8" src="/html/@C6690A74F301515E6881523D52BDF6AA/rank_icons/sap-logo-small-14px.png" title="Product and Topic Expert"/>
                              <span class="">
                               Gerhard_Welker
                              </span>
                             </span>
                            </div>
                           </div>
                          </a>
                         </div>
                         <div class="lia-user-attributes">
                         </div>
                        </div>
                       </td>
                       <td aria-label="Number of kudos: 1,072" class="kudosCountColumn lia-data-cell-tertiary lia-data-cell-integer">
                        1072
                       </td>
                      </tr>
                      <tr class="lia-list-row lia-row-even">
                       <td class="userAvatarNameColumn lia-data-cell-primary lia-data-cell-text">
                        <div class="UserProfileSummary lia-user-item lia-js-data-userId-755 lia-user-info-group">
                         <div class="lia-message-author-avatar-username">
                          <a class="UserAvatarName lia-link-navigation" href="/t5/user/viewprofilepage/user-id/755" id="link_35">
                           <div class="UserAvatar lia-user-avatar lia-component-common-widget-user-avatar">
                            <img alt="mahesh_sardesai" class="lia-user-avatar-message" id="imagedisplay_3" src="https://avatars.profile.sap.com/8/a/id8add3238dfb86754123fab2374437646e50fe79a842990baf674e3f6f49e0e6e_small.jpeg" title="mahesh_sardesai"/>
                           </div>
                           <div class="lia-user-attributes">
                            <div class="lia-user-name">
                             <span class="UserName lia-user-name lia-user-rank-Advisor">
                              <img alt="Advisor" class="lia-user-rank-icon lia-user-rank-icon-left" id="display_9" src="/html/@C6690A74F301515E6881523D52BDF6AA/rank_icons/sap-logo-small-14px.png" title="Advisor"/>
                              <span class="">
                               mahesh_sardesai
                              </span>
                             </span>
                            </div>
                           </div>
                          </a>
                         </div>
                         <div class="lia-user-attributes">
                         </div>
                        </div>
                       </td>
                       <td aria-label="Number of kudos: 1,055" class="kudosCountColumn lia-data-cell-tertiary lia-data-cell-integer">
                        1055
                       </td>
                      </tr>
                      <tr class="lia-list-row lia-row-odd">
                       <td class="userAvatarNameColumn lia-data-cell-primary lia-data-cell-text">
                        <div class="UserProfileSummary lia-user-item lia-js-data-userId-131707 lia-user-info-group">
                         <div class="lia-message-author-avatar-username">
                          <a class="UserAvatarName lia-link-navigation" href="/t5/user/viewprofilepage/user-id/131707" id="link_36">
                           <div class="UserAvatar lia-user-avatar lia-component-common-widget-user-avatar">
                            <img alt="Masaaki" class="lia-user-avatar-message" id="imagedisplay_4" src="https://avatars.profile.sap.com/d/c/iddc47e4382f12bb063df8959eaa11212843bb1f4922da76e911867d0a173536f6_small.jpeg" title="Masaaki"/>
                           </div>
                           <div class="lia-user-attributes">
                            <div class="lia-user-name">
                             <span class="UserName lia-user-name lia-user-rank-Advisor">
                              <img alt="Advisor" class="lia-user-rank-icon lia-user-rank-icon-left" id="display_10" src="/html/@C6690A74F301515E6881523D52BDF6AA/rank_icons/sap-logo-small-14px.png" title="Advisor"/>
                              <span class="">
                               Masaaki
                              </span>
                             </span>
                            </div>
                           </div>
                          </a>
                         </div>
                         <div class="lia-user-attributes">
                         </div>
                        </div>
                       </td>
                       <td aria-label="Number of kudos: 899" class="kudosCountColumn lia-data-cell-tertiary lia-data-cell-integer">
                        899
                       </td>
                      </tr>
                      <tr class="lia-list-row lia-row-even">
                       <td class="userAvatarNameColumn lia-data-cell-primary lia-data-cell-text">
                        <div class="UserProfileSummary lia-user-item lia-js-data-userId-17414 lia-user-info-group">
                         <div class="lia-message-author-avatar-username">
                          <a class="UserAvatarName lia-link-navigation" href="/t5/user/viewprofilepage/user-id/17414" id="link_37">
                           <div class="UserAvatar lia-user-avatar lia-component-common-widget-user-avatar">
                            <img alt="janmusil" class="lia-user-avatar-message" id="imagedisplay_5" src="https://avatars.profile.sap.com/7/5/id75bacc120bd928ba0e87c1c763270c5b287b548075ed54265fa072ac81d68074_small.jpeg" title="janmusil"/>
                           </div>
                           <div class="lia-user-attributes">
                            <div class="lia-user-name">
                             <span class="UserName lia-user-name lia-user-rank-Product-and-Topic-Expert">
                              <img alt="Product and Topic Expert" class="lia-user-rank-icon lia-user-rank-icon-left" id="display_11" src="/html/@C6690A74F301515E6881523D52BDF6AA/rank_icons/sap-logo-small-14px.png" title="Product and Topic Expert"/>
                              <span class="">
                               janmusil
                              </span>
                             </span>
                            </div>
                           </div>
                          </a>
                         </div>
                         <div class="lia-user-attributes">
                         </div>
                        </div>
                       </td>
                       <td aria-label="Number of kudos: 888" class="kudosCountColumn lia-data-cell-tertiary lia-data-cell-integer">
                        888
                       </td>
                      </tr>
                      <tr class="lia-list-row lia-row-odd">
                       <td class="userAvatarNameColumn lia-data-cell-primary lia-data-cell-text">
                        <div class="UserProfileSummary lia-user-item lia-js-data-userId-6638 lia-user-info-group">
                         <div class="lia-message-author-avatar-username">
                          <a class="UserAvatarName lia-link-navigation" href="/t5/user/viewprofilepage/user-id/6638" id="link_38">
                           <div class="UserAvatar lia-user-avatar lia-component-common-widget-user-avatar">
                            <img alt="OlgaDolinskaja" class="lia-user-avatar-message" id="imagedisplay_6" src="https://avatars.profile.sap.com/3/d/id3de2dde04a1aa64b641424db3bbbc1eb95d2a09d6ce8813af73d131db316e370_small.jpeg" title="OlgaDolinskaja"/>
                           </div>
                           <div class="lia-user-attributes">
                            <div class="lia-user-name">
                             <span class="UserName lia-user-name lia-user-rank-Product-and-Topic-Expert">
                              <img alt="Product and Topic Expert" class="lia-user-rank-icon lia-user-rank-icon-left" id="display_12" src="/html/@C6690A74F301515E6881523D52BDF6AA/rank_icons/sap-logo-small-14px.png" title="Product and Topic Expert"/>
                              <span class="">
                               OlgaDolinskaja
                              </span>
                             </span>
                            </div>
                           </div>
                          </a>
                         </div>
                         <div class="lia-user-attributes">
                         </div>
                        </div>
                       </td>
                       <td aria-label="Number of kudos: 755" class="kudosCountColumn lia-data-cell-tertiary lia-data-cell-integer">
                        755
                       </td>
                      </tr>
                      <tr class="lia-list-row lia-row-even">
                       <td class="userAvatarNameColumn lia-data-cell-primary lia-data-cell-text">
                        <div class="UserProfileSummary lia-user-item lia-js-data-userId-40376 lia-user-info-group">
                         <div class="lia-message-author-avatar-username">
                          <a class="UserAvatarName lia-link-navigation" href="/t5/user/viewprofilepage/user-id/40376" id="link_39">
                           <div class="UserAvatar lia-user-avatar lia-component-common-widget-user-avatar">
                            <img alt="christian_v2" class="lia-user-avatar-message" id="imagedisplay_7" src="https://avatars.profile.sap.com/c/f/idcf5d0531ff6e21c8818384a144f9b5c1859d67ba97b8b86286ee779db9418445_small.jpeg" title="christian_v2"/>
                           </div>
                           <div class="lia-user-attributes">
                            <div class="lia-user-name">
                             <span class="UserName lia-user-name lia-user-rank-Product-and-Topic-Expert">
                              <img alt="Product and Topic Expert" class="lia-user-rank-icon lia-user-rank-icon-left" id="display_13" src="/html/@C6690A74F301515E6881523D52BDF6AA/rank_icons/sap-logo-small-14px.png" title="Product and Topic Expert"/>
                              <span class="">
                               christian_v2
                              </span>
                             </span>
                            </div>
                           </div>
                          </a>
                         </div>
                         <div class="lia-user-attributes">
                         </div>
                        </div>
                       </td>
                       <td aria-label="Number of kudos: 680" class="kudosCountColumn lia-data-cell-tertiary lia-data-cell-integer">
                        680
                       </td>
                      </tr>
                      <tr class="lia-list-row lia-row-odd">
                       <td class="userAvatarNameColumn lia-data-cell-primary lia-data-cell-text">
                        <div class="UserProfileSummary lia-user-item lia-js-data-userId-17006 lia-user-info-group">
                         <div class="lia-message-author-avatar-username">
                          <a class="UserAvatarName lia-link-navigation" href="/t5/user/viewprofilepage/user-id/17006" id="link_40">
                           <div class="UserAvatar lia-user-avatar lia-component-common-widget-user-avatar">
                            <img alt="Amin-Hoque" class="lia-user-avatar-message" id="imagedisplay_8" src="https://avatars.profile.sap.com/a/a/idaa332129eb19aa8ce045f53b67fec68127885409a9d7524aaa2498c2fefaebab_small.jpeg" title="Amin-Hoque"/>
                           </div>
                           <div class="lia-user-attributes">
                            <div class="lia-user-name">
                             <span class="UserName lia-user-name lia-user-rank-Advisor">
                              <img alt="Advisor" class="lia-user-rank-icon lia-user-rank-icon-left" id="display_14" src="/html/@C6690A74F301515E6881523D52BDF6AA/rank_icons/sap-logo-small-14px.png" title="Advisor"/>
                              <span class="">
                               Amin-Hoque
                              </span>
                             </span>
                            </div>
                           </div>
                          </a>
                         </div>
                         <div class="lia-user-attributes">
                         </div>
                        </div>
                       </td>
                       <td aria-label="Number of kudos: 647" class="kudosCountColumn lia-data-cell-tertiary lia-data-cell-integer">
                        647
                       </td>
                      </tr>
                      <tr class="lia-list-row lia-row-even t-last">
                       <td class="userAvatarNameColumn lia-data-cell-primary lia-data-cell-text">
                        <div class="UserProfileSummary lia-user-item lia-js-data-userId-10611 lia-user-info-group">
                         <div class="lia-message-author-avatar-username">
                          <a class="UserAvatarName lia-link-navigation" href="/t5/user/viewprofilepage/user-id/10611" id="link_41">
                           <div class="UserAvatar lia-user-avatar lia-component-common-widget-user-avatar">
                            <img alt="Nancy" class="lia-user-avatar-message" id="imagedisplay_9" src="https://avatars.profile.sap.com/b/6/idb649706629af2724d98fb26f5d1428fb257cf2d216340afe52cd31da26978bc0_small.jpeg" title="Nancy"/>
                           </div>
                           <div class="lia-user-attributes">
                            <div class="lia-user-name">
                             <span class="UserName lia-user-name lia-user-rank-Product-and-Topic-Expert">
                              <img alt="Product and Topic Expert" class="lia-user-rank-icon lia-user-rank-icon-left" id="display_15" src="/html/@C6690A74F301515E6881523D52BDF6AA/rank_icons/sap-logo-small-14px.png" title="Product and Topic Expert"/>
                              <span class="">
                               Nancy
                              </span>
                             </span>
                            </div>
                           </div>
                          </a>
                         </div>
                         <div class="lia-user-attributes">
                         </div>
                        </div>
                       </td>
                       <td aria-label="Number of kudos: 515" class="kudosCountColumn lia-data-cell-tertiary lia-data-cell-integer">
                        515
                       </td>
                      </tr>
                     </tbody>
                    </table>
                   </div>
                  </div>
                  <div class="lia-view-all">
                   <a class="lia-link-navigation view-all-link" href="/t5/forums/kudosleaderboardpage/board-id/erp-blog-sap/timerange/one_month/page/1/tab/authors" id="link_42">
                    View all
                   </a>
                  </div>
                 </div>
                </div>
               </div>
              </div>
              <div class="lia-decoration-border-bottom">
               <div>
               </div>
              </div>
             </div>
            </div>
           </div>
          </div>
         </div>
         <div class="lia-quilt-row lia-quilt-row-footer">
          <div class="lia-quilt-column lia-quilt-column-24 lia-quilt-column-single lia-quilt-column-common-footer">
           <div class="lia-quilt-column-alley lia-quilt-column-alley-single">
            <div class="lia-quilt lia-quilt-footer lia-quilt-layout-custom-community-footer lia-component-quilt-footer">
             <div class="lia-quilt-row lia-quilt-row-footer-top">
              <div class="lia-quilt-column lia-quilt-column-24 lia-quilt-column-single lia-quilt-column-footer-top-content lia-mark-empty">
              </div>
             </div>
             <div class="lia-quilt-row lia-quilt-row-footer-main">
              <div class="lia-quilt-column lia-quilt-column-24 lia-quilt-column-single lia-quilt-column-footer-main-content lia-mark-empty">
              </div>
             </div>
             <div class="lia-quilt-row lia-quilt-row-footer-bottom">
              <div class="lia-quilt-column lia-quilt-column-24 lia-quilt-column-single lia-quilt-column-footer-bottom-content">
               <div class="lia-quilt-column-alley lia-quilt-column-alley-single lia-mark-empty">
               </div>
              </div>
             </div>
             <div class="lia-quilt-row lia-quilt-row-footer-external">
              <div class="lia-quilt-column lia-quilt-column-24 lia-quilt-column-single lia-quilt-column-footer-external-content">
               <div class="lia-quilt-column-alley lia-quilt-column-alley-single">
                <ds-footer is-light-theme="false" is-show-cookie-preferences="true" navigation-items='[
{"url": "https://pages.community.sap.com/resources/sap-community-privacy-statement", "title": "Privacy Statement", "target": "_blank", "label": "Privacy"},
{"url": "https://www.sap.com/corporate/en/legal/terms-of-use.html", "title": "Terms of Use", "target": "_blank", "label": "Terms of Use"},
{"url": "https://www.sap.com/about/legal/copyright.html", "title": "View the Copyright Information", "target": "_blank", "label": "Copyright"},
{"url": "https://www.sap.com/about/legal/impressum.html", "target": "_blank", "label": "Legal Disclosure"},
{"url": "https://www.sap.com/about/legal/trademark.html", "title": "View the Trademark Information", "target": "_blank", "label": "Trademark"},
{"url": "https://www.sap.com/cmp/nl/sap-community-voice/index.html", "title": "View the Community newsletter information", "target": "_blank", "label": "Newsletter"},
{"url": "mailto:<EMAIL>", "title": "Get Community support", "target": "_blank", "label": "Support"}
]' social-links='[{"tooltip":"Visit the Services and Support from SAP Facebook page","target":"_blank","rel":"noopener noreferrer","name":"facebook","url":"https://www.facebook.com/SAPDigitalBusinessServices/","iconName":"Facebook"},{"tooltip":"Follow the SAP Support Twitter page","target":"_blank","rel":"noopener noreferrer","name":"twitter","url":"https://twitter.com/SAPSupportHelp","iconName":"Twitter"},{"tooltip":"Subscribe to Services and Support from SAP","target":"_blank","rel":"noopener noreferrer","name":"youtube","url":"https://www.youtube.com/user/SAPSupportInfo","iconName":"Youtube"},{"tooltip":"Follow SAP Support Help","target":"_blank","rel":"noopener noreferrer","name":"linkedin","url":"https://www.linkedin.com/groups/138840","iconName":"LinkedIn"}]' social-links-title='"Follow"'>
                </ds-footer>
               </div>
              </div>
             </div>
            </div>
           </div>
          </div>
         </div>
        </div>
       </div>
      </div>
     </div>
    </div>
   </center>
  </div>
  <script type="text/javascript">
   _satellite.pageBottom();
  </script>
  <!-- skin Page Hitbox Content START -->
  <script type="text/javascript">
   new Image().src = ["/","b","e","a","c","o","n","/","2","3","9","0","3","0","0","6","5","7","4","_","1","7","0","7","2","8","6","2","0","0","1","8","4",".","g","i","f"].join("");
  </script>
  <script language="javascript" type="text/javascript">
   <!--
LITHIUM.ScriptLoader.ready(['common', 'body', 'angularjs', 'angularjsModules'], function () {
LITHIUM.AngularSupport.setOptions({
  "useCsp" : true,
  "useNg2" : false,
  "coreModuleDeps" : [
    "li.directives.messages.message-image",
    "li.directives.common.non-bindable"
  ],
  "debugEnabled" : false
});
LITHIUM.AngularSupport.initGlobal(angular);LITHIUM.AngularSupport.init();LITHIUM.Globals.restoreGlobals(['define', '$', 'jQuery', 'angular']);LITHIUM.Sandbox.restore();
LITHIUM.jQuery.fn.cssData.defaults = {"dataPrefix":"lia-js-data","pairDelimeter":"-","prefixDelimeter":"-"};
(($) => {
$(document).ready(() => {
    var Prism = window.PrsmK || window.Prism;
    var classupdates = [
            {'old': 'language-visual', 'new': 'language-visual-basic'},
            {'old': '-basic', 'new': ''},
            {'old': 'language-excel', 'new': 'language-excel-formula'},
            {'old': '-formula', 'new': ''}
        ];
    const fixer = (el5) => {
        $.each(classupdates, (i5, v5) => {
            if ((0 < (v5.old || '').length) && $(el5).hasClass(v5.old)) {
                if (0 < (v5.new || '').length) $(el5).addClass(v5.new);
                $(el5).removeClass(v5.old);
            }
        });
    };
    const worker = (el) => {
        fixer(el);
        //add classes for answers and blogs posts
        if(($(el)[0].className.length > 0) && $(el)[0].className.match(/language-(\w+)/) && !$(el).hasClass('lia-code-sample')) $(el).addClass('lia-code-sample');

        if($(el)[0].className.length === 0) $(el).addClass('lia-code-sample language-abap');
        //end add classes for answers and blogs

        if (!$(el).hasClass('line-numbers')) $(el).addClass('line-numbers');
        if ($('> code', el).length < 1){
            $(el).wrapInner('<code></code>');
            if($(el).hasClass("language-abap")){
                $(el).find('> code').addClass('language-abap');
            }
        }
        $('> code', el).each((i, v) => {
            fixer(v);
            if ($('.line-numbers-rows', v).length < 1) Prism.highlightElement(v);
        });
    };
    const styleNewSamples = () => {
        $('body pre.lia-code-sample').each((i2, v2) => {
            worker(v2);
        });
        $('.mce-edit-area iframe').each((i3, v3) => {
            $(v3).contents().find('body pre.lia-code-sample').each((i4, v4) => {
                worker(v4);
            });
        });
        //add classes for answers and blogs posts
        $('body pre').each((i5, v5) => {
            worker(v5);
        });
    };
    styleNewSamples();
    setInterval(() => {
        styleNewSamples();
    }, 500);
});
})(LITHIUM.jQuery);

LITHIUM.CommunityJsonObject.User.policies['forums.action.message-view.batch-messages.allow'] = false;
LITHIUM.InformationBox({"updateFeedbackEvent":"LITHIUM:updateAjaxFeedback","componentSelector":"#informationbox","feedbackSelector":".InfoMessage"});
LITHIUM.InformationBox({"updateFeedbackEvent":"LITHIUM:updateAjaxFeedback","componentSelector":"#informationbox_0","feedbackSelector":".InfoMessage"});
LITHIUM.InformationBox({"updateFeedbackEvent":"LITHIUM:updateAjaxFeedback","componentSelector":"#informationbox_1","feedbackSelector":".InfoMessage"});
LITHIUM.InformationBox({"updateFeedbackEvent":"LITHIUM:updateAjaxFeedback","componentSelector":"#informationbox_2","feedbackSelector":".InfoMessage"});
LITHIUM.AjaxFeedback(".lia-inline-ajax-feedback", "LITHIUM:hideAjaxFeedback", ".lia-inline-ajax-feedback-persist");
LITHIUM.Placeholder();
LITHIUM.AutoComplete({"options":{"autosuggestionAvailableInstructionText":"Auto-suggestions available. Use Up and Down arrow keys to navigate.","triggerTextLength":0,"autocompleteInstructionsSelector":"#autocompleteInstructionsText","updateInputOnSelect":true,"loadingText":"Searching...","emptyText":"No Matches","successText":"Results:","defaultText":"Enter a search word","autosuggestionUnavailableInstructionText":"No suggestions available","disabled":false,"footerContent":[{"scripts":"\n\n(function(b){LITHIUM.Link=function(f){function g(a){var c=b(this),e=c.data(\"lia-action-token\");!0!==c.data(\"lia-ajax\")&&void 0!==e&&!1===a.isPropagationStopped()&&!1===a.isImmediatePropagationStopped()&&!1===a.isDefaultPrevented()&&(a.stop(),a=b(\"\\x3cform\\x3e\",{method:\"POST\",action:c.attr(\"href\"),enctype:\"multipart/form-data\"}),e=b(\"\\x3cinput\\x3e\",{type:\"hidden\",name:\"lia-action-token\",value:e}),a.append(e),b(document.body).append(a),a.submit(),d.trigger(\"click\"))}var d=b(document);void 0===d.data(\"lia-link-action-handler\")&&\n(d.data(\"lia-link-action-handler\",!0),d.on(\"click.link-action\",f.linkSelector,g),b.fn.on=b.wrap(b.fn.on,function(a){var c=a.apply(this,b.makeArray(arguments).slice(1));this.is(document)&&(d.off(\"click.link-action\",f.linkSelector,g),a.call(this,\"click.link-action\",f.linkSelector,g));return c}))}})(LITHIUM.jQuery);\nLITHIUM.Link({\n  \"linkSelector\" : \"a.lia-link-ticket-post-action\"\n});LITHIUM.AjaxSupport.defaultAjaxFeedbackHtml = \"<div class=\\\"lia-inline-ajax-feedback lia-component-common-widget-ajax-feedback\\\">\\n\\t\\t\\t<div class=\\\"AjaxFeedback\\\" id=\\\"ajaxFeedback_3dfd1fae05334\\\"><\\/div>\\n\\t\\t\\t\\n\\t\\n\\n\\t\\n\\n\\t\\t<\\/div>\";LITHIUM.AjaxSupport.defaultAjaxErrorHtml = \"<span id=\\\"feedback-errorfeedback_3dfd1fb01fdb5\\\"> <\\/span>\\n\\n\\t\\n\\t\\t<div class=\\\"InfoMessage lia-panel-feedback-inline-alert lia-component-common-widget-feedback\\\" id=\\\"feedback_3dfd1fb01fdb5\\\">\\n\\t\\t\\t<div role=\\\"alert\\\" class=\\\"lia-text\\\">\\n\\t\\t\\t\\t\\n\\n\\t\\t\\t\\t\\n\\t\\t\\t\\t\\t<p ng-non-bindable=\\\"\\\" tabindex=\\\"0\\\">\\n\\t\\t\\t\\t\\t\\tSorry, unable to complete the action you requested.\\n\\t\\t\\t\\t\\t<\\/p>\\n\\t\\t\\t\\t\\n\\n\\t\\t\\t\\t\\n\\n\\t\\t\\t\\t\\n\\n\\t\\t\\t\\t\\n\\t\\t\\t<\\/div>\\n\\n\\t\\t\\t\\n\\t\\t<\\/div>\";LITHIUM.AjaxSupport.fromLink('#disableAutoComplete_3dfd1fad6fbd2', 'disableAutoComplete', '#ajaxfeedback_0', 'LITHIUM:ajaxError', {}, 'tiGWN4b6QS-cLTHXTmEidOK6HrPVlpA8y5ndr_prwfQ.', 'ajax');","content":"<a class=\"lia-link-navigation lia-autocomplete-toggle-off lia-link-ticket-post-action lia-component-search-action-disable-auto-complete\" data-lia-action-token=\"jUcazG33mN-9KtsoTmlqM6XIK2MJBlIDC9aHyfVnHFs.\" rel=\"nofollow\" id=\"disableAutoComplete_3dfd1fad6fbd2\" href=\"https://community.sap.com/t5/blogs/v2/blogarticlepage.disableautocomplete:disableautocomplete?t:ac=blog-id/erp-blog-sap/article-id/26183&amp;t:cp=action/contributions/searchactions\">Turn off suggestions<\/a>"}],"prefixTriggerTextLength":3},"inputSelector":"#messageSearchField_0","redirectToItemLink":false,"url":"https://community.sap.com/t5/blogs/v2/blogarticlepage.searchformv32.messagesearchfield.messagesearchfield:autocomplete?t:ac=blog-id/erp-blog-sap/article-id/26183&t:cp=search/contributions/page","resizeImageEvent":"LITHIUM:renderImages"});
LITHIUM.AutoComplete({"options":{"autosuggestionAvailableInstructionText":"Auto-suggestions available. Use Up and Down arrow keys to navigate.","triggerTextLength":0,"autocompleteInstructionsSelector":"#autocompleteInstructionsText_0","updateInputOnSelect":true,"loadingText":"Searching...","emptyText":"No Matches","successText":"Results:","defaultText":"Enter a search word","autosuggestionUnavailableInstructionText":"No suggestions available","disabled":false,"footerContent":[{"scripts":"\n\n(function(b){LITHIUM.Link=function(f){function g(a){var c=b(this),e=c.data(\"lia-action-token\");!0!==c.data(\"lia-ajax\")&&void 0!==e&&!1===a.isPropagationStopped()&&!1===a.isImmediatePropagationStopped()&&!1===a.isDefaultPrevented()&&(a.stop(),a=b(\"\\x3cform\\x3e\",{method:\"POST\",action:c.attr(\"href\"),enctype:\"multipart/form-data\"}),e=b(\"\\x3cinput\\x3e\",{type:\"hidden\",name:\"lia-action-token\",value:e}),a.append(e),b(document.body).append(a),a.submit(),d.trigger(\"click\"))}var d=b(document);void 0===d.data(\"lia-link-action-handler\")&&\n(d.data(\"lia-link-action-handler\",!0),d.on(\"click.link-action\",f.linkSelector,g),b.fn.on=b.wrap(b.fn.on,function(a){var c=a.apply(this,b.makeArray(arguments).slice(1));this.is(document)&&(d.off(\"click.link-action\",f.linkSelector,g),a.call(this,\"click.link-action\",f.linkSelector,g));return c}))}})(LITHIUM.jQuery);\nLITHIUM.Link({\n  \"linkSelector\" : \"a.lia-link-ticket-post-action\"\n});LITHIUM.AjaxSupport.fromLink('#disableAutoComplete_3dfd1fb549289', 'disableAutoComplete', '#ajaxfeedback_0', 'LITHIUM:ajaxError', {}, 'Z7XJU5kyxHQdErvLTv4Jm0wkbhXpiEdeWsTZsR4jgR4.', 'ajax');","content":"<a class=\"lia-link-navigation lia-autocomplete-toggle-off lia-link-ticket-post-action lia-component-search-action-disable-auto-complete\" data-lia-action-token=\"AKsuTrmNTlQ3siz0yYAiN3Bx_dDshiqB17UWBCUjq-s.\" rel=\"nofollow\" id=\"disableAutoComplete_3dfd1fb549289\" href=\"https://community.sap.com/t5/blogs/v2/blogarticlepage.disableautocomplete:disableautocomplete?t:ac=blog-id/erp-blog-sap/article-id/26183&amp;t:cp=action/contributions/searchactions\">Turn off suggestions<\/a>"}],"prefixTriggerTextLength":3},"inputSelector":"#messageSearchField_1","redirectToItemLink":false,"url":"https://community.sap.com/t5/blogs/v2/blogarticlepage.searchformv32.tkbmessagesearchfield.messagesearchfield:autocomplete?t:ac=blog-id/erp-blog-sap/article-id/26183&t:cp=search/contributions/page","resizeImageEvent":"LITHIUM:renderImages"});
LITHIUM.AutoComplete({"options":{"autosuggestionAvailableInstructionText":"Auto-suggestions available. Use Up and Down arrow keys to navigate.","triggerTextLength":0,"autocompleteInstructionsSelector":"#autocompleteInstructionsText_1","updateInputOnSelect":true,"loadingText":"Searching for users...","emptyText":"No Matches","successText":"Users found:","defaultText":"Enter a user name or rank","autosuggestionUnavailableInstructionText":"No suggestions available","disabled":false,"footerContent":[{"scripts":"\n\n(function(b){LITHIUM.Link=function(f){function g(a){var c=b(this),e=c.data(\"lia-action-token\");!0!==c.data(\"lia-ajax\")&&void 0!==e&&!1===a.isPropagationStopped()&&!1===a.isImmediatePropagationStopped()&&!1===a.isDefaultPrevented()&&(a.stop(),a=b(\"\\x3cform\\x3e\",{method:\"POST\",action:c.attr(\"href\"),enctype:\"multipart/form-data\"}),e=b(\"\\x3cinput\\x3e\",{type:\"hidden\",name:\"lia-action-token\",value:e}),a.append(e),b(document.body).append(a),a.submit(),d.trigger(\"click\"))}var d=b(document);void 0===d.data(\"lia-link-action-handler\")&&\n(d.data(\"lia-link-action-handler\",!0),d.on(\"click.link-action\",f.linkSelector,g),b.fn.on=b.wrap(b.fn.on,function(a){var c=a.apply(this,b.makeArray(arguments).slice(1));this.is(document)&&(d.off(\"click.link-action\",f.linkSelector,g),a.call(this,\"click.link-action\",f.linkSelector,g));return c}))}})(LITHIUM.jQuery);\nLITHIUM.Link({\n  \"linkSelector\" : \"a.lia-link-ticket-post-action\"\n});LITHIUM.AjaxSupport.fromLink('#disableAutoComplete_3dfd1fb808824', 'disableAutoComplete', '#ajaxfeedback_0', 'LITHIUM:ajaxError', {}, 'toAUbiqJ1MrBQ5rf5dDjaFLWq9R6cCK3VF4qzy1MXcM.', 'ajax');","content":"<a class=\"lia-link-navigation lia-autocomplete-toggle-off lia-link-ticket-post-action lia-component-search-action-disable-auto-complete\" data-lia-action-token=\"_-kzRpXh8FSIcnjYwlPdq3FHCKMatZ8bX_VJ3EI49v0.\" rel=\"nofollow\" id=\"disableAutoComplete_3dfd1fb808824\" href=\"https://community.sap.com/t5/blogs/v2/blogarticlepage.disableautocomplete:disableautocomplete?t:ac=blog-id/erp-blog-sap/article-id/26183&amp;t:cp=action/contributions/searchactions\">Turn off suggestions<\/a>"}],"prefixTriggerTextLength":0},"inputSelector":"#userSearchField","redirectToItemLink":false,"url":"https://community.sap.com/t5/blogs/v2/blogarticlepage.searchformv32.usersearchfield.usersearchfield:autocomplete?t:ac=blog-id/erp-blog-sap/article-id/26183&t:cp=search/contributions/page","resizeImageEvent":"LITHIUM:renderImages"});
LITHIUM.AjaxSupport({"ajaxOptionsParam":{"event":"LITHIUM:userExistsQuery","parameters":{"javascript.ignore_combine_and_minify":"true"}},"tokenId":"ajax","elementSelector":"#userSearchField","action":"userExistsQuery","feedbackSelector":"#ajaxfeedback_0","url":"https://community.sap.com/t5/blogs/v2/blogarticlepage.searchformv32.usersearchfield:userexistsquery?t:ac=blog-id/erp-blog-sap/article-id/26183&t:cp=search/contributions/page","ajaxErrorEventName":"LITHIUM:ajaxError","token":"FS_rJ6fKc7R1r9UnmMF7pYAJyrLQs06rdxda_TciW04."});
LITHIUM.AutoComplete({"options":{"autosuggestionAvailableInstructionText":"Auto-suggestions available. Use Up and Down arrow keys to navigate.","triggerTextLength":0,"autocompleteInstructionsSelector":"#autocompleteInstructionsText_2","updateInputOnSelect":true,"loadingText":"Searching...","emptyText":"No Matches","successText":"Results:","defaultText":"Enter a search word","autosuggestionUnavailableInstructionText":"No suggestions available","disabled":false,"footerContent":[{"scripts":"\n\n(function(b){LITHIUM.Link=function(f){function g(a){var c=b(this),e=c.data(\"lia-action-token\");!0!==c.data(\"lia-ajax\")&&void 0!==e&&!1===a.isPropagationStopped()&&!1===a.isImmediatePropagationStopped()&&!1===a.isDefaultPrevented()&&(a.stop(),a=b(\"\\x3cform\\x3e\",{method:\"POST\",action:c.attr(\"href\"),enctype:\"multipart/form-data\"}),e=b(\"\\x3cinput\\x3e\",{type:\"hidden\",name:\"lia-action-token\",value:e}),a.append(e),b(document.body).append(a),a.submit(),d.trigger(\"click\"))}var d=b(document);void 0===d.data(\"lia-link-action-handler\")&&\n(d.data(\"lia-link-action-handler\",!0),d.on(\"click.link-action\",f.linkSelector,g),b.fn.on=b.wrap(b.fn.on,function(a){var c=a.apply(this,b.makeArray(arguments).slice(1));this.is(document)&&(d.off(\"click.link-action\",f.linkSelector,g),a.call(this,\"click.link-action\",f.linkSelector,g));return c}))}})(LITHIUM.jQuery);\nLITHIUM.Link({\n  \"linkSelector\" : \"a.lia-link-ticket-post-action\"\n});LITHIUM.AjaxSupport.fromLink('#disableAutoComplete_3dfd1fbb11e27', 'disableAutoComplete', '#ajaxfeedback_0', 'LITHIUM:ajaxError', {}, 'mhzlp9Qzpn8dpa2UGpAlszdDOwNb_ohbcWlRzsEzSD4.', 'ajax');","content":"<a class=\"lia-link-navigation lia-autocomplete-toggle-off lia-link-ticket-post-action lia-component-search-action-disable-auto-complete\" data-lia-action-token=\"27vIBqHasjillMCH6WOeSMTXldOMXCiBejBajQCc9yY.\" rel=\"nofollow\" id=\"disableAutoComplete_3dfd1fbb11e27\" href=\"https://community.sap.com/t5/blogs/v2/blogarticlepage.disableautocomplete:disableautocomplete?t:ac=blog-id/erp-blog-sap/article-id/26183&amp;t:cp=action/contributions/searchactions\">Turn off suggestions<\/a>"}],"prefixTriggerTextLength":0},"inputSelector":"#noteSearchField_0","redirectToItemLink":false,"url":"https://community.sap.com/t5/blogs/v2/blogarticlepage.searchformv32.notesearchfield.notesearchfield:autocomplete?t:ac=blog-id/erp-blog-sap/article-id/26183&t:cp=search/contributions/page","resizeImageEvent":"LITHIUM:renderImages"});
LITHIUM.AutoComplete({"options":{"autosuggestionAvailableInstructionText":"Auto-suggestions available. Use Up and Down arrow keys to navigate.","triggerTextLength":0,"autocompleteInstructionsSelector":"#autocompleteInstructionsText_3","updateInputOnSelect":true,"loadingText":"Searching...","emptyText":"No Matches","successText":"Results:","defaultText":"Enter a search word","autosuggestionUnavailableInstructionText":"No suggestions available","disabled":false,"footerContent":[{"scripts":"\n\n(function(b){LITHIUM.Link=function(f){function g(a){var c=b(this),e=c.data(\"lia-action-token\");!0!==c.data(\"lia-ajax\")&&void 0!==e&&!1===a.isPropagationStopped()&&!1===a.isImmediatePropagationStopped()&&!1===a.isDefaultPrevented()&&(a.stop(),a=b(\"\\x3cform\\x3e\",{method:\"POST\",action:c.attr(\"href\"),enctype:\"multipart/form-data\"}),e=b(\"\\x3cinput\\x3e\",{type:\"hidden\",name:\"lia-action-token\",value:e}),a.append(e),b(document.body).append(a),a.submit(),d.trigger(\"click\"))}var d=b(document);void 0===d.data(\"lia-link-action-handler\")&&\n(d.data(\"lia-link-action-handler\",!0),d.on(\"click.link-action\",f.linkSelector,g),b.fn.on=b.wrap(b.fn.on,function(a){var c=a.apply(this,b.makeArray(arguments).slice(1));this.is(document)&&(d.off(\"click.link-action\",f.linkSelector,g),a.call(this,\"click.link-action\",f.linkSelector,g));return c}))}})(LITHIUM.jQuery);\nLITHIUM.Link({\n  \"linkSelector\" : \"a.lia-link-ticket-post-action\"\n});LITHIUM.AjaxSupport.fromLink('#disableAutoComplete_3dfd1fbd7a070', 'disableAutoComplete', '#ajaxfeedback_0', 'LITHIUM:ajaxError', {}, 'WLvcAaREJMWnmtmHg9IWmK4uSqMRBS9yVoOwtmc6nnk.', 'ajax');","content":"<a class=\"lia-link-navigation lia-autocomplete-toggle-off lia-link-ticket-post-action lia-component-search-action-disable-auto-complete\" data-lia-action-token=\"EhIj8h4N_0wDauBlF3qkoI80E1ogWFUjPucMoeFExRo.\" rel=\"nofollow\" id=\"disableAutoComplete_3dfd1fbd7a070\" href=\"https://community.sap.com/t5/blogs/v2/blogarticlepage.disableautocomplete:disableautocomplete?t:ac=blog-id/erp-blog-sap/article-id/26183&amp;t:cp=action/contributions/searchactions\">Turn off suggestions<\/a>"}],"prefixTriggerTextLength":0},"inputSelector":"#productSearchField","redirectToItemLink":false,"url":"https://community.sap.com/t5/blogs/v2/blogarticlepage.searchformv32.productsearchfield.productsearchfield:autocomplete?t:ac=blog-id/erp-blog-sap/article-id/26183&t:cp=search/contributions/page","resizeImageEvent":"LITHIUM:renderImages"});
LITHIUM.Link({"linkSelector":"a.lia-link-ticket-post-action"});
LITHIUM.AjaxSupport.fromLink('#enableAutoComplete', 'enableAutoComplete', '#ajaxfeedback_0', 'LITHIUM:ajaxError', {}, 'Kam6Hir1sjwPk0s0NM8Q7MWfRIJQ5Tu91JI9xcmNzaY.', 'ajax');
LITHIUM.Tooltip({"bodySelector":"body#lia-body","delay":30,"enableOnClickForTrigger":false,"predelay":10,"triggerSelector":"#link_5","tooltipContentSelector":"#link_6-tooltip-element .content","position":["bottom","left"],"tooltipElementSelector":"#link_6-tooltip-element","events":{"def":"focus mouseover keydown,blur mouseout keydown"},"hideOnLeave":true});
LITHIUM.HelpIcon({"selectors":{"helpIconSelector":".help-icon .lia-img-icon-help"}});
LITHIUM.SearchAutoCompleteToggle({"containerSelector":"#searchautocompletetoggle","enableAutoCompleteSelector":".search-autocomplete-toggle-link","enableAutocompleteSuccessEvent":"LITHIUM:ajaxSuccess:enableAutoComplete","disableAutoCompleteSelector":".lia-autocomplete-toggle-off","disableAutocompleteSuccessEvent":"LITHIUM:ajaxSuccess:disableAutoComplete","autoCompleteSelector":".lia-autocomplete-input"});
LITHIUM.SearchForm({"asSearchActionIdSelector":".lia-as-search-action-id","useAutoComplete":true,"selectSelector":".lia-search-form-granularity","useClearSearchButton":false,"buttonSelector":".lia-button-searchForm-action","asSearchActionIdParamName":"as-search-action-id","formSelector":"#lia-searchformV32","nodesModel":{"erp|category":{"title":"Search Category: Enterprise Resource Planning Blogs by SAP","inputSelector":".lia-search-input-message"},"khhcw49343|community":{"title":"Search Community: Enterprise Resource Planning Blogs by SAP","inputSelector":".lia-search-input-message"},"erp-blog-sap|blog-board":{"title":"Search Blog: Enterprise Resource Planning Blogs by SAP","inputSelector":".lia-search-input-message"},"tkb|tkb":{"title":"Knowledge base","inputSelector":".lia-search-input-tkb-article"},"product|product":{"title":"Managed tags","inputSelector":".lia-search-input-product"},"user|user":{"title":"Users","inputSelector":".lia-search-input-user"}},"asSearchActionIdHeaderKey":"X-LI-AS-Search-Action-Id","inputSelector":"#messageSearchField_0:not(.lia-js-hidden)","clearSearchButtonSelector":null});
LITHIUM.InformationBox({"updateFeedbackEvent":"LITHIUM:updateAjaxFeedback","componentSelector":"#pageInformation","feedbackSelector":".InfoMessage"});
LITHIUM.InformationBox({"updateFeedbackEvent":"LITHIUM:updateAjaxFeedback","componentSelector":"#informationbox_3","feedbackSelector":".InfoMessage"});
LITHIUM.InformationBox({"updateFeedbackEvent":"LITHIUM:updateAjaxFeedback","componentSelector":"#informationbox_4","feedbackSelector":".InfoMessage"});
LITHIUM.InformationBox({"updateFeedbackEvent":"LITHIUM:updateAjaxFeedback","componentSelector":"#informationbox_5","feedbackSelector":".InfoMessage"});
LITHIUM.Form.resetFieldForFocusFound();
LITHIUM.CustomEvent('.lia-custom-event', 'click');
LITHIUM.DropDownMenu({"userMessagesFeedOptionsClass":"div.user-messages-feed-options-menu a.lia-js-menu-opener","menuOffsetContainer":".lia-menu-offset-container","hoverLeaveEvent":"LITHIUM:hoverLeave","mouseoverElementSelector":".lia-js-mouseover-menu","userMessagesFeedOptionsAriaLabel":"Show contributions of the user, selected option is Show SAP S/4HANA Simplification Item Check - How to do it right. post option menu. You may choose another option from the dropdown menu.","disabledLink":"lia-link-disabled","menuOpenCssClass":"dropdownHover","menuElementSelector":".lia-menu-navigation-wrapper","dialogSelector":".lia-panel-dialog-trigger","messageOptions":"lia-component-message-view-widget-action-menu","menuBarComponent":"lia-component-menu-bar","closeMenuEvent":"LITHIUM:closeMenu","menuOpenedEvent":"LITHIUM:menuOpened","pageOptions":"lia-component-community-widget-page-options","clickElementSelector":".lia-js-click-menu","menuItemsSelector":".lia-menu-dropdown-items","menuClosedEvent":"LITHIUM:menuClosed"});
LITHIUM.DropDownMenuVisibilityHandler({"selectors":{"menuSelector":"#actionMenuDropDown","menuItemsSelector":".lia-menu-dropdown-items"}});
LITHIUM.AjaxSupport.fromLink('#kudoEntity', 'kudoEntity', '#ajaxfeedback_1', 'LITHIUM:ajaxError', {}, 'FzmFtjkX3xPuRowGJDngos3tO8Ye8wXQgu0awT8varM.', 'ajax');
LITHIUM.AjaxSupport.ComponentEvents.set({
  "eventActions" : [
    {
      "event" : "kudoEntity",
      "actions" : [
        {
          "context" : "envParam:entity",
          "action" : "rerender"
        }
      ]
    }
  ],
  "componentId" : "kudos.widget.button",
  "initiatorBinding" : true,
  "selector" : "#kudosButtonV2",
  "parameters" : {
    "displayStyle" : "horizontal",
    "disallowZeroCount" : "false",
    "revokeMode" : "true",
    "kudosable" : "true",
    "showCountOnly" : "false",
    "disableKudosForAnonUser" : "false",
    "useCountToKudo" : "false",
    "entity" : "13386669",
    "linkDisabled" : "false"
  },
  "initiatorDataMatcher" : "data-lia-kudos-id"
});
LITHIUM.MessageBodyDisplay('#bodyDisplay', '.lia-truncated-body-container', '#viewMoreLink', '.lia-full-body-container' );
LITHIUM.InformationBox({"updateFeedbackEvent":"LITHIUM:updateAjaxFeedback","componentSelector":"#informationbox_6","feedbackSelector":".InfoMessage"});
LITHIUM.AjaxSupport.ComponentEvents.set({
  "eventActions" : [
    {
      "event" : "approveMessage",
      "actions" : [
        {
          "context" : "",
          "action" : "rerender"
        },
        {
          "context" : "",
          "action" : "pulsate"
        }
      ]
    },
    {
      "event" : "unapproveMessage",
      "actions" : [
        {
          "context" : "",
          "action" : "rerender"
        },
        {
          "context" : "",
          "action" : "pulsate"
        }
      ]
    },
    {
      "event" : "deleteMessage",
      "actions" : [
        {
          "context" : "lia-deleted-state",
          "action" : "addClassName"
        },
        {
          "context" : "",
          "action" : "pulsate"
        }
      ]
    },
    {
      "event" : "QuickReply",
      "actions" : [
        {
          "context" : "envParam:feedbackData",
          "action" : "rerender"
        }
      ]
    },
    {
      "event" : "expandMessage",
      "actions" : [
        {
          "context" : "envParam:quiltName,expandedQuiltName",
          "action" : "rerender"
        }
      ]
    },
    {
      "event" : "ProductAnswer",
      "actions" : [
        {
          "context" : "envParam:quiltName",
          "action" : "rerender"
        }
      ]
    },
    {
      "event" : "ProductAnswerComment",
      "actions" : [
        {
          "context" : "envParam:selectedMessage",
          "action" : "rerender"
        }
      ]
    },
    {
      "event" : "editProductMessage",
      "actions" : [
        {
          "context" : "envParam:quiltName,message",
          "action" : "rerender"
        }
      ]
    },
    {
      "event" : "MessagesWidgetEditAction",
      "actions" : [
        {
          "context" : "envParam:quiltName,message,product,contextId,contextUrl",
          "action" : "rerender"
        }
      ]
    },
    {
      "event" : "ProductMessageEdit",
      "actions" : [
        {
          "context" : "envParam:quiltName",
          "action" : "rerender"
        }
      ]
    },
    {
      "event" : "MessagesWidgetMessageEdit",
      "actions" : [
        {
          "context" : "envParam:quiltName,product,contextId,contextUrl",
          "action" : "rerender"
        }
      ]
    },
    {
      "event" : "AcceptSolutionAction",
      "actions" : [
        {
          "context" : "",
          "action" : "rerender"
        }
      ]
    },
    {
      "event" : "RevokeSolutionAction",
      "actions" : [
        {
          "context" : "",
          "action" : "rerender"
        }
      ]
    },
    {
      "event" : "addThreadUserEmailSubscription",
      "actions" : [
        {
          "context" : "",
          "action" : "rerender"
        }
      ]
    },
    {
      "event" : "removeThreadUserEmailSubscription",
      "actions" : [
        {
          "context" : "",
          "action" : "rerender"
        }
      ]
    },
    {
      "event" : "addMessageUserEmailSubscription",
      "actions" : [
        {
          "context" : "",
          "action" : "rerender"
        }
      ]
    },
    {
      "event" : "removeMessageUserEmailSubscription",
      "actions" : [
        {
          "context" : "",
          "action" : "rerender"
        }
      ]
    },
    {
      "event" : "markAsSpamWithoutRedirect",
      "actions" : [
        {
          "context" : "",
          "action" : "rerender"
        }
      ]
    },
    {
      "event" : "MessagesWidgetAnswerForm",
      "actions" : [
        {
          "context" : "envParam:messageUid,page,quiltName,product,contextId,contextUrl",
          "action" : "rerender"
        }
      ]
    },
    {
      "event" : "MessagesWidgetEditAnswerForm",
      "actions" : [
        {
          "context" : "envParam:messageUid,quiltName,product,contextId,contextUrl",
          "action" : "rerender"
        }
      ]
    },
    {
      "event" : "MessagesWidgetCommentForm",
      "actions" : [
        {
          "context" : "envParam:messageUid,quiltName,product,contextId,contextUrl",
          "action" : "rerender"
        }
      ]
    },
    {
      "event" : "MessagesWidgetEditCommentForm",
      "actions" : [
        {
          "context" : "envParam:messageUid,quiltName,product,contextId,contextUrl",
          "action" : "rerender"
        }
      ]
    }
  ],
  "componentId" : "forums.widget.message-view",
  "initiatorBinding" : true,
  "selector" : "#messageview",
  "parameters" : {
    "disableLabelLinks" : "false",
    "truncateBodyRetainsHtml" : "false",
    "forceSearchRequestParameterForBlurbBuilder" : "false",
    "kudosLinksDisabled" : "false",
    "useSubjectIcons" : "true",
    "quiltName" : "BlogTopicMessage",
    "truncateBody" : "true",
    "message" : "13386669",
    "includeRepliesModerationState" : "false",
    "useSimpleView" : "false",
    "useTruncatedSubject" : "true",
    "disableLinks" : "false",
    "messageViewOptions" : "1111110111111100111111101110100101111101",
    "displaySubject" : "true"
  },
  "initiatorDataMatcher" : "data-lia-message-uid"
});
LITHIUM.Components.renderInPlace('blogsPageCommentList', {"componentParams":"{\n  \"articleRef\" : \"26183\",\n  \"blogRef\" : \"-0vHuYDnFQ1wPgm-fISEZ5EgukgB-F0uAh6rwT9BgU3xYgDWMh5LBVE7nBqN_YS72W_GZ4hoDXKuH7dk4uQWH3UoOwHnIPkkRLSmTqzcDLTJJ0zULg2IgcUZYbTQWPFrWEU1fu5qTtp0CXP9R63AaN667CigEuA4T32xOs70of9WVuZl6GvL8fPmB5VZAEmrpauWFAL6A0zWEto8SjFmEEYCov44TS6CwAw_cS6TqSL7CEkJPOlzp_yFy0rK7hqWB4VdX4VLlxkRuJPinih5_WbZeEYY9TAukRbrvCaPHW5EFFRJy8odORCwNlu8C1uNi62kfhhO9J0x9aq2NwoazIVSPx0wxyGnF7d5fyAN20VntnZRjkKlPPtb5o7eVvw1JvWCQAhzuZLKQpWmPXDvz6YQFOEthPsB2VRY18zggr0cglMBTXC4MeN-CTCLiFre7ywYvKbOZM5K14ijKdLg61LkfmpV_92WV_fNDNI18Mk4khQBw08MWSqURSlCLuQFsc80JkoJAhMB72D_nPMKAo9sHSQovJVU3E8hAFXo5tckKj7y5QwZXrslKnxZL6uk-dvg2TA7rVW2NDso9PYvs9apwa1IDTf3XouXgK6yxblS9UEXhbAoV-bY5evAldfaGgH8mMreNi8-ssW2FV7YNPhRc3mIABhtF7EXXcPBgOEXKQ2RJNhwWS3dNNMo1m5dez5Dx9YEtjusBSM1bBEIvk6dgx4320NYd0W8oPU1a8rPJDb2ka2IdKL4nvGIrNTFPqtZGyyBTrsEPJ0bS8aR3m7Ve0Hczr0nff4aw_1UwilGQI2gbtMIt80UuXfMAvJg9LtefTDrXQY32uY8fXrGPmwlaO66_hJ3tfIaZ8tCnpqyvq8jZOvtlz5K7p-NPvVZu8imC381VGKR4d3gZp9TdvjNHbPchVjjcCO0IJw6MqlzPBPturPb4KTv12Up6MvaMJ7vACyxFqkCETTn8cVY3BJRV9TSY70YOPA7SuDY4tPuDWI1A-1xZAmESqf_-uF5CR-K6mU5KULR3NhjqD5HI0hUHGbF7FDkZQFXPD8eZyAHI7BB47FunKILDH0ac9DcoR_lSHWYlcSaf1lAElBwwEwIbKgZAkJES4SovXmsgcyezTn-lAXukzX7rL5Lp1aoRUOK4meKBKHw4g3nNZNXdHEsDbCBoupCJpfOzkuKnXYLlfH4nYbmM7NJu56iT1y3McVlXQtUWk6gnqYycL1RcqOvHDyKO4veYYMStto-JOXyUDBlpAE-B5sUM-sMfERSus8ljZtcoiCYju-gFR361_elz8tCwauJ_PgsaaWMgsFCSjNt9nA4t4bMrid0myDgP_toIv_XNS5hhMR5TtXYZbSWi67J3keEGHbCX2D60_-sE4g9yUHEwdm0KIlPHh231AIkOlfXwOfWNCQNK36JvRIi4RLtME5K4ttzOAlUaBbmZLLgeSJ9cC2IOIS9UZORLgoL2hS4HEEgkFPkVndLHUKrfrY7hBF9gY1kGeduNNvPWn7_iOpoFtd_7m9TW3t6j9WX3W2npD33j8_viL2lc6F4Pn2Kf3tSEi-go16hgAb7mZLAmBhVDGRzSQRDtZf4QWYc6dOTtJ_h-TT8oD_QBgjWmrUl481coeITpHnJwCXg0z21PSCJMcQj35i_29U887OHj2nNBBXomQ9ya3fl5wxCQy72iI62fegBR_SYTDgpljMtFRhUR7dBI6-i70TUeP0OHn1bpVQpj_3kANJEldBCGD9et-Be2VKyMChlw9dF0ILw4dpPYqAomMIpnHZ6XUOr8grDCDz_C0oJZ9EHyyxPBCZ42jqlvKXsb_wurfOaC2wp7mFC1e6ERS0C22J6\",\n  \"messageViewOptions\" : \"1111110111111100111111101110100101111101\",\n  \"viewAllComments\" : \"false\",\n  \"commentPageId\" : \"1\"\n}","componentId":"blogsPageCommentList"}, {"errorMessage":"An Unexpected Error has occurred.","type":"POST","url":"https://community.sap.com/t5/blogs/v2/blogarticlepage.componentdisplay:lazyrender?t:ac=blog-id/erp-blog-sap/article-id/26183"}, 'lazyload');
LITHIUM.InformationBox({"updateFeedbackEvent":"LITHIUM:updateAjaxFeedback","componentSelector":"#informationbox_7","feedbackSelector":".InfoMessage"});
LITHIUM.InformationBox({"updateFeedbackEvent":"LITHIUM:updateAjaxFeedback","componentSelector":"#informationbox_8","feedbackSelector":".InfoMessage"});
LITHIUM.AjaxSupport.ComponentEvents.set({
  "eventActions" : [
    {
      "event" : "sortLabelsWidget",
      "actions" : [
        {
          "context" : "envParam:viewOrderSpec",
          "action" : "rerender"
        }
      ]
    }
  ],
  "componentId" : "labels.widget.labels.sortable",
  "initiatorBinding" : false,
  "selector" : "#labelsTaplet",
  "parameters" : {
    "labelDisplaySize" : "100",
    "useSortHeader" : "false",
    "viewOrderSpec" : "_mAVxxYoHZgdIiZT4agFxwfrcRtKAyIHsdm9fqkLNBTbmqyrb-to2lMIA8MuFaXRfzZwFOzboqZ2hTgS2jPfAqeykudjcYJDRrg3_YDtVQlPSVWz4Mt1bMRwahxNQrOtNaZi8CiQl1UxdsEYIq3LZ0_GHdpZuYO7BWAkK4SB_s14Sf4oOC0-JRCCJLlOm8kWMhwyZTrnP6B0M9ozRXcD9kPtgJaaWwEDBlglgzYMZOG6JuLstDcylONsIUehUQZh6r1XmqR1KAGxeUV-XdDR8n3ozx4F6BkgyuwAkTLiWNbuIGwiY3umFAk7BtCBoYpEpIXyfj6ztwP1etuSjKaVg0p65RWAQg09T1lf7Z626lcKfvYku-Tr0y-3CzIs5mW_BXN9w0silhkQn82foqIavfoL2XHBd2Pn2sk_DSYUgNl664VNVT8nnuOiMKmMxyef8LxZg5O4oh29SdAqlxkZ5MNHmOao65ofqd95NsbxmufrLtE0lAUcTEbjjp4uJ5pDi-ERtuDL2YW7tUoG1vyDFtoWXtmrOLFWDl0YcH58tvc."
  },
  "initiatorDataMatcher" : ""
});
LITHIUM.AjaxSupport({"ajaxOptionsParam":{"event":"LITHIUM:sortLabelsWidget","parameters":{"javascript.ignore_combine_and_minify":"true"}},"tokenId":"ajax","elementSelector":"#labelsTaplet","action":"sortLabelsWidget","feedbackSelector":false,"url":"https://community.sap.com/t5/blogs/v2/blogarticlepage.labelstaplet:sortlabelswidget?t:ac=blog-id/erp-blog-sap/article-id/26183&t:cp=labels/contributions/page","ajaxErrorEventName":"LITHIUM:ajaxError","token":"tTCfRHlcRNj_SZaQl4AN1k5bziE7ZNQXKFgI_zghcAg."});
LITHIUM.UserListActual({"acceptedSolutionsColumnSelector":".UserList .lia-list-row .acceptedSolutionsCountColumn","kudosColumnSelector":".UserList .lia-list-row .kudosCountColumn"});
;document.createElement('ds-footer');
document.addEventListener('openCookiePreferences', (e) => {
  truste.eu.reopenBanner();

});

LITHIUM.PartialRenderProxy({"limuirsComponentRenderedEvent":"LITHIUM:limuirsComponentRendered","relayEvent":"LITHIUM:partialRenderProxyRelay","listenerEvent":"LITHIUM:partialRenderProxy"});
LITHIUM.AjaxSupport({"ajaxOptionsParam":{"event":"LITHIUM:partialRenderProxyRelay","parameters":{"javascript.ignore_combine_and_minify":"true"}},"tokenId":"ajax","elementSelector":document,"action":"partialRenderProxyRelay","feedbackSelector":false,"url":"https://community.sap.com/t5/blogs/v2/blogarticlepage.liabase.basebody.partialrenderproxy:partialrenderproxyrelay?t:ac=blog-id/erp-blog-sap/article-id/26183","ajaxErrorEventName":"LITHIUM:ajaxError","token":"Jml2VIY4eawZPaVlT2PJdTPs6jpkv5YXBT44qItie1E."});
LITHIUM.Auth.API_URL = "/t5/util/authcheckpage";
LITHIUM.Auth.LOGIN_URL_TMPL = "/plugins/common/feature/oidcss/sso_login_redirect/providerid/sap_ids?referer=https%3A%2F%2FREPLACE_TEXT";
LITHIUM.Auth.KEEP_ALIVE_URL = "/t5/status/blankpage?keepalive";
LITHIUM.Auth.KEEP_ALIVE_TIME = 180000;
LITHIUM.Auth.CHECK_SESSION_TOKEN = 'MorUk2QW6gJrGnQXupT-6WmTcztDaVIPCOgRVypMqgI.';
LITHIUM.AjaxSupport.useTickets = false;
LITHIUM.Cache.CustomEvent.set([{"elementId":"link_7","stopTriggerEvent":false,"fireEvent":"LITHIUM:selectMessage","triggerEvent":"click","eventContext":{"message":13386669}},{"elementId":"link_10","stopTriggerEvent":false,"fireEvent":"LITHIUM:labelSelected","triggerEvent":"click","eventContext":{"uid":1308,"selectedLabel":"technology updates","title":"Technology Updates"}},{"elementId":"link_16","stopTriggerEvent":false,"fireEvent":"LITHIUM:changePage","triggerEvent":"click","eventContext":{"parameters":{"page":2}}},{"elementId":"link_17","stopTriggerEvent":false,"fireEvent":"LITHIUM:changePage","triggerEvent":"click","eventContext":{"parameters":{"pageNavigationDirection":"next","page":2}}}]);
LITHIUM.Loader.runJsAttached();

});
// -->
  </script>
 </body>
</html>
