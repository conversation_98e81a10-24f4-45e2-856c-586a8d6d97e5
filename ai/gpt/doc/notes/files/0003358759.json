{"Request": {"Number": "0003358759", "LanguageSource": "EN", "LanguageTarget": "EN"}, "Response": {"_elapsedTime": 324, "Error": {}, "SAPNote": {"_type": "00200720410000000235", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": false, "url": ""}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0003358759?language=E&token=84B0CDD62CB54BE0686615D6CF212A96"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0003358759", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0003358759/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "3358759"}, "Type": {"_label": "Type", "value": "SAP Knowledge Base Article"}, "Version": {"_label": "Version", "value": 3}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Problem"}, "Priority": {"_label": "Priority", "value": "Normal"}, "Status": {"_label": "Release Status", "value": "Released to Customer"}, "ReleasedOn": {"_label": "Released On", "value": "27.09.2023"}, "SAPComponentKey": {"_label": "Component", "value": "BC-ESI-WS-ABA-MON"}, "SAPComponentKeyText": {"_label": "Component", "value": "WebServices ABAP Monitoring"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}]}, "Title": {"_label": "Title", "value": "3358759 - Long running batch job RC_COLLECT_ANALYSIS_DATA"}, "LongText": {"_label": "Description", "value": "<h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>In SAP Solution Manager 7.2, job with name RC_COLLECT_ANALYSIS_DATA run for multiple hours or days. The job log is hanging on task &#34;Start to collect IDoc Interface data&#34;</p>\r\n<p>Job log overview for job: &#x00A0; &#x00A0;RC_COLLECT_ANALYSIS_DATA / 11094800</p>\r\n<p>Date &#x00A0; &#x00A0; &#x00A0; Time &#x00A0; &#x00A0; Message text</p>\r\n<p>Job started<br />Step 001 started (program RC_COLLECT_ANALYSIS_DATA, variant &amp;XXXXXXXXXXXXX, user ID XXXXXXX)<br />Start to collect analysis data<br />Target S/4HANA PPMS stack: XXXXXXXXXXXXXXXX<br />Job TMW_RC_BPA_DATA_COLL is scheduled to collect Business Process Analytics data.<br />TMW_RC_HANAS_DATA_COLL is scheduled to collect HANA Sizing data.<br />TMW_RC_DVM_DATA_COLL is scheduled to collect Data Volume Management data.<br />TMW_RC_SITEM_DATA_COLL is scheduled to collect Simplification Item relevance check data.<br />TMW_RC_EFD_DATA_COLL is scheduled to collect Simplification Item Effort Driver data.<br />TMW_RC_INNOVA_DATA_COLL is scheduled to collect Innovation Potential data.<br />TMW_RC_FDQ_DATA_COLL is scheduled to collect Financial Data Quality data.<br />Start to collect Recommend SAP Fiori Apps data<br />Start to collect transaction object directory information<br />Transaction object directory information collected<br />Recommend SAP Fiori Apps data is collected<br />Start to collect Business Warehouse Extractor data<br />Buffered data less than 30 days( XXXXXXXXXXXXX ) is used.<br />Start to collect IDoc Interface data</p><h3 data-toc-skip class=\"section\" id=\"Environment\">Environment</h3><p>SAP Solution Manager 7.2</p><h3 data-toc-skip class=\"section\" id=\"Reproducing the Issue\">Reproducing the Issue</h3><ol><li>Execute transaction SA38</li><li>Execute report RC_COLLECT_ANALYSIS_DATA&#x00A0;</li></ol><h3 data-toc-skip class=\"section\" id=\"Cause\">Cause</h3><p>Millions of entries in table SRT_RTC_DATA</p><h3 data-toc-skip class=\"section\" id=\"Resolution\">Resolution</h3><p>If table SRT_RTC_DATA table contains too many entries, this will lead the report RC_COLLECT_ANALYSIS_DATA to stall.</p>\r\n<p>This table contains literally every runtime configuration entries that are created in the system. Basically every configuration that is in SOAMANAGER transaction.&#x00A0;</p>\r\n<p>There&#39;s a possibility that this table was<span style=\"background-color: rgb(var(--sn-tinymce_content-background,var(--now-form-field--background-color,var(--now-color_background--primary,var(--now-color--neutral-0,255,255,255))))); color: rgb(var(--sn-tinymce_content-text,var(--now-form-field--color,var(--now-color_text--primary,var(--now-color--neutral-18,22,27,28))))); font-size: 10pt;\"> involved in a system copy. This table&#39;s configuration entries shall not be copied. Probably you&#39;ll see entries in table SRT_RTC_DATA of some years ago of a </span>bunch of configurations that were created that were really old, and it was never considered, that these cannot and should not be copied during a system copy/refresh, causing the buildup.</p>\r\n<p>The only suitable option is that all currently existing, working and valid configurations should be manually copied. Then as the next step, you&#39;ll need to use an &#39;initialization&#39; report, SRT_WSP_INITIALIZE_WS_CFG (This report will clear and delete all the Web Service configurations in the system)</p>\r\n<p>For more information please see the note: <a href=\"https://me.sap.com/notes/2348042\" target=\"_blank\" rel=\"nofollow noopener noreferrer\">2348042 </a>- Web Service Configuration and system copy/refresh</p>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "SV-SCS-S4R (SAP Readiness Check)"}, {"Key": "Requires Action", "Value": "0"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON> (I820243)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON> (I820243)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0003358759/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0003358759/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0003358759/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0003358759/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0003358759/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0003358759/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0003358759/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0003358759/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0003358759/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}, "Validity": {"_label": "Products", "_columnNames": {"Product": "Products"}, "Items": [{"Product": "SAP Solution Manager 7.2"}]}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": [{"Key": "URF_QA1", "Title": "This article is inaccurate", "Description": ""}, {"Key": "URF_QA2", "Title": "This article is confusing", "Description": ""}, {"Key": "URF_QA3", "Title": "This article doesn't solve my problem", "Description": ""}, {"Key": "URF_QA4", "Title": "I do not like the feature described", "Description": ""}]}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}