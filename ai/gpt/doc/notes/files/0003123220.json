{"Request": {"Number": "0003123220", "LanguageSource": "EN", "LanguageTarget": "EN"}, "Response": {"_elapsedTime": 571, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000001759322021"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0003123220?language=E&token=CDCFEEA64516DC916DCA11D2BBBE468D"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0003123220", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0003123220/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "3123220"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 45}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Advance development"}, "Priority": {"_label": "Priority", "value": "Correction with medium priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "17.01.2024"}, "SAPComponentKey": {"_label": "Component", "value": "SV-SCS-S4R"}, "SAPComponentKeyText": {"_label": "Component", "value": "SAP Readiness Check"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Service", "value": "SV", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'SV*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Support Technology Cloud Services", "value": "SV-SCS", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'SV-SCS*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "SAP Readiness Check", "value": "SV-SCS-S4R", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'SV-SCS-S4R*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "3123220 - SAP Readiness Check for SAP Customer Experience Solutions"}, "LongText": {"_label": "Description", "value": "<h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>You are either considering or planning to move your SAP CRM system to SAP S/4HANA Services and/or Sales or SAP Customer Experience. As a result, you want to analyze your existing SAP CRM system with the SAP Readiness Check tool for SAP Customer Experience, to help scope and plan your project.</p>\r\n<p>SAP Readiness Check for SAP Customer Experience supports SAP CRM 7.0 and subsequent versions.</p>\r\n<p>This SAP Note provides guidance on how to implement and execute SAP Readiness Check for SAP Customer Experience and CRM solutions. Additionally, it provides answers to frequently asked questions.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>SAP Readiness Check; SAP S/4HANA; CRM;&#160;SAP Readiness Check for SAP Customer Experience solutions.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>To run SAP Readiness Check for SAP Customer Experience solutions, data collectors are required to gather statistical data and a limited set of configuration data from your system.</p>\r\n<p>The data collection framework and the associated collectors listed below are needed to implement this SAP Note 3123220:</p>\r\n<ul>\r\n<li>SAP CRM Usage Profile</li>\r\n<li>Initial Data Migration Profile</li>\r\n<li>Custom Code &#8211; BSP Enhancements</li>\r\n<li>Custom Code &#8211; ABAP Enhancements</li>\r\n<li>Enhanced Business Structures And Custom Tables</li>\r\n</ul>\r\n<p>The Interface analysis needs the installation of new APIs as required.</p>\r\n<ul>\r\n<li>To install these APIs, update the add-on <strong>ST-A/PI</strong> to the version <strong>01U* SP02 </strong>and implement SAP Note <a target=\"_blank\" href=\"/notes/3072059\">3072059</a>, or update it to the version<strong> 01U* SP03+</strong>.</li>\r\n<li>Your SAP_BASIS component should be on <strong>SAP_BASIS 7.00 SP0+</strong>, <strong>SAP_BASIS 7.02 SP15+</strong>, <strong>SAP_BASIS 731 SP12+</strong>, <strong>SAP_BASIS 740 or above</strong>.</li>\r\n<li>Please note that Web service discovery will not work with SAP_BASIS 7.00.</li>\r\n</ul>\r\n<p>The add-ons and active business functions need the correct maintenance planner configuration. You can refer to SAP Note <a target=\"_blank\" href=\"/notes/2408911\">2408911 </a>to define the system in the maintenance planner.</p>\r\n<p><strong>Note</strong>: Before you implement the related SAP Notes,&#160;we strongly recommend implementing the latest version of SAP Note&#160;<a target=\"_blank\" href=\"/notes/1668882\">1668882</a>&#160;and&#160;<a target=\"_blank\" href=\"/notes/2971435\">2971435</a>&#160;if they are relevant.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p>To install the data collection framework and the associated collectors, implement this SAP Note.</p>\r\n<p>This will install the data collection framework, which is controlled via report RC_CRM_COLLECT_ANALYSIS_DATA.</p>\r\n<p><span style=\"text-decoration: underline;\">Schedule the Data Collectors</span></p>\r\n<p>To schedulethe data collectors, proceed as follows:</p>\r\n<p>Launch the program&#160;RC_CRM_COLLECT_ANALYSIS_DATA&#160;via transaction SA38 in the productive client of the production system (or a recently updated QA or Pre-prod system) to download the check results by performing the following steps:</p>\r\n<ol start=\"1\">\r\n<li>Select the checks to be included in the analysis.</li>\r\n<li>Choose <em>Schedule Analysis</em> to schedule the master job to collect data.</li>\r\n<li>Once the data collection jobs finish, choose the <em>Download Analysis Data</em> option from within the program RC_CRM_COLLECT_ANALYSIS_DATA.</li>\r\n<li>You can review the content of the archive file. The data is presented in an XML format and can be manually masked if required for security purposes.</li>\r\n<li>Optional: To enable the integration analysis for the flat file interface type, please refer to <a target=\"_blank\" href=\"https://me.sap.com/notes/3322944\">SAP Note 3322944</a> - File interface discovery for SAP Readiness Check - incl. corrections for ST-A/PI 01V* SP01 or SP02.</li>\r\n</ol>\r\n<p><strong>Note</strong>: If you are using a recently updated QA or Pre-prod system, the figures displayed in the usage profile of the check related to usage of the system in the last year might be different from the ones in the Production system.</p>\r\n<p><span style=\"text-decoration: underline;\">Upload the Collected Data</span></p>\r\n<p>To upload the collected data, perform the following steps:</p>\r\n<ol start=\"1\">\r\n<li>Launch the landing page for the SAP Readiness Check cloud application&#160;(link:&#160;<a target=\"_blank\" href=\"https://me.sap.com/readinesscheck\">https://me.sap.com/readinesscheck</a>).</li>\r\n<li>Choose <em>Start New Analysis</em>.</li>\r\n<li>Provide a name for the analysis, including the prefix \"RCX-\", locate the ZIP file that was generated from the program RC_CRM_COLLECT_ANALYSIS_DATA, review and acknowledge the <em>Terms of Use</em> and <em>Disclaimer</em>, and then choose <em>Create</em>.</li>\r\n<li>After a short period of time, usually less than half an hour, the status of the analysis will change from <em>In Preparation</em> to <em>Available</em>. You will need to refresh your browser or choose <em>Refresh</em> to get the updated status.</li>\r\n<li>Once the analysis is <em>Available</em>, you can open the analysis to find an interactive dashboard where you can explore the results of the analysis. The <em>Learn More</em> side panel and the embedded help capabilities can assist you as you explore and start to capture the next steps.</li>\r\n<li>If you would like support in further analyzing your results, reach out to your SAP contact to schedule the following assessment:&#160;<a target=\"_blank\" href=\"https://www.sapstore.com/solutions/12430/CRM-Transformation-Assessment-Service-for-SAP-Customer-Experience\" title=\"https://www.sapstore.com/solutions/12430/CRM-Transformation-Assessment-Service-for-SAP-Customer-Experience\">https://www.sapstore.com/solutions/12430/CRM-Transformation-Assessment-Service-for-SAP-Customer-Experience</a>.</li>\r\n</ol>\r\n<p><strong>Note</strong>:&#160;Once the analysis has the status <em>Available</em>, anyone with the authorization can append information to the analysis (for example, the custom code analysis archive) by choosing <em>Update Analysis</em> at the top of the dashboard view.</p>\r\n<p><span style=\"text-decoration: underline;\">Further Technical Insight into What Is Collected</span></p>\r\n<p>By running the RC_CRM_COLLECT_ANALYSIS_DATA&#160;report, a ZIP file will be generated with the following name: RC_CRM_AnalysisData&lt;SID&gt;&lt;DATE&gt;. This compressed file contains the following XML files:</p>\r\n<ul>\r\n<li>rcx_abap_enhancements.xml, which contains technical information for all ABAP developments</li>\r\n<li>rcx_aet_fields.xml, which contains all business objects with custom fields added using AET</li>\r\n<li>rcx_bsp_enhancements.xml, which contains all BSP enhanced components as well as custom components</li>\r\n<li>rcx_cust_tables.xml, which contains all custom tables</li>\r\n<li>rcx_interface_bwe.xml, which contains all BW extractors</li>\r\n<li>rcx_interface_idoc.xml, which contains all available IDocs</li>\r\n<li>rcx_interface_bdoc.xml, which contains all available BDocs</li>\r\n<li>rcx_interface_slt.xml, which contains all&#160;SLT Replication</li>\r\n<li>rcx_interface_rfc.xml, which contains all Remote Function Calls</li>\r\n<li>rcx_interface_odata.xml, which contains all OData services</li>\r\n<li>rcx_interface_soap.xml, which contains all SOAP services</li>\r\n<li>rcx_usage_profile, which contains all the business functions activated or in use in the system</li>\r\n<li>rcx_data_migration.xml,&#160;which contains the number of records for each business functions&#160;&#160;&#160;</li>\r\n</ul>\r\n<p><strong>Note</strong>: No transactional or master data will be extracted from the source system. The data collectors will only count the number of records of the tables, but no data will collected.</p>\r\n<p><strong>Frequently Asked Questions?</strong></p>\r\n<p>1. What is SAP Readiness Check for SAP Customer Experience solutions?</p>\r\n<p>SAP Readiness Check for SAP Customer Experience solutions is a self-service solution to help customers assess the current state of an existing SAP CRM system in preparation for the move to other customer-centric SAP solutions, like CX.</p>\r\n<p>2. Is SAP Solution Manager required with SAP Readiness Check for SAP Customer Experience solutions?</p>\r\n<p>No, SAP Solution Manager is not required for the use of SAP Readiness for SAP Customer Experience solutions.</p>\r\n<p>It is not possible to remotely schedule the data collection job via SAP Solution Manager. SAP Readiness Check for SAP Customer Experience solutions intentionally only supports manual downloads and manual uploads.</p>\r\n<p>3. What authorization is required to perform SAP Readiness Check?</p>\r\n<p>The user ID executing the data collection job must have <em>Execute (16)</em> rights in the <em>Activity</em> authorization field within authorization object S_DEVELOP. In addition, they must have unrestricted OBJECT_MS access in authorization object SM_BPM_DET. Otherwise, the required reports and function modules will not complete successfully.</p>\r\n<p>If you have further questions or comments, please&#160;contact us at the following e-mail address:&#160;<a target=\"_blank\" href=\"mailto:<EMAIL>\"><EMAIL></a>.</p>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON> (I814543)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (I816000)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0003123220/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0003123220/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0003123220/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0003123220/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0003123220/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0003123220/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0003123220/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0003123220/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0003123220/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "3072059", "RefComponent": "SV-SMG-MON-BPM-MON", "RefTitle": "Interface discovery for ST-A/PI 01U* SP02 (version Oct 2021)", "RefUrl": "/notes/3072059"}, {"RefNumber": "3061414", "RefComponent": "SV-SMG-MON-BPM-DCM", "RefTitle": "Enabling extended integration impact analysis for SAP Readiness Check", "RefUrl": "/notes/3061414"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_BASIS", "From": "700", "To": "702", "Subsequent": ""}, {"SoftwareComponent": "SAP_BASIS", "From": "710", "To": "730", "Subsequent": ""}, {"SoftwareComponent": "SAP_BASIS", "From": "731", "To": "731", "Subsequent": ""}, {"SoftwareComponent": "SAP_BASIS", "From": "804", "To": "804", "Subsequent": ""}, {"SoftwareComponent": "SAP_BASIS", "From": "740", "To": "740", "Subsequent": ""}, {"SoftwareComponent": "SAP_BASIS", "From": "750", "To": "756", "Subsequent": ""}, {"SoftwareComponent": "SAP_BASIS", "From": "786", "To": "786", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": [{"SoftwareComponent": "SAP_BASIS", "NumberOfCorrin": 1, "URL": "/corrins/0003123220/41"}]}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 1, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}