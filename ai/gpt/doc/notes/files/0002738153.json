{"Request": {"Number": "0002738153", "LanguageSource": "EN", "LanguageTarget": "EN"}, "Response": {"_elapsedTime": 187, "Error": {}, "SAPNote": {"_type": "00200720410000000235", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": false, "url": ""}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0002738153?language=E&token=B0FA5907EBD2D51AF228F852166181A2"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0002738153", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": false, "url": ""}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "2738153"}, "Type": {"_label": "Type", "value": "SAP Knowledge Base Article"}, "Version": {"_label": "Version", "value": 1}, "Recency": {"_label": "Recency", "key": "NEW", "value": "New"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Problem"}, "Priority": {"_label": "Priority", "value": "Normal"}, "Status": {"_label": "Release Status", "value": "Released to Customer"}, "ReleasedOn": {"_label": "Released On", "value": "15.01.2019"}, "SAPComponentKey": {"_label": "Component", "value": "BC-UPG-NA"}, "SAPComponentKeyText": {"_label": "Component", "value": "Note Assistant"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}]}, "Title": {"_label": "Title", "value": "2738153 - SAP Note 2399707: Implementation fails with \"Format of correction instructions XXX; unable to read corr. instruct.  Message no. SCWN409\""}, "LongText": {"_label": "Description", "value": "<h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<ul>\r\n<li>You are preparing to convert SAP ERP to SAP S/4HANA or upgrade SAP S/4HANA to a higher release</li>\r\n<li>As a pre-requisite step &#8220;Simplification Item Check&#8221;, you are prompted to implement the SAP Note 2399707</li>\r\n<li>When you implement this SAP Note in SNOTE you see the below error message:</li>\r\n</ul>\r\n<pre style=\"padding-left: 30px;\">Format of correction instructions 0020751258 212 0000092576 0092; unable to read corr. instruct.</pre>\r\n<pre style=\"padding-left: 30px;\">Message no. SCWN409</pre>\r\n<ul>\r\n<li>When you try to download the latest version of this SAP Note you&#160;see the below error message:</li>\r\n</ul>\r\n<pre style=\"padding-left: 30px;\">Object directory entry R3TR CLAS /SDF/CL_RC_CHK_UTILITY does not exist.&#160;</pre>\r\n<h3 data-toc-skip class=\"section\" id=\"Environment\">Environment</h3>\r\n<ul>\r\n<li>&#160;&#160;SAP S/4HANA</li>\r\n</ul>\r\n<h3 data-toc-skip class=\"section\" id=\"Reproducing the Issue\">Reproducing the Issue</h3>\r\n<ul>\r\n<li>Implement SAP Note&#160;2399707 using SNOTE</li>\r\n</ul>\r\n<h3 data-toc-skip class=\"section\" id=\"Cause\">Cause</h3>\r\n<p>This SAP Note was already implemented in the system. During the previous upgrade using the Software Update Manager (SUM), the objects delivered in this SAP Note were deleted. This is the reason this SAP Note is now inconsistent in the system.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Resolution\">Resolution</h3>\r\n<p>Transport the missing objects from another system in your landscape that is at the same Support Package (SP) level. This will restore the TADIR entries for the objects.</p>\r\n<p>If there is no other system available with same SP level, please create a ticket under BC-UPG-NA to reach out to SAP Product Support</p>\r\n<p>For more details on this issue, please check SAP Blog '<a target=\"_blank\" href=\"https://blogs.sap.com/2018/03/26/sap-s4hana-simplification-item-check-how-to-do-it-right./\">SAP S/4HANA Simplification Item Check &#8211; How to do it right.</a>', section -&#160;Common Issues and Solutions for Implementing the Simplification Item Check</p>\r\n<h3 data-toc-skip class=\"section\" id=\"See Also\">See Also</h3>\r\n<p><a target=\"_blank\" href=\"https://blogs.sap.com/2018/03/26/sap-s4hana-simplification-item-check-how-to-do-it-right./\" style=\"font-size: 14px;\">SAP S/4HANA Simplification Item Check &#8211; How to do it right.</a></p>\r\n<h3 data-toc-skip class=\"section\" id=\"Keywords\">Keywords</h3>\r\n<p>R3TR CLAS /SDF/CL_RC_SIMPLE_CHK, R3TR CLAS /SDF/CL_RC_SIMPLE_CHK_BUZ_FUNC, R3TR CLAS /SDF/CL_RC_SIMPLE_CHK_DB, R3TR CLAS /SDF/CL_RC_SIMPLE_CHK_EN_POINT, R3TR CLAS /SDF/CL_RC_SIMPLE_CHK_IDOC,&#160;R3TR CLAS /SDF/CL_RC_CHK_UTILITY</p>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "CA-TRS-PRCK (S/4HANA Conversion Pre-Checks)"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON><PERSON> (I857516)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON><PERSON> (I857516)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002738153/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002738153/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002738153/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002738153/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002738153/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002738153/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002738153/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002738153/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002738153/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "", "RefComponent": "", "RefTitle": "SAP S/4HANA Simplification Item Check – How to do it right.", "RefUrl": "https://blogs.sap.com/2018/03/26/sap-s4hana-simplification-item-check-how-to-do-it-right./"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}, "Validity": {"_label": "Products", "_columnNames": {"Product": "Products"}, "Items": [{"Product": "SAP S/4HANA 1610"}, {"Product": "SAP S/4HANA 1709"}, {"Product": "SAP S/4HANA 1809"}]}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": [{"Key": "URF_QA1", "Title": "This article is inaccurate", "Description": ""}, {"Key": "URF_QA2", "Title": "This article is confusing", "Description": ""}, {"Key": "URF_QA3", "Title": "This article doesn't solve my problem", "Description": ""}, {"Key": "URF_QA4", "Title": "I do not like the feature described", "Description": ""}]}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}