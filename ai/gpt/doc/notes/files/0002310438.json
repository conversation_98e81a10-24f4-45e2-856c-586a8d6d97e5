{"Request": {"Number": "0002310438", "LanguageSource": "EN", "LanguageTarget": "EN"}, "Response": {"_elapsedTime": 546, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000013649302017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0002310438?language=E&token=9BF2D77913E762347540B74028892116"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0002310438", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0002310438/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "2310438"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 86}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Program error"}, "Priority": {"_label": "Priority", "value": "Correction with medium priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "19.06.2020"}, "SAPComponentKey": {"_label": "Component", "value": "SV-SCS-S4R"}, "SAPComponentKeyText": {"_label": "Component", "value": "SAP Readiness Check"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Service", "value": "SV", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'SV*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Support Technology Cloud Services", "value": "SV-SCS", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'SV-SCS*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "SAP Readiness Check", "value": "SV-SCS-S4R", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'SV-SCS-S4R*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "2310438 - SAP Readiness Check for SAP S/4HANA - Managed System (Obsolete)"}, "LongText": {"_label": "Description", "value": "<h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p><strong>#################</strong></p>\r\n<p><strong>ATTENTION: SAP Readiness Check 2.0 is available as of May 17th 2019. If you have not yet started implementing the prerequisites so far, please refer to our new SAP Note <a target=\"_blank\" href=\"/notes/2913617\"><strong>2913617</strong>&#160;</a>for Readiness Check 2.0. SAP highly recommends using the new SAP Readiness Check 2.0 for you to benefit from new and improved functionalities. </strong></p>\r\n<p><strong>The note is obsolete. You cannot create SAP Readiness Check 1.0 analysis. </strong></p>\r\n<p><strong>If you have already implemented this SAP Note, deimplement this SAP Note and then follow SAP Note&#160;<a target=\"_blank\" href=\"/notes/2913617\"><strong>2913617</strong></a><strong>&#160;to prepare and run SAP Readiness Check 2.0 for SAP S/4HANA.</strong></strong></p>\r\n<p><strong>#################</strong></p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>S/4HANA; Readiness Check; Transition to SAP S/4HANA</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<div class=\"OutlineElement Ltr SCXW80366444\" style=\"font-size: 14px; display: inline !important;\">\r\n<p class=\"Paragraph SCXW80366444\" style=\"display: inline !important;\">To run SAP Readiness Check for SAP S/4HANA, follow SAP Note <a target=\"_blank\" href=\"/notes/2913617\"><strong>2913617</strong>&#160;</a>to prepare and run SAP Readiness Check 2.0 for SAP S/4HANA.</p>\r\n</div>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p><strong>Change log:&#160;</strong></p>\r\n<div class=\"OutlineElement Ltr SCXW10822204\">\r\n<div class=\"OutlineElement Ltr SCXW136177182\">\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td><strong>Version</strong></td>\r\n<td><strong>Change</strong></td>\r\n</tr>\r\n<tr>\r\n<td>85</td>\r\n<td>Update the note description</td>\r\n</tr>\r\n<tr>\r\n<td>79&#126;84</td>\r\n<td>Update to be a rerequiste note of SAP Note 2758146</td>\r\n</tr>\r\n<tr>\r\n<td>77&#126;78</td>\r\n<td>Provide the ability to clear buffered data</td>\r\n</tr>\r\n<tr>\r\n<td>76</td>\r\n<td>Enhance prequiste SAP Note check</td>\r\n</tr>\r\n<tr>\r\n<td>74&#126;75</td>\r\n<td>Bugfix for unknown parameter when using function module&#160;SHOW_JOBSTATE in a low BASIS version.</td>\r\n</tr>\r\n<tr>\r\n<td>72&#126;73</td>\r\n<td>Enhance log for Data Volume Management job.</td>\r\n</tr>\r\n<tr>\r\n<td>71</td>\r\n<td>Bugfix for&#160;collecting business process analytics data.</td>\r\n</tr>\r\n<tr>\r\n<td>69&#126;70</td>\r\n<td>Text change that unicode is not required to perform Readiness Check.</td>\r\n</tr>\r\n<tr>\r\n<td>65&#126;68</td>\r\n<td>\r\n<p>Improve performance, collect data in parallel.</p>\r\n<p>Allow user to schedule a job with specified date/time to run the checks.</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>60&#126;64</td>\r\n<td>Simplify the note instruction for easy setup.</td>\r\n</tr>\r\n<tr>\r\n<td>59&#126;60&#160;</td>\r\n<td>Extend waiting time for Data Volume Management job.&#160;</td>\r\n</tr>\r\n<tr>\r\n<td>58</td>\r\n<td>Use transaction usage data uploaded by report TMW_RC_MANAGE_ST03N_DATA. Refer to SAP Note&#160;<a target=\"_blank\" class=\"Hyperlink SCXW25438027\" href=\"/notes/0002568736\" rel=\"noreferrer\">2568736</a>.&#160;</td>\r\n</tr>\r\n<tr>\r\n<td>57</td>\r\n<td>\r\n<p>To prevent time-out, the necessary data is collected in a background job when you trigger a new analysis.</p>\r\n<p>You can download the analysis data once the job is finished. If you also perform simplification item check, you need to implement version 98 of SAP Note&#160;<a target=\"_blank\" class=\"Hyperlink SCXW206088939\" href=\"/notes/2399707\" rel=\"noreferrer\">2399707</a>&#160;first.</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>56</td>\r\n<td>Text change to&#160;run custom code&#160;analyzer&#160;SAP Note&#160;<a target=\"_blank\" class=\"Hyperlink SCXW209907339\" href=\"/notes/0002185390\" rel=\"noreferrer\">2185390</a>&#160;in the development system&#160;</td>\r\n</tr>\r\n<tr>\r\n<td>55</td>\r\n<td>Enhancement to support Data Volume Management (need to implement SAP Note&#160;<a target=\"_blank\" class=\"Hyperlink SCXW176343\" href=\"/notes/2612179\" rel=\"noreferrer\">2612179</a>&#160;in the managed system) and Business Warehouse Extractor analysis&#160;</td>\r\n</tr>\r\n<tr>\r\n<td>54</td>\r\n<td>Text change to HANA sizing data required SAP Note from&#160;<a target=\"_blank\" class=\"Hyperlink SCXW252694882\" href=\"/notes/0002462288\" rel=\"noreferrer\">2462288</a>&#160;to&#160;<a target=\"_blank\" class=\"Hyperlink SCXW252694882\" href=\"/notes/0002504480\" rel=\"noreferrer\">2504480</a>.</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>51 (mandatory</p>\r\n<p>required minimum version)</p>\r\n</td>\r\n<td>\r\n<ul class=\"BulletListStyle1 SCXW75703594\">\r\n<li class=\"OutlineElement Ltr SCXW75703594\">\r\n<p class=\"Paragraph SCXW75703594\">Harmonization&#160;of managed system&#160;prerequisite&#160;check and error handling.</p>\r\n</li>\r\n<li class=\"OutlineElement Ltr SCXW75703594\">\r\n<p class=\"Paragraph SCXW75703594\">The SAP Readiness Check application&#160;<a target=\"_blank\" class=\"Hyperlink SCXW75703594\" href=\"https://launchpad.support.sap.com/#readiness\" rel=\"noreferrer\">https://launchpad.support.sap.com/#readiness</a>&#160;has been updated on&#160;21.02.2018 and it is only compatible with version 51 or higher. If you still use the zip files generated by the old note version, you would have \"Upload fail\" error. Implement version 51 or higher to generate a new&#160;ZIP&#160;file.</p>\r\n</li>\r\n</ul>\r\n</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n</div>\r\n</div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "I075765"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON> (I323231)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0002310438/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002310438/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002310438/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002310438/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002310438/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002310438/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002310438/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002310438/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002310438/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2443236", "RefComponent": "SV-SMG-MON-BPM-ANA", "RefTitle": "SAP Readiness Check for SAP S/4HANA Business Process Improvement Content", "RefUrl": "/notes/2443236"}, {"RefNumber": "2399707", "RefComponent": "CA-TRS-PRCK", "RefTitle": "Simplification Item Check", "RefUrl": "/notes/2399707"}, {"RefNumber": "2367595", "RefComponent": "XX-SER-SIZING", "RefTitle": "Suite on HANA memory Sizing report - Advanced correction 7", "RefUrl": "/notes/2367595"}, {"RefNumber": "2290622", "RefComponent": "SV-SCS-S4R", "RefTitle": "SAP Readiness Check for SAP S/4HANA", "RefUrl": "/notes/2290622"}, {"RefNumber": "2262129", "RefComponent": "XX-SER-SIZING", "RefTitle": "Sizing of S/4 HANA cannot be triggered with /SDF/TRIGGER_HDB_SIZING", "RefUrl": "/notes/2262129"}, {"RefNumber": "2185390", "RefComponent": "BC-DWB-CEX", "RefTitle": "Custom Code Analyzer", "RefUrl": "/notes/2185390"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2748075", "RefComponent": "SV-SMG-MON-BPM-ANA", "RefTitle": "Message error \"Error while data collection\" in job log while running RC_VALUE_DISCOVERY_COLL_DATA", "RefUrl": "/notes/2748075 "}, {"RefNumber": "2700979", "RefComponent": "SV-SMG-DVM", "RefTitle": "SAP Notes 2612179 and 2611746 \"Cannot be implemented\" in SNOTE", "RefUrl": "/notes/2700979 "}, {"RefNumber": "2657737", "RefComponent": "BC-DWB-CEX", "RefTitle": "ZIP file not found after running report SYCM_DOWNLOAD_REPOSITORY_INFO", "RefUrl": "/notes/2657737 "}, {"RefNumber": "2816275", "RefComponent": "XX-SER-REL", "RefTitle": "SAP S/4HANA 1909 - application specific notes in system conversion / upgrade follow-on phase", "RefUrl": "/notes/2816275 "}, {"RefNumber": "2758146", "RefComponent": "SV-SCS-S4R", "RefTitle": "SAP Readiness Check for SAP S/4HANA & Process Discovery (evolution of SAP Business Scenario Recommendations) or SAP Innovation and Optimization Pathfinder", "RefUrl": "/notes/2758146 "}, {"RefNumber": "2290622", "RefComponent": "SV-SCS-S4R", "RefTitle": "SAP Readiness Check for SAP S/4HANA", "RefUrl": "/notes/2290622 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_BASIS", "From": "700", "To": "702", "Subsequent": ""}, {"SoftwareComponent": "SAP_BASIS", "From": "710", "To": "730", "Subsequent": ""}, {"SoftwareComponent": "SAP_BASIS", "From": "731", "To": "731", "Subsequent": ""}, {"SoftwareComponent": "SAP_BASIS", "From": "740", "To": "740", "Subsequent": ""}, {"SoftwareComponent": "SAP_BASIS", "From": "750", "To": "753", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": [{"SoftwareComponent": "SAP_BASIS", "NumberOfCorrin": 1, "URL": "/corrins/0002310438/41"}]}, "ManualActions": {"_label": "Manual Activities", "value": " <code>   <P><br/><br/>------------------------------------------------------------------------<br/>|Manual Activity&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>------------------------------------------------------------------------<br/>|VALID FOR&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>|Software Component&nbsp;&nbsp; SAP_BASIS&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;SAP Basis compo...|<br/>| Release 700&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;From SAPKB70004&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>| Release 710&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;All Support Package Levels&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;|<br/>| Release 711&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;From SAPKB71101&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>| Release 701&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;All Support Package Levels&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;|<br/>| Release 702&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;From SAPKB70201&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>| Release 730&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;From SAPKB73001&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>| Release 720&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;From SAPKB72002&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>| Release 731&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;From SAPKB73101&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>| Release 740&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;All Support Package Levels&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;|<br/>| Release 750&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;All Support Package Levels&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;|<br/>------------------------------------------------------------------------<br/><br/><B>Caution</B>: You have to perform this manual activity separately in each system into which you transport the Note for  implementation.<br/><br/>In the system where the custom code data is collected; download and implement latest version of SAP Note 2185390 first.<br/></P> <br/>  </code> "}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 1, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 1, "state": "Error"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}