{"Request": {"Number": "0003373112", "LanguageSource": "EN", "LanguageTarget": "EN"}, "Response": {"_elapsedTime": 176, "Error": {}, "SAPNote": {"_type": "00200720410000000235", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": false, "url": ""}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0003373112?language=E&token=B56A511509ED9D3EE68732B2CF16E38A"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0003373112", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0003373112/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "3373112"}, "Type": {"_label": "Type", "value": "SAP Knowledge Base Article"}, "Version": {"_label": "Version", "value": 2}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Problem"}, "Priority": {"_label": "Priority", "value": "Normal"}, "Status": {"_label": "Release Status", "value": "Released to Customer"}, "ReleasedOn": {"_label": "Released On", "value": "01/30/2024"}, "SAPComponentKey": {"_label": "Component", "value": "SV-SCS-S4R"}, "SAPComponentKeyText": {"_label": "Component", "value": "SAP Readiness Check"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}]}, "Title": {"_label": "Title", "value": "3373112 - SAP Note 3341267 is missing or prerequisites are not up to date when running RC_COLLECT_ANALYSIS_DATA"}, "LongText": {"_label": "Description", "value": "<h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><ul><li>Executing readiness check RC_COLLECT_ANALYSIS_DATA selecting only Financial Data Quality (FDQ) option show below warning message:<br />\r\n<ul><li><span style=\"color: #e03e2d;\"><em><span style=\"font-weight: bold;\">SAP Note 3341267 is missing or prerequisites are not up to date</span></em></span></li></ul>\r\n</li><li>SAP Note <a href=\"https://me.sap.com/notes/3341267\" target=\"_blank\" rel=\"noopener noreferrer nofollow\">3341267</a> is not applicable on the system</li></ul><h3 data-toc-skip class=\"section\" id=\"Environment\">Environment</h3><p>SAP ERP</p><h3 data-toc-skip class=\"section\" id=\"Reproducing the Issue\">Reproducing the Issue</h3><ol><li>SE38</li><li>run RC_COLLECT_ANALYSIS_DATA with following options:\r\n<ol><li>Target: SAP S/4HANA 2022 02</li><li>On FDQ, select General Ledger</li></ol>\r\n</li><li>On the bottom, see reported message &#34;SAP Note <a href=\"https://me.sap.com/notes/3341267\" target=\"_blank\" rel=\"noopener noreferrer nofollow\">3341267</a> is missing or prerequisites are not up to date&#34;</li></ol><h3 data-toc-skip class=\"section\" id=\"Cause\">Cause</h3><p>SAP Note is missing on SNOTE</p><h3 data-toc-skip class=\"section\" id=\"Resolution\">Resolution</h3><p>Validity of SAP Note has been extended so this message should not happen anymore. Follow steps below for solution:</p>\r\n<ol><li>Using SNOTE, download <strong>latest version</strong> of SAP Note <a href=\"https://me.sap.com/notes/3341267\" target=\"_blank\" rel=\"noopener noreferrer nofollow\">3341267</a></li><li>Implement SAP Note</li><li>On SE38, run RC_COLLECT_ANALYSIS_DATA with FDQ again</li></ol>\r\n<p><strong>Keep in mind:</strong></p>\r\n<ul><li>Validity of a SAP Note can only be checked by downloading the SAP Note or if the note data was transported</li><li><span style=\"background-color: rgb(var(--sn-tinymce_content-background,var(--now-form-field--background-color,var(--now-color_background--primary,var(--now-color--neutral-0,255,255,255))))); color: rgb(var(--sn-tinymce_content-text,var(--now-form-field--color,var(--now-color_text--primary,var(--now-color--neutral-18,22,27,28)))));\">Usually Test an Prod systems are not opened for note download so a transport is usually required. In cases when SAP Note cannot be implemented, it does not create a transport request. For these cases where there is no Transport Request created because SAP Note cannot be implemented, method described in the SAP Note </span><a href=\"https://me.sap.com/notes/1788379\" target=\"_blank\" rel=\"noopener noreferrer nofollow\">1788379</a><span style=\"background-color: rgb(var(--sn-tinymce_content-background,var(--now-form-field--background-color,var(--now-color_background--primary,var(--now-color--neutral-0,255,255,255))))); color: rgb(var(--sn-tinymce_content-text,var(--now-form-field--color,var(--now-color_text--primary,var(--now-color--neutral-18,22,27,28)))));\"> (using report SCWN_TRANSPORT_NOTES) can be helpful</span></li></ul><h3 data-toc-skip class=\"section\" id=\"See Also\">See Also</h3><ul><li>SAP Note <a href=\"https://me.sap.com/notes/3341267\" target=\"_blank\" rel=\"noopener noreferrer nofollow\">3341267</a> - Reconciliation prior to S/4HANA Conversion: Error &#34;Ledger Group Does Not Exist&#34; occurred while reconciling financial data</li><li>SAP Note <a href=\"https://me.sap.com/notes/1788379\" target=\"_blank\" rel=\"noopener noreferrer nofollow\">1788379</a> - Transport of SAP NOTES</li></ul>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Requires Action", "Value": "0"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON><PERSON> (I827751)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON><PERSON> (I827751)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0003373112/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0003373112/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0003373112/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0003373112/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0003373112/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0003373112/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0003373112/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0003373112/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0003373112/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}, "Validity": {"_label": "Products", "_columnNames": {"Product": "Products"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": [{"Key": "URF_QA1", "Title": "This article is inaccurate", "Description": ""}, {"Key": "URF_QA2", "Title": "This article is confusing", "Description": ""}, {"Key": "URF_QA3", "Title": "This article doesn't solve my problem", "Description": ""}, {"Key": "URF_QA4", "Title": "I do not like the feature described", "Description": ""}]}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}