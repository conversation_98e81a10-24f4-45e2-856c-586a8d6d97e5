{"Request": {"Number": "2584804", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 280, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000000319802018"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0002584804?language=E&token=CD62DED88644150F3391CBCCC9AE9826"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0002584804", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0002584804/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "2584804"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 4}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Consulting"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "04.03.2020"}, "SAPComponentKey": {"_label": "Component", "value": "BC-DB-ORA"}, "SAPComponentKeyText": {"_label": "Component", "value": "Oracle"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Basis Components", "value": "BC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Database Interface, Database Platforms", "value": "BC-DB", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-DB*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Oracle", "value": "BC-DB-ORA", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-DB-ORA*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "2584804 - FAQ: Oracle SQL Patch"}, "LongText": {"_label": "Description", "value": "<h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<ol>\r\n<li><a target=\"_self\" href=\"#ch01\">What is Oracle SQL Patch?</a></li>\r\n<li><a target=\"_self\" href=\"#ch02\">What is the mechanism of Oracle SQL Patch?</a></li>\r\n<li><a target=\"_self\" href=\"#ch03\">What is the advantage and disadvantage of Oracle SQL Patch, especially in comparison with Oracle SQL Plan Baseline (Note 1776485)?</a></li>\r\n<li><a target=\"_self\" href=\"#ch04\">How can we check if Oracle SQL Patch already exists?</a></li>\r\n<li><a target=\"_self\" href=\"#ch05\">How to manipulate Oracle SQL Patch? (Create/Alter/Drop/Transport)</a></li>\r\n<li><a target=\"_self\" href=\"#ch06\">How to specify a full hint including query block name?</a></li>\r\n<li><a target=\"_self\" href=\"#ch07\">Where to find an example to apply Oracle SQL Patch?</a></li>\r\n<li><a target=\"_self\" href=\"#ch08\">Do we need additional licenses or the SQL diag pack to use Oracle SQL Patch?</a></li>\r\n</ol>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>Oracle SQL Patch, Oracle SQL Plan Baseline</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p><strong><a target=\"_blank\" name=\"ch01\"></a>&#65279;1. What is Oracle SQL Patch?</strong></p>\r\n<p>Oracle SQL Patch (referred to as \"SQL Patch\" in the rest of this Note for the sake of brevity) is a way to fix the execution plan of a particular SQL statement with the help of a \"full hint\" (See question 6 for detailed explanation of \"full hint\").</p>\r\n<p>With the help of SQL Patch, it is possible to guide Oracle optimizer to a desired optimal execution plan without the need to change the application source code or other common tuning methods such as statistics manipulation.</p>\r\n<p>Furthermore, the \"full hint\" specified in the SQL Patch would overwrite the \"normal hint\" in the SQL statement. Therefore, the SQL Patch provides the possibility to fix a suboptimal hint in the source code without modifying it.</p>\r\n<p><strong><a target=\"_blank\" name=\"ch02\"></a>&#65279;2. What is the mechanism of Oracle SQL Patch?</strong></p>\r\n<p>Normalized SQL text (i.e. SQL text without extra space and line breaks) is used as a unique identifier to an SQL Patch. During parsing, if an SQL Patch is found based on its normalized SQL text, the full hint specified in the SQL Patch will be applied so that the desired execution plan will be used.</p>\r\n<p><strong><a target=\"_blank\" name=\"ch03\"></a>&#65279;3. What is the advantage and disadvantage of Oracle SQL Patch, especially in comparison with Oracle SQL Plan Baseline (Note <a target=\"_blank\" href=\"/notes/1776485\">1776485</a>)?</strong></p>\r\n<p>Oracle SQL Plan Baseline (referred to as \" SQL Plan Baseline \" in the rest of this Note for the sake of brevity) has already provided similar function to freeze the execution plan of one SQL statement. However, one limitation of the SQL Plan Baseline is that it is anchored to both the normalized SQL text and an existing execution plan.</p>\r\n<p>For example, if we need to tune one SQL statement with</p>\r\n<p>SQL _ID = bad_sql_id <br />SQL text = original_text<br />plan_hash_value = bad_plan</p>\r\n<p>we need to add a hint,&#160; execute the hinted statement so that a good execution plan exists in the cursor cache:</p>\r\n<p>SQL _ID = good_sql_id <br />SQL text = hinted_text<br />plan_hash_value = good_plan</p>\r\n<p>Then the below procedure can be used to create a SQL Plan Baseline:</p>\r\n<p>dbms_spm.load_plans_from_cursor_cache<br />( sql_id=&gt;'bad_sql_id', <br />&#160; plan_hash_value=&gt;'good_plan',<br />&#160; sql_text=&gt;'original_text',<br />&#160; fixed=&gt;'YES');</p>\r\n<p>It can be seen that the SQL Plan Baseline is nothing but to bind one execution plan to a normalized SQL text. The first obvious restriction of SQL Plan Baseline is that one good execution plan needs to be physically existing in the cursor cache which is sometimes not so practical: e.g. we need to execute the good statement with literals at least once.</p>\r\n<p>To execute the statement with literals actually leads to the second potential problem with SQL Plan Baseline: using the literals may sometimes generate a slightly different execution plan in comparison with the original statement using bind variable. <br />Check the below SQL statement and its execution plan as an example:</p>\r\n<p>SELECT * FROM T WHERE A BETWEEN :A0 AND :A1;&#160; <br />&#160;&#160; <br />SELECT STATEMENT (plan_hash_value = xxx)&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; <br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; <br />&#160; FILTER&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; <br />&#160;&#160;&#160; Filter predicates: :A0&lt;=:A1</p>\r\n<p>&#160;&#160;&#160; TABLE ACCESS BY INDEX ROWID (T)&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; <br />&#160;&#160;&#160;&#160;&#160;&#160; <br />&#160;&#160;&#160;&#160;&#160;&#160;&#160; INDEX RANGE SCAN (T&#126;1)</p>\r\n<p>If we execute this statement with literals, the execution plan will change:</p>\r\n<p>SELECT * FROM T WHERE A BETWEEN 'A' AND 'D';&#160; <br />&#160;&#160; <br />SELECT STATEMENT (plan_hash_value = yyy)</p>\r\n<p>&#160;&#160;&#160; TABLE ACCESS BY INDEX ROWID (T)&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; <br />&#160;&#160;&#160;&#160;&#160;&#160; <br />&#160;&#160;&#160;&#160;&#160;&#160;&#160; INDEX RANGE SCAN (T&#126;1)</p>\r\n<p>We can notice that the FILTER step disappears because Oracle is aware of the relationship of literals 'A' and 'D' so that the FILTER is no longer required.<br />If now we use SQL Plan Baseline to bind plan_hash_value = yyy to the original statement text, it will fail because the two execution plans are incompatible.</p>\r\n<p>On the other hand, SQL Patch is more flexible because it is not anchored with one existing execution plan. As long as the full hint specified in the SQL Patch is working as design (can be proved by a simple EXPLAIN PLAN without actually executing the statement), the optimal execution plan will be acquired.</p>\r\n<p>One disadvantage of SQL Patch is that it needs a full hint rather than a normal hint (Note 772497). Such full hint is not so straightforward and tends to be problematic without proper testing. Please refer to question 6 for more details.</p>\r\n<p>In summary, below table shows the advantage and disadvantage of SQL Patch in comparison with SQL Plan Baseline:</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td>&nbsp;</td>\r\n<td>Advantage</td>\r\n<td>Disadvantage</td>\r\n</tr>\r\n<tr>\r\n<td>SQL Plan Baseline</td>\r\n<td>No need to specify full hint</td>\r\n<td>Bonded to existing execution plan therefore not so flexible</td>\r\n</tr>\r\n<tr>\r\n<td>SQL Patch</td>\r\n<td>Anchored only to normalized SQL text therefore more flexible</td>\r\n<td>Need to understand the syntax of full hint</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><strong><a target=\"_blank\" name=\"ch04\"></a>&#65279;4. How can we check if Oracle SQL Patch already exists?</strong></p>\r\n<p>Please use script SQL_ProfilesBaselinesAndPatches_11g+.txt from Note <a target=\"_blank\" href=\"/notes/1438410\">1438410 </a>to list all existing SQL Patches. This script also display existing SQL Profile and SQL Plan Baselines. If you only want to show SQL Patches, please change the BASIS_INFO part in the script as below:</p>\r\n<p>( SELECT<br />&#160;&#160;&#160; -1 DBID,<br />&#160;&#160;&#160; -1 INSTANCE_NUMBER,&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; /* -2 for all instances, -1 for current instance */<br />&#160;&#160;&#160; ' ' DISPLAY_BASELINES,<br />&#160;&#160;&#160; ' ' DISPLAY_PROFILES,<br />&#160;&#160;&#160; 'X' DISPLAY_PATCHES,<br />&#160;&#160;&#160; 'ALL' DATA_SOURCE&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; /* AWR, CURRENT, ALL */<br />FROM<br />&#160; DUAL<br />)</p>\r\n<p><strong><a target=\"_blank\" name=\"ch05\"></a>&#65279;5. How to manipulate Oracle SQL Patch? (Create/Drop/Transport)</strong></p>\r\n<p>Before Oracle 12.2, SQL Patch can only be created by an undocumented procedure sys.dbms_sqldiag_internal.i_create_patch<br />As of Oracle 12.2, a standard procedure is available: dbms_sqldiag.create_sql_patch<br />Therefore depending on the Oracle version, different methods are explained below:</p>\r\n<p>1). CREATE SQL Patch<br />Run the below PL/SQL commands to create an SQL Patch.<br />Please modify the customizing section according to the comment.</p>\r\n<p><span style=\"text-decoration: underline;\">Before Oracle 12.2:</span></p>\r\n<p>declare<br />-- ### Customizing section start #################################################################<br />&#160;&#160; vName&#160;&#160;&#160;&#160;&#160;&#160;&#160; varchar2(30)&#160;&#160; :='&lt;SQL Patch Name&gt;';&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; /* Name of the SQL Patch */<br />&#160;&#160; vDescription varchar2(500)&#160; :='&lt;SQL Patch Description&gt;';&#160;&#160; /* Description of the SQL Patch */<br />&#160;&#160; vSQL_ID&#160;&#160;&#160;&#160;&#160; varchar2(13)&#160;&#160; :='&lt;SQL_ID&gt;';&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; /* SQL_ID of the statement */<br />&#160;&#160; vHintText&#160;&#160;&#160; varchar2(500)&#160; :='&lt;Hint Text&gt;';&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; /* The full hint text including query block name */<br />&#160;&#160; vStatus&#160;&#160;&#160;&#160;&#160; varchar2(8)&#160;&#160;&#160; :='ENABLED';&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; /* ENABLED or DISABLED */<br />-- ### Customizing section end ###################################################################<br />&#160;&#160; vSQLText CLOB;<br />begin<br />&#160;&#160; begin<br />&#160;&#160;&#160;&#160; select sql_fulltext into vSQLText from gv$sql where sql_id=vSQL_ID and rownum &lt; 2;<br />&#160;&#160; exception<br />&#160;&#160;&#160;&#160; when others then<br />&#160;&#160;&#160;&#160;&#160;&#160; select sql_text into vSQLText from dba_hist_sqltext where sql_id=vSQL_ID and rownum &lt; 2;<br />&#160;&#160; end;<br />&#160;&#160; vDescription := vHintText||' '||vdescription;<br />&#160;&#160; sys.dbms_sqldiag_internal.i_create_patch(<br />&#160;&#160;&#160;&#160; sql_text&#160;&#160;&#160; =&gt; vSQLText,<br />&#160;&#160;&#160;&#160; hint_text&#160;&#160; =&gt; vHintText,<br />&#160;&#160;&#160;&#160; description =&gt; vDescription,<br />&#160;&#160;&#160;&#160; name&#160;&#160;&#160;&#160;&#160;&#160;&#160; =&gt; vName);<br />&#160;&#160; dbms_sqldiag.alter_sql_patch(vName,'STATUS',vStatus);<br />end;<br />/</p>\r\n<p><span style=\"text-decoration: underline;\">As of Oracle 12.2:</span></p>\r\n<p>declare<br />-- ### Customizing section start #################################################################<br />&#160; &#160;vName&#160; &#160; &#160; &#160; &#160;varchar2(30)&#160; &#160;:='&lt;SQL Patch Name&gt;'; /* Name of the SQL Patch */<br />&#160; &#160;vDescription varchar2(500) :='&lt;SQL Patch Description&gt;'; /* Description of the SQL Patch */<br />&#160; &#160;vSQL_ID&#160; &#160; &#160; varchar2(13)&#160; &#160;:='&lt;SQL_ID&gt;'; /* SQL_ID of the statement */<br />&#160; &#160;vHintText&#160; &#160; &#160;varchar2(500) :='&lt;Hint Text&gt;'; /* The full hint text including query block name */<br />&#160; &#160;vStatus&#160; &#160; &#160; &#160; varchar2(8)&#160; &#160; :='ENABLED'; /* ENABLED or DISABLED */<br />-- ### Customizing section end ###################################################################<br />&#160;&#160;&#160;vNameOutput varchar2(30);<br />begin<br />&#160; &#160;vDescription := vHintText||' '||vdescription;<br />&#160; &#160;vNameOutput := dbms_sqldiag.create_sql_patch(<br />&#160; &#160; &#160; sql_id =&gt; vSQL_ID,<br />&#160; &#160; &#160; hint_text =&gt; vHintText,<br />&#160; &#160; &#160; description =&gt; vDescription,<br />&#160; &#160; &#160; name =&gt; vName);<br />&#160; &#160;dbms_sqldiag.alter_sql_patch(vName,'STATUS',vStatus);<br />end;<br />/</p>\r\n<p>2). DROP SQL Patch</p>\r\n<p>begin<br />&#160; dbms_sqldiag.drop_sql_patch(name =&gt; '&lt;SQL Patch Name&gt;');<br />end;<br />/</p>\r\n<p>3). Transport SQL Patch</p>\r\n<p>First create a staging table to store the SQL patch via:<br />DBMS_SQLDIAG.CREATE_STGTAB_SQLPATCH</p>\r\n<p>On the source system, use procedure DBMS_SQLDIAG.PACK_STGTAB_SQLPATCH to pack all SQL Patches into the staging table.</p>\r\n<p>Export/Import the staging table from the source system to the target system.</p>\r\n<p>On the target system, use procedure DBMS_SQLDIAG.UNPACK_STGTAB_SQLPATCH to unpack SQL Patches from the staging table to the data dictionary.</p>\r\n<p>Please refer to Oracle online document for more detailed explanation: <a target=\"_blank\" href=\"https://docs.oracle.com/en/database/oracle/oracle-database/12.2/arpls/DBMS_SQLDIAG.html\">https://docs.oracle.com/en/database/oracle/oracle-database/12.2/arpls/DBMS_SQLDIAG.html</a></p>\r\n<p><strong><a target=\"_blank\" name=\"ch06\"></a>&#65279;6. How to specify a full hint including query block name?</strong></p>\r\n<p>Check the below example, there is one SQL statement that we want to tune via SQL Patch:</p>\r\n<p>SELECT * FROM T100 WHERE SPRSL = 'D'; (SQL_ID = acy6tksk27ct5)</p>\r\n<p>It is important to show its execution plan with format = '+OUTLINE +ALIAS', where OUTLINE will show the correct syntax for a full hint and ALIAS will show the query block name.</p>\r\n<p>SELECT * FROM TABLE(DBMS_XPLAN.DISPLAY_CURSOR(SQL_ID =&gt; 'acy6tksk27ct5', FORMAT =&gt; '+OUTLINE +ALIAS'));</p>\r\n<p>SQL_ID&#160; acy6tksk27ct5, child number 0<br />-------------------------------------<br />SELECT * FROM T100 WHERE SPRSL = 'D'<br />&#160;<br />Plan hash value: 2922349212<br />&#160;<br />--------------------------------------------------------------------------<br />| Id&#160; | Operation&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; | Name | Rows&#160; | Bytes | Cost (%CPU)| Time&#160;&#160;&#160;&#160; |<br />--------------------------------------------------------------------------<br />|&#160;&#160; 0 | SELECT STATEMENT&#160; |&#160;&#160;&#160;&#160;&#160; |&#160;&#160;&#160;&#160;&#160;&#160; |&#160;&#160;&#160;&#160;&#160;&#160; |&#160;&#160; 638 (100)|&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; |<br />|*&#160; 1 |&#160; TABLE ACCESS FULL| T100 |&#160;&#160; 181K|&#160;&#160;&#160; 10M|&#160;&#160; 638&#160;&#160; (1)| 00:00:01 |<br />--------------------------------------------------------------------------</p>\r\n<p>Query Block Name / Object Alias (identified by operation id):<br />-------------------------------------------------------------<br />&#160;<br />&#160;&#160; 1 - SEL$1 / <a target=\"_blank\" href=\"mailto:T100@SEL$1\">T100@SEL$1</a><br />&#160;<br />Outline Data<br />-------------<br />&#160;<br />&#160; /*+<br />&#160;&#160;&#160;&#160;&#160; BEGIN_OUTLINE_DATA<br />&#160;&#160;&#160;&#160;&#160; IGNORE_OPTIM_EMBEDDED_HINTS<br />&#160;&#160;&#160;&#160;&#160; OPTIMIZER_FEATURES_ENABLE('12.1.0.2')<br />&#160;&#160;&#160;&#160;&#160; DB_VERSION('12.1.0.2')<br />&#160;&#160;&#160;&#160;&#160; ALL_ROWS<br />&#160;&#160;&#160;&#160;&#160; OUTLINE_LEAF(@\"SEL$1\")<br />&#160;&#160;&#160;&#160;&#160; FULL(@\"SEL$1\" <a target=\"_blank\" href=\"mailto:%22T100%22@%22SEL$1\">\"T100\"@\"SEL$1</a>\")<br />&#160;&#160;&#160;&#160;&#160; END_OUTLINE_DATA<br />&#160; */<br />&#160;<br />Predicate Information (identified by operation id):<br />---------------------------------------------------<br />&#160;<br />&#160;&#160; 1 - filter(\"SPRSL\"='D')</p>\r\n<p>The alias section shows the so called \"query block name\" and table alias. E.g. step 1 has query block name = SEL$1 and table T100 has alias = <a target=\"_blank\" href=\"mailto:T100@SEL$1\">T100@SEL$1</a>. The table alias is normally constructed by &lt;TABLE_NAME&gt;@&lt;QUERY_BLOCK_NAME&gt;</p>\r\n<p>The outline section shows the format of the full hint. E.g. here full table scan is used, so we see the hint looks like:</p>\r\n<p>FULL(@\"SEL$1\" <a target=\"_blank\" href=\"mailto:%22T100%22@%22SEL$1\">\"T100\"@\"SEL$1</a>\")</p>\r\n<p>We could notice that the syntax of a full hint is different from the normal hint as described in Note 772497. Use the hint FULL as an example:<br />Normal hint: FULL(&lt;TABLE_NAME&gt;)<br />Full hint: FULL(@&lt;QUERY_BLOCK_NAME&gt; &lt;TABLE_ALIAS&gt;)<br />The first query block name can also be omitted if there is only one query block existing in the execution plan or the query block name is the same in the table alias (the query block name can be different in the table alias if a view is accessed or the statement is much more complicated), in another word below full hint is also valid:<br />FULL(&lt;TABLE_ALIAS&gt;)<br />Example: FULL(<a target=\"_blank\" href=\"mailto:T100@SEL$1\">T100@SEL$1</a>)</p>\r\n<p>Similarly, some commonly used hints are listed as below:</p>\r\n<p>Choose particular index:<br />Normal hint: INDEX(&lt;TABLE_NAME&gt; &lt;INDEX_NAME&gt;)<br />Full hint: INDEX(&lt;TABLE_ALIAS&gt; &lt;INDEX_NAME&gt;)<br />Example: INDEX(<a target=\"_blank\" href=\"mailto:T100@SEL$1\">T100@SEL$1</a> \"T100&#126;1\")</p>\r\n<p>Specify join order:<br />Normal hint: LEADING(&lt;T1&gt;) USE_NL(&lt;T2&gt;) <br />Full hint: LEADING(&lt;T1_ALIAS&gt;) USE_NL(&lt;T2_ALIAS&gt;)<br />Example: LEADING(<a target=\"_blank\" href=\"mailto:T1@SEL$1\">T1@SEL$1</a>) USE_NL(<a target=\"_blank\" href=\"mailto:T2@SEL$1\">T2@SEL$1</a>)</p>\r\n<p>Specify join order in a view:<br />Normal hint: LEADING(&lt;VIEW_NAME&gt;.&lt;T1_ALIAS_IN_VIEW&gt;) USE_HASH(&lt;VIEW_NAME&gt;.&lt;T2_ALIAS_IN_VIEW&gt;) <br />Full hint: LEADING(@&lt;QUERY_BLOCK_NAME&gt; &lt;T1_ALIAS&gt;) USE_HASH(@&lt;QUERY_BLOCK_NAME&gt; &lt;T2_ALIAS&gt;)<br />Example: LEADING(@\"SEL$F5BB74E1\" <a target=\"_blank\" href=\"mailto:%22T0002%22@%22SEL$2\">\"T0002\"@\"SEL$2</a>\") USE_HASH(@\"SEL$F5BB74E1\" <a target=\"_blank\" href=\"mailto:%22T0001%22@%22SEL$2\">\"T0001\"@\"SEL$2</a>\")</p>\r\n<p>In summary, to write the full hint correctly, follow the below steps:<br />1). Show the execution plan of a statement via DBMS_XPLAN.DISPLAY (use&#160;DISPLAY_CURSOR if the plan is&#160;available in the cursor cache or use DISPLAY_AWR if the&#160;plan is available in AWR)&#160;with FORMAT set to '+OUTLINE +ALIAS' so that the query block name and table alias is shown.</p>\r\n<p>2). Write the full hint just like the normal hint but bear in mind the below rule:<br />2a). Add query block name in the beginning, even though it is allowed to omit the query block name under certain circumstances, it is advisable to always include it.<br />2b). Replace the &lt;TABLE_NAME&gt; in the normal hint with the &lt;TABLE_ALIAS&gt;; Index name is not effected, i.e. use the normal index name.</p>\r\n<p><strong><a target=\"_blank\" name=\"ch07\"></a>&#65279;7.&#160;Where to find an example to apply Oracle SQL Patch?</strong></p>\r\n<p>Here we show an example how to use SQL Patch to change the execution plan of a particular SQL statement.</p>\r\n<p>1). For example we have a statement like below, which is executed by user SAPSR3:</p>\r\n<p>SELECT * FROM T100VV;<br /><br />where T100VV is a view which has the following definition:<br /><br />CREATE VIEW T100VV<br />&#160; &#160;(ARBGB,<br />&#160; &#160;MSGNR,<br />&#160; &#160;SPRSL,<br />&#160; &#160;MASTERLANG,<br />&#160; &#160;NTEXT )<br />AS SELECT<br />&#160; &#160;T0001.\"ARBGB\",<br />&#160; &#160; T0002.\"MSGNR\",<br />&#160; &#160; T0002.\"SPRSL\",<br />&#160; &#160; T0001.\"MASTERLANG\",<br />&#160; &#160; T0002.\"TEXT\"<br />FROM<br />&#160; &#160; \"T100A\" T0001,<br />&#160; &#160; \"T100\" T0002<br />WHERE<br />&#160; &#160; &#160;T0001.\"ARBGB\" = T0002.\"ARBGB\"<br /><br />This statement has SQL_ID = '441c8jxn4qq9v'.<br /><br />As described above, we first show the execution plan of this statement with additional information:<br /><br />SELECT * FROM TABLE(DBMS_XPLAN.DISPLAY_CURSOR(SQL_ID =&gt; '441c8jxn4qq9v', FORMAT =&gt; '+OUTLINE +ALIAS'));<br /><br />SQL_ID&#160; 441c8jxn4qq9v, child number 0<br />-------------------------------------<br />SELECT * FROM T100VV<br /><br />Plan hash value: 228665427<br /><br />----------------------------------------------------------------------------<br />| Id&#160; | Operation&#160; &#160; &#160; &#160; &#160; | Name&#160; | Rows&#160; | Bytes | Cost (%CPU)| Time&#160; &#160; &#160;|<br />----------------------------------------------------------------------------<br />|&#160; &#160;0 | SELECT STATEMENT&#160; &#160;|&#160; &#160; &#160; &#160;|&#160; &#160; &#160; &#160;|&#160; &#160; &#160; &#160;|&#160; &#160;445 (100)|&#160; &#160; &#160; &#160; &#160; |<br />|*&#160; 1 |&#160; HASH JOIN&#160; &#160; &#160; &#160; &#160;|&#160; &#160; &#160; &#160;|&#160; &#160;359K|&#160; &#160; 24M|&#160; &#160;444&#160; &#160;(1)| 00:00:01 |<br />|&#160; &#160;2 |&#160; &#160;TABLE ACCESS FULL| T100A |&#160; 2516 | 32708 |&#160; &#160; &#160;7&#160; &#160;(0)| 00:00:01 |<br />|&#160; &#160;3 |&#160; &#160;TABLE ACCESS FULL| T100&#160; |&#160; &#160;359K|&#160; &#160; 20M|&#160; &#160;437&#160; &#160;(1)| 00:00:01 |<br />----------------------------------------------------------------------------<br /><br />Query Block Name / Object Alias (identified by operation id):<br />-------------------------------------------------------------<br /><br />&#160; &#160;1 - SEL$F5BB74E1<br />&#160; &#160;2 - SEL$F5BB74E1 / T0001@SEL$2<br />&#160; &#160;3 - SEL$F5BB74E1 / T0002@SEL$2<br /><br />Outline Data<br />-------------<br /><br />&#160; /*+<br />&#160; &#160; &#160; BEGIN_OUTLINE_DATA<br />&#160; &#160; &#160; IGNORE_OPTIM_EMBEDDED_HINTS<br />&#160; &#160; &#160; OPTIMIZER_FEATURES_ENABLE('12.2.0.1')<br />&#160; &#160; &#160; DB_VERSION('12.2.0.1')<br />&#160; &#160; &#160; OPT_PARAM('query_rewrite_enabled' 'false')<br />&#160; &#160; &#160; OPT_PARAM('_optim_peek_user_binds' 'false')<br />&#160; &#160; &#160; OPT_PARAM('_optimizer_extended_cursor_sharing_rel' 'none')<br />&#160; &#160; &#160; OPT_PARAM('_optimizer_adaptive_cursor_sharing' 'false')<br />&#160; &#160; &#160; OPT_PARAM('_optimizer_use_feedback' 'false')<br />&#160; &#160; &#160; OPT_PARAM('_px_adaptive_dist_method' 'off')<br />&#160; &#160; &#160; OPT_PARAM('_optimizer_batch_table_access_by_rowid' 'false')<br />&#160; &#160; &#160; OPT_PARAM('_optimizer_strans_adaptive_pruning' 'false')<br />&#160; &#160; &#160; OPT_PARAM('_optimizer_reduce_groupby_key' 'false')<br />&#160; &#160; &#160; OPT_PARAM('_optimizer_nlj_hj_adaptive_join' 'false')<br />&#160; &#160; &#160; OPT_PARAM('optimizer_index_cost_adj' 20)<br />&#160; &#160; &#160; OPT_PARAM('_fix_control' '6120483:0 6972291:1 6399597:1 7324224:0<br />&#160; &#160; &#160; &#160; &#160; &#160; &#160; 6430500:1 5099019:1 9495669:1 9196440:1 8937971:1 6055658:0 13627489:1<br />&#160; &#160; &#160; &#160; &#160; &#160; &#160; 14255600:1 14595273:1 14846352:0 18405517:2 20355502:8 20636003:0<br />&#160; &#160; &#160; &#160; &#160; &#160; &#160; 22540411:1 22746853:1 23197730:1 25643889:1 23738304:1 23738553:1')<br />&#160; &#160; &#160; ALL_ROWS<br />&#160; &#160; &#160; OUTLINE_LEAF(@\"SEL$F5BB74E1\")<br />&#160; &#160; &#160; MERGE(@\"SEL$2\" &gt;\"SEL$1\")<br />&#160; &#160; &#160; OUTLINE(@\"SEL$1\")<br />&#160; &#160; &#160; OUTLINE(@\"SEL$2\")<br />&#160; &#160; &#160; FULL(@\"SEL$F5BB74E1\" \"T0001\"@\"SEL$2\")<br />&#160; &#160; &#160; FULL(@\"SEL$F5BB74E1\" \"T0002\"@\"SEL$2\")<br />&#160; &#160; &#160; LEADING(@\"SEL$F5BB74E1\" \"T0001\"@\"SEL$2\" \"T0002\"@\"SEL$2\")<br />&#160; &#160; &#160; USE_HASH(@\"SEL$F5BB74E1\" \"T0002\"@\"SEL$2\")<br />&#160; &#160; &#160; END_OUTLINE_DATA<br />&#160; */<br /><br />Predicate Information (identified by operation id):<br />---------------------------------------------------<br /><br />&#160; &#160;1 - access(\"T0001\".\"ARBGB\"=\"T0002\".\"ARBGB\")</p>\r\n<p>It can be seen that the execution plan is to use T100A as the driven table and then hash join to table T100.</p>\r\n<p>2). Now let us test to swap the join order of this statement via an SQL Patch, i.e. start the join with table T100 first</p>\r\n<p>According to the template provided in section 5/1). use the below command to create an SQL Patch.<br />The most important part is the full hint text, please compare the hint below with the output above to see how the join order is swapped.</p>\r\n<p>declare<br />-- ### Customizing section start #################################################################<br />&#160; &#160;vName&#160; &#160; &#160; &#160; varchar2(30)&#160; &#160;:='Test on View T100VV';&#160; &#160; &#160; &#160; &#160; /* Name of the SQL Patch */<br />&#160; &#160;vDescription varchar2(500)&#160; :='Change the join order on view T100VV';&#160; &#160;/* Description of the SQL Patch */<br />&#160; &#160;vSQL_ID&#160; &#160; &#160; varchar2(13)&#160; &#160;:='441c8jxn4qq9v';&#160; &#160; &#160; &#160; &#160; &#160; &#160; &#160; &#160; /* SQL_ID of the statement */<br />&#160; &#160;vHintText&#160; &#160; varchar2(100)&#160; :='LEADING(@\"SEL$F5BB74E1\" \"T0002\"@\"SEL$2\") USE_HASH(@\"SEL$F5BB74E1\" \"T0001\"@\"SEL$2\")';&#160; /* The full hint text including query block name */<br />&#160; &#160;vStatus&#160; &#160; &#160; varchar2(8)&#160; &#160; :='ENABLED';&#160; &#160; &#160; &#160; &#160; &#160; &#160; &#160; &#160; &#160;/* ENABLED or DISABLED */<br />-- ### Customizing section end ###################################################################<br />&#160; &#160;vSQLText CLOB;<br />&#160; &#160;vNameOutput varchar2(30);<br />begin<br />&#160; &#160;vDescription := vHintText||' '||vdescription;<br />&#160; &#160;vNameOutput := dbms_sqldiag.create_sql_patch(<br />&#160; &#160; &#160;sql_id&#160; &#160; &#160; =&gt; vSQL_ID,<br />&#160; &#160; &#160;hint_text&#160; &#160;=&gt; vHintText,<br />&#160; &#160; &#160;description =&gt; vDescription,<br />&#160; &#160; &#160;name&#160; &#160; &#160; &#160; =&gt; vName);<br />&#160; &#160;dbms_sqldiag.alter_sql_patch(vName,'STATUS',vStatus);<br />end;<br />/<br /><br />PL/SQL procedure successfully completed.</p>\r\n<p>We could check the status of the newly created SQL patch as below:</p>\r\n<p>SELECT NAME,SQL_TEXT,DESCRIPTION,STATUS FROM DBA_SQL_PATCHES WHERE NAME LIKE '%T100VV%';<br /><br />--------------------------------------------------------------------------------------------------------------------------------------------------------------------------<br />|NAME&#160; &#160; &#160; &#160; &#160; &#160; &#160; &#160;|&#160; &#160; &#160; &#160; &#160; &#160; SQL_TEXT|DESCRIPTION&#160; &#160; &#160; &#160; &#160; &#160; &#160; &#160; &#160; &#160; &#160; &#160; &#160; &#160; &#160; &#160; &#160; &#160; &#160; &#160; &#160; &#160; &#160; &#160; &#160; &#160; &#160; &#160; &#160; &#160; &#160; &#160; &#160; &#160; &#160; &#160; &#160; &#160; &#160; &#160; &#160; &#160; &#160; &#160; &#160; &#160; &#160; &#160; &#160; &#160; &#160; &#160; &#160; &#160; |STATUS |<br />--------------------------------------------------------------------------------------------------------------------------------------------------------------------------<br />|Test on View T100VV|SELECT * FROM T100VV|LEADING(@\"SEL$F5BB74E1\" \"T0002\"@\"SEL$2\") USE_HASH(@\"SEL$F5BB74E1\" \"T0001\"@\"SEL$2\") Change the join order on view T100VV|ENABLED|<br />--------------------------------------------------------------------------------------------------------------------------------------------------------------------------<br /><br />3). Now let us check the execution plan again:</p>\r\n<p>EXPLAIN PLAN FOR SELECT * FROM T100VV;<br /><br />SELECT * FROM TABLE(DBMS_XPLAN.DISPLAY(FORMAT =&gt; '+OUTLINE +ALIAS'));<br /><br />PLAN_TABLE_OUTPUT<br />---------------------------------------------------------------------------------------------------<br />Plan hash value: 100037336<br /><br />------------------------------------------------------------------------------------<br />| Id&#160; | Operation&#160; &#160; &#160; &#160; &#160; | Name&#160; | Rows&#160; | Bytes |TempSpc| Cost (%CPU)| Time&#160; &#160; &#160;|<br />------------------------------------------------------------------------------------<br />|&#160; &#160;0 | SELECT STATEMENT&#160; &#160;|&#160; &#160; &#160; &#160;|&#160; &#160;359K|&#160; &#160; 24M|&#160; &#160; &#160; &#160;|&#160; &#160;803&#160; &#160;(1)| 00:00:01 |<br />|*&#160; 1 |&#160; HASH JOIN&#160; &#160; &#160; &#160; &#160;|&#160; &#160; &#160; &#160;|&#160; &#160;359K|&#160; &#160; 24M|&#160; &#160; 24M|&#160; &#160;803&#160; &#160;(1)| 00:00:01 |<br />|&#160; &#160;2 |&#160; &#160;TABLE ACCESS FULL| T100&#160; |&#160; &#160;359K|&#160; &#160; 20M|&#160; &#160; &#160; &#160;|&#160; &#160;437&#160; &#160;(1)| 00:00:01 |<br />|&#160; &#160;3 |&#160; &#160;TABLE ACCESS FULL| T100A |&#160; 2516 | 32708 |&#160; &#160; &#160; &#160;|&#160; &#160; &#160;7&#160; &#160;(0)| 00:00:01 |<br />------------------------------------------------------------------------------------<br /><br />Query Block Name / Object Alias (identified by operation id):<br />-------------------------------------------------------------<br /><br />&#160; &#160;1 - SEL$F5BB74E1<br />&#160; &#160;2 - SEL$F5BB74E1 / T0002@SEL$2<br />&#160; &#160;3 - SEL$F5BB74E1 / T0001@SEL$2<br /><br />Outline Data<br />-------------<br /><br />&#160; /*+<br />&#160; &#160; &#160; BEGIN_OUTLINE_DATA<br />&#160; &#160; &#160; USE_HASH(@\"SEL$F5BB74E1\" \"T0001\"@\"SEL$2\")<br />&#160; &#160; &#160; LEADING(@\"SEL$F5BB74E1\" \"T0002\"@\"SEL$2\" \"T0001\"@\"SEL$2\")<br />&#160; &#160; &#160; FULL(@\"SEL$F5BB74E1\" \"T0001\"@\"SEL$2\")<br />&#160; &#160; &#160; FULL(@\"SEL$F5BB74E1\" \"T0002\"@\"SEL$2\")<br />&#160; &#160; &#160; OUTLINE(@\"SEL$2\")<br />&#160; &#160; &#160; OUTLINE(@\"SEL$1\")<br />&#160; &#160; &#160; MERGE(@\"SEL$2\" &gt;\"SEL$1\")<br />&#160; &#160; &#160; OUTLINE_LEAF(@\"SEL$F5BB74E1\")<br />&#160; &#160; &#160; ALL_ROWS<br />&#160; &#160; &#160; OPT_PARAM('_fix_control' '6120483:0 6972291:1 6399597:1 7324224:0 6430500:1<br />&#160; &#160; &#160; &#160; &#160; &#160; &#160; 5099019:1 9495669:1 9196440:1 8937971:1 6055658:0 13627489:1 14255600:1<br />&#160; &#160; &#160; &#160; &#160; &#160; &#160; 14595273:1 14846352:0 18405517:2 20355502:8 20636003:0 22540411:1<br />&#160; &#160; &#160; &#160; &#160; &#160; &#160; 22746853:1 23197730:1 25643889:1 23738304:1 23738553:1')<br />&#160; &#160; &#160; OPT_PARAM('optimizer_index_cost_adj' 20)<br />&#160; &#160; &#160; OPT_PARAM('_optimizer_nlj_hj_adaptive_join' 'false')<br />&#160; &#160; &#160; OPT_PARAM('_optimizer_reduce_groupby_key' 'false')<br />&#160; &#160; &#160; OPT_PARAM('_optimizer_strans_adaptive_pruning' 'false')<br />&#160; &#160; &#160; OPT_PARAM('_optimizer_batch_table_access_by_rowid' 'false')<br />&#160; &#160; &#160; OPT_PARAM('_px_adaptive_dist_method' 'off')<br />&#160; &#160; &#160; OPT_PARAM('_optimizer_use_feedback' 'false')<br />&#160; &#160; &#160; OPT_PARAM('_optimizer_adaptive_cursor_sharing' 'false')<br />&#160; &#160; &#160; OPT_PARAM('_optimizer_extended_cursor_sharing_rel' 'none')<br />&#160; &#160; &#160; OPT_PARAM('_optim_peek_user_binds' 'false')<br />&#160; &#160; &#160; OPT_PARAM('query_rewrite_enabled' 'false')<br />&#160; &#160; &#160; DB_VERSION('12.2.0.1')<br />&#160; &#160; &#160; OPTIMIZER_FEATURES_ENABLE('12.2.0.1')<br />&#160; &#160; &#160; IGNORE_OPTIM_EMBEDDED_HINTS<br />&#160; &#160; &#160; END_OUTLINE_DATA<br />&#160; */<br /><br />Predicate Information (identified by operation id):<br />---------------------------------------------------<br /><br />&#160; &#160;1 - access(\"T0001\".\"ARBGB\"=\"T0002\".\"ARBGB\")<br /><br />Note<br />-----<br />&#160; &#160;- SQL patch \"Test on View T100VV\" used for this statement<br /><br />We can see that a 'Note' is shown to indicate that our SQL Patch has take effect and also now the join order has changed as expected.</p>\r\n<p><span style=\"font-size: 14px;\"><br /></span><strong><a target=\"_blank\" name=\"ch08\"></a>&#65279;8. Do we need additional licenses or the SQL diag pack to use Oracle SQL Patch?</strong></p>\r\n<p>No additional licenses are needed to use SQL Repair Advisor or SQL patches. SQL Repair Advisor is available as part of Oracle Database Enterprise Edition.</p>\r\n<ul></ul>\r\n<ul></ul>\r\n<ul></ul>\r\n<ul></ul>\r\n<ul></ul>\r\n<ul></ul>\r\n<ul></ul>\r\n<ul></ul>\r\n<ul></ul>\r\n<ul></ul>\r\n<ul></ul>\r\n<ul></ul>\r\n<ul></ul>\r\n<ul></ul>\r\n<ul></ul>\r\n<ul></ul>\r\n<ul></ul>\r\n<ul></ul>\r\n<ul></ul>\r\n<ul></ul>\r\n<ul></ul>\r\n<ul></ul>\r\n<ul></ul>\r\n<ul></ul>\r\n<ul></ul>\r\n<ul></ul>\r\n<ul></ul>\r\n<ul></ul>\r\n<ul></ul>\r\n<ul></ul>\r\n<ul></ul>\r\n<ul></ul>\r\n<ul></ul>\r\n<ul></ul>\r\n<ul></ul>\r\n<ul></ul>\r\n<ul></ul>\r\n<ul></ul>\r\n<ul></ul>\r\n<ul></ul>\r\n<ul></ul>\r\n<ul></ul>\r\n<ul></ul>\r\n<ul></ul>\r\n<ul></ul>\r\n<ul></ul>\r\n<ul></ul>\r\n<ul></ul>\r\n<ul></ul>\r\n<ul></ul>\r\n<ul></ul>\r\n<ul></ul>\r\n<ul></ul>\r\n<ul></ul>\r\n<ul></ul>\r\n<ul></ul>\r\n<ul></ul>\r\n<ul></ul>\r\n<ul></ul>\r\n<ul></ul>\r\n<ul></ul>\r\n<ul></ul>\r\n<ul></ul>\r\n<ul></ul>\r\n<ul></ul>\r\n<ul></ul>\r\n<ul></ul>\r\n<ul></ul>\r\n<ul></ul>\r\n<ul></ul>\r\n<ul></ul>\r\n<ul></ul>\r\n<ul></ul>\r\n<ul></ul>\r\n<ul></ul>\r\n<ul></ul>\r\n<ul></ul>\r\n<ul></ul>\r\n<ul></ul>\r\n<ul></ul>\r\n<ul></ul>\r\n<ul></ul>\r\n<ul></ul>\r\n<ul></ul>\r\n<ul></ul>\r\n<ul></ul>\r\n<ul></ul>\r\n<ul></ul>\r\n<ul></ul>\r\n<ul></ul>\r\n<ul></ul>\r\n<ul></ul>\r\n<ul></ul>\r\n<ul></ul>\r\n<ul></ul>\r\n<ul></ul>\r\n<ul></ul>\r\n<ul></ul>\r\n<ul></ul>\r\n<ul></ul>\r\n<ul></ul>\r\n<ul></ul>\r\n<ul></ul>\r\n<ul></ul>\r\n<ul></ul>\r\n<ul></ul>\r\n<ul></ul>\r\n<ul></ul>\r\n<ul></ul>\r\n<ul></ul>\r\n<ul></ul>\r\n<ul></ul>\r\n<ul></ul>\r\n<ul></ul>\r\n<ul></ul>\r\n<ul></ul>\r\n<ul></ul>\r\n<ul></ul>\r\n<ul></ul>\r\n<ul></ul>\r\n<ul></ul>\r\n<ul></ul>\r\n<ul></ul>\r\n<ul></ul>\r\n<ul></ul>\r\n<ul></ul>\r\n<ul></ul>\r\n<ul></ul>\r\n<ul></ul>\r\n<ul></ul>\r\n<ul></ul>\r\n<ul></ul>\r\n<ul></ul>\r\n<ul></ul>\r\n<ul></ul>\r\n<ul></ul>\r\n<ul></ul>\r\n<ul></ul>\r\n<ul></ul>\r\n<ul></ul>\r\n<ul></ul>\r\n<ul></ul>\r\n<ul></ul>\r\n<ul></ul>\r\n<ul></ul>\r\n<ul></ul>\r\n<ul></ul>\r\n<ul></ul>\r\n<ul></ul>\r\n<ul></ul>\r\n<ul></ul>\r\n<ul></ul>\r\n<ul></ul>\r\n<ul></ul>\r\n<ul></ul>\r\n<ul></ul>\r\n<ul></ul>\r\n<ul></ul>\r\n<ul></ul>\r\n<ul></ul>\r\n<ul></ul>\r\n<ul></ul>\r\n<ul></ul>\r\n<ul></ul>\r\n<ul></ul>\r\n<ul></ul>\r\n<ul></ul>\r\n<ul></ul>\r\n<ul></ul>\r\n<ul></ul>\r\n<ul></ul>\r\n<ul></ul>\r\n<ul></ul>\r\n<ul></ul>\r\n<ul></ul>\r\n<ul></ul>\r\n<ul></ul>\r\n<ul></ul>\r\n<ul></ul>\r\n<ul></ul>\r\n<ul></ul>\r\n<ul></ul>\r\n<ul></ul>\r\n<ul></ul>\r\n<ul></ul>\r\n<ul></ul>\r\n<ul></ul>\r\n<ul></ul>\r\n<ul></ul>\r\n<ul></ul>\r\n<ul></ul>\r\n<ul></ul>\r\n<ul></ul>\r\n<ul></ul>\r\n<ul></ul>\r\n<ul></ul>\r\n<ul></ul>\r\n<ul></ul>\r\n<ul></ul>\r\n<ul></ul>\r\n<ul></ul>\r\n<ul></ul>\r\n<ul></ul>\r\n<ul></ul>\r\n<ul></ul>\r\n<ul></ul>\r\n<ul></ul>\r\n<ul></ul>\r\n<ul></ul>\r\n<ul></ul>\r\n<ul></ul>\r\n<ul></ul>\r\n<ul></ul>\r\n<ul></ul>\r\n<ul></ul>\r\n<ul></ul>\r\n<ul></ul>\r\n<ul></ul>\r\n<ul></ul>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p>.</p>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON><PERSON> (D053841)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON><PERSON> (D053841)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0002584804/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002584804/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002584804/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002584804/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002584804/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002584804/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002584804/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002584804/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002584804/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2889465", "RefComponent": "SV-PERF-DB-ORA", "RefTitle": "Long runtime of TMW_RC_BPA_DATA_COLL in SAP Readiness Check 2.0", "RefUrl": "/notes/2889465 "}, {"RefNumber": "2863382", "RefComponent": "BC-DB-ORA", "RefTitle": "How to create a correct Oracle SQL Patch", "RefUrl": "/notes/2863382 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": []}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}