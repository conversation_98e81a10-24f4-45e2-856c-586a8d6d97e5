{"Request": {"Number": "884809", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 392, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000015959142017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000884809?language=E&token=0CB851E2F394664B346E77E943C97D33"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000884809", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000884809/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "884809"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 3}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Help for error analysis"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "04.09.2006"}, "SAPComponentKey": {"_label": "Component", "value": "BC-DB-MSS"}, "SAPComponentKeyText": {"_label": "Component", "value": "SQL Server in SAP NetWeaver Products"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Basis Components", "value": "BC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Database Interface, Database Platforms", "value": "BC-DB", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-DB*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "SQL Server in SAP NetWeaver Products", "value": "BC-DB-MSS", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-DB-MSS*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "884809 - SQL Error 11 - General network error"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>Symptoms:<br /><br />1. Upgrade fails in phase SHADOW_IMPORT_INC<br />SHDALLIMP.ELG shows something like:<br />===================================<br />~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~<br />SHADOW IMPORT ERRORS and RETURN CODE in SAPKH60001.&lt;SID&gt;<br />~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~<br />&#x00A0;&#x00A0;2EETW000 sap_dext called with msgnr \"1\":<br />&#x00A0;&#x00A0;2EETW000&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;db call info<br />&#x00A0;&#x00A0;2EETW000 function:&#x00A0;&#x00A0; db_xrtab<br />&#x00A0;&#x00A0;2EETW000 fcode:&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;RT_MI_LOAD<br />&#x00A0;&#x00A0;2EETW000 tabname:&#x00A0;&#x00A0;&#x00A0;&#x00A0;ENHOBJCONTRACT~<br />&#x00A0;&#x00A0;2EETW000 len:&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;292<br />&#x00A0;&#x00A0;2EETW000 key:&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;DIMP_GENERAL_CL_COST_A<br />&#x00A0;&#x00A0;ACLASCL_COST_ESTIMATE<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;C005<br />&#x00A0;&#x00A0;2EETW000 retcode:&#x00A0;&#x00A0;&#x00A0;&#x00A0;1<br />&#x00A0;&#x00A0;1 ETP111 exit code&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; : \"12\"<br /><br />The log for the transport (SAPKH60001.&lt;SID&gt; for example) shows:<br />===============================================================<br />&#x00A0;&#x00A0;...<br />&#x00A0;&#x00A0;... ***LOG BY4=&gt;sql error 0 performing INS on table ENHOBJCONT<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;[dbtrtab#3 @ 3873]<br />&#x00A0;&#x00A0;... ***LOG BY0=&gt;[0] Unspecified error [dbtrtab#3 @ 3873]<br />&#x00A0;&#x00A0;2EETW000 sap_dext called with msgnr \"1\":<br />&#x00A0;&#x00A0;2EETW000&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;db call info<br />&#x00A0;&#x00A0;2EETW000 function:&#x00A0;&#x00A0; db_xrtab<br />&#x00A0;&#x00A0;2EETW000 fcode:&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;RT_MI_LOAD<br />&#x00A0;&#x00A0;2EETW000 tabname:&#x00A0;&#x00A0;&#x00A0;&#x00A0;ENHOBJCONTRACT~<br />&#x00A0;&#x00A0;2EETW000 len:&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;292<br />&#x00A0;&#x00A0;2EETW000 key:&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;DIMP_GENERAL_CL_COST_A&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;ACLASCL_COST_ESTIMA<br />&#x00A0;&#x00A0;2EETW000 retcode:&#x00A0;&#x00A0;&#x00A0;&#x00A0;1<br />&#x00A0;&#x00A0;...<br />&#x00A0;&#x00A0;... RollbackTx: line 7171. hr: 0x8000ffff [DBNETLIB][ConnectionRead<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;(WrapperRead()).]General network error. Check your network<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;documentation.<br />&#x00A0;&#x00A0;... sloledb.cpp [RollbackTx,line 7171]: Error/Message: (err 11,<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;sev 17), [DBNETLIB][ConnectionRead (WrapperRead()).]<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;General network error. Check your network documentation.<br />&#x00A0;&#x00A0;...<br /><br />The SQL Server ERRORLOG shows something like:<br />==============================================================<br />2005-09-29 15:55:08.25 spid102&#x00A0;&#x00A0; WARNING:&#x00A0;&#x00A0;Failed to reserve contiguous<br />2005-09-29 15:55:08.25 spid102&#x00A0;&#x00A0; Buffer Distribution:&#x00A0;&#x00A0;Stolen=2530 Free=<br />&#x00A0;&#x00A0;Inram=0 Dirty=1301 Kept=0<br />&#x00A0;&#x00A0;I/O=0, Latched=3468, Other=104804<br />2005-09-29 15:55:08.25 spid102&#x00A0;&#x00A0; Buffer Counts:&#x00A0;&#x00A0;Commited=116096 Target=<br />&#x00A0;&#x00A0;InternalReservation=3663 ExternalReservation=0 Min Free=128 Visible= 1<br />2005-09-29 15:55:08.25 spid102&#x00A0;&#x00A0; Procedure Cache:&#x00A0;&#x00A0;TotalProcs=2288 Total<br />2005-09-29 15:55:08.25 spid102&#x00A0;&#x00A0; Dynamic Memory Manager:&#x00A0;&#x00A0;Stolen=6405 OS<br />&#x00A0;&#x00A0;OS Committed=24674<br />&#x00A0;&#x00A0;OS In Use=24668<br />&#x00A0;&#x00A0;Query Plan=3841 Optimizer=0<br />&#x00A0;&#x00A0;General=2658<br />&#x00A0;&#x00A0;Utilities=7 Connection=23990<br />2005-09-29 15:55:08.25 spid102&#x00A0;&#x00A0; Global Memory Objects:&#x00A0;&#x00A0;Resource=2188 L<br />&#x00A0;&#x00A0;SQLCache=333 Replication=2<br />&#x00A0;&#x00A0;LockBytes=2 ServerGlobal=46<br />&#x00A0;&#x00A0;Xact=12<br />2005-09-29 15:55:08.25 spid102&#x00A0;&#x00A0; Query Memory Manager:&#x00A0;&#x00A0;Grants=0 Waiting<br />2005-09-29 15:55:08.26 spid102&#x00A0;&#x00A0; Error: 17803, Severity: 20, State: 12<br />2005-09-29 15:55:08.26 spid102&#x00A0;&#x00A0; Insufficient memory available..<br /> ==============================================================<br /><br /><br />2.&#x00A0;&#x00A0;The SQL Error 11 - general network error, can occur in the general operation of any SAP system running on SQL Server.&#x00A0;&#x00A0;It is important to check the SQL Server ERRORLOG in this case.&#x00A0;&#x00A0;If the error shown above is in the ERRORLOG, then apply the solution of this note.&#x00A0;&#x00A0;See also note 392892 for more information on dealing with SQL Error 11 which may be the symptom of a number of underlying problems.<br /><br /></p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>Contiguous memory SHADOW_IMPORT_INC network error</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>SQL Server can't allocate a sufficient amount of contiguous memory for a statement which processes a lot of data.<br /></p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>Set SQL Server command line option -g512.&#x00A0;&#x00A0;See books-on-line for more information.&#x00A0;&#x00A0; Proceed as follows:<br /><br />start SQL Server Enterprise Manager (Start-&gt;Programs-&gt;Microsoft SQ<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Server-&gt;Enterprise Manager)<br />open the Tree (left pane) till the appropriate server shows,<br />mark the server,<br />right mouse click,<br />&#x00A0;&#x00A0;choose properties<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; popup \"SQL Server Properties (Configure)\"<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;on register \"General\" click on \"Startup Parameters\"<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;in the popup \"Startup Parameters\"<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;enter the new parameter: -g512<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Click on Add<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;confirm (OK)<br /><br />This setting will take effect the next time you restart SQL Server.<br /><br />See also note 885827 which explains the underlying technical problem in detail.<br /><br /></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON><PERSON><PERSON> (I002675)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON> (C5024907)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000884809/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000884809/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000884809/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000884809/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000884809/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000884809/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000884809/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000884809/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000884809/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "961410", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info. on upgrading to SAP ECC 6.0 SR2 ABAP", "RefUrl": "/notes/961410"}, {"RefNumber": "913971", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info. on upgrading to SAP ERP Central Component 6.0 SR1", "RefUrl": "/notes/913971"}, {"RefNumber": "885827", "RefComponent": "BC-DB-MSS", "RefTitle": "SQL error 17803: Failed to reserve contiguous memory", "RefUrl": "/notes/885827"}, {"RefNumber": "826092", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info. on upgrading to SAP ERP Central Component 6.0", "RefUrl": "/notes/826092"}, {"RefNumber": "392892", "RefComponent": "BC-DB-MSS", "RefTitle": "Errors with connections to SQL Server databases", "RefUrl": "/notes/392892"}, {"RefNumber": "1553208", "RefComponent": "BC-DB-MSS", "RefTitle": "SQL error -1 with severity 0", "RefUrl": "/notes/1553208"}, {"RefNumber": "1292069", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info. on upgrading to SAP ERP 6.0 EHP4 SR1 ABAP", "RefUrl": "/notes/1292069"}, {"RefNumber": "1156968", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info. on upgrading to EHP 4 for SAP ERP 6.0 ABAP", "RefUrl": "/notes/1156968"}, {"RefNumber": "1088904", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info. on upgrading to SAP ECC 6.0 SR3 ABAP", "RefUrl": "/notes/1088904"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "826092", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info. on upgrading to SAP ERP Central Component 6.0", "RefUrl": "/notes/826092 "}, {"RefNumber": "1088904", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info. on upgrading to SAP ECC 6.0 SR3 ABAP", "RefUrl": "/notes/1088904 "}, {"RefNumber": "1156968", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info. on upgrading to EHP 4 for SAP ERP 6.0 ABAP", "RefUrl": "/notes/1156968 "}, {"RefNumber": "1292069", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info. on upgrading to SAP ERP 6.0 EHP4 SR1 ABAP", "RefUrl": "/notes/1292069 "}, {"RefNumber": "885827", "RefComponent": "BC-DB-MSS", "RefTitle": "SQL error 17803: Failed to reserve contiguous memory", "RefUrl": "/notes/885827 "}, {"RefNumber": "392892", "RefComponent": "BC-DB-MSS", "RefTitle": "Errors with connections to SQL Server databases", "RefUrl": "/notes/392892 "}, {"RefNumber": "1553208", "RefComponent": "BC-DB-MSS", "RefTitle": "SQL error -1 with severity 0", "RefUrl": "/notes/1553208 "}, {"RefNumber": "913971", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info. on upgrading to SAP ERP Central Component 6.0 SR1", "RefUrl": "/notes/913971 "}, {"RefNumber": "961410", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info. on upgrading to SAP ECC 6.0 SR2 ABAP", "RefUrl": "/notes/961410 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": []}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}