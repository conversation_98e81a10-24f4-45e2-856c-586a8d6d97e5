{"Request": {"Number": "50088", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 1811, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000014455952017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000050088?language=E&token=182379D6448E0A9C1A6EA0688F0405E1"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000050088", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000050088/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "50088"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 42}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Installation information"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "05.07.2006"}, "SAPComponentKey": {"_label": "Component", "value": "BC-DB-ORA"}, "SAPComponentKeyText": {"_label": "Component", "value": "Oracle"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Basis Components", "value": "BC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Database Interface, Database Platforms", "value": "BC-DB", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-DB*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Oracle", "value": "BC-DB-ORA", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-DB-ORA*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "50088 - Creating OPS$ users on Windows NT/Oracle"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>Creating OPS$ users on Windows NT/Oracle<br /></p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>SQL*NetV2<br />SQL*NetV1<br />OPS$<br />ops$<br />Password change<br />Changing the password<br />OPS$ user<br /></p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>OPS$ users should be created on the ORACLE database for R/3 users SAPService&lt;SAPSID&gt; and &lt;SAPSID&gt;ADM.<br /></p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>You can use the ORACLE OPS$ user mechanism to assign a password other than 'sap' to the user 'sapr3' in the SAP System. This prevents unauthorized access to the database, as only the operating system users of the SAP System (normally SAPService &lt;SAPSID&gt; and &lt;SAPSID&gt;ADM) have access to the password of user 'sapr3' in the database.<br /><br />Furthermore, the OPS$ mechanism is required for backup planning using transaction DB13 in CCMS.<br /><br />In Windows NT SQL*Net V1, this mechanism only works in central systems.<br />With the ORACLE network protocol SQL*Net V2, you can use the OPS$ user mechanism in both central and distributed SAP Systems. However, if you use the OPS$ in distributed systems, the following parameter must be set in the file init&lt;SAPSID&gt;.ora.<br />\"remote_os_authent=TRUE\" (not Oracle 8.*)<br /><br />To set up SQL*Net V2, refer to Note 48736.<br /><br />Contents<br />Ia&#x00A0;&#x00A0;Creating OPS$ users (up to Release 4.0B)<br />Ib&#x00A0;&#x00A0;Creating OPS$ users (as of Release 4.0B)<br />Ic&#x00A0;&#x00A0;Creating OPS$ users (as of Oracle 8.1.6)<br />Id&#x00A0;&#x00A0;Creating OPS$ users in mixed environments (as of 8.1.6)<br />IIa Changing the password for user 'sapr3' (&lt; Oracle 8.1.6)<br />IIb Changing the password for user 'sapr3' (Oracle 8.1.6)<br />IIc Changing the password for user 'SAP&lt;SID&gt;'(&gt; Oracle 8.1.6)<br /><br /></p> <b>Ia&#x00A0;&#x00A0;Creating OPS$ users (up to Release 4.0B)</b><br /> <p><br />The OPS$ user has to be created for the following users:</p> <OL>1. A user who logs on to the database outside of the SAP system.</OL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;If you call programs which make a connection to the database via another user (for example, program \"tp\"), then you must create an OPS$ user for this user. In general, this is user &lt;SAPSID&gt;ADM. <OL>2. A user under which the SAP System is started.</OL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;You can find this specified user under \"Control Panel\" -&gt; \"Administrative Tools\" -&gt; \"Services\". <OL><OL>a) Select the service SAP&lt;SAPSID&gt;_&lt;Instance_ID&gt; from the service list.</OL></OL> <OL><OL>b) Choose \"Startup\". The specified user, under which the SAP system is started, is shown in the field \"Log on As\".</OL></OL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;In general, the SAP System is started under user SAPService&lt;SAPSID&gt;. <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Note that you may need to adapt the commands given below (How to create user &lt;SAPSID&gt;ADM) to the user that you are using. This might be the case, for example, if you installed the latest R/3 Version by repeated upgrades starting from a very old R/3 Release. <p><br />Please proceed as follows to create both OPS$ users (sample commands for user &lt;SAPSID&gt;ADM and SAPSERVICE&lt;SAPSID&gt;):<br /></p> <b>How to create user &lt;SAPSID&gt;ADM</b><br /> <OL>1. Stop the SAP System.</OL> <OL>2. Log on as user &lt;SAPSID&gt;ADM to the host on which the R/3 database for the SAP System is running.<br />All the following actions have to be carried out on this host.</OL><OL>3. Start sqldba72 (or svrmgr23, svrmgr30) and log on to the database with 'connect internal'.</OL> <OL>4. Execute the following commands in the database one after the other. Replace &lt;SAPSID&gt; with the corresponding SAP system ID for your system:</OL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;<br />'create user OPS$&lt;SAPSID&gt;ADM default tablespace psapuser1d<br /> temporary tablespace psaptemp identified externally;'<br /><br />'grant connect, resource to OPS$&lt;SAPSID&gt;ADM;'<br /><br />'connect /'<br /><br />'create table SAPUSER<br /> ( USERID VARCHAR2(256), PASSWD VARCHAR2 (256));'<br /><br />'insert into SAPUSER values ('SAPR3', '&lt;password&gt;');'<br /><br />'connect internal'<br /><br />'alter user sapr3 identified by &lt;password&gt;;'<br /></p> <b>How to create user SAPSERVICE&lt;SAPSID&gt;</b><br /> <OL>1. Log on as user &lt;SAPSID&gt;ADM to the host on which the R/3 database for the SAP System is running.</OL> <OL>2. Start sqldba72 (or svrmgr23, svrmgr30) and log on to the database with 'connect internal'.</OL> <OL>3. Execute the following commands in the database one after the other: Replace &lt;SAPSID&gt; with the corresponding SAP system ID for your system:</OL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;<br />'create user OPS$SAPSERVICE&lt;SAPSID&gt; default tablespace psapuser1d<br /> temporary tablespace psaptemp identified externally;'<br /><br />'grant connect, resource to OPS$SAPSERVICE&lt;SAPSID&gt;;'<br /><br />'create public synonym sapuser for OPS$&lt;SAPSID&gt;ADM.SAPUSER;'<br /><br />'connect /'<br /><br />'grant select on sapuser to OPS$SAPSERVICE&lt;SAPSID&gt;;'<br /><br />The R/3 processes can now log on with the new password after reading the password from table SAPUSER in the database.<br /></p> <b>Ib&#x00A0;&#x00A0;Creating OPS$ users (as of Release 4.0B)<br /></b><br /> <p>For the creation and change of OPS$ users SQL scripts are available as of SAP R/3 Version 4.0B under Oracle 8.0.6.<br />Oracle 8.0.6:&#x00A0;&#x00A0;Open a DOS BOX, and change to the directory \\orant\\orainst.<br />Oracle 8.1.5: the scripts are no longer automatically copied to Orainst during the Oracle installation and must therefore be copied manually from the RDMBS CD (directory \\SAP) to a writeable working directory.<br /><br />Execute the following command under user &lt;SID&gt;ADM:<br /><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;plus80 internal @sapuser.sql&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;(Oracle 8.0.6)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;sqlplus internal @sapuser.sql&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; (Oracle 8.1.5)<br />You will be prompted for a new password for the database user sapr3. After successful completion of sapuser.sql the following line is displayed:<br /><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;| Script sapuser.sql successfully finished |<br /><br />If this line is not displayed, check the file sapuser.log in the same directory for possible error causes and consult your SAP Support center, if necessary.<br />You can also use this procedure to change the password for the database user sapr3.<br /></p> <b>Ic&#x00A0;&#x00A0;Creating OPS$ users (as of Oracle 8.1.6)</b><br /> <p>On Oracle 8.1.6, security for OPS$ access has been enhanced by including the domain name in the OPS$ user names.<br />Creating the OPS$ user is done using script oradbusr.sql.<br />By mistake, there are two different scripts that share the same name but with a different number of parameters. With the older script, two parameters are used. With the new script, three parameters ('schemaowner' added) are used.<br />The old version with two parameters is contained on the RDBMS CD, on earlier Kernel CDs, and in the installation directory. A current version has also been appended to this note.<br />The new version with three parameters is contained in the new installation kits. The new version must be used as of Version 6.10 and in MCOD systems as of 4.6.<br />As of Oracle Version 10 use the script contained in archive oradbusr10.zip.<br />You can find out whether you have the new script or the old script installed as follows:<br />Open script 'oradbusr.sql' using a text editor.<br />If 'oradbusrSD.sql' is listed under 'Usage', you have the new version.<br />If 'oradbusr.sql' is listed, you have the old version.<br />Execute the following command to set up the OPS$ user:<br /><br />New script:<br />&#x00A0;&#x00A0; sqlplus /NOLOG @oradbusr.sql &lt;schemaowner&gt; NT &lt;userdomain&gt;<br />Old script:<br />&#x00A0;&#x00A0; sqlplus /NOLOG @oradbusr.sql NT &lt;userdomain&gt;<br /><br />Set the domain name of the domain for &lt;userdomain&gt; in which the user &lt;SID&gt;ADM was created. This usually corresponds to the value of the environment variable USERDOMAIN if you have logged on as &lt;SID&gt;ADM. You can find out what the current value of the environment variable is<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;set USER<br />in a command prompt.<br />For &lt;schemaowner&gt;, you may need to set a different value if you use the new oradbusr.sql script:<br />Up to Version 4.6D, use 'SAPR3' for 'schemaowner'.<br />As of Version 6.10, use 'SAP&lt;SID&gt;' for 'schemaowner'.<br />You can also use the environment variable to check which value you need to set for 'schemaowner':<br />If the environment variable was set to DBS_ORA_SCHEMA, use the value specified there (SAPR3 or SAP&lt;SID&gt;).<br />If DBS_ORA_SCHEMA was not set, use 'SAPR3'.<br /><br />Now you need to set the password for user sapr3 acc. to IIb or IIc.<br /></p> <b>Id Creating OPS$ users in mixed environments (as of 8.1.6)<br /></b><br /> <p>This section describes the creation of the OPS$ access in mixed environments, i.e. the Oracle client (R/3 Application Server) and the Oracle database are operated on different platforms (Unix / Windows).</p> <OL>1. Log on to the database server as user &lt;SID&gt;adm.</OL> <OL>2. Start sqlplus:<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;sqlplus /nolog<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;sqlplus&gt; connect / as sysdba</OL> <p></p> <UL><LI>If the database server runs on a Unix computer:<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;sqlplus&gt; create user \"OPS$&lt;DOMAIN&gt;\\SAPSERVICE&lt;SID&gt;\" identified<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;externally;<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;sqlplus&gt; grant connect, resource to<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;\"OPS$&lt;DOMAIN&gt;\\SAPSERVICE&lt;SID&gt;\";<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;sqlplus&gt; connect /<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;sqlplus&gt; grant select, insert, update on sapuser to<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;\"OPS$&lt;DOMAIN&gt;\\SAPSERVICE&lt;SID&gt;\"<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;sqlplus&gt; exit</LI></UL> <p></p> <UL><LI>If the database server runs on a Windows computer:<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;sqlplus&gt; create user OPS$&lt;SID&gt;ADM identified externally;<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;sqlplus&gt; grant connect, resource to OPS$&lt;SID&gt;ADM;<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;sqlplus&gt; connect /<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;sqlplus&gt; grant select, insert, update on sapuser to<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; OPS$&lt;SID&gt;ADM;<br /></LI></UL> <p>Note:<br />As a general rule, the user name and the domain of the Windows user, under which the SAP service was started on the NT computer, must be used. If the service is started with a local user, use the host name of the Application Server as the domain name.<br />Some Unix derivatives can only manage users with a user name length of 8 characters. In this case, unlike with the SAP standard, user &lt;SID&gt;adm was entered as service user. Likewise, user \"OPS$&lt;DOMAIN&gt;\\&lt;SID&gt;ADM\" must be registered in the Oracle database.<br />The OPS$ user names for the Windows users must be delimited by double quotes, since the character string contains the \"\\\" character. In this case, the user name must be written in uppercase letters as the case is observed when inserting character strings that are limited by quotes!<br /></p> <b>IIa&#x00A0;&#x00A0;Changing the password for the user 'sapr3'<br /></b><br /> <p>If the OPS$ user has already been created as described above, the password for the 'sapr3' user can be changed at any time. As user &lt;SAPSID&gt;ADM, you must follow two steps (replace &lt;new password&gt; with the new password for the user 'sapr3'):</p> <OL>1. Stop the SAP System.</OL> <OL>2. Log on as user &lt;SAPSID&gt;ADM to the host on which the R/3 database for the SAP System is running.<br />All the following actions have to be carried out on this host.</OL><OL>3. Change the entry for the user 'sapr3' in table SAPUSER.</OL> <OL><OL>a) Start sqldba72 (or svrmgr23, svrmgr30) and log on to the database with 'connect internal'.</OL></OL> <OL><OL>b) Execute an update on the table SAPUSER.<br />'update OPS$&lt;SAPSID&gt;ADM.SAPUSER set PASSWD='&lt;new password&gt;'<br /> where USERID='SAPR3';'<br /></OL></OL> <OL>4. Change the password for the 'sapr3' user the database with the following commands:</OL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;<br />'connect internal'<br /><br /> 'alter user sapr3 identified by &lt;new password&gt;;'<br /></p> <b>IIb Changing the password for user 'sapr3' (Oracle 8.1.6)<br /></b><br /> <p>To do this, call BRCONNECT as follows:<br /><br /><br />&#x00A0;&#x00A0;brconnect -u system/&lt;syst_pwd&gt; -f chpass -o sapr3 -p &lt;new_sap_pwd&gt;<br /><br />You do not need to carry out the steps under IIa.<br /></p> <b>IIc Changing the password for user 'schemaowner&gt;' (as of Oracle 8.1.7)<br /></b><br /> <p>Applies to MCOD systems or installations with kernel version &gt; 4.6D<br /><br /><br />&#x00A0;&#x00A0;brconnect -u system/&lt;syst_pwd&gt; -f chpass -o sap&lt;sid&gt; -p &lt;new_sap_pwd&gt;<br /><br />You do not need to carry out the steps under IIa.<br /></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "BC-INS-NT (SAP Netweaver based Solutions on Windows)"}, {"Key": "Database System", "Value": "ORACLE"}, {"Key": "Database System", "Value": "ORACLE  7.2"}, {"Key": "Transaction codes", "Value": "DB13"}, {"Key": "Transaction codes", "Value": "MCOD"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON>-<PERSON><PERSON><PERSON> (D029385)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D036707)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "J", "TranslationLanguage": "日本語", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000050088/J"}, {"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000050088/D"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000050088/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000050088/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000050088/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000050088/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000050088/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000050088/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000050088/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": [{"FileName": "oradbusr10.ZIP", "FileSize": "3", "MimeType": "application/x-zip-compressed", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=011000358700009293972001&iv_version=0042&iv_guid=2E04F3F1A543BD4E9FE1F821D13F1432"}, {"FileName": "ORADBUSR.TXT", "FileSize": "14", "MimeType": "text/plain", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=011000358700009293972001&iv_version=0042&iv_guid=C2AA4D4D51D68141B8779064363D0B54"}]}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "95719", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/95719"}, {"RefNumber": "93292", "RefComponent": "BC-UPG-RDM", "RefTitle": "Additional Info: Upgrading to 4.0A    ORACLE", "RefUrl": "/notes/93292"}, {"RefNumber": "89655", "RefComponent": "BC-DB-ORA", "RefTitle": "ORA-1004:\"default username feature not supported\"", "RefUrl": "/notes/89655"}, {"RefNumber": "8523", "RefComponent": "BC-DB-ORA-CCM", "RefTitle": "DB backups using CCMS do not work", "RefUrl": "/notes/8523"}, {"RefNumber": "575280", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add info about upgrade to SAP Web AS 6.20 ORACLE 9.2.0", "RefUrl": "/notes/575280"}, {"RefNumber": "491598", "RefComponent": "BC-UPG-RDM", "RefTitle": "Additional info on upgrading to SAP Web AS 6.20 ORACLE 8.1.x", "RefUrl": "/notes/491598"}, {"RefNumber": "437648", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "DB13: External program terminated with exit code 1/2", "RefUrl": "/notes/437648"}, {"RefNumber": "401721", "RefComponent": "BC-UPG-RDM", "RefTitle": "on upgrading to SAPWeb AS 6.10 ORACLE", "RefUrl": "/notes/401721"}, {"RefNumber": "400241", "RefComponent": "BC-DB-ORA", "RefTitle": "Problems with ops$ or sapr3 connect to Oracle", "RefUrl": "/notes/400241"}, {"RefNumber": "357194", "RefComponent": "BC-DB-ORA", "RefTitle": "ORA-00900 when importing SQL scripts", "RefUrl": "/notes/357194"}, {"RefNumber": "303461", "RefComponent": "BC-UPG-RDM", "RefTitle": "Additions to Upgrade to Basis 4.6D    Oracle", "RefUrl": "/notes/303461"}, {"RefNumber": "201874", "RefComponent": "BC-UPG-RDM", "RefTitle": "Additions for upgrade to 4.6C/4.6C SR1/4.6C SR2 Oracle", "RefUrl": "/notes/201874"}, {"RefNumber": "1911785", "RefComponent": "BC-DB-ORA", "RefTitle": "Password expiration date shown for OPS$ users in DBA_USERS", "RefUrl": "/notes/1911785"}, {"RefNumber": "1868094", "RefComponent": "BC-DB-ORA-SEC", "RefTitle": "Overview: Oracle Security SAP Notes", "RefUrl": "/notes/1868094"}, {"RefNumber": "186119", "RefComponent": "BC-DB-ORA", "RefTitle": "Restricting DB access to specific hosts", "RefUrl": "/notes/186119"}, {"RefNumber": "134592", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Import of SAPDBA role (sapdba_role.sql)", "RefUrl": "/notes/134592"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2469379", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "BRCONNECT hangs after system copy", "RefUrl": "/notes/2469379 "}, {"RefNumber": "1911785", "RefComponent": "BC-DB-ORA", "RefTitle": "Password expiration date shown for OPS$ users in DBA_USERS", "RefUrl": "/notes/1911785 "}, {"RefNumber": "1868094", "RefComponent": "BC-DB-ORA-SEC", "RefTitle": "Overview: Oracle Security SAP Notes", "RefUrl": "/notes/1868094 "}, {"RefNumber": "361641", "RefComponent": "BC-DB-ORA", "RefTitle": "Creating OPS$ users on UNIX", "RefUrl": "/notes/361641 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "KRNL32NUC", "From": "4.6DEXT", "To": "4.6DEX2", "Subsequent": ""}, {"SoftwareComponent": "KRNL32NUC", "From": "6.40", "To": "6.40EX2", "Subsequent": ""}, {"SoftwareComponent": "KRNL32NUC", "From": "7.00", "To": "7.01", "Subsequent": ""}, {"SoftwareComponent": "KRNL32NUC", "From": "7.10", "To": "7.20", "Subsequent": ""}, {"SoftwareComponent": "KRNL32NUC", "From": "7.20EXT", "To": "7.20EXT", "Subsequent": ""}, {"SoftwareComponent": "KRNL32NUC", "From": "7.21", "To": "7.21", "Subsequent": ""}, {"SoftwareComponent": "KRNL32NUC", "From": "7.21EXT", "To": "7.21EXT", "Subsequent": ""}, {"SoftwareComponent": "KRNL32UC", "From": "6.40", "To": "6.40EX2", "Subsequent": ""}, {"SoftwareComponent": "KRNL32UC", "From": "7.00", "To": "7.01", "Subsequent": ""}, {"SoftwareComponent": "KRNL32UC", "From": "7.10", "To": "7.20", "Subsequent": ""}, {"SoftwareComponent": "KRNL32UC", "From": "7.20EXT", "To": "7.20EXT", "Subsequent": ""}, {"SoftwareComponent": "KRNL32UC", "From": "7.21", "To": "7.21", "Subsequent": ""}, {"SoftwareComponent": "KRNL32UC", "From": "7.21EXT", "To": "7.21EXT", "Subsequent": ""}, {"SoftwareComponent": "KRNL64NUC", "From": "4.6DEXT", "To": "4.6DEX2", "Subsequent": ""}, {"SoftwareComponent": "KRNL64NUC", "From": "6.40", "To": "6.40EX2", "Subsequent": ""}, {"SoftwareComponent": "KRNL64NUC", "From": "7.00", "To": "7.01", "Subsequent": ""}, {"SoftwareComponent": "KRNL64NUC", "From": "7.10", "To": "7.20", "Subsequent": ""}, {"SoftwareComponent": "KRNL64NUC", "From": "7.20EXT", "To": "7.20EXT", "Subsequent": ""}, {"SoftwareComponent": "KRNL64NUC", "From": "7.21", "To": "7.21", "Subsequent": ""}, {"SoftwareComponent": "KRNL64NUC", "From": "7.21EXT", "To": "7.21EXT", "Subsequent": ""}, {"SoftwareComponent": "KRNL64NUC", "From": "7.22", "To": "7.22", "Subsequent": ""}, {"SoftwareComponent": "KRNL64NUC", "From": "7.22EXT", "To": "7.22EXT", "Subsequent": ""}, {"SoftwareComponent": "KRNL64UC", "From": "6.40", "To": "6.40EX2", "Subsequent": ""}, {"SoftwareComponent": "KRNL64UC", "From": "7.00", "To": "7.01", "Subsequent": ""}, {"SoftwareComponent": "KRNL64UC", "From": "7.10", "To": "7.20", "Subsequent": ""}, {"SoftwareComponent": "KRNL64UC", "From": "7.20EXT", "To": "7.20EXT", "Subsequent": ""}, {"SoftwareComponent": "KRNL64UC", "From": "7.21", "To": "7.21", "Subsequent": ""}, {"SoftwareComponent": "KRNL64UC", "From": "7.21EXT", "To": "7.21EXT", "Subsequent": ""}, {"SoftwareComponent": "KRNL64UC", "From": "7.22", "To": "7.22", "Subsequent": ""}, {"SoftwareComponent": "KRNL64UC", "From": "7.22EXT", "To": "7.22EXT", "Subsequent": ""}, {"SoftwareComponent": "KERNEL", "From": "4.6D", "To": "4.6D", "Subsequent": ""}, {"SoftwareComponent": "KERNEL", "From": "6.40", "To": "6.40", "Subsequent": ""}, {"SoftwareComponent": "KERNEL", "From": "7.00", "To": "7.01", "Subsequent": ""}, {"SoftwareComponent": "KERNEL", "From": "7.10", "To": "7.11", "Subsequent": ""}, {"SoftwareComponent": "KERNEL", "From": "7.20", "To": "7.22", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}