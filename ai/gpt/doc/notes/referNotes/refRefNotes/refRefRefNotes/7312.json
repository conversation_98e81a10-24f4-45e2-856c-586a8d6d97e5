{"Request": {"Number": "7312", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 7416, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "D", "_masterLanguage": "D", "_loadedLanguage": "D", "_machineTranslationLanguage": "E", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Acknowledge", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "If the quality of the machine translation is not sufficient for your purposes, click here to request a revised version.", "_type": "EVENT", "_visible": true}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": false, "url": ""}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000007312?language=E&token=6589C70F18B7160CD1565B333FDF6381"}, "ShareByEmail": {"_label": "Share via Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000007312", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000007312/D/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "7312"}, "Type": {"_label": "Type", "value": "SAP-Hinweis"}, "Version": {"_label": "Version", "value": 88}, "Recency": {"_label": "Currentness", "key": "", "value": ""}, "Language": {"_label": "Language", "value": "German"}, "MasterLanguage": {"_label": "Original Language", "value": "German"}, "Category": {"_label": "Category", "value": "Upgrade Info"}, "Priority": {"_label": "Priority", "value": "Recommendations/Additional Information"}, "Status": {"_label": "Release Status", "value": "To Be Archived"}, "ReleasedOn": {"_label": "Released On", "value": "09.02.2007"}, "SAPComponentKey": {"_label": "Component", "value": "SV-SMG-SER"}, "SAPComponentKeyText": {"_label": "Component", "value": "SAP Support Services"}, "SAPComponentPath": [{"_label": "Services &amp; Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs &amp; Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Service", "value": "SV", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'SV*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "SAP Solution Manager", "value": "SV-SMG", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'SV-SMG*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "SAP Support Services", "value": "SV-SMG-SER", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'SV-SMG-SER*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "7312 - Client 066 for EarlyWatch"}, "LongText": {"_label": "Description", "value": "<div id=\"DISCLAIMER\" class=\"sapMMsgStrip sapMMsgStripWarning sapUiSmallMarginTop sapUiMediumMarginBottom\"><div class=\"sapMMsgStripMessage\"><span dir=\"auto\" class=\"sapMText sapUiSelectable sapMTextMaxWidth\" style=\"text-align: left;\"><strong>ATTENTION!</strong> This is a machine-translated document that has not been reviewed for accuracy. To provide feedback regarding the machine translation, please <a style=\"color:var(--sapLinkColor);\" href=\"https://sapinsights.eu.qualtrics.com/jfe/form/SV_6nI2C1MOwEviVQG?NoteNumber=7312&TargetLanguage=EN&Component=SV-SMG-SER&SourceLanguage=DE&Priority=06\" target=\"_blank\">click here</a>.<br/>You can access the original document via the following link: <a style=\"color:var(--sapLinkColor);\" href=\"/notes/7312/D\" target=\"_blank\">/notes/7312/D</a>.</span></div></div><div><div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p><strong>Note that SAP does not require client 066 to perform service sessions. We recommend that you do not set up client 066 to avoid the associated maintenance effort. For more information, see <a target=\"_blank\" href=\"/notes/1749142\">SAP Note 1749142</a>.</strong></p>\r\n<p>The EarlyWatch user in client 066 does not exist or does not have the necessary authorizations to perform service sessions.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Weitere Begriffe\">Other Terms</h3>\r\n<p>EarlyWatch, GoingLive, EarlyWatch User, client 066</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Ursache und Voraussetzungen\">Reason and Prerequisites</h3>\r\n<p>The EarlyWatch 066 user delivered in the standard system lacks important authorizations for transaction SDCCN and display authorizations for transport logs, and so on.<br />You must also implement this SAP Note if the user EarlyWatch or the entire client 066 were accidentally deleted.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"L&ouml;sung\">Solution</h3>\r\n<p><br />Carry out the following steps:</p>\r\n<ol>1. Create client 066 if it does not exist.</ol>\r\n<p>              Start transaction SCC4 (Maintain Entries in Table T000) and check whether client 066 exists. If not, create an entry for client 066:</p>\r\n<p>           Man Client Name        Client Location        Currency<br />000 SAP AG                    Walldorf                  DEM<br />066 EarlyWatch                Walldorf                  DEM</p>\r\n<ol>2. Import default content into client 066</ol>\r\n<p>              The following transports contain entries for the EarlyWatch user in client 066 and its profiles. The transport for 3.x also contains the second default user SAP*.</p>\r\n<p>              CAUTION: The passwords of the users are set to the default.</p>\r\n<p>              Make sure to change the passwords!!</p>\r\n<p>           For R/3 Release 3.0* and 3.1*<br />The files client66. dat and client66imp.cmd are attached to this SAP Note and are packed in the file client66_31x.CAR. This file must first be unpacked. Then import the files client66.dat and client66imp.cmd into your transport directory and use R3trans for the import as follows:<br /><br />As user &lt;sid>adm:<br /> cd /usr/sap/trans<br /> ftp sapservX                      ( X= 3..7 )<br /> User: ftp<br /> Passwd: ftp<br /> ftp> lcd /usr/sap/trans           (lcd to the transport directory)<br /> ftp> bin                          (binary mode)<br /> ftp> cd /general/R3server/abap/note.0007312/client066.31G<br /> ftp> get client66.dat<br /> ftp> get client66imp.cmd<br /> ftp> bye<br /><br />The import takes place with the following commands:<br />As user &lt;sid>adm:<br /> cd /usr/sap/trans<br /> setenv ORACLE_HOME /oracle/&lt;SID> (only for Oracle)<br /> setenv ORACLE_SID &lt;SID>          (only for Oracle)<br /> /usr/sap/&lt;SID>/SYS/exe/run/R3trans -v client66imp.cmd<br /><br />You may have to adjust the path names above accordingly. Replace &lt;SID> with the ID of your SAP system, for example, C11. An RC 0004 can be accepted.<br /><br />When you import client 066 on IBM AS/400, note the following:<br />1. Retrieving files from sapservX via ftp: see SAP Note 37987<br />2. Log on as the user &lt;SID>OFR to import the client.<br />3. Call R3trans: R3trans &#39;-v client66imp.cmd&#39; Note the apostrophes around the command parameters.<br /><br />Note the following when importing into an ADABAS system:<br />To use R3trans, the environment variable DBHOST should be temporarily reset to the database host, then reset it (see SAP Note 170297 point 4) in the chapter &quot;Postprocessing only for source release 6.1&quot;).</p>\r\n<ol><ol><ol>a) Valid for R/3 Release 4.x:</ol></ol></ol><ol><ol>The file EARLYWATCH066.SAR is attached to this SAP Note.</ol></ol>\r\n<p>                    1. Download this file and save it in the<br />   &lt;DIR_TRANS> directory.<br />                    2. Unpack the file at operating system level with<br />   the command<br /><br />   SAPCAR -xvf EARLYWATCH066.SAR<br /><br />           The packed file contains the transports for R/3 Releases 4.x, 6.x, and 7.x. Depending on the release of the system into which the transport is to be imported, select the relevant transport file.</p>\r\n<ul>\r\n<ul>\r\n<li>For R/3 Release 4.x, the transport file is SAPKITLP66.<br />To import into client 066, the transport must be placed in the import buffer and then imported. To do this, use the following commands:</li>\r\n</ul>\r\n</ul>\r\n<p>                    tp addtobuffer SAPKITLP66 &lt;SID> pf=/usr/sap/trans/bin/TP_DOMAIN_&lt;SID Domaincontroller>.PFL<br />tp import SAPKITLP66 &lt;SID> client066 U89 pf=/usr/sap/trans/bin/TP_DOMAIN_&lt;SID Domain Controller>.PFL</p>\r\n<ul>\r\n<ul>\r\n<li>For R/3 Release 6.x, the transport file is SAPK-0666XINSDF.<br />To import into client 066, the transport must be placed in the import buffer and then imported. To do this, use the following commands:</li>\r\n</ul>\r\n</ul>\r\n<p>                    tp addtobuffer SAPK-0666XINSDF &lt;SID> pf=/usr/sap/trans/bin/TP_DOMAIN_&lt;SID Domaincontroller>.PFL<br />tp import SAPK-0666XINSDF &lt;SID> client066 U89 pf=/usr/sap/trans/bin/TP_DOMAIN_&lt;SID Domain Controller>.PFL</p>\r\n<ul>\r\n<ul>\r\n<li>For R/3 Releases 7.x, the transport file is SAPK-0667XINSDF.<br />To import into client 066, the transport must be placed in the import buffer and then imported. To do this, use the following commands:</li>\r\n</ul>\r\n</ul>\r\n<p>                    tp addtobuffer SAPK-0667XINSDF &lt;SID> pf=/usr/sap/trans/bin/TP_DOMAIN_&lt;SID Domaincontroller>.PFL<br />tp import SAPK-0667XINSDF &lt;SID> client066 U89 pf=/usr/sap/trans/bin/TP_DOMAIN_&lt;SID Domain Controller>.PFL</p>\r\n<ul>\r\n<ul>\r\n<li>For more information about transport files, see Note 13719.</li>\r\n</ul>\r\n</ul>\r\n<ol><ol><ol>b) If you use transaction STMS for the import, enter a target client 066 in the dialog box after &quot;Request -> Import&quot;.</ol></ol></ol><ol><ol>3. Synchronize User Buffer</ol></ol>\r\n<p><br /><br /></p>\r\n<ol><ol>For R/3 Release 3.1G to 3.1I</ol></ol><ol><ol>In transaction SU01, choose &#39;Utilities -> Mass Change -> Reset All Buffer&#39;. This reset then affects subsequent logons (see also SAP Note 75908).</ol></ol>\r\n<p><br /><br /></p>\r\n<ol><ol>For R/3 Releases 4.0* to 4.6* and 6.10 and 6.20</ol></ol><ol><ol>Execute the following function modules in transaction</ol></ol><ol><ol>SE37 -> Single test off:</ol></ol><ol><ol>SUSR_SYNC_USER_TABLES        with TABLETYPE = &#39;X&#39; and  CLIENT = &#39;066&#39;</ol></ol><ol><ol>SUSR_RESET_ALL_USER_BUFFERS          with CLIENT = &#39;066&#39;</ol></ol>\r\n<p><br /><br /></p>\r\n<ol><ol>Exceptions such as &#39;Refresh_not_successful&#39; can be ignored.</ol></ol><ol><ol>4. For 3.x only: Change password for user SAP*</ol></ol><ol><ol>Log on as user SAP* in client 066 with the password 06071992 and change the password.</ol></ol>\r\n<p><br /><br /></p>\r\n<p>Questions &amp; Answers<br /><br />Q1: Target client=066 was forgotten during import<br />A1: The transport still went to the correct client 066,<br />   because in this case Importclient=Exportclient, here = 066.<br /><br />Q2: Transport was imported into the wrong (productive) clients<br />A2: Log on to this client as user SAP* and change it.<br />   the password. SAP* should not be used as a productive user and<br />  possibly be locked. The EarlyWatch user may not be able to<br />   clients can be deleted.<br />   Afterwards, the transport with unconditional mode U1<br />   (for &#39;Reimport&#39;) into the correct client 066.<br /><br />Q3: 4.x only: User SAP* should also be imported into client 066<br />A3: If you also import the user SAP* again in Release 4.x or<br />   Reset    to default, for example, for the purpose of<br />   user administration in client 066, import the<br />  in point 2) for 4.x, enter the transport SAPKVSDF40. The<br />  Transport files are in the same directory<br />  general/R3server/abap/note.0007312/release4.x  .<br />  Import this transport in client 066 only.<br />  and then change the password for SAP* as described in point 4).<br /><br />Q4: The user EARLYWATCH does not have the authorizations for<br />    /BDL/*, /SDF/*,RFC1, SUGX are missing<br />A4: Import the transport valid for your system from the archive EARLYWATCH066.SAR into your system or adjust the profile S_TOOLS_EX_A as follows:<br /><br />In your system, call transaction SU02 (&quot;Edit Authorization Profiles Manually&quot;) and enter the profile S_TOOLS_EX_A. Select the &#39;Active only&#39; versions field, then choose &#39;Enter&#39;<br /><br />Double-click the profile S_TOOLS_EX_A and then the object S_RFC (authorization check for RFC access).<br /><br />In the menu bar, choose &quot;Maintain Values&quot;.<br />Now add the following four entries to the list:<br /><br />- /BDL/*<br />- /SDF/*<br />-  RFC1<br />-  SUGX<br /><br />Press Enter. Save the entries and choose &quot;Activate&quot; in the menu bar twice in succession.<br /><br />Q5: The Earlywatch user does not have authorizations for the new Service Data Control Center (transaction SDCCN). It is at least ST-PI 2005_1* with SP2<br />implemented.<br /><br />A5: Import the transport valid for your system from the archive EARLYWATCH066.SAR into your system or assign the following authorization objects to the user EARLYWATCH:<br /><br />Profiles (in Basis Release 40*-46D)</p>\r\n<ul>\r\n<ul>\r\n<li>S_SDCC_READN    Read authorization</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>S_SDCC_SERVN    Collect and send data</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>S_SDCC_ADM_N    Admin authorization                                                                 *</li>\r\n</ul>\r\n</ul>\r\n<p>Roles (as of Basis Release 610)</p>\r\n<ul>\r\n<ul>\r\n<li>SAP_SDCCN_DIS   Read authorization</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>SAP_SDCCN_EXE   Collect and send data</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>SAP_SDCCN_ALL   Admin authorization</li>\r\n</ul>\r\n</ul></div></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Owner                                                                                    ", "Value": "<PERSON><PERSON><PERSON> (D028369)"}, {"Key": "Processor                                                                                          ", "Value": "<PERSON><PERSON> (D028075)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000007312/D"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "MIME Type", "URL": "URL"}, "Items": [{"FileName": "EARLYWATCH066.SAR", "FileSize": "79", "MimeType": "application/octet-stream", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=011000358700006697752001&iv_version=0088&iv_guid=1ACA65613B777240A17CA72B5A3078E5"}, {"FileName": "SAPKVSDF42.CAR", "FileSize": "43", "MimeType": "application/octet-stream", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=011000358700006697752001&iv_version=0088&iv_guid=2A704B6877299F4BB574621C7947C33C"}, {"FileName": "client66_31x.CAR", "FileSize": "5", "MimeType": "application/octet-stream", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=011000358700006697752001&iv_version=0088&iv_guid=83F3D7194D5ECE429427093C44404BE5"}, {"FileName": "SAPKVSDF61.CAR", "FileSize": "43", "MimeType": "application/octet-stream", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=011000358700006697752001&iv_version=0088&iv_guid=0ED02E34CD83254B95CED477262D03D9"}, {"FileName": "SAPKVSDF40.CAR", "FileSize": "7", "MimeType": "application/octet-stream", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=011000358700006697752001&iv_version=0088&iv_guid=6BD7146A13BC0A42B23E0B808B9BFC55"}, {"FileName": "SAPKVSDF41.CAR", "FileSize": "10", "MimeType": "application/octet-stream", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=011000358700006697752001&iv_version=0088&iv_guid=D04E092064FEAC49BE71398C5B2EE815"}]}, "References": {"_label": "References", "RefTo": {"_label": "This document references", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "URL"}, "Items": [{"RefNumber": "84207", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/84207"}, {"RefNumber": "78382", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/78382"}, {"RefNumber": "75908", "RefComponent": "BC-SEC-USR-ADM", "RefTitle": "Permissions missing after client copy", "RefUrl": "/notes/75908"}, {"RefNumber": "68048", "RefComponent": "BC-SEC-LGN", "RefTitle": "Deactivation of automatic user SAP*", "RefUrl": "/notes/68048"}, {"RefNumber": "47144", "RefComponent": "BC-OP-AS4", "RefTitle": "Equivalents for UNIX commands in AS/400", "RefUrl": "/notes/47144"}, {"RefNumber": "43288", "RefComponent": "BC-UPG-RDM", "RefTitle": "Enhancements to upgrade to 3.0E", "RefUrl": "/notes/43288"}, {"RefNumber": "37987", "RefComponent": "BC-OP-AS4", "RefTitle": "AS/400: Importing Transports", "RefUrl": "/notes/37987"}, {"RefNumber": "360995", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/360995"}, {"RefNumber": "187939", "RefComponent": "SV-SMG-SDD", "RefTitle": "Update of SAP Service Tools (RTCCTOOL)", "RefUrl": "/notes/187939"}, {"RefNumber": "1749142", "RefComponent": "BC-CTS-CCO", "RefTitle": "Removal of unused clients like client 001 and 066", "RefUrl": "/notes/1749142"}, {"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Advance transports to customer (note for customers)", "RefUrl": "/notes/13719"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "URL"}, "Items": [{"RefNumber": "47144", "RefComponent": "BC-OP-AS4", "RefTitle": "Equivalents for UNIX commands in AS/400", "RefUrl": "/notes/47144 "}, {"RefNumber": "68048", "RefComponent": "BC-SEC-LGN", "RefTitle": "Deactivation of automatic user SAP*", "RefUrl": "/notes/68048 "}, {"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Advance transports to customer (note for customers)", "RefUrl": "/notes/13719 "}, {"RefNumber": "187939", "RefComponent": "SV-SMG-SDD", "RefTitle": "Update of SAP Service Tools (RTCCTOOL)", "RefUrl": "/notes/187939 "}, {"RefNumber": "37987", "RefComponent": "BC-OP-AS4", "RefTitle": "AS/400: Importing Transports", "RefUrl": "/notes/37987 "}, {"RefNumber": "75908", "RefComponent": "BC-SEC-USR-ADM", "RefTitle": "Permissions missing after client copy", "RefUrl": "/notes/75908 "}, {"RefNumber": "43288", "RefComponent": "BC-UPG-RDM", "RefTitle": "Enhancements to upgrade to 3.0E", "RefUrl": "/notes/43288 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And the following"}, "Items": [{"SoftwareComponent": "SAP_APPL", "From": "40A", "To": "40B", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "45A", "To": "45B", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "470", "To": "470", "Subsequent": ""}, {"SoftwareComponent": "SAP_BASIS", "From": "46A", "To": "46D", "Subsequent": ""}, {"SoftwareComponent": "SAP_BASIS", "From": "610", "To": "640", "Subsequent": ""}, {"SoftwareComponent": "SAP_BASIS", "From": "700", "To": "700", "Subsequent": ""}, {"SoftwareComponent": "SAP_BASIS", "From": "710", "To": "710", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software-Komponentenversion", "SupportPackage": "Support Package", "URL": "URL"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Packages and Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "URL"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "URL"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about correction instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisite", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisite", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "URL", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated as helpful", "Helpful-Yes": "0 persons"}, "RatingQuality": {"_label": "Quality Assessment"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document eliminates side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "URL"}, "Items": []}, "SideEffectsSolving": {"_label": "This document causes side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "URL"}, "Items": []}}, "DisclaimerForMachineTranslation": {"Header": "ATTENTION!", "Message": "This is a machine-translated document that has not been reviewed for accuracy.", "Survey": "To provide feedback regarding the machine translation, please <a href=\"https://sapinsights.eu.qualtrics.com/jfe/form/SV_6nI2C1MOwEviVQG?NoteNumber=7312&TargetLanguage=EN&Component=SV-SMG-SER&SourceLanguage=DE&Priority=06\" target=\"_blank\">click here</a>.", "Request": "If the quality of the machine translation is not sufficient for your purposes, click here to request a revised version.", "Origin": "You can access the original document via the following link: <a href=\"/notes/7312/D\" target=\"_blank\">/notes/7312/D</a>."}}}}