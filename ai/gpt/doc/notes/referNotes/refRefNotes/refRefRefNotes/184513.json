{"Request": {"Number": "184513", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 1049, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000014744642017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000184513?language=E&token=59078DD210FD23DED664DCEC2FFC80B3"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000184513", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000184513/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "184513"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 17}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Consulting"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "12.07.2001"}, "SAPComponentKey": {"_label": "Component", "value": "BC-DB-ORA-DBA"}, "SAPComponentKeyText": {"_label": "Component", "value": "Database Administration"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Basis Components", "value": "BC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Database Interface, Database Platforms", "value": "BC-DB", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-DB*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Oracle", "value": "BC-DB-ORA", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-DB-ORA*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Database Administration", "value": "BC-DB-ORA-DBA", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-DB-ORA-DBA*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "184513 - CBO: Parallel generation of optimizer statistics"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>Parallel update of the Optimizer statistics should no longer be carried out using \"sapdba -statistics\" in the future but using the BRCONNECT option \"-f stats -p \". The corresponding functionality is no longer further developed in SAPDBA but in BRCONNECT 6.10.<br />For more information, please refer to Notes 403704, 403713, 408527, and 408532.<br /><br />+++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++<br />Original text:<br />--------------<br />To generate optimizer statistics the two phase concept has been available up to now. First a requirements analysis is carried out by means of 'sapdba -checkopt PSAP%' and, afterwards, statistics are generated by means of 'sapdba -analyze DBSTATCO'.<br /><br />In order to optimize the runtimes of requirements test and statistics generation the SAPDBA offers as of Release 4.6D the option '-statistics' which generates the optimizer statistics in processes which are started in parallel.<br />The new function was ported into already delivered SAPDBA versions and is available as of the following Support Package statuses:<br /><br />Release&#x00A0;&#x00A0;|&#x00A0;&#x00A0;As of Support Package level<br />---------------------------------------<br />45B&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;|&#x00A0;&#x00A0; 84<br />46B&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;|&#x00A0;&#x00A0; 30<br />46C&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;|&#x00A0;&#x00A0;&#x00A0;&#x00A0;6<br /><br />This new method for generating the optimizer statistics is described in this note.<br /></p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>CBO, statistics, sta</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>Customer information</p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><OL>1. General information<br /><br />The complete call option of SAPDBA for parallel generation of statistics is:<br /><br />sapdba -statistics [ ALL | DBSTATCO | &lt;Tables&gt; ]<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;[-method E | C | EH | CH<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;[-option P | R]&#x00A0;&#x00A0;] <br />After calling the command, SAPDBA starts several child processes which carry out the optimizer analyses for the individual tables. That process communication been between father and child processes is carried out via pipes.<br /></OL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Parallelism degree - cbo_parallel_degree<br /> <br />The number of the processes started in parallel (parallelism degree) is determined by parameter cbo_parallel_degree in the SAPDBA profile init&lt;SID&gt;.dba.If the parameter is not specified or equal to 0, SAPDBA uses the parallelism degree equal to the double number of CPUs of the database host. The maximum number of parallel processes is restricted to 16.<br /><br /><br />Logging<br /><br />The respective operation log is stored in SAPCHECK directory under &lt;timestamp&gt;.sta. As of Release 4.5B it can be analyzed using Transaction DB24.<br /><br /> <OL>2. Explanation of the different call options<br /></OL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;sapdba -statistics [ALL]<br /><br /> This is the standard call replacing the calls \"sapdba -checkopt PSAP%\" and \"sapdba -analyze DBSTATCO\" used up to now. The option -statistics without additional arguments corresponds<br />the call option sapdba -statistics ALL.<br /><br />When using this option, the following operations are carried out: <OL><OL>a) Deletion of undesired statistics:<br />Deletion of statistics on tables which should have no optimizer statistics.These are all R/3 pool and cluster tables as well as all tables listed in the DBSTATC (Transaction DB21) for which active was set to 'N' or 'R'.</OL></OL> <OL><OL>b) Statistics for conventional tables:<br />To what degree the number of records in the table have changed is examined by analyzing the primary index for all tables not listed in the DBSTATC. If the criteria described in 3 apply, new statistics are assigned to the table.The method used for generating statistics (Estimate or Compute) depends on the number of table records and results from the rules described in 4.<br />The following tables are excluded from this operation:</OL></OL> <UL><UL><LI>All tables with ACTIV = N or I in DBSTATC.</LI></UL></UL> <UL><UL><LI>ALL R/3 pool and cluster tables.</LI></UL></UL> <OL><OL>c) Initial statistics for new tables:<br />These are assigned to tables without optimizer statistics for the first time.First the respective primary index is analyzed by means of method ESTIMATE STATISTICS SAMPLE 1 PERCENT.The method for the following analysis of the table results from the number of table records (refer to 4).</OL></OL> <OL><OL>d) Statistics for special tables<br />A new optimizer statistics is assigned to all tables listed in DBSTATC, if one of the following criteria applies:</OL></OL> <UL><UL><LI>The TOBDO flag is set for the table in DBSTATC.</LI></UL></UL> <UL><UL><LI>ACTIV is set to 'U' for the table.</LI></UL></UL> <UL><UL><LI>ACTIV is set to 'A' or 'P' for the table and the number of the table records deviates according to the internal SAPDBA.</LI></UL></UL> <UL><UL><LI>ACTIV is set to 'I' for the table, the DURAT flag is set in DBSTATC and the number of the table records deviates according to the internal SAPDBA (see 4.)<br /><br /></LI></UL></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;sapdba -statistics DBSTATCO<br /><br />Only the item described in 2d) is executed with this call.<br /><br /><br />sapdba statistics &lt;tables&gt;<br /><br />All steps listed in 2), but only for tables &lt;tables&gt;.&lt;Tables&gt; is the name of an individual table or a list of tables defined by specifying a pattern (for example TBT%).<br /><br />-method / -option<br />This method should be used to test the behavior of the optimizer after the creation of special statistics.<br /><br /><br />When these call options are entered, SAPDBA generates statistics without taking into account possible settings in DBSTATC and without previous requirements check. In the case of a positive result i.e. the<br />optimizer works better after the creation of the statistics a corresponding entry can be made in DBSTATC.<br />The meaning is as follows <UL><UL><LI>method E:&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; ANALYZE TABLE ESTIMATE STATISTICS</LI></UL></UL> <UL><UL><LI>method C:&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; ANALYZE TABLE COMPUTE STATISTICS</LI></UL></UL> <UL><UL><LI>method EH/CH: ANALYZE TABLE ESTIMATE/COMPUTE STATISTICS FOR TABLE FOR ALL INDEXED COLUMNS FOR ALL INDEXES (histograms are generated this way)</LI></UL></UL> <UL><UL><LI>option R n :&#x00A0;&#x00A0; addition SAMPLE n*1000 ROWS</LI></UL></UL> <UL><UL><LI>option P n :&#x00A0;&#x00A0; addition SAMPLE n PERCENT<br /></LI></UL></UL> <OL>3. Converting from two-phase to single-phase concept<br /><br />The processes that have been scheduled so far<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;sapdba -anaylze DBSTATCO und<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; sapdba -checkopt PSAP%<br />are replaced by 'sapdba -statistics'.<br />The DBA Planning calender (DB13) allows for the scheduling of operation 'sapdba -statistics' as of SAP Release 5. For lower releases you can use the background processing (SM36) to create a job with the<br />following attributes:<br /><br />Job attributes:<br /><br />job name:&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;any<br />job class:&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;any<br />target server:&#x00A0;&#x00A0;any<br /><br />Step attributes:<br />External program name:&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;sapdba<br />Parameter:&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;-u / -statistics<br />Target computer:&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&lt;database computer&gt;<br /><br />Start conditions:<br />Execute the job periodically (weekly is recommended)<br /><br />In particular, note that the role of table DBSTATC has changed.<br />In the single-phase concept the table is used as a Customizing table only. It only contains objects that are to be treated differently and not according to the rules described under 4. and 5. (Exception table).<br />In the two-phase concept the table was also used to store objects with<br />statitics requirements temporarily. 'sapdba -statistics' does not generate or remove DBSTATC entries. The method or option of the entered objects is not adjusted by SAPDBA in the single-phase concept.<br />Therefore note, when converting to the new concept, that all objects<br />temporarily entered by SAPDBA must be removed from the table. These are<br />all tables where ACTIV = 'A' and where the customer flag is not set.<br />Before using 'sapdba -statistics' transmit the following commands in the SERVER MANAGER:<br /></OL> <p>SVRMGRL&gt;delete from sapr3.dbstatc where activ = 'A' and pland &lt;&gt; 'X';<br />SVRMGRL&gt;commit;<br /><br />To suppress the alert \"DB Operation OPT/ALY never started\" with SAPDBA -check, deactivate the type DBO operations with parameters OPT and ALY. To do this, use transaction DB17.<br /></p> <OL>4. Criteria for generating new statistics:<br /><br />SAPDBA generates new statistics, if</OL> <UL><UL><LI>the current number of table records is less than 200000 and the number has changed by at least 10%,</LI></UL></UL> <UL><UL><LI>the current number of table records is greater than 200000 and the number has decreased by at least 10%,</LI></UL></UL> <UL><UL><LI>the current number of table records is greater than 200000 and the number has increased by at least 100%,<br /></LI></UL></UL> <OL>5. Internal rules for choice of method/option in the case of Analyze:<br />Refer to Note 182758.</OL></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Database System", "Value": "ORACLE"}, {"Key": "Responsible                                                                                         ", "Value": "D000674"}, {"Key": "Processor                                                                                           ", "Value": "D000674"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000184513/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000184513/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000184513/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000184513/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000184513/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000184513/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000184513/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000184513/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000184513/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "489690", "RefComponent": "BC-CTS-CCO", "RefTitle": "CC-INFO: Copying large production clients", "RefUrl": "/notes/489690"}, {"RefNumber": "408532", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Using the DBMS_STATS package for collecting statistics", "RefUrl": "/notes/408532"}, {"RefNumber": "408527", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Checking the statistics using DBA_TAB_MODIFICATIONS", "RefUrl": "/notes/408527"}, {"RefNumber": "403713", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "BRCONNECT: Parallel updating of statistics", "RefUrl": "/notes/403713"}, {"RefNumber": "403706", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Corrections in BR*Tools Version 6.10", "RefUrl": "/notes/403706"}, {"RefNumber": "403704", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "BRCONNECT - enhanced function for Oracle DBA", "RefUrl": "/notes/403704"}, {"RefNumber": "321157", "RefComponent": "BC-DB-ORA", "RefTitle": "TORA01 - Information for the trainer", "RefUrl": "/notes/321157"}, {"RefNumber": "301608", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "CBO: incor. method for creating initial stat.", "RefUrl": "/notes/301608"}, {"RefNumber": "215917", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/215917"}, {"RefNumber": "182758", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "CBO: Create histograms / new internal rules", "RefUrl": "/notes/182758"}, {"RefNumber": "132861", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "CBO: Statistics creation with SAPDBA or BRCONNECT", "RefUrl": "/notes/132861"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "489690", "RefComponent": "BC-CTS-CCO", "RefTitle": "CC-INFO: Copying large production clients", "RefUrl": "/notes/489690 "}, {"RefNumber": "403704", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "BRCONNECT - enhanced function for Oracle DBA", "RefUrl": "/notes/403704 "}, {"RefNumber": "408527", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Checking the statistics using DBA_TAB_MODIFICATIONS", "RefUrl": "/notes/408527 "}, {"RefNumber": "132861", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "CBO: Statistics creation with SAPDBA or BRCONNECT", "RefUrl": "/notes/132861 "}, {"RefNumber": "408532", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Using the DBMS_STATS package for collecting statistics", "RefUrl": "/notes/408532 "}, {"RefNumber": "321157", "RefComponent": "BC-DB-ORA", "RefTitle": "TORA01 - Information for the trainer", "RefUrl": "/notes/321157 "}, {"RefNumber": "403706", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Corrections in BR*Tools Version 6.10", "RefUrl": "/notes/403706 "}, {"RefNumber": "182758", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "CBO: Create histograms / new internal rules", "RefUrl": "/notes/182758 "}, {"RefNumber": "301608", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "CBO: incor. method for creating initial stat.", "RefUrl": "/notes/301608 "}, {"RefNumber": "403713", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "BRCONNECT: Parallel updating of statistics", "RefUrl": "/notes/403713 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_APPL", "From": "40B", "To": "40B", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "45B", "To": "45B", "Subsequent": ""}, {"SoftwareComponent": "SAP_BASIS", "From": "46B", "To": "46D", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "SAP_APPL 40B", "SupportPackage": "SAPKH40B61", "URL": "/supportpackage/SAPKH40B61"}, {"SoftwareComponentVersion": "SAP_APPL 45B", "SupportPackage": "SAPKH45B38", "URL": "/supportpackage/SAPKH45B38"}, {"SoftwareComponentVersion": "SAP_APPL 45B", "SupportPackage": "SAPKH45B39", "URL": "/supportpackage/SAPKH45B39"}, {"SoftwareComponentVersion": "SAP_BASIS 46B", "SupportPackage": "SAPKB46B26", "URL": "/supportpackage/SAPKB46B26"}, {"SoftwareComponentVersion": "SAP_BASIS 46B", "SupportPackage": "SAPKB46B27", "URL": "/supportpackage/SAPKB46B27"}, {"SoftwareComponentVersion": "SAP_BASIS 46C", "SupportPackage": "SAPKB46C15", "URL": "/supportpackage/SAPKB46C15"}, {"SoftwareComponentVersion": "SAP_BASIS 46C", "SupportPackage": "SAPKB46C17", "URL": "/supportpackage/SAPKB46C17"}, {"SoftwareComponentVersion": "SAP_BASIS 46D", "SupportPackage": "SAPKB46D06", "URL": "/supportpackage/SAPKB46D06"}, {"SoftwareComponentVersion": "SAP_BASIS 46D", "SupportPackage": "SAPKB46D07", "URL": "/supportpackage/SAPKB46D07"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}