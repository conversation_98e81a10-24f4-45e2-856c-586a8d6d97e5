{"Request": {"Number": "375568", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 1244, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000001626012017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000375568?language=E&token=B1D3E686EAF3C4FF6760FD118F615B06"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000375568", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000375568/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "375568"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 34}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Program error"}, "Priority": {"_label": "Priority", "value": "Correction with high priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "15.03.2002"}, "SAPComponentKey": {"_label": "Component", "value": "CA-CL-CHR"}, "SAPComponentKeyText": {"_label": "Component", "value": "Characteristics"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Cross-Application Components", "value": "CA", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'CA*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Classification", "value": "CA-CL", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'CA-CL*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Characteristics", "value": "CA-CL-CHR", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'CA-CL-CHR*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "375568 - Characteristics maintenance: update redesign"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>Updating in the characteristic management was revised. The changes are imported via the following Support Packages:<br /><br />Release 4.0B: R/3 Support Package 64<br />Release 4.5B: R/3 Support Package 41<br />Release 4.6B: R/3 Support Package 27<br />Release 4.6C: R/3 Support Package 17<br /><br />The new update program can also be imported via a sapserv3- transport. Then, manual changes are necessary as well. Making these changes WITHOUT importing the transport will cause syntax errors.<br />The execution of the transport is described in the attached Note 13719. Accordingly, you can find the files in directory ftp://sapserv3/general/R3server/abap/note.0375568 .<br /><br />The following files in the folder for this note are relevant:<br />Release 4.0B: *.U4B<br />Release 4.5B: *.U4D<br />Release 4.6B: *.U9B<br />Release 4.6C: *.U9C<br /><br />CAUTION: RELEASE &gt; 4.0B<br />If you do NOT use parameter effectivity in the characteristic management, it is not sufficient to make the change in LCTMVTOP.Otherwise you also need to adjust the subroutine BUFFER_CHANGE_SERVICE_NUMBER (INCLUDE LCTMVF1P) and all calls.The calls are to be determined via the where-used list.The change is the same for each call:<br /><br />PERFORM BUFFER_CHANGE_SERVICE_NUMBER USING &lt;Parameter1&gt;<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&lt;Parameter2&gt;<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&lt;Parameter3&gt;.&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; \"&lt;DELETE<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&lt;Parameter3&gt; \"&lt;INSERT<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;G_ECN_EFFECTIVITY.&#x00A0;&#x00A0;\"&lt;INSERT<br /><br />Only after you changed all calls of BUFFER_CHANGE_SERVICE_NUMBER in this way, you will be able to activate function group CTMV again.<br /><br />You may need to create and activate structure CLATINNRANGE in the development class CT for all releases as follows:<br />Field name Data element<br />SIGN&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; BAPISIGN<br />OPTION&#x00A0;&#x00A0;&#x00A0;&#x00A0; BAPIOPTION<br />LOW&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;ATINN<br />HIGH&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; ATINN<br />As of Release 4.5B, you may also need to create and activate table type TT_ATINN_RANGE in development class CT with line category CLATINNRANGE.<br /><br />The current version of the transport contains all relevant notes up to and including number 442695 (Support Packages 40B68, 45B47, 46C26) or for Release 4.6B Note 441897 (Support Package 46B34).</p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>CT01, CT02, CT04, CTVB, BUS1088_DATABASE_UPDATE</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>This is caused by a program error.</p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>Import the corresponding Supprt Package.<br /><br /></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Transaction codes", "Value": "CLEAR"}, {"Key": "Transaction codes", "Value": "CT04"}, {"Key": "Transaction codes", "Value": "CT02"}, {"Key": "Transaction codes", "Value": "CT01"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D028903)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D028903)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000375568/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000375568/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000375568/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000375568/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000375568/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000375568/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000375568/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000375568/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000375568/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "481674", "RefComponent": "CA-CL-CHR", "RefTitle": "Characteristics management: Change documents are missing", "RefUrl": "/notes/481674"}, {"RefNumber": "460387", "RefComponent": "CA-CL-CHR", "RefTitle": "Characteristics management: Update termination C1197", "RefUrl": "/notes/460387"}, {"RefNumber": "458573", "RefComponent": "CA-CL-CHR", "RefTitle": "Characteristics maintenance: Termination message C1197", "RefUrl": "/notes/458573"}, {"RefNumber": "457103", "RefComponent": "CA-CL-CHR", "RefTitle": "Characteristics management: runtime objects not deleted", "RefUrl": "/notes/457103"}, {"RefNumber": "455976", "RefComponent": "CA-CL-CHR", "RefTitle": "Characteristics management: value description ot saved", "RefUrl": "/notes/455976"}, {"RefNumber": "452125", "RefComponent": "CA-CL-CHR", "RefTitle": "Characteristcs management: Update terminatn value descriptns", "RefUrl": "/notes/452125"}, {"RefNumber": "448578", "RefComponent": "CA-CL-CHR", "RefTitle": "Characteristics management: value descriptions lost", "RefUrl": "/notes/448578"}, {"RefNumber": "445833", "RefComponent": "CA-CL-CHR", "RefTitle": "Characteristics management: value descriptions lost", "RefUrl": "/notes/445833"}, {"RefNumber": "444190", "RefComponent": "CA-CL-CHR", "RefTitle": "Overwritten attributes: Update termination change documents", "RefUrl": "/notes/444190"}, {"RefNumber": "443495", "RefComponent": "CA-CL-CHR", "RefTitle": "Characteristics management: deleted value descr. displayed", "RefUrl": "/notes/443495"}, {"RefNumber": "442695", "RefComponent": "CA-CL-CHR", "RefTitle": "Characteristics management: Information in change documents", "RefUrl": "/notes/442695"}, {"RefNumber": "441897", "RefComponent": "CA-CL-CHR", "RefTitle": "Charcterstics managemnt: value descriptions & long text lost", "RefUrl": "/notes/441897"}, {"RefNumber": "440791", "RefComponent": "CA-CL-CHR", "RefTitle": "Characteristic maintenance: Values deleted", "RefUrl": "/notes/440791"}, {"RefNumber": "435753", "RefComponent": "CA-CL-CHR", "RefTitle": "Characteristics management: too many deletion records", "RefUrl": "/notes/435753"}, {"RefNumber": "432744", "RefComponent": "CA-CL-CHR", "RefTitle": "Characteristic maintenance: value descriptions lost", "RefUrl": "/notes/432744"}, {"RefNumber": "432316", "RefComponent": "CA-CL-CHR", "RefTitle": "Characteristic management: double values", "RefUrl": "/notes/432316"}, {"RefNumber": "430518", "RefComponent": "CA-CL-CHR", "RefTitle": "Characteristics mgmt: Update terminat. w/ value descriptions", "RefUrl": "/notes/430518"}, {"RefNumber": "428246", "RefComponent": "CA-CL-CHR", "RefTitle": "Characteristics management: Update termination value descriptions", "RefUrl": "/notes/428246"}, {"RefNumber": "427607", "RefComponent": "CA-CL-CHR", "RefTitle": "Characteristics mgmt: Update termination value description", "RefUrl": "/notes/427607"}, {"RefNumber": "427394", "RefComponent": "CA-CL-CHR", "RefTitle": "Characteristics management: Update termination C1061", "RefUrl": "/notes/427394"}, {"RefNumber": "426157", "RefComponent": "CA-CL-CHR", "RefTitle": "Overwrite: Not saved", "RefUrl": "/notes/426157"}, {"RefNumber": "426034", "RefComponent": "CA-CL-CHR", "RefTitle": "Characteristics management: Change of values in past", "RefUrl": "/notes/426034"}, {"RefNumber": "424931", "RefComponent": "CA-CL-CHR", "RefTitle": "Characteristics management: Update problems", "RefUrl": "/notes/424931"}, {"RefNumber": "424885", "RefComponent": "CA-CL-CHR", "RefTitle": "Characteristic maintenance: Update termination names", "RefUrl": "/notes/424885"}, {"RefNumber": "424072", "RefComponent": "CA-CL-CHR", "RefTitle": "Characteristics management: Update termination CABNT", "RefUrl": "/notes/424072"}, {"RefNumber": "423608", "RefComponent": "CA-CL-CHR", "RefTitle": "Characteristics managmt: Description assigned incorrectly", "RefUrl": "/notes/423608"}, {"RefNumber": "422680", "RefComponent": "CA-CL-CHR", "RefTitle": "Characteristics mgmt: Update termination GETWA_NOT_ASSIGNED", "RefUrl": "/notes/422680"}, {"RefNumber": "422138", "RefComponent": "CA-CL-CHR", "RefTitle": "Characteristics management: Update termin. for descriptions", "RefUrl": "/notes/422138"}, {"RefNumber": "419046", "RefComponent": "CA-CL-CHR", "RefTitle": "Characteristic managem:Update termination value description", "RefUrl": "/notes/419046"}, {"RefNumber": "417944", "RefComponent": "CA-CL-CHR", "RefTitle": "Characteristic maintenance: redundant data records", "RefUrl": "/notes/417944"}, {"RefNumber": "415742", "RefComponent": "CA-CL-CHR", "RefTitle": "Overwriting: object dependencies not saved", "RefUrl": "/notes/415742"}, {"RefNumber": "412777", "RefComponent": "CA-CL-CHR", "RefTitle": "Overwriting: value description assigned incorrectly", "RefUrl": "/notes/412777"}, {"RefNumber": "409510", "RefComponent": "CA-CL-CHR", "RefTitle": "Overwriting: Error when changing several characteristics", "RefUrl": "/notes/409510"}, {"RefNumber": "407790", "RefComponent": "CA-CL-CHR", "RefTitle": "Characteristics maintenance: Update termination", "RefUrl": "/notes/407790"}, {"RefNumber": "403632", "RefComponent": "CA-CL-CHR", "RefTitle": "Overwrite: missing values", "RefUrl": "/notes/403632"}, {"RefNumber": "394202", "RefComponent": "CA-CL-CHR", "RefTitle": "Characteristics maintenance: change documents", "RefUrl": "/notes/394202"}, {"RefNumber": "393805", "RefComponent": "CA-CL-CHR", "RefTitle": "Characteristics maintenance: multiple changes to sequence", "RefUrl": "/notes/393805"}, {"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "394202", "RefComponent": "CA-CL-CHR", "RefTitle": "Characteristics maintenance: change documents", "RefUrl": "/notes/394202 "}, {"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719 "}, {"RefNumber": "426157", "RefComponent": "CA-CL-CHR", "RefTitle": "Overwrite: Not saved", "RefUrl": "/notes/426157 "}, {"RefNumber": "393805", "RefComponent": "CA-CL-CHR", "RefTitle": "Characteristics maintenance: multiple changes to sequence", "RefUrl": "/notes/393805 "}, {"RefNumber": "428246", "RefComponent": "CA-CL-CHR", "RefTitle": "Characteristics management: Update termination value descriptions", "RefUrl": "/notes/428246 "}, {"RefNumber": "444190", "RefComponent": "CA-CL-CHR", "RefTitle": "Overwritten attributes: Update termination change documents", "RefUrl": "/notes/444190 "}, {"RefNumber": "457103", "RefComponent": "CA-CL-CHR", "RefTitle": "Characteristics management: runtime objects not deleted", "RefUrl": "/notes/457103 "}, {"RefNumber": "481674", "RefComponent": "CA-CL-CHR", "RefTitle": "Characteristics management: Change documents are missing", "RefUrl": "/notes/481674 "}, {"RefNumber": "460387", "RefComponent": "CA-CL-CHR", "RefTitle": "Characteristics management: Update termination C1197", "RefUrl": "/notes/460387 "}, {"RefNumber": "458573", "RefComponent": "CA-CL-CHR", "RefTitle": "Characteristics maintenance: Termination message C1197", "RefUrl": "/notes/458573 "}, {"RefNumber": "448578", "RefComponent": "CA-CL-CHR", "RefTitle": "Characteristics management: value descriptions lost", "RefUrl": "/notes/448578 "}, {"RefNumber": "422680", "RefComponent": "CA-CL-CHR", "RefTitle": "Characteristics mgmt: Update termination GETWA_NOT_ASSIGNED", "RefUrl": "/notes/422680 "}, {"RefNumber": "424931", "RefComponent": "CA-CL-CHR", "RefTitle": "Characteristics management: Update problems", "RefUrl": "/notes/424931 "}, {"RefNumber": "455976", "RefComponent": "CA-CL-CHR", "RefTitle": "Characteristics management: value description ot saved", "RefUrl": "/notes/455976 "}, {"RefNumber": "452125", "RefComponent": "CA-CL-CHR", "RefTitle": "Characteristcs management: Update terminatn value descriptns", "RefUrl": "/notes/452125 "}, {"RefNumber": "427607", "RefComponent": "CA-CL-CHR", "RefTitle": "Characteristics mgmt: Update termination value description", "RefUrl": "/notes/427607 "}, {"RefNumber": "445833", "RefComponent": "CA-CL-CHR", "RefTitle": "Characteristics management: value descriptions lost", "RefUrl": "/notes/445833 "}, {"RefNumber": "443495", "RefComponent": "CA-CL-CHR", "RefTitle": "Characteristics management: deleted value descr. displayed", "RefUrl": "/notes/443495 "}, {"RefNumber": "442695", "RefComponent": "CA-CL-CHR", "RefTitle": "Characteristics management: Information in change documents", "RefUrl": "/notes/442695 "}, {"RefNumber": "435753", "RefComponent": "CA-CL-CHR", "RefTitle": "Characteristics management: too many deletion records", "RefUrl": "/notes/435753 "}, {"RefNumber": "441897", "RefComponent": "CA-CL-CHR", "RefTitle": "Charcterstics managemnt: value descriptions & long text lost", "RefUrl": "/notes/441897 "}, {"RefNumber": "440791", "RefComponent": "CA-CL-CHR", "RefTitle": "Characteristic maintenance: Values deleted", "RefUrl": "/notes/440791 "}, {"RefNumber": "432744", "RefComponent": "CA-CL-CHR", "RefTitle": "Characteristic maintenance: value descriptions lost", "RefUrl": "/notes/432744 "}, {"RefNumber": "432316", "RefComponent": "CA-CL-CHR", "RefTitle": "Characteristic management: double values", "RefUrl": "/notes/432316 "}, {"RefNumber": "430518", "RefComponent": "CA-CL-CHR", "RefTitle": "Characteristics mgmt: Update terminat. w/ value descriptions", "RefUrl": "/notes/430518 "}, {"RefNumber": "427394", "RefComponent": "CA-CL-CHR", "RefTitle": "Characteristics management: Update termination C1061", "RefUrl": "/notes/427394 "}, {"RefNumber": "426034", "RefComponent": "CA-CL-CHR", "RefTitle": "Characteristics management: Change of values in past", "RefUrl": "/notes/426034 "}, {"RefNumber": "424885", "RefComponent": "CA-CL-CHR", "RefTitle": "Characteristic maintenance: Update termination names", "RefUrl": "/notes/424885 "}, {"RefNumber": "417944", "RefComponent": "CA-CL-CHR", "RefTitle": "Characteristic maintenance: redundant data records", "RefUrl": "/notes/417944 "}, {"RefNumber": "423608", "RefComponent": "CA-CL-CHR", "RefTitle": "Characteristics managmt: Description assigned incorrectly", "RefUrl": "/notes/423608 "}, {"RefNumber": "424072", "RefComponent": "CA-CL-CHR", "RefTitle": "Characteristics management: Update termination CABNT", "RefUrl": "/notes/424072 "}, {"RefNumber": "422138", "RefComponent": "CA-CL-CHR", "RefTitle": "Characteristics management: Update termin. for descriptions", "RefUrl": "/notes/422138 "}, {"RefNumber": "407790", "RefComponent": "CA-CL-CHR", "RefTitle": "Characteristics maintenance: Update termination", "RefUrl": "/notes/407790 "}, {"RefNumber": "419046", "RefComponent": "CA-CL-CHR", "RefTitle": "Characteristic managem:Update termination value description", "RefUrl": "/notes/419046 "}, {"RefNumber": "415742", "RefComponent": "CA-CL-CHR", "RefTitle": "Overwriting: object dependencies not saved", "RefUrl": "/notes/415742 "}, {"RefNumber": "409510", "RefComponent": "CA-CL-CHR", "RefTitle": "Overwriting: Error when changing several characteristics", "RefUrl": "/notes/409510 "}, {"RefNumber": "403632", "RefComponent": "CA-CL-CHR", "RefTitle": "Overwrite: missing values", "RefUrl": "/notes/403632 "}, {"RefNumber": "412777", "RefComponent": "CA-CL-CHR", "RefTitle": "Overwriting: value description assigned incorrectly", "RefUrl": "/notes/412777 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_APPL", "From": "40B", "To": "40B", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "45B", "To": "45B", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "46B", "To": "46B", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "46C", "To": "46C", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "SAP_APPL 40B", "SupportPackage": "SAPKH40B61", "URL": "/supportpackage/SAPKH40B61"}, {"SoftwareComponentVersion": "SAP_APPL 40B", "SupportPackage": "SAPKH40B64", "URL": "/supportpackage/SAPKH40B64"}, {"SoftwareComponentVersion": "SAP_APPL 45B", "SupportPackage": "SAPKH45B39", "URL": "/supportpackage/SAPKH45B39"}, {"SoftwareComponentVersion": "SAP_APPL 45B", "SupportPackage": "SAPKH45B41", "URL": "/supportpackage/SAPKH45B41"}, {"SoftwareComponentVersion": "SAP_APPL 46B", "SupportPackage": "SAPKH46B29", "URL": "/supportpackage/SAPKH46B29"}, {"SoftwareComponentVersion": "SAP_APPL 46B", "SupportPackage": "SAPKH46B27", "URL": "/supportpackage/SAPKH46B27"}, {"SoftwareComponentVersion": "SAP_APPL 46C", "SupportPackage": "SAPKH46C15", "URL": "/supportpackage/SAPKH46C15"}, {"SoftwareComponentVersion": "SAP_APPL 46C", "SupportPackage": "SAPKH46C20", "URL": "/supportpackage/SAPKH46C20"}, {"SoftwareComponentVersion": "SAP_APPL 46C", "SupportPackage": "SAPKH46C17", "URL": "/supportpackage/SAPKH46C17"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": [{"SoftwareComponent": "SAP_APPL", "NumberOfCorrin": 4, "URL": "/corrins/0000375568/1"}]}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 4, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}