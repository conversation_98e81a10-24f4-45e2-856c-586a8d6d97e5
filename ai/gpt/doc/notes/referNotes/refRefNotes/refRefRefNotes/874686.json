{"Request": {"Number": "874686", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 759, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000004956822017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000874686?language=E&token=E9D35EADCFB3A7353D153626ABDA5C4A"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000874686", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000874686/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "874686"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 6}, "Recency": {"_label": "Recency", "key": "", "value": ""}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Program error"}, "Priority": {"_label": "Priority", "value": "Correction with low priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "18.11.2005"}, "SAPComponentKey": {"_label": "Component", "value": "PY-BR"}, "SAPComponentKeyText": {"_label": "Component", "value": "Brazil"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Payroll", "value": "PY", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'PY*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Brazil", "value": "PY-BR", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'PY-BR*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "874686 - HBRCFER0 - new BAdIs available for customer specific process"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>It is required that report HBRCFER0 permits some customer specific process for defining payment date and additional process before running Payroll.<br /></p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>HBRCFER0 ; f&#x00E9;rias; paydate; BADI;</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p></p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>For release 4.5B and 4.6B the BAdIs will not be created, but the new Type Pool and the changes in code of report HBRCFER0 were done in these releases due to code synchronization.<br /><br /><br />It is now included in report HBRCFER0 two Business Add-ins(BAdI) that<br />allow customers to develop a specific process for defining payment date and execute additional process before running Payroll.<br /><br /><br />The correction described in this note will be included in an HR Support<br />Package, as indicated in item \"Reference to Support Packages\".<br /><br />The support package includes:<br /></p> <UL><LI>a new type pool PBR30;</LI></UL> <UL><LI>change in include PCFERBR1, PCFERBR3 and PCFERBR6;</LI></UL> <UL><LI>Business Add-in(BADI) definition HRPAYBR_CFER_PAYDATE and HRPAYBR_CFER_PAYROLL;</LI></UL> <UL><LI>interface IF_EX_HRPAYBR_CFER_PAYDATE and IF_EX_HRPAYBR_CFER_PAYROLL;</LI></UL> <UL><LI>class CL_EX_HRPAYBR_CFER_PAYDATE AND CL_EX_HRPAYBR_CFER_PAYROLL;</LI></UL> <UL><LI>a new node in IMG under Payroll-&gt;Absences-&gt;Leave payment and then Business Add-Ins(BAdI).</LI></UL> <p><br /><br />The use of a BADI is optional, if you want to implement this BADI you<br />can do it using transaction SE19 or via IMG.<br /><br /><br />An Advanced Delivery is available for release 4.70(Enterprise) in the<br />attached file of name L6BKxxxxxx_470.CAR (\"xxxxxx\" means numbers).<br /><br />For more details about Advance Delivery installation procedure please<br />read the notes listed in \"Related Notes\".<br /></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "D034789"}, {"Key": "Processor                                                                                           ", "Value": "D035062"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000874686/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000874686/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000874686/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000874686/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000874686/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000874686/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000874686/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000874686/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000874686/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": [{"FileName": "L9CK197360_46C.CAR", "FileSize": "64", "MimeType": "application/octet-stream", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000327662005&iv_version=0006&iv_guid=DA7154DD7D8587429B32A7DE9785E141"}, {"FileName": "L6BK098829_470.CAR", "FileSize": "72", "MimeType": "application/octet-stream", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000327662005&iv_version=0006&iv_guid=7C21BD7B13287F44BCEF37470B445E95"}]}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "212876", "RefComponent": "BC-INS", "RefTitle": "The new archiving tool SAPCAR", "RefUrl": "/notes/212876"}, {"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "212876", "RefComponent": "BC-INS", "RefTitle": "The new archiving tool SAPCAR", "RefUrl": "/notes/212876 "}, {"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_HR", "From": "46C", "To": "46C", "Subsequent": ""}, {"SoftwareComponent": "SAP_HR", "From": "470", "To": "470", "Subsequent": ""}, {"SoftwareComponent": "SAP_HR", "From": "500", "To": "500", "Subsequent": ""}, {"SoftwareComponent": "SAP_HR", "From": "600", "To": "600", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "SAP_HR 46C", "SupportPackage": "SAPKE46CA4", "URL": "/supportpackage/SAPKE46CA4"}, {"SoftwareComponentVersion": "SAP_HR 470", "SupportPackage": "SAPKE47049", "URL": "/supportpackage/SAPKE47049"}, {"SoftwareComponentVersion": "SAP_HR 500", "SupportPackage": "SAPKE50015", "URL": "/supportpackage/SAPKE50015"}, {"SoftwareComponentVersion": "SAP_HR 600", "SupportPackage": "SAPKE60003", "URL": "/supportpackage/SAPKE60003"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": [{"SoftwareComponent": "SAP_HR", "NumberOfCorrin": 3, "URL": "/corrins/0000874686/2"}]}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 3, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 10, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": [{"SoftwareComponent": "SAP_HR", "ValidFrom": "45B", "ValidTo": "46C", "Number": "493275 ", "URL": "/notes/493275 ", "Title": "New version of Vacation Payment in Payroll", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "46C", "Number": "198139 ", "URL": "/notes/198139 ", "Title": "Relatório de adiantamento de férias - HBRCFER0", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "46C", "Number": "301058 ", "URL": "/notes/301058 ", "Title": "Report HBRCFER0 doesn't process vacations already paid", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "46C", "Number": "318955 ", "URL": "/notes/318955 ", "Title": "Vacation pay date (HBRCFER0)", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "46C", "Number": "427960 ", "URL": "/notes/427960 ", "Title": "HR_OC_INSERT_BATCH is called without MOLGA", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "46C", "Number": "445736 ", "URL": "/notes/445736 ", "Title": "Accidential Update of IT2001 in FERI offcycle", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "46C", "Number": "622377 ", "URL": "/notes/622377 ", "Title": "Vacation off-cycle at the vacation begin day", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "46C", "Number": "663058 ", "URL": "/notes/663058 ", "Title": "Vacation off-cycle after vacation begin date", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "470", "Number": "882948 ", "URL": "/notes/882948 ", "Title": "HBRCFER0 (vacations) may not update the payment date.", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "470", "ValidTo": "470", "Number": "617384 ", "URL": "/notes/617384 ", "Title": "Report dumps with error \"CX_HRPA_INVALID_PARAMETER\"", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "470", "ValidTo": "470", "Number": "622377 ", "URL": "/notes/622377 ", "Title": "Vacation off-cycle at the vacation begin day", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "470", "ValidTo": "470", "Number": "663058 ", "URL": "/notes/663058 ", "Title": "Vacation off-cycle after vacation begin date", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "500", "ValidTo": "500", "Number": "882948 ", "URL": "/notes/882948 ", "Title": "HBRCFER0 (vacations) may not update the payment date.", "Component": "PY-BR"}]}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}