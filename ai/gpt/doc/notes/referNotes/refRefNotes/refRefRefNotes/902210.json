{"Request": {"Number": "902210", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 2016, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "D", "_masterLanguage": "D", "_loadedLanguage": "D", "_machineTranslationLanguage": "E", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Acknowledge", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "If the quality of the machine translation is not sufficient for your purposes, click here to request a revised version.", "_type": "EVENT", "_visible": true}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000015992912017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000902210?language=E&token=3356E8A7D5F8E021A9CFD4998AB04556"}, "ShareByEmail": {"_label": "Share via Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000902210", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000902210/D/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "902210"}, "Type": {"_label": "Type", "value": "SAP-Hinweis"}, "Version": {"_label": "Version", "value": 5}, "Recency": {"_label": "Currentness", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "German"}, "MasterLanguage": {"_label": "Original Language", "value": "German"}, "Category": {"_label": "Category", "value": "Legal change"}, "Priority": {"_label": "Priority", "value": "Correction with medium priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "04.01.2006"}, "SAPComponentKey": {"_label": "Component", "value": "PY-AT-PS"}, "SAPComponentKeyText": {"_label": "Component", "value": "Public Sector"}, "SAPComponentPath": [{"_label": "Services &amp; Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs &amp; Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Payroll", "value": "PY", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'PY*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Austria", "value": "PY-AT", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'PY-AT*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Public Sector", "value": "PY-AT-PS", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'PY-AT-PS*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "902210 - Year-end legal change 2005/06 (public sector): Changes to BVA"}, "LongText": {"_label": "Description", "value": "<div id=\"DISCLAIMER\" class=\"sapMMsgStrip sapMMsgStripWarning sapUiSmallMarginTop sapUiMediumMarginBottom\"><div class=\"sapMMsgStripMessage\"><span dir=\"auto\" class=\"sapMText sapUiSelectable sapMTextMaxWidth\" style=\"text-align: left;\"><strong>ATTENTION!</strong> This is a machine-translated document that has not been reviewed for accuracy. To provide feedback regarding the machine translation, please <a style=\"color:var(--sapLinkColor);\" href=\"https://sapinsights.eu.qualtrics.com/jfe/form/SV_6nI2C1MOwEviVQG?NoteNumber=902210&TargetLanguage=EN&Component=PY-AT-PS&SourceLanguage=DE&Priority=03\" target=\"_blank\">click here</a>.<br/>You can access the original document via the following link: <a style=\"color:var(--sapLinkColor);\" href=\"/notes/902210/D\" target=\"_blank\">/notes/902210/D</a>.</span></div></div><div><div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>************************************************************************ Note has changed as of January 4, 2006. The change<BR/> affects the contribution group BVGD &quot;Low-Income Earner CF with DGA&quot;.<BR/> The percentages have changed. Please share the employer<BR/> percentages as follows: HI: 3.60%, AI: 0.47%, CI: 12.55%. The<BR/> Employer contributions are all zero. The changes have been made with the<BR/> the latest HR Support Package in accordance with this SAP Note. The changes are not<BR/> in the attachments according to the SAP Note and must therefore be<BR/> be maintained subsequently.<BR/> In feature AVKAT, maintain all your part-time employees<BR/> employee and assign the specification &#39;G&#39;.<BR/> This is necessary so that the report reports these as such using ELDA.<BR/> can be used.<BR/> ************************************************************************<br />According to the official information from the Insurance Institution for Public Service Wage and Salary Earners (BVA) from September 2005, the following changes have been made:</p> <OL>1. Part-time employees at the BVA</OL> <OL>2. Removal of the minimum contribution basis</OL> <OL>3. Contribution Statements:  New Contribution Types</OL><h3 data-toc-skip class=\"section\" id=\"Weitere Begriffe\">Other Terms</h3><p>Fiscal year change 2005/2006<br />Insurance Institution for Public Service Wage and Salary Earners (BVA)</p><h3 data-toc-skip class=\"section\" id=\"Ursache und Voraussetzungen\">Reason and Prerequisites</h3><p>Legal changes effective as of 01/01/2006</p><h3 data-toc-skip class=\"section\" id=\"L&ouml;sung\">Solution</h3><p>You can implement the changes in your system before the HR Support Package is available. To do this, we have prepared a file for your release in the attachment.<br />For Release 4.6B: PI6K013051.CAR For Release 4.6C: PI7K018964.SAR<BR/> For Release 4.70: L6BK105775.SAR<BR/> For Release ERP 2004: L6DK036911.SAR<BR/> For Release ERP 2005: L7DK007442.SAR<br />When you import the files, refer to the related SAP Notes.<br /><br />ad 1) Import the HR Support Package in accordance with this SAP Note. Note that the contributions relating to contractual civil servants with employer tax (DAG) must be displayed in the SI contribution statement for the first time in December 2006.<br />Action required: Assign the following new technical contribution groups for the new part-time employees as of January 01, 2006: BVGB BVA: Marginally Employed Civil Servant<BR/> BVGD BVA: Low-Income Earner CF with DGA<BR/> BVGE BVA: Low-Income Employed CF Without DGA<BR/> BVGM BVA: Part-Time Employee Mandate<br />and report the affected persons from the GKK to the BVA.<br /><br />ad 2) Import the HR Support Package in accordance with SAP Note 893697 (&quot;Year-end legal change 2005/06: SI minimum and maximum contribution bases as of 2006&quot;) in your system or make the following adjustments in advance:<br />Delimit the following entry in the view V_T511K as follows: Constant                            Start     End       Value<br />------------------------------------+----------+----------+-------------<BR/> V2MTK BVA Gen. HI M.Amt-Limits, day 01.01.2005 31.12.2005 18.15<br />                                     01.01.2006 31.12.9999 0,00<BR/> ------------------------------------+----------+----------+-------------<br />ad 3) Import the HR Support Package in accordance with this SAP Note. This contains extensive adjustments in payroll, in the monthly contribution statement, and in the form.<br />Action required: Assign the new value 08 in feature AVKBN for retired mandates after you have imported the HR Support Package. Please understand that we cannot provide the adjustments in advance.<br /><br />Note that these adjustments resulted in a change in the Customizing architecture. Tables T5APBS07, T5APBS08, T5APBS09, and T5APBS12 are no longer attached to the technical contribution group (data element AUBGR) but to the official contribution group (data element AMTBG) of the table T5A1B. The Customizing of the affected tables is adjusted accordingly with the delivery. If you use other technical contribution groups than those delivered by SAP, simply copy the value of the technical contribution group to the official contribution group. You can do this using the view cluster VC_5A1I using transaction SM34.</p></div></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "PY-AT (Austria)"}, {"Key": "Owner                                                                                    ", "Value": "D001888"}, {"Key": "Processor                                                                                          ", "Value": "D030823"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000902210/D"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "MIME Type", "URL": "URL"}, "Items": [{"FileName": "PI6K013051.CAR", "FileSize": "503", "MimeType": "application/octet-stream", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012003146900000677312005&iv_version=0005&iv_guid=74733AF149743449A1D2575F65C19B96"}, {"FileName": "L7DK007442.SAR", "FileSize": "354", "MimeType": "application/octet-stream", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012003146900000677312005&iv_version=0005&iv_guid=1B424A442CC79A4D88E86A5F24B92847"}, {"FileName": "L6BK105775.SAR", "FileSize": "342", "MimeType": "application/octet-stream", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012003146900000677312005&iv_version=0005&iv_guid=A967CE7E3787714FA220D7464FD605CE"}, {"FileName": "PI7K018964.SAR", "FileSize": "522", "MimeType": "application/octet-stream", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012003146900000677312005&iv_version=0005&iv_guid=2F6488EDFFC70547B0DE4A6FA048AC97"}, {"FileName": "L6DK036911.SAR", "FileSize": "359", "MimeType": "application/octet-stream", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012003146900000677312005&iv_version=0005&iv_guid=ABEED3DD92EAAD4C8BE4C8CB606F303D"}]}, "References": {"_label": "References", "RefTo": {"_label": "This document references", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "URL"}, "Items": [{"RefNumber": "925454", "RefComponent": "PY-AT-PS", "RefTitle": "Contribution Statement BVA: Contractual(DGA) with Special Payment", "RefUrl": "/notes/925454"}, {"RefNumber": "921028", "RefComponent": "PY-AT-PS", "RefTitle": "BVA: Reassignment for Contribution Types for Part-Time Employees with DAG", "RefUrl": "/notes/921028"}, {"RefNumber": "920869", "RefComponent": "PY-AT-PS", "RefTitle": "Monthly SI contribution statement BVA total amount incorrect", "RefUrl": "/notes/920869"}, {"RefNumber": "920711", "RefComponent": "PY-AT-PS", "RefTitle": "Incorrect delivery of SAP Note 902210 for project PM-SAP", "RefUrl": "/notes/920711"}, {"RefNumber": "915355", "RefComponent": "PY-AT-PS", "RefTitle": "Contribution Statement BVA: Contr. Cont. w/o DGA Not Monthly", "RefUrl": "/notes/915355"}, {"RefNumber": "913714", "RefComponent": "PY-AT-PS", "RefTitle": "ELDA BVA registration of part-time employees with DAG", "RefUrl": "/notes/913714"}, {"RefNumber": "913713", "RefComponent": "PY-AT-PS", "RefTitle": "Contribution statement BVA: DAG for contractual civil servants incorrect", "RefUrl": "/notes/913713"}, {"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Advance transports to customer (note for customers)", "RefUrl": "/notes/13719"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "URL"}, "Items": [{"RefNumber": "925454", "RefComponent": "PY-AT-PS", "RefTitle": "Contribution Statement BVA: Contractual(DGA) with Special Payment", "RefUrl": "/notes/925454 "}, {"RefNumber": "913713", "RefComponent": "PY-AT-PS", "RefTitle": "Contribution statement BVA: DAG for contractual civil servants incorrect", "RefUrl": "/notes/913713 "}, {"RefNumber": "913714", "RefComponent": "PY-AT-PS", "RefTitle": "ELDA BVA registration of part-time employees with DAG", "RefUrl": "/notes/913714 "}, {"RefNumber": "915355", "RefComponent": "PY-AT-PS", "RefTitle": "Contribution Statement BVA: Contr. Cont. w/o DGA Not Monthly", "RefUrl": "/notes/915355 "}, {"RefNumber": "920869", "RefComponent": "PY-AT-PS", "RefTitle": "Monthly SI contribution statement BVA total amount incorrect", "RefUrl": "/notes/920869 "}, {"RefNumber": "921028", "RefComponent": "PY-AT-PS", "RefTitle": "BVA: Reassignment for Contribution Types for Part-Time Employees with DAG", "RefUrl": "/notes/921028 "}, {"RefNumber": "920711", "RefComponent": "PY-AT-PS", "RefTitle": "Incorrect delivery of SAP Note 902210 for project PM-SAP", "RefUrl": "/notes/920711 "}, {"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Advance transports to customer (note for customers)", "RefUrl": "/notes/13719 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And the following"}, "Items": [{"SoftwareComponent": "HR-PS", "From": "461A", "To": "461A", "Subsequent": ""}, {"SoftwareComponent": "HR-PS", "From": "462A", "To": "462A", "Subsequent": ""}, {"SoftwareComponent": "SAP_HR", "From": "46C", "To": "46C", "Subsequent": ""}, {"SoftwareComponent": "SAP_HR", "From": "470", "To": "470", "Subsequent": ""}, {"SoftwareComponent": "SAP_HR", "From": "500", "To": "500", "Subsequent": ""}, {"SoftwareComponent": "SAP_HR", "From": "600", "To": "600", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software-Komponentenversion", "SupportPackage": "Support Package", "URL": "URL"}, "Items": [{"SoftwareComponentVersion": "HR-PS 461A", "SupportPackage": "SAPKISM664", "URL": "/supportpackage/SAPKISM664"}, {"SoftwareComponentVersion": "HR-PS 462A", "SupportPackage": "SAPKISM854", "URL": "/supportpackage/SAPKISM854"}, {"SoftwareComponentVersion": "HR-PS 462A", "SupportPackage": "SAPKISM856", "URL": "/supportpackage/SAPKISM856"}, {"SoftwareComponentVersion": "SAP_HR 46C", "SupportPackage": "SAPKE46CA6", "URL": "/supportpackage/SAPKE46CA6"}, {"SoftwareComponentVersion": "SAP_HR 470", "SupportPackage": "SAPKE47051", "URL": "/supportpackage/SAPKE47051"}, {"SoftwareComponentVersion": "SAP_HR 500", "SupportPackage": "SAPKE50017", "URL": "/supportpackage/SAPKE50017"}, {"SoftwareComponentVersion": "SAP_HR 600", "SupportPackage": "SAPKE60004", "URL": "/supportpackage/SAPKE60004"}]}, "SupportPackagePatch": {"_label": "Support Packages and Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "URL"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "URL"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about correction instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisite", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisite", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "URL", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated as helpful", "Helpful-Yes": "0 persons"}, "RatingQuality": {"_label": "Quality Assessment"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document eliminates side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "URL"}, "Items": []}, "SideEffectsSolving": {"_label": "This document causes side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "URL"}, "Items": []}}, "DisclaimerForMachineTranslation": {"Header": "ATTENTION!", "Message": "This is a machine-translated document that has not been reviewed for accuracy.", "Survey": "To provide feedback regarding the machine translation, please <a href=\"https://sapinsights.eu.qualtrics.com/jfe/form/SV_6nI2C1MOwEviVQG?NoteNumber=902210&TargetLanguage=EN&Component=PY-AT-PS&SourceLanguage=DE&Priority=03\" target=\"_blank\">click here</a>.", "Request": "If the quality of the machine translation is not sufficient for your purposes, click here to request a revised version.", "Origin": "You can access the original document via the following link: <a href=\"/notes/902210/D\" target=\"_blank\">/notes/902210/D</a>."}}}}