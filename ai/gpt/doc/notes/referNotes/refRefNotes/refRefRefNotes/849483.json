{"Request": {"Number": "849483", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 459, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000015903002017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000849483?language=E&token=28EDD205A868F900E43A36918A291F0D"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000849483", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000849483/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "849483"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 50}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Help for error analysis"}, "Priority": {"_label": "Priority", "value": "Correction with medium priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "08.03.2011"}, "SAPComponentKey": {"_label": "Component", "value": "BC-DB-ORA-DBA"}, "SAPComponentKeyText": {"_label": "Component", "value": "Database Administration"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Basis Components", "value": "BC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Database Interface, Database Platforms", "value": "BC-DB", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-DB*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Oracle", "value": "BC-DB-ORA", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-DB-ORA*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Database Administration", "value": "BC-DB-ORA-DBA", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-DB-ORA-DBA*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "849483 - Corrections for BR*Tools Version 7.00"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>This is a composite SAP Note for known problems in BR*Tools Version 7.00. The notes listed below contain detailed information about the problems that were solved.<br /><br />The most important enhancements in BR*Tools 7.00 are as follows:<br /> * Online conversion of LONG and LONG RAW fields to CLOBs and BLOBs (refer to Note 646681 for more information).<br /> * Tablespaces have been renamed.<br /> * Tables and indexes are reduced online.<br />For more detailed information about BR*Tools 7.00, see the SAP documentation:<br /><B>https://www.sdn.sap.com/irj/sdn/ora -&gt; Oracle DBA Overview -&gt;</B><br /><B>SAP Database Guide: Oracle</B><br /><br />You can use BR*Tools 7.00 for all SAP releases and kernel versions provided that the system is based on an Oracle 10g database.<br /><br />Using BR*Tools 7.00 with Oracle 9.2<br />------------------------------------------<br />BR*Tools 7.00 is used with Oracle 10g by default. This is also a prerequisite for most of the new features, such as those described above. However, you can also use the tools with Oracle 9.2 in order to use the new database-independent features from patch 7 or 8. To do this, you must first install Oracle Instant Client 10g on the database server.<br />For Unix platforms, see Note 819829 for more information.<br />Important: On some platforms, ORA-12705 errors may nevertheless occur in BR*Tools when establishing a connection to the database. In this case, create the following soft link in the Unix system library directory as the \"root\" user. Example for HP-UX PA Risc:<br />ln -s /oracle/client/instantclient/libociei.sl /usr/lib/pa20_64<br />or for SUN Solaris Sparc:<br />ln -s /oracle/client/instantclient/libociei.so /usr/lib/64<br />On Windows platforms, the Instant Client must be installed in a separate directory (such as in %ORACLE_HOME%\\InstClient, but not in %ORACLE_HOME%\\BIN or in the sap-exe directory). Copy the new BR*Tools into the directory, and delete the old tools from the sap-exe directory. In addition, set the environmental variable SAPEXE to the directory, and include it in the environmental variable PATH. BR*Tools must have patch level 13 or higher.<br />Important: These actions are not required if no ABAP application server runs on the database server (for example, if it is a standalone database server or a pure Java database). In that case, you can simply copy BR*Tools 7.00 and the Instant client into the sap-exe directory.</p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>BRARCHIVE, BRBACKUP, BRCONNECT, BRRESTORE, BRRECOVER, BRSPACE, BRTOOLS</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>The following problems were solved with patches for BR*Tools 7.00:<br /><br />Patch&#x00A0;&#x00A0;&#x00A0;&#x00A0;Date&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Note&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Tool<br /><br />&#x00A0;&#x00A0; 1&#x00A0;&#x00A0; MAY/31/2005&#x00A0;&#x00A0; 849484&#x00A0;&#x00A0;BRCONNECT<br />New CRITICAL_TABLESPACE check condition in BRCONNECT<br />&#x00A0;&#x00A0; 2&#x00A0;&#x00A0; MAY/31/2005&#x00A0;&#x00A0; 849485&#x00A0;&#x00A0;BRRECOVER<br />Reconstruction of the NOLOGGING indexes after recovery<br />&#x00A0;&#x00A0; 3&#x00A0;&#x00A0; JUL/26/2005&#x00A0;&#x00A0;&#x00A0;&#x00A0;865365&#x00A0;&#x00A0;BRCONNECT<br />Placeholder support in BRCONNECT parameters<br />&#x00A0;&#x00A0; 4&#x00A0;&#x00A0; JUL/26/2005&#x00A0;&#x00A0; 865366&#x00A0;&#x00A0;BRCONNECT<br />Changes in relation to collecting statistics for partitions<br />&#x00A0;&#x00A0; 5&#x00A0;&#x00A0; AUG/31/2005&#x00A0;&#x00A0; 874911&#x00A0;&#x00A0;BRSPACE<br />Workaround for ORA-31603 (BR0996E) during reorganization<br />&#x00A0;&#x00A0; 6&#x00A0;&#x00A0; AUG/31/2005&#x00A0;&#x00A0; 874912&#x00A0;&#x00A0;BRSPACE<br />Displaying extended database disk volume space in BRSPACE<br />&#x00A0;&#x00A0; 7&#x00A0;&#x00A0; OCT/26/2005&#x00A0;&#x00A0; 892294&#x00A0;&#x00A0;BRCONNECT<br />Extended support of non-ABAP databases in BRCONNECT<br />&#x00A0;&#x00A0; 8&#x00A0;&#x00A0; OCT/26/2005&#x00A0;&#x00A0; 892296&#x00A0;&#x00A0;BRCONNECT<br />Improvements to the collection of statistics in BRCONNECT<br />&#x00A0;&#x00A0; 9&#x00A0;&#x00A0; NOV/09/2005&#x00A0;&#x00A0; 896160&#x00A0;&#x00A0;BRBACKUP<br />BRBACKUP ignores file copy errors during disk backups<br />&#x00A0;&#x00A0;10&#x00A0;&#x00A0; JAN/05/2006&#x00A0;&#x00A0; 912969&#x00A0;&#x00A0;BR*Tools<br />BR*Tools 7.00 fails due to license problems<br />&#x00A0;&#x00A0;11&#x00A0;&#x00A0; JAN/11/2006&#x00A0;&#x00A0;&#x00A0;&#x00A0;914174&#x00A0;&#x00A0;BR*Tools<br />Minor functional enhancements in BR*Tools<br />&#x00A0;&#x00A0;12&#x00A0;&#x00A0; MAR/03/2006&#x00A0;&#x00A0; 900905&#x00A0;&#x00A0;BRSPACE<br />Creation of tablespaces with UNIFORM SIZE with BRSPACE<br />&#x00A0;&#x00A0;13&#x00A0;&#x00A0; MAR/29/2006&#x00A0;&#x00A0; 936665&#x00A0;&#x00A0;BR*Tools<br />BR*Tools support for MDM database<br />&#x00A0;&#x00A0;14&#x00A0;&#x00A0; MAY/23/2006&#x00A0;&#x00A0; 950787&#x00A0;&#x00A0;BRARCHIVE, BRCONNECT<br />Incorrect BRARCHIVE and BRCONNECT exit code<br />&#x00A0;&#x00A0;15&#x00A0;&#x00A0; JUL/28/2006&#x00A0;&#x00A0;&#x00A0;&#x00A0;968507&#x00A0;&#x00A0;BR*Tools<br />Enhancements to backups using BR*Tools 7.00<br />&#x00A0;&#x00A0;16&#x00A0;&#x00A0; AUG/11/2006&#x00A0;&#x00A0;&#x00A0;&#x00A0;972136&#x00A0;&#x00A0;BR*Tools<br />BR*Tools start error: Library libnnz10 not found<br />&#x00A0;&#x00A0;17&#x00A0;&#x00A0; AUG/29/2006&#x00A0;&#x00A0; 976435&#x00A0;&#x00A0;BRSPACE<br />Oracle Data Pump support in BRSPACE<br />&#x00A0;&#x00A0;18&#x00A0;&#x00A0; SEP/15/2006&#x00A0;&#x00A0; 981371&#x00A0;&#x00A0;BRARCHIVE<br />Backing up archive log files with RMAN-01009 fails<br />&#x00A0;&#x00A0;19&#x00A0;&#x00A0; OCT/30/2006&#x00A0;&#x00A0; 994136&#x00A0;&#x00A0;BRARCHIVE,BRBACKUP,BRRESTORE<br />Backups on hard disks fail with error BR0278E<br />&#x00A0;&#x00A0;20&#x00A0;&#x00A0; NOV/24/2006&#x00A0;&#x00A0;1003028&#x00A0;&#x00A0;BR*Tools<br />Enhanced support for system copy in BR*Tools<br />&#x00A0;&#x00A0;21&#x00A0;&#x00A0; JAN/10/2007&#x00A0;&#x00A0; 1016172&#x00A0;&#x00A0;BRSPACE<br />Sorting table records during the reorganization<br />&#x00A0;&#x00A0;22&#x00A0;&#x00A0; JAN/10/2007&#x00A0;&#x00A0; 1016173&#x00A0;&#x00A0;BRARCHIVE,BRBACKUP,BRRESTORE<br />Verification of database files and archive log files with RMAN<br />&#x00A0;&#x00A0;23&#x00A0;&#x00A0; MAR/01/2007&#x00A0;&#x00A0;1033125&#x00A0;&#x00A0;BRBACKUP,BRSPACE<br />BR*Tools support for Oracle 10g RAC<br />&#x00A0;&#x00A0;24&#x00A0;&#x00A0; MAR/01/2007&#x00A0;&#x00A0;1033126&#x00A0;&#x00A0;BRSPACE<br />Management of database statistics with BRSPACE<br />&#x00A0;&#x00A0;25&#x00A0;&#x00A0; APR/26/2007&#x00A0;&#x00A0;1050329&#x00A0;&#x00A0;BRARCHIVE,BRBACKUP,BRCONNECT,BRSPACE<br />BR*Tools fails with ORA-01455 for databases larger than 16 TB<br />&#x00A0;&#x00A0;26&#x00A0;&#x00A0; MAY/31/2007&#x00A0;&#x00A0;1060696&#x00A0;&#x00A0;BR*Tools<br />New BR*Tools command options<br />&#x00A0;&#x00A0;27&#x00A0;&#x00A0; JUL/05/2007&#x00A0;&#x00A0;&#x00A0;&#x00A0; 892296&#x00A0;&#x00A0;BRCONNECT<br />Further enhancements in statistics collection<br />&#x00A0;&#x00A0;28&#x00A0;&#x00A0; AUG/02/2007&#x00A0;&#x00A0;1080376&#x00A0;&#x00A0;BRSPACE<br />Enhancements in reorganization and rebuild<br />&#x00A0;&#x00A0;29&#x00A0;&#x00A0; OCT/10/2007&#x00A0;&#x00A0; 1101528&#x00A0;&#x00A0;BRSPACE<br />Longer runtime of table reorganisation<br />&#x00A0;&#x00A0;30&#x00A0;&#x00A0; OCT/10/2007&#x00A0;&#x00A0; 1101530&#x00A0;&#x00A0;BRARCHIVE,BRBACKUP,BRRESTORE<br />Support for RMAN savesets for backups on hard disk<br />&#x00A0;&#x00A0;31&#x00A0;&#x00A0; JAN/03/2008&#x00A0;&#x00A0;1129197&#x00A0;&#x00A0;BRARCHIVE,BRBACKUP,BRRESTORE<br />Terminations of BRARCHIVE, BRBACKUP and BRRESTORE runs<br />&#x00A0;&#x00A0;32&#x00A0;&#x00A0; FEB/05/2008&#x00A0;&#x00A0; 1138968&#x00A0;&#x00A0;BRBACKUP,BRCONNECT<br />Corrections for BR*Tools 7.00 Patch 31 and 7.10 Patch 7<br />&#x00A0;&#x00A0;33&#x00A0;&#x00A0; MAR/27/2008&#x00A0;&#x00A0;1155162&#x00A0;&#x00A0;BRSPACE<br />New option values for BRSPACE function \"dbshow\"<br />&#x00A0;&#x00A0;34&#x00A0;&#x00A0; MAY/27/2008&#x00A0;&#x00A0; 1173115&#x00A0;&#x00A0;BRRESTORE<br />RMAN Restore of raw files fails with ORA-19507<br />&#x00A0;&#x00A0;35&#x00A0;&#x00A0; JUL/29/2008&#x00A0;&#x00A0; 1235951&#x00A0;&#x00A0;BRBACKUP,BRCONNECT<br />SQL error ORA-01012 in BRCONNECT for offline backup<br />&#x00A0;&#x00A0;36&#x00A0;&#x00A0; JUL/29/2008&#x00A0;&#x00A0; 1235952&#x00A0;&#x00A0;BR*Tools<br />Minor functional enhancements in BR*Tools(2)<br />&#x00A0;&#x00A0;37&#x00A0;&#x00A0; OCT/08/2008&#x00A0;&#x00A0; 1259765&#x00A0;&#x00A0;BRBACKUP<br />BRBACKUP fails with error BR0164E for \"saveset_members\"<br />&#x00A0;&#x00A0;38&#x00A0;&#x00A0; OCT/08/2008&#x00A0;&#x00A0;1259766&#x00A0;&#x00A0;BRSPACE<br />Incomplete consistent table export with data pump<br />&#x00A0;&#x00A0;39&#x00A0;&#x00A0; NOV/27/2008&#x00A0;&#x00A0;1279681&#x00A0;&#x00A0;BRARCHIVE, BRRECOVER<br />Importing offline redolog files fails with SP2-0310<br />&#x00A0;&#x00A0;40&#x00A0;&#x00A0; JAN/28/2009&#x00A0;&#x00A0; 1299335&#x00A0;&#x00A0;BRARCHIVE<br />BRARCHIVE backup fails after reset log<br />&#x00A0;&#x00A0;41&#x00A0;&#x00A0; APR/01/2009&#x00A0;&#x00A0;1325242&#x00A0;&#x00A0;BRARCHIVE,BRBACKUP<br />BR*Tools fail when calling the function OpenService()<br />&#x00A0;&#x00A0;42&#x00A0;&#x00A0; Jul/02/2009&#x00A0;&#x00A0;1360542&#x00A0;&#x00A0;BRARCHIVE, BRRECOVER<br />Importing archive log files fails with BR0338E<br />&#x00A0;&#x00A0;43&#x00A0;&#x00A0; AUG/12/2009&#x00A0;&#x00A0; 1375023&#x00A0;&#x00A0;BRARCHIVE<br />BRARCHIVE does not save all summary logs on RAC<br />&#x00A0;&#x00A0;44&#x00A0;&#x00A0; OCT/28/2009&#x00A0;&#x00A0;1400845&#x00A0;&#x00A0;BRRECOVER<br />Setting up NOLOGGING index partitions again after recovery<br />&#x00A0;&#x00A0;45&#x00A0;&#x00A0; JAN/14/2010&#x00A0;&#x00A0; 1426635&#x00A0;&#x00A0;BRRESTORE<br />Restore of archive logs fails with BR0100E.<br />&#x00A0;&#x00A0;46&#x00A0;&#x00A0; JAN/26/2010&#x00A0;&#x00A0;1430669&#x00A0;&#x00A0;BR*Tools<br />BR*Tools support for Oracle 11g<br />&#x00A0;&#x00A0;47&#x00A0;&#x00A0; FEB/24/2010&#x00A0;&#x00A0; 1441450&#x00A0;&#x00A0;BRRECOVER<br />BRRECOVER fails with BR1333E for DB PIT recovery<br />&#x00A0;&#x00A0;48&#x00A0;&#x00A0; APR/29/2010&#x00A0;&#x00A0;1464155&#x00A0;&#x00A0;BRSPACE<br />Error in BRSPACE w/ options -f|-file and -a5|-autoextend5<br />&#x00A0;&#x00A0;49&#x00A0;&#x00A0; JUL/28/2010&#x00A0;&#x00A0; 1493500&#x00A0;&#x00A0;BR*Tools<br />BR*Tools terminate with segmentation fault (core dump)<br />&#x00A0;&#x00A0;50&#x00A0;&#x00A0; OCT/12/2010&#x00A0;&#x00A0;1518053&#x00A0;&#x00A0;BRSPACE<br />There is an error when copying redologs in the BRSPACE function \"dbcreate\".<br />&#x00A0;&#x00A0;51&#x00A0;&#x00A0; NOV/24/2010&#x00A0;&#x00A0;1532314&#x00A0;&#x00A0;BRSPACE<br />Endless loop in BRSPACE \"tbreorg\" during DDL creation<br />&#x00A0;&#x00A0;52&#x00A0;&#x00A0; MAR/02/2011&#x00A0;&#x00A0;1564526&#x00A0;&#x00A0;BRARCHIVE<br />Core dump in BRARCHIVE when importing Archivelog files<br /><br />The latest patch level for a particular tool is determined by the highest patch number that refers to a BR tool. This level corresponds to the current patch available on SAP Service Marketplace. A specific patch level contains all corrections with the same or a smaller patch number.</p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>Download the most recent BR*Tools patch from SAP<br />Service Marketplace. For more information about the precise procedure, see Notes 12741<br />and 19466.<br />Caution:<br />The program package for BR*Tools 7.00 for TRU64 64bit is not contained in the paths defined for Version 7.00, but in the paths defined for Version 6.40.</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "D000674"}, {"Key": "Processor                                                                                           ", "Value": "D000674"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "J", "TranslationLanguage": "日本語", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000849483/J"}, {"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000849483/D"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000849483/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000849483/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000849483/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000849483/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000849483/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000849483/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000849483/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "994136", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Backup to hard disk fails with error BR0278E", "RefUrl": "/notes/994136"}, {"RefNumber": "981371", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Backup of archive log files fails with RMAN-01009", "RefUrl": "/notes/981371"}, {"RefNumber": "976435", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Support for Oracle Data Pump in BRSPACE", "RefUrl": "/notes/976435"}, {"RefNumber": "972136", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "BR*Tools start error: Library libnnz10 not found", "RefUrl": "/notes/972136"}, {"RefNumber": "968507", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Considerable enhancements in backups using BR*Tools 7.00", "RefUrl": "/notes/968507"}, {"RefNumber": "950787", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Incorrect exit-code of BRARCHIVE and BRCONNECT", "RefUrl": "/notes/950787"}, {"RefNumber": "936665", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "BR*Tools support for MDM database", "RefUrl": "/notes/936665"}, {"RefNumber": "914174", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Minor functional enhancements in BR*Tools (1)", "RefUrl": "/notes/914174"}, {"RefNumber": "912969", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "BR*Tools fails due to license problems", "RefUrl": "/notes/912969"}, {"RefNumber": "900905", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Creating tablespaces with UNIFORM SIZE using BRSPACE", "RefUrl": "/notes/900905"}, {"RefNumber": "896160", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "BRBACKUP ignores file copy errors during disk backups", "RefUrl": "/notes/896160"}, {"RefNumber": "892296", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Enhancements in update statistics in BRCONNECT 7.00 / 7.10", "RefUrl": "/notes/892296"}, {"RefNumber": "892294", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Enhanced support for non-ABAP database in BRCONNECT 7.00", "RefUrl": "/notes/892294"}, {"RefNumber": "874911", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Workaround for ORA-31603 (BR0996E) during reorganization", "RefUrl": "/notes/874911"}, {"RefNumber": "865366", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Changes in relation to collecting statistics for partitions", "RefUrl": "/notes/865366"}, {"RefNumber": "865365", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Placeholder support in BRCONNECT parameters", "RefUrl": "/notes/865365"}, {"RefNumber": "849485", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Reconstruction of the NOLOGGING indexes after recovery", "RefUrl": "/notes/849485"}, {"RefNumber": "849484", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "New CRITICAL_TABLESPACE check condition in BRCONNECT", "RefUrl": "/notes/849484"}, {"RefNumber": "828268", "RefComponent": "BC-DB-ORA", "RefTitle": "Oracle Database 10g: New functions", "RefUrl": "/notes/828268"}, {"RefNumber": "19466", "RefComponent": "XX-SER-SAPSMP-SWC", "RefTitle": "Downloading SAP kernel patches", "RefUrl": "/notes/19466"}, {"RefNumber": "1564526", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Core dump in BRARCHIVE when importing archivelog files", "RefUrl": "/notes/1564526"}, {"RefNumber": "1532314", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Endless loop in BRSPACE-\"tbreorg\" for DDL creation", "RefUrl": "/notes/1532314"}, {"RefNumber": "1518053", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Error when copying redologs in BRSPACE \"dbcreate\"", "RefUrl": "/notes/1518053"}, {"RefNumber": "1493500", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "BR*Tools terminate with segmentation fault (core dump)", "RefUrl": "/notes/1493500"}, {"RefNumber": "1464155", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Error in BRSPACE w/ options -f|-file and -a5|-autoextend5", "RefUrl": "/notes/1464155"}, {"RefNumber": "1441450", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "BRRECOVER 7.00 fails with BR1333E for DB PIT recovery", "RefUrl": "/notes/1441450"}, {"RefNumber": "1430669", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "BR*Tools support for Oracle 11g", "RefUrl": "/notes/1430669"}, {"RefNumber": "1426635", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Restore of archive logs fails with BR0100E", "RefUrl": "/notes/1426635"}, {"RefNumber": "1400845", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Setting up NOLOGGING index partitions again after recovery", "RefUrl": "/notes/1400845"}, {"RefNumber": "1375023", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "BRARCHIVE does not save all summary logs on RAC", "RefUrl": "/notes/1375023"}, {"RefNumber": "1360542", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Importing archive log files fails with BR0338E", "RefUrl": "/notes/1360542"}, {"RefNumber": "1325242", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "BR*Tools fail when OpenService() function is called", "RefUrl": "/notes/1325242"}, {"RefNumber": "1299335", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "BRARCHIVE backup terminates after a reset log", "RefUrl": "/notes/1299335"}, {"RefNumber": "1279682", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Support for Oracle data encryption in BR*Tools", "RefUrl": "/notes/1279682"}, {"RefNumber": "1279681", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Importing offline redolog files fails with SP2-0310", "RefUrl": "/notes/1279681"}, {"RefNumber": "12741", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Current versions of BR*Tools", "RefUrl": "/notes/12741"}, {"RefNumber": "1259766", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Incomplete consistent table export using data pump", "RefUrl": "/notes/1259766"}, {"RefNumber": "1259765", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "BRBACKUP fails with error BR0164E for \"saveset_members\"", "RefUrl": "/notes/1259765"}, {"RefNumber": "1235952", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Minor functional enhancements in BR*Tools (2)", "RefUrl": "/notes/1235952"}, {"RefNumber": "1235951", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "SQL error ORA-01012 in BRCONNECT for offline backup", "RefUrl": "/notes/1235951"}, {"RefNumber": "1173115", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "RMAN Restore of raw data fails with ORA-19507", "RefUrl": "/notes/1173115"}, {"RefNumber": "1155162", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "New option values for BRSPACE function \"dbshow\"", "RefUrl": "/notes/1155162"}, {"RefNumber": "1138968", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Corrections for BR*Tools 7.00 Patch 31 and 7.10 Patch 7", "RefUrl": "/notes/1138968"}, {"RefNumber": "1129197", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Terminating BRARCHIVE, BRBACKUP and BRRESTORE runs", "RefUrl": "/notes/1129197"}, {"RefNumber": "1101530", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Support for RMAN savesets for backups on hard disk", "RefUrl": "/notes/1101530"}, {"RefNumber": "1101528", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Longer runtime of table reorganization", "RefUrl": "/notes/1101528"}, {"RefNumber": "1080376", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Enhancements for reorganization and rebuild", "RefUrl": "/notes/1080376"}, {"RefNumber": "1060696", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "New BR*Tools command options", "RefUrl": "/notes/1060696"}, {"RefNumber": "1050329", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "BR*Tools fails with ORA-01455 when database exceeds 16 TB", "RefUrl": "/notes/1050329"}, {"RefNumber": "1033126", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "BR*Tools support for Oracle 10g RAC", "RefUrl": "/notes/1033126"}, {"RefNumber": "1033125", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Managing database statistics with BRSPACE", "RefUrl": "/notes/1033125"}, {"RefNumber": "1016173", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Verifying database and archive log files using RMAN", "RefUrl": "/notes/1016173"}, {"RefNumber": "1016172", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Sorting table records during reorganization", "RefUrl": "/notes/1016172"}, {"RefNumber": "1003028", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Extended support for database copy in BR*Tools", "RefUrl": "/notes/1003028"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "892294", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Enhanced support for non-ABAP database in BRCONNECT 7.00", "RefUrl": "/notes/892294 "}, {"RefNumber": "12741", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Current versions of BR*Tools", "RefUrl": "/notes/12741 "}, {"RefNumber": "892296", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Enhancements in update statistics in BRCONNECT 7.00 / 7.10", "RefUrl": "/notes/892296 "}, {"RefNumber": "914174", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Minor functional enhancements in BR*Tools (1)", "RefUrl": "/notes/914174 "}, {"RefNumber": "1235952", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Minor functional enhancements in BR*Tools (2)", "RefUrl": "/notes/1235952 "}, {"RefNumber": "968507", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Considerable enhancements in backups using BR*Tools 7.00", "RefUrl": "/notes/968507 "}, {"RefNumber": "849484", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "New CRITICAL_TABLESPACE check condition in BRCONNECT", "RefUrl": "/notes/849484 "}, {"RefNumber": "912969", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "BR*Tools fails due to license problems", "RefUrl": "/notes/912969 "}, {"RefNumber": "1003028", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Extended support for database copy in BR*Tools", "RefUrl": "/notes/1003028 "}, {"RefNumber": "1129197", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Terminating BRARCHIVE, BRBACKUP and BRRESTORE runs", "RefUrl": "/notes/1129197 "}, {"RefNumber": "976435", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Support for Oracle Data Pump in BRSPACE", "RefUrl": "/notes/976435 "}, {"RefNumber": "1016172", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Sorting table records during reorganization", "RefUrl": "/notes/1016172 "}, {"RefNumber": "1430669", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "BR*Tools support for Oracle 11g", "RefUrl": "/notes/1430669 "}, {"RefNumber": "972136", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "BR*Tools start error: Library libnnz10 not found", "RefUrl": "/notes/972136 "}, {"RefNumber": "1360542", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Importing archive log files fails with BR0338E", "RefUrl": "/notes/1360542 "}, {"RefNumber": "1564526", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Core dump in BRARCHIVE when importing archivelog files", "RefUrl": "/notes/1564526 "}, {"RefNumber": "1259766", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Incomplete consistent table export using data pump", "RefUrl": "/notes/1259766 "}, {"RefNumber": "1532314", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Endless loop in BRSPACE-\"tbreorg\" for DDL creation", "RefUrl": "/notes/1532314 "}, {"RefNumber": "1441450", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "BRRECOVER 7.00 fails with BR1333E for DB PIT recovery", "RefUrl": "/notes/1441450 "}, {"RefNumber": "1518053", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Error when copying redologs in BRSPACE \"dbcreate\"", "RefUrl": "/notes/1518053 "}, {"RefNumber": "1279682", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Support for Oracle data encryption in BR*Tools", "RefUrl": "/notes/1279682 "}, {"RefNumber": "1493500", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "BR*Tools terminate with segmentation fault (core dump)", "RefUrl": "/notes/1493500 "}, {"RefNumber": "1464155", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Error in BRSPACE w/ options -f|-file and -a5|-autoextend5", "RefUrl": "/notes/1464155 "}, {"RefNumber": "874911", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Workaround for ORA-31603 (BR0996E) during reorganization", "RefUrl": "/notes/874911 "}, {"RefNumber": "1426635", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Restore of archive logs fails with BR0100E", "RefUrl": "/notes/1426635 "}, {"RefNumber": "1400845", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Setting up NOLOGGING index partitions again after recovery", "RefUrl": "/notes/1400845 "}, {"RefNumber": "849485", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Reconstruction of the NOLOGGING indexes after recovery", "RefUrl": "/notes/849485 "}, {"RefNumber": "1375023", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "BRARCHIVE does not save all summary logs on RAC", "RefUrl": "/notes/1375023 "}, {"RefNumber": "828268", "RefComponent": "BC-DB-ORA", "RefTitle": "Oracle Database 10g: New functions", "RefUrl": "/notes/828268 "}, {"RefNumber": "1080376", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Enhancements for reorganization and rebuild", "RefUrl": "/notes/1080376 "}, {"RefNumber": "1325242", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "BR*Tools fail when OpenService() function is called", "RefUrl": "/notes/1325242 "}, {"RefNumber": "1101530", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Support for RMAN savesets for backups on hard disk", "RefUrl": "/notes/1101530 "}, {"RefNumber": "1299335", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "BRARCHIVE backup terminates after a reset log", "RefUrl": "/notes/1299335 "}, {"RefNumber": "1016173", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Verifying database and archive log files using RMAN", "RefUrl": "/notes/1016173 "}, {"RefNumber": "1279681", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Importing offline redolog files fails with SP2-0310", "RefUrl": "/notes/1279681 "}, {"RefNumber": "1259765", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "BRBACKUP fails with error BR0164E for \"saveset_members\"", "RefUrl": "/notes/1259765 "}, {"RefNumber": "1033125", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Managing database statistics with BRSPACE", "RefUrl": "/notes/1033125 "}, {"RefNumber": "1235951", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "SQL error ORA-01012 in BRCONNECT for offline backup", "RefUrl": "/notes/1235951 "}, {"RefNumber": "1173115", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "RMAN Restore of raw data fails with ORA-19507", "RefUrl": "/notes/1173115 "}, {"RefNumber": "1155162", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "New option values for BRSPACE function \"dbshow\"", "RefUrl": "/notes/1155162 "}, {"RefNumber": "1101528", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Longer runtime of table reorganization", "RefUrl": "/notes/1101528 "}, {"RefNumber": "1138968", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Corrections for BR*Tools 7.00 Patch 31 and 7.10 Patch 7", "RefUrl": "/notes/1138968 "}, {"RefNumber": "1050329", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "BR*Tools fails with ORA-01455 when database exceeds 16 TB", "RefUrl": "/notes/1050329 "}, {"RefNumber": "1033126", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "BR*Tools support for Oracle 10g RAC", "RefUrl": "/notes/1033126 "}, {"RefNumber": "1060696", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "New BR*Tools command options", "RefUrl": "/notes/1060696 "}, {"RefNumber": "865365", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Placeholder support in BRCONNECT parameters", "RefUrl": "/notes/865365 "}, {"RefNumber": "896160", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "BRBACKUP ignores file copy errors during disk backups", "RefUrl": "/notes/896160 "}, {"RefNumber": "900905", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Creating tablespaces with UNIFORM SIZE using BRSPACE", "RefUrl": "/notes/900905 "}, {"RefNumber": "936665", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "BR*Tools support for MDM database", "RefUrl": "/notes/936665 "}, {"RefNumber": "981371", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Backup of archive log files fails with RMAN-01009", "RefUrl": "/notes/981371 "}, {"RefNumber": "994136", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Backup to hard disk fails with error BR0278E", "RefUrl": "/notes/994136 "}, {"RefNumber": "950787", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Incorrect exit-code of BRARCHIVE and BRCONNECT", "RefUrl": "/notes/950787 "}, {"RefNumber": "865366", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Changes in relation to collecting statistics for partitions", "RefUrl": "/notes/865366 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_BASIS", "From": "700", "To": "700", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}