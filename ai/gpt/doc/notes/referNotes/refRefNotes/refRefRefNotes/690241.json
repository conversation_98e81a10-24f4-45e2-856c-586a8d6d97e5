{"Request": {"Number": "690241", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 299, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000015579722017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000690241?language=E&token=8D224338C002C427B0B089C0267A1F04"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000690241", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000690241/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "690241"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 9}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Performance"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "06.03.2013"}, "SAPComponentKey": {"_label": "Component", "value": "BC-DB-ORA"}, "SAPComponentKeyText": {"_label": "Component", "value": "Oracle"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Basis Components", "value": "BC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Database Interface, Database Platforms", "value": "BC-DB", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-DB*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Oracle", "value": "BC-DB-ORA", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-DB-ORA*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "690241 - The Shared SQL Area in the Shared Pool and large SQL Stmnts"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>ORA-4031 in SAP R/3</p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>Oracle, shared pool, ORA-4031</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>Sizing of the shared pool for a SAP R3 system</p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p><STRONG>The Shared SQL Area in the Shared Pool and large SQL Statements<br /></STRONG><br />The shared SQL area is a part of the library cache in the shared pool.<br /><br />The total size of the shared pool is determined by the initialization parameter SHARED_POOL_SIZE. Increasing the value of this parameter increases the amount of memory reserved for the shared pool.<br /><br /><STRONG>Library Cache<br /></STRONG><br />The library cache includes the shared SQL areas, PL/SQL procedures, packages, and control structures such as locks and library cache handles. Shared SQL areas are accessible to all users, so the library cache is contained in the shared pool within the SGA.<br /><br /><STRONG>Shared SQL Areas and Private SQL Areas<br /></STRONG><br />Oracle represents each SQL statement it runs, with a shared SQL area and a private SQL area. Oracle recognizes when two users are executing the same SQL statement and reuses the shared SQL area for those users. However, each user must have a separate copy of the statements private SQL area.<br /><br />A shared SQL area contains the parse tree and execution plan for a given SQL statement. Oracle saves memory by using one shared SQL area for SQL statements run multiple times, which often happens when many users run the same application.<br /><br />Oracle allocates memory from the shared pool when a new SQL statement is parsed, to store in the shared SQL area. The size of this memory depends on the complexity of the statement. If the entire shared pool has already been allocated, Oracle can deallocate items from the pool using a modified LRU (least recently used) algorithm until there is enough free space for the new statements shared SQL area. If Oracle deallocates a shared SQL area, the associated SQL statement must be reparsed and reassigned to another shared SQL area at its next execution.<br /><br /><STRONG>Allocation and Reuse of Memory in the Shared Pool<br /></STRONG><br />In general, any item (shared SQL area or dictionary row) in the shared pool remains until it is flushed according to a modified LRU algorithm. The memory for items that are not being used regularly is freed if space is required for new items that must be allocated some space in the shared pool. A modified LRU algorithm allows shared pool items that are used by many sessions to remain in memory as long as they are useful, even if the process that originally created the item terminates. As a result, the overhead and processing of SQL statements associated with a multiuser Oracle system is minimized.<br /><br />When a SQL statement is submitted to Oracle for execution, Oracle automatically performs the following memory allocation steps:</p> <OL>1. Oracle checks the shared pool to see if a shared SQL area already exists for an identical statement. If so, that shared SQL area is used for the execution of the subsequent new instances of the statement.Alternatively, if there is no shared SQL area for a statement Oracle allocates a new shared SQL area in the shared pool. In either case, the users private SQL area is associated with the shared SQL area that contains the statement.</OL> <OL>2. Oracle allocates a private SQL area on behalf of the session. The location of the private SQL area depends on the type of connection established for the session.</OL> <p><br />Oracle also removes a shared SQL area from the shared pool in these circumstances:</p> <UL><LI>When the DBMS_STATS statement is used to update or delete the statistics of a table, cluster, or index, all shared SQL areas that contain statements referencing the analyzed schema object are removed from the shared pool. The next time a removed statement is run, the statement is parsed in a new shared SQL area to reflect the new statistics for the schema object.</LI></UL> <UL><LI>If a schema object is referenced in a SQL statement and that object is later modified in any way, the shared SQL area is invalidated (marked invalid), and the statement must be reparsed the next time it is run.</LI></UL> <UL><LI>If you change a databases global database name, all information is removed from the shared pool.</LI></UL> <UL><LI>The administrator can manually remove all information in the shared pool to assess the performance (with respect to the shared pool, not the data buffer cache) that can be expected after instance startup without shutting down the current instance. The statement ALTER SYSTEM FLUSH SHARED_POOL is used to do this.</LI></UL> <p><br />Note: A shared SQL area can be removed from the shared pool, even if the shared SQL area corresponds to an open cursor that has not been used for some time. If the open cursor is subsequently used to run its statement, Oracle reparses the statement, and a new shared SQL area is allocated in the shared pool.<br /><br /><STRONG>Large SQL Statements<br /></STRONG><br />SAP R/3 issues a specific class of SQL Statements with very large inlists, which need, when optimized with the Rule Based Optimizer (RBO), a lot of space in the shared pool. The Cost Based Optimizer(CBO) uses for this type of statements a new access path, which is called the \"inlist iterator\". This access path has a lot better ressource usage. It uses less CPU time and a lot less space in the shared pool. In the current R/3 releases the use of the RBO is in a couple of cases required, as the CBO will not always pick the \"inlist iterator\" access plan, and will instead use a full table scan. In future releases of R/3 it is planned to use an advanced option of the CBO, to make the use of the RBO no longer required for these statements.<br /><br />An example for such a statement is the following:<br />SELECT X1, X2, X3<br />FROM TAB1<br />WHERE X1 in (:b1, :b2, :b3, ...., :b500)<br /><br />The RBO will optimize this statement with a concatenation of 500 of the following simple statements:<br /><br />SELECT X1, X2, X3<br />FROM TAB1<br />WHERE X1 = :b(n)<br /><br />Each of these statements needs to allocate the space of a simple statement from the shared pool. The complete Statement will therefore allocate the space of 500 simple Statements. This could be for more complex ones a couple of 100 MBs.<br /><br />These large memory allocations require a relatively large shared pool for R/3 applications, as a couple of users might ask for quite large memory allocations at the same time. Shared pool sizes of more than 1GB are not uncommon.<br /><br /><STRONG>Large Buffercaches<br /></STRONG><br />Oracle needs for the management of large database buffer caches significant space in the shared pool. The space needed in the shared pool is app. 5 MB per 1 GB in the Buffer cache.<br /><br /><STRONG>Automatic Subpools<br /></STRONG><br />In Oracle the shared pool can be automatically split in multiple so-called subpools. Oracle splits the shared pool when you have at least 4 CPUs and the shared pool is large enough. This will improve the scalability of Oracle, as each subpool has its own management structures but limits also the maximal space that can be allocated for a single memory request.<br /><br />A shared pool, which is not split, might therefore accommodate SQL statements which require a couple of 100 MBs, and runs quite well with this special type of SQL statements. But as soon as you increase the number of CPUs or decrease the size of the shared pool, the same statement might get the error <B> ORA-04031 unable to allocate n  bytes of shared memory </B>. If a single SQL statement fills a subpool then it will get an ORA-4031. Hence if you have a large SQL statement in 8i it may work with a large shared pool but upgrading the same system to 9i or higher may cause the same statement to fail<br /><br /><STRONG>RAC Systems</STRONG><br /><br />To accomodate the additional storage areas a RAC system allocates in the shared pool, increase the size of the shared pool by 5% of the SGA_MAX_SIZE for RAC systems.<br /><br /><STRONG>Solution</STRONG><br /><br />To benefit from the improved scalability of having multiple subpools please set your SHARED_POOL_SIZE init.ora parameter for Oracle by using the following formula:<br /><br />SPS&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;=&#x00A0;&#x00A0;minimum SHARED_POOL_SIZE needed for Oracle non RAC<br />SPS_RAC&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;=&#x00A0;&#x00A0;minimum SHARED_POOL_SIZE needed for Oracle RAC<br />cpus&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;=&#x00A0;&#x00A0; CPU_COUNT<br />SGA_MAX_SIZE =&#x00A0;&#x00A0; Parameter SGA_MAX_SIZE in GB<br /><br />The existing values of SGA_MAX_SIZE and cpus can be queried from v$parameter:<br /><br />select name, value from v$parameter where name in ('sga_max_size', 'cpu_count');<br /><br />SPS =&#x00A0;&#x00A0;(cpus / 4 * 500 MB) + (SGA_MAX_SIZE * 5 MB) + 300 MB<br /><br />SPS_RAC = SPS + (SGA_MAX_SIZE * 50 MB)<br /><br />Examples:</p> <div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\"><TR><TH ALIGN=LEFT>CPUs SPS for 20GB SGA_MAX&#x00A0;&#x00A0;&#x00A0;&#x00A0;SPS for 200GB SGA_MAX</TH></TR> <TR><TD> <U>NON RAC</U> <U>&#x00A0;&#x00A0;RAC</U> <U>NON RAC</U> <U> RAC</U></TD></TR> <TR><TD>&#x00A0;&#x00A0; 4&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;900M&#x00A0;&#x00A0;&#x00A0;&#x00A0;1900M&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 1800M&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;11.8G</TD></TR> <TR><TD>&#x00A0;&#x00A0; 8&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;1400M&#x00A0;&#x00A0;&#x00A0;&#x00A0;2400M&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 2300M&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;12.3G</TD></TR> <TR><TD>&#x00A0;&#x00A0;12&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;1900M&#x00A0;&#x00A0;&#x00A0;&#x00A0;2900M&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 2800M&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;12.8G</TD></TR> <TR><TD>&#x00A0;&#x00A0;32&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;4400M&#x00A0;&#x00A0;&#x00A0;&#x00A0;5400M&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 5300M&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;15.3G</TD></TR> <TR><TD>&#x00A0;&#x00A0;64&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;8400M&#x00A0;&#x00A0;&#x00A0;&#x00A0;9400M&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 9300M&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;19.3G</TD></TR> <TR><TD> 128&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;16.4G&#x00A0;&#x00A0;&#x00A0;&#x00A0;17.4G&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 17.3G&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;27.3G</TD></TR> <TR><TD></TD></TR> </TABLE></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON> (C5025128)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (C5004095)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000690241/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000690241/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000690241/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000690241/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000690241/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000690241/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000690241/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000690241/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000690241/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "890797", "RefComponent": "CA-TDM-BUS-ERP", "RefTitle": "SAP TDMS - required and recommended system settings", "RefUrl": "/notes/890797"}, {"RefNumber": "869006", "RefComponent": "BC-DB-ORA", "RefTitle": "Composite SAP note: ORA-04031", "RefUrl": "/notes/869006"}, {"RefNumber": "830576", "RefComponent": "BC-DB-ORA", "RefTitle": "Parameter recommendations for Oracle 10g", "RefUrl": "/notes/830576"}, {"RefNumber": "789011", "RefComponent": "BC-DB-ORA", "RefTitle": "FAQ: Oracle memory areas", "RefUrl": "/notes/789011"}, {"RefNumber": "706132", "RefComponent": "BC-DB-ORA", "RefTitle": "Composite SAP Note: Problems with Oracle 9i", "RefUrl": "/notes/706132"}, {"RefNumber": "507254", "RefComponent": "BC-DB-ORA", "RefTitle": "ORA-4031 and ORA-3113 when processing large IN lists", "RefUrl": "/notes/507254"}, {"RefNumber": "505246", "RefComponent": "BC-DB-ORA", "RefTitle": "Several ora-600 [12333] and ora-4031 errors", "RefUrl": "/notes/505246"}, {"RefNumber": "1472386", "RefComponent": "BC-DB-ORA", "RefTitle": "ORA-04031 out of memory error in BW systems", "RefUrl": "/notes/1472386"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1873631", "RefComponent": "BC-DB-ORA", "RefTitle": "Solving Oracle memory issues", "RefUrl": "/notes/1873631 "}, {"RefNumber": "830576", "RefComponent": "BC-DB-ORA", "RefTitle": "Parameter recommendations for Oracle 10g", "RefUrl": "/notes/830576 "}, {"RefNumber": "789011", "RefComponent": "BC-DB-ORA", "RefTitle": "FAQ: Oracle memory areas", "RefUrl": "/notes/789011 "}, {"RefNumber": "890797", "RefComponent": "CA-TDM-BUS-ERP", "RefTitle": "SAP TDMS - required and recommended system settings", "RefUrl": "/notes/890797 "}, {"RefNumber": "869006", "RefComponent": "BC-DB-ORA", "RefTitle": "Composite SAP note: ORA-04031", "RefUrl": "/notes/869006 "}, {"RefNumber": "1472386", "RefComponent": "BC-DB-ORA", "RefTitle": "ORA-04031 out of memory error in BW systems", "RefUrl": "/notes/1472386 "}, {"RefNumber": "505246", "RefComponent": "BC-DB-ORA", "RefTitle": "Several ora-600 [12333] and ora-4031 errors", "RefUrl": "/notes/505246 "}, {"RefNumber": "706132", "RefComponent": "BC-DB-ORA", "RefTitle": "Composite SAP Note: Problems with Oracle 9i", "RefUrl": "/notes/706132 "}, {"RefNumber": "507254", "RefComponent": "BC-DB-ORA", "RefTitle": "ORA-4031 and ORA-3113 when processing large IN lists", "RefUrl": "/notes/507254 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": []}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}