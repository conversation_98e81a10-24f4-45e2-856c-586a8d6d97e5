{"Request": {"Number": "680046", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 2784, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000015563992017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000680046?language=E&token=DBFCF9213E0EC1CCF2797236A338B5B5"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000680046", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000680046/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "680046"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 46}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Help for error analysis"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "03.02.2010"}, "SAPComponentKey": {"_label": "Component", "value": "BC-DB-ORA-DBA"}, "SAPComponentKeyText": {"_label": "Component", "value": "Database Administration"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Basis Components", "value": "BC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Database Interface, Database Platforms", "value": "BC-DB", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-DB*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Oracle", "value": "BC-DB-ORA", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-DB-ORA*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Database Administration", "value": "BC-DB-ORA-DBA", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-DB-ORA-DBA*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "680046 - Corrections in BR*Tools Version 6.40"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>This is a composite SAP Note for known problems in BR*Tools Version 6.40. The notes listed below contain detailed information about the problems that were solved.<br /><br />Caution:<br />--------<br />1. BR*Tools 6.40 is released for use on production systems as of patch level 6. Do not continue to use earlier patches.<br />2. BR*Tools 6.40 is not compatible with earlier BR*Tools versions (for example, 6.20), particularly regarding the Restore/Recovery function. This means that BRRESTORE 6.40 can only restore backups that were carried out with BRBACKUP/BRARCHIVE 6.40. Likewise, BRRESTORE 6.20 also fails for backups of BRBACKUP/BRARCHIVE 6.40. However, to switch to BR*Tools 6.40 you do not need to perform any special actions. For more information, see Note 668640.<br />3. SAPDBA is no longer delivered with Web AS 6.40 (NetWeaver 04). The remaining functions that were not yet covered by BR*Tools (see Notes 403704 and 602497) are now taken over by BRSPACE. You can find more information in Note 647697.<br />4. You can use BR*Tools 6.40 for all SAP releases. A prerequisite for this, however, is that the database runs on Oracle 9.2.<br />5. You can continue to use SAPDBA 6.20 for Oracle 9.2 on SAP systems that are based on Web AS 6.40.<br />SAPDBA functions that are based on the internal call of BRBACKUP, BRARCHIVE BRRESTORE (backup, restore, recovery) are no longer compatible with BR*Tools 6.40. Instead, you must only use the BR*Tools. However, you can use SAPDBA 6.20 in Web AS 6.40 to execute functions that run independently of the BR*Tools, such as reorganization, tablespace management, changes to storage parameters. (The BRCONNECT 6.40 call for creating statistics when reorganizing with SAPDBA 6.20 works properly.)</p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>BRARCHIVE, BRBACKUP, BRCONNECT, BRRESTORE, BRRECOVER, BRSPACE, BRTOOLS</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>The following problems were solved with patches for BR*Tools 6.40:<br /><br />Patch&#x00A0;&#x00A0;&#x00A0;&#x00A0;Date&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Note&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Tool<br />-----&#x00A0;&#x00A0;----------&#x00A0;&#x00A0;-------&#x00A0;&#x00A0;-------------------------------------------<br />&#x00A0;&#x00A0;1&#x00A0;&#x00A0; 02/DEC/03&#x00A0;&#x00A0; 686546&#x00A0;&#x00A0;BRSPACE<br />New values for option \"-d\" of BRSPACE function \"-f tbreorg\"<br />&#x00A0;&#x00A0;2&#x00A0;&#x00A0; 17/DEC/03&#x00A0;&#x00A0; 691903&#x00A0;&#x00A0;BRSPACE<br />New main option -o|-output from BRSPACE<br />&#x00A0;&#x00A0; 3&#x00A0;&#x00A0;26/JAN/04&#x00A0;&#x00A0; 700733&#x00A0;&#x00A0;BRARCHIVE,BRBACKUP,BRRECOVER,BRRESTORE<br />Support for the scp command in BR*Tools<br />&#x00A0;&#x00A0;4&#x00A0;&#x00A0; 05/MAR/04&#x00A0;&#x00A0;713497&#x00A0;&#x00A0;BRSPACE<br />New BRSPACE options for \"Reorg\", \"Rebuild\" and \"Tablespace\"<br />&#x00A0;&#x00A0;5&#x00A0;&#x00A0; 19/MAR/04&#x00A0;&#x00A0; 719581&#x00A0;&#x00A0;BRCONNECT<br />Deleting old entries in arch&lt;DBSID&gt;.log with BRCONNECT<br />&#x00A0;&#x00A0;6&#x00A0;&#x00A0; 29/MAR/04&#x00A0;&#x00A0; 722879&#x00A0;&#x00A0;BRRECOVER<br />BRRECOVER support for Oracle RAC databases<br />&#x00A0;&#x00A0;7&#x00A0;&#x00A0; 07/MAY/04&#x00A0;&#x00A0; 735365&#x00A0;&#x00A0;BRSPACE<br />Creating tablespaces with BRSPACE fails with BR0157E<br />&#x00A0;&#x00A0;8&#x00A0;&#x00A0; 17/MAY/04&#x00A0;&#x00A0; 737598&#x00A0;&#x00A0;BRSPACE<br />New BRSPACE options for managing Reorg and Rebuild<br />&#x00A0;&#x00A0;9&#x00A0;&#x00A0; 28/MAY/04&#x00A0;&#x00A0; 741348&#x00A0;&#x00A0;BRRECOVER<br />Recovery of Index tablespaces with BRRECOVER without Restore<br />&#x00A0;&#x00A0;10&#x00A0;&#x00A0;23/JUN/04&#x00A0;&#x00A0; 748434&#x00A0;&#x00A0;BRSPACE<br />New BRSPACE function \"dbcreate\" - Create new database<br />&#x00A0;&#x00A0;11&#x00A0;&#x00A0;25/JUN/04&#x00A0;&#x00A0; 749041&#x00A0;&#x00A0;BRCONNECT<br />Workaround in BR*Tools for ORA-12158 on Tru64 Unix<br />&#x00A0;&#x00A0;12&#x00A0;&#x00A0;28/JUL/04&#x00A0;&#x00A0; 759839&#x00A0;&#x00A0;BRARCHIVE<br />Termination in BRARCHIVE when database has a MOUNT status<br />&#x00A0;&#x00A0;13&#x00A0;&#x00A0;28/SEP/04&#x00A0;&#x00A0; 777556&#x00A0;&#x00A0;BRRECOVER<br />BRRECOVER fails after structure change of database<br />&#x00A0;&#x00A0;14&#x00A0;&#x00A0;29/SEP/04&#x00A0;&#x00A0; 777823&#x00A0;&#x00A0;BRSPACE<br />Moving of temporary files fails with ORA-01511<br />&#x00A0;&#x00A0;15&#x00A0;&#x00A0;30/SEP/04&#x00A0;&#x00A0; 778426&#x00A0;&#x00A0;BRSPACE<br />Special exports and imports with BRSPACE<br />&#x00A0;&#x00A0;16&#x00A0;&#x00A0;28/OCT/04&#x00A0;&#x00A0; 786840&#x00A0;&#x00A0;BRSPACE<br />Tablespaces not deleted when recreating a database<br />&#x00A0;&#x00A0;17&#x00A0;&#x00A0;28/OCT/04&#x00A0;&#x00A0; 786921&#x00A0;&#x00A0;BRARCHIVE<br />Copying structural changes to the standby database<br />&#x00A0;&#x00A0;18&#x00A0;&#x00A0;02/DEC/04&#x00A0;&#x00A0; 797314&#x00A0;&#x00A0;BRSPACE<br />Generic keywords for tablespaces and data files<br />&#x00A0;&#x00A0;19&#x00A0;&#x00A0;02/DEC/04&#x00A0;&#x00A0; 797315&#x00A0;&#x00A0;BRARCHIVE<br />Enhanced processing for archive duplex destinations<br />&#x00A0;&#x00A0;20&#x00A0;&#x00A0;10/DEC/04&#x00A0;&#x00A0; 800096&#x00A0;&#x00A0;BRBACKUP<br />Directory name is missing in BRBACKUP message #DIR<br />&#x00A0;&#x00A0;21&#x00A0;&#x00A0;21/JAN/05&#x00A0;&#x00A0; 811637&#x00A0;&#x00A0;BRARCHIVE,BRBACKUP,BRRESTORE,BRCONNECT<br />New parameters and options for BR*Tools 6.40<br />&#x00A0;&#x00A0;22&#x00A0;&#x00A0;21/JAN/05&#x00A0;&#x00A0; 811638&#x00A0;&#x00A0;BRARCHIVE,BRBACKUP,BRRESTORE<br />Enhancements in the verification of backups<br />&#x00A0;&#x00A0;23&#x00A0;&#x00A0;17/FEB/05&#x00A0;&#x00A0; 819860&#x00A0;&#x00A0;BRBACKUP/BRCONNECT<br />Backup with \"util_file_online\" fails with ORA-01403<br />&#x00A0;&#x00A0;24&#x00A0;&#x00A0;10/MAR/05&#x00A0;&#x00A0; 827019&#x00A0;&#x00A0;BRCONNECT<br />Changed handling of temporary tablespaces in BRCONNECT<br />&#x00A0;&#x00A0;25&#x00A0;&#x00A0;10/MAR/05&#x00A0;&#x00A0; 827020&#x00A0;&#x00A0;BRSPACE<br />Workaround for ORA-00054 with Reorg and Rebuild<br />&#x00A0;&#x00A0;26&#x00A0;&#x00A0; 26/APR/05&#x00A0;&#x00A0; 841014&#x00A0;&#x00A0;BRSPACE<br />Error ORA-01580 in BRSPACE database space administration<br />&#x00A0;&#x00A0;27&#x00A0;&#x00A0; 31/MAY/05&#x00A0;&#x00A0; 849484&#x00A0;&#x00A0;BRCONNECT<br />New CRITICAL_TABLESPACE check condition in BRCONNECT<br />&#x00A0;&#x00A0;28&#x00A0;&#x00A0; 31/MAY/05&#x00A0;&#x00A0; 849485&#x00A0;&#x00A0;BRRECOVER<br />Reconstruction of the NOLOGGING indexes after recovery<br />&#x00A0;&#x00A0;29&#x00A0;&#x00A0; 20/JUN/05&#x00A0;&#x00A0; 855182&#x00A0;&#x00A0;BRSPACE<br />Error message BR1000E during table reorganization<br />&#x00A0;&#x00A0;30&#x00A0;&#x00A0; 20/JUN/05&#x00A0;&#x00A0; 855183&#x00A0;&#x00A0;BRRECOVER<br />Workaround for ORA-06553 when recreating the database<br />&#x00A0;&#x00A0;31&#x00A0;&#x00A0; 26/JUL/05&#x00A0;&#x00A0;&#x00A0;&#x00A0;865365&#x00A0;&#x00A0;BRCONNECT<br />Placeholder support in BRCONNECT parameters<br />&#x00A0;&#x00A0;32&#x00A0;&#x00A0; 26/JUL/05&#x00A0;&#x00A0;&#x00A0;&#x00A0;865366&#x00A0;&#x00A0;BRCONNECT<br />Changes in relation to collecting statistics for partitions<br />&#x00A0;&#x00A0;33&#x00A0;&#x00A0; 31/AUG/05&#x00A0;&#x00A0; 874911&#x00A0;&#x00A0;BRSPACE<br />Workaround for ORA-31603 (BR0996E) during reorganization<br />&#x00A0;&#x00A0;34&#x00A0;&#x00A0; 31/AUG/05&#x00A0;&#x00A0; 874912&#x00A0;&#x00A0;BRSPACE<br />Displaying extended database disk volume space in BRSPACE<br />&#x00A0;&#x00A0;35&#x00A0;&#x00A0; 09/NOV/05&#x00A0;&#x00A0; 896160&#x00A0;&#x00A0;BRBACKUP<br />BRBACKUP ignores file copy errors during disk backups<br />&#x00A0;&#x00A0;36&#x00A0;&#x00A0; 11/JAN/06&#x00A0;&#x00A0;&#x00A0;&#x00A0;914174&#x00A0;&#x00A0;BR*Tools<br />Minor functional enhancements in BR*Tools<br />&#x00A0;&#x00A0;37&#x00A0;&#x00A0; 03/MAR/06&#x00A0;&#x00A0; 900905&#x00A0;&#x00A0;BRSPACE<br />Creation of tablespaces with UNIFORM SIZE with BRSPACE<br />&#x00A0;&#x00A0;38&#x00A0;&#x00A0; 29/MAR/06&#x00A0;&#x00A0; 936665&#x00A0;&#x00A0;BR*Tools<br />BR*Tools support for MDM database<br />&#x00A0;&#x00A0;39&#x00A0;&#x00A0; 23/MAY/06&#x00A0;&#x00A0; 950787&#x00A0;&#x00A0;BRARCHIVE, BRCONNECT<br />Incorrect BRARCHIVE and BRCONNECT exit code<br />&#x00A0;&#x00A0;40&#x00A0;&#x00A0; 30/AUG/06&#x00A0;&#x00A0;&#x00A0;&#x00A0;976755&#x00A0;&#x00A0;BR*Tools<br />Incorrect message number in BR*Tools 6.40<br />&#x00A0;&#x00A0;41&#x00A0;&#x00A0; 28/DEC/06&#x00A0;&#x00A0; 1013000&#x00A0;&#x00A0;BRCONNECT<br />Corrections for ORA-06502 in DBMS_STATS in Oracle 9.2.0.8<br />&#x00A0;&#x00A0;42&#x00A0;&#x00A0; 23/JAN/07&#x00A0;&#x00A0; 1019965&#x00A0;&#x00A0;BRSPACE,BRCONNECT<br />Long initialization times for BRSPACE and BRCONNECT 6.40<br />&#x00A0;&#x00A0;43&#x00A0;&#x00A0; 26/APR/07&#x00A0;&#x00A0;1050329&#x00A0;&#x00A0;BRARCHIVE,BRBACKUP,BRCONNECT,BRSPACE<br />BR*Tools fails with ORA-01455 for databases larger than 16 TB<br />&#x00A0;&#x00A0;44&#x00A0;&#x00A0; 31/MAY/07&#x00A0;&#x00A0;&#x00A0;&#x00A0;1060696&#x00A0;&#x00A0;BR*Tools<br />New BR*Tools command options<br />&#x00A0;&#x00A0;45&#x00A0;&#x00A0; 02/AUG/07&#x00A0;&#x00A0; 1080376&#x00A0;&#x00A0;BRSPACE<br />Enhancements in reorganization and rebuild<br />&#x00A0;&#x00A0;46&#x00A0;&#x00A0; 10/OCT/07&#x00A0;&#x00A0; 1101528&#x00A0;&#x00A0;BRSPACE<br />Longer runtime of table reorganisation<br />&#x00A0;&#x00A0;47&#x00A0;&#x00A0; 03/JAN/08&#x00A0;&#x00A0;1129197&#x00A0;&#x00A0;BRARCHIVE,BRBACKUP,BRRESTORE<br />Terminations of BRARCHIVE, BRBACKUP and BRRESTORE runs<br />&#x00A0;&#x00A0;48&#x00A0;&#x00A0; 27/MAR/08&#x00A0;&#x00A0; 1155162&#x00A0;&#x00A0;BRSPACE<br />New option values for BRSPACE function \"dbshow\"<br />&#x00A0;&#x00A0;49&#x00A0;&#x00A0; 27/MAY/2008&#x00A0;&#x00A0; 1173115&#x00A0;&#x00A0;BRRESTORE<br />RMAN Restore of raw files fails with ORA-19507<br />&#x00A0;&#x00A0;50&#x00A0;&#x00A0; 08/OCT/08&#x00A0;&#x00A0; 1259765&#x00A0;&#x00A0;BRBACKUP<br />BRBACKUP fails with error BR0164E for \"saveset_members\"<br />&#x00A0;&#x00A0;51&#x00A0;&#x00A0; 12/AUG/09&#x00A0;&#x00A0; 1375023&#x00A0;&#x00A0;BRARCHIVE<br />BRARCHIVE does not save all summary logs on RAC<br />&#x00A0;&#x00A0;52&#x00A0;&#x00A0; 14/JAN/10&#x00A0;&#x00A0; 1426635&#x00A0;&#x00A0;BRRESTORE<br />Restore of archive logs fails with BR0100E.<br /><br />The latest patch level for a particular tool is determined by the highest patch number that refers to a BR tool. This level corresponds to the current patch available on SAP Service Marketplace. A specific patch level contains all corrections with the same or a smaller patch number.</p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>Download the latest patch for the relevant tools from SAP Service Marketplace. The precise procedure is described in Notes 12741 and 19466.</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "D000674"}, {"Key": "Processor                                                                                           ", "Value": "D000674"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000680046/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000680046/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000680046/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000680046/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000680046/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000680046/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000680046/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000680046/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000680046/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "976755", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Incorrect message numbers in BR*Tools 6.40", "RefUrl": "/notes/976755"}, {"RefNumber": "950787", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Incorrect exit-code of BRARCHIVE and BRCONNECT", "RefUrl": "/notes/950787"}, {"RefNumber": "914174", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Minor functional enhancements in BR*Tools (1)", "RefUrl": "/notes/914174"}, {"RefNumber": "900905", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Creating tablespaces with UNIFORM SIZE using BRSPACE", "RefUrl": "/notes/900905"}, {"RefNumber": "896160", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "BRBACKUP ignores file copy errors during disk backups", "RefUrl": "/notes/896160"}, {"RefNumber": "874912", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Displaying extended database disk volume space in BRSPACE", "RefUrl": "/notes/874912"}, {"RefNumber": "874911", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Workaround for ORA-31603 (BR0996E) during reorganization", "RefUrl": "/notes/874911"}, {"RefNumber": "865366", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Changes in relation to collecting statistics for partitions", "RefUrl": "/notes/865366"}, {"RefNumber": "865365", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Placeholder support in BRCONNECT parameters", "RefUrl": "/notes/865365"}, {"RefNumber": "855183", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Workaround for ORA-06553 when recreating the database", "RefUrl": "/notes/855183"}, {"RefNumber": "855182", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Error message BR1000E during table reorganization", "RefUrl": "/notes/855182"}, {"RefNumber": "849485", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Reconstruction of the NOLOGGING indexes after recovery", "RefUrl": "/notes/849485"}, {"RefNumber": "849484", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "New CRITICAL_TABLESPACE check condition in BRCONNECT", "RefUrl": "/notes/849484"}, {"RefNumber": "841014", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Error ORA-01580 in BRSPACE database space administration", "RefUrl": "/notes/841014"}, {"RefNumber": "827020", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Workaround for ORA-00054 during reorganization and rebuild", "RefUrl": "/notes/827020"}, {"RefNumber": "827019", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Changed handling of TEMP/UNDO tablespaces in BRCONNECT", "RefUrl": "/notes/827019"}, {"RefNumber": "819860", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Backup with \"util_file_online\" fails with ORA-01403", "RefUrl": "/notes/819860"}, {"RefNumber": "811638", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Enhancements in the verification of backups", "RefUrl": "/notes/811638"}, {"RefNumber": "811637", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "New parameters and options for BR*Tools 6.40", "RefUrl": "/notes/811637"}, {"RefNumber": "800096", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Directory name is missing in BRBACKUP message #DIR", "RefUrl": "/notes/800096"}, {"RefNumber": "797315", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Enhanced processing for archive duplex destinations", "RefUrl": "/notes/797315"}, {"RefNumber": "797314", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Generic keywords for tablespaces and data files", "RefUrl": "/notes/797314"}, {"RefNumber": "786921", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Copying structural changes to the standby database", "RefUrl": "/notes/786921"}, {"RefNumber": "786840", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Tablespaces not deleted when recreating a database", "RefUrl": "/notes/786840"}, {"RefNumber": "778700", "RefComponent": "FS-BA", "RefTitle": "Bank Analyzer: Database parameter Tool-BW", "RefUrl": "/notes/778700"}, {"RefNumber": "778426", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Special exports and imports with BRSPACE", "RefUrl": "/notes/778426"}, {"RefNumber": "777823", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Moving of temporary files fails with ORA-01511", "RefUrl": "/notes/777823"}, {"RefNumber": "777556", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "BRRECOVER fails after structure change of database", "RefUrl": "/notes/777556"}, {"RefNumber": "759839", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Termination in BRARCHIVE when database has a MOUNT status", "RefUrl": "/notes/759839"}, {"RefNumber": "749041", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Workaround in BR*Tools for ORA-12158 on Tru64 Unix", "RefUrl": "/notes/749041"}, {"RefNumber": "748434", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "New BRSPACE function \"dbcreate\" - Create new database", "RefUrl": "/notes/748434"}, {"RefNumber": "741348", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Using BRRECOVER w/out restore to recover index tablespaces", "RefUrl": "/notes/741348"}, {"RefNumber": "737598", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "New BRSPACE options for control of Reorg and Rebuild", "RefUrl": "/notes/737598"}, {"RefNumber": "735365", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Creating a tablespace with BRSPACE fails with BR0157E", "RefUrl": "/notes/735365"}, {"RefNumber": "722879", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "BRRECOVER support for Oracle-RAC databases", "RefUrl": "/notes/722879"}, {"RefNumber": "719581", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Deletion of old entries in arch<DBSID>.log and back<DBSID>.log with BRCONNECT", "RefUrl": "/notes/719581"}, {"RefNumber": "713497", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "New BRSPACE options for \"Reorg\", \"Rebuild\" and \"Tablespace\"", "RefUrl": "/notes/713497"}, {"RefNumber": "700733", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Support for the scp command in BR*Tools", "RefUrl": "/notes/700733"}, {"RefNumber": "691903", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "New main option -o|-output from BRSPACE", "RefUrl": "/notes/691903"}, {"RefNumber": "686546", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "New values for option \"-d\" of BRSPACE function \"-f tbreorg\"", "RefUrl": "/notes/686546"}, {"RefNumber": "668640", "RefComponent": "BC-DB-ORA-CCM", "RefTitle": "Corrections in DB transactions for Oracle (1)", "RefUrl": "/notes/668640"}, {"RefNumber": "647697", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "BRSPACE - New tool for Oracle database administration", "RefUrl": "/notes/647697"}, {"RefNumber": "646681", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Reorganization of tables with BRSPACE", "RefUrl": "/notes/646681"}, {"RefNumber": "19466", "RefComponent": "XX-SER-SAPSMP-SWC", "RefTitle": "Downloading SAP kernel patches", "RefUrl": "/notes/19466"}, {"RefNumber": "1426635", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Restore of archive logs fails with BR0100E", "RefUrl": "/notes/1426635"}, {"RefNumber": "1375023", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "BRARCHIVE does not save all summary logs on RAC", "RefUrl": "/notes/1375023"}, {"RefNumber": "12741", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Current versions of BR*Tools", "RefUrl": "/notes/12741"}, {"RefNumber": "1259765", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "BRBACKUP fails with error BR0164E for \"saveset_members\"", "RefUrl": "/notes/1259765"}, {"RefNumber": "1173115", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "RMAN Restore of raw data fails with ORA-19507", "RefUrl": "/notes/1173115"}, {"RefNumber": "1155162", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "New option values for BRSPACE function \"dbshow\"", "RefUrl": "/notes/1155162"}, {"RefNumber": "1129197", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Terminating BRARCHIVE, BRBACKUP and BRRESTORE runs", "RefUrl": "/notes/1129197"}, {"RefNumber": "1101528", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Longer runtime of table reorganization", "RefUrl": "/notes/1101528"}, {"RefNumber": "1080376", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Enhancements for reorganization and rebuild", "RefUrl": "/notes/1080376"}, {"RefNumber": "1060696", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "New BR*Tools command options", "RefUrl": "/notes/1060696"}, {"RefNumber": "1050329", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "BR*Tools fails with ORA-01455 when database exceeds 16 TB", "RefUrl": "/notes/1050329"}, {"RefNumber": "1019965", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Long initialization times for BRSPACE and BRCONNECT 6.40", "RefUrl": "/notes/1019965"}, {"RefNumber": "1013000", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Corrections for ORA-06502 in DBMS_STATS in Oracle 9.2.0.8", "RefUrl": "/notes/1013000"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "12741", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Current versions of BR*Tools", "RefUrl": "/notes/12741 "}, {"RefNumber": "646681", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Reorganization of tables with BRSPACE", "RefUrl": "/notes/646681 "}, {"RefNumber": "914174", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Minor functional enhancements in BR*Tools (1)", "RefUrl": "/notes/914174 "}, {"RefNumber": "849484", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "New CRITICAL_TABLESPACE check condition in BRCONNECT", "RefUrl": "/notes/849484 "}, {"RefNumber": "778426", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Special exports and imports with BRSPACE", "RefUrl": "/notes/778426 "}, {"RefNumber": "748434", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "New BRSPACE function \"dbcreate\" - Create new database", "RefUrl": "/notes/748434 "}, {"RefNumber": "1129197", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Terminating BRARCHIVE, BRBACKUP and BRRESTORE runs", "RefUrl": "/notes/1129197 "}, {"RefNumber": "827019", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Changed handling of TEMP/UNDO tablespaces in BRCONNECT", "RefUrl": "/notes/827019 "}, {"RefNumber": "874911", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Workaround for ORA-31603 (BR0996E) during reorganization", "RefUrl": "/notes/874911 "}, {"RefNumber": "1019965", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Long initialization times for BRSPACE and BRCONNECT 6.40", "RefUrl": "/notes/1019965 "}, {"RefNumber": "1426635", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Restore of archive logs fails with BR0100E", "RefUrl": "/notes/1426635 "}, {"RefNumber": "849485", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Reconstruction of the NOLOGGING indexes after recovery", "RefUrl": "/notes/849485 "}, {"RefNumber": "1375023", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "BRARCHIVE does not save all summary logs on RAC", "RefUrl": "/notes/1375023 "}, {"RefNumber": "1080376", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Enhancements for reorganization and rebuild", "RefUrl": "/notes/1080376 "}, {"RefNumber": "827020", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Workaround for ORA-00054 during reorganization and rebuild", "RefUrl": "/notes/827020 "}, {"RefNumber": "1259765", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "BRBACKUP fails with error BR0164E for \"saveset_members\"", "RefUrl": "/notes/1259765 "}, {"RefNumber": "1173115", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "RMAN Restore of raw data fails with ORA-19507", "RefUrl": "/notes/1173115 "}, {"RefNumber": "1155162", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "New option values for BRSPACE function \"dbshow\"", "RefUrl": "/notes/1155162 "}, {"RefNumber": "668640", "RefComponent": "BC-DB-ORA-CCM", "RefTitle": "Corrections in DB transactions for Oracle (1)", "RefUrl": "/notes/668640 "}, {"RefNumber": "1101528", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Longer runtime of table reorganization", "RefUrl": "/notes/1101528 "}, {"RefNumber": "719581", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Deletion of old entries in arch<DBSID>.log and back<DBSID>.log with BRCONNECT", "RefUrl": "/notes/719581 "}, {"RefNumber": "1013000", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Corrections for ORA-06502 in DBMS_STATS in Oracle 9.2.0.8", "RefUrl": "/notes/1013000 "}, {"RefNumber": "1050329", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "BR*Tools fails with ORA-01455 when database exceeds 16 TB", "RefUrl": "/notes/1050329 "}, {"RefNumber": "1060696", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "New BR*Tools command options", "RefUrl": "/notes/1060696 "}, {"RefNumber": "647697", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "BRSPACE - New tool for Oracle database administration", "RefUrl": "/notes/647697 "}, {"RefNumber": "865365", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Placeholder support in BRCONNECT parameters", "RefUrl": "/notes/865365 "}, {"RefNumber": "797315", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Enhanced processing for archive duplex destinations", "RefUrl": "/notes/797315 "}, {"RefNumber": "896160", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "BRBACKUP ignores file copy errors during disk backups", "RefUrl": "/notes/896160 "}, {"RefNumber": "900905", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Creating tablespaces with UNIFORM SIZE using BRSPACE", "RefUrl": "/notes/900905 "}, {"RefNumber": "976755", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Incorrect message numbers in BR*Tools 6.40", "RefUrl": "/notes/976755 "}, {"RefNumber": "950787", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Incorrect exit-code of BRARCHIVE and BRCONNECT", "RefUrl": "/notes/950787 "}, {"RefNumber": "786921", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Copying structural changes to the standby database", "RefUrl": "/notes/786921 "}, {"RefNumber": "855183", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Workaround for ORA-06553 when recreating the database", "RefUrl": "/notes/855183 "}, {"RefNumber": "865366", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Changes in relation to collecting statistics for partitions", "RefUrl": "/notes/865366 "}, {"RefNumber": "778700", "RefComponent": "FS-BA", "RefTitle": "Bank Analyzer: Database parameter Tool-BW", "RefUrl": "/notes/778700 "}, {"RefNumber": "874912", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Displaying extended database disk volume space in BRSPACE", "RefUrl": "/notes/874912 "}, {"RefNumber": "713497", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "New BRSPACE options for \"Reorg\", \"Rebuild\" and \"Tablespace\"", "RefUrl": "/notes/713497 "}, {"RefNumber": "737598", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "New BRSPACE options for control of Reorg and Rebuild", "RefUrl": "/notes/737598 "}, {"RefNumber": "855182", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Error message BR1000E during table reorganization", "RefUrl": "/notes/855182 "}, {"RefNumber": "841014", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Error ORA-01580 in BRSPACE database space administration", "RefUrl": "/notes/841014 "}, {"RefNumber": "786840", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Tablespaces not deleted when recreating a database", "RefUrl": "/notes/786840 "}, {"RefNumber": "686546", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "New values for option \"-d\" of BRSPACE function \"-f tbreorg\"", "RefUrl": "/notes/686546 "}, {"RefNumber": "691903", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "New main option -o|-output from BRSPACE", "RefUrl": "/notes/691903 "}, {"RefNumber": "700733", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Support for the scp command in BR*Tools", "RefUrl": "/notes/700733 "}, {"RefNumber": "741348", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Using BRRECOVER w/out restore to recover index tablespaces", "RefUrl": "/notes/741348 "}, {"RefNumber": "749041", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Workaround in BR*Tools for ORA-12158 on Tru64 Unix", "RefUrl": "/notes/749041 "}, {"RefNumber": "777556", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "BRRECOVER fails after structure change of database", "RefUrl": "/notes/777556 "}, {"RefNumber": "777823", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Moving of temporary files fails with ORA-01511", "RefUrl": "/notes/777823 "}, {"RefNumber": "797314", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Generic keywords for tablespaces and data files", "RefUrl": "/notes/797314 "}, {"RefNumber": "800096", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Directory name is missing in BRBACKUP message #DIR", "RefUrl": "/notes/800096 "}, {"RefNumber": "811637", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "New parameters and options for BR*Tools 6.40", "RefUrl": "/notes/811637 "}, {"RefNumber": "811638", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Enhancements in the verification of backups", "RefUrl": "/notes/811638 "}, {"RefNumber": "819860", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Backup with \"util_file_online\" fails with ORA-01403", "RefUrl": "/notes/819860 "}, {"RefNumber": "735365", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Creating a tablespace with BRSPACE fails with BR0157E", "RefUrl": "/notes/735365 "}, {"RefNumber": "759839", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Termination in BRARCHIVE when database has a MOUNT status", "RefUrl": "/notes/759839 "}, {"RefNumber": "722879", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "BRRECOVER support for Oracle-RAC databases", "RefUrl": "/notes/722879 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_BASIS", "From": "640", "To": "640", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}