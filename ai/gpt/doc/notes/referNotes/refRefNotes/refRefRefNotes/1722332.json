{"Request": {"Number": "1722332", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 3132, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000010233502017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001722332?language=E&token=127A8D55540095424FB26A030A68BBB4"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001722332", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0001722332/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1722332"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 21}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Program error"}, "Priority": {"_label": "Priority", "value": "Correction with high priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "16.12.2014"}, "SAPComponentKey": {"_label": "Component", "value": "SV-SMG-INS-CFG"}, "SAPComponentKeyText": {"_label": "Component", "value": "Setup and Configuration of the Solution Manager system"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Service", "value": "SV", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'SV*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "SAP Solution Manager", "value": "SV-SMG", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'SV-SMG*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Installation, Configuration and Upgrade of Solution Manager", "value": "SV-SMG-INS", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'SV-SMG-INS*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Setup and Configuration of the Solution Manager system", "value": "SV-SMG-INS-CFG", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'SV-SMG-INS-CFG*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1722332 - SAP Solution Manager 7.1 SP6 - Basic functions"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>This SAP Note is the central correction note of SAP Solution Manager 7.1 Support Package 6. This SAP Note is necessary to guarantee the basic functions of your SAP Solution Manager. It is a composite SAP Note that is linked to additional notes.</p>\r\n<p>Important: This SAP Note is no longer updated. However, you can still import the final version into your systems. If you require new information about important SAP Notes for SAP Solution Manager 7.1 SP6, create a customer message using the component SV-SMG-INS-CFG.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>Basic Configuration, basic configuration, SAP Solution Manager</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p>You should implement this SAP Note by default using the SAP Solution Manager configuration (SOLMAN_SETUP).</p>\r\n<p>You must perform the following procedure <strong>before</strong> implementing this SAP Note:</p>\r\n<ol>\r\n<li>Update the available SAP Notes</li>\r\n<li>Update the Note Assistant</li>\r\n<li>Perform an SPAU adjustment</li>\r\n<li>Ensure that there are no inactive objects in your system</li>\r\n</ol>\r\n<p>For more detailed information, refer to the manual pre-implementation steps.</p>\r\n<p>Additional tips:</p>\r\n<ul>\r\n<li>Do not interrupt the implementation of this SAP Note.</li>\r\n<li>If the system issues the error message &quot;SAP Note 0000000000 does not exist&quot;, proceed as described in SAP Note 1935301.</li>\r\n<li>If the system issues an error message indicating that an SAP Note is &quot;incomplete&quot; or &quot;not released&quot;, use the Note Assistant to download the SAP Note separately. Then, continue with the implementation of this SAP Note.</li>\r\n</ul>\r\n<p><strong>This SAP Note is divided into three sections:</strong></p>\r\n<ol style=\"list-style-type: upper-roman;\">\r\n<li>Corrections with manual steps</li>\r\n<li>Additional information (SAP Notes for managed systems, FAQ notes, general SAP Notes)</li>\r\n<li>SAP Notes that can be implemented automatically</li>\r\n</ol>\r\n<p><br />The &quot;Date&quot; column contains the day on which the listed SAP Note was included in this SAP Note, or when a relevant change was made. Go through the SAP Notes in the specified order (from top to bottom).<br /><br />All of the SAP Notes are listed again under &quot;References&quot;.</p>\r\n<p><br /><br />######################################################################<br />1) Corrections with manual steps<br />######################################################################<br /><br />The SAP Notes of this section are divided into three groups:<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;I. 1) SAP Notes that you must implement manually using transaction SNOTE. The reason for this may be, for example, that manual pre-implementation steps are required or that the SAP Note requires a large amount of memory space.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;I.2) SAP Notes for which you must still carry out manual activities after you implement this central correction note<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;I. 3) SAP Notes whose manual steps can be carried out automatically using postprocessing in the SAP Solution Manager configuration (SOLMAN_SETUP -&gt; System Preparation -&gt; 3. &quot;Implement SAP Note -&gt; Postprocess&quot;).<br /><br />The category indicates the type of manual activity. Possible values:</p>\r\n<ul>\r\n<li>A = Mandatory; you must carry this out.</li>\r\n</ul>\r\n<ul>\r\n<li>B = Optional, if required (for example, error message texts, starting reports manually)</li>\r\n</ul>\r\n<p><br />I.1)</p>\r\n<p>11/12/2014</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\">\r\n<tbody>\r\n<tr><th>Category  SAP Note number  Short text (may be abridged)</th></tr>\r\n<tr>\r\n<td>\r\n<p>B&#x00A0;&#x00A0; 1873727&#x00A0;&#x00A0; RAISE_EXCEPTION &#39;CANCELLED&#39; for object catalog entry</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>B&#x00A0;&#x00A0; 2085201&#x00A0;&#x00A0; OSS Upload - Data Preparation for DbTypeForSAP property</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>B&#x00A0;&#x00A0; 2091644&#x00A0;&#x00A0; OSS Upload - Enhancement of Upload for DbTypeForSAP Property</p>\r\n</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p>&#x00A0;7/5/2014</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\">\r\n<tbody>\r\n<tr><th>Category  SAP Note number  Short text (may be abridged)</th></tr>\r\n<tr>\r\n<td>A   1935993     SMSY: EHP7 - Product data is missing</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p>17/2/2014</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\">\r\n<tbody>\r\n<tr><th>Category  SAP Note number  Short text (may be abridged)</th></tr>\r\n<tr>\r\n<td>\r\n<p>B   1710578     BI Content Activation Errors in SolMan </p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>B   1810837     BI Content Activation Errors in SolMan </p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>B   1849098     Error handling in content synchronization</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>B&#x00A0;  1906937     Corrections for unified rendering 702/15 (UR-mimes)</p>\r\n</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><br />27/2/2013</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\">\r\n<tbody>\r\n<tr><th>Category  SAP Note number  Short text (may be abridged)</th></tr>\r\n<tr>\r\n<td>B   1745794     Data Collectors corrections: ST-A/PI 01P*</td>\r\n</tr>\r\n<tr>\r\n<td>B   1803778     Dump in class CL_AC_TEMPLATE_REPOSITORY</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><br />13/11/2012</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\">\r\n<tbody>\r\n<tr><th>Category  SAP Note number  Short text (may be abridged)</th></tr>\r\n<tr>\r\n<td>A   1765689     SOLMAN_SETUP: Warning in managed system...</td>\r\n</tr>\r\n<tr>\r\n<td>B   1365796     Local host name resolution fails in Windows Failover</td>\r\n</tr>\r\n<tr>\r\n<td>This SAP Note is required only for Windows Server 2008 and Windows Server 2008 R2.</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><br />20/09/2012</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\">\r\n<tbody>\r\n<tr><th>Category  SAP Note number  Short text (may be abridged)</th></tr>\r\n<tr>\r\n<td>A   1678162     Solution Manager Setup: Performance when copying roles</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><br />22/08/2012</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\">\r\n<tbody>\r\n<tr><th>Category  SAP Note number  Short text (may be abridged)</th></tr>\r\n<tr>\r\n<td>B   1653635     New HANA Extraktors for BASIS SP10;SP11</td>\r\n</tr>\r\n<tr>\r\n<td>B   1702711     Adjustments for the Solution Manager update</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><br />13/08/2012</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\">\r\n<tbody>\r\n<tr><th>Category  SAP Note number  Short text (may be abridged)</th></tr>\r\n<tr>\r\n<td>A   1572183     Authorizations for SAP Solution Manager RFC...</td>\r\n</tr>\r\n<tr>\r\n<td>A   1682370     POWL: Removal of &quot;Reapply Table settings&quot;...</td>\r\n</tr>\r\n<tr>\r\n<td>A   1710384     WD ABAP ALV performance improvements</td>\r\n</tr>\r\n<tr>\r\n<td>A   1737347     structure COMT_PROD_SRV_MAINTAIN_API contains</td>\r\n</tr>\r\n<tr>\r\n<td>A   1746625     Extended Search in Business Process Repository</td>\r\n</tr>\r\n<tr>\r\n<td>B   1697052     WDA: Link in FormattedTextView causes a dump</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><br />I.2)<br /><br /></p>\r\n<p>12/11/2014</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\">\r\n<tbody>\r\n<tr><th>Category  SAP Note number  Short text (may be abridged)</th></tr>\r\n<tr>\r\n<td>\r\n<p>A&#x00A0;&#x00A0;1980254&#x00A0;&#x00A0; Relevant roles aren&#39;t updated in a CUA child system via SOLMAN_SETUP</p>\r\n</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p>7/5/2014</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\">\r\n<tbody>\r\n<tr><th>Category  SAP Note number  Short text (may be abridged)</th></tr>\r\n<tr>\r\n<td>B&#x00A0;&#x00A0; 1990166&#x00A0;&#x00A0; Deletion of RFC destinations</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p>2/17/2014</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\">\r\n<tbody>\r\n<tr><th>Category  SAP Note number  Short text (may be abridged)</th></tr>\r\n<tr>\r\n<td>\r\n<p>A   1886549     Flag TEMP_INACTIVE in table SMSY_SYSTEM_SAP wrong</p>\r\n</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p>24/7/2013</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\">\r\n<tbody>\r\n<tr><th>Category  SAP Note number  Short text (may be abridged)</th></tr>\r\n<tr>\r\n<td>A   1816471     SE24: Error for POST methods when preferred parameter</td>\r\n</tr>\r\n<tr>\r\n<td>B   1849006     All traffic lights grey in Solution Manager Configuration</td>\r\n</tr>\r\n<tr>\r\n<td>B   1854317     Update needed flag remains in managed system overview</td>\r\n</tr>\r\n<tr>\r\n<td>B   1886344     DVM: &#39;CMC_RFC_INTERFACE not found&#39; (ST-A/PI 01Q SP1)</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><br />27/2/2013</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\">\r\n<tbody>\r\n<tr><th>Category  SAP Note number  Short text (may be abridged)</th></tr>\r\n<tr>\r\n<td>A   1733236      Advance Corrections BPMon SM 7.1 ST710 delivered</td>\r\n</tr>\r\n<tr>\r\n<td>A   1786117     SOLMAN_SETUP: System Alias not available after running Setup</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><br />22/11/2012</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\">\r\n<tbody>\r\n<tr><th>Category  SAP Note number  Short text (may be abridged)</th></tr>\r\n<tr>\r\n<td>B   1783860     Wrong calendar date in Extractor </td>\r\n</tr>\r\n<tr>\r\n<td>B   1788486     InfoProvider Settings not setup correctly</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><br />13/11/2012</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\">\r\n<tbody>\r\n<tr><th>Category  SAP Note number  Short text (may be abridged)</th></tr>\r\n<tr>\r\n<td>B   1579462     Sending data from chart item without check to IGS</td>\r\n</tr>\r\n<tr>\r\n<td>B   1758728     &quot;Active&quot; selection of product versions is removed</td>\r\n</tr>\r\n<tr>\r\n<td>B   1768764     TWB reporting displays incorrect test case status</td>\r\n</tr>\r\n<tr>\r\n<td>B   1769570     Function module SMD_DATA_LOADER101 not found</td>\r\n</tr>\r\n<tr>\r\n<td>B   1778532     SOLMAN_SETUP: change of role name for user SM_BW</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><br />20/09/2012</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\">\r\n<tbody>\r\n<tr><th>Category  SAP Note number  Short text (may be abridged)</th></tr>\r\n<tr>\r\n<td>A   1760744     Add Diagnostics Relevant flag in LMDB for Solution Manager</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\">\r\n<tbody>\r\n<tr>\r\n<td>13/08/2012</td>\r\n</tr>\r\n<tr><th>Category  SAP Note number  Short text (may be abridged)</th></tr>\r\n<tr>\r\n<td>B   1710791     Wrong RFC destination during test plan data...</td>\r\n</tr>\r\n<tr>\r\n<td>B   1712091     CRM WebUI scrolls top on roundtrips</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><br />I.3)<br /><br />6/11/2013</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\">\r\n<tbody>\r\n<tr><th>Category  SAP Note number  Short text (may be abridged)</th></tr>\r\n<tr>\r\n<td>A   1856234     Missing sort string in Ibase text components</td>\r\n</tr>\r\n<tr>\r\n<td>B   1768544     Corrections for EEM 7.1 SP06 and SP07</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><br />22/11/2012</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\">\r\n<tbody>\r\n<tr><th>Category  SAP Note number  Short text (may be abridged)</th></tr>\r\n<tr>\r\n<td>A   1599582     Plugin Status Details - Delete sp_level check</td>\r\n</tr>\r\n<tr>\r\n<td>A   1787921     Moving up &quot;Activate BW Source System&quot; activity</td>\r\n</tr>\r\n<tr>\r\n<td>A   1789223     SOLMAN_SETUP: Delete manual activities</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><br />20/09/2012</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\">\r\n<tbody>\r\n<tr><th>Category  SAP Note number  Short text (may be abridged)</th></tr>\r\n<tr>\r\n<td>A   1761913     Activity table update for SP6 (AGS_GS_MIGRATION)</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><br />22/08/2012</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\">\r\n<tbody>\r\n<tr><th>Category  SAP Note number  Short text (may be abridged)</th></tr>\r\n<tr>\r\n<td>A   1734464     SOLMAN_SETUP: notes to be checked are wrong</td>\r\n</tr>\r\n<tr>\r\n<td>A   1753061     Deactivate LMDB notifications</td>\r\n</tr>\r\n<tr>\r\n<td>B   1753587     &quot;No Scope Selected&quot; message although a scope</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><br />13/08/2012</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\">\r\n<tbody>\r\n<tr><th>Category  SAP Note number  Short text (may be abridged)</th></tr>\r\n<tr>\r\n<td>A  1734897     Upgrade SP05/06: Error in maintenance project...</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><br />######################################################################<br />II) Additional information (SAP Notes for managed systems, FAQs, general notes)<br />######################################################################<br /><br />Knowledge Articles:<br />27/2/2013<br />1741109   Generation of IBase Components is not working<br /><br />Implement the following SAP Notes - potentially depending on the<br />Release status of the systems - in the managed systems:<br /><br />23/07/2013<br />1535611   Missing authorization check in ST-PI<br /><br />13/11/2012<br />1729771   Collective corrections as of ST-PI 2008_1 SP06<br /><br />13/08/2012<br />1011229   ST-PI: Corrections for E2E Diagnostics<br />1559499   DPC data providers as of ST-PI 2008_1_XX SP4<br />1572183   Authorizations for SAP Solution Manager<br /><br />######################################################################<br />III) Corrections that can be implemented automatically<br />######################################################################<br /><br />This section contains the list of SAP Notes that can be implemented automatically and that are implemented with this central correction note.</p>\r\n<p>12/11/2014</p>\r\n<p>1491227<br />1980254<br />2018727<br />2020863<br />2051461<br />2052138<br />2056893</p>\r\n<p><br />7/5/2014<br />1774508<br />1791622<br />1885750<br />1925760<br />1979131<br />1979207<br />1990166<br />1990294<br />2006375</p>\r\n<p>2/17/2014<br />1826109<br />1833412<br />1863515<br />1875434<br />1886549<br />1887132<br />1890903<br />1892092<br />1900402<br />1909714<br />1913332<br />1914296<br />1923595<br />1935934</p>\r\n<p>7/24/2013 (ST 1654623)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 1682750<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 1740375<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 1743244<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 1797388<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 1813914<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 1816471<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 1833865<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 1844394<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 1849006<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 1854317<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 1862212<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 1871793<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 1875419<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 1878887<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 1881857<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 1885765<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 1886344<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 1887558<br /><br />6/11/2013 (ST 1094074)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 1629551<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 1717403<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 1718522<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 1739620<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 1763274<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 1768544<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 1771378<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 1775643<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 1777767<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 1801808<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 1803874<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 1805702<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 1811558<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 1813468<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 1817955<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 1821638<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 1824197<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 1826982<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 1827161<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 1834983<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 1836214<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 1837889<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 1847398<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 1849566<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 1851723<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 1855272<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 1856234<br /><br />2/27/2013 (ST 1233384)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 1457391<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 1710009<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 1710170<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 1733236<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 1738276<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 1739631<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 1747098<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 1749795<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 1752517<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 1755568<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 1759409<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 1761039<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 1762967<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 1766608<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 1769053<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 1775564<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 1777317<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 1778300<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 1780480<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 1783371<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 1786117<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 1786378<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 1788217<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 1791436<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 1796193<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 1799283<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 1800683<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 1801878<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 1802520<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 1804373<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 1808259<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 1808294<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 1809705<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 1810060<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 1810837<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 1812046<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 1814921<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 1814977<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 1817266<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 1818156<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 1819525<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 1820285<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 1820291<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 1821727<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 1823420<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 1824471<br /><br />11/22/2012 (ST 1599582)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 1770638<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 1779435<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 1779712<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 1780509<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 1782151<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 1783688<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 1783860<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 1783887<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 1784446<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 1784629<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 1784936<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 1784991<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 1786016<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 1787220<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 1787921<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 1788346<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 1788486<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 1789223<br /><br />11/13/2012 (ST 1579462)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 1643760<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 1745786<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 1748968<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 1749788<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 1754672<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 1758728<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 1758772<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 1762087<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 1763793<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 1764070<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 1765074<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 1765809<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 1768764<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 1769570<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 1771470<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 1774271<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 1774418<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 1775883<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 1776209<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 1777302<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 1778532<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 1779513<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 1780089<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 1780349<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 1782250<br /><br />9/20/2012 (ST 1705783)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 1710578<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 1728717<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 1740217<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 1741470<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 1745716<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 1745879<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 1747859<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 1749774<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 1750994<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 1753107<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 1753625<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 1753985<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 1754071<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 1754275<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 1755049<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 1758150<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 1758475<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 1760744<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 1761913<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 1763066<br /><br />8/22/2012 (ST 1700427)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 1731879<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 1734250<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 1734464<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 1736368<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 1740545<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 1741080<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 1748138<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 1750618<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 1752181<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 1753061<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 1753209<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 1753587<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 1753639<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 1753936<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 1754637<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 1754992<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 1755218<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 1755389<br /><br />8/13/2012 (ST 1559499)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 1673201<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 1679697<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 1679740<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 1687765<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 1694458<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 1696526<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 1710791<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 1712091<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 1712468<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 1719016<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 1719245<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 1727976<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 1729270<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 1733824<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 1734897<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 1735641<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 1736425<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 1736740<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 1737495<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 1737668<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 1738511<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 1740466<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 1740467<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 1740720<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 1741525<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 1742435<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 1743244<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 1743949<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 1745114<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 1745942<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 1746103<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 1748192<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 1749374<br /><br /></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON> (D027512)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D053442)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0001722332/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001722332/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001722332/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001722332/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001722332/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001722332/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001722332/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001722332/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001722332/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "875986", "RefComponent": "BC-UPG-NA", "RefTitle": "Note Assistant: Important notes for SAP_BASIS up to 702", "RefUrl": "/notes/875986"}, {"RefNumber": "2091644", "RefComponent": "SV-SMG-SVC", "RefTitle": "OSS Upload - Enhancement of Upload for DbTypeForSAP Property", "RefUrl": "/notes/2091644"}, {"RefNumber": "2085201", "RefComponent": "SV-SMG-LDB", "RefTitle": "OSS Upload - Data Preparation for DbTypeForSAP property", "RefUrl": "/notes/2085201"}, {"RefNumber": "2056893", "RefComponent": "SV-SMG-INS-CFG", "RefTitle": "SAP Roles Not Copied to the Customer Namespace and Not Assigned To Users", "RefUrl": "/notes/2056893"}, {"RefNumber": "2052138", "RefComponent": "SV-SMG-SYS", "RefTitle": "RFC Destination to SAP Solution Manager(BACK RFC) gets created with wrong System Number", "RefUrl": "/notes/2052138"}, {"RefNumber": "2051461", "RefComponent": "SV-SMG-INS-CFG-MNG", "RefTitle": "User gets locked in the step of RFC creation Or DESTINATION_NOT_OPEN exception", "RefUrl": "/notes/2051461"}, {"RefNumber": "2020863", "RefComponent": "SV-SMG-OP", "RefTitle": "Service Notification doesn't send rating", "RefUrl": "/notes/2020863"}, {"RefNumber": "2018727", "RefComponent": "SV-SMG-SER-RFW", "RefTitle": "Improve the data quality of Engagement Report", "RefUrl": "/notes/2018727"}, {"RefNumber": "2006375", "RefComponent": "SV-SMG-SUP", "RefTitle": "AI_CRM_IM_UPDATE_FROM_SAP: Messages are created multiple times", "RefUrl": "/notes/2006375"}, {"RefNumber": "1990294", "RefComponent": "BW-WHM-DST-SRC", "RefTitle": "Myself system cannot be created in the background", "RefUrl": "/notes/1990294"}, {"RefNumber": "1990166", "RefComponent": "SV-SMG-SYS", "RefTitle": "Deletion of RFC destinations", "RefUrl": "/notes/1990166"}, {"RefNumber": "1980254", "RefComponent": "SV-SMG-INS-CFG", "RefTitle": "Relevant roles aren't updated in a CUA child system via SOLMAN_SETUP", "RefUrl": "/notes/1980254"}, {"RefNumber": "1979207", "RefComponent": "SV-SMG-DVM", "RefTitle": "Incorrect size of DVM workcenter caused by different timezone in managed system", "RefUrl": "/notes/1979207"}, {"RefNumber": "1979131", "RefComponent": "BC-SRV-BR", "RefTitle": "Runtime Error ASSERTION_FAILED in  CL_FDT_APPLICATION_EXIT=>GET_FORMULA_FUNCTIONALS", "RefUrl": "/notes/1979131"}, {"RefNumber": "1935993", "RefComponent": "SV-SMG-SYS", "RefTitle": "SMSY: EHP7 product data or EHP8 product data is missing", "RefUrl": "/notes/1935993"}, {"RefNumber": "1935934", "RefComponent": "SV-SMG-SUP", "RefTitle": "Various problems when creating IBase components and objects", "RefUrl": "/notes/1935934"}, {"RefNumber": "1925760", "RefComponent": "SV-SMG-SUP", "RefTitle": "Multiple IBase created for SOL_MAN_DATA_REP", "RefUrl": "/notes/1925760"}, {"RefNumber": "1923595", "RefComponent": "SV-SMG-SUP", "RefTitle": "Ibase Sortf report should ignore already used longsids", "RefUrl": "/notes/1923595"}, {"RefNumber": "1914296", "RefComponent": "SV-SMG-SUP", "RefTitle": "Checking installed base consistency before creating components", "RefUrl": "/notes/1914296"}, {"RefNumber": "1913332", "RefComponent": "SV-SMG-SUP", "RefTitle": "Check Scope Assignment Block before allowing to delete Ibase", "RefUrl": "/notes/1913332"}, {"RefNumber": "1909714", "RefComponent": "BC-MUS-POW", "RefTitle": "POWL:Dump when switching the queries", "RefUrl": "/notes/1909714"}, {"RefNumber": "1906937", "RefComponent": "BC-WD-ABA", "RefTitle": "Corrections for unified rendering 702/15 V (UR-mimes)", "RefUrl": "/notes/1906937"}, {"RefNumber": "1900402", "RefComponent": "SV-SMG-SUP", "RefTitle": "ULA GUID upper/lower case", "RefUrl": "/notes/1900402"}, {"RefNumber": "1892092", "RefComponent": "SV-SMG-INS-CFG", "RefTitle": "SOLMAN_SETUP: issue with the connection to BW", "RefUrl": "/notes/1892092"}, {"RefNumber": "1890903", "RefComponent": "HAN-DB", "RefTitle": "HANA Extractors for Solution Manager: Global table size", "RefUrl": "/notes/1890903"}, {"RefNumber": "1887558", "RefComponent": "SV-SMG", "RefTitle": "Usage data collector for QGM", "RefUrl": "/notes/1887558"}, {"RefNumber": "1887132", "RefComponent": "SV-SMG-MON-ALR", "RefTitle": "MAI: Work mode managment does not work", "RefUrl": "/notes/1887132"}, {"RefNumber": "1886549", "RefComponent": "SV-SMG-LDB", "RefTitle": "Flag TEMP_INACTIVE in table SMSY_SYSTEM_SAP wrong", "RefUrl": "/notes/1886549"}, {"RefNumber": "1886344", "RefComponent": "SV-SMG-DVM", "RefTitle": "DVM: 'CMC_RFC_INTERFACE not found' (ST-A/PI 01Q SP1)", "RefUrl": "/notes/1886344"}, {"RefNumber": "1885765", "RefComponent": "SV-SMG-SDG", "RefTitle": "Self-Diagnosis: Performance issues due to connection checks", "RefUrl": "/notes/1885765"}, {"RefNumber": "1885750", "RefComponent": "BC-CCM-BTC", "RefTitle": "Dump ITAB_ILLEGAL_SORT_ORDER in CL_SBAL_LOGGER", "RefUrl": "/notes/1885750"}, {"RefNumber": "1881857", "RefComponent": "SV-SMG-SUP", "RefTitle": "ST710: AI_CRM_IM_UPDATE_FROM_SAP unnecessary transfers", "RefUrl": "/notes/1881857"}, {"RefNumber": "1878887", "RefComponent": "SV-SMG-LDB", "RefTitle": "Missing CVs in cl_diagls_util=>get_all_scv_with_lsc_ids_f_ts", "RefUrl": "/notes/1878887"}, {"RefNumber": "1875434", "RefComponent": "SV-SMG-SDG", "RefTitle": "Dump in Self-Diagnosis due to Resource Failure", "RefUrl": "/notes/1875434"}, {"RefNumber": "1875419", "RefComponent": "SV-SMG-SUP", "RefTitle": "ST710:AI_CRM_IM_UPDATE_FROM_SAP has a long runtime", "RefUrl": "/notes/1875419"}, {"RefNumber": "1873727", "RefComponent": "BW-WHM-MTD", "RefTitle": "RAISE_EXCEPTION 'CANCELLED' for object catalog entry", "RefUrl": "/notes/1873727"}, {"RefNumber": "1871793", "RefComponent": "SV-SMG-SUP", "RefTitle": "HANADB should not create technical system Objects", "RefUrl": "/notes/1871793"}, {"RefNumber": "1863515", "RefComponent": "SV-SMG-LDB", "RefTitle": "HANA Soft. Comp. Versions missing in the Landscape API", "RefUrl": "/notes/1863515"}, {"RefNumber": "1862212", "RefComponent": "SV-SMG-SDG", "RefTitle": "BI Reporting alerts show incorrect details", "RefUrl": "/notes/1862212"}, {"RefNumber": "1856234", "RefComponent": "SV-SMG-SUP", "RefTitle": "Missing sort string in Ibase text components", "RefUrl": "/notes/1856234"}, {"RefNumber": "1855272", "RefComponent": "SV-SMG-INS-CFG-MNG", "RefTitle": "SOLMAN_SETUP: admin user shouldn't modify S* profiles", "RefUrl": "/notes/1855272"}, {"RefNumber": "1854317", "RefComponent": "SV-SMG-INS-CFG", "RefTitle": "Update needed flag remains in managed system overview", "RefUrl": "/notes/1854317"}, {"RefNumber": "1851723", "RefComponent": "SV-SMG-SVC", "RefTitle": "REFRESH_ADMIN_DATA_FROM_SUPPORT no licence data generation", "RefUrl": "/notes/1851723"}, {"RefNumber": "1849566", "RefComponent": "SV-SMG-SVC", "RefTitle": "Users without authorization see the installations", "RefUrl": "/notes/1849566"}, {"RefNumber": "1849098", "RefComponent": "SV-SMG-LDB", "RefTitle": "Error handling in content synchronization", "RefUrl": "/notes/1849098"}, {"RefNumber": "1849006", "RefComponent": "SV-SMG-LDB", "RefTitle": "All traffic lights grey in Solution Manager Configuration", "RefUrl": "/notes/1849006"}, {"RefNumber": "1847398", "RefComponent": "SV-SMG-INS-CFG", "RefTitle": "SOLMAN_SETUP: issue with the temporary connection to the BW", "RefUrl": "/notes/1847398"}, {"RefNumber": "1844394", "RefComponent": "SV-SMG-SUP", "RefTitle": "Contact person cannot be entered via BP id directly", "RefUrl": "/notes/1844394"}, {"RefNumber": "1837889", "RefComponent": "SV-SMG-SUP", "RefTitle": "VAR scenario: Unjustified deactivation of S users", "RefUrl": "/notes/1837889"}, {"RefNumber": "1836214", "RefComponent": "SV-SMG-SR", "RefTitle": "SysRec: short dumps in managed systems with user SOLMAN_BTC", "RefUrl": "/notes/1836214"}, {"RefNumber": "1834983", "RefComponent": "SV-SMG-LDB", "RefTitle": "SMSY: Unexpected overwriting of system descriptions", "RefUrl": "/notes/1834983"}, {"RefNumber": "1833865", "RefComponent": "SV-SMG-MON-ALR-CFG", "RefTitle": "Avoid Obsolete Recipient Lists/Recipients during transport", "RefUrl": "/notes/1833865"}, {"RefNumber": "1833412", "RefComponent": "SV-SMG-INS-CFG", "RefTitle": "RFCs: BACK RFC destination removed from SMSY tables", "RefUrl": "/notes/1833412"}, {"RefNumber": "1827161", "RefComponent": "SV-SMG-SUP", "RefTitle": "Duplicate client component", "RefUrl": "/notes/1827161"}, {"RefNumber": "1826982", "RefComponent": "SV-SMG-SUP", "RefTitle": "IBase: Not authorized for initial object", "RefUrl": "/notes/1826982"}, {"RefNumber": "1826109", "RefComponent": "SV-SMG-SUP", "RefTitle": "ITSM: Standard CRM authorization checks skipped for search", "RefUrl": "/notes/1826109"}, {"RefNumber": "1824471", "RefComponent": "SV-SMG-MON-ALR", "RefTitle": "MAI: Metric \"DBACockpit Connection\" is gray when DB is down", "RefUrl": "/notes/1824471"}, {"RefNumber": "1824197", "RefComponent": "SV-SMG-TWB-PLN", "RefTitle": "STWB_2 - Test plan display requires re-logon", "RefUrl": "/notes/1824197"}, {"RefNumber": "1823420", "RefComponent": "SV-SMG-DIA-APP-CA", "RefTitle": "CCDB: Fatal Error - CX_COMPONENT_VERSION_NOT_FOUND", "RefUrl": "/notes/1823420"}, {"RefNumber": "1821727", "RefComponent": "BC-MUS-POW", "RefTitle": "POWL: Detail area not updated in accordance with current lead selection", "RefUrl": "/notes/1821727"}, {"RefNumber": "1821638", "RefComponent": "CRM-BTX-SRQ", "RefTitle": "Rule Policy on Multilevel Categorization", "RefUrl": "/notes/1821638"}, {"RefNumber": "1820291", "RefComponent": "SV-SMG-IMP-BIM", "RefTitle": "Project Documentation tab flagged as changed in Template", "RefUrl": "/notes/1820291"}, {"RefNumber": "1820285", "RefComponent": "SV-SMG-IMP-BIM", "RefTitle": "Comparison against successor nodes shows wrong results", "RefUrl": "/notes/1820285"}, {"RefNumber": "1819525", "RefComponent": "SV-SMG-SDG", "RefTitle": "Self-Diagnosis job fails due to error in alert 234", "RefUrl": "/notes/1819525"}, {"RefNumber": "1818156", "RefComponent": "SV-SMG-GPF-AUT", "RefTitle": "Guided Procedure Authoring- fix Request &TRKORR& is unknown", "RefUrl": "/notes/1818156"}, {"RefNumber": "1817955", "RefComponent": "SV-SMG-SUP", "RefTitle": "LMDB/IBase: Duplicated objects", "RefUrl": "/notes/1817955"}, {"RefNumber": "1817266", "RefComponent": "SV-SMG-SDG", "RefTitle": "First row of functions table in Self-Diagnosis is not shown", "RefUrl": "/notes/1817266"}, {"RefNumber": "1816471", "RefComponent": "BC-DWB-TOO-ENH", "RefTitle": "SE24: Error for POST methods for defined preferred parameter", "RefUrl": "/notes/1816471"}, {"RefNumber": "1814977", "RefComponent": "SV-SMG-INS-CFG", "RefTitle": "Missing Contact Person Role for Template Users.", "RefUrl": "/notes/1814977"}, {"RefNumber": "1814921", "RefComponent": "BC-CCM-BTC", "RefTitle": "BAE: Performance issue when using RFC_VERIFY_DESTINATION", "RefUrl": "/notes/1814921"}, {"RefNumber": "1813914", "RefComponent": "SV-SMG-DIA-APP-CA", "RefTitle": "CCDB: ConfigStore bo40.dump_all_xml - SYSTEM_NO_ROLL", "RefUrl": "/notes/1813914"}, {"RefNumber": "1813468", "RefComponent": "BC-WD-ABA", "RefTitle": "Web Dynpro: Conversion for non-printable chars (NON-UNICODE)", "RefUrl": "/notes/1813468"}, {"RefNumber": "1812046", "RefComponent": "SV-SMG-INS-CFG", "RefTitle": "SOLMAN_SETUP: issue in role update on systems linked to CUA", "RefUrl": "/notes/1812046"}, {"RefNumber": "1811558", "RefComponent": "HAN-DB", "RefTitle": "Extractors for HANA Database Analysis Workload", "RefUrl": "/notes/1811558"}, {"RefNumber": "1810837", "RefComponent": "SV-SMG-SER-ESR", "RefTitle": "BI Content Activation error in SolMan Basic Configuration", "RefUrl": "/notes/1810837"}, {"RefNumber": "1810060", "RefComponent": "SV-SMG-DVM", "RefTitle": "DVM:Multiple Save Not Working in Analysis Admin.", "RefUrl": "/notes/1810060"}, {"RefNumber": "1809705", "RefComponent": "SV-SMG-SUP", "RefTitle": "ISV: SAP status not set correctly after sending to customer", "RefUrl": "/notes/1809705"}, {"RefNumber": "1808294", "RefComponent": "SV-SMG-SUP", "RefTitle": "Incorrect automatic assignment settings in problem", "RefUrl": "/notes/1808294"}, {"RefNumber": "1808259", "RefComponent": "SV-SMG-SVD-GSS", "RefTitle": "Wrong information text for column title in Web DSA UI", "RefUrl": "/notes/1808259"}, {"RefNumber": "1805702", "RefComponent": "SV-SMG-INS-CFG", "RefTitle": "solman_setup:update flag checked but no step needs update", "RefUrl": "/notes/1805702"}, {"RefNumber": "1804373", "RefComponent": "SV-SMG-INS-CFG", "RefTitle": "Solman_setup : The BW content is not active", "RefUrl": "/notes/1804373"}, {"RefNumber": "1803874", "RefComponent": "SV-SMG-SUP", "RefTitle": "LMDB/IBase: Duplicate clients for dual stack systems", "RefUrl": "/notes/1803874"}, {"RefNumber": "1803778", "RefComponent": "SV-SMG-MON-ALR-CFG", "RefTitle": "Dump in class cl_ac_template_repository", "RefUrl": "/notes/1803778"}, {"RefNumber": "1802520", "RefComponent": "BC-DB-DB6-CCM", "RefTitle": "DPW: <PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON> in Data Loader", "RefUrl": "/notes/1802520"}, {"RefNumber": "1801878", "RefComponent": "SV-SMG-INS-CFG", "RefTitle": "SOLMAN_SETUP: warning wrongly raised for managed admin user", "RefUrl": "/notes/1801878"}, {"RefNumber": "1801808", "RefComponent": "SV-SMG-SUP", "RefTitle": "Fields in SERVICE_H structure deleted after update from SAP", "RefUrl": "/notes/1801808"}, {"RefNumber": "1800683", "RefComponent": "SV-SMG-IMP-BIM", "RefTitle": "Possible loss of data in Compare and Adjust", "RefUrl": "/notes/1800683"}, {"RefNumber": "1799283", "RefComponent": "SV-SMG-SDG", "RefTitle": "Erroneous date format in Self-Diagnosis", "RefUrl": "/notes/1799283"}, {"RefNumber": "1797388", "RefComponent": "BC-WD-ABA", "RefTitle": "Web Dynpro: Conversion of non-prinatable characters", "RefUrl": "/notes/1797388"}, {"RefNumber": "1796193", "RefComponent": "SV-SMG-MON-ALR-CNS", "RefTitle": "New alert creation on manual notification/incident creation", "RefUrl": "/notes/1796193"}, {"RefNumber": "1791622", "RefComponent": "SV-SMG-SUP", "RefTitle": "VAR: Send to SAP - unknown installation number - message not permitted", "RefUrl": "/notes/1791622"}, {"RefNumber": "1791436", "RefComponent": "SV-SMG-DVM", "RefTitle": "DVM GSS: Incorrect calculation of Content Coverage", "RefUrl": "/notes/1791436"}, {"RefNumber": "1789223", "RefComponent": "SV-SMG-INS-CFG", "RefTitle": "Solman_setup : Delete manual activities", "RefUrl": "/notes/1789223"}, {"RefNumber": "1788486", "RefComponent": "SV-SMG-DVM", "RefTitle": "InfoProvider Settings not setup correctly", "RefUrl": "/notes/1788486"}, {"RefNumber": "1788346", "RefComponent": "SV-SMG-MON-ALR", "RefTitle": "MAI: Wrong calculation of deadlock, lock escalation", "RefUrl": "/notes/1788346"}, {"RefNumber": "1788217", "RefComponent": "SV-SMG-PSM", "RefTitle": "JSM: job step print&archive parameters related issue", "RefUrl": "/notes/1788217"}, {"RefNumber": "1787921", "RefComponent": "SV-SMG-INS-CFG", "RefTitle": "Moving up \"Activate BW Source System\" activity in Basic Conf", "RefUrl": "/notes/1787921"}, {"RefNumber": "1787220", "RefComponent": "SV-SMG-INS-CFG", "RefTitle": "Gap between template configuration and set scheduler table", "RefUrl": "/notes/1787220"}, {"RefNumber": "1786378", "RefComponent": "SV-SMG-LDB", "RefTitle": "LMDB-MOPZ-API: Filter for customer product data", "RefUrl": "/notes/1786378"}, {"RefNumber": "1786117", "RefComponent": "SV-SMG-INS-CFG", "RefTitle": "Solman_Setup: System Alias not availble after running Setup", "RefUrl": "/notes/1786117"}, {"RefNumber": "1786016", "RefComponent": "SV-SMG-MON-ALR-CNS", "RefTitle": "Multiple specification of Managed Object tag dump fix", "RefUrl": "/notes/1786016"}, {"RefNumber": "1784991", "RefComponent": "SV-SMG-SER-ESR", "RefTitle": "Post-processing of SOLMAN_SETUP re-execute", "RefUrl": "/notes/1784991"}, {"RefNumber": "1784936", "RefComponent": "SV-SMG-MON-ALR-CFG", "RefTitle": "Transport Custom Metric Variants from one system to other system", "RefUrl": "/notes/1784936"}, {"RefNumber": "1784629", "RefComponent": "SV-SMG-INS-CFG", "RefTitle": "Overview status correction", "RefUrl": "/notes/1784629"}, {"RefNumber": "1784446", "RefComponent": "SV-SMG-MON-ALR-CFG", "RefTitle": "Custom Metric Variants were not copied during copy template", "RefUrl": "/notes/1784446"}, {"RefNumber": "1783887", "RefComponent": "SV-SMG-INS-CFG", "RefTitle": "Solman_setup: wrong client in the dropdown lists for RFC", "RefUrl": "/notes/1783887"}, {"RefNumber": "1783860", "RefComponent": "SV-SMG-DVM", "RefTitle": "Wrong calendard date in Extractor AGS_DVM_TOPOBJ_EXTRACT", "RefUrl": "/notes/1783860"}, {"RefNumber": "1783688", "RefComponent": "SV-SMG-MON-ALR-CFG", "RefTitle": "Propagation still happens though it is switched off", "RefUrl": "/notes/1783688"}, {"RefNumber": "1783371", "RefComponent": "SV-SMG-MAI", "RefTitle": "MOpz: System landscape data incomplete in Customer Profile", "RefUrl": "/notes/1783371"}, {"RefNumber": "1782250", "RefComponent": "SV-SMG-INS-CFG", "RefTitle": "Solman_setup: Problem with RFC for SAP Solution Manager", "RefUrl": "/notes/1782250"}, {"RefNumber": "1782151", "RefComponent": "SV-SMG-INS-CFG", "RefTitle": "SOLMAN_SETUP : Setup BW is finished with warnings", "RefUrl": "/notes/1782151"}, {"RefNumber": "1780509", "RefComponent": "SV-SMG-MON-ALR-CFG", "RefTitle": "Select Options for Metric parameter values is not working", "RefUrl": "/notes/1780509"}, {"RefNumber": "1780480", "RefComponent": "SV-SMG-PSM", "RefTitle": "Issues of Job documentation importing", "RefUrl": "/notes/1780480"}, {"RefNumber": "1780349", "RefComponent": "BC-TWB-TST-ECA", "RefTitle": "Error in CL_APL_ECATT_SCRIPT_API->CREATE_CMD_INF_PARAM", "RefUrl": "/notes/1780349"}, {"RefNumber": "1780089", "RefComponent": "SV-SMG-DVM", "RefTitle": "No available RFC for system of DVM template configuraion", "RefUrl": "/notes/1780089"}, {"RefNumber": "1779712", "RefComponent": "SV-SMG-INS-CFG", "RefTitle": "SOLMAN_SETUP : BW Content activation fix", "RefUrl": "/notes/1779712"}, {"RefNumber": "1779513", "RefComponent": "SV-SMG-INS-CFG", "RefTitle": "Solman_setup:generic storage notif remove key/value from log", "RefUrl": "/notes/1779513"}, {"RefNumber": "1779435", "RefComponent": "SV-SMG-INS-CFG", "RefTitle": "System status details not expandable", "RefUrl": "/notes/1779435"}, {"RefNumber": "1778532", "RefComponent": "SV-SMG-INS-CFG", "RefTitle": "SOLMAN_SETUP: change of role name for user SM_BW", "RefUrl": "/notes/1778532"}, {"RefNumber": "1778300", "RefComponent": "SV-SMG-DIA", "RefTitle": "DPW: Short dump during data deletion", "RefUrl": "/notes/1778300"}, {"RefNumber": "1777767", "RefComponent": "SV-SMG-IMP-BIM", "RefTitle": "Incomplete display of messages in component view", "RefUrl": "/notes/1777767"}, {"RefNumber": "1777317", "RefComponent": "SV-SMG-DIA", "RefTitle": "DPW: No aggregation of daily values to monthly values", "RefUrl": "/notes/1777317"}, {"RefNumber": "1777302", "RefComponent": "SV-SMG-DVM", "RefTitle": "Wrong SOURCERDCDEST of extractor AGS_DVM_TOPOBJ_EXTRACT", "RefUrl": "/notes/1777302"}, {"RefNumber": "1776209", "RefComponent": "SV-SMG-INS-CFG", "RefTitle": "Reintroducing \"Configure Gateway\" Step in System Prep.", "RefUrl": "/notes/1776209"}, {"RefNumber": "1775883", "RefComponent": "SV-SMG-SVD-GSS", "RefTitle": "GSS: unexpected dump when using webdynpro logon popup in GSS", "RefUrl": "/notes/1775883"}, {"RefNumber": "1775643", "RefComponent": "SV-SMG-SUP", "RefTitle": "AI_SDK_SP_GENERATE_BP_V2: diverse corrections III", "RefUrl": "/notes/1775643"}, {"RefNumber": "1775564", "RefComponent": "SV-SMG-MON-BPM", "RefTitle": "Issues with BPMon Mirgration to SM 7.1 SP 5 or higher", "RefUrl": "/notes/1775564"}, {"RefNumber": "1774508", "RefComponent": "SV-SMG-DVM", "RefTitle": "DVM: Simple AObj Or Business Object Footprint issue", "RefUrl": "/notes/1774508"}, {"RefNumber": "1774418", "RefComponent": "BC-MUS-POW", "RefTitle": "POWL: Unnecessary refresh triggered when using query api", "RefUrl": "/notes/1774418"}, {"RefNumber": "1774271", "RefComponent": "SV-SMG-GPF-AUT", "RefTitle": "GPA Fix incomplete transport request for Guided Procedure", "RefUrl": "/notes/1774271"}, {"RefNumber": "1771470", "RefComponent": "SV-SMG-INS-CFG", "RefTitle": "SOLMAN_SETUP: impossible to update user roles other than Z", "RefUrl": "/notes/1771470"}, {"RefNumber": "1771378", "RefComponent": "HAN-DB", "RefTitle": "HANA Extractors for Solution Manager", "RefUrl": "/notes/1771378"}, {"RefNumber": "1770638", "RefComponent": "SV-SMG-MON-ALR-CFG", "RefTitle": "Working with Templates in different languages", "RefUrl": "/notes/1770638"}, {"RefNumber": "1769570", "RefComponent": "SV-SMG-DVM", "RefTitle": "Function Moduel SMD_DATA_LOADER101 not found", "RefUrl": "/notes/1769570"}, {"RefNumber": "1769053", "RefComponent": "SV-SMG-DIA-SRV-SET", "RefTitle": "Instance Log Path not displayed for <PERSON>a", "RefUrl": "/notes/1769053"}, {"RefNumber": "1768764", "RefComponent": "SV-SMG-TWB-REP", "RefTitle": "TWB reporting displays incorrect test case status counters", "RefUrl": "/notes/1768764"}, {"RefNumber": "1768544", "RefComponent": "SV-SMG-MON-EEM", "RefTitle": "Corrections for EEM 7.1 SP06 and SP07", "RefUrl": "/notes/1768544"}, {"RefNumber": "1766608", "RefComponent": "SV-SMG-INS-CFG", "RefTitle": "Activity status reset when using activity \"navigation\"", "RefUrl": "/notes/1766608"}, {"RefNumber": "1765809", "RefComponent": "SV-SMG-INS-CFG", "RefTitle": "SOLMAN_SETUP: status of java user step is wrongly warning", "RefUrl": "/notes/1765809"}, {"RefNumber": "1765689", "RefComponent": "SV-SMG-INS-CFG", "RefTitle": "SOLMAN_SETUP: Warning in managed system step 'Create Users'", "RefUrl": "/notes/1765689"}, {"RefNumber": "1765074", "RefComponent": "SV-SMG-INS-CFG", "RefTitle": "SOLMAN_SETUP: status of step users not updated", "RefUrl": "/notes/1765074"}, {"RefNumber": "1764070", "RefComponent": "SV-SMG-INS-CFG", "RefTitle": "Changing status in 'Connect managed system'", "RefUrl": "/notes/1764070"}, {"RefNumber": "1763793", "RefComponent": "BC-MUS-POW", "RefTitle": "POWL: Column headers are rendered incorrectly", "RefUrl": "/notes/1763793"}, {"RefNumber": "1763274", "RefComponent": "BC-WD-ABA", "RefTitle": "Select options: Error when deleting a parameter", "RefUrl": "/notes/1763274"}, {"RefNumber": "1763066", "RefComponent": "SV-SMG-MON-ALR-CNS", "RefTitle": "Mismatch in number of alerts in alert inbox tables", "RefUrl": "/notes/1763066"}, {"RefNumber": "1762967", "RefComponent": "SV-SMG-MON-REP", "RefTitle": "DPW: Overlapping records in DBH_CUBE_DATA", "RefUrl": "/notes/1762967"}, {"RefNumber": "1762087", "RefComponent": "SV-SMG-INS-CFG", "RefTitle": "SOLMAN_SETUP: message 'invalid date' when creating users", "RefUrl": "/notes/1762087"}, {"RefNumber": "1761913", "RefComponent": "SV-SMG-INS-CFG", "RefTitle": "Activity table update for SP6 (AGS_GS_MIGRATION)", "RefUrl": "/notes/1761913"}, {"RefNumber": "1761039", "RefComponent": "SV-SMG-MON-BPM", "RefTitle": "Advance Corrections BPMon SM 7.1 ST710 delivered with SP08", "RefUrl": "/notes/1761039"}, {"RefNumber": "1760744", "RefComponent": "SV-SMG-INS-CFG", "RefTitle": "Add Diagnostics Relevant flag in LMDB for Solution Manager", "RefUrl": "/notes/1760744"}, {"RefNumber": "1759409", "RefComponent": "BC-MUS-POW", "RefTitle": "POWL: Dump when \"enable default lead selection\" is ON", "RefUrl": "/notes/1759409"}, {"RefNumber": "1758772", "RefComponent": "SV-SMG-SUP", "RefTitle": "Edit button is missing in details assignment block", "RefUrl": "/notes/1758772"}, {"RefNumber": "1758728", "RefComponent": "SV-SMG-SYS", "RefTitle": "\"Active\" selection of product versions is removed", "RefUrl": "/notes/1758728"}, {"RefNumber": "1758475", "RefComponent": "SV-SMG-MON-ALR-CNS", "RefTitle": "Alert Inbox Dumps with error TYPE NOT FOUND.", "RefUrl": "/notes/1758475"}, {"RefNumber": "1758150", "RefComponent": "SV-SMG-DIA-APP-CA", "RefTitle": "CCDB: Extraction for local system failed", "RefUrl": "/notes/1758150"}, {"RefNumber": "1755568", "RefComponent": "SV-SMG-OP", "RefTitle": "BPMon attached to tech. interfaces: Copying does not work", "RefUrl": "/notes/1755568"}, {"RefNumber": "1755389", "RefComponent": "SV-SMG-MON-ALR-PRV", "RefTitle": "Metrics: \"Number of Exceptions in Domain Log\" is grey", "RefUrl": "/notes/1755389"}, {"RefNumber": "1755218", "RefComponent": "SV-SMG-INS-CFG", "RefTitle": "Scope dependent parent step not accessible in solman_setup", "RefUrl": "/notes/1755218"}, {"RefNumber": "1755049", "RefComponent": "SV-SMG-INS-CFG", "RefTitle": "Unable to create RFC with SAP router", "RefUrl": "/notes/1755049"}, {"RefNumber": "1754992", "RefComponent": "SV-SMG-MON-ALR-CFG", "RefTitle": "Create Transport Request Failed with package checks", "RefUrl": "/notes/1754992"}, {"RefNumber": "1754672", "RefComponent": "SV-SMG-DVM", "RefTitle": "Correction for DVM Template creation", "RefUrl": "/notes/1754672"}, {"RefNumber": "1754637", "RefComponent": "SV-SMG-INS-CFG", "RefTitle": "DVM Setup: wrong navigation link", "RefUrl": "/notes/1754637"}, {"RefNumber": "1754275", "RefComponent": "SV-SMG-INS-CFG", "RefTitle": "Wrong message 'RFC SM_XXXCLNTnnn_BACK does not exist'", "RefUrl": "/notes/1754275"}, {"RefNumber": "1754071", "RefComponent": "SV-SMG-INS-CFG", "RefTitle": "RFC for SAP Solution Manager  is not persisted", "RefUrl": "/notes/1754071"}, {"RefNumber": "1753985", "RefComponent": "SV-SMG-SDG", "RefTitle": "Downloaded XML data for Root Cause Analysis is not correct", "RefUrl": "/notes/1753985"}, {"RefNumber": "1753936", "RefComponent": "SV-SMG-MON-ALR-CFG", "RefTitle": "Apply Delivery Fixes", "RefUrl": "/notes/1753936"}, {"RefNumber": "1753639", "RefComponent": "SV-SMG-INS-CFG", "RefTitle": "Adding the \"Scenario Operations\" button on Define scope step", "RefUrl": "/notes/1753639"}, {"RefNumber": "1753625", "RefComponent": "SV-SMG-INS-CFG", "RefTitle": "The Post processing section is not visible in step2 of CHARM", "RefUrl": "/notes/1753625"}, {"RefNumber": "1753587", "RefComponent": "SV-SMG-INS-CFG", "RefTitle": "\"No Scope Selected\" message although a scope is selected.", "RefUrl": "/notes/1753587"}, {"RefNumber": "1753209", "RefComponent": "SV-SMG-INS-CFG", "RefTitle": "SOLMAN_SETUP ITSM 2.2 Incident/SR Templates - wrong types.", "RefUrl": "/notes/1753209"}, {"RefNumber": "1753107", "RefComponent": "SV-SMG-INS-CFG", "RefTitle": "Fixing context's loss when executing an automatic activity.", "RefUrl": "/notes/1753107"}, {"RefNumber": "1753061", "RefComponent": "SV-SMG-INS-CFG", "RefTitle": "Deactivate LMDB notifications", "RefUrl": "/notes/1753061"}, {"RefNumber": "1752517", "RefComponent": "SV-SMG-INS-CFG", "RefTitle": "Managed system configuration - warning in step 7 create user", "RefUrl": "/notes/1752517"}, {"RefNumber": "1752181", "RefComponent": "SV-SMG-MON-ALR-PRV", "RefTitle": "Grey metrics in Technical System Monitoring", "RefUrl": "/notes/1752181"}, {"RefNumber": "1750994", "RefComponent": "SV-SMG-SDG", "RefTitle": "Alert/subalert name/id are missing in XML generation", "RefUrl": "/notes/1750994"}, {"RefNumber": "1750618", "RefComponent": "SV-SMG-INS-CFG", "RefTitle": "RFC destinations created in SMSU_MANAGED_SYSTEM not delete", "RefUrl": "/notes/1750618"}, {"RefNumber": "1749795", "RefComponent": "BC-DB-DB6-CCM", "RefTitle": "DPW: Housekeeping does not finish", "RefUrl": "/notes/1749795"}, {"RefNumber": "1749788", "RefComponent": "SV-SMG-DVM", "RefTitle": "Correction - Housekeeping Settings of Data Volume Management", "RefUrl": "/notes/1749788"}, {"RefNumber": "1749774", "RefComponent": "SV-SMG-MON-ALR-CFG", "RefTitle": "Request popup opens, eventhough template saved under $TMP", "RefUrl": "/notes/1749774"}, {"RefNumber": "1749374", "RefComponent": "SV-SMG-SUP", "RefTitle": "Creation of I-Objects: Incorrect message type in appl log", "RefUrl": "/notes/1749374"}, {"RefNumber": "1748968", "RefComponent": "SV-SMG-MON-ALR-DIR", "RefTitle": "Collective corrections for the MEA Directory SP6", "RefUrl": "/notes/1748968"}, {"RefNumber": "1748192", "RefComponent": "BC-DB-DB6-CCM", "RefTitle": "DBA: Validity Fallback Is Not Evaluated", "RefUrl": "/notes/1748192"}, {"RefNumber": "1748138", "RefComponent": "BC-MUS-POW", "RefTitle": "POWL: Unnecessary update of detail area", "RefUrl": "/notes/1748138"}, {"RefNumber": "1747859", "RefComponent": "SV-SMG-INS-CFG", "RefTitle": "Create or Update Administrator from Connect Managed System", "RefUrl": "/notes/1747859"}, {"RefNumber": "1747098", "RefComponent": "SV-SMG-SVC", "RefTitle": "REFRESH_ADMIN_DATA_FROM_SUPPORT failed to create systems", "RefUrl": "/notes/1747098"}, {"RefNumber": "1746625", "RefComponent": "SV-SMG-BPR", "RefTitle": "Extended Search in Business Process Repository does not work", "RefUrl": "/notes/1746625"}, {"RefNumber": "1746103", "RefComponent": "SV-SMG-DVM", "RefTitle": "Solution Manager: Turning Configuration Step Status to Grey", "RefUrl": "/notes/1746103"}, {"RefNumber": "1745942", "RefComponent": "SV-SMG", "RefTitle": "DPW: Cube Aggregation Memory Usage", "RefUrl": "/notes/1745942"}, {"RefNumber": "1745879", "RefComponent": "SV-SMG-SDG", "RefTitle": "Failed to get RTCC recommendation due to RFC call failure.", "RefUrl": "/notes/1745879"}, {"RefNumber": "1745794", "RefComponent": "SV-SMG-DVM", "RefTitle": "Data Collectors corrections ST-A/PI 01P* SP0", "RefUrl": "/notes/1745794"}, {"RefNumber": "1745786", "RefComponent": "SV-SMG-DIA-SRV-LSC", "RefTitle": "Landscape LMDB wrapper SP6 related corrections", "RefUrl": "/notes/1745786"}, {"RefNumber": "1745716", "RefComponent": "SV-SMG-INS-CFG", "RefTitle": "issue with EFWK RESOURCE MANAGER report scheduled twice", "RefUrl": "/notes/1745716"}, {"RefNumber": "1745114", "RefComponent": "SV-SMG", "RefTitle": "DBA: Dump in CL_DBA_DBH_CUBE_DATA", "RefUrl": "/notes/1745114"}, {"RefNumber": "1743949", "RefComponent": "SV-SMG-SVD-SWB", "RefTitle": "Corrupted automatic service report sent via notification", "RefUrl": "/notes/1743949"}, {"RefNumber": "1743244", "RefComponent": "BC-MUS-POW", "RefTitle": "POWL: Better description needed in query template dropdown", "RefUrl": "/notes/1743244"}, {"RefNumber": "1742435", "RefComponent": "SV-SMG-LDB", "RefTitle": "LMDB: Confirmation error when creating a product system", "RefUrl": "/notes/1742435"}, {"RefNumber": "1741525", "RefComponent": "SV-SMG-SUP", "RefTitle": "VAR: Multiple nodes per customer in IBase", "RefUrl": "/notes/1741525"}, {"RefNumber": "1741470", "RefComponent": "BC-WD-CMP-ALV-ABA", "RefTitle": "WD ABAP ALV error when context changes are dispatched", "RefUrl": "/notes/1741470"}, {"RefNumber": "1741080", "RefComponent": "SV-SMG-MON-ALR", "RefTitle": "no/wrong metrics collected on SMD Agent assoicated to DB", "RefUrl": "/notes/1741080"}, {"RefNumber": "1740720", "RefComponent": "SV-SMG-DIA-APP-CA", "RefTitle": "CCDB: Incorrect 'Customizing change detected' message", "RefUrl": "/notes/1740720"}, {"RefNumber": "1740545", "RefComponent": "CRM-IC-BF-CAT", "RefTitle": "Categories trigger entity data change", "RefUrl": "/notes/1740545"}, {"RefNumber": "1740467", "RefComponent": "SV-SMG-MON-ALR-CLC", "RefTitle": "ECE: Event Calculation Engine Note for SP06", "RefUrl": "/notes/1740467"}, {"RefNumber": "1740466", "RefComponent": "SV-SMG-MON-ALR-CLC", "RefTitle": "ECE: old metrics are used if rule type is \"Already Rated\"", "RefUrl": "/notes/1740466"}, {"RefNumber": "1740375", "RefComponent": "SV-SMG-SUP", "RefTitle": "Incident creation fails in Test Workbench or Help Menu", "RefUrl": "/notes/1740375"}, {"RefNumber": "1740217", "RefComponent": "SV-SMG-INS-CFG", "RefTitle": "Note Post Processing not enabled in solman_setup", "RefUrl": "/notes/1740217"}, {"RefNumber": "1739631", "RefComponent": "SV-SMG-SVD", "RefTitle": "Service delivery session information not updated accordingly", "RefUrl": "/notes/1739631"}, {"RefNumber": "1739620", "RefComponent": "SV-SMG-MON-ALR-CFG", "RefTitle": "Update Content Step Display Fixes", "RefUrl": "/notes/1739620"}, {"RefNumber": "1738511", "RefComponent": "SV-SMG-IMP", "RefTitle": "Editing MS Office documents in SAP Solution Manager", "RefUrl": "/notes/1738511"}, {"RefNumber": "1738276", "RefComponent": "SV-SMG-SVD-SWB", "RefTitle": "DSA: Word creation dumps caused by memory lack", "RefUrl": "/notes/1738276"}, {"RefNumber": "1737668", "RefComponent": "SV-SMG-MON-REP", "RefTitle": "Data not re-organized after upgrade", "RefUrl": "/notes/1737668"}, {"RefNumber": "1737495", "RefComponent": "SV-SMG-DVM", "RefTitle": "Analysis Creation for Simple Archiving Objects not working", "RefUrl": "/notes/1737495"}, {"RefNumber": "1737347", "RefComponent": "CRM-IU-S", "RefTitle": "structure COMT_PROD_SRV_MAINTAIN_API contains too long data", "RefUrl": "/notes/1737347"}, {"RefNumber": "1736740", "RefComponent": "SV-SMG-INS-CFG", "RefTitle": "SOLMAN_SETUP:java user manually maintained might be in error", "RefUrl": "/notes/1736740"}, {"RefNumber": "1736425", "RefComponent": "SV-SMG-LDB", "RefTitle": "Technical Scenario DUAL_STACK: MaxLen violated", "RefUrl": "/notes/1736425"}, {"RefNumber": "1736368", "RefComponent": "SV-SMG-INS-CFG", "RefTitle": "Error in 'Replace Service Definitions' activity", "RefUrl": "/notes/1736368"}, {"RefNumber": "1735641", "RefComponent": "BC-MUS-POW", "RefTitle": "POWL: Criteria Personalization not saved for queries", "RefUrl": "/notes/1735641"}, {"RefNumber": "1734897", "RefComponent": "SV-SMG-OP", "RefTitle": "Upgrade SP05/06/07:Error in maintenance project or BPMon", "RefUrl": "/notes/1734897"}, {"RefNumber": "1734464", "RefComponent": "SV-SMG-INS-CFG", "RefTitle": "SOLMAN_SETUP: notes to be checked are wrong", "RefUrl": "/notes/1734464"}, {"RefNumber": "1734250", "RefComponent": "BC-MUS-POW", "RefTitle": "POWL: Error in detail area update", "RefUrl": "/notes/1734250"}, {"RefNumber": "1733824", "RefComponent": "BC-WD-ABA", "RefTitle": "Dump in CL_WDR_NOTIFICATION", "RefUrl": "/notes/1733824"}, {"RefNumber": "1733236", "RefComponent": "SV-SMG-MON-BPM", "RefTitle": "Advance Corrections BPMon SM 7.1 ST710 delivered with SP07", "RefUrl": "/notes/1733236"}, {"RefNumber": "1731879", "RefComponent": "SV-SMG-INS-CFG", "RefTitle": "Activate BI Content for INCIDENT and CHARM,and BPO DASHBOARD", "RefUrl": "/notes/1731879"}, {"RefNumber": "1729771", "RefComponent": "SV-SMG-SER", "RefTitle": "Collective corrections as of ST-PI 2008_1 SP06", "RefUrl": "/notes/1729771"}, {"RefNumber": "1729270", "RefComponent": "BC-DB-HDB-CCM", "RefTitle": "Collective corrections BC-DB-HDB-CCM for SAP_BASIS 7.02 SP11", "RefUrl": "/notes/1729270"}, {"RefNumber": "1728717", "RefComponent": "SV-SMG-INS-CFG", "RefTitle": "Mass Activation of templates", "RefUrl": "/notes/1728717"}, {"RefNumber": "1727976", "RefComponent": "BC-DB-ORA-CCM", "RefTitle": "Error in setup of Database Perf. Warehouse for local DB", "RefUrl": "/notes/1727976"}, {"RefNumber": "1719245", "RefComponent": "BC-DB-MSS-CCM", "RefTitle": "SolMan 7.1 PDW: data gaps in extractions for MSSQL systems", "RefUrl": "/notes/1719245"}, {"RefNumber": "1719016", "RefComponent": "SV-SMG-INS-CFG", "RefTitle": "SOLMAN_SETUP: obsolete roles still added to users", "RefUrl": "/notes/1719016"}, {"RefNumber": "1718522", "RefComponent": "SV-SMG-IMP", "RefTitle": "Customizing table SMCRM_CUST_APPL - General corrections", "RefUrl": "/notes/1718522"}, {"RefNumber": "1717403", "RefComponent": "SV-SMG-DIA-SRV-EFW", "RefTitle": "Collective Note for Extractor FWK - ST710 (SP05 - SP08)", "RefUrl": "/notes/1717403"}, {"RefNumber": "1712468", "RefComponent": "SV-SMG-DIA-SRV-LSC", "RefTitle": "DBA: Outside db discovery support for virtual hostnames", "RefUrl": "/notes/1712468"}, {"RefNumber": "1712091", "RefComponent": "CRM-FRW-UI", "RefTitle": "CRM WebUI scrolls top on roundtrips", "RefUrl": "/notes/1712091"}, {"RefNumber": "1710791", "RefComponent": "BC-TWB-ORG", "RefTitle": "Wrong RFC destination during test plan data read access", "RefUrl": "/notes/1710791"}, {"RefNumber": "1710578", "RefComponent": "SV-SMG-SER-RFW", "RefTitle": "BI Content Activation Errors in SolMan Basic Configuration", "RefUrl": "/notes/1710578"}, {"RefNumber": "1710384", "RefComponent": "BC-WD-CMP-ALV-ABA", "RefTitle": "WD ABAP ALV performance improvements", "RefUrl": "/notes/1710384"}, {"RefNumber": "1710170", "RefComponent": "SV-SMG-INS-CFG", "RefTitle": "SAP Solution Manager Software Prerequisites", "RefUrl": "/notes/1710170"}, {"RefNumber": "1710009", "RefComponent": "CRM-IC-EMS-CAT", "RefTitle": "Category Links are not copied to schema's new version", "RefUrl": "/notes/1710009"}, {"RefNumber": "1705783", "RefComponent": "BC-DB-DB6-CCM", "RefTitle": "Remote monitoring of SAP Solution Manager failed with a dump", "RefUrl": "/notes/1705783"}, {"RefNumber": "1702711", "RefComponent": "BC-DB-HDB-CCM", "RefTitle": "Adjustments for Solution Manager update as of 7.1 SP 5", "RefUrl": "/notes/1702711"}, {"RefNumber": "1700427", "RefComponent": "BC-DB-SDB-CCM", "RefTitle": "MaxDB: Potential corruption of DB release in DB6NAVSYST", "RefUrl": "/notes/1700427"}, {"RefNumber": "1697052", "RefComponent": "BC-WD-ABA", "RefTitle": "WDA: Link in FormattedTextView causes a dump", "RefUrl": "/notes/1697052"}, {"RefNumber": "1696526", "RefComponent": "SV-SMG-TWB-PLN", "RefTitle": "Test workbench requests login to local system", "RefUrl": "/notes/1696526"}, {"RefNumber": "1694458", "RefComponent": "BC-CUS-TOL-PAD", "RefTitle": "SOLAR_PROJECT_ADMIN:Select Countries not Cleared/Refreshed", "RefUrl": "/notes/1694458"}, {"RefNumber": "1687765", "RefComponent": "SV-SMG-IMP-PAD", "RefTitle": "Displaying tab page for RDS in project administration", "RefUrl": "/notes/1687765"}, {"RefNumber": "1682750", "RefComponent": "BC-WD-ABA", "RefTitle": "FormattedTextView: Unexpected characters (such as #)", "RefUrl": "/notes/1682750"}, {"RefNumber": "1682370", "RefComponent": "BC-MUS-POW", "RefTitle": "POWL: Removal of \"Reapply Table settings\" functionality", "RefUrl": "/notes/1682370"}, {"RefNumber": "1679740", "RefComponent": "SV-SMG-IMP-PAD", "RefTitle": "Navigation on scope tab page during scenario comparison", "RefUrl": "/notes/1679740"}, {"RefNumber": "1679697", "RefComponent": "BC-MUS-POW", "RefTitle": "POWL: Selection criteria ignored during POWL rendering", "RefUrl": "/notes/1679697"}, {"RefNumber": "1678162", "RefComponent": "BC-SEC-AUT-PFC", "RefTitle": "Solution Manager Setup: Performance when copying roles", "RefUrl": "/notes/1678162"}, {"RefNumber": "1673201", "RefComponent": "BC-MUS-POW", "RefTitle": "POWL: Row selection lost on Single/Multi selection modes", "RefUrl": "/notes/1673201"}, {"RefNumber": "1654623", "RefComponent": "SV-SMG-MON-ALR-CFG", "RefTitle": "Problems when copying custom templates", "RefUrl": "/notes/1654623"}, {"RefNumber": "1653635", "RefComponent": "HAN-DB", "RefTitle": "New HANA Extraktors for BASIS SP10;SP11 and some corrections", "RefUrl": "/notes/1653635"}, {"RefNumber": "1643760", "RefComponent": "SV-SMG-LDB", "RefTitle": "No target namespace when settung up LMDB", "RefUrl": "/notes/1643760"}, {"RefNumber": "1629551", "RefComponent": "BC-WD-ABA", "RefTitle": "Business Graphics: Dump due to missing resources", "RefUrl": "/notes/1629551"}, {"RefNumber": "1599582", "RefComponent": "SV-SMG-INS-CFG", "RefTitle": "Plugin Status Details - Delete sp_level check for ST-A/PI", "RefUrl": "/notes/1599582"}, {"RefNumber": "1579462", "RefComponent": "BW-BEX-ET-WEB", "RefTitle": "Sending data from chart item without check to IGS", "RefUrl": "/notes/1579462"}, {"RefNumber": "1572183", "RefComponent": "SV-SMG-AUT", "RefTitle": "Authorizations for SAP Solution Manager RFC users", "RefUrl": "/notes/1572183"}, {"RefNumber": "1559499", "RefComponent": "SV-SMG-MON-SYS", "RefTitle": "DPC data providers as of ST-PI 2008_1_XX SP4", "RefUrl": "/notes/1559499"}, {"RefNumber": "1535611", "RefComponent": "SV-SMG-SDD", "RefTitle": "Missing authorization check in ST-PI", "RefUrl": "/notes/1535611"}, {"RefNumber": "1491227", "RefComponent": "SV-SMG-SVD-SCU", "RefTitle": "Adjustments to service content update from ST 700 SP 23", "RefUrl": "/notes/1491227"}, {"RefNumber": "1457391", "RefComponent": "AP-MD-IBA", "RefTitle": "Bad Performance while accessing table IBST.", "RefUrl": "/notes/1457391"}, {"RefNumber": "1365796", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1365796"}, {"RefNumber": "1233384", "RefComponent": "CRM-BTX-SVO", "RefTitle": "Solution Manager Service Desk for ISVs", "RefUrl": "/notes/1233384"}, {"RefNumber": "1094074", "RefComponent": "CRM-BTX-SVO", "RefTitle": "Unnecessary Authority Check in CRM_DNO_MONITOR", "RefUrl": "/notes/1094074"}, {"RefNumber": "1011229", "RefComponent": "SV-SMG-SER", "RefTitle": "ST-PI: Corrections for E2E Diagnostics", "RefUrl": "/notes/1011229"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1758728", "RefComponent": "SV-SMG-SYS", "RefTitle": "\"Active\" selection of product versions is removed", "RefUrl": "/notes/1758728 "}, {"RefNumber": "1094074", "RefComponent": "CRM-BTX-SVO", "RefTitle": "Unnecessary Authority Check in CRM_DNO_MONITOR", "RefUrl": "/notes/1094074 "}, {"RefNumber": "1729771", "RefComponent": "SV-SMG-SER", "RefTitle": "Collective corrections as of ST-PI 2008_1 SP06", "RefUrl": "/notes/1729771 "}, {"RefNumber": "1748968", "RefComponent": "SV-SMG-MON-ALR-DIR", "RefTitle": "Collective corrections for the MEA Directory SP6", "RefUrl": "/notes/1748968 "}, {"RefNumber": "1700427", "RefComponent": "BC-DB-SDB-CCM", "RefTitle": "MaxDB: Potential corruption of DB release in DB6NAVSYST", "RefUrl": "/notes/1700427 "}, {"RefNumber": "1728717", "RefComponent": "SV-SMG-INS-CFG", "RefTitle": "Mass Activation of templates", "RefUrl": "/notes/1728717 "}, {"RefNumber": "1787220", "RefComponent": "SV-SMG-INS-CFG", "RefTitle": "Gap between template configuration and set scheduler table", "RefUrl": "/notes/1787220 "}, {"RefNumber": "1871793", "RefComponent": "SV-SMG-SUP", "RefTitle": "HANADB should not create technical system Objects", "RefUrl": "/notes/1871793 "}, {"RefNumber": "1816471", "RefComponent": "BC-DWB-TOO-ENH", "RefTitle": "SE24: Error for POST methods for defined preferred parameter", "RefUrl": "/notes/1816471 "}, {"RefNumber": "1886344", "RefComponent": "SV-SMG-DVM", "RefTitle": "DVM: 'CMC_RFC_INTERFACE not found' (ST-A/PI 01Q SP1)", "RefUrl": "/notes/1886344 "}, {"RefNumber": "1811558", "RefComponent": "HAN-DB", "RefTitle": "Extractors for HANA Database Analysis Workload", "RefUrl": "/notes/1811558 "}, {"RefNumber": "1710170", "RefComponent": "SV-SMG-INS-CFG", "RefTitle": "SAP Solution Manager Software Prerequisites", "RefUrl": "/notes/1710170 "}, {"RefNumber": "1753061", "RefComponent": "SV-SMG-INS-CFG", "RefTitle": "Deactivate LMDB notifications", "RefUrl": "/notes/1753061 "}, {"RefNumber": "1805702", "RefComponent": "SV-SMG-INS-CFG", "RefTitle": "solman_setup:update flag checked but no step needs update", "RefUrl": "/notes/1805702 "}, {"RefNumber": "1854317", "RefComponent": "SV-SMG-INS-CFG", "RefTitle": "Update needed flag remains in managed system overview", "RefUrl": "/notes/1854317 "}, {"RefNumber": "1779712", "RefComponent": "SV-SMG-INS-CFG", "RefTitle": "SOLMAN_SETUP : BW Content activation fix", "RefUrl": "/notes/1779712 "}, {"RefNumber": "1782151", "RefComponent": "SV-SMG-INS-CFG", "RefTitle": "SOLMAN_SETUP : Setup BW is finished with warnings", "RefUrl": "/notes/1782151 "}, {"RefNumber": "1804373", "RefComponent": "SV-SMG-INS-CFG", "RefTitle": "Solman_setup : The BW content is not active", "RefUrl": "/notes/1804373 "}, {"RefNumber": "1717403", "RefComponent": "SV-SMG-DIA-SRV-EFW", "RefTitle": "Collective Note for Extractor FWK - ST710 (SP05 - SP08)", "RefUrl": "/notes/1717403 "}, {"RefNumber": "1739631", "RefComponent": "SV-SMG-SVD", "RefTitle": "Service delivery session information not updated accordingly", "RefUrl": "/notes/1739631 "}, {"RefNumber": "1710578", "RefComponent": "SV-SMG-SER-RFW", "RefTitle": "BI Content Activation Errors in SolMan Basic Configuration", "RefUrl": "/notes/1710578 "}, {"RefNumber": "1801808", "RefComponent": "SV-SMG-SUP", "RefTitle": "Fields in SERVICE_H structure deleted after update from SAP", "RefUrl": "/notes/1801808 "}, {"RefNumber": "1747098", "RefComponent": "SV-SMG-SVC", "RefTitle": "REFRESH_ADMIN_DATA_FROM_SUPPORT failed to create systems", "RefUrl": "/notes/1747098 "}, {"RefNumber": "1827161", "RefComponent": "SV-SMG-SUP", "RefTitle": "Duplicate client component", "RefUrl": "/notes/1827161 "}, {"RefNumber": "1856234", "RefComponent": "SV-SMG-SUP", "RefTitle": "Missing sort string in Ibase text components", "RefUrl": "/notes/1856234 "}, {"RefNumber": "1803893", "RefComponent": "BC-DB-HDB-CCM", "RefTitle": "Adjustments for Solution Manager", "RefUrl": "/notes/1803893 "}, {"RefNumber": "1789223", "RefComponent": "SV-SMG-INS-CFG", "RefTitle": "Solman_setup : Delete manual activities", "RefUrl": "/notes/1789223 "}, {"RefNumber": "1783887", "RefComponent": "SV-SMG-INS-CFG", "RefTitle": "Solman_setup: wrong client in the dropdown lists for RFC", "RefUrl": "/notes/1783887 "}, {"RefNumber": "1782250", "RefComponent": "SV-SMG-INS-CFG", "RefTitle": "Solman_setup: Problem with RFC for SAP Solution Manager", "RefUrl": "/notes/1782250 "}, {"RefNumber": "1754071", "RefComponent": "SV-SMG-INS-CFG", "RefTitle": "RFC for SAP Solution Manager  is not persisted", "RefUrl": "/notes/1754071 "}, {"RefNumber": "1750618", "RefComponent": "SV-SMG-INS-CFG", "RefTitle": "RFC destinations created in SMSU_MANAGED_SYSTEM not delete", "RefUrl": "/notes/1750618 "}, {"RefNumber": "1833865", "RefComponent": "SV-SMG-MON-ALR-CFG", "RefTitle": "Avoid Obsolete Recipient Lists/Recipients during transport", "RefUrl": "/notes/1833865 "}, {"RefNumber": "1881857", "RefComponent": "SV-SMG-SUP", "RefTitle": "ST710: AI_CRM_IM_UPDATE_FROM_SAP unnecessary transfers", "RefUrl": "/notes/1881857 "}, {"RefNumber": "1885765", "RefComponent": "SV-SMG-SDG", "RefTitle": "Self-Diagnosis: Performance issues due to connection checks", "RefUrl": "/notes/1885765 "}, {"RefNumber": "1803874", "RefComponent": "SV-SMG-SUP", "RefTitle": "LMDB/IBase: Duplicate clients for dual stack systems", "RefUrl": "/notes/1803874 "}, {"RefNumber": "1887558", "RefComponent": "SV-SMG", "RefTitle": "Usage data collector for QGM", "RefUrl": "/notes/1887558 "}, {"RefNumber": "1849006", "RefComponent": "SV-SMG-LDB", "RefTitle": "All traffic lights grey in Solution Manager Configuration", "RefUrl": "/notes/1849006 "}, {"RefNumber": "1844394", "RefComponent": "SV-SMG-SUP", "RefTitle": "Contact person cannot be entered via BP id directly", "RefUrl": "/notes/1844394 "}, {"RefNumber": "1572183", "RefComponent": "SV-SMG-AUT", "RefTitle": "Authorizations for SAP Solution Manager RFC users", "RefUrl": "/notes/1572183 "}, {"RefNumber": "1875419", "RefComponent": "SV-SMG-SUP", "RefTitle": "ST710:AI_CRM_IM_UPDATE_FROM_SAP has a long runtime", "RefUrl": "/notes/1875419 "}, {"RefNumber": "1768544", "RefComponent": "SV-SMG-MON-EEM", "RefTitle": "Corrections for EEM 7.1 SP06 and SP07", "RefUrl": "/notes/1768544 "}, {"RefNumber": "1878887", "RefComponent": "SV-SMG-LDB", "RefTitle": "Missing CVs in cl_diagls_util=>get_all_scv_with_lsc_ids_f_ts", "RefUrl": "/notes/1878887 "}, {"RefNumber": "1758150", "RefComponent": "SV-SMG-DIA-APP-CA", "RefTitle": "CCDB: Extraction for local system failed", "RefUrl": "/notes/1758150 "}, {"RefNumber": "1679697", "RefComponent": "BC-MUS-POW", "RefTitle": "POWL: Selection criteria ignored during POWL rendering", "RefUrl": "/notes/1679697 "}, {"RefNumber": "1849566", "RefComponent": "SV-SMG-SVC", "RefTitle": "Users without authorization see the installations", "RefUrl": "/notes/1849566 "}, {"RefNumber": "1813468", "RefComponent": "BC-WD-ABA", "RefTitle": "Web Dynpro: Conversion for non-printable chars (NON-UNICODE)", "RefUrl": "/notes/1813468 "}, {"RefNumber": "1738511", "RefComponent": "SV-SMG-IMP", "RefTitle": "Editing MS Office documents in SAP Solution Manager", "RefUrl": "/notes/1738511 "}, {"RefNumber": "1836214", "RefComponent": "SV-SMG-SR", "RefTitle": "SysRec: short dumps in managed systems with user SOLMAN_BTC", "RefUrl": "/notes/1836214 "}, {"RefNumber": "1738276", "RefComponent": "SV-SMG-SVD-SWB", "RefTitle": "DSA: Word creation dumps caused by memory lack", "RefUrl": "/notes/1738276 "}, {"RefNumber": "1740375", "RefComponent": "SV-SMG-SUP", "RefTitle": "Incident creation fails in Test Workbench or Help Menu", "RefUrl": "/notes/1740375 "}, {"RefNumber": "1851723", "RefComponent": "SV-SMG-SVC", "RefTitle": "REFRESH_ADMIN_DATA_FROM_SUPPORT no licence data generation", "RefUrl": "/notes/1851723 "}, {"RefNumber": "1862212", "RefComponent": "SV-SMG-SDG", "RefTitle": "BI Reporting alerts show incorrect details", "RefUrl": "/notes/1862212 "}, {"RefNumber": "1654623", "RefComponent": "SV-SMG-MON-ALR-CFG", "RefTitle": "Problems when copying custom templates", "RefUrl": "/notes/1654623 "}, {"RefNumber": "875986", "RefComponent": "BC-UPG-NA", "RefTitle": "Note Assistant: Important notes for SAP_BASIS up to 702", "RefUrl": "/notes/875986 "}, {"RefNumber": "1837889", "RefComponent": "SV-SMG-SUP", "RefTitle": "VAR scenario: Unjustified deactivation of S users", "RefUrl": "/notes/1837889 "}, {"RefNumber": "1775564", "RefComponent": "SV-SMG-MON-BPM", "RefTitle": "Issues with BPMon Mirgration to SM 7.1 SP 5 or higher", "RefUrl": "/notes/1775564 "}, {"RefNumber": "1855272", "RefComponent": "SV-SMG-INS-CFG-MNG", "RefTitle": "SOLMAN_SETUP: admin user shouldn't modify S* profiles", "RefUrl": "/notes/1855272 "}, {"RefNumber": "1786378", "RefComponent": "SV-SMG-LDB", "RefTitle": "LMDB-MOPZ-API: Filter for customer product data", "RefUrl": "/notes/1786378 "}, {"RefNumber": "1599582", "RefComponent": "SV-SMG-INS-CFG", "RefTitle": "Plugin Status Details - Delete sp_level check for ST-A/PI", "RefUrl": "/notes/1599582 "}, {"RefNumber": "1682750", "RefComponent": "BC-WD-ABA", "RefTitle": "FormattedTextView: Unexpected characters (such as #)", "RefUrl": "/notes/1682750 "}, {"RefNumber": "1847398", "RefComponent": "SV-SMG-INS-CFG", "RefTitle": "SOLMAN_SETUP: issue with the temporary connection to the BW", "RefUrl": "/notes/1847398 "}, {"RefNumber": "1780509", "RefComponent": "SV-SMG-MON-ALR-CFG", "RefTitle": "Select Options for Metric parameter values is not working", "RefUrl": "/notes/1780509 "}, {"RefNumber": "1780480", "RefComponent": "SV-SMG-PSM", "RefTitle": "Issues of Job documentation importing", "RefUrl": "/notes/1780480 "}, {"RefNumber": "1736368", "RefComponent": "SV-SMG-INS-CFG", "RefTitle": "Error in 'Replace Service Definitions' activity", "RefUrl": "/notes/1736368 "}, {"RefNumber": "1788217", "RefComponent": "SV-SMG-PSM", "RefTitle": "JSM: job step print&archive parameters related issue", "RefUrl": "/notes/1788217 "}, {"RefNumber": "1770638", "RefComponent": "SV-SMG-MON-ALR-CFG", "RefTitle": "Working with Templates in different languages", "RefUrl": "/notes/1770638 "}, {"RefNumber": "1743244", "RefComponent": "BC-MUS-POW", "RefTitle": "POWL: Better description needed in query template dropdown", "RefUrl": "/notes/1743244 "}, {"RefNumber": "1753936", "RefComponent": "SV-SMG-MON-ALR-CFG", "RefTitle": "Apply Delivery Fixes", "RefUrl": "/notes/1753936 "}, {"RefNumber": "1719016", "RefComponent": "SV-SMG-INS-CFG", "RefTitle": "SOLMAN_SETUP: obsolete roles still added to users", "RefUrl": "/notes/1719016 "}, {"RefNumber": "1743949", "RefComponent": "SV-SMG-SVD-SWB", "RefTitle": "Corrupted automatic service report sent via notification", "RefUrl": "/notes/1743949 "}, {"RefNumber": "1739620", "RefComponent": "SV-SMG-MON-ALR-CFG", "RefTitle": "Update Content Step Display Fixes", "RefUrl": "/notes/1739620 "}, {"RefNumber": "1629551", "RefComponent": "BC-WD-ABA", "RefTitle": "Business Graphics: Dump due to missing resources", "RefUrl": "/notes/1629551 "}, {"RefNumber": "1755218", "RefComponent": "SV-SMG-INS-CFG", "RefTitle": "Scope dependent parent step not accessible in solman_setup", "RefUrl": "/notes/1755218 "}, {"RefNumber": "1740217", "RefComponent": "SV-SMG-INS-CFG", "RefTitle": "Note Post Processing not enabled in solman_setup", "RefUrl": "/notes/1740217 "}, {"RefNumber": "1777302", "RefComponent": "SV-SMG-DVM", "RefTitle": "Wrong SOURCERDCDEST of extractor AGS_DVM_TOPOBJ_EXTRACT", "RefUrl": "/notes/1777302 "}, {"RefNumber": "1834983", "RefComponent": "SV-SMG-LDB", "RefTitle": "SMSY: Unexpected overwriting of system descriptions", "RefUrl": "/notes/1834983 "}, {"RefNumber": "1769570", "RefComponent": "SV-SMG-DVM", "RefTitle": "Function Moduel SMD_DATA_LOADER101 not found", "RefUrl": "/notes/1769570 "}, {"RefNumber": "1821638", "RefComponent": "CRM-BTX-SRQ", "RefTitle": "Rule Policy on Multilevel Categorization", "RefUrl": "/notes/1821638 "}, {"RefNumber": "1769053", "RefComponent": "SV-SMG-DIA-SRV-SET", "RefTitle": "Instance Log Path not displayed for <PERSON>a", "RefUrl": "/notes/1769053 "}, {"RefNumber": "1821727", "RefComponent": "BC-MUS-POW", "RefTitle": "POWL: Detail area not updated in accordance with current lead selection", "RefUrl": "/notes/1821727 "}, {"RefNumber": "1809705", "RefComponent": "SV-SMG-SUP", "RefTitle": "ISV: SAP status not set correctly after sending to customer", "RefUrl": "/notes/1809705 "}, {"RefNumber": "1801878", "RefComponent": "SV-SMG-INS-CFG", "RefTitle": "SOLMAN_SETUP: warning wrongly raised for managed admin user", "RefUrl": "/notes/1801878 "}, {"RefNumber": "1826982", "RefComponent": "SV-SMG-SUP", "RefTitle": "IBase: Not authorized for initial object", "RefUrl": "/notes/1826982 "}, {"RefNumber": "1797388", "RefComponent": "BC-WD-ABA", "RefTitle": "Web Dynpro: Conversion of non-prinatable characters", "RefUrl": "/notes/1797388 "}, {"RefNumber": "1824197", "RefComponent": "SV-SMG-TWB-PLN", "RefTitle": "STWB_2 - Test plan display requires re-logon", "RefUrl": "/notes/1824197 "}, {"RefNumber": "1808259", "RefComponent": "SV-SMG-SVD-GSS", "RefTitle": "Wrong information text for column title in Web DSA UI", "RefUrl": "/notes/1808259 "}, {"RefNumber": "1824471", "RefComponent": "SV-SMG-MON-ALR", "RefTitle": "MAI: Metric \"DBACockpit Connection\" is gray when DB is down", "RefUrl": "/notes/1824471 "}, {"RefNumber": "1820291", "RefComponent": "SV-SMG-IMP-BIM", "RefTitle": "Project Documentation tab flagged as changed in Template", "RefUrl": "/notes/1820291 "}, {"RefNumber": "1823420", "RefComponent": "SV-SMG-DIA-APP-CA", "RefTitle": "CCDB: Fatal Error - CX_COMPONENT_VERSION_NOT_FOUND", "RefUrl": "/notes/1823420 "}, {"RefNumber": "1813914", "RefComponent": "SV-SMG-DIA-APP-CA", "RefTitle": "CCDB: ConfigStore bo40.dump_all_xml - SYSTEM_NO_ROLL", "RefUrl": "/notes/1813914 "}, {"RefNumber": "1819525", "RefComponent": "SV-SMG-SDG", "RefTitle": "Self-Diagnosis job fails due to error in alert 234", "RefUrl": "/notes/1819525 "}, {"RefNumber": "1817266", "RefComponent": "SV-SMG-SDG", "RefTitle": "First row of functions table in Self-Diagnosis is not shown", "RefUrl": "/notes/1817266 "}, {"RefNumber": "1818156", "RefComponent": "SV-SMG-GPF-AUT", "RefTitle": "Guided Procedure Authoring- fix Request &TRKORR& is unknown", "RefUrl": "/notes/1818156 "}, {"RefNumber": "1814977", "RefComponent": "SV-SMG-INS-CFG", "RefTitle": "Missing Contact Person Role for Template Users.", "RefUrl": "/notes/1814977 "}, {"RefNumber": "1763274", "RefComponent": "BC-WD-ABA", "RefTitle": "Select options: Error when deleting a parameter", "RefUrl": "/notes/1763274 "}, {"RefNumber": "1774271", "RefComponent": "SV-SMG-GPF-AUT", "RefTitle": "GPA Fix incomplete transport request for Guided Procedure", "RefUrl": "/notes/1774271 "}, {"RefNumber": "1800683", "RefComponent": "SV-SMG-IMP-BIM", "RefTitle": "Possible loss of data in Compare and Adjust", "RefUrl": "/notes/1800683 "}, {"RefNumber": "1820285", "RefComponent": "SV-SMG-IMP-BIM", "RefTitle": "Comparison against successor nodes shows wrong results", "RefUrl": "/notes/1820285 "}, {"RefNumber": "1814921", "RefComponent": "BC-CCM-BTC", "RefTitle": "BAE: Performance issue when using RFC_VERIFY_DESTINATION", "RefUrl": "/notes/1814921 "}, {"RefNumber": "1233384", "RefComponent": "CRM-BTX-SVO", "RefTitle": "Solution Manager Service Desk for ISVs", "RefUrl": "/notes/1233384 "}, {"RefNumber": "1817955", "RefComponent": "SV-SMG-SUP", "RefTitle": "LMDB/IBase: Duplicated objects", "RefUrl": "/notes/1817955 "}, {"RefNumber": "1812046", "RefComponent": "SV-SMG-INS-CFG", "RefTitle": "SOLMAN_SETUP: issue in role update on systems linked to CUA", "RefUrl": "/notes/1812046 "}, {"RefNumber": "1810060", "RefComponent": "SV-SMG-DVM", "RefTitle": "DVM:Multiple Save Not Working in Analysis Admin.", "RefUrl": "/notes/1810060 "}, {"RefNumber": "1559499", "RefComponent": "SV-SMG-MON-SYS", "RefTitle": "DPC data providers as of ST-PI 2008_1_XX SP4", "RefUrl": "/notes/1559499 "}, {"RefNumber": "1808294", "RefComponent": "SV-SMG-SUP", "RefTitle": "Incorrect automatic assignment settings in problem", "RefUrl": "/notes/1808294 "}, {"RefNumber": "1766608", "RefComponent": "SV-SMG-INS-CFG", "RefTitle": "Activity status reset when using activity \"navigation\"", "RefUrl": "/notes/1766608 "}, {"RefNumber": "1810837", "RefComponent": "SV-SMG-SER-ESR", "RefTitle": "BI Content Activation error in SolMan Basic Configuration", "RefUrl": "/notes/1810837 "}, {"RefNumber": "1771378", "RefComponent": "HAN-DB", "RefTitle": "HANA Extractors for Solution Manager", "RefUrl": "/notes/1771378 "}, {"RefNumber": "1754672", "RefComponent": "SV-SMG-DVM", "RefTitle": "Correction for DVM Template creation", "RefUrl": "/notes/1754672 "}, {"RefNumber": "1802520", "RefComponent": "BC-DB-DB6-CCM", "RefTitle": "DPW: <PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON> in Data Loader", "RefUrl": "/notes/1802520 "}, {"RefNumber": "1761039", "RefComponent": "SV-SMG-MON-BPM", "RefTitle": "Advance Corrections BPMon SM 7.1 ST710 delivered with SP08", "RefUrl": "/notes/1761039 "}, {"RefNumber": "1778300", "RefComponent": "SV-SMG-DIA", "RefTitle": "DPW: Short dump during data deletion", "RefUrl": "/notes/1778300 "}, {"RefNumber": "1777317", "RefComponent": "SV-SMG-DIA", "RefTitle": "DPW: No aggregation of daily values to monthly values", "RefUrl": "/notes/1777317 "}, {"RefNumber": "1780349", "RefComponent": "BC-TWB-TST-ECA", "RefTitle": "Error in CL_APL_ECATT_SCRIPT_API->CREATE_CMD_INF_PARAM", "RefUrl": "/notes/1780349 "}, {"RefNumber": "1791436", "RefComponent": "SV-SMG-DVM", "RefTitle": "DVM GSS: Incorrect calculation of Content Coverage", "RefUrl": "/notes/1791436 "}, {"RefNumber": "1799283", "RefComponent": "SV-SMG-SDG", "RefTitle": "Erroneous date format in Self-Diagnosis", "RefUrl": "/notes/1799283 "}, {"RefNumber": "1797629", "RefComponent": "SV-SMG-INS-CFG", "RefTitle": "E2E_HK_CONTROLLER report scheduled twice", "RefUrl": "/notes/1797629 "}, {"RefNumber": "1763066", "RefComponent": "SV-SMG-MON-ALR-CNS", "RefTitle": "Mismatch in number of alerts in alert inbox tables", "RefUrl": "/notes/1763066 "}, {"RefNumber": "1796193", "RefComponent": "SV-SMG-MON-ALR-CNS", "RefTitle": "New alert creation on manual notification/incident creation", "RefUrl": "/notes/1796193 "}, {"RefNumber": "1783371", "RefComponent": "SV-SMG-MAI", "RefTitle": "MOpz: System landscape data incomplete in Customer Profile", "RefUrl": "/notes/1783371 "}, {"RefNumber": "1754992", "RefComponent": "SV-SMG-MON-ALR-CFG", "RefTitle": "Create Transport Request Failed with package checks", "RefUrl": "/notes/1754992 "}, {"RefNumber": "1733236", "RefComponent": "SV-SMG-MON-BPM", "RefTitle": "Advance Corrections BPMon SM 7.1 ST710 delivered with SP07", "RefUrl": "/notes/1733236 "}, {"RefNumber": "1775643", "RefComponent": "SV-SMG-SUP", "RefTitle": "AI_SDK_SP_GENERATE_BP_V2: diverse corrections III", "RefUrl": "/notes/1775643 "}, {"RefNumber": "1786117", "RefComponent": "SV-SMG-INS-CFG", "RefTitle": "Solman_Setup: System Alias not availble after running Setup", "RefUrl": "/notes/1786117 "}, {"RefNumber": "1784991", "RefComponent": "SV-SMG-SER-ESR", "RefTitle": "Post-processing of SOLMAN_SETUP re-execute", "RefUrl": "/notes/1784991 "}, {"RefNumber": "1780089", "RefComponent": "SV-SMG-DVM", "RefTitle": "No available RFC for system of DVM template configuraion", "RefUrl": "/notes/1780089 "}, {"RefNumber": "1011229", "RefComponent": "SV-SMG-SER", "RefTitle": "ST-PI: Corrections for E2E Diagnostics", "RefUrl": "/notes/1011229 "}, {"RefNumber": "1788346", "RefComponent": "SV-SMG-MON-ALR", "RefTitle": "MAI: Wrong calculation of deadlock, lock escalation", "RefUrl": "/notes/1788346 "}, {"RefNumber": "1788486", "RefComponent": "SV-SMG-DVM", "RefTitle": "InfoProvider Settings not setup correctly", "RefUrl": "/notes/1788486 "}, {"RefNumber": "1760744", "RefComponent": "SV-SMG-INS-CFG", "RefTitle": "Add Diagnostics Relevant flag in LMDB for Solution Manager", "RefUrl": "/notes/1760744 "}, {"RefNumber": "1749774", "RefComponent": "SV-SMG-MON-ALR-CFG", "RefTitle": "Request popup opens, eventhough template saved under $TMP", "RefUrl": "/notes/1749774 "}, {"RefNumber": "1787921", "RefComponent": "SV-SMG-INS-CFG", "RefTitle": "Moving up \"Activate BW Source System\" activity in Basic Conf", "RefUrl": "/notes/1787921 "}, {"RefNumber": "1779435", "RefComponent": "SV-SMG-INS-CFG", "RefTitle": "System status details not expandable", "RefUrl": "/notes/1779435 "}, {"RefNumber": "1784446", "RefComponent": "SV-SMG-MON-ALR-CFG", "RefTitle": "Custom Metric Variants were not copied during copy template", "RefUrl": "/notes/1784446 "}, {"RefNumber": "1755568", "RefComponent": "SV-SMG-OP", "RefTitle": "BPMon attached to tech. interfaces: Copying does not work", "RefUrl": "/notes/1755568 "}, {"RefNumber": "1457391", "RefComponent": "AP-MD-IBA", "RefTitle": "Bad Performance while accessing table IBST.", "RefUrl": "/notes/1457391 "}, {"RefNumber": "1758475", "RefComponent": "SV-SMG-MON-ALR-CNS", "RefTitle": "Alert Inbox Dumps with error TYPE NOT FOUND.", "RefUrl": "/notes/1758475 "}, {"RefNumber": "1786016", "RefComponent": "SV-SMG-MON-ALR-CNS", "RefTitle": "Multiple specification of Managed Object tag dump fix", "RefUrl": "/notes/1786016 "}, {"RefNumber": "1776209", "RefComponent": "SV-SMG-INS-CFG", "RefTitle": "Reintroducing \"Configure Gateway\" Step in System Prep.", "RefUrl": "/notes/1776209 "}, {"RefNumber": "1784629", "RefComponent": "SV-SMG-INS-CFG", "RefTitle": "Overview status correction", "RefUrl": "/notes/1784629 "}, {"RefNumber": "1784936", "RefComponent": "SV-SMG-MON-ALR-CFG", "RefTitle": "Transport Custom Metric Variants from one system to other system", "RefUrl": "/notes/1784936 "}, {"RefNumber": "1783860", "RefComponent": "SV-SMG-DVM", "RefTitle": "Wrong calendard date in Extractor AGS_DVM_TOPOBJ_EXTRACT", "RefUrl": "/notes/1783860 "}, {"RefNumber": "1764070", "RefComponent": "SV-SMG-INS-CFG", "RefTitle": "Changing status in 'Connect managed system'", "RefUrl": "/notes/1764070 "}, {"RefNumber": "1783688", "RefComponent": "SV-SMG-MON-ALR-CFG", "RefTitle": "Propagation still happens though it is switched off", "RefUrl": "/notes/1783688 "}, {"RefNumber": "1710791", "RefComponent": "BC-TWB-ORG", "RefTitle": "Wrong RFC destination during test plan data read access", "RefUrl": "/notes/1710791 "}, {"RefNumber": "1759409", "RefComponent": "BC-MUS-POW", "RefTitle": "POWL: Dump when \"enable default lead selection\" is ON", "RefUrl": "/notes/1759409 "}, {"RefNumber": "1643760", "RefComponent": "SV-SMG-LDB", "RefTitle": "No target namespace when settung up LMDB", "RefUrl": "/notes/1643760 "}, {"RefNumber": "1771470", "RefComponent": "SV-SMG-INS-CFG", "RefTitle": "SOLMAN_SETUP: impossible to update user roles other than Z", "RefUrl": "/notes/1771470 "}, {"RefNumber": "1765809", "RefComponent": "SV-SMG-INS-CFG", "RefTitle": "SOLMAN_SETUP: status of java user step is wrongly warning", "RefUrl": "/notes/1765809 "}, {"RefNumber": "1765074", "RefComponent": "SV-SMG-INS-CFG", "RefTitle": "SOLMAN_SETUP: status of step users not updated", "RefUrl": "/notes/1765074 "}, {"RefNumber": "1762087", "RefComponent": "SV-SMG-INS-CFG", "RefTitle": "SOLMAN_SETUP: message 'invalid date' when creating users", "RefUrl": "/notes/1762087 "}, {"RefNumber": "1779513", "RefComponent": "SV-SMG-INS-CFG", "RefTitle": "Solman_setup:generic storage notif remove key/value from log", "RefUrl": "/notes/1779513 "}, {"RefNumber": "1749788", "RefComponent": "SV-SMG-DVM", "RefTitle": "Correction - Housekeeping Settings of Data Volume Management", "RefUrl": "/notes/1749788 "}, {"RefNumber": "1778532", "RefComponent": "SV-SMG-INS-CFG", "RefTitle": "SOLMAN_SETUP: change of role name for user SM_BW", "RefUrl": "/notes/1778532 "}, {"RefNumber": "1775883", "RefComponent": "SV-SMG-SVD-GSS", "RefTitle": "GSS: unexpected dump when using webdynpro logon popup in GSS", "RefUrl": "/notes/1775883 "}, {"RefNumber": "1734897", "RefComponent": "SV-SMG-OP", "RefTitle": "Upgrade SP05/06/07:Error in maintenance project or BPMon", "RefUrl": "/notes/1734897 "}, {"RefNumber": "1777767", "RefComponent": "SV-SMG-IMP-BIM", "RefTitle": "Incomplete display of messages in component view", "RefUrl": "/notes/1777767 "}, {"RefNumber": "1745786", "RefComponent": "SV-SMG-DIA-SRV-LSC", "RefTitle": "Landscape LMDB wrapper SP6 related corrections", "RefUrl": "/notes/1745786 "}, {"RefNumber": "1774418", "RefComponent": "BC-MUS-POW", "RefTitle": "POWL: Unnecessary refresh triggered when using query api", "RefUrl": "/notes/1774418 "}, {"RefNumber": "1718522", "RefComponent": "SV-SMG-IMP", "RefTitle": "Customizing table SMCRM_CUST_APPL - General corrections", "RefUrl": "/notes/1718522 "}, {"RefNumber": "1763793", "RefComponent": "BC-MUS-POW", "RefTitle": "POWL: Column headers are rendered incorrectly", "RefUrl": "/notes/1763793 "}, {"RefNumber": "1768764", "RefComponent": "SV-SMG-TWB-REP", "RefTitle": "TWB reporting displays incorrect test case status counters", "RefUrl": "/notes/1768764 "}, {"RefNumber": "1737347", "RefComponent": "CRM-IU-S", "RefTitle": "structure COMT_PROD_SRV_MAINTAIN_API contains too long data", "RefUrl": "/notes/1737347 "}, {"RefNumber": "1745716", "RefComponent": "SV-SMG-INS-CFG", "RefTitle": "issue with EFWK RESOURCE MANAGER report scheduled twice", "RefUrl": "/notes/1745716 "}, {"RefNumber": "1753107", "RefComponent": "SV-SMG-INS-CFG", "RefTitle": "Fixing context's loss when executing an automatic activity.", "RefUrl": "/notes/1753107 "}, {"RefNumber": "1762967", "RefComponent": "SV-SMG-MON-REP", "RefTitle": "DPW: Overlapping records in DBH_CUBE_DATA", "RefUrl": "/notes/1762967 "}, {"RefNumber": "1753587", "RefComponent": "SV-SMG-INS-CFG", "RefTitle": "\"No Scope Selected\" message although a scope is selected.", "RefUrl": "/notes/1753587 "}, {"RefNumber": "1761913", "RefComponent": "SV-SMG-INS-CFG", "RefTitle": "Activity table update for SP6 (AGS_GS_MIGRATION)", "RefUrl": "/notes/1761913 "}, {"RefNumber": "1734250", "RefComponent": "BC-MUS-POW", "RefTitle": "POWL: Error in detail area update", "RefUrl": "/notes/1734250 "}, {"RefNumber": "1702711", "RefComponent": "BC-DB-HDB-CCM", "RefTitle": "Adjustments for Solution Manager update as of 7.1 SP 5", "RefUrl": "/notes/1702711 "}, {"RefNumber": "1758772", "RefComponent": "SV-SMG-SUP", "RefTitle": "Edit button is missing in details assignment block", "RefUrl": "/notes/1758772 "}, {"RefNumber": "1747859", "RefComponent": "SV-SMG-INS-CFG", "RefTitle": "Create or Update Administrator from Connect Managed System", "RefUrl": "/notes/1747859 "}, {"RefNumber": "1753209", "RefComponent": "SV-SMG-INS-CFG", "RefTitle": "SOLMAN_SETUP ITSM 2.2 Incident/SR Templates - wrong types.", "RefUrl": "/notes/1753209 "}, {"RefNumber": "1727976", "RefComponent": "BC-DB-ORA-CCM", "RefTitle": "Error in setup of Database Perf. Warehouse for local DB", "RefUrl": "/notes/1727976 "}, {"RefNumber": "1745879", "RefComponent": "SV-SMG-SDG", "RefTitle": "Failed to get RTCC recommendation due to RFC call failure.", "RefUrl": "/notes/1745879 "}, {"RefNumber": "1712468", "RefComponent": "SV-SMG-DIA-SRV-LSC", "RefTitle": "DBA: Outside db discovery support for virtual hostnames", "RefUrl": "/notes/1712468 "}, {"RefNumber": "1755389", "RefComponent": "SV-SMG-MON-ALR-PRV", "RefTitle": "Metrics: \"Number of Exceptions in Domain Log\" is grey", "RefUrl": "/notes/1755389 "}, {"RefNumber": "1740467", "RefComponent": "SV-SMG-MON-ALR-CLC", "RefTitle": "ECE: Event Calculation Engine Note for SP06", "RefUrl": "/notes/1740467 "}, {"RefNumber": "1748138", "RefComponent": "BC-MUS-POW", "RefTitle": "POWL: Unnecessary update of detail area", "RefUrl": "/notes/1748138 "}, {"RefNumber": "1740466", "RefComponent": "SV-SMG-MON-ALR-CLC", "RefTitle": "ECE: old metrics are used if rule type is \"Already Rated\"", "RefUrl": "/notes/1740466 "}, {"RefNumber": "1755049", "RefComponent": "SV-SMG-INS-CFG", "RefTitle": "Unable to create RFC with SAP router", "RefUrl": "/notes/1755049 "}, {"RefNumber": "1741080", "RefComponent": "SV-SMG-MON-ALR", "RefTitle": "no/wrong metrics collected on SMD Agent assoicated to DB", "RefUrl": "/notes/1741080 "}, {"RefNumber": "1748192", "RefComponent": "BC-DB-DB6-CCM", "RefTitle": "DBA: Validity Fallback Is Not Evaluated", "RefUrl": "/notes/1748192 "}, {"RefNumber": "1754637", "RefComponent": "SV-SMG-INS-CFG", "RefTitle": "DVM Setup: wrong navigation link", "RefUrl": "/notes/1754637 "}, {"RefNumber": "1752517", "RefComponent": "SV-SMG-INS-CFG", "RefTitle": "Managed system configuration - warning in step 7 create user", "RefUrl": "/notes/1752517 "}, {"RefNumber": "1754275", "RefComponent": "SV-SMG-INS-CFG", "RefTitle": "Wrong message 'RFC SM_XXXCLNTnnn_BACK does not exist'", "RefUrl": "/notes/1754275 "}, {"RefNumber": "1753639", "RefComponent": "SV-SMG-INS-CFG", "RefTitle": "Adding the \"Scenario Operations\" button on Define scope step", "RefUrl": "/notes/1753639 "}, {"RefNumber": "1753985", "RefComponent": "SV-SMG-SDG", "RefTitle": "Downloaded XML data for Root Cause Analysis is not correct", "RefUrl": "/notes/1753985 "}, {"RefNumber": "1753625", "RefComponent": "SV-SMG-INS-CFG", "RefTitle": "The Post processing section is not visible in step2 of CHARM", "RefUrl": "/notes/1753625 "}, {"RefNumber": "1750994", "RefComponent": "SV-SMG-SDG", "RefTitle": "Alert/subalert name/id are missing in XML generation", "RefUrl": "/notes/1750994 "}, {"RefNumber": "1752181", "RefComponent": "SV-SMG-MON-ALR-PRV", "RefTitle": "Grey metrics in Technical System Monitoring", "RefUrl": "/notes/1752181 "}, {"RefNumber": "1734464", "RefComponent": "SV-SMG-INS-CFG", "RefTitle": "SOLMAN_SETUP: notes to be checked are wrong", "RefUrl": "/notes/1734464 "}, {"RefNumber": "1731879", "RefComponent": "SV-SMG-INS-CFG", "RefTitle": "Activate BI Content for INCIDENT and CHARM,and BPO DASHBOARD", "RefUrl": "/notes/1731879 "}, {"RefNumber": "1705783", "RefComponent": "BC-DB-DB6-CCM", "RefTitle": "Remote monitoring of SAP Solution Manager failed with a dump", "RefUrl": "/notes/1705783 "}, {"RefNumber": "1740545", "RefComponent": "CRM-IC-BF-CAT", "RefTitle": "Categories trigger entity data change", "RefUrl": "/notes/1740545 "}, {"RefNumber": "1735641", "RefComponent": "BC-MUS-POW", "RefTitle": "POWL: Criteria Personalization not saved for queries", "RefUrl": "/notes/1735641 "}, {"RefNumber": "1749795", "RefComponent": "BC-DB-DB6-CCM", "RefTitle": "DPW: Housekeeping does not finish", "RefUrl": "/notes/1749795 "}, {"RefNumber": "1749374", "RefComponent": "SV-SMG-SUP", "RefTitle": "Creation of I-Objects: Incorrect message type in appl log", "RefUrl": "/notes/1749374 "}, {"RefNumber": "1741525", "RefComponent": "SV-SMG-SUP", "RefTitle": "VAR: Multiple nodes per customer in IBase", "RefUrl": "/notes/1741525 "}, {"RefNumber": "1653635", "RefComponent": "HAN-DB", "RefTitle": "New HANA Extraktors for BASIS SP10;SP11 and some corrections", "RefUrl": "/notes/1653635 "}, {"RefNumber": "1729270", "RefComponent": "BC-DB-HDB-CCM", "RefTitle": "Collective corrections BC-DB-HDB-CCM for SAP_BASIS 7.02 SP11", "RefUrl": "/notes/1729270 "}, {"RefNumber": "1741470", "RefComponent": "BC-WD-CMP-ALV-ABA", "RefTitle": "WD ABAP ALV error when context changes are dispatched", "RefUrl": "/notes/1741470 "}, {"RefNumber": "1733824", "RefComponent": "BC-WD-ABA", "RefTitle": "Dump in CL_WDR_NOTIFICATION", "RefUrl": "/notes/1733824 "}, {"RefNumber": "1746625", "RefComponent": "SV-SMG-BPR", "RefTitle": "Extended Search in Business Process Repository does not work", "RefUrl": "/notes/1746625 "}, {"RefNumber": "1746103", "RefComponent": "SV-SMG-DVM", "RefTitle": "Solution Manager: Turning Configuration Step Status to Grey", "RefUrl": "/notes/1746103 "}, {"RefNumber": "1745114", "RefComponent": "SV-SMG", "RefTitle": "DBA: Dump in CL_DBA_DBH_CUBE_DATA", "RefUrl": "/notes/1745114 "}, {"RefNumber": "1745942", "RefComponent": "SV-SMG", "RefTitle": "DPW: Cube Aggregation Memory Usage", "RefUrl": "/notes/1745942 "}, {"RefNumber": "1737495", "RefComponent": "SV-SMG-DVM", "RefTitle": "Analysis Creation for Simple Archiving Objects not working", "RefUrl": "/notes/1737495 "}, {"RefNumber": "1719245", "RefComponent": "BC-DB-MSS-CCM", "RefTitle": "SolMan 7.1 PDW: data gaps in extractions for MSSQL systems", "RefUrl": "/notes/1719245 "}, {"RefNumber": "1742435", "RefComponent": "SV-SMG-LDB", "RefTitle": "LMDB: Confirmation error when creating a product system", "RefUrl": "/notes/1742435 "}, {"RefNumber": "1736740", "RefComponent": "SV-SMG-INS-CFG", "RefTitle": "SOLMAN_SETUP:java user manually maintained might be in error", "RefUrl": "/notes/1736740 "}, {"RefNumber": "1740720", "RefComponent": "SV-SMG-DIA-APP-CA", "RefTitle": "CCDB: Incorrect 'Customizing change detected' message", "RefUrl": "/notes/1740720 "}, {"RefNumber": "1737668", "RefComponent": "SV-SMG-MON-REP", "RefTitle": "Data not re-organized after upgrade", "RefUrl": "/notes/1737668 "}, {"RefNumber": "1682370", "RefComponent": "BC-MUS-POW", "RefTitle": "POWL: Removal of \"Reapply Table settings\" functionality", "RefUrl": "/notes/1682370 "}, {"RefNumber": "1694458", "RefComponent": "BC-CUS-TOL-PAD", "RefTitle": "SOLAR_PROJECT_ADMIN:Select Countries not Cleared/Refreshed", "RefUrl": "/notes/1694458 "}, {"RefNumber": "1736425", "RefComponent": "SV-SMG-LDB", "RefTitle": "Technical Scenario DUAL_STACK: MaxLen violated", "RefUrl": "/notes/1736425 "}, {"RefNumber": "1710384", "RefComponent": "BC-WD-CMP-ALV-ABA", "RefTitle": "WD ABAP ALV performance improvements", "RefUrl": "/notes/1710384 "}, {"RefNumber": "1712091", "RefComponent": "CRM-FRW-UI", "RefTitle": "CRM WebUI scrolls top on roundtrips", "RefUrl": "/notes/1712091 "}, {"RefNumber": "1687765", "RefComponent": "SV-SMG-IMP-PAD", "RefTitle": "Displaying tab page for RDS in project administration", "RefUrl": "/notes/1687765 "}, {"RefNumber": "1579462", "RefComponent": "BW-BEX-ET-WEB", "RefTitle": "Sending data from chart item without check to IGS", "RefUrl": "/notes/1579462 "}, {"RefNumber": "1710009", "RefComponent": "CRM-IC-EMS-CAT", "RefTitle": "Category Links are not copied to schema's new version", "RefUrl": "/notes/1710009 "}, {"RefNumber": "1673201", "RefComponent": "BC-MUS-POW", "RefTitle": "POWL: Row selection lost on Single/Multi selection modes", "RefUrl": "/notes/1673201 "}, {"RefNumber": "1697052", "RefComponent": "BC-WD-ABA", "RefTitle": "WDA: Link in FormattedTextView causes a dump", "RefUrl": "/notes/1697052 "}, {"RefNumber": "1696526", "RefComponent": "SV-SMG-TWB-PLN", "RefTitle": "Test workbench requests login to local system", "RefUrl": "/notes/1696526 "}, {"RefNumber": "1679740", "RefComponent": "SV-SMG-IMP-PAD", "RefTitle": "Navigation on scope tab page during scenario comparison", "RefUrl": "/notes/1679740 "}, {"RefNumber": "1174591", "RefComponent": "SCM-TM-TCM-FA", "RefTitle": "Freight Agreement: Status Information not updated", "RefUrl": "/notes/1174591 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "ST", "From": "710", "To": "710", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": [{"SoftwareComponent": "ST", "NumberOfCorrin": 1, "URL": "/corrins/0001722332/162"}]}, "ManualActions": {"_label": "Manual Activities", "value": " <code>   <P><br/><br/>------------------------------------------------------------------------<br/>|Manual Activity&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>------------------------------------------------------------------------<br/>|VALID FOR&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>|Software Component&nbsp;&nbsp; ST&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;SAP Solution Ma...|<br/>| Release 710&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;SAPKITL706 - SAPKITL706&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>------------------------------------------------------------------------<br/><br/><B>Caution</B>: You have to perform this manual activity separately in each system into which you transport the Note for  implementation.<br/></P> <OL>1. Updating the available SAP Notes</OL> <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;When you implement this SAP Note, the system does not automatically take  into account whether there are newer versions of required SAP Notes that have already been implemented in SAP Support Portal. <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;To ensure that all prerequisite notes are implemented in the current  version, call the Note Assistant (transaction SNOTE), choose \"Goto -&gt;  SAP Note Browser -&gt; Execute (F8)\", and then choose \"Download Latest Version of SAP Notes\" in the application toolbar. <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;As a result, when you implement this SAP Note using the Note Assistant,  the system checks whether the necessary SAP Notes have been implemented  successfully. If this is not the case, the Note Assistant implements the latest version. <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<OL>1. Update the Note Assistant</OL> <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Implement SAP Note 875986 to ensure that you use the current version of the Note Assistant (transaction SNOTE). <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Then, exit the Note Assistant (transaction SNOTE) and start it again. <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<OL>1. Perform an SPAU adjustment</OL> <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Call transaction SPAU (\"Modification Adjustment: Object Selection\") and check the status of the SAP Notes. <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Perform the adjustment for all SAP Notes that already exist or reset them to their original status. <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;You can find further information about the modification adjustment in  the documentation and in the SDN (for example here: http://scn.sap.com/docs/DOC-10312). <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<OL>1. Ensure that there are no inactive objects in your system</OL> <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Call transaction SE80 (\"Object Navigator\"). You can use this transaction to display any inactive objects in the system. <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;To do this, select \"Inactive Objects\" from the dropdown menu and enter \"*\" in the user field. <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Ensure that there are no inactive objects in your system. <P></P> <br/>  </code> "}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 1, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 1, "state": "Error"}, "Prerequisites": {"_label": "Prerequisites", "value": 232, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": [{"SoftwareComponent": "BBPCRM", "ValidFrom": "600", "ValidTo": "701", "Number": "1094074 ", "URL": "/notes/1094074 ", "Title": "Unnecessary Authority Check in CRM_DNO_MONITOR", "Component": "CRM-BTX-SVO"}, {"SoftwareComponent": "BBPCRM", "ValidFrom": "701", "ValidTo": "701", "Number": "1233384 ", "URL": "/notes/1233384 ", "Title": "Solution Manager Service Desk for ISVs", "Component": "CRM-BTX-SVO"}, {"SoftwareComponent": "BBPCRM", "ValidFrom": "701", "ValidTo": "701", "Number": "1740545 ", "URL": "/notes/1740545 ", "Title": "Categories trigger entity data change", "Component": "CRM-IC-BF-CAT"}, {"SoftwareComponent": "BBPCRM", "ValidFrom": "701", "ValidTo": "701", "Number": "1821638 ", "URL": "/notes/1821638 ", "Title": "Rule Policy on Multilevel Categorization", "Component": "CRM-BTX-SRQ"}, {"SoftwareComponent": "SAP_ABA", "ValidFrom": "700", "ValidTo": "702", "Number": "1735641 ", "URL": "/notes/1735641 ", "Title": "POWL: Criteria Personalization not saved for queries", "Component": "BC-MUS-POW"}, {"SoftwareComponent": "SAP_ABA", "ValidFrom": "700", "ValidTo": "702", "Number": "1763793 ", "URL": "/notes/1763793 ", "Title": "POWL: Column headers are rendered incorrectly", "Component": "BC-MUS-POW"}, {"SoftwareComponent": "SAP_ABA", "ValidFrom": "700", "ValidTo": "702", "Number": "1774418 ", "URL": "/notes/1774418 ", "Title": "POWL: Unnecessary refresh triggered when using query api", "Component": "BC-MUS-POW"}, {"SoftwareComponent": "SAP_ABA", "ValidFrom": "701", "ValidTo": "702", "Number": "1759409 ", "URL": "/notes/1759409 ", "Title": "POWL: Dump when \"enable default lead selection\" is ON", "Component": "BC-MUS-POW"}, {"SoftwareComponent": "SAP_ABA", "ValidFrom": "702", "ValidTo": "702", "Number": "1673201 ", "URL": "/notes/1673201 ", "Title": "POWL: Row selection lost on Single/Multi selection modes", "Component": "BC-MUS-POW"}, {"SoftwareComponent": "SAP_ABA", "ValidFrom": "702", "ValidTo": "702", "Number": "1734250 ", "URL": "/notes/1734250 ", "Title": "POWL: Error in detail area update", "Component": "BC-MUS-POW"}, {"SoftwareComponent": "SAP_ABA", "ValidFrom": "702", "ValidTo": "702", "Number": "1743244 ", "URL": "/notes/1743244 ", "Title": "POWL: Better description needed in query template dropdown", "Component": "BC-MUS-POW"}, {"SoftwareComponent": "SAP_ABA", "ValidFrom": "702", "ValidTo": "702", "Number": "1748138 ", "URL": "/notes/1748138 ", "Title": "POWL: Unnecessary update of detail area", "Component": "BC-MUS-POW"}, {"SoftwareComponent": "SAP_ABA", "ValidFrom": "702", "ValidTo": "702", "Number": "1909714 ", "URL": "/notes/1909714 ", "Title": "POWL:Dump when switching the queries", "Component": "BC-MUS-POW"}, {"SoftwareComponent": "SAP_AP", "ValidFrom": "700", "ValidTo": "700", "Number": "1457391 ", "URL": "/notes/1457391 ", "Title": "Bad Performance while accessing table IBST.", "Component": "AP-MD-IBA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "700", "ValidTo": "702", "Number": "1738511 ", "URL": "/notes/1738511 ", "Title": "Editing MS Office documents in SAP Solution Manager", "Component": "SV-SMG-IMP"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "700", "ValidTo": "740", "Number": "1885750 ", "URL": "/notes/1885750 ", "Title": "Dump ITAB_ILLEGAL_SORT_ORDER in CL_SBAL_LOGGER", "Component": "BC-CCM-BTC"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "701", "ValidTo": "731", "Number": "1814921 ", "URL": "/notes/1814921 ", "Title": "BAE: Performance issue when using RFC_VERIFY_DESTINATION", "Component": "BC-CCM-BTC"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "702", "ValidTo": "702", "Number": "1629551 ", "URL": "/notes/1629551 ", "Title": "Business Graphics: Dump due to missing resources", "Component": "BC-WD-ABA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "702", "ValidTo": "702", "Number": "1679740 ", "URL": "/notes/1679740 ", "Title": "Navigation on scope tab page during scenario comparison", "Component": "SV-SMG-IMP-PAD"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "702", "ValidTo": "702", "Number": "1682750 ", "URL": "/notes/1682750 ", "Title": "FormattedTextView: Unexpected characters (such as &#xd;)", "Component": "BC-WD-ABA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "702", "ValidTo": "702", "Number": "1687765 ", "URL": "/notes/1687765 ", "Title": "Displaying tab page for RDS in project administration", "Component": "SV-SMG-IMP-PAD"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "702", "ValidTo": "702", "Number": "1694458 ", "URL": "/notes/1694458 ", "Title": "SOLAR_PROJECT_ADMIN:Select Countries not Cleared/Refreshed", "Component": "BC-CUS-TOL-PAD"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "702", "ValidTo": "702", "Number": "1696526 ", "URL": "/notes/1696526 ", "Title": "Test workbench requests login to local system", "Component": "SV-SMG-TWB-PLN"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "702", "ValidTo": "702", "Number": "1705783 ", "URL": "/notes/1705783 ", "Title": "Remote monitoring of SAP Solution Manager failed with a dump", "Component": "BC-DB-DB6-CCM"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "702", "ValidTo": "702", "Number": "1710791 ", "URL": "/notes/1710791 ", "Title": "Wrong RFC destination during test plan data read access", "Component": "BC-TWB-ORG"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "702", "ValidTo": "702", "Number": "1719245 ", "URL": "/notes/1719245 ", "Title": "SolMan 7.1 PDW: data gaps in extractions for MSSQL systems", "Component": "BC-DB-MSS-CCM"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "702", "ValidTo": "702", "Number": "1729270 ", "URL": "/notes/1729270 ", "Title": "Collective corrections BC-DB-HDB-CCM for SAP_BASIS 7.02 SP11", "Component": "BC-DB-HDB-CCM"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "702", "ValidTo": "702", "Number": "1733824 ", "URL": "/notes/1733824 ", "Title": "Dump in CL_WDR_NOTIFICATION", "Component": "BC-WD-ABA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "702", "ValidTo": "702", "Number": "1741470 ", "URL": "/notes/1741470 ", "Title": "WD ABAP ALV error when context changes are dispatched", "Component": "BC-WD-CMP-ALV-ABA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "702", "ValidTo": "702", "Number": "1758150 ", "URL": "/notes/1758150 ", "Title": "CCDB: Extraction for local system failed", "Component": "SV-SMG-DIA-APP-CA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "702", "ValidTo": "702", "Number": "1763274 ", "URL": "/notes/1763274 ", "Title": "Select options: Error when deleting a parameter", "Component": "BC-WD-ABA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "702", "ValidTo": "702", "Number": "1771378 ", "URL": "/notes/1771378 ", "Title": "HANA Extractors for Solution Manager", "Component": "HAN-DB"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "702", "ValidTo": "702", "Number": "1780349 ", "URL": "/notes/1780349 ", "Title": "Error in CL_APL_ECATT_SCRIPT_API->CREATE_CMD_INF_PARAM", "Component": "BC-TWB-TST-ECA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "702", "ValidTo": "702", "Number": "1788346 ", "URL": "/notes/1788346 ", "Title": "MAI: Wrong calculation of deadlock, lock escalation", "Component": "SV-SMG-MON-ALR"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "702", "ValidTo": "702", "Number": "1797388 ", "URL": "/notes/1797388 ", "Title": "Web Dynpro: Conversion of non-prinatable characters", "Component": "BC-WD-ABA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "702", "ValidTo": "702", "Number": "1811558 ", "URL": "/notes/1811558 ", "Title": "Extractors for HANA Database Analysis Workload", "Component": "HAN-DB"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "702", "ValidTo": "702", "Number": "1813468 ", "URL": "/notes/1813468 ", "Title": "Web Dynpro: Conversion for non-printable chars (NON-UNICODE)", "Component": "BC-WD-ABA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "702", "ValidTo": "702", "Number": "1816471 ", "URL": "/notes/1816471 ", "Title": "SE24: Error for POST methods for defined preferred parameter", "Component": "BC-DWB-TOO-ENH"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "702", "ValidTo": "702", "Number": "1824197 ", "URL": "/notes/1824197 ", "Title": "STWB_2 - Test plan display requires re-logon", "Component": "SV-SMG-TWB-PLN"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "702", "ValidTo": "702", "Number": "1890903 ", "URL": "/notes/1890903 ", "Title": "HANA Extractors for Solution Manager: Global table size", "Component": "HAN-DB"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "702", "ValidTo": "731", "Number": "1700427 ", "URL": "/notes/1700427 ", "Title": "MaxDB: Potential corruption of DB release in DB6NAVSYST", "Component": "BC-DB-SDB-CCM"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "702", "ValidTo": "731", "Number": "1727976 ", "URL": "/notes/1727976 ", "Title": "Error in setup of Database Perf. Warehouse for local DB", "Component": "BC-DB-ORA-CCM"}, {"SoftwareComponent": "SAP_BW", "ValidFrom": "700", "ValidTo": "730", "Number": "1579462 ", "URL": "/notes/1579462 ", "Title": "Sending data from chart item without check to IGS", "Component": "BW-BEX-ET-WEB"}, {"SoftwareComponent": "SAP_BW", "ValidFrom": "700", "ValidTo": "740", "Number": "1990294 ", "URL": "/notes/1990294 ", "Title": "Myself system cannot be created in the background", "Component": "BW-WHM-DST-SRC"}, {"SoftwareComponent": "ST", "ValidFrom": "400", "ValidTo": "712", "Number": "1491227 ", "URL": "/notes/1491227 ", "Title": "Adjustments to service content update from ST 700 SP 23", "Component": "SV-SMG-SVD-SCU"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1599582 ", "URL": "/notes/1599582 ", "Title": "Plugin Status Details - Delete sp_level check for ST-A/PI", "Component": "SV-SMG-INS-CFG"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1643760 ", "URL": "/notes/1643760 ", "Title": "No target namespace when settung up LMDB", "Component": "SV-SMG-LDB"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1654623 ", "URL": "/notes/1654623 ", "Title": "Problems when copying custom templates", "Component": "SV-SMG-MON-ALR-CFG"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1710170 ", "URL": "/notes/1710170 ", "Title": "SAP Solution Manager Software Prerequisites", "Component": "SV-SMG-INS-CFG"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1712468 ", "URL": "/notes/1712468 ", "Title": "DBA: Outside db discovery support for virtual hostnames", "Component": "SV-SMG-DIA-SRV-LSC"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1718522 ", "URL": "/notes/1718522 ", "Title": "Customizing table SMCRM_CUST_APPL - General corrections", "Component": "SV-SMG-IMP"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1728717 ", "URL": "/notes/1728717 ", "Title": "Mass Activation of templates", "Component": "SV-SMG-INS-CFG"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1731879 ", "URL": "/notes/1731879 ", "Title": "Activate BI Content for INCIDENT and CHARM,and BPO DASHBOARD", "Component": "SV-SMG-INS-CFG"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1733236 ", "URL": "/notes/1733236 ", "Title": "Advance Corrections BPMon SM 7.1 ST710 delivered with SP07", "Component": "SV-SMG-MON-BPM"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1734464 ", "URL": "/notes/1734464 ", "Title": "SOLMAN_SETUP: notes to be checked are wrong", "Component": "SV-SMG-INS-CFG"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1734897 ", "URL": "/notes/1734897 ", "Title": "Upgrade SP05/06/07:Error in maintenance project or BPMon", "Component": "SV-SMG-OP"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1736368 ", "URL": "/notes/1736368 ", "Title": "Error in 'Replace Service Definitions' activity", "Component": "SV-SMG-INS-CFG"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1736425 ", "URL": "/notes/1736425 ", "Title": "Technical Scenario DUAL_STACK: MaxLen violated", "Component": "SV-SMG-LDB"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1736740 ", "URL": "/notes/1736740 ", "Title": "SOLMAN_SETUP:java user manually maintained might be in error", "Component": "SV-SMG-INS-CFG"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1737495 ", "URL": "/notes/1737495 ", "Title": "Analysis Creation for Simple Archiving Objects not working", "Component": "SV-SMG-DVM"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1738276 ", "URL": "/notes/1738276 ", "Title": "DSA: Word creation dumps caused by memory lack", "Component": "SV-SMG-SVD-SWB"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1739620 ", "URL": "/notes/1739620 ", "Title": "Update Content Step Display Fixes", "Component": "SV-SMG-MON-ALR-CFG"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1739631 ", "URL": "/notes/1739631 ", "Title": "Service delivery session information not updated accordingly", "Component": "SV-SMG-SVD"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1740217 ", "URL": "/notes/1740217 ", "Title": "Note Post Processing not enabled in solman_setup", "Component": "SV-SMG-INS-CFG"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1740375 ", "URL": "/notes/1740375 ", "Title": "Incident creation fails in Test Workbench or Help Menu", "Component": "SV-SMG-SUP"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1740466 ", "URL": "/notes/1740466 ", "Title": "ECE: old metrics are used if rule type is \"Already Rated\"", "Component": "SV-SMG-MON-ALR-CLC"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1740467 ", "URL": "/notes/1740467 ", "Title": "ECE: Event Calculation Engine Note for SP06", "Component": "SV-SMG-MON-ALR-CLC"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1740720 ", "URL": "/notes/1740720 ", "Title": "CCDB: Incorrect 'Customizing change detected' message", "Component": "SV-SMG-DIA-APP-CA"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1741080 ", "URL": "/notes/1741080 ", "Title": "no/wrong metrics collected on SMD Agent assoicated to DB", "Component": "SV-SMG-MON-ALR"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1741525 ", "URL": "/notes/1741525 ", "Title": "VAR: Multiple nodes per customer in IBase", "Component": "SV-SMG-SUP"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1742435 ", "URL": "/notes/1742435 ", "Title": "LMDB: Confirmation error when creating a product system", "Component": "SV-SMG-LDB"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1743949 ", "URL": "/notes/1743949 ", "Title": "Corrupted automatic service report sent via notification", "Component": "SV-SMG-SVD-SWB"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1745716 ", "URL": "/notes/1745716 ", "Title": "issue with EFWK RESOURCE MANAGER report scheduled twice", "Component": "SV-SMG-INS-CFG"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1745786 ", "URL": "/notes/1745786 ", "Title": "Landscape LMDB wrapper SP6 related corrections", "Component": "SV-SMG-DIA-SRV-LSC"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1746103 ", "URL": "/notes/1746103 ", "Title": "Solution Manager: Turning Configuration Step Status to Grey", "Component": "SV-SMG-DVM"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1747098 ", "URL": "/notes/1747098 ", "Title": "REFRESH_ADMIN_DATA_FROM_SUPPORT failed to create systems", "Component": "SV-SMG-SVC"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1747859 ", "URL": "/notes/1747859 ", "Title": "Create or Update Administrator from Connect Managed System", "Component": "SV-SMG-INS-CFG"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1748192 ", "URL": "/notes/1748192 ", "Title": "DBA: Validity Fallback Is Not Evaluated", "Component": "BC-DB-DB6-CCM"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1748968 ", "URL": "/notes/1748968 ", "Title": "Collective corrections for the MEA Directory SP6", "Component": "SV-SMG-MON-ALR-DIR"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1749374 ", "URL": "/notes/1749374 ", "Title": "Creation of I-Objects: Incorrect message type in appl log", "Component": "SV-SMG-SUP"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1749774 ", "URL": "/notes/1749774 ", "Title": "Request popup opens, eventhough template saved under $TMP", "Component": "SV-SMG-MON-ALR-CFG"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1749788 ", "URL": "/notes/1749788 ", "Title": "Correction - Housekeeping Settings of Data Volume Management", "Component": "SV-SMG-DVM"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1750618 ", "URL": "/notes/1750618 ", "Title": "RFC destinations created in SMSU_MANAGED_SYSTEM not delete", "Component": "SV-SMG-INS-CFG"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1750994 ", "URL": "/notes/1750994 ", "Title": "Alert/subalert name/id are missing in XML generation", "Component": "SV-SMG-SDG"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1752181 ", "URL": "/notes/1752181 ", "Title": "Grey metrics in Technical System Monitoring", "Component": "SV-SMG-MON-ALR-PRV"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1752517 ", "URL": "/notes/1752517 ", "Title": "Managed system configuration - warning in step 7 create user", "Component": "SV-SMG-INS-CFG"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1753061 ", "URL": "/notes/1753061 ", "Title": "Deactivate LMDB notifications", "Component": "SV-SMG-INS-CFG"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1753107 ", "URL": "/notes/1753107 ", "Title": "Fixing context's loss when executing an automatic activity.", "Component": "SV-SMG-INS-CFG"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1753209 ", "URL": "/notes/1753209 ", "Title": "SOLMAN_SETUP ITSM 2.2 Incident/SR Templates - wrong types.", "Component": "SV-SMG-INS-CFG"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1753587 ", "URL": "/notes/1753587 ", "Title": "\"No Scope Selected\" message although a scope is selected.", "Component": "SV-SMG-INS-CFG"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1753625 ", "URL": "/notes/1753625 ", "Title": "The Post processing section is not visible in step2 of CHARM", "Component": "SV-SMG-INS-CFG"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1753639 ", "URL": "/notes/1753639 ", "Title": "Adding the \"Scenario Operations\" button on Define scope step", "Component": "SV-SMG-INS-CFG"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1753936 ", "URL": "/notes/1753936 ", "Title": "Apply Delivery Fixes", "Component": "SV-SMG-MON-ALR-CFG"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1753985 ", "URL": "/notes/1753985 ", "Title": "Downloaded XML data for Root Cause Analysis is not correct", "Component": "SV-SMG-SDG"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1754071 ", "URL": "/notes/1754071 ", "Title": "RFC for SAP Solution Manager  is not persisted", "Component": "SV-SMG-INS-CFG"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1754275 ", "URL": "/notes/1754275 ", "Title": "Wrong message 'RFC SM_XXXCLNTnnn_BACK does not exist'", "Component": "SV-SMG-INS-CFG"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1754637 ", "URL": "/notes/1754637 ", "Title": "DVM Setup: wrong navigation link", "Component": "SV-SMG-INS-CFG"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1754672 ", "URL": "/notes/1754672 ", "Title": "Correction for DVM Template creation", "Component": "SV-SMG-DVM"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1754992 ", "URL": "/notes/1754992 ", "Title": "Create Transport Request Failed with package checks", "Component": "SV-SMG-MON-ALR-CFG"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1755049 ", "URL": "/notes/1755049 ", "Title": "Unable to create RFC with SAP router", "Component": "SV-SMG-INS-CFG"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1755218 ", "URL": "/notes/1755218 ", "Title": "Scope dependent parent step not accessible in solman_setup", "Component": "SV-SMG-INS-CFG"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1755389 ", "URL": "/notes/1755389 ", "Title": "Metrics: \"Number of Exceptions in Domain Log\" is grey", "Component": "SV-SMG-MON-ALR-PRV"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1755568 ", "URL": "/notes/1755568 ", "Title": "BPMon attached to tech. interfaces: Copying does not work", "Component": "SV-SMG-OP"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1758475 ", "URL": "/notes/1758475 ", "Title": "Alert Inbox Dumps with error TYPE NOT FOUND.", "Component": "SV-SMG-MON-ALR-CNS"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1758728 ", "URL": "/notes/1758728 ", "Title": "\"Active\" selection of product versions is removed", "Component": "SV-SMG-SYS"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1758772 ", "URL": "/notes/1758772 ", "Title": "Edit button is missing in details assignment block", "Component": "SV-SMG-SUP"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1760744 ", "URL": "/notes/1760744 ", "Title": "Add Diagnostics Relevant flag in LMDB for Solution Manager", "Component": "SV-SMG-INS-CFG"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1761039 ", "URL": "/notes/1761039 ", "Title": "Advance Corrections BPMon SM 7.1 ST710 delivered with SP08", "Component": "SV-SMG-MON-BPM"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1761913 ", "URL": "/notes/1761913 ", "Title": "Activity table update for SP6 (AGS_GS_MIGRATION)", "Component": "SV-SMG-INS-CFG"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1762087 ", "URL": "/notes/1762087 ", "Title": "SOLMAN_SETUP: message 'invalid date' when creating users", "Component": "SV-SMG-INS-CFG"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1763066 ", "URL": "/notes/1763066 ", "Title": "Mismatch in number of alerts in alert inbox tables", "Component": "SV-SMG-MON-ALR-CNS"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1764070 ", "URL": "/notes/1764070 ", "Title": "Changing status in 'Connect managed system'", "Component": "SV-SMG-INS-CFG"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1765074 ", "URL": "/notes/1765074 ", "Title": "SOLMAN_SETUP: status of step users not updated", "Component": "SV-SMG-INS-CFG"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1765809 ", "URL": "/notes/1765809 ", "Title": "SOLMAN_SETUP: status of java user step is wrongly warning", "Component": "SV-SMG-INS-CFG"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1766608 ", "URL": "/notes/1766608 ", "Title": "Activity status reset when using activity \"navigation\"", "Component": "SV-SMG-INS-CFG"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1768544 ", "URL": "/notes/1768544 ", "Title": "Corrections for EEM 7.1 SP06 and SP07", "Component": "SV-SMG-MON-EEM"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1769053 ", "URL": "/notes/1769053 ", "Title": "Instance Log Path not displayed for <PERSON>a", "Component": "SV-SMG-DIA-SRV-SET"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1769570 ", "URL": "/notes/1769570 ", "Title": "Function Moduel SMD_DATA_LOADER101 not found", "Component": "SV-SMG-DVM"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1770638 ", "URL": "/notes/1770638 ", "Title": "Working with Templates in different languages", "Component": "SV-SMG-MON-ALR-CFG"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1771470 ", "URL": "/notes/1771470 ", "Title": "SOLMAN_SETUP: impossible to update user roles other than Z", "Component": "SV-SMG-INS-CFG"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1774271 ", "URL": "/notes/1774271 ", "Title": "GPA Fix incomplete transport request for Guided Procedure", "Component": "SV-SMG-GPF-AUT"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1774508 ", "URL": "/notes/1774508 ", "Title": "DVM: Simple AObj Or Business Object Footprint issue", "Component": "SV-SMG-DVM"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1775564 ", "URL": "/notes/1775564 ", "Title": "Issues with BPMon Mirgration to SM 7.1 SP 5 or higher", "Component": "SV-SMG-MON-BPM"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1775643 ", "URL": "/notes/1775643 ", "Title": "AI_SDK_SP_GENERATE_BP_V2: diverse corrections III", "Component": "SV-SMG-SUP"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1775883 ", "URL": "/notes/1775883 ", "Title": "GSS: unexpected dump when using webdynpro logon popup in GSS", "Component": "SV-SMG-SVD-GSS"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1776209 ", "URL": "/notes/1776209 ", "Title": "Reintroducing \"Configure Gateway\" Step in System Prep.", "Component": "SV-SMG-INS-CFG"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1777302 ", "URL": "/notes/1777302 ", "Title": "Wrong SOURCERDCDEST of extractor AGS_DVM_TOPOBJ_EXTRACT", "Component": "SV-SMG-DVM"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1777767 ", "URL": "/notes/1777767 ", "Title": "Incomplete display of messages in component view", "Component": "SV-SMG-IMP-BIM"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1778532 ", "URL": "/notes/1778532 ", "Title": "SOLMAN_SETUP: change of role name for user SM_BW", "Component": "SV-SMG-INS-CFG"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1779435 ", "URL": "/notes/1779435 ", "Title": "System status details not expandable", "Component": "SV-SMG-INS-CFG"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1779513 ", "URL": "/notes/1779513 ", "Title": "Solman_setup:generic storage notif remove key/value from log", "Component": "SV-SMG-INS-CFG"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1779712 ", "URL": "/notes/1779712 ", "Title": "SOLMAN_SETUP : BW Content activation fix", "Component": "SV-SMG-INS-CFG"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1780089 ", "URL": "/notes/1780089 ", "Title": "No available RFC for system of DVM template configuraion", "Component": "SV-SMG-DVM"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1780480 ", "URL": "/notes/1780480 ", "Title": "Issues of Job documentation importing", "Component": "SV-SMG-PSM"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1780509 ", "URL": "/notes/1780509 ", "Title": "Select Options for Metric parameter values is not working", "Component": "SV-SMG-MON-ALR-CFG"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1782151 ", "URL": "/notes/1782151 ", "Title": "SOLMAN_SETUP : Setup BW is finished with warnings", "Component": "SV-SMG-INS-CFG"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1782250 ", "URL": "/notes/1782250 ", "Title": "Solman_setup: Problem with RFC for SAP Solution Manager", "Component": "SV-SMG-INS-CFG"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1783371 ", "URL": "/notes/1783371 ", "Title": "MOpz: System landscape data incomplete in Customer Profile", "Component": "SV-SMG-MAI"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1783688 ", "URL": "/notes/1783688 ", "Title": "Propagation still happens though it is switched off", "Component": "SV-SMG-MON-ALR-CFG"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1783887 ", "URL": "/notes/1783887 ", "Title": "Solman_setup: wrong client in the dropdown lists for RFC", "Component": "SV-SMG-INS-CFG"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1784446 ", "URL": "/notes/1784446 ", "Title": "Custom Metric Variants were not copied during copy template", "Component": "SV-SMG-MON-ALR-CFG"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1784629 ", "URL": "/notes/1784629 ", "Title": "Overview status correction", "Component": "SV-SMG-INS-CFG"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1784936 ", "URL": "/notes/1784936 ", "Title": "Transport Custom Metric Variants from one system to other system", "Component": "SV-SMG-MON-ALR-CFG"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1784991 ", "URL": "/notes/1784991 ", "Title": "Post-processing of SOLMAN_SETUP re-execute", "Component": "SV-SMG-SER-ESR"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1786016 ", "URL": "/notes/1786016 ", "Title": "Multiple specification of Managed Object tag dump fix", "Component": "SV-SMG-MON-ALR-CNS"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1786117 ", "URL": "/notes/1786117 ", "Title": "Solman_Setup: System Alias not availble after running Setup", "Component": "SV-SMG-INS-CFG"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1786378 ", "URL": "/notes/1786378 ", "Title": "LMDB-MOPZ-API: Filter for customer product data", "Component": "SV-SMG-LDB"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1787220 ", "URL": "/notes/1787220 ", "Title": "Gap between template configuration and set scheduler table", "Component": "SV-SMG-INS-CFG"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1787921 ", "URL": "/notes/1787921 ", "Title": "Moving up \"Activate BW Source System\" activity in Basic Conf", "Component": "SV-SMG-INS-CFG"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1788217 ", "URL": "/notes/1788217 ", "Title": "JSM: job step print&archive parameters related issue", "Component": "SV-SMG-PSM"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1789223 ", "URL": "/notes/1789223 ", "Title": "Solman_setup : Delete manual activities", "Component": "SV-SMG-INS-CFG"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1791436 ", "URL": "/notes/1791436 ", "Title": "DVM GSS: Incorrect calculation of Content Coverage", "Component": "SV-SMG-DVM"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1791622 ", "URL": "/notes/1791622 ", "Title": "VAR: Send to SAP - unknown installation number - message not permitted", "Component": "SV-SMG-SUP"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1796193 ", "URL": "/notes/1796193 ", "Title": "New alert creation on manual notification/incident creation", "Component": "SV-SMG-MON-ALR-CNS"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1799283 ", "URL": "/notes/1799283 ", "Title": "Erroneous date format in Self-Diagnosis", "Component": "SV-SMG-SDG"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1800683 ", "URL": "/notes/1800683 ", "Title": "Possible loss of data in Compare and Adjust", "Component": "SV-SMG-IMP-BIM"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1801808 ", "URL": "/notes/1801808 ", "Title": "Fields in SERVICE_H structure deleted after update from SAP", "Component": "SV-SMG-SUP"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1801878 ", "URL": "/notes/1801878 ", "Title": "SOLMAN_SETUP: warning wrongly raised for managed admin user", "Component": "SV-SMG-INS-CFG"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1803874 ", "URL": "/notes/1803874 ", "Title": "LMDB/IBase: Duplicate clients for dual stack systems", "Component": "SV-SMG-SUP"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1804373 ", "URL": "/notes/1804373 ", "Title": "Solman_setup : The BW content is not active", "Component": "SV-SMG-INS-CFG"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1805702 ", "URL": "/notes/1805702 ", "Title": "solman_setup:update flag checked but no step needs update", "Component": "SV-SMG-INS-CFG"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1808259 ", "URL": "/notes/1808259 ", "Title": "Wrong information text for column title in Web DSA UI", "Component": "SV-SMG-SVD-GSS"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1808294 ", "URL": "/notes/1808294 ", "Title": "Incorrect automatic assignment settings in problem", "Component": "SV-SMG-SUP"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1809705 ", "URL": "/notes/1809705 ", "Title": "ISV: SAP status not set correctly after sending to customer", "Component": "SV-SMG-SUP"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1810060 ", "URL": "/notes/1810060 ", "Title": "DVM:Multiple Save Not Working in Analysis Admin.", "Component": "SV-SMG-DVM"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1812046 ", "URL": "/notes/1812046 ", "Title": "SOLMAN_SETUP: issue in role update on systems linked to CUA", "Component": "SV-SMG-INS-CFG"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1813914 ", "URL": "/notes/1813914 ", "Title": "CCDB: ConfigStore bo40.dump_all_xml - SYSTEM_NO_ROLL", "Component": "SV-SMG-DIA-APP-CA"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1814977 ", "URL": "/notes/1814977 ", "Title": "Missing Contact Person Role for Template Users.", "Component": "SV-SMG-INS-CFG"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1817266 ", "URL": "/notes/1817266 ", "Title": "First row of functions table in Self-Diagnosis is not shown", "Component": "SV-SMG-SDG"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1817955 ", "URL": "/notes/1817955 ", "Title": "LMDB/IBase: Duplicated objects", "Component": "SV-SMG-SUP"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1818156 ", "URL": "/notes/1818156 ", "Title": "Guided Procedure Authoring- fix Request &TRKORR& is unknown", "Component": "SV-SMG-GPF-AUT"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1819525 ", "URL": "/notes/1819525 ", "Title": "Self-Diagnosis job fails due to error in alert 234", "Component": "SV-SMG-SDG"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1820285 ", "URL": "/notes/1820285 ", "Title": "Comparison against successor nodes shows wrong results", "Component": "SV-SMG-IMP-BIM"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1820291 ", "URL": "/notes/1820291 ", "Title": "Project Documentation tab flagged as changed in Template", "Component": "SV-SMG-IMP-BIM"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1823420 ", "URL": "/notes/1823420 ", "Title": "CCDB: Fatal Error - CX_COMPONENT_VERSION_NOT_FOUND", "Component": "SV-SMG-DIA-APP-CA"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1824471 ", "URL": "/notes/1824471 ", "Title": "MAI: Metric \"DBACockpit Connection\" is gray when DB is down", "Component": "SV-SMG-MON-ALR"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1826109 ", "URL": "/notes/1826109 ", "Title": "ITSM: Standard CRM authorization checks skipped for search", "Component": "SV-SMG-SUP"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1826982 ", "URL": "/notes/1826982 ", "Title": "IBase: Not authorized for initial object", "Component": "SV-SMG-SUP"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1827161 ", "URL": "/notes/1827161 ", "Title": "Duplicate client component", "Component": "SV-SMG-SUP"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1833412 ", "URL": "/notes/1833412 ", "Title": "RFCs: BACK RFC destination removed from SMSY tables", "Component": "SV-SMG-INS-CFG"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1833865 ", "URL": "/notes/1833865 ", "Title": "Avoid Obsolete Recipient Lists/Recipients during transport", "Component": "SV-SMG-MON-ALR-CFG"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1834983 ", "URL": "/notes/1834983 ", "Title": "SMSY: Unexpected overwriting of system descriptions", "Component": "SV-SMG-LDB"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1836214 ", "URL": "/notes/1836214 ", "Title": "SysRec: short dumps in managed systems with user SOLMAN_BTC", "Component": "SV-SMG-SR"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1837889 ", "URL": "/notes/1837889 ", "Title": "VAR scenario: Unjustified deactivation of S users", "Component": "SV-SMG-SUP"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1844394 ", "URL": "/notes/1844394 ", "Title": "Contact person cannot be entered via BP id directly", "Component": "SV-SMG-SUP"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1847398 ", "URL": "/notes/1847398 ", "Title": "SOLMAN_SETUP: issue with the temporary connection to the BW", "Component": "SV-SMG-INS-CFG"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1849006 ", "URL": "/notes/1849006 ", "Title": "All traffic lights grey in Solution Manager Configuration", "Component": "SV-SMG-LDB"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1849566 ", "URL": "/notes/1849566 ", "Title": "Users without authorization see the installations", "Component": "SV-SMG-SVC"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1851723 ", "URL": "/notes/1851723 ", "Title": "REFRESH_ADMIN_DATA_FROM_SUPPORT no licence data generation", "Component": "SV-SMG-SVC"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1854317 ", "URL": "/notes/1854317 ", "Title": "Update needed flag remains in managed system overview", "Component": "SV-SMG-INS-CFG"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1855272 ", "URL": "/notes/1855272 ", "Title": "SOLMAN_SETUP: admin user shouldn't modify S* profiles", "Component": "SV-SMG-INS-CFG-MNG"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1856234 ", "URL": "/notes/1856234 ", "Title": "Missing sort string in Ibase text components", "Component": "SV-SMG-SUP"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1863515 ", "URL": "/notes/1863515 ", "Title": "HANA Soft. Comp. Versions missing in the Landscape API", "Component": "SV-SMG-LDB"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1871793 ", "URL": "/notes/1871793 ", "Title": "HANADB should not create technical system Objects", "Component": "SV-SMG-SUP"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1875419 ", "URL": "/notes/1875419 ", "Title": "ST710:AI_CRM_IM_UPDATE_FROM_SAP has a long runtime", "Component": "SV-SMG-SUP"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1875434 ", "URL": "/notes/1875434 ", "Title": "Dump in Self-Diagnosis due to Resource Failure", "Component": "SV-SMG-SDG"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1878887 ", "URL": "/notes/1878887 ", "Title": "Missing CVs in cl_diagls_util=>get_all_scv_with_lsc_ids_f_ts", "Component": "SV-SMG-LDB"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1886344 ", "URL": "/notes/1886344 ", "Title": "DVM: 'CMC_RFC_INTERFACE not found' (ST-A/PI 01Q SP1)", "Component": "SV-SMG-DVM"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1886549 ", "URL": "/notes/1886549 ", "Title": "Flag TEMP_INACTIVE in table SMSY_SYSTEM_SAP wrong", "Component": "SV-SMG-LDB"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1887132 ", "URL": "/notes/1887132 ", "Title": "MAI: Work mode managment does not work", "Component": "SV-SMG-MON-ALR"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1892092 ", "URL": "/notes/1892092 ", "Title": "SOLMAN_SETUP: issue with the connection to BW", "Component": "SV-SMG-INS-CFG"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1900402 ", "URL": "/notes/1900402 ", "Title": "ULA GUID upper/lower case", "Component": "SV-SMG-SUP"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1913332 ", "URL": "/notes/1913332 ", "Title": "Check Scope Assignment Block before allowing to delete Ibase", "Component": "SV-SMG-SUP"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1914296 ", "URL": "/notes/1914296 ", "Title": "Checking installed base consistency before creating components", "Component": "SV-SMG-SUP"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1923595 ", "URL": "/notes/1923595 ", "Title": "Ibase Sortf report should ignore already used longsids", "Component": "SV-SMG-SUP"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1925760 ", "URL": "/notes/1925760 ", "Title": "Multiple IBase created for SOL_MAN_DATA_REP", "Component": "SV-SMG-SUP"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1935934 ", "URL": "/notes/1935934 ", "Title": "Various problems when creating IBase components and objects", "Component": "SV-SMG-SUP"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1979207 ", "URL": "/notes/1979207 ", "Title": "Incorrect size of DVM workcenter caused by different timezone in managed system", "Component": "SV-SMG-DVM"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1980254 ", "URL": "/notes/1980254 ", "Title": "Relevant roles aren't updated in a CUA child system via SOLMAN_SETUP", "Component": "SV-SMG-INS-CFG"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "1990166 ", "URL": "/notes/1990166 ", "Title": "Deletion of RFC destinations", "Component": "SV-SMG-SYS"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "2006375 ", "URL": "/notes/2006375 ", "Title": "AI_CRM_IM_UPDATE_FROM_SAP: Messages are created multiple times", "Component": "SV-SMG-SUP"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "2018727 ", "URL": "/notes/2018727 ", "Title": "Improve the data quality of Engagement Report", "Component": "SV-SMG-SER-RFW"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "2020863 ", "URL": "/notes/2020863 ", "Title": "Service Notification doesn't send rating", "Component": "SV-SMG-OP"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "2052138 ", "URL": "/notes/2052138 ", "Title": "RFC Destination to SAP Solution Manager(BACK RFC) gets created with wrong System Number", "Component": "SV-SMG-SYS"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "710", "Number": "2056893 ", "URL": "/notes/2056893 ", "Title": "SAP Roles Not Copied to the Customer Namespace and Not Assigned To Users", "Component": "SV-SMG-INS-CFG"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "712", "Number": "1881857 ", "URL": "/notes/1881857 ", "Title": "ST710: AI_CRM_IM_UPDATE_FROM_SAP unnecessary transfers", "Component": "SV-SMG-SUP"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "712", "Number": "1887558 ", "URL": "/notes/1887558 ", "Title": "Usage data collector for QGM", "Component": "SV-SMG"}, {"SoftwareComponent": "ST", "ValidFrom": "710", "ValidTo": "712", "Number": "2051461 ", "URL": "/notes/2051461 ", "Title": "User gets locked in the step of RFC creation Or DESTINATION_NOT_OPEN exception", "Component": "SV-SMG-INS-CFG-MNG"}, {"SoftwareComponent": "ST-BCO", "ValidFrom": "710", "ValidTo": "710", "Number": "1737668 ", "URL": "/notes/1737668 ", "Title": "Data not re-organized after upgrade", "Component": "SV-SMG-MON-REP"}, {"SoftwareComponent": "ST-BCO", "ValidFrom": "710", "ValidTo": "710", "Number": "1745114 ", "URL": "/notes/1745114 ", "Title": "DBA: Dump in CL_DBA_DBH_CUBE_DATA", "Component": "SV-SMG"}, {"SoftwareComponent": "ST-BCO", "ValidFrom": "710", "ValidTo": "710", "Number": "1745942 ", "URL": "/notes/1745942 ", "Title": "DPW: Cube Aggregation Memory Usage", "Component": "SV-SMG"}, {"SoftwareComponent": "ST-BCO", "ValidFrom": "710", "ValidTo": "710", "Number": "1749795 ", "URL": "/notes/1749795 ", "Title": "DPW: Housekeeping does not finish", "Component": "BC-DB-DB6-CCM"}, {"SoftwareComponent": "ST-BCO", "ValidFrom": "710", "ValidTo": "710", "Number": "1762967 ", "URL": "/notes/1762967 ", "Title": "DPW: Overlapping records in DBH_CUBE_DATA", "Component": "SV-SMG-MON-REP"}, {"SoftwareComponent": "ST-BCO", "ValidFrom": "710", "ValidTo": "710", "Number": "1768764 ", "URL": "/notes/1768764 ", "Title": "TWB reporting displays incorrect test case status counters", "Component": "SV-SMG-TWB-REP"}, {"SoftwareComponent": "ST-BCO", "ValidFrom": "710", "ValidTo": "710", "Number": "1777317 ", "URL": "/notes/1777317 ", "Title": "DPW: No aggregation of daily values to monthly values", "Component": "SV-SMG-DIA"}, {"SoftwareComponent": "ST-BCO", "ValidFrom": "710", "ValidTo": "710", "Number": "1778300 ", "URL": "/notes/1778300 ", "Title": "DPW: Short dump during data deletion", "Component": "SV-SMG-DIA"}, {"SoftwareComponent": "ST-BCO", "ValidFrom": "710", "ValidTo": "710", "Number": "1783860 ", "URL": "/notes/1783860 ", "Title": "Wrong calendard date in Extractor AGS_DVM_TOPOBJ_EXTRACT", "Component": "SV-SMG-DVM"}, {"SoftwareComponent": "ST-BCO", "ValidFrom": "710", "ValidTo": "710", "Number": "1788486 ", "URL": "/notes/1788486 ", "Title": "InfoProvider Settings not setup correctly", "Component": "SV-SMG-DVM"}, {"SoftwareComponent": "ST-BCO", "ValidFrom": "710", "ValidTo": "710", "Number": "1802520 ", "URL": "/notes/1802520 ", "Title": "DPW: <PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON> in Data Loader", "Component": "BC-DB-DB6-CCM"}, {"SoftwareComponent": "ST-PI", "ValidFrom": "2008_1_46C", "ValidTo": "2008_1_710", "Number": "1719016 ", "URL": "/notes/1719016 ", "Title": "SOLMAN_SETUP: obsolete roles still added to users", "Component": "SV-SMG-INS-CFG"}, {"SoftwareComponent": "ST-PI", "ValidFrom": "2008_1_620", "ValidTo": "2008_1_710", "Number": "1559499 ", "URL": "/notes/1559499 ", "Title": "DPC data providers as of ST-PI 2008_1_XX SP4", "Component": "SV-SMG-MON-SYS"}, {"SoftwareComponent": "WEBCUIF", "ValidFrom": "701", "ValidTo": "701", "Number": "1712091 ", "URL": "/notes/1712091 ", "Title": "CRM WebUI scrolls top on roundtrips", "Component": "CRM-FRW-UI"}]}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}