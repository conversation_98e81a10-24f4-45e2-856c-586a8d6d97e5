{"Request": {"Number": "919840", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 442, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000016050442017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000919840?language=E&token=D62525BFD96AEF20A780E05BF787E50B"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000919840", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000919840/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "919840"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 2}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Performance"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "08.04.2009"}, "SAPComponentKey": {"_label": "Component", "value": "SCM-APO-SPP-CPD"}, "SAPComponentKeyText": {"_label": "Component", "value": "Historical Data Capture and Maintenance"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Supply Chain Management", "value": "SCM", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'SCM*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Advanced Planning and Optimization", "value": "SCM-APO", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'SCM-APO*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Service Parts Planning", "value": "SCM-APO-SPP", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'SCM-APO-SPP*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Historical Data Capture and Maintenance", "value": "SCM-APO-SPP-CPD", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'SCM-APO-SPP-CPD*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "919840 - Performance for creation/maintenance of demand history"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>This note contains recommendations for improving performance during creation and maintenance of demand history.</p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>Demand history, capture demand, manage demand, performance, service parts planning (SPP)</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p></p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>The following approaches can improve performance:</p> <UL><LI>Data access using BI aggregates</LI></UL> <UL><LI>Compression of the InfoCube 9ADEMAND</LI></UL> <UL><LI>Compression of the data from 9ADEMCRT/9ARAWCRT into 9ADEMAND/9ARAWDAT</LI></UL> <UL><LI>Archiving or deleting the demand history data</LI></UL> <UL><LI>Performance optimization of access to the data realignment steps</LI></UL> <p></p> <OL>1. Data access using BI aggregates for the InfoCube 9ADEMAND</OL> <OL><OL>a) The following aggregates are delivered for the 9ADEMAND InfoCube:</OL></OL> <UL><UL><LI>Demand history in the month</LI></UL></UL> <UL><UL><LI>Demand history in the week</LI></UL></UL> <UL><UL><LI>Demand history in the posting period</LI></UL></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;You must transfer and activate these aggregates according to the global forecast periodicity (month or fiscal year, for example). Depending on the specific requirements, in InfoCube maintenance for the 9ADEMAND InfoCube, you must configure how data packages are to be rolled up in the aggregates. <OL><OL>b) Refer to the section about efficiently loading data into aggregates in the following online document:<br />http://help.sap.com/saphelp_bw33/helpdata/de/ad/6b023b6069d22ee10000000a11402f/frameset.htm</OL></OL> <OL><OL>c) Refer to the following SAP BI notes in relation to creating roll ups and aggregates:</OL></OL> <UL><UL><LI>Note 484536: Filling aggregates of large InfoCubes</LI></UL></UL> <UL><UL><LI>Note 582529: Rollup of aggregates &amp; indices (new as of BW 3.0B Support Package 9)</LI></UL></UL> <OL>2. Compression of the InfoCube 9ADEMAND</OL> <OL><OL>a) For reasons of performance and memory space, you should compress a request as soon as you determine that it is correct and is no longer to be removed from the InfoCube. Use the InfoCube compression function and compress the InfoCube 9ADEMAND regularly.</OL></OL> <OL><OL>b) Refer to the section on compressing InfoCubes in the following online document:<br />http://help.sap.com/saphelp_bw33/helpdata/de/ad/6b023b6069d22ee10000000a11402f/frameset.htm</OL></OL> <OL><OL>c) Refer to the following SAP BI notes in relation to compressing InfoCubes:</OL></OL> <UL><UL><LI>Note 375132: Performance optimization for InfoCube condensation</LI></UL></UL> <UL><UL><LI>Note 583202: Realignment run and condensing</LI></UL></UL> <OL>3. Compressing data from 9ADEMCRT/9ARAWCRT into 9ADEMAND/9ARAWDAT</OL> <OL><OL>a) You can keep the data volume in the InfoProvider 9ADEMCRT small by uploading the data of 9ADEMCRT into 9ADEMAND and then deleting the data from 9ADEMCRT. When you do this, you should use the generated InfoSource 89ADEMCRT. Create corresponding update rules between 89ADEMCRT and 9ADEMAND. When you do this, in the update rules you must assign source InfoObjects to the target InfoObjects with the same type.</OL></OL> <OL><OL>b) To compress the data from 9ARAWCRT into 9ARAWDAT, use Note 1322340. Do not upload the data in 9ARAWCRT directly to 9ARAWDAT, since this may cause data inconsistency.</OL></OL> <OL>4. Archiving or deleting the demand history data</OL> <OL><OL>a) Keep the data volumes in the InfoProviders 9ADEMAND, 9ARAWDAT, and so on, as small as possible. The forecast service, for example, uses only the demand history data from the past 5 years. Therefore, we recommend that you delete or archive data that is older than 5 years from the InfoProviders.</OL></OL> <OL><OL>b) Archive or delete data that is no longer required from 9ADEMAND and 9ARAWDAT. When you do this, you should note the following:</OL></OL> <UL><UL><LI>The consistency of the demand history data in 9ADEMMUL (9ADEMAND and 9ADEMCRT) and 9ARAWMUL (9ARAWDAT and 9ARAWCRT) must be ensured.</LI></UL></UL> <UL><UL><LI>Data realignment steps in relation to the data to be archived or deleted in 9ARAWMUL should be archived or deleted accordingly (see below).</LI></UL></UL> <OL>5. Performance optimization of access to the data realignment steps</OL> <OL><OL>a) Archive or delete data realignment steps that are no longer required. A data realignment step (for example, product A is replaced by product B) is no longer required if and only if the source product is no longer relevant for planning. This means that no more orders are loaded for the source product by capture demand AND no more data for the source product exists in 9ARAWMUL (because it has already been archived, for example).<br />The data realignment steps are saved in two database tables: You can determine the step table (/1APO/*) using the view /SAPAPO/V_RLGPRF. The database table with the administrative data for a step is /SAPAPO/TSRLGADM. You can define a view for both database tables and, for example, extract the data realignment steps that are no longer required into a corresponding InfoCube using BW tools.<br />The report /SAPAPO/PDEM_RLG_STEPS_DELETE is provided to delete the data realignment steps.</OL></OL> <OL><OL>b) Partition the step table (/1APO/*, database name can be determined using the view /SAPAPO/V_RLGPRF) according to the field for the source product (the default field name is F_9APRODUCT). We recommend hash partitioning. For more information, see Note 742243.</OL></OL> <OL><OL>c) For the step table, create a (non-unique) index that includes the fields source product (default field name is F_9APRODUCT) and VALIDITY_DATE.</OL></OL></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "D040969"}, {"Key": "Processor                                                                                           ", "Value": "D040969"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000919840/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000919840/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000919840/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000919840/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000919840/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000919840/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000919840/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000919840/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000919840/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "742243", "RefComponent": "BC-DB-ORA", "RefTitle": "General table partitioning as of SAP_BASIS 46C", "RefUrl": "/notes/742243"}, {"RefNumber": "583202", "RefComponent": "BW-BEX-OT-AGGR", "RefTitle": "Realignment run and condensing", "RefUrl": "/notes/583202"}, {"RefNumber": "582529", "RefComponent": "BW-BEX-OT-AGGR", "RefTitle": "Rollup of aggregates & indices", "RefUrl": "/notes/582529"}, {"RefNumber": "567747", "RefComponent": "BW", "RefTitle": "Composite note BW 3.x performance: Extraction & loading", "RefUrl": "/notes/567747"}, {"RefNumber": "484536", "RefComponent": "BW-BEX-OT-AGGR", "RefTitle": "Filling aggregates of large InfoCubes", "RefUrl": "/notes/484536"}, {"RefNumber": "375132", "RefComponent": "BW-BEX-OT-DBIF-CON", "RefTitle": "Performance optimization for InfoCube condensation", "RefUrl": "/notes/375132"}, {"RefNumber": "1270069", "RefComponent": "SCM-APO-SPP", "RefTitle": "SPP - Runtime and Hardware Performance", "RefUrl": "/notes/1270069"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "3019572", "RefComponent": "SCM-APO-SPP-CPD", "RefTitle": "Delete Negative Demand History Manually From 9ADEMAND or 9ADEMCRT", "RefUrl": "/notes/3019572 "}, {"RefNumber": "742243", "RefComponent": "BC-DB-ORA", "RefTitle": "General table partitioning as of SAP_BASIS 46C", "RefUrl": "/notes/742243 "}, {"RefNumber": "1270069", "RefComponent": "SCM-APO-SPP", "RefTitle": "SPP - Runtime and Hardware Performance", "RefUrl": "/notes/1270069 "}, {"RefNumber": "375132", "RefComponent": "BW-BEX-OT-DBIF-CON", "RefTitle": "Performance optimization for InfoCube condensation", "RefUrl": "/notes/375132 "}, {"RefNumber": "484536", "RefComponent": "BW-BEX-OT-AGGR", "RefTitle": "Filling aggregates of large InfoCubes", "RefUrl": "/notes/484536 "}, {"RefNumber": "567747", "RefComponent": "BW", "RefTitle": "Composite note BW 3.x performance: Extraction & loading", "RefUrl": "/notes/567747 "}, {"RefNumber": "583202", "RefComponent": "BW-BEX-OT-AGGR", "RefTitle": "Realignment run and condensing", "RefUrl": "/notes/583202 "}, {"RefNumber": "582529", "RefComponent": "BW-BEX-OT-AGGR", "RefTitle": "Rollup of aggregates & indices", "RefUrl": "/notes/582529 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SCM", "From": "500", "To": "500", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}