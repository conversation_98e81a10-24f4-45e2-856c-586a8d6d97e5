{"Request": {"Number": "600229", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 2159, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "D", "_masterLanguage": "D", "_loadedLanguage": "D", "_machineTranslationLanguage": "E", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Acknowledge", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "If the quality of the machine translation is not sufficient for your purposes, click here to request a revised version.", "_type": "EVENT", "_visible": true}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000015378922017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000600229?language=E&token=7B8BFE8F28E5C8AB4F63CF7B0BA82A21"}, "ShareByEmail": {"_label": "Share via Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000600229", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000600229/D/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "600229"}, "Type": {"_label": "Type", "value": "SAP-Hinweis"}, "Version": {"_label": "Version", "value": 9}, "Recency": {"_label": "Currentness", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "German"}, "MasterLanguage": {"_label": "Original Language", "value": "German"}, "Category": {"_label": "Category", "value": "Installation Info"}, "Priority": {"_label": "Priority", "value": "Correction with low priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "29.04.2003"}, "SAPComponentKey": {"_label": "Component", "value": "PY-DE-PS-ZV"}, "SAPComponentKeyText": {"_label": "Component", "value": "Supplementary Pension"}, "SAPComponentPath": [{"_label": "Services &amp; Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs &amp; Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Payroll", "value": "PY", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'PY*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Germany", "value": "PY-DE", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'PY-DE*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Public Sector", "value": "PY-DE-PS", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'PY-DE-PS*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Supplementary Pension", "value": "PY-DE-PS-ZV", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'PY-DE-PS-ZV*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "600229 - SP Advance Transport: DATUEV-ZVE, SR, Guaranteed Net Amount,..."}, "LongText": {"_label": "Description", "value": "<div id=\"DISCLAIMER\" class=\"sapMMsgStrip sapMMsgStripWarning sapUiSmallMarginTop sapUiMediumMarginBottom\"><div class=\"sapMMsgStripMessage\"><span dir=\"auto\" class=\"sapMText sapUiSelectable sapMTextMaxWidth\" style=\"text-align: left;\"><strong>ATTENTION!</strong> This is a machine-translated document that has not been reviewed for accuracy. To provide feedback regarding the machine translation, please <a style=\"color:var(--sapLinkColor);\" href=\"https://sapinsights.eu.qualtrics.com/jfe/form/SV_6nI2C1MOwEviVQG?NoteNumber=600229&TargetLanguage=EN&Component=PY-DE-PS-ZV&SourceLanguage=DE&Priority=04\" target=\"_blank\">click here</a>.<br/>You can access the original document via the following link: <a style=\"color:var(--sapLinkColor);\" href=\"/notes/600229/D\" target=\"_blank\">/notes/600229/D</a>.</span></div></div><div><div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>SAP Notes 591261 (new DATUEV-ZVE), 594400 (SP increase for semiretirement), and 594394 (guaranteed net amount) promise an advance transport in the first week of May with the missing functions.<br />The advance transport that affects only the notification program leads to errors in the payroll. Therefore, SAP has decided to provide a larger advance transport that contains all functions (see the attached SAP Notes).<br /><br /></p> <b>This SAP Note is no longer valid due to the available Support Packages.<br /><br /></b><br /><h3 data-toc-skip class=\"section\" id=\"Weitere Begriffe\">Other Terms</h3><p>SPI, SPF, VBL, semiretirement, RPCZVMD0, net projection, net iteration</p><h3 data-toc-skip class=\"section\" id=\"Ursache und Voraussetzungen\">Reason and Prerequisites</h3><p><br /></p> <b>This SAP Note is no longer valid due to available Support Packages.</b><br /> <b>All SAP Notes related to this SAP Note have already been</b><br /> <b>Support Package.<br /><br /></b><br /> <p>The following Support Packages are prerequisites for the advance transport:</p> <UL><LI>SP 67 for Release 4.6C</LI></UL> <UL><LI>SP 76 for Release 4.6B</LI></UL> <UL><LI>SP 94 for Release 4.5B</LI></UL> <UL><LI>SP B0 for Rel. 4.0B<br /></LI></UL> <p>For information about importing advance transports, see Note 13719. Note that when you import advance transports, a certain residual risk cannot be ruled out.</p><h3 data-toc-skip class=\"section\" id=\"L&ouml;sung\">Solution</h3><p>The preliminary transport contains four files, two of which belong together (R and K files). One pair is a system transport, the other is a customizing transport. First import the system transport and then the Customizing transport into client 000. Afterwards, a comparison with your client is required.<br />You can find out which files contain which request in the attached README file.<br />Refer to SAP Note 480180 for information about downloading the advance transport.<br /></p> <b>After you import the subsequent Support Package that is available in the middle of March (e.g. B. SP 68 for Release 4.6C), you should import the advance transport again because SAP Notes 594400, 594394, and 599087 are not yet available in this Support Package.<br /></b><br /> <b>The following Support Packages are now available:</b><br /> <UL><LI>SP 69 for Rel. 4.6C: as of March 26, 2003</LI></UL> <UL><LI>SP 78 for Rel. 4.6B: Since 03/31/2003</LI></UL> <UL><LI>SP 96 for Rel. 4.5B: Since 03/31/2003</LI></UL> <UL><LI>SP B2 for Release 4.0B: expected as of April 3, 2003</LI></UL></div></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Owner                                                                                    ", "Value": "<PERSON> (D038330)"}, {"Key": "Processor                                                                                          ", "Value": "<PERSON><PERSON> (D031307)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000600229/D"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "MIME Type", "URL": "URL"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document references", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "URL"}, "Items": [{"RefNumber": "600044", "RefComponent": "PY-DE-PS-ZV", "RefTitle": "Conversion of Contribution Taxation: ER Table -> T5D5H", "RefUrl": "/notes/600044"}, {"RefNumber": "599889", "RefComponent": "PY-DE-PS-ZV", "RefTitle": "Fictitious calculations (SR, SPS, ZMuSch) too low", "RefUrl": "/notes/599889"}, {"RefNumber": "599201", "RefComponent": "PY-DE-PS-ZV", "RefTitle": "Employer costs (wage type /700) too high", "RefUrl": "/notes/599201"}, {"RefNumber": "599087", "RefComponent": "PY-DE-PS-ZV", "RefTitle": "New DATUEV-ZVE: Separate Notification Special Remuneration SR", "RefUrl": "/notes/599087"}, {"RefNumber": "598147", "RefComponent": "PY-DE-PS-ZV", "RefTitle": "Adjustment error for Release 4.5B for SAP Note 586410", "RefUrl": "/notes/598147"}, {"RefNumber": "597432", "RefComponent": "PY-DE-PS-ZV", "RefTitle": "When you search in the log, the program crashes.", "RefUrl": "/notes/597432"}, {"RefNumber": "597393", "RefComponent": "PY-DE-PS-ZV", "RefTitle": "Unnecessary error message when reading feature DOZSM", "RefUrl": "/notes/597393"}, {"RefNumber": "595404", "RefComponent": "PY-DE-PS-ZV", "RefTitle": "Figure: Additional Contributions (for Remuneration Using BAT-I)", "RefUrl": "/notes/595404"}, {"RefNumber": "594400", "RefComponent": "PY-DE-PS-ZV", "RefTitle": "Changes regarding SP Calculation During SR", "RefUrl": "/notes/594400"}, {"RefNumber": "594394", "RefComponent": "PY-DE-PS-ZV", "RefTitle": "Guaranteed Net Amounts, AVmG, and New SP Calculation", "RefUrl": "/notes/594394"}, {"RefNumber": "592292", "RefComponent": "PY-DE-PS-ZV", "RefTitle": "Subj. to SP Bonus in absence month not subject to SP", "RefUrl": "/notes/592292"}, {"RefNumber": "591261", "RefComponent": "PY-DE-PS-ZV", "RefTitle": "Notification program after new DATUEV-ZVE", "RefUrl": "/notes/591261"}, {"RefNumber": "587380", "RefComponent": "PY-DE-PS-ZV", "RefTitle": "Combination Model and Flat-Rate SI Additional Amount", "RefUrl": "/notes/587380"}, {"RefNumber": "586410", "RefComponent": "PY-DE-PS-ZV", "RefTitle": "Fictitious gross amounts (SR,...) too high for new SP calculation", "RefUrl": "/notes/586410"}, {"RefNumber": "577084", "RefComponent": "PY-DE-PS-ZV", "RefTitle": "New Tax and SI Calculation of Supplementary Pension", "RefUrl": "/notes/577084"}, {"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Advance transports to customer (note for customers)", "RefUrl": "/notes/13719"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "URL"}, "Items": [{"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Advance transports to customer (note for customers)", "RefUrl": "/notes/13719 "}, {"RefNumber": "594394", "RefComponent": "PY-DE-PS-ZV", "RefTitle": "Guaranteed Net Amounts, AVmG, and New SP Calculation", "RefUrl": "/notes/594394 "}, {"RefNumber": "586410", "RefComponent": "PY-DE-PS-ZV", "RefTitle": "Fictitious gross amounts (SR,...) too high for new SP calculation", "RefUrl": "/notes/586410 "}, {"RefNumber": "591261", "RefComponent": "PY-DE-PS-ZV", "RefTitle": "Notification program after new DATUEV-ZVE", "RefUrl": "/notes/591261 "}, {"RefNumber": "599201", "RefComponent": "PY-DE-PS-ZV", "RefTitle": "Employer costs (wage type /700) too high", "RefUrl": "/notes/599201 "}, {"RefNumber": "599889", "RefComponent": "PY-DE-PS-ZV", "RefTitle": "Fictitious calculations (SR, SPS, ZMuSch) too low", "RefUrl": "/notes/599889 "}, {"RefNumber": "609197", "RefComponent": "PY-DE-PS-ZV", "RefTitle": "Double consideration of flat-rate tax from SPF calculation", "RefUrl": "/notes/609197 "}, {"RefNumber": "594400", "RefComponent": "PY-DE-PS-ZV", "RefTitle": "Changes regarding SP Calculation During SR", "RefUrl": "/notes/594400 "}, {"RefNumber": "599087", "RefComponent": "PY-DE-PS-ZV", "RefTitle": "New DATUEV-ZVE: Separate Notification Special Remuneration SR", "RefUrl": "/notes/599087 "}, {"RefNumber": "577084", "RefComponent": "PY-DE-PS-ZV", "RefTitle": "New Tax and SI Calculation of Supplementary Pension", "RefUrl": "/notes/577084 "}, {"RefNumber": "587380", "RefComponent": "PY-DE-PS-ZV", "RefTitle": "Combination Model and Flat-Rate SI Additional Amount", "RefUrl": "/notes/587380 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And the following"}, "Items": [{"SoftwareComponent": "SAP_HR", "From": "40B", "To": "40B", "Subsequent": ""}, {"SoftwareComponent": "SAP_HR", "From": "45B", "To": "45B", "Subsequent": ""}, {"SoftwareComponent": "SAP_HR", "From": "46B", "To": "46B", "Subsequent": ""}, {"SoftwareComponent": "SAP_HR", "From": "46C", "To": "46C", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software-Komponentenversion", "SupportPackage": "Support Package", "URL": "URL"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Packages and Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "URL"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "URL"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about correction instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisite", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisite", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "URL", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated as helpful", "Helpful-Yes": "0 persons"}, "RatingQuality": {"_label": "Quality Assessment"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document eliminates side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "URL"}, "Items": []}, "SideEffectsSolving": {"_label": "This document causes side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "URL"}, "Items": []}}, "DisclaimerForMachineTranslation": {"Header": "ATTENTION!", "Message": "This is a machine-translated document that has not been reviewed for accuracy.", "Survey": "To provide feedback regarding the machine translation, please <a href=\"https://sapinsights.eu.qualtrics.com/jfe/form/SV_6nI2C1MOwEviVQG?NoteNumber=600229&TargetLanguage=EN&Component=PY-DE-PS-ZV&SourceLanguage=DE&Priority=04\" target=\"_blank\">click here</a>.", "Request": "If the quality of the machine translation is not sufficient for your purposes, click here to request a revised version.", "Origin": "You can access the original document via the following link: <a href=\"/notes/600229/D\" target=\"_blank\">/notes/600229/D</a>."}}}}