{"Request": {"Number": "425488", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 474, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": false, "url": ""}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000425488?language=E&token=A47808E2718A3C0AD200D9D30996D2F2"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000425488", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000425488/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "425488"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 27}, "Recency": {"_label": "Recency", "key": "", "value": ""}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Installation information"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "To Be Archived"}, "ReleasedOn": {"_label": "Released On", "value": "00.00.0000"}, "SAPComponentKey": {"_label": "Component", "value": "XX-PROJ-CAG"}, "SAPComponentKeyText": {"_label": "Component", "value": "Connect and Go  ASP Solution SAP Hosting"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Miscellaneous", "value": "XX", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Project-based solutions", "value": "XX-PROJ", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-PROJ*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Connect and Go ASP Solution SAP Hosting", "value": "XX-PROJ-CAG", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-PROJ-CAG*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "425488 - INST: Connect and Go 46C 1.00 Installation"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>***********************************************************************<br />*&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; *<br />*&#x00A0;&#x00A0;&#x00A0;&#x00A0; Connect and Go Installation&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;46C 1.00&#x00A0;&#x00A0; *<br />*&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; *<br />***********************************************************************</p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>SME, BusinessManager, C&amp;G, ASP, CAG, SAPHosting, Connect &amp; Go<br /></p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>I/&#x00A0;&#x00A0;&#x00A0;&#x00A0;Problems discovered after publication of the Installation Guide<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;to Connect and Go 46C Release 1.00<br />II/&#x00A0;&#x00A0; Corrections after import of C&amp;G transports from the CD-Rom<br />III/&#x00A0;&#x00A0;Temporal contents specification<br /></p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p><B>CAUTION: This note is continually being updated!</B><br /><B>CAUTION: The product is discontinued and no longer supported!</B></p> <b>I/ ....... Correction of the installation guide</b><br /> <p><br />1. Unpack CAR Files: (Page 11)<br />&#x00A0;&#x00A0; ----------------<br />&#x00A0;&#x00A0; Connect and Go has been repacked as SAR File, the correct<br />&#x00A0;&#x00A0; command line to unpack the transports is<br /><br />&#x00A0;&#x00A0; SAPCAR -xvf CAG46CR1.SAR<br /><br />&#x00A0;&#x00A0; You can find the SAPCAR tool in the kernel directory or on the kernel &#x00A0;&#x00A0;CD.<br /><br />2. Reinstate depreciation keys (Step 11 of post installation)<br />&#x00A0;&#x00A0; ---------------------------<br />&#x00A0;&#x00A0; Do not start the program \"Depreciation_methods_fill\" mentioned<br />&#x00A0;&#x00A0; in this step. This step was replaced by the correction transport<br />&#x00A0;&#x00A0; order for C&amp;G available on the SAPSERV3.<br /><br />3. Import User groups and Info Sets (Step 27 of post installation)<br />&#x00A0;&#x00A0; --------------------------------<br />&#x00A0;&#x00A0; (This step of installation concern the UK version (HR) only!)<br />&#x00A0;&#x00A0; The task sequences of this step are described in a wrong way. Start<br />&#x00A0;&#x00A0; at first the task B (Select \"Standard area-client specific\" for<br />&#x00A0;&#x00A0; Query Areas) before starting the task A (Importing the listed<br />&#x00A0;&#x00A0; transport orders). Then continue with the task C and D.<br /><br />4. Post-Process Overhead Rates (Step 12 of post installation)<br />&#x00A0;&#x00A0; --------------------------------<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;The input file YS_OHRAT_ZZ.TXT was delivered with the wrong<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;transport order numbers in it.&#x00A0;&#x00A0;Before running this step delete<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;all transport order numbers beginning with DCG* from the file<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;and replace them with transport orders: PCGK900015, PCGK900016<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;and PCGK900017.<br /><br />5. Installation Steps (page 10)<br />&#x00A0;&#x00A0; ----------------------------<br />&#x00A0;&#x00A0; The transport request should be of course<br />&#x00A0;&#x00A0; PCGK900001 instead of CAGK900001<br />&#x00A0;&#x00A0; PCGK900025 instead of CAGK900025<br />&#x00A0;&#x00A0; Also, prior to installing PCGK900025 please be sure to have the<br />&#x00A0;&#x00A0; required support packages and the LSMW installed in your system!<br /></p> <b>II/ .....Correction transport (not included into the delivered CD-rom)</b><br /> <p><br />1. After importing all transports from the 'Connect And Go' - CD please<br />import the following transports from SAPSERV3:<br /><br />Correction 1:<br />Transport request WC1K900017<br />ftp://sapserv3/general/R3server/abap/note.0425488<br /><br />Contents of correction 1:<br />- Calendar for Switzerland (Aargau) - ID is 2a<br />- Tansaction codes and variants for Switzerland<br />&#x00A0;&#x00A0;Variants (/CGCH/...) are requalified as cross client<br />- Depreciation tables (T090NA, ...) are included in this correction<br />&#x00A0;&#x00A0;(Step 11 of the implementation is no longer relevant)<br />- Calculation schema for Logistic (ZM0000, RM0000)<br /><br />Correction 2:<br />Transport request WC1K900045<br />ftp://sapserv3/general/R3server/abap/note.0434117<br /><br />Contents of correction 2:<br />- See SAP note 434117<br /><br />Correction 3:<br />Transport request WC1K900033<br />ftp://sapserv3/general/R3server/abap/note.0434990<br /><br />Contents of correction 3:<br />- See SAP note 434990<br /><br />Correction 4:<br />Transport request WC1K900055<br />ftp://sapserv3/general/R3server/abap/note.0453774<br /><br />Contents of correction 4:<br />- See SAP note 453774<br /><br />2. Please also take the following C&amp;G notes into consideration:<br />&#x00A0;&#x00A0; 424932 : Fiscal Year variant V4<br />&#x00A0;&#x00A0; 429014 : Commitment Mgt. for Internal Orders<br />&#x00A0;&#x00A0; 458682 : VAT code for the CAG Swiss version<br />&#x00A0;&#x00A0; 513651 : Connect &amp; Go - Coding error for BTE 2310<br />&#x00A0;&#x00A0; 513799 : Sundry financial configuration issues (U.K. only)<br /></p> <b>III/........Temporal contents specification</b><br /> <p>Date....Topic....Short description<br />------------------------------------------------------------------------<br />25/AUG/01....I...Usage of SAPCAR instead of CAR<br />27/AUG/01....I...Modification of postinst steps<br />27/AUG/01...II...Correction transport<br />11/SEP/01....I...typo correction for installation steps diagram<br />01/JAN/03....I...Product discontinued</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Transaction codes", "Value": "FILE"}, {"Key": "Transaction codes", "Value": "SAR"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON> (D026202)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON> (D026202)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000425488/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000425488/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000425488/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000425488/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000425488/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000425488/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000425488/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000425488/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000425488/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "212876", "RefComponent": "BC-INS", "RefTitle": "The new archiving tool SAPCAR", "RefUrl": "/notes/212876"}, {"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "212876", "RefComponent": "BC-INS", "RefTitle": "The new archiving tool SAPCAR", "RefUrl": "/notes/212876 "}, {"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_APPL", "From": "46C", "To": "46C", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}