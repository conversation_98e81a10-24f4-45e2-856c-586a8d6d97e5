{"Request": {"Number": "434647", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 324, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000015069372017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000434647?language=E&token=E228AF62576EBAB7D491ED68224D276D"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000434647", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000434647/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "434647"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 15}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Consulting"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "13.01.2009"}, "SAPComponentKey": {"_label": "Component", "value": "BC"}, "SAPComponentKeyText": {"_label": "Component", "value": "Basis Components"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Basis Components", "value": "BC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "434647 - Point-in-time recovery in an SAP system group"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>You have to use a point-in-time recovery to reset the database of a system in a system group to a previous version. You cannot use the the alternative options to avoid a point-in-time recovery described in Note 434645.<br />This note describes the options available and the general procedure for an incomplete recovery of a system in a system group.<br /></p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>Point-in-time restore, PIT, point-in-log recovery, incomplete recovery, data loss, data inconsistency, flashback<br /></p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>A point-in-time recovery of a system in a system group always causes data to be lost in the relevant system and also causes data inconsistencies in relation to other components in the system landscape. These types of inconsistencies can cause considerable problems in the business processes and, over time, may even result in the database becoming inoperative. It is generally very time-consuming to compare these inconsistencies and it also requires a good knowledge of the application data and application processes.<br /><br />Therefore, we generally recommend that you do not perform an incomplete recovery in a production system. In particular, logical errors in a system are often specified as a reason for an incomplete recovery. However, you must use other options to repair these (see Note 434645).<br /><br />In the case of a point-in-time recovery, you must weigh up the following factors:</p> <UL><LI>Consistency within the system to be restored</LI></UL> <UL><LI>Consistency between the system to be restored and all remaining systems in the landscape</LI></UL> <UL><LI>Loss of data in the systems that are part of the landscape</LI></UL> <p><br />Basically, there are three variants of a point-in-time recovery for systems in a system landscape and each of these has different effects on the amount of data that is lost or the number of inconsistencies.</p> <OL><OL>a) Point-in-time recovery only in the affected system: If you use this variant, only the affected system is reset to a previous version. In this variant, the number of inconsistencies is at its highest; however, the amount of data lost is restricted to one system. You may also be able to recover the missing data from the data in the other components.</OL></OL> <OL><OL>b) Point-in-time recovery for all components that are part of the system landscape: All components in the landscape are reset to the same version. In this variant, only a few inconsistencies remain and this is because the system clocks do not run to exactly the same time on the different computers. The amount of data lost is higher than in the first variant and there is no option to restore the data from the other components.</OL></OL> <OL><OL>c) Importing a consistent backup of the entire landscape (if this exists): In this variant, you import a consistent backup of the entire system landscape. However, to do this, you must have created this type of backup at some time before the cause of the error. Since this time is generally not just before the cause of the error, the amount of data lost is greatest in this variant but no inconsistencies exist between the systems.</OL></OL> <p></p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>We cannot describe a procedure that is generally valid because the solution depends on the business processes that are implemented. However, you MUST consider the following points if you want to perform a point-in-time recovery:</p> <OL>1. Create a CSS message before you start a point-in-time recovery. Ensure that you make it clear in the message that this system is part of a system group. You may be able to avoid a restore by using different options.</OL> <OL>2. If you cannot avoid a point-in-time recovery with a loss of data in a system, you must decide whether,</OL> <UL><LI>when you consider all systems, you want to ensure that the amount of data lost is as minimal as possible by restoring only this system (this causes a larger number of inconsistencies between the systems and, therefore, requires more effort to clean up the inconsistencies) or whether,</LI></UL> <UL><LI>when you consider all systems, you would prefer to lose more data by restoring all systems in the system group but, in doing this, you ensure that few or no inconsistencies arise between the systems. When you do this, you must bear in mind that additional systems always exist outside the group and that additional inconsistencies will arise in relation to the real world, which you must solve.</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;In each case, you must solve all of the logical inconsistencies between the systems before you restart production operation. <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;We also recommend that you do not perform the restore on the original production system or that you at least create a copy of the old production system so that you can manually reconstruct the lost data at a later time. <OL>3. Depending on the system components that are affected and depending on the business processes that are implemented, you must perform different steps to compare the data between the individual components.<br />To recognize and also possibly correct inconsistencies between SAP systems, SAP provides tools that can analyze the particular objects. These tools can be used to highlight any inconsistencies between systems arising from operating errors in the normal operation as well as support the correction of data inconsistencies after a point-in-time recovery.<br />For more information about the options that are currently available, see the related notes and the \"Data Consistency Check for Logistics\" Best Practice document provided at http://service.sap.com/runsap.<br />In the ALE environment, you can also use the IDocs that still exist in the system to compare the data (see Note 438820).</OL> <p><br />More information about this, including information about creating a backup and restore concept is also provided in the \"Backup and Restore for mySAP.com Business Suite\" Best Practice document (also at http://service.sap.com/runsap).</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "BC-DB-DB6 (DB2 Universal Database for Unix / NT)"}, {"Key": "Other Components", "Value": "BC-DB-SDB (MaxDB)"}, {"Key": "Other Components", "Value": "BC-DB-LVC (liveCache)"}, {"Key": "Other Components", "Value": "BC-DB-MSS (SQL Server in SAP NetWeaver Products)"}, {"Key": "Other Components", "Value": "BC-DB-DB4 (DB2 for AS/400)"}, {"Key": "Other Components", "Value": "BC-DB-DB2 (DB2 for z/OS)"}, {"Key": "Other Components", "Value": "BC-DB-ORA (Oracle)"}, {"Key": "Other Components", "Value": "BC-DB-INF (Informix)"}, {"Key": "Other Components", "Value": "BC-DB-LCA (liveCache Applications)"}, {"Key": "Responsible                                                                                         ", "Value": "D024788"}, {"Key": "Processor                                                                                           ", "Value": "D024788"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000434647/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000434647/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000434647/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000434647/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000434647/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000434647/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000434647/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000434647/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000434647/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "966117", "RefComponent": "BC-DB-ORA", "RefTitle": "Oracle Flashback Database technology", "RefUrl": "/notes/966117"}, {"RefNumber": "731682", "RefComponent": "BW-SYS", "RefTitle": "Backup in BW and OLTP: Experiences, risks & recommendations", "RefUrl": "/notes/731682"}, {"RefNumber": "643542", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/643542"}, {"RefNumber": "605062", "RefComponent": "BC-DB-ORA", "RefTitle": "FAQ: Restore and recovery", "RefUrl": "/notes/605062"}, {"RefNumber": "438820", "RefComponent": "BC", "RefTitle": "Point-in-time recovery in the ALE environment", "RefUrl": "/notes/438820"}, {"RefNumber": "434645", "RefComponent": "BC", "RefTitle": "Point-in-time recovery: What must I be aware of?", "RefUrl": "/notes/434645"}, {"RefNumber": "425825", "RefComponent": "SCM-TEC", "RefTitle": "Consistency checks, /sapapo/om17, /sapapo/cif_deltareport", "RefUrl": "/notes/425825"}, {"RefNumber": "363602", "RefComponent": "CRM-MW-ADP", "RefTitle": "New Compare monitor - Request for Compare", "RefUrl": "/notes/363602"}, {"RefNumber": "1420452", "RefComponent": "BC-DB-MSS", "RefTitle": "FAQ: Restore and recovery with MS SQL Server", "RefUrl": "/notes/1420452"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "966117", "RefComponent": "BC-DB-ORA", "RefTitle": "Oracle Flashback Database technology", "RefUrl": "/notes/966117 "}, {"RefNumber": "731682", "RefComponent": "BW-SYS", "RefTitle": "Backup in BW and OLTP: Experiences, risks & recommendations", "RefUrl": "/notes/731682 "}, {"RefNumber": "605062", "RefComponent": "BC-DB-ORA", "RefTitle": "FAQ: Restore and recovery", "RefUrl": "/notes/605062 "}, {"RefNumber": "1420452", "RefComponent": "BC-DB-MSS", "RefTitle": "FAQ: Restore and recovery with MS SQL Server", "RefUrl": "/notes/1420452 "}, {"RefNumber": "434645", "RefComponent": "BC", "RefTitle": "Point-in-time recovery: What must I be aware of?", "RefUrl": "/notes/434645 "}, {"RefNumber": "363602", "RefComponent": "CRM-MW-ADP", "RefTitle": "New Compare monitor - Request for Compare", "RefUrl": "/notes/363602 "}, {"RefNumber": "425825", "RefComponent": "SCM-TEC", "RefTitle": "Consistency checks, /sapapo/om17, /sapapo/cif_deltareport", "RefUrl": "/notes/425825 "}, {"RefNumber": "438820", "RefComponent": "BC", "RefTitle": "Point-in-time recovery in the ALE environment", "RefUrl": "/notes/438820 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": []}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}