{"Request": {"Number": "601157", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 339, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000015381192017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000601157?language=E&token=4683AE3C5B700E9AEAC96BF00D9DA87F"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000601157", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000601157/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "601157"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 35}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Upgrade information"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "02.07.2007"}, "SAPComponentKey": {"_label": "Component", "value": "BC-DB-ORA"}, "SAPComponentKeyText": {"_label": "Component", "value": "Oracle"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Basis Components", "value": "BC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Database Interface, Database Platforms", "value": "BC-DB", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-DB*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Oracle", "value": "BC-DB-ORA", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-DB-ORA*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "601157 - Oracle9i: Server Parameter File"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p><br /></p> <b>Oracle Server Parameter File (SPFILE)</b><br /> <p>The Oracle Server Parameter File (SPFILE) is a new feature available as of Oracle Release 9i. This note describes how to use the SPFILE rather than the Oracle parameter file init&lt;SID&gt;.ora (short: init.ora).<br /><br />The following designations are used in this note:</p> <UL><LI><B>&lt;SID&gt;</B> is the &lt;ORACLE_SID&gt; which is the unique indicator of an Oracle database instance. This is not to be confused with the &lt;SAPSID&gt;, the SAP system ID (unique indicator of an SAP system).</LI></UL> <UL><LI><B>init.ora</B> is used as a synonym for the known Oracle parameter file init&lt;SID&gt;.ora.</LI></UL> <UL><LI><B>SPFILE</B> is the short form for Server Parameter File.<br /></LI></UL> <p>Oracle profile file, init&lt;SID&gt;.ora and init.ora (short form) are synonyms for the known Oracle parameter file.<br /><br />SPFILE (short form), server-side parameter file, and sometimes also persistent parameter file are used as synonyms for the server parameter file spfile&lt;SID&gt;.ora.<br /></p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p><br /></p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p><br /><br />Documentation and migration note for Oracle9i: <B>SPFILE</B><br /></p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p><br /></p> <b>Structure of this note<br /></b><br /> <p>This note is structured as follows:<br />Support by BR*Tools and SAPDBA<br />BR*Tools and SPFILE - Contexts and explanations<br />General Recommendation<br />SPFILE - Description<br />SPFILE - Search Sequence<br />SPFILE - Functions, Prerequisites, Constraints<br />SPFILE - Advantages<br />SPFILE - Special Features<br />SPFILE - Special Features with Oracle Real Application Cluster (RAC)<br />SPFILE - Migration<br />SPFILE - Migration (Return path)<br />SPFILE - Restoring an Inconsistent SPFILE<br />SPFILE - Administration<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;- Default Location and Default Name of the SPFILE<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;- Determining the active SPFILE<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;- Exporting the SPFILE<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; - Displaying the parameter in SPFILE at operating system level<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; - Displaying Instance Parameters with SQL*PLUS<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; - Setting system parameters in the SPFILE (persistent)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;- Setting system parameters temporarily<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;- Resetting system parameters<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;- Setting events in the SPFILE<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; - Setting underscore parameters<br /></p> <b>Support by BR*Tools and SAPDBA<br /></b><br /> <p>Backup and restore of the SPFILE are supported as of BR*Tools 6.20 Patch Level 103.<br /><br />In addition, BR*Tools 6.40 also provides parameter maintenance for SPFILE with the program BRSPACE (see Note 647697).<br /><br />Due to improved SPFILE support in BR*Tools (BRSPACE), we recommend that you generally use the latest version of the BR*Tools (6.40 or higher).<br /></p> <b>BR*Tools and SPFILE - Contexts and explanations<br /></b><br /> <p>With every run, brbackup, brarchive, and 'brconnect -check' create a new profile file 'init&lt;DBSID&gt;.ora' in the standard directory and under the default name, if a SPFILE exists in the standard directory under the default name regardless of whether this SPFILE is active or not. This process will overwrite a file 'init&lt;DBSID&gt;.ora' that exists there! If you want to keep the last original version of init&lt;DBSID&gt;.ora for the moment, copy it (do not rename it, because certain SAP transactions, or example, ST04, need this file).<br /><br />A file generated by the BR*Tools can be recognized by the file header:<br /><br />################################################################<br /># This init.ora profile is a transparent copy of Oracle spfile.<br /># It was created by BR*Tools at 2004-03-26 08.53.03.<br /># Please do not change this file! Maintain only the spfile.<br /># You can use BRSPACE for the altering of database parameters:<br />#&#x00A0;&#x00A0;&#x00A0;&#x00A0;brspace -f dbparam<br />################################################################<br /><br />When you change a parameter in SPFILE using brspace, the system also automatically creates a new init&lt;SID&gt;.ora from the SPFILE so the contents of SPFILE and init&lt;SID&gt;.ora remain consistent. Certain SAP transactions (CCMS:DB02, DB03, DB26; ST04) still rely on the existence of an init.ora. If the spfile and init&lt;SID&gt;.ora are not kept consistent, these transactions show a status that is out-of-date in comparison with the SPFILE. If no init&lt;SID&gt;.ora exists, no display occurs at all. These SAP transactions only have a display character of lesser importance. Their function has no effect on the operation of the database itself.<br /><br />Another reason for generating an init&lt;SID&gt;.ora from the SPFILE is brarchive. brarchive does not change the status of the database. To save archives logs if the database is stopped, brarchive must therefore read the init&lt;SID&gt;.ora to determine the archive directory.<br /></p> <b>General Recommendation<br /></b><br /> <p>We recommend that you switch to using the SPFILE as part of the database upgrade to Oracle9i.<br /></p> <b>SPFILE - Description<br /></b><br /> <p>Until now, initializing parameters have been specified for an Oracle instance in the parameter file known as init.ora <B>on the client side</B>. In most cases, this parameter file is located on the database server. However, this does have to be the case, since the instance can also be started from another (remote) host. The prerequisite for this, however, is that the init.ora file is stored locally on that host. Therefore, the init.ora file may exist several times on several hosts, which complicates the synchronization if a parameter is changed. This problem no longer occurs with a server parameter file.<br /><br /><B>SPFILE - Search Sequence</B><br />Oracle searches for the parameter file in the platform-dependent default location (Unix: $ORACLE_HOME/dbs; Windows: %ORACLE_HOME%\\DATABASE) using the following names and in the specified sequence:<br />1. spfile&lt;SID&gt;.ora<br />2. spfile.ora<br />3. init&lt;SID&gt;.ora<br /></p> <b>SPFILE - Functions, Prerequisites, Constraints<br /></b><br /> <p>The SPFILE is managed by the Oracle server. New entries or parameter changes are performed using the command 'ALTER SYSTEM SET...'. In the scope clause, you specify whether the change should be temporary (scope = memory, valid until the next instance restart) or whether it should be saved permanently in the SPFILE (scope = spfile, only effective after the instance is restarted). The default setting is scope=both (valid immediately and permanently, even after an instance restart). You can also delete a parameter entry from the SPFILE again using 'ALTER SYSTEM RESET...'.<br /><br />A SPFILE can generate from an existing init.ora with the CREATE SPFILE command, regardless of the instance status (nomount, mount, open). The <B>active SPFILE</B> of an instance is the one that has been used to start the instance.<br /></p> <b>SPFILE - Advantages<br /></b><br /> <p>The main advantages of an SPFILE are described below:</p> <UL><LI>Administration through the Oracle server (for example, with SQL*PLUS) using SQL commands.</LI></UL> <UL><LI>Dynamic changes to the system configuration with 'ALTER SYSTEM SET' are by default written simultaneously to the SPFILE and are therefore retained after the instance is restarted. Consequently, unlike the classic init.ora, you do not need to maintain the init.ora file after you change a parameter using 'ALTER SYSTEM SET'.</LI></UL> <UL><LI>Starting an Oracle instance remotely without a local copy of the init.ora.</LI></UL> <UL><LI>If an instance is started, you can use the instance information to determine which SPFILE was used to start it. This is not possible if the instance has been started with a standard init.ora file.</LI></UL> <UL><LI>If parameter changes are made in the SPFILE with BRSPACE, a history of the parameter changes is recorded in the relevant BRSPACE logs. The usual manual maintenance of parameter changes as a comment in the init.ora is not required in this case.<br /></LI></UL> <p>As a result of the advantages already mentioned, the SPFILE already forms the basis for future enhancements of the Oracle database in the area of Self-administration and Automatic Tuning (Oracle 10 g).<br /></p> <b>SPFILE - Special Features<br /></b><br /> <p>Note the following points when you use an SPFILE:</p> <UL><LI>An SPFILE is a binary file. The contents can be displayed with a normal editor. However, write access corrupts the SPFILE. If the SPFILE is corrupted, the instance cannot be started or a current instance terminates.</LI></UL> <UL><LI>SYSOPER or SYSDBA authorization is required for working with the SPFILE.</LI></UL> <UL><LI>If an instance was started with an SPFILE and this SPFILE was deleted by mistake, parameter changes can no longer be written permanently to the SPFILE. You can no longer execute 'ALTER SYSTEM SET' commands with SCOPE=SPFILE or SCOPE=BOTH. Error message:<br />&#x00A0;&#x00A0; ORA-27041: unable to open file<br />&#x00A0;&#x00A0; OSD-04002: unable to open file<br /></LI></UL> <p>The configuration option provided by Oracle (SPFILE in a non-standard location or with a non-standard name) is not supported in the SAP environment. For a non-standard SPFILE to be found automatically by Oracle, you would have to make following entry as the only entry in the init&lt;SID&gt;.ora:<br /><br />&#x00A0;&#x00A0;spfile=&lt;path to non-standard-spfile&gt;<br /><br />This configuration is incompatible with the use of BR*Tools. <br /></p> <b>SPFILE - Special Features with Oracle Real Application Cluster (RAC)<br /></b><br /> <p>In an RAC environment, a single SPFILE can be used for all RAC instances. Since this SPFILE must be accessible to all RAC instances, it must be located on the cluster file system (shared disk). See Note 621293.<br /> <br />The following configuration recommendation applies to SAP on Oracle RAC:</p> <UL><LI>Perform Oracle-RAC installations only with SPFILE</LI></UL> <UL><LI>SPFILE on shared disk (prerequisite: Cluster File System CFS)<br />This allows all RAC instances to have read and write access to the SPFILE.</LI></UL> <UL><LI>Name of the SPFILE: spfile.ora<br />This allows all RAC instances to recognize and use a single SPFILE. This differs from single instance systems, where the naming convention spfile&lt;SID&gt;.ora applies. <br /></LI></UL> <b>SPFILE - Migration<br /></b><br /> <p>Before the SPFILE mechanism is activated in a system, you must ensure that the installed versions of BR*Tools or SAPDBA support this feature (see above).<br /><br />Important: If an SPFILE is generated from the current parameter file, comments are then transferred to the SPFILE if they belong to a parameter, so if they correspond to the following form:<br /><br />&lt;parameter_name&gt; = &lt;value&gt; # &lt;Comment for this parameter&gt;<br /><br />For example:<br />undo_management = AUTO # Automatic Undo activated by Larry<br /><br />Pure comment lines are not transferred. Therefore, we recommend that you check the validity and usefulness of existing comments before a migration to SPFILE to see whether they are to be copied into spfile or not.<br /><br />The following steps activate the SPFILE mechanism:<br />1. Create SPFILE<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0; SQL&gt;connect / as sysdba<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0; SQL&gt;create spfile from pfile;<br />This command creates a SPFILE at the platform-dependent default position with the default name spfile&lt;SID&gt;.ora.<br /><br />2. Copy the old parameter file as a backup and save it under a different name:<br /><br />$&gt;cp $ORACLE_HOME/dbs/init&lt;SID&gt;.ora $ORACLE_HOME/dbs/init&lt;SID&gt;.ora.SAVE<br /><br />3.&#x00A0;&#x00A0;Restart the database instance and check whether the SPFILE is active<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;SQL&gt;connect / as sysdba<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;SQL&gt;shutdown<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0; SQL&gt;startup<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0; SQL&gt;show parameter spfile<br /></p> <b>SPFILE - Migration (Return path)<br /></b><br /> <p>The section below describes the return path from the SPFILE to the classic init&lt;SID&gt;.ora. We assume that an SPFILE exists in the standard location and the init&lt;SID&gt;.ora is also to be created in the standard location. Therefore, the instance is started with a normal 'startup' command. Execute the following steps on the database server:<br />1. Create PFILE<br />&#x00A0;&#x00A0; SQL&gt;connect / as sysdba<br />&#x00A0;&#x00A0; SQL&gt;create pfile from spfile;<br />2. Delete or rename the old SPFILE at the standard position<br />3.&#x00A0;&#x00A0;Start and check the database instance<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;SQL&gt;connect / as sysdba<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;SQL&gt;shutdown<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;SQL&gt;startup<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;SQL&gt;show parameter spfile<br />The parameter 'spfile' should now be an empty string. The SPFILE is no longer active.<br /></p> <b>SPFILE - Restoring an Inconsistent SPFILE<br /></b><br /> <p>If an invalid parameter value (for example, db_cache_size too large) has been written to the SPFILE by mistake - for example, due to a typing error - and the mistake is not spotted immediately, the system recognizes this error when it reads the SPFILE the next time you try to restart the instance and you cannot restart the instance. The start procedure terminates with an error message referring to the incorrect parameter.<br /><br />To start the instance, you can either access a backup of the SPFILE or generate a temporary standard text parameter file from the current, inconsistent SPFILE. Correct the incorrect parameter value in this text file. Then recreate an SPFILE from the corrected parameter. To do this, proceed as follows:<br /><br />1. Logon with SYSDBA or SYSOPER authorization<br />&#x00A0;&#x00A0; SQL&gt;connect / as sysdba<br />2. Create the parameter file<br />&#x00A0;&#x00A0; SQL&gt;create pfile='temp_init.ora' from spfile;<br />3. Correct the incorrect parameter in the created parameter file<br />4. Create SPFILE (at the standard position)<br />&#x00A0;&#x00A0; SQL&gt;create spfile from pfile='temp_init.ora'<br />5. Start the instance (using the corrected spfile)<br />&#x00A0;&#x00A0; SQL&gt;startup<br /></p> <b>SPFILE - Administration<br /></b><br /> <b>Default Location and Default Name of the SPFILE<br /></b><br /> <p>Unix platforms: $ORACLE_HOME/dbs/init&lt;SID&gt;.ora<br />Windows platforms: %ORACLE_HOME%\\database\\INIT&lt;SID&gt;.ORA<br /><br />Algorithm used to search for the correct parameter file in the default location:<br />1. spfile&lt;SID&gt;.ora<br />2. spfile.ora<br />3. init&lt;SID&gt;.ora<br /></p> <b>Determining the active SPFILE<br /></b><br /> <p>The following sources allow you to determine which SPFILE was used to start the instance:<br />Option 1: SQL&gt;show parameter spfile<br />An empty string means that the instance was started with a standard init.ora. Otherwise, the SPFILE was used with the displayed path.<br />Option 2: Alert Log<br />If an SPFILE other than the SPFILE of the standard location was used, this is also shown in the Oracle Alert Log: When the instance is started, the system parameters that do not have default values are also listed.<br /></p> <b>Exporting the SPFILE<br /></b><br /> <p>You can export the contents of an SPFILE, in particular the active SPFILE, at any time, regardless of the status of the instance, to a normal init.ora text file (standard Oracle parameter file). Reasons for an export include:</p> <UL><LI>Regular backups of the SPFILE content</LI></UL> <UL><LI>Diagnosis/listing of the set parameters</LI></UL> <UL><LI>for changing parameters manually using export/change/SPFILE (for instance, in the case of an inconsistent SPFILE)</LI></UL> <p>You can also use the parameter file created to start the instance by means of the PFILE option.<br /><br />Syntax:<br />SQL command variant 1:<br />&#x00A0;&#x00A0; SQL&gt;create pfile from spfile;<br />SQL command variant 2:<br />&#x00A0;&#x00A0; SQL&gt;create pfile='&lt;path_pfile&gt;' from spfile;<br />SQL command variant 3:<br />&#x00A0;&#x00A0;SQL&gt;create pfile='&lt;path_pfile&gt;' from<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; from&#x00A0;&#x00A0;spfile='&lt;path_spfile&gt;';<br /><br />Note that Oracle overwrites an existing file without issuing a warning. If no explicit path is specified, Oracle generates the init&lt;SID&gt;.ora using the Oracle default path (see above).<br /><br />If a path is specified, it must exist. If the path does not exist, the system issued the following error messages:<br />&#x00A0;&#x00A0;ORA-01078: failure in processing system parameters<br />&#x00A0;&#x00A0;ORA-27040: skgfrcre: create error, unable to create file<br />&#x00A0;&#x00A0;OSD-04002: unable to open file<br />&#x00A0;&#x00A0;O/S-Error: (OS 3) The system cannot find the path specified.<br /></p> <b>Displaying the parameter in SPFILE at operating system level<br /></b><br /> <p>The following alternatives exist for displaying the parameter set in SPFILE, its values and (if available) the comment for a parameter value:<br />Option 1: with a normal text editor<br />CAUTION: Writing with a normal editor corrupts the data in the SPFILE and therefore renders it unusable.<br />Option 2: 'strings' command<br />On UNIX platforms only: strings command<br />&#x00A0;&#x00A0;$strings SPFILE&lt;SID&gt;.ORA | more<br />Displaying specific parameters in SPFILE:<br />&#x00A0;&#x00A0;$strings SPFILE&lt;&gt;SID&gt;.ORA | more | grep &lt;parameter_name&gt;<br /></p> <b>Displaying Instance Parameters with SQL*PLUS<br /></b><br /> <p>You can use the performance view V$SPPARAMETER to display the contents of the active SPFILE.</p> <UL><LI>Which parameters are set explicitly in the SPFILE?<br />SQL&gt;select name, value from v$spparameter where isspecified = 'TRUE'</LI></UL> <UL><LI>Which parameters are not specified in the SPFILE?<br />SQL&gt;select name, value from v$spparameter where isspecified = 'FALSE'</LI></UL> <UL><LI>Which current session parameters deviate from the settings of the system-wide parameters stored in the SPFILE?<br />SQL&gt;select sp.name NAME, sp.value SYSVALUE, p.value SESVALUE<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;from v$spparameter sp, v$parameter p<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;where p.name = sp.name and<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;p.value &lt;&gt; sp.value;</LI></UL> <UL><LI>Displaying the parameters set in SPFILE with comments<br />SQL&gt;select name, value, update_comment<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;from v$spparameter<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;where update_comment &lt;&gt; ' ';<br /></LI></UL> <b>Setting system parameters in the SPFILE (persistent)<br /></b><br /> <p>Whether or not a parameter change is stored in the SPFILE is specified using the SCOPE clause of the 'ALTER SYSTEM SET' command.</p> <UL><LI>SCOPE=BOTH (default):<br />Change both in the SPFILE and in the current instance (not permitted for static parameters).</LI></UL> <UL><LI>SCOPE=MEMORY:<br />The change is valid for the current instance, but not persistent in the SPFILE. Not permitted for static parameters.</LI></UL> <UL><LI>SCOPE=SPFILE:<br />the change is stored persistently in the SPFILE and activated with the next restart. This is the only option for changing static parameters.<br /></LI></UL> <p>General syntax:<br />SQL&gt;ALTER SYSTEM SET &lt;parameter_name&gt;=&lt;value&gt;<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;COMMENT = 'comment explaining why this parameter was changed&gt;<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;SCOPE = {BOTH|SPFILE|MEMORY}<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;SID&#x00A0;&#x00A0; = '&lt;SID&gt;|'*'&gt;;<br /><br />The parameters that can be changed dynamically are:<br />SQL&gt;select name, value, issys_modifiable<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;from v$parameter<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;where issys_modifiable = 'IMMEDIATE';<br />For these parameters, you can specify scope = memory, scope = both or scope = spfile.<br /><br />The following parameters are static:<br />SQL&gt;select name, value, issys_modifiable<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;from v$parameter<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;where issys_modifiable = 'FALSE' or issys_modifiable = 'DEFERRED';<br />These parameters can only be changed in the SPFILE. To activate them, you have to rstarte the instance.<br /></p> <b>Setting system parameters temporarily<br /></b><br /> <p>If you want to test the effect of a changed dynamic parameter first, you can execute the change with 'ALTER SYSTEM SET' without making the change persistent in the SPFILE. To do this, you must specify the scope clause.<br /></p> <b>Resetting System Parameters<br /></b><br /> <p>You can delete parameter entries from the SPFILE using the following command:<br />SQL&gt;ALTER SYSTEM RESET &lt;parameter_name&gt; scope = spfile sid = '*';<br /></p> <b>Setting Events in the SPFILE<br /></b><br /> <p>Explanation with an example:<br />Syntax in init&lt;SID&gt;.ora:<br /># events set for debug tracing of control and rollback<br />event = '10325 trace name context forever, level 10'<br />event = '10015 trace name context forever, level 1'<br /><br />SQL syntax for SPFILE:<br /><br />The following syntax is ideal: This method sets line breaks between the events.<br /><br />ALTER SYSTEM SET<br />EVENT = 'Event 1',<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;'Event 2',<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;'Event 3',<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;'Event n'<br />COMMENT = 'debug trace events for bug ...'<br />SCOPE = SPFILE;<br /><br />The following syntactic alternative is more critical and is therefore not recommended:<br />ALTER SYSTEM SET<br />&#x00A0;&#x00A0;EVENT='10325 trace name context forever,<br />&#x00A0;&#x00A0;level 10:10015 trace name context forever, level&#x00A0;&#x00A0;1'<br />&#x00A0;&#x00A0;COMMENT='debug tracing of control and rollback'<br />&#x00A0;&#x00A0;SCOPE=SPFILE;<br /><br />If you use this syntax, you must make absolutely sure that no line breaks occur in the event text. Even though this does not cause an error when you set the events, the SPFILE is corrupted when you delete the event parameters because not all lines are deleted correctly. (See Oracle Enhancement Request 2946487). Therefore it is better not to use this syntax.<br />If the different events are separated by ':', then these should be in one line:<br />ALTER SYSTEM SET<br />&#x00A0;&#x00A0;EVENT='Event1:Event2:Event3' COMMENT = 'comment' SCOPE = SPFILE;<br />This prevents the SPFILE from being corrupted when the event parameters are deleted.<br /><br />You must restart the instance for the change to become effective.<br /><br />To set new events, remove individual events from the list, or to make changes to an event from the list, you must reset the complete list and restart the instance.<br /><br />All events are removed from the SPFILE using the following command:<br />SQL&gt;ALTER SYSTEM RESET EVENT SCOPE=SPFILE SID='*';<br />With a RAC configuration, you must replace '*' with the instance name.<br /></p> <b>Setting Underscore Parameters<br /></b><br /> <p>You must set the underscore parameters in inverted commas:<br />SQL&gt;alter system set \"_push_join_predicate\"=false scope=spfile;<br />System altered.<br />SQL&gt;alter system reset \"_push_join_predicate\"<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;scope = spfile sid = '*';<br />System altered.<br /><br />Otherwise, you get the following error message:<br />SQL&gt;alter system set _push_join_predicate=false scope=spfile;<br />ERROR at line 1:&#x00A0;&#x00A0; ORA-00911: invalid character<br /></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "BC-DB-ORA-DBA (Database Administration)"}, {"Key": "Database System", "Value": "ORACLE"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON> (C5000979)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON> (C5015687)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000601157/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000601157/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000601157/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000601157/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000601157/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000601157/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000601157/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000601157/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000601157/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "948197", "RefComponent": "BC-DB-ORA", "RefTitle": "Merge fix for DBMS_STATS package on Oracle 9.2.x and 10.2.x", "RefUrl": "/notes/948197"}, {"RefNumber": "848708", "RefComponent": "BC-CTS-DTR-SRV", "RefTitle": "Oracle Database Configuration for NWDI server", "RefUrl": "/notes/848708"}, {"RefNumber": "830576", "RefComponent": "BC-DB-ORA", "RefTitle": "Parameter recommendations for Oracle 10g", "RefUrl": "/notes/830576"}, {"RefNumber": "773173", "RefComponent": "BC-DB-ORA", "RefTitle": "Using Oracle Fail Safe cluster on Windows with SPFILE", "RefUrl": "/notes/773173"}, {"RefNumber": "647697", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "BRSPACE - New tool for Oracle database administration", "RefUrl": "/notes/647697"}, {"RefNumber": "598678", "RefComponent": "BC-DB-ORA", "RefTitle": "Oracle 9i: New functions", "RefUrl": "/notes/598678"}, {"RefNumber": "596423", "RefComponent": "BC-DB-ORA", "RefTitle": "Events and SPFILE", "RefUrl": "/notes/596423"}, {"RefNumber": "1178409", "RefComponent": "BC-DB-ORA", "RefTitle": "ORA-32021: parameter value longer than 255 characters", "RefUrl": "/notes/1178409"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1864212", "RefComponent": "BC-DB-ORA", "RefTitle": "Forcing complete space allocation for temp files", "RefUrl": "/notes/1864212 "}, {"RefNumber": "830576", "RefComponent": "BC-DB-ORA", "RefTitle": "Parameter recommendations for Oracle 10g", "RefUrl": "/notes/830576 "}, {"RefNumber": "948197", "RefComponent": "BC-DB-ORA", "RefTitle": "Merge fix for DBMS_STATS package on Oracle 9.2.x and 10.2.x", "RefUrl": "/notes/948197 "}, {"RefNumber": "1178409", "RefComponent": "BC-DB-ORA", "RefTitle": "ORA-32021: parameter value longer than 255 characters", "RefUrl": "/notes/1178409 "}, {"RefNumber": "647697", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "BRSPACE - New tool for Oracle database administration", "RefUrl": "/notes/647697 "}, {"RefNumber": "598678", "RefComponent": "BC-DB-ORA", "RefTitle": "Oracle 9i: New functions", "RefUrl": "/notes/598678 "}, {"RefNumber": "596423", "RefComponent": "BC-DB-ORA", "RefTitle": "Events and SPFILE", "RefUrl": "/notes/596423 "}, {"RefNumber": "773173", "RefComponent": "BC-DB-ORA", "RefTitle": "Using Oracle Fail Safe cluster on Windows with SPFILE", "RefUrl": "/notes/773173 "}, {"RefNumber": "848708", "RefComponent": "BC-CTS-DTR-SRV", "RefTitle": "Oracle Database Configuration for NWDI server", "RefUrl": "/notes/848708 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": []}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}