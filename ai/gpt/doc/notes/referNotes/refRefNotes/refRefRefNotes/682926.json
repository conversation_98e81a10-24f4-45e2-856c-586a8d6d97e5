{"Request": {"Number": "682926", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 389, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000015569252017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000682926?language=E&token=00C9C8261562505C6C75375B375B52D5"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000682926", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000682926/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "682926"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 21}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Consulting"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "07.07.2011"}, "SAPComponentKey": {"_label": "Component", "value": "BC-DB-ORA"}, "SAPComponentKeyText": {"_label": "Component", "value": "Oracle"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Basis Components", "value": "BC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Database Interface, Database Platforms", "value": "BC-DB", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-DB*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Oracle", "value": "BC-DB-ORA", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-DB-ORA*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "682926 - Composite SAP note: Problems with \"create/rebuild index\""}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>You experience problems with create index or alter index rebuild online.</p> <OL>1. Create/Rebuild online reports the following ORA errors:<br />ORA-08104: this index object &lt;no. x&gt; is being online built or rebuilt<br />ORA-08106: can not create journal table SAPR3.SYS_JOURNAL_&lt;no. x&gt;</OL> <OL>2. A create/rebuild online locks the entire table.<br />A create/rebuild online does not use any CPU resources and appears to hang.</OL> <OL>3. The index rebuild (not necessarily online) destroys the Oracle dictionary.</OL> <OL>4. Corrupt blocks after index tablespace recovery.</OL> <OL>5. ORA-08120: Need to create SYS.IND_ONLINE$ table in order to<br />(re)build indexes</OL> <OL>6. Termination of transactions with:<br />ORA-01632: max # extents (&lt;extents&gt;) reached in index SAPR3.SYS_IOT_TOP_&lt;nr x&gt;</OL> <OL>7. \"library cache lock\" wait situations possible</OL> <OL>8. \"library cache lock\", \"row cache lock\", \"enqueue\" wait situations occur when several index online creates/rebuilds are executed simultaneously on different indexes</OL> <OL>9. You are unable to export index statistics after rebuild or rebuild online.</OL> <OL>10. Queries in tables with indexes that are simultaneously rebuilt online terminate with ORA-01410 (and possibly also with ora-08103).</OL> <OL>11. \"Rebuild online\" takes significantly longer than \"rebuild\"</OL> <OL>12. Corruptions, incomplete data and/or duplicate keys after index online rebuild/create</OL> <OL>13. Global statistics or partitioned indexes are lost during the rebuild (not necessarily online) or are not created during the create index (not necessarily online).</OL> <p><br /></p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>alter index rebuild online problems, problem, bug, bugs, ORA-08104, ORA-08106<br /><br /></p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><OL>1. Create/Rebuild online reports the following ORA errors:<br />ORA-08104: this index object &lt;nr x&gt; is being online built or rebuilt<br />ORA-08106: can not create journal table SAPR3.SYS_JOURNAL_&lt;nr x&gt;<br /><br />A previous create/rebuild online is terminated (ctrl-c in sqlplus in which the index was created/rebuilt, killing the shadow process, etc.). Attempts to rebuild the index again fail with ORA-08104 or ORA-08106.<br />An index is flagged during the \"create/rebuild online\". If the \"create/rebuild online\" terminates, the flag may not be deleted. Further attempts to set up the index again fail with ORA-8104, even though no \"create/rebuild online\" is still running.<br />During a \"create/rebuild online\", temporary objects are created in the database to be able to track the modifications to the table. If the index to be created/rebuilt has the object_id &lt;no x&gt; in dba_objects, then a journal table called SYS_JOURNAL_&lt;no x&gt; exists. In turn, if this has the object_id &lt;no y&gt;, then you will find a corresponding index with the name SYS_IOT_TOP_&lt;no y&gt;.<br />If the journal table still exists after the terminated \"create/rebuild online\", ORA-8106 occurs when you attempt the \"rebuild online\" again.</OL> <OL>2. A create/rebuild online locks the entire table.<br />A create/rebuild online does not use any CPU resources and appears to hang.<br /><br />A create/rebuild online does not mean that Oracle does not set any locks on the tables. Directly after the start and before the end of the create/rebuild, the table is completely locked. If there are no open transactions on the table in question, these locks are only retained for a short time. However, if there are open transactions on the table in question when the lock is requested (TX enqueue) for the create/rebuild, the rebuild waits until all of these transactions are committed. During this wait time, the entire table is already locked so that new modifying transactions must wait for the table.<br />It does not matter whether the index to be created/rebuilt is a unique index.<br /><br />For example: Four sessions are working on the same table<br />1 insert into test values('1');<br />2&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;insert into &lt;table&gt; values('2');<br />3&#x00A0;&#x00A0;&#x00A0;&#x00A0;alter index &lt;index on table&gt; rebuild online;<br />4&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;insert into &lt;table&gt; values('3');<br />1&#x00A0;&#x00A0;commit;<br />2&#x00A0;&#x00A0; commit;<br /><br />The online create/rebuild in session 3 can only start when all previously called open transactions are committed on the table. Session 4 waits for enqueue until the online create/rebuild can be started.<br />In concrete terms, this can mean that a batch job that modifies a table and an online create/rebuild (started after job start) of an index for this table hang all work processes that want to modify the table, until the batch job is completed.<br />If a rebuild online does not consume any CPU resources, first select ST04-&gt;Detailed Analysis-&gt;Oracle Sessions to check whether the corresponding session is waiting for ENQUEUE. In this case, this is probably caused by the phenomenon described above.<br />See Note 869521, which describes workarounds to the above behavior as of Oracle 9.2.0.6.</OL> <OL>3. The index rebuild (not necessarily online) destroys the Oracle dictionary.<br /><br />Oracle Release 8.1.7.x where x is 2 or lower.<br />(Inadvertently) executing the index rebuild for the same index simultaneously in two different sessions (not to be confused with a parallel rebuild in one session). Important: There is an ABAP program that rebuilds indexes. If you incorrectly execute it repeatedly and simultaneously (for example, as a batch job for each client), the Oracle Dictionary may be destroyed. For more detailed information, refer to the note specified in the Solution below.</OL> <OL>4. Corrupt blocks after index tablespace recovery.<br /><br />Indexes of the recovered tablespace were created/rebuilt using the \"nologging\" option. For this reason, the online redo logs only log which blocks are used for the created/rebuilt index, but not their contents. If a file is restored from a backup that was made before the create/rebuild, Oracle will not be able to restore the block contents. Therefore, they are marked as corrupt during the recovery.</OL> <OL>5. ORA-08120: Need to create SYS.IND_ONLINE$ table in order to<br />(re)build indexes<br /><br />As of Patch Set *******, the catcio.sql script must be executed to create object IND_ONLINE$, which is required for the online create/rebuild (see also Note 539921). If the script is forgotten, ORA-08120 occurs.</OL> <OL>6. Termination of transactions with:<br />ORA-01632: max # extents (&lt;extents&gt;) reached in index SAPR3.SYS_IOT_TOP_&lt;nr x&gt;<br /><br />The more data that is changed during a create/rebuild online on the table in question, the larger the temporary journal table SYS_JOURNAL_&lt;no x&gt; and its index SYS_IOT_TOP_&lt;no x&gt; will be. The storage parameters of these segments are determined on the basis of the tablespace default values. If no LMTS are used and the MAXEXTENTS default is set too low, ORA-01632 can occur when a transaction tries to change data in the table in question. This can result in a deactivated update in R/3. The CREATE/REBUILD ONLINE itself is not affected by the error and continues as normal.</OL> <OL>7. \"library cache lock\" wait situations possible<br /><br />If a different DLL statement (such as ALTER or EXPLAIN) is run on the index or the corresponding table at the same time as the CREATE/REBUILD or CREATE/REBUILD ONLINE, \"library cache lock\" locks can occur, which means it is no longer possible to access the corresponding table (even with SELECT).</OL> <OL>8. \"library cache lock\", \"row cache lock\", \"enqueue\" wait situations occur when several index online creates/rebuilds are executed simultaneously on different indexes<br /><br />\"library cache lock\", \"row cache lock\", and \"enqueue\" wait situations during the simultaneous execution of several index online creates/rebuilds on different indexes are caused by unrecognized and therefore irresolvable deadlocks of the objects mentioned.</OL><OL>9. You are unable to export index statistics after rebuild or rebuild online.<br /><br />A rebuild or rebuild online without an implicit or subsequently explicit statistics calculation transfers the statistics of the rebuilt index. As a result of an Oracle bug, you can no longer export these statistics.</OL> <OL>10. Queries in tables with indexes that are simultaneously rebuilt online terminate with ORA-01410 (and possibly also with ora-08103).<br /><br />After the rebuild online, the relevant table should be checked with analyze table &lt;table&gt; validate structure cascade online;<br />to rule out the possibility that there is a corruption. If the analysis is without errors, no further action is required.</OL> <OL>11. \"Rebuild online\" takes significantly longer than \"rebuild\"<br /><br />A rebuild online of an index scans the entire table to rebuild the index. In contrast, a rebuild without online option scans the old index to be rebuilt. The index is usually much smaller and tends to result in more index blocks than table blocks in the DB cache. Therefore, significantly more IO must generally be made with a rebuild online, which leads to a longer runtime.</OL> <OL>12. Corruptions, incomplete data and/or duplicate keys after index online rebuild/create</OL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;A rebuild/create index online may cause the index to have fewer entries than the table. This in turn leads to too few rows during a select, terminations during deletes, or updates or duplicate keys, which are noticed only when the index is rebuilt at a later stage. <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;More information about this bug, which is published as Hot News, is available in Note 1413928 for Oracle 10g: \"Index corruption/wrong results after rebuild index ONLINE\". <OL>13. Global statistics or partitioned indexes are lost during the rebuild (not necessarily online) or are not created during the create index (not necessarily online).</OL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Oracle classifies this behavior as works as designed.<br /> <p><br /></p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><OL>1. Create/Rebuild online reports the following ORA errors:<br />ORA-08104: this index object &lt;nr x&gt; is being online built or rebuilt<br />ORA-08106: can not create journal table SAPR3.SYS_JOURNAL_&lt;nr x&gt;<br /><br />This is corrected with Oracle *******. As of this patch set, the SMON cleans up the allocated resources by searching once an hour for fragments of interrupted creates/rebuilds. If there is an open transaction on the respective table at the time the search is performed, the fragments are not cleaned up. As of Oracle 10.2, an explicit clean-up attempt using<br /><br />set serveroutput on<br />begin<br />&#x00A0;&#x00A0;if dbms_repair.online_index_clean() then<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;dbms_output.put_line('All fragments cleaned');<br />&#x00A0;&#x00A0;else<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;dbms_output.put_line('DML Locks on some tables.');<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;dbms_output.put_line('Not all fragments cleaned. ');<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;dbms_output.put_line('Try again later');<br />&#x00A0;&#x00A0;end if;<br />end;<br />/<br /><br />can be activated.<br />If you experience the problem in a release older than 9204, open a message to correct the situation as described in internal Note 685004.</OL> <OL>2. A create/rebuild online locks the entire table.<br />A create/rebuild online does not use any CPU resources and appears to hang.<br /><br />Oracle does not consider this phenomenon to be a bug. Therefore, a \"create/rebuild online\" should only be started if the table in question is not being modified when you start the create/rebuild. The same problem can also occur when the rebuild is completed, although here it is more difficult to tell whether modifications made to the table have not been committed for any length of time. The batch jobs that modify the table may have to have been completed by the estimated end of the create/rebuild online.</OL> <OL>3. The index rebuild (not necessarily online) destroys the Oracle dictionary.<br /><br />For more information, see Note 498735.</OL> <OL>4. Corrupt blocks after index tablespace recovery.<br /><br />This is the intended behavior, and not a bug. Rebuild the indexes and, if necessary, eliminate any remaining freespace corruptions (Note 354293). If you want to restore and recover a backup that was made after the rebuild using \"nologging\", no blocks are marked as corrupt. For more information, see Note 547464.</OL> <OL>5. ORA-08120: Need to create SYS.IND_ONLINE$ table in order to<br />(re)build indexes<br /><br />You must repeat the import of the script catcio.sql and check that all other actions from Note 539921 have been executed correctly.</OL> <OL>6. Termination of transactions with:<br />ORA-01632: max # extents (&lt;extents&gt;) reached in index SAPR3.SYS_IOT_TOP_&lt;nr x&gt;<br /><br />Before the CREATE/REBUILD ONLINE of large indexes on change-intensive tables, check whether the tablespace defaults are set large enough to accommodate the expected volume of changes to the table:<br /><br />SELECT NEXT_EXTENT, MAX_EXTENTS FROM DBA_TABLESPACES<br />WHERE TABLESPACE_NAME = '&lt;tablespace&gt;';</OL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;To be on the safe side, you can set the NEXT value to 20M and the MAXEXTENTS value to 1000:<br /><br />ALTER TABLESPACE &lt;tablespace&gt; DEFAULT STORAGE (NEXT 20M MAXEXTENTS 1000); <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;In the mean time, you can also transfer to LMTS to avoid problems (See Note 214995). <OL>7. \"library cache lock\" wait situations possible<br /><br />Avoid other DLL operations at the same time as the create/rebuild. Also note the \"library cache lock\" situation described in Note 619188.</OL> <OL>8. \"library cache lock\", \"row cache lock\", \"enqueue\" wait situations occur when several index online creates/rebuilds are executed simultaneously on different indexes<br /><br />See Note 904188.</OL> <OL>9. You are unable to export index statistics after rebuild or rebuild online.<br /><br />See Note 1095171.</OL> <OL>10. Queries in tables with indexes that are simultaneously rebuilt online terminate with ORA-01410 (and possibly also with ora-08103).<br /><br />After the rebuild online, the relevant table should be checked with analyze table &lt;table&gt; validate structure cascade online;<br />to rule out the possibility that there is a corruption. If there are no errors in the analysis, for further clarification, open a message under BC-DB-ORA because there is a bug or behavior that requires explanation.</OL> <OL>11. \"Rebuild online\" takes significantly longer than \"rebuild\"<br /><br />This system response is correct. In individual cases, you must decide which has higher priority: No lock of the entire table during the online rebuild of the index or a shorter runtime of the index rebuild without online option.</OL> <OL>12. Corruptions, incomplete data and/or duplicate keys after index online rebuild/create</OL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Proceed as described in Note 1413928 for Oracle 10g: \"Index corruption/wrong results after rebuild index ONLINE\". <OL>13. Global statistics or partitioned indexes are lost during the rebuild (not necessarily online) or are not created during the create index (not necessarily online).</OL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Use brconnect to create explicitly new statistics. <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;</div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "BW-SYS-DB-ORA (BW ORACLE)"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON> (D021978)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON> (D021978)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "J", "TranslationLanguage": "日本語", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000682926/J"}, {"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000682926/D"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000682926/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000682926/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000682926/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000682926/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000682926/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000682926/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000682926/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "915242", "RefComponent": "BC-DB-ORA", "RefTitle": "FAQ: Reverse key indexes", "RefUrl": "/notes/915242"}, {"RefNumber": "869521", "RefComponent": "BC-DB-ORA", "RefTitle": "Oracle <= 10g: TM locks with REBUILD ONLINE / CREATE ONLINE", "RefUrl": "/notes/869521"}, {"RefNumber": "825653", "RefComponent": "BC-DB-ORA", "RefTitle": "Oracle: Popular misconceptions", "RefUrl": "/notes/825653"}, {"RefNumber": "806554", "RefComponent": "BC-DB-ORA", "RefTitle": "FAQ: I/O-intensive database operations", "RefUrl": "/notes/806554"}, {"RefNumber": "745639", "RefComponent": "BC-DB-ORA", "RefTitle": "FAQ: Oracle enqueues", "RefUrl": "/notes/745639"}, {"RefNumber": "691764", "RefComponent": "BC-DB-ORA", "RefTitle": "RSANAORA enhancement: online option for index rebuild", "RefUrl": "/notes/691764"}, {"RefNumber": "539921", "RefComponent": "BC-DB-ORA", "RefTitle": "Current patch set for Oracle 9.2.0", "RefUrl": "/notes/539921"}, {"RefNumber": "332677", "RefComponent": "BC-DB-ORA", "RefTitle": "Rebuilding fragmented indexes", "RefUrl": "/notes/332677"}, {"RefNumber": "214995", "RefComponent": "BC-DB-ORA", "RefTitle": "Oracle locally managed tablespaces in the SAP environment", "RefUrl": "/notes/214995"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "745639", "RefComponent": "BC-DB-ORA", "RefTitle": "FAQ: Oracle enqueues", "RefUrl": "/notes/745639 "}, {"RefNumber": "332677", "RefComponent": "BC-DB-ORA", "RefTitle": "Rebuilding fragmented indexes", "RefUrl": "/notes/332677 "}, {"RefNumber": "806554", "RefComponent": "BC-DB-ORA", "RefTitle": "FAQ: I/O-intensive database operations", "RefUrl": "/notes/806554 "}, {"RefNumber": "825653", "RefComponent": "BC-DB-ORA", "RefTitle": "Oracle: Popular misconceptions", "RefUrl": "/notes/825653 "}, {"RefNumber": "869521", "RefComponent": "BC-DB-ORA", "RefTitle": "Oracle <= 10g: TM locks with REBUILD ONLINE / CREATE ONLINE", "RefUrl": "/notes/869521 "}, {"RefNumber": "214995", "RefComponent": "BC-DB-ORA", "RefTitle": "Oracle locally managed tablespaces in the SAP environment", "RefUrl": "/notes/214995 "}, {"RefNumber": "834118", "RefComponent": "BC-MID-ALE", "RefTitle": "RBDCPIDXRE in building online indexes", "RefUrl": "/notes/834118 "}, {"RefNumber": "691764", "RefComponent": "BC-DB-ORA", "RefTitle": "RSANAORA enhancement: online option for index rebuild", "RefUrl": "/notes/691764 "}, {"RefNumber": "915242", "RefComponent": "BC-DB-ORA", "RefTitle": "FAQ: Reverse key indexes", "RefUrl": "/notes/915242 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": []}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}