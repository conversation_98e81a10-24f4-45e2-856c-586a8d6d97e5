{"Request": {"Number": "938986", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 916, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000016086482017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000938986?language=E&token=D5DCF4B955223EB7084927D2217C7AB2"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000938986", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000938986/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "938986"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 19}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Program error"}, "Priority": {"_label": "Priority", "value": "Correction with high priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "07.05.2008"}, "SAPComponentKey": {"_label": "Component", "value": "BC-DB-ORA"}, "SAPComponentKeyText": {"_label": "Component", "value": "Oracle"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Basis Components", "value": "BC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Database Interface, Database Platforms", "value": "BC-DB", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-DB*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Oracle", "value": "BC-DB-ORA", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-DB-ORA*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "938986 - Oracle Database 9.2: <PERSON><PERSON> for 9.2.0"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>This note contains information about which patch sets and patches you must install as a minimum requirement for Oracle Database Release 9.2.0.<br /><br />The individual patches, which are listed below as required patches, are available for UNIX and Linux platforms only.<br />For Windows platforms, check the relevant note to see which mini patch contains the bug fix.<br /><br />Note 539921 tells you which patch set for Oracle 9.2.0 or which Oracle mini patch for Windows is released for use in the SAP environment.<br /><br /><B><U>Change history</U></B><br /><br /><U>Date</U>  <U>Change</U><br /><br />22.06.2007 #18. Note 1067939 added<br />25.06.2007 #19. Note 1068391 added<br />29.11.2007 #01. CPUOct2007 updated for *******<br />03.12.2007 #20. Note 1120875 / patch 5057695 added<br />21.02.2008 #21. Note 1136063 / patch 5386204 added<br />24.04.2008 #22. Note 1144178 / patch 6826661 added<br />07.05.2008 #01. CPUJan2008 updated for *******<br /><br /><br /></p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>*******, *******, Oracle, patch, patches, patch set</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p></p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p></p> <b>Oracle Patches and Patch Sets on SAP Service Marketplace</b><br /> <p>Oracle Database 9.2.0 patch sets and patches are available on SAP Service Marketplace and can be downloaded from there. See Note 539921.<br /></p> <b>Oracle server patches for Oracle Release ******* (UNIX)</b><br /> <p><br />You must apply the following patches.<br />If a patch is not available for your platform, implement the workaround described in the relevant note (if available).<br /><br />1.&#x00A0;&#x00A0;Note 965912: Patch 5225794 <B>(*M)</B><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;CPU Patches July 2006 (Oracle Critical Update Program)<br /><br />2.&#x00A0;&#x00A0;Note 901313, Patch 4029101<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;ORA-60 Deadlock during dictionary operations<br /><br />3.&#x00A0;&#x00A0;Note 884659, Patch 4192148<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;Incorrect view definition in DBA_VIEWS w/ patch set *******<br /><br />4.&#x00A0;&#x00A0;Note 983230, patches 5464555 / 5032137 / 4288876 <B>(*M)</B><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;LOB corruption for NOCACHE-LOBs in ASSM (ORA-1555/ORA-2292)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;This patch solves patch 5032137/428876, Note 912918<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; Massive UNDO/REDO generation when you use ASSM<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;For this reason, the obsolete patches on SAP Service<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;Marketplace have been deleted.<br /><br />5.&#x00A0;&#x00A0;Note 929560, Patch 4487610<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;ORA-00600 [13009] occurs with SELECT FOR UPDATE on Oracle 9.2<br /><br />6.&#x00A0;&#x00A0;Note 929560, Patch 3901785<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;ORA-00600 [13009] occurs with SELECT FOR UPDATE on Oracle 9.2<br /><br />7.&#x00A0;&#x00A0;Note 898244, Patch 4627335<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;ORA-904 with the DBMS_STATS package<br /><br />8.&#x00A0;&#x00A0;Note 894078, patches 4947798 / 4658188 <B>(*M)</B><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;*******: Long waits for KSU PROCESS ALLOC LATCH YIELD<br /><br />9.&#x00A0;&#x00A0;Note 896903, patches 5123487 / 4939797 <B>(*M)</B><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;Optimizer merge fix for Oracle *******<br /><br />10. Note 950658, Patch 4942939<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;Huge values shown in READTIM, WRITETIM of V$FILESTAT<br /><br />11. Note 967915, patches 5087349 / 3396162 <B>(*M)</B><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;ORA-3106 possible on *******<br /><br />12. Note 932251, Patch 4542620<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;ARCH no longer archives after full file system<br /><br />13. Note 945735, Patch 4581385<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;ORA-600 [kcbbxsv_2] during ALTER DATABASE DATAFILE ... OFFLINE<br /><br />14. Note 980555, Patch 2965960<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;ORA 600 [kdtdelrow-2] during insert with deadlock<br /><br />15. Note 980152, Patch 5496862 <B>(*P)</B> (only AIX 5L)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;DB reports I/O error after installing AIX 5.3 TL 5 (5300-05)<br /><br /><B>(*M)</B> 'Merge Patch'<br /><B>(*P)</B> Port-specific patch<br /><br /></p> <b>Oracle server patches for Oracle Release ******* (UNIX)</b><br /> <p><br />1.&#x00A0;&#x00A0;Note 1140644: Patch 664842 (*M)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;CPU patch January 2008 (Oracle Critical Update Program)<br /><br />2.&#x00A0;&#x00A0;Note 1037600, Patch 3755693<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;Oracle *******: Data is not selected using index skip scan<br /><br />3.&#x00A0;&#x00A0;Note 896717, Patch 4660718<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;Table VBDATA grows despite ASSM tablespace<br /><br />4.&#x00A0;&#x00A0;Note 1038659, Patch 5345999<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;ORA-600[17147] during SELECT on V$SPPARAMETER<br /><br />5.&#x00A0;&#x00A0;Note 971261, Patch 5369855<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;Oracle 9.2 / 10.2: Hanging queries with STAR_TRANSFORMATION<br /><br />6.&#x00A0;&#x00A0;Note 1045320, Patch 5442919<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;ORA-30036 despite high number expired/unexpired undo extents<br /><br />7.&#x00A0;&#x00A0;Note 980152, Patch 5496862 (only AIX 5L) <B>(*P)</B><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;DB reports I/O error after installing AIX 5.3 TL 5 (5300-05)<br /><br />8.&#x00A0;&#x00A0;Note 1025537, Patch 5508574<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;ORA-600[504] if cpu_count is higher than 31<br /><br />9.&#x00A0;&#x00A0;Note 1028099, Patch 5530958<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;Long import runtimes for large db_cache_size<br /><br />10.&#x00A0;&#x00A0;Note 992261, Patch 5977665 or 5562159,<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;if Patch 5977665 is not yet available <B>(*M)</B><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;Optimizer merge fix for Oracle *******<br /><br />11. Note 1007708 or 1017418, Patch 5660451<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;LS_LOADSTREAM: unexpect. Cvtret when -loadprocedure fast<br /><br />12. Note 1052003, Patch 5842790<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;ORA-00947 in connection w/ STAR_TRANSFORMATION_ENABLED=TRUE<br /><br />13. Note 948197, Patch 5915066 <B>(*M) (*G)</B><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;Merge fix for DBMS_STATS package on Oracle 9.2.x and 10.2.x<br /><br />14. Note 1043381, Patch 5985053<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;Long update runtimes if index located in ASSM tablespace<br /><br />15. Note 1037755, Patch 5728380<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;Performance problems with ASSM tablespaces<br /><br />16. Note 932251, Patch 4542620<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;ARCH no longer archives after full file system<br /><br />17. Note 1064644, Patch 6047085 <B>(*P)</B><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;SGA Corruption or Missing Tracefiles on Linux x86-64 in 9208<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;This patch is only relevant for the Linux x86-64 platform.<br /><br />18. Note 1067939, Patch 5532656 <B>(*P)</B><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;Invalid Object utl_file after upgrade to ******* on HP-UX<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;This patch is only relevant for the HP-UX PA-RISC 64-bit platform.<br /><br />19. Note 1068391, Patch 6139605 <B>(*G)</B><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;TIMESTAMP values off by one hour on SELECT via JDBC<br /><br />20. Note 1120875, Patch 5057695<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;After Oracle *******, shutdown immediate may take longer.<br /><br />21. Note 1136063, Patch 5386204<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;Corruptions in parallel direct patch load operations.<br /><br />22. Note 1144178, Patch 6826661<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;Invalid float values<br /><br /><br /><br /><B>(*M) </B> 'Merge Patch'<br /><B>(*P)</B> Port-specific patch<br /><B>(*G)</B> Generic patch<br /><br /><br /></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "BW-SYS-DB-ORA (BW ORACLE)"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON> (C5016411)"}, {"Key": "Processor                                                                                           ", "Value": "C5000541"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000938986/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000938986/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000938986/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000938986/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000938986/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000938986/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000938986/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000938986/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000938986/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "992261", "RefComponent": "BC-DB-ORA", "RefTitle": "Optimizer merge fix for Oracle *******", "RefUrl": "/notes/992261"}, {"RefNumber": "983230", "RefComponent": "BC-DB-ORA", "RefTitle": "LOB corruption for NOCACHE LOBs in ASSM (ORA-1555/ORA-22924)", "RefUrl": "/notes/983230"}, {"RefNumber": "980555", "RefComponent": "BC-DB-ORA", "RefTitle": "ORA 600 [kdtdelrow-2] during insert with deadlock", "RefUrl": "/notes/980555"}, {"RefNumber": "980152", "RefComponent": "BC-DB-ORA", "RefTitle": "DB reports I/O error after inst of AIX 5.3 TL 05, 06, 07, 08", "RefUrl": "/notes/980152"}, {"RefNumber": "967915", "RefComponent": "BC-DB-ORA", "RefTitle": "ORA-3106 possible on *******", "RefUrl": "/notes/967915"}, {"RefNumber": "950658", "RefComponent": "BC-DB-ORA", "RefTitle": "Huge values shown in READTIM, WRITETIM of V$FILESTAT", "RefUrl": "/notes/950658"}, {"RefNumber": "949339", "RefComponent": "BC-DB-ORA", "RefTitle": "CPU patches April 2006 (Oracle Critical Update program)", "RefUrl": "/notes/949339"}, {"RefNumber": "932251", "RefComponent": "BC-DB-ORA", "RefTitle": "ARCH no longer archives after full file system", "RefUrl": "/notes/932251"}, {"RefNumber": "929560", "RefComponent": "BC-DB-ORA", "RefTitle": "ORA-00600[13009] occurs w/ SELECT FOR UPDATE on Oracle 9/10", "RefUrl": "/notes/929560"}, {"RefNumber": "920781", "RefComponent": "BC-DB-ORA", "RefTitle": "CPU Patches January 2006 (Oracle Critical Update Program)", "RefUrl": "/notes/920781"}, {"RefNumber": "912918", "RefComponent": "BC-DB-ORA", "RefTitle": "Massive UNDO/REDO generation when you use ASSM", "RefUrl": "/notes/912918"}, {"RefNumber": "901313", "RefComponent": "BC-DB-ORA", "RefTitle": "ORA-60 deadlock during dictionary operations", "RefUrl": "/notes/901313"}, {"RefNumber": "898244", "RefComponent": "BC-DB-ORA", "RefTitle": "ORA-904 with the DBMS_STATS package", "RefUrl": "/notes/898244"}, {"RefNumber": "896903", "RefComponent": "BC-DB-ORA", "RefTitle": "Optimizer merge fix for Oracle *******", "RefUrl": "/notes/896903"}, {"RefNumber": "894078", "RefComponent": "BC-DB-ORA", "RefTitle": "*******: Long waits for KSU PROCESS ALLOC LATCH YIELD", "RefUrl": "/notes/894078"}, {"RefNumber": "886783", "RefComponent": "BC-DB-ORA", "RefTitle": "Installing Oracle 9207 Client Software on UNIX", "RefUrl": "/notes/886783"}, {"RefNumber": "884659", "RefComponent": "BC-DB-ORA", "RefTitle": "Incorrect view definition in DBA_VIEWS w/ Patch Set *******", "RefUrl": "/notes/884659"}, {"RefNumber": "645735", "RefComponent": "CRM-MD-BP-IF", "RefTitle": "Two BUPA_REL after insert of Contact in CDB", "RefUrl": "/notes/645735"}, {"RefNumber": "592393", "RefComponent": "BC-DB-ORA", "RefTitle": "FAQ: Oracle", "RefUrl": "/notes/592393"}, {"RefNumber": "1178120", "RefComponent": "BC-DB-ORA", "RefTitle": "Oracle Patchnumbers on Windows Platforms", "RefUrl": "/notes/1178120"}, {"RefNumber": "1120875", "RefComponent": "BC-DB-ORA", "RefTitle": "Shutdown immediate may take a long time > Oracle 9208/10202", "RefUrl": "/notes/1120875"}, {"RefNumber": "1068391", "RefComponent": "BC-DB-ORA", "RefTitle": "TIMESTAMP values off by one hour on SELECT via JDBC", "RefUrl": "/notes/1068391"}, {"RefNumber": "1067939", "RefComponent": "BC-DB-ORA", "RefTitle": "Invalid Object utl_file after upgrade to ******* on HP-UX", "RefUrl": "/notes/1067939"}, {"RefNumber": "1064644", "RefComponent": "BC-DB-ORA", "RefTitle": "SGA Corruption or Missing Tracefiles on Linux x86-64 in 9208", "RefUrl": "/notes/1064644"}, {"RefNumber": "1037755", "RefComponent": "BC-DB-ORA", "RefTitle": "Performance problems with ASSM tablespaces", "RefUrl": "/notes/1037755"}, {"RefNumber": "1005179", "RefComponent": "BW-SYS-DB-ORA", "RefTitle": "Performance problems with BW Queries with Oracle 9.2", "RefUrl": "/notes/1005179"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "592393", "RefComponent": "BC-DB-ORA", "RefTitle": "FAQ: Oracle", "RefUrl": "/notes/592393 "}, {"RefNumber": "1178120", "RefComponent": "BC-DB-ORA", "RefTitle": "Oracle Patchnumbers on Windows Platforms", "RefUrl": "/notes/1178120 "}, {"RefNumber": "980152", "RefComponent": "BC-DB-ORA", "RefTitle": "DB reports I/O error after inst of AIX 5.3 TL 05, 06, 07, 08", "RefUrl": "/notes/980152 "}, {"RefNumber": "950658", "RefComponent": "BC-DB-ORA", "RefTitle": "Huge values shown in READTIM, WRITETIM of V$FILESTAT", "RefUrl": "/notes/950658 "}, {"RefNumber": "886783", "RefComponent": "BC-DB-ORA", "RefTitle": "Installing Oracle 9207 Client Software on UNIX", "RefUrl": "/notes/886783 "}, {"RefNumber": "1120875", "RefComponent": "BC-DB-ORA", "RefTitle": "Shutdown immediate may take a long time > Oracle 9208/10202", "RefUrl": "/notes/1120875 "}, {"RefNumber": "983230", "RefComponent": "BC-DB-ORA", "RefTitle": "LOB corruption for NOCACHE LOBs in ASSM (ORA-1555/ORA-22924)", "RefUrl": "/notes/983230 "}, {"RefNumber": "1005179", "RefComponent": "BW-SYS-DB-ORA", "RefTitle": "Performance problems with BW Queries with Oracle 9.2", "RefUrl": "/notes/1005179 "}, {"RefNumber": "1068391", "RefComponent": "BC-DB-ORA", "RefTitle": "TIMESTAMP values off by one hour on SELECT via JDBC", "RefUrl": "/notes/1068391 "}, {"RefNumber": "1067939", "RefComponent": "BC-DB-ORA", "RefTitle": "Invalid Object utl_file after upgrade to ******* on HP-UX", "RefUrl": "/notes/1067939 "}, {"RefNumber": "1064644", "RefComponent": "BC-DB-ORA", "RefTitle": "SGA Corruption or Missing Tracefiles on Linux x86-64 in 9208", "RefUrl": "/notes/1064644 "}, {"RefNumber": "992261", "RefComponent": "BC-DB-ORA", "RefTitle": "Optimizer merge fix for Oracle *******", "RefUrl": "/notes/992261 "}, {"RefNumber": "932251", "RefComponent": "BC-DB-ORA", "RefTitle": "ARCH no longer archives after full file system", "RefUrl": "/notes/932251 "}, {"RefNumber": "1037755", "RefComponent": "BC-DB-ORA", "RefTitle": "Performance problems with ASSM tablespaces", "RefUrl": "/notes/1037755 "}, {"RefNumber": "980555", "RefComponent": "BC-DB-ORA", "RefTitle": "ORA 600 [kdtdelrow-2] during insert with deadlock", "RefUrl": "/notes/980555 "}, {"RefNumber": "929560", "RefComponent": "BC-DB-ORA", "RefTitle": "ORA-00600[13009] occurs w/ SELECT FOR UPDATE on Oracle 9/10", "RefUrl": "/notes/929560 "}, {"RefNumber": "901313", "RefComponent": "BC-DB-ORA", "RefTitle": "ORA-60 deadlock during dictionary operations", "RefUrl": "/notes/901313 "}, {"RefNumber": "884659", "RefComponent": "BC-DB-ORA", "RefTitle": "Incorrect view definition in DBA_VIEWS w/ Patch Set *******", "RefUrl": "/notes/884659 "}, {"RefNumber": "912918", "RefComponent": "BC-DB-ORA", "RefTitle": "Massive UNDO/REDO generation when you use ASSM", "RefUrl": "/notes/912918 "}, {"RefNumber": "967915", "RefComponent": "BC-DB-ORA", "RefTitle": "ORA-3106 possible on *******", "RefUrl": "/notes/967915 "}, {"RefNumber": "898244", "RefComponent": "BC-DB-ORA", "RefTitle": "ORA-904 with the DBMS_STATS package", "RefUrl": "/notes/898244 "}, {"RefNumber": "894078", "RefComponent": "BC-DB-ORA", "RefTitle": "*******: Long waits for KSU PROCESS ALLOC LATCH YIELD", "RefUrl": "/notes/894078 "}, {"RefNumber": "949339", "RefComponent": "BC-DB-ORA", "RefTitle": "CPU patches April 2006 (Oracle Critical Update program)", "RefUrl": "/notes/949339 "}, {"RefNumber": "896903", "RefComponent": "BC-DB-ORA", "RefTitle": "Optimizer merge fix for Oracle *******", "RefUrl": "/notes/896903 "}, {"RefNumber": "920781", "RefComponent": "BC-DB-ORA", "RefTitle": "CPU Patches January 2006 (Oracle Critical Update Program)", "RefUrl": "/notes/920781 "}, {"RefNumber": "645735", "RefComponent": "CRM-MD-BP-IF", "RefTitle": "Two BUPA_REL after insert of Contact in CDB", "RefUrl": "/notes/645735 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": []}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}