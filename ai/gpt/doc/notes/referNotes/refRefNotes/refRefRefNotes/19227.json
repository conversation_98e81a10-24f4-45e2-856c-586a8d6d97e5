{"Request": {"Number": "19227", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 332, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000014339772017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000019227?language=E&token=8A10FC4368D48AFC48FD327C78AFE188"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000019227", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000019227/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "19227"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 54}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Installation information"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "14.07.2016"}, "SAPComponentKey": {"_label": "Component", "value": "BC-CCM-MON-OS"}, "SAPComponentKeyText": {"_label": "Component", "value": "Operating System Monitoring Tool"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Basis Components", "value": "BC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Computer Center Management System (CCMS)", "value": "BC-CCM", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-CCM*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "CCMS Monitoring & Alerting", "value": "BC-CCM-MON", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-CCM-MON*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Operating System Monitoring Tool", "value": "BC-CCM-MON-OS", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-CCM-MON-OS*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "19227 - Retrieving the latest saposcol"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>There is incorrect data in ST06, OS07N, OS07, or in the 'Operating System' monitor of transaction RZ20, or via web service methods of SAPOSCOL.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>CCMS agents, SapHostAgent</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>Incorrect data in one of the above monitors may have different causes. For example, an obsolete version of the SAPOSCOL operating system collector may cause incorrect or incomplete data. SAPOSCOL is a standalone program. It can be installed and exchanged independently of the SAP System. Always use a current version of SAPOSCOL, if possible.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p>Install a new version of SAPHOSTAGENT.SAR.<br /><br />Saposcol is part of the package SAPHOSTAGENT.SAR. See SAP Note 1031096 for installation information.</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Transaction codes", "Value": "HIER"}, {"Key": "Transaction codes", "Value": "SM59"}, {"Key": "Transaction codes", "Value": "RZ20"}, {"Key": "Transaction codes", "Value": "ST06"}, {"Key": "Transaction codes", "Value": "OS07"}, {"Key": "Transaction codes", "Value": "OS06"}, {"Key": "Transaction codes", "Value": "AL17"}, {"Key": "Transaction codes", "Value": "AL19"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON> (I065580)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (I065580)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "J", "TranslationLanguage": "日本語", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000019227/J"}, {"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000019227/D"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000019227/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000019227/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000019227/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000019227/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000019227/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000019227/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000019227/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "961512", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/961512"}, {"RefNumber": "961410", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info. on upgrading to SAP ECC 6.0 SR2 ABAP", "RefUrl": "/notes/961410"}, {"RefNumber": "936887", "RefComponent": "BC-OP-LNX", "RefTitle": "End of maintenance for Linux distributions", "RefUrl": "/notes/936887"}, {"RefNumber": "925240", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/925240"}, {"RefNumber": "91488", "RefComponent": "SV-SMG-SER", "RefTitle": "SAP Support Services - Central preparatory note", "RefUrl": "/notes/91488"}, {"RefNumber": "913971", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info. on upgrading to SAP ERP Central Component 6.0 SR1", "RefUrl": "/notes/913971"}, {"RefNumber": "912905", "RefComponent": "BC-DB-SDB", "RefTitle": "FAQ: Storage systems used with SAP MaxDB", "RefUrl": "/notes/912905"}, {"RefNumber": "826093", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/826093"}, {"RefNumber": "826092", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info. on upgrading to SAP ERP Central Component 6.0", "RefUrl": "/notes/826092"}, {"RefNumber": "790639", "RefComponent": "BC-CCM-MON-OS", "RefTitle": "SAPOSCOL and ABAP server: Monitoring processes", "RefUrl": "/notes/790639"}, {"RefNumber": "788218", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/788218"}, {"RefNumber": "787586", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/787586"}, {"RefNumber": "786509", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/786509"}, {"RefNumber": "775047", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info. on upgrading to SAP Web AS 6.40 SR1", "RefUrl": "/notes/775047"}, {"RefNumber": "737800", "RefComponent": "BC-UPG-RDM", "RefTitle": "Enhancements to upgrade on SAP CRM 4.0 SR1", "RefUrl": "/notes/737800"}, {"RefNumber": "737696", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/737696"}, {"RefNumber": "727683", "RefComponent": "BC-DB-SDB", "RefTitle": "SAP kernel 6.x MaxDB: Released operating systems", "RefUrl": "/notes/727683"}, {"RefNumber": "726094", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/726094"}, {"RefNumber": "710975", "RefComponent": "BC-OP-AIX", "RefTitle": "FAQ: which saposcol should be used on AIX", "RefUrl": "/notes/710975"}, {"RefNumber": "688131", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/688131"}, {"RefNumber": "684406", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/684406"}, {"RefNumber": "661640", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info. on upgrading to SAP Web AS 6.40", "RefUrl": "/notes/661640"}, {"RefNumber": "657164", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/657164"}, {"RefNumber": "637683", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/637683"}, {"RefNumber": "603852", "RefComponent": "BC-UPG-RDM", "RefTitle": "Additional information about upgrading to SAP SCM 4.0", "RefUrl": "/notes/603852"}, {"RefNumber": "591441", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/591441"}, {"RefNumber": "587896", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/587896"}, {"RefNumber": "548699", "RefComponent": "BC-CCM-MON-OS", "RefTitle": "FAQ: OS collector SAPOSCOL", "RefUrl": "/notes/548699"}, {"RefNumber": "542768", "RefComponent": "BC-CCM-MON-OS", "RefTitle": "Filesystem filter & process monitor broken on AIX saposcol", "RefUrl": "/notes/542768"}, {"RefNumber": "541936", "RefComponent": "BC-CCM-MON-OS", "RefTitle": "4.6D saposcol for AIX based on perfstat library", "RefUrl": "/notes/541936"}, {"RefNumber": "485866", "RefComponent": "BC-OP-FTS-SOL", "RefTitle": "saposcol does not display sfxfs- and sfcfs-filesystems", "RefUrl": "/notes/485866"}, {"RefNumber": "485827", "RefComponent": "BC-OP-FTS-SOL", "RefTitle": "Solaris saposcol terminates after a few days", "RefUrl": "/notes/485827"}, {"RefNumber": "484876", "RefComponent": "BC-UPG-RDM", "RefTitle": "Additional information about upgrading to SAP Web AS 6.20", "RefUrl": "/notes/484876"}, {"RefNumber": "445404", "RefComponent": "BC-CCM-MON-OS", "RefTitle": "TABLE_INVALID_INDEX in SAPLSMON", "RefUrl": "/notes/445404"}, {"RefNumber": "435140", "RefComponent": "BC-UPG-RDM", "RefTitle": "Additional information on upgrading to Basis 4.6D (APO 3.1x", "RefUrl": "/notes/435140"}, {"RefNumber": "420371", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info on upgrading to Basis 4.6C SR2 (NDI upgrade)", "RefUrl": "/notes/420371"}, {"RefNumber": "410783", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/410783"}, {"RefNumber": "407328", "RefComponent": "XX-SER-REL", "RefTitle": "SAP Kernel 6.x MS SQL Server: Released operating systems", "RefUrl": "/notes/407328"}, {"RefNumber": "407325", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/407325"}, {"RefNumber": "407322", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/407322"}, {"RefNumber": "407320", "RefComponent": "BC-DB-SDB", "RefTitle": "Released operating systems SAP R/3 kernel 6.x SAP DB", "RefUrl": "/notes/407320"}, {"RefNumber": "407317", "RefComponent": "BC-DB-INF", "RefTitle": "Released operating systems for SAP kernel 6.x INFORMIX", "RefUrl": "/notes/407317"}, {"RefNumber": "407314", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/407314"}, {"RefNumber": "401717", "RefComponent": "BC-UPG-RDM", "RefTitle": "Additional info on upgrading to SAP Web AS 6.10 (Basis)", "RefUrl": "/notes/401717"}, {"RefNumber": "390062", "RefComponent": "BC-UPG-RDM", "RefTitle": "Additional information about upgrading to 4.6C SR2", "RefUrl": "/notes/390062"}, {"RefNumber": "351876", "RefComponent": "BC-OP-TRU64", "RefTitle": "SAPOSCOL for Compaq Tru64 5.x", "RefUrl": "/notes/351876"}, {"RefNumber": "335029", "RefComponent": "BC-UPG-RDM", "RefTitle": "Additions to the upgrade to Basis 4.6D (NDI Upgrade)", "RefUrl": "/notes/335029"}, {"RefNumber": "327285", "RefComponent": "BC-UPG-RDM", "RefTitle": "Additions to upgrade to 4.6C SR1", "RefUrl": "/notes/327285"}, {"RefNumber": "302309", "RefComponent": "BC-UPG-RDM", "RefTitle": "Additions Workplace Server Upgrade to Rel. 2.10/2.11", "RefUrl": "/notes/302309"}, {"RefNumber": "192949", "RefComponent": "BC-UPG-RDM", "RefTitle": "Additional info for upgrade to 4.6C", "RefUrl": "/notes/192949"}, {"RefNumber": "192217", "RefComponent": "BC-CCM-MON-OS", "RefTitle": "DB12 (NT):  Incorrect values for large freespace", "RefUrl": "/notes/192217"}, {"RefNumber": "189072", "RefComponent": "BC-CCM-MON", "RefTitle": "ST06/OS06: No data or incorrect data displayed", "RefUrl": "/notes/189072"}, {"RefNumber": "179373", "RefComponent": "BC-UPG-RDM", "RefTitle": "Additional info for upgrade to Rel.4.6B", "RefUrl": "/notes/179373"}, {"RefNumber": "178248", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/178248"}, {"RefNumber": "173353", "RefComponent": "BC-CCM-MON-OS", "RefTitle": "COMPUTE_INT_PLUS_OVERFLOW with Transaction OS07", "RefUrl": "/notes/173353"}, {"RefNumber": "168439", "RefComponent": "XX-SER-TCC-EW", "RefTitle": "Preparándose para una sesión de Early Watch ó GL", "RefUrl": "/notes/168439"}, {"RefNumber": "1666481", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1666481"}, {"RefNumber": "162980", "RefComponent": "BC-CCM-MON-OS", "RefTitle": "saposcol for 64-bit Solaris", "RefUrl": "/notes/162980"}, {"RefNumber": "1611933", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1611933"}, {"RefNumber": "156558", "RefComponent": "XX-SER-REL", "RefTitle": "Released SAP Kernel 4.6x MS SQL Server operat. sys.", "RefUrl": "/notes/156558"}, {"RefNumber": "156557", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/156557"}, {"RefNumber": "156554", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/156554"}, {"RefNumber": "156553", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/156553"}, {"RefNumber": "156551", "RefComponent": "BC-DB-SDB", "RefTitle": "Released operating systems for SAP kernel 4.6x SAP DB", "RefUrl": "/notes/156551"}, {"RefNumber": "156549", "RefComponent": "BC-DB-INF", "RefTitle": "Released operating systems for SAP kernel 4.6x INFORMIX", "RefUrl": "/notes/156549"}, {"RefNumber": "156548", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/156548"}, {"RefNumber": "153396", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/153396"}, {"RefNumber": "139806", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/139806"}, {"RefNumber": "1349967", "RefComponent": "BC-UPG-RDM", "RefTitle": "Upgrade to Business Suite 7 Innovation 2010: IBM DB2 for i", "RefUrl": "/notes/1349967"}, {"RefNumber": "1338387", "RefComponent": "BC-UPG-RDM", "RefTitle": "Upgrade to EHP 2 for SAP NetWeaver 7.0 Java (DB2 for IBM i)", "RefUrl": "/notes/1338387"}, {"RefNumber": "1338386", "RefComponent": "BC-UPG-RDM", "RefTitle": "Upgrade to SAP NetWeaver 7.0 EHP2 ABAP (IBM DB2 for i)", "RefUrl": "/notes/1338386"}, {"RefNumber": "1292071", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info. on upgrading to SAP SCM 7.0 SR1 ABAP", "RefUrl": "/notes/1292071"}, {"RefNumber": "1292069", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info. on upgrading to SAP ERP 6.0 EHP4 SR1 ABAP", "RefUrl": "/notes/1292069"}, {"RefNumber": "1179413", "RefComponent": "BC-UPG-RDM", "RefTitle": "Upgrade to SAP Business Suite 7: IBM DB2 for i", "RefUrl": "/notes/1179413"}, {"RefNumber": "117668", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/117668"}, {"RefNumber": "1168235", "RefComponent": "BC-UPG-RDM", "RefTitle": "Upgrade to SAP NetWeaver 7.0 EHP1 ABAP (IBM DB2 for i)", "RefUrl": "/notes/1168235"}, {"RefNumber": "115976", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/115976"}, {"RefNumber": "1156968", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info. on upgrading to EHP 4 for SAP ERP 6.0 ABAP", "RefUrl": "/notes/1156968"}, {"RefNumber": "1108700", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1108700"}, {"RefNumber": "110007", "RefComponent": "BC-OP-AIX", "RefTitle": "SMP CPU statistics displayed for first cpu only", "RefUrl": "/notes/110007"}, {"RefNumber": "1092053", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1092053"}, {"RefNumber": "1088904", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info. on upgrading to SAP ECC 6.0 SR3 ABAP", "RefUrl": "/notes/1088904"}, {"RefNumber": "1031096", "RefComponent": "BC-CCM-HAG", "RefTitle": "Installing Package SAPHOSTAGENT", "RefUrl": "/notes/1031096"}, {"RefNumber": "1010762", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1010762"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "3030837", "RefComponent": "BC-CCM-MON-OS", "RefTitle": "SAPOSCOL Does not start", "RefUrl": "/notes/3030837 "}, {"RefNumber": "2463083", "RefComponent": "BC-CCM-MON-OS", "RefTitle": "VMware errors in saposcol dev_coll trace file on Linux", "RefUrl": "/notes/2463083 "}, {"RefNumber": "826092", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info. on upgrading to SAP ERP Central Component 6.0", "RefUrl": "/notes/826092 "}, {"RefNumber": "1031096", "RefComponent": "BC-CCM-HAG", "RefTitle": "Installing Package SAPHOSTAGENT", "RefUrl": "/notes/1031096 "}, {"RefNumber": "91488", "RefComponent": "SV-SMG-SER", "RefTitle": "SAP Support Services - Central preparatory note", "RefUrl": "/notes/91488 "}, {"RefNumber": "1292071", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info. on upgrading to SAP SCM 7.0 SR1 ABAP", "RefUrl": "/notes/1292071 "}, {"RefNumber": "189072", "RefComponent": "BC-CCM-MON", "RefTitle": "ST06/OS06: No data or incorrect data displayed", "RefUrl": "/notes/189072 "}, {"RefNumber": "1088904", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info. on upgrading to SAP ECC 6.0 SR3 ABAP", "RefUrl": "/notes/1088904 "}, {"RefNumber": "936887", "RefComponent": "BC-OP-LNX", "RefTitle": "End of maintenance for Linux distributions", "RefUrl": "/notes/936887 "}, {"RefNumber": "407317", "RefComponent": "BC-DB-INF", "RefTitle": "Released operating systems for SAP kernel 6.x INFORMIX", "RefUrl": "/notes/407317 "}, {"RefNumber": "156549", "RefComponent": "BC-DB-INF", "RefTitle": "Released operating systems for SAP kernel 4.6x INFORMIX", "RefUrl": "/notes/156549 "}, {"RefNumber": "1338386", "RefComponent": "BC-UPG-RDM", "RefTitle": "Upgrade to SAP NetWeaver 7.0 EHP2 ABAP (IBM DB2 for i)", "RefUrl": "/notes/1338386 "}, {"RefNumber": "1168235", "RefComponent": "BC-UPG-RDM", "RefTitle": "Upgrade to SAP NetWeaver 7.0 EHP1 ABAP (IBM DB2 for i)", "RefUrl": "/notes/1168235 "}, {"RefNumber": "1156968", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info. on upgrading to EHP 4 for SAP ERP 6.0 ABAP", "RefUrl": "/notes/1156968 "}, {"RefNumber": "1292069", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info. on upgrading to SAP ERP 6.0 EHP4 SR1 ABAP", "RefUrl": "/notes/1292069 "}, {"RefNumber": "1338387", "RefComponent": "BC-UPG-RDM", "RefTitle": "Upgrade to EHP 2 for SAP NetWeaver 7.0 Java (DB2 for IBM i)", "RefUrl": "/notes/1338387 "}, {"RefNumber": "1349967", "RefComponent": "BC-UPG-RDM", "RefTitle": "Upgrade to Business Suite 7 Innovation 2010: IBM DB2 for i", "RefUrl": "/notes/1349967 "}, {"RefNumber": "912905", "RefComponent": "BC-DB-SDB", "RefTitle": "FAQ: Storage systems used with SAP MaxDB", "RefUrl": "/notes/912905 "}, {"RefNumber": "710975", "RefComponent": "BC-OP-AIX", "RefTitle": "FAQ: which saposcol should be used on AIX", "RefUrl": "/notes/710975 "}, {"RefNumber": "1179413", "RefComponent": "BC-UPG-RDM", "RefTitle": "Upgrade to SAP Business Suite 7: IBM DB2 for i", "RefUrl": "/notes/1179413 "}, {"RefNumber": "913971", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info. on upgrading to SAP ERP Central Component 6.0 SR1", "RefUrl": "/notes/913971 "}, {"RefNumber": "961410", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info. on upgrading to SAP ECC 6.0 SR2 ABAP", "RefUrl": "/notes/961410 "}, {"RefNumber": "661640", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info. on upgrading to SAP Web AS 6.40", "RefUrl": "/notes/661640 "}, {"RefNumber": "484876", "RefComponent": "BC-UPG-RDM", "RefTitle": "Additional information about upgrading to SAP Web AS 6.20", "RefUrl": "/notes/484876 "}, {"RefNumber": "156558", "RefComponent": "XX-SER-REL", "RefTitle": "Released SAP Kernel 4.6x MS SQL Server operat. sys.", "RefUrl": "/notes/156558 "}, {"RefNumber": "156551", "RefComponent": "BC-DB-SDB", "RefTitle": "Released operating systems for SAP kernel 4.6x SAP DB", "RefUrl": "/notes/156551 "}, {"RefNumber": "407320", "RefComponent": "BC-DB-SDB", "RefTitle": "Released operating systems SAP R/3 kernel 6.x SAP DB", "RefUrl": "/notes/407320 "}, {"RefNumber": "727683", "RefComponent": "BC-DB-SDB", "RefTitle": "SAP kernel 6.x MaxDB: Released operating systems", "RefUrl": "/notes/727683 "}, {"RefNumber": "775047", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info. on upgrading to SAP Web AS 6.40 SR1", "RefUrl": "/notes/775047 "}, {"RefNumber": "407328", "RefComponent": "XX-SER-REL", "RefTitle": "SAP Kernel 6.x MS SQL Server: Released operating systems", "RefUrl": "/notes/407328 "}, {"RefNumber": "790639", "RefComponent": "BC-CCM-MON-OS", "RefTitle": "SAPOSCOL and ABAP server: Monitoring processes", "RefUrl": "/notes/790639 "}, {"RefNumber": "542768", "RefComponent": "BC-CCM-MON-OS", "RefTitle": "Filesystem filter & process monitor broken on AIX saposcol", "RefUrl": "/notes/542768 "}, {"RefNumber": "541936", "RefComponent": "BC-CCM-MON-OS", "RefTitle": "4.6D saposcol for AIX based on perfstat library", "RefUrl": "/notes/541936 "}, {"RefNumber": "302309", "RefComponent": "BC-UPG-RDM", "RefTitle": "Additions Workplace Server Upgrade to Rel. 2.10/2.11", "RefUrl": "/notes/302309 "}, {"RefNumber": "435140", "RefComponent": "BC-UPG-RDM", "RefTitle": "Additional information on upgrading to Basis 4.6D (APO 3.1x", "RefUrl": "/notes/435140 "}, {"RefNumber": "335029", "RefComponent": "BC-UPG-RDM", "RefTitle": "Additions to the upgrade to Basis 4.6D (NDI Upgrade)", "RefUrl": "/notes/335029 "}, {"RefNumber": "737800", "RefComponent": "BC-UPG-RDM", "RefTitle": "Enhancements to upgrade on SAP CRM 4.0 SR1", "RefUrl": "/notes/737800 "}, {"RefNumber": "603852", "RefComponent": "BC-UPG-RDM", "RefTitle": "Additional information about upgrading to SAP SCM 4.0", "RefUrl": "/notes/603852 "}, {"RefNumber": "390062", "RefComponent": "BC-UPG-RDM", "RefTitle": "Additional information about upgrading to 4.6C SR2", "RefUrl": "/notes/390062 "}, {"RefNumber": "327285", "RefComponent": "BC-UPG-RDM", "RefTitle": "Additions to upgrade to 4.6C SR1", "RefUrl": "/notes/327285 "}, {"RefNumber": "401717", "RefComponent": "BC-UPG-RDM", "RefTitle": "Additional info on upgrading to SAP Web AS 6.10 (Basis)", "RefUrl": "/notes/401717 "}, {"RefNumber": "179373", "RefComponent": "BC-UPG-RDM", "RefTitle": "Additional info for upgrade to Rel.4.6B", "RefUrl": "/notes/179373 "}, {"RefNumber": "420371", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info on upgrading to Basis 4.6C SR2 (NDI upgrade)", "RefUrl": "/notes/420371 "}, {"RefNumber": "173353", "RefComponent": "BC-CCM-MON-OS", "RefTitle": "COMPUTE_INT_PLUS_OVERFLOW with Transaction OS07", "RefUrl": "/notes/173353 "}, {"RefNumber": "445404", "RefComponent": "BC-CCM-MON-OS", "RefTitle": "TABLE_INVALID_INDEX in SAPLSMON", "RefUrl": "/notes/445404 "}, {"RefNumber": "351876", "RefComponent": "BC-OP-TRU64", "RefTitle": "SAPOSCOL for Compaq Tru64 5.x", "RefUrl": "/notes/351876 "}, {"RefNumber": "548699", "RefComponent": "BC-CCM-MON-OS", "RefTitle": "FAQ: OS collector SAPOSCOL", "RefUrl": "/notes/548699 "}, {"RefNumber": "485827", "RefComponent": "BC-OP-FTS-SOL", "RefTitle": "Solaris saposcol terminates after a few days", "RefUrl": "/notes/485827 "}, {"RefNumber": "192949", "RefComponent": "BC-UPG-RDM", "RefTitle": "Additional info for upgrade to 4.6C", "RefUrl": "/notes/192949 "}, {"RefNumber": "485866", "RefComponent": "BC-OP-FTS-SOL", "RefTitle": "saposcol does not display sfxfs- and sfcfs-filesystems", "RefUrl": "/notes/485866 "}, {"RefNumber": "162980", "RefComponent": "BC-CCM-MON-OS", "RefTitle": "saposcol for 64-bit Solaris", "RefUrl": "/notes/162980 "}, {"RefNumber": "168439", "RefComponent": "XX-SER-TCC-EW", "RefTitle": "Preparándose para una sesión de Early Watch ó GL", "RefUrl": "/notes/168439 "}, {"RefNumber": "192217", "RefComponent": "BC-CCM-MON-OS", "RefTitle": "DB12 (NT):  Incorrect values for large freespace", "RefUrl": "/notes/192217 "}, {"RefNumber": "178248", "RefComponent": "BC-CCM-MON-OS", "RefTitle": "Overflow error from os07 monitoring remote box/ rfcoscol.", "RefUrl": "/notes/178248 "}, {"RefNumber": "110007", "RefComponent": "BC-OP-AIX", "RefTitle": "SMP CPU statistics displayed for first cpu only", "RefUrl": "/notes/110007 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": []}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}