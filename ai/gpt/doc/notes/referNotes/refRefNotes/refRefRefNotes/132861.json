{"Request": {"Number": "132861", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 334, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000014630082017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000132861?language=E&token=B85A5529FCC6EE6348101E4680491271"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000132861", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000132861/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "132861"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 13}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Consulting"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "11.11.2011"}, "SAPComponentKey": {"_label": "Component", "value": "BC-DB-ORA-DBA"}, "SAPComponentKeyText": {"_label": "Component", "value": "Database Administration"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Basis Components", "value": "BC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Database Interface, Database Platforms", "value": "BC-DB", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-DB*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Oracle", "value": "BC-DB-ORA", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-DB-ORA*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Database Administration", "value": "BC-DB-ORA-DBA", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-DB-ORA-DBA*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "132861 - CBO: Statistics creation with SAPDBA or BRCONNECT"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>System performance is poor because database accesses take too long.&#x00A0;&#x00A0;The Oracle statistics are not up-to-date.<br /></p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>CBO, cost-based optimizer, performance</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>So that the cost-cased optimizer can calculate correct access paths for SQL statements, the statistics you use as a basis for the calculation must be as up-to-date as possible. SAP therefore provides a procedure with which you can update the statistics simply. There are several options for creating statistics using SAP tools:</p> <UL><LI>\"sapdba -checkopt PSAP%\" and \"sapdba -analyze DBSTATCO\" (Notes 93098, 93256)</LI></UL> <UL><LI>\"sapdba -statistics\" (Note 184513)</LI></UL> <UL><LI>\"brconnect -f stats\" (Note 403704)<br /></LI></UL> <b>CAUTION: Since the SAPDBA command line functions are not developed further, implement the BRCONNECT solution if possible!<br /></b><br /> <p>If these jobs are not scheduled or are not scheduled correctly, this may lead to obsolete statistics that have a negative effect on system performance. For further information, also refer to the R/3 online documentation.<br /><br />We recommend that you schedule a corresponding statistics run at least once a week (for Oracle 10g or higher: at least once a day). For this purpose, you can use either transaction DB13 or actions at operating system level that are scheduled accordingly.<br /></p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>You can use transaction DB14 to check whether the statistics runs are scheduled correctly at least once a week.</p> <UL><LI>The corresponding log files must exist:</LI></UL> <UL><UL><LI>Use of \"sapdba -checkopt\" and \"sapdba -analyze\": The button \"SAPDBA\" or \"Optimizer\" must show jobs with FID=opt (checkopt) and FID=aly (analyze). The details log indicates whether the jobs were called correctly using argument PSAP% (checkopt) or DBSTATCO (analyze). The execution times of the jobs must not overlap.</LI></UL></UL> <UL><UL><LI>Use of \"sapdba -statistics\": The button \"SAPDBA\" or \"Optimizer\" must show jobs with FID=sta.</LI></UL></UL> <UL><UL><LI>Use of \"brconnect -f stats\": The button \"BRCONNECT\", \"Others\" or \"Optimizer\" must show jobs with FID=sta (or, depending on the use of brconnect, with FID=opt and FID=aly.</LI></UL></UL> <UL><LI>The return code should be 0000. If the return code is greater than 0, you can check the details log for the cause of the error or warning.</LI></UL> <UL><LI>The statistics should be updated at least once a week (Oracle 9i or lower) or once a day (Oracle 10g or higher). Therefore, the DB14 overview should contain&#x00A0;&#x00A0;the aforementioned log files at least once a day or once a week.<br /></LI></UL> <p>If the statistics runs were scheduled incorrectly or not at all, correct the scheduling. We recommend that you use \"brconnect -f stats\" as described in Note 403704.<br /></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "BC-DB-ORA (Oracle)"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D030484)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D030484)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000132861/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000132861/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000132861/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000132861/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000132861/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000132861/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000132861/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000132861/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000132861/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "96296", "RefComponent": "BC-CTS-CCO", "RefTitle": "CC-ERROR: Database problem in client copy", "RefUrl": "/notes/96296"}, {"RefNumber": "93256", "RefComponent": "BC-DB-ORA", "RefTitle": "CBO: Changes for installation of 4.0", "RefUrl": "/notes/93256"}, {"RefNumber": "93098", "RefComponent": "BC-DB-ORA", "RefTitle": "CBO: Changes in upgrade to 4.0", "RefUrl": "/notes/93098"}, {"RefNumber": "766349", "RefComponent": "BC-DB-ORA", "RefTitle": "FAQ: Oracle SQL optimization", "RefUrl": "/notes/766349"}, {"RefNumber": "618868", "RefComponent": "BC-DB-ORA", "RefTitle": "FAQ: Oracle performance", "RefUrl": "/notes/618868"}, {"RefNumber": "588668", "RefComponent": "BC-DB-ORA", "RefTitle": "FAQ: Database statistics", "RefUrl": "/notes/588668"}, {"RefNumber": "403704", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "BRCONNECT - enhanced function for Oracle DBA", "RefUrl": "/notes/403704"}, {"RefNumber": "303140", "RefComponent": "PM-EQM", "RefTitle": "Techn.object: performance problem f.structure lists", "RefUrl": "/notes/303140"}, {"RefNumber": "215917", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/215917"}, {"RefNumber": "184513", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "CBO: Parallel generation of optimizer statistics", "RefUrl": "/notes/184513"}, {"RefNumber": "131372", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/131372"}, {"RefNumber": "118556", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "SAPDBA: New ALIAS (DBAORA) and NEWS in the SAPNET", "RefUrl": "/notes/118556"}, {"RefNumber": "109034", "RefComponent": "BC-DB-ORA", "RefTitle": "Collective note: SAPDBA - performance/CBO", "RefUrl": "/notes/109034"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "618868", "RefComponent": "BC-DB-ORA", "RefTitle": "FAQ: Oracle performance", "RefUrl": "/notes/618868 "}, {"RefNumber": "766349", "RefComponent": "BC-DB-ORA", "RefTitle": "FAQ: Oracle SQL optimization", "RefUrl": "/notes/766349 "}, {"RefNumber": "96296", "RefComponent": "BC-CTS-CCO", "RefTitle": "CC-ERROR: Database problem in client copy", "RefUrl": "/notes/96296 "}, {"RefNumber": "403704", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "BRCONNECT - enhanced function for Oracle DBA", "RefUrl": "/notes/403704 "}, {"RefNumber": "588668", "RefComponent": "BC-DB-ORA", "RefTitle": "FAQ: Database statistics", "RefUrl": "/notes/588668 "}, {"RefNumber": "184513", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "CBO: Parallel generation of optimizer statistics", "RefUrl": "/notes/184513 "}, {"RefNumber": "93098", "RefComponent": "BC-DB-ORA", "RefTitle": "CBO: Changes in upgrade to 4.0", "RefUrl": "/notes/93098 "}, {"RefNumber": "93256", "RefComponent": "BC-DB-ORA", "RefTitle": "CBO: Changes for installation of 4.0", "RefUrl": "/notes/93256 "}, {"RefNumber": "303140", "RefComponent": "PM-EQM", "RefTitle": "Techn.object: performance problem f.structure lists", "RefUrl": "/notes/303140 "}, {"RefNumber": "118556", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "SAPDBA: New ALIAS (DBAORA) and NEWS in the SAPNET", "RefUrl": "/notes/118556 "}, {"RefNumber": "109034", "RefComponent": "BC-DB-ORA", "RefTitle": "Collective note: SAPDBA - performance/CBO", "RefUrl": "/notes/109034 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": []}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}