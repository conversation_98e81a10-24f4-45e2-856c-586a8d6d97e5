{"Request": {"Number": "161100", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 2118, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "D", "_masterLanguage": "D", "_loadedLanguage": "D", "_machineTranslationLanguage": "E", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Acknowledge", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "If the quality of the machine translation is not sufficient for your purposes, click here to request a revised version.", "_type": "EVENT", "_visible": true}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": false, "url": ""}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000161100?language=E&token=8B178B2C68E36B7C4C98456A1BEDAE41"}, "ShareByEmail": {"_label": "Share via Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000161100", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000161100/D/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "161100"}, "Type": {"_label": "Type", "value": "SAP-Hinweis"}, "Version": {"_label": "Version", "value": 1}, "Recency": {"_label": "Currentness", "key": "", "value": ""}, "Language": {"_label": "Language", "value": "German"}, "MasterLanguage": {"_label": "Original Language", "value": "German"}, "Category": {"_label": "Category", "value": "Consulting"}, "Priority": {"_label": "Priority", "value": "Recommendations/Additional Information"}, "Status": {"_label": "Release Status", "value": "To Be Archived"}, "ReleasedOn": {"_label": "Released On", "value": "00.00.0000"}, "SAPComponentKey": {"_label": "Component", "value": "BC-DB-DB6"}, "SAPComponentKeyText": {"_label": "Component", "value": "DB2 Universal Database for Unix / NT"}, "SAPComponentPath": [{"_label": "Services &amp; Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs &amp; Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Basis Components", "value": "BC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Database Interface, Database Platforms", "value": "BC-DB", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-DB*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "DB2 Universal Database for Unix / NT", "value": "BC-DB-DB6", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-DB-DB6*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "161100 - DB2/UDB Database Administrator <PERSON><PERSON><PERSON>"}, "LongText": {"_label": "Description", "value": "<div id=\"DISCLAIMER\" class=\"sapMMsgStrip sapMMsgStripWarning sapUiSmallMarginTop sapUiMediumMarginBottom\"><div class=\"sapMMsgStripMessage\"><span dir=\"auto\" class=\"sapMText sapUiSelectable sapMTextMaxWidth\" style=\"text-align: left;\"><strong>ATTENTION!</strong> This is a machine-translated document that has not been reviewed for accuracy. To provide feedback regarding the machine translation, please <a style=\"color:var(--sapLinkColor);\" href=\"https://sapinsights.eu.qualtrics.com/jfe/form/SV_6nI2C1MOwEviVQG?NoteNumber=161100&TargetLanguage=EN&Component=BC-DB-DB6&SourceLanguage=DE&Priority=06\" target=\"_blank\">click here</a>.<br/>You can access the original document via the following link: <a style=\"color:var(--sapLinkColor);\" href=\"/notes/161100/D\" target=\"_blank\">/notes/161100/D</a>.</span></div></div><div><div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>Further information and documentation is required for troubleshooting and administration of DB2/UDB (for UNIX and NT) databases.</p><h3 data-toc-skip class=\"section\" id=\"Weitere Begriffe\">Other Terms</h3><p></p><h3 data-toc-skip class=\"section\" id=\"Ursache und Voraussetzungen\">Reason and Prerequisites</h3><p></p><h3 data-toc-skip class=\"section\" id=\"L&ouml;sung\">Solution</h3><b>**********************************</b><br /> <b>Not intended for publication</b><br /> <b>**********************************</b><br /> <p></p> <b></b><br /> <b></b><br /> <b></b><br /> <b>*** BASICS ***</b><br /> <p>0030289   SAProuter Documentation<br />0031515   Service connection<br />0033772   Correct Configuration of Dr. Watson<br />0037001   TELNET link to Customer Systems<br /></p> <b>*** DB2 BASICS ***</b><br /> <p>0083819   SUPPORT: Collecting required data<br />0101809   DB2 UDB for Unix/NT: Supported Fixpaks<br />0089290   Database Storage requirement<br />0122222   REDIRECTED RESTORE via DB2 CLP<br />0136702   Moving tables to other DB2 tablespaces<br />0147634   Tips and Tricks when creating DB2 tablespaces<br />0149125   Re-activitation of VIEWs under DB2 UDB<br /></p> <b>*** DB2 TOOLS ***</b><br /> <p>0098524   DB2DART: Description<br />0102200   DB2LOOK: DDL and statistics information<br />0038513   DB trace (db2trc) during database malfunction<br />0050896   System Shutdown: First Problem Analysis<br />0076577   brdb6 tools for Redirected Restore<br /><br /></p> <b>*** BACKUP &amp; RESTORE ***</b><br /> <b>0082029    ADSM Installation  in DB2 environment</b><br /> <b></b><br /> <b>*** LOOGING and USEREXIT ***</b><br /> <p>0072557   Logging behaviour of the database<br /></p> <b>*** BRARCHIVE &amp; BRRESTORE ***</b><br /> <b>*** PERFORMANCE ***</b><br /> <b>*** WINDOWS NT/2000 ***</b><br /> <p>0147816   User concept R/3 on DB2 on NT<br /></p></div></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "BW-SYS-DB-DB6 (BW DB2 Universal Database)"}, {"Key": "Database System", "Value": "DB2/UDB"}, {"Key": "Owner                                                                                    ", "Value": "********"}, {"Key": "Processor                                                                                          ", "Value": "********"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000161100/D"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "MIME Type", "URL": "URL"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document references", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "URL"}, "Items": [{"RefNumber": "99843", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/99843"}, {"RefNumber": "98524", "RefComponent": "BC-DB-DB6", "RefTitle": "DB6: Description of DB2DART", "RefUrl": "/notes/98524"}, {"RefNumber": "92495", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/92495"}, {"RefNumber": "89290", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/89290"}, {"RefNumber": "85484", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/85484"}, {"RefNumber": "83819", "RefComponent": "BC-DB-DB6", "RefTitle": "DB6: Collect Support Data", "RefUrl": "/notes/83819"}, {"RefNumber": "82029", "RefComponent": "BC-DB-DB6-DBA", "RefTitle": "DB6: TSM installation in DB2 environment", "RefUrl": "/notes/82029"}, {"RefNumber": "76577", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/76577"}, {"RefNumber": "72557", "RefComponent": "BC-DB-DB6", "RefTitle": "Logging in DB2 UDB Systems", "RefUrl": "/notes/72557"}, {"RefNumber": "70085", "RefComponent": "BC-CST-UP", "RefTitle": "Subsequent Posting of Terminated Update Records with SM13", "RefUrl": "/notes/70085"}, {"RefNumber": "50896", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/50896"}, {"RefNumber": "49776", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/49776"}, {"RefNumber": "39412", "RefComponent": "BC-CST", "RefTitle": "Configure how many work processes?", "RefUrl": "/notes/39412"}, {"RefNumber": "38513", "RefComponent": "BC-DB-DB6", "RefTitle": "Database trace (db2trc) for DB malfunctions", "RefUrl": "/notes/38513"}, {"RefNumber": "31707", "RefComponent": "BC-DB-DB6-SYS", "RefTitle": "DB6: DBSL trace for performance/error analysis", "RefUrl": "/notes/31707"}, {"RefNumber": "31515", "RefComponent": "XX-SER-NET-HTL", "RefTitle": "Service Connections", "RefUrl": "/notes/31515"}, {"RefNumber": "30289", "RefComponent": "BC-CST-NI", "RefTitle": "SAProuter documentation", "RefUrl": "/notes/30289"}, {"RefNumber": "167361", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/167361"}, {"RefNumber": "167349", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/167349"}, {"RefNumber": "156829", "RefComponent": "BC-DB-DB6", "RefTitle": "DB6: DB2 Explain with db2exfmt", "RefUrl": "/notes/156829"}, {"RefNumber": "152877", "RefComponent": "BC-DB-DB6", "RefTitle": "Adding Containers to a Tablespace", "RefUrl": "/notes/152877"}, {"RefNumber": "152872", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/152872"}, {"RefNumber": "151085", "RefComponent": "BC-DB-DB6", "RefTitle": "Some work processes terminate with SQL1403", "RefUrl": "/notes/151085"}, {"RefNumber": "147634", "RefComponent": "BC-DB-DB6", "RefTitle": "DB6: Tips and Tricks for Creating Db2 Tablespaces", "RefUrl": "/notes/147634"}, {"RefNumber": "144839", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/144839"}, {"RefNumber": "141619", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/141619"}, {"RefNumber": "141615", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/141615"}, {"RefNumber": "139512", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/139512"}, {"RefNumber": "139253", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/139253"}, {"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Advance transports to customer (note for customers)", "RefUrl": "/notes/13719"}, {"RefNumber": "135042", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/135042"}, {"RefNumber": "130528", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/130528"}, {"RefNumber": "122321", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/122321"}, {"RefNumber": "11920", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customer (internal only)", "RefUrl": "/notes/11920"}, {"RefNumber": "1050", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1050"}, {"RefNumber": "104019", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/104019"}, {"RefNumber": "102200", "RefComponent": "BC-DB-DB6", "RefTitle": "DB6: db2look - DDL and Statistics Information", "RefUrl": "/notes/102200"}, {"RefNumber": "101809", "RefComponent": "BC-DB-DB6", "RefTitle": "DB6: Supported DB2 versions and Fix Pack levels", "RefUrl": "/notes/101809"}, {"RefNumber": "100900", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/100900"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "URL"}, "Items": [{"RefNumber": "30289", "RefComponent": "BC-CST-NI", "RefTitle": "SAProuter documentation", "RefUrl": "/notes/30289 "}, {"RefNumber": "72557", "RefComponent": "BC-DB-DB6", "RefTitle": "Logging in DB2 UDB Systems", "RefUrl": "/notes/72557 "}, {"RefNumber": "147634", "RefComponent": "BC-DB-DB6", "RefTitle": "DB6: Tips and Tricks for Creating Db2 Tablespaces", "RefUrl": "/notes/147634 "}, {"RefNumber": "83819", "RefComponent": "BC-DB-DB6", "RefTitle": "DB6: Collect Support Data", "RefUrl": "/notes/83819 "}, {"RefNumber": "31707", "RefComponent": "BC-DB-DB6-SYS", "RefTitle": "DB6: DBSL trace for performance/error analysis", "RefUrl": "/notes/31707 "}, {"RefNumber": "98524", "RefComponent": "BC-DB-DB6", "RefTitle": "DB6: Description of DB2DART", "RefUrl": "/notes/98524 "}, {"RefNumber": "31515", "RefComponent": "XX-SER-NET-HTL", "RefTitle": "Service Connections", "RefUrl": "/notes/31515 "}, {"RefNumber": "156829", "RefComponent": "BC-DB-DB6", "RefTitle": "DB6: DB2 Explain with db2exfmt", "RefUrl": "/notes/156829 "}, {"RefNumber": "70085", "RefComponent": "BC-CST-UP", "RefTitle": "Subsequent Posting of Terminated Update Records with SM13", "RefUrl": "/notes/70085 "}, {"RefNumber": "102200", "RefComponent": "BC-DB-DB6", "RefTitle": "DB6: db2look - DDL and Statistics Information", "RefUrl": "/notes/102200 "}, {"RefNumber": "39412", "RefComponent": "BC-CST", "RefTitle": "Configure how many work processes?", "RefUrl": "/notes/39412 "}, {"RefNumber": "11920", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customer (internal only)", "RefUrl": "/notes/11920 "}, {"RefNumber": "82029", "RefComponent": "BC-DB-DB6-DBA", "RefTitle": "DB6: TSM installation in DB2 environment", "RefUrl": "/notes/82029 "}, {"RefNumber": "38513", "RefComponent": "BC-DB-DB6", "RefTitle": "Database trace (db2trc) for DB malfunctions", "RefUrl": "/notes/38513 "}, {"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Advance transports to customer (note for customers)", "RefUrl": "/notes/13719 "}, {"RefNumber": "151085", "RefComponent": "BC-DB-DB6", "RefTitle": "Some work processes terminate with SQL1403", "RefUrl": "/notes/151085 "}, {"RefNumber": "152877", "RefComponent": "BC-DB-DB6", "RefTitle": "Adding Containers to a Tablespace", "RefUrl": "/notes/152877 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And the following"}, "Items": []}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software-Komponentenversion", "SupportPackage": "Support Package", "URL": "URL"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Packages and Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "URL"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "URL"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about correction instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisite", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisite", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "URL", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated as helpful", "Helpful-Yes": "0 persons"}, "RatingQuality": {"_label": "Quality Assessment"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document eliminates side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "URL"}, "Items": []}, "SideEffectsSolving": {"_label": "This document causes side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "URL"}, "Items": []}}, "DisclaimerForMachineTranslation": {"Header": "ATTENTION!", "Message": "This is a machine-translated document that has not been reviewed for accuracy.", "Survey": "To provide feedback regarding the machine translation, please <a href=\"https://sapinsights.eu.qualtrics.com/jfe/form/SV_6nI2C1MOwEviVQG?NoteNumber=161100&TargetLanguage=EN&Component=BC-DB-DB6&SourceLanguage=DE&Priority=06\" target=\"_blank\">click here</a>.", "Request": "If the quality of the machine translation is not sufficient for your purposes, click here to request a revised version.", "Origin": "You can access the original document via the following link: <a href=\"/notes/161100/D\" target=\"_blank\">/notes/161100/D</a>."}}}}