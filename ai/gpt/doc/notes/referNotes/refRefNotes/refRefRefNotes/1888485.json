{"Request": {"Number": "1888485", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 384, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000017691182017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001888485?language=E&token=01BD61A15D005BBE54C0B3C4F371B6AA"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001888485", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0001888485/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1888485"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 66}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Installation information"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "26.05.2021"}, "SAPComponentKey": {"_label": "Component", "value": "BC-DB-ORA"}, "SAPComponentKeyText": {"_label": "Component", "value": "Oracle"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Basis Components", "value": "BC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Database Interface, Database Platforms", "value": "BC-DB", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-DB*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Oracle", "value": "BC-DB-ORA", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-DB-ORA*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1888485 - Database Parameter for ********"}, "LongText": {"_label": "Description", "value": "<h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p><strong>Oracle Database Parameter for Oracle Database 12c Release 1 (12.1)</strong></p>\r\n<p>This SAP note contains recommendations for the best configuration of Oracle Database 12c Release 1 (12.1) for SAP.</p>\r\n<p><span style=\"text-decoration: underline;\">Validity of Recommendations</span></p>\r\n<p>The parameter recommendations provided in this note are targeted for <span style=\"text-decoration: underline;\">SAP NetWeaver-based SAP products and Solutions</span>. The parameter settings are tested and proven by customer experience only for these SAP products.</p>\r\n<p>Although these parameter settings might be a good starting point for non-SAP NetWeaver-based products, we cannot provide any guarantee that they work reasonably well in such environments. For non-SAP NetWeaver-based products, the relevant product owners are responsible to provide recommendations for Oracle parameter settings if requested.</p>\r\n<p>Note&#160;for SAP Manufacturing Execution (SAP ME)&#160;on Oracle:</p>\r\n<ul>\r\n<li>SAP applications 'SAP ME' and 'SAP MII/MEINT'&#160;are based on SAP NetWeaver. For Oracle&#160;databases&#160;of these SAP applications&#160;please follow this SAP note.</li>\r\n<li>However, parameter and configuration requirements for 'SAP ME WIP' and 'SAP ME ODS'&#160;databases are different. For these Oracle databases&#160;you must follow SAP Note <a target=\"_blank\" href=\"/notes/1405260\">1405260</a>.</li>\r\n</ul>\r\n\r\n<p><span style=\"text-decoration: underline;\"><span style=\"text-decoration: underline;\">Change History</span></span></p>\r\n<p>Here you find the latest changes. Older changes are located at the end of this SAP Note.</p>\r\n<p>All parameter&#160;recommendations given in this note&#160;are subject to&#160;change. It is therefore recommended to regularly check the latest version of this note and apply the necessary changes to the database, if needed.</p>\r\n<p>When a new database patch&#160;becomes available that requires a parameter change, this note is reviewed and updated. Beyond that, changes will only be made in exceptional cases for critical Oracle parameters.</p>\r\n<p>&#160;</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td><span style=\"text-decoration: underline;\">Date</span></td>\r\n<td><span style=\"text-decoration: underline;\">Change</span></td>\r\n</tr>\r\n<tr>\r\n<td>May 26, 2021</td>\r\n<td>\r\n<p>Added \"_fix_control\"='23473108:ON'<br />Added \"_fix_control\"='28173995:ON'<br />Added \"_fix_control\"='29867728:ON'<br />Added \"_fix_control\"='30786641:ON'<br />Adjusted \"_fix_control\"='31444353:1'</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>November 30, 2020</td>\r\n<td>\r\n<p>Added \"_fix_control\"='31444353:1'</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>May 15, 2020</td>\r\n<td>\r\n<p>Added \"_fix_control\"='22174392:ON'<br />Added \"_fix_control\"='25643889:ON'<br />Added \"_ipddb_enable\" (SAP Note <a target=\"_blank\" href=\"/notes/2919894\">2919894</a>)</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>March 05, 2020</td>\r\n<td>\r\n<p>Updated \"db_create_online_log_dest_2\" recommendation for Exadata<br />Updated \"disk_asynch_io\" additional information<br />Added \"_fix_control\"='23738304:OFF' (SAP Note <a target=\"_blank\" href=\"/notes/2522894\">2522894</a>)<br />Added \"_enable_space_preallocation\" (SAP Note Hot News&#160;<a target=\"_blank\" href=\"/notes/2538588\">2538588</a>)</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>November 19, 2019</td>\r\n<td>\r\n<p>Added recommendation for SAP Manufacturing Execution (SAP ME)</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>November 18, 2019</td>\r\n<td>\r\n<p>Adjusted \"_fix_control\"='23738304:ON' (SAP Note <a target=\"_blank\" href=\"/notes/2522894\">2522894</a>)</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>July 15, 2019</td>\r\n<td>\r\n<p>Added \"_fix_control\"='28072567:ON'<br />Added \"_in_memory_undo\"= FALSE (Hot&#160;News&#160;<a target=\"_blank\" href=\"/notes/2812178\">2812178</a>, non-RAC systems only)</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>November 23, 2018</td>\r\n<td>\r\n<p>Adjusted \"_fix_control\"='20355502:10' (SAP Note <a target=\"_blank\" href=\"/notes/2698967\">2698967</a>)<br />Added \"_fix_control\"='8932139:ON'</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>October 01, 2018</td>\r\n<td>\r\n<p>Added \"_fix_control\"='26536320:ON'</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>June 04, 2018</td>\r\n<td>\r\n<p>Adjusted \"_fix_control\"='20107874:OFF'<br />Adjusted \"_fix_control\"='20107874:ON'</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>June 01, 2018</td>\r\n<td>\r\n<p>Added \"_fix_control\"='27466597:ON'</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>February 15, 2018</td>\r\n<td>\r\n<p>Added \"_fix_control\"='19475484:ON'<br />Added \"_fix_control\"='21509656:ON'<br />Added \"_fix_control\"='21802552:ON'<br />Added \"_fix_control\"='27321179:ON'<br />Updated \"_fix_control\"='14846352:OFF'<br />Updated \"_fix_control\"='23738553:ON'&#160;<br />Updated&#160;\"_ktb_debug_flags=8\"<br />Updated \"disk_asynch_io\"</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>January 3, 2018</td>\r\n<td>\r\n<p>Updated&#160;recommendation&#160;for \"_fix_control\"&#160;marked with (<a target=\"_self\" href=\"#PARAM_BASIC_FIX_CONTROL_EXPLANATION\">***</a>)&#160;for easier parameter check (SAP Note <a target=\"_blank\" href=\"/notes/1171650\">1171650</a>)<br />Now applying to&#160;higher minimum patch level UNIX 12102x_date ( date &gt;=&#160;201711 ) / WIN ********.nP ( n &gt;=&#160;171017 )</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>December 21, 2017</td>\r\n<td>\r\n<p>Updated recommendation for \"heat_map\"<br />Updated recommendation for \"optimizer_adaptive_features\" (SAP Note <a target=\"_blank\" href=\"/notes/2537839\">2537839</a>)<br />Updated recommendation for \"optimizer_adaptive_plans\" (SAP Note <a target=\"_blank\" href=\"/notes/2537839\">2537839</a>)<br />Updated recommendation for \"optimizer_adaptive_statistics\" (SAP Note <a target=\"_blank\" href=\"/notes/2537839\">2537839</a>)</p>\r\n<p>Structure change: moved \"event\" into a <a target=\"_self\" href=\"#PARAM_BASIC_EVENT\">separate table</a>.<br />Structure change: moved \"_fix_control\"&#160;into a&#160;<a target=\"_self\" href=\"#PARAM_BASIC_FIX_CONTROL\">separate table</a>.</p>\r\n<p>Updated recommendation for \"_fix_control\"<br />Updated recommendation for \"_fix_control\" (<a target=\"_self\" href=\"#PARAM_BASIC_FIX_CONTROL_EXPLANATION\">***</a>)<br />Modified&#160;\"_fix_control\"=&#65279;'20107874:OFF'&#65279; / &#65279;'20107874:ON'&#65279;<br />Modified&#160;\"_fix_control\"='10038517:OFF'<br />Added&#160;\"_fix_control\"='17973658:ON'&#160; (***)<br />Added&#160;\"_fix_control\"='18876528:ON'&#160; (***)<br />Added&#160;\"_fix_control\"='19507904:ON'&#160; (***)<br />Added&#160;\"_fix_control\"='19563657:ON'&#160; (***)<br />Added&#160;\"_fix_control\"='20129763:ON'&#160; (***)<br />Added&#160;\"_fix_control\"='20636003:OFF'&#160;&#160;&#160;&#160;&#160; <br />Added&#160;\"_fix_control\"='20914534:OFF' (***)<br />Added&#160;\"_fix_control\"='21833220:ON'&#160; (***)<br />Added&#160;\"_fix_control\"='22077191:ON'&#160; (***)<br />Added&#160;\"_fix_control\"='22518491:ON'&#160; (***)<br />Added&#160;\"_fix_control\"='22533539:ON'&#160; (***)<br />Added&#160;\"_fix_control\"='22746853:ON'&#160; (***)<br />Added&#160;\"_fix_control\"='23102649:ON'&#160; (***)<br />Added&#160;\"_fix_control\"='23136865:ON'&#160; (***)<br />Added&#160;\"_fix_control\"='23197730:ON'&#160; (***)<br />Added&#160;\"_fix_control\"='23738304:ON'&#160; (***)<br />Added&#160;\"_fix_control\"='23738553:ON'&#160; (***)<br />Added&#160;\"_fix_control\"='25476149:ON'</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>September 29, 2017</td>\r\n<td>\r\n<p>Added \"_fix_control\"='7324224:OFF'<br />Adjust parameter \"optimizer_adaptive_features\"<br />Added parameter \"optimizer_adaptive_plans\"<br />Added parameter \"optimizer_adaptive_statistics\"<br />Added parameter \"pre_page_sga\"<br />Removed parameter \"optimizer_capture_sql_plan_baselines\"</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>May 11, 2017</td>\r\n<td>\r\n<p>New event 12099<br />New&#160;value&#160;for parameter \"_fix_control\"='20228468:OFF' added<br />New parameter \"_log_segment_dump_parameter\" added<br />New parameter \"_log_segment_dump_patch\" added</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>March 16, 2017</td>\r\n<td>\r\n<p>New event 60025<br />New parameter \"_bug12963364_bug12963364_spacebg_sync_segblocks\" for Windows</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>February 24, 2017</td>\r\n<td>\r\n<p>New parameter \"_bug12963364_spacebg_sync_segblocks\" added</p>\r\n</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p>&#160;</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>init.ora, init&lt;SID&gt;.ora, initSID.ora, SPFILE, Server Parameter File</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>This SAP note contains SAP's recommendations for the optimal configuration of Oracle Database 12c Release (12.1).</p>\r\n<p><strong>Common&#160;Recommendations</strong></p>\r\n\r\n<p><span style=\"text-decoration: underline;\"><span style=\"text-decoration: underline;\">Server Parameter File</span></span></p>\r\n<p>All database parameters are configured in the Server Parameter File (SPFILE). For information about the&#160;setting or&#160;changing&#160;parameters in a Server Parameter File, see SAP note <a target=\"_blank\" href=\"/notes/601157\">601157</a>.</p>\r\n<p>For further information on Oracle database parameters, SPFILE management and parameter checking for an SAP database see SAP note <a target=\"_blank\" href=\"/notes/2378252\">2378252</a>.</p>\r\n<p><span style=\"text-decoration: underline;\">Obsolete Parameters</span></p>\r\n<p>Obsolete parameters are parameters which don't exist any more or don't need to be set any more in the current release. These parameters should be removed from the Spfile.</p>\r\n<p><span style=\"text-decoration: underline;\">Parameters that must not be set</span></p>\r\n<p>The following database parameters must never be set in an SAP environment:</p>\r\n<ul style=\"list-style-type: none;\">\r\n<li>BACKGROUND_DUMP_DEST<br />COMMIT_LOGGING<br />COMMIT_WAIT<br />COMMIT_WRITE<br />CORE_DUMP_DEST<br />DB_FILE_MULTIBLOCK_READ_COUNT<br />NLS_LENGTH_SEMANTICS<br />OPTIMIZER_FEATURES_ENABLE<br />OPTIMIZER_INDEX_CACHING<br />OPTIMIZER_MODE<br />REMOTE_OS_AUTHENT<br />USER_DUMP_DEST</li>\r\n</ul>\r\n<p>As a general rule,&#160;database parameters that are not explicitly mentioned must not be set in the server parameter file. Only database parameters which are explicitly mentioned in this note are allowed to be set. There are the following exceptions to this rule:</p>\r\n<ul style=\"list-style-type: none;\">\r\n<li>A&#160;parameter that is not mentioned&#160;in this note&#160;can be set if it&#160;is recommended in another SAP note as&#160;-temporary- solution or workaround for a problem.</li>\r\n<li>A&#160;parameter that is not mentioned&#160;in this note can be set if it&#160;is required in order to implement an individual database configuration.</li>\r\n</ul>\r\n<p>You can check&#160;whether a parameter is&#160;specified in the SPFILE (see appendix).</p>\r\n\r\n<p><span style=\"text-decoration: underline;\">Parameter with Path Values for Directories and Files</span></p>\r\n<p>&lt;SAPDATA_HOME&gt; refers to the value of the environment variable SAPDATA_HOME.<br />Path values are given in UNIX syntax using slashed '/'. On WINDOWS, you must replace the forward slashes '/' with back slashes '\\'.</p>\r\n<p><span style=\"text-decoration: underline;\">Configuring for OLAP or OLTP Systems</span></p>\r\n<p>The terms OLAP system and OLTP system have the following meaning:</p>\r\n<ul>\r\n<li>SAP OLAP system: an SAP OLAP system is an SAP system with mainly BW functions (BW/BI, APO with mainly DP usage, SEM-BPS, BW-based SEM-BCS).</li>\r\n<li>SAP OLTP system: an SAP OLTP system&#160;is an&#160;SAP system with mainly non-BW functions (this also includes, for example, Bank Analyzer systems)</li>\r\n</ul>\r\n<p>SAP&#160;applications that should be configured for OLAP are:</p>\r\n<ul>\r\n<li>SAP Advanced Planning and Optimization (APO)</li>\r\n<li>SAP Business Warehouse (BW), SAP Business Information Warehouse (BW)</li>\r\n<li>SAP Strategic Enterprise Management (SEM)</li>\r\n</ul>\r\n\r\n<p>An SAP system with a pure Java stack&#160;should be&#160;configured as an OLTP system.</p>\r\n<p>An SAP&#160;double stack system -that is, an SAP system with both ABAP and JAVA stack- should be configured&#160;as an OLTP or OLAP system, depending on the degree to which you use BW functions (see above).</p>\r\n\r\n<p><span style=\"text-decoration: underline;\">Configuration of Database Instance Memory</span></p>\r\n<p>You are allowed to optimize memory parameters and resource parameters such as DB_CACHE_SIZE or DB_WRITER_PROCESSES individually. This note cannot give any general recommendations. However, you can determine options for optimization on the basis of a database performance analysis (see SAP Notes 618868, 619188, 789011). The parameterization described below is directed towards the use of the features of the dynamic SGA (Note 617416) and the automatic PGA administration (Note 619876).</p>\r\n<p>The \"Available Memory\" which is referred to in some of the following parameters is the [physical] memory, that is available and dedicated for the Oracle database instance.</p>\r\n<p><span style=\"text-decoration: underline;\">Database Parameter REMOTE_OS_AUTHENT</span></p>\r\n<p>Starting with Oracle Database 12c, this parameter must not be set any more. The following error messages indicate that remote_os_authent=TRUE is still set:</p>\r\n<ul>\r\n<li>\"ORA-32006: REMOTE_OS_AUTHENT initialization parameter has been deprecated\" (set in pfile)</li>\r\n<li>\"ORA-32004: obsolete or deprecated parameter(s) specified for RDBMS instance\" (set in spfile)</li>\r\n</ul>\r\n<p><strong>Automatic Database Parameter Check<br /></strong></p>\r\n<p>For information about the automatic check of the parameter, see Note <a target=\"_blank\" href=\"/notes/1171650\">1171650</a>.</p>\r\n<p><strong>Description of Database Parameters</strong></p>\r\n<p>For further explanation on individual database parameters, see SAP note <a target=\"_blank\" href=\"/notes/1289199\">1289199</a>&#160;or refer to the documentation for&#160;<a target=\"_blank\" href=\"https://docs.oracle.com/database/121/index.htm\">Oracle Database 12c Release 1 (12.1)</a>.</p>\r\n<p>Links to Oracle documentation:<br /><a target=\"_blank\" href=\"https://docs.oracle.com/database/121/index.htm\">https://docs.oracle.com/database/121/index.htm</a><br /><a target=\"_blank\" href=\"http://www.oracle.com/pls/db121/homepage\">http://www.oracle.com/pls/db121/homepage</a></p>\r\n\r\n<p>&#160;</p>\r\n\r\n<p>&#160;</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p><strong>Oracle Database Parameter&#160;Settings for Oracle Database 12c Release 1 (********) for SAP</strong></p>\r\n<p>All parameter recommendations given below are valid for Unix and Windows platforms (except platform-specific parameter).</p>\r\n<p>All parameter recommendations given below are valid for Oracle Database 12c Release 1 (********).</p>\r\n<p>Certain database parameter are valid for a single instance database while others are valid only for a RAC database or&#160;only on Oracle Engineered Systems. Some parameters have different values depending whether the database files are&#160;located on file system or&#160;in ASM. Therefore parameter recommendations are&#160;separated into different sections referring to one of the following installation type:</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td><span style=\"text-decoration: underline;\">Name</span></td>\r\n<td><span style=\"text-decoration: underline;\">Description</span></td>\r\n</tr>\r\n<tr>\r\n<td>SI</td>\r\n<td>Oracle Single Instance Database</td>\r\n</tr>\r\n<tr>\r\n<td>SI/FS</td>\r\n<td>Oracle Single Instance Database on file system</td>\r\n</tr>\r\n<tr>\r\n<td>SI/ASM</td>\r\n<td>Oracle Single Instance Database on ASM (Automatic Storage Management)</td>\r\n</tr>\r\n<tr>\r\n<td>RAC</td>\r\n<td>Oracle Real Application Clusters Database</td>\r\n</tr>\r\n<tr>\r\n<td>RAC/FS</td>\r\n<td>Oracle Real Application Clusters Database on file system</td>\r\n</tr>\r\n<tr>\r\n<td>RAC/ASM</td>\r\n<td>Oracle Real Application Clusters Database on ASM</td>\r\n</tr>\r\n<tr>\r\n<td>ASM</td>\r\n<td>Oracle Database in ASM (SI/ASM or RAC/ASM)</td>\r\n</tr>\r\n<tr>\r\n<td>OES</td>\r\n<td>Oracle Database on Oracle Engineered Systems (Oracle Exadata, Oracle Database Appliance (ODA), Oracle SuperCluster)</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p>Based on these installation types, the parameter recommendations&#160;are structured&#160;as follows:</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td><span style=\"text-decoration: underline;\">Parameter Recommendation</span></td>\r\n<td>\r\n<p><span style=\"text-decoration: underline;\">Validity</span></p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>'<a target=\"_self\" href=\"#PARAM_BASIC\">Basic Database Parameter Settings</a>'</td>\r\n<td>\r\n<p>Valid for all Oracle installation types (basic settings +&#160;basic parameter <a target=\"_self\" href=\"#PARAM_BASIC_EVENT\">\"event\"</a> +&#160;basic parameter <a target=\"_self\" href=\"#PARAM_BASIC_FIX_CONTROL\">\"_fix_control\"</a> )</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>'<a target=\"_self\" href=\"#PARAM_WITH_LICENSE_COST\">Database Parameter Settings with Additional License Costs</a>'</td>\r\n<td>Valid for all Oracle installation types</td>\r\n</tr>\r\n<tr>\r\n<td>'<a target=\"_self\" href=\"#PARAM_FOR_SIFS\">Database Parameter Settings for SI/FS</a>'</td>\r\n<td>Valid only for Oracle installations of type Single Instance on file system (SI/FS), must be set in addition to basic settings</td>\r\n</tr>\r\n<tr>\r\n<td>'<a target=\"_self\" href=\"#PARAM_FOR_RAC\">Database Parameter Settings for RAC</a>'</td>\r\n<td>Valid only for Oracle installations of type&#160;RAC (incl. RAC on Oracle Engineered Systems),&#160;must be set in addition to basic settings</td>\r\n</tr>\r\n<tr>\r\n<td>'<a target=\"_self\" href=\"#PARAM_FOR_OES\">Database Parameter Settings for Oracle Engineered Systems</a>'</td>\r\n<td>Valid only for Oracle installations on Oracle Engineered Systems, must be set in addition to basic settings and RAC settings</td>\r\n</tr>\r\n<tr>\r\n<td>'<a target=\"_self\" href=\"#PARAM_FOR_ASM\">Database Parameter Settings for ASM</a>'</td>\r\n<td>Valid only for Oracle installations&#160;on ASM (except Oracle Engineered Systems),&#160;must be set in addition to basic settings</td>\r\n</tr>\r\n<tr>\r\n<td>'<a target=\"_self\" href=\"#PARAM_PLATFORMSPECIFIC\">Database Parameter Settings for Specific Platforms</a>'</td>\r\n<td>Valid only for&#160;specific platforms</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><strong><a target=\"_blank\" name=\"PARAM_BASIC\"></a>&#65279;Basic Database Parameter Settings</strong></p>\r\n<p>The table below contains SAP's parameter recommendations for Oracle Database 12c Release (12.1). These parameters are basic for every SAP Oracle database.</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td><span style=\"text-decoration: underline;\">Parameter</span></td>\r\n<td><span style=\"text-decoration: underline;\">Recommended Value</span></td>\r\n<td><span style=\"text-decoration: underline;\">Remark and Explanation</span></td>\r\n</tr>\r\n<tr>\r\n<td>audit_file_dest</td>\r\n<td>&lt;SAPDATA_HOME&gt;/saptrace/audit</td>\r\n<td>-</td>\r\n</tr>\r\n<tr>\r\n<td>compatible</td>\r\n<td>********.0</td>\r\n<td>\r\n<p>(SAP note <a target=\"_blank\" href=\"/notes/1739274\">1739274</a>)</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>control_file_record_keep_time</p>\r\n</td>\r\n<td>30 or higher</td>\r\n<td>-</td>\r\n</tr>\r\n<tr>\r\n<td>db_block_size</td>\r\n<td>8192</td>\r\n<td>-</td>\r\n</tr>\r\n<tr>\r\n<td>db_cache_size</td>\r\n<td>&lt;size of database buffer cache&gt;</td>\r\n<td>Size depends on the available memory<br />See SAP notes <a target=\"_blank\" href=\"/notes/789011\">789011</a>, <a target=\"_blank\" href=\"/notes/617416\">617416</a></td>\r\n</tr>\r\n<tr>\r\n<td>db_files</td>\r\n<td>&lt;number of datafiles&gt;</td>\r\n<td>\r\n<p>&gt; 200<br />Larger than the number of data files <br />that is expected in the short term</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>db_name</td>\r\n<td>&lt;dbname&gt;</td>\r\n<td>Name of the database</td>\r\n</tr>\r\n<tr>\r\n<td>diagnostic_dest</td>\r\n<td>&lt;SAPDATA_HOME&gt;/saptrace</td>\r\n<td>-</td>\r\n</tr>\r\n<tr>\r\n<td>event</td>\r\n<td>-</td>\r\n<td>Recommended setting for this parameter see separate table below.</td>\r\n</tr>\r\n<tr>\r\n<td>filesystemio_options</td>\r\n<td><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">&#65279;SETALL&#65279;</span></td>\r\n<td>\r\n<p>-</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>log_archive_format</td>\r\n<td>%t_%s_%r.dbf</td>\r\n<td>-</td>\r\n</tr>\r\n<tr>\r\n<td>log_buffer</td>\r\n<td>&lt;size of log buffer&gt;</td>\r\n<td>Set according to SAP note <a target=\"_blank\" href=\"/notes/1627481\">1627481</a>.</td>\r\n</tr>\r\n<tr>\r\n<td>log_checkpoints_to_alert</td>\r\n<td><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">&#65279;TRUE&#65279;</span></td>\r\n<td>-</td>\r\n</tr>\r\n<tr>\r\n<td>max_dump_file_size</td>\r\n<td>20000</td>\r\n<td>-<br /></td>\r\n</tr>\r\n<tr>\r\n<td>open_cursors</td>\r\n<td>800</td>\r\n<td>(up to a maximum of 2000)</td>\r\n</tr>\r\n<tr>\r\n<td>optimizer_adaptive_features</td>\r\n<td><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">&#65279;FALSE&#65279;</span></td>\r\n<td>\r\n<p>adaptive optimizer features must be disabled for SAP in release 12.1 (SAP Note <a target=\"_blank\" href=\"/notes/2537839\">2537839</a>)</p>\r\n<p>UNIX 12102x_date ( date &lt;&#160;201711 )<br />WIN ********.nP ( n &lt;&#160;170831 ) , see SAP Note <a target=\"_blank\" href=\"/notes/2537839\">2537839</a>&#160;before installing WINDBBP &gt;= 170831</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>optimizer_adaptive_plans</td>\r\n<td><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">&#65279;FALSE&#65279;</span></td>\r\n<td>\r\n<p>adaptive optimizer&#160;plans must be disabled for SAP in release 12.1 (SAP Note <a target=\"_blank\" href=\"/notes/2537839\">2537839</a>)</p>\r\n<p>UNIX 12102x_date ( date&#160;&gt;=&#160;201711 )<br />WIN ********.nP ( n&#160;&gt;=&#160;170831 )</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>optimizer_adaptive_statistics</td>\r\n<td><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">&#65279;FALSE&#65279;</span></td>\r\n<td>\r\n<p>adaptive optimizer&#160;statistics must be disabled for SAP in release 12.1 (SAP Note <a target=\"_blank\" href=\"/notes/2537839\">2537839</a>)</p>\r\n<p>UNIX 12102x_date ( date&#160;&gt;=&#160;201711 )<br />WIN ********.nP ( n&#160;&gt;=&#160;170831 )</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>optimizer_dynamic_sampling</td>\r\n<td>\r\n<p>SAP OLTP: Do not set!<br />SAP OLAP: 6</p>\r\n</td>\r\n<td>SAP OLAP Applications are: APO, BW,&#160;SEM</td>\r\n</tr>\r\n<tr>\r\n<td>optimizer_index_cost_adj</td>\r\n<td>SAP OLTP: 20<br />SAP OLAP: Do not set!</td>\r\n<td>SAP OLAP Applications are: APO, BW,&#160;SEM</td>\r\n</tr>\r\n<tr>\r\n<td>parallel_execution_message_size</td>\r\n<td>16384</td>\r\n<td>\r\n<p>Default value&#160;is 16384 on most platforms (see <a target=\"_blank\" href=\"http://docs.oracle.com/database/121/REFRN/refrn10156.htm#REFRN10156\">Oracle doc</a>)</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>parallel_min_servers</td>\r\n<td>0</td>\r\n<td>\r\n<p>No processes will be created during instance startup for parallel operations such as parallel query, parallel index creation or parallel create as select.</p>\r\n<p>Parallel processes will be dynamically started for any parallel operation needed.</p>\r\n<p>Value of 0 was the default in Oracle Release 11.2 which has proven to be the correct setting for SAP systems.</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>parallel_max_servers</td>\r\n<td>#DB-CPU-Cores * 10</td>\r\n<td>#DB-CPU-Cores is equivalent to 'CPU_COUNT'<br />SQL&gt; show parameter cpu_count</td>\r\n</tr>\r\n<tr>\r\n<td>parallel_threads_per_cpu</td>\r\n<td>1</td>\r\n<td>-</td>\r\n</tr>\r\n<tr>\r\n<td>pga_aggregate_target</td>\r\n<td>OLTP: 20 % of available memory<br />OLAP: 40 % of available memory</td>\r\n<td>-</td>\r\n</tr>\r\n<tr>\r\n<td>pre_page_sga</td>\r\n<td><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">&#65279;FALSE&#65279;</span></td>\r\n<td>\r\n<p>-</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>processes</td>\r\n<td>\r\n<p>(#ABAP work processes * 2) + <br />(#J2EE server processes * &lt;max-connections&gt; ) +<br />PARALLEL_MAX_SERVERS + 40</p>\r\n</td>\r\n<td>\r\n<p>max. number of OS processes that can <br />simultaneously connect to the database.</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>query_rewrite_enabled</td>\r\n<td><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">&#65279;FALSE&#65279;</span></td>\r\n<td>-</td>\r\n</tr>\r\n<tr>\r\n<td>recyclebin</td>\r\n<td><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">&#65279;OFF&#65279;</span></td>\r\n<td>-</td>\r\n</tr>\r\n<tr>\r\n<td>replication_dependency_tracking</td>\r\n<td><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">&#65279;FALSE&#65279;</span></td>\r\n<td>\r\n<p>Set to FALSE if Oracle Replication is not used <br />(Oracle Default is TRUE, see <a target=\"_blank\" href=\"http://docs.oracle.com/database/121/REFRN/refrn10187.htm#REFRN10187\">Oracle doc</a>).</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>sessions</td>\r\n<td>2 * &lt;PROCESSES&gt;</td>\r\n<td>-</td>\r\n</tr>\r\n<tr>\r\n<td>shared_pool_size</td>\r\n<td>400MB or more</td>\r\n<td>(SAP Note <a target=\"_blank\" href=\"/notes/690241\">690241</a>)</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>sql92_security</p>\r\n</td>\r\n<td>DV: <span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">TRUE</span><br />non-DV: <span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">TRUE</span> or <span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">FALSE&#65279;</span></td>\r\n<td>\r\n<p>For Database Vault&#160;systems, setting <span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">sql92_security=TRUE</span> is mandatory.<br />&#65279;For non-DV systems, setting <span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">sql92_security=TRUE</span> is recommended (for security reasons).</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>star_transformation_enabled</td>\r\n<td>\r\n<p>SAP OLTP: Do not set!<br />SAP OLAP: <span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">TRUE&#65279;</span></p>\r\n</td>\r\n<td>\r\n<p>SAP OLAP Applications are: APO, BW,&#160;SEM</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>undo_retention</td>\r\n<td>set if required</td>\r\n<td>(SAP Note <a target=\"_blank\" href=\"/notes/1035137\">1035137</a>)</td>\r\n</tr>\r\n<tr>\r\n<td>_awr_mmon_deep_purge_all_expired</td>\r\n<td><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">&#65279;TRUE&#65279;</span></td>\r\n<td>\r\n<p>-</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>_bug12963364_bug12963364_spacebg_sync_segblocks</td>\r\n<td><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">TRUE&#160;</span></td>\r\n<td>\r\n<p>WIN ********.nP ( n &gt;=&#160;170228 )</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>_bug12963364_spacebg_sync_segblocks</td>\r\n<td><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">TRUE&#160;</span></td>\r\n<td>\r\n<p>UNIX 12102x_date ( date &gt;=&#160;201702 )</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>_enable_space_preallocation</td>\r\n<td>0</td>\r\n<td>\r\n<p>UNIX 12102x_date ( 201705 &lt;= date &lt;= 201708 ) (Hot News <a target=\"_blank\" href=\"/notes/2538588\">2538588</a>)</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>_fix_control</td>\r\n<td>-</td>\r\n<td>Recommended setting for this parameter see separate table below.</td>\r\n</tr>\r\n<tr>\r\n<td>_in_memory_undo</td>\r\n<td><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">FALSE&#160;</span></td>\r\n<td>\r\n<p>UNIX 12102x_date&#160;&#160;( date &lt;= 201905 )<br />WIN ********<br />(Hot&#160;News&#160;<a target=\"_blank\" href=\"/notes/2812178\">2812178</a>, non-RAC systems only)</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>_ipddb_enable</td>\r\n<td>-</td>\r\n<td>\r\n<p>(DO_NOT_SET), (SAP Note <a target=\"_blank\" href=\"/notes/2919894\">2919894</a>)</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>_ktb_debug_flags</td>\r\n<td>8</td>\r\n<td>\r\n<p>(SAP Note <a target=\"_blank\" href=\"/notes/2005311\">2005311</a>, affects recovery)<br />UNIX 12102x_date ( date&#160;&lt;=&#160;201711 )<br />WIN ********.nP ( n&#160;&lt;=&#160;171017 )<br /><br /></p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>_log_segment_dump_parameter</p>\r\n</td>\r\n<td>FALSE</td>\r\n<td>Oracle Support Document <a target=\"_blank\" href=\"https://support.oracle.com/epmos/faces/DocumentDisplay?id=2049516.1\">2049516.1</a></td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>_log_segment_dump_patch</p>\r\n</td>\r\n<td>FALSE</td>\r\n<td>Oracle Support Document <a target=\"_blank\" href=\"https://support.oracle.com/epmos/faces/DocumentDisplay?id=2049516.1\">2049516.1</a></td>\r\n</tr>\r\n<tr>\r\n<td>_mutex_wait_scheme</td>\r\n<td>1</td>\r\n<td>(SAP Note <a target=\"_blank\" href=\"/notes/1588876\">1588876</a>)</td>\r\n</tr>\r\n<tr>\r\n<td>_mutex_wait_time</td>\r\n<td>10</td>\r\n<td>(SAP Note <a target=\"_blank\" href=\"/notes/1588876\">1588876</a>)</td>\r\n</tr>\r\n<tr>\r\n<td>_optim_peek_user_binds</td>\r\n<td><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">&#65279;FALSE&#65279;</span></td>\r\n<td>(SAP Note <a target=\"_blank\" href=\"/notes/755342\">755342</a>)</td>\r\n</tr>\r\n<tr>\r\n<td>_optimizer_adaptive_cursor_sharing</td>\r\n<td><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">&#65279;FALSE&#65279;</span></td>\r\n<td>-</td>\r\n</tr>\r\n<tr>\r\n<td>_optimizer_aggr_groupby_elim</td>\r\n<td><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">&#65279;FALSE&#65279;</span></td>\r\n<td>\r\n<p>(Hot News <a target=\"_blank\" href=\"/notes/2159551\">2159551</a>)</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>_optimizer_batch_table_access_by_rowid</p>\r\n</td>\r\n<td><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">&#65279;FALSE&#65279;</span></td>\r\n<td>\r\n<p>UNIX 12102x_date&#160;&#160;( date &lt;= 201605 )<br />WIN ********.nP ( n&#160; &lt;= 160531 )<br />(Hot&#160;News&#160;<a target=\"_blank\" href=\"/notes/2240098\" title=\"2240098  - Wrong data / missing data after unclustering tables\">2240098</a>)</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>_optimizer_extended_cursor_sharing_rel</p>\r\n</td>\r\n<td><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">&#65279;NONE&#65279;</span></td>\r\n<td>-</td>\r\n</tr>\r\n<tr>\r\n<td>_optimizer_reduce_groupby_key</td>\r\n<td><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">&#65279;FALSE&#65279;</span></td>\r\n<td>\r\n<p>(Hot News <a target=\"_blank\" href=\"/notes/2258559\">2258559</a>)</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>_optimizer_use_feedback</td>\r\n<td><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">&#65279;FALSE&#65279;</span></td>\r\n<td>-</td>\r\n</tr>\r\n<tr>\r\n<td><span lang=\"EN\" style=\"font-size: 12pt; font-family: 'Arial',sans-serif; color: black; line-height: 115%; mso-fareast-font-family: 'Times New Roman'; mso-ansi-language: EN; mso-fareast-language: DE; mso-bidi-language: AR-SA;\">_rowsets_enabled</span></td>\r\n<td><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">&#65279;FALSE&#65279;</span></td>\r\n<td>(Hot News <a target=\"_blank\" href=\"/notes/0002374058\">2374058</a>)</td>\r\n</tr>\r\n<tr>\r\n<td>_securefiles_concurrency_estimate</td>\r\n<td>50</td>\r\n<td>(SAP Note <a target=\"_blank\" href=\"/notes/1887235\">1887235</a>)</td>\r\n</tr>\r\n<tr>\r\n<td>_suppress_identifiers_on_dupkey</td>\r\n<td><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">&#65279;TRUE&#65279;</span></td>\r\n<td>\r\n<p>-</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>_use_single_log_writer</td>\r\n<td><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">&#65279;TRUE&#65279;</span></td>\r\n<td>\r\n<p>Oracle Support Document <a target=\"_blank\" href=\"https://support.oracle.com/epmos/faces/DocumentDisplay?id=1957710.1\">1957710.1</a></p>\r\n</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><strong><a target=\"_blank\" name=\"PARAM_BASIC_EVENT\"></a>&#65279;Basic Database Parameter \"EVENT\"</strong></p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td><span style=\"text-decoration: underline;\">Parameter</span></td>\r\n<td><span style=\"text-decoration: underline;\">Value</span></td>\r\n<td><span style=\"text-decoration: underline;\">Explanation</span></td>\r\n</tr>\r\n<tr>\r\n<td rowspan=\"12\">event</td>\r\n<td>'10027'</td>\r\n<td>(SAP Note <a target=\"_blank\" href=\"/notes/596420\">596420</a>)</td>\r\n</tr>\r\n<tr>\r\n<td>'10028'</td>\r\n<td>(SAP Note <a target=\"_blank\" href=\"/notes/596420\">596420</a>)</td>\r\n</tr>\r\n<tr>\r\n<td>'10142'</td>\r\n<td>(SAP Note <a target=\"_blank\" href=\"/notes/1284478\">1284478</a>)</td>\r\n</tr>\r\n<tr>\r\n<td>'10183'</td>\r\n<td>(SAP Note <a target=\"_blank\" href=\"/notes/128648\">128648</a>)</td>\r\n</tr>\r\n<tr>\r\n<td>'10191'</td>\r\n<td>(SAP Note <a target=\"_blank\" href=\"/notes/128221\">128221</a>)</td>\r\n</tr>\r\n<tr>\r\n<td>'10995 level 2'</td>\r\n<td>(SAP Note <a target=\"_blank\" href=\"/notes/1565421\">1565421</a>)</td>\r\n</tr>\r\n<tr>\r\n<td>'12099'</td>\r\n<td>(SAP Note <a target=\"_blank\" href=\"/notes/2407854\">2407854</a>)<br />SXD 12102x_date ( date &lt;=&#160;201610 )</td>\r\n</tr>\r\n<tr>\r\n<td>'38068 level 100'</td>\r\n<td>(SAP Note <a target=\"_blank\" href=\"/notes/176754\">176754</a>)</td>\r\n</tr>\r\n<tr>\r\n<td>'38085'</td>\r\n<td>(SAP Note <a target=\"_blank\" href=\"/notes/176754\">176754</a>)</td>\r\n</tr>\r\n<tr>\r\n<td>'38087'</td>\r\n<td>(SAP Note <a target=\"_blank\" href=\"/notes/948197\">948197</a>)</td>\r\n</tr>\r\n<tr>\r\n<td>'44951 level 1024'</td>\r\n<td>(SAP Note <a target=\"_blank\" href=\"/notes/1166242\">1166242</a>)</td>\r\n</tr>\r\n<tr>\r\n<td>'60025'</td>\r\n<td>(SAP Note <a target=\"_blank\" href=\"/notes/2393275\">2393275</a>)</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><strong><a target=\"_blank\" name=\"PARAM_BASIC_FIX_CONTROL\"></a>&#65279;Basic Database Parameter \"_FIX_CONTROL\"</strong></p>\r\n<p style=\"padding-left: 30px;\">Note:<br />(<a target=\"_blank\" name=\"PARAM_BASIC_FIX_CONTROL_EXPLANATION\"></a>&#65279;***) The default value of \"_fix_control\" depends on whether the fix is &#8203;&#8203;installed over a temporary fix (interim patch) or over a DBBP (Database Bundle Patch). If the correction comes over an interim patch, the corresponding _fix_control is by default ON '(so the correction is active). If the the correction comes over a DBBP, then the _fix_control is by default OFF and the correction is not active. In order not to have to differentiate these two cases by different parameter recommendations, we now always set this additional _fix_control value, regardless of whether the correction is installed as an interim patch or DBBP.</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td><span style=\"text-decoration: underline;\">Parameter</span></td>\r\n<td><span style=\"text-decoration: underline;\">Value</span></td>\r\n<td><span style=\"text-decoration: underline;\">Explanation</span></td>\r\n</tr>\r\n<tr>\r\n<td rowspan=\"61\">_fix_control</td>\r\n<td>&#65279;'5099019:ON'&#65279;</td>\r\n<td>dbms_stats counts leaf blocks correctly</td>\r\n</tr>\r\n<tr>\r\n<td>'5705630:ON'</td>\r\n<td>use optimal OR concatenation; SAP Note <a target=\"_blank\" href=\"/notes/176754\">176754</a></td>\r\n</tr>\r\n<tr>\r\n<td>'6055658:OFF'</td>\r\n<td>\r\n<p>calculate correct join card. with histograms</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>'6120483:OFF'</td>\r\n<td>-</td>\r\n</tr>\r\n<tr>\r\n<td>'6399597:ON'</td>\r\n<td>sort group by instead of hash group by; SAP Note <a target=\"_blank\" href=\"/notes/176754\">176754</a></td>\r\n</tr>\r\n<tr>\r\n<td>'6430500:ON'</td>\r\n<td>avoid that unique index not chosen</td>\r\n</tr>\r\n<tr>\r\n<td>'6440977:ON'</td>\r\n<td>consider redundant predicates in join; SAP Note <a target=\"_blank\" href=\"/notes/981875\">981875</a></td>\r\n</tr>\r\n<tr>\r\n<td>'6626018:ON'</td>\r\n<td>avoid to low filter costs; note 981875</td>\r\n</tr>\r\n<tr>\r\n<td>'6972291:ON'</td>\r\n<td>use column group selectivity with hgrm; SAP Note <a target=\"_blank\" href=\"/notes/1165319\">1165319</a></td>\r\n</tr>\r\n<tr>\r\n<td>'7168184:OFF'</td>\r\n<td>avoid multi-column/bloom filter problems on comp. Tab.</td>\r\n</tr>\r\n<tr>\r\n<td>'7324224:OFF'</td>\r\n<td>\r\n<p>remove predicates that are redundant because of subtrees</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>&#65279;'7658097:ON'&#65279;</td>\r\n<td>\r\n<p>temp. workaround for Oracle Bug 19875411</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>'8932139:ON'</td>\r\n<td>\r\n<p>use PGA for bloom filter with broadcast<br />UNIX 12102x_date ( date &gt;= 201811 )<br />WIN ********.nP ( n &gt;= 181130 )</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>'8937971:ON'</td>\r\n<td>\r\n<p>correct clause definition dbms_metadata.get_ddl</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>'9196440:ON'</td>\r\n<td>\r\n<p>fixes low distinct keys in index stats</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>'9495669:ON'</td>\r\n<td>\r\n<p>disable histogram use for join cardinality</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>'10038517:OFF'</td>\r\n<td>\r\n<p>(Hot News&#160;<a target=\"_blank\" href=\"/notes/2373505\">2373505</a>)<br />UNIX 12102x_date (&#160;date&#160;&lt; 201711 )<br />WIN ********.nP (&#160;n &#160;&lt; 161118 )</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>'13077335:ON'</td>\r\n<td>-</td>\r\n</tr>\r\n<tr>\r\n<td>'13627489:ON'</td>\r\n<td>-</td>\r\n</tr>\r\n<tr>\r\n<td>'14255600:ON'</td>\r\n<td>-</td>\r\n</tr>\r\n<tr>\r\n<td>'14595273:ON'</td>\r\n<td>-</td>\r\n</tr>\r\n<tr>\r\n<td>'14846352:OFF'</td>\r\n<td>\r\n<p dir=\"ltr\">UNIX 12102x_date ( date &lt;&#160;201605 )<br />WIN ********.nP ( n &lt; 160531 )</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>'17973658:ON'</td>\r\n<td>\r\n<p>UNIX 12102x_date ( date &gt;=&#160;201711 ) (***)<br />WIN ********.nP ( n &gt;=&#160;171017 ) (***)</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>&#65279;'18405517:2'&#65279;</td>\r\n<td>-</td>\r\n</tr>\r\n<tr>\r\n<td>'18876528:ON'</td>\r\n<td>\r\n<p>UNIX 12102x_date ( date &gt;=&#160;201711 ) (***)<br />WIN ********.nP ( n &gt;=&#160;171017 ) (***)</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>'19475484:ON'</p>\r\n</td>\r\n<td>\r\n<p dir=\"ltr\">UNIX 12102x_date ( date &gt;= 201802 )<br />WIN ********.nP ( n &gt;= 180116 )</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>'19507904:ON'</td>\r\n<td>\r\n<p>UNIX 12102x_date ( date &gt;=&#160;201711 ) (***)<br />WIN ********.nP ( n &gt;=&#160;171017 ) (***)</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>'19563657:ON'</td>\r\n<td>\r\n<p>UNIX 12102x_date ( date &gt;=&#160;201711 ) (***)<br />WIN ********.nP ( n &gt;=&#160;171017 ) (***)</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>&#65279;'20107874:OFF'&#65279;</td>\r\n<td>\r\n<p>(SAP Note <a target=\"_blank\" href=\"/notes/2395585\">2395585</a>)<br />UNIX 12102x_date (&#160;201608 &lt;= date&#160;&lt; 201805 )<br />WIN ********.nP (&#160;161018 &lt;=&#160;n &#160;&lt; 180717 )</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>&#65279;'20107874:ON'&#65279;</td>\r\n<td>\r\n<p>(SAP Note <a target=\"_blank\" href=\"/notes/2395585\">2395585</a>)<br />UNIX 12102x_date ( date &gt;= 201805 )<br />WIN ********.nP ( n &gt;= 180717&#160;)</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>'20129763:ON'</td>\r\n<td>\r\n<p>UNIX 12102x_date ( date &gt;=&#160;201711 ) (***)<br />WIN ********.nP ( n &gt;=&#160;171017 ) (***)</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>'20228468:OFF'&#160;</td>\r\n<td>\r\n<p>UNIX 12102x_date ( date &gt;= 201705 )</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>'20355502:10'</td>\r\n<td>\r\n<p>reduces parse time with OR-expansion (SAP Note <a target=\"_blank\" href=\"/notes/2698967\">2698967</a>)<br />UNIX 12102x_date ( date &gt;=&#160;201802 )<br />WIN ********.nP ( n &gt;=&#160;180417 )</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>'20636003:OFF'</td>\r\n<td>\r\n<p>enable (ON)/disable (OFF)&#160;use of dynamic sampling to estimate the row count from&#160;a query block<br />UNIX 12102x_date ( date &gt;=&#160;201708 )<br />WIN ********.nP ( n &gt;=&#160;170531 )</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>'20914534:OFF'</td>\r\n<td>\r\n<p>UNIX 12102x_date ( date &gt;=&#160;201711 ) (***)<br />WIN ********.nP ( n &gt;=&#160;171017 ) (***)</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>'21509656:ON'</p>\r\n</td>\r\n<td>\r\n<p>UNIX 12102x_date ( date &gt;= 201802 )<br />WIN ********.nP ( n &gt;=&#160;180417 )</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>'21802552:ON'</p>\r\n</td>\r\n<td>\r\n<p>UNIX 12102x_date ( date &gt;= 201802 )</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>'21833220:ON'</td>\r\n<td>\r\n<p>UNIX 12102x_date ( date &gt;=&#160;201711 ) (***)<br />WIN ********.nP ( n &gt;=&#160;171017 ) (***)</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>'22077191:ON'</td>\r\n<td>\r\n<p>UNIX 12102x_date ( date &gt;=&#160;201711 ) (***)<br />WIN ********.nP ( n &gt;=&#160;171017 ) (***)</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>'22174392:ON'</td>\r\n<td>\r\n<p>first k row optimization for window function rownum predicate<br />UNIX 12102x_date ( date &gt;=&#160;202005 )</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>'22518491:ON'</td>\r\n<td>\r\n<p>UNIX 12102x_date ( date &gt;=&#160;201711 ) (***)<br />WIN ********.nP ( n &gt;=&#160;171017 ) (***)</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>'22533539:ON'</td>\r\n<td>\r\n<p>UNIX 12102x_date ( date &gt;=&#160;201711 ) (***)<br />WIN ********.nP ( n &gt;=&#160;171017 ) (***)</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>'22540411:ON'</p>\r\n</td>\r\n<td>\r\n<p>use Hash group by with Sort ordering as default aggregation method<br />UNIX 12102x_date ( date &gt;=&#160;201605 )<br />WIN ********.nP ( n &gt;=&#160;160531 )</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>'22746853:ON'</p>\r\n</td>\r\n<td>\r\n<p>UNIX 12102x_date ( date &gt;=&#160;201711 ) (***)<br />WIN ********.nP ( n &gt;=&#160;171017 ) (***)</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>'23102649:ON'</p>\r\n</td>\r\n<td>\r\n<p>UNIX 12102x_date ( date &gt;=&#160;201711 ) (***)<br />WIN ********.nP ( n &gt;=&#160;171017 ) (***)</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>'23136865:ON'</p>\r\n</td>\r\n<td>\r\n<p>UNIX 12102x_date ( date &gt;=&#160;201711 ) (***)<br />WIN ********.nP ( n &gt;=&#160;171017 ) (***)</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>'23197730:ON'</p>\r\n</td>\r\n<td>\r\n<p>high parse time with multiple inlists<br />UNIX 12102x_date ( date &gt;=&#160;201711 ) (***)<br />WIN ********.nP ( n &gt;=&#160;171017 ) (***)</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>'23473108:ON'</p>\r\n</td>\r\n<td>\r\n<p>Partial JPPD with cartesian join in parent query block<br />UNIX ********.x_date (&#160;date&#160;&gt;= 202105 )</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>'23738304:ON'</p>\r\n</td>\r\n<td>\r\n<p>(SAP Note <a target=\"_blank\" href=\"/notes/2522894\">2522894</a>); set if inmemory_size &gt; 0 (Improvement of 'group by' placement)</p>\r\n<p>UNIX 12102x_date ( date &gt;=&#160;201711 ) (***)<br />WIN ********.nP ( n &gt;=&#160;171017 ) (***)</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>'23738304:OFF'</p>\r\n</td>\r\n<td>\r\n<p>(SAP Note <a target=\"_blank\" href=\"/notes/2522894\">2522894</a>), set if inmemory_size = 0</p>\r\n<p>UNIX 12102x_date ( date &gt;=&#160;201711 ) (***)<br />WIN ********.nP ( n &gt;=&#160;171017 ) (***)</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>'23738553:ON'</p>\r\n</td>\r\n<td>\r\n<p>group by is not pushed down to table level for inmemory tables<br />UNIX 12102x_date ( date &gt;=&#160;201711 ) (***)<br />WIN ********.nP ( n &gt;=&#160;180116 ) (***)</p>\r\n\r\n<p>&#160;</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>'25476149:ON'</p>\r\n</td>\r\n<td>\r\n<p>impose a limit on memory used by bitmap access paths<br />UNIX 12102x_date ( date &gt;=&#160;201711 )<br />WIN ********.nP ( n &gt;=&#160;171017 )</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>'25643889:ON'</p>\r\n</td>\r\n<td>\r\n<p>force inline with clause if plsql function in select<br />UNIX 12102x_date ( date &gt;=&#160;202005 )</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>'26536320:ON'</p>\r\n</td>\r\n<td>\r\n<p>disallow HASH GROUP BY for WiF + uncorrelated select list subquery<br />UNIX ********.x_date (&#160;date&#160;&gt;= 201808 )<br />WIN ********.nP ( n&#160;&gt;=&#160;180831 )</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>'27321179:ON'</p>\r\n</td>\r\n<td>\r\n<p dir=\"ltr\">LORE: consider only valid OR-chains for DNF nodes computation&#160;(SAP Note <a target=\"_blank\" href=\"/notes/2698967\">2698967</a>)<br />UNIX 12102x_date ( date &gt;= 201802 )<br />WIN ********.nP ( n&#160;&gt;=&#160;180417 )</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>'27466597:ON'</p>\r\n</td>\r\n<td>\r\n<p dir=\"ltr\">no adjustment to number of skips with multi-column statistics (SAP Note <a target=\"_blank\" href=\"/notes/2395585\">2395585</a>)<br />UNIX 12102x_date ( date &gt;= 201805 )<br />WIN ********.nP ( n&#160;&gt;=&#160;180717 )</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>'28072567:ON'</p>\r\n</td>\r\n<td>\r\n<p>reuse table stats for column if UA view has remote table<br />UNIX ********.x_date (&#160;date&#160;&gt;= 201905 )<br />WIN ********.nP ( n&#160;&gt;=&#160;181215 )</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>'28173995:ON'</p>\r\n</td>\r\n<td>\r\n<p>Prevent Cartesian Merge Join with partial join predicate pushdown<br />UNIX ********.x_date (&#160;date&#160;&gt;= 202105 )</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>'29867728:ON'</p>\r\n</td>\r\n<td>\r\n<p>Do not unset partial JPPD info in additional phase<br />UNIX ********.x_date (&#160;date&#160;&gt;= 202105 )</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>'30786641:ON'</p>\r\n</td>\r\n<td>\r\n<p>Relax restriction on JPPD within lateral view<br />UNIX ********.x_date (&#160;date&#160;&gt;= 202105 )</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>'31444353:1'</p>\r\n</td>\r\n<td>\r\n<p>separate fix control for GBP &amp; JPPD under fix 7658097<br />UNIX ********.x_date (&#160;date&#160;&gt;= 202011 )<br />WIN ********.nP ( n&#160;&gt;=&#160;210119 )</p>\r\n</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p>&#160;</p>\r\n<p>&#160;</p>\r\n<p>&#160;</p>\r\n<p><a target=\"_blank\" name=\"PARAM_WITH_LICENSE_COST\"></a>&#65279;<strong>Database Parameter&#160;Settings with Additional License Costs</strong></p>\r\n<p>The following parameter settings require additional licenses for the Oracle database. For information which licenses are included in the&#160;SAP Application Specific Full Use License (SAP ASFU), see SAP Note <a target=\"_blank\" href=\"/notes/**********\">105047</a>.</p>\r\n<p><span style=\"text-decoration: underline;\">Oracle Advanced Compression License</span></p>\r\n<p>The license for Oracle Advanced Compression Option is included in the SAP ASFU.<br />For information about using Oracle&#160;Advanced Compression Option&#160;with SAP NetWeaver see SAP Note <a target=\"_blank\" href=\"/notes/2138262\">2138262</a>.</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td><span style=\"text-decoration: underline;\">Parameter</span></td>\r\n<td><span style=\"text-decoration: underline;\">Oracle License</span></td>\r\n<td><span style=\"text-decoration: underline;\">Recommended Value</span></td>\r\n<td><span style=\"text-decoration: underline;\">Remark and Explanation</span></td>\r\n</tr>\r\n<tr>\r\n<td>heat_map</td>\r\n<td><a target=\"_blank\" href=\"http://docs.oracle.com/database/121/DBLIC/options.htm#CJACCDBA\">Oracle Advanced Compression</a></td>\r\n<td><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">&#65279;ON&#65279;</span></td>\r\n<td>\r\n<p>Enables Heat Map and Automatic Data Optimization features, see SAP Note <a target=\"_blank\" href=\"/notes/2254866\">2254866</a>.</p>\r\n<p>Set heat_map=ON only if ADO/ILM is used.</p>\r\n<p>UNIX 12102x_date ( date &gt;=&#160;201511 )<br />WIN ********.nP (&#160;n &gt;=&#160;10 )</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>_advanced_index_compression_options</td>\r\n<td><a target=\"_blank\" href=\"http://docs.oracle.com/database/121/DBLIC/options.htm#CJACCDBA\">Oracle Advanced Compression</a></td>\r\n<td>16</td>\r\n<td>\r\n<p>All newly created&#160;indexes will be compressed by default (Reference: SAP Note <a target=\"_blank\" href=\"/notes/2138262\">2138262</a>).</p>\r\n<p>UNIX 12102x_date ( date &gt;=&#160;201511 )<br />WIN ********.nP (&#160;n &gt;=&#160;10 )</p>\r\n</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><span style=\"text-decoration: underline;\">Oracle Database In-Memory</span></p>\r\n<p>A license for&#160;<a target=\"_blank\" href=\"http://docs.oracle.com/database/121/DBLIC/options.htm#CHDHFJDA\">Oracle Database In-Memory</a> is required if you set parameter 'inmemory_size' to a value &gt; 0 (enables in-memory column store). For best performance, additional parameters should be set as described in the following table. For information about using Oracle Database In-Memory Option with SAP NetWeaver see SAP Note <a target=\"_blank\" href=\"/notes/2178980\">2178980</a>.</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td>\r\n<p><span style=\"text-decoration: underline;\">Parameter</span></p>\r\n</td>\r\n<td>\r\n<p><span style=\"text-decoration: underline;\">Oracle License</span></p>\r\n</td>\r\n<td><span style=\"text-decoration: underline;\">Recommended Value</span></td>\r\n<td>\r\n<p><span style=\"text-decoration: underline;\">Remark and Explanation</span></p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>inmemory_clause_default</p>\r\n</td>\r\n<td>\r\n<p><a target=\"_blank\" href=\"http://docs.oracle.com/database/121/DBLIC/options.htm#CHDHFJDA\">Oracle Database In-Memory</a></p>\r\n</td>\r\n<td>\r\n<p><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">&#65279;PRIORITY HIGH</span><br /><br />&#65279;On Exadata/SuperCluster:<br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">PRIORITY HIGH DUPLICATE ALL&#65279;</span></p>\r\n</td>\r\n<td>\r\n<p>Set this parameter only if parameter inmemory_size &gt; 0.</p>\r\n<p><span style=\"text-decoration: underline;\">PRIORITY HIGH</span><br />Ensures&#160;that tables/partititions are loaded into the IM column store as quickly as possible;<br />Ensures&#160;that new inserted data of tables/partitions in the IM column store can be used by queries as quickly as possible<br /><span style=\"text-decoration: underline;\"><br />PRIORITY HIGH DUPLICATE ALL</span><br />Improves&#160;performance and high availability of the IM column store on Oracle Exadata and Oracle SuperCluster.</p>\r\n<p>UNIX 12102x_date ( date &gt;=&#160;201506 )<br />WIN ********.nP ( n &gt;=&#160;6 )</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>inmemory_max_populate_servers</p>\r\n</td>\r\n<td>\r\n<p><a target=\"_blank\" href=\"http://docs.oracle.com/database/121/DBLIC/options.htm#CHDHFJDA\">Oracle Database In-Memory</a></p>\r\n</td>\r\n<td>4</td>\r\n<td>\r\n<p>Set this parameter only if parameter inmemory_size &gt; 0.</p>\r\n<p>This parameter limits the resources for column store background processing on a system.</p>\r\n<p>UNIX 12102x_date ( date &gt;=&#160;201506 )<br />WIN ********.nP ( n &gt;=&#160;6 )</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>inmemory_size</p>\r\n</td>\r\n<td>\r\n<p><a target=\"_blank\" href=\"http://docs.oracle.com/database/121/DBLIC/options.htm#CHDHFJDA\">Oracle Database In-Memory</a></p>\r\n</td>\r\n<td>&lt;Size of the IM Column Store&gt;</td>\r\n<td>\r\n<p>Default Value of 0, which means that the IM column store is not used</p>\r\n<p>UNIX 12102x_date ( date &gt;=&#160;201506 )<br />WIN ********.nP ( n &gt;=&#160;6 )</p>\r\n</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p>&#160;</p>\r\n\r\n<p>&#160;</p>\r\n<p><span style=\"text-decoration: underline;\">Oracle Multitenant</span></p>\r\n<p>A license for <a target=\"_blank\" href=\"http://docs.oracle.com/database/121/DBLIC/options.htm#CJAEAJEI\">Oracle Multitenant</a>&#160;is required&#160;to use&#160;a container database (CDB) that has&#160;2 or more pluggable databases (PDBs). For single tenant - CDB with only one PDB - no Oracle multitenant license is required.</p>\r\n\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td><span style=\"text-decoration: underline;\">Parameter</span></td>\r\n<td><span style=\"text-decoration: underline;\"><span style=\"text-decoration: underline;\">Oracle License</span></span></td>\r\n<td><span style=\"text-decoration: underline;\">Recommended Value</span></td>\r\n<td><span style=\"text-decoration: underline;\">Remark and Explanation</span></td>\r\n</tr>\r\n<tr>\r\n<td>enable_pluggable_database</td>\r\n<td><a target=\"_blank\" href=\"http://docs.oracle.com/database/121/DBLIC/options.htm#CJAEAJEI\">Oracle Multitenant</a></td>\r\n<td>CDB: <span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">TRUE</span><br />Non-CDB: not set</td>\r\n<td>\r\n<p>UNIX 12102x_date ( date &gt;=&#160;201605 )<br />WIN ********.nP ( n &gt;=&#160;160531 )</p>\r\n<p>This parameter enables the Oracle multitenant architecture when a new CDB is created.<br />The value of this parameter can not be changed later.<br />For non-CDB databases, this parameter is not set (Default = <span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">FALSE</span>).</p>\r\n</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><span style=\"text-decoration: underline;\">Oracle Diagnostics Pack&#160;/ Oracle Tuning Pack</span></p>\r\n<p><span style=\"text-decoration: underline;\"><a target=\"_blank\" href=\"http://docs.oracle.com/database/121/DBLIC/options.htm#CIHIHDDJ\">Oracle Diagnostics Pack</a></span>&#160;includes automatic performance diagnostics and advanced system monitoring functionality. <a target=\"_blank\" href=\"http://docs.oracle.com/database/121/DBLIC/options.htm#CIHFIHFG\">Oracle Tuning Pack</a>&#160;provides database administrators with expert performance management for the Oracle environment. Oracle Diagnostics Pack is a prerequisite product to Oracle Tuning Pack. Both packs are required for&#160;SAP DBACOCKPIT (SAP Note <a target=\"_blank\" href=\"/notes/1028068\">1028068</a>). Without these packs,&#160;some of the SAP DBACOCKPIT functionality is not available. Licenses for both packs are included in the SAP ASFU license (SAP Note <a target=\"_blank\" href=\"/notes/740897\">740897</a>).</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td><span style=\"text-decoration: underline;\">Parameter</span></td>\r\n<td>\r\n<p><span style=\"text-decoration: underline;\">&#160;Oracle License</span></p>\r\n</td>\r\n<td><span style=\"text-decoration: underline;\">Recommended Value</span></td>\r\n<td>\r\n<p><span style=\"text-decoration: underline;\">Remark and Explanation</span></p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>control_management_pack_access</td>\r\n<td>\r\n<p><a target=\"_blank\" href=\"http://docs.oracle.com/database/121/DBLIC/options.htm#DBLIC159\">Oracle Diagnostic Pack</a><br /><a target=\"_blank\" href=\"http://docs.oracle.com/database/121/DBLIC/options.htm#DBLIC159\">Oracle Tuning Pack</a></p>\r\n</td>\r\n<td><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">&#65279;DIAGNOSTIC+TUNING&#65279;</span></td>\r\n<td>\r\n<p>'DIAGNOSTICS+TUNING' is the Oracle default value for this parameter in the Oracle Enterprise Edition.<br />If these packs are not licensed, you must set this parameter to &#8216;NONE&#8217;.</p>\r\n</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p>&#160;</p>\r\n<p><strong><a target=\"_blank\" name=\"PARAM_FOR_SIFS\"></a>Database Parameter Settings for SI/FS</strong></p>\r\n<p>The table below contains SAP's parameter recommendations for Oracle Database 12c Release (12.1) for SAP on an Oracle Single Instance database on file system (SI/FS).<br />Parameters listed here are set in addition to the basic parameters mentioned above, or with different value.</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td><span style=\"text-decoration: underline;\">Parameter</span></td>\r\n<td><span style=\"text-decoration: underline;\">Recommended Value</span></td>\r\n<td><span style=\"text-decoration: underline;\">Remark and Explanation</span></td>\r\n</tr>\r\n<tr>\r\n<td>control_files</td>\r\n<td>&lt;location of control files&gt;</td>\r\n<td>At least 3 copies on different disk areas</td>\r\n</tr>\r\n<tr>\r\n<td>local_listener</td>\r\n<td>(ADDRESS = (PROTOCOL=TCP)<br />(HOST=<em>&lt;hostname&gt;</em>) (PORT=<em>&lt;port&gt;</em>))</td>\r\n<td>Exception: Windows with Oracle Fail Safe<br />Reference: SAP&#160;Note <a target=\"_blank\" href=\"/notes/1915325\">1915325</a></td>\r\n</tr>\r\n<tr>\r\n<td>log_archive_dest_1</td>\r\n<td>'LOCATION=&lt;SAPDATA_HOME&gt;/oraarch/&lt;sid&gt;arch'</td>\r\n<td>SAP standard location for archive logs</td>\r\n</tr>\r\n<tr>\r\n<td>undo_tablespace</td>\r\n<td>PSAPUNDO</td>\r\n<td>SAP note <a target=\"_blank\" href=\"/notes/600141\">600141</a></td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p>&#160;</p>\r\n<p><strong><a target=\"_blank\" name=\"PARAM_FOR_RAC\"></a>Database Parameter Settings for RAC</strong></p>\r\n<p>The table below contains SAP's parameter recommendations for Oracle Database 12c Release (12.1) for SAP on Oracle Real Application Clusters (RAC).<br />Parameters listed here are set in addition to the basic parameters mentioned above, or with different value.</p>\r\n<p>The column 'RAC Instance' defines whether a parameter is valid for one or all RAC instances:<br />Parameters marked with '*' are valid for all RAC instances.<br />Parameters&#160;marked with '&lt;instance_name&gt;' are instance specific and must be specified for every RAC instance.</p>\r\n\r\n<p>&#160;</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td><span style=\"text-decoration: underline;\">RAC Instance</span></td>\r\n<td><span style=\"text-decoration: underline;\">Parameter</span></td>\r\n<td><span style=\"text-decoration: underline;\">Recommended Value</span></td>\r\n<td><span style=\"text-decoration: underline;\">Remark and Explanation</span></td>\r\n</tr>\r\n<tr>\r\n<td>*</td>\r\n<td>cluster_database</td>\r\n<td><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">&#65279;TRUE&#65279;</span></td>\r\n<td>Requires license for RAC</td>\r\n</tr>\r\n<tr>\r\n<td>*</td>\r\n<td>cluster_database_instances</td>\r\n<td>&lt;n&gt;</td>\r\n<td>number of&#160;RAC instances</td>\r\n</tr>\r\n<tr>\r\n<td>*</td>\r\n<td>control_files</td>\r\n<td>&lt;location of control files&gt;</td>\r\n<td>-</td>\r\n</tr>\r\n<tr>\r\n<td>*</td>\r\n<td>log_archive_dest_1</td>\r\n<td>'LOCATION=&lt;SAPDATA_HOME&gt;/oraarch/&lt;sid&gt;arch'</td>\r\n<td>\r\n<p>SAP standard location for archive logs</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>*</td>\r\n<td>remote_listener</td>\r\n<td>//&lt;scan_name&gt;:1521</td>\r\n<td>Name of the scan listener as defined in DNS ($srvctl config scan)</td>\r\n</tr>\r\n<tr>\r\n<td>&lt;instance_name&gt;</td>\r\n<td>instance_name</td>\r\n<td>&lt;instance_name&gt;</td>\r\n<td>Example for instance PRD001: PRD001<br />&lt;instancename&gt;=&lt;dbname&gt;+&lt;instance number&gt;</td>\r\n</tr>\r\n<tr>\r\n<td>&lt;instance_name&gt;</td>\r\n<td>instance_number</td>\r\n<td>&lt;instance_number&gt;</td>\r\n<td>\r\n<p>1-digit number&#160;(without leading zeros) or&#160;3-digit number (with leading zeros)<br />SAP Default: 3-digit number (see 'instance name')</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>&lt;instance_name&gt;</td>\r\n<td>local_listener</td>\r\n<td>(ADDRESS = (PROTOCOL=TCP)<br />(HOST=<em>&lt;hostname_vip&gt;</em>) (PORT=<em>1521</em>))</td>\r\n<td>\r\n<p>Use only host vip</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>&lt;instance_name&gt;</td>\r\n<td>service_names</td>\r\n<td>(&lt;dbname&gt;, &lt;instancename&gt;)</td>\r\n<td>\r\n<p>Example for instance PRD001: PRD, PRD001</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>&lt;instance_name&gt;</td>\r\n<td>thread</td>\r\n<td>&lt;thread_number&gt;</td>\r\n<td>instance number without leading '0's</td>\r\n</tr>\r\n<tr>\r\n<td>&lt;instance_name&gt;</td>\r\n<td>undo_tablespace</td>\r\n<td>PSAPUNDO&lt;instance_number&gt;</td>\r\n<td>Example for instance PRD001: PSAPUNDO001<br />SAP note <a target=\"_blank\" href=\"/notes/600141\">600141</a></td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p>&#160;</p>\r\n<p><strong><a target=\"_blank\" name=\"PARAM_FOR_ASM\"></a>Database Parameter&#160;Settings for ASM</strong></p>\r\n<p>The table below contains SAP's parameter recommendations for Oracle Database 12c Release (12.1) for SAP on Oracle Automatic Storage Management (ASM).<br />Parameters listed here are set in addition to the basic parameters mentioned above, or with different value.</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td><span style=\"text-decoration: underline;\">Parameter</span></td>\r\n<td><span style=\"text-decoration: underline;\">Recommended Value</span></td>\r\n<td><span style=\"text-decoration: underline;\">Remark and Explanation</span></td>\r\n</tr>\r\n<tr>\r\n<td>control_files</td>\r\n<td>'+ARCH/&lt;DBNAME&gt;/cntrl&lt;DBNAME&gt;.dbf'<br />'+DATA/&lt;DBNAME&gt;/cntrl&lt;DBNAME&gt;.dbf'<br />'+RECO/&lt;DBNAME&gt;/cntrl&lt;DBNAME&gt;.dbf'</td>\r\n<td>\r\n<p>At least 3 copies on different disk groups</p>\r\n<p>&#160;</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>db_create_file_dest</td>\r\n<td>'+DATA'</td>\r\n<td>data files, temp files</td>\r\n</tr>\r\n<tr>\r\n<td>db_create_online_log_dest_1</td>\r\n<td>'+DATA'</td>\r\n<td>online redo log 1st copy/member</td>\r\n</tr>\r\n<tr>\r\n<td>db_create_online_log_dest_2</td>\r\n<td>'+RECO'</td>\r\n<td>online redo log 2nd copy/member</td>\r\n</tr>\r\n<tr>\r\n<td>db_recovery_file_dest</td>\r\n<td>'+RECO'</td>\r\n<td>Disk group for Fast Recovery Area (FRA)</td>\r\n</tr>\r\n<tr>\r\n<td>log_archive_dest_1</td>\r\n<td>\r\n<p>'LOCATION=+&lt;DGNAME&gt;/&lt;DBNAME&gt;/oraarch'</p>\r\n</td>\r\n<td>SAP standard location for archive logs for SI&#160;and RAC</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p>&#160;</p>\r\n<p><strong><strong><a target=\"_blank\" name=\"PARAM_FOR_OES\"></a>&#65279;Database Parameter Settings for Oracle Engineered Systems</strong></strong></p>\r\n<p>The table below contains SAP's parameter recommendations for Oracle Database 12c Release (12.1) for SAP on Oracle Engineered Systems. <br />Parameters listed here are set in addition to the basic parameters mentioned above, or with different value.</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td><span style=\"text-decoration: underline;\">Parameter</span></td>\r\n<td><span style=\"text-decoration: underline;\">Recommended Value</span></td>\r\n<td><span style=\"text-decoration: underline;\">Remark and Explanation</span></td>\r\n</tr>\r\n<tr>\r\n<td>control_files</td>\r\n<td>'+DATA/&lt;DBNAME&gt;/cntrl&lt;DBNAME&gt;.dbf'<br />'+RECO/&lt;DBNAME&gt;/cntrl&lt;DBNAME&gt;.dbf'</td>\r\n<td>\r\n<p>2&#160;copies on different disk groups</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>db_create_file_dest</td>\r\n<td>'+DATA'</td>\r\n<td>\r\n<p>-</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>db_create_online_log_dest_1</td>\r\n<td>'+DATA'</td>\r\n<td>\r\n<p>online redo log 1st copy/member;</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>db_create_online_log_dest_2</td>\r\n<td>'+RECO'</td>\r\n<td>\r\n<p>online redo log 2nd copy/member;<br />Do not configure a 2<sup>nd</sup> member if disk group &#160;&#8216;+DATA&#8217; is configured with high redundancy (see white paper)</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>db_recovery_file_dest</td>\r\n<td>'+RECO'</td>\r\n<td>\r\n<p>-</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>log_archive_dest_1</td>\r\n<td>\r\n<p>'LOCATION=+&lt;DGNAME&gt;/&lt;DBNAME&gt;/oraarch'</p>\r\n</td>\r\n<td>\r\n<p>SAP standard location for archive logs</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>log_buffer</p>\r\n</td>\r\n<td>\r\n<p>128M or higher</p>\r\n</td>\r\n<td>\r\n<p>Set according to SAP note <a target=\"_blank\" href=\"/notes/1627481\">1627481</a>.<br />On Oracle Exadata&#160;LOG_BUFFER &gt;= 128M<br />On Oracle SPARC SuperCluster LOG_BUFFER &gt;= 128M</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>_file_size_increase_increment</p>\r\n</td>\r\n<td>\r\n<p>2143289344</p>\r\n</td>\r\n<td>\r\n<p>Required for RMAN backup performance on Oracle Engineered Systems</p>\r\n</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p>&#160;</p>\r\n<p><strong><strong><strong><strong><a target=\"_blank\" name=\"PARAM_PLATFORMSPECIFIC\"></a>&#65279;Database Parameter Settings for Specific Platforms</strong></strong></strong></strong></p>\r\n<p>The table below contains platform-specific parameter recommendations for Oracle Database 12c Release (12.1) for SAP.</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td><span style=\"text-decoration: underline;\">Parameter</span></td>\r\n<td><span style=\"text-decoration: underline;\">Platform</span></td>\r\n<td><span style=\"text-decoration: underline;\">Recommended Value</span></td>\r\n<td><span style=\"text-decoration: underline;\">Remark and Explanation</span></td>\r\n</tr>\r\n<tr>\r\n<td>disk_asynch_io</td>\r\n<td>HP-UX</td>\r\n<td><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">&#65279;FALSE&#65279;</span></td>\r\n<td>\r\n<p>(SAP Note <a target=\"_blank\" href=\"/notes/2799946\">2799946</a>)</p>\r\n<p>If set to 'FALSE': HP-UX only and only for standard filesystems; not for OnlineJFS (VxFS 5.x), not for ASM, not for raw devices, Reference: bug 19825394; See SAP note <a target=\"_blank\" href=\"/notes/798194\">798194</a></p>\r\n<p>If set to 'TRUE': Ensure that HP-UX asynchronous I/O support is enabled and configured according to Oracle database documentation. If not, you might see error like <br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">ORA-27090: Unable to reserve kernel resources for asynchronous disk I/O</span><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">Additional information: 2</span></p>\r\n<p>Reference: <a target=\"_blank\" href=\"https://docs.oracle.com/database/121/UNXAR/appb_hpux.htm\">Database Administrator's Reference</a>, <span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">D - Administering Oracle Database on HP-UX, D.4 - Asynchronous Input-Output</span></p>\r\n\r\n<p>If required, give the device file the operating system owner and permissions consistent with those of the Oracle software owner and OSDBA group (oracle:oinstall or ora&lt;dbsid&gt;:dba).<br /># /usr/bin/chown oracle:oinstall /dev/async<br /># /usr/bin/chown ora&lt;dbsid&gt;:dba /dev/async<br /># /usr/bin/chmod 660 /dev/async</p>\r\n<p>For better understanding how and when you can set this parameter to false:</p>\r\n<ul>\r\n<li>The default value of this parameter is TRUE.</li>\r\n<li>If your platform supports asynchronous I/O to disk, Oracle recommends that you leave this parameter set to its default value. In this case, ensure that HP-UX asynchronous I/O support is enabled and configured according to Oracle database documentation. If not, you might see error like <br />ORA-27090: Unable to reserve kernel resources for asynchronous disk I/O</li>\r\n<li>For <strong>SAP with Oracle database on HP-UX</strong> (only for HP-UX) we allow to set the parameter to <strong>disk_asynch_io=FALSE</strong> if the database files reside on an standard file system, but not OnlineJFS (VxFS 5.x)</li>\r\n<li>With other words: if your SAP database of release 12c or later resides on (1) HP-UX with OnlineJFS (VxFS 5.x) or (2) on ASM or (3) on a platform other than HP-UX, then use the Oracle default value (TRUE). Raw Devices are not supported any more starting 12c.</li>\r\n</ul>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>_enable_numa_support</td>\r\n<td>all</td>\r\n<td>see MOS note <a target=\"_blank\" href=\"https://support.oracle.com/epmos/faces/DocumentDisplay?id=864633.1\">864633.1</a></td>\r\n<td>\r\n<p>By default, '_ENABLE_NUMA_SUPPORT' is not set (=FALSE) and <br />Oracle NUMA support (Non Uniform Memory Architecture) is disabled.</p>\r\n<p>Before you enable Oracle NUMA support in a production system by setting <br />this parameter to TRUE it is necessary to run additional (performance) tests: <br />you need to evaluate the performance before and after enabling NUMA in a <br />test environment before you go into production. <br />Additional information can be found in My Oracle Support note <a target=\"_blank\" href=\"https://support.oracle.com/epmos/faces/DocumentDisplay?id=864633.1\">864633.1</a>.</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td><span style=\"font-size: 11pt; font-family: 'Calibri',sans-serif; color: black; mso-fareast-font-family: Calibri; mso-fareast-theme-font: minor-latin; mso-ansi-language: DE; mso-fareast-language: EN-US; mso-bidi-language: AR-SA; mso-bidi-font-family: 'Times New Roman';\">_px_numa_support_enabled</span></td>\r\n<td>all</td>\r\n<td>depending on _enable_numa_support</td>\r\n<td>\r\n<p>If \"_enable_NUMA_support\"=TRUE then you should&#160;also set \"_px_numa_support_enabled\"=TRUE.<br />This is required if you perform parallel operations&#160;like parallel query or create index under NUMA support.</p>\r\n<p>For details, see <a target=\"_blank\" href=\"https://support.oracle.com/epmos/faces/DocumentDisplay?id=1956463.1\">Oracle Support Document 1956463.1</a></p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>hpux_sched_noage</td>\r\n<td>HP-UX</td>\r\n<td>178</td>\r\n<td>\r\n<p>HP-UX only, without RAC</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>use_large_pages</td>\r\n<td>Linux</td>\r\n<td>see SAP note <a target=\"_blank\" href=\"/notes/1672954\">1672954</a></td>\r\n<td>\r\n<p>Linux only<br />Possible values: TRUE, FALSE, ONLY<br />see SAP note <a target=\"_blank\" href=\"/notes/1672954\">1672954</a></p>\r\n</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p>&#160;</p>\r\n<p><strong>Change History Archive (2016 and older)</strong></p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td><span style=\"text-decoration: underline;\">Date</span></td>\r\n<td><span style=\"text-decoration: underline;\">Change</span></td>\r\n</tr>\r\n<tr>\r\n<td>December 6, 2016</td>\r\n<td>\r\n<p>New&#160;value&#160;for parameter \"_fix_control\"='20107874:OFF' added</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>December 1, 2016</td>\r\n<td>\r\n<p>Added recommendation for \"sql92_security\"&#160;to basic parameters<br />Added new parameter \"_px_numa_support_enabled\"<br />Added recommendation for \"log_archive_dest_1\" to RAC parameter<br />Updated recommendation for \"_advanced_index_compression_options\"</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>October 7, 2016</td>\r\n<td>\r\n<p>Collective&#160;update<br />New parameter _rowsets_enabled=false added (Hot News <a target=\"_blank\" href=\"/notes/2374058\">2374058</a>)<br />New&#160;value&#160;for parameter \"_fix_control\"='10038517:OFF' added (Hot News <a target=\"_blank\" href=\"/notes/2373505\">2373505</a>)<br />Recommendation&#160;for database parameter 'log_archive_dest_1' adapted<br />Recommendation for parameter inmemory_clause_default adapted<br />Recommendation for parameter&#160;_optimizer_batch_table_access_by_rowid adapted<br />Recommendation for parameter star_transformation_enabled adapted<br />Note structure for parameters with additional license costs&#160;changed (separate tables per license)<br />German&#160;translation of certain&#160;parameter values corrected</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>July 6, 2016</td>\r\n<td>\r\n<p>Added new parameter enable_pluggable_database</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>July 4, 2016</td>\r\n<td>\r\n<p>Added new parameter parallel_min_servers=0 (was default in 11.2)</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>June 7, 2016</td>\r\n<td>\r\n<p>Added \"_fix_control\"='22540411:ON'</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>February 23, 2016</td>\r\n<td>\r\n<p>Added \"_fix_control\"='14846352:OFF'</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>December 23, 2015</td>\r\n<td>\r\n<p>Changed&#160;heat_map parameter, SAP Note <a target=\"_blank\" href=\"/notes/2254866\">2254866</a></p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>December 16, 2015</td>\r\n<td>\r\n<p>Added \"_optimizer_reduce_groupby_key\"=FALSE (Hot News <a target=\"_blank\" href=\"/notes/2258559\">2258559</a>)</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>December 3, 2015</td>\r\n<td>\r\n<p>Added \"_fix_control\"='20355502:8'</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>November 6, 2015</td>\r\n<td>\r\n<p>Added \"_optimizer_batch_table_access_by_rowid\"=FALSE (Hot News <a target=\"_blank\" href=\"/notes/2240098\">2240098</a>)</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>September 30, 2015</td>\r\n<td>\r\n<p>Updated control_management_pack_access</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>August 13, 2015</td>\r\n<td>\r\n<p>Added inmemory_clause_default, updated inmemory_size and inmemory_max_populate_servers</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>June 30, 2015</td>\r\n<td>\r\n<p>Added inmemory_size and inmemory_max_populate_servers, SAP Note <a target=\"_blank\" href=\"/notes/2178980\">2178980</a></p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>May 12, 2015</td>\r\n<td>Correction: \"_optimizer_aggr_groupby_elim\"=FALSE (Hot News <a target=\"_blank\" href=\"/notes/2159551\">2159551</a>)</td>\r\n</tr>\r\n<tr>\r\n<td>April 9, 2015</td>\r\n<td>Correction: parameter \"_bug16850197_enable_fix_for_13602883\" removed</td>\r\n</tr>\r\n<tr>\r\n<td>April 7, 2015</td>\r\n<td>Correction: OLAP or OLTP: CRM must be configured as OLTP</td>\r\n</tr>\r\n<tr>\r\n<td>March 31, 2015</td>\r\n<td>Initial version of this SAP Note released for customers</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p>&#160;</p>\r\n\r\n<p><strong><a target=\"_blank\" name=\"APPENDIX\"></a>&#65279;Appendix</strong></p>\r\n<p><span style=\"text-decoration: underline;\">Viewing Parameter Settings</span></p>\r\n<p>You can view parameter settings in several ways, see <span style=\"text-decoration: underline;\"><a target=\"_blank\" href=\"http://docs.oracle.com/database/121/ADMIN/create.htm#ADMIN11124\">http://docs.oracle.com/database/121/ADMIN/create.htm#ADMIN11124</a></span></p>\r\n<p><span style=\"text-decoration: underline;\">Which parameters are configured in the Server Parameter File?</span></p>\r\n<p>From SQL*Plus, run the following commands to find out which parameters are set in the SPFILE:</p>\r\n<p style=\"padding-left: 30px;\"><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">&#65279;set pages 1000</span><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">set lines 200</span><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">column sid format A10</span><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">column name format A40</span><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">column value format A80</span><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">column \"Instance Parameter\" format A50</span><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">column \"Parameter Value\" format A80</span><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">select sid || '.' || name \"Instance Parameter\",&#160;value \"Parameter Value\"</span><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">&#160; from v$spparameter&#160;</span><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">&#160;&#160;where isspecified = 'TRUE'&#160;</span><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">&#160;&#160;order by sid, name;&#65279;</span></p>\r\n<p><span style=\"text-decoration: underline;\">Which parameters are set to a non-default value?</span></p>\r\n<p>From SQL*Plus, run the following commands to find out which parameters are set to a non-default value:</p>\r\n<p style=\"padding-left: 30px;\"><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">&#65279;set pagesize 5000 </span><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">set linesize 128 </span><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">col KSPPINM&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; format a50 </span><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">col KSPPSTVL&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; format a40 </span><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">col \"Parameter Name\"&#160; format A50</span><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">col \"Parameter Value\" format A50</span><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">select </span><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">&#160; i.KSPPINM&#160; \"Parameter Name\", </span><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">&#160; c.KSPPSTVL \"Parameter Value\", </span><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">&#160; c.KSPPSTDF \"Default?\" </span><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">&#160; from x$ksppi i, x$ksppcv c </span><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">&#160;&#160;&#160; where i.indx=c.indx </span><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">&#160;&#160;&#160;&#160;&#160; and c.KSPPSTDF = 'FALSE'</span><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">&#160; order by 1 asc;&#65279;</span></p>\r\n<p><span style=\"text-decoration: underline;\">SQL Command Syntax for Database Parameter 'event'</span></p>\r\n<p><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">&#65279;SQL&gt; ALTER SYSTEM SET EVENT= '&lt;event_1&gt;', '&lt;event_2&gt;' SCOPE = SPFILE;</span><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">SQL&gt; ALTER SYSTEM SET EVENT = '10027','10028','38068 level 100' SCOPE=SPFILE;</span><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">SQL&gt;&#160;ALTER SYSTEM RESET EVENT;</span></p>\r\n<p><span style=\"text-decoration: underline;\">SQL Command Syntax for Database Parameter 'fix_control'</span></p>\r\n<p><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">&#65279;SQL&gt; ALTER SYSTEM SET \"_FIX_CONTROL\" = '&lt;bugid_1&gt;:&lt;val_1&gt;', '&lt;bugid_2&gt;:&lt;val_2&gt;' SCOPE=SPFILE;</span><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">SQL&gt; ALTER SYSTEM SET \"_FIX_CONTROL\" ='5099019:ON','5705630:ON' SCOPE=SPFILE;</span><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">SQL&gt; ALTER SYSTEM RESET \"_FIX_CONTROL\";&#65279;</span></p>\r\n<p>&#160;</p>\r\n"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "BC-DB-ORA-DBA (Database Administration)"}, {"Key": "Database System", "Value": "ORACLE"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON> (C5000979)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (C5041128)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0001888485/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001888485/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001888485/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001888485/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001888485/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001888485/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001888485/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001888485/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001888485/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": [{"FileName": "scripts-20150303.zip", "FileSize": "1238", "MimeType": "application/x-zip-compressed", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000693012013&iv_version=0066&iv_guid=D31CADB2CFE23744B3A77C533723097E"}]}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2812178", "RefComponent": "BC-DB-ORA", "RefTitle": "ORA-00600: internal error code, arguments: [2025]  during recovery", "RefUrl": "/notes/2812178"}, {"RefNumber": "2374058", "RefComponent": "BC-DB-ORA", "RefTitle": "12c: Wrong results using hash joins and rowsets", "RefUrl": "/notes/2374058"}, {"RefNumber": "2373505", "RefComponent": "BC-DB-ORA", "RefTitle": "12c: Corrupt index with CREATE INDEX ONLINE", "RefUrl": "/notes/2373505"}, {"RefNumber": "2336881", "RefComponent": "BC-DB-ORA", "RefTitle": "Using Oracle Multitenant with SAP NetWeaver based Products", "RefUrl": "/notes/2336881"}, {"RefNumber": "2258559", "RefComponent": "BC-DB-ORA", "RefTitle": "Oracle 12c: Wrong results when  _optimizer_reduce_groupby_key_=true", "RefUrl": "/notes/2258559"}, {"RefNumber": "2254866", "RefComponent": "BC-DB-ORA", "RefTitle": "Using Oracle Database Automatic Data Optimization with SAP NetWeaver", "RefUrl": "/notes/2254866"}, {"RefNumber": "2240098", "RefComponent": "BC-DB-ORA", "RefTitle": "Wrong data / missing data after unclustering tables", "RefUrl": "/notes/2240098"}, {"RefNumber": "2178980", "RefComponent": "BC-DB-ORA", "RefTitle": "Using Oracle Database In-Memory with SAP NetWeaver based Products", "RefUrl": "/notes/2178980"}, {"RefNumber": "2159551", "RefComponent": "BC-DB-ORA", "RefTitle": "Oracle 12.1: Wrong results when _optimizer_aggr_groupby_elim=true", "RefUrl": "/notes/2159551"}, {"RefNumber": "1914631", "RefComponent": "BC-DB-ORA", "RefTitle": "Central Technical Note for Oracle Database 12c Release 1 (12.1)", "RefUrl": "/notes/1914631"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1898915", "RefComponent": "BC-DB-ORA", "RefTitle": "FAQ: Oracle Hidden Parameter - NetWeaver", "RefUrl": "/notes/1898915 "}, {"RefNumber": "2346988", "RefComponent": "BC-CTS", "RefTitle": "Long Runtime in transport step \"Generation of Programs and Screens\" <SourceSID>G9<number>.<targetSID>", "RefUrl": "/notes/2346988 "}, {"RefNumber": "2968993", "RefComponent": "BC-DB-ORA", "RefTitle": "BR0978W parameter: COMPATIBLE, value: % (<>°%) in DB13/DBACOCKPIT", "RefUrl": "/notes/2968993 "}, {"RefNumber": "2163425", "RefComponent": "BC-CTS-CCO", "RefTitle": "Recommendations for client copy performance improvement", "RefUrl": "/notes/2163425 "}, {"RefNumber": "1616401", "RefComponent": "BC-UPG-TLS-TLA", "RefTitle": "Parallelism in the Upgrades, EhPs and Support Packages implementations", "RefUrl": "/notes/1616401 "}, {"RefNumber": "2694512", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Warning BR1640W: (probably missing permissions) occurs when archiving logs - NetWeaver", "RefUrl": "/notes/2694512 "}, {"RefNumber": "1875086", "RefComponent": "BC-DB-ORA", "RefTitle": "ORA-12537 when connecting to the database", "RefUrl": "/notes/1875086 "}, {"RefNumber": "2064052", "RefComponent": "BC-UPG-TLS-TLA", "RefTitle": "\"ORA-00922: missing or invalid option\" error in phase MAIN_SHDCRE/SUBMOD_SHDDBCLONE/DBCLONE or MAIN_NEWBAS/PARCONV_UPG", "RefUrl": "/notes/2064052 "}, {"RefNumber": "2571948", "RefComponent": "BC-DB-ORA", "RefTitle": "ORA-16032: parameter LOG_ARCHIVE_DEST_1 destination string cannot be translated", "RefUrl": "/notes/2571948 "}, {"RefNumber": "2546451", "RefComponent": "BC-DB-ORA", "RefTitle": "Wrong Values with \"Deactivate DB Optimizer Functions\" of BW Query in RSRT - NetWeaver", "RefUrl": "/notes/2546451 "}, {"RefNumber": "1873631", "RefComponent": "BC-DB-ORA", "RefTitle": "Solving Oracle memory issues", "RefUrl": "/notes/1873631 "}, {"RefNumber": "2919894", "RefComponent": "BC-DB-ORA", "RefTitle": "Oracle Database Parameter \"_ipddb_enable\"", "RefUrl": "/notes/2919894 "}, {"RefNumber": "2847437", "RefComponent": "BC-DB-ORA", "RefTitle": "Older Versions:  SAP Software and Oracle Exadata", "RefUrl": "/notes/2847437 "}, {"RefNumber": "2698967", "RefComponent": "BC-DB-ORA", "RefTitle": "12c: Bad performance due to CBO does not choose OR expansion", "RefUrl": "/notes/2698967 "}, {"RefNumber": "2636470", "RefComponent": "BW-SYS-DB-ORA", "RefTitle": "BW Oracle RSRV parameter COMPATIBLE (12.1, 12.2)", "RefUrl": "/notes/2636470 "}, {"RefNumber": "2611764", "RefComponent": "BC-DB-ORA", "RefTitle": "12c: Nested Loop Joins return wrong results if parameter \"_optimizer_batch_table_access_by_rowid\" is set to FALSE", "RefUrl": "/notes/2611764 "}, {"RefNumber": "789011", "RefComponent": "BC-DB-ORA", "RefTitle": "FAQ: Oracle memory areas", "RefUrl": "/notes/789011 "}, {"RefNumber": "2537839", "RefComponent": "BC-DB-ORA", "RefTitle": "Database does not startup after installation of Windows Bundle ********.170831 1708", "RefUrl": "/notes/2537839 "}, {"RefNumber": "2378252", "RefComponent": "BC-DB-ORA", "RefTitle": "Oracle Database Initialization Parameters for SAP NetWeaver Systems", "RefUrl": "/notes/2378252 "}, {"RefNumber": "2375218", "RefComponent": "BC-DB-ORA-CCM", "RefTitle": "Enhancements in RSANAORA for tables (INMEMORY/NO INMEMORY)", "RefUrl": "/notes/2375218 "}, {"RefNumber": "2351252", "RefComponent": "BC-DB-ORA", "RefTitle": "Oracle Database 12c In-Memory toolkit for SAP BW", "RefUrl": "/notes/2351252 "}, {"RefNumber": "2336881", "RefComponent": "BC-DB-ORA", "RefTitle": "Using Oracle Multitenant with SAP NetWeaver based Products", "RefUrl": "/notes/2336881 "}, {"RefNumber": "2335159", "RefComponent": "BW-SYS-DB-ORA", "RefTitle": "Flat cube on BW on Oracle", "RefUrl": "/notes/2335159 "}, {"RefNumber": "1550133", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Using Oracle Automatic Storage Management (ASM) with SAP NetWeaver based Products", "RefUrl": "/notes/1550133 "}, {"RefNumber": "2290084", "RefComponent": "BC-DB-ORA", "RefTitle": "SAP Software and Oracle Database Appliance Version 12.1", "RefUrl": "/notes/2290084 "}, {"RefNumber": "1915316", "RefComponent": "BC-DB-ORA", "RefTitle": "Database: Patches for ********", "RefUrl": "/notes/1915316 "}, {"RefNumber": "2254866", "RefComponent": "BC-DB-ORA", "RefTitle": "Using Oracle Database Automatic Data Optimization with SAP NetWeaver", "RefUrl": "/notes/2254866 "}, {"RefNumber": "2178980", "RefComponent": "BC-DB-ORA", "RefTitle": "Using Oracle Database In-Memory with SAP NetWeaver based Products", "RefUrl": "/notes/2178980 "}, {"RefNumber": "1739274", "RefComponent": "BC-DB-ORA", "RefTitle": "Database Parameter COMPATIBLE", "RefUrl": "/notes/1739274 "}, {"RefNumber": "1590515", "RefComponent": "BC-DB-ORA", "RefTitle": "SAP Software and Oracle Exadata", "RefUrl": "/notes/1590515 "}, {"RefNumber": "1914631", "RefComponent": "BC-DB-ORA", "RefTitle": "Central Technical Note for Oracle Database 12c Release 1 (12.1)", "RefUrl": "/notes/1914631 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": []}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}