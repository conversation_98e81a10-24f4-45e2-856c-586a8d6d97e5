{"Request": {"Number": "373992", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 493, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000014956062017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000373992?language=E&token=07AE77980C78934A3ADE76A2E8D93CE8"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000373992", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000373992/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "373992"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 3}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Program error"}, "Priority": {"_label": "Priority", "value": "Correction with high priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "05.03.2001"}, "SAPComponentKey": {"_label": "Component", "value": "BW-BEX-ET-QDEF"}, "SAPComponentKeyText": {"_label": "Component", "value": "Queries (Definition and Modification)"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "SAP Business Warehouse", "value": "BW", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Business Explorer", "value": "BW-BEX", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW-BEX*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Enduser Technology", "value": "BW-BEX-ET", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW-BEX-ET*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Queries (Definition and Modification)", "value": "BW-BEX-ET-QDEF", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW-BEX-ET-QDEF*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "373992 - Hotfix query definition for 2.0B Frontend Patch 9"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>Caution: This note is relevant if you <B>use BW 2.0B/2.1C Frontend Patch</B> 09.<br />Symptom 1:<br />If you edit a formula in the formula editor and if you expand the variable node in the operand tree to do this, the system destroys the current formula internally, even though the system reports an OK during \"checking\".<br />If you exit the formula editor and restart it with this formula, then runtime error<br />RBFormulaTokenManager::InsertTokenEx - unknown Nodetype! occurs.<br />If you save the query or the calculated key figure, you make the error a persistent one.Afterwards, you can no longer edit this calculated key figure or the formula column of the query.<br />Another symptom of this error is that \"checking\" delivers a \"general syntax error in the formula\", even though the formula is correct.<br />For such damaged formulas, a check of the query delivers message \"Formula errors in formula ... OiFLG=( , )\". (BRAIN426)<br />Symptom 2:<br />If you save a query that contains exceptions or conditions, by using SaveAs..., an endless loop occurs.<br />Symptom 3:<br />If you save the structure (that contains the key figures) in a query (that contains exceptions or conditions) by using SaveAs..., or if you call a reusable structure by using Expand reference, the system destroys the exceptions and conditions contained in the query and you cannot edit them (only delete them).<br />Symptom 4:<br />The system saves key figure setting \"Calculate result as...\"however, when you call the dialog again, the system displays \"No setting\".</p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>Business Explorer, Bex Analyzer, query, calculated key figure, formula, formula column, unknown node type, variables, saving formula variables, save as, endless loop, query hangs, exception, condition, expand reference, unexpected structure, calculate result as ..., RBFormulaTokenManager, InsertTokenEx - unknown nodetype, BRAIN426, BRAIN 426</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>This problem is caused by a program error in the query definition<br /></p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>The error is corrected in <B>BW 2.0B Frontend Patch 10.</B><br /><br />Import BW 2.0B Frontend Patch 10. The BW Frontend <B>Patch is</B> available when Note 0367278 informs you of this.This note contains additional information on this frontend patch.<br /><br />The installation <B>is described</B> in <B>Note 309281</B> \"BW 2.0B Frontend installation of patches\".In this note, the frontend patch currently available is also mentioned.<br /><br />Using sapbexc.xla (see Note 197460), you can check the installation of the frontend patch: the local PC and your installation server.<br /><br />Since this error can also cause incorrect formulas on the database, we recommend that you replace the incorrect files wdbrLog.ocx and wdbrFml.ocx with an corrected version, which is available on sapservX. You find it in<br />/general/R3server/abap/note.0373992<br />See also Note 13719.<br /><br />These files must be copied to your installation server.You should overwrite the existing files while doing so.<br /></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "BW-BEX (Business Explorer)"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON><PERSON><PERSON> (D019748)"}, {"Key": "Processor                                                                                           ", "Value": ""}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000373992/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000373992/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000373992/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000373992/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000373992/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000373992/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000373992/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000373992/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000373992/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "96885", "RefComponent": "XX-SER-SAPSMP-SWC", "RefTitle": "Downloading front end patches", "RefUrl": "/notes/96885"}, {"RefNumber": "336599", "RefComponent": "BW-BEX", "RefTitle": "BW 2.0B Frontend Patch 09", "RefUrl": "/notes/336599"}, {"RefNumber": "309281", "RefComponent": "BW-BEX", "RefTitle": "BW 2.0B frontend installation of patches", "RefUrl": "/notes/309281"}, {"RefNumber": "197460", "RefComponent": "BW-BEX-ET", "RefTitle": "BW Frontend Checktool sapbexc.xla", "RefUrl": "/notes/197460"}, {"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "197460", "RefComponent": "BW-BEX-ET", "RefTitle": "BW Frontend Checktool sapbexc.xla", "RefUrl": "/notes/197460 "}, {"RefNumber": "336599", "RefComponent": "BW-BEX", "RefTitle": "BW 2.0B Frontend Patch 09", "RefUrl": "/notes/336599 "}, {"RefNumber": "309281", "RefComponent": "BW-BEX", "RefTitle": "BW 2.0B frontend installation of patches", "RefUrl": "/notes/309281 "}, {"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719 "}, {"RefNumber": "96885", "RefComponent": "XX-SER-SAPSMP-SWC", "RefTitle": "Downloading front end patches", "RefUrl": "/notes/96885 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_APPL", "From": "45A", "To": "45B", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "46C", "To": "46C", "Subsequent": "X"}, {"SoftwareComponent": "SAP_BW", "From": "20B", "To": "20B", "Subsequent": ""}, {"SoftwareComponent": "SAP_BASIS", "From": "46D", "To": "46D", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}