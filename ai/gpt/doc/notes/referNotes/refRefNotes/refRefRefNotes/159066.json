{"Request": {"Number": "159066", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 416, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000014693882017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000159066?language=E&token=BA94A9B92F1095A7653391A1ED0E5BB8"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000159066", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000159066/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "159066"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 19}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Installation information"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "25.11.1999"}, "SAPComponentKey": {"_label": "Component", "value": "CRM-MW"}, "SAPComponentKeyText": {"_label": "Component", "value": "Middleware"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Customer Relationship Management", "value": "CRM", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'CRM*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Middleware", "value": "CRM-MW", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'CRM-MW*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "159066 - CRM Middleware Notes Overview"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>-</p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>CRM,Middleware,Oracle,NT,SFA<br /></p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p></p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p><br />Dear customer,<br />find below an overview of notes about CRM.<br /><br />The following remarks are related to the document \"CRM Middleware<br />Installation Overview\" in its respective version. The remarks refer<br />to the section given in parenthesis [].<br /></p> <b>***** Version 1.1.4 Notes *****</b><br /> <p><br />a.) Get the latest documents from SAPServ3 [p. 1, Overview /<br />Introduction]:<br />Documents are located in path \"sapserv3/home/<USER>/specific/crm/CRM114/\";<br />see SAPNet note 13719 how to get documents via ftp.<br /><br />b.) Determination of required disk space [p. 4, hardware requirements]:<br />Be aware that required disk space depends on the amount of data to be<br />transferred from the OLTP to the CRM environment. Please adjust table<br />spaces of the Middleware Server by use of the \"sapdba\"-tool accordingly<br />[p.5, Installation and Activation/step2].<br /><br />c.) See SAPNet note 156102 about OLTP add-on installation [p. 5,<br />Installation and Activation / Step 1].<br /><br />d.)&#x00A0;&#x00A0;Increase the following tablespaces using the \"sapdba\"-tool:<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;PSAPBTABD<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;PSAPBTABI<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;PSAPSTABI<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;PSAPCLUD<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;PSAPPOOLI<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;PSAPDDICD<br /><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;Increase the number/size of extends for the following tables:<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;smo8_log<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;smokonv<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;smokonh<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;smokonm<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; smokonw<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; smokonp<br /><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;Follow the instructions in document 'addmwsrv.pdf' and see SAPNet<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;note 153606 about MW Server installation [p. 6, Installation and<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;Activation/step2]. Additionally read note 178234.<br /><br />e.) Run a SQL correction script:<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;Login as &lt;SID&gt;adm and change to folder &lt;Disk Drive&gt;:\\orant\\ORAINST<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;Execute the following command from a command prompt:<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;plus80 internal @sapuser<br /><br />f.)&#x00A0;&#x00A0;Installation of SAPGui [p. 6, Installation and Activation/step2]:<br />In addition to the R/3 installation the SAPGui has to be installed on<br />the Middleware Server. This step generates required entries in the<br />services files, which allow communication between the Middleware Server<br />and your OLTP system. You have to ensure, that RFC communication is<br />possible both between R/3 OLTP and Middleware Server and between<br />Middleware Server and Administration Station. You may have to insert<br />entries in your Windows \"Services\" file - usually located in<br />\"c:\\winnt\\system32\\drivers\\etc\".<br /><br />g.) Filter settings for&#x00A0;&#x00A0;OLTP download [p.6, Installation and<br />Activation/step4]:<br />Be aware that you have to chose filter settings in a way, that<br />downloaded data fit on the laptop.<br /><br />h.) See Sapnet note 161660 how to prepare download of data for the SAP<br />configuration engine (SCE) [p.7, Installation and Activation/step4].<br /><br />i.) Monitoring table spaces [p. 7, Installation and Activation/step 4]:<br />During initial download of OLTP data to the Middleware Server an<br />overflow of table spaces might happen. You can either adjust table<br />spaces during the transfer process, before its start or before restart.<br />In case of a transfer interruption you have to adjust tablespaces and<br />then to restart the transfer process as described in the document<br />\"mwr3a*.pdf\".<br /><br /><br /></p> <b>***** Version 1.1.3 Notes *****</b><br /> <p><br />a.) Get the latest documents from SAPServ3 [p. 1, Overview /<br />Introduction]:<br />Documents are located in path \"sapserv3/home/<USER>/specific/crm/CRM113/\";<br />see SAPNet note 13719 how to get documents via ftp.<br /><br />b.) Determination of required disk space [p. 4, hardware requirements]:<br />Be aware that required disk space depends on the amount of data to be<br />transferred from the OLTP to the CRM environment. Please adjust table<br />spaces of the Middleware Server by use of the \"sapdba\"-tool accordingly<br />5, Installation and Activation/step2].<br /><br />c.) See SAPNet note 156102 about OLTP add-on installation [p. 5,<br />Installation and Activation / Step 1].<br /><br />d.)&#x00A0;&#x00A0;See SAPNet note 153606 about MW Server installation [p. 5,<br />Installation and Activation/step2].<br /><br />e.)&#x00A0;&#x00A0;Installation of SAPGui [p. 5, Installation and Activation/step2]:<br />In addition to the R/3 installation the SAPGui has to be installed on<br />the Middleware Server. This step generates required entries in the<br />services files, which allow communication between the Middleware Server<br />and your OLTP system. You have to ensure, that RFC communication is<br />possible both between R/3 OLTP and Middleware Server and between<br />Middleware Server and Administration Station. You may have to insert<br />entries in your Windows \"Services\" file - usually located in<br />\"c:\\winnt\\system32\\drivers\\etc\".<br /><br />f.) Filter settings for&#x00A0;&#x00A0;OLTP download [p.5, Installation and<br />Activation/step4]:<br />Be aware that you have to chose filter settings in a way, that<br />downloaded data fit on the laptop.<br /><br />g.) See Sapnet note 161660 how to prepare download of data for the SAP<br />configuration engine (SCE) [p.6, Installation and Activation/step4].<br /><br />h.) Monitoring table spaces [p. 6, Installation and Activation/step 4]:<br />During initial download of OLTP data to the Middleware Server an<br />overflow of table spaces might happen. You can either adjust table<br />spaces during the transfer process, before its start or before restart.<br />In case of a transfer interruption you have to adjust tablespaces and<br />then to restart the transfer process as described in the document<br />\"mwr3a*.pdf\".<br /><br /><br /></p> <b>***** Version 1.1.2 Notes *****</b><br /> <p><br />a.) Get the latest documents from SAPServ3 [p. 1, Overview /<br />Introduction]:<br />Get the documents \"mwinstas.pdf\", \"mwinstol.pdf\" and \"mwqload.pdf\",<br />which are located in path \"sapserv3/home/<USER>/specific/crm/CRM112/\"; see<br />SAPNet note 13719 how to get documents via ftp.<br /><br />b.) Determination of required disk space [p. 4, Hardware Requirements]:<br />Be aware that required disk space depends on the amount of data to be<br />transferred from the R/3 OLTP to the CRM environment. Until a formula<br />for diskspace calculation is available, you have to calculate the<br />requirements on your own. Please adjust table spaces of the Middleware<br />Server by use of the \"sapdba\"-tool accordingly [p. 5, Installation and<br />Activation / Step 2].<br /><br />c.) See SAPNet note 156102 about OLTP add-on installation [p. 5,<br />Installation and Activation / Step 1].<br /><br />d.) See SAPNet note 153606 about Middleware Server installation [p. 5,<br />Installation and Activation / Step 2].<br /><br />e.) Installation of SAPGui [p. 5, Installation and Activation/Step 2]:<br />In addition to the R/3 installation the SAPGui has to be installed on<br />the Middleware Server. This step generates required entries in the<br />services files, which allow communication between the Middleware Server<br />and your OLTP system. You have to ensure, that RFC communication is<br />possible both between R/3 OLTP and Middleware Server and between<br />Middleware Server and Administration Station. You may have to insert<br />entries in your Windows \"Services\" file - usually located in<br />\"c:\\winnt\\system32\\drivers\\etc\".<br /><br />f.) Filter settings for&#x00A0;&#x00A0;OLTP download [p.5, Installation and Acti-<br />vation / Step 4]:<br />Be aware that you have to choose filter settings in a way, that<br />downloaded data fit on the laptop (Ver. 1.1.2 only).<br /><br />g.) See SAPNet note 161660 how to prepare download of data for the SAP<br />configuration engine (SCE) [p.5, Installation and Activation / Step 4].<br /><br />h.) Monitoring table spaces [p. 5, Installation and Activation / Step<br />4]:&#x00A0;&#x00A0;During initial download of OLTP data to the Middleware Server an<br />overflow of table spaces might happen. You can either adjust table<br />spaces during the transfer process, before its start or before restart.<br />In case of a transfer interruption you have to adjust tablespaces and<br />then to restart the transfer process as described in the document<br />\"mwr3a*.pdf\".<br /><br /><br /><br /><br /></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "CRM-MT-IU (Installation & Upgrade)"}, {"Key": "Operating system", "Value": "NT/INTEL4.0"}, {"Key": "Database System", "Value": "ORACLE"}, {"Key": "Responsible                                                                                         ", "Value": "********"}, {"Key": "Processor                                                                                           ", "Value": ""}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000159066/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000159066/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000159066/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000159066/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000159066/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000159066/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000159066/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000159066/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000159066/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "375925", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/375925"}, {"RefNumber": "372969", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/372969"}, {"RefNumber": "169550", "RefComponent": "CRM-MW-COM", "RefTitle": "CRM Client Messaging: Installation / Administration", "RefUrl": "/notes/169550"}, {"RefNumber": "168839", "RefComponent": "CRM-MW-SRV", "RefTitle": "Filling source_c fields by Extract", "RefUrl": "/notes/168839"}, {"RefNumber": "168690", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/168690"}, {"RefNumber": "167604", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/167604"}, {"RefNumber": "161660", "RefComponent": "CRM-MSA-CFG", "RefTitle": "Product configuration on Mobile Sales client", "RefUrl": "/notes/161660"}, {"RefNumber": "153619", "RefComponent": "CRM-MW", "RefTitle": "Installation Guide CRM Middleware / Non R/3 components", "RefUrl": "/notes/153619"}, {"RefNumber": "153613", "RefComponent": "CRM-MW", "RefTitle": "Troubleshooting CRM Middleware Server Installation", "RefUrl": "/notes/153613"}, {"RefNumber": "153610", "RefComponent": "CRM-MW", "RefTitle": "FAQ CRM Middleware Server Installation", "RefUrl": "/notes/153610"}, {"RefNumber": "153606", "RefComponent": "CRM-MW", "RefTitle": "Installation of CRM Middleware Server", "RefUrl": "/notes/153606"}, {"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "169550", "RefComponent": "CRM-MW-COM", "RefTitle": "CRM Client Messaging: Installation / Administration", "RefUrl": "/notes/169550 "}, {"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719 "}, {"RefNumber": "153619", "RefComponent": "CRM-MW", "RefTitle": "Installation Guide CRM Middleware / Non R/3 components", "RefUrl": "/notes/153619 "}, {"RefNumber": "153613", "RefComponent": "CRM-MW", "RefTitle": "Troubleshooting CRM Middleware Server Installation", "RefUrl": "/notes/153613 "}, {"RefNumber": "153610", "RefComponent": "CRM-MW", "RefTitle": "FAQ CRM Middleware Server Installation", "RefUrl": "/notes/153610 "}, {"RefNumber": "153606", "RefComponent": "CRM-MW", "RefTitle": "Installation of CRM Middleware Server", "RefUrl": "/notes/153606 "}, {"RefNumber": "168839", "RefComponent": "CRM-MW-SRV", "RefTitle": "Filling source_c fields by Extract", "RefUrl": "/notes/168839 "}, {"RefNumber": "161660", "RefComponent": "CRM-MSA-CFG", "RefTitle": "Product configuration on Mobile Sales client", "RefUrl": "/notes/161660 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_APPL", "From": "45B", "To": "45B", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "BBPCRM 20B", "SupportPackage": "SAPKU20B03", "URL": "/supportpackage/SAPKU20B03"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}