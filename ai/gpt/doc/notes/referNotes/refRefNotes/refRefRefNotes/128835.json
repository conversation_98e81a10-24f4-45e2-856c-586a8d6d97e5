{"Request": {"Number": "128835", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 1102, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000014622152017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000128835?language=E&token=C84E18E649FF3D64328EA76E3EC31A79"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000128835", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000128835/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "128835"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 5}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Consulting"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "17.10.1999"}, "SAPComponentKey": {"_label": "Component", "value": "XX-SER-Y2000"}, "SAPComponentKeyText": {"_label": "Component", "value": "Year 2000 Check for Customer Modifications"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Miscellaneous", "value": "XX", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Service Messages", "value": "XX-SER", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-SER*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Year 2000 Check for Customer Modifications", "value": "XX-SER-Y2000", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-SER-Y2000*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "128835 - Error when importing Safety Check 2000"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>The import terminates with one of the following messages:</p> <OL>1. ERROR: stopping on error 12 during IMPORT OF SELFDEFINED OBJECTS<br />tp finished with return code: 210<br />meaning:<br />&#x00A0;&#x00A0;emergency brake</OL> <p></p> <OL>2. 2EETW150 sap_dext called with msgnr \"900\":<br />2EETW000 Internal error in DB interface<br />2EETW000 unknown db interface function<br />R3trans finished (0012).<br />ERROR: stopping on error 12 during DD IMPORT<br />stopping on error 12 during DD IMPORT<br />tp finished with return code: 210<br />meaning:<br />&#x00A0;&#x00A0;emergency brake<br /></OL> <OL>3. Transaction BAOV does not display a patch level, Support<br />Packages or CRTs.</OL><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>SELFDEFINED, ADO, error 12, error 16, source code 210</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>Return code 12 from TP often appears if this cannot read the logs of the online ABAP. These ABAPs are called RDD* in SM37 and are normally writing into the directory /usr/sap/trans/tmp.<br /><br />1a) RDDIMPDP is not scheduled in the target client. See Note 34964.<br />1b) profile parameter DIR_TRANS is not identical with transdir in<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;TPPARAM<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;The ABAPs then write to a directory and tp attempts to read in<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;another one.<br />1c) Background jobs run on application server which has another<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;transport directory (for example, UNIX database and NT). tp then<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;waits in vain for its log file, for example in the database<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;server.<br />1d) tp takes the highest return code from log file, although the<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;last imports were error-free.<br /><br />2) Data files destroyed. This error can occur if the R3trans file<br />&#x00A0;&#x00A0; (RICN01O.SAP) was not transferred in the binary mode.<br />&#x00A0;&#x00A0; See Note 13719.<br /><br />3) Transaction BAOV is out-of-date for the Y2000 transport.</p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>1a) Check whether the job RDDIMPDP_CLIENT_xxx is scheduled in all clients.<br />1b) Check, whether DIR_TRANS from the R/3 profile corresponds to transdir from TPPARAM in /usr/sap/trans/bin (for example,<br />1c) Check the SM37 logs of the RDD* ABAP, especially RDDDIC1L (ADO import).<br />1d) Clear the directory /usr/sap/trans/tmp, check the log files<br /><br />2) Check whether your files have the same size as on the CD or try<br />&#x00A0;&#x00A0; whether it is possible to read the data file with R3trans -l.<br />&#x00A0;&#x00A0; See Note 2050.<br />&#x00A0;&#x00A0; If not, download the newest version of the Safety Check 2000<br />&#x00A0;&#x00A0; from SAPSERVx which also contains some error corrections. See Note &#x00A0;&#x00A0; 106351.<br />&#x00A0;&#x00A0; Make sure that the R3trans file (RICN01O.SAP) is transferred in<br />&#x00A0;&#x00A0; the binary mode.<br /><br />3) Read Note 175374.<br /></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "D001788"}, {"Key": "Processor                                                                                           ", "Value": ""}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000128835/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000128835/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000128835/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000128835/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000128835/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000128835/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000128835/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000128835/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000128835/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "71353", "RefComponent": "BC-CTS", "RefTitle": "Transports hang during background steps", "RefUrl": "/notes/71353"}, {"RefNumber": "34964", "RefComponent": "BC-CTS", "RefTitle": "New RDDIMPDP features in Release 3.0", "RefUrl": "/notes/34964"}, {"RefNumber": "2050", "RefComponent": "BC-CTS", "RefTitle": "R3trans: Data file destroyed", "RefUrl": "/notes/2050"}, {"RefNumber": "175374", "RefComponent": "BC-UPG-ADDON", "RefTitle": "Transaction BAOV is out of date", "RefUrl": "/notes/175374"}, {"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719"}, {"RefNumber": "106351", "RefComponent": "XX-SER-Y2000", "RefTitle": "Safety chck 2000 chck progr. f. yr2000 compatiblty", "RefUrl": "/notes/106351"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2050", "RefComponent": "BC-CTS", "RefTitle": "R3trans: Data file destroyed", "RefUrl": "/notes/2050 "}, {"RefNumber": "71353", "RefComponent": "BC-CTS", "RefTitle": "Transports hang during background steps", "RefUrl": "/notes/71353 "}, {"RefNumber": "175374", "RefComponent": "BC-UPG-ADDON", "RefTitle": "Transaction BAOV is out of date", "RefUrl": "/notes/175374 "}, {"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719 "}, {"RefNumber": "106351", "RefComponent": "XX-SER-Y2000", "RefTitle": "Safety chck 2000 chck progr. f. yr2000 compatiblty", "RefUrl": "/notes/106351 "}, {"RefNumber": "34964", "RefComponent": "BC-CTS", "RefTitle": "New RDDIMPDP features in Release 3.0", "RefUrl": "/notes/34964 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": []}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}