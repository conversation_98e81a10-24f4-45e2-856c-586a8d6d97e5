{"Request": {"Number": "804186", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 635, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000015820622017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000804186?language=E&token=AB2E94AF8690527C829D83AF938FA6CE"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000804186", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000804186/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "804186"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 4}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Installation information"}, "Priority": {"_label": "Priority", "value": "Correction with medium priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "08.08.2005"}, "SAPComponentKey": {"_label": "Component", "value": "BC-DB-ORA"}, "SAPComponentKeyText": {"_label": "Component", "value": "Oracle"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Basis Components", "value": "BC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Database Interface, Database Platforms", "value": "BC-DB", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-DB*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Oracle", "value": "BC-DB-ORA", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-DB-ORA*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "804186 - sga size > 2GB on Linux"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>increasing the oracle SGA beyond the 32-bit OS-limits on linux</p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>VLM<br />redhat<br />SUSE<br />3GB<br />SGA</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>Your application requires your SGA size to be configured beyond the 3GB addressspace limit, that apply to 32-bit operating systems.<br />Please note that the options described in here are valid only for oracle releases 9.2 and above.<br />Some of the adjustments differ depending on the linux distribution used. The procedures in here are in general valid for</p> <UL><LI>Red Hat Enterprise Linux (RHEL) 2.1 AS</LI></UL> <UL><LI>Red Hat Enterprise Linux (RHEL) 3 AS</LI></UL> <UL><LI>SUSE Linux Enterprise Server (SLES) 8</LI></UL> <UL><LI>SUSE Linux Enterprise Server (SLES) 9</LI></UL> <p><br /></p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>Depending on what size of SGA you are anticipating, you have to pick different options to acomplish that size. The 9.2 version of oracle allows to allocate ~1.7GB of address space for its SGA without any modifications to be done.</p> <b>increasing the oracle SGA to &lt; 4GB</b><br /> <p>To increase to this size, Oracle needs to be relinked with a lower SGA base address; and linux needs to have the mapped base lowered for processes running Oracle.</p> <OL>1. lowering the oracle base address (linux-independent)</OL> <p>The current base adresss that is used by default by oracle is 0x50000000.When lowering this address it is important to note that the newly linked oracle binary will no longer work unless a corresponding modification is also made to Linux. Here are the steps to lower tohe SGA-starting address:</p> <OL><OL>a) shutdown all instances of Oracle</OL></OL> <OL><OL>b) cd $ORACLE_HOME/lib</OL></OL> <OL><OL>c) cp -a libserver9.a libserver9.a.save</OL></OL> <OL><OL>d) cd $ORACLE_HOME/bin</OL></OL> <OL><OL>e) cp -a oracle oracle.save</OL></OL> <OL><OL>f) cd $ORACLE_HOME/rdbms/lib</OL></OL> <OL><OL>g) genksms -s 0x15000000 &gt; ksms.s</OL></OL> <OL><OL>h) make -f ins_rdbms.mk ksms.o</OL></OL> <OL><OL>i) make -f ins_rdbms.mk install</OL></OL> <p>The relinked Oracle binary now has a lower base and is able to allocate ~ 2.65GB of address space (if linux is also modified to support this)</p> <OL>2. lowering the linux kernel's mapped base</OL> <p>This step differs depending on the linux that you are using</p> <UL><LI>Red Hat Enterprise Linux (RHEL) 2.1 AS</LI></UL> <UL><LI>Red Hat Enterprise Linux (RHEL) 3 AS</LI></UL> <UL><LI>SUSE Linux Enterprise Server (SLES) 8</LI></UL> <UL><LI>SUSE Linux Enterprise Server (SLES) 9</LI></UL> <UL><LI></LI></UL> <b>increasing the SGA to &gt; 4GB</b><br /> <p>In order to increase the amount of memory that can be adressed for a 32-bit operating system, an OS-kernel is equired that allows Oracle to allocate and use more than 4GB of memory for the database buffer cache on a 32-bit Intel platform. In the SAP environment, only PAE aware kernels are certified, so as long as you have a certified kernel, no linux kernel exchange is required. This feature is also called VLM (Very Large Memory) in Oracle documents. The page directories and the page tables are extended from 4 byte to 8 byte formats, allowing the extension of the base addresses of page tables and page frames to 24 bits (from 20 bits).&#x00A0;&#x00A0;This is where&#x00A0;&#x00A0;the extra four bits are introduced  to complete the 36-bit physical address.<br />Only the buffer cache part of the SGA can take advantage of this feature; the shared pool or redolog buffer cannot be moved into this 'virtually' created addressspace.<br />When using this feature, the 3GB limit can be extended to a larger limit based on the Operating system. The 'theoretical' limit of Red Hat Advanced server e.g. is 62GB.<br />Additionally to the 'theoretical' OS-limits, the amount of memory that can be addressed is limited by the amount of physical memory in the server.<br />Current hardware limitations and practical considerations further limit the actual size of SGA that can be configured, but it is still several times larger than the size of the SGA without VLM.<br />Please note that when using this feature, access to this 'virtual' addressed memory will not be equivilantly fast than when using the regular address space. It is expected that there is about a 10% performance impact to memory operations. Please therefore test this feature thorowly first to make sure that the benefit you gain from using this feature overweitghts this performance impact. (In nearly all situations this is the case)<br /><br />There are some steps that need to be taken in order to enable this PAE from an OS perspective and for oracle to be able to use it.<br /><br />This so far only has been tested within SAP with SUSE SLES8 Kernels.<br />In order to activate this feature with SLES, the following steps are necessary on OS-level:<br /><br />1. Check Linux kernel parameter /proc/sys/kernel/shmmax and set it to &#x00A0;&#x00A0;&#x00A0;&#x00A0; approx half of the physical memory available. Please note that on<br />&#x00A0;&#x00A0; some versions this is a 32-bit parameter, so we noticed that when<br />&#x00A0;&#x00A0; setting the parameter to &gt; 4GB it may cut off the first 4GB (e.g.<br />&#x00A0;&#x00A0; when setting it to 4.1GB it would result in 0.1GB). This then may<br />&#x00A0;&#x00A0; result in not being able to startup the instance, so be aware of it<br />&#x00A0;&#x00A0; and doublecheck in case you run into issues bringing the system up.<br />2. set up the /dev/shm as described in Note 386605. Make sure it is at &#x00A0;&#x00A0;&#x00A0;&#x00A0;minimum big enough to have db_block_buffer and the extended memory &#x00A0;&#x00A0;&#x00A0;&#x00A0; of the SAP instance in it.<br />3. \"Dynamic SGA\" cannot be used together with VLM. The parameter<br />&#x00A0;&#x00A0; db_cache_size must be replaced with db_block_buffers, if already<br />&#x00A0;&#x00A0; set.<br />4. Set use_indirect_data_buffers=true in the oracle parameter file.<br />&#x00A0;&#x00A0; Set db_block_buffers to the desired value.<br /><br />Further details can also be seen in Metalink Notes 260152.1 , 197044.1<br />and 200266.1<br /><br />There is a theoretical limit of 14GB that can be allocated via this method for Oracle on SLES8/SLES9.<br /><br />Please note that the values displayed for he SGA size during db startup are not correct. However, the memory specified via the VLM parameters is used, even though it also does notshow in v$sga.<br /></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "BW-SYS-DB-ORA (BW ORACLE)"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON> (I800932)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (C5050086)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000804186/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000804186/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000804186/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000804186/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000804186/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000804186/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000804186/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000804186/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000804186/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "789011", "RefComponent": "BC-DB-ORA", "RefTitle": "FAQ: Oracle memory areas", "RefUrl": "/notes/789011"}, {"RefNumber": "502782", "RefComponent": "BC-DB-ORA", "RefTitle": "Composite SAP Note ora-4030", "RefUrl": "/notes/502782"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "789011", "RefComponent": "BC-DB-ORA", "RefTitle": "FAQ: Oracle memory areas", "RefUrl": "/notes/789011 "}, {"RefNumber": "502782", "RefComponent": "BC-DB-ORA", "RefTitle": "Composite SAP Note ora-4030", "RefUrl": "/notes/502782 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": []}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}