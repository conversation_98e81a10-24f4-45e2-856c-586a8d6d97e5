{"Request": {"Number": "129439", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 399, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000014623272017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000129439?language=E&token=2312D6FA652AF2B24F11DADD02755D77"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000129439", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000129439/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "129439"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 23}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Consulting"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "07.05.2007"}, "SAPComponentKey": {"_label": "Component", "value": "BC-DB-ORA"}, "SAPComponentKeyText": {"_label": "Component", "value": "Oracle"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Basis Components", "value": "BC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Database Interface, Database Platforms", "value": "BC-DB", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-DB*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Oracle", "value": "BC-DB-ORA", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-DB-ORA*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "129439 - Maximum file sizes with Oracle"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p><br />The maximum file size is limited on some operating systems</p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>filesize &gt; 2GB, file size limit, max_file_size, limits<br />ORA-27092: skgfofi: size of file exceeds file size limit of the process</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>On most platforms, Oracle 7 supports the creation of 1022 data files. With a file limit of 2GB, the database size is limited to 2044 GB. As of Oracle 8, the maximum number of data files is now 65533. This is also the maximum number for Oracle 9 and 10.<br /><br />Despite this, it is often desirable to create large data files, so that</p> <UL><LI>you do not have to manage as many files,</LI></UL> <UL><LI>to have fewer required file handle resources</LI></UL> <UL><LI>fewer file headers have to be updated when checkpointing, starting up or shutting down.<br /></LI></UL> <p>However, note that larger data files:</p> <UL><LI>mean longer re-import times (for individual files) in case they need to be recovered.</LI></UL> <UL><LI>On a few platforms, the async I/O operations on data files, that are over the 2GB limit, do not work on the operating system side.</LI></UL> <UL><LI>May not function on some platforms until you apply particular patches</LI></UL> <p>As of Oracle 9, the file size limit of 4194302*DB_BLOCK_SIZE applies (8k in the SAP environment), that is, 32GB.<br /><br />At any rate, test along with your hardware producer to what extent support is guaranteed for large data files.<br />Before you can use large files, make sure that all settings required for large files were made in the operating system:</p> <UL><LI>The file system must be created as a large file system.</LI></UL> <UL><LI>Users &lt;SID&gt;adm and ora&lt;SID&gt; must not have a ulimit set for maxfilesize, or this ulimit must be larger than the size of the largest file. In most operating systems, you can use command 'ulimit -a ' to check the maximum file size of the corresponding user.</LI></UL> <UL><LI>ulimit may also be set generically in operaing systems - and must also be changed there.</LI></UL> <UL><LI>For some operating systems, existing file systems can be converted into large file systems.</LI></UL> <UL><UL><LI>for example, HPUX<br />/usr/sbin/fsadm -F vxfs -o largefiles {special device file}</LI></UL></UL> <UL><LI>The file size set in the kernel must be sufficiently large.</LI></UL> <UL><UL><LI>for example, HPUX<br />/usr/sbin/kmtune|grep max<br />maxdsiz&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;512*1024*1024<br />maxdsiz_64bit&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;(256*1024*1024)<br />maxfiles&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;1024<br />maxfiles_lim&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;2048</LI></UL></UL> <UL><UL><LI>TRU64<br />/sbin/sysconfig -q proc<br />per_proc_data_size = 1073741824<br />max_per_proc_data_size&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;=&#x00A0;&#x00A0; 2073741824<br />max_per_proc_address_space&#x00A0;&#x00A0;=&#x00A0;&#x00A0;4294967296<br />per_proc_address_space = 4294967296&#x00A0;&#x00A0;&#x00A0;&#x00A0;.&#x00A0;&#x00A0;&#x00A0;&#x00A0;.<br />open_max_soft = 4096<br />open_max_hard = 4096</LI></UL></UL> <UL><LI>For large file systems, the maximum size of a file is also limited depending on the operating system.<br />This size is for<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;max. size of a file system&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;max size of a file<br />AIX&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;128GB&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;64GB<br />HP&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 128GB&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;128GB<br />Linux&#x00A0;&#x00A0;&#x00A0;&#x00A0;2&#x00A0;&#x00A0; TB&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;1TB (Redhat 3 )<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;8TB&#x00A0;&#x00A0;(Redhat 3 )</LI></UL> <UL><LI>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;16GB (SUSE ext2 and ext3</LI></UL> <UL><LI>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;with 1k block size)</LI></UL> <UL><LI>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;8EB (SUSE JFS)<br />Solaris&#x00A0;&#x00A0; 1&#x00A0;&#x00A0; TB&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;1&#x00A0;&#x00A0; TB(2GB for solaris &lt;=2.5.1)<br />TRU64&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;128GB&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;128GB (&lt;=3.2G)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 512GB&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;512GB (&gt;=4.0)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 16&#x00A0;&#x00A0; TB&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;16 TB (advfs)</LI></UL> <UL><LI>Other operating system dependant restrictions/settings are described below for the relevant platform.</LI></UL> <UL><LI>The maximam file size allowed by the operating system must be specified as a parameter max_file_size in the profile init&lt;SID&gt;.dba. If this is not specified, the following are used as a default:<br />max_file_size =&#x00A0;&#x00A0;&#x00A0;&#x00A0;2 TB&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;(in all UNIX systems) or<br />max_file_size = 2048 TB&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;(in Windows)<br />Also refer to Note 90937 or the description in the init&lt;SID&gt;.dba file for this parameter.</LI></UL><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>If the conditions described above are met, you can create files with the following maximum sizes depending on the operating system and the Oracle release:<br />________________________________________________________________________<br />AIX:<br />RELEASE&#x00A0;&#x00A0;&#x00A0;&#x00A0; AIX-REL&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Patchset?&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; FS&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;RAW&#x00A0;&#x00A0;&#x00A0;&#x00A0;AIO &gt; 2GB<br />_______________________________________________________________________<br />&gt;=8.1.7.x&#x00A0;&#x00A0;&#x00A0;&#x00A0; &gt;=4.3.2&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;32GB&#x00A0;&#x00A0;&#x00A0;&#x00A0;32GB&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 32GB<br />&#x00A0;&#x00A0;8.1.6.x&#x00A0;&#x00A0;&#x00A0;&#x00A0; &gt;=4.3.2&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;32GB&#x00A0;&#x00A0;&#x00A0;&#x00A0;32GB&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 32GB<br />&#x00A0;&#x00A0;*******&#x00A0;&#x00A0;&#x00A0;&#x00A0; &gt;=4.2.1&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;32GB&#x00A0;&#x00A0;&#x00A0;&#x00A0;32GB&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 32GB<br />&#x00A0;&#x00A0;8.0.6.x&#x00A0;&#x00A0;&#x00A0;&#x00A0; &gt;=4.2.1&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;32GB&#x00A0;&#x00A0;&#x00A0;&#x00A0;32GB&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 32GB<br />&#x00A0;&#x00A0;8.0.6.x&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 4.1.x&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;2GB&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;2GB&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;2GB<br />&#x00A0;&#x00A0;8.0.4.x&#x00A0;&#x00A0;&#x00A0;&#x00A0;&gt;=4.2.1&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&gt;=8.0.4.1&#x00A0;&#x00A0;&#x00A0;&#x00A0;32GB&#x00A0;&#x00A0;&#x00A0;&#x00A0; 32GB&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 32GB<br />&#x00A0;&#x00A0;8.0.4.x&#x00A0;&#x00A0;&#x00A0;&#x00A0;4.1.X&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;2GB&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;2GB&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;2GB<br />&#x00A0;&#x00A0;7.3.4.X&#x00A0;&#x00A0;&#x00A0;&#x00A0;&gt;=4.2.1&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&gt;=7.3.4.2&#x00A0;&#x00A0;&#x00A0;&#x00A0;32GB&#x00A0;&#x00A0;&#x00A0;&#x00A0; 32GB&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 32GB<br />&#x00A0;&#x00A0;7.3.4.X&#x00A0;&#x00A0;&#x00A0;&#x00A0;4.1.X&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;2GB&#x00A0;&#x00A0;&#x00A0;&#x00A0; 32GB&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;2GB<br />&#x00A0;&#x00A0;7.3.3.X&#x00A0;&#x00A0;&#x00A0;&#x00A0;&gt;=4.2.1&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&gt;=7.3.3.6&#x00A0;&#x00A0;&#x00A0;&#x00A0;32GB&#x00A0;&#x00A0;&#x00A0;&#x00A0; 32GB&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 32GB<br />&#x00A0;&#x00A0;7.3.3.X&#x00A0;&#x00A0;&#x00A0;&#x00A0;4.1.X&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;2GB&#x00A0;&#x00A0;&#x00A0;&#x00A0; 32GB&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;2GB<br /><br />If you want to use the functions, apply the corresponding patch set.<br /><br />AIX 4.2.1 and 4.3 allow creating data files up to 32 GB<br />If problems nevertheless exist creating such a file despite the Oracle patch, check whether the parameter ulimit for fsize open -1 in /etc/security/limits is set.<br />As ora&lt;sid&gt;:&#x00A0;&#x00A0;ulimit<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;the value of the parameter fsize is diplayed (unlimited)<br />As users root: lsuser ora&lt;sid&gt;<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;the settings of the different parameters valid for this user are displayed (for example fsize=-1)<br />If the parameter must be changed, the server must be restarted. If the value fsize_hard needs to be explicitly set for the user, the value for fsize must not exceed this value. Set the paramter fsize_hard:<br />as user root: \"chuser fsize_hard=&lt;value&gt; &lt;User&gt;\"<br />Default: Fsize_hard = fsize<br /><br />Important: When creating the file systems you must make sure that these are created using the option \"bf: true\" (big files) (smitty jfs -&gt; Add a large file enabled journaled file system).<br />This setting can NOT be changed later!<br />Checking the settings of a file system:<br />As users root: lsfs -q <br /><br />_______________________________________________________________________<br />COMPAQ TRU64, DEC-UNIX, HP TRU64<br />_______________________________________________________________________<br />RELEASE&#x00A0;&#x00A0;OS-REL&#x00A0;&#x00A0;&#x00A0;&#x00A0;FS/RAW&#x00A0;&#x00A0;&#x00A0;&#x00A0;AIO &gt; 2GB&#x00A0;&#x00A0;&#x00A0;&#x00A0;EXP/IMP SQL Loader<br /> &gt;=8.1.6.x&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;128Gb&#x00A0;&#x00A0;&#x00A0;&#x00A0;on RAW dev.&#x00A0;&#x00A0;OS-LIM OS-LIM<br />&#x00A0;&#x00A0;8.1.5.x&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;128Gb&#x00A0;&#x00A0;&#x00A0;&#x00A0;on RAW dev.&#x00A0;&#x00A0;OS-LIM OS-LIM<br />&#x00A0;&#x00A0;8.0.6.x&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;128Gb&#x00A0;&#x00A0;&#x00A0;&#x00A0;on RAW dev.&#x00A0;&#x00A0;OS-LIM OS-LIM<br />&#x00A0;&#x00A0;8.0.5.x&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;128Gb&#x00A0;&#x00A0;&#x00A0;&#x00A0;on RAW dev.&#x00A0;&#x00A0;OS-LIM OS-LIM<br />&#x00A0;&#x00A0; 8.0.4.X&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;*)128 GB&#x00A0;&#x00A0;&#x00A0;&#x00A0;on RAW dev.&#x00A0;&#x00A0;OS-LIM OS-LIM<br />&#x00A0;&#x00A0;7.3.4.X&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;*)128 GB&#x00A0;&#x00A0;&#x00A0;&#x00A0;on raw dev.&#x00A0;&#x00A0;OS-LIM OS-LIM<br />&#x00A0;&#x00A0;7.3.3.X&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;*)128 GB&#x00A0;&#x00A0;&#x00A0;&#x00A0;on raw dev.&#x00A0;&#x00A0;OS-LIM OS-LIM<br /><br />From the OS side, the following restrictions apply:<br /> Max file system size UFS&#x00A0;&#x00A0;4.0d &lt;(&gt;&amp;&lt;)&gt; 4.0e : &lt;(&gt;&lt;&lt;)&gt;= 128GB. AdvFS&#x00A0;&#x00A0;49T (Hardware limit)<br /><br />Max OS file size<br />UFS: an individual file can take up the entire file system<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;file limit: 128Gb.(4.0d/4.0e)<br />AdvFS&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 16Tb<br /><br />*) Data files of 128 GB need a 32k block size on an advanced file system or RAW. The theoretical file size has been reduced to 4 million Oracle blocks.That is, with a block size of 8KB as used in the SAP environment, the maximum file size is 32GB.<br /><br />_______________________________________________________________________<br />Linux<br />_______________________________________________________________________<br />Since true &gt;2GB filesize support only is guaranteed for the latest versions of glibc, and only oracle 9.2 supports these new versions, there is NO support for files &gt; 2GB for oracle versions &lt; 9.2<br />&#x00A0;&#x00A0;RELEASE&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;FS&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;RAW&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;EXP/IMP&#x00A0;&#x00A0;&#x00A0;&#x00A0;SQL-Loader<br />&#x00A0;&#x00A0;&lt;=8.1.7&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;2GB&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;2GB&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;2GB&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;2GB<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;9.2&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&gt;4GB&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&gt;4GB&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&gt;4GB&#x00A0;&#x00A0;&#x00A0;&#x00A0; &gt;4GB<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0; 10.X&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;1TB (ext2,ext3, jfs)<br /><br /><br />_______________________________________________________________________<br />HP_UX<br />_______________________________________________________________________<br />&#x00A0;&#x00A0; RELEASE&#x00A0;&#x00A0;HP-REL&#x00A0;&#x00A0;&#x00A0;&#x00A0;FS/RAW&#x00A0;&#x00A0;&#x00A0;&#x00A0;AIO &gt; 2GB&#x00A0;&#x00A0;&#x00A0;&#x00A0;EXP/IMP SQL Loader<br />&gt;&#x00A0;&#x00A0;8.1.7.4 64bit&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;*)64Gb&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;on raw dev.&#x00A0;&#x00A0;&gt;4Gb&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;?<br />&#x00A0;&#x00A0;8.1.7.4 32bit&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;*)64Gb&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;on raw dev.&#x00A0;&#x00A0;&gt;4Gb&#x00A0;&#x00A0; &gt;2Gb<br />&#x00A0;&#x00A0;8.1.6.x&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;*)64Gb&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;on raw dev.&#x00A0;&#x00A0;&gt;2Gb&#x00A0;&#x00A0; &gt;2Gb<br />&#x00A0;&#x00A0;8.0.6.x&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;*)64Gb&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;on raw dev.&#x00A0;&#x00A0; 2GB&#x00A0;&#x00A0;&#x00A0;&#x00A0;2GB<br />&#x00A0;&#x00A0;8.0.5.x&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;*)64Gb&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;on raw dev.&#x00A0;&#x00A0; 2Gb&#x00A0;&#x00A0;&#x00A0;&#x00A0;2GB<br />&#x00A0;&#x00A0;8.0.5.X&#x00A0;&#x00A0;&gt;=10.20&#x00A0;&#x00A0; *)64Gb&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;on raw dev.&#x00A0;&#x00A0;HP-LIM&#x00A0;&#x00A0;2GB<br />&#x00A0;&#x00A0;8.0.4.X&#x00A0;&#x00A0;&gt;=10.20&#x00A0;&#x00A0; *)64Gb&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;on raw dev.&#x00A0;&#x00A0;HP-LIM&#x00A0;&#x00A0;2GB<br />&#x00A0;&#x00A0;7.3.4.X&#x00A0;&#x00A0;&gt;=10.20&#x00A0;&#x00A0; *)64Gb&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;on raw dev.&#x00A0;&#x00A0;HP-LIM&#x00A0;&#x00A0;2GB<br />&gt;= 7.3.3.4.1&#x00A0;&#x00A0;11.0&#x00A0;&#x00A0;&#x00A0;&#x00A0;*)64Gb&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;on raw dev.&#x00A0;&#x00A0;HP-LIM&#x00A0;&#x00A0;2GB<br />&#x00A0;&#x00A0;7.3.3.X&#x00A0;&#x00A0;&gt;=10.20&#x00A0;&#x00A0; *)64Gb&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;on raw dev.&#x00A0;&#x00A0;HP-LIM&#x00A0;&#x00A0;2GB<br /><br />*) Data files of 64 GB need at least a block size of 16KB.&#x00A0;&#x00A0;The theoretical file size has been reduced to 4 million Oracle blocks.That is, with a block size of 8KB as used in the SAP environment, the maximum file size is 32GB.<br /><br />From the OS side, the following restrictions apply:<br />Up to and including HP_UX Release 10.10 it is possible to create files with a maximum size of 2GB.<br /><br />To use the functions on higher operating system versions (10.20/11.0) it is necessary to activate the largefiles option for the operating system.<br />You can convert non large-file file systems as follows:<br />/usr/sbin/fsadm -F vxfs -o largefiles &lt;device&gt;<br /> Max file system size:<br /> &lt;= HP-UX 10.10&#x00A0;&#x00A0;&#x00A0;&#x00A0;4Gb<br /> &gt;= HP-UX 10.20&#x00A0;&#x00A0; 128Gb<br /> &gt;= HP-UX 11.00&#x00A0;&#x00A0;&#x00A0;&#x00A0;1Tb<br /><br />&#x00A0;&#x00A0;Max OS file size:<br /> &lt;= HP-UX 10.10&#x00A0;&#x00A0;&#x00A0;&#x00A0;2Gb<br /> &gt;= HP-UX 10.20&#x00A0;&#x00A0; 128Gb<br /> &gt;= HP-UX 11.00&#x00A0;&#x00A0;&#x00A0;&#x00A0; 1Tb<br />_______________________________________________________________________<br />SOLARIS<br /><br />The following tables are valid for Solaris &gt;= 2.6<br />_______________________________________________________________________<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;RELEASE&#x00A0;&#x00A0;&#x00A0;&#x00A0;FS&#x00A0;&#x00A0;&#x00A0;&#x00A0; RAW&#x00A0;&#x00A0;&#x00A0;&#x00A0;EXP/IMP&#x00A0;&#x00A0;SQL Loadr&#x00A0;&#x00A0; Problems?<br />&#x00A0;&#x00A0;10.1.0.1.0&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;no 32-bit release<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;9.2.0.1&#x00A0;&#x00A0; &gt;4GB&#x00A0;&#x00A0; &gt;=2GB&#x00A0;&#x00A0;&gt;=2GB&#x00A0;&#x00A0;&#x00A0;&#x00A0;&gt;=2GB<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;8.1.7.0&#x00A0;&#x00A0; &gt;4GB&#x00A0;&#x00A0; &gt;=2GB&#x00A0;&#x00A0;&gt;=2GB&#x00A0;&#x00A0;&#x00A0;&#x00A0;&gt;=2GB<br /> &gt;=&#x00A0;&#x00A0;8.1.6.0 *)&gt;4GB&#x00A0;&#x00A0; &gt;=2GB&#x00A0;&#x00A0;&gt;=2GB&#x00A0;&#x00A0;&#x00A0;&#x00A0;&gt;=2GB<br /> &gt;=&#x00A0;&#x00A0;8.0.5.1 *)&gt;4GB&#x00A0;&#x00A0;&gt;=2GB&#x00A0;&#x00A0; &lt;2GB&#x00A0;&#x00A0;&#x00A0;&#x00A0; &lt;2GB&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;files &gt; 4GB<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;8.0.5.0&#x00A0;&#x00A0; &lt;2GB&#x00A0;&#x00A0; &gt;=2GB&#x00A0;&#x00A0; &lt;2GB&#x00A0;&#x00A0;&#x00A0;&#x00A0;&lt;2GB<br />&#x00A0;&#x00A0; &gt;=8.0.4.2&#x00A0;&#x00A0; &lt;4GB&#x00A0;&#x00A0; &gt;=2GB&#x00A0;&#x00A0; &lt;2GB&#x00A0;&#x00A0;&#x00A0;&#x00A0; &lt;2GB &#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; files &gt; 4GB<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&lt;8.0.4.X&#x00A0;&#x00A0;&lt;2GB&#x00A0;&#x00A0; &gt;=2GB&#x00A0;&#x00A0; &lt;2GB&#x00A0;&#x00A0;&#x00A0;&#x00A0; &lt;2GB<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;7.3.4.X&#x00A0;&#x00A0; &lt;2GB&#x00A0;&#x00A0; &gt;=2GB&#x00A0;&#x00A0; &lt;2GB&#x00A0;&#x00A0;&#x00A0;&#x00A0; &lt;2GB<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;7.3.3.X&#x00A0;&#x00A0; &lt;2GB&#x00A0;&#x00A0;&gt;=2GB&#x00A0;&#x00A0; &lt;2GB&#x00A0;&#x00A0;&#x00A0;&#x00A0; &lt;2GB<br /><br />For files &gt; 2GB there are only restrictions from Sun<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;file limit&#x00A0;&#x00A0;&#x00A0;&#x00A0;File system limit<br />as of Solaris 2.5.1&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;2 GB&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;1 TB<br />Solaris 2.6 - Solaris 9 12/03 (U3)&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;~1012 GB&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;1 TB<br />Solaris 9 08/03 (U4) - Solaris 10 FCS&#x00A0;&#x00A0; ~1012 GB&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 16 TB<br />See also Sun infodoc 76856 \"Filesystem Size Limitations in Solaris\" for more information about filesystem size on Solaris.<br /><br /><br /><br />*) The theoretical file size has been reduced to 4 million Oracle blocks that is, with a block size of 8KB, as used in the SAP environment, the maximum file size is 32GB.<br />_______________________________________________________________________<br />Windows<br />_______________________________________________________________________<br />On NT systems only files with a size of 4 GB can be created. (FAT). It is theoretically possible to create files up to 16GB on the NTFS file system.<br />Due to a bug, no AUTOEXTEND can be carried out beyond the limit of 4GB at present. This has been fixed as of 8.1.7.4.1. 9.2.0.2 does not contain the fix. Check Note 482435 to determine whether your release supports this before implementing autoextend.</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON> (I800932)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (I800932)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "J", "TranslationLanguage": "日本語", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000129439/J"}, {"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000129439/D"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000129439/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000129439/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000129439/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000129439/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000129439/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000129439/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000129439/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "90937", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "SAPDBA: Database files larger than 2G", "RefUrl": "/notes/90937"}, {"RefNumber": "606395", "RefComponent": "BC-DB-ORA", "RefTitle": "FAQ: Oracle Limitations", "RefUrl": "/notes/606395"}, {"RefNumber": "592393", "RefComponent": "BC-DB-ORA", "RefTitle": "FAQ: Oracle", "RefUrl": "/notes/592393"}, {"RefNumber": "553854", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Oracle: Problems with file size limit", "RefUrl": "/notes/553854"}, {"RefNumber": "546006", "RefComponent": "BC-DB-ORA", "RefTitle": "Problems with Oracle due to operating system errors", "RefUrl": "/notes/546006"}, {"RefNumber": "482435", "RefComponent": "BC-DB-ORA", "RefTitle": "AUTOEXTEND/RESIZE with Oracle 8i and NT/2000", "RefUrl": "/notes/482435"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2434534", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "ORA-01686 - What to do if number of datafiles in tablespace reached max limit?", "RefUrl": "/notes/2434534 "}, {"RefNumber": "592393", "RefComponent": "BC-DB-ORA", "RefTitle": "FAQ: Oracle", "RefUrl": "/notes/592393 "}, {"RefNumber": "606395", "RefComponent": "BC-DB-ORA", "RefTitle": "FAQ: Oracle Limitations", "RefUrl": "/notes/606395 "}, {"RefNumber": "546006", "RefComponent": "BC-DB-ORA", "RefTitle": "Problems with Oracle due to operating system errors", "RefUrl": "/notes/546006 "}, {"RefNumber": "553854", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Oracle: Problems with file size limit", "RefUrl": "/notes/553854 "}, {"RefNumber": "482435", "RefComponent": "BC-DB-ORA", "RefTitle": "AUTOEXTEND/RESIZE with Oracle 8i and NT/2000", "RefUrl": "/notes/482435 "}, {"RefNumber": "90937", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "SAPDBA: Database files larger than 2G", "RefUrl": "/notes/90937 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": []}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}