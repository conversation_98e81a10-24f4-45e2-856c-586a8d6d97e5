{"Request": {"Number": "839187", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 312, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000015884752017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000839187?language=E&token=EF8AA4ACC2F621CEDD8756717F70BE48"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000839187", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000839187/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "839187"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 23}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Installation information"}, "Priority": {"_label": "Priority", "value": "Correction with high priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "31.08.2009"}, "SAPComponentKey": {"_label": "Component", "value": "BC-DB-ORA"}, "SAPComponentKeyText": {"_label": "Component", "value": "Oracle"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Basis Components", "value": "BC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Database Interface, Database Platforms", "value": "BC-DB", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-DB*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Oracle", "value": "BC-DB-ORA", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-DB-ORA*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "839187 - Oracle 10.2.0: Applying patch set/patches/patch collection"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>This note contains important information about applying the current Oracle RDBMS patch sets, the required patches (UNIX), or the patch collection (Windows) and the CPU (UNIX) for Oracle ********.<br /><br />The note applies in the following two situations:</p> <UL><LI>Installing an SAP system on Oracle ******** for the first time:<br /><br />You want to install a new SAP system with a new Oracle database.<br />For more information about this, see the installation guide for your SAP system. The current version of the guide is available at: <NOBR>http://service.sap.com/instguides</NOBR></LI></UL> <UL><LI>Oracle database upgrade for an SAP system:<br /><br />You have already installed an SAP system and you want to upgrade from Oracle Release 9iR2 (9.2.0.X) to Oracle Release ********.<br />Additional information is available in the SAP database upgrade guide for Oracle \"Upgrade to Oracle Database Release 10g Release 2 (10.2)\".<br />This guide describes the preimplementation steps and the postimplementation steps that are required to apply a patch set, a patch, or a patch collection. These steps are not described in this note.<br />The current version of this guide for Windows and for UNIX is provided at: &lt;ZG&gt;http://service.sap.com/instguides -&gt; Installation &lt;(&gt;&amp;&lt;)&gt; Upgrade Guides -&gt; Database Upgrades -&gt; Oracle&lt;/&gt;</LI></UL> <p><br />At the end of this note, you will find a glossary of the most important Oracle terms that relate to applying the patch set, patch or patch collection.<br /><br />This note does not describe upgrades and patches for the following:</p> <UL><LI>Cluster Ready Services (CRS) (see Note 1010217)</LI></UL> <UL><LI>Oracle Cluster File System (OCFS)</LI></UL> <UL><LI>Real Application Clusters (RAC) (see patch set README file).</LI></UL> <p><br /></p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p><br />Oracle database 10g, patch set, patch, patch collection, ********, ********, upgrade from 9.2.0<br /></p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><UL><LI>For more information about the latest features in Oracle Release 10.2, see Note 828268. More information is available at: <NOBR>http://www.oracle.com/technology</NOBR></LI></UL> <UL><LI>For more information about solutions to known problems when installing Oracle software (Oracle Universal Installer, OPatch) or when upgrading the database (<NOBR>DBUA, utlu102i.sql</NOBR>), see Note 841728.</LI></UL> <UL><LI>If you want to apply the current patch set, patches (UNIX), or the patch collection (Windows) and CPUs (UNIX) for Oracle ********, we recommend that you have the relevant Oracle software before the application. You can download this from SAP Service Marketplace at: <NOBR>http://service.sap.com/oracle-download</NOBR></LI></UL> <UL><LI>The installation instructions for the current patch set, patches (UNIX), or the patch collection (Windows) and the CPUs (UNIX) are provided in the relevant README file.</LI></UL> <p></p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p><br />This note describes the steps that are required <B>after</B> you install the Oracle database software. For more information about installing the Oracle software, see the SAP installation guide or the SAP Oracle database upgrade guide.<br />Steps that apply only to UNIX or Windows are clearly marked.<br /><br /><B>Contents:</B><br /><br />1.&#x00A0;&#x00A0;Applying the current patch set<br />2.&#x00A0;&#x00A0;Applying the recommended patches (UNIX) or the patch collection (Windows)<br />3.&#x00A0;&#x00A0;UNIX: Applying the current CPU<br />4.&#x00A0;&#x00A0;Glossary<br /><br /><br /><B>1. Applying the current patch set</B><br />You must apply the current patch sets <B>after</B> you install the Oracle database software.<br />For more information about the current patch set and the download, see Note 871735.<br />To apply the patch set, follow the instructions in the patch set README file.<br /><br /><B>2. Applying the recommended patches (UNIX) or the patch collection (Windows)</B><br />You must apply the patches or the patch collection <B>after</B> you apply the patch set.</p> <UL><LI>UNIX, Linux:<br />For information about the current patches that we recommend for ********, see Note 1137346.<br />You recommend that you use MOPatch to apply these patches (see Note 1027012). When you use MOPatch to apply patches on UNIX/Linux, the system merges the individual patch README files into one README file.<br />Apply the patches as described in the relevant patch README files.</LI></UL> <UL><LI>Windows:<br />On Windows, apart from generic individual patches, a patch collection is applied instead of several individual patches like on Unix. In addition to a patch collection, you can apply further generic patches if required. For information about the current patch collection and the required generic patches, see Note 1137346.<br />You must use OPatch to apply the patch collection and the generic patches (see Note 839182).<br />Apply the patch collection and the generic patches as described in the relevant README file for the patch collection or the patch. The system uses the term \"bundle patch\" instead of \"patch collection\" in the Oracle patch collection README file.<br /></LI></UL> <p>For information about using MOPatch and OPatch to apply patches, see Note 1227404 about the Oracle Configuration Manager (OCM).<br /><br /><B>3. UNIX: Applying the current CPU</B><br />You must apply the current Critical Patch Update (CPU) <B>after</B> you apply the patches recommended for SAP systems.<br />Note 850306 contains an overview of all of the CPUs for Release ******** that are currently released for SAP.<br />Use OPatch to apply the CPU patches (see Note 839182). Using MOPatch to apply these is not supported.<br />Apply the Oracle CPU as described in the relevant README file.<br />Note: If an installation conflict arises between a CPU patch and a patch that is required for the SAP system, the patch that is required for the SAP system has priority over the CPU patch (see Note 1137346).<br /><br /><B>4. Glossary</B><br />Below is a list of terms and their explanations with regard to applying Oracle patch sets and patches.</p> <UL><LI>Patch set<br />Software package that contains a large number of error corrections.<br />It is prerequisite that the software of the relevant Basis release is already installed.<br />The patch sets are cumulative.<br />You must use the Oracle Universal Installer (OUI) to apply this.<br />You can no longer remove a patch set that has been applied.<br />When you apply a patch set, the Oracle version also changes (for example, ********, ********).</LI></UL> <UL><LI>Patch<br />This is marked as a single patch if it corrects one single error.<br />This term is also used as a superordinate term in general (for example, for CPUs (UNIX), merge patches, Windows bundle patches).<br />You must use OPatch to apply this.<br />To install several individual patches on UNIX, see \"MOPatch\".</LI></UL> <UL><LI>Bundle patch (Windows)<br />This is a patch collection to correct several errors (this is also known as a \"patch collection\" in the SAP environment).<br />You must use OPatch to apply it.<br />You can apply only one Windows bundle patch at a time. You may also be able to apply generic patches in addition to the bundle patch (see the \"Generic patch\" section).<br />When you apply a bundle patch, the Oracle version changes (from ********.17 to ********.18). You can use DBA_REGISTRY_HISTORY to determine which bundle patch has been applied.<br />Windows bundle patches are cumulative (that is, bundle patch 18 also contains all of the corrections from bundle patch 17 and the current CPU).</LI></UL> <UL><LI>Patch collection (Windows)<br />See \"Bundle patch\".</LI></UL> <UL><LI>Generic patch<br />This is a patch that is not dependent on the platform. You can identify this from the name of the patch file: p&lt;patchid&gt;_&lt;release&gt;_GENERIC.zip.<br />This generally contains a correction for an SQL script (.sql), a PL/SQL script (.sql, .plb), or a Java class.</LI></UL> <UL><LI>Mini patch (Windows)<br />This is a previous term for a bundle patch. This term is now obsolete.</LI></UL> <UL><LI>Interim patch (UNIX)<br />This is the official Oracle term for an individual patch (see \"Patch\").</LI></UL> <UL><LI>One-off (UNIX)<br />Obsolete term for an interim patch.</LI></UL> <UL><LI>Merge patch (UNIX)<br />This is a patch that consists of several individual patches and groups these under one patch ID.<br />To apply this, use the \"apply\" option in OPatch (opatch apply).<br />A merge patch is required if the corrections contained in individual patches cause a patch conflict and, therefore, you cannot apply the patches individually.<br />You can only apply a merge patch completely or, if a conflict arises, not at all (also see N-Apply-Patch).</LI></UL> <UL><LI>Patch molecule<br />This is an individual patch that forms part of an N-Apply-Patch (see N-Apply-Patch).</LI></UL> <UL><LI>N-Apply-Patch (UNIX)<br />This consists of several patch molecules that are grouped together under a single patch ID.<br />To apply this, use the option \"napply\" in OPatch (opatch napply) and, if required, additional options (-skip_conflicting).<br />If a conflict arises between a patch that you already applied and a patch molecule, the system does not apply the patch molecule that is affected by the conflict. All of the other patch molecules that are not affected by this conflict can be applied (depending on the command line option in OPatch).</LI></UL> <UL><LI>Critical Patch Update (CPU)(UNIX, Windows)<br />This cumulative security patch is recommended by Oracle for security reasons.<br />On Windows, the regular patch collection contains the current CPU.<br />As of Release ********, you must use the \"napply\" option in OPatch (opatch napply) to apply a CPU (in Release ********, use opatch apply).<br />CPUs are issued quarterly on set dates that are publicized in advance by Oracle:<br /><NOBR>http://www.oracle.com/technology/deploy/security/alerts.htm</NOBR>;<br />More information is available on OTN:<br /><NOBR>http://www.oracle.com/technology/pub/columns/davidson_cpu.html</NOBR><br /><NOBR>http://www.oracle.com/technology/deploy/security/alerts.htm</NOBR></LI></UL> <UL><LI> <br /></LI></UL> <UL><LI>Generic Bundle Patch/Recommended Bundle Patch<br />This is not permitted in the SAP environment.<br />You must use the \"napply\" option in OPatch (opatch napply) to apply this.</LI></UL> <UL><LI>CRS bundle patch<br />This is a bundle patch for Cluster Ready Services (CRS) (only for RAC).</LI></UL> <UL><LI>Database Vault patch<br />This is a patch for the Database Vault (DV).<br />This is required only if you installed the DV option.</LI></UL> <UL><LI>OPatch<br />This is an Oracle tool to apply patches.</LI></UL> <UL><LI>MOPatch (UNIX)<br />This is an Oracle tool for UNIX platforms for applying several Oracle RDBMS patches automatically.</LI></UL> <p></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Database System", "Value": "ORACLE"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON> (********)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D036707)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000839187/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000839187/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000839187/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000839187/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000839187/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000839187/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000839187/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000839187/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000839187/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "986578", "RefComponent": "BC-DB-ORA", "RefTitle": "Oracle support in Itanium 2 systems (Montecito & following)", "RefUrl": "/notes/986578"}, {"RefNumber": "871735", "RefComponent": "BC-DB-ORA", "RefTitle": "Oracle 10.2.0: Current patch set", "RefUrl": "/notes/871735"}, {"RefNumber": "871096", "RefComponent": "BC-DB-ORA", "RefTitle": "Oracle 10.2.0: Patches/patch collections for Oracle ********", "RefUrl": "/notes/871096"}, {"RefNumber": "850306", "RefComponent": "BC-DB-ORA", "RefTitle": "Oracle Critical Patch Update Program", "RefUrl": "/notes/850306"}, {"RefNumber": "841728", "RefComponent": "BC-DB-ORA", "RefTitle": "10.2: Solutions for installation and upgrade problems", "RefUrl": "/notes/841728"}, {"RefNumber": "839574", "RefComponent": "BC-DB-ORA", "RefTitle": "Oracle Database 10g: Stopping Oracle CSS Service ocssd.bin", "RefUrl": "/notes/839574"}, {"RefNumber": "839182", "RefComponent": "BC-DB-ORA", "RefTitle": "Oracle patch installation with OPatch", "RefUrl": "/notes/839182"}, {"RefNumber": "828268", "RefComponent": "BC-DB-ORA", "RefTitle": "Oracle Database 10g: New functions", "RefUrl": "/notes/828268"}, {"RefNumber": "582427", "RefComponent": "BC-DB-ORA", "RefTitle": "Error due to inconsistent Oracle DDIC", "RefUrl": "/notes/582427"}, {"RefNumber": "1431752", "RefComponent": "BC-DB-ORA", "RefTitle": "Oracle 10.2.0: Patches/Patch Collections for ********", "RefUrl": "/notes/1431752"}, {"RefNumber": "1228643", "RefComponent": "BC-DB-ORA-RAC", "RefTitle": "Oracle Clusterware: <PERSON><PERSON> on top of Oracle ********", "RefUrl": "/notes/1228643"}, {"RefNumber": "1227404", "RefComponent": "BC-DB-ORA", "RefTitle": "Oracle Configuration Manager (OCM)", "RefUrl": "/notes/1227404"}, {"RefNumber": "1137346", "RefComponent": "BC-DB-ORA", "RefTitle": "Oracle 10.2.0: Patches/patch collections for Oracle ********", "RefUrl": "/notes/1137346"}, {"RefNumber": "1027012", "RefComponent": "BC-DB-ORA", "RefTitle": "MOPatch - Install Multiple Oracle Patches in One Run", "RefUrl": "/notes/1027012"}, {"RefNumber": "1026237", "RefComponent": "BC-DB-ORA", "RefTitle": "FAQ: OPatch and Universal Installer 10.2", "RefUrl": "/notes/1026237"}, {"RefNumber": "1010217", "RefComponent": "BC-DB-ORA", "RefTitle": "Oracle CRS: Patches for RAC enabled SAP Systems", "RefUrl": "/notes/1010217"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "871735", "RefComponent": "BC-DB-ORA", "RefTitle": "Oracle 10.2.0: Current patch set", "RefUrl": "/notes/871735 "}, {"RefNumber": "1431752", "RefComponent": "BC-DB-ORA", "RefTitle": "Oracle 10.2.0: Patches/Patch Collections for ********", "RefUrl": "/notes/1431752 "}, {"RefNumber": "839182", "RefComponent": "BC-DB-ORA", "RefTitle": "Oracle patch installation with OPatch", "RefUrl": "/notes/839182 "}, {"RefNumber": "1027012", "RefComponent": "BC-DB-ORA", "RefTitle": "MOPatch - Install Multiple Oracle Patches in One Run", "RefUrl": "/notes/1027012 "}, {"RefNumber": "841728", "RefComponent": "BC-DB-ORA", "RefTitle": "10.2: Solutions for installation and upgrade problems", "RefUrl": "/notes/841728 "}, {"RefNumber": "1137346", "RefComponent": "BC-DB-ORA", "RefTitle": "Oracle 10.2.0: Patches/patch collections for Oracle ********", "RefUrl": "/notes/1137346 "}, {"RefNumber": "986578", "RefComponent": "BC-DB-ORA", "RefTitle": "Oracle support in Itanium 2 systems (Montecito & following)", "RefUrl": "/notes/986578 "}, {"RefNumber": "871096", "RefComponent": "BC-DB-ORA", "RefTitle": "Oracle 10.2.0: Patches/patch collections for Oracle ********", "RefUrl": "/notes/871096 "}, {"RefNumber": "1228643", "RefComponent": "BC-DB-ORA-RAC", "RefTitle": "Oracle Clusterware: <PERSON><PERSON> on top of Oracle ********", "RefUrl": "/notes/1228643 "}, {"RefNumber": "828268", "RefComponent": "BC-DB-ORA", "RefTitle": "Oracle Database 10g: New functions", "RefUrl": "/notes/828268 "}, {"RefNumber": "1010217", "RefComponent": "BC-DB-ORA", "RefTitle": "Oracle CRS: Patches for RAC enabled SAP Systems", "RefUrl": "/notes/1010217 "}, {"RefNumber": "1227404", "RefComponent": "BC-DB-ORA", "RefTitle": "Oracle Configuration Manager (OCM)", "RefUrl": "/notes/1227404 "}, {"RefNumber": "1026237", "RefComponent": "BC-DB-ORA", "RefTitle": "FAQ: OPatch and Universal Installer 10.2", "RefUrl": "/notes/1026237 "}, {"RefNumber": "582427", "RefComponent": "BC-DB-ORA", "RefTitle": "Error due to inconsistent Oracle DDIC", "RefUrl": "/notes/582427 "}, {"RefNumber": "839574", "RefComponent": "BC-DB-ORA", "RefTitle": "Oracle Database 10g: Stopping Oracle CSS Service ocssd.bin", "RefUrl": "/notes/839574 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": []}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}