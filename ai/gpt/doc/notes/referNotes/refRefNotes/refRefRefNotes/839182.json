{"Request": {"Number": "839182", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 273, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000015884732017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000839182?language=E&token=DE61C86F3F79DD110285E5F4AED5BE74"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000839182", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000839182/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "839182"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 78}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Installation information"}, "Priority": {"_label": "Priority", "value": "Correction with high priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "2018/04/25"}, "SAPComponentKey": {"_label": "Component", "value": "BC-DB-ORA"}, "SAPComponentKeyText": {"_label": "Component", "value": "Oracle"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Basis Components", "value": "BC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Database Interface, Database Platforms", "value": "BC-DB", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-DB*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Oracle", "value": "BC-DB-ORA", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-DB-ORA*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "839182 - Oracle patch installation with OPatch"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p><strong>OPatch - Oracle patch tool</strong></p>\r\n<p>This SAP Note describes how to use <span style=\"text-decoration: underline;\">OPatch</span> to install and uninstall Oracle database patches on UNIX and Windows platforms in an SAP environment.</p>\r\n<p>This SAP Note applies to Oracle Database Release 10g and higher. For Oracle Database 9i Release 2 (9.2), see <a target=\"_blank\" href=\"/notes/306408\">SAP Note 306408</a>.</p>\r\n<p><span style=\"text-decoration: underline;\">OPatch in the Oracle online documentation</span></p>\r\n<p>Oracle database online documentation <a target=\"_blank\" href=\"http://docs.oracle.com/en/database/\">http://docs.oracle.com/en/database/</a></p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td><span style=\"text-decoration: underline;\">Release</span></td>\r\n<td><span style=\"text-decoration: underline;\">Reference</span></td>\r\n</tr>\r\n<tr>\r\n<td>12c (12.x)</td>\r\n<td>\r\n<p>See '<a target=\"_blank\" href=\"https://docs.oracle.com/cd/E73210_01/OPTCH/toc.htm\">OPatch Users's Guide</a>' from <a target=\"_blank\" href=\"https://docs.oracle.com/cd/E73210_01/index.htm\">Oracle Enterprise Manager Cloud Control Online Documentation Library, Release 13.2</a>&#x00A0;/ <a target=\"_blank\" href=\"https://docs.oracle.com/cd/E73210_01/nav/reference.htm\">Reference</a><br />or<br />see '<a target=\"_blank\" href=\"https://docs.oracle.com/cd/E24628_01/doc.121/e39376/toc.htm\">OPatch User's Guide</a>' from <a target=\"_blank\" href=\"https://docs.oracle.com/en/enterprise-manager/\">Enterprise Manager</a>&#x00A0;/ <a target=\"_blank\" href=\"https://docs.oracle.com/cd/E24628_01/index.htm\">Oracle Enterprise Manager Cloud Control Documentation, 12c Release 5</a> / <a target=\"_blank\" href=\"https://docs.oracle.com/cd/E24628_01/nav/reference.htm\">Reference</a></p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>11g (11.2)</td>\r\n<td>\r\n<p>See '<a target=\"_blank\" href=\"http://docs.oracle.com/cd/E11882_01/em.112/e12255/toc.htm\">Universal Installer and OPatch User's Guide</a>' from <a target=\"_blank\" href=\"http://docs.oracle.com/en/database/database.html\">Database </a>/ <a target=\"_blank\" href=\"http://docs.oracle.com/cd/E11882_01/index.htm\">Oracle Database Online Documentation 11g Release 2 (11.2)</a> / <a target=\"_blank\" href=\"http://docs.oracle.com/cd/E11882_01/nav/portal_11.htm\">Installing and Upgrading</a></p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>10g (10.2)</td>\r\n<td>\r\n<p>See '<a target=\"_blank\" href=\"http://docs.oracle.com/cd/B19306_01/em.102/b16227/toc.htm\">Universal Installer and OPatch User's Guide</a>' from <a target=\"_blank\" href=\"http://docs.oracle.com/en/database/database.html\">Database</a> / <a target=\"_blank\" href=\"http://docs.oracle.com/cd/B19306_01/index.htm\">Oracle Database Online Documentation, 10g Release 2 (10.2)</a> / <a target=\"_blank\" href=\"http://docs.oracle.com/cd/B19306_01/nav/portal_2.htm\">Installation</a></p>\r\n</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p>&#x00A0;</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>MOPatch</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p><span style=\"text-decoration: underline;\">Using OPatch for the installation of Oracle database patches</span></p>\r\n<p>The following table contains an overvoew of abbreviations and names used in this SAP Note.</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td><span style=\"text-decoration: underline;\">Abbreviation/name</span></td>\r\n<td><span style=\"text-decoration: underline;\">Description</span></td>\r\n</tr>\r\n<tr>\r\n<td><em>&lt;opatch&gt;</em></td>\r\n<td>\r\n<p><span style=\"text-decoration: underline;\">Call of OPatch on Unix</span></p>\r\n<p><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">&#xFEFF;OS&gt; $ORACLE_HOME/OPatch/opatch&#xFEFF; &lt;args&gt;</span></p>\r\n<p><span style=\"text-decoration: underline;\">Call of OPatch on Windows</span></p>\r\n<p><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">&#xFEFF;OS&gt; %ORACLE_HOME%\\OPatch\\opatch&#xFEFF; &lt;args&gt;</span></p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td><em>&lt;patch_base_dir&gt;</em></td>\r\n<td>Base directory for database patches</td>\r\n</tr>\r\n<tr>\r\n<td><em>&lt;patchfile&gt;</em></td>\r\n<td>Compressed patch file (zip file that contains the patch)</td>\r\n</tr>\r\n<tr>\r\n<td><em>&lt;patch_dir&gt;</em></td>\r\n<td>Patch directory that results when unpacking a patch file. <br />The name of the directory corresponds to the number of the related Oracle error (bug ID).</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p>&#x00A0;</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p><strong><a target=\"_blank\" name=\"OPatch_Infos\"></a>General information and prerequisites for OPatch</strong></p><ul>\r\n<li>OPatch is used to install Oracle database patches</li>\r\n<li>For the installation of SAP bundle patches (SBPs) under UNIX, MOPatch is used in accordance with the instructions in the SBP README file. OPatch is used to install SBPs only if the SBP README file explicitly states it.</li>\r\n<li>The OPatch tool is located in the directory '&lt;ORACLE_HOME&gt;/OPatch'.</li>\r\n<li>The same operating system user that was used to install the Oracle software is also used when you install or uninstall patches using OPatch.</li>\r\n<li>The environment variable ORACLE_HOME must be set. If this condition is not met, the system issues the following message: <br /><em>Oracle Home is not set. OPatch cannot proceed!</em><br /><em>OPatch failed with error code = 1<br /></em>As of OPatch version ********.6, ORACLE_HOME (on Windows) does not necessarily have to be set.</li>\r\n<li>Windows: On Windows Server 2008 or Windows Server 2008 R2, OPatch must be started by a command prompt or a PowerShell in 'elevated mode' (that is, with administrator privileges).</li>\r\n<li>Without extending the search path, you can call OPatch in the following way:<br />UNIX: OS&gt; $ORACLE_HOME/OPatch/opatch [command] [options]<br />Windows: OS&gt; %ORACLE_HOME%\\OPatch\\opatch [command] [options]</li>\r\n<li>Refer to the information provided in the patch README (pre-installation steps, post-installation steps). Generic patches usually require SQL commands or an SQL or PL/SQL script to be executed to activate the patch in a database.</li>\r\n<li>Before you install or uninstall patches, you must stop all the instances and processes that were started from the ORACLE_HOME. On Windows, you must also stop the database services (listener, agent, and DB service).</li>\r\n<li>You can install several Oracle database patches in direct succession without having to restart the Oracle instance after each patch, provided that the patch description does not contain other instructions (for UNIX, see Note 1027012 about MOPatch).</li>\r\n<li>You can use OPatch (see lsinventory below) to determine the patches that are currently installed.</li>\r\n<li>After you install or uninstall patches, you can check and control the new patch status using \"opatch lsinventory\".</li>\r\n<li>The OPatch log files are stored in the directory <em>ORACLE_HOME/cfgtoollogs/opatch</em>. In addition to these action logs for each individual OPatch action, OPatch also logs all the OPatch activities in a summary log opatch_history.txt in the same directory. OPatch inventory listings are also saved in the directory &lt;ORACLE_HOME&gt;/cfgtoollogs/opatch/lsinv.</li>\r\n</ul>\r\n<p><strong><a target=\"_blank\" name=\"OPATCH_Kommando_Uebersicht\"></a>Overview of OPatch commands</strong></p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td><span style=\"text-decoration: underline;\">OPatch command</span></td>\r\n<td><span style=\"text-decoration: underline;\">Description</span></td>\r\n</tr>\r\n<tr>\r\n<td><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\"><em>&#xFEFF;OS&gt; opatch version&#xFEFF;</em></span></td>\r\n<td>Displaying the OPatch version</td>\r\n</tr>\r\n<tr>\r\n<td><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\"><em>OS&gt; opatch -help</em></span></td>\r\n<td>Displaying the OPatch online help</td>\r\n</tr>\r\n<tr>\r\n<td><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\"><em>OS&gt; opatch &lt;command&gt; -help</em></span></td>\r\n<td>Displaying the OPatch online help for a certain command; for example,&lt;command&gt; = apply, rollback, lsinventory, util</td>\r\n</tr>\r\n<tr>\r\n<td><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\"><em>OS&gt; opatch lsinventory [-all] [-detail]</em></span></td>\r\n<td>Displaying the Oracle inventory</td>\r\n</tr>\r\n<tr>\r\n<td><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\"><em>OS&gt; opatch apply</em></span></td>\r\n<td>Installing <span style=\"text-decoration: underline;\">a</span> patch</td>\r\n</tr>\r\n<tr>\r\n<td><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\"><em>OS&gt; opatch napply</em></span></td>\r\n<td>Installing <span style=\"text-decoration: underline;\">several</span> patches</td>\r\n</tr>\r\n<tr>\r\n<td><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\"><em>OS&gt; opatch rollback -id &lt;patchid&gt;</em></span></td>\r\n<td>Uninstalling a patch</td>\r\n</tr>\r\n<tr>\r\n<td><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\"><em>OS&gt; opatch util</em></span></td>\r\n<td>Special functions of OPatch</td>\r\n</tr>\r\n<tr>\r\n<td><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\"><em>OS&gt; opatch util cleanup</em></span></td>\r\n<td>Cleanup of the patch storage directory<br />When you install patches using MOPatch (Unix/Linux), the patch storage directory is automatically cleaned up by default using this option.</td>\r\n</tr>\r\n<tr>\r\n<td><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\"><em>OS&gt; opatch &lt;command&gt; -silent</em></span></td>\r\n<td>\r\n<p>Suppresses the interactive user query <em>\"Is the local system ready for patching?\"</em>.</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\"><em>OS&gt; opatch &lt;command&gt; -verbose</em></span></td>\r\n<td>\r\n<p>Display detailed information about the patch process</p>\r\n</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p>&#x00A0;</p><p><strong><a target=\"_blank\" name=\"OPATCH_LSINVENTORY\"></a>Display the installed patches</strong></p>\r\n<p>\"opatch lsinventory\" shows the patches that are currently installed in the Oracle Home. Prerequisite: ORACLE_HOME is set.</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td><span style=\"text-decoration: underline;\">OPatch command</span></td>\r\n<td><span style=\"text-decoration: underline;\">Description</span></td>\r\n</tr>\r\n<tr>\r\n<td><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\"><em>&#xFEFF;opatch lsinventory&#xFEFF;</em></span></td>\r\n<td>lists the installed patches in the current Oracle Home</td>\r\n</tr>\r\n<tr>\r\n<td><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\"><em>opatch lsinventory -all</em></span></td>\r\n<td>lists the installed patches in the current Oracle Home and all existing Oracle Homes</td>\r\n</tr>\r\n<tr>\r\n<td><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\"><em>opatch lsinventory -details</em></span></td>\r\n<td>shows detailed information about each individual patch (modules, make objects, and so on)</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\"><em>opatch lsinventory -group_by_date</em></span></p>\r\n</td>\r\n<td>lists the installed patches on a daily basis, grouped by installation date</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\"><em>opatch lsinventory -inactive</em></span></p>\r\n</td>\r\n<td>lists the installed inactive patches. Available with OPatch ********.5, OPatch ********.14, and later versions. See <a target=\"_blank\" href=\"/notes/2422996\">SAP Note 2422996</a>.</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p>&#x00A0;Platform-specific call:</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td><span style=\"text-decoration: underline;\">Unix/Linux</span></td>\r\n<td><span style=\"text-decoration: underline;\">Windows</span></td>\r\n</tr>\r\n<tr>\r\n<td>$ORACLE_HOME/OPatch/opatch lsinventory</td>\r\n<td>%ORACLE_HOME%\\OPatch\\opatch lsinventory</td>\r\n</tr>\r\n</tbody>\r\n</table></div><p><strong><a target=\"_blank\" name=\"OPATCH_NEUE_VERSION\"></a>Installing a new OPatch version</strong></p><p><span style=\"text-decoration: underline;\">SAP-specific OPatch version</span></p>\r\n<p>There is a special SAP version of OPatch for SAP environments. This can recognized by the following message or heading:<br /><span style=\"text-decoration: underline;\"><br /></span><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">========================================================</span><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">GENERIC OPATCH VERSION - FOR USE IN SAP ENVIRONMENT ONLY</span><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">========================================================&#xFEFF;</span></p>\r\n<p>The current SAP version of OPatch is available via the <a target=\"_blank\" href=\"http://support.sap.com/software/databases.html\">SAP Support Portal</a> and can be downloaded from there. For Unix platforms or Linux platforms, the current version of OPatch is also contained in the relevant SAP bundle patch (SBP) and can be updated according to the SBP README file when you install the SBP.</p>\r\n<p>The installation is as follows:</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td><span style=\"text-decoration: underline;\">Step</span></td>\r\n<td><span style=\"text-decoration: underline;\">Description</span></td>\r\n<td><span style=\"text-decoration: underline;\">Unix/Linux</span></td>\r\n</tr>\r\n<tr>\r\n<td>1.</td>\r\n<td>Download the new OPatch version from the SAP Support Portal.</td>\r\n<td>&#xFEFF;-&gt; 'Oracle Patches &lt;release&gt;' -&gt; '# OS independent' -&gt; OPatch File</td>\r\n</tr>\r\n<tr>\r\n<td>2.</td>\r\n<td>Rename the old OPatch version installed in the Oracle home.</td>\r\n<td>\r\n<p>Unix: <br />&#xFEFF;&#xFEFF;$ cd $ORACLE_HOME<br />$ mv OPatch OPatch.SAVE&#xFEFF;&#xFEFF;&#xFEFF;&#xFEFF;&#xFEFF;&#xFEFF;</p>\r\n<p>Windows:<br />&#xFEFF;OS&gt; cd %ORACLE_HOME%<br />OS&gt; rename OPatch OPatch.SAVE&#xFEFF;&#xFEFF;&#xFEFF;&#xFEFF;</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>3.</td>\r\n<td>Unpack the file with the new OPatch version into the Oracle Home.</td>\r\n<td>\r\n<p>Unix: &#xFEFF;&#xFEFF;$ unzip -d $ORACLE_HOME &lt;file&gt;&#xFEFF;&#xFEFF;&#xFEFF;&#xFEFF;</p>\r\n<p>Windows: Unpack with winzip, winrar, or a similar process.</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>4.</td>\r\n<td>Test the new version, for example, by displaying the version.</td>\r\n<td>\r\n<p>Unix: &#xFEFF;$ $ORACLE_HOME/OPatch/opatch version&#xFEFF;&#xFEFF;</p>\r\n<p>Windows: &#xFEFF;&#xFEFF;OS&gt; %ORACLE_HOME%\\OPatch\\opatch version&#xFEFF;&#xFEFF;&#xFEFF;&#xFEFF;</p>\r\n</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><strong><a target=\"_blank\" name=\"PATCH_INSTALL_UNIX\"></a>Installing database patches on Unix/Linux</strong></p>\r\n<p>For the installation of SAP bundle patches (SBPs) under UNIX, MOPatch is used in accordance with the instructions in the SBP README file. OPatch is used to install SBPs only if the SBP README file explicitly states it.</p>\r\n<p>The following table describes the procedure for installing other and additional database patches with OPatch.</p>\r\n<p>Prerequisites: The enviironment variable ORACLE_HOME must be set and you must be logged on as the software owner.</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td><span style=\"text-decoration: underline;\">Step</span></td>\r\n<td><span style=\"text-decoration: underline;\">Description</span></td>\r\n<td><span style=\"text-decoration: underline;\">Command</span></td>\r\n</tr>\r\n<tr>\r\n<td>1.</td>\r\n<td>\r\n<p>Create a base directory &lt;patch_base_dir&gt; for the patches to be installed.<br />For example: \"patches.&lt;release&gt;'\"</p>\r\n</td>\r\n<td>\r\n<p>&#x00A0;OS&gt; mkdir patches.&lt;release&gt;</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>2.</td>\r\n<td>Copy the patch file(s) that is (are) to be installed to the base directory and unpack them there. <br />In this case, an individual patch directory &lt;patch_dir&gt; is created for each individual patch &lt;patch_file&gt;. <br />in the base directory.<br />Refer to the patch README.</td>\r\n<td>OS&gt; cp &lt;patchfile&gt; &lt;patch_base_dir&gt;<br />OS&gt;&#x00A0;cd &lt;patch_base_dir&gt;<br />OS&gt; unzip &lt;patchfile&gt;</td>\r\n</tr>\r\n<tr>\r\n<td>3.</td>\r\n<td>Stop the Oracle database instance and all running processes from the Oracle Home.<br />Refer to the patch README.</td>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td>4.</td>\r\n<td>Create a backup of the Oracle Home and the Oracle Inventory.</td>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td>5.</td>\r\n<td>Install the patch with OPatch. Refer to the patch README.</td>\r\n<td>\r\n<p><span style=\"text-decoration: underline;\">Call option #1</span><br />OS&gt; $ORACLE_HOME/OPatch/opatch apply &lt;patch_dir&gt;<br /><br /><span style=\"text-decoration: underline;\">Call option #2</span><br />OS&gt; cd &lt;patch_dir&gt;<br />OS&gt; $ORACLE_HOME/OPatch/opatch apply</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>6.</td>\r\n<td>Refer to the patch README regarding patch post-installation steps that may be necessary.</td>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td>7.</td>\r\n<td>Clean up the patch storage directory (cleanup, optional).</td>\r\n<td>OS&gt; $ORACLE_HOME/OPatch/opatch util cleanup</td>\r\n</tr>\r\n<tr>\r\n<td>8.</td>\r\n<td>Check the patch in the inventory.</td>\r\n<td>OS&gt; $ORACLE_HOME/OPatch/opatch lsinventory</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><strong><br /></strong><br /><strong><a target=\"_blank\" name=\"PATCH_INSTALL_WINDOWS\"></a>Installing database patches on Windows</strong></p>\r\n<p>For information about the installation of database patches on Windows, follow the respective installation instructions in the README file for the patch.<br /><br />Important note: To install bundle patches for Oracle database Release ********, you must use at least OPatch Version ********.6.</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td><span style=\"text-decoration: underline;\">Step</span></td>\r\n<td><span style=\"text-decoration: underline;\">Description</span></td>\r\n<td><span style=\"text-decoration: underline;\">Command</span></td>\r\n</tr>\r\n<tr>\r\n<td>1</td>\r\n<td>\r\n<p>Create a base directory &lt;patch_base_dir&gt; for the patches to be installed.<br />For example: \"patches.&lt;release&gt;'\"</p>\r\n</td>\r\n<td>\r\n<p>OS&gt; mkdir patches.&lt;release&gt;</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>2</td>\r\n<td>Copy the patch file(s) that is (are) to be installed to the base directory and unpack them there. <br />In this case, an individual patch directory &lt;patch_dir&gt; is created for each individual patch &lt;patch_file&gt;. <br />in the base directory.</td>\r\n<td>zip, winzip, winrar, ...</td>\r\n</tr>\r\n<tr>\r\n<td>3</td>\r\n<td>Stop the Oracle database instance and all running <br />Oracle processes and Oracle services from the Oracle Home.</td>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td>4</td>\r\n<td>Create a backup of the Oracle Home and the Oracle Inventory.</td>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td>5</td>\r\n<td>For installation with OPatch, start a Windows Command Prompt or <br />a Windows Powershell <span style=\"text-decoration: underline;\">as administrator</span> (\"run as administrator\"&#x00A0;or \"elevated mode\").</td>\r\n<td>cmd.exe, powershell.exe</td>\r\n</tr>\r\n<tr>\r\n<td>6</td>\r\n<td>Install the patch with OPatch.</td>\r\n<td>Call option #1:<br />OS&gt; %ORACLE_HOME%\\OPatch\\opatch apply &lt;patch_dir&gt;<br /><br />Call option #2:<br />OS&gt; cd &lt;patch_dir&gt;<br />OS&gt; %ORACLE_HOME%\\OPatch\\opatch apply</td>\r\n</tr>\r\n<tr>\r\n<td>7</td>\r\n<td>Refer to the patch README regarding post-installation steps.</td>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td>8</td>\r\n<td>Recommendation: Clean up the patch storage directory (cleanup).</td>\r\n<td>OS&gt; %ORACLE_HOME%\\OPatch\\opatch util cleanup</td>\r\n</tr>\r\n<tr>\r\n<td>9</td>\r\n<td>Check the patch in the inventory.</td>\r\n<td>OS&gt; %ORACLE_HOME%\\OPatch\\opatch lsinventory</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><br /><strong><a target=\"_blank\" name=\"OPATCH_NAPPLY\"></a>Installation of several patches with \"napply\"</strong></p>\r\n<p>OPatch provides the option \"napply\", which enables you to install several patches with an OPatch call.</p>\r\n<ul>\r\n<li>Copy all patches to be installed to an empty directory &lt;patch_base_dir&gt;.</li>\r\n<li>The patch files must have '.zip' as the file ending (not '.ZIP').</li>\r\n<li>The best thing to do is to switch to the directory &lt;patch_base_dir&gt; and call OPatch from here with 'OS&gt; &lt;ORACLE_HOME&gt;/OPatch/opatch napply'.</li>\r\n</ul>\r\n<p>Here are the individual steps:</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td><span style=\"text-decoration: underline;\">Step</span></td>\r\n<td><span style=\"text-decoration: underline;\">Description</span></td>\r\n<td><span style=\"text-decoration: underline;\">Command</span></td>\r\n</tr>\r\n<tr>\r\n<td>1</td>\r\n<td>Copy the patch files (&lt;patchfile 1&gt;, &lt;patchfile2&gt;, and so on) of the patches to be installed to the base directory &lt;patch_base_dir&gt;.</td>\r\n<td>OS&gt; cp &lt;patchfile1&gt; &lt;patch_base_dir&gt;<br />OS&gt; cp &lt;patchfile2&gt; &lt;patch_base_dir&gt;<br />...</td>\r\n</tr>\r\n<tr>\r\n<td>2</td>\r\n<td>\r\n<p>Do not unpack the patch files.</p>\r\n</td>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td>3</td>\r\n<td>\r\n<p>CAUTION: Oracle patch files &lt;patch_file&gt; from the SAP Support Portal with the file ending '.ZIP' (uppercase letters) must be renamed as files &lt;patch_file&gt; with the file ending '.zip' (lowercase letters). Otherwise, OPatch does not recognize the files as patch files, and reports the following:<br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">UtilSession failed: NApply was not able to get the list of patches to apply.&#xFEFF;</span></p>\r\n</td>\r\n<td>\r\n<p><span style=\"text-decoration: underline;\">Windows (command):<br /><br /></span>OS&gt; rename *.ZIP *.zip</p><p><span style=\"text-decoration: underline;\"><span style=\"text-decoration: underline;\">Windows (PowerShell):</span></span></p>\r\n<p>PS1&gt; Get-ChildItem -Filter \"*ZIP*\" -Recurse | Rename-Item -NewName {$_.name -replace 'ZIP','zip'}</p><p><span style=\"text-decoration: underline;\"><br /><br />UNIX:</span><br />OS&gt; mv &lt;patch_file1&gt;.ZIP &lt;patch_file1&gt;.zip<br />OS&gt; mv &lt;patch_file&gt;.ZIP &lt;patch_file&gt;.zip<br />OS&gt; ...</p></td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>4</p>\r\n</td>\r\n<td>\r\n<p>Install the patches with OPatch, using the \"napply\" option. <br />If some patches are already installed, you can skip them with '-skip_duplicate'.</p>\r\n</td>\r\n<td>\r\n<p>OS&gt; &lt;opatch&gt; napply &lt;patch_base_dir&gt; [-skip_duplicate]</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>5</td>\r\n<td>Perform the postprocessing steps for the patches (patch post-installation steps).</td>\r\n<td>&nbsp;</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p>&#x00A0;</p><p>&#x00A0;</p><p>&#x00A0;</p><p>&#x00A0;</p><p>&#x00A0;</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "BC-DB-ORA-DBA (Database Administration)"}, {"Key": "Database System", "Value": "ORACLE"}, {"Key": "Database System", "Value": "Oracle 11.2"}, {"Key": "Database System", "Value": "Oracle 10"}, {"Key": "Database System", "Value": "Oracle 10.2"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON> (C5000979)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON>-<PERSON><PERSON><PERSON> (D029385)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000839182/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000839182/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000839182/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000839182/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000839182/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000839182/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000839182/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000839182/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000839182/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "899070", "RefComponent": "BC-DB-ORA", "RefTitle": "ORA-01406 reported on SELECT against SAP Tables (eg.TRDIR)", "RefUrl": "/notes/899070"}, {"RefNumber": "871735", "RefComponent": "BC-DB-ORA", "RefTitle": "Oracle 10.2.0: Current patch set", "RefUrl": "/notes/871735"}, {"RefNumber": "871096", "RefComponent": "BC-DB-ORA", "RefTitle": "Oracle 10.2.0: Patches/patch collections for Oracle ********", "RefUrl": "/notes/871096"}, {"RefNumber": "839574", "RefComponent": "BC-DB-ORA", "RefTitle": "Oracle Database 10g: Stopping Oracle CSS Service ocssd.bin", "RefUrl": "/notes/839574"}, {"RefNumber": "839187", "RefComponent": "BC-DB-ORA", "RefTitle": "Oracle 10.2.0: Applying patch set/patches/patch collection", "RefUrl": "/notes/839187"}, {"RefNumber": "820062", "RefComponent": "BC-DB-ORA", "RefTitle": "Oracle Database 10g: Patch sets/patches for 10.1.0", "RefUrl": "/notes/820062"}, {"RefNumber": "640910", "RefComponent": "BC-DB-ORA", "RefTitle": "Problems when using OPatch", "RefUrl": "/notes/640910"}, {"RefNumber": "629588", "RefComponent": "BC-DB-ORA", "RefTitle": "Current versions of OPatch and OUI", "RefUrl": "/notes/629588"}, {"RefNumber": "306408", "RefComponent": "BC-DB-ORA", "RefTitle": "Oracle 9.2.0: Using OPatch to install patches", "RefUrl": "/notes/306408"}, {"RefNumber": "1431800", "RefComponent": "BC-DB-ORA", "RefTitle": "Oracle 11.2.0: Central Technical Note", "RefUrl": "/notes/1431800"}, {"RefNumber": "1431752", "RefComponent": "BC-DB-ORA", "RefTitle": "Oracle 10.2.0: Patches/Patch Collections for ********", "RefUrl": "/notes/1431752"}, {"RefNumber": "1228643", "RefComponent": "BC-DB-ORA-RAC", "RefTitle": "Oracle Clusterware: <PERSON><PERSON> on top of Oracle ********", "RefUrl": "/notes/1228643"}, {"RefNumber": "1227404", "RefComponent": "BC-DB-ORA", "RefTitle": "Oracle Configuration Manager (OCM)", "RefUrl": "/notes/1227404"}, {"RefNumber": "1178339", "RefComponent": "BC-DB-ORA", "RefTitle": "Universal Installer / OPatch tools are Hanging and / or Fail", "RefUrl": "/notes/1178339"}, {"RefNumber": "1137346", "RefComponent": "BC-DB-ORA", "RefTitle": "Oracle 10.2.0: Patches/patch collections for Oracle ********", "RefUrl": "/notes/1137346"}, {"RefNumber": "1027012", "RefComponent": "BC-DB-ORA", "RefTitle": "MOPatch - Install Multiple Oracle Patches in One Run", "RefUrl": "/notes/1027012"}, {"RefNumber": "1026237", "RefComponent": "BC-DB-ORA", "RefTitle": "FAQ: OPatch and Universal Installer 10.2", "RefUrl": "/notes/1026237"}, {"RefNumber": "1010217", "RefComponent": "BC-DB-ORA", "RefTitle": "Oracle CRS: Patches for RAC enabled SAP Systems", "RefUrl": "/notes/1010217"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "871735", "RefComponent": "BC-DB-ORA", "RefTitle": "Oracle 10.2.0: Current patch set", "RefUrl": "/notes/871735 "}, {"RefNumber": "1431752", "RefComponent": "BC-DB-ORA", "RefTitle": "Oracle 10.2.0: Patches/Patch Collections for ********", "RefUrl": "/notes/1431752 "}, {"RefNumber": "1431800", "RefComponent": "BC-DB-ORA", "RefTitle": "Oracle 11.2.0: Central Technical Note", "RefUrl": "/notes/1431800 "}, {"RefNumber": "1027012", "RefComponent": "BC-DB-ORA", "RefTitle": "MOPatch - Install Multiple Oracle Patches in One Run", "RefUrl": "/notes/1027012 "}, {"RefNumber": "1137346", "RefComponent": "BC-DB-ORA", "RefTitle": "Oracle 10.2.0: Patches/patch collections for Oracle ********", "RefUrl": "/notes/1137346 "}, {"RefNumber": "640910", "RefComponent": "BC-DB-ORA", "RefTitle": "Problems when using OPatch", "RefUrl": "/notes/640910 "}, {"RefNumber": "306408", "RefComponent": "BC-DB-ORA", "RefTitle": "Oracle 9.2.0: Using OPatch to install patches", "RefUrl": "/notes/306408 "}, {"RefNumber": "871096", "RefComponent": "BC-DB-ORA", "RefTitle": "Oracle 10.2.0: Patches/patch collections for Oracle ********", "RefUrl": "/notes/871096 "}, {"RefNumber": "1228643", "RefComponent": "BC-DB-ORA-RAC", "RefTitle": "Oracle Clusterware: <PERSON><PERSON> on top of Oracle ********", "RefUrl": "/notes/1228643 "}, {"RefNumber": "839187", "RefComponent": "BC-DB-ORA", "RefTitle": "Oracle 10.2.0: Applying patch set/patches/patch collection", "RefUrl": "/notes/839187 "}, {"RefNumber": "1010217", "RefComponent": "BC-DB-ORA", "RefTitle": "Oracle CRS: Patches for RAC enabled SAP Systems", "RefUrl": "/notes/1010217 "}, {"RefNumber": "1227404", "RefComponent": "BC-DB-ORA", "RefTitle": "Oracle Configuration Manager (OCM)", "RefUrl": "/notes/1227404 "}, {"RefNumber": "1178339", "RefComponent": "BC-DB-ORA", "RefTitle": "Universal Installer / OPatch tools are Hanging and / or Fail", "RefUrl": "/notes/1178339 "}, {"RefNumber": "820062", "RefComponent": "BC-DB-ORA", "RefTitle": "Oracle Database 10g: Patch sets/patches for 10.1.0", "RefUrl": "/notes/820062 "}, {"RefNumber": "1026237", "RefComponent": "BC-DB-ORA", "RefTitle": "FAQ: OPatch and Universal Installer 10.2", "RefUrl": "/notes/1026237 "}, {"RefNumber": "629588", "RefComponent": "BC-DB-ORA", "RefTitle": "Current versions of OPatch and OUI", "RefUrl": "/notes/629588 "}, {"RefNumber": "839574", "RefComponent": "BC-DB-ORA", "RefTitle": "Oracle Database 10g: Stopping Oracle CSS Service ocssd.bin", "RefUrl": "/notes/839574 "}, {"RefNumber": "899070", "RefComponent": "BC-DB-ORA", "RefTitle": "ORA-01406 reported on SELECT against SAP Tables (eg.TRDIR)", "RefUrl": "/notes/899070 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": []}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}