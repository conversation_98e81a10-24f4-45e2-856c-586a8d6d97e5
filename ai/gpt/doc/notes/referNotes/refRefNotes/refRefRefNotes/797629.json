{"Request": {"Number": "797629", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 1248, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000015806782017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000797629?language=E&token=251D086703DE8EFB162BECEB2DE40BDE"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000797629", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000797629/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "797629"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 30}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "FAQ"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "05.05.2014"}, "SAPComponentKey": {"_label": "Component", "value": "BC-DB-ORA"}, "SAPComponentKeyText": {"_label": "Component", "value": "Oracle"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Basis Components", "value": "BC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Database Interface, Database Platforms", "value": "BC-DB", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-DB*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Oracle", "value": "BC-DB-ORA", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-DB-ORA*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "797629 - FAQ: Oracle histograms"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<ol>1. What are histograms?</ol><ol>2. What are histogram buckets?</ol><ol>3. What kinds of histograms are there?</ol><ol>4. What limitations do histograms have?</ol><ol>5. How can I create histograms using Oracle?</ol><ol>6. How can I create histograms using SAP?</ol><ol>7. How can I delete histograms?</ol><ol>8. Where is histogram information stored?</ol><ol>9. How can I determine whether histograms are created for a table?</ol><ol>10. How can I determine for which tables histograms are created?</ol><ol>11. When are Oracle histograms useful?</ol><ol>12. Why did SAP also recommend creating histograms in other cases?</ol><ol>13. What must I do to ensure that I do not experience performance problems as of Oracle ******* due to the corrected histogram bug?</ol><ol>14. When are histograms useful in an R/3 environment?</ol><ol>15. Where are histograms useful in the SAP environment?</ol><ol>16. Why is ENDPOINT_ACTUAL_VALUE in DBA_TAB_HISTOGRAMS partially unfilled?</ol><ol><ol>17. What problems are there regarding histograms?</ol></ol>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>FAQ</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<ol>1. What are histograms?</ol>\r\n<p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Histograms are an optional component for database statistics. Database statistics are required as a basis for the decision process of the cost-based optimizer (CBO) when determining the optimum access path (index access, full table scan...) (see Note 588668).</p>\r\n<p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Histograms contain information about how the data of a table column is distributed. If there are no histograms, Oracle assumes the column values are distributed equally. Histograms enable the CBO to determine whether certain values occur frequently, rarely or not at all, and based on this information, the CBO can select an optimum access path.</p>\r\n<p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Example (a table whose column COL1 10000 sometimes has the value 1 and only has the value 2 once):<br/><br/>CREATE TABLE HIST_EXAMPLE (COL1 NUMBER, COL2 NUMBER);<br/>BEGIN<br/>&#x00A0;&#x00A0;FOR I IN 1..10000 LOOP<br/>&#x00A0;&#x00A0;&#x00A0;&#x00A0;INSERT INTO HIST_EXAMPLE VALUES (1, I);<br/>&#x00A0;&#x00A0;END LOOP;<br/>&#x00A0;&#x00A0;INSERT INTO HIST_EXAMPLE VALUES (2, 0);<br/>END;<br/>/</p>\r\n<p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;If you now create an index for COL1 as well as statistics with histograms, the CBO may reach different decisions depending on the comparison value:<br/><br/>SELECT * FROM HIST_EXAMPLE WHERE COL1 = 1;<br/><br/>SELECT STATEMENT Optimizer=CHOOSE (Cost=5 Card=10000)<br/>&#x00A0;&#x00A0;TABLE ACCESS (FULL) OF &#39;HIST_EXAMPLE&#39; (Cost=5 Card=10000)<br/><br/>SELECT * FROM HIST_EXAMPLE WHERE COL1 = 2;<br/><br/>SELECT STATEMENT Optimizer=CHOOSE (Cost=2 Card=1 Bytes=5)<br/>&#x00A0;&#x00A0;TABLE ACCESS (BY INDEX ROWID) OF &#39;HIST_EXAMPLE&#39; (Cost=2 Card=1)<br/>&#x00A0;&#x00A0;&#x00A0;&#x00A0;INDEX (RANGE SCAN) OF &#39;HIST_EXAMPLE_1&#39; (NON-UNIQUE) (Cost=1 Card=1)</p>\r\n<p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;If &quot;COL1=1&quot;, the CBO selects a full table scan because almost all of the table entry meets the condition, and an index access is therefore not necessary. If &quot;COL1=2&quot;, the index is used because only one entry is relevant.</p>\r\n<p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Histograms therefore permit different access paths depending on the values that exist in the WHERE condition.</p>\r\n<ol>2. What are histogram buckets?</ol>\r\n<p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;With multiple column values, it is not possible to store an exact statement using value distribution in the form of histograms. Instead, the system only stores a certain maximum amount of information. This value distribution information is referred to as a bucket. A maximum of 75 buckets per column is the default value.</p>\r\n<p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;The more buckets that are used, the more exact the histogram information is. At the same time, however, this means that the memory and administration requirements are greater. Generally, 75 buckets are sufficient.</p>\r\n<p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Normal statistics without histograms are always based on exactly one bucket, and represent the lowest and highest values of the column.</p>\r\n<ol>3. What kinds of histograms are there?</ol>\r\n<p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;The following types of histograms exist:</p>\r\n<ul>\r\n<li>Value-based histograms (FREQUENCY histograms)</li>\r\n</ul>\r\n<p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;If there are fewer different values in the column than the amount of buckets used, each bucket contains one column value and the frequency of this column value.</p>\r\n<ul>\r\n<li>Height-balanced histograms (HEIGHT BALANCED histograms)</li>\r\n</ul>\r\n<p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;If there are more different values in the column than the amount of buckets used, the column values are divided into intervals of the same size. In this case, an interval is assigned to each bucket.</p>\r\n<ol>4. What limitations do histograms have?</ol>\r\n<p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Histograms have the following Oracle limitations:</p>\r\n<ul>\r\n<li>The maximum number of histogram buckets is 254.</li>\r\n</ul>\r\n<ul>\r\n<li>In the case of character columns, only the first 32 characters are included in the determination of histograms. If columns only differ in later characters, the histogram does not reflect this difference.</li>\r\n</ul>\r\n<ol>5. How can I create histograms using Oracle?</ol>\r\n<p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;You have the following options to create histograms using Oracle:</p>\r\n<ul>\r\n<li>ANALYZE TABLE</li>\r\n</ul>\r\n<p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;As part of the ANALYZE TABLE command for compiling statistics, you can specify<br/><br/>FOR ALL [INDEXED] COLUMNS [SIZE &lt;buckets&gt;]<br/>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;to create histograms (&lt;buckets&gt; &gt; 1). If youdo not specify the SIZE, 75 buckets are used. For example, with the following command, histograms are created with 75 buckets for all indexed columns of the T100 table:<br/><br/>ANALYZE TABLE T100 COMPUTE STATISTICS FOR ALL INDEXED COLUMNS;</p>\r\n<ul>\r\n<li>DBMS_STATS</li>\r\n</ul>\r\n<p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;You can also specify the METHOD_OPTS argument as part of the DBMS_STATS command GATHER_*_STATS, the contents of this argument are syntactically identical to the FOR expression of ANALYZE TABLE.<br/>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Therefore, the following is comparable with the above ANALYZE TABLE example:<br/><br/>DBMS_STATS.GATHER_TABLE_STATS(&#39;SAPR3&#39;, &#39;T100&#39;,<br/>&#x00A0;&#x00A0;METHOD_OPT=&gt;&#39;FOR ALL INDEXED COLUMNS&#39;);<br/>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Note that these commands only create additional histograms for existing statistics. A prerequisite in this case is that normal statistics must already exist. See also Note 588668.</p>\r\n<ol>6. How can I create histograms using SAP?</ol>\r\n<p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;The &quot;brconnect -F stats&quot; function enables you to create CBO statistics with and without histograms (see the BRCONNECT online documentation). To create statistics with histograms, you must explicitly use the &quot; -m EH&quot; (ESTIMATE) or &quot; -m CH&quot; (COMPUTE) option. For the continuous generation of histograms for a table, you can create an entry with AMETH=EH or AMETH=CH in the DBSTATC control table.</p>\r\n<p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;By default, BRCONNECT always creates statistics without histograms in the R/3 environment.</p>\r\n<ol>7. How can I delete histograms?</ol>\r\n<p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Up to and including Oracle 10g, you can delete histograms only indirectly by creating new statistics without histograms for the relevant table.</p>\r\n<p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;As of Oracle 11g, you can use DBMS_STATS.DELETE_COLUMN_STATS and the parameter COL_STAT_TYPE =&gt; &#39;HISTOGRAM&#39; to delete histograms.</p>\r\n<ol>8. Where is histogram information stored?</ol>\r\n<p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Histogram information is stored in objects of the Oracle data dictionaries in the SYSTEM tablespace in exactly the same way as information about normal statistics. You can access this information using DBA_TAB_HISTOGRAMS (tables) and DBA_PART_HISTOGRAMS (partitions). For each histogram bucket, there is an entry in the DBA view. The ENDPOINT_NUMBER column references the relevant bucket. For volume-based histograms, there is also another entry that defines the starting point for the first bucket.</p>\r\n<p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;The actual data is contained in the HIST_HEAD$ and HISTGRM$ tables, in which HISTGRM$ is contained in the C_OBJ#_INTCOL# Oracle cluster.</p>\r\n<ol>9. How can I determine whether histograms are created for a table?</ol>\r\n<p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;The following queries determine for which columns histograms were created and of how many buckets each of these is made up:</p>\r\n<ul>\r\n<li>Unpartitioned tables:</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>General information:<br/><br/>SELECT COLUMN_NAME, NUM_BUCKETS FROM DBA_TAB_COLUMNS WHERE<br/>&#x00A0;&#x00A0;OWNER = &#39;&lt;table_owner&gt;&#39; AND TABLE_NAME = &#39;&lt;table_name&gt;&#39;<br/>&#x00A0;&#x00A0;AND NUM_BUCKETS &gt; 1;</li>\r\n</ul>\r\n</ul>\r\n<p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Note that columns with only one distinct value only ever have one bucket, even if histograms are created. This means that the SELECT described above is not 100% correct. For Oracle 9i and lower, there is no further information about DBA or V$ views.</p>\r\n<ul>\r\n<ul>\r\n<li>As of Oracle 10g and higher, you can read the existence directly from the HISTOGRAM column:<br/>SELECT COLUMN_NAME, HISTOGRAM, NUM_BUCKETS FROM DBA_TAB_COLUMNS WHERE<br/>&#x00A0;&#x00A0;OWNER = &#39;&lt;table_owner&gt;&#39; AND TABLE_NAME = &#39;&lt;table_name&gt;&#39;;</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li>Partitioned tables:</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>General information<br/><br/>SELECT COLUMN_NAME, PARTITION_NAME, NUM_BUCKETS<br/>FROM DBA_PART_COL_STATISTICS<br/>WHERE OWNER = &#39;&lt;table_owner&gt;&#39; AND TABLE_NAME = &#39;&lt;table_name&gt;&#39;<br/>&#x00A0;&#x00A0;AND NUM_BUCKETS &gt; 1;</li>\r\n</ul>\r\n</ul>\r\n<p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Note the restriction described above for columns that have only one distinct value.</p>\r\n<ul>\r\n<ul>\r\n<li>Oracle 10g and higher:<br/>SELECT COLUMN_NAME, PARTITION_NAME, HISTOGRAM, NUM_BUCKETS<br/>FROM DBA_PART_COL_STATISTICS<br/>WHERE OWNER = &#39;&lt;table_owner&gt;&#39; AND TABLE_NAME = &#39;&lt;table_name&gt;&#39;<br/>ORDER BY 1, 2;</li>\r\n</ul>\r\n</ul>\r\n<ol>10. How can I determine for which tables histograms are created?</ol>\r\n<p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;The following statement delivers all tables on which histograms are created (due to the restrictions described above, histograms are not taken into account in columns with only one distinct value):<br/><br/>SELECT OWNER, TABLE_NAME FROM DBA_TAB_COLUMNS<br/>WHERE NUM_BUCKETS &gt; 1<br/>GROUP BY OWNER, TABLE_NAME<br/>UNION<br/>SELECT OWNER, TABLE_NAME FROM DBA_PART_COL_STATISTICS<br/>WHERE NUM_BUCKETS &gt; 1<br/>GROUP BY OWNER, TABLE_NAME;</p>\r\n<p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;As of Oracle 10g, you can also use the following query:<br/>SELECT OWNER, TABLE_NAME FROM DBA_TAB_COLUMNS<br/>WHERE  HISTOGRAM != &#39;NONE&#39;<br/>GROUP BY OWNER, TABLE_NAME<br/>UNION<br/>SELECT OWNER, TABLE_NAME FROM DBA_PART_COL_STATISTICS<br/>WHERE  HISTOGRAM != &#39;NONE&#39;<br/>GROUP BY OWNER, TABLE_NAME;</p>\r\n<ol>11. When are Oracle histograms useful?</ol>\r\n<p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;The CBO can only productively analyze histograms if the actual values for selection conditions or join conditions are known. In the R/3 environment, the actual values are replaced by bind variables by default, therefore histograms are not useful here. Histograms can only be analyzed productively under the following conditions:</p>\r\n<ul>\r\n<li>Using SUBSTITUTE Hints</li>\r\n</ul>\r\n<p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;You can use the DBI Hints SUBSTITUTE VALUES or SUBSTITUTE LITERALS to ensure that literals or all the values of an SQL statement are passed on to the database directly, without being substituted by bind variables. Histograms are useful for this. See also Note 772497.</p>\r\n<ul>\r\n<li>Database accesses without bind variables</li>\r\n</ul>\r\n<p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Some SAP components such as BW, Bank Analyzer or EP sometimes execute database accesses without bind variables. In cases where the values are transferred directly, histograms may influence the execution plan.</p>\r\n<ul>\r\n<li>Selection conditions for views</li>\r\n</ul>\r\n<p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;If you create views with selection conditions, the database receives their values directly - there is no substitution by bind variables. Histograms are useful for this.</p>\r\n<ul>\r\n<li>Bind Value Peeking active</li>\r\n</ul>\r\n<p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;As of Oracle 9i, Bind Value Peeking is active by default. This means, when you first execute an SQL statement, Oracle determines the contents of the bind variables to determine an optimal access path. Due to many problems with this feature, you must deactivate it in the SAP environment (_OPTIM_PEEK_USER_BINDS=FALSE).</p>\r\n<ol>12. Why did SAP also recommend creating histograms in other cases?</ol>\r\n<p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Up to and including Oracle 9.2.0.5, the cost accounting function of the CBO had a bug that meant that even if bind variables were used, accesses that used columns with only one distinct value could be better analyzed by a factor of 100 if histograms were created for these columns (see also Note 750631). This is why we recommended creating histograms in certain cases, in order to force the system to use certain indexes (Note 335415).</p>\r\n<p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;This bug has been corrected as of Oracle *******, so that histograms no longer have any affect on cost accounting.</p>\r\n<p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;In the BW environment, histograms are used as standard because concrete values are transferred to Oracle rather than bind variables in many situations (Note 129252).</p>\r\n<ol>13. What must I do to ensure that I do not experience performance problems as of Oracle ******* due to the corrected histogram bug?</ol>\r\n<p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;First, use the checks outlined above to determine whether you use histograms in your system. If histograms exist on non-BW tables (that is, tables with names that do not start with /BIC/ or /BI0/) and there is no plausible explanation for this (for example, deliberate use of SUBSTITUTE_VALUES or SUBSTITUTE_LITERALS hints), these histograms have normally been created to take advantage of the Oracle bug described above.</p>\r\n<p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;As of *******, you must find a different solution for these tables. To do this, proceed as follows before you upgrade to *******:</p>\r\n<ul>\r\n<li>Delete histograms in a non-production environment, and check whether performance problems occur as a result.</li>\r\n</ul>\r\n<ul>\r\n<li>If performance problems occur after you delete histograms due to long SQL statements, optimize these according to Note 766349 using hints or adjustments to the statistics. Transfer these adjustments to the live environment.</li>\r\n</ul>\r\n<ul>\r\n<li>Remove the relevant entries with CH and EH from DBSTATC because the histograms are no longer required.</li>\r\n</ul>\r\n<ul>\r\n<li>If none of the other measures work, then you can use the command<br/><br/>EVENT=&#39;38052 trace name context forever&#39;</li>\r\n</ul>\r\n<p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;as a workaround to bring about the previous behavior in the Oracle profile (see Note 592393 (7)). In addition, you must create histograms on the relevant tables again.</p>\r\n<ol>14. When are histograms useful in an R/3 environment?</ol>\r\n<p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Histograms are only useful if the actual values are known from the WHERE condition. In the R/3 environment, although binding-variables are used as standard, it is possible to allow Oracle to access the actual values used:</p>\r\n<ul>\r\n<li>You can use the DBI Hints &amp;SUBSTITUTE VALUES&amp; and &amp;SUBSTITUTE LITERALS&amp; to instruct the database interface of the R/3 System to replace bind variables with values before the SQL statement is sent to Oracle (Note 129385).</li>\r\n</ul>\r\n<ul>\r\n<li>If you use Oracle 9i, and the parameter _OPTIM_PEEK_USER_BINDS is not explicitly set to FALSE, Oracle automatically determines the contents of the bind variables during the initial parse. Due to other performance problems, however, we currently recommend that you set the parameter to FALSE (Note 755342).</li>\r\n</ul>\r\n<ul>\r\n<li>With current SAP releases, you can set the SAP parameter dbs/ora/substitute_literals to 1 to prevent bind variables from being used in connection with literals (Note 902042). In this case, histograms may be useful on all columns that are accessed with literals (among other things). However, note that the parameter also comes into effect for dynamic WHERE clauses and this may lead to unintended side-effects and problems. Therefore, you should only use this global parameter in exceptional cases and you should always consult with SAP Support (for example, by creating a message under BC-DB-ORA).</li>\r\n</ul>\r\n<p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;In such cases, the Oracle optimizer can use the histogram information productively.</p>\r\n<ol>15. Where are histograms useful in the SAP environment?</ol>\r\n<p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;During the following accesses, SUBSTITUTE hints are sometimes used and histograms may be useful for this:</p>\r\n<ul>\r\n<li>BDCP, BDCPS (Note 706836)</li>\r\n</ul>\r\n<ul>\r\n<li>COSS, COSP (Note 1312060)</li>\r\n</ul>\r\n<ul>\r\n<li>LTAP, LTBP (Note 811852)</li>\r\n</ul>\r\n<ul>\r\n<li>MSEG, MKPF (Note 902157, 902675)</li>\r\n</ul>\r\n<ul>\r\n<li>PPC_HEAD</li>\r\n</ul>\r\n<ul>\r\n<li>RSDD_TMPNM_ADM (Note 802299)</li>\r\n</ul>\r\n<ul>\r\n<li>RSSELDONE, RSREQDONE, RSTSODSREQUEST, RSSTATMANSTATUS, RSSTATMANREQMAP (see Note 1113602)</li>\r\n</ul>\r\n<ul>\r\n<li>VBUK (due to the use of selection conditions in views)</li>\r\n</ul>\r\n<ul>\r\n<li>F4 search help (Note 1008433)</li>\r\n</ul>\r\n<p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;In the BW environment, histograms are created for the following tables:</p>\r\n<ul>\r\n<li>Tables whose names begin with a prefix that is stored in the column NSPACEGEN of the table RSNSPACE, which is followed by one of the characters described in Note 428212</li>\r\n</ul>\r\n<ul>\r\n<li>Tables with the naming convention /BIC/A...00</li>\r\n</ul>\r\n<p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Additionally, histograms are sometimes used for objects of the Oracle DDIC as of Oracle 10g (in connection with the statistics creation that is described in Note 838725).</p>\r\n<ol>16. Why is ENDPOINT_ACTUAL_VALUE in DBA_TAB_HISTOGRAMS partially unfilled?</ol>\r\n<p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;The ENDPOINT_ACTUAL_VALUE contains the end points of individual histogram buckets in readable form. Therefore, it is well suited for analyses of the optimizer decisions in relation to histograms. However, this column is not always filled. DBMS_STATS statistics in particular do not often fill this column. This is not a matter of a bug, but rather the normal behavior (compare information from Oracle bug 3333781), since ENDPOINT_ACTUAL_VALUE is only intended for Oracle-internal purposes.</p>\r\n<ol>17. What problems are there regarding histograms?</ol>\r\n<p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;The following problems may occur when you use histograms:</p>\r\n<ul>\r\n<li>With Oracle Release ******* or higher, the elimination of the above histogram bug may cause performance problems for statements that were optimized when used during the previous incorrect behavior. The same problem may also occur with Oracle 9.2.0.5 or lower if Bind Value Peeking is active. (Refer to Note 690702).</li>\r\n</ul>\r\n<ul>\r\n<li>As of Oracle 9i, performance problems may occur in connection with Bind Value Peeking if you did not deactivate this using _OPTIM_PEEK_USER_BINDS=FALSE (Note 755342).</li>\r\n</ul>\r\n<ul>\r\n<li>As described in Note 176754 (32) and (33), the existence of histograms can have an effect on the cost calculation for SQL statements with bind variables.</li>\r\n</ul>\r\n<ul>\r\n<li>The C_OBJ#_INTCOL# Oracle cluster that stores histogram information may become very large. See Notes 566665 and 636670 for more information.</li>\r\n</ul>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p>.</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D030484)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON><PERSON> (I035058)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000797629/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000797629/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000797629/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000797629/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000797629/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000797629/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000797629/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000797629/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000797629/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "902042", "RefComponent": "BC-DB-ORA-SYS", "RefTitle": "DBSL: Hint and profile parameters for \"substitute literals\"", "RefUrl": "/notes/902042"}, {"RefNumber": "846308", "RefComponent": "LE-WM", "RefTitle": "Performance of SELECTs in WM using Oracle database", "RefUrl": "/notes/846308"}, {"RefNumber": "838725", "RefComponent": "BC-DB-ORA", "RefTitle": "Oracle dictionary statistics and system statistics", "RefUrl": "/notes/838725"}, {"RefNumber": "825653", "RefComponent": "BC-DB-ORA", "RefTitle": "Oracle: Popular misconceptions", "RefUrl": "/notes/825653"}, {"RefNumber": "766349", "RefComponent": "BC-DB-ORA", "RefTitle": "FAQ: Oracle SQL optimization", "RefUrl": "/notes/766349"}, {"RefNumber": "755342", "RefComponent": "BC-DB-ORA", "RefTitle": "Incorrect execution plans with bind variable peeking", "RefUrl": "/notes/755342"}, {"RefNumber": "750631", "RefComponent": "BC-DB-ORA", "RefTitle": "Rules of thumb for cost calculation of CBO", "RefUrl": "/notes/750631"}, {"RefNumber": "706132", "RefComponent": "BC-DB-ORA", "RefTitle": "Composite SAP Note: Problems with Oracle 9i", "RefUrl": "/notes/706132"}, {"RefNumber": "690702", "RefComponent": "BC-DB-ORA", "RefTitle": "Histograms cause long runtimes on Oracle 9", "RefUrl": "/notes/690702"}, {"RefNumber": "636670", "RefComponent": "BW-SYS-DB-ORA", "RefTitle": "SYSTEM Tablespace grows too much due to SYS.C_OBJ#_INTCOL#", "RefUrl": "/notes/636670"}, {"RefNumber": "618868", "RefComponent": "BC-DB-ORA", "RefTitle": "FAQ: Oracle performance", "RefUrl": "/notes/618868"}, {"RefNumber": "588668", "RefComponent": "BC-DB-ORA", "RefTitle": "FAQ: Database statistics", "RefUrl": "/notes/588668"}, {"RefNumber": "566665", "RefComponent": "BW-SYS-DB-ORA", "RefTitle": "rapid growing of cluster C_OBJ#_INTCOL#", "RefUrl": "/notes/566665"}, {"RefNumber": "539921", "RefComponent": "BC-DB-ORA", "RefTitle": "Current patch set for Oracle 9.2.0", "RefUrl": "/notes/539921"}, {"RefNumber": "335415", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/335415"}, {"RefNumber": "176754", "RefComponent": "BC-DB-ORA", "RefTitle": "Problems with CBO and RBO", "RefUrl": "/notes/176754"}, {"RefNumber": "129385", "RefComponent": "BC-DB-DBI", "RefTitle": "Database hints in Open SQL", "RefUrl": "/notes/129385"}, {"RefNumber": "129252", "RefComponent": "BW-SYS-DB-ORA", "RefTitle": "Oracle DB Statistics for BW Tables", "RefUrl": "/notes/129252"}, {"RefNumber": "122718", "RefComponent": "BC-DB-ORA", "RefTitle": "CBO: Tables with special treatment", "RefUrl": "/notes/122718"}, {"RefNumber": "1013912", "RefComponent": "BC-DB-ORA", "RefTitle": "FAQ: Oracle BW performance", "RefUrl": "/notes/1013912"}, {"RefNumber": "1008433", "RefComponent": "BC-DWB-DIC-F4", "RefTitle": "F4 help: Long runtime with Oracle V.10", "RefUrl": "/notes/1008433"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "129385", "RefComponent": "BC-DB-DBI", "RefTitle": "Database hints in Open SQL", "RefUrl": "/notes/129385 "}, {"RefNumber": "618868", "RefComponent": "BC-DB-ORA", "RefTitle": "FAQ: Oracle performance", "RefUrl": "/notes/618868 "}, {"RefNumber": "1013912", "RefComponent": "BC-DB-ORA", "RefTitle": "FAQ: Oracle BW performance", "RefUrl": "/notes/1013912 "}, {"RefNumber": "766349", "RefComponent": "BC-DB-ORA", "RefTitle": "FAQ: Oracle SQL optimization", "RefUrl": "/notes/766349 "}, {"RefNumber": "176754", "RefComponent": "BC-DB-ORA", "RefTitle": "Problems with CBO and RBO", "RefUrl": "/notes/176754 "}, {"RefNumber": "588668", "RefComponent": "BC-DB-ORA", "RefTitle": "FAQ: Database statistics", "RefUrl": "/notes/588668 "}, {"RefNumber": "122718", "RefComponent": "BC-DB-ORA", "RefTitle": "CBO: Tables with special treatment", "RefUrl": "/notes/122718 "}, {"RefNumber": "825653", "RefComponent": "BC-DB-ORA", "RefTitle": "Oracle: Popular misconceptions", "RefUrl": "/notes/825653 "}, {"RefNumber": "539921", "RefComponent": "BC-DB-ORA", "RefTitle": "Current patch set for Oracle 9.2.0", "RefUrl": "/notes/539921 "}, {"RefNumber": "838725", "RefComponent": "BC-DB-ORA", "RefTitle": "Oracle dictionary statistics and system statistics", "RefUrl": "/notes/838725 "}, {"RefNumber": "1008433", "RefComponent": "BC-DWB-DIC-F4", "RefTitle": "F4 help: Long runtime with Oracle V.10", "RefUrl": "/notes/1008433 "}, {"RefNumber": "755342", "RefComponent": "BC-DB-ORA", "RefTitle": "Incorrect execution plans with bind variable peeking", "RefUrl": "/notes/755342 "}, {"RefNumber": "750631", "RefComponent": "BC-DB-ORA", "RefTitle": "Rules of thumb for cost calculation of CBO", "RefUrl": "/notes/750631 "}, {"RefNumber": "846308", "RefComponent": "LE-WM", "RefTitle": "Performance of SELECTs in WM using Oracle database", "RefUrl": "/notes/846308 "}, {"RefNumber": "902042", "RefComponent": "BC-DB-ORA-SYS", "RefTitle": "DBSL: Hint and profile parameters for \"substitute literals\"", "RefUrl": "/notes/902042 "}, {"RefNumber": "566665", "RefComponent": "BW-SYS-DB-ORA", "RefTitle": "rapid growing of cluster C_OBJ#_INTCOL#", "RefUrl": "/notes/566665 "}, {"RefNumber": "706132", "RefComponent": "BC-DB-ORA", "RefTitle": "Composite SAP Note: Problems with Oracle 9i", "RefUrl": "/notes/706132 "}, {"RefNumber": "636670", "RefComponent": "BW-SYS-DB-ORA", "RefTitle": "SYSTEM Tablespace grows too much due to SYS.C_OBJ#_INTCOL#", "RefUrl": "/notes/636670 "}, {"RefNumber": "690702", "RefComponent": "BC-DB-ORA", "RefTitle": "Histograms cause long runtimes on Oracle 9", "RefUrl": "/notes/690702 "}, {"RefNumber": "129252", "RefComponent": "BW-SYS-DB-ORA", "RefTitle": "Oracle DB Statistics for BW Tables", "RefUrl": "/notes/129252 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": []}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}