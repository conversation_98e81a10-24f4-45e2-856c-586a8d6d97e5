{"Request": {"Number": "157499", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 301, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000014690452017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000157499?language=E&token=28CA127CAB1B0A6E5A9324415E684285"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000157499", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000157499/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "157499"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 8}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Installation information"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "2011.11.08"}, "SAPComponentKey": {"_label": "Component", "value": "BC-DB-ORA"}, "SAPComponentKeyText": {"_label": "Component", "value": "Oracle"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Basis Components", "value": "BC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Database Interface, Database Platforms", "value": "BC-DB", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-DB*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Oracle", "value": "BC-DB-ORA", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-DB-ORA*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "157499 - OPS$ connect and security aspects"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>This note concerns the use of the OPS$ mechanism to access the encrypted Oracle password of the SAP user.</p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>Security, OPS$&lt;SID&gt;ADM</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>The OPS$ mechanism of Oracle allows the execution of a connect to the database without a password if the user has already proven their authorization at operating system level using the operating system password.<br /><br />SAP uses this method to determine the password of the SAP user SAP&lt;SID&gt; (standard SAPSR3, previously SAPR3). For this, it is stored in encrypted form in the table OPS$&lt;SID&gt;ADM.SAPUSER. The password is read each time a work process or an SAP tool logs on to the database.<br /><br />Since, when the work process is started, it is unknown whether the database is available locally on the SAP application server, a connect using the OPS$ mechanism via the network (remote) must be possible. To ensure this, the Oracle initialization parameter remote_os_authent=true is set. However, as a result, an operating system user with the name &lt;sid&gt;adm from each server in the network always has the authorization to log on to the database as the database user OPS$&lt;SID&gt;ADM without a password. The rights of the database user OPS$&lt;SID&gt;ADM are therefore restricted to logging on to the database, reading the password table SAPUSER, and the SAPDBA role. In particular, this user does not have authorization to read, change, or create SAP application tables (tables of the user SAP&lt;SID&gt;).<br /></p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>To minimize the possibility of a misuse of the OPS$ connect, we recommend the following steps:<br /><br />a)<br />Isolate the SAP network from the remaining network, that is, only allow certain IP addresses to access this network from a TCP/IP point of view. Only the central instance and the application servers should be part of the network.<br /><br />b)<br />Assign a non-standard port number for Oracle NetV2:<br />To be able to log on via the network, NetV2 must first be configured on the client. In addition to the host name and the instance name, the TCP/IP port number under which the NetV2 listener listens must be known. If you select a non-standard address for this, setting up a connection is much more difficult.<br /><br />c)<br />Avoid using a second OS user with the name &lt;sid&gt;adm:<br />The connect is possible only if the user who wants to log on via the network actually has the user name &lt;sid&gt;adm in the operating system. When you then manage OS user accounts, for example, using an NIS integrated system, and the setup of local users is not allowed, there can be no other user with the same name.<br /><br />d)<br />Assign the connect authorization only for certain IP addresses:<br />As of SQLNET Version 2.3, you can enter the following parameters in protocol.ora; they either allow only certain IP addresses or exclude certain IP addresses from using the Oracle network components and therefore exclude them from the connect to the database:<br />&#x00A0;&#x00A0; tcp.validnode_checking = yes<br />&#x00A0;&#x00A0; tcp.invited_nodes&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;= (&lt;Host name or IP address&gt;, ...)<br />&#x00A0;&#x00A0; tcp.excluded_nodes&#x00A0;&#x00A0;&#x00A0;&#x00A0; = (&lt;Host name or IP address&gt;, ...)<br /><br />You must enter host names and IP addresses in a single line and separate them using commas. A separation due to a line break would cause the system to ignore the separated parameter.<br /></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON> (I800932)"}, {"Key": "Processor                                                                                           ", "Value": "D022850"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000157499/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000157499/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000157499/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000157499/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000157499/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000157499/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000157499/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000157499/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000157499/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1868094", "RefComponent": "BC-DB-ORA-SEC", "RefTitle": "Overview: Oracle Security SAP Notes", "RefUrl": "/notes/1868094"}, {"RefNumber": "1622837", "RefComponent": "BC-DB-ORA-SYS", "RefTitle": "Secure connection of AS ABAP to Oracle via SSFS", "RefUrl": "/notes/1622837"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1622837", "RefComponent": "BC-DB-ORA-SYS", "RefTitle": "Secure connection of AS ABAP to Oracle via SSFS", "RefUrl": "/notes/1622837 "}, {"RefNumber": "1868094", "RefComponent": "BC-DB-ORA-SEC", "RefTitle": "Overview: Oracle Security SAP Notes", "RefUrl": "/notes/1868094 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": []}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}