{"Request": {"Number": "156198", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 501, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": false, "url": ""}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000156198?language=E&token=838CED14C3A69B5D92A000D7F77FC8F7"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000156198", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000156198/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "156198"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 26}, "Recency": {"_label": "Recency", "key": "", "value": ""}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Advance development"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "To Be Archived"}, "ReleasedOn": {"_label": "Released On", "value": "18.05.2000"}, "SAPComponentKey": {"_label": "Component", "value": "SV-BO"}, "SAPComponentKeyText": {"_label": "Component", "value": "Backoffice Service Delivery"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Service", "value": "SV", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'SV*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Backoffice Service Delivery", "value": "SV-BO", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'SV-BO*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "156198 - SAP Services - suppl. tool transport for Euro Conversion"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>Tools required for the execution of SAP GoingLive Check for Euro Conversion are missing. System settings need to be adjusted for the<br />session.</p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>Euro Conversion, GoingLive, Euro local currency changeover<br /></p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>Some of the required tools are only available as of Release 3.1I or are not available at all.<br /></p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><b>************************************************************************</b><br /> <b>For the GoingLive Check for Euro Conversion, you need to transport the</b><br /> <b>necessary tools into both the test and production systems! ************************************************************************</b><br /> <p></p> <OL>1. Needed transports for the Euro GoingLive Check</OL> <p>Perform the steps described in Note 187939. This note will show you how  and what steps need to be taken to enable us to perform the GoingLive session.<br /></p> <OL>1. Checking if the needed tools are implemented</OL> <p>Since the following notes and transports are not needed for the  GoingLive Check for Euro Conversion, you can disregard these messages during the run of the RTCCTOOL:</p> <b>Any message for Note 69455</b><br /> <p>However, if you do include this transport in the workflow, it will not cause any problems for the GoingLive session. </p> <OL>1. Updating the collector information in the test system</OL> <p>To ensure an optimal run of the Going Live Analysis Session, following  programs must be run on the production system, so that the correct  information will be retrieved from the system. This is necessary as  the test system is only a copy of the production system, and the values we have gathered through our collectors are for the old system.<BR/> <BR/> These programs need to be run in the following sequence:<BR/> 1) On each instance, you must start program RSSTAT98.<BR/> 2) On one instance, start program RSSTAT60.<BR/> 3) On every instance, start program RSSTATPH followed by program &#x00A0;&#x00A0; RSHOSTPH.<BR/> 4) On one instance, start program RSORAPAR.<BR/> <BR/> Important note regarding this procedure:<BR/> 1) You can implement the above programs manually --- the total runtime is about 10 min. (on a central instance),<br />OR 2) You can plan everything using table TCOLL. In this case, you need to plan every step (program) for a different hour in the sequence mentioned above. Refer to SAP Note 12103 for more information.<BR/> <br />By implementing these programs in the correct sequence, you ensure that the correct paramerters are available for the system.<br /><br /></p> <OL>1. Correcting the SAProuter settings Since the SAProuter settings in the test system reflect the configuration in your production system, it is necessary for you to set them in the following way:<BR/> <BR/> 1) Call transaction OSS1.<BR/> 2) Choose Parameters --&gt; Technical settings --&gt; SAProuter entry.<BR/> 3) Set the following parameters so that the test system will access the correct SAP Router:<BR/> <BR/> The following parameters need to be set to reflect the test system.<BR/> SAPRouter<BR/> SAPRouter IP-Adresse<BR/> SAPRouter Debitor<BR/> SAPRouter Instanznr.</OL> <p> Please remember that in this particular GoingLive session, only a very specific subset of tools is needed. If you plan to have additional GoingLive or Early Watch sessions performed for this particlar system, you should consider implementing all the suggested notes!<br /> Change history:<BR/> &#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;o Version 1.0<BR/> &#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; first version.<BR/> &#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;o Version 1.1<BR/> &#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; modifications regarding the RTCCTOOL, and version support.<BR/> &#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;o Version 1.2<BR/> &#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; modifications regarding the run of the collectors.</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "Offer <PERSON> (D022497)"}, {"Key": "Processor                                                                                           ", "Value": ""}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000156198/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000156198/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000156198/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000156198/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000156198/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000156198/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000156198/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000156198/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000156198/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "207901", "RefComponent": "CA-EUR-CNV", "RefTitle": "Going Live check (remote services) for Euroconversion", "RefUrl": "/notes/207901"}, {"RefNumber": "187939", "RefComponent": "SV-SMG-SDD", "RefTitle": "SAP Servicetools Update (RTCCTOOL)", "RefUrl": "/notes/187939"}, {"RefNumber": "171271", "RefComponent": "CA-EUR-CNV", "RefTitle": "Read check Euro: runtime forecast", "RefUrl": "/notes/171271"}, {"RefNumber": "12103", "RefComponent": "BC-CCM-MON-TUN", "RefTitle": "Contents of table TCOLL", "RefUrl": "/notes/12103"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "12103", "RefComponent": "BC-CCM-MON-TUN", "RefTitle": "Contents of table TCOLL", "RefUrl": "/notes/12103 "}, {"RefNumber": "207901", "RefComponent": "CA-EUR-CNV", "RefTitle": "Going Live check (remote services) for Euroconversion", "RefUrl": "/notes/207901 "}, {"RefNumber": "187939", "RefComponent": "SV-SMG-SDD", "RefTitle": "SAP Servicetools Update (RTCCTOOL)", "RefUrl": "/notes/187939 "}, {"RefNumber": "171271", "RefComponent": "CA-EUR-CNV", "RefTitle": "Read check Euro: runtime forecast", "RefUrl": "/notes/171271 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_APPL", "From": "31I", "To": "31I", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "40B", "To": "40B", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "45A", "To": "45B", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}