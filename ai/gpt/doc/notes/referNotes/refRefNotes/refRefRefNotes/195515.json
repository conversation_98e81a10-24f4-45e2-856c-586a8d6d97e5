{"Request": {"Number": "195515", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 497, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000014766992017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/**********?language=E&token=FDA8C68A05990C3D02142E57DA68B655"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/**********", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/**********/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "195515"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 40}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Installation information"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "23.11.2009"}, "SAPComponentKey": {"_label": "Component", "value": "FI-GL-GL-X"}, "SAPComponentKeyText": {"_label": "Component", "value": "Database Inconsistencies"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Financial Accounting", "value": "FI", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'FI*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "General Ledger Accounting", "value": "FI-GL", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'FI-GL*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Basic Functions", "value": "FI-GL-GL", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'FI-GL-GL*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Database Inconsistencies", "value": "FI-GL-GL-X", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'FI-GL-GL-X*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "195515 - Installing ZFINDEX"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>This note concerns the installation of the FI analysis report ZFINDEX from SAPSERV#.<br /><br />***********************************************************************<br />+<br />* NOTE THE FOLLOWING:<br />* YOU ONLY HAVE TO INSTALL THE REPORT ZFINDEX FOR RELEASES LOWER THAN 4.6B.<br />* AS OF RELEASE 4.6C (SAPKH46C29), THE REPORT IS CONTAINED UNDER THE NAME \"RFINDEX\" * IN THE STANDARD SAP SYSTEM.<br />*<br />***********************************************************************<br /></p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>ZFINDEX, FIINCON, RFINDEX<br /></p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>Within message processing, you have to install the report ZFINDEX in your system.<br /><br />************************************************************************<br />* Note that the report ZFINDEX is not suitable for regular reconciliations<br />* due to its complexity.<br />* Only start the report ZFINDEX after consultation with SAP for problem analysis.<br />************************************************************************<br /></p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>The source code is available as an advance correction on SAPSERV# (for more information about installation, see Note 13719).<br /><br />The directories are:<br />Release&#x00A0;&#x00A0;&#x00A0;&#x00A0;Path</p> <UL><LI> As of 4.5&#x00A0;&#x00A0; ~general/R3server/abap/note.0195515/rel45b</LI></UL> <UL><LI>3.1-4.0&#x00A0;&#x00A0;&#x00A0;&#x00A0;~general/R3server/abap/note.0195515/rel40b</LI></UL> <UL><LI>&#x00A0;&#x00A0;&#x00A0;&#x00A0;3.0&#x00A0;&#x00A0;&#x00A0;&#x00A0;~general/R3server/abap/note.0195515/rel30f</LI></UL> <p><br />The report contains English text symbols and selection texts, but is currently only available in the logon language German. If you want to transfer the texts in another language, translate the texts using transaction SE63:</p> <UL><UL><LI>'Translation -&gt; Short texts -&gt; Program texts': ZFINDEX</LI></UL></UL> <UL><UL><LI>Source language: DE</LI></UL></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Target language: EN</p> <UL><UL><LI>Choose 'Edit'.</LI></UL></UL> <UL><UL><LI>In the follow-on menu: Choose 'Edit -&gt; Copy source text -&gt; All lines'</LI></UL></UL> <p><br />------------------------------------------------------------------------<br />Overview of functions<br />(You can find additional instructions for SAP Support in Note 206581.)<br /></p> <OL>1. Reconciliation of the document tables against the index tables</OL> <OL>2. Reconciliation of the index tables against the document tables</OL> <OL>3. Search for missing document headers</OL> <OL>4. Reconciliation of document headers against line items</OL> <OL>5. Check for clearing transactions</OL> <OL>6. Reconciliation of documents against transaction figures (for example, SAPF070)</OL> <OL>7. Reconciliation of indexes against transaction figures (for example, SAPF190)</OL> <OL>8. Open item as at the key date: Period start (for example, RF$OPO00)</OL> <OL>9. Finding double entries in the index tables</OL> <OL>10. Zero balance check of financial statements and account master record check</OL> <p><br />Description of functions:<br />The report consists of nine parts that can be activated by indicators on the 'Reconciliation' selection screen.<br /></p> <b>(1) The 'Documents versus Indexes' indicator</b><br /> <p><br />Effect: This indicator searches for missing and incorrect entries in the index tables (BSIS, BSAS, BSID, BSAD, BSIK and BSAK).<br /><br />How is the check carried out?: The system searches for a corresponding index table entry for every BSEG row. If only one of the index key fields (exception 'ZUONR') is inconsistent with the corresponding BSEG fields, the system does not find an existing index (for example, AUGBL changed for a BSEG entry only and not in the index).<br /><br />Example:<br />After you run SAPF190, you notice the following situation : In the SAPF070 part (Reconcile Documents and Account Transaction Figures), the system does not find any differences between documents and transaction figures for an account. In the SAPF190 part (Financial Accounting Comparative Analysis) of the affected account the amounts of the indexes are smaller than the transaction figures in the debit or credit. Reason: Index entries are missing for BSEG entries.<br /><br />Performance:<br />Refer to (4). In addition, the restriction to certain accounts ('Account Selection' selection screen) results in a performance gain: The indicators 'Check G/L Account', 'Check Customers' and 'Check Vendors' activate the checks for the corresponding account types and the select options 'G/L Accounts', 'Customers' and 'Vendors' allow a restriction to certain account numbers. A further restriction can be achieved by setting 'All Items', 'Open Items' (line items and open items) and 'Cleared items' (cleared items only).<br /><br /></p> <b>(2) The 'Indexes versus documents' indicator is set:</b><br /> <p><br />Effect: Entries in the index tables are checked against the entries in the table BSEG (is there a consistent BSEG entry there for an index?)<br /><br />How is the check carried out?<br />All index key fields must agree with the corresponding BSEG entries. The system also checks important non-key fields. In addition, the checks described under (4) are executed.<br /><br />Example:<br />After an SAPF190 run, you notice the following situation: In the SAPF070 part (Reconcile Documents and Account Transaction Figures), the system does not find any differences between documents and transaction figures for an account. In the SAPF190 part (Financial Accounting Comparative Analysis) of the affected account, the amounts of the indexes are larger than the transaction figures, in the debit or credit. Reason: There are duplicate index entries for BSEG entries.<br /><br />Performance:<br />The indicators 'Check G/L Account', 'Check Customers' and 'Check Vendors' activate the checks for the corresponding account types, and the select options 'G/L Accounts', 'Customers' and 'Vendors' allow a restriction to certain account numbers, and are as essential for the performance as the specification of company code, fiscal year and period. A further performance improvement can be achieved by setting 'All Items', 'Open Items' (line items and open items) and 'Cleared Items' (cleared items only) or by specifying document numbers.<br /></p> <b>(3) The 'Find Missing Header' indicator</b><br /> <p><br />Effect: The indicator searches for missing document headers.<br /><br />How is the check carried out? There must be one document header per entry in the cluster table RFBLG, otherwise, the system generates an error. In addition, the system carries out a consistency check for the cluster (cluster size, initial cluster).<br /><br />Performance:<br />The specification of company Code, fiscal year and document number are essential for the performance.<br /></p> <b>(4) The 'Find Missing Bseg' indicator</b><br /> <b></b><br /> <p>Effect: The indicator searches for missing line items.<br /><br />How is the check carried out?<br />Line items must exist for every entry in the document header table BKPF, otherwise, the system issues an error message. In addition, the system carries out a zero balance check of the document and various consistency checks in the document itself (month/posting date, posting date/clearing date, account type posting key). In addition, the system examines the account master record tables (SKB1, KNB1 and LFB1). These checks are also processed implicitly for (1). If you start the report 'online', a drill-down option exists for document display.<br /><br />Performance:<br />The specification of company code, (fiscal year and period or posting date) and document number is essential for the performance.<br /></p> <b>(5) The 'Check Clearings' indicator</b><br /> <b></b><br /> <p>Effect: The system carries out a zero balance check for clearing transactions.<br /><br />How is the check carried out?<br />For every clearing transaction, the system checks in the index tables (BSAS, BSAD and BSAK), whether a clearing transaction for important account assignment objects balances to zero. The account assignment objects that are checked are: HKONT, (UMSKS, UMSKZ), AUGDT, AUGBL, GSBER and PSWSL. If you start the report 'online', a drill-down option exists to select index table entries that cause an incorrect clearing. From this level you can, in turn, branch to the document display.<br />When you set the additional indicator 'Check Bseg', the system carries out an additional check of AUGDT and AUGBL in the index tables with the relevant BSEG fields.<br /><br />Performance:<br />The indicators 'Check G/L Account', 'Check Customers' and 'Check Vendors' activate the checks for the relevant account types. The select options 'G/L Accounts', 'Customers' and 'Vendors' allow a restriction to certain account numbers, and are essential for the perfomance as is the specification of the company code, clearing document number and clearing date. The specification of a fiscal year is also ignored, as is the specification of document numbers. However, you can restrict the check to business areas and account currency.<br /></p> <b>(6) The 'Docs/Transaction Figures' indicator</b><br /> <b></b><br /> <p>Effect:<br />The indicator reconciles documents against transaction figures. How is the check carried out? The system reconciles the documents against the transaction figures, as is the case in the report SAPF070. Special general ledger transaction figures can also be reconciled, but only if there is no restriction to individual posting periods.<br /><br />Performance:<br />The restriction to company code, fiscal year and posting period<br />is crucial for the performance.<br /></p> <b>(7) The 'Indexes/Txn. Figs' indicator</b><br /> <p><br />Effect: The indicator reconciles indexes against transaction figures.<br /><br />How is the check carried out?<br />The system reconciles the indexes against the transaction figures as carried out by the report SAPF190. In addition, special general ledger transaction figures can also be reconciled, but only if there is no restriction to individual posting periods. The system checks debit and credit only for customer and vendors. The system does not check sales for performance reasons.<br /><br />Performance:<br />The indicators 'Check G/L Account', 'Check Customers' and 'Check Vendors' activate the checks for the corresponding account types, and the select options 'G/L Accounts', 'Customers' and 'Vendors' allow a restriction to certain account numbers and are as essential for the performance as the specification of company code, fiscal year and period.<br /></p> <b>(8) The 'Sum of Open Items' indicator</b><br /> <p><br />Effect:<br />The indicator reconciles the total of the open items of an account with the balance of the transaction figures.<br /><br />How is the check carried out?<br />Key date: If you only specify the fiscal year as the key date: the system checks the balance carried forward for the specified year. If another posting period is also specified in the period, then the key date (inclusive) is: The last day of the preceding period. You must specify a fiscal year to execute the check. It is not absolutely necessary to specify a period.<br />For G/L accounts: All G/L accounts with line/open item management can be checked, apart from profit and loss accounts and profit and loss accounts for balance carried forward. Customers and vendors can all be checked. If the check runs across all customers or vendors, then the reconcilliation account is still also checked against the balance. Special G/L transactions can only then be checked if no period is specified. The check logic corresponds to a reconciliation of the results of the report RF$OPO00 with RF$SLD00.<br /><br />Performance:<br />The indicators 'Check G/L Account', 'Check Customers' and 'Check Vendors' activate the check for the corresponding account types. The select options 'G/L Accounts', 'Customers' and 'Vendors' allow a restriction to certain account numbers and are essential for the performance as is the specification of a company code.<br /></p> <b>(9) The 'Find duplicate Index' indicator</b><br /> <p>Effect: The indicator finds duplicate entries in the index tables<br />(BSIS, BSAS, BSID, BSAD, BSIK and BSAK) for a line item.<br /><br />How is the check carried out?<br />The check logic finds duplicate entries in an index table (for example, two index entries with different assignment numbers for one line item) and also searches for duplicate entries in two different tables BSI$/BSA$, $= S, D or K (for example, 1 line item is found as an open item in the table BSIS, and at the same time as a cleared Personal Identification Number of the table BSAS. The following criterion is used:<br />Field equality index table fields: BUKRS, (HKONT/KUNNR/LIFNR), BELNR, GJAHR, BUZEI.<br /><br />Performance:<br />The indicators 'Check G/L Account', 'Check Customers' and 'Check Vendors' activate the checks for the corresponding account types, and the select options 'G/L Accounts', 'Customers' and 'Vendors' allow a restriction to certain account numbers, and are essential for the performance as is the specification of a company code.<br /></p> <b>(10) The 'Check Balance' indicator:</b><br /> <p>Effect: The indicator totals the balances (GLT0, LFC1, KNC1, LFC3, KNC3) and checks the consistency of the balances with the master record settings.<br /><br />How is the check carried out?<br />The balances of the selected period for the corresponding account type are totalled.&#x00A0;&#x00A0;If, for example, you select all G/L accounts, you can use this option to check whether the balance sheet balances to zero.<br />Furthermore, the master records of the accounts are checked. Possible error messages are:</p> <UL><UL><LI>XSALH=X_&amp;_RTCUR=&lt;CURR&gt;<br />The indicator 'Only balances in local crcy' is activated in the master record of the account. However, balances exist in a currency &lt;CURR&gt; that is not the local currency.</LI></UL></UL> <UL><UL><LI>MISSING_MASTER_DATA_FOR_GLT0_RACCT<br />The master record data of the G/L account (tables SKA1, SKB1) is incomplete.</LI></UL></UL> <UL><UL><LI>MISSING_MASTER_DATA_FOR_KNC1_KUNNR / MISSING_MASTER_DATA_FOR_KNC3_KUNNR<br />The master record data of a customer (tables KNA1, KNB1) is incomplete.</LI></UL></UL> <UL><UL><LI>MISSING_MASTER_DATA_FOR_LFC1_KUNNR / MISSING_MASTER_DATA_FOR_LFC3_KUNNR<br />The master record data of a vendor (tables LFA1, LFB1) is incomplete.</LI></UL></UL> <p><br />Performance:<br />The indicators 'Check G/L Account', 'Check Customers' and 'Check Vendors' activate the checks for the corresponding account types, and the select options 'G/L Accounts', 'Customers' und 'Vendors' allow you to make a restriction to certain account numbers, and are essential for the performance as is the specification of a company code.<br /></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "CA-EUR-CNV-FI (Financial Accounting)"}, {"Key": "Responsible                                                                                         ", "Value": "D027488"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON> (D033001)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "J", "TranslationLanguage": "日本語", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/**********/J"}, {"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/**********/D"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "89135", "RefComponent": "FI", "RefTitle": "Posting with invalid combination of posting key and UMSKZ", "RefUrl": "/notes/89135"}, {"RefNumber": "86067", "RefComponent": "FI-GL-GL-X", "RefTitle": "SAPF190: Differences INDIZES - BALANCES", "RefUrl": "/notes/86067"}, {"RefNumber": "810757", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/810757"}, {"RefNumber": "744139", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/744139"}, {"RefNumber": "653137", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/653137"}, {"RefNumber": "645361", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/645361"}, {"RefNumber": "631458", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/631458"}, {"RefNumber": "483647", "RefComponent": "FI-GL-GL-X", "RefTitle": "Missing clearing information in table BSEG", "RefUrl": "/notes/483647"}, {"RefNumber": "40024", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/40024"}, {"RefNumber": "397761", "RefComponent": "FI-AA-SVA", "RefTitle": "Correction documents, depreciation areas, direct FI posting", "RefUrl": "/notes/397761"}, {"RefNumber": "396808", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/396808"}, {"RefNumber": "393821", "RefComponent": "FI-AP-AP-A", "RefTitle": "FZ326 in the payment program: Document balance not zero", "RefUrl": "/notes/393821"}, {"RefNumber": "378669", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/378669"}, {"RefNumber": "358905", "RefComponent": "CA-EUR-CNV-FI", "RefTitle": "Difference between total of open items and balance", "RefUrl": "/notes/358905"}, {"RefNumber": "352049", "RefComponent": "PSM", "RefTitle": "Payment program: Dump SAPSQL_ARRAY_INSERT_DUPREC", "RefUrl": "/notes/352049"}, {"RefNumber": "206581", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/206581"}, {"RefNumber": "200226", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/200226"}, {"RefNumber": "148612", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/148612"}, {"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719"}, {"RefNumber": "114566", "RefComponent": "CA-EUR-CNV-FI", "RefTitle": "EMU: MQ530, MQ519 sec. index entries are missing", "RefUrl": "/notes/114566"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "744139", "RefComponent": "FI-GL-GL-X", "RefTitle": "FI_DOCUMNT-XARCH not set-Index does not get deleted.", "RefUrl": "/notes/744139 "}, {"RefNumber": "358905", "RefComponent": "CA-EUR-CNV-FI", "RefTitle": "Difference between total of open items and balance", "RefUrl": "/notes/358905 "}, {"RefNumber": "396808", "RefComponent": "CA-EUR-CNV-FI", "RefTitle": "Opening old posting periods after euro changeover", "RefUrl": "/notes/396808 "}, {"RefNumber": "810757", "RefComponent": "FI-GL-GL-X", "RefTitle": "FAQ FI-GL-GL-X Data Consistency Check Composite Note", "RefUrl": "/notes/810757 "}, {"RefNumber": "378669", "RefComponent": "FI-GL-GL-A", "RefTitle": "Dealing with errors from Note 323813", "RefUrl": "/notes/378669 "}, {"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719 "}, {"RefNumber": "645361", "RefComponent": "FI-GL-GL-X", "RefTitle": "FI INCON.Steps to follow before forwarding message", "RefUrl": "/notes/645361 "}, {"RefNumber": "352049", "RefComponent": "PSM", "RefTitle": "Payment program: Dump SAPSQL_ARRAY_INSERT_DUPREC", "RefUrl": "/notes/352049 "}, {"RefNumber": "483647", "RefComponent": "FI-GL-GL-X", "RefTitle": "Missing clearing information in table BSEG", "RefUrl": "/notes/483647 "}, {"RefNumber": "206581", "RefComponent": "FI-GL-GL-X", "RefTitle": "Function description ZFINDEX", "RefUrl": "/notes/206581 "}, {"RefNumber": "86067", "RefComponent": "FI-GL-GL-X", "RefTitle": "SAPF190: Differences INDIZES - BALANCES", "RefUrl": "/notes/86067 "}, {"RefNumber": "393821", "RefComponent": "FI-AP-AP-A", "RefTitle": "FZ326 in the payment program: Document balance not zero", "RefUrl": "/notes/393821 "}, {"RefNumber": "397761", "RefComponent": "FI-AA-SVA", "RefTitle": "Correction documents, depreciation areas, direct FI posting", "RefUrl": "/notes/397761 "}, {"RefNumber": "89135", "RefComponent": "FI", "RefTitle": "Posting with invalid combination of posting key and UMSKZ", "RefUrl": "/notes/89135 "}, {"RefNumber": "114566", "RefComponent": "CA-EUR-CNV-FI", "RefTitle": "EMU: MQ530, MQ519 sec. index entries are missing", "RefUrl": "/notes/114566 "}, {"RefNumber": "148612", "RefComponent": "CA-EUR-CNV-FI", "RefTitle": "|Incons.clearings on reconc.a/cts;MQ519 in RFEWUC1O", "RefUrl": "/notes/148612 "}, {"RefNumber": "200226", "RefComponent": "FI", "RefTitle": "Correction FI documents from billing document", "RefUrl": "/notes/200226 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_APPL", "From": "30C", "To": "30F", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "31G", "To": "31I", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "40A", "To": "40B", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "45A", "To": "45B", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "46A", "To": "46B", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "46C", "To": "46C", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "500", "To": "500", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "SAP_APPL 31I", "SupportPackage": "SAPKH31I84", "URL": "/supportpackage/SAPKH31I84"}, {"SoftwareComponentVersion": "SAP_HR 31I", "SupportPackage": "SAPKE31I84", "URL": "/supportpackage/SAPKE31I84"}, {"SoftwareComponentVersion": "SAP_APPL 40B", "SupportPackage": "SAPKH40B65", "URL": "/supportpackage/SAPKH40B65"}, {"SoftwareComponentVersion": "SAP_APPL 45B", "SupportPackage": "SAPKH45B44", "URL": "/supportpackage/SAPKH45B44"}, {"SoftwareComponentVersion": "SAP_APPL 46B", "SupportPackage": "SAPKH46B32", "URL": "/supportpackage/SAPKH46B32"}, {"SoftwareComponentVersion": "SAP_APPL 46C", "SupportPackage": "SAPKH46C29", "URL": "/supportpackage/SAPKH46C29"}, {"SoftwareComponentVersion": "SAP_APPL 500", "SupportPackage": "SAPKH50005", "URL": "/supportpackage/SAPKH50005"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}