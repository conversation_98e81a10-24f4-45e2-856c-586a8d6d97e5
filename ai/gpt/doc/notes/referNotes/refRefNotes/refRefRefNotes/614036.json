{"Request": {"Number": "614036", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 362, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000015438382017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000614036?language=E&token=E9BA50B0CF08C244EAC629A7725D3B06"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000614036", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000614036/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "614036"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 9}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Help for error analysis"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "05.12.2021"}, "SAPComponentKey": {"_label": "Component", "value": "BC-DB-ORA"}, "SAPComponentKeyText": {"_label": "Component", "value": "Oracle"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Basis Components", "value": "BC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Database Interface, Database Platforms", "value": "BC-DB", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-DB*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Oracle", "value": "BC-DB-ORA", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-DB-ORA*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "614036 - Composite SAP Note: ORA-12631 / ORA-12638"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>ORA-12631: Username retrieval failed ORA-12638: Credential retrieval failed<br /> A follow-on error can be the following:<br /> ORA-03113: end-of-file on communication channel<br />This error currently affects only WINDOWS platforms in the SAP environment.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>TNS-12631 TNS-12638 12631 12638</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>Oracle uses certain authentication methods depending on the sqlnet.ora parameter SQLNET.AUTHENTICATION_SERVICES. In the SAP environment, this parameter is set to \"(NTS)\" (native authentication) on Windows, which means that depending on the operating system and<br />Oracle release, the following authentication methods are activated:</p>\r\n<ul>\r\n<li>NT, Oracle &lt;= 8.0:<br /><br />Authentication took place using named pipes and the NT LAN Manager (NTLM).</li>\r\n</ul>\r\n<ul>\r\n<li>NT, Oracle &gt;= 8.1:<br /><br />Authentication takes place using the Security Service Provider interface and the NT LAN Manager (NTLM). The service \"NT LM Security Support Provider\" is required for this.</li>\r\n</ul>\r\n<ul>\r\n<li>W2K:<br /><br />Authentication takes place using KERBEROS. The client contacts the KERBEROS Key Distribution Center (KDC) on the W2K domain controller for this. However, NTLM is used in the following cases:</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>The system does not belong to a W2K domain</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>A local WINDOWS user is used for the logon.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>The connection to the domain controller is temporarily unavailable (for example, network problems, high network load).</li>\r\n</ul>\r\n</ul>\r\n<p>The following causes are possible for ORA-12631/ORA-12638:</p>\r\n<ol>1. W2K: Problem accessing domain controller</ol><ol>2. W2K: Large time difference between client and domain controller</ol><ol>3. W2K: Use of unqualified host names</ol><ol>4. W2K: Oracle service started using local user</ol><ol>5. W2K: Cached domain entries</ol><ol>6. \"NT LM Security Support Provider\" service not started</ol><ol>7. W2K, Oracle 8.1.6: Oracle bug</ol><ol>8. Oracle &gt;= 19.10: Changed NT LM default</ol>\r\n<p>If the problem cannot be solved, you can use Oracle Net traces (client and server traces) to determine more detailed information about the error situation in accordance with SAP Note 562403. See also SAP Note 620540.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<ol>1. If the error occurs sporadically on W2K, there might be problems accessing the domain controller. You should therefore check whether there are network problems between the Oracle server and the domain controller. In addition, you must ensure that a domain controller is available at all times.</ol><ol>2. Make sure that the system time on the domain controller differs by less than five minutes from the system time on the machine with the Oracle database.</ol><ol><ol>3. Make sure that the local host name in</ol></ol><ol><ol>&lt;drive&gt;:\\winnt\\system32\\drivers\\etc\\hosts</ol></ol><ol>is also specified with a fully qualified domain.</ol><ol>4. If the Oracle service is started using a local user other than SYSTEM, the authentication of a domain user fails with one of the above messages. Normally, the OracleService&lt;sid&gt; should be started using the local SYSTEM account, and the error then does not occur. The use of a domain user to start the Oracle service would also correct the error. However, the use of the SYSTEM user is to be preferred.</ol><ol>5. If the error occurs upon the failure of one domain controller even though other functioning domain controls are available, the deactivation of the domain cache in accordance with SAP Note 595874 can help. Otherwise, among other things, the IsAlive poll can fail in Oracle FailSafe environments due to ORA-12638, and this can trigger a switch of the Oracle cluster resource.</ol><ol>6. On NT and in the conditions mentioned above on W2K, too, the authentication is performed via NTLM. In these cases, refer to SAP Note 608280 and start the \"NT LM Security Support Provider\" service.</ol><ol>7. See SAP Note 425828 and install the specified bug fix.</ol><ol>8. As of Oracle 19.10, the default for NO_NTLM has been changed (FALSE -&gt; TRUE), leading to terminations with ORA-12638 if authentication still runs using the NT LAN Manager (NT LM). The following parameter can be set in sqlnet.ora (at least on client side) to restore the original state:</ol><ol>SQLNET.NO_NTLM = FALSE</ol></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D030484)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D030484)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000614036/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000614036/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000614036/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000614036/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000614036/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000614036/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000614036/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000614036/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000614036/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "620540", "RefComponent": "BC-DB-ORA", "RefTitle": "Authentication Troubleshooting Guide", "RefUrl": "/notes/620540"}, {"RefNumber": "608280", "RefComponent": "BC-DB-ORA", "RefTitle": "ora-12638 when starting svrmgrl", "RefUrl": "/notes/608280"}, {"RefNumber": "595874", "RefComponent": "BC-DB-ORA", "RefTitle": "Failure of domain controller causes Oracle failover", "RefUrl": "/notes/595874"}, {"RefNumber": "480266", "RefComponent": "BC-DB-ORA", "RefTitle": "Problems with SYSDBA/SYSOPER/INTERNAL connect", "RefUrl": "/notes/480266"}, {"RefNumber": "425828", "RefComponent": "BC-DB-ORA", "RefTitle": "ORA-12638 credential retrieval failed (Windows 2000)", "RefUrl": "/notes/425828"}, {"RefNumber": "370278", "RefComponent": "BC-DB-ORA", "RefTitle": "ORA-3113 when starting SQLPLUS or SVRMGRL", "RefUrl": "/notes/370278"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "592393", "RefComponent": "BC-DB-ORA", "RefTitle": "FAQ: Oracle", "RefUrl": "/notes/592393 "}, {"RefNumber": "480266", "RefComponent": "BC-DB-ORA", "RefTitle": "Problems with SYSDBA/SYSOPER/INTERNAL connect", "RefUrl": "/notes/480266 "}, {"RefNumber": "620540", "RefComponent": "BC-DB-ORA", "RefTitle": "Authentication Troubleshooting Guide", "RefUrl": "/notes/620540 "}, {"RefNumber": "370278", "RefComponent": "BC-DB-ORA", "RefTitle": "ORA-3113 when starting SQLPLUS or SVRMGRL", "RefUrl": "/notes/370278 "}, {"RefNumber": "595874", "RefComponent": "BC-DB-ORA", "RefTitle": "Failure of domain controller causes Oracle failover", "RefUrl": "/notes/595874 "}, {"RefNumber": "608280", "RefComponent": "BC-DB-ORA", "RefTitle": "ora-12638 when starting svrmgrl", "RefUrl": "/notes/608280 "}, {"RefNumber": "425828", "RefComponent": "BC-DB-ORA", "RefTitle": "ORA-12638 credential retrieval failed (Windows 2000)", "RefUrl": "/notes/425828 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": []}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}