{"Request": {"Number": "551542", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 258, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000015282122017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000551542?language=E&token=E2918C3AA9C240D2C00CAB8C412A7518"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000551542", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000551542/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "551542"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 9}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "FAQ"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "18.10.2022"}, "SAPComponentKey": {"_label": "Component", "value": "BC-DB-ORA"}, "SAPComponentKeyText": {"_label": "Component", "value": "Oracle"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Basis Components", "value": "BC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Database Interface, Database Platforms", "value": "BC-DB", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-DB*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Oracle", "value": "BC-DB-ORA", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-DB-ORA*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "551542 - FAQ: Oracle software installation, upgrade, migration links up to Oracle 10.2"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>This SAP Note provides information and answers questions about the following topics:<br />Oracle Installation<br />Oracle Migration<br />Oracle Patchset Installation<br />Oracle Patch Installation<br />Oracle Upgrade<br />Oracle Downgrade<br />Oracle Software<br />Oracle Software Relinking</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>odma,<br />opatch<br />runInstaller,<br />orainst<br />relinking<br />upgrade<br />downgrade</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<ol><ol><ol>1.&#x00A0;</ol></ol></ol>\r\n<p><strong>Which tool do I use to install Oracle?</strong></p>\r\n<ol><ol><ol>2.&#x00A0;</ol></ol></ol>\r\n<p><strong>Can I also install the Oracle software by copying from another system?</strong></p>\r\n<ol><ol><ol>3.&#x00A0;</ol></ol></ol>\r\n<p><strong>Which options do I select in the installer and where do I find documentation about the installation?</strong></p>\r\n<ol><ol><ol>4.&#x00A0;</ol></ol></ol>\r\n<p><strong>What can I do if my runInstaller does not start?</strong></p>\r\n<ol><ol><ol>5.&#x00A0;</ol></ol></ol>\r\n<p><strong>Where do I find the log files for the installation process?</strong></p>\r\n<ol><ol><ol>6.&#x00A0;</ol></ol></ol>\r\n<p><strong>What do I have to delete to start the installation from scratch?</strong></p>\r\n<ol><ol><ol>7.&#x00A0;</ol></ol></ol>\r\n<p><strong>Where does Oracle store information about software that has already been installed?</strong></p>\r\n<ol><ol><ol>8.&#x00A0;</ol></ol></ol>\r\n<p><strong>What can I do if problems occur during the installation?</strong></p>\r\n<ol><ol><ol>9.&#x00A0;</ol></ol></ol>\r\n<p><strong>Which migration paths are supported by SAP?</strong></p>\r\n<ol><ol><ol>10.&#x00A0;</ol></ol></ol>\r\n<p><strong>Which tool do I use to upgrade/migrate Oracle?</strong></p>\r\n<ol><ol><ol>11.&#x00A0;</ol></ol></ol>\r\n<p><strong>How should Oracle recognize my source database release?</strong></p>\r\n<ol><ol><ol>12.&#x00A0;</ol></ol></ol>\r\n<p><strong>Where can I find log files for the migration?</strong></p>\r\n<ol><ol><ol>13.&#x00A0;</ol></ol></ol>\r\n<p><strong>Can I manually carry out the migration if problems occur with the tools?</strong></p>\r\n<ol><ol><ol>14.&#x00A0;</ol></ol></ol>\r\n<p><strong>What is the significance of the init&lt;SID&gt;.ora parameter 'compatible'?</strong></p>\r\n<ol><ol><ol>15.&#x00A0;</ol></ol></ol>\r\n<p><strong>Can I downgrade to my old Oracle release?</strong></p>\r\n<ol><ol><ol>16.&#x00A0;</ol></ol></ol>\r\n<p><strong>Can I delete the old Oracle software?</strong></p>\r\n<ol><ol><ol>17.&#x00A0;</ol></ol></ol>\r\n<p><strong>What are the requirements for installing a patch set?</strong></p>\r\n<ol><ol><ol>18.&#x00A0;</ol></ol></ol>\r\n<p><strong>Which tools do I use to install a patch set?</strong></p>\r\n<ol><ol><ol>19.&#x00A0;</ol></ol></ol>\r\n<p><strong>Why does the software have to be relinked on my computer during installation?</strong></p>\r\n<ol><ol><ol>20.&#x00A0;</ol></ol></ol>\r\n<p><strong>How can I manually link my software?</strong></p>\r\n<ol><ol>21.&#x00A0;</ol></ol>\r\n<p><strong>What are the known problems during software linking?</strong></p>\r\n<ol><ol><ol></ol><ol>22.&#x00A0;</ol></ol></ol>\r\n<p><strong>Where can I find more information about software installation/upgrade and related problems?</strong></p>\r\n<ol><ol><ol></ol></ol></ol>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<ol><ol><ol>1.&#x00A0;</ol></ol></ol>\r\n<p><strong>Which tool do I use to install Oracle?</strong></p>\r\n<p>The tool depends on the Oracle release that you want to install. Up to and including Oracle Release 8.0.6, the text-based 'orainst' was used for the installation. After you unpack the staging area, the tool is available in the &lt;staging_area&gt;/orainst directory. The guide describes how you must now copy the complete directory into the ORACLE_HOME/orainst_sap directory twice. You can then start orainst from there.</p>\r\n<p>As of Oracle 8.1. X, the Java-based runInstaller tool is delivered. After you unpack the staging area, this tool is available in the &lt;STAGING_AREA&gt;\\Disk1 directory. You can start runInstaller directly from this directory.</p>\r\n<ol><ol><ol>2.&#x00A0;</ol></ol></ol>\r\n<p><strong>Can I also install the Oracle software by copying from another system?</strong></p>\r\n<p>The installation of the Oracle software by copying (in the operating system) from another system can result in various errors that may be difficult to analyze and is therefore not allowed.</p>\r\n<ol><ol><ol>3.&#x00A0;</ol></ol></ol>\r\n<p><strong>Which options do I select in the installer and where do I find documentation about the installation?</strong></p>\r\n<p>To install Oracle, refer to the installation guide created by us for the relevant release. You can download the relevant guide for the release to be installed from the Service Marketplace:</p>\r\n<p>http://service.sap.com/instguides --&gt; Database upgrades -&gt; Oracle</p>\r\n<ol><ol><ol>4.&#x00A0;</ol></ol></ol>\r\n<p><strong>What can I do if my runInstaller does not start?</strong></p>\r\n<p>Note 490403 contains a few checks that you can perform if problems arise when you start the runInstaller.</p>\r\n<ol><ol><ol>5.&#x00A0;</ol></ol></ol>\r\n<p><strong>Where do I find the log files for the installation process?</strong></p>\r\n<p>The log files are stored in the inventory directory.</p>\r\n<p>Windows:<br /> The inventory is usually created in &lt;drive&gt;:\\program files\\oracle. The installation log files are located in &lt;drive&gt;:\\program files\\oracle\\Inventory\\logs.</p>\r\n<p><br /> UNIX:<br /> The inventory is installed in /oracle/oraInventory by default. If this is not the case, check the location of the $ORACLE_BASE environment variable or the inventory_loc parameter in the /var/opt/oracle/oraInst.loc file. The InstallActions.log is stored in the 'logs' subdirectory.</p>\r\n<ol><ol><ol>6.&#x00A0;</ol></ol></ol>\r\n<p><strong>What do I have to delete to start the installation from scratch?</strong></p>\r\n<p>First use the installation tool to deinstall everything that was already installed. Call the 'runinstaller' (universal installer on Windows) for this. On the first screen, select the 'deinstall products' button. A list of all products currently installed appears on the next screen. Select all of the ORACLE_HOMES products that you want to clean up. The deinstallation process starts when you select the 'remove' button.<br /> After deinstallation, you must perform additional steps on the relevant operating system:<br /> UNIX:<br /> Rename the current ORACLE_HOME subdirectory and create it again (920_64 --&gt; 920_64_old, for example). Do not fully delete the directory because if a database was already started with this ORACLE_HOME, some files from this database will have to be saved. In particular, the files must be copied from $ORACLE_HOME/network/admin and $ORACLE_HOME/dbs into the new ORACLE_HOME as soon as the new installation is completed.<br /> If this was the only ORACLE_HOME in OracleInventory, rename the OraInventory directory, if possible. Details regarding the location of this directory are stored in the /var/opt/oracle directory. Also rename this file (OraInst.loc) because as soon as this file exists, Oracle will assume that there is an existing oraInventory.<br /> Windows:<br /> Here you should also initially rename the current ORACLE_HOME subdirectory once (920 -&gt; 920_old). The same applies here as for Unix: If a database with this ORACLE_HOME has already been operated, certain files from this _OLD directory must be installed in the new ORACLE_HOME directory as soon as this is fully installed again. The relevant files are located in the two directories %ORACLE_HOME%\\network\\admin (or %ORACLE_HOME%\\net80\\admin with oracle 8.0.x) and %ORACLE_HOME%\\database.<br /> The OracleInventory is usually located in the subdirectory &lt;Drive&gt;:\\program files\\oracle. If the ORACLE_HOME to be cleaned up is the only one in the inventory (in case no other Oracle version is installed on this computer), delete or rename this directory. Oracle also installs the required JRE files in this directory by default. We recommend that you also create this directory again. (That is, rename this directory as well.)<br /> Unfortunately, Oracle usually does not delete all of the many services created during installation. The simplest way to delete all services is to do this in the Windows Registry.<br /> CAUTION: Be especially careful with this point because the operating system may have be restored if incorrect services are deleted by mistake.<br /> Go to the registry editor (regedit) for the following key: \\\\hkey_local_machine\\system\\System\\currentcontrolset\\Services For security reasons it is best to export the complete registry key first. (registry -&gt; export registry file when 'Services' is called. Then go through the list of installed services until the relevant Oracle* services appear. Do NOT delete services with the names 'OracleService&lt;SID&gt;' and, if they exist, 'Oraclecontext*'. All other services generally contain the Oracle home name in the service name. If this concerns the only Oracle installation on this server, you can delete all of the remaining services. If there are several Oracle homes on this host, delete only THOSE services that contain their Oracle home name in the service name.<br /> Note that after you delete the services, you have to reboot the computer again because Windows may not allow you to recreate a service with the same name if the new installation is restarted WITHOUT a reboot.<br /> <br /> You can also delete the values from the registry. <br /> CAUTION: Be especially careful with this point because the operating system may have be restored if incorrect services are deleted by mistake.<br /> In the registry editor, change to the \\hkey_local_machine\\software\\oracle key. For security reasons, first export this complete key (registry -&gt; export registry file). If this is the only Oracle home installation on this computer, delete the complete key. If other ORACLE_HOMES are installed, you can delete only the keys of the ORACLE_HOMES to be cleaned up. In the all homes subdirectory, find and delete the ID of their relevant ORACLE_HOMES. If this was NOT the largest ID, rename all subsequent IDs so that there is one integrated ID list. There is a HOME_COUNTER value directly in the ORACLE_HOMES key. Please decrease this by 1. Furthermore, under the main key \\HKEY_LOCAL_MACHINE\\SOFTWARE\\Oracle, the subkey HOMES exists. Delete the key for the relevant ORACLE_HOME. If it is not the last installed HOME, decrease the IDs of the following HOMES by 1, so there is a continuous HOME list.</p>\r\n<ol><ol><ol>7.&#x00A0;</ol></ol></ol>\r\n<p><strong>Where does Oracle store information about software that has already been installed?</strong></p>\r\n<p>This information is stored in the Oracle Inventory. For more information on the Oracle Inventory, see Note 395521.</p>\r\n<ol><ol><ol>8.&#x00A0;</ol></ol></ol>\r\n<p><strong>What can I do if problems occur during the installation?</strong></p>\r\n<p>First, check the InstallActions. log file that is located in the directory oraInventory/logs. It is important to establish the extent of these problems when installing or relinking software. The following problems may occur during installation:</p>\r\n<ul>\r\n<ul>\r\n<li>Read errors from the staging area</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Storage problems when writing to the $ORACLE_HOME file system</li>\r\n</ul>\r\n</ul>\r\n<p>Note 499055 deals with problems in relation to relinking software. For information on relinking software manually, see Note 97953. You can relink software manually only if the software has already been fully installed.</p>\r\n<ol><ol><ol>9.&#x00A0;</ol></ol></ol>\r\n<p><strong>Which migration paths are supported by SAP?</strong></p>\r\n<p>The upgrade from a certain Oracle source version to a higher Oracle target version is considered as a migration path.<br /> In principle, we support all migration paths supported by Oracle. At present, the only exception to this rule is that we do NOT support direct migration from 7.3.X to 8.1.7. For various reasons, this migration path requires you to use interim release 8.0.6 or 8.1.6.</p>\r\n<ul>\r\n<ul>\r\n<li>The following are supported:</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Source release Target release supported? Supported intermediate release<br />7.3.4 8.1.6 yes<br />7.3.4 8.1.7 NO 8.0.6 or 8.1.6<br />7.3.4 9.2 NO 8.0.6 or 8.1.6<br />8.0.X 8.1.6 yes<br />8.0.x 8.1.7 yes<br />8.0.x 9.2 NO *******<br />8.1.6 9.2 NO *******<br />8.1.7 9.2 yes recommended: ******* patch set<br />******* 10.2 yes, but not recommended<br /> Oracle supports this<br /> upgrade path. We do<br /> not provide any documentation<br /> for this path.<br />  <br />9.0.1.X 10.2 yes at least Patch Set *******<br />9.2.0.X 10.2 yes at least Patch Set *******<br />******** 10.2 yes</li>\r\n</ul>\r\n</ul>\r\n<ol><ol><ol>10.&#x00A0;</ol></ol></ol>\r\n<p><strong>Which tool do I use to upgrade/migrate Oracle?</strong></p>\r\n<p>Unfortunately, the tool changes with every release.<br /> UNIX:<br /> Source -&gt; Target</p>\r\n<ul>\r\n<ul>\r\n<li>7.3.X -&gt; 8.X: orainst is called with special menu options to perform the migration.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>8.x -&gt; 8.1.X odma is called after the software installation.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>8.1.x -&gt; 8.1.7 odma is called after the software installation.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>8.1.7 -&gt; 9.2 migration is performed manually by executing scripts.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>8.1.7 -&gt; 10.2 We do not provide an upgrade path or upgrade guide. For more information about this upgrade path, see the Oracle 10g documentation.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>9.2 -&gt; 10.2 dbua</li>\r\n</ul>\r\n</ul>\r\n<p>Windows:<br /> Source -&gt; Target</p>\r\n<ul>\r\n<ul>\r\n<li>7.3.X -&gt; 8.0.5: mig80 is called manually.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>7.3.X -&gt; 8.0.6: mig80 is called using the universal installer.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>8.x -&gt; 8.1.X odma is called after the software installation.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>8.1.x -&gt; 8.1.7 odma is called after the software installation.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>8.1.7 -&gt; 9.2 Oracle Data Migration Assistant (DUA) is called.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>9.2. -&gt; 10.2 dbua</li>\r\n</ul>\r\n</ul>\r\n<p>Download the relevant upgrade guide from http://service.sap.com/instguides and follow the exact migration steps.</p>\r\n<ol><ol><ol>11.&#x00A0;</ol></ol></ol>\r\n<p><strong>How should Oracle recognize my source database release?</strong></p>\r\n<p>For the corresponding migration tool to know which scripts are to be executed, you must first determine the source release that you are starting from. This information is determined as follows:</p>\r\n<ul>\r\n<ul>\r\n<li>UNIX:<br />In accordance with the entry in the oratab file.<br /><br />For AIX, HPUX, TRU64, and SIEMENS, the file is located in the directory /etcund. For SOLARIS, it is located in the directory /var/opt/oracle.<br />The file is usually as follows<br />&lt;SID&gt;:&lt;source ORACLE_HOME&gt;:N<br />PRD:/oracle/PRD/817_64/:N, or<br />*:/oracle/PRD/817_64/:N<br />These entries are made when the software is installed (by executing the root.sh files). If this was neglected during installation, you can also do this now. Generally, the root.sh script is in the $ORACLE_HOME/install/utl directory. CAUTION: The entry for the source release is usually missing, that is, root.sh must be executed using the source release environment (ORACLE_HOME setting) and the install/utl directory from the Source Oracle Home.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>WINDOWS:<br />Using the Oracle Service version<br />The source release is determined by the Oracle version currently used to start the Oracleservice&lt;SID&gt; for the system.<br />This can be determined manually using the registry \\hkey_local_machine\\system\\currentcontolset\\Services\\Oracleservice&lt;SID&gt;<br />There is an 'instpath' value under this key. This value indicates the Oracle executable of the corresponding Oracle installation.<br />We do NOT recommend that you change this value directly in the registry - if problems occur, create the service with the correct Oracle home as follows:<br />&lt;oracle_home of the current service&gt;\\bin\\oradim -delete -sid &lt;SID&gt;<br />&lt;oracle-home of the source release&gt;\\bin\\oradim -new -sid &lt;SID&gt;</li>\r\n</ul>\r\n</ul>\r\n<ol><ol><ol>12.&#x00A0;</ol></ol></ol>\r\n<p><strong>Where can I find log files for the migration?</strong></p>\r\n<ul>\r\n<ul>\r\n<li>Unix:<br />The $ORACLE_HOME (target release)/assistants/dbma directory contains all of the log files written by the Migration Assistant (odma). For versions that are migrated manually, a spool file is specified at the beginning. In this case, the corresponding information can be taken from the spool file.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>WINDOWS:<br />The %ORACLE_HOME% (target release)\\assistants\\dbma\\logs directory contains the corresponding files.</li>\r\n</ul>\r\n</ul>\r\n<ol><ol><ol>13.&#x00A0;</ol></ol></ol>\r\n<p><strong>Can I manually carry out the migration if problems occur with the tools?</strong></p>\r\n<p>It is also possible to perform the migration steps manually (without the tools mentioned above). Read the following notes for this:</p>\r\n<ul>\r\n<ul>\r\n<li>UNIX: Note 499663</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>WINDOWS: Note 366907</li>\r\n</ul>\r\n</ul>\r\n<ol><ol><ol>14.&#x00A0;</ol></ol></ol>\r\n<p><strong>What is the significance of the init&lt;SID&gt;.ora parameter 'compatible'?</strong></p>\r\n<p>The 'compatible' parameter determines which Oracle release is the lowest one that you may be able to 'downgrade' to. If the parameter is at the current Oracle release, it is not possible to go back to a lower Oracle release.<br /> However, you cannot use certain 'new' functions if the parameter is set lower.<br /> We therefore recommend that you do NOT set the parameter to the current Oracle release after a migration, but instead leave it at the old Oracle release. In this way, you still have the option to go back to the earlier release if there are problems. If, after a certain period of time, you are satisfied with the upgrade result and it is clear there will be no further downgrade, you should set the parameter to the current Oracle release.</p>\r\n<ol><ol><ol>15.&#x00A0;</ol></ol></ol>\r\n<p><strong>Can I downgrade to my old Oracle release?</strong></p>\r\n<p>As mentioned above, this is only possible if you have not yet adjusted the 'compatible' parameter. Note 310031 describes the steps that you must perform to execute a downgrade to a lower Oracle release.</p>\r\n<ol><ol><ol>16.&#x00A0;</ol></ol></ol>\r\n<p><strong>Can I delete the old Oracle software?</strong></p>\r\n<p>You can delete the old software (formerly Source Oracle Home) if the migration is completed successfully. However, the old software is still required for the migration itself, under no circumstances should you delete it before the migration is completed. Note 392800 describes how to proceed when uninstalling software.</p>\r\n<ol><ol><ol>17.&#x00A0;</ol></ol></ol>\r\n<p><strong>What are the requirements for installing a patch set?</strong></p>\r\n<p>You must install at least the main release used to set up the patch set (for example, this would be 8.1.7.0 for the ******* patch set). In general, patch sets do not have to be installed in sequence, that is, in the example mentioned above, it is possible to install ******* directly on 8.1.7.0 without having to first install 8.1.7.1, 8.1.7.2 and 8.1.7.3. Patch sets with lower numbers are contained in all of the subsequent patch sets.<br /> The oraInventory (starting with 8.1.X) or the .rgs file (all releases lower than 8.1) must also contain the Oracle main release. In this case, the ORACLE_HOME on which the main release was initially installed must also be the same (that is, copying into another directory does not work). If you are using different inventories, the oraInst.loc file must also point to the oraInventory directory. For more information about oraInventory and oraInst.loc, see Notes 350251 and 395521.<br /> You can use the installActions.log to determine whether the inventory corresponds to the prerequisites.<br />At the end of the file, there is a list of products currently located in the inventory.</p>\r\n<ol><ol><ol>18.&#x00A0;</ol></ol></ol>\r\n<p><strong>Which tools do I use to install a patch set?</strong></p>\r\n<p>Patch sets are installed using the tool that was used to perform the previous Oracle installation, namely:</p>\r\n<ul>\r\n<ul>\r\n<li>UNIX:<br />up to 8.0.X, orainst<br />as of 8.1.X, runInstaller</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>WINDOWS: Oracle Universal Installer.</li>\r\n</ul>\r\n</ul>\r\n<p>&#x00A0;</p>\r\n<ol><ol><ol>19.&#x00A0;</ol></ol></ol>\r\n<p><strong>Why does the software have to be relinked on my computer during installation?</strong></p>\r\n<p>Oracle requires certain operating system libraries, especially for network activities. Relinking software ensures that these libraries exist and are available in the correct version.</p>\r\n<ol><ol><ol>20.&#x00A0;</ol></ol></ol>\r\n<p><strong>How can I manually link my software?</strong></p>\r\n<p>For more information, see Note 97953.</p>\r\n<ol><ol><ol>21.&#x00A0;</ol></ol></ol>\r\n<p><strong>What are the known problems during software linking?</strong></p>\r\n<p>For more information, see Note 499055.</p>\r\n<p>22.</p>\r\n<p><strong>Where can I find more information about software installation/upgrade and related problems?</strong></p>\r\n<p style=\"padding-left: 30px;\">We release installation notes and upgrade notes for each Oracle release as soon as we support the specific Oracle release. To find SAP Notes for Oracle installations, search our documentation with keywords such as 'oracle' + 'installation' + 'Oracle release'. If you are looking for upgrade notes, use the keywords 'oracle' + 'upgrade' + 'Oracle release' instead.<br />Example: 'oracle installation 18c', among others, finds the following SAP Note:&#x00A0;<a target=\"_blank\" href=\"/notes/2660020\" title=\"2660020 - Central Technical Note for Oracle Database 18c\">2660020 - Central Technical Note for Oracle Database 18c&#x00A0; </a><br />Or 'oracle upgrade 19c', among others, finds the following SAP Note: <a target=\"_blank\" href=\"/notes/2800001\" title=\"2800001 - Upgrading to Oracle Database 19c (DBUA)\">2800001 - Upgrading to Oracle Database 19c (DBUA)</a></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "BC-DB-ORA-INS (Installation SAP System)"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON><PERSON> (I035058)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON> (I070380)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000551542/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000551542/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000551542/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000551542/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000551542/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000551542/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000551542/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000551542/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000551542/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "97953", "RefComponent": "BC-DB-ORA", "RefTitle": "UNIX: Relinking of the Oracle executables", "RefUrl": "/notes/97953"}, {"RefNumber": "741361", "RefComponent": "BC-DB-ORA", "RefTitle": "Problem when installing Oracle patch set 9.2.0.x on Windows", "RefUrl": "/notes/741361"}, {"RefNumber": "640910", "RefComponent": "BC-DB-ORA", "RefTitle": "Problems when using OPatch", "RefUrl": "/notes/640910"}, {"RefNumber": "592393", "RefComponent": "BC-DB-ORA", "RefTitle": "FAQ: Oracle", "RefUrl": "/notes/592393"}, {"RefNumber": "499663", "RefComponent": "BC-DB-ORA", "RefTitle": "Manually upgrading the Oracle database", "RefUrl": "/notes/499663"}, {"RefNumber": "499055", "RefComponent": "BC-DB-ORA", "RefTitle": "Errors during relinking of Oracle software", "RefUrl": "/notes/499055"}, {"RefNumber": "490403", "RefComponent": "BC-DB-ORA", "RefTitle": "Display problems with runInstaller/ odma", "RefUrl": "/notes/490403"}, {"RefNumber": "441518", "RefComponent": "BC-DB-ORA", "RefTitle": "ORA-12560 composite SAP Note", "RefUrl": "/notes/441518"}, {"RefNumber": "395521", "RefComponent": "BC-DB-ORA", "RefTitle": "runInstaller problems and solutions", "RefUrl": "/notes/395521"}, {"RefNumber": "392800", "RefComponent": "BC-DB-ORA", "RefTitle": "Deinstall of Oracle Software", "RefUrl": "/notes/392800"}, {"RefNumber": "366907", "RefComponent": "BC-DB-ORA", "RefTitle": "odma is hanging during upgrade from 8.X to 8.1.X", "RefUrl": "/notes/366907"}, {"RefNumber": "350251", "RefComponent": "BC-DB-ORA-INS", "RefTitle": "Creating a New or 2nd Oracle SID with runInstaller", "RefUrl": "/notes/350251"}, {"RefNumber": "310031", "RefComponent": "BC-DB-ORA", "RefTitle": "Downgrading an Oracle database", "RefUrl": "/notes/310031"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2988826", "RefComponent": "BC-DB-ORA", "RefTitle": "Downgrading Oracle Database - before changing COMPATIBLE", "RefUrl": "/notes/2988826 "}, {"RefNumber": "592393", "RefComponent": "BC-DB-ORA", "RefTitle": "FAQ: Oracle", "RefUrl": "/notes/592393 "}, {"RefNumber": "97953", "RefComponent": "BC-DB-ORA", "RefTitle": "UNIX: Relinking of the Oracle executables", "RefUrl": "/notes/97953 "}, {"RefNumber": "741361", "RefComponent": "BC-DB-ORA", "RefTitle": "Problem when installing Oracle patch set 9.2.0.x on Windows", "RefUrl": "/notes/741361 "}, {"RefNumber": "640910", "RefComponent": "BC-DB-ORA", "RefTitle": "Problems when using OPatch", "RefUrl": "/notes/640910 "}, {"RefNumber": "441518", "RefComponent": "BC-DB-ORA", "RefTitle": "ORA-12560 composite SAP Note", "RefUrl": "/notes/441518 "}, {"RefNumber": "392800", "RefComponent": "BC-DB-ORA", "RefTitle": "Deinstall of Oracle Software", "RefUrl": "/notes/392800 "}, {"RefNumber": "395521", "RefComponent": "BC-DB-ORA", "RefTitle": "runInstaller problems and solutions", "RefUrl": "/notes/395521 "}, {"RefNumber": "499055", "RefComponent": "BC-DB-ORA", "RefTitle": "Errors during relinking of Oracle software", "RefUrl": "/notes/499055 "}, {"RefNumber": "310031", "RefComponent": "BC-DB-ORA", "RefTitle": "Downgrading an Oracle database", "RefUrl": "/notes/310031 "}, {"RefNumber": "490403", "RefComponent": "BC-DB-ORA", "RefTitle": "Display problems with runInstaller/ odma", "RefUrl": "/notes/490403 "}, {"RefNumber": "350251", "RefComponent": "BC-DB-ORA-INS", "RefTitle": "Creating a New or 2nd Oracle SID with runInstaller", "RefUrl": "/notes/350251 "}, {"RefNumber": "499663", "RefComponent": "BC-DB-ORA", "RefTitle": "Manually upgrading the Oracle database", "RefUrl": "/notes/499663 "}, {"RefNumber": "366907", "RefComponent": "BC-DB-ORA", "RefTitle": "odma is hanging during upgrade from 8.X to 8.1.X", "RefUrl": "/notes/366907 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": []}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}