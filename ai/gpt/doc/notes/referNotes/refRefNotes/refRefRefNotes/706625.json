{"Request": {"Number": "706625", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 417, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000015610672017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000706625?language=E&token=EE3351B5B159AA71612B186273920A8F"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000706625", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000706625/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "706625"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 7}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Installation information"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "16.11.2004"}, "SAPComponentKey": {"_label": "Component", "value": "BC-DB-ORA"}, "SAPComponentKeyText": {"_label": "Component", "value": "Oracle"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Basis Components", "value": "BC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Database Interface, Database Platforms", "value": "BC-DB", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-DB*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Oracle", "value": "BC-DB-ORA", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-DB-ORA*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "706625 - Oracle9i: Locally managed SYSTEM tablespace"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p><br />This is a documentation note to describe the special features of a locally-managed SYSTEM tablespace.<br /></p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p><br />LMTS=Locally Managed Tablespace<br />SYSTEM tablespace reorganization<br />brspace -f dbcreate<br /></p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p><br />Documentation and migration note about Oracle9i: 'Locally managed SYSTEM tablespace'<br /></p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p></p> <b>Structure of this note<br />Locally managed SYSTEM tablespace<br />Prerequisites<br />Support by BR*Tools<br />Special features from an Oracle viewpoint<br />Special features from an SAP viewpoint<br /></b><br /> <b>Locally managed SYSTEM tablespace<br /></b><br /> <p>Oracle Release 9.2 now also supports a locally managed SYSTEM tablespace (this was not yet possible with Oracle 8i).<br /><br />Starting with SAP kernel Release 6.40, the SYSTEM tablespace is created as a locally managed tablespace during a new installation on Oracle 9i (9.2.0). The advantages that generally apply to LMTS can then also be used for objects in the SYSTEM tablespace.<br /> <br />If the database is set up again during a database copy, a database migration or a reorganization of the SYSTEM tablespace, the SYSTEM tablespace can be created as locally managed, if the prerequisites (see below) are met.<br /><br />There are a few unproblematic special features from an Oracle viewpoint in conjunction with a locally managed SYSTEM tablespace that you should be familiar with as a database administrator. These are described in this note. Other than these, there is no difference in comparison with a dictionary-managed SYSTEM tablespace in terms of administration.<br /></p> <b>Prerequisites<br /></b><br /> <UL><LI>The general prerequisites for locally managed tablespaces must be met. See Note 214995.</LI></UL> <UL><LI>Oracle Release: at least 9.2.0</LI></UL> <UL><LI>Automatic undo management must be activated.</LI></UL> <UL><LI>A default temporary tablespace must be defined.<br /></LI></UL> <b>Support by BR*Tools<br /></b><br /> <p>This Oracle feature is supported by BR*Tools as of Release 6.40.<br /><br />The migration of the SYSTEM tablespace from dictionary-managed to locally-managed is supported by BRSPACE 6.40. Refer to Note 748434 (brspace -f dbcreate).<br /></p> <b>Special features from an Oracle viewpoint<br /></b><br /> <p>The following applies to databases with a locally-managed SYSTEM tablespace:</p> <OL>1. A default temporary tablespace must exist, and this cannot be the SYSTEM tablespace.<br />For the default temporary tablespace, see Note 683075. This restriction is not a problem, because a separate tablespace is always created for temporary data in the SAP environment (usually PSAPTEMP).</OL><OL>2. Additional tablespaces can only be created as locally-managed (LMTS), not as dictionary-managed (DMTS).<br />This restriction is not a problem because we recommend that you still only create new tablespaces as locally-managed.</OL> <OL>3. Automatic undo management is recommended in conjunction with a locally-managed SYSTEM tablespace (see below: special features from an SAP viewpoint).<br />In addition, rollback segments can no longer be created in dictionary-managed tablespaces.</OL> <OL>4. Although dictionary-managed tablespaces can be attached as transportable tablespaces, they are in Read-Only mode only. </OL> <OL>5. The migration from a dictionary-managed SYSTEM tablespace to a locally-managed tablespace using the PL/SQL procedure DBMS_SPACE_ADMIN.TABLESPACE_MIGRATE_TO_LOCAL is not supported in the SAP environment. <br /></OL> <b>Special features from an SAP viewpoint<br /></b><br /> <p>If the SYSTEM tablespace is locally-managed, Automatic Undo Administration (AUM, see Note 600141) must be active. The conventional manual Undo management, in other words rollback segments in normal locally-managed tablespaces, is not supported.<br /><br /></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "BC-DB-ORA-DBA (Database Administration)"}, {"Key": "Database System", "Value": "ORACLE"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON> (C5000979)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON><PERSON> (I030984)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "J", "TranslationLanguage": "日本語", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000706625/J"}, {"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000706625/D"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000706625/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000706625/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000706625/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000706625/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000706625/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000706625/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000706625/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "748434", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "New BRSPACE function \"dbcreate\" - Create new database", "RefUrl": "/notes/748434"}, {"RefNumber": "683075", "RefComponent": "BC-DB-ORA", "RefTitle": "Oracle9i: De<PERSON>ult Temporary Tablespace", "RefUrl": "/notes/683075"}, {"RefNumber": "600141", "RefComponent": "BC-DB-ORA", "RefTitle": "Oracle9i: Automatic UNDO Management", "RefUrl": "/notes/600141"}, {"RefNumber": "598678", "RefComponent": "BC-DB-ORA", "RefTitle": "Oracle 9i: New functions", "RefUrl": "/notes/598678"}, {"RefNumber": "214995", "RefComponent": "BC-DB-ORA", "RefTitle": "Oracle locally managed tablespaces in the SAP environment", "RefUrl": "/notes/214995"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "748434", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "New BRSPACE function \"dbcreate\" - Create new database", "RefUrl": "/notes/748434 "}, {"RefNumber": "214995", "RefComponent": "BC-DB-ORA", "RefTitle": "Oracle locally managed tablespaces in the SAP environment", "RefUrl": "/notes/214995 "}, {"RefNumber": "600141", "RefComponent": "BC-DB-ORA", "RefTitle": "Oracle9i: Automatic UNDO Management", "RefUrl": "/notes/600141 "}, {"RefNumber": "598678", "RefComponent": "BC-DB-ORA", "RefTitle": "Oracle 9i: New functions", "RefUrl": "/notes/598678 "}, {"RefNumber": "683075", "RefComponent": "BC-DB-ORA", "RefTitle": "Oracle9i: De<PERSON>ult Temporary Tablespace", "RefUrl": "/notes/683075 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": []}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}