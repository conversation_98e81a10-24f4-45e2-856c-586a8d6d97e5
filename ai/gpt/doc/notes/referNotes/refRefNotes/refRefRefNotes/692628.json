{"Request": {"Number": "692628", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 456, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000003700752017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000692628?language=E&token=8E496F587557BAA085DEDA443D7617EC"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000692628", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000692628/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "692628"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 10}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Program error"}, "Priority": {"_label": "Priority", "value": "Correction with medium priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "04.03.2004"}, "SAPComponentKey": {"_label": "Component", "value": "XX-PART-ISHMED"}, "SAPComponentKeyText": {"_label": "Component", "value": "Clinical System i.s.h.med"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Miscellaneous", "value": "XX", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Partner solutions", "value": "XX-PART", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-PART*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Clinical System i.s.h.med", "value": "XX-PART-ISHMED", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-PART-ISHMED*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "692628 - IS-H*MED: Planning Grid - Plan Appointments for Midnight (00"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>In the planning grid you plan an appointment at 00:00. In the status bar the system will display an error message stating that no planning authority exists, although this is correctly maintained. You then immediately plan an appointment at, for example, 00:10. This appointment is created correctly. You then again try to plan an appointment at 00:00. The system will preset the time of the appointment with 00:10.<br />If you have entered the parameter value \"5\" in the OU-parameter N1PTTMNR, the following problem will also occur:<br />You plan an appointment at 00:05. You system will incorrectly preset the planning time with 08:05.</p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>N1LU, ISHMEDMSCHED.OCX</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><OL>1. You execute the source code correction</OL> <OL>2. You import the transport request stored in the following file on SAPSERV3:</OL> <UL><UL><LI>/specific/ish/ISHMED/Hinweis692628_463B.zip for Release 4.63B</LI></UL></UL> <UL><UL><LI>/specific/ish/ISHMED/Hinweis692628_471.zip&#x00A0;&#x00A0;for Release 4.71</LI></UL></UL> <OL>3. You download the ISHMEDMSCHED.SETUP file using the report RN2LN210 and install the planning grid (ISHMEDMSched.ocx) locally on the work stations.</OL> <p>Note number 13719 contains detailed information on how to download from SAPSERV3.<br />Information:<br />This version of the planning grid also corrects the errors from the following notes:</p> <UL><LI>623179 (IS-H*MED: Planning Grid - More Than 3 Simultaneous Appointments</LI></UL> <UL><LI>624788 (IS-H*MED: Planning Grid - More Than 100 Appointments)</LI></UL> <UL><LI>629828 (Support of Hebraic Fonts)</LI></UL> <UL><LI>640046 (IS-H*MED: Planning Grid Call - Short Dump)</LI></UL> <p>Information:<br />The planning grid (ISHMEDMSched.ocx) is contained in patch 17 of Release 4.63B, but the code change will be delivered in patch 18 of Release 4.63B.<br />This means that when you import patch 17 of Release 4.63B you should import the code change from this note.<br />The installation of patch 17 is only necessary with patch 17.</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "XX-PART-ISHMED-ORD (Service Management i.s.h.med)"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON> (C5044749)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON> (C5048269)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000692628/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000692628/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000692628/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000692628/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000692628/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000692628/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000692628/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000692628/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000692628/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "744374", "RefComponent": "XX-PART-ISHMED", "RefTitle": "IS-H*MED: Planning Grid - Problems with Time Grid Size", "RefUrl": "/notes/744374"}, {"RefNumber": "733917", "RefComponent": "XX-PART-ISHMED", "RefTitle": "IS-H*MED: Planning Grid: Appointments/Tab Pages not Displaye", "RefUrl": "/notes/733917"}, {"RefNumber": "640046", "RefComponent": "XX-PART-ISHMED", "RefTitle": "IS-H*MED: Planning Grid Call - Short Dump", "RefUrl": "/notes/640046"}, {"RefNumber": "629828", "RefComponent": "XX-PART-ISHMED", "RefTitle": "IS-H*MED: Support of Hebra<PERSON> Fonts", "RefUrl": "/notes/629828"}, {"RefNumber": "624788", "RefComponent": "XX-PART-ISHMED", "RefTitle": "IS-H*MED: Planning Grid - More Than 100 Appts", "RefUrl": "/notes/624788"}, {"RefNumber": "623179", "RefComponent": "XX-PART-ISHMED", "RefTitle": "IS-H*MED: Planning Grid - More Than 3 Simultaneous Appts", "RefUrl": "/notes/623179"}, {"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719 "}, {"RefNumber": "744374", "RefComponent": "XX-PART-ISHMED", "RefTitle": "IS-H*MED: Planning Grid - Problems with Time Grid Size", "RefUrl": "/notes/744374 "}, {"RefNumber": "733917", "RefComponent": "XX-PART-ISHMED", "RefTitle": "IS-H*MED: Planning Grid: Appointments/Tab Pages not Displaye", "RefUrl": "/notes/733917 "}, {"RefNumber": "640046", "RefComponent": "XX-PART-ISHMED", "RefTitle": "IS-H*MED: Planning Grid Call - Short Dump", "RefUrl": "/notes/640046 "}, {"RefNumber": "629828", "RefComponent": "XX-PART-ISHMED", "RefTitle": "IS-H*MED: Support of Hebra<PERSON> Fonts", "RefUrl": "/notes/629828 "}, {"RefNumber": "624788", "RefComponent": "XX-PART-ISHMED", "RefTitle": "IS-H*MED: Planning Grid - More Than 100 Appts", "RefUrl": "/notes/624788 "}, {"RefNumber": "623179", "RefComponent": "XX-PART-ISHMED", "RefTitle": "IS-H*MED: Planning Grid - More Than 3 Simultaneous Appts", "RefUrl": "/notes/623179 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "IS-H", "From": "463B", "To": "463B", "Subsequent": ""}, {"SoftwareComponent": "IS-H", "From": "471", "To": "471", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "IS-H 463B", "SupportPackage": "SAPKIPHD18", "URL": "/supportpackage/SAPKIPHD18"}, {"SoftwareComponentVersion": "IS-H 463B", "SupportPackage": "SAPKIPHD17", "URL": "/supportpackage/SAPKIPHD17"}, {"SoftwareComponentVersion": "IS-H 471", "SupportPackage": "SAPKIPHE10", "URL": "/supportpackage/SAPKIPHE10"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": [{"SoftwareComponent": "IS-H", "NumberOfCorrin": 2, "URL": "/corrins/0000692628/6"}]}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 2, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}