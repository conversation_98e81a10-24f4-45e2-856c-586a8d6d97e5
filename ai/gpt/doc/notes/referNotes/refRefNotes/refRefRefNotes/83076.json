{"Request": {"Number": "83076", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 486, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000014521212017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/**********?language=E&token=E02EE1A36FA45E60341A8F2AEA9A0E20"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/**********", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/**********/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "83076"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 62}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Advance development"}, "Priority": {"_label": "Priority", "value": "Correction with medium priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "21.10.2002"}, "SAPComponentKey": {"_label": "Component", "value": "AC-INT"}, "SAPComponentKeyText": {"_label": "Component", "value": "Accounting Interface"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "AC - Accounting", "value": "AC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'AC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Accounting Interface", "value": "AC-INT", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'AC-INT*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "83076 - MM_ACCTIT: Archiving programs for ACCTIT, ACCTHD, ACCTCR"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><OL>1. You want to archive tables ACCTHD, ACCTIT, ACCTCR and are searching for the suitable archiving object.</OL> <OL>2. You are already using archiving object MM_ACCTIT for archiving tables <PERSON>THD, ACCTIT and ACCTCR, but are not content with the performance of the write program.</OL> <OL>3. You want to use the current version of the programs for archiving object MM_ACCTIT. SAP recommends you to use the current version.</OL><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>ADK, archiving<br />Archiving object: MM_ACCTIT<br />Programs: RGU<PERSON>CM<PERSON>, RG<PERSON><PERSON>LM<PERSON>, RG<PERSON><PERSON>MM, RGUTITIC, RGUTITID</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><OL>1. Cause: Missing development or missing information.</OL> <OL>2. You are not using the latest version of the MM_ACCTIT archiving.</OL> <p>Precondition: You have carefully read Note 48009 and have checked whether the data is required for subsequent postings or for auditing purposes. Only if data is important you should archive it. Otherwise, you should delete the data as described in Note 48009.<br /></p> <b>Additional information</b><br /> <p>If you archive MM documents with archiving object MM_MATBEL and then reset the MM number ranges, the 'duplicate record' phenomenon occurs when a new MM document is created and the ACCT* tables are updated in this context. This is because the new MM document can be posted, whereas its information cannot be updated in the ACCT* tables. The ACCT* tables already contain an entry with this document number. This is information which would be required for subsequently posting the document which has already been archived with MM_MATBEL.<br />Solution:<br />After the MM document archiving, you must NOT reset the number ranges as long as the above ACCT* tables still contain corresponding data. First archive or delete the corresponding data of the above-mentioned tables.<br /></p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>Use archiving object MM_ACCTIT for archiving tables ACCTHD, ACCTIT und ACCTCR.<br />Depending on which release you are using, this object may be contained in your system. If this object is not yet contained in your system, if you are not content with the performance of the write program, or if you are using an old version of the archiving object, then import the files listed below which are relevant for your release. They include programs in German and English.<br /><br />Solution for Releases 3.0D up to and including 3.1I:<br />----------------------------------------------------</p> <OL>1. Import the latest version of the ADK (Archive Development Kit).</OL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Implement Note 89324 if you have not yet done so. <OL>2. Import archiving object MM_ACCTIT.</OL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Import, according to Note 13719, the files R001211.ZAT and K001211.ZAT from the directory<br />/general/R3server/abap/note.0083076/3.x_release on the relevant SapservX server, where X = 3, 4, 5, 6 or 7. <OL>3. If you want to read archived ACCT* records, connect archiving object MM_ACCTIT to the SAP AS (archive information system). If you do not need read access, you can skip this point.</OL> <OL><OL>a) Import the SAP AS into your system (if you have not yet done so) according to Note 99388. This note also includes the documentation on the SAP AS.</OL></OL> <OL><OL>b) Import the connection for the archiving object MM_ACCTIT to SAP AS:<br />files R001081.ZAT and&#x00A0;&#x00A0;K001081.ZAT from directory /general/R3server/abap/note.0083076 on the relevant SapservX server.<br />Now you have field catalog SAP_MM_ACCTIT02 and the information structure with the same name. Activate this information structure.<br />If you have used information structure SAP_MM_ACCTIT or an own information structur for field catalog SAP_MM_ACCTIT01 up to now, we recommend to deactivate it and to delete the associated table. Acivate the new information structure SAP_MM_ACCTIT02 instead and set it up for the relevant archive files.</OL></OL> <OL>4. Make sure the system does not update index table ARC_IDX_AT for MM_ACCTIT. This index table was especially designed for this archiving object to allow for swift access to the archive. However, it is not used and thus should not be unnecessarily filled with data. The SAP Archive Information System AS has been designed for swift archive access.</OL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Go to Transaction SARA and enter MM_ACCTIT as the object. If pushbutton \"Index\" is displayed in the \"Actions\" group, carry out subpoints a) and b). If this key does not appear, no further action is required in this point. <OL><OL>a) Delete the contents of the index table.</OL></OL> <UL><LI>Call Transaction SARA and enter MM_ACCTIT as object. Click on buttons \"Index\" and then \"Reduce index\".</LI></UL> <UL><LI>Create a variant. Click on \"Archive selection\" and select all archive files. If message GX017 \"There are no archives for the current selections\" is displayed, this means that no index has been built for any archive file. Reducing the index is not require since Table ARC_IDX_AT is empty. In this case, continue with item 4 b).</LI></UL> <OL><OL>a) Turn off the option for index creation.</OL></OL> <UL><LI>To do this, call Transaction AOBJ. Messages may be displayed that inform you about changes to the SAP standard. You can ignore them.</LI></UL> <UL><LI>Double-click on the entry for object MM_ACCTIT. If the 'Build index' checkbox is activated on the next screen, then deactivate it. (Depending on the release the 'Fill index' ('Make index') checkbox may not be available; in this case you do not need to do anything.)</LI></UL> <UL><LI>Exit the screen, select the entry for object MM_ACCTIT and in the upper or left part of the screen choose 'Customizing settings'. If the 'Fill index' ('Make index')&#x00A0;&#x00A0;checkbox is activated on the next screen, then deactivate it. (Depending on the release the 'Build index' checkbox may not be available; in this case you do not need to do anything.)</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Transport the changed customizing settings into all systems concerned. <p><br />After an upgrade to a release prior to 4.6C, repeat the steps from point 2.<br /><br /><br />Solution for Release 4.0B:<br />--------------------------</p> <OL>1. Import archiving object MM_ACCTIT.</OL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Import, according to Note 13719, the files R001169.ZAT and K001169.ZAT from directory<br />/general/R3server/abap/note.0083076/4.x_release on the relevant SapservX server, where X = 3, 4, 5, 6 or 7. <OL>2. Perform the steps described in point 3 which contains the solution for Release 3.0D up to and including 3.1I.</OL> <OL>3. Perform the steps described in point 4 which contains the solution for Release 3.0D up to and including 3.1I.</OL> <p><br />After an upgrade to a release earlier than 4.6C, repeat the steps from point 1.<br /><br /><br />Solution for Releases as of 4.5B:<br />---------------------------------</p> <OL>1. Import archiving object MM_ACCTIT.</OL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Import, according to Note 13719, the files R001169.ZAT and K001169.ZAT from directory<br />/general/R3server/abap/note.0083076/4.x_release on the relevant SapservX server, where X = 3, 4, 5, 6 or 7. <OL>2. Perform step b) in point 3 which contains the solution for Release 3.0D up to and including 3.1.</OL> <OL>3. Perform the steps described in point 4 which contains the solution for Release 3.0D up to and including 3.1I.</OL> <p><br />After an upgrade to a release prior or equal to&#x00A0;&#x00A0;Release 4.6C, repeat the steps from point 1.<br /><br /></p> <b>Additional information</b><br /> <p>For information on the archive write, archive delete and archive read program refer to Transaction SARA, where you can either choose 'Archive' or 'Delete', or 'Analyze' and then 'Goto -&gt; Hints'.<br />For information on the reload program refer to Note 186369.<br />You can access the archived data via the Archive Information System SAP AS (Transaktion SARI). The corresponding documentation is contained in Note 99388. The appropriate information structure is called SAP_MM_ACCTIT (field catalog SAP_MM_ACCTIT01).<br />The connection to the SAP AS supersedes the original, optional indexes (table ARC_IDX_AT, programs RGUTITIC, RGUTITID).<br /></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "MM-CBP (Consumption-Based Planning)"}, {"Key": "Other Components", "Value": "FI (Financial Accounting)"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON><PERSON> (D022872)"}, {"Key": "Processor                                                                                           ", "Value": "D020848"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "J", "TranslationLanguage": "日本語", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/**********/J"}, {"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/**********/D"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "48009", "RefComponent": "AC-INT", "RefTitle": "Tables ACCTHD, ACCTIT, ACCTCR: Questions and answers", "RefUrl": "/notes/48009"}, {"RefNumber": "436751", "RefComponent": "AC-INT", "RefTitle": "Archiving MM_ACCTIT: ACCTHD records incorrectly deleted", "RefUrl": "/notes/436751"}, {"RefNumber": "369786", "RefComponent": "AC-INT", "RefTitle": "MM_ACCTIT/SAP AS: Error Q6330 w/info struct creatn", "RefUrl": "/notes/369786"}, {"RefNumber": "316468", "RefComponent": "AC-INT", "RefTitle": "Tables ACCTIT + ACCTHD:Analysis of table contents", "RefUrl": "/notes/316468"}, {"RefNumber": "186369", "RefComponent": "AC-INT", "RefTitle": "MM_ACCTIT/reload: Reload program as add-on", "RefUrl": "/notes/186369"}, {"RefNumber": "186217", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/186217"}, {"RefNumber": "178476", "RefComponent": "SV-BO-REQ", "RefTitle": "High increase of table ACCTIT, ACCTHD or ACCTCR", "RefUrl": "/notes/178476"}, {"RefNumber": "171692", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/171692"}, {"RefNumber": "149034", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/149034"}, {"RefNumber": "148245", "RefComponent": "AC-INT", "RefTitle": "MM_ACCTIT/delete: Error CANNOT_CHANGE_STATUS", "RefUrl": "/notes/148245"}, {"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "48009", "RefComponent": "AC-INT", "RefTitle": "Tables ACCTHD, ACCTIT, ACCTCR: Questions and answers", "RefUrl": "/notes/48009 "}, {"RefNumber": "316468", "RefComponent": "AC-INT", "RefTitle": "Tables ACCTIT + ACCTHD:Analysis of table contents", "RefUrl": "/notes/316468 "}, {"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719 "}, {"RefNumber": "369786", "RefComponent": "AC-INT", "RefTitle": "MM_ACCTIT/SAP AS: Error Q6330 w/info struct creatn", "RefUrl": "/notes/369786 "}, {"RefNumber": "148245", "RefComponent": "AC-INT", "RefTitle": "MM_ACCTIT/delete: Error CANNOT_CHANGE_STATUS", "RefUrl": "/notes/148245 "}, {"RefNumber": "186369", "RefComponent": "AC-INT", "RefTitle": "MM_ACCTIT/reload: Reload program as add-on", "RefUrl": "/notes/186369 "}, {"RefNumber": "436751", "RefComponent": "AC-INT", "RefTitle": "Archiving MM_ACCTIT: ACCTHD records incorrectly deleted", "RefUrl": "/notes/436751 "}, {"RefNumber": "178476", "RefComponent": "SV-BO-REQ", "RefTitle": "High increase of table ACCTIT, ACCTHD or ACCTCR", "RefUrl": "/notes/178476 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_APPL", "From": "30C", "To": "31I", "Subsequent": "X"}, {"SoftwareComponent": "SAP_APPL", "From": "40B", "To": "40B", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "45B", "To": "45B", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "46A", "To": "46B", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "46C", "To": "46C", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}