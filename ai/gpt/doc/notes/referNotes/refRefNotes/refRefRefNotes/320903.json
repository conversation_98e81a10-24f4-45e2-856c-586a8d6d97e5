{"Request": {"Number": "320903", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 284, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000014874862017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000320903?language=E&token=46C1BAF79928D9F2CBF655AA3B588312"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000320903", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000320903/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "320903"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 27}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Workaround of missing functionality"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "06.02.2012"}, "SAPComponentKey": {"_label": "Component", "value": "SV-PERF-SCM"}, "SAPComponentKeyText": {"_label": "Component", "value": "SCM related performance Incidents"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Service", "value": "SV", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'SV*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Performance Problems", "value": "SV-PERF", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'SV-PERF*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "SCM related performance Incidents", "value": "SV-PERF-SCM", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'SV-PERF-SCM*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "320903 - Preparing the SCM (APO) system for EarlyWatch and GoingLive"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>Tools that you require for the following SAP services are missing: SAP GoingLive check, SAP GoingLive functional upgrade check, SAP EarlyWatch service.</p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>SCM, Logistics, software, check<br />SD monitor, ST14, logistics monitor<br />SQLR<br />Advanced Planning and Optimization, APO</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>Some of the tools that you require are only delivered in higher releases or are available in release updates.</p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>Follow the instructions of Note 91488. For Solution Manager,the installed ST-SER release must be a version that is in maintenance. Currently this is ST-SER 701_2010_1 for SAP Solution Manager 7.0 Enhancement Package 1. Refer to SAP Note 569116 .<br /><br />In addition, the following apply to Supply Chain Management (SCM) Systems, (Advanced Planning and Optimization (APO) Systems).<br /></p> <OL>1. Install a RFC-SAPOSCOL as described in Note 19227 for all servers without an R/3 instance in the APO system, for example, standalone LiveCache servers, all servers with optimizers, selected front-end servers or standalone database servers. See Note 371023 for additional information about the installation. Transactions RZ20 and RZ21 allow long-term monitoring by configuring the collector runs. This function is required for standalone liveCache servers and standalone CTM/SNP servers for GoingLive and EarlyWatch services. Note 402535 contains additional information about SAPOSCOL problems in AIX.</OL> <OL>2. Implement SAP Note 539977 (ST/PI) and SAP Note 69455 (ST-A/PI) before executing a service session.</OL> <OL>3. SAP Note 202344 describes the setup of a 'SAP DB Connection' in the SAP Support Portal for liveCache customers so that we can access your system using SAP DB-SQLStudio. See Note 386714 for further information about installing SAP DB-SQLStudios.</OL> <OL>4. APO 2.0A only: Implement Note 374346 before executing a service session.</OL> <OL>5. To work in the managed SCM system, the service consultant should be provided with the <B>RSDUSER</B> described in SAP Note 1405975, or a user with equivalent authorisations.</OL> <OL>6. To execute the GoingLive Optimization service with the APO-GATP<br />function, see Note 407293.</OL> <OL>7. If you are making a BW configuration to SCM Basis (for example, because you are using APO demand planning), take the special requirements for BW into account that are described in Note 160777.</OL> <OL>8. Implement Note 176641 before executing a service session ONLY if the following are used: SAP Basis Component 46B,46C,46D. SAP Application Component 40A,40B,45A,45B,46A,46B,46C.</OL> <OL>9. Regarding the service preparation of sessions, it may be necessary for Unicode AIX customers to implement SAP Note 1345697 \"Database Analyzer data is no longer displayed\". Otherwise it will not be possible to display the monitoring data in LC10 -&gt; Database Analyzer, although DbAnalyzer is enabled and the monitoring data is collected.The following basis SPs&#x00A0;&#x00A0;are affected(as mentioned above: only the AIX + Unicode combination is affected):</OL> <p><br /> 4.6C SP 58<br /> 6.20 SP 66<br /> 6.40 SP 24<br /> 7.00 SP 18-19<br /> 7.01 SP 03-04<br /> 7.02 SP 00<br /> 7.10 SP 07-08<br /> 7.11 SP 01-02<br /><br /><br /><br /></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON><PERSON> (I020126)"}, {"Key": "Processor                                                                                           ", "Value": "I019960"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000320903/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000320903/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000320903/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000320903/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000320903/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000320903/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000320903/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000320903/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000320903/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "91488", "RefComponent": "SV-SMG-SER", "RefTitle": "SAP Support Services - Central preparatory note", "RefUrl": "/notes/91488"}, {"RefNumber": "569116", "RefComponent": "SV-SMG-SER", "RefTitle": "Release strategy for Solution Manager Service Tools (ST-SER)", "RefUrl": "/notes/569116"}, {"RefNumber": "407293", "RefComponent": "SV-BO-REQ", "RefTitle": "Preparing APO System with GATP for GoingLive", "RefUrl": "/notes/407293"}, {"RefNumber": "374346", "RefComponent": "BC-DB-DBI", "RefTitle": "ST05: Performance trace display takes very long", "RefUrl": "/notes/374346"}, {"RefNumber": "1162394", "RefComponent": "SV-SMG-SER", "RefTitle": "SA: SCM/APO GV - memory used by liveCache server not correct", "RefUrl": "/notes/1162394"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "91488", "RefComponent": "SV-SMG-SER", "RefTitle": "SAP Support Services - Central preparatory note", "RefUrl": "/notes/91488 "}, {"RefNumber": "569116", "RefComponent": "SV-SMG-SER", "RefTitle": "Release strategy for Solution Manager Service Tools (ST-SER)", "RefUrl": "/notes/569116 "}, {"RefNumber": "1162394", "RefComponent": "SV-SMG-SER", "RefTitle": "SA: SCM/APO GV - memory used by liveCache server not correct", "RefUrl": "/notes/1162394 "}, {"RefNumber": "407293", "RefComponent": "SV-BO-REQ", "RefTitle": "Preparing APO System with GATP for GoingLive", "RefUrl": "/notes/407293 "}, {"RefNumber": "374346", "RefComponent": "BC-DB-DBI", "RefTitle": "ST05: Performance trace display takes very long", "RefUrl": "/notes/374346 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": []}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}