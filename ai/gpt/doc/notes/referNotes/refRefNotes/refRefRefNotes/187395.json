{"Request": {"Number": "187395", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 1029, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000000957192017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/**********?language=E&token=8C45A10E25643BCEB3E1A6918DA55933"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/**********", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/**********/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "187395"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 15}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Special development"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "10.04.2000"}, "SAPComponentKey": {"_label": "Component", "value": "XX-PROJ-IHC"}, "SAPComponentKeyText": {"_label": "Component", "value": "Inhouse Cash"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Miscellaneous", "value": "XX", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Project-based solutions", "value": "XX-PROJ", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-PROJ*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Inhouse Cash", "value": "XX-PROJ-IHC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-PROJ-IHC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "187395 - In House Cash: Current Transports"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>You are a pilot customer for the new CFM component In House Cash and you want to obtain the latest transports from SAPSERVx</p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>IHC, IHB</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>WARNING!!! PLEASE DO NOT IMPORT THE FOLLOWING TRANSPORT WITHOUT BEING AN INHOUSE CASH PILOT CUSTOMER.<br /><br />There are currently two transports on SAPSERVx:<br /><br />The first one contains an update of the IHC add on for the BCA banking solution (development class FKBI) and has to be imported ONLY(!!!) into the IHB system.<br /><br />transport name: KK4K042381<br />directory on SAPSERVx: /general/R3server/abap/ihc/version1<br /><br />For the second transport, please refer to note 203403.<br /><br />Please make sure that there is no open repair request on objects in FKBI or on structure PAYRQ when you import the transport.<br />In case of having problems with the import, please refer to the related note 13719.</p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>Once you have imported the transport, there are some things you have to do.</p> <UL><LI>Run the attached program ZREPAIR_FKBI in your IHB system.</LI></UL> <p></p> <UL><LI>During the import of the main IHC transport you'll get an error: 'dynpro source mutilated: D023S entries can't be imported.' The reason is: Unfortunately the&#x00A0;&#x00A0;maintainance dialog for View V_TBKKIHB5 that was automatically created in the 4.6 development system does not work in 4.0 or 4.5, thus you have to repeat the steps in your IHC system:</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Go into SE54 for view V_TBKKIHB5 and choose 'Generated Objects - Create/Change'. On the following screen you should enter the same values as for V_TBKKIHB4, except for the screen number: The overview screen is '500'.<br /><br />Please use the new component&#x00A0;&#x00A0;XX-PROJ-IHC&#x00A0;&#x00A0;when you want to post a customer message concerning In House Cash.<br /></p> <h3 data-toc-skip>Important functionality contained in this transport:</H3> <p><br />First a few words about terminology: For payments going through the In House Bank, there are two possible points of origin: Either a subsidiary FI system (automatic payment) or the IHB itself (manual payment). Moreover there are two possible targets: Either the external Headquarter FI system (external payment) or an account in the IHB (internal payment).<br /><br />The two highlights of this transports are<br /></p> <UL><LI>Creation of multiple payment requests for automatic external payments so that invoice reference is forwarded into the HQ FI and finally to the vendor.</LI></UL> <UL><LI>Extended customizing possibilities for automatic (Table TBKKIHB5; transaction FIHB5) and manual (TBKKIHB4; FIHB4) payments. Both tables can be maintained generically, only the bank area is strictly required. The generic value is SPACE.</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Please note that the last two value fields in TBKKIHB5 are the internal BCA transaction types for the posting on the BCA account of the sender. The first one is for 'Credit' lines (default type '71'), the second for 'Debit' lines ('70').</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D021772)"}, {"Key": "Processor                                                                                           ", "Value": ""}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "203403", "RefComponent": "XX-PROJ-IHC", "RefTitle": "Inhouse Cash: Current transport for PAYRQ structure", "RefUrl": "/notes/203403"}, {"RefNumber": "198860", "RefComponent": "FI-BL-PT", "RefTitle": "FI_PAYMENT_REQUEST_MODIFY_RFC: Error messages", "RefUrl": "/notes/198860"}, {"RefNumber": "189236", "RefComponent": "XX-PROJ-IHC", "RefTitle": "Reference flds deducs and withholding tax in PAYRQ", "RefUrl": "/notes/189236"}, {"RefNumber": "185897", "RefComponent": "XX-PROJ-IHC", "RefTitle": "Adjustment in the FI system for In-House Cash", "RefUrl": "/notes/185897"}, {"RefNumber": "181175", "RefComponent": "FI-BL-PT", "RefTitle": "Amount in local currency for payment requests", "RefUrl": "/notes/181175"}, {"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719 "}, {"RefNumber": "181175", "RefComponent": "FI-BL-PT", "RefTitle": "Amount in local currency for payment requests", "RefUrl": "/notes/181175 "}, {"RefNumber": "198860", "RefComponent": "FI-BL-PT", "RefTitle": "FI_PAYMENT_REQUEST_MODIFY_RFC: Error messages", "RefUrl": "/notes/198860 "}, {"RefNumber": "203403", "RefComponent": "XX-PROJ-IHC", "RefTitle": "Inhouse Cash: Current transport for PAYRQ structure", "RefUrl": "/notes/203403 "}, {"RefNumber": "189236", "RefComponent": "XX-PROJ-IHC", "RefTitle": "Reference flds deducs and withholding tax in PAYRQ", "RefUrl": "/notes/189236 "}, {"RefNumber": "185897", "RefComponent": "XX-PROJ-IHC", "RefTitle": "Adjustment in the FI system for In-House Cash", "RefUrl": "/notes/185897 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": []}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": [{"SoftwareComponent": "SAP_APPL", "NumberOfCorrin": 1, "URL": "/corrins/**********/1"}]}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 1, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}