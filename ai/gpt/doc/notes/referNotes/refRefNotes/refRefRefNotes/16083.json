{"Request": {"Number": "16083", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 293, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000014335662017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000016083?language=E&token=075406D53047F707488B3B08DEC671A9"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000016083", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000016083/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "16083"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 83}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Consulting"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "13.05.2020"}, "SAPComponentKey": {"_label": "Component", "value": "BC-CCM-BTC"}, "SAPComponentKeyText": {"_label": "Component", "value": "Background Processing"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Basis Components", "value": "BC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Computer Center Management System (CCMS)", "value": "BC-CCM", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-CCM*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Background Processing", "value": "BC-CCM-BTC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-CCM-BTC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "16083 - Standard jobs, reorganization jobs"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>There are a number of jobs that must periodically run in a live SAP installation, for example, to delete outdated jobs or spool objects.<br />As of Release 4.6C, you can easily schedule these jobs as follows:<br />Transaction SM36 -&gt; 'Standard jobs' button. We recommend scheduling the jobs in client 000.&#x00A0;<br /><br />Unfortunately, there is no easy-to-use support for such jobs within Basis customizing before Release 4.6C. Therefore, you must schedule the jobs explicitly for these release levels.<br />This note contains a list of the required programs, their parameters, and the recommended repeat interval. In addition, names are suggested for the required jobs. Adhere to the recommendations, as the naming conventions enable us to check quickly and easily whether these jobs have been activated in your system.<br />This note applies as of Releases 2.1G and 2.2A.</p>\r\n<p>For S/4HANA, this SAP Note is not valid. Please see SAP Note <a target=\"_blank\" href=\"/notes/2190119\">2190119</a>, instead.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>REORGJOBS.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>Note: Application-specific reorganization programs are not included in this list.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\">\r\n<tbody>\r\n<tr>\r\n<td>Job name:</td>\r\n<td>&nbsp;</td>\r\n<td>Program</td>\r\n<td>&nbsp;</td>\r\n<td>Variant</td>\r\n<td>&nbsp;</td>\r\n<td>Repeat interval</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td>SAP_REORG_JOBS</td>\r\n<td>&nbsp;</td>\r\n<td>RSBTCDEL2</td>\r\n<td>&nbsp;</td>\r\n<td>Yes</td>\r\n<td>&nbsp;</td>\r\n<td>Daily</td>\r\n</tr>\r\n<tr>\r\n<td>SAP_REORG_SPOOL</td>\r\n<td>&nbsp;</td>\r\n<td>RSPO0041/1041</td>\r\n<td>&nbsp;</td>\r\n<td>Yes</td>\r\n<td>&nbsp;</td>\r\n<td>Daily</td>\r\n</tr>\r\n<tr>\r\n<td>SAP_REORG_BATCHINPUT</td>\r\n<td>&nbsp;</td>\r\n<td>RSBDCREO</td>\r\n<td>&nbsp;</td>\r\n<td>Yes</td>\r\n<td>&nbsp;</td>\r\n<td>Daily</td>\r\n</tr>\r\n<tr>\r\n<td>SAP_REORG_ABAPDUMPS</td>\r\n<td>&nbsp;</td>\r\n<td>RSSNAPDL</td>\r\n<td>&nbsp;</td>\r\n<td>Yes</td>\r\n<td>&nbsp;</td>\r\n<td>Daily</td>\r\n</tr>\r\n<tr>\r\n<td>SAP_REORG_JOBSTATISTIC</td>\r\n<td>&nbsp;</td>\r\n<td>RSBPSTDE</td>\r\n<td>&nbsp;</td>\r\n<td>Yes</td>\r\n<td>&nbsp;</td>\r\n<td>Monthly</td>\r\n</tr>\r\n<tr>\r\n<td>SAP_COLLECTOR_FOR_JOBSTATISTIC</td>\r\n<td>&nbsp;</td>\r\n<td>RSBPCOLL</td>\r\n<td>&nbsp;</td>\r\n<td>No</td>\r\n<td>&nbsp;</td>\r\n<td>Daily</td>\r\n</tr>\r\n<tr>\r\n<td>SAP_COLLECTOR_FOR_PERFMONITOR</td>\r\n<td>&nbsp;</td>\r\n<td>RSCOLL00</td>\r\n<td>&nbsp;</td>\r\n<td>No</td>\r\n<td>&nbsp;</td>\r\n<td>Hourly</td>\r\n</tr>\r\n<tr>\r\n<td>SAP_COLLECTOR_FOR_NONE_R3_STAT</td>\r\n<td>&nbsp;</td>\r\n<td>RSN3_STAT_</td>\r\n<td>&nbsp;</td>\r\n<td>No</td>\r\n<td>&nbsp;</td>\r\n<td>Hourly</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n<td>&nbsp;</td>\r\n<td>COLLECTOR</td>\r\n</tr>\r\n<tr>\r\n<td>SAP_REORG_PRIPARAMS</td>\r\n<td>&nbsp;</td>\r\n<td>RSBTCPRIDEL,</td>\r\n<td>&nbsp;</td>\r\n<td>Yes*</td>\r\n<td>&nbsp;</td>\r\n<td>Monthly</td>\r\n</tr>\r\n<tr>\r\n<td>SAP_REORG_XMILOG</td>\r\n<td>&nbsp;</td>\r\n<td>RSXMILOGREORG</td>\r\n<td>&nbsp;</td>\r\n<td>Yes</td>\r\n<td>&nbsp;</td>\r\n<td>Weekly</td>\r\n</tr>\r\n<tr>\r\n<td>SAP_CCMS_MONI_BATCH_DP</td>\r\n<td>&nbsp;</td>\r\n<td>RSAL_BATCH_</td>\r\n<td>&nbsp;</td>\r\n<td>No</td>\r\n<td>&nbsp;</td>\r\n<td>Hourly</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n<td>&nbsp;</td>\r\n<td>TOOL_DISPATCHING</td>\r\n</tr>\r\n<tr>\r\n<td>SAP_SPOOL_CONSISTENCY_CHECK</td>\r\n<td>&nbsp;</td>\r\n<td>RSPO1043</td>\r\n<td>&nbsp;</td>\r\n<td>Yes</td>\r\n<td>&nbsp;</td>\r\n<td>Daily</td>\r\n</tr>\r\n<tr>\r\n<td>SAP_REORG_ORPHANED_JOBLOGS</td>\r\n<td>&nbsp;</td>\r\n<td>RSTS0024</td>\r\n<td>&nbsp;</td>\r\n<td>Yes</td>\r\n<td>&nbsp;</td>\r\n<td>Weekly</td>\r\n</tr>\r\n<tr>\r\n<td>SAP_CHECK_ACTIVE_JOBS</td>\r\n<td>&nbsp;</td>\r\n<td>BTCAUX07</td>\r\n<td>&nbsp;</td>\r\n<td>Yes</td>\r\n<td>&nbsp;</td>\r\n<td>Hourly</td>\r\n</tr>\r\n<tr>\r\n<td>SAP_DELETE_ORPHANED_IVARIS</td>\r\n<td>&nbsp;</td>\r\n<td>BTC_DELETE_ORPHANED_IVARIS</td>\r\n<td>&nbsp;</td>\r\n<td>Yes</td>\r\n<td>&nbsp;</td>\r\n<td>Weekly</td>\r\n</tr>\r\n<tr>\r\n<td>SAP_REORG_ORPHANED_TEMSE_FILES</td>\r\n<td>&nbsp;</td>\r\n<td>RSTS0043</td>\r\n<td>&nbsp;</td>\r\n<td>Yes</td>\r\n<td>&nbsp;</td>\r\n<td>Weekly</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p>* <em>As of Release 6.20</em></p>\r\n<p><br />In any case, refer to Note 48400 regarding the spool and TemSe<br />consistency check.<br /><br />Note:</p>\r\n<ul>\r\n<li>SAP_CCMS_MONI_BATCH_DP is not a reorganization job but is needed to start tools/methods in the system monitoring area in the background -&gt; transaction RZ20/RZ21.</li>\r\n</ul>\r\n<ul>\r\n<li>SAP_REORG_XMILOG reorganizes the table TXMILOGRAW. This table contains log information on the XMI interface (-&gt; SAP Note 182963).</li>\r\n</ul>\r\n<ul>\r\n<li>As of Release 4.6A, the job steps are managed separately from the print parameters. This means that report RSBTCDEL no longer deletes any print parameters. You must therefore schedule the following report, too:<br /><br /> RSBTCPRIDEL<br /><br />It reorganizes print parameters in a cross-client manner. Since the number of print parameters increases more slowly than the number of background processing steps, you can execute this report after longer periods of time ( longer than one month).<br />You must refer to Note 307970 in connection with RSBTCPRIDEL.</li>\r\n</ul>\r\n<ul>\r\n<li>The job SAP_COLLECTOR_FOR_PERFMONITOR was previously also called COLLECTOR_FOR_PERFORMANCEMONITOR.</li>\r\n</ul>\r\n<ul>\r\n<li>The job SAP_COLLECTOR_FOR_NONE_R3_STAT is available as of SAP Web Application Server 6.20 only.</li>\r\n</ul>\r\n<ul>\r\n<li>In addition, you should regularly run a consistency check of the spooler and the TemSe (Note 48400).</li>\r\n</ul>\r\n<ul>\r\n<li></li>\r\n</ul>\r\n<ul>\r\n<li>***** Caution: The standard job SAP_REORG_UPDATERECORDS that was contained in some delivered versions must no longer be executed (see Note 67014). In its new version, the underlying ABAP Program RSM13002 is programmed so that it terminates when running in a job (that is, in the background). Therefore, the job SAP_REORG_UPDATERECORDS always terminates in this case. In any case, the job SAP_REORG_UPDATERECORDS should be deleted from the pool of standard jobs, if it is still there: SM36 -&gt; 'Standard jobs' button -&gt; 'Delete standard jobs' button. In addition, released jobs that may exist and contain report RSM13002 should be deleted. You can find and delete these jobs using sm37.</li>\r\n</ul>\r\n<ul>\r\n<li>***** Caution: The job SAP_WP_CACHE_RELOAD_FULL is normally used to update data from the workplace component system to the workplace server.<br />This job is allowed to be run on a workplace server only; otherwise it terminates. In the future, this job will no longer be delivered as a standard job.<br />If this job has been scheduled for your system, but you do not need it, delete the scheduled job in transaction SM37.</li>\r\n</ul>\r\n<ul>\r\n<li>To eliminate ABAP dumps that are created due to runtime errors within an ABAP program, use the program RSSNAPDL. To simplify the associated job scheduling, you also have the program RSNAPJOB. This program schedules RSSNAPDL within a job. Implicit assumptions:</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Job name: RSSNAPDL</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Variant: DEFAULT (therefore it must exist)</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Start date: From the following day, 1:00 am</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Repeat interval: Daily</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li>Using the Support Packages specified in Note 666290, report RSTS0024 deletes job logs that no longer belong to any job. Contact SAP if you need the report in a release that is not specified in SAP Note 666290.</li>\r\n</ul>\r\n<p><strong>Clients and authorizations</strong></p>\r\n<p>Some of the jobs specified work with client-specific objects (for example, jobs). Whether a reorganization has any cross-client influence then normally depends on particular authorizations. All client-specific jobs are listed below. None of the other jobs are client-specific.<br /><br />Jobs that are not client-dependent perform a reorganization in all affected clients. They require neither special authorizations nor a special user name.</p>\r\n<p>The job SAP_COLLECTOR_FOR_PERFMONITOR must always be scheduled in client 000 with user DDIC or with a user with the same authorization.<br /><br />For some jobs, note the following correlations:</p>\r\n<ul>\r\n<li>Job SAP_REORG_JOBS:</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>An authorization for the object S_BTCH_ADM = 'Y'&#x00A0;is required in order to be able to delete jobs of other users and in other clients.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li>Job SAP_REORG_SPOOL:</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Authorization S_ADMI_FCD-S_ADMI_FCD = 'SPAD'<br />Reorganization runs in chosen client (Client = '*', then runs in all clients)</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Authorization S_ADMI_FCD-S_ADMI_FCD = 'SPAR'<br />Reorganization only in the client in which the actual job is running</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li>Job SAP_REORG_BATCHINPUT:</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Authorization profile S_BDC_MONI - BDCAKTI = 'REOG'<br /> S_BDC_MONI - BDCGROUPID = '*'<br />Reorganization only in the client in which the job is running (as of SAP Note <a target=\"_blank\" href=\"/notes/2177694\">2177694</a> and Release&#x00A0;7.00, cross-client).</li>\r\n</ul>\r\n</ul>\r\n<p><br /><br />Comments:</p>\r\n<ul>\r\n<li>The authorizations always relate to the user under whose ID the job is being processed.</li>\r\n</ul>\r\n<ul>\r\n<li>If this user has the authorizations required to work in a cross-client manner, the client in which the job actually runs is irrelevant.</li>\r\n</ul>\r\n<p><strong>Examples</strong></p>\r\n<ul>\r\n<li>User ADMIN has the Authorization S_BTCH_ADM = 'Y'. If Job SAP_REORG_JOBS is now scheduled with User ADMIN, the jobs are reorganized in all clients.</li>\r\n</ul>\r\n<ul>\r\n<li>User REORG has the authorization profile S_BDC_ALL. among others. If the job SAP_REORG_BATCHINPUT is now scheduled with the user REORG in client 002, the batch input objects are reorganized in all clients as of Release 7.00. In previous releases, they are reorganized only in client 002.</li>\r\n</ul>\r\n<ul>\r\n<li>If the job SAP_REORG_ABAPDUMPS is scheduled in any client, all ABAP short dumps in all clients are reorganized.</li>\r\n</ul>\r\n<ul>\r\n<li>User SPOOLADM has the authorization S_ADMI_FCD-S_ADMI_FCD = 'SPAD'. If the job SAP_REORG_SPOOL is now scheduled with user SPOOLADM and client 123 is specified for the program parameters, then the spool objects in client 123 are reorganized irrespective of the client in which the actual job is running. If you enter '*' as the client, all clients are reorganized.</li>\r\n</ul></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "BC-CCM-MON (CCMS Monitoring & Alerting)"}, {"Key": "Other Components", "Value": "BC-ABA-SC (Dynpro and CUA engine)"}, {"Key": "Other Components", "Value": "BC-ABA-LA (Syntax, Compiler,  Runtime)"}, {"Key": "Other Components", "Value": "BC-CCM-PRN (Print and Output Management)"}, {"Key": "Other Components", "Value": "BC-ABA-LI (ABAP List Processing)"}, {"Key": "Transaction codes", "Value": "HIER"}, {"Key": "Transaction codes", "Value": "SPAD"}, {"Key": "Transaction codes", "Value": "SM36"}, {"Key": "Transaction codes", "Value": "SPAR"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON><PERSON> (D023157)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D025322)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "J", "TranslationLanguage": "日本語", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000016083/J"}, {"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000016083/D"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000016083/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000016083/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000016083/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000016083/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000016083/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000016083/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000016083/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "98065", "RefComponent": "BC-CCM-PRN-SPO", "RefTitle": "Spool consistency check with RSPO1043 as of 4.0A", "RefUrl": "/notes/98065"}, {"RefNumber": "800927", "RefComponent": "BC-DB-LCA", "RefTitle": "Standard jobs in the SCM/APO area", "RefUrl": "/notes/800927"}, {"RefNumber": "706478", "RefComponent": "HAN-DB", "RefTitle": "Preventing Basis tables from increasing considerably", "RefUrl": "/notes/706478"}, {"RefNumber": "48400", "RefComponent": "BC-CCM-PRN-TMS", "RefTitle": "Reorganization of TemSe and spool", "RefUrl": "/notes/48400"}, {"RefNumber": "41547", "RefComponent": "BC-CCM-PRN-SPO", "RefTitle": "How does report RSPO0041 work?", "RefUrl": "/notes/41547"}, {"RefNumber": "2190119", "RefComponent": "BC-CCM-BTC-JR", "RefTitle": "Background information about SAP S/4HANA technical job repository", "RefUrl": "/notes/2190119"}, {"RefNumber": "2177694", "RefComponent": "BC-ABA-SC", "RefTitle": "Batch input: Standard job SAP_REORG_BATCHINPUT becomes cross-client", "RefUrl": "/notes/2177694"}, {"RefNumber": "19706", "RefComponent": "BC-CCM-PRN-SPO", "RefTitle": "Tuning the Spooler", "RefUrl": "/notes/19706"}, {"RefNumber": "18307", "RefComponent": "BC-ABA-SC", "RefTitle": "Batch input logs and reorganization", "RefUrl": "/notes/18307"}, {"RefNumber": "16513", "RefComponent": "BC-CCM-MON-OS", "RefTitle": "AS ABAP file system full - what to do", "RefUrl": "/notes/16513"}, {"RefNumber": "147354", "RefComponent": "BC-ABA-SC", "RefTitle": "Batch input: Reorg. and delete sessions and logs", "RefUrl": "/notes/147354"}, {"RefNumber": "1411877", "RefComponent": "BC-CCM-BTC", "RefTitle": "New standard jobs", "RefUrl": "/notes/1411877"}, {"RefNumber": "130978", "RefComponent": "BC-CCM-PRN", "RefTitle": "RSPO1041 - alternative to RSPO0041", "RefUrl": "/notes/130978"}, {"RefNumber": "1255188", "RefComponent": "BC-CCM-PRN-TMS", "RefTitle": "Missing standard variants for reorg reports", "RefUrl": "/notes/1255188"}, {"RefNumber": "12103", "RefComponent": "BC-CCM-MON-TUN", "RefTitle": "Contents of table TCOLL", "RefUrl": "/notes/12103"}, {"RefNumber": "11070", "RefComponent": "BC-CCM-PRN-TMS", "RefTitle": "Space requirements of TemSe and spooler", "RefUrl": "/notes/11070"}, {"RefNumber": "1021775", "RefComponent": "BC-ABA-TO", "RefTitle": "Orphaned temporary variants in the table VARI", "RefUrl": "/notes/1021775"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2219868", "RefComponent": "BC-CCM-MON-TUN", "RefTitle": "Component for report in TCOLL table", "RefUrl": "/notes/2219868 "}, {"RefNumber": "3121227", "RefComponent": "BC-DB-HDB-CCM", "RefTitle": "DB Size History is not displayed in DBACOCKPIT even if standard job 'SAP_COLLECTOR_FOR_PERFMONITOR' was scheduled", "RefUrl": "/notes/3121227 "}, {"RefNumber": "1900570", "RefComponent": "BC-CCM-BTC", "RefTitle": "Can't schedule job SAP_REORG_ORPHANED_JOBLOGS from \"Standard jobs\"", "RefUrl": "/notes/1900570 "}, {"RefNumber": "2382606", "RefComponent": "BC-CCM-BTC", "RefTitle": "Job start phase takes unusually long -- Jobs unexpectedly take a long time", "RefUrl": "/notes/2382606 "}, {"RefNumber": "3051831", "RefComponent": "BC-CCM-BTC", "RefTitle": "How to create custom variant for RSBTCDEL2.", "RefUrl": "/notes/3051831 "}, {"RefNumber": "3008195", "RefComponent": "BC-CCM-BTC", "RefTitle": "FAQ: Background Processing BC-CCM-BTC-*", "RefUrl": "/notes/3008195 "}, {"RefNumber": "2231932", "RefComponent": "BC-ESI-WS-ABA-MON", "RefTitle": "ESI - How to schedule the SAP_SOAP_RUNTIME_MANAGEMENT clean-up job", "RefUrl": "/notes/2231932 "}, {"RefNumber": "2982953", "RefComponent": "BC-UPG-TLS-TLA", "RefTitle": "SPOOL_INTERNAL_ERROR dump (spool overflow) in phase MAIN_SHDCRE/SUBMOD_SHDDBCLONE/DBCLONE", "RefUrl": "/notes/2982953 "}, {"RefNumber": "2866110", "RefComponent": "BC-CCM-PRN-SPO", "RefTitle": "Spools are always deleted by 8 days", "RefUrl": "/notes/2866110 "}, {"RefNumber": "2794194", "RefComponent": "BC-CCM-MON-TUN", "RefTitle": "ST03 no data with error \"System Failure: Name or password is incorrect (repeat logon)\" in report SWNC_TCOLL_LOG", "RefUrl": "/notes/2794194 "}, {"RefNumber": "2779970", "RefComponent": "LE-WM", "RefTitle": "L5103 issued in VG03 and VG02 where predecessor document no longer exists", "RefUrl": "/notes/2779970 "}, {"RefNumber": "1926388", "RefComponent": "BC-OP-NT", "RefTitle": "Various errors in batch area on MSCS", "RefUrl": "/notes/1926388 "}, {"RefNumber": "2224014", "RefComponent": "BC-CCM-MON-SLG", "RefTitle": "Old system log entries disappear and how to extend the duration of system log", "RefUrl": "/notes/2224014 "}, {"RefNumber": "2593715", "RefComponent": "BC-CCM-PRN-TMS", "RefTitle": "(Internal KBA)  Spool: RT_INSERT Reports Error 128 for Table TSP02", "RefUrl": "/notes/2593715 "}, {"RefNumber": "2573665", "RefComponent": "BC-DB-MSS", "RefTitle": "st22 dump DBIF_REPO_SQL_ERROR  with 3989 while accessing program", "RefUrl": "/notes/2573665 "}, {"RefNumber": "2544466", "RefComponent": "BC-CCM-MON-ORA", "RefTitle": "Grey metrics and obsolete/no data in DB02 - First steps", "RefUrl": "/notes/2544466 "}, {"RefNumber": "2369736", "RefComponent": "BC-CCM-MON-TUN", "RefTitle": "Troubleshooting missing data in ST03N/ST03", "RefUrl": "/notes/2369736 "}, {"RefNumber": "2205695", "RefComponent": "BC-CCM-MON-ORA", "RefTitle": "Inactive modules in DB02", "RefUrl": "/notes/2205695 "}, {"RefNumber": "3283316", "RefComponent": "CA-VCM", "RefTitle": "Scope activation of Technical Job Definitions in VCM processes", "RefUrl": "/notes/3283316 "}, {"RefNumber": "3083045", "RefComponent": "PP-MRP-BD", "RefTitle": "Report RMMDMONI does not capture the runtime statistics of all MRP runs", "RefUrl": "/notes/3083045 "}, {"RefNumber": "2190119", "RefComponent": "BC-CCM-BTC-JR", "RefTitle": "Background information about SAP S/4HANA technical job repository", "RefUrl": "/notes/2190119 "}, {"RefNumber": "2177694", "RefComponent": "BC-ABA-SC", "RefTitle": "Batch input: Standard job SAP_REORG_BATCHINPUT becomes cross-client", "RefUrl": "/notes/2177694 "}, {"RefNumber": "1510259", "RefComponent": "BC-CCM-PRN-TMS", "RefTitle": "Syslog message F3V  'Error XXX for write/read access to a file. File = <filename>'", "RefUrl": "/notes/1510259 "}, {"RefNumber": "2066896", "RefComponent": "BC-ABA-SC", "RefTitle": "Batch input: Standard job SAP_REORG_BATCHINPUT does not delete anything", "RefUrl": "/notes/2066896 "}, {"RefNumber": "500950", "RefComponent": "BC-INS-AS4", "RefTitle": "iSeries: R/3 EBCDIC --> ASCII Codepage Conversion Doc.", "RefUrl": "/notes/500950 "}, {"RefNumber": "706478", "RefComponent": "HAN-DB", "RefTitle": "Preventing Basis tables from increasing considerably", "RefUrl": "/notes/706478 "}, {"RefNumber": "1440439", "RefComponent": "BC-CCM-BTC", "RefTitle": "New Standard Jobs (2)", "RefUrl": "/notes/1440439 "}, {"RefNumber": "1630506", "RefComponent": "BC-DB-DB6-CCM", "RefTitle": "DB6: History management of monitoring data", "RefUrl": "/notes/1630506 "}, {"RefNumber": "1411877", "RefComponent": "BC-CCM-BTC", "RefTitle": "New standard jobs", "RefUrl": "/notes/1411877 "}, {"RefNumber": "48400", "RefComponent": "BC-CCM-PRN-TMS", "RefTitle": "Reorganization of TemSe and spool", "RefUrl": "/notes/48400 "}, {"RefNumber": "1407635", "RefComponent": "BC-ABA-LI", "RefTitle": "Unnecessary database accesses in RSBTCPRIDEL", "RefUrl": "/notes/1407635 "}, {"RefNumber": "144864", "RefComponent": "SV-SMG-SER", "RefTitle": "Setting Up the ABAP Workload Monitor", "RefUrl": "/notes/144864 "}, {"RefNumber": "12103", "RefComponent": "BC-CCM-MON-TUN", "RefTitle": "Contents of table TCOLL", "RefUrl": "/notes/12103 "}, {"RefNumber": "19706", "RefComponent": "BC-CCM-PRN-SPO", "RefTitle": "Tuning the Spooler", "RefUrl": "/notes/19706 "}, {"RefNumber": "130978", "RefComponent": "BC-CCM-PRN", "RefTitle": "RSPO1041 - alternative to RSPO0041", "RefUrl": "/notes/130978 "}, {"RefNumber": "98065", "RefComponent": "BC-CCM-PRN-SPO", "RefTitle": "Spool consistency check with RSPO1043 as of 4.0A", "RefUrl": "/notes/98065 "}, {"RefNumber": "11070", "RefComponent": "BC-CCM-PRN-TMS", "RefTitle": "Space requirements of TemSe and spooler", "RefUrl": "/notes/11070 "}, {"RefNumber": "1255188", "RefComponent": "BC-CCM-PRN-TMS", "RefTitle": "Missing standard variants for reorg reports", "RefUrl": "/notes/1255188 "}, {"RefNumber": "1260586", "RefComponent": "BC-DB-MSS-CCM", "RefTitle": "Runtime error in SQL Server Performance History", "RefUrl": "/notes/1260586 "}, {"RefNumber": "1083786", "RefComponent": "BC-CCM-MON", "RefTitle": "RZ20: Job monitoring data collector terminates", "RefUrl": "/notes/1083786 "}, {"RefNumber": "788797", "RefComponent": "BC-ABA-SC", "RefTitle": "Batch input: Session multiple selection for reorganization", "RefUrl": "/notes/788797 "}, {"RefNumber": "509454", "RefComponent": "BC-ABA-SC", "RefTitle": "Batch input log overview: Delayed display", "RefUrl": "/notes/509454 "}, {"RefNumber": "769429", "RefComponent": "BC-CCM-BTC", "RefTitle": "Standard job cannot be created", "RefUrl": "/notes/769429 "}, {"RefNumber": "553621", "RefComponent": "BC-DB-DB6-CCM", "RefTitle": "DB6: No history data in the application monitor (ST07)", "RefUrl": "/notes/553621 "}, {"RefNumber": "307970", "RefComponent": "BC-ABA-LI", "RefTitle": "Wrong print specifications, wrong printer in background", "RefUrl": "/notes/307970 "}, {"RefNumber": "800927", "RefComponent": "BC-DB-LCA", "RefTitle": "Standard jobs in the SCM/APO area", "RefUrl": "/notes/800927 "}, {"RefNumber": "568690", "RefComponent": "BC-CCM-BTC", "RefTitle": "Incorrect system variant for RSBTCDEL", "RefUrl": "/notes/568690 "}, {"RefNumber": "728947", "RefComponent": "BC-CCM-BTC", "RefTitle": "Job count cannot be created", "RefUrl": "/notes/728947 "}, {"RefNumber": "16513", "RefComponent": "BC-CCM-MON-OS", "RefTitle": "AS ABAP file system full - what to do", "RefUrl": "/notes/16513 "}, {"RefNumber": "41547", "RefComponent": "BC-CCM-PRN-SPO", "RefTitle": "How does report RSPO0041 work?", "RefUrl": "/notes/41547 "}, {"RefNumber": "129252", "RefComponent": "BW-SYS-DB-ORA", "RefTitle": "Oracle DB Statistics for BW Tables", "RefUrl": "/notes/129252 "}, {"RefNumber": "18307", "RefComponent": "BC-ABA-SC", "RefTitle": "Batch input logs and reorganization", "RefUrl": "/notes/18307 "}, {"RefNumber": "147354", "RefComponent": "BC-ABA-SC", "RefTitle": "Batch input: Reorg. and delete sessions and logs", "RefUrl": "/notes/147354 "}, {"RefNumber": "168439", "RefComponent": "XX-SER-TCC-EW", "RefTitle": "Preparándose para una sesión de Early Watch ó GL", "RefUrl": "/notes/168439 "}, {"RefNumber": "18319", "RefComponent": "BC-ABA-SC", "RefTitle": "Difficulties with the batch input log file", "RefUrl": "/notes/18319 "}, {"RefNumber": "7224", "RefComponent": "BC-CTS", "RefTitle": "Deleting old corrections and transport requests", "RefUrl": "/notes/7224 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": []}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}