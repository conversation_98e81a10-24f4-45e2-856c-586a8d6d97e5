{"Request": {"Number": "122718", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 315, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000014608012017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000122718?language=E&token=0660CC59D676C1F13B3384BD3CF932A8"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000122718", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000122718/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "122718"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 27}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Performance"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "19.12.2011"}, "SAPComponentKey": {"_label": "Component", "value": "BC-DB-ORA"}, "SAPComponentKeyText": {"_label": "Component", "value": "Oracle"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Basis Components", "value": "BC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Database Interface, Database Platforms", "value": "BC-DB", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-DB*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Oracle", "value": "BC-DB-ORA", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-DB-ORA*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "122718 - CBO: Tables with special treatment"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>The performance is poor.<br />Statistics differ from the standard values.<br /></p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p></p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>When you create statistics for the Cost Based Optimizer (CBO), BRCONNECT behaves according to certain rules (see also Note 588668):</p> <UL><LI>All SAP tables contain CBO statistics. Exceptions to this are pool tables and cluster tables on Oracle 9i or lower.</LI></UL> <UL><LI>New statistics are created if the dataset in a table has changed by more than a threshold value.</LI></UL> <UL><LI>The sample size for new statistics is determined on the basis of the number of table entries.<br /></LI></UL> <p>For certain tables, these standard mechanisms are not optimal. Possible reasons are as follows:</p> <UL><LI>With Oracle 9i or lower, the Rule Based Optimizer (RBO) returns better accesses for a table than the CBO, so no statistics need to be created.</LI></UL> <UL><LI>A table requires new statistics more frequently even if the threshold value has not yet been achieved.</LI></UL> <UL><LI>The sample size used by BRCONNECT for the statistics is insufficient for a table because the statistics are not accurate enough.<br /></LI></UL> <p>Exception rules can therefore be defined in the DBSTATC control table. Note 106047 describes how customer-specific exceptions can be defined and what the entries in DBSTATC mean. This note contains the standard SAP exception rules for statistics creation, which are also delivered as part of the script dbstatc.sql (Oracle 9i or lower), dbstatc10.sql (Oracle 10g), or dbstatc11.sql (Oracle 11g) (see Note 403704). Note that this script contains many more entries to activate certain tables for the SAP application monitor. These entries are not important in terms of database statistics, so we will not discuss them further here. Also note that the R/3 pool and cluster tables are handled with Oracle 9i or lower by default with ACTIV=R, so that an explicit entry in dbstatc.sql is not necessary, and these tables are not listed below.<br /></p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><OL>1. No statistics should be created for the following tables with Oracle 9i or lower (ACTIV=N) so that the Rule Based Optimizer is used. This setting is particularly useful for tables with widely varying contents because it prevents new statistics from being created at an inconvenient time (for example, if the table is almost empty), which could cause incorrect CBO decisions during later accesses.</OL> <UL><LI>RFC: ARFCSSTATE, ARFCSDATA, ARFCRSTATE, TRFCQDATA, TRFCQIN, TRFCQINS, TRFCQOUT, TRFCQSTATE (Note 371068)</LI></UL> <UL><LI>Controlling information systems: COIX, COIX_DATA40</LI></UL> <UL><LI>Buffer synchronization: DDLOG</LI></UL> <UL><LI>Nametab: DDNTT, DDXTF, DDXTT</LI></UL> <UL><LI>Table conversion: TATAF</LI></UL> <UL><LI>Table logging: DBTABLOG</LI></UL> <UL><LI>Transport: TRBAT, TRBATC, TRBAT2</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;As of Oracle 10g, statistics are created for all tables so that dbstatc10.sql for the database Oracle no longer contains ACTIV=N entries. You can create statistics delivered by SAP for critical tables using Note 1020260 instead. <OL>2. For the following tables, only one temporary statistic should be created with Oracle 9i or lower (for example, to determine storage information), which is immediately deleted again afterwards (ACTIV=R). This setting is also useful for tables with widely varying contents.</OL> <UL><LI>Batch input: APQD, APQI</LI></UL> <UL><LI>Nametab: DDNTF</LI></UL> <UL><LI>Short dumps: SNAP, SNAPT</LI></UL> <UL><LI>Update: VBDATA, VBHDR, VBLOG, VBMOD</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;As of Oracle 10g, statistics are created for all tables so that dbstatc10.sql no longer contains any ACTIV=R entries of the original meaning. You can create statistics delivered by SAP for critical tables using Note 1020260 instead. If you access the new method of statistics fixing that is available as of BRCONNECT 7.10 (25), ACTIV=R entries with a new meaning are used. For more information, see Note 1374807. <OL>3. By default, the following tables always receive statistics with histograms (METHOD = EH), which allows a useful evaluation of SUBSTITUTE-Hints (Note 797629):</OL> <UL><LI>LTAP, LTBP</LI></UL> <UL><LI>MSEG, MKPF</LI></UL> <UL><LI>PPC_HEAD</LI></UL> <UL><LI>RSDD_TMPNM_ADM</LI></UL> <UL><LI>BDCP, BDCPS</LI></UL> <OL>4. As of Oracle 10g, SAP delivers useful statistics for several tables (Note 1020260). Depending on the method you use, you make entries of the following type in DBSTATC:</OL> <UL><LI>Classical approach: ACTIV=I</LI></UL> <UL><LI>New method (can be used as of BRCONNECT 7.10 (25)): ACTIV=I for tables with complete statistics adjustment, special entries with ACTIV=R and ACTIV=A (Note 1374807) for tables with adjustment of individual index values<br /></LI></UL> <p>Further adjustments (for example, forcing a higher degree of statistical accuracy on individual tables) are currently not necessary by default.<br /></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Database System", "Value": "ORACLE"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D030484)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D030484)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000122718/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000122718/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000122718/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000122718/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000122718/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000122718/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000122718/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000122718/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000122718/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "869006", "RefComponent": "BC-DB-ORA", "RefTitle": "Composite SAP note: ORA-04031", "RefUrl": "/notes/869006"}, {"RefNumber": "797629", "RefComponent": "BC-DB-ORA", "RefTitle": "FAQ: Oracle histograms", "RefUrl": "/notes/797629"}, {"RefNumber": "766349", "RefComponent": "BC-DB-ORA", "RefTitle": "FAQ: Oracle SQL optimization", "RefUrl": "/notes/766349"}, {"RefNumber": "756335", "RefComponent": "BC-DB-ORA", "RefTitle": "Statistics in tables w/ heavily fluctuating volumes of data", "RefUrl": "/notes/756335"}, {"RefNumber": "742950", "RefComponent": "BC-MID-RFC", "RefTitle": "Performance affected on Oracle DB with Supplement 11", "RefUrl": "/notes/742950"}, {"RefNumber": "618868", "RefComponent": "BC-DB-ORA", "RefTitle": "FAQ: Oracle performance", "RefUrl": "/notes/618868"}, {"RefNumber": "588668", "RefComponent": "BC-DB-ORA", "RefTitle": "FAQ: Database statistics", "RefUrl": "/notes/588668"}, {"RefNumber": "505246", "RefComponent": "BC-DB-ORA", "RefTitle": "Several ora-600 [12333] and ora-4031 errors", "RefUrl": "/notes/505246"}, {"RefNumber": "403704", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "BRCONNECT - enhanced function for Oracle DBA", "RefUrl": "/notes/403704"}, {"RefNumber": "1374807", "RefComponent": "BC-DB-ORA", "RefTitle": "Freezing single kinds of statistics", "RefUrl": "/notes/1374807"}, {"RefNumber": "131372", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/131372"}, {"RefNumber": "106047", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "DB21: Customizing the DBSTATC", "RefUrl": "/notes/106047"}, {"RefNumber": "1020260", "RefComponent": "BC-DB-ORA", "RefTitle": "Delivery of Oracle statistics (Oracle >= 10g)", "RefUrl": "/notes/1020260"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1897137", "RefComponent": "BC-SRV-SSF", "RefTitle": "Smartform activation takes a long time", "RefUrl": "/notes/1897137 "}, {"RefNumber": "1020260", "RefComponent": "BC-DB-ORA", "RefTitle": "Delivery of Oracle statistics (Oracle >= 10g)", "RefUrl": "/notes/1020260 "}, {"RefNumber": "618868", "RefComponent": "BC-DB-ORA", "RefTitle": "FAQ: Oracle performance", "RefUrl": "/notes/618868 "}, {"RefNumber": "797629", "RefComponent": "BC-DB-ORA", "RefTitle": "FAQ: Oracle histograms", "RefUrl": "/notes/797629 "}, {"RefNumber": "766349", "RefComponent": "BC-DB-ORA", "RefTitle": "FAQ: Oracle SQL optimization", "RefUrl": "/notes/766349 "}, {"RefNumber": "106047", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "DB21: Customizing the DBSTATC", "RefUrl": "/notes/106047 "}, {"RefNumber": "403704", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "BRCONNECT - enhanced function for Oracle DBA", "RefUrl": "/notes/403704 "}, {"RefNumber": "869006", "RefComponent": "BC-DB-ORA", "RefTitle": "Composite SAP note: ORA-04031", "RefUrl": "/notes/869006 "}, {"RefNumber": "588668", "RefComponent": "BC-DB-ORA", "RefTitle": "FAQ: Database statistics", "RefUrl": "/notes/588668 "}, {"RefNumber": "1374807", "RefComponent": "BC-DB-ORA", "RefTitle": "Freezing single kinds of statistics", "RefUrl": "/notes/1374807 "}, {"RefNumber": "756335", "RefComponent": "BC-DB-ORA", "RefTitle": "Statistics in tables w/ heavily fluctuating volumes of data", "RefUrl": "/notes/756335 "}, {"RefNumber": "742950", "RefComponent": "BC-MID-RFC", "RefTitle": "Performance affected on Oracle DB with Supplement 11", "RefUrl": "/notes/742950 "}, {"RefNumber": "505246", "RefComponent": "BC-DB-ORA", "RefTitle": "Several ora-600 [12333] and ora-4031 errors", "RefUrl": "/notes/505246 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": []}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}