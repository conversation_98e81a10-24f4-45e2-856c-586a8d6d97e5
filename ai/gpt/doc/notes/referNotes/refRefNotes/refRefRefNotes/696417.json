{"Request": {"Number": "696417", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 2913, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "D", "_masterLanguage": "D", "_loadedLanguage": "D", "_machineTranslationLanguage": "E", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Acknowledge", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "If the quality of the machine translation is not sufficient for your purposes, click here to request a revised version.", "_type": "EVENT", "_visible": true}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000015591442017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/**********?language=E&token=CF90D193D1245675AB4B31EAABF0FF01"}, "ShareByEmail": {"_label": "Share via Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/**********", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/**********/D/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "696417"}, "Type": {"_label": "Type", "value": "SAP-Hinweis"}, "Version": {"_label": "Version", "value": 1}, "Recency": {"_label": "Currentness", "key": "", "value": ""}, "Language": {"_label": "Language", "value": "German"}, "MasterLanguage": {"_label": "Original Language", "value": "German"}, "Category": {"_label": "Category", "value": "Legal change"}, "Priority": {"_label": "Priority", "value": "Correction with medium priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "09.03.2004"}, "SAPComponentKey": {"_label": "Component", "value": "IS-H-CM-OUT"}, "SAPComponentKeyText": {"_label": "Component", "value": "Hospital External Data Transmission"}, "SAPComponentPath": [{"_label": "Services &amp; Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs &amp; Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Industry Components", "value": "IS", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'IS*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Hospital", "value": "IS-H", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'IS-H*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Communication", "value": "IS-H-CM", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'IS-H-CM*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Hospital External Data Transmission", "value": "IS-H-CM-OUT", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'IS-H-CM-OUT*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "696417 - IS-H §21: New features for RNAP21K01/F01 for 4.63B/18 and 4.71/10"}, "LongText": {"_label": "Description", "value": "<div id=\"DISCLAIMER\" class=\"sapMMsgStrip sapMMsgStripWarning sapUiSmallMarginTop sapUiMediumMarginBottom\"><div class=\"sapMMsgStripMessage\"><span dir=\"auto\" class=\"sapMText sapUiSelectable sapMTextMaxWidth\" style=\"text-align: left;\"><strong>ATTENTION!</strong> This is a machine-translated document that has not been reviewed for accuracy. To provide feedback regarding the machine translation, please <a style=\"color:var(--sapLinkColor);\" href=\"https://sapinsights.eu.qualtrics.com/jfe/form/SV_6nI2C1MOwEviVQG?NoteNumber=696417&TargetLanguage=EN&Component=IS-H-CM-OUT&SourceLanguage=DE&Priority=03\" target=\"_blank\">click here</a>.<br/>You can access the original document via the following link: <a style=\"color:var(--sapLinkColor);\" href=\"/notes/696417/D\" target=\"_blank\">/notes/696417/D</a>.</span></div></div><div><div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>SAP supports the provision of data according to §21 KHEntgG with the two reports RNAP21K01 (hospital-related data) and RNAP21F01 (medical data for treatment cases).<br />These programs do not currently take into account the special requirements for option hospitals.<br /><br />During the enhancement of the report RNAP21F01, the following corrections were made, among others:</p> <UL><LI>When you execute the program with a large evaluation period, the runtime error SYSTEM_IMODE_TOO_LARGE may occur if you store your files on the R/3 server.</LI></UL> <UL><LI>When you create the ICD file, duplicate diagnoses are removed, even if the secondary diagnoses linked to them are different.</LI></UL> <UL><LI>If a procedure is assigned to the preadmission treatment for a case that is not purely preadmission, this procedure is deleted.</LI></UL> <UL><LI>For purely preadmission cases, the system incorrectly issues the error message &quot;Required field cannot be determined for diagnosis type/ICD-KODE/ICD version&quot; if cases are evaluated in the report for which a lower case number has a later movement date than a larger case number. If you evaluate the affected cases separately, no problems occur.</LI></UL><h3 data-toc-skip class=\"section\" id=\"Weitere Begriffe\">Other Terms</h3><p>§21 P21 DRG FALL</p><h3 data-toc-skip class=\"section\" id=\"Ursache und Voraussetzungen\">Reason and Prerequisites</h3><p>There are legal requirements and program errors.</p><h3 data-toc-skip class=\"section\" id=\"L&ouml;sung\">Solution</h3><p>The following enhancements have been made:</p> <UL><LI>Creation of the new file ABRECHNUNG (RNAP21K01).</LI></UL> <UL><LI>Creation of the new file ENTGELTE (RNAP21F01).</LI></UL> <UL><LI>Adjustments to the creation of the file FALL for option hospitals.</LI></UL> <UL><LI>Adjustments to the creation of the FAB file for option hospitals.</LI></UL> <UL><LI>Only procedures in version 2.1 of OPS-301 are permitted in the OPS file.<br />If you use the enhanced SPC in your institution, this version is also accepted by the DRG data collection point. Since the enhanced SPC is not supported in SAP Patient Management, the system cannot recognize this catalog. You can use the Business Add-In (BAdI) ISH_P21_TRANSFER, method GET_P21_OPS to fill the OPSKAT field yourself in the ISH_P21_OPS structure. In this case, you can ignore the information in the error list that an incorrect OPS version exists.</LI></UL> <UL><LI>You now have the option of removing undesired cases from the evaluation using the BAdI ISH_P21_TRANSFER, method GET_P21_FALL. Previously, you could only change the data in one record of the file FALL.<br />This enables you, among other things, to remove companions from your evaluation that are not medically relevant.<br />For more information, see the documentation for the BAdI.</LI></UL> <UL><LI>With report RNAP21F01, you can now also include cases with a statistics block in your evaluation.</LI></UL> <UL><LI>Program corrections<br /></LI></UL> <p>The changes are available with the following IS-H releases:</p> <UL><LI>4.63B/18</LI></UL> <UL><LI>4.71/10<br /></LI></UL> <p>The delivery in the patches mentioned above represents the recommended delivery method. If you cannot import the patches mentioned above in time, we provide you with an advance delivery using requests for 4.63B/17.<br /><br />For Release 4.63B/17, import the requests from the attachment RNAP21_V2004_463B.zip. For more information, see SAP Notes 13719 and 480180.<br />The attachment cannot be downloaded using OSS, but only from SAP Service Marketplace. To do this, use the alias /notes.<br /><br />SAP supports the provision of the data in the legally required form in accordance with Annex 2 to the agreement in accordance with Sec. 21 Para. 4 and Para. 5 KHEntgG dated February 20, 2003.<br />This documentation does not contain any information about the following questions (as of 10.2.04):</p> <UL><LI>Readmission due to complications (Sec. 8(5) KHEntgG).</LI></UL> <UL><LI>Creation of the FAB file for hospitals that do not bill according to KHentG.<br /></LI></UL> <p>On February 13, 2004, the following specification was made in consultation with the DRG data office:</p> <UL><LI>Readmission cases from 2003 are not grouped together in a logical case, but are transferred as individual cases. The admission reason of a readmission case is &#39;07&#39; and the cases are merged using the patient number.</LI></UL> <UL><LI>The file FAB is created for non-option houses in the same way as for 2002.</LI></UL></div></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Owner", "Value": "<PERSON><PERSON><PERSON><PERSON> (D022511)"}, {"Key": "Processor", "Value": "<PERSON> (D030297)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/**********/D"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "MIME Type", "URL": "URL"}, "Items": [{"FileName": "RNAP21_V2004_463B.zip", "FileSize": "113", "MimeType": "application/x-zip-compressed", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012003146900000026552004&iv_version=0001&iv_guid=C2430BE48B4E9D4A9DB33EE622E0C3A7"}]}, "References": {"_label": "References", "RefTo": {"_label": "This document references", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "URL"}, "Items": [{"RefNumber": "720988", "RefComponent": "IS-H-CM-OUT", "RefTitle": "IS-H DE §21: Transfer Age/Weight for Pre-Inpat. Cases", "RefUrl": "/notes/720988"}, {"RefNumber": "718484", "RefComponent": "IS-H-CM-OUT", "RefTitle": "IS-H DE P21: Corrections for 4.63B/18", "RefUrl": "/notes/718484"}, {"RefNumber": "716815", "RefComponent": "IS-H-CM-OUT", "RefTitle": "IS-H §21: Optional Services Without Charge Type", "RefUrl": "/notes/716815"}, {"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Advance transports to customer (note for customers)", "RefUrl": "/notes/13719"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "URL"}, "Items": [{"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Advance transports to customer (note for customers)", "RefUrl": "/notes/13719 "}, {"RefNumber": "716815", "RefComponent": "IS-H-CM-OUT", "RefTitle": "IS-H §21: Optional Services Without Charge Type", "RefUrl": "/notes/716815 "}, {"RefNumber": "720988", "RefComponent": "IS-H-CM-OUT", "RefTitle": "IS-H DE §21: Transfer Age/Weight for Pre-Inpat. Cases", "RefUrl": "/notes/720988 "}, {"RefNumber": "718484", "RefComponent": "IS-H-CM-OUT", "RefTitle": "IS-H DE P21: Corrections for 4.63B/18", "RefUrl": "/notes/718484 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And the following"}, "Items": [{"SoftwareComponent": "IS-H", "From": "463B", "To": "463B", "Subsequent": ""}, {"SoftwareComponent": "IS-H", "From": "471", "To": "471", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software-Komponentenversion", "SupportPackage": "Support Package", "URL": "URL"}, "Items": [{"SoftwareComponentVersion": "IS-H 463B", "SupportPackage": "SAPKIPHD18", "URL": "/supportpackage/SAPKIPHD18"}, {"SoftwareComponentVersion": "IS-H 471", "SupportPackage": "SAPKIPHE10", "URL": "/supportpackage/SAPKIPHE10"}]}, "SupportPackagePatch": {"_label": "Support Packages and Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "URL"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "URL"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about correction instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisite", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisite", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "URL", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated as helpful", "Helpful-Yes": "0 persons"}, "RatingQuality": {"_label": "Quality Assessment"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document eliminates side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "URL"}, "Items": []}, "SideEffectsSolving": {"_label": "This document causes side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "URL"}, "Items": []}}, "DisclaimerForMachineTranslation": {"Header": "ATTENTION!", "Message": "This is a machine-translated document that has not been reviewed for accuracy.", "Survey": "To provide feedback regarding the machine translation, please <a href=\"https://sapinsights.eu.qualtrics.com/jfe/form/SV_6nI2C1MOwEviVQG?NoteNumber=696417&TargetLanguage=EN&Component=IS-H-CM-OUT&SourceLanguage=DE&Priority=03\" target=\"_blank\">click here</a>.", "Request": "If the quality of the machine translation is not sufficient for your purposes, click here to request a revised version.", "Origin": "You can access the original document via the following link: <a href=\"/notes/696417/D\" target=\"_blank\">/notes/696417/D</a>."}}}}