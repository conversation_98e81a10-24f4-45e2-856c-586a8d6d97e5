{"Request": {"Number": "1118193", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 669, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": false, "url": ""}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001118193?language=E&token=EB44434941B6661404719429D7BB23C3"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001118193", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0001118193/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1118193"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 1}, "Recency": {"_label": "Recency", "key": "", "value": ""}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Legal change"}, "Priority": {"_label": "Priority", "value": "Correction with low priority"}, "Status": {"_label": "Release Status", "value": "To Be Archived"}, "ReleasedOn": {"_label": "Released On", "value": "00.00.0000"}, "SAPComponentKey": {"_label": "Component", "value": "PY-ES"}, "SAPComponentKeyText": {"_label": "Component", "value": "Spain"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Payroll", "value": "PY", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'PY*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Spain", "value": "PY-ES", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'PY-ES*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1118193 - Consult: Seguridad Social - Régimen de Artistas"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>This note informs you about the necessary activities for running the new functionality 'Seguridad Social - R&#x00E9;gimen de Cotizaci&#x00F3;n de los Artistas' within the payroll calculation.<br /></p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>Infotype 0061 MP006100<br />Payroll function ESV00 EBPI0 EPEU1<br />Variable split type G<br />Report RPCTC0E0</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>Legal requirement<br /></p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>This part is divided into a section for System-Administrators and a section for HR- and Payroll-Administrators.<br /></p> <b>1. Activities for SYSTEM-ADMINISTRATORS</b><br /> <p>Please follow the steps in the same sequence as described below.<br /></p> <b>1.1. Availability</b><br /> <p><br />This functionality will be available since ES27.4. Please update your<br />system to this Support Package in order to make it compliant to the<br />Artists' Regime.<br /></p> <b>1.2. New Wage Types</b><br /> <p>The system provides the wage type M80A as a model for creating new ones by copying. To do so, call transaction PU30 (Copy of wage types) to create copies of the wage type M80A as dialogue wagetypes for infotypes 0014 and 0015 in your namespace. All other needed customizing for your new wage type will be replicated, except step (1.2.1).<br /></p> <b>1.2.1. Enabling Wage Type Reading by Payroll Function</b><br /> <p>Once all the new wage types to be assigned to artists are created, it is necessary to insert an entry for each one of them in the view V_T5EU1 in order to make them visible for the Social Insurance payroll function. This is done by assigning each new wage type to the container \"0002\", which is a logical grouping reserved for artists.<br /><br />Call transaction SM30 and insert new entries in the view V_T5EU1 'Contenedores de resultados de n&#x00F3;mina' according to the example below, where your copies of the wage type M80A (for example 980A and 980B) are being assigned to container 0002. You can copy the entry for Contenedor 0002 and wage type M80A.<br />Example:</p> <div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\"><TR><TH ALIGN=LEFT>Contenedor</TH><TH ALIGN=LEFT> N&#x00B0;</TH><TH ALIGN=LEFT> CC-N&#x00F3;mina</TH></TR> <TR><TD>0002 </TD><TD> 01</TD><TD> M80A</TD></TR> <TR><TD>0002 </TD><TD> 02</TD><TD> 980A</TD></TR> <TR><TD>0002 </TD><TD> 03</TD><TD> 980B</TD></TR> </TABLE> <p>(All other fields stay with their initial value)<br /></p> <b>1.3. NISSE for Artists</b><br /> <p>View V_T5E08 'Caracter&#x00ED;sticas dependientes del tiempo de CCCs' - Enter in this table the NISSE for artists. Additionally, you have to set in the field 'Tipo CCC' (TPCCC) the adequate TPCCC ('007' CCC Artistas). All possible values for TPCCC are already delivered in View V_T5ES8.<br /></p> <b>1.3.1. Enabling Employee CCC for Input in Infotype 0061</b><br /> <p>To set an employee as an artist you have to manually modify his or her<br />CCC in infotype 0061. To make it possible, it is necessary to set this<br />field as editable by proceeding as follows:<br /><br />- Enter in transaction SM30 and open the view V_T588M for editing;<br />- Choose module pool MP006100;<br />- Look for the entry that corresponds to the field Q0061-CCCEE and set<br />it as \"Required Field (Obligatorio)\".<br /></p> <b>1.4. Unitary Vs. Global Treatment of Wage Types</b><br /> <p>When informing the amount an artist has earned, the user has the possibility to choose between global treatment and unitary treatment for the selected wage type.<br /><br />Unitary treatment - The amount is daily. For instance, if an artist performs for 5 days and the informed amount is 100, the total earnings will be 500.<br /><br />Global treatment - The informed amount corresponds to the whole period. In the example described above, the total earnings would be 100, while the daily earnings would be 20.<br /><br />The system's default behavior is to adopt the unitary treatment, thus no customizing is required.<br /><br />To change the calculation type for a wage type, enter transaction SM30, open view V_T5E4L for editing and insert an entry for the wage type you wish to configure. In the example below the wage type 980A is being configured to be calculated with global treatment.</p> <div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\"><TR><TH ALIGN=LEFT>CC-n&#x00F3;mina</TH><TH ALIGN=LEFT> Valido de&#x00A0;&#x00A0;-&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;hasta</TH> <TH ALIGN=LEFT> Tratamiento</TH></TR> <TR><TD>980A </TD><TD> 01.01.1900 - 31.12.9999</TD><TD> Global</TD></TR> <TR><TD></TD></TR> </TABLE> <b>1.5. Importing from client 000</b><br /> <p>The delivery class C table entries(i.e.T5A4P) must be syncronized<br />by adjusting based on the client 000 through sm30 -&gt; utilities -&gt;<br />adjustment.</p> <b></b><br /> <p></p> <b>2.1. Treating an Employee as an Artist</b><br /> <p>To treat an employee as an artist you have to set his or her CCC with a value that is already assigned to 'Tipo de CCC' 007 in the view V_T5E08 (see section 1.3).<br /><br />This configuration is done by filling the field 'CCC' in infotype 'Social Insurance Spain' (0061). The value help (F4) shows you all possible values for CCC. The next step is to set a value for the field 'Gr.Cotiz' (Grupo de Cotizaci&#x00F3;n). By pressing F4 you will see all 'Grupos de Cotizaciones' that correspond to 'Tipo de CCC' 007.<br /></p> <b>2.2. Registering Earnings</b><br /> <p>An acting period can be informed either through infotype 'Recurring Payments/Deductions' (0014) or 'Additional Payments' (0015), by using a wage type copied from M80A. This procedure is described below.<br /></p> <b>2.2.1. Recording Payments Using Infotype 0014</b><br /> <p>Besides the wage type, the amount, the number of days and the interval are required to register an earning for an artist through infotype 0014. The informed values will be valid for every month within the interval.<br /></p> <b>2.2.2. Recording Payments Using Infotype 0015</b><br /> <p>Unlike infotype 0014, the Additional Payments infotype is not used for storing monthly data, but to hold information that concerns to a single period. The needed data are the starting date, the amount and the number of days. The system assumes that the employee has worked for all days in a row beginning with the starting date.<br />If the interval spreads across more than one payroll period, the employee will be rejected, since this record will not be detected when the next period is calculated.<br /></p> <b>2.3. Record Overlapping</b><br /> <p>The system supports overlapping records of both infotypes 0014 and 0015, however, with restrictions. To be able to calculate the contribution correctly in this case, one of the conditions below must be obeyed:<br /><br />- The overlapping records must have exactly the same interval, as well as the same number of days;<br /><br />OR<br /><br />- The number of acting days must be equal to the number of days between<br />the record start date and end date.<br /><br />Since infotype 0015 is only valid for a specific date and not an interval there is a special treatment during the payroll calculation. The assumed acting period begins with the date for which the infotype 0015 record is defined. The system assumes that the acting period ends after the number of days given in the record (i.e. acting days is equal to calendar days).<br /><br />Example:<br />Begin date:&#x00A0;&#x00A0;15.08.2007<br />Number/Unit: 5 days<br />Assumed acting period: 15.08.2007 - 19.08.2007<br /></p> <b>2.4. Unitary Vs. Global Treatment of Wage Types</b><br /> <p>Please see section 1.4.<br /></p> <b>2.5. Temporal Discapacity (IT)</b><br /> <p>For artists, IT absences have direct payment behavior. For this reason,<br />a new absence class named \"Artists in IT\" has been created with number<br />7000. Therefore, this absence class must be used to record an IT for an<br />artist.<br /><br />Some absences (IT) must not overlap with the acting periods defined in infotype 0014 or 0015. In case of an overlap the payroll calculation will be rejected for that artist.<br /><br />Additionally, the total number of acting days - field 'Cantidad/unidad' of infotypes 0014 and 0015 - and days of IT - field 'D&#x00ED;as de absentismo' of infotype 2001 - must not exceed the number of calendar days of the payroll period.<br /><br />The example below shows a situation where an artist has an absence due to IT. This situation is valid since there is no overlapping between records of infotypes 2001 and 0014 / 0015.<br /><br />Situation:<br />- An artist is acting from 01.07.2007 until 10.07.2007;<br />- He or she has an IT from 11.07.2007 until 20.07.2007;<br />- Starts to perform again on 21.07.2007 until 30.07.2007.<br /></p> <div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\"><TR><TH ALIGN=LEFT>1st record:</TH><TH ALIGN=LEFT> </TH></TR> <TR><TD>Infotype </TD><TD> 0015</TD></TR> <TR><TD>Valid for: </TD><TD> 01.07.2007</TD></TR> <TR><TD>Amount: </TD><TD> 100 Euro</TD></TR> <TR><TD>Cost center</TD><TD> \"A\"</TD></TR> <TR><TD>10 days of acting</TD></TR> </TABLE> <p></p> <div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\"><TR><TH ALIGN=LEFT>2nd record:</TH><TH ALIGN=LEFT> </TH></TR> <TR><TD>Infotype </TD><TD> 2001</TD></TR> <TR><TD>Valid from:</TD><TD> 11.07.2007</TD></TR> <TR><TD>Valid to: </TD><TD> 20.07.2007</TD></TR> <TR><TD>Cost center</TD><TD> \"A\"</TD></TR> <TR><TD>10 days of absence</TD></TR> </TABLE> <p></p> <div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\"><TR><TH ALIGN=LEFT>3rd record:</TH><TH ALIGN=LEFT></TH></TR> <TR><TD>Infotype </TD><TD> 0014</TD></TR> <TR><TD>Valid from:</TD><TD> 21.07.2007</TD></TR> <TR><TD>Valid to: </TD><TD> 30.07.2007</TD></TR> <TR><TD>Amount:</TD><TD> 80 Euro</TD></TR> <TR><TD>Cost center</TD><TD> \"A\"</TD></TR> <TR><TD>11 days of acting</TD></TR> </TABLE> <p></p> <div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\"><TR><TH ALIGN=LEFT>4th record:</TH><TH ALIGN=LEFT> </TH></TR> <TR><TD>Infotype </TD><TD> 0014</TD></TR> <TR><TD>Valid from:</TD><TD> 21.07.2007</TD></TR> <TR><TD>Valid to: </TD><TD> 30.07.2007</TD></TR> <TR><TD>Amount: </TD><TD> 150 Euro</TD></TR> <TR><TD>Cost center</TD><TD> \"B\"</TD></TR> <TR><TD>11 days of acting</TD></TR> </TABLE> <p><br />Total days (acting plus absences): 30<br /><br />The situation of temporal incapacity of an artist obeys the general regime, except by the fact that the base is calculated by dividing by 365 the annual contribution that corresponds to the year that precedes the event of incapacity. If the artist's relationship with the Social Insurance has not completed one year yet, the base equals the total contributed divided by the number of days of contribution.<br /><br /></p> <b>2.6. Artists Regime In The Payroll</b><br /> <p><br />In the payroll calculation the new wage types<br /></p> <UL><LI>/3SA: Integral earnings, common contingencies</LI></UL> <UL><LI>/3SB: Integral earnings, professional contingencies</LI></UL> <UL><LI>/3SC: Limited earnings, common contingencies</LI></UL> <UL><LI>/3SD: Limited earnings, professional contingencies</LI></UL> <p><br />will be created. The amounts of the wage types /3SC and /3SD may be different from /3SA and /3SB since they can be changed according to the limits specified by the Social Insurance. You can see these values by accessing views V_T5E4A (daily minimum and monthly maximum) and V_T5E4I (daily maximum).<br /><br />Every record of infotypes 0014 and 0015 generates one entry for each one of the wage types above. These wagetypes are always assigned to a variable split of type G.<br />If the total earnings exceed the monthly maximum, negative entries will be inserted to indicate this situation.<br /></p> <b>2.7. The Calculation Method</b><br /> <p>An artist's contribution base equals the amount that he or she earned, unless one of the situations below occurs:<br /></p> <UL><LI>The amount received in a day is less than the daily lower limit: in this case, the amount is raised to the lower limit and stored in wage types /3SC and /3SD.</LI></UL> <UL><LI>The amount received in a day is greater than the daily upper limit: if it occurs, the amount is lowered to the upper limit and stored in wage types /3SC and /3SD.</LI></UL> <UL><LI>The total amount received in a month exceeds the monthly upper limit: In this case another pair of the wage types /3SC and /3SD with negative amount and also with a variable split of type G will be created. The sum of the amounts of wage types /3SC or /3SD equals the monthly upper limit.<br /></LI></UL> <p>The wage types /3SA and /3SB will always store the full amount earned by the artist, while /342 and /343 will store the contribution base itself. The contribution base will never be greater than the monthly maximum.<br /></p> <b>2.8. TC</b><br /> <p>The calculation of an artist's contribution to the Social Insurance affects the report that generates FAN files. When the contribution of an artist is processed, one DAT segment is generated for each acting period, which means that every pair of the wage types /3SC and&#x00A0;&#x00A0;/3SD generates a DAT segment. However, if the artist's earnings exceed the monthly upper limit, only one DAT segment with the overall data will be added to the file.<br /></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "PA-PA-ES (Spain)"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON> (I811660)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (I811660)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001118193/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001118193/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001118193/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001118193/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001118193/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001118193/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001118193/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001118193/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001118193/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_HR", "From": "500", "To": "500", "Subsequent": ""}, {"SoftwareComponent": "SAP_HR", "From": "600", "To": "600", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}