{"Request": {"Number": "724545", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 242, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000015641812017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000724545?language=E&token=ADAE6C29C99ACF375677844EBB15834F"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000724545", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000724545/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "724545"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 18}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Help for error analysis"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "26.06.2013"}, "SAPComponentKey": {"_label": "Component", "value": "BC-DB-ORA"}, "SAPComponentKeyText": {"_label": "Component", "value": "Oracle"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Basis Components", "value": "BC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Database Interface, Database Platforms", "value": "BC-DB", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-DB*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Oracle", "value": "BC-DB-ORA", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-DB-ORA*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "724545 - Adjusting the CBO statistics manually using DBMS_STATS"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>The Cost Based Optimizer (CBO) uses an unsuitable access path. This causes a longer runtime and higher system load.<br /></p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p></p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>If performance problems occur because the CBO does not use the best access path, you can use various solutions, for example:</p> <UL><LI>You can create more exact statistics.</LI></UL> <UL><LI>You can create histograms (Note 797629)</LI></UL> <UL><LI>You can specify hints.</LI></UL> <UL><LI>You can adjust the statistics manually with DBMS_STATS.<br /></LI></UL> <p>Before you adjust the statistics using DBMS_STATS, check whether there are other options to solve the problem.<br /><br />Refer to Note 176754 for a description of typical situations in which it makes sense to adjust the statistics. Refer to Note 588668 for background information on database statistics. Refer to Note 448380 for information about the DBMS_STATS functions. Note 750631 contains details on the cost accounting of the CBOs.<br /><br />Note 1020260 contains a script for the implementation of CBO statistics for critical tables in the SAP environment.<br /></p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>Use the following commands to adjust the statistical values for tables, indexes or columns:<br /><br />SET_COLUMN_STATS<br />SET_INDEX_STATS<br />SET_TABLE_STATS<br /><br />The following gives you an overview over typical statistical changes to optimize the access path:</p> <UL><LI>Increasing or decreasing the distinct values of a column:</LI></UL> <UL><UL><LI>Oracle DDIC reference: NUM_DISTINCT in DBA_TAB_COLUMNS;</LI></UL></UL> <UL><UL><LI>Result: If you increase the distinct values, an index access using this column becomes more attractive to the system. If you are using a Join, a Nested Loop Join with an access point using this index becomes more likely. If you decrease the distinct values, an index access using this column becomes less attractive to the system (which is useful if several indexes have a costing of 1 and the incorrect index is used; see Note 176754)</LI></UL></UL> <UL><UL><LI>Call:<br /><br />DBMS_STATS.SET_COLUMN_STATS('&lt;owner&gt;', '\"&lt;table&gt;\"',<br />&#x00A0;&#x00A0;'\"&lt;column&gt;\"', DISTCNT =&gt; &lt;new_value&gt;, NO_INVALIDATE=&gt;FALSE);</LI></UL></UL> <UL><UL><LI>Caution: After you execute this command, the column may lose histogram statistics that may exist.</LI></UL></UL> <UL><UL><LI>Note: With Oracle 10g or higher, the DISTINCT_KEYS of the used index are a limitation and may also need to be increased.<br /><br />DBMS_STATS.SET_INDEX_STATS('&lt;owner&gt;', '\"&lt;index&gt;\"',<br />&#x00A0;&#x00A0;NUMDIST =&gt; &lt;new_value&gt;, NO_INVALIDATE=&gt;FALSE);</LI></UL></UL> <UL><LI>Reducing the clustering factor of an index:</LI></UL> <UL><UL><LI>Oracle DDIC reference: CLUSTERING_FACTOR in DBA_INDEXES;</LI></UL></UL> <UL><UL><LI>Result: The access with index range scans on this index becomes more attractive to the system.</LI></UL></UL> <UL><UL><LI>Call:<br /><br />DBMS_STATS.SET_INDEX_STATS('&lt;owner&gt;', '\"&lt;index&gt;\"',<br />&#x00A0;&#x00A0;CLSTFCT =&gt; &lt;new_value&gt;, NO_INVALIDATE=&gt;FALSE);</LI></UL></UL> <UL><LI>Reducing the number of rows in a table:</LI></UL> <UL><UL><LI>ORACLE DDIC reference: NUM_ROWS in DBA_TABLES;</LI></UL></UL> <UL><UL><LI>Result: The access on a table becomes more attractive. If you are using Joins, a Nested Loop Join with an access point using this table become more likely.</LI></UL></UL> <UL><UL><LI>Call:<br /><br />DBMS_STATS.SET_TABLE_STATS('&lt;owner&gt;', '\"&lt;table&gt;\"',<br />&#x00A0;&#x00A0;NUMROWS =&gt; &lt;new_value&gt;, NO_INVALIDATE=&gt;FALSE);<br /></LI></UL></UL> <p>Here is a complete overview of the statistics values to which a new value can be assigned using \"=&gt;\" in the last call parameter:</p> <UL><LI>Column statistics: DISTCNT, DENSITY, NULLCNT, AVGCLEN</LI></UL> <UL><LI>Index statistics: NUMROWS, NUMLBLKS, NUMDIST, AVGLBLK, AVGDBLK, CLSTFCT, INDLEVEL</LI></UL> <UL><LI>Table statistics: NUMROWS, NUMBLKS, AVGRLEN<br /></LI></UL> <p>Refer to the Oracle online documentation for further information.<br /><br />Only adjust the statistics after you have performed a thorough check. In a non-production environment, check whether the changed statistics have unwanted side effects before you transfer the changes to the production system. Change the statistical values as little as possible to avoid superfluous side effects. To determine a good statistical value, you may have to perform a large number of tests with different values.<br /><br />To retain the changed statistics permanently, you must prevent BRCONNECT from overwriting the statistics during its statistical runs. To do so, make sure that you use BRCONNECT 7.10 (25) or higher and fix the changed statistic value in accordance with Note 1374807.<br /><br />Caution: If you use a BRCONNECT Version 7.10 (24) or lower, you may not use Note 1374807 because the older BRCONNECT will delete the CBO statistics otherwise. In this case, you must create an entry with ACTIV=I in the table DBSTATC (Note 106047) instead, and (as of Oracle 10g) you must lock the statistics at Oracle level using DBMS_STATS.LOCK_TABLE_STATS.</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D030484)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D030484)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "J", "TranslationLanguage": "日本語", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000724545/J"}, {"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000724545/D"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000724545/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000724545/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000724545/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000724545/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000724545/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000724545/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000724545/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "852365", "RefComponent": "SRM-EBP-PD", "RefTitle": "Performance of BBP_PDVIEW_LIST / BBP_PDHGP", "RefUrl": "/notes/852365"}, {"RefNumber": "799649", "RefComponent": "PM-WOC-M<PERSON>", "RefTitle": "Runtime IQ03 (history) long SELECT to VIAUFKST", "RefUrl": "/notes/799649"}, {"RefNumber": "772497", "RefComponent": "BC-DB-ORA", "RefTitle": "FAQ: Oracle Hints", "RefUrl": "/notes/772497"}, {"RefNumber": "766349", "RefComponent": "BC-DB-ORA", "RefTitle": "FAQ: Oracle SQL optimization", "RefUrl": "/notes/766349"}, {"RefNumber": "756335", "RefComponent": "BC-DB-ORA", "RefTitle": "Statistics in tables w/ heavily fluctuating volumes of data", "RefUrl": "/notes/756335"}, {"RefNumber": "750631", "RefComponent": "BC-DB-ORA", "RefTitle": "Rules of thumb for cost calculation of CBO", "RefUrl": "/notes/750631"}, {"RefNumber": "744315", "RefComponent": "PS-IS-LDB", "RefTitle": "LDB PSJ: Elimination of 'rule' hints Oracle", "RefUrl": "/notes/744315"}, {"RefNumber": "735510", "RefComponent": "PS-IS-LDB", "RefTitle": "LDB PSJ: Elimination of the Hints Oracle 'rule' statement", "RefUrl": "/notes/735510"}, {"RefNumber": "588668", "RefComponent": "BC-DB-ORA", "RefTitle": "FAQ: Database statistics", "RefUrl": "/notes/588668"}, {"RefNumber": "448380", "RefComponent": "BC-DB-ORA", "RefTitle": "Information: Oracle Package DBMS_STATS", "RefUrl": "/notes/448380"}, {"RefNumber": "1374807", "RefComponent": "BC-DB-ORA", "RefTitle": "Freezing single kinds of statistics", "RefUrl": "/notes/1374807"}, {"RefNumber": "106047", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "DB21: Customizing the DBSTATC", "RefUrl": "/notes/106047"}, {"RefNumber": "1020260", "RefComponent": "BC-DB-ORA", "RefTitle": "Delivery of Oracle statistics (Oracle >= 10g)", "RefUrl": "/notes/1020260"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2957955", "RefComponent": "BC-DB-ORA", "RefTitle": "Wrong index used for table INOB - NetWeaver", "RefUrl": "/notes/2957955 "}, {"RefNumber": "2482775", "RefComponent": "SV-PERF", "RefTitle": "Optimizing Expensive SQL Statements - Guided Answers", "RefUrl": "/notes/2482775 "}, {"RefNumber": "1020260", "RefComponent": "BC-DB-ORA", "RefTitle": "Delivery of Oracle statistics (Oracle >= 10g)", "RefUrl": "/notes/1020260 "}, {"RefNumber": "1757248", "RefComponent": "CRM-BTX-SVO", "RefTitle": "Service Order Template Search Performance", "RefUrl": "/notes/1757248 "}, {"RefNumber": "766349", "RefComponent": "BC-DB-ORA", "RefTitle": "FAQ: Oracle SQL optimization", "RefUrl": "/notes/766349 "}, {"RefNumber": "106047", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "DB21: Customizing the DBSTATC", "RefUrl": "/notes/106047 "}, {"RefNumber": "588668", "RefComponent": "BC-DB-ORA", "RefTitle": "FAQ: Database statistics", "RefUrl": "/notes/588668 "}, {"RefNumber": "1062361", "RefComponent": "IS-R-BD-OAP", "RefTitle": "OAPC: Performance Reading Table MARA", "RefUrl": "/notes/1062361 "}, {"RefNumber": "1374807", "RefComponent": "BC-DB-ORA", "RefTitle": "Freezing single kinds of statistics", "RefUrl": "/notes/1374807 "}, {"RefNumber": "448380", "RefComponent": "BC-DB-ORA", "RefTitle": "Information: Oracle Package DBMS_STATS", "RefUrl": "/notes/448380 "}, {"RefNumber": "756335", "RefComponent": "BC-DB-ORA", "RefTitle": "Statistics in tables w/ heavily fluctuating volumes of data", "RefUrl": "/notes/756335 "}, {"RefNumber": "772497", "RefComponent": "BC-DB-ORA", "RefTitle": "FAQ: Oracle Hints", "RefUrl": "/notes/772497 "}, {"RefNumber": "750631", "RefComponent": "BC-DB-ORA", "RefTitle": "Rules of thumb for cost calculation of CBO", "RefUrl": "/notes/750631 "}, {"RefNumber": "744315", "RefComponent": "PS-IS-LDB", "RefTitle": "LDB PSJ: Elimination of 'rule' hints Oracle", "RefUrl": "/notes/744315 "}, {"RefNumber": "852365", "RefComponent": "SRM-EBP-PD", "RefTitle": "Performance of BBP_PDVIEW_LIST / BBP_PDHGP", "RefUrl": "/notes/852365 "}, {"RefNumber": "799649", "RefComponent": "PM-WOC-M<PERSON>", "RefTitle": "Runtime IQ03 (history) long SELECT to VIAUFKST", "RefUrl": "/notes/799649 "}, {"RefNumber": "735510", "RefComponent": "PS-IS-LDB", "RefTitle": "LDB PSJ: Elimination of the Hints Oracle 'rule' statement", "RefUrl": "/notes/735510 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": []}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}