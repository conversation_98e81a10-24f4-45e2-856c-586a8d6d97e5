{"Request": {"Number": "332677", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 381, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000014895852017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000332677?language=E&token=DE81DE430023B8A859D26AFE878244D7"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000332677", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000332677/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "332677"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 18}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Consulting"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "07.02.2012"}, "SAPComponentKey": {"_label": "Component", "value": "BC-DB-ORA"}, "SAPComponentKeyText": {"_label": "Component", "value": "Oracle"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Basis Components", "value": "BC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Database Interface, Database Platforms", "value": "BC-DB", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-DB*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Oracle", "value": "BC-DB-ORA", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-DB-ORA*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "332677 - Rebuilding fragmented indexes"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>Indexes are fragmented and require an unnecessarily large amount of space on the database.<br /></p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p></p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>If, on the basis of an index fragmentation analysis (for example, according to Note 771929) you decide to defragment indexes, there are different approaches available. The following section deals with these approaches and their advantages and disadvantages.<br /></p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>The essential alternatives to defragmenting indexes are:</p> <OL>1. REBUILD</OL> <UL><LI>Mechanism</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;In a REBUILD an index is completely rebuilt as a new segment. </p> <UL><LI>Advantages</LI></UL> <UL><UL><LI>Optimal defragmentation is available.</LI></UL></UL> <UL><UL><LI>Parallel processing based on parallel DDL (Note 651060) is possible.</LI></UL></UL> <UL><UL><LI>If required, storage parameters can be changed.</LI></UL></UL> <UL><UL><LI>The index can be moved to another tablespace.</LI></UL></UL> <UL><LI>Disadvantages</LI></UL> <UL><UL><LI>There is a longer runtime than with COALESCE.</LI></UL></UL> <UL><UL><LI>There is an increased storage requirement in the tablespace during the REBUILD (as the original and new index exist in parallel during this time).</LI></UL></UL> <UL><UL><LI>There may be problems as described in Note 682926.</LI></UL></UL> <UL><LI>Options</LI></UL> <UL><UL><LI>ONLINE: The index is built without keeping a permanent lock. However, critical locks are still possible (Note 682926). Complete tables must be scanned when the index is being rebuilt (during the OFFLINE rebuild \"only\" the source index has to be scanned).</LI></UL></UL> <UL><UL><LI>NOLOGGING: Changes are not logged in the redo logs (Note 547464).</LI></UL></UL> <UL><UL><LI>PARALLEL: The index build is distributed to several parallel execution slaves. When you use this option, NOPARALLEL must be used to undo the parallelism (Note 651060) after the REBUILD is completed. BRSPACE does this automatically.</LI></UL></UL> <UL><UL><LI>COMPUTE STATISTICS: New CBO statistics are created for the index. As of Oracle 10g and when you use BRSPACE, this is the case by default.</LI></UL></UL> <UL><LI>Call</LI></UL> <UL><UL><LI>SQLPLUS:<br /><br />ALTER INDEX \"&lt;index_name&gt;\" REBUILD [ONLINE] [NOLOGGING]<br />[PARALLEL &lt;degree&gt;] [COMPUTE STATISTICS]<br />[REBUILD PARTITION \"&lt;partition_name&gt;\"];</LI></UL></UL> <UL><UL><LI>BRSPACE:<br /><br />brspace -f idrebuild -i &lt;index_name&gt; [-NBR] [-e &lt;degree&gt;]<br />[-a rebpart -ip &lt;partition_name&gt;]</LI></UL></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;The \"-NBR\" option ensures that NOLOGGING is used during the REBUILD. The \"-e\"- option provides parallel processing. You can use \"-a rebpart -ip\" as of BRSPACE 7.10 (24) to restructure individual index partitions (Note 1360603).<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;By default, indexes are rebuilt online. Under the patch prerequiste of Note 1080376 indexes can also be rebuilt offline using \"-m offline\".</p> <UL><UL><LI>SAP:<br /><br />Report RSANAORA<br />Report RSORAISQN (Note 979054)</LI></UL></UL> <OL>2. COALESCE</OL> <UL><LI>Mechanism</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Data in the leaf blocks under the same branch blocks is defragmented; the index in relation to the root and branch blocks remains unchanged. However, the number of leaf blocks may sink dramatically.</p> <UL><LI>Advantages</LI></UL> <UL><UL><LI>There is no lock.</LI></UL></UL> <UL><UL><LI>No additional space is required in the tablespace.</LI></UL></UL> <UL><UL><LI>It is relatively fast.</LI></UL></UL> <UL><LI>Disadvantages</LI></UL> <UL><UL><LI>There is no defragmentation in the root and branch block area.</LI></UL></UL> <UL><UL><LI>The index cann be moved to another tablespace.</LI></UL></UL> <UL><LI>Options</LI></UL> <UL><UL><LI>PARALLEL: COALESCE is distributed to several parallel execution slaves. When you use this option, you must use NOPARALLEL to undo the parallelism (Note 651060) after COALESCE is completed. BRSPACE executes this automatically.</LI></UL></UL> <UL><LI>Call</LI></UL> <UL><UL><LI>SQLPLUS:<br /><br />ALTER INDEX \"&lt;index_name&gt;\" COALESCE;</LI></UL></UL> <UL><UL><LI>BRSPACE:<br /><br />brspace -f idalter -i &lt;index_name&gt; -a coalesce [-d &lt;degree&gt;]</LI></UL></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;You can use the \"-d\" option to define the degree of parallelism of the operation COALESCE.</p> <UL><UL><LI>SAP:<br /><br />Report RSORAISQN (Note 979054)</LI></UL></UL> <OL>3. DROP / CREATE</OL> <UL><LI>Mechanism</LI></UL> <UL><UL><LI>DROP is used first to completely delete the index and then CREATE rebuilds it.</LI></UL></UL> <UL><LI>Advantages</LI></UL> <UL><UL><LI>There are none.</LI></UL></UL> <UL><LI>Disadvantages</LI></UL> <UL><UL><LI>There is a longer runtime than with COALESCE.</LI></UL></UL> <UL><UL><LI>There is a danger of duplicate key records if primary indexes are dropped during live operation.</LI></UL></UL> <UL><UL><LI>There may be problems as described in Note 682926.</LI></UL></UL> <UL><LI>Conclusion</LI></UL> <UL><UL><LI>Due to the significant disadvantages you should generally not use DROP and CREATE.<br /></LI></UL></UL></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D030484)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D030484)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000332677/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000332677/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000332677/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000332677/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000332677/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000332677/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000332677/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000332677/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000332677/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "979054", "RefComponent": "BC-DB-ORA", "RefTitle": "RSORAISQN", "RefUrl": "/notes/979054"}, {"RefNumber": "852189", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/852189"}, {"RefNumber": "821687", "RefComponent": "BC-DB-ORA", "RefTitle": "FAQ: Space utilization and fragmentation in Oracle", "RefUrl": "/notes/821687"}, {"RefNumber": "771929", "RefComponent": "BC-DB-ORA", "RefTitle": "FAQ: Index fragmentation", "RefUrl": "/notes/771929"}, {"RefNumber": "706478", "RefComponent": "HAN-DB", "RefTitle": "Preventing Basis tables from increasing considerably", "RefUrl": "/notes/706478"}, {"RefNumber": "682926", "RefComponent": "BC-DB-ORA", "RefTitle": "Composite SAP note: Problems with \"create/rebuild index\"", "RefUrl": "/notes/682926"}, {"RefNumber": "651060", "RefComponent": "BC-DB-ORA", "RefTitle": "FAQ: Oracle Parallel Execution", "RefUrl": "/notes/651060"}, {"RefNumber": "618868", "RefComponent": "BC-DB-ORA", "RefTitle": "FAQ: Oracle performance", "RefUrl": "/notes/618868"}, {"RefNumber": "541538", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "FAQ: Reorganization", "RefUrl": "/notes/541538"}, {"RefNumber": "459199", "RefComponent": "CRM-MW", "RefTitle": "Measures during the Euro currency conversion", "RefUrl": "/notes/459199"}, {"RefNumber": "435125", "RefComponent": "BC-DB-ORA", "RefTitle": "Poor performance of tables used by CRM (Oracle only)", "RefUrl": "/notes/435125"}, {"RefNumber": "369123", "RefComponent": "BC-DB-ORA", "RefTitle": "ORA-600 [6050] or ORA-600 [25012]", "RefUrl": "/notes/369123"}, {"RefNumber": "1597364", "RefComponent": "BW-BCT-GEN", "RefTitle": "FAQ: BW-BCT: Extraction performance in source system", "RefUrl": "/notes/1597364"}, {"RefNumber": "1485841", "RefComponent": "BC-DB-ORA", "RefTitle": "ALTER INDEX REBUILD does not backup old index statistics", "RefUrl": "/notes/1485841"}, {"RefNumber": "147139", "RefComponent": "CO-PA-TO", "RefTitle": "Build.summarization levels CO-PA is slow/terminates", "RefUrl": "/notes/147139"}, {"RefNumber": "1360603", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Reorganization and rebuild of individual partitions", "RefUrl": "/notes/1360603"}, {"RefNumber": "1080376", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Enhancements for reorganization and rebuild", "RefUrl": "/notes/1080376"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2931495", "RefComponent": "BC-DB-ORA", "RefTitle": "Program RSANAORA failed with DBIF_DSQL2_OBJ_UNKNOWN runtime error", "RefUrl": "/notes/2931495 "}, {"RefNumber": "2441059", "RefComponent": "CRM-BTX-ANA-RFW", "RefTitle": "Performance Improvement Suggestions when Searching One Order transactions", "RefUrl": "/notes/2441059 "}, {"RefNumber": "618868", "RefComponent": "BC-DB-ORA", "RefTitle": "FAQ: Oracle performance", "RefUrl": "/notes/618868 "}, {"RefNumber": "821687", "RefComponent": "BC-DB-ORA", "RefTitle": "FAQ: Space utilization and fragmentation in Oracle", "RefUrl": "/notes/821687 "}, {"RefNumber": "706478", "RefComponent": "HAN-DB", "RefTitle": "Preventing Basis tables from increasing considerably", "RefUrl": "/notes/706478 "}, {"RefNumber": "541538", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "FAQ: Reorganization", "RefUrl": "/notes/541538 "}, {"RefNumber": "1597364", "RefComponent": "BW-BCT-GEN", "RefTitle": "FAQ: BW-BCT: Extraction performance in source system", "RefUrl": "/notes/1597364 "}, {"RefNumber": "682926", "RefComponent": "BC-DB-ORA", "RefTitle": "Composite SAP note: Problems with \"create/rebuild index\"", "RefUrl": "/notes/682926 "}, {"RefNumber": "771929", "RefComponent": "BC-DB-ORA", "RefTitle": "FAQ: Index fragmentation", "RefUrl": "/notes/771929 "}, {"RefNumber": "1485841", "RefComponent": "BC-DB-ORA", "RefTitle": "ALTER INDEX REBUILD does not backup old index statistics", "RefUrl": "/notes/1485841 "}, {"RefNumber": "1360603", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Reorganization and rebuild of individual partitions", "RefUrl": "/notes/1360603 "}, {"RefNumber": "1080376", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Enhancements for reorganization and rebuild", "RefUrl": "/notes/1080376 "}, {"RefNumber": "651060", "RefComponent": "BC-DB-ORA", "RefTitle": "FAQ: Oracle Parallel Execution", "RefUrl": "/notes/651060 "}, {"RefNumber": "979054", "RefComponent": "BC-DB-ORA", "RefTitle": "RSORAISQN", "RefUrl": "/notes/979054 "}, {"RefNumber": "834118", "RefComponent": "BC-MID-ALE", "RefTitle": "RBDCPIDXRE in building online indexes", "RefUrl": "/notes/834118 "}, {"RefNumber": "435125", "RefComponent": "BC-DB-ORA", "RefTitle": "Poor performance of tables used by CRM (Oracle only)", "RefUrl": "/notes/435125 "}, {"RefNumber": "147139", "RefComponent": "CO-PA-TO", "RefTitle": "Build.summarization levels CO-PA is slow/terminates", "RefUrl": "/notes/147139 "}, {"RefNumber": "459199", "RefComponent": "CRM-MW", "RefTitle": "Measures during the Euro currency conversion", "RefUrl": "/notes/459199 "}, {"RefNumber": "369123", "RefComponent": "BC-DB-ORA", "RefTitle": "ORA-600 [6050] or ORA-600 [25012]", "RefUrl": "/notes/369123 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": []}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}