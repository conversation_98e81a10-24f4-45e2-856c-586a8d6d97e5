{"Request": {"Number": "145850", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 3268, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000014663032017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000145850?language=E&token=A4D625C0FD30663FBACCBAE8C3446621"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000145850", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000145850/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "145850"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 173}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Program error"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "25.06.2002"}, "SAPComponentKey": {"_label": "Component", "value": "IS-OIL-BC"}, "SAPComponentKeyText": {"_label": "Component", "value": "Please use component BC-UPG-ADDON"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Industry-Specific Components", "value": "IS", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'IS*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Oil", "value": "IS-OIL", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'IS-OIL*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Please use component BC-UPG-ADDON", "value": "IS-OIL-BC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'IS-OIL-BC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "145850 - Order of IS-OIL notes 4.0b on basis R/3 4.0B (SP)"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>**********************************************************************<br />* WARNING: This is an IS-OIL-specific note. If you DON'T have IS-Oil *<br />* installed on your system, this note does not apply to you. If this *<br />* note is applied and you do not have IS-Oil installed, you could&#x00A0;&#x00A0;&#x00A0;&#x00A0;*<br />* cause serious damage to your system.&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; *<br />**********************************************************************<br /><br />**********************************************************************<br />* this is an IS-OIL-specific NOTE FOR CUSTOMERS USING SUPPORT&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;*<br />* PACKAGES (SP) instead of Legal Change Patches (LCP). If you apply&#x00A0;&#x00A0;*<br />* LCPs to your system, see note 145854.<br />* For details about LCPs see SAPNet note 86241 and notes cited&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; *<br />* therein.&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; *<br />**********************************************************************<br /></p> <b>List of important changes:</b><br /> <p>FEBRUARY 2000:<br />This note has been enhanced for each transport by the<br />following information:<br />&#x00A0;&#x00A0; * 'X' means, that additional manual steps have to be performed<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0; after the implementation of the transport (please refer to<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0; the corresponding note).<br />&#x00A0;&#x00A0; * '-' means, that no additional manual steps are neccessary<br /><br />MARCH 2002:<br />Please apply the corrections&#x00A0;&#x00A0;named in this Note with the tp option -Dclientcascade=yes<br />Example: tp addtobuffer &lt;transport&gt; &lt;SID&gt;<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;tp import&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&lt;transport&gt; &lt;SID&gt; -Dclientcascade=yes<br /><br />To avoid conflicts between different oil fixes, we have written this common note, which describes the IS-OIL US/DS fixes available for those systems, which have installed IS-OIL 4.0b (based on R/3 4.0B). This SAPNet Note is updated on a regularly basis (normally once a week). There might exist further brand new OIL corrections not yet mentioned in this Note (even at the direct update time). This is caused by the following problem:<br />The creation of corrections is a sequential process in our support system. Whenever a correction is created and released after a correction, which has not yet been released for customers it is not possible to update the sequence note directly.<br />In case you are not directly affected by such a current correction (for example: the correction has not been created because of a SAPNet Request raised by you) it is recommended to wait with the import of the correction until it is mentioned in this sequence note.</p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>IS-Oil, Industry Solution, SAPSERV, IS-OIL correction transports</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>The transports/fixes mentioned here can only be applied to IS-OIL systems based on Core R/3 Release 4.0B.<br />If in the following list a transport request A is replaced by transport request B, this means:<br />&#x00A0;&#x00A0; 1. Ignore transport A and do NOT import it.<br />&#x00A0;&#x00A0; 2. The right sequence for the import of transport B is given<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;in the list and it must NOT be imported at the point where<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;transport A is mentioned.<br />&#x00A0;&#x00A0; 3. If you have already imported transport A just proceed with<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;importing further fixes and don&#x00B4;t forget to import B.<br /><br />If in the following list a transport request of a CRT (e.g. SAPKI40C15)<br />is mentioned, proceed in the following way:<br />&#x00A0;&#x00A0;1. If not already carried out, apply those Support Packages which<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0; were released since the last CRT was installed and which had<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0; no conflicts with IS-Oil. (i.e. no CRT exist for these Support<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0; Packages).<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0; Support Packages without CRTs do not conflict with IS-Oil and<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0; therefore they only have to be applied in the correct Support<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0; Package sequence.<br />&#x00A0;&#x00A0;2. Apply the Support Package that corresponds to the CRT mentioned<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0; in the list together with the CRT (bound together in one (!)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0; Patch-Queue).<br /><br />NEW PROCEDURE FOR APPLYING CORRECTIONS<br />--------------------------------------<br />To improve the process of applying corrections we have included all single corrections released between CRT n and CRT n+1 into CRT n+1. Therefore, it is recommended to apply ONLY SPs and CRTs and NOT single corrections. Single corrections should only be applied in exceptional circumstances, i.e. that the correction is not yet available as part of a CRT.<br /><br />The possible situations are explained with the following examples:<br /><br />1. Example A (You don`t have to apply any special corrections):<br />&#x00A0;&#x00A0; Just apply the SPs together with the CRTs with the transaction<br />&#x00A0;&#x00A0; SPAM. Do NOT apply any single correction.<br /><br />2. Example B (You must apply a `special correction` and cannot wait<br />&#x00A0;&#x00A0; until the next CRT n+1 is released)<br />&#x00A0;&#x00A0; As prerequisite you must install all SPs and CRTs up to the last<br />&#x00A0;&#x00A0; released CRT n. Afterwards it is mandatory to apply all corrections,<br />&#x00A0;&#x00A0; which have been released after this CRT n, up to the required<br />&#x00A0;&#x00A0; `special correction` as mentioned in this sequence note. When CRT<br />&#x00A0;&#x00A0; n+1 is released later on, just proceed by applying SP n+1 and the<br />&#x00A0;&#x00A0; corresponding CRT n+1.<br /><br />Additional Remark:<br />------------------<br />Special single corrections may have the need to perform manual steps in addition to the import of the correction transport. If this is the case it is described in the SAPNet Note mentioned together with the transport request in the list below.<br />This is also valid for applying IS-OIL CRT&#x00B4;s for Support Packages or Legal Change Patches. These CRT&#x00B4;s also do not include the manual steps necessary for the included single corrections.</p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>transport&#x00A0;&#x00A0;&#x00A0;&#x00A0;date&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;appl.area&#x00A0;&#x00A0;&#x00A0;&#x00A0; note<br />==========&#x00A0;&#x00A0; ==================&#x00A0;&#x00A0;&#x00A0;&#x00A0;==========&#x00A0;&#x00A0;&#x00A0;&#x00A0;===========</p> <UL><LI>CRT 11:</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;!!!! CAUTION: Read SAPNet Note 149943 BEFORE applying SP 11 !!!!<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;SAPKI40C11&#x00A0;&#x00A0;27 Apr 1999&#x00A0;&#x00A0;15:11&#x00A0;&#x00A0;&#x00A0;&#x00A0;CRT11&#x00A0;&#x00A0; 149943</p> <UL><LI>SOEK000030&#x00A0;&#x00A0;30 Apr 1999&#x00A0;&#x00A0;13:50&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/HPM&#x00A0;&#x00A0;0067261&#x00A0;&#x00A0;repl. by SOEK000040</LI></UL> <UL><LI>SOEK000025&#x00A0;&#x00A0; 30 Apr 1999&#x00A0;&#x00A0;16:57&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;0150318&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK000040&#x00A0;&#x00A0;&#x00A0;&#x00A0;3 May 1999&#x00A0;&#x00A0;09:30&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/HPM&#x00A0;&#x00A0;0067261&#x00A0;&#x00A0;repl. by SOEK000118</LI></UL> <UL><LI>SOEK000065&#x00A0;&#x00A0; 12 May 1999&#x00A0;&#x00A0;10:36&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;0151721&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK000084&#x00A0;&#x00A0; 12 May 1999&#x00A0;&#x00A0;15:29&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;0152285&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK000058&#x00A0;&#x00A0; 17 May 1999&#x00A0;&#x00A0;14:56&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TD&#x00A0;&#x00A0; 0149493&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK000100&#x00A0;&#x00A0; 18 May 1999&#x00A0;&#x00A0;10:37&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/HPM&#x00A0;&#x00A0;0152777&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK000118&#x00A0;&#x00A0;18 May 1999&#x00A0;&#x00A0;16:53&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/HPM&#x00A0;&#x00A0;0067261&#x00A0;&#x00A0;repl. by SOEK000182</LI></UL> <UL><LI>CRT 12:</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;!!!! CAUTION: Read SAPNet Note 154962 BEFORE applying SP 12 !!!!<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;SAPKI40C12&#x00A0;&#x00A0;02 Jun 1999&#x00A0;&#x00A0;08:55&#x00A0;&#x00A0;&#x00A0;&#x00A0;CRT12&#x00A0;&#x00A0; 154962</p> <UL><LI>CRT 13:</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;!!!! CAUTION: Read SAPNet Note 154962 BEFORE applying SP 13 !!!!<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;SAPKI40C13&#x00A0;&#x00A0;02 Jun 1999&#x00A0;&#x00A0;08:56&#x00A0;&#x00A0;&#x00A0;&#x00A0;CRT13&#x00A0;&#x00A0; 154962</p> <UL><LI>SOEK000184&#x00A0;&#x00A0;&#x00A0;&#x00A0;2 Jun 1999&#x00A0;&#x00A0;11:53&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/DS&#x00A0;&#x00A0; 0155337&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK000192&#x00A0;&#x00A0;&#x00A0;&#x00A0;2 Jun 1999&#x00A0;&#x00A0;11:55&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/DS&#x00A0;&#x00A0; 0155337&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK000182&#x00A0;&#x00A0;&#x00A0;&#x00A0;1 Jun 1999&#x00A0;&#x00A0;11:54&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/HPM&#x00A0;&#x00A0;0067261&#x00A0;&#x00A0;repl. by SOEK000201</LI></UL> <UL><LI>SOEK000048&#x00A0;&#x00A0;&#x00A0;&#x00A0;7 Jun 1999&#x00A0;&#x00A0;16:16&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/MAP&#x00A0;&#x00A0;0154538&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK000190&#x00A0;&#x00A0;&#x00A0;&#x00A0;8 Jun 1999&#x00A0;&#x00A0;14:52&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TD&#x00A0;&#x00A0; 0154790&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK000178&#x00A0;&#x00A0;&#x00A0;&#x00A0;8 Jun 1999&#x00A0;&#x00A0;16:52&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TD&#x00A0;&#x00A0; 0154841&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK000152&#x00A0;&#x00A0;&#x00A0;&#x00A0;9 Jun 1999&#x00A0;&#x00A0;10:52&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;0154470&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK000199&#x00A0;&#x00A0;&#x00A0;&#x00A0;9 Jun 1999&#x00A0;&#x00A0;14:33&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TD&#x00A0;&#x00A0; 0155661&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK000169&#x00A0;&#x00A0;&#x00A0;&#x00A0;9 Jun 1999&#x00A0;&#x00A0;14:21&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;0152478&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK000172&#x00A0;&#x00A0;&#x00A0;&#x00A0;9 Jun 1999&#x00A0;&#x00A0;14:22&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;0154537&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK000174&#x00A0;&#x00A0;&#x00A0;&#x00A0;9 Jun 1999&#x00A0;&#x00A0;14:24&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;0154539&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK000201&#x00A0;&#x00A0;&#x00A0;&#x00A0;9 Jun 1999&#x00A0;&#x00A0;16:54&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/HPM&#x00A0;&#x00A0;0067261&#x00A0;&#x00A0;repl. by SOEK000393</LI></UL> <UL><LI>SOEK000203&#x00A0;&#x00A0; 10 Jun 1999&#x00A0;&#x00A0;10:25&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/MAP&#x00A0;&#x00A0;0156403&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK000207&#x00A0;&#x00A0; 10 Jun 1999&#x00A0;&#x00A0;15:52&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TDP&#x00A0;&#x00A0;0156455&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK000197&#x00A0;&#x00A0; 10 Jun 1999&#x00A0;&#x00A0;16:56&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/MCO&#x00A0;&#x00A0;0147813&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK000213&#x00A0;&#x00A0; 11 Jun 1999&#x00A0;&#x00A0;11:51&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/MCO&#x00A0;&#x00A0;0153222&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK000218&#x00A0;&#x00A0;11 Jun 1999&#x00A0;&#x00A0;13:32&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/MAP&#x00A0;&#x00A0;0156697&#x00A0;&#x00A0;repl. by SOEK000229</LI></UL> <UL><LI>SOEK000194&#x00A0;&#x00A0; 14 Jun 1999&#x00A0;&#x00A0;09:12&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;0155914&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK000227&#x00A0;&#x00A0; 14 Jun 1999&#x00A0;&#x00A0;11:56&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/MRN&#x00A0;&#x00A0;0156906&#x00A0;&#x00A0; X</LI></UL><UL><LI>SOEK000211&#x00A0;&#x00A0; 14 Jun 1999&#x00A0;&#x00A0;13:43&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;0156700&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK000229&#x00A0;&#x00A0; 14 Jun 1999&#x00A0;&#x00A0;15:18&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/MAP&#x00A0;&#x00A0;0156697&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK000216&#x00A0;&#x00A0; 15 Jun 1999&#x00A0;&#x00A0;08:05&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TD&#x00A0;&#x00A0; 0156993&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK000245&#x00A0;&#x00A0; 15 Jun 1999&#x00A0;&#x00A0;11:11&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TD&#x00A0;&#x00A0; 0157044&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK000255&#x00A0;&#x00A0; 16 Jun 1999&#x00A0;&#x00A0;12:41&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TD&#x00A0;&#x00A0; 0156599&#x00A0;&#x00A0; X</LI></UL><UL><LI>SOEK000249&#x00A0;&#x00A0; 16 Jun 1999&#x00A0;&#x00A0;12:53&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/MRN&#x00A0;&#x00A0;0157494&#x00A0;&#x00A0; X</LI></UL><UL><LI>SOEK000176&#x00A0;&#x00A0; 16 Jun 1999&#x00A0;&#x00A0;16:33&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/MAP&#x00A0;&#x00A0;0157569&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK000056&#x00A0;&#x00A0; 17 Jun 1999&#x00A0;&#x00A0;09:58&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;0151361&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK000261&#x00A0;&#x00A0; 17 Jun 1999&#x00A0;&#x00A0;13:46&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/MAP&#x00A0;&#x00A0;0157768&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK000256&#x00A0;&#x00A0; 17 Jun 1999&#x00A0;&#x00A0;14:54&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TD&#x00A0;&#x00A0; 0157801&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK000269&#x00A0;&#x00A0; 18 Jun 1999&#x00A0;&#x00A0;11:25&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;0157975&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK000267&#x00A0;&#x00A0; 18 Jun 1999&#x00A0;&#x00A0;13:37&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/MAP&#x00A0;&#x00A0;0157920&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK000275&#x00A0;&#x00A0; 24 Jun 1999&#x00A0;&#x00A0;10:36&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TD&#x00A0;&#x00A0; 0158344&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK000393&#x00A0;&#x00A0;15 Jul 1999&#x00A0;&#x00A0;17:17&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/HPM&#x00A0;&#x00A0;0067261&#x00A0;&#x00A0;repl. by SOEK003441</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;!!!! CAUTION: Read SAPNet Note 167594 BEFORE applying SP 14 !!!!</p> <UL><LI>SAPKI40C14&#x00A0;&#x00A0; 25 Aug 1999&#x00A0;&#x00A0;10:43&#x00A0;&#x00A0;&#x00A0;&#x00A0;CRT14</LI></UL> <UL><LI>SAPKI40C15&#x00A0;&#x00A0; 25 Aug 1999&#x00A0;&#x00A0;10:45&#x00A0;&#x00A0;&#x00A0;&#x00A0;CRT15</LI></UL> <UL><LI>SAPKI40C16&#x00A0;&#x00A0; 25 Aug 1999&#x00A0;&#x00A0;10:55&#x00A0;&#x00A0;&#x00A0;&#x00A0;CRT16</LI></UL> <UL><LI>SAPKI40C17&#x00A0;&#x00A0; 25 Aug 1999&#x00A0;&#x00A0;10:56&#x00A0;&#x00A0;&#x00A0;&#x00A0;CRT17</LI></UL> <UL><LI>SAPKI40C18&#x00A0;&#x00A0; 25 Aug 1999&#x00A0;&#x00A0;11:00&#x00A0;&#x00A0;&#x00A0;&#x00A0;CRT18</LI></UL> <UL><LI>SAPKI40C19&#x00A0;&#x00A0; 25 Aug 1999&#x00A0;&#x00A0;11:03&#x00A0;&#x00A0;&#x00A0;&#x00A0;CRT19</LI></UL> <UL><LI>SAPKI40C20&#x00A0;&#x00A0; 25 Aug 1999&#x00A0;&#x00A0;14:55&#x00A0;&#x00A0;&#x00A0;&#x00A0;CRT20</LI></UL> <UL><LI>SOEK001537&#x00A0;&#x00A0; 30 Aug 1999&#x00A0;&#x00A0;12:13&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;0102306&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK001631&#x00A0;&#x00A0; 30 Aug 1999&#x00A0;&#x00A0;13:48&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;0170066&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK000711&#x00A0;&#x00A0; 31 Aug 1999&#x00A0;&#x00A0;09:02&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/HPM&#x00A0;&#x00A0;0165201&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK001713&#x00A0;&#x00A0; 31 Aug 1999&#x00A0;&#x00A0;12:57&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;0170314&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK001721&#x00A0;&#x00A0;&#x00A0;&#x00A0;1 Sep 1999&#x00A0;&#x00A0;16:22&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/ITA&#x00A0;&#x00A0;0170803&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK001752&#x00A0;&#x00A0;&#x00A0;&#x00A0;1 Sep 1999&#x00A0;&#x00A0;16:27&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/ITA&#x00A0;&#x00A0;0170822&#x00A0;&#x00A0; X</LI></UL> <UL><LI>SOEK001971&#x00A0;&#x00A0;&#x00A0;&#x00A0;1 Sep 1999&#x00A0;&#x00A0;18:21&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TSW&#x00A0;&#x00A0;0170815&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK001411&#x00A0;&#x00A0;&#x00A0;&#x00A0;2 Sep 1999&#x00A0;&#x00A0;11:05&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/HPM&#x00A0;&#x00A0;0168765&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK001719&#x00A0;&#x00A0;&#x00A0;&#x00A0;2 Sep 1999&#x00A0;&#x00A0;11:26&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/MAP&#x00A0;&#x00A0;0170219&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK002083&#x00A0;&#x00A0;&#x00A0;&#x00A0;3 Sep 1999&#x00A0;&#x00A0;10:01&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/ITA&#x00A0;&#x00A0;0171065&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK002111&#x00A0;&#x00A0;&#x00A0;&#x00A0;3 Sep 1999&#x00A0;&#x00A0;10:41&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TPI&#x00A0;&#x00A0;0171086&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK001621&#x00A0;&#x00A0;&#x00A0;&#x00A0;3 Sep 1999&#x00A0;&#x00A0;13:48&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TD&#x00A0;&#x00A0; 0171053&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK001918&#x00A0;&#x00A0;&#x00A0;&#x00A0;6 Sep 1999&#x00A0;&#x00A0;10:20&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;0170668&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK002313&#x00A0;&#x00A0;&#x00A0;&#x00A0;6 Sep 1999&#x00A0;&#x00A0;15:53&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/HPM&#x00A0;&#x00A0;0171391&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK001838&#x00A0;&#x00A0;&#x00A0;&#x00A0;7 Sep 1999&#x00A0;&#x00A0;13:35&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/HPM&#x00A0;&#x00A0;0170620&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK002085&#x00A0;&#x00A0;&#x00A0;&#x00A0;7 Sep 1999&#x00A0;&#x00A0;16:37&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TDP&#x00A0;&#x00A0;0171037&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK002311&#x00A0;&#x00A0;&#x00A0;&#x00A0;7 Sep 1999&#x00A0;&#x00A0;16:41&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TDP&#x00A0;&#x00A0;0171736&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK001974&#x00A0;&#x00A0;&#x00A0;&#x00A0;7 Sep 1999&#x00A0;&#x00A0;17:03&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TDP&#x00A0;&#x00A0;0170871&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK001587&#x00A0;&#x00A0;&#x00A0;&#x00A0;8 Sep 1999&#x00A0;&#x00A0;08:58&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/HPM&#x00A0;&#x00A0;0169295&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK002403&#x00A0;&#x00A0;&#x00A0;&#x00A0;8 Sep 1999&#x00A0;&#x00A0;15:40&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/BDRP 0171871&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK002405&#x00A0;&#x00A0;&#x00A0;&#x00A0;8 Sep 1999&#x00A0;&#x00A0;17:02&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TD&#x00A0;&#x00A0; 0172009&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK002408&#x00A0;&#x00A0;&#x00A0;&#x00A0;9 Sep 1999&#x00A0;&#x00A0;13:40&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;0172063&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK002484&#x00A0;&#x00A0; 10 Sep 1999&#x00A0;&#x00A0;09:10&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;0166548&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK002540&#x00A0;&#x00A0; 13 Sep 1999&#x00A0;&#x00A0;11:07&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/ITA&#x00A0;&#x00A0;0172527&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK002638&#x00A0;&#x00A0; 13 Sep 1999&#x00A0;&#x00A0;16:01&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TSW&#x00A0;&#x00A0;0172681&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK002339&#x00A0;&#x00A0; 13 Sep 1999&#x00A0;&#x00A0;17:04&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TD&#x00A0;&#x00A0; 0172412&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK001709&#x00A0;&#x00A0; 13 Sep 1999&#x00A0;&#x00A0;17:25&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/MAP&#x00A0;&#x00A0;0170287&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK002668&#x00A0;&#x00A0; 13 Sep 1999&#x00A0;&#x00A0;17:43&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TD&#x00A0;&#x00A0; 0172759&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK002490&#x00A0;&#x00A0; 13 Sep 1999&#x00A0;&#x00A0;20:22&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TPI&#x00A0;&#x00A0;0172738&#x00A0;&#x00A0; X</LI></UL><UL><LI>SOEK001976&#x00A0;&#x00A0; 14 Sep 1999&#x00A0;&#x00A0;15:59&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/MCOE 0170968&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK001053&#x00A0;&#x00A0; 14 Sep 1999&#x00A0;&#x00A0;16:06&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/MAP&#x00A0;&#x00A0;0167025&#x00A0;&#x00A0; X</LI></UL><UL><LI>SOEK002919&#x00A0;&#x00A0; 14 Sep 1999&#x00A0;&#x00A0;17:11&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TPI&#x00A0;&#x00A0;0173139&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK002946&#x00A0;&#x00A0; 15 Sep 1999&#x00A0;&#x00A0;09:43&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/MCO&#x00A0;&#x00A0;0173205&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK002915&#x00A0;&#x00A0; 16 Sep 1999&#x00A0;&#x00A0;09:39&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/MAP&#x00A0;&#x00A0;0173079&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK003065&#x00A0;&#x00A0; 17 Sep 1999&#x00A0;&#x00A0;10:45&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/ITA&#x00A0;&#x00A0;0173532&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK003069&#x00A0;&#x00A0; 17 Sep 1999&#x00A0;&#x00A0;11:40&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TD&#x00A0;&#x00A0; 0173715&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK003071&#x00A0;&#x00A0; 17 Sep 1999&#x00A0;&#x00A0;12:58&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TD&#x00A0;&#x00A0; 0173720&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK003063&#x00A0;&#x00A0; 17 Sep 1999&#x00A0;&#x00A0;13:21&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/ITA&#x00A0;&#x00A0;0173492&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK003154&#x00A0;&#x00A0; 17 Sep 1999&#x00A0;&#x00A0;14:54&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/ITA&#x00A0;&#x00A0;0173754&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK003161&#x00A0;&#x00A0; 20 Sep 1999&#x00A0;&#x00A0;18:16&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/MCOE 0174020&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK003193&#x00A0;&#x00A0; 21 Sep 1999&#x00A0;&#x00A0;16:04&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/MCO&#x00A0;&#x00A0;0174228&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK003151&#x00A0;&#x00A0; 22 Sep 1999&#x00A0;&#x00A0;13:58&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TPI&#x00A0;&#x00A0;0174462&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK003187&#x00A0;&#x00A0; 22 Sep 1999&#x00A0;&#x00A0;14:44&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/HPM&#x00A0;&#x00A0;0174144&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK003396&#x00A0;&#x00A0; 23 Sep 1999&#x00A0;&#x00A0;10:48&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TDP&#x00A0;&#x00A0;0174437&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK003392&#x00A0;&#x00A0; 23 Sep 1999&#x00A0;&#x00A0;14:20&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/MAP&#x00A0;&#x00A0;0174648&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK003060&#x00A0;&#x00A0; 23 Sep 1999&#x00A0;&#x00A0;18:54&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/BDRP 0173484&#x00A0;&#x00A0; X</LI></UL> <UL><LI>SOEK003400&#x00A0;&#x00A0; 24 Sep 1999&#x00A0;&#x00A0;15:59&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/MAP&#x00A0;&#x00A0;0175059&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK003398&#x00A0;&#x00A0; 24 Sep 1999&#x00A0;&#x00A0;17:19&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TPI&#x00A0;&#x00A0;0174710&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK003410&#x00A0;&#x00A0; 27 Sep 1999&#x00A0;&#x00A0;13:37&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;0175037&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK003454&#x00A0;&#x00A0; 28 Sep 1999&#x00A0;&#x00A0;16:06&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/DS&#x00A0;&#x00A0; 0175501&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK003394&#x00A0;&#x00A0; 29 Sep 1999&#x00A0;&#x00A0;08:38&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;0174656&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK003456&#x00A0;&#x00A0; 29 Sep 1999&#x00A0;&#x00A0;09:05&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/ITA&#x00A0;&#x00A0;0175646&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK003412&#x00A0;&#x00A0; 29 Sep 1999&#x00A0;&#x00A0;10:22&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/MAP&#x00A0;&#x00A0;0175140&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK003450&#x00A0;&#x00A0; 30 Sep 1999&#x00A0;&#x00A0;09:01&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;0175463&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK003476&#x00A0;&#x00A0; 30 Sep 1999&#x00A0;&#x00A0;09:03&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TPI&#x00A0;&#x00A0;0175840&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK003464&#x00A0;&#x00A0; 30 Sep 1999&#x00A0;&#x00A0;13:41&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TPI&#x00A0;&#x00A0;0175777&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK003480&#x00A0;&#x00A0; 30 Sep 1999&#x00A0;&#x00A0;14:42&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TPI&#x00A0;&#x00A0;0175917&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK003491&#x00A0;&#x00A0;&#x00A0;&#x00A0;1 Oct 1999&#x00A0;&#x00A0;17:06&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TPI&#x00A0;&#x00A0;0176218&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK003489&#x00A0;&#x00A0;&#x00A0;&#x00A0;1 Oct 1999&#x00A0;&#x00A0;17:11&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TPI&#x00A0;&#x00A0;0176214&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK002917&#x00A0;&#x00A0;&#x00A0;&#x00A0;4 Oct 1999&#x00A0;&#x00A0;15:56&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TSW&#x00A0;&#x00A0;0173130&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK003402&#x00A0;&#x00A0;&#x00A0;&#x00A0;5 Oct 1999&#x00A0;&#x00A0;10:39&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/HPM&#x00A0;&#x00A0;0174746&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK003291&#x00A0;&#x00A0;&#x00A0;&#x00A0;5 Oct 1999&#x00A0;&#x00A0;11:25&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TPI&#x00A0;&#x00A0;0175494&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK003502&#x00A0;&#x00A0;&#x00A0;&#x00A0;5 Oct 1999&#x00A0;&#x00A0;11:34&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TPI&#x00A0;&#x00A0;0176589&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK003510&#x00A0;&#x00A0;&#x00A0;&#x00A0;5 Oct 1999&#x00A0;&#x00A0;16:37&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TPI&#x00A0;&#x00A0;0176741&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK003441&#x00A0;&#x00A0;&#x00A0;&#x00A0;6 Oct 1999&#x00A0;&#x00A0;09:42&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/HPM&#x00A0;&#x00A0;0067261&#x00A0;&#x00A0;repl. by SOEK003579</LI></UL> <UL><LI>SOEK003514&#x00A0;&#x00A0;&#x00A0;&#x00A0;6 Oct 1999&#x00A0;&#x00A0;11:22&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/BDR&#x00A0;&#x00A0;0176785&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK003518&#x00A0;&#x00A0;&#x00A0;&#x00A0;6 Oct 1999&#x00A0;&#x00A0;21:24&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/MRN&#x00A0;&#x00A0;0176909&#x00A0;&#x00A0; X</LI></UL> <UL><LI>SOEK003406&#x00A0;&#x00A0;&#x00A0;&#x00A0;7 Oct 1999&#x00A0;&#x00A0;08:37&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TD&#x00A0;&#x00A0; 0176647&#x00A0;&#x00A0; X</LI></UL> <UL><LI>SOEK003522&#x00A0;&#x00A0;&#x00A0;&#x00A0;8 Oct 1999&#x00A0;&#x00A0;10:48&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/DS&#x00A0;&#x00A0; 0176746&#x00A0;&#x00A0;repl. by SOEK003660</LI></UL> <UL><LI>SOEK003539&#x00A0;&#x00A0;&#x00A0;&#x00A0;8 Oct 1999&#x00A0;&#x00A0;11:12&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TPI&#x00A0;&#x00A0;0177556&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK003542&#x00A0;&#x00A0;&#x00A0;&#x00A0;8 Oct 1999&#x00A0;&#x00A0;14:37&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TPI&#x00A0;&#x00A0;0177563&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK003508&#x00A0;&#x00A0;&#x00A0;&#x00A0;8 Oct 1999&#x00A0;&#x00A0;15:18&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TSW&#x00A0;&#x00A0;0176988&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK003439&#x00A0;&#x00A0; 11 Oct 1999&#x00A0;&#x00A0;16:24&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TDP&#x00A0;&#x00A0;0175342&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK003564&#x00A0;&#x00A0; 12 Oct 1999&#x00A0;&#x00A0;15:15&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/ITA&#x00A0;&#x00A0;0178164&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK003566&#x00A0;&#x00A0; 12 Oct 1999&#x00A0;&#x00A0;15:43&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TDP&#x00A0;&#x00A0;0177949&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK003549&#x00A0;&#x00A0; 13 Oct 1999&#x00A0;&#x00A0;17:34&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TPI&#x00A0;&#x00A0;0177825&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK003579&#x00A0;&#x00A0;14 Oct 1999&#x00A0;&#x00A0;10:23&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/HPM&#x00A0;&#x00A0;0067261&#x00A0;&#x00A0;repl. by SOEK004315</LI></UL> <UL><LI>SOEK003516&#x00A0;&#x00A0; 14 Oct 1999&#x00A0;&#x00A0;13:46&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/MAP&#x00A0;&#x00A0;0176858&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK002309&#x00A0;&#x00A0; 14 Oct 1999&#x00A0;&#x00A0;14:48&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/HPM&#x00A0;&#x00A0;0171364&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK003571&#x00A0;&#x00A0; 14 Oct 1999&#x00A0;&#x00A0;18:14&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/MAP&#x00A0;&#x00A0;0177866&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK003660&#x00A0;&#x00A0;25 Oct 1999&#x00A0;&#x00A0;15:35&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/DS&#x00A0;&#x00A0; 0176746&#x00A0;&#x00A0;repl. by SOEK004023</LI></UL> <UL><LI>SOEK004023&#x00A0;&#x00A0;&#x00A0;&#x00A0;8 Nov 1999&#x00A0;&#x00A0;16:47&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/DS&#x00A0;&#x00A0; 0176746</LI></UL> <UL><LI>SOEK004124&#x00A0;&#x00A0;&#x00A0;&#x00A0;9 Nov 1999&#x00A0;&#x00A0;14:16&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/DS&#x00A0;&#x00A0; 0181652&#x00A0;&#x00A0;repl. by SOEK004773</LI></UL> <UL><LI>SOEK003996&#x00A0;&#x00A0;&#x00A0;&#x00A0;8 Nov 1999&#x00A0;&#x00A0;15:39&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/MAP&#x00A0;&#x00A0;0182651</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;!!!! CAUTION: Read SAPNet Note 180083 BEFORE applying SP 21&#x00A0;&#x00A0;!!!!</p> <UL><LI>SAPKI40C21&#x00A0;&#x00A0; 11 Nov 1999&#x00A0;&#x00A0;02:50&#x00A0;&#x00A0;&#x00A0;&#x00A0;CRT21</LI></UL> <UL><LI>SAPKI40C23&#x00A0;&#x00A0; 11 Nov 1999&#x00A0;&#x00A0;02:52&#x00A0;&#x00A0;&#x00A0;&#x00A0;CRT23</LI></UL> <UL><LI>SAPKI40C24&#x00A0;&#x00A0; 11 Nov 1999&#x00A0;&#x00A0;02:53&#x00A0;&#x00A0;&#x00A0;&#x00A0;CRT24</LI></UL> <UL><LI>SAPKI40C25&#x00A0;&#x00A0; 11 Nov 1999&#x00A0;&#x00A0;02:54&#x00A0;&#x00A0;&#x00A0;&#x00A0;CRT25</LI></UL> <UL><LI>SAPKI40C26&#x00A0;&#x00A0; 11 Nov 1999&#x00A0;&#x00A0;02:55&#x00A0;&#x00A0;&#x00A0;&#x00A0;CRT26</LI></UL> <UL><LI>SAPKI40C27&#x00A0;&#x00A0; 11 Nov 1999&#x00A0;&#x00A0;02:56&#x00A0;&#x00A0;&#x00A0;&#x00A0;CRT27</LI></UL> <UL><LI>SAPKI40C28&#x00A0;&#x00A0; 11 Nov 1999&#x00A0;&#x00A0;02:58&#x00A0;&#x00A0;&#x00A0;&#x00A0;CRT28</LI></UL> <UL><LI>SOEK004215&#x00A0;&#x00A0; 15 Nov 1999&#x00A0;&#x00A0;12:04&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/ITA&#x00A0;&#x00A0;0184148&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK004175&#x00A0;&#x00A0; 15 Nov 1999&#x00A0;&#x00A0;13:19&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TPI&#x00A0;&#x00A0;0183374&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK004217&#x00A0;&#x00A0; 15 Nov 1999&#x00A0;&#x00A0;14:34&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TSW&#x00A0;&#x00A0;0184209&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK004207&#x00A0;&#x00A0; 15 Nov 1999&#x00A0;&#x00A0;15:08&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;0183811&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK001629&#x00A0;&#x00A0; 15 Nov 1999&#x00A0;&#x00A0;16:28&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/MAP&#x00A0;&#x00A0;0169833&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK004219&#x00A0;&#x00A0; 16 Nov 1999&#x00A0;&#x00A0;09:47&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/ITA&#x00A0;&#x00A0;0183036&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK004223&#x00A0;&#x00A0; 16 Nov 1999&#x00A0;&#x00A0;11:52&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TPI&#x00A0;&#x00A0;0184295&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK003736&#x00A0;&#x00A0; 16 Nov 1999&#x00A0;&#x00A0;13:52&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/ITA&#x00A0;&#x00A0;0181457&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK003822&#x00A0;&#x00A0; 16 Nov 1999&#x00A0;&#x00A0;13:53&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/ITA&#x00A0;&#x00A0;0181711&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK003874&#x00A0;&#x00A0; 16 Nov 1999&#x00A0;&#x00A0;13:54&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/ITA&#x00A0;&#x00A0;0182178&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK004209&#x00A0;&#x00A0; 16 Nov 1999&#x00A0;&#x00A0;13:56&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;0183761&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK003537&#x00A0;&#x00A0; 16 Nov 1999&#x00A0;&#x00A0;15:35&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/BDR&#x00A0;&#x00A0;0177535&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK004233&#x00A0;&#x00A0; 17 Nov 1999&#x00A0;&#x00A0;10:24&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/MAP&#x00A0;&#x00A0;0184644&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK004255&#x00A0;&#x00A0; 18 Nov 1999&#x00A0;&#x00A0;16:21&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/DS&#x00A0;&#x00A0; 0185010&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK004262&#x00A0;&#x00A0; 18 Nov 1999&#x00A0;&#x00A0;18:16&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TSW&#x00A0;&#x00A0;0185062&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK003535&#x00A0;&#x00A0; 19 Nov 1999&#x00A0;&#x00A0;09:38&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TSW&#x00A0;&#x00A0;0177592&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK003554&#x00A0;&#x00A0; 19 Nov 1999&#x00A0;&#x00A0;10:36&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TSW&#x00A0;&#x00A0;0185159&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK004241&#x00A0;&#x00A0; 19 Nov 1999&#x00A0;&#x00A0;11:31&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TSW&#x00A0;&#x00A0;0185187&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK004257&#x00A0;&#x00A0; 19 Nov 1999&#x00A0;&#x00A0;11:38&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TSW&#x00A0;&#x00A0;0185034&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK003600&#x00A0;&#x00A0; 19 Nov 1999&#x00A0;&#x00A0;13:54&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/HPM&#x00A0;&#x00A0;0178808&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK001633&#x00A0;&#x00A0; 19 Nov 1999&#x00A0;&#x00A0;13:56&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/HPM&#x00A0;&#x00A0;0170087&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK004272&#x00A0;&#x00A0; 19 Nov 1999&#x00A0;&#x00A0;15:15&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/HPM&#x00A0;&#x00A0;0185231&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK004268&#x00A0;&#x00A0; 19 Nov 1999&#x00A0;&#x00A0;16:00&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;0185245&#x00A0;&#x00A0; X</LI></UL><UL><LI>SOEK004237&#x00A0;&#x00A0; 22 Nov 1999&#x00A0;&#x00A0;08:01&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;0184640&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK003656&#x00A0;&#x00A0; 22 Nov 1999&#x00A0;&#x00A0;10:02&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TPI&#x00A0;&#x00A0;0180418&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK003672&#x00A0;&#x00A0; 22 Nov 1999&#x00A0;&#x00A0;13:17&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TPI&#x00A0;&#x00A0;0180696&#x00A0;&#x00A0; X</LI></UL><UL><LI>SOEK003706&#x00A0;&#x00A0; 22 Nov 1999&#x00A0;&#x00A0;13:19&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TPI&#x00A0;&#x00A0;0185453&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK003714&#x00A0;&#x00A0; 22 Nov 1999&#x00A0;&#x00A0;13:20&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TPI&#x00A0;&#x00A0;0181099&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK003699&#x00A0;&#x00A0; 22 Nov 1999&#x00A0;&#x00A0;14:22&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TPI&#x00A0;&#x00A0;0181008&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK003609&#x00A0;&#x00A0; 22 Nov 1999&#x00A0;&#x00A0;15:08&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TPI&#x00A0;&#x00A0;0179143&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK003637&#x00A0;&#x00A0; 22 Nov 1999&#x00A0;&#x00A0;20:44&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/ITA&#x00A0;&#x00A0;0179813&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK003544&#x00A0;&#x00A0; 22 Nov 1999&#x00A0;&#x00A0;20:46&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TPI&#x00A0;&#x00A0;0179868&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK003721&#x00A0;&#x00A0; 23 Nov 1999&#x00A0;&#x00A0;09:59&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TPI&#x00A0;&#x00A0;0181239&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK003618&#x00A0;&#x00A0; 23 Nov 1999&#x00A0;&#x00A0;10:03&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TPI&#x00A0;&#x00A0;0179319&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK003708&#x00A0;&#x00A0; 23 Nov 1999&#x00A0;&#x00A0;10:06&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TPI&#x00A0;&#x00A0;0185620&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK003710&#x00A0;&#x00A0; 23 Nov 1999&#x00A0;&#x00A0;10:07&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TPI&#x00A0;&#x00A0;0185617&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK003684&#x00A0;&#x00A0; 23 Nov 1999&#x00A0;&#x00A0;10:46&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TPI&#x00A0;&#x00A0;0180759&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK003704&#x00A0;&#x00A0; 23 Nov 1999&#x00A0;&#x00A0;11:27&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TPI&#x00A0;&#x00A0;0181061&#x00A0;&#x00A0; X</LI></UL><UL><LI>SOEK003731&#x00A0;&#x00A0; 23 Nov 1999&#x00A0;&#x00A0;12:08&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TPI&#x00A0;&#x00A0;0181435&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK004290&#x00A0;&#x00A0; 23 Nov 1999&#x00A0;&#x00A0;13:13&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;0185685&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK004264&#x00A0;&#x00A0; 24 Nov 1999&#x00A0;&#x00A0;08:43&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;0184012&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK004229&#x00A0;&#x00A0; 24 Nov 1999&#x00A0;&#x00A0;10:36&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/MAP&#x00A0;&#x00A0;0184532&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK004295&#x00A0;&#x00A0; 24 Nov 1999&#x00A0;&#x00A0;13:59&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;0185895&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK004304&#x00A0;&#x00A0; 25 Nov 1999&#x00A0;&#x00A0;09:25&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TPI&#x00A0;&#x00A0;0186151&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK004315&#x00A0;&#x00A0;25 Nov 1999&#x00A0;&#x00A0;15:57&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/HPM&#x00A0;&#x00A0;0067261&#x00A0;&#x00A0;repl. by SOEK005022</LI></UL> <UL><LI>SOEK003466&#x00A0;&#x00A0; 25 Nov 1999&#x00A0;&#x00A0;17:18&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TPI&#x00A0;&#x00A0;0175799&#x00A0;&#x00A0; X</LI></UL><UL><LI>SOEK004320&#x00A0;&#x00A0; 26 Nov 1999&#x00A0;&#x00A0;08:02&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/MAP&#x00A0;&#x00A0;0179557&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK004312&#x00A0;&#x00A0; 25 Nov 1999&#x00A0;&#x00A0;16:18&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TSW&#x00A0;&#x00A0;0186341&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK004393&#x00A0;&#x00A0;&#x00A0;&#x00A0;9 Dec 1999&#x00A0;&#x00A0;11:48&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/DS&#x00A0;&#x00A0; 0188842&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SAPKI40C29&#x00A0;&#x00A0; 20 Dec 1999&#x00A0;&#x00A0;15:30&#x00A0;&#x00A0;&#x00A0;&#x00A0;CRT29</LI></UL> <UL><LI>SAPKI40C30&#x00A0;&#x00A0; 20 Dec 1999&#x00A0;&#x00A0;15.32&#x00A0;&#x00A0;&#x00A0;&#x00A0;CRT30</LI></UL> <UL><LI>SOEK004638&#x00A0;&#x00A0; 21 Dec 1999&#x00A0;&#x00A0;13:58&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;0190248&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK004658&#x00A0;&#x00A0; 22 Dec 1999&#x00A0;&#x00A0;11:11&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TPI&#x00A0;&#x00A0;0191149&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK004656&#x00A0;&#x00A0; 22 Dec 1999&#x00A0;&#x00A0;14:43&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TPI&#x00A0;&#x00A0;0191154&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK004668&#x00A0;&#x00A0; 23 Dec 1999&#x00A0;&#x00A0;11:30&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;0191413&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK004666&#x00A0;&#x00A0; 23 Dec 1999&#x00A0;&#x00A0;11:33&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;0188270&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK004205&#x00A0;&#x00A0; 27 Dec 1999&#x00A0;&#x00A0;14:24&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TD&#x00A0;&#x00A0; 0183597&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK004368&#x00A0;&#x00A0; 28 Dec 1999&#x00A0;&#x00A0;06:21&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TD&#x00A0;&#x00A0; 0187744&#x00A0;&#x00A0; X</LI></UL><UL><LI>SOEK004691&#x00A0;&#x00A0; 28 Dec 1999&#x00A0;&#x00A0;16:45&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/MAP&#x00A0;&#x00A0;0191913&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK004664&#x00A0;&#x00A0; 29 Dec 1999&#x00A0;&#x00A0;10:08&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TPI&#x00A0;&#x00A0;0191401&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK004670&#x00A0;&#x00A0; 29 Dec 1999&#x00A0;&#x00A0;12:00&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TPI&#x00A0;&#x00A0;0191429&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK004697&#x00A0;&#x00A0; 29 Dec 1999&#x00A0;&#x00A0;12:33&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TSW&#x00A0;&#x00A0;0192081&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK004699&#x00A0;&#x00A0; 29 Dec 1999&#x00A0;&#x00A0;16:21&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/DS&#x00A0;&#x00A0; 0192152&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK004651&#x00A0;&#x00A0; 29 Dec 1999&#x00A0;&#x00A0;16:21&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TPI&#x00A0;&#x00A0;0191151&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK004695&#x00A0;&#x00A0; 30 Dec 1999&#x00A0;&#x00A0;09:23&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TPI&#x00A0;&#x00A0;0192046&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK004676&#x00A0;&#x00A0; 30 Dec 1999&#x00A0;&#x00A0;09:24&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TPI&#x00A0;&#x00A0;0191696&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK004693&#x00A0;&#x00A0; 30 Dec 1999&#x00A0;&#x00A0;13:46&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TPI&#x00A0;&#x00A0;0192014&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK004701&#x00A0;&#x00A0; 30 Dec 1999&#x00A0;&#x00A0;15:39&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TD&#x00A0;&#x00A0; 0192304&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK004709&#x00A0;&#x00A0; 31 Dec 1999&#x00A0;&#x00A0;08:22&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TSW&#x00A0;&#x00A0;0192367&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK004713&#x00A0;&#x00A0;&#x00A0;&#x00A0;4 Jan 2000&#x00A0;&#x00A0;13:33&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;0192520&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK004720&#x00A0;&#x00A0;&#x00A0;&#x00A0;7 Jan 2000&#x00A0;&#x00A0;11:36&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;0192864&#x00A0;&#x00A0;repl. by SOEK004920</LI></UL> <UL><LI>SOEK004642&#x00A0;&#x00A0;&#x00A0;&#x00A0;7 Jan 2000&#x00A0;&#x00A0;13:45&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;0189441&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK004734&#x00A0;&#x00A0; 10 Jan 2000&#x00A0;&#x00A0;11:49&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TPI&#x00A0;&#x00A0;0193227&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK004728&#x00A0;&#x00A0; 10 Jan 2000&#x00A0;&#x00A0;13:13&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/MAP&#x00A0;&#x00A0;0193225&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK004689&#x00A0;&#x00A0; 10 Jan 2000&#x00A0;&#x00A0;13:55&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/MAP&#x00A0;&#x00A0;0191923&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK004736&#x00A0;&#x00A0; 10 Jan 2000&#x00A0;&#x00A0;16:18&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TPI&#x00A0;&#x00A0;0193229&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK004703&#x00A0;&#x00A0; 10 Jan 2000&#x00A0;&#x00A0;16:21&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TPI&#x00A0;&#x00A0;0192273&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK004738&#x00A0;&#x00A0; 10 Jan 2000&#x00A0;&#x00A0;16:22&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TPI&#x00A0;&#x00A0;0193231&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK004748&#x00A0;&#x00A0; 10 Jan 2000&#x00A0;&#x00A0;16:30&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TD&#x00A0;&#x00A0; 0193559&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK004711&#x00A0;&#x00A0; 11 Jan 2000&#x00A0;&#x00A0;09:38&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/BDRP 0192527&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK004758&#x00A0;&#x00A0; 11 Jan 2000&#x00A0;&#x00A0;09:41&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;0193619&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK004741&#x00A0;&#x00A0; 11 Jan 2000&#x00A0;&#x00A0;10:09&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;0193381&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK004732&#x00A0;&#x00A0; 11 Jan 2000&#x00A0;&#x00A0;16:52&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TDP&#x00A0;&#x00A0;0193194&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK004681&#x00A0;&#x00A0; 12 Jan 2000&#x00A0;&#x00A0;09:51&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TDP&#x00A0;&#x00A0;0191705&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK004763&#x00A0;&#x00A0; 12 Jan 2000&#x00A0;&#x00A0;10:05&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/HPM&#x00A0;&#x00A0;0193770&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK004722&#x00A0;&#x00A0; 12 Jan 2000&#x00A0;&#x00A0;13:22&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;0193001&#x00A0;&#x00A0; X</LI></UL><UL><LI>SOEK004769&#x00A0;&#x00A0; 12 Jan 2000&#x00A0;&#x00A0;13:24&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TD&#x00A0;&#x00A0; 0193968&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK004773&#x00A0;&#x00A0; 12 Jan 2000&#x00A0;&#x00A0;14:21&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/DS&#x00A0;&#x00A0; 0181652&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK004743&#x00A0;&#x00A0;13 Jan 2000&#x00A0;&#x00A0;11:19&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/BDRP 0193407&#x00A0;&#x00A0;repl. by SOEK006578</LI></UL> <UL><LI>SOEK004771&#x00A0;&#x00A0; 13 Jan 2000&#x00A0;&#x00A0;16:23&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TD&#x00A0;&#x00A0; 0194386&#x00A0;&#x00A0; -</LI></UL><p><br />!!!&#x00A0;&#x00A0;For the following transports 'X' indicates that manual steps<br />!!!&#x00A0;&#x00A0;are necessary as described in the corresponding note.<br />!!!&#x00A0;&#x00A0;(this note (145850) will be updated soon to show this information<br />!!!&#x00A0;&#x00A0; for all transports listed here. Nevertheless the<br />!!!&#x00A0;&#x00A0; information whether manual steps are necessary is always<br />!!!&#x00A0;&#x00A0; contained in the corresponding note)<br /></p> <UL><LI>SOEK004784&#x00A0;&#x00A0; 17 Jan 2000&#x00A0;&#x00A0;11:09&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;0194642&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK004793&#x00A0;&#x00A0; 17 Jan 2000&#x00A0;&#x00A0;15:17&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TD&#x00A0;&#x00A0; 0195018&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK004672&#x00A0;&#x00A0; 20 Jan 2000&#x00A0;&#x00A0;16:29&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TPI&#x00A0;&#x00A0;0191432&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK004515&#x00A0;&#x00A0; 20 Jan 2000&#x00A0;&#x00A0;17:10&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TPI&#x00A0;&#x00A0;0189648&#x00A0;&#x00A0; X</LI></UL><UL><LI>SOEK004780&#x00A0;&#x00A0; 20 Jan 2000&#x00A0;&#x00A0;18:05&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TPI&#x00A0;&#x00A0;0194327&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK004724&#x00A0;&#x00A0; 21 Jan 2000&#x00A0;&#x00A0;11:06&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TPI&#x00A0;&#x00A0;0193023&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK004811&#x00A0;&#x00A0; 21 Jan 2000&#x00A0;&#x00A0;11:52&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TPI&#x00A0;&#x00A0;0196052&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK004815&#x00A0;&#x00A0; 21 Jan 2000&#x00A0;&#x00A0;11:41&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/DS&#x00A0;&#x00A0; 0196075&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK004793&#x00A0;&#x00A0; 17 Jan 2000&#x00A0;&#x00A0;15:17&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TD&#x00A0;&#x00A0; 0195018&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK004839&#x00A0;&#x00A0; 26 Jan 2000&#x00A0;&#x00A0;11:31&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/MCO&#x00A0;&#x00A0;0196890&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK004782&#x00A0;&#x00A0; 27 Jan 2000&#x00A0;&#x00A0;13:13&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;0194512&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK004847&#x00A0;&#x00A0; 28 Jan 2000&#x00A0;&#x00A0;11:44&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/BDRP 0197470&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK004857&#x00A0;&#x00A0; 28 Jan 2000&#x00A0;&#x00A0;11:44&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/MAP&#x00A0;&#x00A0;0197466&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK004874&#x00A0;&#x00A0; 31 Jan 2000&#x00A0;&#x00A0;14:51&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;0197769&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK004884&#x00A0;&#x00A0;&#x00A0;&#x00A0;1 Feb 2000&#x00A0;&#x00A0;07:11&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/MAP&#x00A0;&#x00A0;0197908&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK004880&#x00A0;&#x00A0;&#x00A0;&#x00A0;1 Feb 2000&#x00A0;&#x00A0;09:02&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/MRN&#x00A0;&#x00A0;0197770&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK004850&#x00A0;&#x00A0;&#x00A0;&#x00A0;1 Feb 2000&#x00A0;&#x00A0;11:01&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TPI&#x00A0;&#x00A0;0197182&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK004892&#x00A0;&#x00A0;&#x00A0;&#x00A0;1 Feb 2000&#x00A0;&#x00A0;14:42&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/DS&#x00A0;&#x00A0; 0198085&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK004882&#x00A0;&#x00A0;&#x00A0;&#x00A0;1 Feb 2000&#x00A0;&#x00A0;15:18&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;0197834&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK004746&#x00A0;&#x00A0;&#x00A0;&#x00A0;1 Feb 2000&#x00A0;&#x00A0;15:22&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TDP&#x00A0;&#x00A0;0197905&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK004821&#x00A0;&#x00A0;&#x00A0;&#x00A0;2 Feb 2000&#x00A0;&#x00A0;07:48&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/MAP&#x00A0;&#x00A0;0196251&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK004843&#x00A0;&#x00A0;&#x00A0;&#x00A0;4 Feb 2000&#x00A0;&#x00A0;09:11&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TDP&#x00A0;&#x00A0;0189505&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK004843&#x00A0;&#x00A0;&#x00A0;&#x00A0;4 Feb 2000&#x00A0;&#x00A0;09:11&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TDP&#x00A0;&#x00A0;0189505&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK004861&#x00A0;&#x00A0;&#x00A0;&#x00A0;7 Feb 2000&#x00A0;&#x00A0;15:10&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TAS&#x00A0;&#x00A0;0199227&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK004760&#x00A0;&#x00A0;&#x00A0;&#x00A0;7 Feb 2000&#x00A0;&#x00A0;15:40&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;0195009&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK004859&#x00A0;&#x00A0;&#x00A0;&#x00A0;7 Feb 2000&#x00A0;&#x00A0;17:01&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TAS&#x00A0;&#x00A0;0199325&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK004920&#x00A0;&#x00A0;&#x00A0;&#x00A0;9 Feb 2000&#x00A0;&#x00A0;09:52&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;0192864&#x00A0;&#x00A0;repl. by CRT 39 - 44</LI></UL> <UL><LI>SOEK004900&#x00A0;&#x00A0;&#x00A0;&#x00A0;9 Feb 2000&#x00A0;&#x00A0;11:23&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/MAP&#x00A0;&#x00A0;0198515&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK004922&#x00A0;&#x00A0; 11 Feb 2000&#x00A0;&#x00A0;08:15&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TPI&#x00A0;&#x00A0;0199541&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK004932&#x00A0;&#x00A0; 11 Feb 2000&#x00A0;&#x00A0;09:01&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TPI&#x00A0;&#x00A0;0200316&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK004940&#x00A0;&#x00A0; 14 Feb 2000&#x00A0;&#x00A0;16:11&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TD&#x00A0;&#x00A0; 0200816&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK004936&#x00A0;&#x00A0; 15 Feb 2000&#x00A0;&#x00A0;08:46&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/BDRP 0200706&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK004952&#x00A0;&#x00A0; 15 Feb 2000&#x00A0;&#x00A0;14:25&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TDP&#x00A0;&#x00A0;0197738&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK004945&#x00A0;&#x00A0; 16 Feb 2000&#x00A0;&#x00A0;08:52&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TPI&#x00A0;&#x00A0;0200957&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK004646&#x00A0;&#x00A0; 16 Feb 2000&#x00A0;&#x00A0;09:03&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;0098642&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK004958&#x00A0;&#x00A0; 16 Feb 2000&#x00A0;&#x00A0;09:13&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/HPM&#x00A0;&#x00A0;0201122&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK004938&#x00A0;&#x00A0; 16 Feb 2000&#x00A0;&#x00A0;10:46&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;0200940&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK004969&#x00A0;&#x00A0; 16 Feb 2000&#x00A0;&#x00A0;13:49&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;0201309&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK004886&#x00A0;&#x00A0; 16 Feb 2000&#x00A0;&#x00A0;15:18&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TDP&#x00A0;&#x00A0;0201437&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK004912&#x00A0;&#x00A0; 17 Feb 2000&#x00A0;&#x00A0;08:45&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TD&#x00A0;&#x00A0; 0201282&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK004971&#x00A0;&#x00A0; 17 Feb 2000&#x00A0;&#x00A0;09:57&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;0201383&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK004908&#x00A0;&#x00A0; 17 Feb 2000&#x00A0;&#x00A0;10:17&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/MCO&#x00A0;&#x00A0;0199746&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK004967&#x00A0;&#x00A0; 18 Feb 2000&#x00A0;&#x00A0;10:39&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;0201305&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK004978&#x00A0;&#x00A0; 18 Feb 2000&#x00A0;&#x00A0;10:49&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TPI&#x00A0;&#x00A0;0201913&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK004986&#x00A0;&#x00A0; 21 Feb 2000&#x00A0;&#x00A0;15:21&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;0202267&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK004795&#x00A0;&#x00A0; 22 Feb 2000&#x00A0;&#x00A0;17:00&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TAS&#x00A0;&#x00A0;0196306&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK005002&#x00A0;&#x00A0; 22 Feb 2000&#x00A0;&#x00A0;17:01&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TPI&#x00A0;&#x00A0;0193130&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK005008&#x00A0;&#x00A0; 22 Feb 2000&#x00A0;&#x00A0;17:01&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TPI&#x00A0;&#x00A0;0202626&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK005018&#x00A0;&#x00A0; 24 Feb 2000&#x00A0;&#x00A0;19:43&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/BDR&#x00A0;&#x00A0;0203077&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK005042&#x00A0;&#x00A0; 29 Feb 2000&#x00A0;&#x00A0;12:11&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TAS&#x00A0;&#x00A0;0204005&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK005022&#x00A0;&#x00A0;29 Feb 2000&#x00A0;&#x00A0;13:49&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/HPM&#x00A0;&#x00A0;0067261&#x00A0;&#x00A0;repl. by SOEK005836</LI></UL> <UL><LI>SOEK004896&#x00A0;&#x00A0; 29 Feb 2000&#x00A0;&#x00A0;14:11&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TD&#x00A0;&#x00A0; 0198229&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK005052&#x00A0;&#x00A0; 29 Feb 2000&#x00A0;&#x00A0;16:39&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TDP&#x00A0;&#x00A0;0204158&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK005065&#x00A0;&#x00A0;&#x00A0;&#x00A0;2 Mar 2000&#x00A0;&#x00A0;18:24&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/MAP&#x00A0;&#x00A0;0204830&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK005063&#x00A0;&#x00A0;&#x00A0;&#x00A0;3 Mar 2000&#x00A0;&#x00A0;09:31&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/MAP&#x00A0;&#x00A0;0204741&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK005016&#x00A0;&#x00A0;&#x00A0;&#x00A0;3 Mar 2000&#x00A0;&#x00A0;13:25&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TAS&#x00A0;&#x00A0;0203283&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK005071&#x00A0;&#x00A0;&#x00A0;&#x00A0;6 Mar 2000&#x00A0;&#x00A0;09:09&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TDP&#x00A0;&#x00A0;0205175&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK005044&#x00A0;&#x00A0;&#x00A0;&#x00A0;7 Mar 2000&#x00A0;&#x00A0;13:32&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TD&#x00A0;&#x00A0; 0205561&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK004332&#x00A0;&#x00A0;&#x00A0;&#x00A0;8 Mar 2000&#x00A0;&#x00A0;14:41&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/HPM&#x00A0;&#x00A0;0186505&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK005081&#x00A0;&#x00A0;&#x00A0;&#x00A0;8 Mar 2000&#x00A0;&#x00A0;15:54&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/HPM&#x00A0;&#x00A0;0205759&#x00A0;&#x00A0; -</LI></UL> <UL><LI>&#x00A0;&#x00A0;&#x00A0;&#x00A0;!!!! CAUTION: Read SAPNet Note 210205 BEFORE applying SP 38&#x00A0;&#x00A0;!!!!</LI></UL> <UL><LI>SAPKI40C31&#x00A0;&#x00A0; 30 Mar 2000&#x00A0;&#x00A0;16:08&#x00A0;&#x00A0;&#x00A0;&#x00A0;CRT31</LI></UL> <UL><LI>SAPKI40C32&#x00A0;&#x00A0; 30 Mar 2000&#x00A0;&#x00A0;16:09&#x00A0;&#x00A0;&#x00A0;&#x00A0;CRT32</LI></UL> <UL><LI>SAPKI40C33&#x00A0;&#x00A0; 30 Mar 2000&#x00A0;&#x00A0;16:10&#x00A0;&#x00A0;&#x00A0;&#x00A0;CRT33</LI></UL> <UL><LI>SAPKI40C35&#x00A0;&#x00A0; 30 Mar 2000&#x00A0;&#x00A0;16:11&#x00A0;&#x00A0;&#x00A0;&#x00A0;CRT35</LI></UL> <UL><LI>SAPKI40C36&#x00A0;&#x00A0; 30 Mar 2000&#x00A0;&#x00A0;16:11&#x00A0;&#x00A0;&#x00A0;&#x00A0;CRT36</LI></UL> <UL><LI>SAPKI40C37&#x00A0;&#x00A0; 30 Mar 2000&#x00A0;&#x00A0;16:12&#x00A0;&#x00A0;&#x00A0;&#x00A0;CRT37</LI></UL> <UL><LI>SAPKI40C38&#x00A0;&#x00A0; 30 Mar 2000&#x00A0;&#x00A0;16:15&#x00A0;&#x00A0;&#x00A0;&#x00A0;CRT38</LI></UL> <UL><LI>SOEK005074&#x00A0;&#x00A0; 10 Mar 2000&#x00A0;&#x00A0;08:24&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;0205300&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK005154&#x00A0;&#x00A0; 31 Mar 2000&#x00A0;&#x00A0;14:20&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TAS&#x00A0;&#x00A0;0209689&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK005394&#x00A0;&#x00A0;&#x00A0;&#x00A0;4 Apr 2000&#x00A0;&#x00A0;17:39&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/MCOE 0211421&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK005180&#x00A0;&#x00A0;&#x00A0;&#x00A0;6 Apr 2000&#x00A0;&#x00A0;09:15&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;0207504&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK005246&#x00A0;&#x00A0; 27 Mar 2000&#x00A0;&#x00A0;16:28&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TPI&#x00A0;&#x00A0;0209999&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK004813&#x00A0;&#x00A0;&#x00A0;&#x00A0;7 Apr 2000&#x00A0;&#x00A0;10:42&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/HPM&#x00A0;&#x00A0;0196054&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK005250&#x00A0;&#x00A0;&#x00A0;&#x00A0;7 Apr 2000&#x00A0;&#x00A0;10:27&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;0210030&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK005420&#x00A0;&#x00A0;&#x00A0;&#x00A0;7 Apr 2000&#x00A0;&#x00A0;14:17&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/MCOE 0213093&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK005412&#x00A0;&#x00A0;&#x00A0;&#x00A0;7 Apr 2000&#x00A0;&#x00A0;16:05&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/HPM&#x00A0;&#x00A0;0212670&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK005424&#x00A0;&#x00A0;&#x00A0;&#x00A0;7 Apr 2000&#x00A0;&#x00A0;16:49&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TSW&#x00A0;&#x00A0;0213049&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK005432&#x00A0;&#x00A0; 11 Apr 2000&#x00A0;&#x00A0;12:03&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/MCOE 0213579&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK005446&#x00A0;&#x00A0; 11 Apr 2000&#x00A0;&#x00A0;16:32&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;0213939&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK005416&#x00A0;&#x00A0; 11 Apr 2000&#x00A0;&#x00A0;08:27&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;0213030&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK005409&#x00A0;&#x00A0; 11 Apr 2000&#x00A0;&#x00A0;11:50&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/MCO&#x00A0;&#x00A0;0212492&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK005398&#x00A0;&#x00A0; 12 Apr 2000&#x00A0;&#x00A0;16:11&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TD&#x00A0;&#x00A0; 0213757&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK005458&#x00A0;&#x00A0; 13 Apr 2000&#x00A0;&#x00A0;10:57&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/MCOE 0214346&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK005464&#x00A0;&#x00A0; 13 Apr 2000&#x00A0;&#x00A0;17:45&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;0214666&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK005011&#x00A0;&#x00A0; 14 Apr 2000&#x00A0;&#x00A0;09:32&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TPI&#x00A0;&#x00A0;0215139&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK005476&#x00A0;&#x00A0; 14 Apr 2000&#x00A0;&#x00A0;17:55&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/MCOE 0214996&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK005341&#x00A0;&#x00A0; 17 Apr 2000&#x00A0;&#x00A0;13:36&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TAS&#x00A0;&#x00A0;0210687&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK005096&#x00A0;&#x00A0; 17 Apr 2000&#x00A0;&#x00A0;13:43&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/BDRP 0206287&#x00A0;&#x00A0; X</LI></UL> <UL><LI>SOEK005422&#x00A0;&#x00A0;18 Apr 2000&#x00A0;&#x00A0;11:58&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TAS&#x00A0;&#x00A0;0215608&#x00A0;&#x00A0;repl. by SOEK007540</LI></UL> <UL><LI>SOEK005470&#x00A0;&#x00A0; 18 Apr 2000&#x00A0;&#x00A0;15:56&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TAS&#x00A0;&#x00A0;0214650&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK005512&#x00A0;&#x00A0; 19 Apr 2000&#x00A0;&#x00A0;15:07&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/MAP&#x00A0;&#x00A0;0215522&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK005494&#x00A0;&#x00A0; 19 Apr 2000&#x00A0;&#x00A0;15:12&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;0215460&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK005488&#x00A0;&#x00A0; 19 Apr 2000&#x00A0;&#x00A0;15:31&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;0215633&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK005501&#x00A0;&#x00A0; 19 Apr 2000&#x00A0;&#x00A0;16:26&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/MAP&#x00A0;&#x00A0;0215705&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK005517&#x00A0;&#x00A0; 19 Apr 2000&#x00A0;&#x00A0;20:39&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/MCOE 0216007&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK005102&#x00A0;&#x00A0; 20 Apr 2000&#x00A0;&#x00A0;09:32&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TPI&#x00A0;&#x00A0;0208587&#x00A0;&#x00A0; X</LI></UL><UL><LI>SOEK005490&#x00A0;&#x00A0; 20 Apr 2000&#x00A0;&#x00A0;10:01&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;0215636&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK005506&#x00A0;&#x00A0; 20 Apr 2000&#x00A0;&#x00A0;10:22&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TPI&#x00A0;&#x00A0;0216067&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK005480&#x00A0;&#x00A0; 20 Apr 2000&#x00A0;&#x00A0;15:13&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;0215068&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK005530&#x00A0;&#x00A0; 20 Apr 2000&#x00A0;&#x00A0;15:21&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TPI&#x00A0;&#x00A0;0216249&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK005521&#x00A0;&#x00A0; 25 Apr 2000&#x00A0;&#x00A0;12:56&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/HPM&#x00A0;&#x00A0;0216045&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK005532&#x00A0;&#x00A0; 25 Apr 2000&#x00A0;&#x00A0;12:56&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/HPM&#x00A0;&#x00A0;0216413&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK005524&#x00A0;&#x00A0; 25 Apr 2000&#x00A0;&#x00A0;13:02&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TAS&#x00A0;&#x00A0;0216079&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK005544&#x00A0;&#x00A0; 25 Apr 2000&#x00A0;&#x00A0;14:32&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/MCOE 0216522&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK005556&#x00A0;&#x00A0; 26 Apr 2000&#x00A0;&#x00A0;17:31&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TAS&#x00A0;&#x00A0;0216894&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK005468&#x00A0;&#x00A0; 27 Apr 2000&#x00A0;&#x00A0;10:34&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;0214794&#x00A0;&#x00A0; X</LI></UL><UL><LI>SOEK005564&#x00A0;&#x00A0; 27 Apr 2000&#x00A0;&#x00A0;10:58&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TAS&#x00A0;&#x00A0;0217126&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK005442&#x00A0;&#x00A0; 28 Apr 2000&#x00A0;&#x00A0;10:15&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;0213813&#x00A0;&#x00A0; X</LI></UL><UL><LI>SOEK005168&#x00A0;&#x00A0;&#x00A0;&#x00A0;1 May 2000&#x00A0;&#x00A0;12:58&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/BDRP 0215984&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK005301&#x00A0;&#x00A0;&#x00A0;&#x00A0;1 May 2000&#x00A0;&#x00A0;13:02&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TPI&#x00A0;&#x00A0;0211071&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK005568&#x00A0;&#x00A0;&#x00A0;&#x00A0;2 May 2000&#x00A0;&#x00A0;14:39&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TAS&#x00A0;&#x00A0;0300199&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK005554&#x00A0;&#x00A0;&#x00A0;&#x00A0;3 May 2000&#x00A0;&#x00A0;11:17&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TDP&#x00A0;&#x00A0;0300406&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK005558&#x00A0;&#x00A0;&#x00A0;&#x00A0;3 May 2000&#x00A0;&#x00A0;11:13&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TDP&#x00A0;&#x00A0;0300150&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK005589&#x00A0;&#x00A0;&#x00A0;&#x00A0;3 May 2000&#x00A0;&#x00A0;12:37&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TAS&#x00A0;&#x00A0;0300374&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK005585&#x00A0;&#x00A0;&#x00A0;&#x00A0;3 May 2000&#x00A0;&#x00A0;13:39&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TAS&#x00A0;&#x00A0;0300298&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK005587&#x00A0;&#x00A0;&#x00A0;&#x00A0;3 May 2000&#x00A0;&#x00A0;15:46&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TPI&#x00A0;&#x00A0;0300629&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK005616&#x00A0;&#x00A0;&#x00A0;&#x00A0;4 May 2000&#x00A0;&#x00A0;14:00&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/MCOE 0300151&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK005620&#x00A0;&#x00A0;&#x00A0;&#x00A0;4 May 2000&#x00A0;&#x00A0;14:57&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TAS&#x00A0;&#x00A0;0300849&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK004904&#x00A0;&#x00A0;&#x00A0;&#x00A0;4 May 2000&#x00A0;&#x00A0;15:05&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TPI&#x00A0;&#x00A0;0198627&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK005583&#x00A0;&#x00A0;&#x00A0;&#x00A0;4 May 2000&#x00A0;&#x00A0;15:45&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TPI&#x00A0;&#x00A0;0301014&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK005606&#x00A0;&#x00A0;&#x00A0;&#x00A0;5 May 2000&#x00A0;&#x00A0;08:33&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;0300869&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK005604&#x00A0;&#x00A0;&#x00A0;&#x00A0;5 May 2000&#x00A0;&#x00A0;14:01&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;0300650&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK005574&#x00A0;&#x00A0;&#x00A0;&#x00A0;8 May 2000&#x00A0;&#x00A0;17:04&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/HPM&#x00A0;&#x00A0;0217379&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK005602&#x00A0;&#x00A0;&#x00A0;&#x00A0;8 May 2000&#x00A0;&#x00A0;17:42&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TSW&#x00A0;&#x00A0;0300647&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK005641&#x00A0;&#x00A0;&#x00A0;&#x00A0;9 May 2000&#x00A0;&#x00A0;06:44&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TD&#x00A0;&#x00A0; 0301601&#x00A0;&#x00A0; X</LI></UL> <UL><LI>SOEK005618&#x00A0;&#x00A0;&#x00A0;&#x00A0;9 May 2000&#x00A0;&#x00A0;14:58&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/MRN&#x00A0;&#x00A0;0300784&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK005657&#x00A0;&#x00A0; 11 May 2000&#x00A0;&#x00A0;09:00&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TD&#x00A0;&#x00A0; 0302327&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK005659&#x00A0;&#x00A0; 11 May 2000&#x00A0;&#x00A0;09:25&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;0302311&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK005217&#x00A0;&#x00A0; 11 May 2000&#x00A0;&#x00A0;10:37&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TPI&#x00A0;&#x00A0;0301021&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK005677&#x00A0;&#x00A0; 11 May 2000&#x00A0;&#x00A0;11:47&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TAS&#x00A0;&#x00A0;0302746&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK005486&#x00A0;&#x00A0; 11 May 2000&#x00A0;&#x00A0;20:11&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/HPM&#x00A0;&#x00A0;0303128&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK005696&#x00A0;&#x00A0; 12 May 2000&#x00A0;&#x00A0;13:17&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/HPM&#x00A0;&#x00A0;0303089&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK005698&#x00A0;&#x00A0; 15 May 2000&#x00A0;&#x00A0;11:52&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TPI&#x00A0;&#x00A0;0303220&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK005636&#x00A0;&#x00A0; 15 May 2000&#x00A0;&#x00A0;11:53&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TAS&#x00A0;&#x00A0;0301385&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK005622&#x00A0;&#x00A0; 15 May 2000&#x00A0;&#x00A0;15:19&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TAS&#x00A0;&#x00A0;0303471&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK005611&#x00A0;&#x00A0; 16 May 2000&#x00A0;&#x00A0;14:51&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TD&#x00A0;&#x00A0; 0300744&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK005715&#x00A0;&#x00A0; 16 May 2000&#x00A0;&#x00A0;17:40&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/MAP&#x00A0;&#x00A0;0303827&#x00A0;&#x00A0; X</LI></UL><UL><LI>SOEK005717&#x00A0;&#x00A0; 17 May 2000&#x00A0;&#x00A0;08:17&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;0303797&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK005745&#x00A0;&#x00A0; 19 May 2000&#x00A0;&#x00A0;06:58&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/MAP&#x00A0;&#x00A0;0304354&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK005770&#x00A0;&#x00A0; 22 May 2000&#x00A0;&#x00A0;11:40&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;0305064&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK005753&#x00A0;&#x00A0; 24 May 2000&#x00A0;&#x00A0;09:47&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;0304626&#x00A0;&#x00A0; X</LI></UL><UL><LI>SOEK005778&#x00A0;&#x00A0; 24 May 2000&#x00A0;&#x00A0;10:04&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/MCO&#x00A0;&#x00A0;0305891&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK005024&#x00A0;&#x00A0; 24 May 2000&#x00A0;&#x00A0;10:50&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/HPM&#x00A0;&#x00A0;0203605&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK005776&#x00A0;&#x00A0; 24 May 2000&#x00A0;&#x00A0;11:13&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TAS&#x00A0;&#x00A0;0305704&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK005782&#x00A0;&#x00A0; 25 May 2000&#x00A0;&#x00A0;11:08&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TAS&#x00A0;&#x00A0;0305918&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK005345&#x00A0;&#x00A0; 30 May 2000&#x00A0;&#x00A0;15:23&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TDP&#x00A0;&#x00A0;0306286&#x00A0;&#x00A0; X</LI></UL><UL><LI>SOEK005864&#x00A0;&#x00A0; 30 May 2000&#x00A0;&#x00A0;16:45&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TDP&#x00A0;&#x00A0;0306286&#x00A0;&#x00A0; X</LI></UL><UL><LI>SOEK005821&#x00A0;&#x00A0; 31 May 2000&#x00A0;&#x00A0;11:26&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TD&#x00A0;&#x00A0; 0306295&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK005875&#x00A0;&#x00A0;&#x00A0;&#x00A0;2 Jun 2000&#x00A0;&#x00A0;10:14&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TDP&#x00A0;&#x00A0;0307949&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK005877&#x00A0;&#x00A0;&#x00A0;&#x00A0;5 Jun 2000&#x00A0;&#x00A0;12:17&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;0308026&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK005885&#x00A0;&#x00A0;&#x00A0;&#x00A0;6 Jun 2000&#x00A0;&#x00A0;08:08&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TDP&#x00A0;&#x00A0;0308460&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK005893&#x00A0;&#x00A0;&#x00A0;&#x00A0;7 Jun 2000&#x00A0;&#x00A0;09:18&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TD&#x00A0;&#x00A0; 0308633&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK005889&#x00A0;&#x00A0;&#x00A0;&#x00A0;7 Jun 2000&#x00A0;&#x00A0;11:58&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TDP&#x00A0;&#x00A0;0308544&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK005905&#x00A0;&#x00A0;&#x00A0;&#x00A0;9 Jun 2000&#x00A0;&#x00A0;11:53&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TDP&#x00A0;&#x00A0;0309329&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK005905&#x00A0;&#x00A0;&#x00A0;&#x00A0;9 Jun 2000&#x00A0;&#x00A0;11:53&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TDP&#x00A0;&#x00A0;0309329&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK005836&#x00A0;&#x00A0;&#x00A0;&#x00A0;9 Jun 2000&#x00A0;&#x00A0;14:02&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/HPM&#x00A0;&#x00A0;0067261&#x00A0;&#x00A0;repl. by SOEK006148</LI></UL> <UL><LI>SOEK005909&#x00A0;&#x00A0; 13 Jun 2000&#x00A0;&#x00A0;09:07&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TD&#x00A0;&#x00A0; 0309618&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK005907&#x00A0;&#x00A0; 13 Jun 2000&#x00A0;&#x00A0;11:10&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/MAP&#x00A0;&#x00A0;0168468&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK005762&#x00A0;&#x00A0; 13 Jun 2000&#x00A0;&#x00A0;15:51&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TPI&#x00A0;&#x00A0;0306761&#x00A0;&#x00A0; X</LI></UL><UL><LI>SOEK005802&#x00A0;&#x00A0; 13 Jun 2000&#x00A0;&#x00A0;15:52&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TPI&#x00A0;&#x00A0;0306559&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK005887&#x00A0;&#x00A0; 14 Jun 2000&#x00A0;&#x00A0;09:00&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TPI&#x00A0;&#x00A0;0308868&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK005918&#x00A0;&#x00A0; 14 Jun 2000&#x00A0;&#x00A0;09:25&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TDP&#x00A0;&#x00A0;0310043&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK005869&#x00A0;&#x00A0; 14 Jun 2000&#x00A0;&#x00A0;10:23&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TD&#x00A0;&#x00A0; 0307732&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK005926&#x00A0;&#x00A0; 14 Jun 2000&#x00A0;&#x00A0;17:22&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TD&#x00A0;&#x00A0; 0310158&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK005928&#x00A0;&#x00A0; 14 Jun 2000&#x00A0;&#x00A0;16:07&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TPI&#x00A0;&#x00A0;0310287&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK005930&#x00A0;&#x00A0; 14 Jun 2000&#x00A0;&#x00A0;17:27&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TD&#x00A0;&#x00A0; 0310095&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK005940&#x00A0;&#x00A0; 15 Jun 2000&#x00A0;&#x00A0;09:23&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TD&#x00A0;&#x00A0; 0310427&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK005942&#x00A0;&#x00A0; 15 Jun 2000&#x00A0;&#x00A0;10:46&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/MAP&#x00A0;&#x00A0;0309533&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK005911&#x00A0;&#x00A0; 16 Jun 2000&#x00A0;&#x00A0;15:12&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TDP&#x00A0;&#x00A0;0309707&#x00A0;&#x00A0; -</LI></UL><UL><LI>&#x00A0;&#x00A0;&#x00A0;&#x00A0;!!!! CAUTION: Read SAPNet Note 317518 BEFORE applying SP 39&#x00A0;&#x00A0;!!!!</LI></UL> <UL><LI>SAPKI40C39&#x00A0;&#x00A0; 12 Jul 2000&#x00A0;&#x00A0;11:28&#x00A0;&#x00A0;&#x00A0;&#x00A0;CRT39</LI></UL> <UL><LI>SAPKI40C40&#x00A0;&#x00A0; 12 Jul 2000&#x00A0;&#x00A0;11:29&#x00A0;&#x00A0;&#x00A0;&#x00A0;CRT40</LI></UL> <UL><LI>SAPKI40C41&#x00A0;&#x00A0; 12 Jul 2000&#x00A0;&#x00A0;11:30&#x00A0;&#x00A0;&#x00A0;&#x00A0;CRT41</LI></UL> <UL><LI>SAPKI40C42&#x00A0;&#x00A0; 12 Jul 2000&#x00A0;&#x00A0;12:01&#x00A0;&#x00A0;&#x00A0;&#x00A0;CRT42</LI></UL> <UL><LI>SAPKI40C44&#x00A0;&#x00A0; 12 Jul 2000&#x00A0;&#x00A0;12:03&#x00A0;&#x00A0;&#x00A0;&#x00A0;CRT44</LI></UL> <UL><LI>SOEK006280&#x00A0;&#x00A0; 14 Jul 2000&#x00A0;&#x00A0;08:54&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/HIM&#x00A0;&#x00A0;0317717&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK005952&#x00A0;&#x00A0; 14 Jul 2000&#x00A0;&#x00A0;09:01&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TD&#x00A0;&#x00A0; 0310631&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK005959&#x00A0;&#x00A0; 14 Jul 2000&#x00A0;&#x00A0;09:05&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TD&#x00A0;&#x00A0; 0310851&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK006233&#x00A0;&#x00A0; 14 Jul 2000&#x00A0;&#x00A0;09:22&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;0316453&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK006239&#x00A0;&#x00A0; 14 Jul 2000&#x00A0;&#x00A0;10:10&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TSW&#x00A0;&#x00A0;0316839&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK005956&#x00A0;&#x00A0; 14 Jul 2000&#x00A0;&#x00A0;10:15&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TPI&#x00A0;&#x00A0;0310788&#x00A0;&#x00A0; X</LI></UL><UL><LI>SOEK006237&#x00A0;&#x00A0; 14 Jul 2000&#x00A0;&#x00A0;10:16&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/BDR&#x00A0;&#x00A0;0316998&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK006243&#x00A0;&#x00A0; 14 Jul 2000&#x00A0;&#x00A0;10:41&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TPI&#x00A0;&#x00A0;0316891&#x00A0;&#x00A0; X</LI></UL><UL><LI>SOEK006201&#x00A0;&#x00A0; 14 Jul 2000&#x00A0;&#x00A0;11:01&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;0315433&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK006159&#x00A0;&#x00A0; 14 Jul 2000&#x00A0;&#x00A0;14:00&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/HPM&#x00A0;&#x00A0;0314778&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK006148&#x00A0;&#x00A0; 14 Jul 2000&#x00A0;&#x00A0;16:58&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/HPM&#x00A0;&#x00A0;0067261&#x00A0;&#x00A0; X</LI></UL><UL><LI>SOEK006230&#x00A0;&#x00A0; 14 Jul 2000&#x00A0;&#x00A0;17:58&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/MAP&#x00A0;&#x00A0;0316218&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK006228&#x00A0;&#x00A0; 17 Jul 2000&#x00A0;&#x00A0;07:39&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/BDR&#x00A0;&#x00A0;0316386&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK006251&#x00A0;&#x00A0; 17 Jul 2000&#x00A0;&#x00A0;09:55&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TPI&#x00A0;&#x00A0;0316708&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK006139&#x00A0;&#x00A0; 17 Jul 2000&#x00A0;&#x00A0;11:01&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/HPM&#x00A0;&#x00A0;0314232&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK006219&#x00A0;&#x00A0; 17 Jul 2000&#x00A0;&#x00A0;11:15&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;0316516&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK006157&#x00A0;&#x00A0; 17 Jul 2000&#x00A0;&#x00A0;12:05&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TAS&#x00A0;&#x00A0;0318040&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK006327&#x00A0;&#x00A0; 18 Jul 2000&#x00A0;&#x00A0;15:54&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;0318434&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK006331&#x00A0;&#x00A0; 18 Jul 2000&#x00A0;&#x00A0;17:09&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/MAP&#x00A0;&#x00A0;0318436&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK006247&#x00A0;&#x00A0; 19 Jul 2000&#x00A0;&#x00A0;09:23&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TD&#x00A0;&#x00A0; 0316883&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK006291&#x00A0;&#x00A0; 19 Jul 2000&#x00A0;&#x00A0;10:04&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/HPM&#x00A0;&#x00A0;0318750&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK006363&#x00A0;&#x00A0; 19 Jul 2000&#x00A0;&#x00A0;12:14&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TPI&#x00A0;&#x00A0;0318688&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK006325&#x00A0;&#x00A0; 19 Jul 2000&#x00A0;&#x00A0;14:04&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/HPM&#x00A0;&#x00A0;0318351&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK006367&#x00A0;&#x00A0; 19 Jul 2000&#x00A0;&#x00A0;16:00&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/BDR&#x00A0;&#x00A0;0318966&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK006361&#x00A0;&#x00A0; 19 Jul 2000&#x00A0;&#x00A0;17:36&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;0318652&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK006293&#x00A0;&#x00A0; 20 Jul 2000&#x00A0;&#x00A0;13:45&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/MCO&#x00A0;&#x00A0;0318839&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK006245&#x00A0;&#x00A0; 21 Jul 2000&#x00A0;&#x00A0;08:17&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TD&#x00A0;&#x00A0; 0316902&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK006369&#x00A0;&#x00A0; 21 Jul 2000&#x00A0;&#x00A0;13:21&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;0317951&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK006284&#x00A0;&#x00A0; 21 Jul 2000&#x00A0;&#x00A0;13:46&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/MCO&#x00A0;&#x00A0;0317019&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK006164&#x00A0;&#x00A0; 21 Jul 2000&#x00A0;&#x00A0;15:26&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;0314967&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK006213&#x00A0;&#x00A0; 21 Jul 2000&#x00A0;&#x00A0;16:24&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;0315730&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK006329&#x00A0;&#x00A0; 21 Jul 2000&#x00A0;&#x00A0;16:28&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TD&#x00A0;&#x00A0; 0107344&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK005913&#x00A0;&#x00A0; 24 Jul 2000&#x00A0;&#x00A0;10:43&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TDP&#x00A0;&#x00A0;0309717&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK005944&#x00A0;&#x00A0; 24 Jul 2000&#x00A0;&#x00A0;11:10&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TAS&#x00A0;&#x00A0;0310523&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK006383&#x00A0;&#x00A0; 24 Jul 2000&#x00A0;&#x00A0;11:16&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TAS&#x00A0;&#x00A0;0319796&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK006286&#x00A0;&#x00A0; 24 Jul 2000&#x00A0;&#x00A0;14:56&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TAS&#x00A0;&#x00A0;0319760&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK006333&#x00A0;&#x00A0; 24 Jul 2000&#x00A0;&#x00A0;15:57&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/HPM&#x00A0;&#x00A0;0318560&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK006372&#x00A0;&#x00A0; 25 Jul 2000&#x00A0;&#x00A0;08:21&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TD&#x00A0;&#x00A0; 0319203&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK003665&#x00A0;&#x00A0; 25 Jul 2000&#x00A0;&#x00A0;11:29&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/MAP&#x00A0;&#x00A0;0180596&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK006389&#x00A0;&#x00A0; 25 Jul 2000&#x00A0;&#x00A0;15:45&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/HPM&#x00A0;&#x00A0;0320192&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK006387&#x00A0;&#x00A0; 26 Jul 2000&#x00A0;&#x00A0;13:44&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;0320098&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK006223&#x00A0;&#x00A0; 26 Jul 2000&#x00A0;&#x00A0;15:08&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/HPM&#x00A0;&#x00A0;0320496&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK006401&#x00A0;&#x00A0; 28 Jul 2000&#x00A0;&#x00A0;08:20&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;0321127&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK004683&#x00A0;&#x00A0; 28 Jul 2000&#x00A0;&#x00A0;09:53&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/HPM&#x00A0;&#x00A0;0184259&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK006221&#x00A0;&#x00A0; 28 Jul 2000&#x00A0;&#x00A0;10:16&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/BDRP 0321135&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK006376&#x00A0;&#x00A0; 28 Jul 2000&#x00A0;&#x00A0;11:18&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/MAP&#x00A0;&#x00A0;0318202&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK006393&#x00A0;&#x00A0; 28 Jul 2000&#x00A0;&#x00A0;15:40&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/HPM&#x00A0;&#x00A0;0320810&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK006145&#x00A0;&#x00A0; 31 Jul 2000&#x00A0;&#x00A0;11:27&#x00A0;&#x00A0;&#x00A0;&#x00A0;MAP/MCOE 0314379&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK006413&#x00A0;&#x00A0; 31 Jul 2000&#x00A0;&#x00A0;16:46&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/HPM&#x00A0;&#x00A0;0321836&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK006323&#x00A0;&#x00A0;&#x00A0;&#x00A0;2 Aug 2000&#x00A0;&#x00A0;14:32&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TDP&#x00A0;&#x00A0;0318349&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK006456&#x00A0;&#x00A0;&#x00A0;&#x00A0;3 Aug 2000&#x00A0;&#x00A0;13:52&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/MAP&#x00A0;&#x00A0;0316979&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK006397&#x00A0;&#x00A0;&#x00A0;&#x00A0;3 Aug 2000&#x00A0;&#x00A0;16:22&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TD&#x00A0;&#x00A0; 0322765&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK006452&#x00A0;&#x00A0;&#x00A0;&#x00A0;4 Aug 2000&#x00A0;&#x00A0;09:27&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TSW&#x00A0;&#x00A0;0322646&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK006405&#x00A0;&#x00A0;&#x00A0;&#x00A0;4 Aug 2000&#x00A0;&#x00A0;09:50&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;0321161&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK006486&#x00A0;&#x00A0;&#x00A0;&#x00A0;4 Aug 2000&#x00A0;&#x00A0;14:45&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;0322720&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK006450&#x00A0;&#x00A0;&#x00A0;&#x00A0;7 Aug 2000&#x00A0;&#x00A0;15:25&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TD&#x00A0;&#x00A0; 0323387&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK006515&#x00A0;&#x00A0;&#x00A0;&#x00A0;7 Aug 2000&#x00A0;&#x00A0;15:43&#x00A0;&#x00A0;&#x00A0;&#x00A0;TAS&#x00A0;&#x00A0;&#x00A0;&#x00A0; 0323181&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK006517&#x00A0;&#x00A0;&#x00A0;&#x00A0;8 Aug 2000&#x00A0;&#x00A0;09:15&#x00A0;&#x00A0;&#x00A0;&#x00A0;TAS&#x00A0;&#x00A0;&#x00A0;&#x00A0; 0323129&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK006440&#x00A0;&#x00A0;&#x00A0;&#x00A0;8 Aug 2000&#x00A0;&#x00A0;11:32&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/MAP&#x00A0;&#x00A0;0321691&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK006520&#x00A0;&#x00A0;&#x00A0;&#x00A0;8 Aug 2000&#x00A0;&#x00A0;15:55&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;0323761&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK006530&#x00A0;&#x00A0;&#x00A0;&#x00A0;9 Aug 2000&#x00A0;&#x00A0;16:00&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TD&#x00A0;&#x00A0; 0324180&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK006538&#x00A0;&#x00A0; 10 Aug 2000&#x00A0;&#x00A0;09:40&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/MRN&#x00A0;&#x00A0;0324334&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK006484&#x00A0;&#x00A0; 10 Aug 2000&#x00A0;&#x00A0;17:03&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/MAP&#x00A0;&#x00A0;0322262&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK006547&#x00A0;&#x00A0; 15 Aug 2000&#x00A0;&#x00A0;08:53&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/MRN&#x00A0;&#x00A0;0324964&#x00A0;&#x00A0; X</LI></UL><UL><LI>SOEK006555&#x00A0;&#x00A0; 15 Aug 2000&#x00A0;&#x00A0;13:21&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TDP&#x00A0;&#x00A0;0325412&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK006534&#x00A0;&#x00A0;15 Aug 2000&#x00A0;&#x00A0;13:58&#x00A0;&#x00A0;&#x00A0;&#x00A0;BC&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0322879&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK006536&#x00A0;&#x00A0; 16 Aug 2000&#x00A0;&#x00A0;08:05&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/MRN&#x00A0;&#x00A0;0189339&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK006551&#x00A0;&#x00A0; 16 Aug 2000&#x00A0;&#x00A0;08:54&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/MAP&#x00A0;&#x00A0;0325472&#x00A0;&#x00A0; X</LI></UL><UL><LI>SOEK006512&#x00A0;&#x00A0; 16 Aug 2000&#x00A0;&#x00A0;13:43&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/MCO&#x00A0;&#x00A0;0325179&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK006564&#x00A0;&#x00A0;18 Aug 2000&#x00A0;&#x00A0;12:10&#x00A0;&#x00A0;&#x00A0;&#x00A0;TAS&#x00A0;&#x00A0;&#x00A0;&#x00A0; 0326517&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK006553&#x00A0;&#x00A0; 18 Aug 2000&#x00A0;&#x00A0;14:00&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TDP&#x00A0;&#x00A0;0325399&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK006562&#x00A0;&#x00A0; 18 Aug 2000&#x00A0;&#x00A0;14:24&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TSW&#x00A0;&#x00A0;0326623&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK006576&#x00A0;&#x00A0; 21 Aug 2000&#x00A0;&#x00A0;11:54&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TPI&#x00A0;&#x00A0;0326861&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK006558&#x00A0;&#x00A0; 22 Aug 2000&#x00A0;&#x00A0;10:25&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TDP&#x00A0;&#x00A0;0327413&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK006558&#x00A0;&#x00A0; 22 Aug 2000&#x00A0;&#x00A0;10:25&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TDP&#x00A0;&#x00A0;0327413&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK006578&#x00A0;&#x00A0; 22 Aug 2000&#x00A0;&#x00A0;14:06&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/BDRP 0327242&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK006580&#x00A0;&#x00A0; 22 Aug 2000&#x00A0;&#x00A0;14:08&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TPI&#x00A0;&#x00A0;0326895&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK006591&#x00A0;&#x00A0; 25 Aug 2000&#x00A0;&#x00A0;10:10&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/HPM&#x00A0;&#x00A0;0327863&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK006619&#x00A0;&#x00A0; 28 Aug 2000&#x00A0;&#x00A0;09:03&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;0328151&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK006621&#x00A0;&#x00A0;29 Aug 2000&#x00A0;&#x00A0;15:39&#x00A0;&#x00A0;&#x00A0;&#x00A0;TAS&#x00A0;&#x00A0;&#x00A0;&#x00A0; 0328266&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK006626&#x00A0;&#x00A0; 30 Aug 2000&#x00A0;&#x00A0;09:00&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/BDRP 0329200&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK006143&#x00A0;&#x00A0; 30 Aug 2000&#x00A0;&#x00A0;15:42&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/HPM&#x00A0;&#x00A0;0329203&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK006628&#x00A0;&#x00A0;&#x00A0;&#x00A0;1 Sep 2000&#x00A0;&#x00A0;12:56&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;0329466&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK006549&#x00A0;&#x00A0;&#x00A0;&#x00A0;1 Sep 2000&#x00A0;&#x00A0;12:58&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TDP&#x00A0;&#x00A0;0325052&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK006634&#x00A0;&#x00A0;&#x00A0;&#x00A0;5 Sep 2000&#x00A0;&#x00A0;11:07&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;0330214&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK006632&#x00A0;&#x00A0;&#x00A0;&#x00A0;5 Sep 2000&#x00A0;&#x00A0;15:12&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;0330194&#x00A0;&#x00A0; X</LI></UL> <UL><LI>SOEK006645&#x00A0;&#x00A0;&#x00A0;&#x00A0;7 Sep 2000&#x00A0;&#x00A0;12:20&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TSW&#x00A0;&#x00A0;0111268&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK006623&#x00A0;&#x00A0;&#x00A0;&#x00A0;7 Sep 2000&#x00A0;&#x00A0;11:20&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;0328204&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK006643&#x00A0;&#x00A0;&#x00A0;&#x00A0;8 Sep 2000&#x00A0;&#x00A0;13:33&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;0098534&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK006584&#x00A0;&#x00A0; 11 Sep 2000&#x00A0;&#x00A0;11:53&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/HPM&#x00A0;&#x00A0;0327364&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK006654&#x00A0;&#x00A0; 11 Sep 2000&#x00A0;&#x00A0;12:08&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TSW&#x00A0;&#x00A0;0331952&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK006648&#x00A0;&#x00A0; 12 Sep 2000&#x00A0;&#x00A0;11:00&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/MCO&#x00A0;&#x00A0;0331304&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK006641&#x00A0;&#x00A0; 12 Sep 2000&#x00A0;&#x00A0;11:51&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/MAP&#x00A0;&#x00A0;0332287&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK006658&#x00A0;&#x00A0; 12 Sep 2000&#x00A0;&#x00A0;12:48&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;0332092&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK006582&#x00A0;&#x00A0; 13 Sep 2000&#x00A0;&#x00A0;19:09&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/MAP&#x00A0;&#x00A0;0328086&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK006691&#x00A0;&#x00A0; 14 Sep 2000&#x00A0;&#x00A0;10:53&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TSW&#x00A0;&#x00A0;0332989&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK006652&#x00A0;&#x00A0; 15 Sep 2000&#x00A0;&#x00A0;08:23&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/MAP&#x00A0;&#x00A0;0332993&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK006697&#x00A0;&#x00A0; 20 Sep 2000&#x00A0;&#x00A0;15:11&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;0333978&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK006689&#x00A0;&#x00A0; 20 Sep 2000&#x00A0;&#x00A0;16:44&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TDP&#x00A0;&#x00A0;0332960&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK006717&#x00A0;&#x00A0; 21 Sep 2000&#x00A0;&#x00A0;16:39&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TSW&#x00A0;&#x00A0;0334795&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK006710&#x00A0;&#x00A0; 22 Sep 2000&#x00A0;&#x00A0;09:27&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TSW&#x00A0;&#x00A0;0334723&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK006725&#x00A0;&#x00A0; 25 Sep 2000&#x00A0;&#x00A0;13:33&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;0335256&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK005997&#x00A0;&#x00A0; 25 Sep 2000&#x00A0;&#x00A0;15:05&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;0312369&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK006701&#x00A0;&#x00A0; 25 Sep 2000&#x00A0;&#x00A0;16:15&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TD&#x00A0;&#x00A0; 0334462&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK006727&#x00A0;&#x00A0; 26 Sep 2000&#x00A0;&#x00A0;12:55&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/BDRP 0334920&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK006732&#x00A0;&#x00A0; 27 Sep 2000&#x00A0;&#x00A0;14:53&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/MCO&#x00A0;&#x00A0;0335955&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK006734&#x00A0;&#x00A0; 28 Sep 2000&#x00A0;&#x00A0;13:07&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TDP&#x00A0;&#x00A0;0336030&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK006572&#x00A0;&#x00A0; 28 Sep 2000&#x00A0;&#x00A0;14:06&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TDP&#x00A0;&#x00A0;0336373&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK006745&#x00A0;&#x00A0; 28 Sep 2000&#x00A0;&#x00A0;14:09&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TDP&#x00A0;&#x00A0;0336103&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK006751&#x00A0;&#x00A0; 28 Sep 2000&#x00A0;&#x00A0;14:58&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TDP&#x00A0;&#x00A0;0336440&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK006751&#x00A0;&#x00A0; 28 Sep 2000&#x00A0;&#x00A0;14:58&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TDP&#x00A0;&#x00A0;0336440&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK006756&#x00A0;&#x00A0;&#x00A0;&#x00A0;2 Oct 2000&#x00A0;&#x00A0;07:49&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/MAP&#x00A0;&#x00A0;0336109&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK006544&#x00A0;&#x00A0;&#x00A0;&#x00A0;2 Oct 2000&#x00A0;&#x00A0;11:27&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/HPM&#x00A0;&#x00A0;0336950&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK006754&#x00A0;&#x00A0;&#x00A0;&#x00A0;2 Oct 2000&#x00A0;&#x00A0;11:32&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/HPM&#x00A0;&#x00A0;0336664&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK006777&#x00A0;&#x00A0;&#x00A0;&#x00A0;9 Oct 2000&#x00A0;&#x00A0;08:05&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/MRN&#x00A0;&#x00A0;0338202&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK006781&#x00A0;&#x00A0; 11 Oct 2000&#x00A0;&#x00A0;10:08&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TD&#x00A0;&#x00A0; 0337217&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK006788&#x00A0;&#x00A0; 12 Oct 2000&#x00A0;&#x00A0;12:52&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TDP&#x00A0;&#x00A0;0339524&#x00A0;&#x00A0; -</LI></UL><UL><LI>&#x00A0;&#x00A0;&#x00A0;&#x00A0;!!!! CAUTION: Read SAPNet Note 351475 BEFORE applying SP 45&#x00A0;&#x00A0;!!!!</LI></UL> <UL><LI>SAPKI40C45&#x00A0;&#x00A0; 30 Oct 2000&#x00A0;&#x00A0;11:13&#x00A0;&#x00A0;&#x00A0;&#x00A0;CRT45</LI></UL> <UL><LI>SAPKI40C46&#x00A0;&#x00A0; 30 Oct 2000&#x00A0;&#x00A0;11:14&#x00A0;&#x00A0;&#x00A0;&#x00A0;CRT46</LI></UL> <UL><LI>SAPKI40C48&#x00A0;&#x00A0; 30 Oct 2000&#x00A0;&#x00A0;11:16&#x00A0;&#x00A0;&#x00A0;&#x00A0;CRT48</LI></UL> <UL><LI>SAPKI40C49&#x00A0;&#x00A0; 30 Oct 2000&#x00A0;&#x00A0;11:16&#x00A0;&#x00A0;&#x00A0;&#x00A0;CRT49</LI></UL> <UL><LI>SAPKI40C50&#x00A0;&#x00A0; 30 Oct 2000&#x00A0;&#x00A0;11:17&#x00A0;&#x00A0;&#x00A0;&#x00A0;CRT50</LI></UL> <UL><LI>SAPKI40C51&#x00A0;&#x00A0; 30 Oct 2000&#x00A0;&#x00A0;11:18&#x00A0;&#x00A0;&#x00A0;&#x00A0;CRT51</LI></UL> <UL><LI>SAPKI40C52&#x00A0;&#x00A0; 30 Oct 2000&#x00A0;&#x00A0;11:19&#x00A0;&#x00A0;&#x00A0;&#x00A0;CRT52</LI></UL> <UL><LI>SAPKI40C53&#x00A0;&#x00A0; 30 Oct 2000&#x00A0;&#x00A0;11:20&#x00A0;&#x00A0;&#x00A0;&#x00A0;CRT53</LI></UL> <UL><LI>SOEK006893&#x00A0;&#x00A0;&#x00A0;&#x00A0;2 Nov 2000&#x00A0;&#x00A0;09:08&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/MAP&#x00A0;&#x00A0;0351666&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK006919&#x00A0;&#x00A0;&#x00A0;&#x00A0;2 Nov 2000&#x00A0;&#x00A0;13:14&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;0353978&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK006913&#x00A0;&#x00A0;&#x00A0;&#x00A0;2 Nov 2000&#x00A0;&#x00A0;13:44&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;0353230&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK006911&#x00A0;&#x00A0;&#x00A0;&#x00A0;2 Nov 2000&#x00A0;&#x00A0;13:45&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;0352656&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK006871&#x00A0;&#x00A0;&#x00A0;&#x00A0;2 Nov 2000&#x00A0;&#x00A0;13:45&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;0338865&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK006903&#x00A0;&#x00A0;&#x00A0;&#x00A0;2 Nov 2000&#x00A0;&#x00A0;13:47&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;0351931&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK006738&#x00A0;&#x00A0;&#x00A0;&#x00A0;2 Nov 2000&#x00A0;&#x00A0;16:34&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/MAP&#x00A0;&#x00A0;0336131&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK006864&#x00A0;&#x00A0;&#x00A0;&#x00A0;6 Nov 2000&#x00A0;&#x00A0;09:26&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TPI&#x00A0;&#x00A0;0350742&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK006929&#x00A0;&#x00A0;&#x00A0;&#x00A0;6 Nov 2000&#x00A0;&#x00A0;14:35&#x00A0;&#x00A0;&#x00A0;&#x00A0;TAS&#x00A0;&#x00A0;&#x00A0;&#x00A0; 0355142&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK006758&#x00A0;&#x00A0;&#x00A0;&#x00A0;6 Nov 2000&#x00A0;&#x00A0;17:17&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TDP&#x00A0;&#x00A0;0336962&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK006866&#x00A0;&#x00A0;&#x00A0;&#x00A0;7 Nov 2000&#x00A0;&#x00A0;05:57&#x00A0;&#x00A0;&#x00A0;&#x00A0;TAS&#x00A0;&#x00A0;&#x00A0;&#x00A0; 0350766&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK006855&#x00A0;&#x00A0;&#x00A0;&#x00A0;7 Nov 2000&#x00A0;&#x00A0;08:05&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/HPM&#x00A0;&#x00A0;0352264&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK006939&#x00A0;&#x00A0;&#x00A0;&#x00A0;7 Nov 2000&#x00A0;&#x00A0;10:40&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TDP&#x00A0;&#x00A0;0355564&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK006887&#x00A0;&#x00A0;&#x00A0;&#x00A0;7 Nov 2000&#x00A0;&#x00A0;12:10&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TD&#x00A0;&#x00A0; 0351551&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK006730&#x00A0;&#x00A0;&#x00A0;&#x00A0;7 Nov 2000&#x00A0;&#x00A0;13:49&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;0335717&#x00A0;&#x00A0; X</LI></UL> <UL><LI>SOEK006935&#x00A0;&#x00A0;&#x00A0;&#x00A0;7 Nov 2000&#x00A0;&#x00A0;13:54&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TPI&#x00A0;&#x00A0;0355588&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK006875&#x00A0;&#x00A0;&#x00A0;&#x00A0;8 Nov 2000&#x00A0;&#x00A0;05:01&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TPI&#x00A0;&#x00A0;0350797&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK006947&#x00A0;&#x00A0;&#x00A0;&#x00A0;8 Nov 2000&#x00A0;&#x00A0;18:00&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TD&#x00A0;&#x00A0; 0356007&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK006945&#x00A0;&#x00A0;&#x00A0;&#x00A0;9 Nov 2000&#x00A0;&#x00A0;05:29&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/MCO&#x00A0;&#x00A0;0356080&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK006937&#x00A0;&#x00A0;&#x00A0;&#x00A0;9 Nov 2000&#x00A0;&#x00A0;18:06&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TSW&#x00A0;&#x00A0;0355654&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK006955&#x00A0;&#x00A0;10 Nov 2000&#x00A0;&#x00A0;08:57&#x00A0;&#x00A0;&#x00A0;&#x00A0;TAS&#x00A0;&#x00A0;&#x00A0;&#x00A0; 0356471&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK006907&#x00A0;&#x00A0; 10 Nov 2000&#x00A0;&#x00A0;14:27&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;0356301&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK006926&#x00A0;&#x00A0; 10 Nov 2000&#x00A0;&#x00A0;15:31&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/MAP&#x00A0;&#x00A0;0354546&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK006964&#x00A0;&#x00A0;13 Nov 2000&#x00A0;&#x00A0;16:50&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0357273&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK006761&#x00A0;&#x00A0; 14 Nov 2000&#x00A0;&#x00A0;08:14&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/MAP&#x00A0;&#x00A0;0337206&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK006154&#x00A0;&#x00A0; 16 Nov 2000&#x00A0;&#x00A0;14:00&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;0312121&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK006976&#x00A0;&#x00A0; 21 Nov 2000&#x00A0;&#x00A0;15:49&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/HPM&#x00A0;&#x00A0;0358970&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK006901&#x00A0;&#x00A0; 21 Nov 2000&#x00A0;&#x00A0;16:34&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/HPM&#x00A0;&#x00A0;0351836&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK006917&#x00A0;&#x00A0; 22 Nov 2000&#x00A0;&#x00A0;10:45&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/BDRP 0360233&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK006974&#x00A0;&#x00A0; 23 Nov 2000&#x00A0;&#x00A0;13:08&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TDP&#x00A0;&#x00A0;0358259&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK006985&#x00A0;&#x00A0; 23 Nov 2000&#x00A0;&#x00A0;13:15&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TDP&#x00A0;&#x00A0;0360432&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK006996&#x00A0;&#x00A0; 24 Nov 2000&#x00A0;&#x00A0;16:14&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;0361335&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK006988&#x00A0;&#x00A0;27 Nov 2000&#x00A0;&#x00A0;10:35&#x00A0;&#x00A0;&#x00A0;&#x00A0;TAS&#x00A0;&#x00A0;&#x00A0;&#x00A0; 0361254&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK006967&#x00A0;&#x00A0; 27 Nov 2000&#x00A0;&#x00A0;14:11&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/HPM&#x00A0;&#x00A0;0357343&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK006998&#x00A0;&#x00A0; 27 Nov 2000&#x00A0;&#x00A0;16:49&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/MAP&#x00A0;&#x00A0;0146147&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK007007&#x00A0;&#x00A0; 28 Nov 2000&#x00A0;&#x00A0;10:58&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TD&#x00A0;&#x00A0; 0361878&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK006192&#x00A0;&#x00A0; 28 Nov 2000&#x00A0;&#x00A0;11:27&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TPI&#x00A0;&#x00A0;0315091&#x00A0;&#x00A0; X</LI></UL><UL><LI>SOEK006241&#x00A0;&#x00A0;28 Nov 2000&#x00A0;&#x00A0;14:58&#x00A0;&#x00A0;&#x00A0;&#x00A0;TAS&#x00A0;&#x00A0;&#x00A0;&#x00A0; 0318707&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK007025&#x00A0;&#x00A0;30 Nov 2000&#x00A0;&#x00A0;16:22&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TDP&#x00A0;&#x00A0;0362910&#x00A0;&#x00A0;repl. by SOEK007068</LI></UL> <UL><LI>SOEK007015&#x00A0;&#x00A0; 30 Nov 2000&#x00A0;&#x00A0;14:47&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;0362516&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK007031&#x00A0;&#x00A0;&#x00A0;&#x00A0;1 Dec 2000&#x00A0;&#x00A0;09:56&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/MRN&#x00A0;&#x00A0;0363140&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK006895&#x00A0;&#x00A0;&#x00A0;&#x00A0;1 Dec 2000&#x00A0;&#x00A0;10:42&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/MRN&#x00A0;&#x00A0;0351626&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK007029&#x00A0;&#x00A0;&#x00A0;&#x00A0;4 Dec 2000&#x00A0;&#x00A0;09:47&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TD&#x00A0;&#x00A0; 0363131&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK006990&#x00A0;&#x00A0;&#x00A0;&#x00A0;6 Dec 2000&#x00A0;&#x00A0;10:24&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;0361284&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK007002&#x00A0;&#x00A0;&#x00A0;&#x00A0;6 Dec 2000&#x00A0;&#x00A0;15:26&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/MCO&#x00A0;&#x00A0;0362410&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK007009&#x00A0;&#x00A0;&#x00A0;&#x00A0;7 Dec 2000&#x00A0;&#x00A0;09:55&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;0364026&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK007045&#x00A0;&#x00A0;&#x00A0;&#x00A0;7 Dec 2000&#x00A0;&#x00A0;11:26&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;0364320&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK007049&#x00A0;&#x00A0;&#x00A0;&#x00A0;7 Dec 2000&#x00A0;&#x00A0;11:34&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TPI&#x00A0;&#x00A0;0364780&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK007057&#x00A0;&#x00A0;&#x00A0;&#x00A0;7 Dec 2000&#x00A0;&#x00A0;13:10&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TPI&#x00A0;&#x00A0;0364900&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK007055&#x00A0;&#x00A0;&#x00A0;&#x00A0;7 Dec 2000&#x00A0;&#x00A0;13:15&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TPI&#x00A0;&#x00A0;0364919&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK007059&#x00A0;&#x00A0;&#x00A0;&#x00A0;8 Dec 2000&#x00A0;&#x00A0;06:30&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;0364777&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK006972&#x00A0;&#x00A0;&#x00A0;&#x00A0;8 Dec 2000&#x00A0;&#x00A0;12:50&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/MAP&#x00A0;&#x00A0;0363115&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK007062&#x00A0;&#x00A0;&#x00A0;&#x00A0;8 Dec 2000&#x00A0;&#x00A0;14:57&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TPI&#x00A0;&#x00A0;0365418&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK007023&#x00A0;&#x00A0;&#x00A0;&#x00A0;8 Dec 2000&#x00A0;&#x00A0;16:54&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/MAP&#x00A0;&#x00A0;0363310&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK007035&#x00A0;&#x00A0;11 Dec 2000&#x00A0;&#x00A0;09:15&#x00A0;&#x00A0;&#x00A0;&#x00A0;TAS&#x00A0;&#x00A0;&#x00A0;&#x00A0; 0365694&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK007068&#x00A0;&#x00A0; 11 Dec 2000&#x00A0;&#x00A0;15:19&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TDP&#x00A0;&#x00A0;0362910&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK006980&#x00A0;&#x00A0; 12 Dec 2000&#x00A0;&#x00A0;08:31&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TD&#x00A0;&#x00A0; 0365843&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK007037&#x00A0;&#x00A0; 12 Dec 2000&#x00A0;&#x00A0;10:15&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;0365774&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK007064&#x00A0;&#x00A0; 12 Dec 2000&#x00A0;&#x00A0;14:03&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TSW&#x00A0;&#x00A0;0366305&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK007053&#x00A0;&#x00A0; 13 Dec 2000&#x00A0;&#x00A0;09:58&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TPI&#x00A0;&#x00A0;0364644&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK006960&#x00A0;&#x00A0; 13 Dec 2000&#x00A0;&#x00A0;11:23&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TDP&#x00A0;&#x00A0;0356722&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK007070&#x00A0;&#x00A0; 14 Dec 2000&#x00A0;&#x00A0;11:15&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TPI&#x00A0;&#x00A0;0367022&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK007042&#x00A0;&#x00A0; 14 Dec 2000&#x00A0;&#x00A0;15:16&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TSW&#x00A0;&#x00A0;0364092&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK007066&#x00A0;&#x00A0; 15 Dec 2000&#x00A0;&#x00A0;14:55&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/HPM&#x00A0;&#x00A0;0365908&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK007073&#x00A0;&#x00A0; 20 Dec 2000&#x00A0;&#x00A0;16:01&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/MAP&#x00A0;&#x00A0;0368749&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK007081&#x00A0;&#x00A0; 20 Dec 2000&#x00A0;&#x00A0;16:15&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;0366622&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK007087&#x00A0;&#x00A0; 22 Dec 2000&#x00A0;&#x00A0;09:30&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;0367704&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK007013&#x00A0;&#x00A0; 22 Dec 2000&#x00A0;&#x00A0;10:55&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;0362077&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK007095&#x00A0;&#x00A0; 22 Dec 2001&#x00A0;&#x00A0;13:00&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TSW&#x00A0;&#x00A0;0350486&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK006883&#x00A0;&#x00A0;&#x00A0;&#x00A0;2 Jan 2001&#x00A0;&#x00A0;07:55&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TPI&#x00A0;&#x00A0;0370619&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK007133&#x00A0;&#x00A0;&#x00A0;&#x00A0;2 Jan 2001&#x00A0;&#x00A0;07:59&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TD&#x00A0;&#x00A0; 0370008&#x00A0;&#x00A0;repl. by SOEK007196</LI></UL> <UL><LI>SOEK006775&#x00A0;&#x00A0;&#x00A0;&#x00A0;3 Jan 2001&#x00A0;&#x00A0;16:58&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/HPM&#x00A0;&#x00A0;0150132&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK007107&#x00A0;&#x00A0;&#x00A0;&#x00A0;3 Jan 2001&#x00A0;&#x00A0;17:03&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/MAP&#x00A0;&#x00A0;0363888&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK007103&#x00A0;&#x00A0;&#x00A0;&#x00A0;3 Jan 2001&#x00A0;&#x00A0;19:59&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/MAP&#x00A0;&#x00A0;0369927&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK007196&#x00A0;&#x00A0;&#x00A0;&#x00A0;4 Jan 2001&#x00A0;&#x00A0;14:00&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TD&#x00A0;&#x00A0; 0370008&#x00A0;&#x00A0;repl. by SOEK007345</LI></UL> <UL><LI>SOEK007206&#x00A0;&#x00A0; 12 Jan 2001&#x00A0;&#x00A0;11:27&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/HPM&#x00A0;&#x00A0;0372540&#x00A0;&#x00A0; -</LI></UL><UL><LI>&#x00A0;&#x00A0;&#x00A0;&#x00A0;!!!! CAUTION: Read SAPNet Note 375158 BEFORE applying SP 54&#x00A0;&#x00A0;!!!!</LI></UL> <UL><LI>SAPKI40C54&#x00A0;&#x00A0; 23 Jan 2001&#x00A0;&#x00A0;17:33&#x00A0;&#x00A0;&#x00A0;&#x00A0;CRT54</LI></UL> <UL><LI>SAPKI40C55&#x00A0;&#x00A0; 23 Jan 2001&#x00A0;&#x00A0;17:34&#x00A0;&#x00A0;&#x00A0;&#x00A0;CRT55</LI></UL> <UL><LI>SAPKI40C56&#x00A0;&#x00A0; 23 Jan 2001&#x00A0;&#x00A0;17:35&#x00A0;&#x00A0;&#x00A0;&#x00A0;CRT56</LI></UL> <UL><LI>SOEK007327&#x00A0;&#x00A0; 25 Jan 2001&#x00A0;&#x00A0;04:45&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;0374985&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK007191&#x00A0;&#x00A0; 25 Jan 2001&#x00A0;&#x00A0;09:54&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TSW&#x00A0;&#x00A0;0370775&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK007333&#x00A0;&#x00A0; 25 Jan 2001&#x00A0;&#x00A0;13:31&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;0376541&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK007004&#x00A0;&#x00A0; 29 Jan 2001&#x00A0;&#x00A0;11:06&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;0371447&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK007341&#x00A0;&#x00A0; 29 Jan 2001&#x00A0;&#x00A0;15:41&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;0377118&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK007325&#x00A0;&#x00A0; 30 Jan 2001&#x00A0;&#x00A0;10:07&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TPI&#x00A0;&#x00A0;0374846&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK007345&#x00A0;&#x00A0; 31 Jan 2001&#x00A0;&#x00A0;12:34&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TD&#x00A0;&#x00A0; 0370008&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK007162&#x00A0;&#x00A0; 31 Jan 2001&#x00A0;&#x00A0;16:44&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/HPM&#x00A0;&#x00A0;0370570&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK007347&#x00A0;&#x00A0;&#x00A0;&#x00A0;1 Feb 2001&#x00A0;&#x00A0;08:06&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TD&#x00A0;&#x00A0; 0378352&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK007204&#x00A0;&#x00A0;&#x00A0;&#x00A0;1 Feb 2001&#x00A0;&#x00A0;14:39&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/HPM&#x00A0;&#x00A0;0372080&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK007335&#x00A0;&#x00A0;&#x00A0;&#x00A0;2 Feb 2001&#x00A0;&#x00A0;10:17&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/MAP&#x00A0;&#x00A0;0376579&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK007349&#x00A0;&#x00A0;&#x00A0;&#x00A0;2 Feb 2001&#x00A0;&#x00A0;13:04&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;0378406&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK006885&#x00A0;&#x00A0;&#x00A0;&#x00A0;5 Feb 2001&#x00A0;&#x00A0;11:28&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TDP&#x00A0;&#x00A0;0360432&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK007091&#x00A0;&#x00A0;&#x00A0;&#x00A0;5 Feb 2001&#x00A0;&#x00A0;12:58&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;0368957&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK007362&#x00A0;&#x00A0;&#x00A0;&#x00A0;6 Feb 2001&#x00A0;&#x00A0;14:22&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;0379875&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK007351&#x00A0;&#x00A0;&#x00A0;&#x00A0;7 Feb 2001&#x00A0;&#x00A0;11:29&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TDP&#x00A0;&#x00A0;0378700&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK006885&#x00A0;&#x00A0;&#x00A0;&#x00A0;5 Feb 2001&#x00A0;&#x00A0;11:28&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TDP&#x00A0;&#x00A0;0360432&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK007358&#x00A0;&#x00A0;&#x00A0;&#x00A0;8 Feb 2001&#x00A0;&#x00A0;11:01&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TDP&#x00A0;&#x00A0;0379453&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK007370&#x00A0;&#x00A0;&#x00A0;&#x00A0;8 Feb 2001&#x00A0;&#x00A0;12:37&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TDP&#x00A0;&#x00A0;0379751&#x00A0;&#x00A0; X</LI></UL> <UL><LI>SOEK007389&#x00A0;&#x00A0;&#x00A0;&#x00A0;8 Feb 2001&#x00A0;&#x00A0;16:02&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;0380744&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK007317&#x00A0;&#x00A0;&#x00A0;&#x00A0;8 Feb 2001&#x00A0;&#x00A0;18:57&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/HPM&#x00A0;&#x00A0;0373605&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK007381&#x00A0;&#x00A0;&#x00A0;&#x00A0;9 Feb 2001&#x00A0;&#x00A0;08:36&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;0380451&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK007399&#x00A0;&#x00A0; 12 Feb 2001&#x00A0;&#x00A0;14:42&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TPI&#x00A0;&#x00A0;0381462&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK007354&#x00A0;&#x00A0; 13 Feb 2001&#x00A0;&#x00A0;07:23&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TD&#x00A0;&#x00A0; 0379423&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK007406&#x00A0;&#x00A0; 14 Feb 2001&#x00A0;&#x00A0;09:16&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/HPM&#x00A0;&#x00A0;0381772&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK007411&#x00A0;&#x00A0; 14 Feb 2001&#x00A0;&#x00A0;17:54&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TDP&#x00A0;&#x00A0;0381907&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK007413&#x00A0;&#x00A0; 15 Feb 2001&#x00A0;&#x00A0;09:37&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TD&#x00A0;&#x00A0; 0382255&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK006736&#x00A0;&#x00A0; 19 Feb 2001&#x00A0;&#x00A0;08:40&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TDP&#x00A0;&#x00A0;0336419&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK006694&#x00A0;&#x00A0; 19 Feb 2001&#x00A0;&#x00A0;09:23&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TDP&#x00A0;&#x00A0;0333308&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK007395&#x00A0;&#x00A0; 19 Feb 2001&#x00A0;&#x00A0;16:33&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/MAP&#x00A0;&#x00A0;0380993&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK007421&#x00A0;&#x00A0; 20 Feb 2001&#x00A0;&#x00A0;15:28&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TD&#x00A0;&#x00A0; 0383918&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK007384&#x00A0;&#x00A0; 20 Feb 2001&#x00A0;&#x00A0;16:52&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/HPM&#x00A0;&#x00A0;0380630&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK007415&#x00A0;&#x00A0; 22 Feb 2001&#x00A0;&#x00A0;08:28&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;0382846&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK007343&#x00A0;&#x00A0; 26 Feb 2001&#x00A0;&#x00A0;13:43&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/HPM&#x00A0;&#x00A0;0378285&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK007404&#x00A0;&#x00A0;&#x00A0;&#x00A0;1 Mar 2001&#x00A0;&#x00A0;08:42&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;0381616&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK007463&#x00A0;&#x00A0;&#x00A0;&#x00A0;1 Mar 2001&#x00A0;&#x00A0;11:42&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;0385889&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK007427&#x00A0;&#x00A0;&#x00A0;&#x00A0;5 Mar 2001&#x00A0;&#x00A0;14:58&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/MAP&#x00A0;&#x00A0;0384804&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK007465&#x00A0;&#x00A0;&#x00A0;&#x00A0;6 Mar 2001&#x00A0;&#x00A0;08:44&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/HPM&#x00A0;&#x00A0;0386232&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK006897&#x00A0;&#x00A0;&#x00A0;&#x00A0;7 Mar 2001&#x00A0;&#x00A0;14:04&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/HPM&#x00A0;&#x00A0;0351652&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK007431&#x00A0;&#x00A0;&#x00A0;&#x00A0;7 Mar 2001&#x00A0;&#x00A0;16:27&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/HPM&#x00A0;&#x00A0;0351945&#x00A0;&#x00A0;repl. by SOEK007519</LI></UL> <UL><LI>SOEK007480&#x00A0;&#x00A0;&#x00A0;&#x00A0;9 Mar 2001&#x00A0;&#x00A0;14:31&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TD&#x00A0;&#x00A0; 0388449&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK007484&#x00A0;&#x00A0; 12 Mar 2001&#x00A0;&#x00A0;14:22&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TPI&#x00A0;&#x00A0;0388915&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK007472&#x00A0;&#x00A0; 12 Mar 2001&#x00A0;&#x00A0;14:28&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/HPM&#x00A0;&#x00A0;0387303&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK007470&#x00A0;&#x00A0; 13 Mar 2001&#x00A0;&#x00A0;10:56&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;0386718&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK007429&#x00A0;&#x00A0; 13 Mar 2001&#x00A0;&#x00A0;15:52&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;0384877&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK007337&#x00A0;&#x00A0; 14 Mar 2001&#x00A0;&#x00A0;14:08&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TD&#x00A0;&#x00A0; 0381741&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK007486&#x00A0;&#x00A0; 14 Mar 2001&#x00A0;&#x00A0;10:28&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;0388748&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK007433&#x00A0;&#x00A0; 14 Mar 2001&#x00A0;&#x00A0;17:35&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/HPM&#x00A0;&#x00A0;0385417&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK007232&#x00A0;&#x00A0;15 Mar 2001&#x00A0;&#x00A0;10:22&#x00A0;&#x00A0;&#x00A0;&#x00A0;TAS&#x00A0;&#x00A0;&#x00A0;&#x00A0; 0361254&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK007495&#x00A0;&#x00A0; 20 Mar 2001&#x00A0;&#x00A0;14:24&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;0390711&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK007461&#x00A0;&#x00A0; 21 Mar 2001&#x00A0;&#x00A0;09:27&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TDP&#x00A0;&#x00A0;0388788&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK007502&#x00A0;&#x00A0; 22 Mar 2001&#x00A0;&#x00A0;06:30&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/MAP&#x00A0;&#x00A0;0390503&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK007512&#x00A0;&#x00A0; 22 Mar 2001&#x00A0;&#x00A0;09:11&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/MRN&#x00A0;&#x00A0;0391477&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK007508&#x00A0;&#x00A0; 22 Mar 2001&#x00A0;&#x00A0;17:00&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TDP&#x00A0;&#x00A0;0388788&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK007521&#x00A0;&#x00A0;23 Mar 2001&#x00A0;&#x00A0;15:14&#x00A0;&#x00A0;&#x00A0;&#x00A0;TAS&#x00A0;&#x00A0;&#x00A0;&#x00A0; 0392082&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK007514&#x00A0;&#x00A0; 26 Mar 2001&#x00A0;&#x00A0;08:15&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;0391987&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK007523&#x00A0;&#x00A0; 26 Mar 2001&#x00A0;&#x00A0;09:24&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TPI&#x00A0;&#x00A0;0392231&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK007525&#x00A0;&#x00A0; 26 Mar 2001&#x00A0;&#x00A0;11:29&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TPI&#x00A0;&#x00A0;0392236&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK007510&#x00A0;&#x00A0; 26 Mar 2001&#x00A0;&#x00A0;13:36&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;0391448&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK007331&#x00A0;&#x00A0;27 Mar 2001&#x00A0;&#x00A0;11:17&#x00A0;&#x00A0;&#x00A0;&#x00A0;MCOE&#x00A0;&#x00A0;&#x00A0;&#x00A0;0392074&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK007531&#x00A0;&#x00A0; 27 Mar 2001&#x00A0;&#x00A0;19:14&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TD&#x00A0;&#x00A0; 0381741&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK005994&#x00A0;&#x00A0;28 Mar 2001&#x00A0;&#x00A0;14:11&#x00A0;&#x00A0;&#x00A0;&#x00A0;TAS&#x00A0;&#x00A0;&#x00A0;&#x00A0; 0312246&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK007540&#x00A0;&#x00A0;28 Mar 2001&#x00A0;&#x00A0;15:25&#x00A0;&#x00A0;&#x00A0;&#x00A0;TAS&#x00A0;&#x00A0;&#x00A0;&#x00A0; 0215608&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK007542&#x00A0;&#x00A0; 29 Mar 2001&#x00A0;&#x00A0;14:02&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TD&#x00A0;&#x00A0; 0393390&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK007536&#x00A0;&#x00A0; 29 Mar 2001&#x00A0;&#x00A0;14:58&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;0392985&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK007379&#x00A0;&#x00A0; 30 Mar 2001&#x00A0;&#x00A0;14:01&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/HPM&#x00A0;&#x00A0;0380390&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK007529&#x00A0;&#x00A0; 30 Mar 2001&#x00A0;&#x00A0;14:01&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/MCO&#x00A0;&#x00A0;0392666&#x00A0;&#x00A0; X</LI></UL><UL><LI>&#x00A0;&#x00A0;&#x00A0;&#x00A0;!!!! CAUTION: Read SAPNet Note 397831 BEFORE applying SP 57&#x00A0;&#x00A0;!!!!</LI></UL> <UL><LI>SAPKI40C57&#x00A0;&#x00A0; 20 Apr 2001&#x00A0;&#x00A0;11:00&#x00A0;&#x00A0;&#x00A0;&#x00A0;CRT57</LI></UL> <UL><LI>SAPKI40C58&#x00A0;&#x00A0; 20 Apr 2001&#x00A0;&#x00A0;11:05&#x00A0;&#x00A0;&#x00A0;&#x00A0;CRT58</LI></UL> <UL><LI>SAPKI40C59&#x00A0;&#x00A0; 20 Apr 2001&#x00A0;&#x00A0;11:05&#x00A0;&#x00A0;&#x00A0;&#x00A0;CRT59</LI></UL> <UL><LI>SAPKI40C60&#x00A0;&#x00A0; 20 Apr 2001&#x00A0;&#x00A0;11:07&#x00A0;&#x00A0;&#x00A0;&#x00A0;CRT60</LI></UL> <UL><LI>SOEK007603&#x00A0;&#x00A0; 20 Apr 2001&#x00A0;&#x00A0;14:26&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/HPM&#x00A0;&#x00A0;0397003&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK007595&#x00A0;&#x00A0; 24 Apr 2001&#x00A0;&#x00A0;10:06&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TDP&#x00A0;&#x00A0;0395874&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK007608&#x00A0;&#x00A0; 24 Apr 2001&#x00A0;&#x00A0;11:28&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/HPM&#x00A0;&#x00A0;0397101&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK007630&#x00A0;&#x00A0; 24 Apr 2001&#x00A0;&#x00A0;13:33&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TPI&#x00A0;&#x00A0;0398747&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK007402&#x00A0;&#x00A0; 25 Apr 2001&#x00A0;&#x00A0;09:36&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TDP&#x00A0;&#x00A0;0381516&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK007558&#x00A0;&#x00A0; 25 Apr 2001&#x00A0;&#x00A0;09:38&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TDP&#x00A0;&#x00A0;0394795&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK007417&#x00A0;&#x00A0; 25 Apr 2001&#x00A0;&#x00A0;11:21&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TPI&#x00A0;&#x00A0;0398954&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK007634&#x00A0;&#x00A0; 25 Apr 2001&#x00A0;&#x00A0;12:10&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TD&#x00A0;&#x00A0; 0399019&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK007593&#x00A0;&#x00A0; 25 Apr 2001&#x00A0;&#x00A0;18:16&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TDP&#x00A0;&#x00A0;0395803&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK007590&#x00A0;&#x00A0; 26 Apr 2001&#x00A0;&#x00A0;08:53&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TD&#x00A0;&#x00A0; 0396242&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK007632&#x00A0;&#x00A0; 26 Apr 2001&#x00A0;&#x00A0;14:15&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/HPM&#x00A0;&#x00A0;0398764&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK007551&#x00A0;&#x00A0; 27 Apr 2001&#x00A0;&#x00A0;13:00&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;0392391&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK007644&#x00A0;&#x00A0;&#x00A0;&#x00A0;2 May 2001&#x00A0;&#x00A0;08:35&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TPI&#x00A0;&#x00A0;0400071&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK007642&#x00A0;&#x00A0;&#x00A0;&#x00A0;3 May 2001&#x00A0;&#x00A0;17:07&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TDP&#x00A0;&#x00A0;0399436&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK007652&#x00A0;&#x00A0;&#x00A0;&#x00A0;7 May 2001&#x00A0;&#x00A0;12:49&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/HPM&#x00A0;&#x00A0;0392647&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK007656&#x00A0;&#x00A0;&#x00A0;&#x00A0;8 May 2001&#x00A0;&#x00A0;11:04&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TDP&#x00A0;&#x00A0;0401593&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK007666&#x00A0;&#x00A0;11 May 2001&#x00A0;&#x00A0;09:32&#x00A0;&#x00A0;&#x00A0;&#x00A0;TAS&#x00A0;&#x00A0;&#x00A0;&#x00A0; 0402561&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK007646&#x00A0;&#x00A0; 11 May 2001&#x00A0;&#x00A0;11:06&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/MAP&#x00A0;&#x00A0;0400419&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK007668&#x00A0;&#x00A0; 11 May 2001&#x00A0;&#x00A0;13:56&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;0402296&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK007675&#x00A0;&#x00A0; 14 May 2001&#x00A0;&#x00A0;11:44&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TPI&#x00A0;&#x00A0;0403030&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK007648&#x00A0;&#x00A0; 15 May 2001&#x00A0;&#x00A0;07:31&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/HPM&#x00A0;&#x00A0;0401341&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK007670&#x00A0;&#x00A0; 16 May 2001&#x00A0;&#x00A0;16:00&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TDP&#x00A0;&#x00A0;0402367&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK007673&#x00A0;&#x00A0; 18 May 2001&#x00A0;&#x00A0;14:23&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/HPM&#x00A0;&#x00A0;0403703&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK007684&#x00A0;&#x00A0; 18 May 2001&#x00A0;&#x00A0;16:50&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/HPM&#x00A0;&#x00A0;0406580&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK007519&#x00A0;&#x00A0; 21 May 2001&#x00A0;&#x00A0;14:26&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/HPM&#x00A0;&#x00A0;0351945&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK007682&#x00A0;&#x00A0; 22 May 2001&#x00A0;&#x00A0;14:43&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TDP&#x00A0;&#x00A0;0406284&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK007691&#x00A0;&#x00A0; 23 May 2001&#x00A0;&#x00A0;13:14&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/MAP&#x00A0;&#x00A0;0384863&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK006978&#x00A0;&#x00A0; 28 May 2001&#x00A0;&#x00A0;15:21&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TDP&#x00A0;&#x00A0;0358972&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK007698&#x00A0;&#x00A0; 29 May 2001&#x00A0;&#x00A0;14:11&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/MCO&#x00A0;&#x00A0;0408461&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK007696&#x00A0;&#x00A0; 31 May 2001&#x00A0;&#x00A0;09:45&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/MCO&#x00A0;&#x00A0;0407729&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK007679&#x00A0;&#x00A0;&#x00A0;&#x00A0;5 Jun 2001&#x00A0;&#x00A0;17:03&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/HPM&#x00A0;&#x00A0;0403310&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK007709&#x00A0;&#x00A0;&#x00A0;&#x00A0;7 Jun 2001&#x00A0;&#x00A0;09:29&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TPI&#x00A0;&#x00A0;0411207&#x00A0;&#x00A0; -</LI></UL> <UL><LI>SOEK007660&#x00A0;&#x00A0;&#x00A0;&#x00A0;7 Jun 2001&#x00A0;&#x00A0;10:53&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TD&#x00A0;&#x00A0; 0406047&#x00A0;&#x00A0; X</LI></UL> <UL><LI>SOEK007716&#x00A0;&#x00A0; 11 Jun 2001&#x00A0;&#x00A0;11:49&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/TD&#x00A0;&#x00A0; 0396311&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK007664&#x00A0;&#x00A0; 12 Jun 2001&#x00A0;&#x00A0;16:11&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;0401810&#x00A0;&#x00A0; -</LI></UL><UL><LI>SOEK007725&#x00A0;&#x00A0; 13 Jun 2001&#x00A0;&#x00A0;13:41&#x00A0;&#x00A0;&#x00A0;&#x00A0;DS/MAP&#x00A0;&#x00A0;0411898&#x00A0;&#x00A0; -</LI></UL><UL><LI>&#x00A0;&#x00A0;&#x00A0;&#x00A0;!!!! CAUTION: Read SAPNet Note 415989 BEFORE applying SP 61&#x00A0;&#x00A0;!!!!</LI></UL> <UL><LI>SAPKI40C61&#x00A0;&#x00A0; 28 Jun 2001&#x00A0;&#x00A0;14:11&#x00A0;&#x00A0;&#x00A0;&#x00A0;CRT61</LI></UL> <UL><LI>SAPKI40C62&#x00A0;&#x00A0; 28 Jun 2001&#x00A0;&#x00A0;14:12&#x00A0;&#x00A0;&#x00A0;&#x00A0;CRT62</LI></UL> <UL><LI>SOEK007805&#x00A0;&#x00A0; 3 Jul 2001 11:57&#x00A0;&#x00A0;DS/MAP&#x00A0;&#x00A0;&#x00A0;&#x00A0; 0416919 -</LI></UL> <UL><LI>SOEK007776&#x00A0;&#x00A0; 4 Jul 2001 07:34&#x00A0;&#x00A0;DS/BDRP&#x00A0;&#x00A0;&#x00A0;&#x00A0;0414197 -</LI></UL> <UL><LI>SOEK007780&#x00A0;&#x00A0; 4 Jul 2001 07:35&#x00A0;&#x00A0;DS/BDRP&#x00A0;&#x00A0;&#x00A0;&#x00A0;0414289 -</LI></UL> <UL><LI>SOEK007807&#x00A0;&#x00A0; 4 Jul 2001 08:49&#x00A0;&#x00A0;DS/TPI&#x00A0;&#x00A0;&#x00A0;&#x00A0; 0417113 -</LI></UL> <UL><LI>SOEK007788&#x00A0;&#x00A0; 4 Jul 2001 08:53&#x00A0;&#x00A0;DS/TD&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0415005 -</LI></UL> <UL><LI>SOEK007797&#x00A0;&#x00A0; 4 Jul 2001 11:10&#x00A0;&#x00A0;DS/BDRP&#x00A0;&#x00A0;&#x00A0;&#x00A0;0411413 -</LI></UL> <UL><LI>SOEK007706&#x00A0;&#x00A0; 5 Jul 2001 13:27&#x00A0;&#x00A0;DS/MAP&#x00A0;&#x00A0;&#x00A0;&#x00A0; 0409269 -</LI></UL> <UL><LI>SOEK007727&#x00A0;&#x00A0; 5 Jul 2001 14:57&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;&#x00A0;&#x00A0; 0412012 -</LI></UL> <UL><LI>SOEK007814&#x00A0;&#x00A0; 6 Jul 2001 06:53&#x00A0;&#x00A0;DS/TDP&#x00A0;&#x00A0;&#x00A0;&#x00A0; 0417679 -</LI></UL> <UL><LI>SOEK007794&#x00A0;&#x00A0; 9 Jul 2001 11:25&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;&#x00A0;&#x00A0; 0392597 -</LI></UL> <UL><LI>SOEK007746&#x00A0;&#x00A0; 9 Jul 2001 11:45&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;&#x00A0;&#x00A0; 0413748 -</LI></UL> <UL><LI>SOEK007818&#x00A0;&#x00A0; 9 Jul 2001 17:39&#x00A0;&#x00A0;DS/MCO&#x00A0;&#x00A0;&#x00A0;&#x00A0; 0414782 -</LI></UL> <UL><LI>SOEK007754&#x00A0;&#x00A0;10 Jul 2001 14:54&#x00A0;&#x00A0;DS/HPM&#x00A0;&#x00A0;&#x00A0;&#x00A0; 0412858 -</LI></UL> <UL><LI>SOEK007801&#x00A0;&#x00A0;10 Jul 2001 15:42&#x00A0;&#x00A0;DS/HPM&#x00A0;&#x00A0;&#x00A0;&#x00A0; 0416839 -</LI></UL> <UL><LI>SOEK007812&#x00A0;&#x00A0;11 Jul 2001 11:14&#x00A0;&#x00A0;DS/TD&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0418672 -</LI></UL> <UL><LI>SOEK007803&#x00A0;&#x00A0;12 Jul 2001 11:33&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;&#x00A0;&#x00A0; 0416930 -</LI></UL> <UL><LI>SOEK007822&#x00A0;&#x00A0;19 Jul 2001 09:14&#x00A0;&#x00A0;DS/HPM&#x00A0;&#x00A0;&#x00A0;&#x00A0; 0420838 -</LI></UL> <UL><LI>SOEK007830&#x00A0;&#x00A0;19 Jul 2001 14:55&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;&#x00A0;&#x00A0; 0419887 -</LI></UL> <UL><LI>SOEK007834&#x00A0;&#x00A0;20 Jul 2001 06:36&#x00A0;&#x00A0;DS/HPM&#x00A0;&#x00A0;&#x00A0;&#x00A0; 0421035 -</LI></UL> <UL><LI>SOEK007840&#x00A0;&#x00A0;20 Jul 2001 12:01&#x00A0;&#x00A0;DS/TSW&#x00A0;&#x00A0;&#x00A0;&#x00A0; 0421494 -</LI></UL> <UL><LI>SOEK007712&#x00A0;&#x00A0;20 Jul 2001 13:12&#x00A0;&#x00A0;DS/TPI&#x00A0;&#x00A0;&#x00A0;&#x00A0; 0420585 X</LI></UL> <UL><LI>SOEK007838&#x00A0;&#x00A0;23 Jul 2001 13:03&#x00A0;&#x00A0;DS/HPM&#x00A0;&#x00A0;&#x00A0;&#x00A0; 0421302 -</LI></UL> <UL><LI>SOEK007828&#x00A0;&#x00A0;23 Jul 2001 15:06&#x00A0;&#x00A0;DS/TDP&#x00A0;&#x00A0;&#x00A0;&#x00A0; 0420095 -</LI></UL> <UL><LI>SOEK007820&#x00A0;&#x00A0;24 Jul 2001 10:34&#x00A0;&#x00A0;DS/TDP&#x00A0;&#x00A0;&#x00A0;&#x00A0; 0422007 -</LI></UL> <UL><LI>SOEK007851&#x00A0;&#x00A0;26 Jul 2001 12:21&#x00A0;&#x00A0;DS/TD&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0422509 -</LI></UL> <UL><LI>SOEK007853&#x00A0;&#x00A0;26 Jul 2001 12:32&#x00A0;&#x00A0;DS/TD&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0422820 -</LI></UL> <UL><LI>SOEK007855&#x00A0;&#x00A0;30 Jul 2001 14:21&#x00A0;&#x00A0;DS/MCO&#x00A0;&#x00A0;&#x00A0;&#x00A0; 0423369 -</LI></UL> <UL><LI>SOEK007846&#x00A0;&#x00A0;31 Jul 2001 07:31&#x00A0;&#x00A0;TAS&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0423668 -</LI></UL> <UL><LI>SOEK007844&#x00A0;&#x00A0; 2 Aug 2001 07:47&#x00A0;&#x00A0;DS/HPM&#x00A0;&#x00A0;&#x00A0;&#x00A0; 0421873 -</LI></UL> <UL><LI>SOEK007871&#x00A0;&#x00A0;10 Aug 2001 11:50&#x00A0;&#x00A0;DS/MCO&#x00A0;&#x00A0;&#x00A0;&#x00A0; 0426588 -</LI></UL> <UL><LI>SOEK007869&#x00A0;&#x00A0;13 Aug 2001 09:34&#x00A0;&#x00A0;DS/MRN&#x00A0;&#x00A0;&#x00A0;&#x00A0; 0426567 -</LI></UL> <UL><LI>SOEK007873&#x00A0;&#x00A0;14 Aug 2001 13:07&#x00A0;&#x00A0;DS/TD&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0425216 -</LI></UL> <UL><LI>SOEK007861&#x00A0;&#x00A0;15 Aug 2001 13:31&#x00A0;&#x00A0;DS/HPM&#x00A0;&#x00A0;&#x00A0;&#x00A0; 0426381 -</LI></UL> <UL><LI>SOEK007879&#x00A0;&#x00A0;16 Aug 2001 11:03&#x00A0;&#x00A0;DS/MAP&#x00A0;&#x00A0;&#x00A0;&#x00A0; 0427650 -</LI></UL> <UL><LI>SOEK007890&#x00A0;&#x00A0;20 Aug 2001 13:27&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;&#x00A0;&#x00A0; 0428275 -</LI></UL> <UL><LI>SOEK007097&#x00A0;&#x00A0;21 Aug 2001 13:29&#x00A0;&#x00A0;DS/TDP&#x00A0;&#x00A0;&#x00A0;&#x00A0; 0369591 -</LI></UL> <UL><LI>SOEK007897&#x00A0;&#x00A0;21 Aug 2001 14:24&#x00A0;&#x00A0;DS/MRN&#x00A0;&#x00A0;&#x00A0;&#x00A0; 0429002 -</LI></UL> <UL><LI>SOEK007881&#x00A0;&#x00A0;23 Aug 2001 16:05&#x00A0;&#x00A0;DS/HPM&#x00A0;&#x00A0;&#x00A0;&#x00A0; 0424517 -</LI></UL> <UL><LI>SOEK007908&#x00A0;&#x00A0; 3 Sep 2001 15:34&#x00A0;&#x00A0;DS/TSW&#x00A0;&#x00A0;&#x00A0;&#x00A0; 0431985 -</LI></UL> <UL><LI>&#x00A0;&#x00A0;&#x00A0;&#x00A0;!!!! CAUTION: Read SAPNet Note 434924 BEFORE applying SP 63&#x00A0;&#x00A0;!!!!</LI></UL> <UL><LI>SAPKI40C63&#x00A0;&#x00A0; 13 Sep 2001&#x00A0;&#x00A0;15:00&#x00A0;&#x00A0;&#x00A0;&#x00A0;CRT63</LI></UL> <UL><LI>SAPKI40C64&#x00A0;&#x00A0; 13 Sep 2001&#x00A0;&#x00A0;15:00&#x00A0;&#x00A0;&#x00A0;&#x00A0;CRT64</LI></UL> <UL><LI>SAPKI40C65&#x00A0;&#x00A0; 13 Sep 2001&#x00A0;&#x00A0;15:00&#x00A0;&#x00A0;&#x00A0;&#x00A0;CRT65</LI></UL> <UL><LI>SOEK007391&#x00A0;&#x00A0;13 Sep 2001 13:31&#x00A0;&#x00A0;DS/TDP&#x00A0;&#x00A0;&#x00A0;&#x00A0; 0431481 -</LI></UL> <UL><LI>SOEK007973&#x00A0;&#x00A0;14 Sep 2001 08:54&#x00A0;&#x00A0;DS/MRN&#x00A0;&#x00A0;&#x00A0;&#x00A0; 0429002 -</LI></UL> <UL><LI>SOEK007886&#x00A0;&#x00A0;17 Sep 2001 15:42&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;&#x00A0;&#x00A0; 0427847 -</LI></UL> <UL><LI>SOEK007848&#x00A0;&#x00A0;19 Sep 2001 07:23&#x00A0;&#x00A0;TAS&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0429059 -</LI></UL> <UL><LI>SOEK007963&#x00A0;&#x00A0;20 Sep 2001 16:34&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;&#x00A0;&#x00A0; 0432574 -</LI></UL> <UL><LI>SOEK007986&#x00A0;&#x00A0;21 Sep 2001 15:23&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;&#x00A0;&#x00A0; 0436699 -</LI></UL> <UL><LI>SOEK007993&#x00A0;&#x00A0;26 Sep 2001 15:51&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;&#x00A0;&#x00A0; 0438098 -</LI></UL> <UL><LI>SOEK007960&#x00A0;&#x00A0;27 Sep 2001 10:14&#x00A0;&#x00A0;DS/MCO&#x00A0;&#x00A0;&#x00A0;&#x00A0; 0433834 X</LI></UL> <UL><LI>SOEK007995&#x00A0;&#x00A0;27 Sep 2001 10:45&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;&#x00A0;&#x00A0; 0438227 -</LI></UL> <UL><LI>SOEK007877&#x00A0;&#x00A0;27 Sep 2001 15:49&#x00A0;&#x00A0;DS/HPM&#x00A0;&#x00A0;&#x00A0;&#x00A0; 0427899 -</LI></UL> <UL><LI>SOEK008001&#x00A0;&#x00A0;27 Sep 2001 17:20&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;&#x00A0;&#x00A0; 0438767 -</LI></UL> <UL><LI>SOEK007975&#x00A0;&#x00A0; 8 Oct 2001 14:14&#x00A0;&#x00A0;DS/TD&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0439036 -</LI></UL> <UL><LI>SOEK007971&#x00A0;&#x00A0; 8 Oct 2001 15:52&#x00A0;&#x00A0;DS/MCO&#x00A0;&#x00A0;&#x00A0;&#x00A0; 0434809 -</LI></UL> <UL><LI>SOEK008030&#x00A0;&#x00A0;15 Oct 2001 16:33&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;&#x00A0;&#x00A0; 0438549 -</LI></UL> <UL><LI>SOEK008009&#x00A0;&#x00A0;15 Oct 2001 16:58&#x00A0;&#x00A0;DS/HPM&#x00A0;&#x00A0;&#x00A0;&#x00A0; 0427316 -</LI></UL> <UL><LI>SOEK007968&#x00A0;&#x00A0;17 Oct 2001 13:33&#x00A0;&#x00A0;DS/TD&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0443421 -</LI></UL> <UL><LI>SOEK008025&#x00A0;&#x00A0;17 Oct 2001 15:14&#x00A0;&#x00A0;DS/MAP&#x00A0;&#x00A0;&#x00A0;&#x00A0; 0442303 -</LI></UL> <UL><LI>SOEK008019&#x00A0;&#x00A0;17 Oct 2001 15:46&#x00A0;&#x00A0;DS/TD&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0441786 -</LI></UL> <UL><LI>SOEK008027&#x00A0;&#x00A0;30 Oct 2001 11:43&#x00A0;&#x00A0;DS/HPM&#x00A0;&#x00A0;&#x00A0;&#x00A0; 0413635 -</LI></UL> <UL><LI>SOEK008067&#x00A0;&#x00A0;31 Oct 2001 10:12&#x00A0;&#x00A0;DS/TD&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0445741 -</LI></UL> <UL><LI>SOEK008069&#x00A0;&#x00A0;31 Oct 2001 10:45&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;&#x00A0;&#x00A0; 0445150 -</LI></UL> <UL><LI>SOEK008115&#x00A0;&#x00A0; 5 Nov 2001 11:09&#x00A0;&#x00A0;TAS&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0448020 -</LI></UL> <UL><LI>SOEK008149&#x00A0;&#x00A0; 8 Nov 2001 15:06&#x00A0;&#x00A0;DS/HPM&#x00A0;&#x00A0;&#x00A0;&#x00A0; 0067261 -</LI></UL> <UL><LI>SOEK008073&#x00A0;&#x00A0; 9 Nov 2001 13:46&#x00A0;&#x00A0;DS/MCO&#x00A0;&#x00A0;&#x00A0;&#x00A0; 0446350 -</LI></UL> <UL><LI>SOEK008155&#x00A0;&#x00A0;12 Nov 2001 17:00&#x00A0;&#x00A0;DS/TDP&#x00A0;&#x00A0;&#x00A0;&#x00A0; 0449723 -</LI></UL> <UL><LI>SOEK008175&#x00A0;&#x00A0;14 Nov 2001 16:04&#x00A0;&#x00A0;DS/TDP&#x00A0;&#x00A0;&#x00A0;&#x00A0; 0451134 -</LI></UL> <UL><LI>SOEK008173&#x00A0;&#x00A0;14 Nov 2001 17:33&#x00A0;&#x00A0;DS/HPM&#x00A0;&#x00A0;&#x00A0;&#x00A0; 0451143 -</LI></UL> <UL><LI>SOEK008153&#x00A0;&#x00A0;15 Nov 2001 13:18&#x00A0;&#x00A0;DS/TPI&#x00A0;&#x00A0;&#x00A0;&#x00A0; 0451521 X</LI></UL> <UL><LI>SOEK007922&#x00A0;&#x00A0;15 Nov 2001 13:30&#x00A0;&#x00A0;DS/TDP&#x00A0;&#x00A0;&#x00A0;&#x00A0; 0431050 -</LI></UL> <UL><LI>SOEK008182&#x00A0;&#x00A0;16 Nov 2001 09:32&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;&#x00A0;&#x00A0; 0451394 -</LI></UL> <UL><LI>SOEK008195&#x00A0;&#x00A0;19 Nov 2001 10:05&#x00A0;&#x00A0;DS/TDP&#x00A0;&#x00A0;&#x00A0;&#x00A0; 0388602 -</LI></UL> <UL><LI>SOEK008191&#x00A0;&#x00A0;20 Nov 2001 11:42&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;&#x00A0;&#x00A0; 0451483 -</LI></UL> <UL><LI>SOEK008119&#x00A0;&#x00A0;21 Nov 2001 10:27&#x00A0;&#x00A0;DS/TSW&#x00A0;&#x00A0;&#x00A0;&#x00A0; 0448454 -</LI></UL> <UL><LI>SOEK008209&#x00A0;&#x00A0;21 Nov 2001 11:58&#x00A0;&#x00A0;DS/TPI&#x00A0;&#x00A0;&#x00A0;&#x00A0; 0453208 -</LI></UL> <UL><LI>SOEK008215&#x00A0;&#x00A0;21 Nov 2001 12:27&#x00A0;&#x00A0;TAS&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0449455 -</LI></UL> <UL><LI>SOEK008071&#x00A0;&#x00A0;21 Nov 2001 13:30&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;&#x00A0;&#x00A0; 0446038 -</LI></UL> <UL><LI>SOEK008212&#x00A0;&#x00A0;21 Nov 2001 13:31&#x00A0;&#x00A0;DS/TD&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0453243 -</LI></UL> <UL><LI>&#x00A0;&#x00A0;&#x00A0;&#x00A0;!!!! CAUTION: Read SAPNet Note 458906 BEFORE applying SP 66&#x00A0;&#x00A0;!!!!</LI></UL> <UL><LI>SAPKI40C66&#x00A0;&#x00A0;11 Dec 2001 11:45&#x00A0;&#x00A0;CRT66</LI></UL> <UL><LI>SAPKI40C67&#x00A0;&#x00A0;11 Dec 2001 11:45&#x00A0;&#x00A0;CRT67</LI></UL> <UL><LI>SAPKI40C68&#x00A0;&#x00A0;11 Dec 2001 11:45&#x00A0;&#x00A0;CRT68</LI></UL> <UL><LI>SOEK008265&#x00A0;&#x00A0;11 Dec 2001 14:29&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;&#x00A0;&#x00A0; 0458144 -</LI></UL> <UL><LI>SOEK008242&#x00A0;&#x00A0;11 Dec 2001 15:05&#x00A0;&#x00A0;DS/EXG&#x00A0;&#x00A0;&#x00A0;&#x00A0; 0455631 -</LI></UL> <UL><LI>SOEK008257&#x00A0;&#x00A0;11 Dec 2001 19:06&#x00A0;&#x00A0;DS/TDP&#x00A0;&#x00A0;&#x00A0;&#x00A0; 0457770 -</LI></UL> <UL><LI>SOEK008259&#x00A0;&#x00A0;12 Dec 2001 10:29&#x00A0;&#x00A0;DS/TDP&#x00A0;&#x00A0;&#x00A0;&#x00A0; 0458705 -</LI></UL> <UL><LI>SOEK008193&#x00A0;&#x00A0;12 Dec 2001 14:38&#x00A0;&#x00A0;DS/TDP&#x00A0;&#x00A0;&#x00A0;&#x00A0; 0452122 -</LI></UL> <UL><LI>SOEK008277&#x00A0;&#x00A0;13 Dec 2001 11:44&#x00A0;&#x00A0;DS/MAP&#x00A0;&#x00A0;&#x00A0;&#x00A0; 0459644 X</LI></UL> <UL><LI>SOEK008246&#x00A0;&#x00A0;13 Dec 2001 14:59&#x00A0;&#x00A0;DS/TSW&#x00A0;&#x00A0;&#x00A0;&#x00A0; 0456603 -</LI></UL> <UL><LI>SOEK008189&#x00A0;&#x00A0;13 Dec 2001 15:05&#x00A0;&#x00A0;DS/TD&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0455166 -</LI></UL> <UL><LI>SOEK008261&#x00A0;&#x00A0;13 Dec 2001 15:33&#x00A0;&#x00A0;DS/TD&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0459951 -</LI></UL> <UL><LI>SOEK007832&#x00A0;&#x00A0;14 Dec 2001 10:10&#x00A0;&#x00A0;DS/TDP&#x00A0;&#x00A0;&#x00A0;&#x00A0; 0460234 -</LI></UL> <UL><LI>SOEK008227&#x00A0;&#x00A0;19 Dec 2001 12:26 TAS&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 0454030 -</LI></UL> <UL><LI>SOEK008267&#x00A0;&#x00A0;19 Dec 2001 12:29 DS/EXG&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0458242 -</LI></UL> <UL><LI>SOEK008275&#x00A0;&#x00A0;19 Dec 2001 14:40 DS/MCO&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0455317 -</LI></UL> <UL><LI>SOEK008292&#x00A0;&#x00A0;19 Dec 2001 15:45 DS/TDP&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0480639 -</LI></UL> <UL><LI>SOEK008290&#x00A0;&#x00A0;19 Dec 2001 15:57 DS/EXG&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0447778 -</LI></UL> <UL><LI>SOEK008279&#x00A0;&#x00A0;27 Dec 2001 10:20 DS/TD&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 0481675 -</LI></UL> <UL><LI>SOEK008298&#x00A0;&#x00A0;27 Dec 2001 12:13 DS/MAP&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0482204 -</LI></UL> <UL><LI>SOEK008302&#x00A0;&#x00A0; 3 Jan 2002 08:13 DS/MAP&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0482457 -</LI></UL> <UL><LI>SOEK008300&#x00A0;&#x00A0; 4 Jan 2002 08:20 DS/EXG&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0482323 -</LI></UL> <UL><LI>SOEK008316&#x00A0;&#x00A0; 9 Jan 2002 09:46 DS/TDP&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0484461 X</LI></UL> <UL><LI>SOEK008310&#x00A0;&#x00A0; 9 Jan 2002 11:46 DS/MAP&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0483933 -</LI></UL> <UL><LI>SOEK008321&#x00A0;&#x00A0; 9 Jan 2002 15:32 DS/EXG&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0483461 -</LI></UL> <UL><LI>SOEK008005&#x00A0;&#x00A0;16 Jan 2002 07:42 DS/HPM&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0440005 -</LI></UL> <UL><LI>SOEK008077&#x00A0;&#x00A0;16 Jan 2002 18:25 DS/TDP&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0446976 -</LI></UL> <UL><LI>SOEK008342&#x00A0;&#x00A0;21 Jan 2002 10:08 DS/EXG&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0481379 -</LI></UL> <UL><LI>SOEK008340&#x00A0;&#x00A0;22 Jan 2002 06:52 DS/TD&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 0487370 - repl.by SOEK008359</LI></UL> <UL><LI>SOEK008329&#x00A0;&#x00A0;22 Jan 2002 11:07 DS/HPM&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0486504 -</LI></UL> <UL><LI>SOEK008351&#x00A0;&#x00A0;23 Jan 2002 14:57 DS/EXG&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0488197 -</LI></UL> <UL><LI>SOEK008359&#x00A0;&#x00A0;23 Jan 2002 15:07 DS/TD&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 0487370 - replaces SOEK008340</LI></UL> <UL><LI>SOEK008353&#x00A0;&#x00A0;23 Jan 2002 18:04 DS/MCO&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0489034 -</LI></UL> <UL><LI>SOEK008349&#x00A0;&#x00A0;29 Jan 2002 15:21 DS/TDP&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0488329 -</LI></UL> <UL><LI>SOEK008371&#x00A0;&#x00A0;29 Jan 2002 15:40 DS/TDP&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0490200 -</LI></UL> <UL><LI>SOEK008338&#x00A0;&#x00A0;30 Jan 2002 08:19 DS/TAS&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0490648 -</LI></UL> <UL><LI>SOEK008384&#x00A0;&#x00A0; 5 Feb 2002 11:09 IS-OIL&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0492151 -</LI></UL> <UL><LI>SOEK008363&#x00A0;&#x00A0; 6 Feb 2002 14:53 DS/HPM&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0492894 -</LI></UL> <UL><LI>SOEK008388&#x00A0;&#x00A0; 7 Feb 2002 13:02 DS/EXG&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0489894 -</LI></UL> <UL><LI>SOEK008333&#x00A0;&#x00A0; 7 Feb 2002 13:17 DS/HPM&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0487973 -</LI></UL> <UL><LI>SOEK008304&#x00A0;&#x00A0;11 Feb 2002 14:15 DS/TDP&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0483325 -</LI></UL> <UL><LI>SOEK008396&#x00A0;&#x00A0;11 Feb 2002 15:16 DS/MAP&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0491979 -</LI></UL> <UL><LI>SOEK008390&#x00A0;&#x00A0;11 Feb 2002 15:26 DS/TDP&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0379453 -</LI></UL> <UL><LI>SOEK008336&#x00A0;&#x00A0;11 Feb 2002 18:42 DS/TD&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 0488163 -</LI></UL> <UL><LI>SOEK008398&#x00A0;&#x00A0;13 Feb 2002 16:00 DS/MAP&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0494571 -</LI></UL> <UL><LI>SOEK008415&#x00A0;&#x00A0;15 Feb 2002 14:50 DS/MCO&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0495893 -</LI></UL> <UL><LI>SOEK008431&#x00A0;&#x00A0;22 Feb 2002 10:13 DS/EXG&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0490801 -</LI></UL> <UL><LI>SOEK008438&#x00A0;&#x00A0;25 Feb 2002 11:46 DS/TPI&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0498128 -</LI></UL> <UL><LI>SOEK008436&#x00A0;&#x00A0;25 Feb 2002 15:02 DS/TSW&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0498343 -</LI></UL> <UL><LI>SOEK008419&#x00A0;&#x00A0;26 Feb 2002 07:22 DS/TD&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 0498506 -</LI></UL> <UL><LI>SOEK008444&#x00A0;&#x00A0;26 Feb 2002 10:10 DS/EXG&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0498254 -</LI></UL> <UL><LI>SOEK008433&#x00A0;&#x00A0;26 Feb 2002 13:51 DS/TAS&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0498005 -</LI></UL> <UL><LI>SOEK008394&#x00A0;&#x00A0;26 Feb 2002 14:22 DS/TDP&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0495301 -</LI></UL> <UL><LI>SOEK008421&#x00A0;&#x00A0;27 Feb 2002 07:50 DS/MAP&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0497803 -</LI></UL> <UL><LI>SOEK008441&#x00A0;&#x00A0;27 Feb 2002 17:18 DS/TDP&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0498094 -</LI></UL> <UL><LI>SOEK008447&#x00A0;&#x00A0;28 Feb 2002 09:29 DS/TD&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 0498507 X</LI></UL> <UL><LI>SOEK008314&#x00A0;&#x00A0;28 Feb 2002 12:27 DS/HPM&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0484852 -</LI></UL> <UL><LI>SOEK008456&#x00A0;&#x00A0;28 Feb 2002 15:01 DS/HPM&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0499679 -</LI></UL> <UL><LI>SOEK008407&#x00A0;&#x00A0; 5 Mar 2002 12:09 DS/TPI&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0495860 -</LI></UL> <UL><LI>SOEK008475&#x00A0;&#x00A0; 5 Mar 2002 16:29 DS/EXG&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0500915 -</LI></UL> <UL><LI> !!!! CAUTION: Read SAPNet Note 501808 BEFORE applying SP 69&#x00A0;&#x00A0;!!!!</LI></UL> <UL><LI>SAPKI40C69&#x00A0;&#x00A0;22 Mar 2002 12:30&#x00A0;&#x00A0;CRT69</LI></UL> <UL><LI>SAPKI40C70&#x00A0;&#x00A0;22 Mar 2002 12:30&#x00A0;&#x00A0;CRT70</LI></UL> <UL><LI>SAPKI40C71&#x00A0;&#x00A0;22 Mar 2002 12:45&#x00A0;&#x00A0;CRT71</LI></UL> <UL><LI>SAPKI40C72&#x00A0;&#x00A0;22 Mar 2002 12:30&#x00A0;&#x00A0;CRT72</LI></UL> <UL><LI>SOEK008531&#x00A0;&#x00A0;22 Mar 2002 14:31 DS/TD&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 0504601 -</LI></UL> <UL><LI>SOEK008553&#x00A0;&#x00A0;25 Mar 2002 08:36 DS/EXG&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0424163 -</LI></UL> <UL><LI>SOEK008403&#x00A0;&#x00A0;27 Mar 2002 16:15 DS/TD&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 0507453 -</LI></UL> <UL><LI>SOEK008517&#x00A0;&#x00A0;27 Mar 2002 16:25 DS/HPM&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0503013 -</LI></UL> <UL><LI>SOEK008500&#x00A0;&#x00A0;28 Mar 2002 14:44 DS/TDP&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0502622 -</LI></UL> <UL><LI>SOEK008566&#x00A0;&#x00A0;28 Mar 2002 14:57 IS-OIL&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0506942 -</LI></UL> <UL><LI>SOEK008576&#x00A0;&#x00A0; 1 Apr 2002 11:17 DS/TD&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 0508109 -</LI></UL> <UL><LI>SOEK008459&#x00A0;&#x00A0; 2 Apr 2002 08:28 DS/MAP&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0508153 -</LI></UL> <UL><LI>SOEK008522&#x00A0;&#x00A0; 2 Apr 2002 11:17 DS/TSW&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0503655 -</LI></UL> <UL><LI>SOEK008503&#x00A0;&#x00A0; 2 Apr 2002 13:41 DS/HPM&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0502252 -</LI></UL> <UL><LI>SOEK008582&#x00A0;&#x00A0; 2 Apr 2002 14:02 DS/TD&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 0508312 -</LI></UL> <UL><LI>SOEK008379&#x00A0;&#x00A0; 3 Apr 2002 09:17 DS/TD&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 0508499 -</LI></UL> <UL><LI>SOEK008590&#x00A0;&#x00A0; 4 Apr 2002 11:06 DS/MCO&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0508909 -</LI></UL> <UL><LI>SOEK008588&#x00A0;&#x00A0; 4 Apr 2002 11:35 DS/TSW&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0508655 -</LI></UL> <UL><LI>SOEK008586&#x00A0;&#x00A0; 4 Apr 2002 15:11 DS/EXG&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0508237 -</LI></UL> <UL><LI>SOEK008463&#x00A0;&#x00A0; 4 Apr 2002 15:15 DS/EXG&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0500411 -</LI></UL> <UL><LI>SOEK008492&#x00A0;&#x00A0; 5 Apr 2002 11:25 DS/EXG&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0500211 -</LI></UL> <UL><LI>SOEK008423&#x00A0;&#x00A0; 8 Apr 2002 13:41 DS/TD&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 0487370 -</LI></UL> <UL><LI>SOEK008601&#x00A0;&#x00A0; 8 Apr 2002 17:34 DS/TDP&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0487243 -</LI></UL> <UL><LI>SOEK008527&#x00A0;&#x00A0; 9 Apr 2002 10:18 IS-OIL&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0509736 -</LI></UL> <UL><LI>SOEK008635&#x00A0;&#x00A0;10 Apr 2002 10:30 DS/MCO&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0510529 -</LI></UL> <UL><LI>SOEK008529&#x00A0;&#x00A0;10 Apr 2002 11:44 DS/TSW&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0503839 -</LI></UL> <UL><LI>SOEK008597&#x00A0;&#x00A0;11 Apr 2002 13:31 DS/MAP&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0487328 -</LI></UL> <UL><LI>SOEK008669&#x00A0;&#x00A0;15 Apr 2002 13:22 DS/EXG&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0511255 -</LI></UL> <UL><LI>SOEK008641&#x00A0;&#x00A0;17 Apr 2002 06:10 DS/TSW&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0510675 -</LI></UL> <UL><LI>SOEK008671&#x00A0;&#x00A0;17 Apr 2002 08:37 DS/TSW&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0512517 -</LI></UL> <UL><LI>SOEK008691&#x00A0;&#x00A0;19 Apr 2002 09:45 DS/BDRP&#x00A0;&#x00A0;&#x00A0;&#x00A0; 0513514 -</LI></UL> <UL><LI>SOEK008695&#x00A0;&#x00A0;22 Apr 2002 13:19 DS/TSW&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0513542 -</LI></UL> <UL><LI>SOEK008698&#x00A0;&#x00A0;23 Apr 2002 10:04 DS/TPI&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0514381 -</LI></UL> <UL><LI>SOEK008693&#x00A0;&#x00A0;24 Apr 2002 08:58 DS/EXG&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0504889 -</LI></UL> <UL><LI>SOEK008702&#x00A0;&#x00A0;24 Apr 2002 13:29 DS/TDP&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0443575 -</LI></UL> <UL><LI>SOEK008704&#x00A0;&#x00A0;24 Apr 2002 15:28 DS/TSW&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0514995 -</LI></UL> <UL><LI>SOEK008706&#x00A0;&#x00A0;24 Apr 2002 17:00 DS/TSW&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0515099 -</LI></UL> <UL><LI>SOEK008685&#x00A0;&#x00A0;25 Apr 2002 10:46 DS/HPM&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0515246 -</LI></UL> <UL><LI>SOEK008673&#x00A0;&#x00A0; 2 May 2002 13:57 DS/TPI&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0516995 -</LI></UL> <UL><LI>SOEK008580&#x00A0;&#x00A0; 3 May 2002 16:15 DS/MAP&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0508135 -</LI></UL> <UL><LI>SOEK008713&#x00A0;&#x00A0; 6 May 2002 13:03 DS/TD&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 0517380 -</LI></UL> <UL><LI>SOEK008687&#x00A0;&#x00A0; 7 May 2002 12:57 DS/TAS&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0517713 -</LI></UL> <UL><LI>SOEK008711&#x00A0;&#x00A0; 8 May 2002 07:41 DS/MAP&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0517617</LI></UL> <UL><LI>SOEK008737&#x00A0;&#x00A0;10 May 2002 09:35 DS/TDP&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0518541</LI></UL> <UL><LI>SOEK008739&#x00A0;&#x00A0;10 May 2002 13:19 DS/EXG&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0518883</LI></UL> <UL><LI>SOEK008741&#x00A0;&#x00A0;13 May 2002 12:31 DS/HPM&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0519184</LI></UL> <UL><LI>SOEK008756&#x00A0;&#x00A0;14 May 2002 10:45 DS/TDP&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0519228</LI></UL> <UL><LI>SOEK008749&#x00A0;&#x00A0;14 May 2002 11:27 DS/TDP&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0519282</LI></UL> <UL><LI>SOEK008545&#x00A0;&#x00A0;15 May 2002 08:24 DS/TDP&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0519268</LI></UL> <UL><LI>SOEK008509&#x00A0;&#x00A0;16 May 2002 10:20 DS/EXG&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0502650</LI></UL> <UL><LI>SOEK008758&#x00A0;&#x00A0;16 May 2002 10:40 DS/TD&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 0519259</LI></UL> <UL><LI>SOEK008744&#x00A0;&#x00A0;18 May 2002 12:39 DS/TSW&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0519739</LI></UL> <UL><LI>SOEK008772&#x00A0;&#x00A0;27 May 2002 08:49 DS/TAS&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0522365 -</LI></UL> <UL><LI>SOEK008733&#x00A0;&#x00A0;30 May 2002 14:00 DS/TSW&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0518617 -</LI></UL> <UL><LI>SOEK008768&#x00A0;&#x00A0; 4 Jun 2002 14:00 DS/TDP&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0523693 -</LI></UL> <UL><LI>SOEK008746&#x00A0;&#x00A0; 4 Jun 2002 14:01 DS/TDP&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0522165 -</LI></UL> <UL><LI>SOEK008790&#x00A0;&#x00A0; 6 Jun 2002 08:04 DS/TDP&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0524066 -</LI></UL> <UL><LI>SAPKI40C73&#x00A0;&#x00A0;21 Jun 2002 09:08&#x00A0;&#x00A0;CRT73</LI></UL> <UL><LI>SAPKI40C74&#x00A0;&#x00A0;21 Jun 2002 09:08&#x00A0;&#x00A0;CRT74</LI></UL> <UL><LI>SAPKI40C75&#x00A0;&#x00A0;21 Jun 2002 09:08&#x00A0;&#x00A0;CRT75</LI></UL> <UL><LI>***********************IMPORTANT NOTICE*********************</LI></UL> <UL><LI></LI></UL> <UL><LI>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;This note which supports corrections by way of single</LI></UL> <UL><LI>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;transports, is no longer supported.&#x00A0;&#x00A0;Single transport</LI></UL> <UL><LI>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;corrections have been replaced by Add-On Support</LI></UL> <UL><LI>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Packages (AOPs).</LI></UL> <UL><LI>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Please refer to Notes 47531 and 53136.</LI></UL> <UL><LI></LI></UL> <UL><LI>***********************IMPORTANT NOTICE*********************</LI></UL></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "BC-UPG-ADDON (Upgrade Add-On Components)"}, {"Key": "Transaction codes", "Value": "TIME"}, {"Key": "Transaction codes", "Value": "SUCH"}, {"Key": "Transaction codes", "Value": "SPAM"}, {"Key": "Transaction codes", "Value": "MCOE"}, {"Key": "Responsible                                                                                         ", "Value": "C3239103"}, {"Key": "Processor                                                                                           ", "Value": "C3239103"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000145850/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000145850/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000145850/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000145850/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000145850/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000145850/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000145850/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000145850/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000145850/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "98642", "RefComponent": "IS-OIL-BC", "RefTitle": "Order of IS-OIL notes 2.0d & 1.0d on R/3 3.1H (SP)", "RefUrl": "/notes/98642"}, {"RefNumber": "98534", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Abend O1 544 in TD Delivery Confirmation", "RefUrl": "/notes/98534"}, {"RefNumber": "93571", "RefComponent": "IS-OIL", "RefTitle": "Sequence of corrections - IS-Oil / IS-MINE Policy", "RefUrl": "/notes/93571"}, {"RefNumber": "86241", "RefComponent": "PY", "RefTitle": "Legal Change Patches / Support Packages for HR", "RefUrl": "/notes/86241"}, {"RefNumber": "804981", "RefComponent": "BC-UPG-ADDON", "RefTitle": "IS-OIL: Additional Info - SP 84 incl. 4.0B", "RefUrl": "/notes/804981"}, {"RefNumber": "77407", "RefComponent": "IS-OIL-BC", "RefTitle": "CRTs for IS-Oil", "RefUrl": "/notes/77407"}, {"RefNumber": "67261", "RefComponent": "IS-OIL-DS-HPM", "RefTitle": "Reports to analyze and correct stock qties in IS-OIL systems", "RefUrl": "/notes/67261"}, {"RefNumber": "640354", "RefComponent": "IS-OIL-BC", "RefTitle": "IS-OIL: Additional Info - SPs 80-81 incl. 4.0B", "RefUrl": "/notes/640354"}, {"RefNumber": "560213", "RefComponent": "BC-UPG-ADDON", "RefTitle": "IS-OIL: Additional Info - SPs 76-79 incl. 4.0B", "RefUrl": "/notes/560213"}, {"RefNumber": "53136", "RefComponent": "IS-OIL-BC", "RefTitle": "Support Packages and IS-Oil / IS-MINE / IS-CWM - information", "RefUrl": "/notes/53136"}, {"RefNumber": "530459", "RefComponent": "IS-OIL-BC", "RefTitle": "IS-OIL: Additional Info - SPs 73-75 incl. 4.0B", "RefUrl": "/notes/530459"}, {"RefNumber": "528008", "RefComponent": "IS-OIL-DS-TSW", "RefTitle": "MOIJNF01_COPY_REF_NOM_LINES", "RefUrl": "/notes/528008"}, {"RefNumber": "527924", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "Customizing the message type for TD message O9(747)", "RefUrl": "/notes/527924"}, {"RefNumber": "525712", "RefComponent": "IS-OIL-DS-MCO", "RefTitle": "External Details Button rises error M7 001", "RefUrl": "/notes/525712"}, {"RefNumber": "524350", "RefComponent": "IS-OIL", "RefTitle": "Balancing Workplace IMG Activity Documentation", "RefUrl": "/notes/524350"}, {"RefNumber": "522365", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "TAS incompletion log appears for a non TAS relevant order", "RefUrl": "/notes/522365"}, {"RefNumber": "522282", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "ERS creates inconsistent fee table entries (OIANF/OIAFE)", "RefUrl": "/notes/522282"}, {"RefNumber": "521389", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/521389"}, {"RefNumber": "519739", "RefComponent": "IS-OIL-DS-TSW", "RefTitle": "(O4TE) Ticket Create -  showing closed noms", "RefUrl": "/notes/519739"}, {"RefNumber": "519282", "RefComponent": "IS-OIL-DS-TDP", "RefTitle": "IBS OIL&GAS LOCALIZATION BRAZIL LOST IS-OIL DATA DURING IV", "RefUrl": "/notes/519282"}, {"RefNumber": "519268", "RefComponent": "IS-OIL-DS-TDP", "RefTitle": "IBS OIL&GAS LOCALIZATION BRAZIL THIRD NOTA FISCAL", "RefUrl": "/notes/519268"}, {"RefNumber": "519259", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "Customizing the message type for TD message class O9(078).", "RefUrl": "/notes/519259"}, {"RefNumber": "519228", "RefComponent": "IS-OIL-DS-TDP", "RefTitle": "IBS OIL&GAS LOCALIZATION BRAZIL ST Calculation Interstate", "RefUrl": "/notes/519228"}, {"RefNumber": "519184", "RefComponent": "IS-OIL-DS-HPM", "RefTitle": "IS-Oil:MR03 Qty is 0 if more than 1 line item on the invoice", "RefUrl": "/notes/519184"}, {"RefNumber": "518883", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Wrong proposal of Delivery Costs for Weight Based Products", "RefUrl": "/notes/518883"}, {"RefNumber": "518617", "RefComponent": "IS-OIL-DS-TSW", "RefTitle": "Problem with OILNOM01", "RefUrl": "/notes/518617"}, {"RefNumber": "518541", "RefComponent": "IS-OIL-DS-TDP", "RefTitle": "Material valuation type not copied to settlement rule object", "RefUrl": "/notes/518541"}, {"RefNumber": "517617", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "Exclude Event date indicator functionality is not working", "RefUrl": "/notes/517617"}, {"RefNumber": "517380", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "TD Coll. Note - MCHA and MCHB checks do not allow load.conf.", "RefUrl": "/notes/517380"}, {"RefNumber": "516995", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Buffer overflow on OIKPEXORD", "RefUrl": "/notes/516995"}, {"RefNumber": "515246", "RefComponent": "IS-OIL-DS-HPM", "RefTitle": "Error in quantity conversion : Billing Document..archiving", "RefUrl": "/notes/515246"}, {"RefNumber": "515099", "RefComponent": "IS-OIL-DS-TSW", "RefTitle": "O4NC: header material disappeared after entering", "RefUrl": "/notes/515099"}, {"RefNumber": "514995", "RefComponent": "IS-OIL-DS-TSW", "RefTitle": "Insert line on nomination deletes the header material", "RefUrl": "/notes/514995"}, {"RefNumber": "514381", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "E1OILBH-SHPPPT (Transportation planning point) not filled", "RefUrl": "/notes/514381"}, {"RefNumber": "514195", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/514195"}, {"RefNumber": "513542", "RefComponent": "IS-OIL-DS-TSW", "RefTitle": "OIL: O4NC Event Log-not available in create mode for default", "RefUrl": "/notes/513542"}, {"RefNumber": "513514", "RefComponent": "IS-OIL-DS-BDRP", "RefTitle": "Missing export parameter in Function OII_OTWS_GET_REF_SET", "RefUrl": "/notes/513514"}, {"RefNumber": "512517", "RefComponent": "IS-OIL-DS-TSW", "RefTitle": "OIL: O4NV  Need to change Shipper due is diplay only.", "RefUrl": "/notes/512517"}, {"RefNumber": "511255", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Incorrect valuation for exchange receipts", "RefUrl": "/notes/511255"}, {"RefNumber": "510675", "RefComponent": "IS-OIL-DS-TSW", "RefTitle": "OIL: Rename functionality changes entries in database", "RefUrl": "/notes/510675"}, {"RefNumber": "510529", "RefComponent": "IS-OIL-DS-MCO", "RefTitle": "To update VBKD-OIT<PERSON>LE when VBKD-INCO1 is changed.", "RefUrl": "/notes/510529"}, {"RefNumber": "509736", "RefComponent": "IS-OIL-DS-HPM", "RefTitle": "Archiving of billing doc. subj to volume-based rebates", "RefUrl": "/notes/509736"}, {"RefNumber": "508909", "RefComponent": "IS-OIL-DS-MCO", "RefTitle": "External details fields not getting updated in LIKP", "RefUrl": "/notes/508909"}, {"RefNumber": "508655", "RefComponent": "IS-OIL-DS-TSW", "RefTitle": "OIL: With reference to note n°487703 - 2", "RefUrl": "/notes/508655"}, {"RefNumber": "508499", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "TD-O4H1/STO enabling new batch creation in receiving plant", "RefUrl": "/notes/508499"}, {"RefNumber": "508312", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "Consideration for unlimited indicator LIPS-UEBTK in Delivery", "RefUrl": "/notes/508312"}, {"RefNumber": "508237", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Error message O1 033 at changing contract QS header", "RefUrl": "/notes/508237"}, {"RefNumber": "508153", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "Billing index update problems", "RefUrl": "/notes/508153"}, {"RefNumber": "508135", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "TIMEOUT Error while reading the quotations", "RefUrl": "/notes/508135"}, {"RefNumber": "508109", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "O4F1 short dump in Document selection F4 due to W message", "RefUrl": "/notes/508109"}, {"RefNumber": "507291", "RefComponent": "IS-OIL-DS-HPM", "RefTitle": "Quantity unit in RMCSS003", "RefUrl": "/notes/507291"}, {"RefNumber": "506942", "RefComponent": "IS-OIL-DS-HPM", "RefTitle": "Dump when running report ROIB6FIX", "RefUrl": "/notes/506942"}, {"RefNumber": "505973", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Invoice getting blocked on ERS or Invoice Verification.", "RefUrl": "/notes/505973"}, {"RefNumber": "504889", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Wrong delivery costs proposed for invoice verification", "RefUrl": "/notes/504889"}, {"RefNumber": "504601", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "Vehicle status change from 6 to 5 in O4L4.", "RefUrl": "/notes/504601"}, {"RefNumber": "503839", "RefComponent": "IS-OIL-DS-TSW", "RefTitle": "OIL: Rename function in O4NC or O4NV", "RefUrl": "/notes/503839"}, {"RefNumber": "503655", "RefComponent": "IS-OIL-DS-TSW", "RefTitle": "OIL:O4NC Event Log- not available in create mode for defaul", "RefUrl": "/notes/503655"}, {"RefNumber": "503197", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "D-SCM-EX:Call off qty > schedule qty", "RefUrl": "/notes/503197"}, {"RefNumber": "502650", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Already cleared fees shouldn't be displayed", "RefUrl": "/notes/502650"}, {"RefNumber": "502622", "RefComponent": "IS-OIL-DS-TDP", "RefTitle": "Message no. OB 007 when calling off from purchase contract", "RefUrl": "/notes/502622"}, {"RefNumber": "502252", "RefComponent": "IS-OIL", "RefTitle": "IS OIL: short dump in transaction O3C2", "RefUrl": "/notes/502252"}, {"RefNumber": "501808", "RefComponent": "IS-OIL-BC", "RefTitle": "IS-OIL: Additional Info - SPs 69-72 incl. 4.0B", "RefUrl": "/notes/501808"}, {"RefNumber": "500915", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Fee copy rules cannot be maintained for SD documents", "RefUrl": "/notes/500915"}, {"RefNumber": "500499", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Bad performance of ROIAMMA3/ROIAMMAT if no documents found", "RefUrl": "/notes/500499"}, {"RefNumber": "500411", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Split invoicing adds up the invoiced quantity.", "RefUrl": "/notes/500411"}, {"RefNumber": "500211", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Problems in OIAFE with postings different years (LIV)", "RefUrl": "/notes/500211"}, {"RefNumber": "499679", "RefComponent": "IS-OIL-DS-HPM", "RefTitle": "Short dump when when deleting SD delivery (IS-Oil)", "RefUrl": "/notes/499679"}, {"RefNumber": "498507", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "Status handling in TD-Scheduling for zero quantity items", "RefUrl": "/notes/498507"}, {"RefNumber": "498506", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "O4D2 batch input wrong exclusion of FCODE DELE", "RefUrl": "/notes/498506"}, {"RefNumber": "498128", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Deletion of SD delivery through TPI Interface or Txn O4PO", "RefUrl": "/notes/498128"}, {"RefNumber": "498094", "RefComponent": "IS-OIL-DS-TDP", "RefTitle": "IS-OIL: VF01 using different curencies E-KE 476", "RefUrl": "/notes/498094"}, {"RefNumber": "498005", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "Material Group in Load ID determination", "RefUrl": "/notes/498005"}, {"RefNumber": "497803", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "Incorrect VKDFS entry proposed in diff. invoice cancelation", "RefUrl": "/notes/497803"}, {"RefNumber": "495893", "RefComponent": "IS-OIL-DS-MCO", "RefTitle": "Using FCODE OIDX with a SD doc. cat. <> C gives no error", "RefUrl": "/notes/495893"}, {"RefNumber": "495860", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Entry of text id in configuration of TPI interface", "RefUrl": "/notes/495860"}, {"RefNumber": "495547", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "BW, FI-SL, CO-PA and SIS quantities missing in SD invoices", "RefUrl": "/notes/495547"}, {"RefNumber": "494571", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "F&A tables not adjusted properly for multiple F&A conditions", "RefUrl": "/notes/494571"}, {"RefNumber": "492894", "RefComponent": "IS-OIL-DS-HPM", "RefTitle": "PO history incorrect after GR & IR for mult. acct assg", "RefUrl": "/notes/492894"}, {"RefNumber": "492265", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Netting fails with O1 546 for cross-company code LIVs", "RefUrl": "/notes/492265"}, {"RefNumber": "492151", "RefComponent": "IS-OIL", "RefTitle": "VF04: Nota fiscal simulation does not consider BOM's", "RefUrl": "/notes/492151"}, {"RefNumber": "491979", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "Inactive F&A condition results in incorrect billing due list", "RefUrl": "/notes/491979"}, {"RefNumber": "490801", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "O3AB: Fee repricing causes wrong formula description", "RefUrl": "/notes/490801"}, {"RefNumber": "490648", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "Dumps DYNPRO_SEND_IN_BACKGROUND using ROIKPIPR", "RefUrl": "/notes/490648"}, {"RefNumber": "489894", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "MIRO Invoice Repricing uses incorrect pricing date", "RefUrl": "/notes/489894"}, {"RefNumber": "489034", "RefComponent": "IS-OIL-DS-MCO", "RefTitle": "Set Screen error while creating sales order from a contract", "RefUrl": "/notes/489034"}, {"RefNumber": "488559", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Netting proposal not selecting all documents", "RefUrl": "/notes/488559"}, {"RefNumber": "488493", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "MRRS is dumping due to fiscal year issue", "RefUrl": "/notes/488493"}, {"RefNumber": "488197", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Already cleared fees are proposed during MR01", "RefUrl": "/notes/488197"}, {"RefNumber": "488163", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "One OIGSM; Cancel ASTM; Batch in 851", "RefUrl": "/notes/488163"}, {"RefNumber": "487973", "RefComponent": "IS-OIL-DS-HPM", "RefTitle": "Performance problem in material master - Oil view", "RefUrl": "/notes/487973"}, {"RefNumber": "487370", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "Short dump at O4G1-Balance Loading after loading correction", "RefUrl": "/notes/487370"}, {"RefNumber": "487328", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "UOM conversion not drawn from the MM contract or info record", "RefUrl": "/notes/487328"}, {"RefNumber": "487243", "RefComponent": "IS-OIL-DS-TDP", "RefTitle": "Update of MBEW during gain/loss does not occur", "RefUrl": "/notes/487243"}, {"RefNumber": "486076", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Display accounting doc.: weird text on push button", "RefUrl": "/notes/486076"}, {"RefNumber": "485940", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Performance Improvements for O3A7 with Posting Date", "RefUrl": "/notes/485940"}, {"RefNumber": "485680", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Incorrect Values in Exchange Statement with Credit Memos", "RefUrl": "/notes/485680"}, {"RefNumber": "484852", "RefComponent": "IS-OIL-DS-HPM", "RefTitle": "Incorrect error in Subcontracting Subsequent Adjustment", "RefUrl": "/notes/484852"}, {"RefNumber": "484461", "RefComponent": "IS-OIL-DS-TDP", "RefTitle": "IS-OIL: Stock transfer order checking issuing valuation", "RefUrl": "/notes/484461"}, {"RefNumber": "483461", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "LIA item amount changes when using some currencies", "RefUrl": "/notes/483461"}, {"RefNumber": "482457", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "OIEXGNUM lost in table VKDFS in billing due list run", "RefUrl": "/notes/482457"}, {"RefNumber": "482323", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Wrong quantity in the QS after rejecting a sales order", "RefUrl": "/notes/482323"}, {"RefNumber": "482204", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "Incorrect entries in M_VMCFA for differential invoices", "RefUrl": "/notes/482204"}, {"RefNumber": "481675", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "Dlvy qty changed;Rounding;VBRP-CHARG;O9 340;QCI temperat.", "RefUrl": "/notes/481675"}, {"RefNumber": "481379", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "LIV ERS not create fee credit memo for prior year (part2)", "RefUrl": "/notes/481379"}, {"RefNumber": "481165", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/481165"}, {"RefNumber": "480639", "RefComponent": "IS-OIL-DS-TDP", "RefTitle": "IS-OIL: Incorrect stock qty/value in material master", "RefUrl": "/notes/480639"}, {"RefNumber": "47531", "RefComponent": "IS-OIL", "RefTitle": "IS-OIL / IS-MINE / IS-CWM correction guideline", "RefUrl": "/notes/47531"}, {"RefNumber": "460234", "RefComponent": "IS-OIL-DS-TDP", "RefTitle": "Excise License Validiy Dates", "RefUrl": "/notes/460234"}, {"RefNumber": "46023", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/46023"}, {"RefNumber": "459951", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "Autom. compartment allocation before exchange assignment", "RefUrl": "/notes/459951"}, {"RefNumber": "459769", "RefComponent": "IS-OIL-PRA-REV-VAL", "RefTitle": "Valuation document scheduling and processing", "RefUrl": "/notes/459769"}, {"RefNumber": "459644", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "EXIT_SAPLEINR_400: more information required", "RefUrl": "/notes/459644"}, {"RefNumber": "458906", "RefComponent": "IS-OIL-BC", "RefTitle": "IS-OIL: Additional Info - SPs 66-68 incl. 4.0B", "RefUrl": "/notes/458906"}, {"RefNumber": "458705", "RefComponent": "IS-OIL-DS-TDP", "RefTitle": "IS-OIL: VPRS Re-Determination when non TDP material", "RefUrl": "/notes/458705"}, {"RefNumber": "458242", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "ALE for Customer Master / IS/Oil Data (OILDEB)", "RefUrl": "/notes/458242"}, {"RefNumber": "458144", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Selection lost on scroll in Exchange contract copy", "RefUrl": "/notes/458144"}, {"RefNumber": "458055", "RefComponent": "IS-OIL-DS", "RefTitle": "Checks for IS-Oil fields in access sequence", "RefUrl": "/notes/458055"}, {"RefNumber": "457770", "RefComponent": "IS-OIL-DS-TDP", "RefTitle": "IS-OIL: MBST/MB01 REVERSAL OF GR AGAINST PO", "RefUrl": "/notes/457770"}, {"RefNumber": "456603", "RefComponent": "IS-OIL-DS-TSW", "RefTitle": "Consingee changes do not flag nom as changed", "RefUrl": "/notes/456603"}, {"RefNumber": "455631", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "O3B3 Exg doesn't get sender address", "RefUrl": "/notes/455631"}, {"RefNumber": "455317", "RefComponent": "IS-OIL-DS-MCO", "RefTitle": "Wrong cursor position in the OIL 40B SO overview screen", "RefUrl": "/notes/455317"}, {"RefNumber": "455166", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "Rounding 2step;Abend MSEGO2;Don't change temp;Wrong sign G/L", "RefUrl": "/notes/455166"}, {"RefNumber": "454030", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "Wrong text on Txns O4P7 and O4P8", "RefUrl": "/notes/454030"}, {"RefNumber": "454007", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "Group conditions and other cond cumulated value with 431", "RefUrl": "/notes/454007"}, {"RefNumber": "453243", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "TD Transport Unit compartment data customer defined screen", "RefUrl": "/notes/453243"}, {"RefNumber": "453208", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Short Dump in 40b after application of new kernel", "RefUrl": "/notes/453208"}, {"RefNumber": "452122", "RefComponent": "IS-OIL-DS-TDP", "RefTitle": "Nota fiscal compl. does not consider user condition types", "RefUrl": "/notes/452122"}, {"RefNumber": "451521", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Support extended IDOCs during IDOC creation", "RefUrl": "/notes/451521"}, {"RefNumber": "451483", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Exchange number is cleared when changing material document", "RefUrl": "/notes/451483"}, {"RefNumber": "451394", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Fees in delivery multiplied by thousand", "RefUrl": "/notes/451394"}, {"RefNumber": "451143", "RefComponent": "IS-OIL-DS-HPM", "RefTitle": "Tracking number for 2-step transfer not generated in batch", "RefUrl": "/notes/451143"}, {"RefNumber": "451134", "RefComponent": "IS-OIL-DS-TDP", "RefTitle": "Mass update of contract prices and external ED pricing key", "RefUrl": "/notes/451134"}, {"RefNumber": "449723", "RefComponent": "IS-OIL-DS-TDP", "RefTitle": "ASTM corrected volume not used for ED repricing in IR", "RefUrl": "/notes/449723"}, {"RefNumber": "448454", "RefComponent": "IS-OIL-DS-TSW", "RefTitle": "O4TF: LIST OF TICKETS A<PERSON><PERSON><PERSON>LE INCLUDES DELETED TICKETS", "RefUrl": "/notes/448454"}, {"RefNumber": "448020", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "Double update of IDOCS in parallel inbound & Idoc locking", "RefUrl": "/notes/448020"}, {"RefNumber": "447778", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Missing delivery note and bill of loading in a material doc.", "RefUrl": "/notes/447778"}, {"RefNumber": "447012", "RefComponent": "IS-OIL-DS-TSW", "RefTitle": "OIL:Change tkt with same name as deleted tkt/NOMIT not displ", "RefUrl": "/notes/447012"}, {"RefNumber": "446350", "RefComponent": "IS-OIL-DS-MCO", "RefTitle": "External Details in the goods movement 301, 309 and 311.", "RefUrl": "/notes/446350"}, {"RefNumber": "446038", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Program termination during BW contract data upload", "RefUrl": "/notes/446038"}, {"RefNumber": "445741", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "Report Find Shipments", "RefUrl": "/notes/445741"}, {"RefNumber": "445150", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Unable to change pricing date on fee screen in billing", "RefUrl": "/notes/445150"}, {"RefNumber": "443575", "RefComponent": "IS-OIL-DS-TDP", "RefTitle": "Excise duty balance calc incorrect when gain/loss", "RefUrl": "/notes/443575"}, {"RefNumber": "443421", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "Archiving of shipments", "RefUrl": "/notes/443421"}, {"RefNumber": "442657", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Wrong german texts in sales documents", "RefUrl": "/notes/442657"}, {"RefNumber": "442303", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "Billing index update problems", "RefUrl": "/notes/442303"}, {"RefNumber": "441786", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "Display Error in SAPMOIGS-3200 when calling from 3700", "RefUrl": "/notes/441786"}, {"RefNumber": "440799", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Purchase orders without any costs can't be processed via ERS", "RefUrl": "/notes/440799"}, {"RefNumber": "439036", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "BW wrong load; delete OIGSM; load interface; delete dcmt.", "RefUrl": "/notes/439036"}, {"RefNumber": "438767", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Invoice print performance and memory problem", "RefUrl": "/notes/438767"}, {"RefNumber": "438549", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Fee copy controls missing in billing to order copy control", "RefUrl": "/notes/438549"}, {"RefNumber": "438227", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Billing: fee total condition differs from fee sum", "RefUrl": "/notes/438227"}, {"RefNumber": "438098", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Wrong display of values in SAPF124", "RefUrl": "/notes/438098"}, {"RefNumber": "436699", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "LIAs are not properly deriving Profit Center", "RefUrl": "/notes/436699"}, {"RefNumber": "434924", "RefComponent": "IS-OIL-BC", "RefTitle": "IS-OIL: Additional Info - SPs 63-65 incl. 4.0B", "RefUrl": "/notes/434924"}, {"RefNumber": "434809", "RefComponent": "IS-OIL-DS-MCO", "RefTitle": "Runtime error during overflow check in LOICQF0R", "RefUrl": "/notes/434809"}, {"RefNumber": "433834", "RefComponent": "IS-OIL-DS-MCO", "RefTitle": "Internal work areas defined in RVINVB00, RVINVB10 are small.", "RefUrl": "/notes/433834"}, {"RefNumber": "432574", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Quantity schedule on a purchase order is wrong  (TD related)", "RefUrl": "/notes/432574"}, {"RefNumber": "431985", "RefComponent": "IS-OIL-DS-TSW", "RefTitle": "Document validation does not take place for Book Nomination", "RefUrl": "/notes/431985"}, {"RefNumber": "431481", "RefComponent": "IS-OIL-DS-TDP", "RefTitle": "IS-OIL TDP TRANSFER POSTINGS", "RefUrl": "/notes/431481"}, {"RefNumber": "431050", "RefComponent": "IS-OIL-DS-TDP", "RefTitle": "Returns not Updating License Quantity Tracking", "RefUrl": "/notes/431050"}, {"RefNumber": "430515", "RefComponent": "IS-OIL-DS-MCO", "RefTitle": "Item category redetermination not working", "RefUrl": "/notes/430515"}, {"RefNumber": "429059", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "Short dump when creating Call-off against Contract", "RefUrl": "/notes/429059"}, {"RefNumber": "429002", "RefComponent": "IS-OIL-DS-MRN", "RefTitle": "CO-PA charact. OIFWE: not filled properly in Invoice", "RefUrl": "/notes/429002"}, {"RefNumber": "428885", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/428885"}, {"RefNumber": "428884", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/428884"}, {"RefNumber": "428275", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "KE4S fails when loading invoice with fee total condition", "RefUrl": "/notes/428275"}, {"RefNumber": "427899", "RefComponent": "IS-OIL-DS-HPM", "RefTitle": "Wrong qty conversion in billing condition", "RefUrl": "/notes/427899"}, {"RefNumber": "427847", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Wrong fee copy at good receipt cancellation", "RefUrl": "/notes/427847"}, {"RefNumber": "427650", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "Delivery Note and Bill of Lading not Appearing in MB01....", "RefUrl": "/notes/427650"}, {"RefNumber": "427316", "RefComponent": "IS-OIL-DS-HPM", "RefTitle": "ROIABVF6 Batch Invoice programs dumping", "RefUrl": "/notes/427316"}, {"RefNumber": "426588", "RefComponent": "IS-OIL-DS-MCO", "RefTitle": "Shortdump in a sales order with no line items.", "RefUrl": "/notes/426588"}, {"RefNumber": "426567", "RefComponent": "IS-OIL-DS-SSR", "RefTitle": "SSR/MRN update classification table entries in TCLT", "RefUrl": "/notes/426567"}, {"RefNumber": "426381", "RefComponent": "IS-OIL-DS-HPM", "RefTitle": "HPM Appendices Inconsistent After Availability Check", "RefUrl": "/notes/426381"}, {"RefNumber": "425216", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "Batch missing; Vehicle balance; BW /0; Abend mvmt o b; OIGSH", "RefUrl": "/notes/425216"}, {"RefNumber": "424517", "RefComponent": "IS-R-PUR-PO", "RefTitle": "Default value: Time of delivery in online planning", "RefUrl": "/notes/424517"}, {"RefNumber": "424163", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Cancel goods issue: no entry in OIAQB created", "RefUrl": "/notes/424163"}, {"RefNumber": "423668", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "STSI Load Confirmation posts additional qtys for TDP matl", "RefUrl": "/notes/423668"}, {"RefNumber": "423369", "RefComponent": "IS-OIL-DS-MCO", "RefTitle": "Shortdump while changing sales order with no line items.", "RefUrl": "/notes/423369"}, {"RefNumber": "422820", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "Wrong delivered quantity in the Balance Vehicle screen(O4H1)", "RefUrl": "/notes/422820"}, {"RefNumber": "422509", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "Wrong loaded qty in balance vehicle screen in TD-O4H1.", "RefUrl": "/notes/422509"}, {"RefNumber": "422007", "RefComponent": "IS-OIL-DS-TDP", "RefTitle": "IS-OIL TDP BILLING MISSING CO LINES", "RefUrl": "/notes/422007"}, {"RefNumber": "421873", "RefComponent": "IS-OIL-DS-HPM", "RefTitle": "Delivery weight & volume not updated properly in delivery", "RefUrl": "/notes/421873"}, {"RefNumber": "421494", "RefComponent": "IS-OIL", "RefTitle": "Alternative origin validation for purchase order", "RefUrl": "/notes/421494"}, {"RefNumber": "421302", "RefComponent": "IS-OIL-DS-HPM", "RefTitle": "IS-OIL: MMBE - Wrong totals shown", "RefUrl": "/notes/421302"}, {"RefNumber": "421035", "RefComponent": "IS-OIL-DS-HPM", "RefTitle": "Rollback while deleting archived stock entries", "RefUrl": "/notes/421035"}, {"RefNumber": "420838", "RefComponent": "IS-OIL-DS-HPM", "RefTitle": "IS-OIL: MMBE shows wrong stock sums", "RefUrl": "/notes/420838"}, {"RefNumber": "420585", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Compartment Planning disabling NOT possible", "RefUrl": "/notes/420585"}, {"RefNumber": "420095", "RefComponent": "IS-OIL-DS-TDP", "RefTitle": "IS-OIL TDP GI for delivery note missing OIVBELN + OIPOSNR", "RefUrl": "/notes/420095"}, {"RefNumber": "419887", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Impossible to change exchange starting date", "RefUrl": "/notes/419887"}, {"RefNumber": "418672", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "851/852 for real batch; STO VBFA; batch in conversion; Lov=0", "RefUrl": "/notes/418672"}, {"RefNumber": "41767", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/41767"}, {"RefNumber": "417277", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Short dump & BDC error with netting document & BTCI", "RefUrl": "/notes/417277"}, {"RefNumber": "417113", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "O4PO: new created delivery w/o tank id entries in oik37", "RefUrl": "/notes/417113"}, {"RefNumber": "416930", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Cancelation of note 391987 (no agreed redesign of feerepric)", "RefUrl": "/notes/416930"}, {"RefNumber": "416919", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "Short dump while calling BAPI_SALESORDER_CREATEFROMDAT2", "RefUrl": "/notes/416919"}, {"RefNumber": "416839", "RefComponent": "IS-OIL-DS-HPM", "RefTitle": "Performance improvement loading and delivery confirmation", "RefUrl": "/notes/416839"}, {"RefNumber": "415989", "RefComponent": "IS-OIL-BC", "RefTitle": "IS-OIL: Additional Info - SPs 61-62 incl. 4.0B", "RefUrl": "/notes/415989"}, {"RefNumber": "415005", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "data element OIG_AEDTMF field labels are wrong", "RefUrl": "/notes/415005"}, {"RefNumber": "414989", "RefComponent": "IS-OIL", "RefTitle": "Enable internal flag in ticket screen", "RefUrl": "/notes/414989"}, {"RefNumber": "414893", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "Field Control for transport zone & Mode of Transport", "RefUrl": "/notes/414893"}, {"RefNumber": "414782", "RefComponent": "IS-OIL-DS-MCO", "RefTitle": "RM07MMAT does not consider fields EXTBOL/MISCDL", "RefUrl": "/notes/414782"}, {"RefNumber": "414289", "RefComponent": "IS-OIL-DS-BDRP", "RefTitle": "O5NX Batch input error due to LEAVE TO TRANSACTION statement", "RefUrl": "/notes/414289"}, {"RefNumber": "414197", "RefComponent": "IS-OIL-DS-BDRP", "RefTitle": "O5NG batch input error due to LEAVE TO TRANSACTION statement", "RefUrl": "/notes/414197"}, {"RefNumber": "413748", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Redetermine Sold-to party when creating new SD contract", "RefUrl": "/notes/413748"}, {"RefNumber": "413635", "RefComponent": "IS-OIL-DS-HPM", "RefTitle": "Manual pricing conditions are repriced for oil BoM", "RefUrl": "/notes/413635"}, {"RefNumber": "412858", "RefComponent": "IS-OIL-DS-HPM", "RefTitle": "IS OIL: Vendor consignment stock displayed only in base UoM", "RefUrl": "/notes/412858"}, {"RefNumber": "412543", "RefComponent": "IS-OIL-DS-MCO", "RefTitle": "Oil vers. of note 410014: No check of pricing ref. material", "RefUrl": "/notes/412543"}, {"RefNumber": "412494", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/412494"}, {"RefNumber": "412012", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Exg.data/fees aren't copied at cred-memo creation/cancel.", "RefUrl": "/notes/412012"}, {"RefNumber": "411898", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "Cancel credit memo raises message", "RefUrl": "/notes/411898"}, {"RefNumber": "411413", "RefComponent": "IS-OIL-DS-BDRP", "RefTitle": "VA01 errors when removing tank assignment", "RefUrl": "/notes/411413"}, {"RefNumber": "411207", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Shipment Planning corrections", "RefUrl": "/notes/411207"}, {"RefNumber": "409524", "RefComponent": "IS-OIL-DS", "RefTitle": "QA cleanup TODO/SAMT for support systems", "RefUrl": "/notes/409524"}, {"RefNumber": "409269", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "F&A Formula is not deleted to the customizing table", "RefUrl": "/notes/409269"}, {"RefNumber": "408614", "RefComponent": "IS-OIL-DS-MCO", "RefTitle": "MCOE: No Validation for ship. point and route on order entry", "RefUrl": "/notes/408614"}, {"RefNumber": "408461", "RefComponent": "IS-OIL-DS-MCO", "RefTitle": "MKPF-TCODE2 is used instead of MKPF-TCODE - Msg 208803/2001", "RefUrl": "/notes/408461"}, {"RefNumber": "407729", "RefComponent": "IS-OIL-DS-MCO", "RefTitle": "Valuation type not visible in overview screen", "RefUrl": "/notes/407729"}, {"RefNumber": "407197", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Billed order appears again in VF04", "RefUrl": "/notes/407197"}, {"RefNumber": "406895", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/406895"}, {"RefNumber": "406580", "RefComponent": "IS-OIL-DS-HPM", "RefTitle": "Error when changing storage location in delivery", "RefUrl": "/notes/406580"}, {"RefNumber": "406284", "RefComponent": "IS-OIL-DS-TDP", "RefTitle": "MR01 in foreign currency ends in message M8 *********** 085", "RefUrl": "/notes/406284"}, {"RefNumber": "406047", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "Mvt type 851/852-LIS, no value; VL09; QCI in mining", "RefUrl": "/notes/406047"}, {"RefNumber": "403703", "RefComponent": "IS-OIL-DS-HPM", "RefTitle": "Update of CMETH on VBFA for billing", "RefUrl": "/notes/403703"}, {"RefNumber": "403275", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Shortdump when posting netting document with many items", "RefUrl": "/notes/403275"}, {"RefNumber": "403030", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "After implementation of 392231 inbound process short dumps", "RefUrl": "/notes/403030"}, {"RefNumber": "402561", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "Error in sales order for more then one item in TAS", "RefUrl": "/notes/402561"}, {"RefNumber": "402367", "RefComponent": "IS-OIL-DS-TDP", "RefTitle": "Error M7 050 with transfer after partial stock revaluation", "RefUrl": "/notes/402367"}, {"RefNumber": "402296", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Error occurs in LIA account determination", "RefUrl": "/notes/402296"}, {"RefNumber": "402177", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Shortdmp \"Field symbol not assigned\" when leaving fee dialog", "RefUrl": "/notes/402177"}, {"RefNumber": "401810", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Error V0010 occur after repeat the allocation step in O3A2", "RefUrl": "/notes/401810"}, {"RefNumber": "401593", "RefComponent": "IS-OIL-DS-TDP", "RefTitle": "IS-OIL / Euro: Ensure that OIH01 buffer is reset", "RefUrl": "/notes/401593"}, {"RefNumber": "401341", "RefComponent": "IS-OIL-DS-HPM", "RefTitle": "Zero quantity line is not calculated during delivery", "RefUrl": "/notes/401341"}, {"RefNumber": "400863", "RefComponent": "IS-OIL", "RefTitle": "OIL: Problems deleting E&P Hierarchies", "RefUrl": "/notes/400863"}, {"RefNumber": "400585", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/400585"}, {"RefNumber": "400525", "RefComponent": "IS-OIL-OL", "RefTitle": "Incorrect store location during shipment receipt", "RefUrl": "/notes/400525"}, {"RefNumber": "400419", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "Formula Maintenance ends in duplicate record", "RefUrl": "/notes/400419"}, {"RefNumber": "400071", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Corrections to OIK_CHANGE_ORDER", "RefUrl": "/notes/400071"}, {"RefNumber": "399825", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Exclude items from ERS is not correct", "RefUrl": "/notes/399825"}, {"RefNumber": "399436", "RefComponent": "IS-OIL-DS-TDP", "RefTitle": "IS-OIL TDP Sales excise duty revenue adjustment", "RefUrl": "/notes/399436"}, {"RefNumber": "399019", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "TD Travel time fields not filled from shimpent", "RefUrl": "/notes/399019"}, {"RefNumber": "398955", "RefComponent": "IS-OIL", "RefTitle": "ISO conversion leads to blank unit of measures", "RefUrl": "/notes/398955"}, {"RefNumber": "398954", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Shipment Planning corrections", "RefUrl": "/notes/398954"}, {"RefNumber": "398764", "RefComponent": "IS-OIL-DS-HPM", "RefTitle": "Incorrect weights and volumes in delivery", "RefUrl": "/notes/398764"}, {"RefNumber": "398747", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "No error at O4PO if delivery is assigned to shipment", "RefUrl": "/notes/398747"}, {"RefNumber": "398658", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "Append OILTCURR from table TCURR deleted with 4.6C", "RefUrl": "/notes/398658"}, {"RefNumber": "397831", "RefComponent": "IS-OIL-BC", "RefTitle": "IS-OIL: Additional Info - SPs 57-60 incl. 4.0B", "RefUrl": "/notes/397831"}, {"RefNumber": "397767", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "MRRL Duplicate error messages in the ERS run output", "RefUrl": "/notes/397767"}, {"RefNumber": "397101", "RefComponent": "IS-OIL-DS-HPM", "RefTitle": "IS-Oil: Error in rebate agreement with zero document", "RefUrl": "/notes/397101"}, {"RefNumber": "397003", "RefComponent": "IS-OIL-DS-QCI", "RefTitle": "IS OIL: negative quantity entry in HPM dialog box", "RefUrl": "/notes/397003"}, {"RefNumber": "396242", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "Dump in TD bulk shp in OIB2_TD_GET_MAT_TEMP by NO_RECORD exc", "RefUrl": "/notes/396242"}, {"RefNumber": "395885", "RefComponent": "IS-OIL-DS-SSR", "RefTitle": "OIL: SSR Field conversions retrieves wrong value", "RefUrl": "/notes/395885"}, {"RefNumber": "395874", "RefComponent": "IS-OIL-DS-TDP", "RefTitle": "IS-OIL LOCALIZATION BRAZIL ICMS COMPLEMENT POSTING", "RefUrl": "/notes/395874"}, {"RefNumber": "395803", "RefComponent": "IS-OIL-DS-TDP", "RefTitle": "IS-OIL ERS TDP POSTINGS WITH DIFFERENT CURRENCIES", "RefUrl": "/notes/395803"}, {"RefNumber": "395164", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/395164"}, {"RefNumber": "394795", "RefComponent": "IS-OIL-DS-TDP", "RefTitle": "IS-OIL TDP TAX QUANTITY IN MATERIAL DOCUMENT", "RefUrl": "/notes/394795"}, {"RefNumber": "393987", "RefComponent": "IS-OIL-DS-MCO", "RefTitle": "IS-OIL: Clear sales office and group in SO entry", "RefUrl": "/notes/393987"}, {"RefNumber": "393394", "RefComponent": "IS-OIL-DS-SSR", "RefTitle": "OTWS error on SCP screen of Business Location", "RefUrl": "/notes/393394"}, {"RefNumber": "393390", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "Time in 'Status and deadlines box'", "RefUrl": "/notes/393390"}, {"RefNumber": "392985", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "MR21: Double values passed to CO, accounting items missing", "RefUrl": "/notes/392985"}, {"RefNumber": "392666", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "SAPSQL_ARRAY_INSERT_DUPREC in ME_UPDATE_DOCUMENT", "RefUrl": "/notes/392666"}, {"RefNumber": "392647", "RefComponent": "IS-OIL-DS-HPM", "RefTitle": "EKAB updated wrongly during goods receipt in MB01 or MIGO", "RefUrl": "/notes/392647"}, {"RefNumber": "392597", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Impossible to change exchange starting date", "RefUrl": "/notes/392597"}, {"RefNumber": "392391", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Missing gain/loss posting at goods receipt/issue", "RefUrl": "/notes/392391"}, {"RefNumber": "392236", "RefComponent": "IS-OIL", "RefTitle": "ISO conversion leads to blank unit of measures", "RefUrl": "/notes/392236"}, {"RefNumber": "392231", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "No error at O4PO if delivery is assigned to shipment", "RefUrl": "/notes/392231"}, {"RefNumber": "392082", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "Wrong data format in forwarded Idoc OILLDD & OILLDC", "RefUrl": "/notes/392082"}, {"RefNumber": "392074", "RefComponent": "IS-OIL-DS-MCO", "RefTitle": "FDI indicator invisible at delivery note return order", "RefUrl": "/notes/392074"}, {"RefNumber": "391987", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "GR Repricing does not reprice exchange fees", "RefUrl": "/notes/391987"}, {"RefNumber": "391477", "RefComponent": "IS-OIL-DS-MRN", "RefTitle": "Invoice split dur.int-comp.billng:location assgnmt", "RefUrl": "/notes/391477"}, {"RefNumber": "391448", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Create acc. doc. w/ BTCI: no exchange no. field", "RefUrl": "/notes/391448"}, {"RefNumber": "390711", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Cancelling invoice passes wrong quantity to LIS", "RefUrl": "/notes/390711"}, {"RefNumber": "390503", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "Billing docs with zero qty for already billed SD documents.", "RefUrl": "/notes/390503"}, {"RefNumber": "388915", "RefComponent": "IS-OIL", "RefTitle": "ISO conversion leads to blank unit of measures", "RefUrl": "/notes/388915"}, {"RefNumber": "388788", "RefComponent": "IS-OIL-DS-TDP", "RefTitle": "IS-OIL: MBST/MB01 REVERSAL OF GR AGAINST PO", "RefUrl": "/notes/388788"}, {"RefNumber": "388748", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Fees in invoice verification should not be parked", "RefUrl": "/notes/388748"}, {"RefNumber": "388602", "RefComponent": "IS-OIL-DS-TDP", "RefTitle": "Incorrect Repricing in Invoice Correction Request", "RefUrl": "/notes/388602"}, {"RefNumber": "388449", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "Inter company Sales in TD - Error KE 396", "RefUrl": "/notes/388449"}, {"RefNumber": "387303", "RefComponent": "IS-OIL-DS-HPM", "RefTitle": "Inventory batch input", "RefUrl": "/notes/387303"}, {"RefNumber": "386718", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "QS message O1 012 customizable by table T160M", "RefUrl": "/notes/386718"}, {"RefNumber": "386232", "RefComponent": "IS-OIL-DS-HPM", "RefTitle": "OIL: No update of net weight after availability check", "RefUrl": "/notes/386232"}, {"RefNumber": "385889", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Fees or taxes missing on netting statement", "RefUrl": "/notes/385889"}, {"RefNumber": "385417", "RefComponent": "IS-OIL-DS-HPM", "RefTitle": "IS-Oil: Error when changing stor.loc. in delivery", "RefUrl": "/notes/385417"}, {"RefNumber": "384950", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/384950"}, {"RefNumber": "384877", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Bad performance at VF04/VF06 because of too much enqueues", "RefUrl": "/notes/384877"}, {"RefNumber": "384863", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "Display problems in differential invoices", "RefUrl": "/notes/384863"}, {"RefNumber": "384804", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "MAP: Value without quotation in second level analys", "RefUrl": "/notes/384804"}, {"RefNumber": "383918", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "BSW factor is not appearing in oil calc. at load confirm.", "RefUrl": "/notes/383918"}, {"RefNumber": "382846", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Fees missing on invoice print", "RefUrl": "/notes/382846"}, {"RefNumber": "382255", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "TD: Display purchase requisition from documentflow", "RefUrl": "/notes/382255"}, {"RefNumber": "381907", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "TD-F: Conversion of UoM for shipment costing", "RefUrl": "/notes/381907"}, {"RefNumber": "381772", "RefComponent": "IS-OIL-DS-HPM", "RefTitle": "Incorrect screen to display Consignment Pricing", "RefUrl": "/notes/381772"}, {"RefNumber": "381741", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "Mtr rdg; Part.conf.interf.; Load reserv.; Del.conf.<PERSON>ot", "RefUrl": "/notes/381741"}, {"RefNumber": "381616", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Goods receipt: Wrong internal material payables", "RefUrl": "/notes/381616"}, {"RefNumber": "381516", "RefComponent": "IS-OIL-DS-TDP", "RefTitle": "Short dump in VF04 on Nota Fiscal Complementar", "RefUrl": "/notes/381516"}, {"RefNumber": "381462", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "F4 help on shipment workbench shows all drivers", "RefUrl": "/notes/381462"}, {"RefNumber": "380993", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "Wrong sorting in formula term item tab XOICQ8 and XOICQ9", "RefUrl": "/notes/380993"}, {"RefNumber": "380744", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Cancelling invoice with fees: no accounting document", "RefUrl": "/notes/380744"}, {"RefNumber": "380630", "RefComponent": "IS-OIL-DS-HPM", "RefTitle": "IS-OIL: Wrong additional quantities in delivery due list", "RefUrl": "/notes/380630"}, {"RefNumber": "380451", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Wrong/Repetitive warning message O1 811 in MR01 simulation", "RefUrl": "/notes/380451"}, {"RefNumber": "380390", "RefComponent": "IS-OIL-DS-HPM", "RefTitle": "IS-OIL: GR FOR SUBCONTRACT PURCHASE ORDER WITH FRIGHT", "RefUrl": "/notes/380390"}, {"RefNumber": "379875", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Wrong GI price", "RefUrl": "/notes/379875"}, {"RefNumber": "379751", "RefComponent": "IS-OIL-DS-TDP", "RefTitle": "Excise Duty Screen appears empty in the Incompl.Log", "RefUrl": "/notes/379751"}, {"RefNumber": "379423", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "Doc. item qty assign. in case of weight-volume-deviations", "RefUrl": "/notes/379423"}, {"RefNumber": "378700", "RefComponent": "IS-OIL-DS-HPM", "RefTitle": "IS-OIL: CO13/CO15/CO..with ASTM and or TDP Material", "RefUrl": "/notes/378700"}, {"RefNumber": "378406", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "No gain/loss post. at GR/GI with diff. sub/baseproduct", "RefUrl": "/notes/378406"}, {"RefNumber": "378352", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "Compart. not displayed for PTL rebrand during Shpt Loading", "RefUrl": "/notes/378352"}, {"RefNumber": "378285", "RefComponent": "IS-OIL-DS-HPM", "RefTitle": "Correction for short dump due to TABLE_INVALID_INDEX", "RefUrl": "/notes/378285"}, {"RefNumber": "377118", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Quantity missing in logical inventory", "RefUrl": "/notes/377118"}, {"RefNumber": "376579", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "MAP:Conditions with qty scale basis become inactive in docs", "RefUrl": "/notes/376579"}, {"RefNumber": "376541", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Performance improvement Euro-conversion LIKP", "RefUrl": "/notes/376541"}, {"RefNumber": "375158", "RefComponent": "BC-UPG-ADDON", "RefTitle": "IS-OIL: Additional Info - SPs 54-56 incl. 4.0B", "RefUrl": "/notes/375158"}, {"RefNumber": "374985", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "MR01 delivery cost sequence as per GR sequence", "RefUrl": "/notes/374985"}, {"RefNumber": "374846", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Error in changing ´plant´ of order", "RefUrl": "/notes/374846"}, {"RefNumber": "373605", "RefComponent": "IS-OIL-DS-HPM", "RefTitle": "IS-Oil: Error in dynamic UoM enhancement (F&A fees)", "RefUrl": "/notes/373605"}, {"RefNumber": "372540", "RefComponent": "IS-OIL-DS-HPM", "RefTitle": "IS-Oil: Inconsistency in additional quantities for delivery", "RefUrl": "/notes/372540"}, {"RefNumber": "372080", "RefComponent": "IS-OIL-DS-HPM", "RefTitle": "IS-Oil: Error in dynamic UoM enhancement", "RefUrl": "/notes/372080"}, {"RefNumber": "371447", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Consideration of gain/losses at the LIA posting", "RefUrl": "/notes/371447"}, {"RefNumber": "370775", "RefComponent": "IS-OIL-DS-TSW", "RefTitle": "CONVERSION FACTOR ERROR", "RefUrl": "/notes/370775"}, {"RefNumber": "370570", "RefComponent": "IS-OIL-DS-HPM", "RefTitle": "IS-Oil: Incorrect valuation of vendor consignment stock", "RefUrl": "/notes/370570"}, {"RefNumber": "370169", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Missing order item in TPI outbound idoc", "RefUrl": "/notes/370169"}, {"RefNumber": "370008", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "Events with default event type not getting saved.", "RefUrl": "/notes/370008"}, {"RefNumber": "369927", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "Quant. conv. factors from info record only if they exist", "RefUrl": "/notes/369927"}, {"RefNumber": "369591", "RefComponent": "IS-OIL-DS-TDP", "RefTitle": "IS-OIL: Display ED licenses during sales order", "RefUrl": "/notes/369591"}, {"RefNumber": "368957", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "EKBZ update excise duty addback", "RefUrl": "/notes/368957"}, {"RefNumber": "368749", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "MBST:Wrong F&A condition value displayed in PO history", "RefUrl": "/notes/368749"}, {"RefNumber": "367704", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Goods issue: Double S036 update", "RefUrl": "/notes/367704"}, {"RefNumber": "367022", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Redistribution of a Order planned by Ext TPS", "RefUrl": "/notes/367022"}, {"RefNumber": "366778", "RefComponent": "IS-OIL-DS-MRN", "RefTitle": "Inconsistency in prof.segm.number for intercompany billing", "RefUrl": "/notes/366778"}, {"RefNumber": "366622", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Posting of exchange reversal fees to SL not possible", "RefUrl": "/notes/366622"}, {"RefNumber": "365908", "RefComponent": "IS-OIL-DS-HPM", "RefTitle": "IS OIL: PI with freeze book indicator", "RefUrl": "/notes/365908"}, {"RefNumber": "365843", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "No record OIGSVMQO1;GI of STO for new valtyp; 195869", "RefUrl": "/notes/365843"}, {"RefNumber": "365694", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "Minus(-) Temperature is not processed.", "RefUrl": "/notes/365694"}, {"RefNumber": "365418", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "G_OIKIMPORT struct not populated in SD_SALES_ITEM_MAINTAIN", "RefUrl": "/notes/365418"}, {"RefNumber": "364919", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Inbound shipment interface abends on delivery create error", "RefUrl": "/notes/364919"}, {"RefNumber": "364900", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "TankID is lost, if items are deleted+re-created in s/o", "RefUrl": "/notes/364900"}, {"RefNumber": "364780", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Inbound interface abends on deleted order", "RefUrl": "/notes/364780"}, {"RefNumber": "364777", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Negative quantities in quantity schedule", "RefUrl": "/notes/364777"}, {"RefNumber": "364644", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Shipment change on TPI planning screen deletes carrier", "RefUrl": "/notes/364644"}, {"RefNumber": "364320", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Delivery update termin./ excise duty cond. missing", "RefUrl": "/notes/364320"}, {"RefNumber": "364092", "RefComponent": "IS-OIL-DS-TSW", "RefTitle": "TSW Ticketing: Load Confirmation", "RefUrl": "/notes/364092"}, {"RefNumber": "364063", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/364063"}, {"RefNumber": "364026", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Problems at the datacopy from MM contract to po- TDP related", "RefUrl": "/notes/364026"}, {"RefNumber": "363888", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "Differential invoice and taxes", "RefUrl": "/notes/363888"}, {"RefNumber": "363310", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "Dump at the creation of an iv. list (OIC_DITAB is not def.)", "RefUrl": "/notes/363310"}, {"RefNumber": "363140", "RefComponent": "IS-OIL-DS-MRN", "RefTitle": "IS-Oil MRN field OIFWE not initialized properly", "RefUrl": "/notes/363140"}, {"RefNumber": "363131", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "Error proc.while assign. v.mtr with del.flag to Transp.Unit", "RefUrl": "/notes/363131"}, {"RefNumber": "363115", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "Error M7 234 for movement involving vendor consignment", "RefUrl": "/notes/363115"}, {"RefNumber": "362910", "RefComponent": "IS-OIL-DS-TDP", "RefTitle": "IS-OIL: Proposal of valuation type in batch field", "RefUrl": "/notes/362910"}, {"RefNumber": "362516", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "VL02: update error for purchase assignment", "RefUrl": "/notes/362516"}, {"RefNumber": "362410", "RefComponent": "IS-OIL-DS-MCO", "RefTitle": "T063 entries missing with function code OIDE", "RefUrl": "/notes/362410"}, {"RefNumber": "362077", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Financial documents not found in netting", "RefUrl": "/notes/362077"}, {"RefNumber": "361878", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "Event doc type not found (msg E220)", "RefUrl": "/notes/361878"}, {"RefNumber": "361335", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Error O1 573 during purch. ass. with group cond.", "RefUrl": "/notes/361335"}, {"RefNumber": "361284", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "MR01/MRHG: Delivery Cost Credit Memo error between years", "RefUrl": "/notes/361284"}, {"RefNumber": "361254", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "Partner det. in call-off from ship-to with SAP/TAS interfa", "RefUrl": "/notes/361254"}, {"RefNumber": "360432", "RefComponent": "IS-OIL-DS-TDP", "RefTitle": "Error OB 011 create PO with ref to requisition", "RefUrl": "/notes/360432"}, {"RefNumber": "360233", "RefComponent": "IS-OIL-DS-BDRP", "RefTitle": "Open hours check fails for Time Frames", "RefUrl": "/notes/360233"}, {"RefNumber": "358972", "RefComponent": "IS-OIL-DS-TDP", "RefTitle": "A valid handling type has to be entered for this material", "RefUrl": "/notes/358972"}, {"RefNumber": "358259", "RefComponent": "IS-OIL-DS-TDP", "RefTitle": "HT default from Process order during GI", "RefUrl": "/notes/358259"}, {"RefNumber": "357343", "RefComponent": "IS-OIL-DS-QCI", "RefTitle": "Session locked by qty conversion for 0 density/rel. density", "RefUrl": "/notes/357343"}, {"RefNumber": "357274", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "Incorrect tax code in acc.doc. for diff.invoice", "RefUrl": "/notes/357274"}, {"RefNumber": "356722", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "IS-OIL: Adaption of preprocessing Program EWUMMPOA", "RefUrl": "/notes/356722"}, {"RefNumber": "356471", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "CASE II ROIGMS00: Inconsistencies between SAP and terminal", "RefUrl": "/notes/356471"}, {"RefNumber": "356301", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Borrow/Loan exchange posting to Purchase Acct. Mgt.", "RefUrl": "/notes/356301"}, {"RefNumber": "356080", "RefComponent": "IS-OIL-DS-MCO", "RefTitle": "External Details missing when plant entered manually", "RefUrl": "/notes/356080"}, {"RefNumber": "356007", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "Set loading date/time in freight assignment (O4L1)", "RefUrl": "/notes/356007"}, {"RefNumber": "355654", "RefComponent": "IS-OIL-DS-TSW", "RefTitle": "Base Location lookup  for customer exit not working", "RefUrl": "/notes/355654"}, {"RefNumber": "355588", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Error handling in transaction O4PO", "RefUrl": "/notes/355588"}, {"RefNumber": "355564", "RefComponent": "IS-OIL-DS-TDP", "RefTitle": "IS-OIL: ED Revaluation if base UoM not equal ED UoM", "RefUrl": "/notes/355564"}, {"RefNumber": "355142", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "O4F2: Update terminated on vehicle change in bulk shipment", "RefUrl": "/notes/355142"}, {"RefNumber": "354546", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "MAP: Price not found, if non-posted day = 'X'", "RefUrl": "/notes/354546"}, {"RefNumber": "353978", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Ship-to party incorrectly displayed", "RefUrl": "/notes/353978"}, {"RefNumber": "353230", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Authority check fails for quantity schedule", "RefUrl": "/notes/353230"}, {"RefNumber": "352656", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Delivery w/ purchase assignment: update termination", "RefUrl": "/notes/352656"}, {"RefNumber": "352264", "RefComponent": "IS-OIL-DS-HPM", "RefTitle": "Populate LIPS-LGMNG with qty in base UoM for GI reversal", "RefUrl": "/notes/352264"}, {"RefNumber": "351945", "RefComponent": "IS-OIL-DS-HPM", "RefTitle": "Calc. sales tax acc. to new price cond. when price control 5", "RefUrl": "/notes/351945"}, {"RefNumber": "351931", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Abend O1 544 on Delivery confirmation", "RefUrl": "/notes/351931"}, {"RefNumber": "351836", "RefComponent": "IS-OIL-DS-HPM", "RefTitle": "IS-Oil: No repricing for oil BOM header", "RefUrl": "/notes/351836"}, {"RefNumber": "351807", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "LIPS upd missing; Delete NAST", "RefUrl": "/notes/351807"}, {"RefNumber": "351666", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "Input of init.value not allowed for field V_T685A-OIREPORT", "RefUrl": "/notes/351666"}, {"RefNumber": "351652", "RefComponent": "IS-OIL-DS-HPM", "RefTitle": "IS-Oil: Update termination during goods issue", "RefUrl": "/notes/351652"}, {"RefNumber": "351626", "RefComponent": "IS-OIL-DS-MRN", "RefTitle": "Missing IS-Oil fields in CO-PA KENC table", "RefUrl": "/notes/351626"}, {"RefNumber": "351551", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "Received error message when add a new output item", "RefUrl": "/notes/351551"}, {"RefNumber": "351482", "RefComponent": "IS-OIL-BC", "RefTitle": "IS-OIL: Additional Info - LCP 45-53 incl. 4.0B", "RefUrl": "/notes/351482"}, {"RefNumber": "351475", "RefComponent": "IS-OIL-BC", "RefTitle": "IS-OIL: Additional Info - SPs 45-53 incl. 4.0B", "RefUrl": "/notes/351475"}, {"RefNumber": "351229", "RefComponent": "IS-OIL-DS-TDP", "RefTitle": "Valuation type not defaulted on change of plant", "RefUrl": "/notes/351229"}, {"RefNumber": "350797", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "G_OIKIMPORT structure not populated in SD_ITEM_MAINTAIN", "RefUrl": "/notes/350797"}, {"RefNumber": "350766", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "Search help VMVL ID T causes problems", "RefUrl": "/notes/350766"}, {"RefNumber": "350742", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Tank-ID screen in TD-Deliver Confirmation comes blank", "RefUrl": "/notes/350742"}, {"RefNumber": "350486", "RefComponent": "IS-OIL-DS-TSW", "RefTitle": "IS-OIL : TSW : Nominations not printing", "RefUrl": "/notes/350486"}, {"RefNumber": "350212", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "Update termination in shipment delete due to credit check", "RefUrl": "/notes/350212"}, {"RefNumber": "339524", "RefComponent": "IS-OIL-DS-TDP", "RefTitle": "Enable stock transfer when Han.Type&VT have diff. ED Status", "RefUrl": "/notes/339524"}, {"RefNumber": "338865", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Split invoicing not working for accruals", "RefUrl": "/notes/338865"}, {"RefNumber": "338202", "RefComponent": "IS-OIL-DS-MRN", "RefTitle": "Characteristic derivation PSPID -> OIFPBL does not work", "RefUrl": "/notes/338202"}, {"RefNumber": "337560", "RefComponent": "IS-OIL-DS-MCO", "RefTitle": "Credit memo request with reference to invoice.", "RefUrl": "/notes/337560"}, {"RefNumber": "337217", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "TD Status Handling enhancement during Scheduling", "RefUrl": "/notes/337217"}, {"RefNumber": "336962", "RefComponent": "IS-OIL-DS-TDP", "RefTitle": "IS-OIL: MR21/MR22 allow postings at negativ stock level", "RefUrl": "/notes/336962"}, {"RefNumber": "336664", "RefComponent": "IS-OIL-DS-HPM", "RefTitle": "IS-Oil: Quantity conversion error in rebate agreement", "RefUrl": "/notes/336664"}, {"RefNumber": "336419", "RefComponent": "IS-OIL-DS-TDP", "RefTitle": "VT cannot be changed after default from contract to order", "RefUrl": "/notes/336419"}, {"RefNumber": "336131", "RefComponent": "IS-OIL-DS-MCO", "RefTitle": "External details WAOIDEVB entry missing in T063", "RefUrl": "/notes/336131"}, {"RefNumber": "336109", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "Short dump due to formula & average fee condition type", "RefUrl": "/notes/336109"}, {"RefNumber": "335955", "RefComponent": "IS-OIL-DS-MCO", "RefTitle": "GR: Field RM07M-LFSNR is not filled from MKPF-XBLNR", "RefUrl": "/notes/335955"}, {"RefNumber": "335717", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Exchange abstract reprices sales fees incorrectly", "RefUrl": "/notes/335717"}, {"RefNumber": "335256", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Fees in invoice verification should not be parked", "RefUrl": "/notes/335256"}, {"RefNumber": "334920", "RefComponent": "IS-OIL-DS-BDRP", "RefTitle": "Problem in Archiving of Customer without BDRP data", "RefUrl": "/notes/334920"}, {"RefNumber": "334795", "RefComponent": "IS-OIL-DS-TSW", "RefTitle": "Error in conversion factor in rundown report", "RefUrl": "/notes/334795"}, {"RefNumber": "334462", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "Sort order doc.flow; Rounding load interface; reserv. batch", "RefUrl": "/notes/334462"}, {"RefNumber": "333978", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "duplicates in netting document list for exchange", "RefUrl": "/notes/333978"}, {"RefNumber": "333308", "RefComponent": "IS-OIL-DS-TDP", "RefTitle": "IS-OIL: Excise Duty Inventory check reports", "RefUrl": "/notes/333308"}, {"RefNumber": "333273", "RefComponent": "IS-OIL-DS-TSW", "RefTitle": "TSW Modifications to fix Batch/Valuations", "RefUrl": "/notes/333273"}, {"RefNumber": "332993", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "Formula Price calculation in relation to quantity", "RefUrl": "/notes/332993"}, {"RefNumber": "332989", "RefComponent": "IS-OIL-DS-TSW", "RefTitle": "Update of nomination failed", "RefUrl": "/notes/332989"}, {"RefNumber": "332960", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/332960"}, {"RefNumber": "332654", "RefComponent": "IS-OIL-BC", "RefTitle": "IS-OIL / IS-MINE / IS-CWM: Overview of SAP Notes", "RefUrl": "/notes/332654"}, {"RefNumber": "332287", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "S3VBRKWR: SQL error 4031 when accessing table OICQ7", "RefUrl": "/notes/332287"}, {"RefNumber": "332092", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "LIA does not transfer Trading partner to FI", "RefUrl": "/notes/332092"}, {"RefNumber": "331952", "RefComponent": "IS-OIL-DS-TSW", "RefTitle": "Rundown engine calculations for transport system.", "RefUrl": "/notes/331952"}, {"RefNumber": "331304", "RefComponent": "IS-OIL-DS-MCO", "RefTitle": "Error displaying Ext. Dtls. in Goods Issue Docs.", "RefUrl": "/notes/331304"}, {"RefNumber": "330214", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Opening balance on exchange statement missing", "RefUrl": "/notes/330214"}, {"RefNumber": "330194", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Exchange statement stops printing", "RefUrl": "/notes/330194"}, {"RefNumber": "329466", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Fee details can not be processed at goods receipt", "RefUrl": "/notes/329466"}, {"RefNumber": "329203", "RefComponent": "IS-OIL-DS-HPM", "RefTitle": "IS-OIL: Material Master / ALE / oil specific data", "RefUrl": "/notes/329203"}, {"RefNumber": "329200", "RefComponent": "IS-OIL-DS-BDRP", "RefTitle": "Data Archiving Problem : OII_METER_CUSTOMER_INDEX_GET", "RefUrl": "/notes/329200"}, {"RefNumber": "328266", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "ASTM tolerance checks ignored during STSI IDOC processing", "RefUrl": "/notes/328266"}, {"RefNumber": "328204", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "GR reversals missing in movements-based netting", "RefUrl": "/notes/328204"}, {"RefNumber": "328151", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Netting abends saying accounting doc not found", "RefUrl": "/notes/328151"}, {"RefNumber": "328086", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "Short dump due to negative net price in contract display", "RefUrl": "/notes/328086"}, {"RefNumber": "327863", "RefComponent": "IS-OIL-DS-HPM", "RefTitle": "table parameters missing for call RV_DELIVERY_ADD etc.", "RefUrl": "/notes/327863"}, {"RefNumber": "327413", "RefComponent": "IS-OIL-DS-TDP", "RefTitle": "IS-OIL: Document date in material document", "RefUrl": "/notes/327413"}, {"RefNumber": "327364", "RefComponent": "IS-OIL-DS-HPM", "RefTitle": "IS-Oil: Incorrect quantity in SIS info structures", "RefUrl": "/notes/327364"}, {"RefNumber": "327242", "RefComponent": "IS-OIL-DS-BDRP", "RefTitle": "Data Archiving Problem", "RefUrl": "/notes/327242"}, {"RefNumber": "326970", "RefComponent": "IS-OIL", "RefTitle": "Missing setting for change pointers", "RefUrl": "/notes/326970"}, {"RefNumber": "326895", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Open hours check fails for Time Frames", "RefUrl": "/notes/326895"}, {"RefNumber": "326861", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Update termination in SAPLOIIU", "RefUrl": "/notes/326861"}, {"RefNumber": "326623", "RefComponent": "IS-OIL-DS-TSW", "RefTitle": "Cannot create ticket for PO, SO with date < delivery date", "RefUrl": "/notes/326623"}, {"RefNumber": "326517", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "VA01 TAS relevancy checks hinder order entry performance", "RefUrl": "/notes/326517"}, {"RefNumber": "325472", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "Incorrect rebate scale basis after running SDBONTO2", "RefUrl": "/notes/325472"}, {"RefNumber": "325412", "RefComponent": "IS-OIL-DS-TDP", "RefTitle": "IS-OIL:Material movement with n lines, account mod", "RefUrl": "/notes/325412"}, {"RefNumber": "325399", "RefComponent": "IS-OIL-DS-TDP", "RefTitle": "Field KNVV-OIINEX . is not an input field", "RefUrl": "/notes/325399"}, {"RefNumber": "325179", "RefComponent": "IS-OIL-DS-MCO", "RefTitle": "Short dump in display sales order : VA03", "RefUrl": "/notes/325179"}, {"RefNumber": "325052", "RefComponent": "IS-OIL-DS-TDP", "RefTitle": "IS-Oil: Sales - Automatic update of Excise Duty values", "RefUrl": "/notes/325052"}, {"RefNumber": "324964", "RefComponent": "IS-OIL-DS-MRN", "RefTitle": "Classification for business location", "RefUrl": "/notes/324964"}, {"RefNumber": "324806", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Reversal Documents reported in Vertex with the Wrong Sign", "RefUrl": "/notes/324806"}, {"RefNumber": "324518", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "Renaming customer exit function to oil namespace", "RefUrl": "/notes/324518"}, {"RefNumber": "324334", "RefComponent": "IS-OIL-DS-MRN", "RefTitle": "F4 Help for projects within business location", "RefUrl": "/notes/324334"}, {"RefNumber": "324180", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "Abend in VA02 after notes 316902 and 322765", "RefUrl": "/notes/324180"}, {"RefNumber": "323761", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Problem with exchange number when creating sales contracts", "RefUrl": "/notes/323761"}, {"RefNumber": "323387", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "Characteristics for postings to CO-PA from shpmnt", "RefUrl": "/notes/323387"}, {"RefNumber": "323181", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "Update termination in shipment creation", "RefUrl": "/notes/323181"}, {"RefNumber": "323129", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "LID internal numbering not checking OIKLID for duplicates", "RefUrl": "/notes/323129"}, {"RefNumber": "322879", "RefComponent": "IS-OIL-BC", "RefTitle": "SAPMM07M,..: Too many DATA control blocks / generation error", "RefUrl": "/notes/322879"}, {"RefNumber": "322765", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "BOM comp.qty;rebr. batch PtL;check batch STO;scroll on 3700", "RefUrl": "/notes/322765"}, {"RefNumber": "322720", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Impossible to display quantity schedule in contract", "RefUrl": "/notes/322720"}, {"RefNumber": "322646", "RefComponent": "IS-OIL-DS-TSW", "RefTitle": "Ticket delivery date < Ordr delivery date", "RefUrl": "/notes/322646"}, {"RefNumber": "322262", "RefComponent": "IS-OIL-DS-MCO", "RefTitle": "Enable scroll bars/min. resolution in OIL entry order screen", "RefUrl": "/notes/322262"}, {"RefNumber": "322240", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/322240"}, {"RefNumber": "321836", "RefComponent": "IS-OIL-DS-TDP", "RefTitle": "IS-OIL: Tracking of tax exemption licenses", "RefUrl": "/notes/321836"}, {"RefNumber": "321691", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "Incorrect amount in Inv.verif. with GR cancellations", "RefUrl": "/notes/321691"}, {"RefNumber": "321161", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Exchange statement: MM reversal quantity missing", "RefUrl": "/notes/321161"}, {"RefNumber": "321135", "RefComponent": "IS-OIL-DS-BDRP", "RefTitle": "Transp. functionality f. Operational Time Window Sets (OTWS)", "RefUrl": "/notes/321135"}, {"RefNumber": "321127", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Condition base value not moved in routine 409", "RefUrl": "/notes/321127"}, {"RefNumber": "320810", "RefComponent": "IS-OIL-DS-HPM", "RefTitle": "IS-Oil: Incorrect weight/volume in batch split main item", "RefUrl": "/notes/320810"}, {"RefNumber": "320496", "RefComponent": "IS-OIL-DS-HPM", "RefTitle": "IS-OIL: Period close using up all database locks", "RefUrl": "/notes/320496"}, {"RefNumber": "320192", "RefComponent": "IS-OIL-DS-HPM", "RefTitle": "IS-Oil: VL02, two HPM screens appears during batch input", "RefUrl": "/notes/320192"}, {"RefNumber": "320098", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "LIA movement in wrong exchange statement section", "RefUrl": "/notes/320098"}, {"RefNumber": "319796", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "Shipment Load ID valid-to < valid from date", "RefUrl": "/notes/319796"}, {"RefNumber": "319760", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "E1OILSC-MAX_VOL incorrect in OILSHL shipment d/load IDOC", "RefUrl": "/notes/319760"}, {"RefNumber": "319758", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/319758"}, {"RefNumber": "319203", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "To avoid ASTM pop-up during batch input.", "RefUrl": "/notes/319203"}, {"RefNumber": "318966", "RefComponent": "IS-OIL-DS-BDRP", "RefTitle": "Function OII_GET_SOLD_TO_FOR_SHIP_TO short dumps", "RefUrl": "/notes/318966"}, {"RefNumber": "318839", "RefComponent": "IS-OIL-DS-MCO", "RefTitle": "Handling Type in Sales Order Display Screen 8025", "RefUrl": "/notes/318839"}, {"RefNumber": "318707", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "OILLDD IDOC posts w/out ASTM conv is density out of range", "RefUrl": "/notes/318707"}, {"RefNumber": "318688", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Errors in Tank ID handing in VA01/VA02", "RefUrl": "/notes/318688"}, {"RefNumber": "318652", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "IV ignores GR cancelation for items with split conditions", "RefUrl": "/notes/318652"}, {"RefNumber": "318607", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "Wrong excise duty posting to accounting", "RefUrl": "/notes/318607"}, {"RefNumber": "318560", "RefComponent": "IS-OIL-DS-HPM", "RefTitle": "IS-Oil: Error during automatic order creation against GR", "RefUrl": "/notes/318560"}, {"RefNumber": "318436", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "Short dump in invoice verification", "RefUrl": "/notes/318436"}, {"RefNumber": "318434", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "QS Messages customizable by table T160M", "RefUrl": "/notes/318434"}, {"RefNumber": "318351", "RefComponent": "IS-OIL-DS-HPM", "RefTitle": "IS-OIL: Incorrect quantity in rebate agreement", "RefUrl": "/notes/318351"}, {"RefNumber": "318349", "RefComponent": "IS-OIL-DS-TDP", "RefTitle": "IS-OIL: ED Revaluation / Post Difference not possible", "RefUrl": "/notes/318349"}, {"RefNumber": "318202", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "IV: doubled items and fixed amount conditions", "RefUrl": "/notes/318202"}, {"RefNumber": "318040", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "'AW' partners not available in EXIT_SAPLOIK7_130 user exit", "RefUrl": "/notes/318040"}, {"RefNumber": "317951", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Error message when creating Intercompany invoice", "RefUrl": "/notes/317951"}, {"RefNumber": "317717", "RefComponent": "IS-OIL-DS-TDP", "RefTitle": "GR Reversal / Excise Duty / wrong Doc. Currency amounts", "RefUrl": "/notes/317717"}, {"RefNumber": "317519", "RefComponent": "IS-OIL-BC", "RefTitle": "IS-OIL: Additional Info - LCP 39-44 incl. 4.0B", "RefUrl": "/notes/317519"}, {"RefNumber": "317518", "RefComponent": "IS-OIL-BC", "RefTitle": "IS-OIL: Additional Info - SPs 39-44 incl. 4.0B", "RefUrl": "/notes/317518"}, {"RefNumber": "317019", "RefComponent": "IS-OIL-DS-MCO", "RefTitle": "External details not copied to batch split items", "RefUrl": "/notes/317019"}, {"RefNumber": "316998", "RefComponent": "IS-OIL-DS-BDRP", "RefTitle": "ROIIP-RCBLK,DOBLK not update db & SCP blank screen", "RefUrl": "/notes/316998"}, {"RefNumber": "316979", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "Divide by zero error during invoice verification", "RefUrl": "/notes/316979"}, {"RefNumber": "316902", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "Close TD BOM component according to main item", "RefUrl": "/notes/316902"}, {"RefNumber": "316891", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Flexible Locking of Driver Vehicle Assignment", "RefUrl": "/notes/316891"}, {"RefNumber": "316839", "RefComponent": "IS-OIL-DS-TSW", "RefTitle": "RD report - handling of RDCONV = 0", "RefUrl": "/notes/316839"}, {"RefNumber": "316708", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "IDOC Control record incorrect in IDOC_INPUT_OILDVA", "RefUrl": "/notes/316708"}, {"RefNumber": "316516", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "VL01/VL02/ME32/ME33 errors because OIA05/OIA06 unsorted", "RefUrl": "/notes/316516"}, {"RefNumber": "316453", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Incorrect exchange statement print out when no activities", "RefUrl": "/notes/316453"}, {"RefNumber": "316386", "RefComponent": "IS-OIL-DS-BDRP", "RefTitle": "CALL_FUNCTION_CONFLICT_LENG", "RefUrl": "/notes/316386"}, {"RefNumber": "316218", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "Incorrect account postings in GR with multiples POs", "RefUrl": "/notes/316218"}, {"RefNumber": "315973", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "S036 rebuild picks up some transactions twice", "RefUrl": "/notes/315973"}, {"RefNumber": "315968", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "IS-Oil reference note", "RefUrl": "/notes/315968"}, {"RefNumber": "315730", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Incorrect freight posting for exchange related GR", "RefUrl": "/notes/315730"}, {"RefNumber": "315433", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Mov.based Netting errors for cancelled invoice ver.", "RefUrl": "/notes/315433"}, {"RefNumber": "315091", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Advanced functionality for Shipment Planning", "RefUrl": "/notes/315091"}, {"RefNumber": "314967", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Saving of return order terminates", "RefUrl": "/notes/314967"}, {"RefNumber": "314778", "RefComponent": "IS-OIL-DS-QCI", "RefTitle": "IS OIL: QCI: Chemicals and TAS; BSW conversion; BAdI methods", "RefUrl": "/notes/314778"}, {"RefNumber": "314379", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "Shortdump in S3VBRKWR and increase performance", "RefUrl": "/notes/314379"}, {"RefNumber": "314232", "RefComponent": "IS-OIL-DS-HPM", "RefTitle": "IS-OIL: Update termination when saving delivery", "RefUrl": "/notes/314232"}, {"RefNumber": "312369", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Long runtimes when archiving with FI_DOCUMNT", "RefUrl": "/notes/312369"}, {"RefNumber": "312246", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "Parallel OILLDD processing - advanced development", "RefUrl": "/notes/312246"}, {"RefNumber": "312121", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "MM_MATBEL: bad performance and incorrect check for netting", "RefUrl": "/notes/312121"}, {"RefNumber": "311935", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Goods receipt: calculation of base product price", "RefUrl": "/notes/311935"}, {"RefNumber": "311456", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Short dump creating contract with quantity schedule", "RefUrl": "/notes/311456"}, {"RefNumber": "310851", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "Incorrect Quantity for BOM Header in Load Bulk on Activation", "RefUrl": "/notes/310851"}, {"RefNumber": "310788", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "No provision to extend Idoc OILDVA01 Driver/Veh", "RefUrl": "/notes/310788"}, {"RefNumber": "310671", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "External ref number not preserved in transaction O4PP", "RefUrl": "/notes/310671"}, {"RefNumber": "310631", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "Error with Prior to Load ;PTL Not scheduled for load.", "RefUrl": "/notes/310631"}, {"RefNumber": "310523", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "Shipment Load ID re-determination", "RefUrl": "/notes/310523"}, {"RefNumber": "310427", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "Brazil: Cancellation of documents from TD-transfers", "RefUrl": "/notes/310427"}, {"RefNumber": "310287", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Wrong default for date/ time on the time frame screen", "RefUrl": "/notes/310287"}, {"RefNumber": "310158", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "TD-Master User Screen Settings not customizable", "RefUrl": "/notes/310158"}, {"RefNumber": "310095", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "Wrong positioning in shpt scheduling - doc./compart. details", "RefUrl": "/notes/310095"}, {"RefNumber": "309717", "RefComponent": "IS-OIL-DS-TDP", "RefTitle": "ED qty in special ledger / ED inventory during transfer", "RefUrl": "/notes/309717"}, {"RefNumber": "309707", "RefComponent": "IS-OIL-DS-TDP", "RefTitle": "No account posting in sub-contract PO with Oil material", "RefUrl": "/notes/309707"}, {"RefNumber": "309618", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "IS-OIL Output determination for Delivery Confirmation", "RefUrl": "/notes/309618"}, {"RefNumber": "309533", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "Performance problems in differential invoice", "RefUrl": "/notes/309533"}, {"RefNumber": "308790", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "TD - Document flow;Branch to mat.doc,Incorrect GJAHR", "RefUrl": "/notes/308790"}, {"RefNumber": "308026", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Incorrect Txn Curr  Amount Displayed on Netting Proposal", "RefUrl": "/notes/308026"}, {"RefNumber": "307732", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "TD Automatic document item quantity assignment", "RefUrl": "/notes/307732"}, {"RefNumber": "306559", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Inconsistent date used in order item create", "RefUrl": "/notes/306559"}, {"RefNumber": "306295", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "Shipment number selection based on vehicle details", "RefUrl": "/notes/306295"}, {"RefNumber": "305918", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "Air buoyancy indicator handling via STSI Load Confirmation", "RefUrl": "/notes/305918"}, {"RefNumber": "305891", "RefComponent": "IS-OIL-DS-MCO", "RefTitle": "VA01 allows order type which is not allowed for this SA", "RefUrl": "/notes/305891"}, {"RefNumber": "305704", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "OIK03 table entry not deleted after manual POST GI", "RefUrl": "/notes/305704"}, {"RefNumber": "305064", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Variable PO doc. type in purch. assignment", "RefUrl": "/notes/305064"}, {"RefNumber": "304626", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Bad performance of ERS fee processing", "RefUrl": "/notes/304626"}, {"RefNumber": "303827", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "Enable Group Conditions 4 and 5 in IS-OIL 4.0B", "RefUrl": "/notes/303827"}, {"RefNumber": "303797", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Euro conversion for fee rates in invoice verification", "RefUrl": "/notes/303797"}, {"RefNumber": "303471", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "LID Flowlog report display incorrect", "RefUrl": "/notes/303471"}, {"RefNumber": "303220", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Tank ID defaulting in orders incorrect on change", "RefUrl": "/notes/303220"}, {"RefNumber": "303128", "RefComponent": "IS-OIL-DS-HPM", "RefTitle": "IS-Oil: AUM posting with incorrect plant", "RefUrl": "/notes/303128"}, {"RefNumber": "303089", "RefComponent": "IS-OIL-DS-HPM", "RefTitle": "IS-Oil: Update termination when saving dlv. with oil BoM", "RefUrl": "/notes/303089"}, {"RefNumber": "302746", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "Error in Creation of Idoc OILORD", "RefUrl": "/notes/302746"}, {"RefNumber": "302327", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "IS OIL OUTPUT DETERMINATION", "RefUrl": "/notes/302327"}, {"RefNumber": "302311", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Unable to Use Valuation Class in LIA Acct. Determination", "RefUrl": "/notes/302311"}, {"RefNumber": "301601", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "Part.Conf.STO; Tolerance gain STO; Batch in interf.; uexit", "RefUrl": "/notes/301601"}, {"RefNumber": "301385", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "Population of ship-to in TAS shipment IDoc", "RefUrl": "/notes/301385"}, {"RefNumber": "301021", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Inconsistent date used in order item create", "RefUrl": "/notes/301021"}, {"RefNumber": "301014", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Incorrect TPP check in IDOC_OUTPUT_OILTPI50", "RefUrl": "/notes/301014"}, {"RefNumber": "300869", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Split Indicator required in SD&MM Documents", "RefUrl": "/notes/300869"}, {"RefNumber": "300849", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "ABAP ROIKPGIS", "RefUrl": "/notes/300849"}, {"RefNumber": "300784", "RefComponent": "IS-OIL-DS-MRN", "RefTitle": "Table \"S???\" is not listed in the ABAP/4 Dictionary", "RefUrl": "/notes/300784"}, {"RefNumber": "300744", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "TD Material freight classes/freight codes in shpt costing", "RefUrl": "/notes/300744"}, {"RefNumber": "300650", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Incorrect market quote found after pricing date change in SO", "RefUrl": "/notes/300650"}, {"RefNumber": "300647", "RefComponent": "IS-OIL-DS-TSW", "RefTitle": "Mode of transport indicator not enabled for rail", "RefUrl": "/notes/300647"}, {"RefNumber": "300629", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Unnecessary messages in Tank defaulting", "RefUrl": "/notes/300629"}, {"RefNumber": "300611", "RefComponent": "IS-OIL-DS-HPM", "RefTitle": "IS-Oil: Minor corrections hydrocarbon inventory management", "RefUrl": "/notes/300611"}, {"RefNumber": "300406", "RefComponent": "IS-OIL-DS-TDP", "RefTitle": "IS-OIL: Batch split / ED license not copied to mat. doc.", "RefUrl": "/notes/300406"}, {"RefNumber": "300374", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "Actual goods issue date in TAS interface (pick-up)", "RefUrl": "/notes/300374"}, {"RefNumber": "300298", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "Multiple Change pointers generated for OILLID", "RefUrl": "/notes/300298"}, {"RefNumber": "300199", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Tables OIKLIDR/OIK01 become inconsistent for shipments", "RefUrl": "/notes/300199"}, {"RefNumber": "300151", "RefComponent": "IS-OIL-DS-MCO", "RefTitle": "Hold data not enabled in IS-Oil overview screen", "RefUrl": "/notes/300151"}, {"RefNumber": "300150", "RefComponent": "IS-OIL-DS-TDP", "RefTitle": "Exd license not valid (problem with the valid from/to date)", "RefUrl": "/notes/300150"}, {"RefNumber": "217379", "RefComponent": "IS-OIL-DS-HPM", "RefTitle": "IS-Oil: incorrect quantity in GR for replenishment del.", "RefUrl": "/notes/217379"}, {"RefNumber": "217126", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "Parallel OILLDD inbound processing", "RefUrl": "/notes/217126"}, {"RefNumber": "216894", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "Object Key of change pointer (OILLID-OIGV) is wrong ?", "RefUrl": "/notes/216894"}, {"RefNumber": "216522", "RefComponent": "IS-OIL-DS-MCO", "RefTitle": "Screen sequence control entry:T185 SAPMV45B OIDX...", "RefUrl": "/notes/216522"}, {"RefNumber": "216413", "RefComponent": "IS-OIL-DS-HPM", "RefTitle": "IS-OIL: Incorrect quantity proposed in sales order", "RefUrl": "/notes/216413"}, {"RefNumber": "216249", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Status messages not returned to TPI", "RefUrl": "/notes/216249"}, {"RefNumber": "216079", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "Low performance when creat. deliveries for big contracts", "RefUrl": "/notes/216079"}, {"RefNumber": "216067", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Additional customer exits", "RefUrl": "/notes/216067"}, {"RefNumber": "216045", "RefComponent": "IS-OIL-DS-HPM", "RefTitle": "IS-OIL: Incorrect packing status", "RefUrl": "/notes/216045"}, {"RefNumber": "216007", "RefComponent": "IS-OIL-DS-MCO", "RefTitle": "NO CURRENCY/EX<PERSON>ANGE RATE FIELD ON DYNPRO SAPMV45A 4301", "RefUrl": "/notes/216007"}, {"RefNumber": "215984", "RefComponent": "IS-OIL-DS-BDRP", "RefTitle": "Minor errors on Plant Site Control Parameters", "RefUrl": "/notes/215984"}, {"RefNumber": "215705", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "Quotation Integrity Check - Date Range Requirement", "RefUrl": "/notes/215705"}, {"RefNumber": "215636", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "New fee pricing date is not stored in the PO", "RefUrl": "/notes/215636"}, {"RefNumber": "215633", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Checking base product for a line that has been deleted.", "RefUrl": "/notes/215633"}, {"RefNumber": "215608", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "Error Handling in TAS Inbound Process", "RefUrl": "/notes/215608"}, {"RefNumber": "215522", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "Tables for sec. level analysis were not sorted", "RefUrl": "/notes/215522"}, {"RefNumber": "215460", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "TD Purchase Assignment Scr: F4 help on PO docs", "RefUrl": "/notes/215460"}, {"RefNumber": "215139", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Order time frame check vs OTWS incorrect", "RefUrl": "/notes/215139"}, {"RefNumber": "215068", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Movement based netting corrections", "RefUrl": "/notes/215068"}, {"RefNumber": "214996", "RefComponent": "IS-OIL-DS-MCO", "RefTitle": "VA03 - Handling Type field Invisible in Screen 8025", "RefUrl": "/notes/214996"}, {"RefNumber": "214794", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "QS error O1017 not customizable by table T160M", "RefUrl": "/notes/214794"}, {"RefNumber": "214666", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "MR8M/MR08 M8607 - Error when reversing invoice", "RefUrl": "/notes/214666"}, {"RefNumber": "214650", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "OILORD02: Segment E1EDP01-ACTION not filled correctly", "RefUrl": "/notes/214650"}, {"RefNumber": "214346", "RefComponent": "IS-OIL-DS-MCO", "RefTitle": "Fields available for input in Display Sales Order", "RefUrl": "/notes/214346"}, {"RefNumber": "213939", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "MR21 posts inconsistent FI-documents with no items", "RefUrl": "/notes/213939"}, {"RefNumber": "213813", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Create sales order with reference to invoice", "RefUrl": "/notes/213813"}, {"RefNumber": "213757", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "Err. PTL, Rebr. to non-batch, Avail.with warning, Veh.Recon", "RefUrl": "/notes/213757"}, {"RefNumber": "213579", "RefComponent": "IS-OIL-DS-MCO", "RefTitle": "Valuation type not displayed as label", "RefUrl": "/notes/213579"}, {"RefNumber": "213093", "RefComponent": "IS-OIL-DS-MCO", "RefTitle": "Manual entry of payment term in order header level", "RefUrl": "/notes/213093"}, {"RefNumber": "213049", "RefComponent": "IS-OIL-DS-TSW", "RefTitle": "TSW: Partner Role Detail Screen Tabcontrol size", "RefUrl": "/notes/213049"}, {"RefNumber": "213030", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "PO History leads to wrong IR Fee Document due to OIAFE", "RefUrl": "/notes/213030"}, {"RefNumber": "212670", "RefComponent": "IS-OIL-DS-HPM", "RefTitle": "IS-Oil: Error in goods issue for oil BoMs", "RefUrl": "/notes/212670"}, {"RefNumber": "212492", "RefComponent": "IS-OIL-DS-MCO", "RefTitle": "Item proposal and item redetermination", "RefUrl": "/notes/212492"}, {"RefNumber": "211421", "RefComponent": "IS-OIL-DS-MCO", "RefTitle": "Delivery Note and Bill of Lading not Appearing in MB02", "RefUrl": "/notes/211421"}, {"RefNumber": "211071", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Order time frame maintenenace not consistent", "RefUrl": "/notes/211071"}, {"RefNumber": "210687", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "No VBTYP in TAS relevancy check for contract creation", "RefUrl": "/notes/210687"}, {"RefNumber": "210205", "RefComponent": "IS-OIL-BC", "RefTitle": "IS-OIL: Additional Info - SPs 31-38 incl. 4.0B", "RefUrl": "/notes/210205"}, {"RefNumber": "210204", "RefComponent": "IS-OIL-BC", "RefTitle": "IS-OIL: Additional Info - LCP 31-38 incl. 4.0B", "RefUrl": "/notes/210204"}, {"RefNumber": "210030", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "MR1M:Wrong Update of planned cost with diffrent Inv.Cycle", "RefUrl": "/notes/210030"}, {"RefNumber": "209999", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "SMOD entries OIGMEN01 and OIGMEN02 not available", "RefUrl": "/notes/209999"}, {"RefNumber": "209771", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "Wrong invoice amount for account assigned PO items", "RefUrl": "/notes/209771"}, {"RefNumber": "209689", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "Check Load ID Determination", "RefUrl": "/notes/209689"}, {"RefNumber": "207657", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "Back button crea. entr.in chg Output Det.", "RefUrl": "/notes/207657"}, {"RefNumber": "207609", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "VI11, VI12: Selection of TD shipments don't work properly", "RefUrl": "/notes/207609"}, {"RefNumber": "207504", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "MR01: Del. costs by Vendor can be invoiced multiple times", "RefUrl": "/notes/207504"}, {"RefNumber": "206287", "RefComponent": "IS-OIL-DS-BDRP", "RefTitle": "ROIIP-RCBLK and ROIIP-DOBLK missing in Loc. data-> TPS ctrl", "RefUrl": "/notes/206287"}, {"RefNumber": "205759", "RefComponent": "IS-OIL-DS-HPM", "RefTitle": "IS-OIL: Incorrect error message in goods issue log", "RefUrl": "/notes/205759"}, {"RefNumber": "205561", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "Refer mat.doc. for correction GR; No check val.typ", "RefUrl": "/notes/205561"}, {"RefNumber": "205300", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Fees by outline agreement in MR01 does not work", "RefUrl": "/notes/205300"}, {"RefNumber": "204741", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "shortdump when recal. subtotals for rebates", "RefUrl": "/notes/204741"}, {"RefNumber": "204005", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "Load ID customer screen handling", "RefUrl": "/notes/204005"}, {"RefNumber": "203605", "RefComponent": "IS-OIL-DS-HPM", "RefTitle": "IS-Oil: Low performance when reading sales contracts", "RefUrl": "/notes/203605"}, {"RefNumber": "203283", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "Termination in TAS interface for pick-up's", "RefUrl": "/notes/203283"}, {"RefNumber": "203077", "RefComponent": "IS-OIL-DS-BDRP", "RefTitle": "GUI Status OIIPBLCT missing", "RefUrl": "/notes/203077"}, {"RefNumber": "202626", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Function Group OIFT missing", "RefUrl": "/notes/202626"}, {"RefNumber": "202267", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Pur. Assignm.Scr:F4 on the PO doc.does not copy the Item No.", "RefUrl": "/notes/202267"}, {"RefNumber": "201913", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Function call conflict in OIK_TPI_ORDERS_CREATE", "RefUrl": "/notes/201913"}, {"RefNumber": "201437", "RefComponent": "IS-OIL-DS-TDP", "RefTitle": "IS-OIL: Avoid program dump when exceptions are raised", "RefUrl": "/notes/201437"}, {"RefNumber": "201383", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "PO Creation - Field Status Problem", "RefUrl": "/notes/201383"}, {"RefNumber": "201309", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Delivery cost credit memo error when crossing year", "RefUrl": "/notes/201309"}, {"RefNumber": "201305", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Euro conversion for fee rates in goods receipt", "RefUrl": "/notes/201305"}, {"RefNumber": "201282", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "Receive (new) batch at loading; ASTM error after mvmt o brd", "RefUrl": "/notes/201282"}, {"RefNumber": "201122", "RefComponent": "IS-OIL-DS-HPM", "RefTitle": "IS-Oil: Incorrect goods receipt qty.in 2step transfer", "RefUrl": "/notes/201122"}, {"RefNumber": "200957", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Error in handling of EXIT_SAPLOIKS_001", "RefUrl": "/notes/200957"}, {"RefNumber": "200940", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Program to correct wrong quantity schedules (ROIACM00)", "RefUrl": "/notes/200940"}, {"RefNumber": "200816", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "TD allocation of res. to shipment => short dump", "RefUrl": "/notes/200816"}, {"RefNumber": "200706", "RefComponent": "IS-OIL-DS-BDRP", "RefTitle": "GUI status OIIPBLCT missing", "RefUrl": "/notes/200706"}, {"RefNumber": "200316", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Error in incompletion for mandatory Tank ID", "RefUrl": "/notes/200316"}, {"RefNumber": "199746", "RefComponent": "IS-OIL-DS-MCO", "RefTitle": "Transaction termination VI200. Item status is missing", "RefUrl": "/notes/199746"}, {"RefNumber": "199541", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Endless loop in IDOC_OUTPUT_OILTPI50", "RefUrl": "/notes/199541"}, {"RefNumber": "199325", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "Error message OE 018 during TAS pickup IDOC processing", "RefUrl": "/notes/199325"}, {"RefNumber": "199227", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "Load ID customer screen handling", "RefUrl": "/notes/199227"}, {"RefNumber": "198627", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Incorrect shipping point in O4PO", "RefUrl": "/notes/198627"}, {"RefNumber": "198515", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "Shortdump when assigning contract to item in sales order", "RefUrl": "/notes/198515"}, {"RefNumber": "198229", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "TD Deleting partners from shipment", "RefUrl": "/notes/198229"}, {"RefNumber": "198085", "RefComponent": "IS-OIL-DS", "RefTitle": "HP upgrade merge error", "RefUrl": "/notes/198085"}, {"RefNumber": "197908", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "Wrong date calculation in F&A Pricing after 1999.", "RefUrl": "/notes/197908"}, {"RefNumber": "197905", "RefComponent": "IS-OIL-DS-TDP", "RefTitle": "Wrong exd posting when currency is set to 1 decimal place", "RefUrl": "/notes/197905"}, {"RefNumber": "197834", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Split excise duty fails for stock transfer order", "RefUrl": "/notes/197834"}, {"RefNumber": "197770", "RefComponent": "IS-OIL-DS-MRN", "RefTitle": "Ship-to OIFWE in CO-PA not filled for billing trsf.", "RefUrl": "/notes/197770"}, {"RefNumber": "197769", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Statistics update for value items", "RefUrl": "/notes/197769"}, {"RefNumber": "197738", "RefComponent": "IS-OIL-DS-TDP", "RefTitle": "Brazil: Tax code customizing table changes", "RefUrl": "/notes/197738"}, {"RefNumber": "197470", "RefComponent": "IS-OIL-DS-BDRP", "RefTitle": "Consignment stock fields missing after 4.0B SP1", "RefUrl": "/notes/197470"}, {"RefNumber": "197466", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "Message ME218 Net Price has to be greater than 0", "RefUrl": "/notes/197466"}, {"RefNumber": "197182", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Quantity missing from OILSHI01 if CPLID not '1'", "RefUrl": "/notes/197182"}, {"RefNumber": "196890", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "Ship-to information not pulled into contracts", "RefUrl": "/notes/196890"}, {"RefNumber": "196306", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "Error handling in TAS FM IDOC_INPUT_OILLDD", "RefUrl": "/notes/196306"}, {"RefNumber": "196075", "RefComponent": "IS-OIL-DS", "RefTitle": "Oil-specific messages missing in class VF", "RefUrl": "/notes/196075"}, {"RefNumber": "196054", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/196054"}, {"RefNumber": "196052", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Error OC605 occurs incorrectly", "RefUrl": "/notes/196052"}, {"RefNumber": "195707", "RefComponent": "IS-OIL-DS", "RefTitle": "Slow response to delivery create", "RefUrl": "/notes/195707"}, {"RefNumber": "195018", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "TD Underdelivery tolerance 0% not working", "RefUrl": "/notes/195018"}, {"RefNumber": "195009", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Goods receipt with fees into neg. stock errors", "RefUrl": "/notes/195009"}, {"RefNumber": "194642", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Not possible to post LIA for 0 valued material", "RefUrl": "/notes/194642"}, {"RefNumber": "194512", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Load balancing:qty difference in GR & goods issue documents", "RefUrl": "/notes/194512"}, {"RefNumber": "194386", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "TD Archiving DBIF_RSQL_INVALID_CURSOR", "RefUrl": "/notes/194386"}, {"RefNumber": "194327", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Incorrect OIK37 updates via TPI", "RefUrl": "/notes/194327"}, {"RefNumber": "193968", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "O4F1 : Document Weight not assigned to Shipment.", "RefUrl": "/notes/193968"}, {"RefNumber": "193770", "RefComponent": "IS-OIL-DS-HPM", "RefTitle": "IS-OIL: Oil BoM header cannot be deleted", "RefUrl": "/notes/193770"}, {"RefNumber": "193619", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Exchange number missing in netting index table", "RefUrl": "/notes/193619"}, {"RefNumber": "193559", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "TD Rounding Errors in OIGI_CREATE_SHIPMENT_RFC fix", "RefUrl": "/notes/193559"}, {"RefNumber": "193407", "RefComponent": "IS-OIL-DS-BDRP", "RefTitle": "OII_ARCHIVE_CHECK_CUST_SUBOBJS not found release 31H", "RefUrl": "/notes/193407"}, {"RefNumber": "193381", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Netting does not generate a BTCI session", "RefUrl": "/notes/193381"}, {"RefNumber": "193231", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "ROIKFCL1 functionality missing after Service Pack", "RefUrl": "/notes/193231"}, {"RefNumber": "193229", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Use cust.exit on KNA1 change pointer for updates to SCP OTWS", "RefUrl": "/notes/193229"}, {"RefNumber": "193227", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "Abort A222 in OIK_TAS_DATA_POST", "RefUrl": "/notes/193227"}, {"RefNumber": "193225", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "F&A Time UoM routine 004,005 Year Begnining Problem", "RefUrl": "/notes/193225"}, {"RefNumber": "193194", "RefComponent": "IS-OIL-DS-TDP", "RefTitle": "IS-OIL: Goods Receipt Repricing / Excise Duty posting", "RefUrl": "/notes/193194"}, {"RefNumber": "193130", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "TPI: Trip planning", "RefUrl": "/notes/193130"}, {"RefNumber": "193023", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Handling of ETAs for planned shipments", "RefUrl": "/notes/193023"}, {"RefNumber": "193001", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "LIA accounting document missing", "RefUrl": "/notes/193001"}, {"RefNumber": "192527", "RefComponent": "IS-OIL-DS-BDRP", "RefTitle": "No reference to EXIT_SAPLOIIQ_001", "RefUrl": "/notes/192527"}, {"RefNumber": "192520", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Contracts assigned to an exchange agreement", "RefUrl": "/notes/192520"}, {"RefNumber": "192304", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "TD Rounding Errors in OIGI_CREATE_SHIPMENT_RFC", "RefUrl": "/notes/192304"}, {"RefNumber": "192273", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Collected changes for TPI", "RefUrl": "/notes/192273"}, {"RefNumber": "192046", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "No KNA1 change pointer for updates to SCP OTWS", "RefUrl": "/notes/192046"}, {"RefNumber": "192014", "RefComponent": "IS-OIL-DS-BDRP", "RefTitle": "Error OC709 occurs on TPI OTWS entry", "RefUrl": "/notes/192014"}, {"RefNumber": "191923", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "Incorrect statistical conditions in differential invoice", "RefUrl": "/notes/191923"}, {"RefNumber": "191705", "RefComponent": "IS-OIL-DS-TDP", "RefTitle": "TAX REVALUATION WITH MULTIPLE VALUATIONS AND TRANSIT-STOCK", "RefUrl": "/notes/191705"}, {"RefNumber": "191696", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "OILSHI01 IDoc keeps status 64 after processing", "RefUrl": "/notes/191696"}, {"RefNumber": "191432", "RefComponent": "IS-OIL-DS-BDRP", "RefTitle": "Tank ID assignment at order change incorrect", "RefUrl": "/notes/191432"}, {"RefNumber": "191429", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "PBL OTWS entries missing from TPI Location IDoc", "RefUrl": "/notes/191429"}, {"RefNumber": "191413", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Receipts are missing in movement based netting", "RefUrl": "/notes/191413"}, {"RefNumber": "191401", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Action code in OILTPI50 populated incorrectly", "RefUrl": "/notes/191401"}, {"RefNumber": "191154", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "TPI IDocs fail with mixed user defaults", "RefUrl": "/notes/191154"}, {"RefNumber": "191151", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Missing TPI functionality", "RefUrl": "/notes/191151"}, {"RefNumber": "191149", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Abort A222 in OIK_TAS_DATA_POST", "RefUrl": "/notes/191149"}, {"RefNumber": "190248", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Generic price reference plant valuation", "RefUrl": "/notes/190248"}, {"RefNumber": "189648", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Tank ID not available on Shipment Outbound IDocs", "RefUrl": "/notes/189648"}, {"RefNumber": "189505", "RefComponent": "IS-OIL-DS-TDP", "RefTitle": "Handl.type not def. on change of plant or mat.at material", "RefUrl": "/notes/189505"}, {"RefNumber": "189441", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Performance in billing / unexpected invoice split", "RefUrl": "/notes/189441"}, {"RefNumber": "189339", "RefComponent": "IS-OIL-DS-MRN", "RefTitle": "ORA 1562 on OIFPBL in program RVV05IVB", "RefUrl": "/notes/189339"}, {"RefNumber": "188842", "RefComponent": "MM-IM-GF", "RefTitle": "MB03: display external details fails (WA OIDE A)", "RefUrl": "/notes/188842"}, {"RefNumber": "188640", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Contract update terminates if no fee exists", "RefUrl": "/notes/188640"}, {"RefNumber": "188270", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Fees for goods receipt missing in fee history", "RefUrl": "/notes/188270"}, {"RefNumber": "187744", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "TRUOM; ELIKZandLoss; checkMM-LVORM; Missing GI; SetTDaction", "RefUrl": "/notes/187744"}, {"RefNumber": "187195", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Field overflow in LIA transaction", "RefUrl": "/notes/187195"}, {"RefNumber": "187013", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Base Location not consistent between Sales and Purchase", "RefUrl": "/notes/187013"}, {"RefNumber": "186505", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/186505"}, {"RefNumber": "186491", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "Search help shows only shipment cost relevant shpm.", "RefUrl": "/notes/186491"}, {"RefNumber": "186408", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "Parallel OILLDD inbound processing", "RefUrl": "/notes/186408"}, {"RefNumber": "186341", "RefComponent": "IS-OIL-DS-TSW", "RefTitle": "Max inventory/Min inventory related WL entries", "RefUrl": "/notes/186341"}, {"RefNumber": "186151", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Shipment inbound process update task", "RefUrl": "/notes/186151"}, {"RefNumber": "185895", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Exchange statement: UoM of F&A condition missing", "RefUrl": "/notes/185895"}, {"RefNumber": "185685", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Bad performance/Short dump for ROIAMMA3", "RefUrl": "/notes/185685"}, {"RefNumber": "185245", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Custmr mastr recrd:Distrbtn of deltd partnr funct.", "RefUrl": "/notes/185245"}, {"RefNumber": "185231", "RefComponent": "IS-OIL-DS-HPM", "RefTitle": "IS-OIL: Incorrect delivered quantity in schedule lines", "RefUrl": "/notes/185231"}, {"RefNumber": "185062", "RefComponent": "IS-OIL-DS-TSW", "RefTitle": "Unexpected Error-messages after Planning Engine run", "RefUrl": "/notes/185062"}, {"RefNumber": "185010", "RefComponent": "IS-OIL-DS", "RefTitle": "MM_EKKO: archiving run excludes contracts", "RefUrl": "/notes/185010"}, {"RefNumber": "184640", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Fees by PO picks up previous years GR", "RefUrl": "/notes/184640"}, {"RefNumber": "184532", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "Error mess V1227 New pric/ formulae on MM side", "RefUrl": "/notes/184532"}, {"RefNumber": "184295", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Abort message OE 559 in Order Create", "RefUrl": "/notes/184295"}, {"RefNumber": "184259", "RefComponent": "IS-OIL-DS-HPM", "RefTitle": "IS-OIL: Incorrect qty.for batch split main item", "RefUrl": "/notes/184259"}, {"RefNumber": "184209", "RefComponent": "IS-OIL-DS-TSW", "RefTitle": "TSW: Missing R/3 Documents in Rundown possible", "RefUrl": "/notes/184209"}, {"RefNumber": "184148", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "SOEK000331: Wrong conversion between sales and base UoM", "RefUrl": "/notes/184148"}, {"RefNumber": "184012", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Movement based netting shows wrong sign", "RefUrl": "/notes/184012"}, {"RefNumber": "183761", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "No fee redetermination in MM contract or order", "RefUrl": "/notes/183761"}, {"RefNumber": "183374", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Timeframe function missing on screen 8024", "RefUrl": "/notes/183374"}, {"RefNumber": "183036", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "Message OE 224: STSI calloff can not be deleted manually", "RefUrl": "/notes/183036"}, {"RefNumber": "181955", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Exchange statement for cancelled invoice", "RefUrl": "/notes/181955"}, {"RefNumber": "181711", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "Wrong data initialisation in OIK_TD_SHIPMENT_LOAD_PREPARE", "RefUrl": "/notes/181711"}, {"RefNumber": "181652", "RefComponent": "IS-OIL-DS", "RefTitle": "IS-OIL Application Test 4.0B", "RefUrl": "/notes/181652"}, {"RefNumber": "181530", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Purchase assignment: pass goods issue date", "RefUrl": "/notes/181530"}, {"RefNumber": "181457", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "Load ID assigned with Valid-to = 00:00:0000", "RefUrl": "/notes/181457"}, {"RefNumber": "181437", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Language conversion incorrect for menu option", "RefUrl": "/notes/181437"}, {"RefNumber": "181409", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Negative fees incorrect in movement based netting", "RefUrl": "/notes/181409"}, {"RefNumber": "180596", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "Sales tax incorrect on differential invoice creation", "RefUrl": "/notes/180596"}, {"RefNumber": "180083", "RefComponent": "IS-OIL-BC", "RefTitle": "IS-OIL: Additional Info - SPs 21-28 incl. 4.0B", "RefUrl": "/notes/180083"}, {"RefNumber": "179868", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "OTWS data not available in views v_oiiotwbl, v_oiiotwkn", "RefUrl": "/notes/179868"}, {"RefNumber": "179813", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "Sales UOM conversion wrong in MC for condition record", "RefUrl": "/notes/179813"}, {"RefNumber": "179653", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Short dump in invoice list processing", "RefUrl": "/notes/179653"}, {"RefNumber": "179557", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "Pricing date> sy-datum, quotation error routine not trigger.", "RefUrl": "/notes/179557"}, {"RefNumber": "178164", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "Screen 200 missing in program", "RefUrl": "/notes/178164"}, {"RefNumber": "178046", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Movement based netting issues", "RefUrl": "/notes/178046"}, {"RefNumber": "177949", "RefComponent": "IS-OIL-DS-TDP", "RefTitle": "Taxes Brail Oil: New exception  tables sales,puchase,t-fer", "RefUrl": "/notes/177949"}, {"RefNumber": "177866", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "Index(VKDFS) is upd. incorr. when cancel diff. invoice", "RefUrl": "/notes/177866"}, {"RefNumber": "177825", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Order distribution action code", "RefUrl": "/notes/177825"}, {"RefNumber": "177563", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Save tank for delivery on shipment inbound", "RefUrl": "/notes/177563"}, {"RefNumber": "177556", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "change communication structure for delete IDoc processing", "RefUrl": "/notes/177556"}, {"RefNumber": "177535", "RefComponent": "IS-OIL-DS-BDRP", "RefTitle": "Cannot cancel from Plant SCP sub-screen", "RefUrl": "/notes/177535"}, {"RefNumber": "176988", "RefComponent": "IS-OIL-DS-TSW", "RefTitle": "Update termination in worklist engine", "RefUrl": "/notes/176988"}, {"RefNumber": "176909", "RefComponent": "IS-OIL-DS-MRN", "RefTitle": "OIPBL missing during sales order call-off", "RefUrl": "/notes/176909"}, {"RefNumber": "176858", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "Differential invoice creation in batch processing", "RefUrl": "/notes/176858"}, {"RefNumber": "176785", "RefComponent": "IS-OIL-DS-BDRP", "RefTitle": "Missing SCP component check", "RefUrl": "/notes/176785"}, {"RefNumber": "176741", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Return data from user screen overwritten (OIGD)", "RefUrl": "/notes/176741"}, {"RefNumber": "176647", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "Dens.of chem.prod.;Round.HPM;Veh. recon.;O4G1-Err Msg:V0104", "RefUrl": "/notes/176647"}, {"RefNumber": "176589", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "PBL lookup for TPS parameters fails for OILSH01", "RefUrl": "/notes/176589"}, {"RefNumber": "176108", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "LOAD_NO_SPACE_FOR_TABLE for RBDAPP01 logical msg OILLDD", "RefUrl": "/notes/176108"}, {"RefNumber": "175799", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Correction Trip planning", "RefUrl": "/notes/175799"}, {"RefNumber": "175501", "RefComponent": "IS-OIL-DS", "RefTitle": "IS-OIL: Customer master ALE / KNVV records overwritten", "RefUrl": "/notes/175501"}, {"RefNumber": "175494", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Customer Subscreen not accessible in SAPMOIGV", "RefUrl": "/notes/175494"}, {"RefNumber": "175463", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Wrong quantities in CO-PA after cancelling invoice", "RefUrl": "/notes/175463"}, {"RefNumber": "175342", "RefComponent": "IS-OIL-DS-TDP", "RefTitle": "Brazil taxes: Error on Sales return - ROB", "RefUrl": "/notes/175342"}, {"RefNumber": "175140", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "Abend in batch billing", "RefUrl": "/notes/175140"}, {"RefNumber": "175059", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "Post parked documents: message 00348", "RefUrl": "/notes/175059"}, {"RefNumber": "175037", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Lia account determination with valuation class", "RefUrl": "/notes/175037"}, {"RefNumber": "175029", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/175029"}, {"RefNumber": "174746", "RefComponent": "IS-OIL-DS-HPM", "RefTitle": "IS-OIL: Qty conversion error when using product proposal", "RefUrl": "/notes/174746"}, {"RefNumber": "174656", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "IS-OIL: Zero qty in PO history of EXG assigned PO", "RefUrl": "/notes/174656"}, {"RefNumber": "174437", "RefComponent": "IS-OIL-DS-TDP", "RefTitle": "TDP VALUE CORRECTION WHEN CANCEL AN PO INVOICE", "RefUrl": "/notes/174437"}, {"RefNumber": "173720", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "TD Tables are not archived with archiving object OIG_SHPMNT", "RefUrl": "/notes/173720"}, {"RefNumber": "173715", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "TD customer function for vehicle overload checks", "RefUrl": "/notes/173715"}, {"RefNumber": "173532", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "Order/Contr.: TAS data not filled after delete/cre. item", "RefUrl": "/notes/173532"}, {"RefNumber": "173484", "RefComponent": "IS-OIL-DS-BDRP", "RefTitle": "SOC Assignment in SD document not working", "RefUrl": "/notes/173484"}, {"RefNumber": "173205", "RefComponent": "IS-OIL-DS-MCO", "RefTitle": "Selected BOL is not moved into screen field", "RefUrl": "/notes/173205"}, {"RefNumber": "173139", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "TPI: Fix for Time frame settings handling", "RefUrl": "/notes/173139"}, {"RefNumber": "173130", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/173130"}, {"RefNumber": "173079", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "Zero billing date is filled in the VKDFS table", "RefUrl": "/notes/173079"}, {"RefNumber": "172759", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "output determination: header or detail(scheduling)", "RefUrl": "/notes/172759"}, {"RefNumber": "172738", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Functionality for Time frame settings at IMG", "RefUrl": "/notes/172738"}, {"RefNumber": "172681", "RefComponent": "IS-OIL-DS-TSW", "RefTitle": "TSW : correction for marine transport system in Nomination", "RefUrl": "/notes/172681"}, {"RefNumber": "172527", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "ASTM Calculation doesn't work correctly with ASTM Interface", "RefUrl": "/notes/172527"}, {"RefNumber": "172412", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "Confirm status; Error message E752; Gain/loss mvmt o b", "RefUrl": "/notes/172412"}, {"RefNumber": "172212", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "TD compartment user screens: Selected compartments lost", "RefUrl": "/notes/172212"}, {"RefNumber": "172063", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Mb netting: Wrong invoice cycle determined", "RefUrl": "/notes/172063"}, {"RefNumber": "172009", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "Delete shipment + Nast entry", "RefUrl": "/notes/172009"}, {"RefNumber": "171871", "RefComponent": "IS-OIL-DS-BDRP", "RefTitle": "IS-Oil BDRP Sales Hours not read", "RefUrl": "/notes/171871"}, {"RefNumber": "171736", "RefComponent": "IS-OIL-DS-TDP", "RefTitle": "Nota fiscal with multiple items / Brazil Oil", "RefUrl": "/notes/171736"}, {"RefNumber": "171391", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/171391"}, {"RefNumber": "171364", "RefComponent": "IS-OIL-DS-HPM", "RefTitle": "IS-OIL: Incorrect Qty in base UoM in Inv.verification", "RefUrl": "/notes/171364"}, {"RefNumber": "171086", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "TPI: Collective corrections after Acceptance test", "RefUrl": "/notes/171086"}, {"RefNumber": "171065", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "ITA: Collective corrections after Acceptance test", "RefUrl": "/notes/171065"}, {"RefNumber": "171053", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "Posting dates in one period; Zero lines in loading", "RefUrl": "/notes/171053"}, {"RefNumber": "171037", "RefComponent": "IS-OIL-DS-TDP", "RefTitle": "Billing cancellation error.", "RefUrl": "/notes/171037"}, {"RefNumber": "170968", "RefComponent": "IS-OIL-DS-MCO", "RefTitle": "Fieldsselection texts missing in RM07MMAT", "RefUrl": "/notes/170968"}, {"RefNumber": "170926", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "OIK37 number ranges maintenance", "RefUrl": "/notes/170926"}, {"RefNumber": "170871", "RefComponent": "IS-OIL-DS-TDP", "RefTitle": "IS-OIL BRAZILIAN TAX SOLUTION FOR TRANSFER 833/835", "RefUrl": "/notes/170871"}, {"RefNumber": "170822", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "ITA: Missing copy of some client independent object items", "RefUrl": "/notes/170822"}, {"RefNumber": "170803", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "ITA: Outbound IDoc OILORD02: E1EDP01-ANTLF not filled", "RefUrl": "/notes/170803"}, {"RefNumber": "170668", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Bills of material in exchanges", "RefUrl": "/notes/170668"}, {"RefNumber": "170620", "RefComponent": "IS-OIL-DS-HPM", "RefTitle": "IS-OIL: EXG fee UoM missing in HPM quantity conversion", "RefUrl": "/notes/170620"}, {"RefNumber": "170314", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Error in redetermination of netting statement", "RefUrl": "/notes/170314"}, {"RefNumber": "170287", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "Rounding errors in invoice verification", "RefUrl": "/notes/170287"}, {"RefNumber": "170066", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Fixed value fees are incorrect in the invoice", "RefUrl": "/notes/170066"}, {"RefNumber": "169833", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "MAP: Error in Pricing date during GR repricing", "RefUrl": "/notes/169833"}, {"RefNumber": "169295", "RefComponent": "IS-OIL-DS-HPM", "RefTitle": "IS-OIL: Wrong pick qty. after TRX VL16", "RefUrl": "/notes/169295"}, {"RefNumber": "169283", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "ITA: TAS index for GI not deleted if delivery deleted", "RefUrl": "/notes/169283"}, {"RefNumber": "169228", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "ITA: Error during DELETE OIKLIDR", "RefUrl": "/notes/169228"}, {"RefNumber": "169160", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "TD Corrections in SVPs: 3.1H/SVP3 4.0B/SVP1", "RefUrl": "/notes/169160"}, {"RefNumber": "168765", "RefComponent": "IS-OIL-DS-HPM", "RefTitle": "IS-OIL: Fields not updated for oil BoM header", "RefUrl": "/notes/168765"}, {"RefNumber": "168381", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Billing index entries missing after RVV05IVB", "RefUrl": "/notes/168381"}, {"RefNumber": "168313", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Customizing control for message O1 023", "RefUrl": "/notes/168313"}, {"RefNumber": "167025", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "Dynamic F&A repository selection screen", "RefUrl": "/notes/167025"}, {"RefNumber": "166548", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Exg. statement: reversal printed in wrong period", "RefUrl": "/notes/166548"}, {"RefNumber": "166169", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Fees lost during cancellation of billing document", "RefUrl": "/notes/166169"}, {"RefNumber": "165871", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "TD Auto compartment planning with compatibilities", "RefUrl": "/notes/165871"}, {"RefNumber": "164252", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "TD Delete NAST entries for delete shipments", "RefUrl": "/notes/164252"}, {"RefNumber": "163448", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Quantity schedule: Conversion error", "RefUrl": "/notes/163448"}, {"RefNumber": "160349", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/160349"}, {"RefNumber": "158344", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "TD: Gain on 2-step trf. and GR to mult. store locations", "RefUrl": "/notes/158344"}, {"RefNumber": "157975", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Not possible to maintain posting keys NTB,NTK...", "RefUrl": "/notes/157975"}, {"RefNumber": "157801", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "TD Loss in transfer/STO and change of valuation type", "RefUrl": "/notes/157801"}, {"RefNumber": "157768", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "Incorrect subtotal calculation type with F&A condn", "RefUrl": "/notes/157768"}, {"RefNumber": "157569", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "Errors in repricing in invoice verification", "RefUrl": "/notes/157569"}, {"RefNumber": "157562", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/157562"}, {"RefNumber": "157494", "RefComponent": "IS-OIL-DS-MRN", "RefTitle": "Location ID in Credit note and Credit note request", "RefUrl": "/notes/157494"}, {"RefNumber": "157044", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "TD Exit from step-loop 00043 in scheduling", "RefUrl": "/notes/157044"}, {"RefNumber": "156993", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "TD User Exit 007 before creation of MM document", "RefUrl": "/notes/156993"}, {"RefNumber": "156906", "RefComponent": "IS-OIL-DS-MRN", "RefTitle": "Abort on Location Archiving Deletion program", "RefUrl": "/notes/156906"}, {"RefNumber": "156700", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Wrong UoM in automatically created purchase order", "RefUrl": "/notes/156700"}, {"RefNumber": "156697", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "If taxes are 0,accounting error in differential Inv", "RefUrl": "/notes/156697"}, {"RefNumber": "156599", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "TD Document item quantity assign. missing O9540", "RefUrl": "/notes/156599"}, {"RefNumber": "156403", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "Bug in routine XKOMV_BEWERTEN", "RefUrl": "/notes/156403"}, {"RefNumber": "156237", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "TD 'Delete event' in maintain completed shipment", "RefUrl": "/notes/156237"}, {"RefNumber": "155914", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Printing exchange statements using RSNAST00", "RefUrl": "/notes/155914"}, {"RefNumber": "155337", "RefComponent": "IS-OIL-DS", "RefTitle": "IS-Oil: Material Master Screen Seqence Selection", "RefUrl": "/notes/155337"}, {"RefNumber": "154841", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "TD temperature UoM in popup/multiple MM docs per transaction", "RefUrl": "/notes/154841"}, {"RefNumber": "154790", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "TD Allow non-movement delivery items in TD shipments", "RefUrl": "/notes/154790"}, {"RefNumber": "154538", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "Error in holiday determination in F&A pricing", "RefUrl": "/notes/154538"}, {"RefNumber": "154200", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Bad runtime while MSEG-access in quantity schedule", "RefUrl": "/notes/154200"}, {"RefNumber": "153222", "RefComponent": "IS-OIL-DS-MCO", "RefTitle": "external details at stock transfer order", "RefUrl": "/notes/153222"}, {"RefNumber": "153121", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "TD Scrolling SAPMOIGS3700; take over dates after user exit", "RefUrl": "/notes/153121"}, {"RefNumber": "152777", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/152777"}, {"RefNumber": "152678", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "TD Cost sharing on document item level", "RefUrl": "/notes/152678"}, {"RefNumber": "151721", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Fee edit/copy control fields are missing", "RefUrl": "/notes/151721"}, {"RefNumber": "151361", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Missing exg. number on billing index for diff. inv.", "RefUrl": "/notes/151361"}, {"RefNumber": "150951", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "TD Missing overload check in compartment allocation details", "RefUrl": "/notes/150951"}, {"RefNumber": "150548", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "TD Errors in IMG for shipment costing", "RefUrl": "/notes/150548"}, {"RefNumber": "150318", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Split excise duty may cause wrong IR postings", "RefUrl": "/notes/150318"}, {"RefNumber": "150132", "RefComponent": "IS-OIL-DS-HPM", "RefTitle": "IS-OIL: credit management: value update problem", "RefUrl": "/notes/150132"}, {"RefNumber": "149943", "RefComponent": "IS-OIL-BC", "RefTitle": "IS-OIL: Additional Info - Support Package 11, 4.0B", "RefUrl": "/notes/149943"}, {"RefNumber": "147813", "RefComponent": "IS-OIL-DS-MCO", "RefTitle": "ME31: external details not copied", "RefUrl": "/notes/147813"}, {"RefNumber": "146147", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "MAP-Customer price list does not work for item lev.", "RefUrl": "/notes/146147"}, {"RefNumber": "145854", "RefComponent": "IS-OIL-BC", "RefTitle": "Order of IS-OIL notes 4.0b on basis R/3 4.0B (LCP)", "RefUrl": "/notes/145854"}, {"RefNumber": "143644", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "Error with UoM processing in TD Vehicle creation", "RefUrl": "/notes/143644"}, {"RefNumber": "143233", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "TD missing gain; add tracking; ASTM +/- qty; no OIGSM", "RefUrl": "/notes/143233"}, {"RefNumber": "142981", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "TD ASTM and 0 qty; reason code; excise duty value", "RefUrl": "/notes/142981"}, {"RefNumber": "142387", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "TD Change proposal at load/delivery confirmation", "RefUrl": "/notes/142387"}, {"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719"}, {"RefNumber": "131501", "RefComponent": "TR-TM-IS", "RefTitle": "Termination with drilldown report call (hierarchy)", "RefUrl": "/notes/131501"}, {"RefNumber": "111268", "RefComponent": "PP-REM-ADE", "RefTitle": "MF4A, MF4U: Display material documents, return", "RefUrl": "/notes/111268"}, {"RefNumber": "107344", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "TD partner-specific output determination", "RefUrl": "/notes/107344"}, {"RefNumber": "102306", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Wrong values in statistics update", "RefUrl": "/notes/102306"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "396242", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "Dump in TD bulk shp in OIB2_TD_GET_MAT_TEMP by NO_RECORD exc", "RefUrl": "/notes/396242 "}, {"RefNumber": "522365", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "TAS incompletion log appears for a non TAS relevant order", "RefUrl": "/notes/522365 "}, {"RefNumber": "181652", "RefComponent": "IS-OIL-DS", "RefTitle": "IS-OIL Application Test 4.0B", "RefUrl": "/notes/181652 "}, {"RefNumber": "314778", "RefComponent": "IS-OIL-DS-QCI", "RefTitle": "IS OIL: QCI: Chemicals and TAS; BSW conversion; BAdI methods", "RefUrl": "/notes/314778 "}, {"RefNumber": "336419", "RefComponent": "IS-OIL-DS-TDP", "RefTitle": "VT cannot be changed after default from contract to order", "RefUrl": "/notes/336419 "}, {"RefNumber": "421494", "RefComponent": "IS-OIL", "RefTitle": "Alternative origin validation for purchase order", "RefUrl": "/notes/421494 "}, {"RefNumber": "400863", "RefComponent": "IS-OIL", "RefTitle": "OIL: Problems deleting E&P Hierarchies", "RefUrl": "/notes/400863 "}, {"RefNumber": "458906", "RefComponent": "IS-OIL-BC", "RefTitle": "IS-OIL: Additional Info - SPs 66-68 incl. 4.0B", "RefUrl": "/notes/458906 "}, {"RefNumber": "434924", "RefComponent": "IS-OIL-BC", "RefTitle": "IS-OIL: Additional Info - SPs 63-65 incl. 4.0B", "RefUrl": "/notes/434924 "}, {"RefNumber": "210204", "RefComponent": "IS-OIL-BC", "RefTitle": "IS-OIL: Additional Info - LCP 31-38 incl. 4.0B", "RefUrl": "/notes/210204 "}, {"RefNumber": "351482", "RefComponent": "IS-OIL-BC", "RefTitle": "IS-OIL: Additional Info - LCP 45-53 incl. 4.0B", "RefUrl": "/notes/351482 "}, {"RefNumber": "448020", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "Double update of IDOCS in parallel inbound & Idoc locking", "RefUrl": "/notes/448020 "}, {"RefNumber": "364644", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Shipment change on TPI planning screen deletes carrier", "RefUrl": "/notes/364644 "}, {"RefNumber": "357343", "RefComponent": "IS-OIL-DS-QCI", "RefTitle": "Session locked by qty conversion for 0 density/rel. density", "RefUrl": "/notes/357343 "}, {"RefNumber": "397003", "RefComponent": "IS-OIL-DS-QCI", "RefTitle": "IS OIL: negative quantity entry in HPM dialog box", "RefUrl": "/notes/397003 "}, {"RefNumber": "804981", "RefComponent": "BC-UPG-ADDON", "RefTitle": "IS-OIL: Additional Info - SP 84 incl. 4.0B", "RefUrl": "/notes/804981 "}, {"RefNumber": "560213", "RefComponent": "BC-UPG-ADDON", "RefTitle": "IS-OIL: Additional Info - SPs 76-79 incl. 4.0B", "RefUrl": "/notes/560213 "}, {"RefNumber": "530459", "RefComponent": "IS-OIL-BC", "RefTitle": "IS-OIL: Additional Info - SPs 73-75 incl. 4.0B", "RefUrl": "/notes/530459 "}, {"RefNumber": "375158", "RefComponent": "BC-UPG-ADDON", "RefTitle": "IS-OIL: Additional Info - SPs 54-56 incl. 4.0B", "RefUrl": "/notes/375158 "}, {"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719 "}, {"RefNumber": "501808", "RefComponent": "IS-OIL-BC", "RefTitle": "IS-OIL: Additional Info - SPs 69-72 incl. 4.0B", "RefUrl": "/notes/501808 "}, {"RefNumber": "514995", "RefComponent": "IS-OIL-DS-TSW", "RefTitle": "Insert line on nomination deletes the header material", "RefUrl": "/notes/514995 "}, {"RefNumber": "515099", "RefComponent": "IS-OIL-DS-TSW", "RefTitle": "O4NC: header material disappeared after entering", "RefUrl": "/notes/515099 "}, {"RefNumber": "332654", "RefComponent": "IS-OIL-BC", "RefTitle": "IS-OIL / IS-MINE / IS-CWM: Overview of SAP Notes", "RefUrl": "/notes/332654 "}, {"RefNumber": "519184", "RefComponent": "IS-OIL-DS-HPM", "RefTitle": "IS-Oil:MR03 Qty is 0 if more than 1 line item on the invoice", "RefUrl": "/notes/519184 "}, {"RefNumber": "322765", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "BOM comp.qty;rebr. batch PtL;check batch STO;scroll on 3700", "RefUrl": "/notes/322765 "}, {"RefNumber": "86241", "RefComponent": "PY", "RefTitle": "Legal Change Patches / Support Packages for HR", "RefUrl": "/notes/86241 "}, {"RefNumber": "323761", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Problem with exchange number when creating sales contracts", "RefUrl": "/notes/323761 "}, {"RefNumber": "397831", "RefComponent": "IS-OIL-BC", "RefTitle": "IS-OIL: Additional Info - SPs 57-60 incl. 4.0B", "RefUrl": "/notes/397831 "}, {"RefNumber": "421873", "RefComponent": "IS-OIL-DS-HPM", "RefTitle": "Delivery weight & volume not updated properly in delivery", "RefUrl": "/notes/421873 "}, {"RefNumber": "327364", "RefComponent": "IS-OIL-DS-HPM", "RefTitle": "IS-Oil: Incorrect quantity in SIS info structures", "RefUrl": "/notes/327364 "}, {"RefNumber": "640354", "RefComponent": "IS-OIL-BC", "RefTitle": "IS-OIL: Additional Info - SPs 80-81 incl. 4.0B", "RefUrl": "/notes/640354 "}, {"RefNumber": "53136", "RefComponent": "IS-OIL-BC", "RefTitle": "Support Packages and IS-Oil / IS-MINE / IS-CWM - information", "RefUrl": "/notes/53136 "}, {"RefNumber": "77407", "RefComponent": "IS-OIL-BC", "RefTitle": "CRTs for IS-Oil", "RefUrl": "/notes/77407 "}, {"RefNumber": "93571", "RefComponent": "IS-OIL", "RefTitle": "Sequence of corrections - IS-Oil / IS-MINE Policy", "RefUrl": "/notes/93571 "}, {"RefNumber": "145854", "RefComponent": "IS-OIL-BC", "RefTitle": "Order of IS-OIL notes 4.0b on basis R/3 4.0B (LCP)", "RefUrl": "/notes/145854 "}, {"RefNumber": "421035", "RefComponent": "IS-OIL-DS-HPM", "RefTitle": "Rollback while deleting archived stock entries", "RefUrl": "/notes/421035 "}, {"RefNumber": "510529", "RefComponent": "IS-OIL-DS-MCO", "RefTitle": "To update VBKD-OIT<PERSON>LE when VBKD-INCO1 is changed.", "RefUrl": "/notes/510529 "}, {"RefNumber": "508909", "RefComponent": "IS-OIL-DS-MCO", "RefTitle": "External details fields not getting updated in LIKP", "RefUrl": "/notes/508909 "}, {"RefNumber": "390503", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "Billing docs with zero qty for already billed SD documents.", "RefUrl": "/notes/390503 "}, {"RefNumber": "454007", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "Group conditions and other cond cumulated value with 431", "RefUrl": "/notes/454007 "}, {"RefNumber": "487370", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "Short dump at O4G1-Balance Loading after loading correction", "RefUrl": "/notes/487370 "}, {"RefNumber": "67261", "RefComponent": "IS-OIL-DS-HPM", "RefTitle": "Reports to analyze and correct stock qties in IS-OIL systems", "RefUrl": "/notes/67261 "}, {"RefNumber": "443421", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "Archiving of shipments", "RefUrl": "/notes/443421 "}, {"RefNumber": "47531", "RefComponent": "IS-OIL", "RefTitle": "IS-OIL / IS-MINE / IS-CWM correction guideline", "RefUrl": "/notes/47531 "}, {"RefNumber": "395874", "RefComponent": "IS-OIL-DS-TDP", "RefTitle": "IS-OIL LOCALIZATION BRAZIL ICMS COMPLEMENT POSTING", "RefUrl": "/notes/395874 "}, {"RefNumber": "527924", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "Customizing the message type for TD message O9(747)", "RefUrl": "/notes/527924 "}, {"RefNumber": "519259", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "Customizing the message type for TD message class O9(078).", "RefUrl": "/notes/519259 "}, {"RefNumber": "184259", "RefComponent": "IS-OIL-DS-HPM", "RefTitle": "IS-OIL: Incorrect qty.for batch split main item", "RefUrl": "/notes/184259 "}, {"RefNumber": "427899", "RefComponent": "IS-OIL-DS-HPM", "RefTitle": "Wrong qty conversion in billing condition", "RefUrl": "/notes/427899 "}, {"RefNumber": "416839", "RefComponent": "IS-OIL-DS-HPM", "RefTitle": "Performance improvement loading and delivery confirmation", "RefUrl": "/notes/416839 "}, {"RefNumber": "317019", "RefComponent": "IS-OIL-DS-MCO", "RefTitle": "External details not copied to batch split items", "RefUrl": "/notes/317019 "}, {"RefNumber": "504601", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "Vehicle status change from 6 to 5 in O4L4.", "RefUrl": "/notes/504601 "}, {"RefNumber": "458705", "RefComponent": "IS-OIL-DS-TDP", "RefTitle": "IS-OIL: VPRS Re-Determination when non TDP material", "RefUrl": "/notes/458705 "}, {"RefNumber": "504889", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Wrong delivery costs proposed for invoice verification", "RefUrl": "/notes/504889 "}, {"RefNumber": "312369", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Long runtimes when archiving with FI_DOCUMNT", "RefUrl": "/notes/312369 "}, {"RefNumber": "322879", "RefComponent": "IS-OIL-BC", "RefTitle": "SAPMM07M,..: Too many DATA control blocks / generation error", "RefUrl": "/notes/322879 "}, {"RefNumber": "424163", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Cancel goods issue: no entry in OIAQB created", "RefUrl": "/notes/424163 "}, {"RefNumber": "300629", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Unnecessary messages in Tank defaulting", "RefUrl": "/notes/300629 "}, {"RefNumber": "440799", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Purchase orders without any costs can't be processed via ERS", "RefUrl": "/notes/440799 "}, {"RefNumber": "400071", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Corrections to OIK_CHANGE_ORDER", "RefUrl": "/notes/400071 "}, {"RefNumber": "156237", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "TD 'Delete event' in maintain completed shipment", "RefUrl": "/notes/156237 "}, {"RefNumber": "411898", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "Cancel credit memo raises message", "RefUrl": "/notes/411898 "}, {"RefNumber": "525712", "RefComponent": "IS-OIL-DS-MCO", "RefTitle": "External Details Button rises error M7 001", "RefUrl": "/notes/525712 "}, {"RefNumber": "494571", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "F&A tables not adjusted properly for multiple F&A conditions", "RefUrl": "/notes/494571 "}, {"RefNumber": "403275", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Shortdump when posting netting document with many items", "RefUrl": "/notes/403275 "}, {"RefNumber": "381741", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "Mtr rdg; Part.conf.interf.; Load reserv.; Del.conf.<PERSON>ot", "RefUrl": "/notes/381741 "}, {"RefNumber": "406047", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "Mvt type 851/852-LIS, no value; VL09; QCI in mining", "RefUrl": "/notes/406047 "}, {"RefNumber": "418672", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "851/852 for real batch; STO VBFA; batch in conversion; Lov=0", "RefUrl": "/notes/418672 "}, {"RefNumber": "439036", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "BW wrong load; delete OIGSM; load interface; delete dcmt.", "RefUrl": "/notes/439036 "}, {"RefNumber": "481675", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "Dlvy qty changed;Rounding;VBRP-CHARG;O9 340;QCI temperat.", "RefUrl": "/notes/481675 "}, {"RefNumber": "488163", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "One OIGSM; Cancel ASTM; Batch in 851", "RefUrl": "/notes/488163 "}, {"RefNumber": "518617", "RefComponent": "IS-OIL-DS-TSW", "RefTitle": "Problem with OILNOM01", "RefUrl": "/notes/518617 "}, {"RefNumber": "355588", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Error handling in transaction O4PO", "RefUrl": "/notes/355588 "}, {"RefNumber": "431985", "RefComponent": "IS-OIL-DS-TSW", "RefTitle": "Document validation does not take place for Book Nomination", "RefUrl": "/notes/431985 "}, {"RefNumber": "489034", "RefComponent": "IS-OIL-DS-MCO", "RefTitle": "Set Screen error while creating sales order from a contract", "RefUrl": "/notes/489034 "}, {"RefNumber": "500411", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Split invoicing adds up the invoiced quantity.", "RefUrl": "/notes/500411 "}, {"RefNumber": "503197", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "D-SCM-EX:Call off qty > schedule qty", "RefUrl": "/notes/503197 "}, {"RefNumber": "505973", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Invoice getting blocked on ERS or Invoice Verification.", "RefUrl": "/notes/505973 "}, {"RefNumber": "518883", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Wrong proposal of Delivery Costs for Weight Based Products", "RefUrl": "/notes/518883 "}, {"RefNumber": "335955", "RefComponent": "IS-OIL-DS-MCO", "RefTitle": "GR: Field RM07M-LFSNR is not filled from MKPF-XBLNR", "RefUrl": "/notes/335955 "}, {"RefNumber": "408461", "RefComponent": "IS-OIL-DS-MCO", "RefTitle": "MKPF-TCODE2 is used instead of MKPF-TCODE - Msg 208803/2001", "RefUrl": "/notes/408461 "}, {"RefNumber": "447778", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Missing delivery note and bill of loading in a material doc.", "RefUrl": "/notes/447778 "}, {"RefNumber": "398764", "RefComponent": "IS-OIL-DS-HPM", "RefTitle": "Incorrect weights and volumes in delivery", "RefUrl": "/notes/398764 "}, {"RefNumber": "403703", "RefComponent": "IS-OIL-DS-HPM", "RefTitle": "Update of CMETH on VBFA for billing", "RefUrl": "/notes/403703 "}, {"RefNumber": "197466", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "Message ME218 Net Price has to be greater than 0", "RefUrl": "/notes/197466 "}, {"RefNumber": "388449", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "Inter company Sales in TD - Error KE 396", "RefUrl": "/notes/388449 "}, {"RefNumber": "482204", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "Incorrect entries in M_VMCFA for differential invoices", "RefUrl": "/notes/482204 "}, {"RefNumber": "412012", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Exg.data/fees aren't copied at cred-memo creation/cancel.", "RefUrl": "/notes/412012 "}, {"RefNumber": "413635", "RefComponent": "IS-OIL-DS-HPM", "RefTitle": "Manual pricing conditions are repriced for oil BoM", "RefUrl": "/notes/413635 "}, {"RefNumber": "445150", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Unable to change pricing date on fee screen in billing", "RefUrl": "/notes/445150 "}, {"RefNumber": "508153", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "Billing index update problems", "RefUrl": "/notes/508153 "}, {"RefNumber": "482457", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "OIEXGNUM lost in table VKDFS in billing due list run", "RefUrl": "/notes/482457 "}, {"RefNumber": "442303", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "Billing index update problems", "RefUrl": "/notes/442303 "}, {"RefNumber": "210205", "RefComponent": "IS-OIL-BC", "RefTitle": "IS-OIL: Additional Info - SPs 31-38 incl. 4.0B", "RefUrl": "/notes/210205 "}, {"RefNumber": "351475", "RefComponent": "IS-OIL-BC", "RefTitle": "IS-OIL: Additional Info - SPs 45-53 incl. 4.0B", "RefUrl": "/notes/351475 "}, {"RefNumber": "180083", "RefComponent": "IS-OIL-BC", "RefTitle": "IS-OIL: Additional Info - SPs 21-28 incl. 4.0B", "RefUrl": "/notes/180083 "}, {"RefNumber": "487243", "RefComponent": "IS-OIL-DS-TDP", "RefTitle": "Update of MBEW during gain/loss does not occur", "RefUrl": "/notes/487243 "}, {"RefNumber": "483461", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "LIA item amount changes when using some currencies", "RefUrl": "/notes/483461 "}, {"RefNumber": "518541", "RefComponent": "IS-OIL-DS-TDP", "RefTitle": "Material valuation type not copied to settlement rule object", "RefUrl": "/notes/518541 "}, {"RefNumber": "392647", "RefComponent": "IS-OIL-DS-HPM", "RefTitle": "EKAB updated wrongly during goods receipt in MB01 or MIGO", "RefUrl": "/notes/392647 "}, {"RefNumber": "484852", "RefComponent": "IS-OIL-DS-HPM", "RefTitle": "Incorrect error in Subcontracting Subsequent Adjustment", "RefUrl": "/notes/484852 "}, {"RefNumber": "487973", "RefComponent": "IS-OIL-DS-HPM", "RefTitle": "Performance problem in material master - Oil view", "RefUrl": "/notes/487973 "}, {"RefNumber": "492894", "RefComponent": "IS-OIL-DS-HPM", "RefTitle": "PO history incorrect after GR & IR for mult. acct assg", "RefUrl": "/notes/492894 "}, {"RefNumber": "498128", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Deletion of SD delivery through TPI Interface or Txn O4PO", "RefUrl": "/notes/498128 "}, {"RefNumber": "514381", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "E1OILBH-SHPPPT (Transportation planning point) not filled", "RefUrl": "/notes/514381 "}, {"RefNumber": "517617", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "Exclude Event date indicator functionality is not working", "RefUrl": "/notes/517617 "}, {"RefNumber": "515246", "RefComponent": "IS-OIL-DS-HPM", "RefTitle": "Error in quantity conversion : Billing Document..archiving", "RefUrl": "/notes/515246 "}, {"RefNumber": "513542", "RefComponent": "IS-OIL-DS-TSW", "RefTitle": "OIL: O4NC Event Log-not available in create mode for default", "RefUrl": "/notes/513542 "}, {"RefNumber": "513514", "RefComponent": "IS-OIL-DS-BDRP", "RefTitle": "Missing export parameter in Function OII_OTWS_GET_REF_SET", "RefUrl": "/notes/513514 "}, {"RefNumber": "512517", "RefComponent": "IS-OIL-DS-TSW", "RefTitle": "OIL: O4NV  Need to change Shipper due is diplay only.", "RefUrl": "/notes/512517 "}, {"RefNumber": "511255", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Incorrect valuation for exchange receipts", "RefUrl": "/notes/511255 "}, {"RefNumber": "510675", "RefComponent": "IS-OIL-DS-TSW", "RefTitle": "OIL: Rename functionality changes entries in database", "RefUrl": "/notes/510675 "}, {"RefNumber": "509736", "RefComponent": "IS-OIL-DS-HPM", "RefTitle": "Archiving of billing doc. subj to volume-based rebates", "RefUrl": "/notes/509736 "}, {"RefNumber": "508655", "RefComponent": "IS-OIL-DS-TSW", "RefTitle": "OIL: With reference to note n°487703 - 2", "RefUrl": "/notes/508655 "}, {"RefNumber": "508312", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "Consideration for unlimited indicator LIPS-UEBTK in Delivery", "RefUrl": "/notes/508312 "}, {"RefNumber": "508237", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Error message O1 033 at changing contract QS header", "RefUrl": "/notes/508237 "}, {"RefNumber": "508109", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "O4F1 short dump in Document selection F4 due to W message", "RefUrl": "/notes/508109 "}, {"RefNumber": "443575", "RefComponent": "IS-OIL-DS-TDP", "RefTitle": "Excise duty balance calc incorrect when gain/loss", "RefUrl": "/notes/443575 "}, {"RefNumber": "503839", "RefComponent": "IS-OIL-DS-TSW", "RefTitle": "OIL: Rename function in O4NC or O4NV", "RefUrl": "/notes/503839 "}, {"RefNumber": "503655", "RefComponent": "IS-OIL-DS-TSW", "RefTitle": "OIL:O4NC Event Log- not available in create mode for defaul", "RefUrl": "/notes/503655 "}, {"RefNumber": "502650", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Already cleared fees shouldn't be displayed", "RefUrl": "/notes/502650 "}, {"RefNumber": "502622", "RefComponent": "IS-OIL-DS-TDP", "RefTitle": "Message no. OB 007 when calling off from purchase contract", "RefUrl": "/notes/502622 "}, {"RefNumber": "502252", "RefComponent": "IS-OIL", "RefTitle": "IS OIL: short dump in transaction O3C2", "RefUrl": "/notes/502252 "}, {"RefNumber": "500915", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Fee copy rules cannot be maintained for SD documents", "RefUrl": "/notes/500915 "}, {"RefNumber": "500499", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Bad performance of ROIAMMA3/ROIAMMAT if no documents found", "RefUrl": "/notes/500499 "}, {"RefNumber": "500211", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Problems in OIAFE with postings different years (LIV)", "RefUrl": "/notes/500211 "}, {"RefNumber": "498506", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "O4D2 batch input wrong exclusion of FCODE DELE", "RefUrl": "/notes/498506 "}, {"RefNumber": "498094", "RefComponent": "IS-OIL-DS-TDP", "RefTitle": "IS-OIL: VF01 using different curencies E-KE 476", "RefUrl": "/notes/498094 "}, {"RefNumber": "498005", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "Material Group in Load ID determination", "RefUrl": "/notes/498005 "}, {"RefNumber": "495860", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Entry of text id in configuration of TPI interface", "RefUrl": "/notes/495860 "}, {"RefNumber": "495547", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "BW, FI-SL, CO-PA and SIS quantities missing in SD invoices", "RefUrl": "/notes/495547 "}, {"RefNumber": "490801", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "O3AB: Fee repricing causes wrong formula description", "RefUrl": "/notes/490801 "}, {"RefNumber": "488559", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Netting proposal not selecting all documents", "RefUrl": "/notes/488559 "}, {"RefNumber": "487328", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "UOM conversion not drawn from the MM contract or info record", "RefUrl": "/notes/487328 "}, {"RefNumber": "517380", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "TD Coll. Note - MCHA and MCHB checks do not allow load.conf.", "RefUrl": "/notes/517380 "}, {"RefNumber": "522282", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "ERS creates inconsistent fee table entries (OIANF/OIAFE)", "RefUrl": "/notes/522282 "}, {"RefNumber": "485940", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Performance Improvements for O3A7 with Posting Date", "RefUrl": "/notes/485940 "}, {"RefNumber": "507291", "RefComponent": "IS-OIL-DS-HPM", "RefTitle": "Quantity unit in RMCSS003", "RefUrl": "/notes/507291 "}, {"RefNumber": "358972", "RefComponent": "IS-OIL-DS-TDP", "RefTitle": "A valid handling type has to be entered for this material", "RefUrl": "/notes/358972 "}, {"RefNumber": "528008", "RefComponent": "IS-OIL-DS-TSW", "RefTitle": "MOIJNF01_COPY_REF_NOM_LINES", "RefUrl": "/notes/528008 "}, {"RefNumber": "524350", "RefComponent": "IS-OIL", "RefTitle": "Balancing Workplace IMG Activity Documentation", "RefUrl": "/notes/524350 "}, {"RefNumber": "482323", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Wrong quantity in the QS after rejecting a sales order", "RefUrl": "/notes/482323 "}, {"RefNumber": "481379", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "LIV ERS not create fee credit memo for prior year (part2)", "RefUrl": "/notes/481379 "}, {"RefNumber": "480639", "RefComponent": "IS-OIL-DS-TDP", "RefTitle": "IS-OIL: Incorrect stock qty/value in material master", "RefUrl": "/notes/480639 "}, {"RefNumber": "459951", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "Autom. compartment allocation before exchange assignment", "RefUrl": "/notes/459951 "}, {"RefNumber": "459644", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "EXIT_SAPLEINR_400: more information required", "RefUrl": "/notes/459644 "}, {"RefNumber": "458242", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "ALE for Customer Master / IS/Oil Data (OILDEB)", "RefUrl": "/notes/458242 "}, {"RefNumber": "458144", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Selection lost on scroll in Exchange contract copy", "RefUrl": "/notes/458144 "}, {"RefNumber": "451394", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Fees in delivery multiplied by thousand", "RefUrl": "/notes/451394 "}, {"RefNumber": "451483", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Exchange number is cleared when changing material document", "RefUrl": "/notes/451483 "}, {"RefNumber": "451521", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Support extended IDOCs during IDOC creation", "RefUrl": "/notes/451521 "}, {"RefNumber": "454030", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "Wrong text on Txns O4P7 and O4P8", "RefUrl": "/notes/454030 "}, {"RefNumber": "455166", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "Rounding 2step;Abend MSEGO2;Don't change temp;Wrong sign G/L", "RefUrl": "/notes/455166 "}, {"RefNumber": "455631", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "O3B3 Exg doesn't get sender address", "RefUrl": "/notes/455631 "}, {"RefNumber": "456603", "RefComponent": "IS-OIL-DS-TSW", "RefTitle": "Consingee changes do not flag nom as changed", "RefUrl": "/notes/456603 "}, {"RefNumber": "455317", "RefComponent": "IS-OIL-DS-MCO", "RefTitle": "Wrong cursor position in the OIL 40B SO overview screen", "RefUrl": "/notes/455317 "}, {"RefNumber": "448454", "RefComponent": "IS-OIL-DS-TSW", "RefTitle": "O4TF: LIST OF TICKETS A<PERSON><PERSON><PERSON>LE INCLUDES DELETED TICKETS", "RefUrl": "/notes/448454 "}, {"RefNumber": "364919", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Inbound shipment interface abends on delivery create error", "RefUrl": "/notes/364919 "}, {"RefNumber": "365418", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "G_OIKIMPORT struct not populated in SD_SALES_ITEM_MAINTAIN", "RefUrl": "/notes/365418 "}, {"RefNumber": "368749", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "MBST:Wrong F&A condition value displayed in PO history", "RefUrl": "/notes/368749 "}, {"RefNumber": "491979", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "Inactive F&A condition results in incorrect billing due list", "RefUrl": "/notes/491979 "}, {"RefNumber": "490648", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "Dumps DYNPRO_SEND_IN_BACKGROUND using ROIKPIPR", "RefUrl": "/notes/490648 "}, {"RefNumber": "489894", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "MIRO Invoice Repricing uses incorrect pricing date", "RefUrl": "/notes/489894 "}, {"RefNumber": "488493", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "MRRS is dumping due to fiscal year issue", "RefUrl": "/notes/488493 "}, {"RefNumber": "488197", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Already cleared fees are proposed during MR01", "RefUrl": "/notes/488197 "}, {"RefNumber": "485680", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Incorrect Values in Exchange Statement with Credit Memos", "RefUrl": "/notes/485680 "}, {"RefNumber": "484461", "RefComponent": "IS-OIL-DS-TDP", "RefTitle": "IS-OIL: Stock transfer order checking issuing valuation", "RefUrl": "/notes/484461 "}, {"RefNumber": "519739", "RefComponent": "IS-OIL-DS-TSW", "RefTitle": "(O4TE) Ticket Create -  showing closed noms", "RefUrl": "/notes/519739 "}, {"RefNumber": "519228", "RefComponent": "IS-OIL-DS-TDP", "RefTitle": "IBS OIL&GAS LOCALIZATION BRAZIL ST Calculation Interstate", "RefUrl": "/notes/519228 "}, {"RefNumber": "519268", "RefComponent": "IS-OIL-DS-TDP", "RefTitle": "IBS OIL&GAS LOCALIZATION BRAZIL THIRD NOTA FISCAL", "RefUrl": "/notes/519268 "}, {"RefNumber": "519282", "RefComponent": "IS-OIL-DS-TDP", "RefTitle": "IBS OIL&GAS LOCALIZATION BRAZIL LOST IS-OIL DATA DURING IV", "RefUrl": "/notes/519282 "}, {"RefNumber": "508135", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "TIMEOUT Error while reading the quotations", "RefUrl": "/notes/508135 "}, {"RefNumber": "516995", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Buffer overflow on OIKPEXORD", "RefUrl": "/notes/516995 "}, {"RefNumber": "498507", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "Status handling in TD-Scheduling for zero quantity items", "RefUrl": "/notes/498507 "}, {"RefNumber": "337560", "RefComponent": "IS-OIL-DS-MCO", "RefTitle": "Credit memo request with reference to invoice.", "RefUrl": "/notes/337560 "}, {"RefNumber": "506942", "RefComponent": "IS-OIL-DS-HPM", "RefTitle": "Dump when running report ROIB6FIX", "RefUrl": "/notes/506942 "}, {"RefNumber": "508499", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "TD-O4H1/STO enabling new batch creation in receiving plant", "RefUrl": "/notes/508499 "}, {"RefNumber": "200940", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Program to correct wrong quantity schedules (ROIACM00)", "RefUrl": "/notes/200940 "}, {"RefNumber": "492265", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Netting fails with O1 546 for cross-company code LIVs", "RefUrl": "/notes/492265 "}, {"RefNumber": "497803", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "Incorrect VKDFS entry proposed in diff. invoice cancelation", "RefUrl": "/notes/497803 "}, {"RefNumber": "449723", "RefComponent": "IS-OIL-DS-TDP", "RefTitle": "ASTM corrected volume not used for ED repricing in IR", "RefUrl": "/notes/449723 "}, {"RefNumber": "459769", "RefComponent": "IS-OIL-PRA-REV-VAL", "RefTitle": "Valuation document scheduling and processing", "RefUrl": "/notes/459769 "}, {"RefNumber": "499679", "RefComponent": "IS-OIL-DS-HPM", "RefTitle": "Short dump when when deleting SD delivery (IS-Oil)", "RefUrl": "/notes/499679 "}, {"RefNumber": "337217", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "TD Status Handling enhancement during Scheduling", "RefUrl": "/notes/337217 "}, {"RefNumber": "495893", "RefComponent": "IS-OIL-DS-MCO", "RefTitle": "Using FCODE OIDX with a SD doc. cat. <> C gives no error", "RefUrl": "/notes/495893 "}, {"RefNumber": "326517", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "VA01 TAS relevancy checks hinder order entry performance", "RefUrl": "/notes/326517 "}, {"RefNumber": "323129", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "LID internal numbering not checking OIKLID for duplicates", "RefUrl": "/notes/323129 "}, {"RefNumber": "318707", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "OILLDD IDOC posts w/out ASTM conv is density out of range", "RefUrl": "/notes/318707 "}, {"RefNumber": "318040", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "'AW' partners not available in EXIT_SAPLOIK7_130 user exit", "RefUrl": "/notes/318040 "}, {"RefNumber": "420585", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Compartment Planning disabling NOT possible", "RefUrl": "/notes/420585 "}, {"RefNumber": "351931", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Abend O1 544 on Delivery confirmation", "RefUrl": "/notes/351931 "}, {"RefNumber": "492151", "RefComponent": "IS-OIL", "RefTitle": "VF04: Nota fiscal simulation does not consider BOM's", "RefUrl": "/notes/492151 "}, {"RefNumber": "486076", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Display accounting doc.: weird text on push button", "RefUrl": "/notes/486076 "}, {"RefNumber": "384804", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "MAP: Value without quotation in second level analys", "RefUrl": "/notes/384804 "}, {"RefNumber": "386232", "RefComponent": "IS-OIL-DS-HPM", "RefTitle": "OIL: No update of net weight after availability check", "RefUrl": "/notes/386232 "}, {"RefNumber": "315968", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "IS-Oil reference note", "RefUrl": "/notes/315968 "}, {"RefNumber": "460234", "RefComponent": "IS-OIL-DS-TDP", "RefTitle": "Excise License Validiy Dates", "RefUrl": "/notes/460234 "}, {"RefNumber": "459740", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "Autom. compartment allocation before exchange assignment", "RefUrl": "/notes/459740 "}, {"RefNumber": "452122", "RefComponent": "IS-OIL-DS-TDP", "RefTitle": "Nota fiscal compl. does not consider user condition types", "RefUrl": "/notes/452122 "}, {"RefNumber": "457770", "RefComponent": "IS-OIL-DS-TDP", "RefTitle": "IS-OIL: MBST/MB01 REVERSAL OF GR AGAINST PO", "RefUrl": "/notes/457770 "}, {"RefNumber": "458055", "RefComponent": "IS-OIL-DS", "RefTitle": "Checks for IS-Oil fields in access sequence", "RefUrl": "/notes/458055 "}, {"RefNumber": "446350", "RefComponent": "IS-OIL-DS-MCO", "RefTitle": "External Details in the goods movement 301, 309 and 311.", "RefUrl": "/notes/446350 "}, {"RefNumber": "438549", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Fee copy controls missing in billing to order copy control", "RefUrl": "/notes/438549 "}, {"RefNumber": "360432", "RefComponent": "IS-OIL-DS-TDP", "RefTitle": "Error OB 011 create PO with ref to requisition", "RefUrl": "/notes/360432 "}, {"RefNumber": "422820", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "Wrong delivered quantity in the Balance Vehicle screen(O4H1)", "RefUrl": "/notes/422820 "}, {"RefNumber": "446038", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Program termination during BW contract data upload", "RefUrl": "/notes/446038 "}, {"RefNumber": "453208", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Short Dump in 40b after application of new kernel", "RefUrl": "/notes/453208 "}, {"RefNumber": "453243", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "TD Transport Unit compartment data customer defined screen", "RefUrl": "/notes/453243 "}, {"RefNumber": "388602", "RefComponent": "IS-OIL-DS-TDP", "RefTitle": "Incorrect Repricing in Invoice Correction Request", "RefUrl": "/notes/388602 "}, {"RefNumber": "431050", "RefComponent": "IS-OIL-DS-TDP", "RefTitle": "Returns not Updating License Quantity Tracking", "RefUrl": "/notes/431050 "}, {"RefNumber": "451143", "RefComponent": "IS-OIL-DS-HPM", "RefTitle": "Tracking number for 2-step transfer not generated in batch", "RefUrl": "/notes/451143 "}, {"RefNumber": "451134", "RefComponent": "IS-OIL-DS-TDP", "RefTitle": "Mass update of contract prices and external ED pricing key", "RefUrl": "/notes/451134 "}, {"RefNumber": "392666", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "SAPSQL_ARRAY_INSERT_DUPREC in ME_UPDATE_DOCUMENT", "RefUrl": "/notes/392666 "}, {"RefNumber": "409524", "RefComponent": "IS-OIL-DS", "RefTitle": "QA cleanup TODO/SAMT for support systems", "RefUrl": "/notes/409524 "}, {"RefNumber": "445741", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "Report Find Shipments", "RefUrl": "/notes/445741 "}, {"RefNumber": "409269", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "F&A Formula is not deleted to the customizing table", "RefUrl": "/notes/409269 "}, {"RefNumber": "447012", "RefComponent": "IS-OIL-DS-TSW", "RefTitle": "OIL:Change tkt with same name as deleted tkt/NOMIT not displ", "RefUrl": "/notes/447012 "}, {"RefNumber": "441786", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "Display Error in SAPMOIGS-3200 when calling from 3700", "RefUrl": "/notes/441786 "}, {"RefNumber": "433834", "RefComponent": "IS-OIL-DS-MCO", "RefTitle": "Internal work areas defined in RVINVB00, RVINVB10 are small.", "RefUrl": "/notes/433834 "}, {"RefNumber": "402177", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Shortdmp \"Field symbol not assigned\" when leaving fee dialog", "RefUrl": "/notes/402177 "}, {"RefNumber": "426381", "RefComponent": "IS-OIL-DS-HPM", "RefTitle": "HPM Appendices Inconsistent After Availability Check", "RefUrl": "/notes/426381 "}, {"RefNumber": "442657", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Wrong german texts in sales documents", "RefUrl": "/notes/442657 "}, {"RefNumber": "427316", "RefComponent": "IS-OIL-DS-HPM", "RefTitle": "ROIABVF6 Batch Invoice programs dumping", "RefUrl": "/notes/427316 "}, {"RefNumber": "434809", "RefComponent": "IS-OIL-DS-MCO", "RefTitle": "Runtime error during overflow check in LOICQF0R", "RefUrl": "/notes/434809 "}, {"RefNumber": "438767", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Invoice print performance and memory problem", "RefUrl": "/notes/438767 "}, {"RefNumber": "428275", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "KE4S fails when loading invoice with fee total condition", "RefUrl": "/notes/428275 "}, {"RefNumber": "438098", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Wrong display of values in SAPF124", "RefUrl": "/notes/438098 "}, {"RefNumber": "438227", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Billing: fee total condition differs from fee sum", "RefUrl": "/notes/438227 "}, {"RefNumber": "431481", "RefComponent": "IS-OIL-DS-TDP", "RefTitle": "IS-OIL TDP TRANSFER POSTINGS", "RefUrl": "/notes/431481 "}, {"RefNumber": "436699", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "LIAs are not properly deriving Profit Center", "RefUrl": "/notes/436699 "}, {"RefNumber": "432574", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Quantity schedule on a purchase order is wrong  (TD related)", "RefUrl": "/notes/432574 "}, {"RefNumber": "356301", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Borrow/Loan exchange posting to Purchase Acct. Mgt.", "RefUrl": "/notes/356301 "}, {"RefNumber": "429059", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "Short dump when creating Call-off against Contract", "RefUrl": "/notes/429059 "}, {"RefNumber": "369591", "RefComponent": "IS-OIL-DS-TDP", "RefTitle": "IS-OIL: Display ED licenses during sales order", "RefUrl": "/notes/369591 "}, {"RefNumber": "387303", "RefComponent": "IS-OIL-DS-HPM", "RefTitle": "Inventory batch input", "RefUrl": "/notes/387303 "}, {"RefNumber": "429002", "RefComponent": "IS-OIL-DS-MRN", "RefTitle": "CO-PA charact. OIFWE: not filled properly in Invoice", "RefUrl": "/notes/429002 "}, {"RefNumber": "427847", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Wrong fee copy at good receipt cancellation", "RefUrl": "/notes/427847 "}, {"RefNumber": "430515", "RefComponent": "IS-OIL-DS-MCO", "RefTitle": "Item category redetermination not working", "RefUrl": "/notes/430515 "}, {"RefNumber": "367022", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Redistribution of a Order planned by Ext TPS", "RefUrl": "/notes/367022 "}, {"RefNumber": "376541", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Performance improvement Euro-conversion LIKP", "RefUrl": "/notes/376541 "}, {"RefNumber": "414782", "RefComponent": "IS-OIL-DS-MCO", "RefTitle": "RM07MMAT does not consider fields EXTBOL/MISCDL", "RefUrl": "/notes/414782 "}, {"RefNumber": "215608", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "Error Handling in TAS Inbound Process", "RefUrl": "/notes/215608 "}, {"RefNumber": "365694", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "Minus(-) Temperature is not processed.", "RefUrl": "/notes/365694 "}, {"RefNumber": "392082", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "Wrong data format in forwarded Idoc OILLDD & OILLDC", "RefUrl": "/notes/392082 "}, {"RefNumber": "370169", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Missing order item in TPI outbound idoc", "RefUrl": "/notes/370169 "}, {"RefNumber": "374846", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Error in changing ´plant´ of order", "RefUrl": "/notes/374846 "}, {"RefNumber": "378285", "RefComponent": "IS-OIL-DS-HPM", "RefTitle": "Correction for short dump due to TABLE_INVALID_INDEX", "RefUrl": "/notes/378285 "}, {"RefNumber": "393394", "RefComponent": "IS-OIL-DS-SSR", "RefTitle": "OTWS error on SCP screen of Business Location", "RefUrl": "/notes/393394 "}, {"RefNumber": "388915", "RefComponent": "IS-OIL", "RefTitle": "ISO conversion leads to blank unit of measures", "RefUrl": "/notes/388915 "}, {"RefNumber": "392236", "RefComponent": "IS-OIL", "RefTitle": "ISO conversion leads to blank unit of measures", "RefUrl": "/notes/392236 "}, {"RefNumber": "414289", "RefComponent": "IS-OIL-DS-BDRP", "RefTitle": "O5NX Batch input error due to LEAVE TO TRANSACTION statement", "RefUrl": "/notes/414289 "}, {"RefNumber": "414197", "RefComponent": "IS-OIL-DS-BDRP", "RefTitle": "O5NG batch input error due to LEAVE TO TRANSACTION statement", "RefUrl": "/notes/414197 "}, {"RefNumber": "411413", "RefComponent": "IS-OIL-DS-BDRP", "RefTitle": "VA01 errors when removing tank assignment", "RefUrl": "/notes/411413 "}, {"RefNumber": "381462", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "F4 help on shipment workbench shows all drivers", "RefUrl": "/notes/381462 "}, {"RefNumber": "415005", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "data element OIG_AEDTMF field labels are wrong", "RefUrl": "/notes/415005 "}, {"RefNumber": "399019", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "TD Travel time fields not filled from shimpent", "RefUrl": "/notes/399019 "}, {"RefNumber": "398955", "RefComponent": "IS-OIL", "RefTitle": "ISO conversion leads to blank unit of measures", "RefUrl": "/notes/398955 "}, {"RefNumber": "414989", "RefComponent": "IS-OIL", "RefTitle": "Enable internal flag in ticket screen", "RefUrl": "/notes/414989 "}, {"RefNumber": "423668", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "STSI Load Confirmation posts additional qtys for TDP matl", "RefUrl": "/notes/423668 "}, {"RefNumber": "403030", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "After implementation of 392231 inbound process short dumps", "RefUrl": "/notes/403030 "}, {"RefNumber": "417113", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "O4PO: new created delivery w/o tank id entries in oik37", "RefUrl": "/notes/417113 "}, {"RefNumber": "402561", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "Error in sales order for more then one item in TAS", "RefUrl": "/notes/402561 "}, {"RefNumber": "426567", "RefComponent": "IS-OIL-DS-SSR", "RefTitle": "SSR/MRN update classification table entries in TCLT", "RefUrl": "/notes/426567 "}, {"RefNumber": "427650", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "Delivery Note and Bill of Lading not Appearing in MB01....", "RefUrl": "/notes/427650 "}, {"RefNumber": "420095", "RefComponent": "IS-OIL-DS-TDP", "RefTitle": "IS-OIL TDP GI for delivery note missing OIVBELN + OIPOSNR", "RefUrl": "/notes/420095 "}, {"RefNumber": "426588", "RefComponent": "IS-OIL-DS-MCO", "RefTitle": "Shortdump in a sales order with no line items.", "RefUrl": "/notes/426588 "}, {"RefNumber": "425216", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "Batch missing; Vehicle balance; BW /0; Abend mvmt o b; OIGSH", "RefUrl": "/notes/425216 "}, {"RefNumber": "423369", "RefComponent": "IS-OIL-DS-MCO", "RefTitle": "Shortdump while changing sales order with no line items.", "RefUrl": "/notes/423369 "}, {"RefNumber": "392985", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "MR21: Double values passed to CO, accounting items missing", "RefUrl": "/notes/392985 "}, {"RefNumber": "422007", "RefComponent": "IS-OIL-DS-TDP", "RefTitle": "IS-OIL TDP BILLING MISSING CO LINES", "RefUrl": "/notes/422007 "}, {"RefNumber": "424517", "RefComponent": "IS-R-PUR-PO", "RefTitle": "Default value: Time of delivery in online planning", "RefUrl": "/notes/424517 "}, {"RefNumber": "406580", "RefComponent": "IS-OIL-DS-HPM", "RefTitle": "Error when changing storage location in delivery", "RefUrl": "/notes/406580 "}, {"RefNumber": "422509", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "Wrong loaded qty in balance vehicle screen in TD-O4H1.", "RefUrl": "/notes/422509 "}, {"RefNumber": "421302", "RefComponent": "IS-OIL-DS-HPM", "RefTitle": "IS-OIL: MMBE - Wrong totals shown", "RefUrl": "/notes/421302 "}, {"RefNumber": "420838", "RefComponent": "IS-OIL-DS-HPM", "RefTitle": "IS-OIL: MMBE shows wrong stock sums", "RefUrl": "/notes/420838 "}, {"RefNumber": "419887", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Impossible to change exchange starting date", "RefUrl": "/notes/419887 "}, {"RefNumber": "417277", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Short dump & BDC error with netting document & BTCI", "RefUrl": "/notes/417277 "}, {"RefNumber": "416930", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Cancelation of note 391987 (no agreed redesign of feerepric)", "RefUrl": "/notes/416930 "}, {"RefNumber": "354546", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "MAP: Price not found, if non-posted day = 'X'", "RefUrl": "/notes/354546 "}, {"RefNumber": "412858", "RefComponent": "IS-OIL-DS-HPM", "RefTitle": "IS OIL: Vendor consignment stock displayed only in base UoM", "RefUrl": "/notes/412858 "}, {"RefNumber": "402296", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Error occurs in LIA account determination", "RefUrl": "/notes/402296 "}, {"RefNumber": "413748", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Redetermine Sold-to party when creating new SD contract", "RefUrl": "/notes/413748 "}, {"RefNumber": "392597", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Impossible to change exchange starting date", "RefUrl": "/notes/392597 "}, {"RefNumber": "368957", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "EKBZ update excise duty addback", "RefUrl": "/notes/368957 "}, {"RefNumber": "416919", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "Short dump while calling BAPI_SALESORDER_CREATEFROMDAT2", "RefUrl": "/notes/416919 "}, {"RefNumber": "412543", "RefComponent": "IS-OIL-DS-MCO", "RefTitle": "Oil vers. of note 410014: No check of pricing ref. material", "RefUrl": "/notes/412543 "}, {"RefNumber": "415989", "RefComponent": "IS-OIL-BC", "RefTitle": "IS-OIL: Additional Info - SPs 61-62 incl. 4.0B", "RefUrl": "/notes/415989 "}, {"RefNumber": "414893", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "Field Control for transport zone & Mode of Transport", "RefUrl": "/notes/414893 "}, {"RefNumber": "308790", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "TD - Document flow;Branch to mat.doc,Incorrect GJAHR", "RefUrl": "/notes/308790 "}, {"RefNumber": "393987", "RefComponent": "IS-OIL-DS-MCO", "RefTitle": "IS-OIL: Clear sales office and group in SO entry", "RefUrl": "/notes/393987 "}, {"RefNumber": "327413", "RefComponent": "IS-OIL-DS-TDP", "RefTitle": "IS-OIL: Document date in material document", "RefUrl": "/notes/327413 "}, {"RefNumber": "401810", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Error V0010 occur after repeat the allocation step in O3A2", "RefUrl": "/notes/401810 "}, {"RefNumber": "401341", "RefComponent": "IS-OIL-DS-HPM", "RefTitle": "Zero quantity line is not calculated during delivery", "RefUrl": "/notes/401341 "}, {"RefNumber": "411207", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Shipment Planning corrections", "RefUrl": "/notes/411207 "}, {"RefNumber": "398954", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Shipment Planning corrections", "RefUrl": "/notes/398954 "}, {"RefNumber": "211421", "RefComponent": "IS-OIL-DS-MCO", "RefTitle": "Delivery Note and Bill of Lading not Appearing in MB02", "RefUrl": "/notes/211421 "}, {"RefNumber": "350212", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "Update termination in shipment delete due to credit check", "RefUrl": "/notes/350212 "}, {"RefNumber": "407729", "RefComponent": "IS-OIL-DS-MCO", "RefTitle": "Valuation type not visible in overview screen", "RefUrl": "/notes/407729 "}, {"RefNumber": "333308", "RefComponent": "IS-OIL-DS-TDP", "RefTitle": "IS-OIL: Excise Duty Inventory check reports", "RefUrl": "/notes/333308 "}, {"RefNumber": "408614", "RefComponent": "IS-OIL-DS-MCO", "RefTitle": "MCOE: No Validation for ship. point and route on order entry", "RefUrl": "/notes/408614 "}, {"RefNumber": "384863", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "Display problems in differential invoices", "RefUrl": "/notes/384863 "}, {"RefNumber": "339524", "RefComponent": "IS-OIL-DS-TDP", "RefTitle": "Enable stock transfer when Han.Type&VT have diff. ED Status", "RefUrl": "/notes/339524 "}, {"RefNumber": "207657", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "Back button crea. entr.in chg Output Det.", "RefUrl": "/notes/207657 "}, {"RefNumber": "300744", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "TD Material freight classes/freight codes in shpt costing", "RefUrl": "/notes/300744 "}, {"RefNumber": "379423", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "Doc. item qty assign. in case of weight-volume-deviations", "RefUrl": "/notes/379423 "}, {"RefNumber": "189505", "RefComponent": "IS-OIL-DS-TDP", "RefTitle": "Handl.type not def. on change of plant or mat.at material", "RefUrl": "/notes/189505 "}, {"RefNumber": "317717", "RefComponent": "IS-OIL-DS-TDP", "RefTitle": "GR Reversal / Excise Duty / wrong Doc. Currency amounts", "RefUrl": "/notes/317717 "}, {"RefNumber": "300150", "RefComponent": "IS-OIL-DS-TDP", "RefTitle": "Exd license not valid (problem with the valid from/to date)", "RefUrl": "/notes/300150 "}, {"RefNumber": "197905", "RefComponent": "IS-OIL-DS-TDP", "RefTitle": "Wrong exd posting when currency is set to 1 decimal place", "RefUrl": "/notes/197905 "}, {"RefNumber": "378352", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "Compart. not displayed for PTL rebrand during Shpt Loading", "RefUrl": "/notes/378352 "}, {"RefNumber": "321135", "RefComponent": "IS-OIL-DS-BDRP", "RefTitle": "Transp. functionality f. Operational Time Window Sets (OTWS)", "RefUrl": "/notes/321135 "}, {"RefNumber": "206287", "RefComponent": "IS-OIL-DS-BDRP", "RefTitle": "ROIIP-RCBLK and ROIIP-DOBLK missing in Loc. data-> TPS ctrl", "RefUrl": "/notes/206287 "}, {"RefNumber": "202267", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Pur. Assignm.Scr:F4 on the PO doc.does not copy the Item No.", "RefUrl": "/notes/202267 "}, {"RefNumber": "194512", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Load balancing:qty difference in GR & goods issue documents", "RefUrl": "/notes/194512 "}, {"RefNumber": "376579", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "MAP:Conditions with qty scale basis become inactive in docs", "RefUrl": "/notes/376579 "}, {"RefNumber": "193229", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Use cust.exit on KNA1 change pointer for updates to SCP OTWS", "RefUrl": "/notes/193229 "}, {"RefNumber": "179557", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "Pricing date> sy-datum, quotation error routine not trigger.", "RefUrl": "/notes/179557 "}, {"RefNumber": "177866", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "Index(VKDFS) is upd. incorr. when cancel diff. invoice", "RefUrl": "/notes/177866 "}, {"RefNumber": "309707", "RefComponent": "IS-OIL-DS-TDP", "RefTitle": "No account posting in sub-contract PO with Oil material", "RefUrl": "/notes/309707 "}, {"RefNumber": "363310", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "Dump at the creation of an iv. list (OIC_DITAB is not def.)", "RefUrl": "/notes/363310 "}, {"RefNumber": "378406", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "No gain/loss post. at GR/GI with diff. sub/baseproduct", "RefUrl": "/notes/378406 "}, {"RefNumber": "364026", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Problems at the datacopy from MM contract to po- TDP related", "RefUrl": "/notes/364026 "}, {"RefNumber": "207504", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "MR01: Del. costs by Vendor can be invoiced multiple times", "RefUrl": "/notes/207504 "}, {"RefNumber": "383918", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "BSW factor is not appearing in oil calc. at load confirm.", "RefUrl": "/notes/383918 "}, {"RefNumber": "363131", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "Error proc.while assign. v.mtr with del.flag to Transp.Unit", "RefUrl": "/notes/363131 "}, {"RefNumber": "334462", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "Sort order doc.flow; Rounding load interface; reserv. batch", "RefUrl": "/notes/334462 "}, {"RefNumber": "213757", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "Err. PTL, Rebr. to non-batch, Avail.with warning, Veh.Recon", "RefUrl": "/notes/213757 "}, {"RefNumber": "176647", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "Dens.of chem.prod.;Round.HPM;Veh. recon.;O4G1-Err Msg:V0104", "RefUrl": "/notes/176647 "}, {"RefNumber": "154841", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "TD temperature UoM in popup/multiple MM docs per transaction", "RefUrl": "/notes/154841 "}, {"RefNumber": "309717", "RefComponent": "IS-OIL-DS-TDP", "RefTitle": "ED qty in special ledger / ED inventory during transfer", "RefUrl": "/notes/309717 "}, {"RefNumber": "322262", "RefComponent": "IS-OIL-DS-MCO", "RefTitle": "Enable scroll bars/min. resolution in OIL entry order screen", "RefUrl": "/notes/322262 "}, {"RefNumber": "310095", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "Wrong positioning in shpt scheduling - doc./compart. details", "RefUrl": "/notes/310095 "}, {"RefNumber": "352264", "RefComponent": "IS-OIL-DS-HPM", "RefTitle": "Populate LIPS-LGMNG with qty in base UoM for GI reversal", "RefUrl": "/notes/352264 "}, {"RefNumber": "351945", "RefComponent": "IS-OIL-DS-HPM", "RefTitle": "Calc. sales tax acc. to new price cond. when price control 5", "RefUrl": "/notes/351945 "}, {"RefNumber": "172212", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "TD compartment user screens: Selected compartments lost", "RefUrl": "/notes/172212 "}, {"RefNumber": "407197", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Billed order appears again in VF04", "RefUrl": "/notes/407197 "}, {"RefNumber": "366778", "RefComponent": "IS-OIL-DS-MRN", "RefTitle": "Inconsistency in prof.segm.number for intercompany billing", "RefUrl": "/notes/366778 "}, {"RefNumber": "300784", "RefComponent": "IS-OIL-DS-MRN", "RefTitle": "Table \"S???\" is not listed in the ABAP/4 Dictionary", "RefUrl": "/notes/300784 "}, {"RefNumber": "189339", "RefComponent": "IS-OIL-DS-MRN", "RefTitle": "ORA 1562 on OIFPBL in program RVV05IVB", "RefUrl": "/notes/189339 "}, {"RefNumber": "176909", "RefComponent": "IS-OIL-DS-MRN", "RefTitle": "OIPBL missing during sales order call-off", "RefUrl": "/notes/176909 "}, {"RefNumber": "406284", "RefComponent": "IS-OIL-DS-TDP", "RefTitle": "MR01 in foreign currency ends in message M8 *********** 085", "RefUrl": "/notes/406284 "}, {"RefNumber": "402367", "RefComponent": "IS-OIL-DS-TDP", "RefTitle": "Error M7 050 with transfer after partial stock revaluation", "RefUrl": "/notes/402367 "}, {"RefNumber": "400419", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "Formula Maintenance ends in duplicate record", "RefUrl": "/notes/400419 "}, {"RefNumber": "401593", "RefComponent": "IS-OIL-DS-TDP", "RefTitle": "IS-OIL / Euro: Ensure that OIH01 buffer is reset", "RefUrl": "/notes/401593 "}, {"RefNumber": "361254", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "Partner det. in call-off from ship-to with SAP/TAS interfa", "RefUrl": "/notes/361254 "}, {"RefNumber": "400525", "RefComponent": "IS-OIL-OL", "RefTitle": "Incorrect store location during shipment receipt", "RefUrl": "/notes/400525 "}, {"RefNumber": "399436", "RefComponent": "IS-OIL-DS-TDP", "RefTitle": "IS-OIL TDP Sales excise duty revenue adjustment", "RefUrl": "/notes/399436 "}, {"RefNumber": "394795", "RefComponent": "IS-OIL-DS-TDP", "RefTitle": "IS-OIL TDP TAX QUANTITY IN MATERIAL DOCUMENT", "RefUrl": "/notes/394795 "}, {"RefNumber": "399825", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Exclude items from ERS is not correct", "RefUrl": "/notes/399825 "}, {"RefNumber": "392391", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Missing gain/loss posting at goods receipt/issue", "RefUrl": "/notes/392391 "}, {"RefNumber": "364780", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Inbound interface abends on deleted order", "RefUrl": "/notes/364780 "}, {"RefNumber": "392231", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "No error at O4PO if delivery is assigned to shipment", "RefUrl": "/notes/392231 "}, {"RefNumber": "398747", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "No error at O4PO if delivery is assigned to shipment", "RefUrl": "/notes/398747 "}, {"RefNumber": "395803", "RefComponent": "IS-OIL-DS-TDP", "RefTitle": "IS-OIL ERS TDP POSTINGS WITH DIFFERENT CURRENCIES", "RefUrl": "/notes/395803 "}, {"RefNumber": "397101", "RefComponent": "IS-OIL-DS-HPM", "RefTitle": "IS-Oil: Error in rebate agreement with zero document", "RefUrl": "/notes/397101 "}, {"RefNumber": "381516", "RefComponent": "IS-OIL-DS-TDP", "RefTitle": "Short dump in VF04 on Nota Fiscal Complementar", "RefUrl": "/notes/381516 "}, {"RefNumber": "398658", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "Append OILTCURR from table TCURR deleted with 4.6C", "RefUrl": "/notes/398658 "}, {"RefNumber": "149943", "RefComponent": "IS-OIL-BC", "RefTitle": "IS-OIL: Additional Info - Support Package 11, 4.0B", "RefUrl": "/notes/149943 "}, {"RefNumber": "317518", "RefComponent": "IS-OIL-BC", "RefTitle": "IS-OIL: Additional Info - SPs 39-44 incl. 4.0B", "RefUrl": "/notes/317518 "}, {"RefNumber": "397767", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "MRRL Duplicate error messages in the ERS run output", "RefUrl": "/notes/397767 "}, {"RefNumber": "361335", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Error O1 573 during purch. ass. with group cond.", "RefUrl": "/notes/361335 "}, {"RefNumber": "395885", "RefComponent": "IS-OIL-DS-SSR", "RefTitle": "OIL: SSR Field conversions retrieves wrong value", "RefUrl": "/notes/395885 "}, {"RefNumber": "199227", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "Load ID customer screen handling", "RefUrl": "/notes/199227 "}, {"RefNumber": "174656", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "IS-OIL: Zero qty in PO history of EXG assigned PO", "RefUrl": "/notes/174656 "}, {"RefNumber": "323387", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "Characteristics for postings to CO-PA from shpmnt", "RefUrl": "/notes/323387 "}, {"RefNumber": "388788", "RefComponent": "IS-OIL-DS-TDP", "RefTitle": "IS-OIL: MBST/MB01 REVERSAL OF GR AGAINST PO", "RefUrl": "/notes/388788 "}, {"RefNumber": "380390", "RefComponent": "IS-OIL-DS-HPM", "RefTitle": "IS-OIL: GR FOR SUBCONTRACT PURCHASE ORDER WITH FRIGHT", "RefUrl": "/notes/380390 "}, {"RefNumber": "393390", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "Time in 'Status and deadlines box'", "RefUrl": "/notes/393390 "}, {"RefNumber": "391448", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Create acc. doc. w/ BTCI: no exchange no. field", "RefUrl": "/notes/391448 "}, {"RefNumber": "312246", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "Parallel OILLDD processing - advanced development", "RefUrl": "/notes/312246 "}, {"RefNumber": "392074", "RefComponent": "IS-OIL-DS-MCO", "RefTitle": "FDI indicator invisible at delivery note return order", "RefUrl": "/notes/392074 "}, {"RefNumber": "391987", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "GR Repricing does not reprice exchange fees", "RefUrl": "/notes/391987 "}, {"RefNumber": "331952", "RefComponent": "IS-OIL-DS-TSW", "RefTitle": "Rundown engine calculations for transport system.", "RefUrl": "/notes/331952 "}, {"RefNumber": "334795", "RefComponent": "IS-OIL-DS-TSW", "RefTitle": "Error in conversion factor in rundown report", "RefUrl": "/notes/334795 "}, {"RefNumber": "355654", "RefComponent": "IS-OIL-DS-TSW", "RefTitle": "Base Location lookup  for customer exit not working", "RefUrl": "/notes/355654 "}, {"RefNumber": "370775", "RefComponent": "IS-OIL-DS-TSW", "RefTitle": "CONVERSION FACTOR ERROR", "RefUrl": "/notes/370775 "}, {"RefNumber": "174437", "RefComponent": "IS-OIL-DS-TDP", "RefTitle": "TDP VALUE CORRECTION WHEN CANCEL AN PO INVOICE", "RefUrl": "/notes/174437 "}, {"RefNumber": "174746", "RefComponent": "IS-OIL-DS-HPM", "RefTitle": "IS-OIL: Qty conversion error when using product proposal", "RefUrl": "/notes/174746 "}, {"RefNumber": "320496", "RefComponent": "IS-OIL-DS-HPM", "RefTitle": "IS-OIL: Period close using up all database locks", "RefUrl": "/notes/320496 "}, {"RefNumber": "364092", "RefComponent": "IS-OIL-DS-TSW", "RefTitle": "TSW Ticketing: Load Confirmation", "RefUrl": "/notes/364092 "}, {"RefNumber": "391477", "RefComponent": "IS-OIL-DS-MRN", "RefTitle": "Invoice split dur.int-comp.billng:location assgnmt", "RefUrl": "/notes/391477 "}, {"RefNumber": "390711", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Cancelling invoice passes wrong quantity to LIS", "RefUrl": "/notes/390711 "}, {"RefNumber": "326623", "RefComponent": "IS-OIL-DS-TSW", "RefTitle": "Cannot create ticket for PO, SO with date < delivery date", "RefUrl": "/notes/326623 "}, {"RefNumber": "380451", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Wrong/Repetitive warning message O1 811 in MR01 simulation", "RefUrl": "/notes/380451 "}, {"RefNumber": "381616", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Goods receipt: Wrong internal material payables", "RefUrl": "/notes/381616 "}, {"RefNumber": "384877", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Bad performance at VF04/VF06 because of too much enqueues", "RefUrl": "/notes/384877 "}, {"RefNumber": "386718", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "QS message O1 012 customizable by table T160M", "RefUrl": "/notes/386718 "}, {"RefNumber": "377118", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Quantity missing in logical inventory", "RefUrl": "/notes/377118 "}, {"RefNumber": "380744", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Cancelling invoice with fees: no accounting document", "RefUrl": "/notes/380744 "}, {"RefNumber": "385889", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Fees or taxes missing on netting statement", "RefUrl": "/notes/385889 "}, {"RefNumber": "388748", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Fees in invoice verification should not be parked", "RefUrl": "/notes/388748 "}, {"RefNumber": "378700", "RefComponent": "IS-OIL-DS-HPM", "RefTitle": "IS-OIL: CO13/CO15/CO..with ASTM and or TDP Material", "RefUrl": "/notes/378700 "}, {"RefNumber": "385417", "RefComponent": "IS-OIL-DS-HPM", "RefTitle": "IS-Oil: Error when changing stor.loc. in delivery", "RefUrl": "/notes/385417 "}, {"RefNumber": "335256", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Fees in invoice verification should not be parked", "RefUrl": "/notes/335256 "}, {"RefNumber": "316891", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Flexible Locking of Driver Vehicle Assignment", "RefUrl": "/notes/316891 "}, {"RefNumber": "351652", "RefComponent": "IS-OIL-DS-HPM", "RefTitle": "IS-Oil: Update termination during goods issue", "RefUrl": "/notes/351652 "}, {"RefNumber": "177563", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Save tank for delivery on shipment inbound", "RefUrl": "/notes/177563 "}, {"RefNumber": "177556", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "change communication structure for delete IDoc processing", "RefUrl": "/notes/177556 "}, {"RefNumber": "200706", "RefComponent": "IS-OIL-DS-BDRP", "RefTitle": "GUI status OIIPBLCT missing", "RefUrl": "/notes/200706 "}, {"RefNumber": "192014", "RefComponent": "IS-OIL-DS-BDRP", "RefTitle": "Error OC709 occurs on TPI OTWS entry", "RefUrl": "/notes/192014 "}, {"RefNumber": "191432", "RefComponent": "IS-OIL-DS-BDRP", "RefTitle": "Tank ID assignment at order change incorrect", "RefUrl": "/notes/191432 "}, {"RefNumber": "177825", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Order distribution action code", "RefUrl": "/notes/177825 "}, {"RefNumber": "191401", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Action code in OILTPI50 populated incorrectly", "RefUrl": "/notes/191401 "}, {"RefNumber": "373605", "RefComponent": "IS-OIL-DS-HPM", "RefTitle": "IS-Oil: Error in dynamic UoM enhancement (F&A fees)", "RefUrl": "/notes/373605 "}, {"RefNumber": "363888", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "Differential invoice and taxes", "RefUrl": "/notes/363888 "}, {"RefNumber": "351836", "RefComponent": "IS-OIL-DS-HPM", "RefTitle": "IS-Oil: No repricing for oil BOM header", "RefUrl": "/notes/351836 "}, {"RefNumber": "379751", "RefComponent": "IS-OIL-DS-TDP", "RefTitle": "Excise Duty Screen appears empty in the Incompl.Log", "RefUrl": "/notes/379751 "}, {"RefNumber": "382846", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Fees missing on invoice print", "RefUrl": "/notes/382846 "}, {"RefNumber": "380993", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "Wrong sorting in formula term item tab XOICQ8 and XOICQ9", "RefUrl": "/notes/380993 "}, {"RefNumber": "380630", "RefComponent": "IS-OIL-DS-HPM", "RefTitle": "IS-OIL: Wrong additional quantities in delivery due list", "RefUrl": "/notes/380630 "}, {"RefNumber": "382255", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "TD: Display purchase requisition from documentflow", "RefUrl": "/notes/382255 "}, {"RefNumber": "379875", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Wrong GI price", "RefUrl": "/notes/379875 "}, {"RefNumber": "381772", "RefComponent": "IS-OIL-DS-HPM", "RefTitle": "Incorrect screen to display Consignment Pricing", "RefUrl": "/notes/381772 "}, {"RefNumber": "351229", "RefComponent": "IS-OIL-DS-TDP", "RefTitle": "Valuation type not defaulted on change of plant", "RefUrl": "/notes/351229 "}, {"RefNumber": "381907", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "TD-F: Conversion of UoM for shipment costing", "RefUrl": "/notes/381907 "}, {"RefNumber": "372080", "RefComponent": "IS-OIL-DS-HPM", "RefTitle": "IS-Oil: Error in dynamic UoM enhancement", "RefUrl": "/notes/372080 "}, {"RefNumber": "370570", "RefComponent": "IS-OIL-DS-HPM", "RefTitle": "IS-Oil: Incorrect valuation of vendor consignment stock", "RefUrl": "/notes/370570 "}, {"RefNumber": "370008", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "Events with default event type not getting saved.", "RefUrl": "/notes/370008 "}, {"RefNumber": "371447", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Consideration of gain/losses at the LIA posting", "RefUrl": "/notes/371447 "}, {"RefNumber": "188842", "RefComponent": "MM-IM-GF", "RefTitle": "MB03: display external details fails (WA OIDE A)", "RefUrl": "/notes/188842 "}, {"RefNumber": "374985", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "MR01 delivery cost sequence as per GR sequence", "RefUrl": "/notes/374985 "}, {"RefNumber": "369927", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "Quant. conv. factors from info record only if they exist", "RefUrl": "/notes/369927 "}, {"RefNumber": "315091", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Advanced functionality for Shipment Planning", "RefUrl": "/notes/315091 "}, {"RefNumber": "372540", "RefComponent": "IS-OIL-DS-HPM", "RefTitle": "IS-Oil: Inconsistency in additional quantities for delivery", "RefUrl": "/notes/372540 "}, {"RefNumber": "170620", "RefComponent": "IS-OIL-DS-HPM", "RefTitle": "IS-OIL: EXG fee UoM missing in HPM quantity conversion", "RefUrl": "/notes/170620 "}, {"RefNumber": "335717", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Exchange abstract reprices sales fees incorrectly", "RefUrl": "/notes/335717 "}, {"RefNumber": "150132", "RefComponent": "IS-OIL-DS-HPM", "RefTitle": "IS-OIL: credit management: value update problem", "RefUrl": "/notes/150132 "}, {"RefNumber": "350486", "RefComponent": "IS-OIL-DS-TSW", "RefTitle": "IS-OIL : TSW : Nominations not printing", "RefUrl": "/notes/350486 "}, {"RefNumber": "366622", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Posting of exchange reversal fees to SL not possible", "RefUrl": "/notes/366622 "}, {"RefNumber": "367704", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Goods issue: Double S036 update", "RefUrl": "/notes/367704 "}, {"RefNumber": "362077", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Financial documents not found in netting", "RefUrl": "/notes/362077 "}, {"RefNumber": "326970", "RefComponent": "IS-OIL", "RefTitle": "Missing setting for change pointers", "RefUrl": "/notes/326970 "}, {"RefNumber": "364900", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "TankID is lost, if items are deleted+re-created in s/o", "RefUrl": "/notes/364900 "}, {"RefNumber": "365908", "RefComponent": "IS-OIL-DS-HPM", "RefTitle": "IS OIL: PI with freeze book indicator", "RefUrl": "/notes/365908 "}, {"RefNumber": "363115", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "Error M7 234 for movement involving vendor consignment", "RefUrl": "/notes/363115 "}, {"RefNumber": "356722", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "IS-OIL: Adaption of preprocessing Program EWUMMPOA", "RefUrl": "/notes/356722 "}, {"RefNumber": "362910", "RefComponent": "IS-OIL-DS-TDP", "RefTitle": "IS-OIL: Proposal of valuation type in batch field", "RefUrl": "/notes/362910 "}, {"RefNumber": "365843", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "No record OIGSVMQO1;GI of STO for new valtyp; 195869", "RefUrl": "/notes/365843 "}, {"RefNumber": "364777", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Negative quantities in quantity schedule", "RefUrl": "/notes/364777 "}, {"RefNumber": "362410", "RefComponent": "IS-OIL-DS-MCO", "RefTitle": "T063 entries missing with function code OIDE", "RefUrl": "/notes/362410 "}, {"RefNumber": "352656", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Delivery w/ purchase assignment: update termination", "RefUrl": "/notes/352656 "}, {"RefNumber": "364320", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Delivery update termin./ excise duty cond. missing", "RefUrl": "/notes/364320 "}, {"RefNumber": "361878", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "Event doc type not found (msg E220)", "RefUrl": "/notes/361878 "}, {"RefNumber": "350797", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "G_OIKIMPORT structure not populated in SD_ITEM_MAINTAIN", "RefUrl": "/notes/350797 "}, {"RefNumber": "361284", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "MR01/MRHG: Delivery Cost Credit Memo error between years", "RefUrl": "/notes/361284 "}, {"RefNumber": "356471", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "CASE II ROIGMS00: Inconsistencies between SAP and terminal", "RefUrl": "/notes/356471 "}, {"RefNumber": "360233", "RefComponent": "IS-OIL-DS-BDRP", "RefTitle": "Open hours check fails for Time Frames", "RefUrl": "/notes/360233 "}, {"RefNumber": "351626", "RefComponent": "IS-OIL-DS-MRN", "RefTitle": "Missing IS-Oil fields in CO-PA KENC table", "RefUrl": "/notes/351626 "}, {"RefNumber": "362516", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "VL02: update error for purchase assignment", "RefUrl": "/notes/362516 "}, {"RefNumber": "363140", "RefComponent": "IS-OIL-DS-MRN", "RefTitle": "IS-Oil MRN field OIFWE not initialized properly", "RefUrl": "/notes/363140 "}, {"RefNumber": "146147", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "MAP-Customer price list does not work for item lev.", "RefUrl": "/notes/146147 "}, {"RefNumber": "310671", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "External ref number not preserved in transaction O4PP", "RefUrl": "/notes/310671 "}, {"RefNumber": "319760", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "E1OILSC-MAX_VOL incorrect in OILSHL shipment d/load IDOC", "RefUrl": "/notes/319760 "}, {"RefNumber": "316453", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Incorrect exchange statement print out when no activities", "RefUrl": "/notes/316453 "}, {"RefNumber": "315973", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "S036 rebuild picks up some transactions twice", "RefUrl": "/notes/315973 "}, {"RefNumber": "315730", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Incorrect freight posting for exchange related GR", "RefUrl": "/notes/315730 "}, {"RefNumber": "300650", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Incorrect market quote found after pricing date change in SO", "RefUrl": "/notes/300650 "}, {"RefNumber": "358259", "RefComponent": "IS-OIL-DS-TDP", "RefTitle": "HT default from Process order during GI", "RefUrl": "/notes/358259 "}, {"RefNumber": "312121", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "MM_MATBEL: bad performance and incorrect check for netting", "RefUrl": "/notes/312121 "}, {"RefNumber": "350742", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Tank-ID screen in TD-Deliver Confirmation comes blank", "RefUrl": "/notes/350742 "}, {"RefNumber": "357274", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "Incorrect tax code in acc.doc. for diff.invoice", "RefUrl": "/notes/357274 "}, {"RefNumber": "356007", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "Set loading date/time in freight assignment (O4L1)", "RefUrl": "/notes/356007 "}, {"RefNumber": "356080", "RefComponent": "IS-OIL-DS-MCO", "RefTitle": "External Details missing when plant entered manually", "RefUrl": "/notes/356080 "}, {"RefNumber": "351666", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "Input of init.value not allowed for field V_T685A-OIREPORT", "RefUrl": "/notes/351666 "}, {"RefNumber": "355142", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "O4F2: Update terminated on vehicle change in bulk shipment", "RefUrl": "/notes/355142 "}, {"RefNumber": "336962", "RefComponent": "IS-OIL-DS-TDP", "RefTitle": "IS-OIL: MR21/MR22 allow postings at negativ stock level", "RefUrl": "/notes/336962 "}, {"RefNumber": "351551", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "Received error message when add a new output item", "RefUrl": "/notes/351551 "}, {"RefNumber": "355564", "RefComponent": "IS-OIL-DS-TDP", "RefTitle": "IS-OIL: ED Revaluation if base UoM not equal ED UoM", "RefUrl": "/notes/355564 "}, {"RefNumber": "350766", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "Search help VMVL ID T causes problems", "RefUrl": "/notes/350766 "}, {"RefNumber": "351807", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "LIPS upd missing; Delete NAST", "RefUrl": "/notes/351807 "}, {"RefNumber": "336131", "RefComponent": "IS-OIL-DS-MCO", "RefTitle": "External details WAOIDEVB entry missing in T063", "RefUrl": "/notes/336131 "}, {"RefNumber": "353978", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Ship-to party incorrectly displayed", "RefUrl": "/notes/353978 "}, {"RefNumber": "353230", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Authority check fails for quantity schedule", "RefUrl": "/notes/353230 "}, {"RefNumber": "338865", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Split invoicing not working for accruals", "RefUrl": "/notes/338865 "}, {"RefNumber": "310523", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "Shipment Load ID re-determination", "RefUrl": "/notes/310523 "}, {"RefNumber": "325399", "RefComponent": "IS-OIL-DS-TDP", "RefTitle": "Field KNVV-OIINEX . is not an input field", "RefUrl": "/notes/325399 "}, {"RefNumber": "326895", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Open hours check fails for Time Frames", "RefUrl": "/notes/326895 "}, {"RefNumber": "326861", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Update termination in SAPLOIIU", "RefUrl": "/notes/326861 "}, {"RefNumber": "318688", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Errors in Tank ID handing in VA01/VA02", "RefUrl": "/notes/318688 "}, {"RefNumber": "319796", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "Shipment Load ID valid-to < valid from date", "RefUrl": "/notes/319796 "}, {"RefNumber": "316516", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "VL01/VL02/ME32/ME33 errors because OIA05/OIA06 unsorted", "RefUrl": "/notes/316516 "}, {"RefNumber": "311456", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Short dump creating contract with quantity schedule", "RefUrl": "/notes/311456 "}, {"RefNumber": "302311", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Unable to Use Valuation Class in LIA Acct. Determination", "RefUrl": "/notes/302311 "}, {"RefNumber": "318966", "RefComponent": "IS-OIL-DS-BDRP", "RefTitle": "Function OII_GET_SOLD_TO_FOR_SHIP_TO short dumps", "RefUrl": "/notes/318966 "}, {"RefNumber": "316902", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "Close TD BOM component according to main item", "RefUrl": "/notes/316902 "}, {"RefNumber": "324180", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "Abend in VA02 after notes 316902 and 322765", "RefUrl": "/notes/324180 "}, {"RefNumber": "318607", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "Wrong excise duty posting to accounting", "RefUrl": "/notes/318607 "}, {"RefNumber": "310158", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "TD-Master User Screen Settings not customizable", "RefUrl": "/notes/310158 "}, {"RefNumber": "316386", "RefComponent": "IS-OIL-DS-BDRP", "RefTitle": "CALL_FUNCTION_CONFLICT_LENG", "RefUrl": "/notes/316386 "}, {"RefNumber": "338202", "RefComponent": "IS-OIL-DS-MRN", "RefTitle": "Characteristic derivation PSPID -> OIFPBL does not work", "RefUrl": "/notes/338202 "}, {"RefNumber": "336109", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "Short dump due to formula & average fee condition type", "RefUrl": "/notes/336109 "}, {"RefNumber": "325472", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "Incorrect rebate scale basis after running SDBONTO2", "RefUrl": "/notes/325472 "}, {"RefNumber": "336664", "RefComponent": "IS-OIL-DS-HPM", "RefTitle": "IS-Oil: Quantity conversion error in rebate agreement", "RefUrl": "/notes/336664 "}, {"RefNumber": "318560", "RefComponent": "IS-OIL-DS-HPM", "RefTitle": "IS-Oil: Error during automatic order creation against GR", "RefUrl": "/notes/318560 "}, {"RefNumber": "334920", "RefComponent": "IS-OIL-DS-BDRP", "RefTitle": "Problem in Archiving of Customer without BDRP data", "RefUrl": "/notes/334920 "}, {"RefNumber": "317519", "RefComponent": "IS-OIL-BC", "RefTitle": "IS-OIL: Additional Info - LCP 39-44 incl. 4.0B", "RefUrl": "/notes/317519 "}, {"RefNumber": "329203", "RefComponent": "IS-OIL-DS-HPM", "RefTitle": "IS-OIL: Material Master / ALE / oil specific data", "RefUrl": "/notes/329203 "}, {"RefNumber": "325052", "RefComponent": "IS-OIL-DS-TDP", "RefTitle": "IS-Oil: Sales - Automatic update of Excise Duty values", "RefUrl": "/notes/325052 "}, {"RefNumber": "300611", "RefComponent": "IS-OIL-DS-HPM", "RefTitle": "IS-Oil: Minor corrections hydrocarbon inventory management", "RefUrl": "/notes/300611 "}, {"RefNumber": "333978", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "duplicates in netting document list for exchange", "RefUrl": "/notes/333978 "}, {"RefNumber": "157975", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Not possible to maintain posting keys NTB,NTK...", "RefUrl": "/notes/157975 "}, {"RefNumber": "318839", "RefComponent": "IS-OIL-DS-MCO", "RefTitle": "Handling Type in Sales Order Display Screen 8025", "RefUrl": "/notes/318839 "}, {"RefNumber": "333273", "RefComponent": "IS-OIL-DS-TSW", "RefTitle": "TSW Modifications to fix Batch/Valuations", "RefUrl": "/notes/333273 "}, {"RefNumber": "332993", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "Formula Price calculation in relation to quantity", "RefUrl": "/notes/332993 "}, {"RefNumber": "328086", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "Short dump due to negative net price in contract display", "RefUrl": "/notes/328086 "}, {"RefNumber": "332989", "RefComponent": "IS-OIL-DS-TSW", "RefTitle": "Update of nomination failed", "RefUrl": "/notes/332989 "}, {"RefNumber": "316998", "RefComponent": "IS-OIL-DS-BDRP", "RefTitle": "ROIIP-RCBLK,DOBLK not update db & SCP blank screen", "RefUrl": "/notes/316998 "}, {"RefNumber": "332092", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "LIA does not transfer Trading partner to FI", "RefUrl": "/notes/332092 "}, {"RefNumber": "98534", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Abend O1 544 in TD Delivery Confirmation", "RefUrl": "/notes/98534 "}, {"RefNumber": "331304", "RefComponent": "IS-OIL-DS-MCO", "RefTitle": "Error displaying Ext. Dtls. in Goods Issue Docs.", "RefUrl": "/notes/331304 "}, {"RefNumber": "332287", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "S3VBRKWR: SQL error 4031 when accessing table OICQ7", "RefUrl": "/notes/332287 "}, {"RefNumber": "201437", "RefComponent": "IS-OIL-DS-TDP", "RefTitle": "IS-OIL: Avoid program dump when exceptions are raised", "RefUrl": "/notes/201437 "}, {"RefNumber": "328204", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "GR reversals missing in movements-based netting", "RefUrl": "/notes/328204 "}, {"RefNumber": "330214", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Opening balance on exchange statement missing", "RefUrl": "/notes/330214 "}, {"RefNumber": "330194", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Exchange statement stops printing", "RefUrl": "/notes/330194 "}, {"RefNumber": "329466", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Fee details can not be processed at goods receipt", "RefUrl": "/notes/329466 "}, {"RefNumber": "329200", "RefComponent": "IS-OIL-DS-BDRP", "RefTitle": "Data Archiving Problem : OII_METER_CUSTOMER_INDEX_GET", "RefUrl": "/notes/329200 "}, {"RefNumber": "328266", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "ASTM tolerance checks ignored during STSI IDOC processing", "RefUrl": "/notes/328266 "}, {"RefNumber": "156599", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "TD Document item quantity assign. missing O9540", "RefUrl": "/notes/156599 "}, {"RefNumber": "107344", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "TD partner-specific output determination", "RefUrl": "/notes/107344 "}, {"RefNumber": "328151", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Netting abends saying accounting doc not found", "RefUrl": "/notes/328151 "}, {"RefNumber": "327863", "RefComponent": "IS-OIL-DS-HPM", "RefTitle": "table parameters missing for call RV_DELIVERY_ADD etc.", "RefUrl": "/notes/327863 "}, {"RefNumber": "324334", "RefComponent": "IS-OIL-DS-MRN", "RefTitle": "F4 Help for projects within business location", "RefUrl": "/notes/324334 "}, {"RefNumber": "309533", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "Performance problems in differential invoice", "RefUrl": "/notes/309533 "}, {"RefNumber": "200957", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Error in handling of EXIT_SAPLOIKS_001", "RefUrl": "/notes/200957 "}, {"RefNumber": "300298", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "Multiple Change pointers generated for OILLID", "RefUrl": "/notes/300298 "}, {"RefNumber": "302327", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "IS OIL OUTPUT DETERMINATION", "RefUrl": "/notes/302327 "}, {"RefNumber": "327242", "RefComponent": "IS-OIL-DS-BDRP", "RefTitle": "Data Archiving Problem", "RefUrl": "/notes/327242 "}, {"RefNumber": "193407", "RefComponent": "IS-OIL-DS-BDRP", "RefTitle": "OII_ARCHIVE_CHECK_CUST_SUBOBJS not found release 31H", "RefUrl": "/notes/193407 "}, {"RefNumber": "321691", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "Incorrect amount in Inv.verif. with GR cancellations", "RefUrl": "/notes/321691 "}, {"RefNumber": "171871", "RefComponent": "IS-OIL-DS-BDRP", "RefTitle": "IS-Oil BDRP Sales Hours not read", "RefUrl": "/notes/171871 "}, {"RefNumber": "323181", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "Update termination in shipment creation", "RefUrl": "/notes/323181 "}, {"RefNumber": "325179", "RefComponent": "IS-OIL-DS-MCO", "RefTitle": "Short dump in display sales order : VA03", "RefUrl": "/notes/325179 "}, {"RefNumber": "324518", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "Renaming customer exit function to oil namespace", "RefUrl": "/notes/324518 "}, {"RefNumber": "322646", "RefComponent": "IS-OIL-DS-TSW", "RefTitle": "Ticket delivery date < Ordr delivery date", "RefUrl": "/notes/322646 "}, {"RefNumber": "325412", "RefComponent": "IS-OIL-DS-TDP", "RefTitle": "IS-OIL:Material movement with n lines, account mod", "RefUrl": "/notes/325412 "}, {"RefNumber": "324964", "RefComponent": "IS-OIL-DS-MRN", "RefTitle": "Classification for business location", "RefUrl": "/notes/324964 "}, {"RefNumber": "324806", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Reversal Documents reported in Vertex with the Wrong Sign", "RefUrl": "/notes/324806 "}, {"RefNumber": "321681", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "XXX", "RefUrl": "/notes/321681 "}, {"RefNumber": "322720", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Impossible to display quantity schedule in contract", "RefUrl": "/notes/322720 "}, {"RefNumber": "321161", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Exchange statement: MM reversal quantity missing", "RefUrl": "/notes/321161 "}, {"RefNumber": "316979", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "Divide by zero error during invoice verification", "RefUrl": "/notes/316979 "}, {"RefNumber": "318349", "RefComponent": "IS-OIL-DS-TDP", "RefTitle": "IS-OIL: ED Revaluation / Post Difference not possible", "RefUrl": "/notes/318349 "}, {"RefNumber": "321836", "RefComponent": "IS-OIL-DS-TDP", "RefTitle": "IS-OIL: Tracking of tax exemption licenses", "RefUrl": "/notes/321836 "}, {"RefNumber": "314379", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "Shortdump in S3VBRKWR and increase performance", "RefUrl": "/notes/314379 "}, {"RefNumber": "321127", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Condition base value not moved in routine 409", "RefUrl": "/notes/321127 "}, {"RefNumber": "316218", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "Incorrect account postings in GR with multiples POs", "RefUrl": "/notes/316218 "}, {"RefNumber": "318202", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "IV: doubled items and fixed amount conditions", "RefUrl": "/notes/318202 "}, {"RefNumber": "320810", "RefComponent": "IS-OIL-DS-HPM", "RefTitle": "IS-Oil: Incorrect weight/volume in batch split main item", "RefUrl": "/notes/320810 "}, {"RefNumber": "320098", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "LIA movement in wrong exchange statement section", "RefUrl": "/notes/320098 "}, {"RefNumber": "314967", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Saving of return order terminates", "RefUrl": "/notes/314967 "}, {"RefNumber": "320192", "RefComponent": "IS-OIL-DS-HPM", "RefTitle": "IS-Oil: VL02, two HPM screens appears during batch input", "RefUrl": "/notes/320192 "}, {"RefNumber": "203077", "RefComponent": "IS-OIL-DS-BDRP", "RefTitle": "GUI Status OIIPBLCT missing", "RefUrl": "/notes/203077 "}, {"RefNumber": "317951", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Error message when creating Intercompany invoice", "RefUrl": "/notes/317951 "}, {"RefNumber": "180596", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "Sales tax incorrect on differential invoice creation", "RefUrl": "/notes/180596 "}, {"RefNumber": "319203", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "To avoid ASTM pop-up during batch input.", "RefUrl": "/notes/319203 "}, {"RefNumber": "318652", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "IV ignores GR cancelation for items with split conditions", "RefUrl": "/notes/318652 "}, {"RefNumber": "214996", "RefComponent": "IS-OIL-DS-MCO", "RefTitle": "VA03 - Handling Type field Invisible in Screen 8025", "RefUrl": "/notes/214996 "}, {"RefNumber": "318351", "RefComponent": "IS-OIL-DS-HPM", "RefTitle": "IS-OIL: Incorrect quantity in rebate agreement", "RefUrl": "/notes/318351 "}, {"RefNumber": "318436", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "Short dump in invoice verification", "RefUrl": "/notes/318436 "}, {"RefNumber": "318434", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "QS Messages customizable by table T160M", "RefUrl": "/notes/318434 "}, {"RefNumber": "315433", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Mov.based Netting errors for cancelled invoice ver.", "RefUrl": "/notes/315433 "}, {"RefNumber": "314232", "RefComponent": "IS-OIL-DS-HPM", "RefTitle": "IS-OIL: Update termination when saving delivery", "RefUrl": "/notes/314232 "}, {"RefNumber": "316708", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "IDOC Control record incorrect in IDOC_INPUT_OILDVA", "RefUrl": "/notes/316708 "}, {"RefNumber": "316839", "RefComponent": "IS-OIL-DS-TSW", "RefTitle": "RD report - handling of RDCONV = 0", "RefUrl": "/notes/316839 "}, {"RefNumber": "311935", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Goods receipt: calculation of base product price", "RefUrl": "/notes/311935 "}, {"RefNumber": "310851", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "Incorrect Quantity for BOM Header in Load Bulk on Activation", "RefUrl": "/notes/310851 "}, {"RefNumber": "310631", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "Error with Prior to Load ;PTL Not scheduled for load.", "RefUrl": "/notes/310631 "}, {"RefNumber": "213939", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "MR21 posts inconsistent FI-documents with no items", "RefUrl": "/notes/213939 "}, {"RefNumber": "143233", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "TD missing gain; add tracking; ASTM +/- qty; no OIGSM", "RefUrl": "/notes/143233 "}, {"RefNumber": "310788", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "No provision to extend Idoc OILDVA01 Driver/Veh", "RefUrl": "/notes/310788 "}, {"RefNumber": "310427", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "Brazil: Cancellation of documents from TD-transfers", "RefUrl": "/notes/310427 "}, {"RefNumber": "310287", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Wrong default for date/ time on the time frame screen", "RefUrl": "/notes/310287 "}, {"RefNumber": "307732", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "TD Automatic document item quantity assignment", "RefUrl": "/notes/307732 "}, {"RefNumber": "306559", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Inconsistent date used in order item create", "RefUrl": "/notes/306559 "}, {"RefNumber": "309618", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "IS-OIL Output determination for Delivery Confirmation", "RefUrl": "/notes/309618 "}, {"RefNumber": "308026", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Incorrect Txn Curr  Amount Displayed on Netting Proposal", "RefUrl": "/notes/308026 "}, {"RefNumber": "199746", "RefComponent": "IS-OIL-DS-MCO", "RefTitle": "Transaction termination VI200. Item status is missing", "RefUrl": "/notes/199746 "}, {"RefNumber": "306295", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "Shipment number selection based on vehicle details", "RefUrl": "/notes/306295 "}, {"RefNumber": "305918", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "Air buoyancy indicator handling via STSI Load Confirmation", "RefUrl": "/notes/305918 "}, {"RefNumber": "305704", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "OIK03 table entry not deleted after manual POST GI", "RefUrl": "/notes/305704 "}, {"RefNumber": "305891", "RefComponent": "IS-OIL-DS-MCO", "RefTitle": "VA01 allows order type which is not allowed for this SA", "RefUrl": "/notes/305891 "}, {"RefNumber": "203605", "RefComponent": "IS-OIL-DS-HPM", "RefTitle": "IS-Oil: Low performance when reading sales contracts", "RefUrl": "/notes/203605 "}, {"RefNumber": "304626", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Bad performance of ERS fee processing", "RefUrl": "/notes/304626 "}, {"RefNumber": "305064", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Variable PO doc. type in purch. assignment", "RefUrl": "/notes/305064 "}, {"RefNumber": "303797", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Euro conversion for fee rates in invoice verification", "RefUrl": "/notes/303797 "}, {"RefNumber": "303827", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "Enable Group Conditions 4 and 5 in IS-OIL 4.0B", "RefUrl": "/notes/303827 "}, {"RefNumber": "303471", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "LID Flowlog report display incorrect", "RefUrl": "/notes/303471 "}, {"RefNumber": "301021", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Inconsistent date used in order item create", "RefUrl": "/notes/301021 "}, {"RefNumber": "301385", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "Population of ship-to in TAS shipment IDoc", "RefUrl": "/notes/301385 "}, {"RefNumber": "303220", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Tank ID defaulting in orders incorrect on change", "RefUrl": "/notes/303220 "}, {"RefNumber": "157768", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "Incorrect subtotal calculation type with F&A condn", "RefUrl": "/notes/157768 "}, {"RefNumber": "303089", "RefComponent": "IS-OIL-DS-HPM", "RefTitle": "IS-Oil: Update termination when saving dlv. with oil BoM", "RefUrl": "/notes/303089 "}, {"RefNumber": "303128", "RefComponent": "IS-OIL-DS-HPM", "RefTitle": "IS-Oil: AUM posting with incorrect plant", "RefUrl": "/notes/303128 "}, {"RefNumber": "302746", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "Error in Creation of Idoc OILORD", "RefUrl": "/notes/302746 "}, {"RefNumber": "301601", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "Part.Conf.STO; Tolerance gain STO; Batch in interf.; uexit", "RefUrl": "/notes/301601 "}, {"RefNumber": "300647", "RefComponent": "IS-OIL-DS-TSW", "RefTitle": "Mode of transport indicator not enabled for rail", "RefUrl": "/notes/300647 "}, {"RefNumber": "217379", "RefComponent": "IS-OIL-DS-HPM", "RefTitle": "IS-Oil: incorrect quantity in GR for replenishment del.", "RefUrl": "/notes/217379 "}, {"RefNumber": "301014", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Incorrect TPP check in IDOC_OUTPUT_OILTPI50", "RefUrl": "/notes/301014 "}, {"RefNumber": "198627", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Incorrect shipping point in O4PO", "RefUrl": "/notes/198627 "}, {"RefNumber": "300869", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Split Indicator required in SD&MM Documents", "RefUrl": "/notes/300869 "}, {"RefNumber": "300151", "RefComponent": "IS-OIL-DS-MCO", "RefTitle": "Hold data not enabled in IS-Oil overview screen", "RefUrl": "/notes/300151 "}, {"RefNumber": "215984", "RefComponent": "IS-OIL-DS-BDRP", "RefTitle": "Minor errors on Plant Site Control Parameters", "RefUrl": "/notes/215984 "}, {"RefNumber": "300849", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "ABAP ROIKPGIS", "RefUrl": "/notes/300849 "}, {"RefNumber": "300374", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "Actual goods issue date in TAS interface (pick-up)", "RefUrl": "/notes/300374 "}, {"RefNumber": "300406", "RefComponent": "IS-OIL-DS-TDP", "RefTitle": "IS-OIL: Batch split / ED license not copied to mat. doc.", "RefUrl": "/notes/300406 "}, {"RefNumber": "300199", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Tables OIKLIDR/OIK01 become inconsistent for shipments", "RefUrl": "/notes/300199 "}, {"RefNumber": "213813", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Create sales order with reference to invoice", "RefUrl": "/notes/213813 "}, {"RefNumber": "217126", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "Parallel OILLDD inbound processing", "RefUrl": "/notes/217126 "}, {"RefNumber": "216067", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Additional customer exits", "RefUrl": "/notes/216067 "}, {"RefNumber": "214794", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "QS error O1017 not customizable by table T160M", "RefUrl": "/notes/214794 "}, {"RefNumber": "216249", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Status messages not returned to TPI", "RefUrl": "/notes/216249 "}, {"RefNumber": "215068", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Movement based netting corrections", "RefUrl": "/notes/215068 "}, {"RefNumber": "216894", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "Object Key of change pointer (OILLID-OIGV) is wrong ?", "RefUrl": "/notes/216894 "}, {"RefNumber": "216522", "RefComponent": "IS-OIL-DS-MCO", "RefTitle": "Screen sequence control entry:T185 SAPMV45B OIDX...", "RefUrl": "/notes/216522 "}, {"RefNumber": "211071", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Order time frame maintenenace not consistent", "RefUrl": "/notes/211071 "}, {"RefNumber": "216079", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "Low performance when creat. deliveries for big contracts", "RefUrl": "/notes/216079 "}, {"RefNumber": "216413", "RefComponent": "IS-OIL-DS-HPM", "RefTitle": "IS-OIL: Incorrect quantity proposed in sales order", "RefUrl": "/notes/216413 "}, {"RefNumber": "216045", "RefComponent": "IS-OIL-DS-HPM", "RefTitle": "IS-OIL: Incorrect packing status", "RefUrl": "/notes/216045 "}, {"RefNumber": "201309", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Delivery cost credit memo error when crossing year", "RefUrl": "/notes/201309 "}, {"RefNumber": "197834", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Split excise duty fails for stock transfer order", "RefUrl": "/notes/197834 "}, {"RefNumber": "215522", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "Tables for sec. level analysis were not sorted", "RefUrl": "/notes/215522 "}, {"RefNumber": "215460", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "TD Purchase Assignment Scr: F4 help on PO docs", "RefUrl": "/notes/215460 "}, {"RefNumber": "215636", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "New fee pricing date is not stored in the PO", "RefUrl": "/notes/215636 "}, {"RefNumber": "215633", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Checking base product for a line that has been deleted.", "RefUrl": "/notes/215633 "}, {"RefNumber": "215705", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "Quotation Integrity Check - Date Range Requirement", "RefUrl": "/notes/215705 "}, {"RefNumber": "216007", "RefComponent": "IS-OIL-DS-MCO", "RefTitle": "NO CURRENCY/EX<PERSON>ANGE RATE FIELD ON DYNPRO SAPMV45A 4301", "RefUrl": "/notes/216007 "}, {"RefNumber": "210687", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "No VBTYP in TAS relevancy check for contract creation", "RefUrl": "/notes/210687 "}, {"RefNumber": "214650", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "OILORD02: Segment E1EDP01-ACTION not filled correctly", "RefUrl": "/notes/214650 "}, {"RefNumber": "214666", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "MR8M/MR08 M8607 - Error when reversing invoice", "RefUrl": "/notes/214666 "}, {"RefNumber": "215139", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Order time frame check vs OTWS incorrect", "RefUrl": "/notes/215139 "}, {"RefNumber": "214346", "RefComponent": "IS-OIL-DS-MCO", "RefTitle": "Fields available for input in Display Sales Order", "RefUrl": "/notes/214346 "}, {"RefNumber": "213030", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "PO History leads to wrong IR Fee Document due to OIAFE", "RefUrl": "/notes/213030 "}, {"RefNumber": "212492", "RefComponent": "IS-OIL-DS-MCO", "RefTitle": "Item proposal and item redetermination", "RefUrl": "/notes/212492 "}, {"RefNumber": "207609", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "VI11, VI12: Selection of TD shipments don't work properly", "RefUrl": "/notes/207609 "}, {"RefNumber": "213579", "RefComponent": "IS-OIL-DS-MCO", "RefTitle": "Valuation type not displayed as label", "RefUrl": "/notes/213579 "}, {"RefNumber": "213049", "RefComponent": "IS-OIL-DS-TSW", "RefTitle": "TSW: Partner Role Detail Screen Tabcontrol size", "RefUrl": "/notes/213049 "}, {"RefNumber": "212670", "RefComponent": "IS-OIL-DS-HPM", "RefTitle": "IS-Oil: Error in goods issue for oil BoMs", "RefUrl": "/notes/212670 "}, {"RefNumber": "213093", "RefComponent": "IS-OIL-DS-MCO", "RefTitle": "Manual entry of payment term in order header level", "RefUrl": "/notes/213093 "}, {"RefNumber": "210030", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "MR1M:Wrong Update of planned cost with diffrent Inv.Cycle", "RefUrl": "/notes/210030 "}, {"RefNumber": "209999", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "SMOD entries OIGMEN01 and OIGMEN02 not available", "RefUrl": "/notes/209999 "}, {"RefNumber": "205300", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Fees by outline agreement in MR01 does not work", "RefUrl": "/notes/205300 "}, {"RefNumber": "209689", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "Check Load ID Determination", "RefUrl": "/notes/209689 "}, {"RefNumber": "209771", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "Wrong invoice amount for account assigned PO items", "RefUrl": "/notes/209771 "}, {"RefNumber": "175037", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Lia account determination with valuation class", "RefUrl": "/notes/175037 "}, {"RefNumber": "205759", "RefComponent": "IS-OIL-DS-HPM", "RefTitle": "IS-OIL: Incorrect error message in goods issue log", "RefUrl": "/notes/205759 "}, {"RefNumber": "197769", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Statistics update for value items", "RefUrl": "/notes/197769 "}, {"RefNumber": "205561", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "Refer mat.doc. for correction GR; No check val.typ", "RefUrl": "/notes/205561 "}, {"RefNumber": "155914", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Printing exchange statements using RSNAST00", "RefUrl": "/notes/155914 "}, {"RefNumber": "204741", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "shortdump when recal. subtotals for rebates", "RefUrl": "/notes/204741 "}, {"RefNumber": "196890", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "Ship-to information not pulled into contracts", "RefUrl": "/notes/196890 "}, {"RefNumber": "198229", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "TD Deleting partners from shipment", "RefUrl": "/notes/198229 "}, {"RefNumber": "204005", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "Load ID customer screen handling", "RefUrl": "/notes/204005 "}, {"RefNumber": "203283", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "Termination in TAS interface for pick-up's", "RefUrl": "/notes/203283 "}, {"RefNumber": "196306", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "Error handling in TAS FM IDOC_INPUT_OILLDD", "RefUrl": "/notes/196306 "}, {"RefNumber": "202626", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Function Group OIFT missing", "RefUrl": "/notes/202626 "}, {"RefNumber": "193130", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "TPI: Trip planning", "RefUrl": "/notes/193130 "}, {"RefNumber": "187195", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Field overflow in LIA transaction", "RefUrl": "/notes/187195 "}, {"RefNumber": "201305", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Euro conversion for fee rates in goods receipt", "RefUrl": "/notes/201305 "}, {"RefNumber": "201913", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Function call conflict in OIK_TPI_ORDERS_CREATE", "RefUrl": "/notes/201913 "}, {"RefNumber": "201383", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "PO Creation - Field Status Problem", "RefUrl": "/notes/201383 "}, {"RefNumber": "201282", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "Receive (new) batch at loading; ASTM error after mvmt o brd", "RefUrl": "/notes/201282 "}, {"RefNumber": "197738", "RefComponent": "IS-OIL-DS-TDP", "RefTitle": "Brazil: Tax code customizing table changes", "RefUrl": "/notes/197738 "}, {"RefNumber": "201122", "RefComponent": "IS-OIL-DS-HPM", "RefTitle": "IS-Oil: Incorrect goods receipt qty.in 2step transfer", "RefUrl": "/notes/201122 "}, {"RefNumber": "195018", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "TD Underdelivery tolerance 0% not working", "RefUrl": "/notes/195018 "}, {"RefNumber": "194386", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "TD Archiving DBIF_RSQL_INVALID_CURSOR", "RefUrl": "/notes/194386 "}, {"RefNumber": "193559", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "TD Rounding Errors in OIGI_CREATE_SHIPMENT_RFC fix", "RefUrl": "/notes/193559 "}, {"RefNumber": "192304", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "TD Rounding Errors in OIGI_CREATE_SHIPMENT_RFC", "RefUrl": "/notes/192304 "}, {"RefNumber": "200816", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "TD allocation of res. to shipment => short dump", "RefUrl": "/notes/200816 "}, {"RefNumber": "199541", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Endless loop in IDOC_OUTPUT_OILTPI50", "RefUrl": "/notes/199541 "}, {"RefNumber": "200316", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Error in incompletion for mandatory Tank ID", "RefUrl": "/notes/200316 "}, {"RefNumber": "198515", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "Shortdump when assigning contract to item in sales order", "RefUrl": "/notes/198515 "}, {"RefNumber": "195009", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Goods receipt with fees into neg. stock errors", "RefUrl": "/notes/195009 "}, {"RefNumber": "199325", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "Error message OE 018 during TAS pickup IDOC processing", "RefUrl": "/notes/199325 "}, {"RefNumber": "193381", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Netting does not generate a BTCI session", "RefUrl": "/notes/193381 "}, {"RefNumber": "196075", "RefComponent": "IS-OIL-DS", "RefTitle": "Oil-specific messages missing in class VF", "RefUrl": "/notes/196075 "}, {"RefNumber": "198085", "RefComponent": "IS-OIL-DS", "RefTitle": "HP upgrade merge error", "RefUrl": "/notes/198085 "}, {"RefNumber": "197182", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Quantity missing from OILSHI01 if CPLID not '1'", "RefUrl": "/notes/197182 "}, {"RefNumber": "197770", "RefComponent": "IS-OIL-DS-MRN", "RefTitle": "Ship-to OIFWE in CO-PA not filled for billing trsf.", "RefUrl": "/notes/197770 "}, {"RefNumber": "197908", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "Wrong date calculation in F&A Pricing after 1999.", "RefUrl": "/notes/197908 "}, {"RefNumber": "197470", "RefComponent": "IS-OIL-DS-BDRP", "RefTitle": "Consignment stock fields missing after 4.0B SP1", "RefUrl": "/notes/197470 "}, {"RefNumber": "154538", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "Error in holiday determination in F&A pricing", "RefUrl": "/notes/154538 "}, {"RefNumber": "156403", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "Bug in routine XKOMV_BEWERTEN", "RefUrl": "/notes/156403 "}, {"RefNumber": "157569", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "Errors in repricing in invoice verification", "RefUrl": "/notes/157569 "}, {"RefNumber": "170287", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "Rounding errors in invoice verification", "RefUrl": "/notes/170287 "}, {"RefNumber": "173205", "RefComponent": "IS-OIL-DS-MCO", "RefTitle": "Selected BOL is not moved into screen field", "RefUrl": "/notes/173205 "}, {"RefNumber": "193023", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Handling of ETAs for planned shipments", "RefUrl": "/notes/193023 "}, {"RefNumber": "196052", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Error OC605 occurs incorrectly", "RefUrl": "/notes/196052 "}, {"RefNumber": "194327", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Incorrect OIK37 updates via TPI", "RefUrl": "/notes/194327 "}, {"RefNumber": "189648", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Tank ID not available on Shipment Outbound IDocs", "RefUrl": "/notes/189648 "}, {"RefNumber": "164252", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "TD Delete NAST entries for delete shipments", "RefUrl": "/notes/164252 "}, {"RefNumber": "172009", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "Delete shipment + Nast entry", "RefUrl": "/notes/172009 "}, {"RefNumber": "195707", "RefComponent": "IS-OIL-DS", "RefTitle": "Slow response to delivery create", "RefUrl": "/notes/195707 "}, {"RefNumber": "181530", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Purchase assignment: pass goods issue date", "RefUrl": "/notes/181530 "}, {"RefNumber": "194642", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Not possible to post LIA for 0 valued material", "RefUrl": "/notes/194642 "}, {"RefNumber": "193231", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "ROIKFCL1 functionality missing after Service Pack", "RefUrl": "/notes/193231 "}, {"RefNumber": "193968", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "O4F1 : Document Weight not assigned to Shipment.", "RefUrl": "/notes/193968 "}, {"RefNumber": "193001", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "LIA accounting document missing", "RefUrl": "/notes/193001 "}, {"RefNumber": "193770", "RefComponent": "IS-OIL-DS-HPM", "RefTitle": "IS-OIL: Oil BoM header cannot be deleted", "RefUrl": "/notes/193770 "}, {"RefNumber": "191705", "RefComponent": "IS-OIL-DS-TDP", "RefTitle": "TAX REVALUATION WITH MULTIPLE VALUATIONS AND TRANSIT-STOCK", "RefUrl": "/notes/191705 "}, {"RefNumber": "193194", "RefComponent": "IS-OIL-DS-TDP", "RefTitle": "IS-OIL: Goods Receipt Repricing / Excise Duty posting", "RefUrl": "/notes/193194 "}, {"RefNumber": "193619", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Exchange number missing in netting index table", "RefUrl": "/notes/193619 "}, {"RefNumber": "192527", "RefComponent": "IS-OIL-DS-BDRP", "RefTitle": "No reference to EXIT_SAPLOIIQ_001", "RefUrl": "/notes/192527 "}, {"RefNumber": "191923", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "Incorrect statistical conditions in differential invoice", "RefUrl": "/notes/191923 "}, {"RefNumber": "193225", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "F&A Time UoM routine 004,005 Year Begnining Problem", "RefUrl": "/notes/193225 "}, {"RefNumber": "193227", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "Abort A222 in OIK_TAS_DATA_POST", "RefUrl": "/notes/193227 "}, {"RefNumber": "192273", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Collected changes for TPI", "RefUrl": "/notes/192273 "}, {"RefNumber": "189441", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Performance in billing / unexpected invoice split", "RefUrl": "/notes/189441 "}, {"RefNumber": "192520", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Contracts assigned to an exchange agreement", "RefUrl": "/notes/192520 "}, {"RefNumber": "176858", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "Differential invoice creation in batch processing", "RefUrl": "/notes/176858 "}, {"RefNumber": "186491", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "Search help shows only shipment cost relevant shpm.", "RefUrl": "/notes/186491 "}, {"RefNumber": "185231", "RefComponent": "IS-OIL-DS-HPM", "RefTitle": "IS-OIL: Incorrect delivered quantity in schedule lines", "RefUrl": "/notes/185231 "}, {"RefNumber": "191696", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "OILSHI01 IDoc keeps status 64 after processing", "RefUrl": "/notes/191696 "}, {"RefNumber": "192046", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "No KNA1 change pointer for updates to SCP OTWS", "RefUrl": "/notes/192046 "}, {"RefNumber": "191151", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Missing TPI functionality", "RefUrl": "/notes/191151 "}, {"RefNumber": "191429", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "PBL OTWS entries missing from TPI Location IDoc", "RefUrl": "/notes/191429 "}, {"RefNumber": "187744", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "TRUOM; ELIKZandLoss; checkMM-LVORM; Missing GI; SetTDaction", "RefUrl": "/notes/187744 "}, {"RefNumber": "184012", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Movement based netting shows wrong sign", "RefUrl": "/notes/184012 "}, {"RefNumber": "188270", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Fees for goods receipt missing in fee history", "RefUrl": "/notes/188270 "}, {"RefNumber": "191413", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Receipts are missing in movement based netting", "RefUrl": "/notes/191413 "}, {"RefNumber": "191154", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "TPI IDocs fail with mixed user defaults", "RefUrl": "/notes/191154 "}, {"RefNumber": "191149", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Abort A222 in OIK_TAS_DATA_POST", "RefUrl": "/notes/191149 "}, {"RefNumber": "190248", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Generic price reference plant valuation", "RefUrl": "/notes/190248 "}, {"RefNumber": "147813", "RefComponent": "IS-OIL-DS-MCO", "RefTitle": "ME31: external details not copied", "RefUrl": "/notes/147813 "}, {"RefNumber": "183761", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "No fee redetermination in MM contract or order", "RefUrl": "/notes/183761 "}, {"RefNumber": "188640", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Contract update terminates if no fee exists", "RefUrl": "/notes/188640 "}, {"RefNumber": "187013", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Base Location not consistent between Sales and Purchase", "RefUrl": "/notes/187013 "}, {"RefNumber": "186341", "RefComponent": "IS-OIL-DS-TSW", "RefTitle": "Max inventory/Min inventory related WL entries", "RefUrl": "/notes/186341 "}, {"RefNumber": "186408", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "Parallel OILLDD inbound processing", "RefUrl": "/notes/186408 "}, {"RefNumber": "175799", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Correction Trip planning", "RefUrl": "/notes/175799 "}, {"RefNumber": "186151", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Shipment inbound process update task", "RefUrl": "/notes/186151 "}, {"RefNumber": "185895", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Exchange statement: UoM of F&A condition missing", "RefUrl": "/notes/185895 "}, {"RefNumber": "184532", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "Error mess V1227 New pric/ formulae on MM side", "RefUrl": "/notes/184532 "}, {"RefNumber": "185685", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Bad performance/Short dump for ROIAMMA3", "RefUrl": "/notes/185685 "}, {"RefNumber": "179868", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "OTWS data not available in views v_oiiotwbl, v_oiiotwkn", "RefUrl": "/notes/179868 "}, {"RefNumber": "184640", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Fees by PO picks up previous years GR", "RefUrl": "/notes/184640 "}, {"RefNumber": "181711", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "Wrong data initialisation in OIK_TD_SHIPMENT_LOAD_PREPARE", "RefUrl": "/notes/181711 "}, {"RefNumber": "185245", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Custmr mastr recrd:Distrbtn of deltd partnr funct.", "RefUrl": "/notes/185245 "}, {"RefNumber": "185062", "RefComponent": "IS-OIL-DS-TSW", "RefTitle": "Unexpected Error-messages after Planning Engine run", "RefUrl": "/notes/185062 "}, {"RefNumber": "185010", "RefComponent": "IS-OIL-DS", "RefTitle": "MM_EKKO: archiving run excludes contracts", "RefUrl": "/notes/185010 "}, {"RefNumber": "175059", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "Post parked documents: message 00348", "RefUrl": "/notes/175059 "}, {"RefNumber": "181457", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "Load ID assigned with Valid-to = 00:00:0000", "RefUrl": "/notes/181457 "}, {"RefNumber": "181955", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Exchange statement for cancelled invoice", "RefUrl": "/notes/181955 "}, {"RefNumber": "169833", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "MAP: Error in Pricing date during GR repricing", "RefUrl": "/notes/169833 "}, {"RefNumber": "181409", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Negative fees incorrect in movement based netting", "RefUrl": "/notes/181409 "}, {"RefNumber": "179653", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Short dump in invoice list processing", "RefUrl": "/notes/179653 "}, {"RefNumber": "178046", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Movement based netting issues", "RefUrl": "/notes/178046 "}, {"RefNumber": "166548", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Exg. statement: reversal printed in wrong period", "RefUrl": "/notes/166548 "}, {"RefNumber": "184295", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Abort message OE 559 in Order Create", "RefUrl": "/notes/184295 "}, {"RefNumber": "184148", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "SOEK000331: Wrong conversion between sales and base UoM", "RefUrl": "/notes/184148 "}, {"RefNumber": "183374", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Timeframe function missing on screen 8024", "RefUrl": "/notes/183374 "}, {"RefNumber": "183036", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "Message OE 224: STSI calloff can not be deleted manually", "RefUrl": "/notes/183036 "}, {"RefNumber": "184209", "RefComponent": "IS-OIL-DS-TSW", "RefTitle": "TSW: Missing R/3 Documents in Rundown possible", "RefUrl": "/notes/184209 "}, {"RefNumber": "179813", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "Sales UOM conversion wrong in MC for condition record", "RefUrl": "/notes/179813 "}, {"RefNumber": "181437", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Language conversion incorrect for menu option", "RefUrl": "/notes/181437 "}, {"RefNumber": "168313", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Customizing control for message O1 023", "RefUrl": "/notes/168313 "}, {"RefNumber": "177535", "RefComponent": "IS-OIL-DS-BDRP", "RefTitle": "Cannot cancel from Plant SCP sub-screen", "RefUrl": "/notes/177535 "}, {"RefNumber": "171364", "RefComponent": "IS-OIL-DS-HPM", "RefTitle": "IS-OIL: Incorrect Qty in base UoM in Inv.verification", "RefUrl": "/notes/171364 "}, {"RefNumber": "175463", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Wrong quantities in CO-PA after cancelling invoice", "RefUrl": "/notes/175463 "}, {"RefNumber": "176108", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "LOAD_NO_SPACE_FOR_TABLE for RBDAPP01 logical msg OILLDD", "RefUrl": "/notes/176108 "}, {"RefNumber": "177949", "RefComponent": "IS-OIL-DS-TDP", "RefTitle": "Taxes Brail Oil: New exception  tables sales,puchase,t-fer", "RefUrl": "/notes/177949 "}, {"RefNumber": "175342", "RefComponent": "IS-OIL-DS-TDP", "RefTitle": "Brazil taxes: Error on Sales return - ROB", "RefUrl": "/notes/175342 "}, {"RefNumber": "178164", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "Screen 200 missing in program", "RefUrl": "/notes/178164 "}, {"RefNumber": "176988", "RefComponent": "IS-OIL-DS-TSW", "RefTitle": "Update termination in worklist engine", "RefUrl": "/notes/176988 "}, {"RefNumber": "176785", "RefComponent": "IS-OIL-DS-BDRP", "RefTitle": "Missing SCP component check", "RefUrl": "/notes/176785 "}, {"RefNumber": "175494", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Customer Subscreen not accessible in SAPMOIGV", "RefUrl": "/notes/175494 "}, {"RefNumber": "176741", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Return data from user screen overwritten (OIGD)", "RefUrl": "/notes/176741 "}, {"RefNumber": "176589", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "PBL lookup for TPS parameters fails for OILSH01", "RefUrl": "/notes/176589 "}, {"RefNumber": "175140", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "Abend in batch billing", "RefUrl": "/notes/175140 "}, {"RefNumber": "175501", "RefComponent": "IS-OIL-DS", "RefTitle": "IS-OIL: Customer master ALE / KNVV records overwritten", "RefUrl": "/notes/175501 "}, {"RefNumber": "173484", "RefComponent": "IS-OIL-DS-BDRP", "RefTitle": "SOC Assignment in SD document not working", "RefUrl": "/notes/173484 "}, {"RefNumber": "173532", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "Order/Contr.: TAS data not filled after delete/cre. item", "RefUrl": "/notes/173532 "}, {"RefNumber": "173715", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "TD customer function for vehicle overload checks", "RefUrl": "/notes/173715 "}, {"RefNumber": "173720", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "TD Tables are not archived with archiving object OIG_SHPMNT", "RefUrl": "/notes/173720 "}, {"RefNumber": "171086", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "TPI: Collective corrections after Acceptance test", "RefUrl": "/notes/171086 "}, {"RefNumber": "172759", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "output determination: header or detail(scheduling)", "RefUrl": "/notes/172759 "}, {"RefNumber": "153222", "RefComponent": "IS-OIL-DS-MCO", "RefTitle": "external details at stock transfer order", "RefUrl": "/notes/153222 "}, {"RefNumber": "173079", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "Zero billing date is filled in the VKDFS table", "RefUrl": "/notes/173079 "}, {"RefNumber": "170871", "RefComponent": "IS-OIL-DS-TDP", "RefTitle": "IS-OIL BRAZILIAN TAX SOLUTION FOR TRANSFER 833/835", "RefUrl": "/notes/170871 "}, {"RefNumber": "172681", "RefComponent": "IS-OIL-DS-TSW", "RefTitle": "TSW : correction for marine transport system in Nomination", "RefUrl": "/notes/172681 "}, {"RefNumber": "171736", "RefComponent": "IS-OIL-DS-TDP", "RefTitle": "Nota fiscal with multiple items / Brazil Oil", "RefUrl": "/notes/171736 "}, {"RefNumber": "173139", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "TPI: Fix for Time frame settings handling", "RefUrl": "/notes/173139 "}, {"RefNumber": "167025", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "Dynamic F&A repository selection screen", "RefUrl": "/notes/167025 "}, {"RefNumber": "170968", "RefComponent": "IS-OIL-DS-MCO", "RefTitle": "Fieldsselection texts missing in RM07MMAT", "RefUrl": "/notes/170968 "}, {"RefNumber": "172738", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Functionality for Time frame settings at IMG", "RefUrl": "/notes/172738 "}, {"RefNumber": "172412", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "Confirm status; Error message E752; Gain/loss mvmt o b", "RefUrl": "/notes/172412 "}, {"RefNumber": "172527", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "ASTM Calculation doesn't work correctly with ASTM Interface", "RefUrl": "/notes/172527 "}, {"RefNumber": "171037", "RefComponent": "IS-OIL-DS-TDP", "RefTitle": "Billing cancellation error.", "RefUrl": "/notes/171037 "}, {"RefNumber": "172063", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Mb netting: Wrong invoice cycle determined", "RefUrl": "/notes/172063 "}, {"RefNumber": "171053", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "Posting dates in one period; Zero lines in loading", "RefUrl": "/notes/171053 "}, {"RefNumber": "169295", "RefComponent": "IS-OIL-DS-HPM", "RefTitle": "IS-OIL: Wrong pick qty. after TRX VL16", "RefUrl": "/notes/169295 "}, {"RefNumber": "168765", "RefComponent": "IS-OIL-DS-HPM", "RefTitle": "IS-OIL: Fields not updated for oil BoM header", "RefUrl": "/notes/168765 "}, {"RefNumber": "170668", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Bills of material in exchanges", "RefUrl": "/notes/170668 "}, {"RefNumber": "171065", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "ITA: Collective corrections after Acceptance test", "RefUrl": "/notes/171065 "}, {"RefNumber": "169283", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "ITA: TAS index for GI not deleted if delivery deleted", "RefUrl": "/notes/169283 "}, {"RefNumber": "170803", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "ITA: Outbound IDoc OILORD02: E1EDP01-ANTLF not filled", "RefUrl": "/notes/170803 "}, {"RefNumber": "170822", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "ITA: Missing copy of some client independent object items", "RefUrl": "/notes/170822 "}, {"RefNumber": "170926", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "OIK37 number ranges maintenance", "RefUrl": "/notes/170926 "}, {"RefNumber": "169228", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "ITA: Error during DELETE OIKLIDR", "RefUrl": "/notes/169228 "}, {"RefNumber": "151721", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Fee edit/copy control fields are missing", "RefUrl": "/notes/151721 "}, {"RefNumber": "156700", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Wrong UoM in automatically created purchase order", "RefUrl": "/notes/156700 "}, {"RefNumber": "170314", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Error in redetermination of netting statement", "RefUrl": "/notes/170314 "}, {"RefNumber": "170066", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Fixed value fees are incorrect in the invoice", "RefUrl": "/notes/170066 "}, {"RefNumber": "102306", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Wrong values in statistics update", "RefUrl": "/notes/102306 "}, {"RefNumber": "169160", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "TD Corrections in SVPs: 3.1H/SVP3 4.0B/SVP1", "RefUrl": "/notes/169160 "}, {"RefNumber": "168381", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Billing index entries missing after RVV05IVB", "RefUrl": "/notes/168381 "}, {"RefNumber": "166169", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Fees lost during cancellation of billing document", "RefUrl": "/notes/166169 "}, {"RefNumber": "165871", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "TD Auto compartment planning with compatibilities", "RefUrl": "/notes/165871 "}, {"RefNumber": "163448", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Quantity schedule: Conversion error", "RefUrl": "/notes/163448 "}, {"RefNumber": "156697", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "If taxes are 0,accounting error in differential Inv", "RefUrl": "/notes/156697 "}, {"RefNumber": "150318", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Split excise duty may cause wrong IR postings", "RefUrl": "/notes/150318 "}, {"RefNumber": "158344", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "TD: Gain on 2-step trf. and GR to mult. store locations", "RefUrl": "/notes/158344 "}, {"RefNumber": "157801", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "TD Loss in transfer/STO and change of valuation type", "RefUrl": "/notes/157801 "}, {"RefNumber": "151361", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Missing exg. number on billing index for diff. inv.", "RefUrl": "/notes/151361 "}, {"RefNumber": "157494", "RefComponent": "IS-OIL-DS-MRN", "RefTitle": "Location ID in Credit note and Credit note request", "RefUrl": "/notes/157494 "}, {"RefNumber": "156993", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "TD User Exit 007 before creation of MM document", "RefUrl": "/notes/156993 "}, {"RefNumber": "157044", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "TD Exit from step-loop 00043 in scheduling", "RefUrl": "/notes/157044 "}, {"RefNumber": "156906", "RefComponent": "IS-OIL-DS-MRN", "RefTitle": "Abort on Location Archiving Deletion program", "RefUrl": "/notes/156906 "}, {"RefNumber": "152678", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "TD Cost sharing on document item level", "RefUrl": "/notes/152678 "}, {"RefNumber": "154790", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "TD Allow non-movement delivery items in TD shipments", "RefUrl": "/notes/154790 "}, {"RefNumber": "155337", "RefComponent": "IS-OIL-DS", "RefTitle": "IS-Oil: Material Master Screen Seqence Selection", "RefUrl": "/notes/155337 "}, {"RefNumber": "154200", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Bad runtime while MSEG-access in quantity schedule", "RefUrl": "/notes/154200 "}, {"RefNumber": "150951", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "TD Missing overload check in compartment allocation details", "RefUrl": "/notes/150951 "}, {"RefNumber": "153121", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "TD Scrolling SAPMOIGS3700; take over dates after user exit", "RefUrl": "/notes/153121 "}, {"RefNumber": "142981", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "TD ASTM and 0 qty; reason code; excise duty value", "RefUrl": "/notes/142981 "}, {"RefNumber": "150548", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "TD Errors in IMG for shipment costing", "RefUrl": "/notes/150548 "}, {"RefNumber": "143644", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "Error with UoM processing in TD Vehicle creation", "RefUrl": "/notes/143644 "}, {"RefNumber": "142387", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "TD Change proposal at load/delivery confirmation", "RefUrl": "/notes/142387 "}, {"RefNumber": "131501", "RefComponent": "TR-TM-IS", "RefTitle": "Termination with drilldown report call (hierarchy)", "RefUrl": "/notes/131501 "}, {"RefNumber": "111268", "RefComponent": "PP-REM-ADE", "RefTitle": "MF4A, MF4U: Display material documents, return", "RefUrl": "/notes/111268 "}, {"RefNumber": "98642", "RefComponent": "0 IS-OIL-BC", "RefTitle": "Order of IS-OIL notes 2.0d & 1.0d on R/3 3.1H (SP)", "RefUrl": "/notes/98642 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "IS-OIL", "From": "40B", "To": "40B", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}