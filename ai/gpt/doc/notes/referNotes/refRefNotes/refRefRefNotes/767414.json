{"Request": {"Number": "767414", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 2621, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "D", "_masterLanguage": "D", "_loadedLanguage": "D", "_machineTranslationLanguage": "E", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Acknowledge", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "If the quality of the machine translation is not sufficient for your purposes, click here to request a revised version.", "_type": "EVENT", "_visible": true}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": false, "url": ""}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000767414?language=E&token=BD4EC069092642934A18A47673EDF34D"}, "ShareByEmail": {"_label": "Share via Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000767414", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000767414/D/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "767414"}, "Type": {"_label": "Type", "value": "SAP-Hinweis"}, "Version": {"_label": "Version", "value": 41}, "Recency": {"_label": "Currentness", "key": "", "value": ""}, "Language": {"_label": "Language", "value": "German"}, "MasterLanguage": {"_label": "Original Language", "value": "German"}, "Category": {"_label": "Category", "value": "FAQ"}, "Priority": {"_label": "Priority", "value": "Recommendations/Additional Information"}, "Status": {"_label": "Release Status", "value": "In Process"}, "ReleasedOn": {"_label": "Released On", "value": "23.08.2020"}, "SAPComponentKey": {"_label": "Component", "value": "BC-DB-ORA"}, "SAPComponentKeyText": {"_label": "Component", "value": "Oracle"}, "SAPComponentPath": [{"_label": "Services &amp; Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs &amp; Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Basis Components", "value": "BC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Database Interface, Database Platforms", "value": "BC-DB", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-DB*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Oracle", "value": "BC-DB-ORA", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-DB-ORA*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "767414 - FAQ: Oracle Latches"}, "LongText": {"_label": "Description", "value": "<div id=\"DISCLAIMER\" class=\"sapMMsgStrip sapMMsgStripWarning sapUiSmallMarginTop sapUiMediumMarginBottom\"><div class=\"sapMMsgStripMessage\"><span dir=\"auto\" class=\"sapMText sapUiSelectable sapMTextMaxWidth\" style=\"text-align: left;\"><strong>ATTENTION!</strong> This is a machine-translated document that has not been reviewed for accuracy. To provide feedback regarding the machine translation, please <a style=\"color:var(--sapLinkColor);\" href=\"https://sapinsights.eu.qualtrics.com/jfe/form/SV_6nI2C1MOwEviVQG?NoteNumber=767414&TargetLanguage=EN&Component=BC-DB-ORA&SourceLanguage=DE&Priority=06\" target=\"_blank\">click here</a>.<br/>You can access the original document via the following link: <a style=\"color:var(--sapLinkColor);\" href=\"/notes/767414/D\" target=\"_blank\">/notes/767414/D</a>.</span></div></div><div><div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<ol>1. What is an Oracle Latch?</ol><ol>2. What is the mechanism of latch allocation?</ol><ol>3. How can I access latch information on Oracle side?</ol><ol>4. How can I access latch information on the R/3 side?</ol><ol>5. How can I tell if there is a latch issue?</ol><ol>6. How can I determine which latches are currently being waited for?</ol><ol>7. How can I determine the current latch holders?</ol><ol>8. How can I determine the most critical latches in total?</ol><ol>9. How can I determine objects that are related to the latch waits?</ol><ol>10. What are general causes and solutions for high latch wait times?</ol><ol>11. How can concrete latch waits be tuned?</ol><ol>12. Can latch deadlocks occur?</ol><ol><ol>13th How do I determine the essential latches contained in &quot;latch free&quot;?</ol></ol>\r\n<h3 data-toc-skip class=\"section\" id=\"Weitere Begriffe\">Other Terms</h3>\r\n<p>FAQ</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Ursache und Voraussetzungen\">Reason and Prerequisites</h3>\r\n<ol>1. What is an Oracle Latch?</ol>\r\n<p>              A latch is an Oracle internal lock on a lower level that protects the memory structures of the System Global Area (SGA) from simultaneous accesses.</p>\r\n<p>              Depending on the memory area, there is either a single parent latch or several child latches that protect subareas of the memory.</p>\r\n<ol>2. What is the mechanism of latch allocation?</ol>\r\n<p>              There are two different ways to request a latch: &quot;Willing to Wait&quot; and &quot;Immediate&quot;, with most latch requests based on &quot;Willing to Wait&quot;:</p>\r\n<ul>\r\n<li>Willing to Wait Mode</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>If the latch is currently in use, the system repeatedly checks in a loop whether the latch is now available (&quot;spinning&quot;). CPU is consumed during this loop. At the same time, NO wait event (also not &quot;latch free&quot;) is active. The number of loops is defined by the internal Oracle parameter _SPIN_COUNT. If only one CPU is available on the system, this step is omitted. Spinning makes sense because usually latches are kept very short and the requirement can be fulfilled in very quickly. In such a case, spinning can avoid a context switch that in turn is also resource-intensive.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>If the latch could not be allocated throughout the loops, the process falls asleep for a certain period of time. This time period is 0.01 second for the first time. During this sleep, the wait event &quot;latch free&quot; is active. As of Oracle 10g, &quot;important&quot; latches are explicitly specified by &quot;latch: &lt;latch_name>&quot;, while the less relevant latches are still logged as &quot;latch free&quot;.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>The above two steps are repeated until the latch can be allocated successfully. In this case, the sleep time is doubled with each second pass. The maximum sleep time is 2 seconds if no further latch is held, or 0.04 seconds if the process already holds another latch.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li>Immediate Mode</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>If the requested latch is already in use, all further child latches are immediately tested. If none of the child latches can be allocated, the system switches to Willing to Wait mode.</li>\r\n</ul>\r\n</ul>\r\n<ol>3. How can I access latch information on Oracle side?</ol>\r\n<p>              The following V$ views contain latch information:</p>\r\n<ul>\r\n<li>V$LATCH: Overview of Latch Waits Since System Startup</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>NAME: Name of the latch</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>GETS: Number of Willing to Wait Requests</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>MISSES: Number of Willing to Wait requests that could not allocate the latch on the first attempt</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>SPIN_GETS: Number of Willing to Wait requests that could allocate the latch during the first spinning without having to sleep</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>SLEEPS: Number of Willing to Wait requests where the process has fallen asleep at least once.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>SLEEP1: Number of Willing to Wait Requests with Exactly One Sleep</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>SLEEP2: Number of Willing to Wait Requests with Exactly Two Sleeps</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>SLEEP3: Number of Willing to Wait Requests with Exactly Three Sleeps</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>SLEEP4: Number of Willing to Wait Requests with Four or More Sleeps</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>IMMEDIATE_GETS: Number of Immediate Requests</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>IMMEDIATE_MISSES: Number of immediate requests that could not allocate the latch on the first attempt</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>WAIT_TIME: Accumulated sleep times for the latch (as of Oracle 9i)</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li>V$LATCHHOLDER: Overview of currently held latches</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>PID: Oracle PID &lt;opid> of the process holding the latch</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>SID: Oracle SID of the session holding the latch</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>NAME: Name of Latch Held</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li>V$LATCHNAME: Name overview of all latches</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>LATCH#: Latch Number</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>NAME: Name of the latch</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li>V$LATCH_MISSES: Number of Sleeps and Immediate Misses incl. Oracle Kernel Area</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>NWFAIL_COUNT: Number of Immediate Misses</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>SLEEP_COUNT: Number of Sleeps</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>LOCATION: Oracle kernel area that holds the requested latch</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li>V$LATCH_PARENT: Overview of Parent Latch Waits Since System Startup</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Fields as for V$LATCH</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li>V$LATCH_CHILDREN: Overview of Child Latch Waits Since System Startup</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>CHILD#: Number of Child Latch</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Otherwise as for V$LATCH</li>\r\n</ul>\r\n</ul>\r\n<ol>4. How can I access latch information on the R/3 side?</ol>\r\n<p>              For the basics of the Oracle performance analysis, see SAP Note 618868.</p>\r\n<ol>5. How can I tell if there is a latch issue?</ol>\r\n<p>              A latch problem is usually manifested by high &quot;latch free&quot; wait times. Therefore, during a wait event analysis in accordance with SAP Note 619188, you notice that the entire system or individual transactions are affected by &quot;latch free&quot; or &quot;latch: &lt;latch_name>&quot; waits, it is worth analyzing and optimizing the latch waits.</p>\r\n<ol>6. How can I determine which latches are currently being waited for?</ol>\r\n<p>              You can use the following command to determine which sessions are currently waiting for which latch:<br />SELECT<br />  W.SID<br />  L.NAME<br />FROM<br />  V$SESSION_WAIT W,<br />  V$LATCHNAME L<br />WHERE<br />  W.EVENT LIKE &#39;latch%&#39; AND<br />  W.P2 = L.LATCH# AND<br />  W.STATE = &#39;WAITING&#39;;</p>\r\n<ol>7. How can I determine the current latch holders?</ol>\r\n<p>              SAP Note 20071 contains options for determining the current latch holder using V$LATCHHOLDER or the column BLOCKING_SESSION from V$SESSION (Oracle >= 10g).</p>\r\n<ol>8. How can I determine the most critical latches in total?</ol>\r\n<p>              The overall critical latches can be determined based on the time that had to wait for the latch. This information is contained in the WAIT_TIME column in V$LATCH. The following statement provides you with the ten latches for which you had to wait the longest since the system was started:<br />SELECT * FROM (SELECT NAME, WAIT_TIME FROM V$LATCH<br />ORDER BY WAIT_TIME DESC) WHERE ROWNUM &lt;= 10;</p>\r\n<p>              Up to Oracle 8i, the column WAIT_TIME is not yet available. Instead, you must switch to the SLEEPS column.</p>\r\n<p>              It only makes sense to determine and tune the essential latches if there is actually a latch problem. It is normal for certain latches such as &quot;shared pool&quot; or &quot;library cache&quot; to be at the top of the list with significant wait times.</p>\r\n<ol>9. How can I determine objects that are related to the latch waits?</ol>\r\n<p>              As of Oracle 10g, the view V$ACTIVE_SESSION_HISTORY provides information about the last active wait situations and the affected objects. Refer to the relevant section in SAP Note 619188.</p>\r\n<p>              Latch waits are often the main concurrency waits. Therefore, candidates for latch problems as of Oracle 10g can also be determined using the following query:<br />SELECT * FROM<br />( SELECT<br />    ROUND(CONCURRENCY_WAIT_TIME / 1000000)<br />      &quot;CONCURRENCY WAIT TIME (S)&quot;,<br />    EXECUTIONS<br />    SQL_ID<br />    SQL_TEXT<br />  FROM<br />    V$SQLSTATS<br />  ORDER BY CONCURRENCY_WAIT_TIME DESC )<br />WHERE ROWNUM &lt;= 10;</p>\r\n<ol>10. What are general causes and solutions for high latch wait times?</ol>\r\n<p>              Typical reasons for high latch waits are:</p>\r\n<ul>\r\n<li>Very high access rates to certain memory resources</li>\r\n</ul>\r\n<p>           Reduce the load on the resources depending on the latch waits that occur as described below.</p>\r\n<ul>\r\n<li>CPU bottleneck</li>\r\n</ul>\r\n<p>           A CPU bottleneck on the database server can lead to massive latch waits if processes that hold critical latches are displaced from the CPU (context switch). In addition, spinning further exacerbates a CPU bottleneck. Therefore, check at operating system level or with transaction ST06/OS07 that there is no CPU bottleneck on the database server (see CPU rules from SAP Note 618868).</p>\r\n<ul>\r\n<li>Massive parallelization</li>\r\n</ul>\r\n<p>           If an action is parallelized too much on the R/3 side and therefore a large number of processes access the same memory areas at the same time, latch waits are very likely. As a result of spinning, the processes waiting for a latch consume a relatively large amount of CPU, which means that the processes that hold the latch are displaced from the CPU more quickly. As a result, the latch is kept longer than necessary and the situation worsens. Therefore, check whether reducing the parallelization of processes with similar database accesses reduces the latch waits and improves performance.</p>\r\n<ul>\r\n<li>Oracle Bugs</li>\r\n</ul>\r\n<p>           There are some Oracle bugs that can lead to long latch wait times. Some known bugs are listed below in the tuning tips on concrete latch waits. In any case, however, it makes sense to search for possible further bugs.</p>\r\n<ul>\r\n<li>Abandoned Network Connection</li>\r\n</ul>\r\n<p>           Refer to SAP Note 20071 and ensure that the latch problem is not triggered by terminated network connections.</p>\r\n<ul>\r\n<li>Events and Underscore Parameters</li>\r\n</ul>\r\n<p>           In individual cases, Oracle events or underscore parameters can be responsible for increased latching times. For example, setting event 10501 can lead to massive problems due to &quot;shared pool&quot; latches.<br />           Therefore, check whether there are settings that are not recommended by SAP or are no longer required with the used database release and remove them.</p>\r\n<ul>\r\n<li>Shared pool too large</li>\r\n</ul>\r\n<p>           In individual cases, a shared pool defined too large can cause latch wait situations on the shared pool (for example: &quot;library cache&quot; or &quot;row cache objects&quot; latch) because the Oracle internal administration structures are overwhelmed with the amount of information stored at the same time. Therefore, check whether the shared pool is configured significantly larger than recommended in SAP Note 789011. If so, consider reducing SHARED_POOL_SIZE. As a quick solution, the shared pool can be<br />ALTER SYSTEM FLUSH SHARED_POOL;<br />           be cleaned up. In V$SGASTAT, you can find the current sizes of the individual areas of the shared pool.</p>\r\n<ul>\r\n<li>Paging</li>\r\n</ul>\r\n<p>           Increased paging can also lead to latch problems. If a process has allocated a latch and must then first retrieve an associated memory area from the disk, it takes a correspondingly longer time until the latch is released again. As a result, latch wait situations are more likely.</p>\r\n<ul>\r\n<li>HP-UX: Process Scheduling</li>\r\n</ul>\r\n<p>           On HP-UX, critical latch situations can be amplified by the process scheduling mechanism that is active by default if processes in critical sections (that is, with a latch held) are displaced from the CPU. In this case, you can consider using the Oracle parameter HPUX_SCHED_NOAGE in accordance with Metalink document 217990.1 to reduce such evictions.</p>\r\n<ul>\r\n<li>Error situations such as ORA-04031 or ORA-07445</li>\r\n</ul>\r\n<p>           In individual cases, critical error situations such as ORA-04031 or ORA-07445 can also lead to latch waits. Therefore, check whether errors are logged in the alert log and/or whether Oracle trace files have been written in critical latch periods. If necessary, correct the error and check whether it also corrects the latch waits.</p>\r\n<ol>11. How can concrete latch waits be tuned?</ol>\r\n<p>              Depending on the latch that is being waited for, the following solutions are available:</p>\r\n<ul>\r\n<li>library cache / library cache pin</li>\r\n</ul>\r\n<p>           Wait-Event > = 10g: &quot;latch: library cache&quot; / &quot;latch: library cache pin&quot;<br />           Wait situations for the &quot;library cache&quot; and &quot;library cache pin&quot; latch usually occur in the SAP environment if a large number of identical SQL statements (with regard to bind variables) are executed in parallel.<br />           Therefore, avoid the massive parallel execution of a large number of similar SQL statements that differ only in the contents of their bind variables.<br />           To determine which SQL statements are responsible for the latch problems, first determine the hot child latches:<br />SELECT NAME, CHILD#, WAIT_TIME.<br />FROM V$LATCH_CHILDREN<br />WHERE NAME LIKE &#39;library cache%&#39;<br />ORDER BY WAIT_TIME DESC;<br />           A query on V$SQL can now be started for the child latches with the highest wait time:<br />SELECT * FROM<br />( SELECT EXECUTIONS, PARSE_CALLS, ADDRESS, SQL_TEXT<br />  FROM V$SQL<br />  WHERE CHILD_LATCH = &lt;child#><br />  ORDER BY EXECUTIONS DESC )<br />WHERE ROWNUM &lt;= 10;<br />           The system returns the ten SQL statements that were protected by the specified child latch and that were executed most frequently.<br />           In exceptional cases, it may make sense to increase the number of latches using the Oracle parameter _KGL_LATCH_COUNT. Depending on this parameter, the number of &quot;library cache&quot;, &quot;library cache pin&quot; and &quot;library cache pin allocation&quot; latches is defined according to the following formula:<br />#Latches = MIN(67, next_larger_prime_number(_KGL_LATCH_COUNT))<br />           As a final solution, you can also consider using equivalent SQL statements, but from a parse point of view (for example, by specifying a different comment/pseudo hint). As a result, the load is generally distributed more evenly among different child latches.<br />           Wait situations on the &quot;library cache&quot; latch can also occur in individual cases if a large number of SQL statements have to be parsed. Therefore, check the following points:</p>\r\n<ul>\r\n<ul>\r\n<li>In accordance with SAP Note 618868, check whether the shared pool is sufficiently dimensioned.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Avoid the massive use of REPARSE or SUBSTITUTE VALUES hints in accordance with SAP Note 129385 because these hints mean that significantly more statements must be parsed.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>You can also create statistics, shared pool flushes, and DDL statements (for example: TRUNCATE), a new parsing of statements is forced. Therefore, it should also be checked to what extent such actions occur massively.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li>shared pool</li>\r\n</ul>\r\n<p>           Wait event >= 10g: &quot;latch: shared pool&quot;<br />           See the information for the &quot;library cache&quot; latch above.</p>\r\n<ul>\r\n<li>Cache Buffers Chains</li>\r\n</ul>\r\n<p>           Wait event >= 10g: &quot;latch: cache buffer chains&quot;<br />           The cause of &quot;cache buffers chains&quot; latchwaits are usually hot blocks, that is, blocks that are accessed very frequently. In most cases, the high access frequency is triggered by SQL statements with an unnecessarily large number of buffer GETs. Go to the determination of the<br />           triggering SQL statements as follows:</p>\r\n<ul>\r\n<ul>\r\n<li>Determine the SQL_IDs of those SQL statements with the highest number of buffer gets in the period in question, for example, using the script ASH_Aggregation from SAP Note 1438410 (EVENT = &#39;latch: cache buffers chains&#39;, AGGREGATE_BY = &#39;SQL_ID&#39;).</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Determine more detailed information about the critical returned SQL_IDs, for example, using SQL_SQL_ID_DataCollector from SAP Note 1438410).</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Determine the segments with the highest number of buffers in the period in question, for example, using the script SegmentStatistics_TopSegmentsForStatisticPerAWRInterval from SAP Note 1438410 (STAT_NAME = &#39;Logical Reads&#39;).</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Based on this information, optimize the critical SQL statements. See also SAP Note 766349.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li>cache buffers lru chain</li>\r\n</ul>\r\n<p>           Wait event >= 10g: &quot;latch: cache buffers lru chain&quot;<br />           The triggers are usually expensive SQL statements. Therefore, perform an SQL optimization in accordance with Note 766349.<br />           Also check whether the DBWR has performance problems when writing the dirty blocks (for example, using a wait event analysis in accordance with SAP Note 619188).<br />           Also make sure that the size of the Oracle buffer pool is not too small (see Information from SAP Note 618868).<br />           The number of &quot;cache buffers lru chain&quot; latches can be adjusted using the parameter _DB_BLOCK_LRU_LATCHES. However, this parameter should only be set explicitly in justified individual cases.</p>\r\n<ul>\r\n<li>Cache Buffer Handles</li>\r\n</ul>\r\n<p>           Wait event >= 10g: &quot;latch: cache buffer handles&quot;<br />           With Oracle 8.1. 7, the bug described in Note 400698 for serializations on the &quot;cache buffer handles&quot; latch can be.</p>\r\n<ul>\r\n<li>enqueue hash chains</li>\r\n</ul>\r\n<p>           Wait event >= 10g: &quot;latch: enqueue hash chains&quot;<br />           If wait situations occur for &quot;enqueue hash chains&quot; latches in the context of deadlock dumps, refer to Note 596420 and also ensure that there are no bottlenecks when creating the dump file (I/O, mount options of the file system, and so on).</p>\r\n<ul>\r\n<li>Archive Control</li>\r\n</ul>\r\n<p>           Wait situations for the &quot;archive control&quot; latch are usually a follow-on problem of archiver stuck situations. Therefore, check in accordance with SAP Note 391 whether an archiver stuck has occurred.</p>\r\n<ul>\r\n<li>undo global data</li>\r\n</ul>\r\n<p>           The &quot;undo global data&quot; latch is required for accesses to undo data. Increased wait times on this latch are usually based on expensive SQL statements, which in turn access consistent read images of the data in the undo segments due to open changes. In this case, optimize the expensive SQL statements and ensure that there are no large number of open changes (see also SAP Note 913247).</p>\r\n<ul>\r\n<li>Query Server Process</li>\r\n</ul>\r\n<p>           The &quot;query server process&quot; latch is held if new slaves are created as part of parallel execution. For more information, see Note 651060.</p>\r\n<ul>\r\n<li>simulator lru latch</li>\r\n</ul>\r\n<p>           Significant wait times for &quot;simulator lru latch&quot; are often related to expensive SQL statements that perform a large number of buffer gets. Therefore, first check whether such accesses exist and can be optimized.<br />           Also during the creation of fixed object statistics using DBMS_STATS.GATHER_FIXED_OBJECTS_STATS (or &quot;brconnect -f oradict_stats&quot;), long wait situations may occur for the simulator lru latch. In particular, this affects systems with a large buffer pool because, in this case, the table X$KCBSH becomes quite large and the creation of statistics on this table takes a corresponding amount of time. During the statistics compilation, &quot;simulator lru latch&quot; problems may occur.<br />           As a workaround, you can also set the parameter DB_CACHE_ADVICE to OFF, which deactivates the LRU simulation in the buffer pool. Note, however, that this measure no longer collects data in V$DB_CACHE_ADVICE (SAP Note 617416).</p>\r\n<ul>\r\n<li>resmgr:resource group CPU method</li>\r\n</ul>\r\n<p>           The &quot;resmgr:resource group CPU method&quot; latch under &quot;latch free&quot; is often triggered by bug 10623249 with older versions. For more information, see SAP Note 1579946. With Oracle 19, bug 29030927 may be responsible. You can use &quot;ALTER SYSTEM SET RESOURCE_MANAGER_PLAN = &#39;&#39;&quot; to deactivate the resource manager as a workaround.</p>\r\n<ol>12. Can latch deadlocks occur?</ol>\r\n<p>              The Oracle implementation usually ensures that no latch deadlocks can occur. However, in the case of bugs, deadlocks are conceivable. In such a case, the transaction terminates with ORA-00600 [504]. If you encounter such an error and cannot find a suitable SAP Note, open a customer message.</p>\r\n<ol>13th How do I determine the essential latches contained in &quot;latch free&quot;?</ol>\r\n<p>              You can use the script Locks_NonPopularLatches_ASH from SAP Note 1438410 to determine the most important latch waits combined under &quot;latch free&quot; on the basis of the Oracle Active Session History.</p>\r\n</div></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Owner                                                                                    ", "Value": "<PERSON> (D030484)"}, {"Key": "Processor                                                                                          ", "Value": "<PERSON> (D030484)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000767414/D"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "MIME Type", "URL": "URL"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document references", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "URL"}, "Items": [{"RefNumber": "964344", "RefComponent": "BC-DB-ORA", "RefTitle": "FAQ: Oracle Mutexe", "RefUrl": "/notes/964344"}, {"RefNumber": "913247", "RefComponent": "BC-DB-ORA", "RefTitle": "Performance issues due to open changes", "RefUrl": "/notes/913247"}, {"RefNumber": "869006", "RefComponent": "BC-DB-ORA", "RefTitle": "Composite SAP Note: ORA-04031", "RefUrl": "/notes/869006"}, {"RefNumber": "789011", "RefComponent": "BC-DB-ORA", "RefTitle": "FAQ: Oracle memory areas", "RefUrl": "/notes/789011"}, {"RefNumber": "766349", "RefComponent": "BC-DB-ORA", "RefTitle": "FAQ: Oracle SQL optimization", "RefUrl": "/notes/766349"}, {"RefNumber": "745639", "RefComponent": "BC-DB-ORA", "RefTitle": "FAQ: <PERSON> Enqueues", "RefUrl": "/notes/745639"}, {"RefNumber": "712624", "RefComponent": "BC-DB-ORA", "RefTitle": "High CPU consumption by Oracle", "RefUrl": "/notes/712624"}, {"RefNumber": "651060", "RefComponent": "BC-DB-ORA", "RefTitle": "FAQ: <PERSON><PERSON><PERSON> execution of Oracle", "RefUrl": "/notes/651060"}, {"RefNumber": "619188", "RefComponent": "BC-DB-ORA", "RefTitle": "FAQ: Oacle wait events", "RefUrl": "/notes/619188"}, {"RefNumber": "618868", "RefComponent": "BC-DB-ORA", "RefTitle": "FAQ: Oracle Performance", "RefUrl": "/notes/618868"}, {"RefNumber": "596420", "RefComponent": "BC-DB-ORA", "RefTitle": "System standstill during deadlock (ORA-60)", "RefUrl": "/notes/596420"}, {"RefNumber": "488583", "RefComponent": "BC-DB-ORA", "RefTitle": "Database hangs: Waits for cache buffer chain latch", "RefUrl": "/notes/488583"}, {"RefNumber": "449136", "RefComponent": "BC-DB-ORA", "RefTitle": "Latch Contention \\&quot;cache buffers chains\\&quot; in 8.1", "RefUrl": "/notes/449136"}, {"RefNumber": "400698", "RefComponent": "BC-DB-ORA", "RefTitle": "ORA-4031 Memory leak on Oracle 8.1.7", "RefUrl": "/notes/400698"}, {"RefNumber": "391", "RefComponent": "BC-DB-ORA", "RefTitle": "<PERSON><PERSON>", "RefUrl": "/notes/391"}, {"RefNumber": "20071", "RefComponent": "BC-DB-ORA", "RefTitle": "Permanent Lock After Connection Termination", "RefUrl": "/notes/20071"}, {"RefNumber": "1611285", "RefComponent": "SCM-FRE-DIF", "RefTitle": "FRP Sequence 1: DIF access performance improvement", "RefUrl": "/notes/1611285"}, {"RefNumber": "1579946", "RefComponent": "BC-DB-ORA", "RefTitle": "Oracle Resource Manager with 11.2 and higher", "RefUrl": "/notes/1579946"}, {"RefNumber": "1438410", "RefComponent": "BC-DB-ORA", "RefTitle": "SQL Script Collection for Oracle", "RefUrl": "/notes/1438410"}, {"RefNumber": "1020225", "RefComponent": "BC-DB-ORA", "RefTitle": "High number of &#39;cache buffer chains&#39; in Oracle ********", "RefUrl": "/notes/1020225"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "URL"}, "Items": [{"RefNumber": "1438410", "RefComponent": "BC-DB-ORA", "RefTitle": "SQL Script Collection for Oracle", "RefUrl": "/notes/1438410 "}, {"RefNumber": "618868", "RefComponent": "BC-DB-ORA", "RefTitle": "FAQ: Oracle Performance", "RefUrl": "/notes/618868 "}, {"RefNumber": "745639", "RefComponent": "BC-DB-ORA", "RefTitle": "FAQ: <PERSON> Enqueues", "RefUrl": "/notes/745639 "}, {"RefNumber": "619188", "RefComponent": "BC-DB-ORA", "RefTitle": "FAQ: Oacle wait events", "RefUrl": "/notes/619188 "}, {"RefNumber": "789011", "RefComponent": "BC-DB-ORA", "RefTitle": "FAQ: Oracle memory areas", "RefUrl": "/notes/789011 "}, {"RefNumber": "1579946", "RefComponent": "BC-DB-ORA", "RefTitle": "Oracle Resource Manager with 11.2 and higher", "RefUrl": "/notes/1579946 "}, {"RefNumber": "712624", "RefComponent": "BC-DB-ORA", "RefTitle": "High CPU consumption by Oracle", "RefUrl": "/notes/712624 "}, {"RefNumber": "766349", "RefComponent": "BC-DB-ORA", "RefTitle": "FAQ: Oracle SQL optimization", "RefUrl": "/notes/766349 "}, {"RefNumber": "869006", "RefComponent": "BC-DB-ORA", "RefTitle": "Composite SAP Note: ORA-04031", "RefUrl": "/notes/869006 "}, {"RefNumber": "1611285", "RefComponent": "SCM-FRE-DIF", "RefTitle": "FRP Sequence 1: DIF access performance improvement", "RefUrl": "/notes/1611285 "}, {"RefNumber": "391", "RefComponent": "BC-DB-ORA", "RefTitle": "<PERSON><PERSON>", "RefUrl": "/notes/391 "}, {"RefNumber": "964344", "RefComponent": "BC-DB-ORA", "RefTitle": "FAQ: Oracle Mutexe", "RefUrl": "/notes/964344 "}, {"RefNumber": "596420", "RefComponent": "BC-DB-ORA", "RefTitle": "System standstill during deadlock (ORA-60)", "RefUrl": "/notes/596420 "}, {"RefNumber": "1020225", "RefComponent": "BC-DB-ORA", "RefTitle": "High number of &#39;cache buffer chains&#39; in Oracle ********", "RefUrl": "/notes/1020225 "}, {"RefNumber": "20071", "RefComponent": "BC-DB-ORA", "RefTitle": "Permanent Lock After Connection Termination", "RefUrl": "/notes/20071 "}, {"RefNumber": "913247", "RefComponent": "BC-DB-ORA", "RefTitle": "Performance issues due to open changes", "RefUrl": "/notes/913247 "}, {"RefNumber": "651060", "RefComponent": "BC-DB-ORA", "RefTitle": "FAQ: <PERSON><PERSON><PERSON> execution of Oracle", "RefUrl": "/notes/651060 "}, {"RefNumber": "488583", "RefComponent": "BC-DB-ORA", "RefTitle": "Database hangs: Waits for cache buffer chain latch", "RefUrl": "/notes/488583 "}, {"RefNumber": "400698", "RefComponent": "BC-DB-ORA", "RefTitle": "ORA-4031 Memory leak on Oracle 8.1.7", "RefUrl": "/notes/400698 "}, {"RefNumber": "449136", "RefComponent": "BC-DB-ORA", "RefTitle": "Latch Contention \\&quot;cache buffers chains\\&quot; in 8.1", "RefUrl": "/notes/449136 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And the following"}, "Items": []}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software-Komponentenversion", "SupportPackage": "Support Package", "URL": "URL"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Packages and Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "URL"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "URL"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about correction instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisite", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisite", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "URL", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated as helpful", "Helpful-Yes": "0 persons"}, "RatingQuality": {"_label": "Quality Assessment"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document eliminates side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "URL"}, "Items": []}, "SideEffectsSolving": {"_label": "This document causes side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "URL"}, "Items": []}}, "DisclaimerForMachineTranslation": {"Header": "ATTENTION!", "Message": "This is a machine-translated document that has not been reviewed for accuracy.", "Survey": "To provide feedback regarding the machine translation, please <a href=\"https://sapinsights.eu.qualtrics.com/jfe/form/SV_6nI2C1MOwEviVQG?NoteNumber=767414&TargetLanguage=EN&Component=BC-DB-ORA&SourceLanguage=DE&Priority=06\" target=\"_blank\">click here</a>.", "Request": "If the quality of the machine translation is not sufficient for your purposes, click here to request a revised version.", "Origin": "You can access the original document via the following link: <a href=\"/notes/767414/D\" target=\"_blank\">/notes/767414/D</a>."}}}}