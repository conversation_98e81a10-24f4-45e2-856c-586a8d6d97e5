{"Request": {"Number": "183311", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 866, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000000930212017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000183311?language=E&token=039143E3755102CD80ED891F2C418A38"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000183311", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000183311/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "183311"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 115}, "Recency": {"_label": "Recency", "key": "", "value": ""}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Program error"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "29.08.2012"}, "SAPComponentKey": {"_label": "Component", "value": "BC-DB-DB2"}, "SAPComponentKeyText": {"_label": "Component", "value": "DB2 for z/OS"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Basis Components", "value": "BC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Database Interface, Database Platforms", "value": "BC-DB", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-DB*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "DB2 for z/OS", "value": "BC-DB-DB2", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-DB-DB2*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "183311 - DB2/390: Automated PTF Check"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p></p> <b>Latest News</b><br /> <p>As of <B>7-Jul-2009</B> a new version of RSDB2FIX is available as an attachment (SAPK34POSN.CAR) to this note. <B>Attention:</B> This transport should only be applied for Releases &lt;= 4.6D. For Releases &gt;= 6.10 apply the highest Basis Support Package listed in this note.<br /></p> <b>Short Description</b><br /> <p>It can be time-consuming to check whether all required Program Temporary Fixes (PTF) listed in SAP Notes 81737 (APAR List) and 364109 (DB2 Put Levels) have been applied to a z/OS system. SAP provides a tool that automatically performs the following steps:</p> <OL>1. Determination of the release and/or version of all software components (SAP System, SAP kernel, z/OS system, and DB2 subsystem)</OL> <OL>2. Extraction of all required PTFs from SAP Notes 81737 and 364109.</OL> <OL>3. Determination of the status of all required PTFs within the z/OS system</OL> <OL>4. Output of missing PTFs and/or Function Module IDs (FMID)</OL> <p></p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>DB2/390, OS/390, MVS, AIX, z/OS<br />service, fix, level, patch, PTF, APAR, ++APAR, APARFIX,<br />automated, check, RSDB2FIX, saposcol, rfcoscol, oss, SAPNet, oss1<br /></p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>Please check whether the following requirements are satisfied for the involved SAP systems:</p> <OL>1. SAP Release 4.0B or higher</OL> <OL>2. z/OS Version 1.4 or higher</OL> <OL>3. DB2-z/OS Version 7.1 or higher</OL> <p></p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><b>Technical Details</b><br /> <p>The technical details of the PTF check are illustrated in the graphic below. <BR/> <br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Check system&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; Target system<br />|------------------------|&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; |------------------------|<br />|&#x00A0;&#x00A0; Application server&#x00A0;&#x00A0; |&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; |&#x00A0;&#x00A0; Application server&#x00A0;&#x00A0; |<br />|------------------------|&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; |------------------------|<br />|&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;|&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; ____|&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;SAP&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; |<br />|&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;|&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;/&#x00A0;&#x00A0;&#x00A0;&#x00A0;|------------------------|<br />|&#x00A0;&#x00A0;|------------------|&#x00A0;&#x00A0;|&#x00A0;&#x00A0;&#x00A0;&#x00A0; R/3&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; |<br />|&#x00A0;&#x00A0;|&#x00A0;&#x00A0;&#x00A0;&#x00A0; RSDB2FIX&#x00A0;&#x00A0;&#x00A0;&#x00A0; |&#x00A0;&#x00A0;|&#x00A0;&#x00A0; connect&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;ICLI/emb.SQL<br />|&#x00A0;&#x00A0;|------------------|&#x00A0;&#x00A0;|&#x00A0;&#x00A0;&#x00A0;&#x00A0; /&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; |<br />|&#x00A0;&#x00A0;| 1) read&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;|_______/&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;|------------------------|<br />|&#x00A0;&#x00A0;|&#x00A0;&#x00A0;SAP Note 81737&#x00A0;&#x00A0;|&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; \\&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;|&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;z/OS host&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; |<br />|&#x00A0;&#x00A0;|&#x00A0;&#x00A0;SAP Note 364109 |&#x00A0;&#x00A0;|&#x00A0;&#x00A0; TCP/IP&#x00A0;&#x00A0;&#x00A0;&#x00A0;|------------------------|<br />|&#x00A0;&#x00A0;| 2) connect&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; |&#x00A0;&#x00A0;|&#x00A0;&#x00A0; emb.SQL&#x00A0;&#x00A0; |&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;|<br />|&#x00A0;&#x00A0;| 3) check&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; |&#x00A0;&#x00A0;|&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;\\&#x00A0;&#x00A0;&#x00A0;&#x00A0;| |--------|&#x00A0;&#x00A0;&#x00A0;&#x00A0;|------| |<br />|&#x00A0;&#x00A0;| 4) output&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;|&#x00A0;&#x00A0;|&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; \\___|_|rfcoscol|____|SMP/E-| |<br />|&#x00A0;&#x00A0;|------------------|&#x00A0;&#x00A0;|&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; | | SAPCL&#x00A0;&#x00A0;|&#x00A0;&#x00A0;&#x00A0;&#x00A0;|GIMAPI| |<br />|&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;|&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; | |--------|&#x00A0;&#x00A0;&#x00A0;&#x00A0;|------| |<br />|&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;|&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; |&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;|<br />|------------------------|&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; |------------------------|<br /><br />The automated PTF check is based on the assumption that customers administer all z/OS software components using IBM's System Modification Program Extended (SMP/E). This program keeps a record of all changes (for example, PTFs) to function modules in the Consolidated Software Inventory (CSI).<br />SMP/E also provides an interface (GIMAPI) that can be called by application programs to query the contents of the CSI. For more information on SMP/E, see the IBM manuals \"SMP/E Reference\" and \"SMP/E User's Guide\".<br />SAP's rfcoscol (SAP Release 4.6D - 6.40) and SAPCL (= stored procedure that substitutes rfcoscol starting with SAP Release 7.0) are able to call the GIMAPI interface and forward SMP/E data to the connected SAP System. The PTF check itself is performed by report RSDB2FIX and runs on an SAP System that is called check system in this note. The system to be checked is referred to as target system. The target and check system have to be identical for SAP Release 7.0 and higher. For older SAP Releases (&lt;= 6.40) it is also possible to choose one single SAP System to be the check system for all other SAP installations at the customer site.<br />SAP Notes 81737 and 364109 are formatted such that they can be used directly as input for the check report (SAP Note 364109&#x00A0;&#x00A0;provides a list of PTFs related to the latest DB2 put level). Both SAP Notes need to be downloaded to your PC and are read by RSDB2FIX.<br />Alternatively, the check tool RSDB2FIX is also able to automatically retrieve the most recent versions of SAP Notes 81737 and 364109 directly from SAPNet (also called OSS = Online Service System) if a valid RFC connection exists. In that case, a download of these notes to your PC is not required.<br />The target system's kernel release as well as the versions of the DB2 and z/OS software used are determined. This information combined with the uploaded PTF information is subsequently used to retrieve a list of required PTFs and FMIDs.<br />The kernel release is used to determine which ICLI-PTF is required.<br />Finally, RSDB2FIX queries the status of each required FMID and PTF employing the connection to GIMAPI and SMP/E. PTFs that are not found with status \"applied\" or \"superseded\" are listed as \"missing\" in the output.<br />APARs that are not checked because none of the associated FMIDs can be located in the given CSIs are also written to the output.<br /></p> <b>Setup</b><br /> <p>Before starting the PTF check you have to set up your environment as follows:</p> <OL>1. Check the SMP/E settings.</OL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;The PTF check tool can only check entries in CSI. You have to make sure that these CSI entries reflect exactly the status of the software that is actually running. <OL>2. Select one of your SAP Systems as the check system to perform the PTF check.</OL> <OL>3. Update the check report RSDB2FIX in the check system:</OL> <UL><LI>SAP Web AS Release &gt;= 6.10</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Apply the Support Packages listed below.</p> <UL><LI>SAP Releases &lt;= 4.6D:</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Apply transport SAPK34POSx (attached to this note).</p> <OL>4. SAP Release &lt;= 6.40: Setup rfcoscol</OL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Setup/start rfcoscol on the z/OS Host and establish an RFC connection. For details refer to SAP Note 103135 or the SAP DBA and Planning Guides. Ensure that the user that starts rfcoscol has read access to SMP/E's CSI data sets. <OL>5. SAP Release &gt;= 7.0: Setup stored procedure SAPCL</OL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;For details refer to the SAP DBA Guide. <OL>6. SAP Release &lt;= 6.40: Establish an RFC connection from the check system to the target system</OL> <OL><OL>a) Start the target system.</OL></OL> <OL><OL>b) On your check system, call transaction SM59.</OL></OL> <OL><OL>c) Choose 'Edit -&gt; Create'.</OL></OL> <OL><OL>d) Specify the name of the new \"RFC destination\" and \"Connection type\" '3',and provide a \"Description\".</OL></OL> <OL><OL>e) Press \"Enter\".</OL></OL> <OL><OL>f) Specify and save the \"Technical settings\" and the \"Logon\" data.</OL></OL> <OL><OL>g) Check the new R/3 connection by choosing \"Test connection\" and \"Remote Logon\".</OL></OL> <OL>7. Establish a connection to SAPNet (optional)</OL> <OL><OL>a) Logon to the check system and call transaction OSS1.</OL></OL> <OL><OL>b) Choose 'Parameters-&gt;Technical Settings'.</OL></OL> <OL><OL>c) Specify and save the Log on settings.</OL></OL> <OL><OL>d) Choose 'Log on', specify group '1_PUBLIC', and check whether the connection works.</OL></OL> <p></p> <b>Performing the Check</b><br /> <p>Once you have completed the preparations described in section \"Setup\", the PTF check can be performed.</p> <OL>1. If the check system is not able to connect to SAPNet you need to transfer SAP Notes 81737 and 364109 to your PC.</OL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Within the SAP Service Marketplace proceed as follows: <OL><OL>a) Access web page https://service.sap.com/notes and display the <B>English</B> version of SAP note 81737.</OL></OL> <OL><OL>b) Choose the button 'Download' to add the note to the Download basket.</OL></OL> <OL><OL>c) Add SAP Note 364109 to the Download basked as well. It contains in machine readable format a list of all PTFs related to the currently required service levels.</OL></OL> <OL><OL>d) On your PC call the 'SAP Download Manager' and download the two notes (default file names: NOTE_0000081737 and NOTE_0000364109).</OL></OL> <OL>2. To access the PTF check tool, call transaction SA38 and execute report RSDB2FIX. The input screen of check report RSDB2FIX appears.</OL> <OL>3. Enter the following input values:</OL> <OL><OL>a) SAP Note 81737 (optional if SAPNet connection works)</OL></OL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Full path name of the file on the PC that contains SAP Note 81737 <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Leave this input field empty if SAP notes 81737 and 364109 are retrieved directly from SAPNet. <OL><OL>b) Fix level file (optional)</OL></OL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Full path name of the file on the PC that contains SAP Note 364109. <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Leave this input field empty if SAP notes 81737 and 364109 are retrieved directly from SAPNet. <OL><OL>c) Log name</OL></OL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Name of the log to which the output is written. The pattern <BR/> &#x00A0;&#x00A0;&amp;R3&amp;, &amp;DATE&amp;, and &amp;TIME&amp; <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;are substituted by the name of the R3 destination, the date, and the time, respectively. <OL><OL>d) SAP System (R/3)</OL></OL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;RFC destination of the target system. <OL><OL>e) z/OS Host (TCP/IP)</OL></OL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;RFC destination of the target system's z/OS host. <OL><OL>f) SAPNet (R/3)</OL></OL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;SAPNet's RFC destination. The default connection 'SAPOSS' is created by using transaction OSS1. The transaction can be accessed directly by pressing button 'Online Service System (OSS1)' <OL><OL>g) SMP/E settings</OL></OL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;The input depends on how SMP/E is configured in your environment. Specify the data set and the target zone for at least one CSI library. <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Please note that you cannot check GLOBAL or DLIB zones. <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;For instance, all DB2 function modules may be administered in SMPE.DB261.CSI and target zone TDB261, whereas the remaining software components (Open MVS, JES3, VTAM, and so on) may be kept in data set SMPE.OS390.CSI and target zone TOS390. <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;If global and target zone are kept in different data sets you need to specify the data set that contains the global zone. <OL>4. Choose 'Ping' to check whether the RFC connection(s) work(s).</OL> <OL>5. Save the input as a variant (Ctrl+S; use the target system ID as variant name). The PTF check can then be easily repeated.</OL> <OL>6. Make sure that no other PTF check is currently running on the target system (there is only a risk that the results are incorrect, if you run PTF checks in parallel; there is no risk of damaging the SMP/E data!).</OL> <OL>7. Execute report RSDB2FIX.</OL> <OL>8. The result log is displayed directly after the check. Elder logs can be displayed by choosing button 'Display logs' within the initial selection screen of RSDB2FIX.</OL> <p></p> <b>Analyzing the Output</b><br /> <p>RSDB2FIX writes all errors, warnings, and check results to the output. If the report completes successfully you find a list of missing PTFs and FMIDs at the end of the output.</p> <OL>1. Below the section \"Check PTFs\" the following output may appear:</OL> <UL><LI>\"No missing PTFs found.\"</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;All PTFs required for the FMIDs found within the given SMP/E settings have been applied. Nothing needs to be done.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Please note that if the fix level file was not used, only a subset of all required PTF is checked and that there may well be lots of missing PTFs.</p> <UL><LI>\"The following PFTs are missing.\"</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;A list providing information on the missing PTFs and their associated FMID and APAR is given. The list is ordered by FMID and APAR. Check the status of these missing PTFs. Maybe they are only needed under certain circumstances. (For instance, the additional remark \"required for ...\" indicates that a PTF is only need for certain SAP Releases.) Otherwise, apply them to your z/OS system. For more information, see SAP Note 81737.</p> <OL>2. Below the section \"Check FMIDs\" the following output may appear:</OL> <UL><LI>\"All APARs checked.\"</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;This means that all APARs (and associated PTFs) could be checked. The SMP/E settings specified in the input screen is complete.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Please note that if the fix level file was not used, only a subset of all required APARs/PTFs is checked.</p> <UL><LI>\"The following APARs were not checked because none of the associated FMIDs could be located in the specified' CSIs.\"</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;The list contains all APARs that could not be checked because none of the associated FMIDs has been applied in the given SMP/E settings. It is possible that the APAR refers to a product that is not installed in your environment. This is, for example, the case if you use JES2 and the APAR is related to JES3.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Check whether your input to report RSDB2FIX is incomplete. If that is the case you should correct the SMP/E settings on the input screen and run report RSDB2FIX again.<br /></p> <b>Troubleshooting</b><br /> <p>The following list helps to solve some of the problems that may occur when executing RSDB2FIX:</p> <UL><LI>Error message: \"SAPOSCOL is outdated (version &gt;= 4.6D required) or not running on z/OS. See SAP note 183311 for details.\"</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;-&gt; Install the latest versions of saposcol/rfcoscol/librfc as described in SAP notes 103135 and 359375.</p> <UL><LI>Error message: \"Report RSDB2FIX is outdated. Please obtain current version. See SAP note 183311 for details.\"</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;-&gt; Import the latest version of RSDB2FIX provided on sapservX in file ~ftp/general/R3server/abap/note.0183311/SAPK34POSn.CAR or in a Basis Support Package (SAP Release &gt;= 6.10).</p> <UL><LI>Error message: \"Version of SAP note 81737 is outdated. Please use current version.\"</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;-&gt; Download the latest version of SAP note 81737 and use it as input.</p> <UL><LI>Error message: \"SMP/E API failed.\" with \"GIM59605S ** ENQ FAILED FOR SHARED USE OF&#x00A0;&#x00A0;... FOR QUERY PROCESSING.\"</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;-&gt; RSDB2FIX could not access SMP/E due to a SMP/E job or user session running in parallel.</p> <UL><LI>Error message: \"SMP/E API failed.\" with \"GIM44250I GIMVSMSG - THE VSAM ERROR ANALYSIS OCCURRED ...\"</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;-&gt; Give read access for SMP/E's CSI data sets to the user that starts rfcoscol.<br /></p> <b>Fixes</b><br /> <p>The following list describes the fixes or developments shipped with the current version of RSDB2FIX. Please note that the latest transport always contains all earlier fixes.</p> <OL>1. SAPK34POS1</OL> <UL><LI>PTF check runs on z/OS application servers.</LI></UL> <UL><LI>Node name of the R/3 application server accessed is written to the output.</LI></UL> <OL>2. SAPK34POS2</OL> <UL><LI>Fix level information is provided in a file named fixYYMMDD.txt.</LI></UL> <UL><LI>Additional information is written to the output:</LI></UL> <UL><UL><LI>Put levels related to fix level info</LI></UL></UL> <UL><UL><LI>Global settings ( Report version, min. PTF note version, ...)</LI></UL></UL> <UL><LI>Improved error handling</LI></UL> <UL><LI>Improved consistency checks</LI></UL> <OL>3. SAPK34POS3</OL> <UL><LI>Problems with OS/390 Versions 2.7, 2.9, and 2.10 have been fixed.</LI></UL> <UL><LI>Additional information is written to the output:</LI></UL> <UL><UL><LI>DOC-APARs</LI></UL></UL> <UL><UL><LI>Open APARs</LI></UL></UL> <OL>4. SAPK34POS4</OL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;RSDB2FIX can now be used to check the PTF level before starting a heterogeneous migration from a non-DB2/390 source to a DB2/390 target system. Please proceed as follows: <OL><OL>a) Import the latest version of RSDB2FIX into the source SAP system.</OL></OL> <OL><OL>b) Start saposcol and rfcoscol on the target z/OS host.</OL></OL> <OL><OL>c) Specify an RFC connection between the source SAP system and the target z/OS host.</OL></OL> <OL><OL>d) Call SA38 and execute RSDB2FIX specifying the name of the source system as input value for \"SAP System (R/3)\".</OL></OL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;During the check RSDB2FIX realizes that the SAP system is not running on DB2/390 and issues a warning. The check continues \"assuming\" that DB2/390 version 6.1 is implemented. <OL>5. SAPK34POS5</OL> <UL><LI>Better performance (also see SAP note 359375)</LI></UL> <UL><LI>Result is written to standard R/3 log and can be displayed within RSDB2FIX by choosing \"Display logs\".</LI></UL> <UL><LI>SMP/E API moved to rfcoscol.</LI></UL> <UL><LI>saposcol is not accessed during the PTF check anymore.</LI></UL> <UL><LI>Additional product information is written to the log.</LI></UL> <OL>6. SAPK34POS6</OL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Automatic retrieval of SAP note 81737 and fix level file from SAPNet. <OL>7. SAPK34POS7</OL> <UL><LI>Fix in log name generation.</LI></UL> <UL><LI>Additional output in case of an SMP/E API error.</LI></UL> <OL>8. SAPK34POS8 (Releases 3.0F-4.6D)<br />SAPKB61004 (Release 6.10)</OL> <UL><LI>Support of DB2/390 v7.1</LI></UL> <UL><LI>Workaround for API problem</LI></UL> <OL>9. SAPK34POS9 (Releases 3.0F-4.6D)<br />SAPKB61013 (Release 6.10)</OL> <UL><LI>Support of z/OS 1.2</LI></UL> <UL><LI>Increased number of CSI libraries</LI></UL> <OL>10. SAPK34POSA (Releases 3.0F-4.6D)<br />SAPKB61018 (Release 6.10)<br />SAPKB62002 (Release 6.20)</OL> <UL><LI>Default database version DB2/390 v7.1</LI></UL> <UL><LI>Fix of Error in Step 'Call R/3 System'.</LI></UL> <OL>11. SAPK34POSB (Releases 3.0F-4.6D)<br />SAPKB61023 (Release 6.10)<br />SAPKB62006 (Release 6.20)</OL> <UL><LI>Fix of Error in Step 'Check SAP Note' with error message 'File ... does not contain SAP note 81737 . Please specify full path name.'</LI></UL> <OL>12. SAPK34POSC (Releases 3.0F-4.6D)<br />SAPKB61024 (Release 6.10)<br />SAPKB62009 (Release 6.20)</OL> <UL><LI>Different error message if FMID is not found in specified CSIs.</LI></UL> <OL>13. SAPK34POSD (Releases 3.0F-4.6D)<br />SAPKB61032 (Release 6.10)<br />SAPKB62020 (Release 6.20)</OL> <UL><LI>Fix of an error in the automatic retrieval of SAP note 81737 and fix level file from SAPNet.</LI></UL> <OL>14. SAPK34POSE (Releases 3.0F-4.6D)<br />SAPKB61038 (Release 6.10)<br />SAPKB62031 (Release 6.20)</OL> <UL><LI>Minor changes</LI></UL> <OL>15. SAPK34POSF (Releases 3.0F-4.6D)<br />SAPKB61039 (Release 6.10)<br />SAPKB62037 (Release 6.20)<br />SAPKB64001 (Release 6.40)</OL> <UL><LI>Codepage check added.</LI></UL> <OL>16. SAPK34POSG (Releases 3.0F-4.6D)<br />SAPKB61039 (Release 6.10)<br />SAPKB62039 (Release 6.20)<br />SAPKB64002 (Release 6.40)</OL> <UL><LI>Fix of an error in the automatic retrieval of the fix level file from SAPNet (short dump)</LI></UL> <UL><LI>Adjustments for DB2 v8.1</LI></UL> <OL>17. SAPK34POSH (Releases 3.0F-4.6D)<br />SAPKB61042 (Release 6.10)<br />SAPKB62046 (Release 6.20)<br />SAPKB64010 (Release 6.40)</OL> <UL><LI>Fix of SQL error (SQLCODE = -104, ERROR:&#x00A0;&#x00A0;ILLEGAL SYMBOL \"#\"). Occurs ifthe character '#' is different from 0x7B in the codepage used.</LI></UL> <OL>18. SAPK34POSI (Releases 3.0F-4.6D)<br />SAPKB61043 (Release 6.10)<br />SAPKB62050 (Release 6.20)<br />SAPKB64012 (Release 6.40)</OL> <UL><LI>Extended check of ICLI PTFs</LI></UL> <UL><LI>Structured output of missing ICLI PTFs (now with a header that specifiesthe CSI dataset and target zone)</LI></UL> <OL>19. SAPK34POSJ (Releases 3.0F-4.6D)<br />SAPKB61045 (Release 6.10)<br />SAPKB62055 (Release 6.20)<br />SAPKB64015 (Release 6.40)<br />SAPKB70005 (Release 7.00)</OL> <UL><LI>Additional VARCHAR FOR BIT DATA check (details see SAP note 848384)</LI></UL> <OL>20. SAPK34POSK (Releases 4.0B-4.6D)<br />SAPKB61046 (Release 6.10)<br />SAPKB62057 (Release 6.20)<br />SAPKB64015 (Release 6.40)<br />SAPKB70006 (Release 7.00)</OL> <UL><LI>Modification that allows the upload of SAP notes that where downloaded from SAP Service Marketplace with the SAP Download Manager</LI></UL> <OL>21. SAPKB61046 (Release 6.10)<br />SAPKB62058 (Release 6.20)</OL> <UL><LI>Fix of SQL error (SQLCODE -333). Occurs with z/OS USS application servers and DB2 v8.1.</LI></UL> <OL>22. SAPKB61048 (Release 6.10)<br />SAPKB62061 (Release 6.20)<br />SAPKB64019 (Release 6.40)<br />SAPKB70010 (Release 7.00)</OL> <UL><LI>Enablement of DB2 Connect check.</LI></UL> <OL>23. SAPK34POSL (Releases 3.1I-4.6D)</OL> <UL><LI>Adjustments for SAP Release 3.1x</LI></UL> <OL>24. SAPKB62064<br />SAPKB64022<br />SAPKB70014</OL> <UL><LI>Change in handling of SAPNet connection (only 7.0)</LI></UL> <UL><LI>Adjustment of DB2 Connect check (THIN/FAT client, v9.1)</LI></UL> <OL>25. SAPK34POSM (Releases 3.1I-4.6D)<br />SAPKB62064<br />SAPKB64022<br />SAPKB70016<br />SAPKB71006</OL> <UL><LI>Adjustments for DB2 v9.1</LI></UL> <OL>26. SAPK34POSN (Releases 3.1I-4.6D)<br />SAPKB62067<br />SAPKB64025<br />SAPKB70020<br />SAPKB70105<br />SAPKB70202<br />SAPKB71009<br />SAPKB71104</OL> <UL><LI>Fix of determination of z/OS version</LI></UL> <UL><LI>Upward compatibility of variants</LI></UL> <OL>27. SAPKB62070 SAPKB64028<br />SAPKB70024 SAPKB70109 SAPKB70207<br />SAPKB71012 SAPKB71107 SAPKB72005<br />SAPKB73003<br />Correction 1284924 attached to this note</OL> <UL><LI>Adjustments for DB2 10.1</LI></UL></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Database System", "Value": "DB2/390"}, {"Key": "Transaction codes", "Value": "TIME"}, {"Key": "Transaction codes", "Value": "FILE"}, {"Key": "Transaction codes", "Value": "COPY"}, {"Key": "Transaction codes", "Value": "SUCH"}, {"Key": "Transaction codes", "Value": "EDIT"}, {"Key": "Transaction codes", "Value": "FULL"}, {"Key": "Transaction codes", "Value": "DB2"}, {"Key": "Transaction codes", "Value": "SM59"}, {"Key": "Transaction codes", "Value": "SM37"}, {"Key": "Transaction codes", "Value": "SA38"}, {"Key": "Transaction codes", "Value": "SM36"}, {"Key": "Transaction codes", "Value": "PFTS"}, {"Key": "Transaction codes", "Value": "OSS1"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON> (D022631)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D020040)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "J", "TranslationLanguage": "日本語", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000183311/J"}, {"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000183311/D"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000183311/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000183311/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000183311/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000183311/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000183311/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000183311/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000183311/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": [{"FileName": "SAPK34POSN.CAR", "FileSize": "33", "MimeType": "application/octet-stream", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=011000358700009348672001&iv_version=0115&iv_guid=B1A7D7A4732ACF49814E5825522997E5"}]}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "81737", "RefComponent": "BC-DB-DB2", "RefTitle": "DB2-z/OS: APAR List", "RefUrl": "/notes/81737"}, {"RefNumber": "364109", "RefComponent": "BC-DB-DB2", "RefTitle": "DB2/390: Fix Level 20240123", "RefUrl": "/notes/364109"}, {"RefNumber": "1942514", "RefComponent": "BC-DB-DB2", "RefTitle": "DB2-z/OS:CCMS: RSDB2FIX improvements", "RefUrl": "/notes/1942514"}, {"RefNumber": "1850404", "RefComponent": "BC-DB-DB2", "RefTitle": "DB2-z/OS:v11: Prereqs & preparations for DB2 11", "RefUrl": "/notes/1850404"}, {"RefNumber": "1850403", "RefComponent": "BC-DB-DB2", "RefTitle": "DB2-z/OS:v11: Release of DB2 11 for SAP Components", "RefUrl": "/notes/1850403"}, {"RefNumber": "1745767", "RefComponent": "BC-DB-DB2", "RefTitle": "DB2 z/OS: PTF Check not checking DB2 connect level w/ DB2 10", "RefUrl": "/notes/1745767"}, {"RefNumber": "1734060", "RefComponent": "BC-DB-DB2", "RefTitle": "DB2 z/OS: No longer Fix Level File on sapservX (RSDB2FIX)", "RefUrl": "/notes/1734060"}, {"RefNumber": "1480594", "RefComponent": "BC-DB-DB2", "RefTitle": "DB2-z/OS:v10: Prereqs & preparations for DB2 10", "RefUrl": "/notes/1480594"}, {"RefNumber": "1452370", "RefComponent": "BC-DB-DB2", "RefTitle": "DB2-z/OS:v10: Release of DB2 10 for SAP Components", "RefUrl": "/notes/1452370"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2985629", "RefComponent": "BC-DB-DB2-CCM", "RefTitle": "Dbacockpit \"Performance > Thread Activity\" does not report the SQL statement", "RefUrl": "/notes/2985629 "}, {"RefNumber": "2902128", "RefComponent": "BC-DB-DB2", "RefTitle": "Solman CCDB extractor programs dumping with runtime error 'STRING_OFFSET_TOO_LARGE'", "RefUrl": "/notes/2902128 "}, {"RefNumber": "2637250", "RefComponent": "BC-DB-DB2", "RefTitle": "Seeing error 'DSNU3330I' after applying DB2 11 z/OS PUT 1710 maintenance", "RefUrl": "/notes/2637250 "}, {"RefNumber": "3152993", "RefComponent": "BC-DB-DB2", "RefTitle": "DB2-z/OS:v13: Prereqs & preparations for Db2 13", "RefUrl": "/notes/3152993 "}, {"RefNumber": "3152911", "RefComponent": "BC-DB-DB2", "RefTitle": "Db2-z/OS:v13: Release of Db2 13 for SAP Components", "RefUrl": "/notes/3152911 "}, {"RefNumber": "2303027", "RefComponent": "BC-DB-DB2", "RefTitle": "DB2-z/OS:v12: Prereqs & preparations for DB2 12", "RefUrl": "/notes/2303027 "}, {"RefNumber": "2302997", "RefComponent": "BC-DB-DB2", "RefTitle": "Db2-z/OS:v12: Release of Db2 12 for SAP Components", "RefUrl": "/notes/2302997 "}, {"RefNumber": "1942514", "RefComponent": "BC-DB-DB2", "RefTitle": "DB2-z/OS:CCMS: RSDB2FIX improvements", "RefUrl": "/notes/1942514 "}, {"RefNumber": "1850404", "RefComponent": "BC-DB-DB2", "RefTitle": "DB2-z/OS:v11: Prereqs & preparations for DB2 11", "RefUrl": "/notes/1850404 "}, {"RefNumber": "1850403", "RefComponent": "BC-DB-DB2", "RefTitle": "DB2-z/OS:v11: Release of DB2 11 for SAP Components", "RefUrl": "/notes/1850403 "}, {"RefNumber": "1480594", "RefComponent": "BC-DB-DB2", "RefTitle": "DB2-z/OS:v10: Prereqs & preparations for DB2 10", "RefUrl": "/notes/1480594 "}, {"RefNumber": "81737", "RefComponent": "BC-DB-DB2", "RefTitle": "DB2-z/OS: APAR List", "RefUrl": "/notes/81737 "}, {"RefNumber": "364109", "RefComponent": "BC-DB-DB2", "RefTitle": "DB2/390: Fix Level 20240123", "RefUrl": "/notes/364109 "}, {"RefNumber": "1452370", "RefComponent": "BC-DB-DB2", "RefTitle": "DB2-z/OS:v10: Release of DB2 10 for SAP Components", "RefUrl": "/notes/1452370 "}, {"RefNumber": "1745767", "RefComponent": "BC-DB-DB2", "RefTitle": "DB2 z/OS: PTF Check not checking DB2 connect level w/ DB2 10", "RefUrl": "/notes/1745767 "}, {"RefNumber": "1734060", "RefComponent": "BC-DB-DB2", "RefTitle": "DB2 z/OS: No longer Fix Level File on sapservX (RSDB2FIX)", "RefUrl": "/notes/1734060 "}, {"RefNumber": "145316", "RefComponent": "SV-SMG-SER", "RefTitle": "DB2-z/OS: Preparations for SAP Support Services", "RefUrl": "/notes/145316 "}, {"RefNumber": "742335", "RefComponent": "BC-DB-DB2", "RefTitle": "zSeries: Problems with LOBs", "RefUrl": "/notes/742335 "}, {"RefNumber": "848384", "RefComponent": "BC-DB-DB2", "RefTitle": "DB2-z/OS: Inconsistent fields with DDIC type RAW/LRAW/VARC", "RefUrl": "/notes/848384 "}, {"RefNumber": "103135", "RefComponent": "BC-DB-DB2", "RefTitle": "DB2-z/OS: Installing saposcol manually", "RefUrl": "/notes/103135 "}, {"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719 "}, {"RefNumber": "544569", "RefComponent": "BC-UPG-RDM", "RefTitle": "DB2/390: Additions upgrade to BW 3.10", "RefUrl": "/notes/544569 "}, {"RefNumber": "517278", "RefComponent": "BC-UPG-RDM", "RefTitle": "DB2/390: Additions upgrade to CRM 3.1", "RefUrl": "/notes/517278 "}, {"RefNumber": "585941", "RefComponent": "BC-UPG-RDM", "RefTitle": "DB2/390: Additions upgrade to R/3 Enterprise 4.7 SR1", "RefUrl": "/notes/585941 "}, {"RefNumber": "517267", "RefComponent": "BC-UPG-RDM", "RefTitle": "DB2/390: Additions upgrade to R/3 Enterprise 4.7", "RefUrl": "/notes/517267 "}, {"RefNumber": "600824", "RefComponent": "BC-UPG-RDM", "RefTitle": "DB2/390: Additions upgrade to CRM 4.0 / SRM Server 4.0", "RefUrl": "/notes/600824 "}, {"RefNumber": "506339", "RefComponent": "BC-UPG-RDM", "RefTitle": "DB2/390: Additions upgrade to BW 3.0B", "RefUrl": "/notes/506339 "}, {"RefNumber": "603278", "RefComponent": "BC-UPG-RDM", "RefTitle": "DB2/390: Additions upgrade to SCM 4.0", "RefUrl": "/notes/603278 "}, {"RefNumber": "493599", "RefComponent": "BC-UPG-RDM", "RefTitle": "DB2/390: Additions upgrade to EBP 3.5", "RefUrl": "/notes/493599 "}, {"RefNumber": "640516", "RefComponent": "BC-UPG-RDM", "RefTitle": "DB2/390: Additions upgrade to R/3 4.7 Ext.Set 2.00", "RefUrl": "/notes/640516 "}, {"RefNumber": "490065", "RefComponent": "BC-UPG-RDM", "RefTitle": "DB2/390: Additions upgrade to Web AS 6.20", "RefUrl": "/notes/490065 "}, {"RefNumber": "446226", "RefComponent": "BC-UPG-RDM", "RefTitle": "DB2/390: Additions upgrade to EBP/CRM  3.0 SR1", "RefUrl": "/notes/446226 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_APPL", "From": "40B", "To": "40B", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "45A", "To": "45B", "Subsequent": ""}, {"SoftwareComponent": "SAP_BASIS", "From": "46A", "To": "46D", "Subsequent": ""}, {"SoftwareComponent": "SAP_BASIS", "From": "610", "To": "640", "Subsequent": ""}, {"SoftwareComponent": "SAP_BASIS", "From": "700", "To": "702", "Subsequent": ""}, {"SoftwareComponent": "SAP_BASIS", "From": "710", "To": "730", "Subsequent": ""}, {"SoftwareComponent": "SAP_BASIS", "From": "731", "To": "731", "Subsequent": ""}, {"SoftwareComponent": "SAP_BASIS", "From": "800", "To": "800", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "SAP_BASIS 610", "SupportPackage": "SAPKB61040", "URL": "/supportpackage/SAPKB61040"}, {"SoftwareComponentVersion": "SAP_BASIS 610", "SupportPackage": "SAPKB61046", "URL": "/supportpackage/SAPKB61046"}, {"SoftwareComponentVersion": "SAP_BASIS 610", "SupportPackage": "SAPKB61032", "URL": "/supportpackage/SAPKB61032"}, {"SoftwareComponentVersion": "SAP_BASIS 610", "SupportPackage": "SAPKB61045", "URL": "/supportpackage/SAPKB61045"}, {"SoftwareComponentVersion": "SAP_BASIS 610", "SupportPackage": "SAPKB61004", "URL": "/supportpackage/SAPKB61004"}, {"SoftwareComponentVersion": "SAP_BASIS 610", "SupportPackage": "SAPKB61023", "URL": "/supportpackage/SAPKB61023"}, {"SoftwareComponentVersion": "SAP_BASIS 610", "SupportPackage": "SAPKB61043", "URL": "/supportpackage/SAPKB61043"}, {"SoftwareComponentVersion": "SAP_BASIS 610", "SupportPackage": "SAPKB61013", "URL": "/supportpackage/SAPKB61013"}, {"SoftwareComponentVersion": "SAP_BASIS 610", "SupportPackage": "SAPKB61018", "URL": "/supportpackage/SAPKB61018"}, {"SoftwareComponentVersion": "SAP_BASIS 610", "SupportPackage": "SAPKB61038", "URL": "/supportpackage/SAPKB61038"}, {"SoftwareComponentVersion": "SAP_BASIS 610", "SupportPackage": "SAPKB61024", "URL": "/supportpackage/SAPKB61024"}, {"SoftwareComponentVersion": "SAP_BASIS 610", "SupportPackage": "SAPKB61039", "URL": "/supportpackage/SAPKB61039"}, {"SoftwareComponentVersion": "SAP_BASIS 610", "SupportPackage": "SAPKB61042", "URL": "/supportpackage/SAPKB61042"}, {"SoftwareComponentVersion": "SAP_BASIS 620", "SupportPackage": "SAPKB62058", "URL": "/supportpackage/SAPKB62058"}, {"SoftwareComponentVersion": "SAP_BASIS 620", "SupportPackage": "SAPKB62061", "URL": "/supportpackage/SAPKB62061"}, {"SoftwareComponentVersion": "SAP_BASIS 620", "SupportPackage": "SAPKB62050", "URL": "/supportpackage/SAPKB62050"}, {"SoftwareComponentVersion": "SAP_BASIS 620", "SupportPackage": "SAPKB62057", "URL": "/supportpackage/SAPKB62057"}, {"SoftwareComponentVersion": "SAP_BASIS 620", "SupportPackage": "SAPKB62039", "URL": "/supportpackage/SAPKB62039"}, {"SoftwareComponentVersion": "SAP_BASIS 620", "SupportPackage": "SAPKB62064", "URL": "/supportpackage/SAPKB62064"}, {"SoftwareComponentVersion": "SAP_BASIS 620", "SupportPackage": "SAPKB62046", "URL": "/supportpackage/SAPKB62046"}, {"SoftwareComponentVersion": "SAP_BASIS 620", "SupportPackage": "SAPKB62055", "URL": "/supportpackage/SAPKB62055"}, {"SoftwareComponentVersion": "SAP_BASIS 620", "SupportPackage": "SAPKB62042", "URL": "/supportpackage/SAPKB62042"}, {"SoftwareComponentVersion": "SAP_BASIS 620", "SupportPackage": "SAPKB62002", "URL": "/supportpackage/SAPKB62002"}, {"SoftwareComponentVersion": "SAP_BASIS 620", "SupportPackage": "SAPKB62067", "URL": "/supportpackage/SAPKB62067"}, {"SoftwareComponentVersion": "SAP_BASIS 620", "SupportPackage": "SAPKB62020", "URL": "/supportpackage/SAPKB62020"}, {"SoftwareComponentVersion": "SAP_BASIS 620", "SupportPackage": "SAPKB62031", "URL": "/supportpackage/SAPKB62031"}, {"SoftwareComponentVersion": "SAP_BASIS 620", "SupportPackage": "SAPKB62006", "URL": "/supportpackage/SAPKB62006"}, {"SoftwareComponentVersion": "SAP_BASIS 620", "SupportPackage": "SAPKB62063", "URL": "/supportpackage/SAPKB62063"}, {"SoftwareComponentVersion": "SAP_BASIS 620", "SupportPackage": "SAPKB62009", "URL": "/supportpackage/SAPKB62009"}, {"SoftwareComponentVersion": "SAP_BASIS 620", "SupportPackage": "SAPKB62070", "URL": "/supportpackage/SAPKB62070"}, {"SoftwareComponentVersion": "SAP_BASIS 620", "SupportPackage": "SAPKB62037", "URL": "/supportpackage/SAPKB62037"}, {"SoftwareComponentVersion": "SAP_BASIS 640", "SupportPackage": "SAPKB64025", "URL": "/supportpackage/SAPKB64025"}, {"SoftwareComponentVersion": "SAP_BASIS 640", "SupportPackage": "SAPKB64022", "URL": "/supportpackage/SAPKB64022"}, {"SoftwareComponentVersion": "SAP_BASIS 640", "SupportPackage": "SAPKB64028", "URL": "/supportpackage/SAPKB64028"}, {"SoftwareComponentVersion": "SAP_BASIS 640", "SupportPackage": "SAPKB64010", "URL": "/supportpackage/SAPKB64010"}, {"SoftwareComponentVersion": "SAP_BASIS 640", "SupportPackage": "SAPKB64016", "URL": "/supportpackage/SAPKB64016"}, {"SoftwareComponentVersion": "SAP_BASIS 640", "SupportPackage": "SAPKB64001", "URL": "/supportpackage/SAPKB64001"}, {"SoftwareComponentVersion": "SAP_BASIS 640", "SupportPackage": "SAPKB64005", "URL": "/supportpackage/SAPKB64005"}, {"SoftwareComponentVersion": "SAP_BASIS 640", "SupportPackage": "SAPKB64002", "URL": "/supportpackage/SAPKB64002"}, {"SoftwareComponentVersion": "SAP_BASIS 640", "SupportPackage": "SAPKB64012", "URL": "/supportpackage/SAPKB64012"}, {"SoftwareComponentVersion": "SAP_BASIS 640", "SupportPackage": "SAPKB64015", "URL": "/supportpackage/SAPKB64015"}, {"SoftwareComponentVersion": "SAP_BASIS 640", "SupportPackage": "SAPKB64019", "URL": "/supportpackage/SAPKB64019"}, {"SoftwareComponentVersion": "SAP_BASIS 640", "SupportPackage": "SAPKB64021", "URL": "/supportpackage/SAPKB64021"}, {"SoftwareComponentVersion": "SAP_BASIS 700", "SupportPackage": "SAPKB70013", "URL": "/supportpackage/SAPKB70013"}, {"SoftwareComponentVersion": "SAP_BASIS 700", "SupportPackage": "SAPKB70016", "URL": "/supportpackage/SAPKB70016"}, {"SoftwareComponentVersion": "SAP_BASIS 700", "SupportPackage": "SAPKB70024", "URL": "/supportpackage/SAPKB70024"}, {"SoftwareComponentVersion": "SAP_BASIS 700", "SupportPackage": "SAPKB70010", "URL": "/supportpackage/SAPKB70010"}, {"SoftwareComponentVersion": "SAP_BASIS 700", "SupportPackage": "SAPKB70014", "URL": "/supportpackage/SAPKB70014"}, {"SoftwareComponentVersion": "SAP_BASIS 700", "SupportPackage": "SAPKB70005", "URL": "/supportpackage/SAPKB70005"}, {"SoftwareComponentVersion": "SAP_BASIS 700", "SupportPackage": "SAPKB70006", "URL": "/supportpackage/SAPKB70006"}, {"SoftwareComponentVersion": "SAP_BASIS 700", "SupportPackage": "SAPKB70020", "URL": "/supportpackage/SAPKB70020"}, {"SoftwareComponentVersion": "SAP_BASIS 700", "SupportPackage": "SAPKB70007", "URL": "/supportpackage/SAPKB70007"}, {"SoftwareComponentVersion": "SAP_BASIS 701", "SupportPackage": "SAPKB70109", "URL": "/supportpackage/SAPKB70109"}, {"SoftwareComponentVersion": "SAP_BASIS 701", "SupportPackage": "SAPKB70105", "URL": "/supportpackage/SAPKB70105"}, {"SoftwareComponentVersion": "SAP_BASIS 702", "SupportPackage": "SAPKB70207", "URL": "/supportpackage/SAPKB70207"}, {"SoftwareComponentVersion": "SAP_BASIS 702", "SupportPackage": "SAPKB70202", "URL": "/supportpackage/SAPKB70202"}, {"SoftwareComponentVersion": "SAP_BASIS 710", "SupportPackage": "SAPKB71009", "URL": "/supportpackage/SAPKB71009"}, {"SoftwareComponentVersion": "SAP_BASIS 710", "SupportPackage": "SAPKB71006", "URL": "/supportpackage/SAPKB71006"}, {"SoftwareComponentVersion": "SAP_BASIS 710", "SupportPackage": "SAPKB71012", "URL": "/supportpackage/SAPKB71012"}, {"SoftwareComponentVersion": "SAP_BASIS 711", "SupportPackage": "SAPKB71107", "URL": "/supportpackage/SAPKB71107"}, {"SoftwareComponentVersion": "SAP_BASIS 711", "SupportPackage": "SAPKB71104", "URL": "/supportpackage/SAPKB71104"}, {"SoftwareComponentVersion": "SAP_BASIS 720", "SupportPackage": "SAPKB72005", "URL": "/supportpackage/SAPKB72005"}, {"SoftwareComponentVersion": "SAP_BASIS 730", "SupportPackage": "SAPKB73003", "URL": "/supportpackage/SAPKB73003"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": [{"SoftwareComponent": "SAP_BASIS", "NumberOfCorrin": 1, "URL": "/corrins/0000183311/41"}]}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 1, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}