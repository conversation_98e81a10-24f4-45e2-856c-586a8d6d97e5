{"Request": {"Number": "40584", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 451, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000014412742017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000040584?language=E&token=4B0402560AB72FD8C7F02CAC02260275"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000040584", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000040584/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "40584"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 24}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Consulting"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "05.11.2004"}, "SAPComponentKey": {"_label": "Component", "value": "BC-UPG-OCS"}, "SAPComponentKeyText": {"_label": "Component", "value": "Online Corr. Support (Tools:Supp.-Pack., Addon Install/Upgr)"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Basis Components", "value": "BC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Upgrade - general", "value": "BC-UPG", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-UPG*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Online Corr. Support (Tools:Supp.-Pack., Addon Install/Upgr)", "value": "BC-UPG-OCS", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-UPG-OCS*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "40584 - Return code 8 in patch generation log"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>When you apply a patch to your system using the SAP Patch Manager (transaction SPAM) SPAM may abort at the IMPORT_PROPER step.<br /><br />According to the generation log, generation terminated with return code 8.<br /><br /></p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>CRT, HOT PACKAGE, OCS, PATCH, SPAM,<br />RH_T_MULT_CREATE, CO_ML_CALCULATION_SHEET_LIST, ICDSFD01, ICDTFD01,<br />RH_T77S0_WORKTIME_DEFAULTS_GET<br /><br /></p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>Basically, the cause of the error is either temporary or permanent. Temporary errors are always due to the absence of an SAP buffer synchronization.<br /><br />Permanent errors are caused by inconsistent references between caller and called program or by an error in the SAP buffer synchronization mechanism itself. For more details refer to points 3 and 4 respectively.</p> <OL>1. Your SAP system is parametrised in such a way that no buffer synchronization takes place.</OL> <OL>2. The buffer synchronization does take place, but <B>after</B> SPAM has started the generation (corrected in SAP R/3 3.1G).</OL> <OL>3. The generation error is due to a syntax error in the calling program, e.g. the number of actual parameters does not match number of formal parameters in subroutine calls.</OL> <OL>4. There is an error in the SAP buffer synchronization mechanism.<br /><br /></OL><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><OL>1. Make sure the profile parameter <B>rdisp/bufrefmode</B> has the values \"sendon,exeauto\" if your system runs on several application servers, and the values \"sendoff,exeauto\" if you have only one application server.<br /></OL> <p>Any changes to the SAP profile take effect after you have restarted the SAP system. After restarting follow the instructions under point 2 below.<br /><br /></p> <OL>2. From the SPAM main screen, branch to the generation log by pressing the button 'Log'. Display the 2nd level of detail in the generation log to find the programs (or screens) that SPAM failed to generate. Perform the following actions:</OL> <UL><LI>Call transaction SE38 and enter the name of the program.</LI></UL> <UL><LI>Generate it using the option Program -&gt; Generate</LI></UL> <UL><LI>If generation is successful, call transaction SPAM and resume the patch application.</LI></UL> <UL><LI>If generation is not successful, trigger the SAP buffer synchronization by entering command <B>/$SYNC</B> in the OK-code. You must be very careful about doing so especially in production systems as this could lead to transactions terminating abnormally. An alternative to this is to restart your SAP system. You can now try to generate the program.</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;If screens are involved, use transaction SE51. As of maintenance level 3.1G the buffer synchronisation is forced before the generation is started and hence eliminating this temporary source of error.<br /> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;<B> Note that for SAP systems up to and including maintenance level 3.0C</B> you should use the following workaround: Call up transaction SM31 and specify table PAT03. Press the button 'Maintain'. In the 'Status' column of the patch in question, replace 'E' with 'I'.<br /><br /> <OL>3. This can happen if you have modified an SAP include program in such a way that it is syntactically incorrect and which is affected by the patch in question. Or you have a syntactically incorrect customer program which calls the SAP include.<br /><br />In such a case it is sensible to carry on with the patch application. You have to signal to SPAM to ignore the generation errors and proceed with the next step. As of SAP R/3 3.1H you can ignore the generation errors using options \"Extras -&gt; Ignore gen. errors\". Subsequently resume the patch application with SPAM.<br /><br />If your system is on SAP R/3 3.1G execute program <B>RSSPAM03</B> for the patch in question. Following, resume the patch application with SPAM.<br /><br />RSSPAM03 is available with the first SAP R/3 3.0F Hot Package (SAPKH30F01). If it is not available in your system you will have to make do with the following workaround:</OL> <UL><LI>Maintain table PAT01 with transaction SM31.</LI></UL> <UL><LI>Position on the patch in question and replace the condition for the step IMPORT_PROPER with an 'X'.</LI></UL> <UL><LI>Confirm changes by pressing 'Enter'.</LI></UL> <UL><LI>Create a new cofile entry for the generation step by copying the last entry, setting the return code to '0000' and counting up the time. For example if the last cofile entry is:<br /><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;E50 G 000<B>8 </B>1996121312533<B>8</B>DSW17 SAPServiceE50<br /><br />the new becomes:<br /><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;E50 G 000<B>0 </B>1996121312533<B>9 (workaround Note 40584) </B><br /><br />Note that the time was incremented by one second. For documentation purposes we advise you to add a meaningful comment to your entry such as the one shown above.<br /></LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Call transaction SPAM and restart application of the patch.<br /> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;<B>Note on Add-Ons</B><br /> Add-Ons are similarly affected. That is to say that both the Add-On vendor and the Add-On customer must apply the workaround. Additionally, the Add-On vendor must eliminate the generation error and include those changes in a Conflict Resolution Transport (CRT) which the Add-On customer must apply. For details of how you can handle CRTs refer to Note 53902.<br /><br /> <OL>4. Up to and including maintenance level 3.0E when importing ABAP/4 includes, the masters (the programs themselves) still run with their old loads (runtime versions). For more details of this refer to Note 45456. This error has been eliminated in 3.0F.<br /><br /><br /></OL> <p><B>Warning </B><br />Under no circumstances should you tamper with table PAT03 or PAT01. Only do so if SAP explicitly advises you to, as in this note.<br /><br /><br /></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D028597)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D028597)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000040584/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000040584/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000040584/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000040584/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000040584/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000040584/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000040584/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000040584/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000040584/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "90409", "RefComponent": "XX-PROJ-CS-BC", "RefTitle": "CRT Hot Packages 19-23 for CS 3.0F/2A and corr.", "RefUrl": "/notes/90409"}, {"RefNumber": "87803", "RefComponent": "MM-IM", "RefTitle": "Intrastat: syntax error after Hot Package import", "RefUrl": "/notes/87803"}, {"RefNumber": "86006", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/86006"}, {"RefNumber": "85750", "RefComponent": "BC-CCM-MON-TUN", "RefTitle": "Syntax error in SAPLSTUW after Hot Package 18", "RefUrl": "/notes/85750"}, {"RefNumber": "77407", "RefComponent": "IS-OIL-BC", "RefTitle": "CRTs for IS-Oil", "RefUrl": "/notes/77407"}, {"RefNumber": "75138", "RefComponent": "IS-R", "RefTitle": "R/3 Retail and Hot Packages", "RefUrl": "/notes/75138"}, {"RefNumber": "65590", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/65590"}, {"RefNumber": "53902", "RefComponent": "BC-UPG-OCS", "RefTitle": "Conflicts between Support Packages and add-ons", "RefUrl": "/notes/53902"}, {"RefNumber": "45456", "RefComponent": "BC-CTS", "RefTitle": "Synchronization in transport: Overview (3.0/3.1)", "RefUrl": "/notes/45456"}, {"RefNumber": "115726", "RefComponent": "PA-BN", "RefTitle": "Implementation of notes being overwritten by HotP 41", "RefUrl": "/notes/115726"}, {"RefNumber": "102736", "RefComponent": "FI-SL-IS-A", "RefTitle": "Syntax error in J... after applying Hot Package 37", "RefUrl": "/notes/102736"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "53902", "RefComponent": "BC-UPG-OCS", "RefTitle": "Conflicts between Support Packages and add-ons", "RefUrl": "/notes/53902 "}, {"RefNumber": "77407", "RefComponent": "IS-OIL-BC", "RefTitle": "CRTs for IS-Oil", "RefUrl": "/notes/77407 "}, {"RefNumber": "85750", "RefComponent": "BC-CCM-MON-TUN", "RefTitle": "Syntax error in SAPLSTUW after Hot Package 18", "RefUrl": "/notes/85750 "}, {"RefNumber": "45456", "RefComponent": "BC-CTS", "RefTitle": "Synchronization in transport: Overview (3.0/3.1)", "RefUrl": "/notes/45456 "}, {"RefNumber": "75138", "RefComponent": "IS-R", "RefTitle": "R/3 Retail and Hot Packages", "RefUrl": "/notes/75138 "}, {"RefNumber": "115726", "RefComponent": "PA-BN", "RefTitle": "Implementation of notes being overwritten by HotP 41", "RefUrl": "/notes/115726 "}, {"RefNumber": "90409", "RefComponent": "XX-PROJ-CS-BC", "RefTitle": "CRT Hot Packages 19-23 for CS 3.0F/2A and corr.", "RefUrl": "/notes/90409 "}, {"RefNumber": "102736", "RefComponent": "FI-SL-IS-A", "RefTitle": "Syntax error in J... after applying Hot Package 37", "RefUrl": "/notes/102736 "}, {"RefNumber": "87803", "RefComponent": "MM-IM", "RefTitle": "Intrastat: syntax error after Hot Package import", "RefUrl": "/notes/87803 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_APPL", "From": "30B", "To": "31I", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "SAP_APPL 30F", "SupportPackage": "SAPKH30F01", "URL": "/supportpackage/SAPKH30F01"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}