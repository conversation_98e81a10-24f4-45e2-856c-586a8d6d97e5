{"Request": {"Number": "428398", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 415, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000015056232017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000428398?language=E&token=6CD8253991EE02B01EE4809F23183937"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000428398", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000428398/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "428398"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 3}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Special development"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "19.09.2001"}, "SAPComponentKey": {"_label": "Component", "value": "XX-PROJ-SDP-004-CRM"}, "SAPComponentKeyText": {"_label": "Component", "value": "Customer Relationship Management"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Miscellaneous", "value": "XX", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Project-based solutions", "value": "XX-PROJ", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-PROJ*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Strategic Development Projects", "value": "XX-PROJ-SDP", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-PROJ-SDP*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Strategic Development Project 004 (DIVA)", "value": "XX-PROJ-SDP-004", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-PROJ-SDP-004*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Customer Relationship Management", "value": "XX-PROJ-SDP-004-CRM", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-PROJ-SDP-004-CRM*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "428398 - DIVA ICP1/2: Preliminary Transport III (07/2001)"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>************************************************************************<br />This note is only valid for SAP Add-On/Preliminary Development PSA-DIVA.<br />It should not be used by standard customers.<br />************************************************************************<br />PSA-DIVA ICP1/ICP2 Transport of developments for:<br />R/3 Release 4.6C<br /><br />The developments are to be supplied by the SDP development department<br />for import into the PSA system.<br />It includes following</p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>PSA DIVA ICP1 ICP2 BUPA BDT UPLOAD</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>Preliminary transport III of developments for ICP1 and ICP2 to PSA.<br />IF Support-Package 4 is not imported, see Note 392573.<br />Import of Note 0392177 and 400112</p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>The data is supplied for import into the PSA system on the service<br />computer sapserv3.<br />In order to import the data into the PSA system please first check note<br />13719!<br /><br />Actions in R/3-System:<br />Before importing the data, check the following development classes:<br />If they don't exist, please create them.<br />YDIVA_PROCESS_TPS34&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;(DIVA: Process Interface Up/downloads)<br />YDIVA_PROCESS_CRMO_200_SCE (DIVA: Before Download SCE)<br />YDIVA_PROCESS_CUSTOMER&#x00A0;&#x00A0;&#x00A0;&#x00A0; (DIVA: Up/Download Customer Enhanced Data)<br />YDIVA_BUPA&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;(DIVA: Customer enhancement of BP)<br />Please Check KNA1: Customer-Append ZAKNA1 with field<br />YYDIVA_LTP_PARK Field Type YYDIVA_LTP_PARK (CHAR 1).<br /><br /><br />Actions in CRM-System:<br />Before importing the data, check the following objects:<br />Development class:<br />YDIVA_BUPA_SHIP&#x00A0;&#x00A0; (DIVA: Business Partner Enhancement SHIP-TO-PARTY)<br />YDIVA_BUPA<br />YDIVA_PROCESS_TPS34<br />YDIVA_PROCESS_CUSTOMER<br /><br />Append of BUT000: ZABUT000 with the field YYDIVA_LTP_PARK<br /><br />Please create two BADI-Implementations:<br />Transaction SE19:<br />Name:&#x00A0;&#x00A0;YDIVA_BADI_BP_TO_MW for the Definition CRM_CAPGEN_OUTBOUND<br />YDIVA_BADI_BUPA for the Definition CRM_CAPGEN_INBOUND<br />Please save and activate the interfaces and the implentations<br /><br />You find the file with the data for the R/3 development as follows:<br />SAPSERV3-directory: ~ftp/specific/sdp/psa-diva/note.0428398<br />Containing the Zip-File: Note4001_III_R3.ZIP with the two files:<br />K900175.zbb<br />R900175.zbb<br />You find the file with the data for the CRM development in the same<br />folder. ZIP-File: Note428398_III_CRM.ZIP with the two files:<br />K900472.q2u<br />R900472.q2u<br /><br />Use note 13719 in order to collect and import the data.<br /><br />Before implementing Up/Download in CRM, BDOC'S must be changed and<br />generated. Please see the Documentation DESIGN_BP_ENHANCEMENT_ICPI/2.doc<br />in folder:<br />\\Projects\\PSA\\04_PHASE_DEVELOPMENT\\01_DESIGN\\SP4_SALES_PROCESS\\ICP1&amp;2)<br />After generating the BDOC's im CRM-System<br />following methods must be re-adjusted:<br />Method fill_bdoc_body of the implematation YDIVA_BADI_BP_TO_MW and<br />method call_customer_badi of the implementation YDIVA_BADI_BUPA_MW.<br /><br />Necessary customizing is described in the document:<br />DESIGN_BP_ENHANCEMENT_ICPI/2.doc ( in folder<br />E:\\Projects\\PSA\\04_PHASE_DEVELOPMENT\\01_DESIGN\\SP4_SALES_PROCESS\\ICP1&amp;2)</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON> (D028814)"}, {"Key": "Processor                                                                                           ", "Value": ""}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000428398/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000428398/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000428398/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000428398/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000428398/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000428398/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000428398/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000428398/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000428398/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "400112", "RefComponent": "XX-PROJ-SDP-004", "RefTitle": "DIVA ICP1/2: Preliminary Transport II (05/2001)", "RefUrl": "/notes/400112"}, {"RefNumber": "392177", "RefComponent": "XX-PROJ-SDP-004", "RefTitle": "DIVA ICP1/2: Preliminary Transport (03/2001)", "RefUrl": "/notes/392177"}, {"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719 "}, {"RefNumber": "392177", "RefComponent": "XX-PROJ-SDP-004", "RefTitle": "DIVA ICP1/2: Preliminary Transport (03/2001)", "RefUrl": "/notes/392177 "}, {"RefNumber": "400112", "RefComponent": "XX-PROJ-SDP-004", "RefTitle": "DIVA ICP1/2: Preliminary Transport II (05/2001)", "RefUrl": "/notes/400112 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_APPL", "From": "46C", "To": "46C", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}