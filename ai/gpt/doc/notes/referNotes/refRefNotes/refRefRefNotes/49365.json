{"Request": {"Number": "49365", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 4039, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "D", "_masterLanguage": "D", "_loadedLanguage": "D", "_machineTranslationLanguage": "E", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Acknowledge", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "If the quality of the machine translation is not sufficient for your purposes, click here to request a revised version.", "_type": "EVENT", "_visible": true}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000014452302017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000049365?language=E&token=1CFB0B737658F14D5F1DCCF342A9F3EC"}, "ShareByEmail": {"_label": "Share via Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000049365", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000049365/D/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "49365"}, "Type": {"_label": "Type", "value": "SAP-Hinweis"}, "Version": {"_label": "Version", "value": 74}, "Recency": {"_label": "Currentness", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "German"}, "MasterLanguage": {"_label": "Original Language", "value": "German"}, "Category": {"_label": "Category", "value": "Consulting"}, "Priority": {"_label": "Priority", "value": "Recommendations/Additional Information"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "10.02.2012"}, "SAPComponentKey": {"_label": "Component", "value": "XX-INT-FA-MAKE"}, "SAPComponentKeyText": {"_label": "Component", "value": "Final Assembly Makes"}, "SAPComponentPath": [{"_label": "Services &amp; Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs &amp; Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Miscellaneous", "value": "XX", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "For SAP internal use only!", "value": "XX-INT", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-INT*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "General Final Assembly", "value": "XX-INT-FA", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-INT-FA*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Final Assembly Makes", "value": "XX-INT-FA-MAKE", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-INT-FA-MAKE*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "49365 - iSeries: Applying a patch"}, "LongText": {"_label": "Description", "value": "<div id=\"DISCLAIMER\" class=\"sapMMsgStrip sapMMsgStripWarning sapUiSmallMarginTop sapUiMediumMarginBottom\"><div class=\"sapMMsgStripMessage\"><span dir=\"auto\" class=\"sapMText sapUiSelectable sapMTextMaxWidth\" style=\"text-align: left;\"><strong>ATTENTION!</strong> This is a machine-translated document that has not been reviewed for accuracy. To provide feedback regarding the machine translation, please <a style=\"color:var(--sapLinkColor);\" href=\"https://sapinsights.eu.qualtrics.com/jfe/form/SV_6nI2C1MOwEviVQG?NoteNumber=49365&TargetLanguage=EN&Component=XX-INT-FA-MAKE&SourceLanguage=DE&Priority=06\" target=\"_blank\">click here</a>.<br/>You can access the original document via the following link: <a style=\"color:var(--sapLinkColor);\" href=\"/notes/49365/D\" target=\"_blank\">/notes/49365/D</a>.</span></div></div><div><div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>You are prompted to apply a patch</p><h3 data-toc-skip class=\"section\" id=\"Weitere Begriffe\">Other Terms</h3><p>Kernel patch, APYR3FIX, patches, iSeries, AS400</p><h3 data-toc-skip class=\"section\" id=\"Ursache und Voraussetzungen\">Reason and Prerequisites</h3><p>*<br /></p><h3 data-toc-skip class=\"section\" id=\"L&ouml;sung\">Solution</h3><p>Note: If you have imported the downward-compatible 7.20 kernel into your system, this SAP Note is meaningless. In this case, see SAP Note 1683418.<br />See also SAP Notes 330793 for the download from Service Marketplace 495999 for importing the new version of APYR3FIX and 751131,751132for the changes in 6.40 for APYR3FIX and APYR3KRN.<br /></p> <b>Where are patches for iSeries provided?<br /></b><br /> <p>Patches for the SAP kernel and other executables for iSeries are provided via the Software Center on SAP Service Marketplace. The patches must be downloaded from there (for example, using an Internet browser) before they can be applied to your iSeries.<br />Until the ftp server sapservX is finally deactivated, the patches are still provided in the directories general/R3server/patches/rel&lt;kernel release>/OS400/&lt;OS400-release>.<br /><br />On the SAP Service Market Place, you can find the patches via http://service.sap.com/patches and navigate from there by choosing<br />           Product                    (e.g. SAP R/3)<br />           Product release             (for example, 46C)<br />           Binary Patches<br />           SAP kernel release          (e.g. SAP Kernel 46D)<br />           Operating System + Release (e.g. OS/400 V4R5M0 ASCII)<br />           Database                  (DB2/400)<br /><br />Information about the individual patches is provided on the displayed page and you can start the download from here.<br /><br />Note that the OS/400 release does not correspond to the release of your iSeries, but to the OS/400 release on which the patch was created. The following table contains the currently supported kernel releases and the OS/400 release on which they were created.<br /><br />           31I_COM  EBCDIC   V4R3M0<br />           31I_EXT  EBCDIC   V5R1M0<br />           40B_COM  EBCDIC   V4R4M0<br />           40B_EXT  EBCDIC   V5R1M0<br />           45B      EBCDIC   V4R2M0<br />           45B_EXT  EBCDIC   V5R1M0<br />           46D      EBCDIC   V4R4M0<br />           46D      ASCII    V4R5M0<br />           46D_EXT  EBCDIC   V5R1M0<br />           46D_EXT  ASCII    V5R1M0<br />           640      ASCII    V5R2M0<br />           640      UNICODE  V5R2M0<br />           700      ASCII    V5R3M0<br />           700      UNICODE  V5R3M0<br />           701      ASCII    V5R3M0<br />           701      UNICODE  V5R3M0<br /></p> <b>Patch file names<br /></b><br /> <p>The patch files are always called like the program to be patched. For example, there are: Patches for TP, R3TRANS, and so on The exception is the name DW (previously KERNEL). Depending on the kernel release, this patch file contains patches for several programs, such as DW, MSG_SERVER, GWRD, and so on.<br />In addition, the patch level is added to the name, for example: TP_123 or DW_589<br /></p> <b>File formats of the patches<br /></b><br /> <p>Binary patches for iSeries are saved to save files. For the import, they must be available again in a save file on your iSeries. The SAP Service Marketplace also supports and uses other file formats. These are the file extensions:<br /></p> <OL>1. .SVF<br />This is a save file that was transferred from an iSeries to SAP Service Marketplace using ftp. These files can be transferred to a save file again using ftp.<br /></OL> <OL>2nd STM<br />This is a save file that was copied to a stream file in the IFS using CPYTOSTMF. These files must be transferred back to a save file using CPYFRMSTMF.<br /></OL> <OL>3. .SAR<br />This is a save file that was copied to a stream file in the IFS using the SAP compression tool SAPCAR. These files must be transferred to a save file using SAPCAR.</OL> <p><br />Caution:<br />The ftp servers sapservX contain files without an extension and patch level addition. These are save files that were transferred from iSeries to the server using ftp. They therefore correspond to the patch files .SVF of the Marketplace.<br /></p> <b>Applying the patches<br /></b><br /> <p>To be able to apply patches, you must first download them via the Internet. The file can be saved directly on the iSeries in the IFS. To do this, set up a writable share on your iSeries and specify this share name when you save (for example, \\\\myas400\\patches)<br />The import then takes place using the command APYR3FIX. APYR3FIX supports the file formats described above and can create a save file from them. The only important thing is that the downloaded patch file can be found by APYR3FIX.<br />APYR3FIX restores the saved objects from a save file to the library PCH&lt;sid>. Patch files always contain the program R3INSTFIX, which is called by APYR3FIX, and which then implements the patch.<br /><br />Caution:<br />You must apply a patch from &lt;sid>OFR.<br />APYR3FIX requires the name of a kernel library into which the patch is to be applied. This library must be the only kernel library in the library list, otherwise APYR3FIX aborts.<br /></p> <b>Meaning of Parameters of Command APYR3FIX<br /></b><br /> <OL>1st SID<br />Enter the SAP system ID of your system here.<br /></OL> <OL>2. SAVF<br />Enter the name of the save file and the library from which APYR3FIX is to restore the patch.<br />Important: APYR3FIX unpacks .SAR files in the background. Do not use QTEMP as the library name for .SAR files.<br /></OL> <OL>3. SAVLIB<br />Name of the library from which the patch was saved. Depending on your kernel release, enter the following value:<br />GEN31IOPT  for 31I_COM and 31I_EXT<br />GEN40BOPT  for 40B_COM and 40B_EXT<br />GEN45BOPT  for 45B and 45B_EXT<br />GEN46DEOPT  for 46D and 46D_EXT EBCDIC<br />GEN46DAOPT  for 46D and 46D_EXT ASCII<br />GEN620AOPT  for 620 ASCII<br />GEN620UOPT  for 620 UNICODE<br />GEN640AOPT  for 640 ASCII<br />GEN640UOPT  for 640 UNICODE<br />GEN700AOPT  for 700 ASCII<br />GEN700UOPT  for 700 UNICODE<br />GEN701AOPT  for 701 ASCII<br />GEN701UOPT  for 701 UNICODE<br /></OL> <OL>4. KRNLIB<br />Enter the name of the library into which you want to apply the patch. *CURRENT means the kernel library that is currently used by your SAP system.<br /></OL> <OL>5. GETSAVF<br />Enter *NO if the save file that you specified for the parameter SAVF is already on your iSeries and already contains the patch.<br />Specify *YES if the patch file is located in the IFS or on another machine. In this case, APYR3FIX creates/overwrites the save file specified in the parameter SAVF.<br /></OL> <OL>6. FROMHOST<br />This parameter is only necessary if you have specified GETSAVF(*YES).<br />Specify *LOCAL if the patch file is located on your iSeries in the IFS.<br />Enter the name of the machine on which you saved the patch file.<br /></OL> <OL>7. FROMDIR<br />Enter the name of the directory in which the patch file is located.<br />If the patch is already in a save file on another iSeries, enter the library name of the save file here.</OL> <OL>8. FROMFILE<br />Enter the name of the patch file with the correct extension here. APYR3FIX then searches for the patch file on the specified machine (parameter FROMHOST) in the specified directory (parameter FROMDIR).<br />If one of the special values (*SAR, *SVF, *SAVF, *STM) is specified, APYR3FIX takes the name from the parameter SAVF<br />Important: Use *SAVF or the extension .savf if the patch is already in a save file on another iSeries.<br />In this case, specify the library name in which the save file is located for the parameter FROMDIR.<br /></OL> <OL>9. RMTUSER/RMTPWD<br />Enter the logon data here to be able to retrieve the patch files from another machine.</OL> <p></p> <b>Examples:<br /></b><br /> <OL>1. You want to get a kernel patch with APYR3FIX from sapservX and apply it to your iSeries AS001:<br /> APYR3FIX SID(...)<BR/>         SAVF(QGPL/DW)<BR/>         SAVLIB(GEN....OPT)<BR/>         KRNLIB(.......)<BR/>         GETSAVF(*YES)<BR/>         FROMHOST(sapservX)<BR/>         FROMDIR(&#39;general/R3server/patches/...&#39;)<BR/>         FROMFILE(*SAVF)</OL> <p></p> <OL>2. You want to use this save file to apply the patch to other application servers (AS002).<br /> APYR3FIX SID(...)<BR/>         SAVF(QTEMP/DW)<BR/>         SAVLIB(GEN....OPT)<BR/>         KRNLIB(.......)<BR/>         GETSAVF(*YES)<BR/>         FROMHOST(AS001)<BR/>         FROMDIR(QGPL)<BR/>         FROMFILE(*SAVF)</OL> <p></p> <OL>3. You must repeat the import.<br /> APYR3FIX SID(...)<BR/>         SAVF(QTEMP/DW)<BR/>         SAVLIB(GEN....OPT)<BR/>         KRNLIB(.......)<BR/>         GETSAVF(*NO)</OL> <OL>4. Your iSeries (AS001) contains a kernel patch as a .SAR file in the IFS in the directory /patches620.<br /> APYR3FIX SID(...)<BR/>         SAVF(QGPL/DW)<BR/>         SAVLIB(GEN....OPT)<BR/>         KRNLIB(.......)<BR/>         GETSAVF(*YES)<BR/>         FROMHOST(*LOCAL)<BR/>         FROMDIR(&#39;/patches620&#39;)<br />        FROMFILE(*SAR)<br />As a reminder, you must not specify QTEMP as the library for the parameter SAVF because SAPCAR is executed as a batch job.<br /></OL> <OL>5. You have saved a kernel patch as an .STM file on your PC PC001 on a share PATCH and you use the iSeries file system /QNTC to access the PC shares:<br /> APYR3FIX SID(...)<BR/>         SAVF(QGPL/DW)<BR/>         SAVLIB(GEN....OPT)<BR/>         KRNLIB(.......)<BR/>         GETSAVF(*YES)<BR/>         FROMHOST(*LOCAL)<BR/>         FROMDIR(&#39;/QNTC/PC001/PATCH&#39;)<BR/>         FROMFILE(*STM)</OL> <p></p> <b>Known issues:</b><br /> <OL>1. Object &lt;XXX> in library R3QTEMP not found.<br />Due to the conversion from the old version of APYR3FIX, the following error situation may occur:<br />APYR3FIX returns the objects required for the patch from the save file to the library PCH&lt;sid> and wants to pass control to the program R3INSTFIX. However, this program is created exclusively for the old version of APYR3FIX and expects the returned objects in the library R3QTEMP.<br />Proceed as follows:</OL> <UL><UL><LI>If necessary, create the library R3QTEMP with CRTLIB if it is missing.</LI></UL></UL> <UL><UL><LI>Empty the library R3QTEMP: CLRLIB R3QTEMP</LI></UL></UL> <UL><UL><LI>Execute APYR3FIX until terminated.</LI></UL></UL> <UL><UL><LI>Copy all objects from the library PCH&lt;sid> to the library R3QTEMP.</LI></UL></UL> <UL><UL><LI>Restart APYR3FIX.</LI></UL></UL> <p></p> <OL>2. Problems with the PTF check If there is a problem with the PTF check, it should be reported to SAP. The error can be regarded as insignificant for the application of the patch because this check occurs only after the patch has been applied successfully. The issue can be ignored for now and cleaned up at a later point in time.</OL> <p></p></div></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Operating System", "Value": "OS/400"}, {"Key": "Database System", "Value": "DB2/400"}, {"Key": "Owner                                                                                    ", "Value": "D022989"}, {"Key": "Processor                                                                                          ", "Value": ""}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000049365/D"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "MIME Type", "URL": "URL"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document references", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "URL"}, "Items": [{"RefNumber": "99797", "RefComponent": "BC-UPG", "RefTitle": "AS/400: Loading 3.1H kernels during upgrade", "RefUrl": "/notes/99797"}, {"RefNumber": "99597", "RefComponent": "BC-INS", "RefTitle": "AS/400: Applying the kernel from CD R31H7042", "RefUrl": "/notes/99597"}, {"RefNumber": "99379", "RefComponent": "BC-UPG-RDM", "RefTitle": "Enhancements to upgrade to 4.0B DB2/400", "RefUrl": "/notes/99379"}, {"RefNumber": "988777", "RefComponent": "BC-INS-AS4", "RefTitle": "iSeries: LODSAPKRN: allow long path names", "RefUrl": "/notes/988777"}, {"RefNumber": "97815", "RefComponent": "BC-INS-AS4", "RefTitle": "INST: 4.0B R/3 Installation on IBM AS/400", "RefUrl": "/notes/97815"}, {"RefNumber": "94472", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/94472"}, {"RefNumber": "912575", "RefComponent": "BC-OP-AS4", "RefTitle": "iSeries: Loading a 7.00 Kernel with LODSAPKRN", "RefUrl": "/notes/912575"}, {"RefNumber": "904977", "RefComponent": "BC-OP-AS4", "RefTitle": "iSeries: SP Stacks for SAP Kernel 6.40", "RefUrl": "/notes/904977"}, {"RefNumber": "86062", "RefComponent": "BC-UPG-RDM", "RefTitle": "Enhancements for upgrade to 4.0A DB2/400", "RefUrl": "/notes/86062"}, {"RefNumber": "86061", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/86061"}, {"RefNumber": "82728", "RefComponent": "BC-INS-AS4", "RefTitle": "AS/400: Installation of 3.1G on OS/400 V4R1M0", "RefUrl": "/notes/82728"}, {"RefNumber": "822369", "RefComponent": "BC-OP-AS4", "RefTitle": "iSeries: SAPSTART: MCH3601 of procedure Os400Release", "RefUrl": "/notes/822369"}, {"RefNumber": "822296", "RefComponent": "BC-UPG-RDM", "RefTitle": "Additional information about upgrading to SAP NW 2004s AS ABAP: iSeries", "RefUrl": "/notes/822296"}, {"RefNumber": "814431", "RefComponent": "BC-OP-AS4", "RefTitle": "iSeries: APYR3FIX: CPYFRMSTMF does not exist", "RefUrl": "/notes/814431"}, {"RefNumber": "808719", "RefComponent": "BC-OP-AS4", "RefTitle": "iSeries: APYR3FIX terminates due to missing program", "RefUrl": "/notes/808719"}, {"RefNumber": "796411", "RefComponent": "BC-OP-AS4", "RefTitle": "iSeries: STARTSAP fails with MCH5601", "RefUrl": "/notes/796411"}, {"RefNumber": "795418", "RefComponent": "BC-OP-AS4", "RefTitle": "iSeries: SAPCAR: operation not supported extracting car file", "RefUrl": "/notes/795418"}, {"RefNumber": "79376", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/79376"}, {"RefNumber": "77496", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/77496"}, {"RefNumber": "761187", "RefComponent": "BC-OP-AS4", "RefTitle": "iSeries: SAPCAR cannot run in batch", "RefUrl": "/notes/761187"}, {"RefNumber": "751131", "RefComponent": "BC-OP-AS4", "RefTitle": "iSeries: Applying a Kernel Patch to Release 6.40/7.00", "RefUrl": "/notes/751131"}, {"RefNumber": "74772", "RefComponent": "BC-UPG-RDM", "RefTitle": "Additional information about upgrading to 3.1H DB2/400", "RefUrl": "/notes/74772"}, {"RefNumber": "73749", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/73749"}, {"RefNumber": "732453", "RefComponent": "BC-OP-AS4", "RefTitle": "IBM i: SP Stacks for SAP Kernel 4.6D", "RefUrl": "/notes/732453"}, {"RefNumber": "73065", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/73065"}, {"RefNumber": "72342", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/72342"}, {"RefNumber": "71316", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/71316"}, {"RefNumber": "70372", "RefComponent": "BC-DB-DB4", "RefTitle": "AS/400: Developer license does not work", "RefUrl": "/notes/70372"}, {"RefNumber": "69733", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/69733"}, {"RefNumber": "69016", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/69016"}, {"RefNumber": "68998", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/68998"}, {"RefNumber": "67998", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/67998"}, {"RefNumber": "679602", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/679602"}, {"RefNumber": "66536", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/66536"}, {"RefNumber": "65685", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/65685"}, {"RefNumber": "64063", "RefComponent": "BC-CTS-CCO", "RefTitle": "R3trans Import: No main memory available (any more)", "RefUrl": "/notes/64063"}, {"RefNumber": "63624", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/63624"}, {"RefNumber": "627030", "RefComponent": "SV-SMG-SER", "RefTitle": "Error MESSAGE_TYPE_X in check group GL1_DB400", "RefUrl": "/notes/627030"}, {"RefNumber": "619072", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/619072"}, {"RefNumber": "609176", "RefComponent": "BC-CTS-TLS", "RefTitle": "Incomplete transport of R3TR MSAG (documentation missing)", "RefUrl": "/notes/609176"}, {"RefNumber": "580051", "RefComponent": "BC-OP-AS4", "RefTitle": "iSeries: Prepare Installation of Windows App Server (CPC)", "RefUrl": "/notes/580051"}, {"RefNumber": "564050", "RefComponent": "XX-SER-SAPSMP-SWC", "RefTitle": "iSeries: Reimplementing a Patch", "RefUrl": "/notes/564050"}, {"RefNumber": "546644", "RefComponent": "BC-DB-DB4", "RefTitle": "iSeries: (EXP) ERROR: DbSlLobGetPiece failed in R3LOAD", "RefUrl": "/notes/546644"}, {"RefNumber": "540406", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/540406"}, {"RefNumber": "522878", "RefComponent": "BC-OP-AS4", "RefTitle": "iSeries: Availability of CCMS Agents", "RefUrl": "/notes/522878"}, {"RefNumber": "51651", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/51651"}, {"RefNumber": "508886", "RefComponent": "BC-DB-DB4", "RefTitle": "iSeries: Client copy terminates.", "RefUrl": "/notes/508886"}, {"RefNumber": "495999", "RefComponent": "BC-OP-AS4", "RefTitle": "Installing a new version of the program APYR3FIX", "RefUrl": "/notes/495999"}, {"RefNumber": "487334", "RefComponent": "BC-INS-AS4", "RefTitle": "sapupgos4 reports &#39;unable to determine DB and SAP version&#39;", "RefUrl": "/notes/487334"}, {"RefNumber": "485479", "RefComponent": "BC-OP-AS4", "RefTitle": "R3trans: 2EETW152 Cannot open file \\&quot;/usr/sap/trans/tmp/...\\&quot;", "RefUrl": "/notes/485479"}, {"RefNumber": "43826", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/43826"}, {"RefNumber": "424301", "RefComponent": "BC-OP-AS4", "RefTitle": "iSeries: No object saved or loaded for library", "RefUrl": "/notes/424301"}, {"RefNumber": "406859", "RefComponent": "BC-INS", "RefTitle": "AS/400: Importing the 4.6D kernel (patch 543)", "RefUrl": "/notes/406859"}, {"RefNumber": "392165", "RefComponent": "BC-OP-AS4", "RefTitle": "iSeries: Known Issues under V5R1M0/V5R2M0", "RefUrl": "/notes/392165"}, {"RefNumber": "390665", "RefComponent": "BC-UPG-RDM", "RefTitle": "Enhancements for upgrade to 4.6C SR 2 (DB2/400)", "RefUrl": "/notes/390665"}, {"RefNumber": "390062", "RefComponent": "BC-UPG-RDM", "RefTitle": "Enhancements for upgrade to 4.6C SR2", "RefUrl": "/notes/390062"}, {"RefNumber": "384337", "RefComponent": "BC-INS", "RefTitle": "AS/400: Applying the 4.6D kernel (patch 457)", "RefUrl": "/notes/384337"}, {"RefNumber": "382862", "RefComponent": "BC-OP-AS4", "RefTitle": "APYR3FIX ends with error", "RefUrl": "/notes/382862"}, {"RefNumber": "37987", "RefComponent": "BC-OP-AS4", "RefTitle": "AS/400: Importing Transports", "RefUrl": "/notes/37987"}, {"RefNumber": "330793", "RefComponent": "XX-SER-SAPSMP-SWC", "RefTitle": "Downloading patches", "RefUrl": "/notes/330793"}, {"RefNumber": "318846", "RefComponent": "BC-CST", "RefTitle": "Installation of 4.6D kernel in 4.6A/B/C SAP systems", "RefUrl": "/notes/318846"}, {"RefNumber": "316353", "RefComponent": "BC-INS-MIG", "RefTitle": "INST: 4.6D SAP Basis - Heterogeneous System Copy", "RefUrl": "/notes/316353"}, {"RefNumber": "313721", "RefComponent": "BC-SRV-KPR-CMS", "RefTitle": "HTTP CSrv interface on non-ASCII platforms", "RefUrl": "/notes/313721"}, {"RefNumber": "300279", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/300279"}, {"RefNumber": "205699", "RefComponent": "BC-OP-AS4", "RefTitle": "AS/400: General Recommendations for Problems", "RefUrl": "/notes/205699"}, {"RefNumber": "202169", "RefComponent": "BC-UPG-RDM", "RefTitle": "Enhancements for upgrade to 4.6C DB2/400", "RefUrl": "/notes/202169"}, {"RefNumber": "19466", "RefComponent": "XX-SER-SAPSMP-SWC", "RefTitle": "Downloading SAP Kernel Patches", "RefUrl": "/notes/19466"}, {"RefNumber": "194247", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/194247"}, {"RefNumber": "187007", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/187007"}, {"RefNumber": "178823", "RefComponent": "BC-UPG-RDM", "RefTitle": "Enhancements for upgrade to 4.6B DB2/400", "RefUrl": "/notes/178823"}, {"RefNumber": "162117", "RefComponent": "BC-UPG-RDM", "RefTitle": "Enhancements for upgrade to 4.6A DB2/400", "RefUrl": "/notes/162117"}, {"RefNumber": "156314", "RefComponent": "BC-UPG-TLS", "RefTitle": "Upgrade to Release 4.5B: Termination in phase DIFFEXP*", "RefUrl": "/notes/156314"}, {"RefNumber": "149682", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/149682"}, {"RefNumber": "141661", "RefComponent": "BC-UPG-RDM", "RefTitle": "Enhancements for upgrade to 4.5B DB2/400", "RefUrl": "/notes/141661"}, {"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Advance transports to customer (note for customers)", "RefUrl": "/notes/13719"}, {"RefNumber": "1310133", "RefComponent": "BC-OP-AS4", "RefTitle": "IBM i: Loading a 7.01 kernel with LODSAPKRN", "RefUrl": "/notes/1310133"}, {"RefNumber": "123983", "RefComponent": "BC-CTS-TLS", "RefTitle": "R3trans: Missing includes for function groups with &#39;_&#39;", "RefUrl": "/notes/123983"}, {"RefNumber": "123538", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/123538"}, {"RefNumber": "123418", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/123418"}, {"RefNumber": "115752", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/115752"}, {"RefNumber": "1137680", "RefComponent": "BC-OP-AS4", "RefTitle": "i5/OS: 700 tools no longer work after patch", "RefUrl": "/notes/1137680"}, {"RefNumber": "111730", "RefComponent": "BC-INS-AS4", "RefTitle": "AS/400: MCH3601 in program DBADJDB4", "RefUrl": "/notes/111730"}, {"RefNumber": "111443", "RefComponent": "BC-DB-DB4", "RefTitle": "AS/400: R/3 Monitor for Database Locks", "RefUrl": "/notes/111443"}, {"RefNumber": "108377", "RefComponent": "BC-UPG-RDM", "RefTitle": "Enhancements for upgrade to 4.5A DB2/400", "RefUrl": "/notes/108377"}, {"RefNumber": "1079953", "RefComponent": "BC-OP-AS4", "RefTitle": "LODSAPKRN: Could not find SAPCAR for extraction", "RefUrl": "/notes/1079953"}, {"RefNumber": "102950", "RefComponent": "BC-UPG-RDM", "RefTitle": "Additional information about upgrading to 3.1I DB2/400", "RefUrl": "/notes/102950"}, {"RefNumber": "102461", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/102461"}, {"RefNumber": "102445", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/102445"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "URL"}, "Items": [{"RefNumber": "822296", "RefComponent": "BC-UPG-RDM", "RefTitle": "Additional information about upgrading to SAP NW 2004s AS ABAP: iSeries", "RefUrl": "/notes/822296 "}, {"RefNumber": "86062", "RefComponent": "BC-UPG-RDM", "RefTitle": "Enhancements for upgrade to 4.0A DB2/400", "RefUrl": "/notes/86062 "}, {"RefNumber": "97815", "RefComponent": "BC-INS-AS4", "RefTitle": "INST: 4.0B R/3 Installation on IBM AS/400", "RefUrl": "/notes/97815 "}, {"RefNumber": "99379", "RefComponent": "BC-UPG-RDM", "RefTitle": "Enhancements to upgrade to 4.0B DB2/400", "RefUrl": "/notes/99379 "}, {"RefNumber": "108377", "RefComponent": "BC-UPG-RDM", "RefTitle": "Enhancements for upgrade to 4.5A DB2/400", "RefUrl": "/notes/108377 "}, {"RefNumber": "141661", "RefComponent": "BC-UPG-RDM", "RefTitle": "Enhancements for upgrade to 4.5B DB2/400", "RefUrl": "/notes/141661 "}, {"RefNumber": "162117", "RefComponent": "BC-UPG-RDM", "RefTitle": "Enhancements for upgrade to 4.6A DB2/400", "RefUrl": "/notes/162117 "}, {"RefNumber": "178823", "RefComponent": "BC-UPG-RDM", "RefTitle": "Enhancements for upgrade to 4.6B DB2/400", "RefUrl": "/notes/178823 "}, {"RefNumber": "202169", "RefComponent": "BC-UPG-RDM", "RefTitle": "Enhancements for upgrade to 4.6C DB2/400", "RefUrl": "/notes/202169 "}, {"RefNumber": "313721", "RefComponent": "BC-SRV-KPR-CMS", "RefTitle": "HTTP CSrv interface on non-ASCII platforms", "RefUrl": "/notes/313721 "}, {"RefNumber": "64063", "RefComponent": "BC-CTS-CCO", "RefTitle": "R3trans Import: No main memory available (any more)", "RefUrl": "/notes/64063 "}, {"RefNumber": "424301", "RefComponent": "BC-OP-AS4", "RefTitle": "iSeries: No object saved or loaded for library", "RefUrl": "/notes/424301 "}, {"RefNumber": "392165", "RefComponent": "BC-OP-AS4", "RefTitle": "iSeries: Known Issues under V5R1M0/V5R2M0", "RefUrl": "/notes/392165 "}, {"RefNumber": "382862", "RefComponent": "BC-OP-AS4", "RefTitle": "APYR3FIX ends with error", "RefUrl": "/notes/382862 "}, {"RefNumber": "205699", "RefComponent": "BC-OP-AS4", "RefTitle": "AS/400: General Recommendations for Problems", "RefUrl": "/notes/205699 "}, {"RefNumber": "316353", "RefComponent": "BC-INS-MIG", "RefTitle": "INST: 4.6D SAP Basis - Heterogeneous System Copy", "RefUrl": "/notes/316353 "}, {"RefNumber": "1310133", "RefComponent": "BC-OP-AS4", "RefTitle": "IBM i: Loading a 7.01 kernel with LODSAPKRN", "RefUrl": "/notes/1310133 "}, {"RefNumber": "732453", "RefComponent": "BC-OP-AS4", "RefTitle": "IBM i: SP Stacks for SAP Kernel 4.6D", "RefUrl": "/notes/732453 "}, {"RefNumber": "74772", "RefComponent": "BC-UPG-RDM", "RefTitle": "Additional information about upgrading to 3.1H DB2/400", "RefUrl": "/notes/74772 "}, {"RefNumber": "102950", "RefComponent": "BC-UPG-RDM", "RefTitle": "Additional information about upgrading to 3.1I DB2/400", "RefUrl": "/notes/102950 "}, {"RefNumber": "390665", "RefComponent": "BC-UPG-RDM", "RefTitle": "Enhancements for upgrade to 4.6C SR 2 (DB2/400)", "RefUrl": "/notes/390665 "}, {"RefNumber": "912575", "RefComponent": "BC-OP-AS4", "RefTitle": "iSeries: Loading a 7.00 Kernel with LODSAPKRN", "RefUrl": "/notes/912575 "}, {"RefNumber": "318846", "RefComponent": "BC-CST", "RefTitle": "Installation of 4.6D kernel in 4.6A/B/C SAP systems", "RefUrl": "/notes/318846 "}, {"RefNumber": "1137680", "RefComponent": "BC-OP-AS4", "RefTitle": "i5/OS: 700 tools no longer work after patch", "RefUrl": "/notes/1137680 "}, {"RefNumber": "609176", "RefComponent": "BC-CTS-TLS", "RefTitle": "Incomplete transport of R3TR MSAG (documentation missing)", "RefUrl": "/notes/609176 "}, {"RefNumber": "508886", "RefComponent": "BC-DB-DB4", "RefTitle": "iSeries: Client copy terminates.", "RefUrl": "/notes/508886 "}, {"RefNumber": "384337", "RefComponent": "BC-INS", "RefTitle": "AS/400: Applying the 4.6D kernel (patch 457)", "RefUrl": "/notes/384337 "}, {"RefNumber": "406859", "RefComponent": "BC-INS", "RefTitle": "AS/400: Importing the 4.6D kernel (patch 543)", "RefUrl": "/notes/406859 "}, {"RefNumber": "495999", "RefComponent": "BC-OP-AS4", "RefTitle": "Installing a new version of the program APYR3FIX", "RefUrl": "/notes/495999 "}, {"RefNumber": "796411", "RefComponent": "BC-OP-AS4", "RefTitle": "iSeries: STARTSAP fails with MCH5601", "RefUrl": "/notes/796411 "}, {"RefNumber": "522878", "RefComponent": "BC-OP-AS4", "RefTitle": "iSeries: Availability of CCMS Agents", "RefUrl": "/notes/522878 "}, {"RefNumber": "1079953", "RefComponent": "BC-OP-AS4", "RefTitle": "LODSAPKRN: Could not find SAPCAR for extraction", "RefUrl": "/notes/1079953 "}, {"RefNumber": "751131", "RefComponent": "BC-OP-AS4", "RefTitle": "iSeries: Applying a Kernel Patch to Release 6.40/7.00", "RefUrl": "/notes/751131 "}, {"RefNumber": "988777", "RefComponent": "BC-INS-AS4", "RefTitle": "iSeries: LODSAPKRN: allow long path names", "RefUrl": "/notes/988777 "}, {"RefNumber": "904977", "RefComponent": "BC-OP-AS4", "RefTitle": "iSeries: SP Stacks for SAP Kernel 6.40", "RefUrl": "/notes/904977 "}, {"RefNumber": "814431", "RefComponent": "BC-OP-AS4", "RefTitle": "iSeries: APYR3FIX: CPYFRMSTMF does not exist", "RefUrl": "/notes/814431 "}, {"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Advance transports to customer (note for customers)", "RefUrl": "/notes/13719 "}, {"RefNumber": "390062", "RefComponent": "BC-UPG-RDM", "RefTitle": "Enhancements for upgrade to 4.6C SR2", "RefUrl": "/notes/390062 "}, {"RefNumber": "822369", "RefComponent": "BC-OP-AS4", "RefTitle": "iSeries: SAPSTART: MCH3601 of procedure Os400Release", "RefUrl": "/notes/822369 "}, {"RefNumber": "808719", "RefComponent": "BC-OP-AS4", "RefTitle": "iSeries: APYR3FIX terminates due to missing program", "RefUrl": "/notes/808719 "}, {"RefNumber": "795418", "RefComponent": "BC-OP-AS4", "RefTitle": "iSeries: SAPCAR: operation not supported extracting car file", "RefUrl": "/notes/795418 "}, {"RefNumber": "761187", "RefComponent": "BC-OP-AS4", "RefTitle": "iSeries: SAPCAR cannot run in batch", "RefUrl": "/notes/761187 "}, {"RefNumber": "627030", "RefComponent": "SV-SMG-SER", "RefTitle": "Error MESSAGE_TYPE_X in check group GL1_DB400", "RefUrl": "/notes/627030 "}, {"RefNumber": "111443", "RefComponent": "BC-DB-DB4", "RefTitle": "AS/400: R/3 Monitor for Database Locks", "RefUrl": "/notes/111443 "}, {"RefNumber": "330793", "RefComponent": "XX-SER-SAPSMP-SWC", "RefTitle": "Downloading patches", "RefUrl": "/notes/330793 "}, {"RefNumber": "580051", "RefComponent": "BC-OP-AS4", "RefTitle": "iSeries: Prepare Installation of Windows App Server (CPC)", "RefUrl": "/notes/580051 "}, {"RefNumber": "564050", "RefComponent": "XX-SER-SAPSMP-SWC", "RefTitle": "iSeries: Reimplementing a Patch", "RefUrl": "/notes/564050 "}, {"RefNumber": "546644", "RefComponent": "BC-DB-DB4", "RefTitle": "iSeries: (EXP) ERROR: DbSlLobGetPiece failed in R3LOAD", "RefUrl": "/notes/546644 "}, {"RefNumber": "70372", "RefComponent": "BC-DB-DB4", "RefTitle": "AS/400: Developer license does not work", "RefUrl": "/notes/70372 "}, {"RefNumber": "82728", "RefComponent": "BC-INS-AS4", "RefTitle": "AS/400: Installation of 3.1G on OS/400 V4R1M0", "RefUrl": "/notes/82728 "}, {"RefNumber": "99597", "RefComponent": "BC-INS", "RefTitle": "AS/400: Applying the kernel from CD R31H7042", "RefUrl": "/notes/99597 "}, {"RefNumber": "99797", "RefComponent": "BC-UPG", "RefTitle": "AS/400: Loading 3.1H kernels during upgrade", "RefUrl": "/notes/99797 "}, {"RefNumber": "156314", "RefComponent": "BC-UPG-TLS", "RefTitle": "Upgrade to Release 4.5B: Termination in phase DIFFEXP*", "RefUrl": "/notes/156314 "}, {"RefNumber": "111730", "RefComponent": "BC-INS-AS4", "RefTitle": "AS/400: MCH3601 in program DBADJDB4", "RefUrl": "/notes/111730 "}, {"RefNumber": "123983", "RefComponent": "BC-CTS-TLS", "RefTitle": "R3trans: Missing includes for function groups with &#39;_&#39;", "RefUrl": "/notes/123983 "}, {"RefNumber": "37987", "RefComponent": "BC-OP-AS4", "RefTitle": "AS/400: Importing Transports", "RefUrl": "/notes/37987 "}, {"RefNumber": "487334", "RefComponent": "BC-INS-AS4", "RefTitle": "sapupgos4 reports &#39;unable to determine DB and SAP version&#39;", "RefUrl": "/notes/487334 "}, {"RefNumber": "485479", "RefComponent": "BC-OP-AS4", "RefTitle": "R3trans: 2EETW152 Cannot open file \\&quot;/usr/sap/trans/tmp/...\\&quot;", "RefUrl": "/notes/485479 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And the following"}, "Items": [{"SoftwareComponent": "SAP_APPL", "From": "31I", "To": "31I", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "40B", "To": "40B", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "45B", "To": "45B", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "46C", "To": "46C", "Subsequent": ""}, {"SoftwareComponent": "SAP_BASIS", "From": "46D", "To": "46D", "Subsequent": ""}, {"SoftwareComponent": "SAP_BASIS", "From": "640", "To": "640", "Subsequent": ""}, {"SoftwareComponent": "SAP_BASIS", "From": "700", "To": "701", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software-Komponentenversion", "SupportPackage": "Support Package", "URL": "URL"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Packages and Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "URL"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "URL"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about correction instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisite", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisite", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "URL", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated as helpful", "Helpful-Yes": "0 persons"}, "RatingQuality": {"_label": "Quality Assessment"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document eliminates side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "URL"}, "Items": []}, "SideEffectsSolving": {"_label": "This document causes side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "URL"}, "Items": []}}, "DisclaimerForMachineTranslation": {"Header": "ATTENTION!", "Message": "This is a machine-translated document that has not been reviewed for accuracy.", "Survey": "To provide feedback regarding the machine translation, please <a href=\"https://sapinsights.eu.qualtrics.com/jfe/form/SV_6nI2C1MOwEviVQG?NoteNumber=49365&TargetLanguage=EN&Component=XX-INT-FA-MAKE&SourceLanguage=DE&Priority=06\" target=\"_blank\">click here</a>.", "Request": "If the quality of the machine translation is not sufficient for your purposes, click here to request a revised version.", "Origin": "You can access the original document via the following link: <a href=\"/notes/49365/D\" target=\"_blank\">/notes/49365/D</a>."}}}}