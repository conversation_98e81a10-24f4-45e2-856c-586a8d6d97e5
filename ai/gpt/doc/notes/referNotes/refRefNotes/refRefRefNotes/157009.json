{"Request": {"Number": "157009", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 603, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000014689342017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000157009?language=E&token=9B01D4F18B54CFB775C361D886728211"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000157009", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000157009/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "157009"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 47}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Legal change"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "02.02.2007"}, "SAPComponentKey": {"_label": "Component", "value": "XX-CSC-GB"}, "SAPComponentKeyText": {"_label": "Component", "value": "Great Britain"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Miscellaneous", "value": "XX", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Country/Region-Specific Developments", "value": "XX-CSC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-CSC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Great Britain", "value": "XX-CSC-GB", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-CSC-GB*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "157009 - Construction Industry Scheme (CIS)  UK"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><b>Update January 2007:</b><br /> <p>The New CIS legal Change Implementation note is released. The note number is 1003730.<br /><br />This note will address the following requirements:<br /><br />a. Generate the Generic flat file for submissions through EDI to HMRC<br />b. Payments and Deductions Statement, to be provided to all &#x00A0;&#x00A0; sub-contractors<br />c. List output for supporting low-volume return filings using the HMRC web-site.<br />The solution performs all the validations as per HMRC guidelines.<br /><br />SAP is in the process of getting the internet submission recognition from HMRC for the new CIS returns.<br />Once this is obtained, the SAP XI content for internet transmission of CIS returns will be delivered.<br /><br /></p> <b>Update November 2005:</b><br /> <p>The legal change for UK CIS is postponed from April 2006 to April 2007.<br />SAP has changed the schedule for delivery accordingly.</p> <b>Modified schedule for CIS 2007 legal change:</b><br /> <UL><LI>Development of backend functionality (File): By October 2006</LI></UL> <UL><LI>Development of transmission functionality (XI): By December 2006</LI></UL> <UL><LI>Final delivery of the legal change: End of December</LI></UL> <b></b><br /> <b>Update August 2005:</b><br /> <b>Schedule for CIS 2006 Legal Change:</b><br /> <UL><LI>Development of backend functionality (File): End of November</LI></UL> <UL><LI>Development of transmission functionality (XI): End of January</LI></UL> <UL><LI>Final delivery of the legal change: End of January</LI></UL> <p><br />Update May 2005: In April 2006 the UK tax authority (HMRC) will introduce revisions to the existing CIS. SAP is currently obtaining information on the new scheme and evaluating requirements for development purposes.The current scheme with subcontractor cards, certficates and monthly vouchers (reporting) will cease in March 2006.<br />Original text<br />In August 1999 a new construction industry scheme was introduced by the Inland Revenue in the UK.</p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>UK, withholding tax, Construction Industry Scheme, CIS</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>Legal change information</p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>Solution for releases up to and including 45B are delivered via support package.<br />The changes include:</p> <UL><LI>new report RFKQST90&#x00A0;&#x00A0;(from and including release 46C reporting is with the standard report RFIDYYWT</LI></UL> <UL><LI>updated reports: RFKQST10, RFKQST20</LI></UL> <UL><LI>SAP-Script forms: F_RFKQST90_23I, F_RFKQST90_25I, F_RFKQST90_25I</LI></UL> <UL><LI>New messages</LI></UL> <UL><LI>Number range object</LI></UL> <UL><LI>Table content T001I/J</LI></UL> <p></p> <b>CIS - General Information</b><br /> <p>There follows some information on how to customize the system according to the new regulations.<br />If you are using already withholding tax then you have to continue using it also on Release 4.0 until a migration is available. &gt;Update May 2005 Migration is available - see note 390148.&lt; Otherwise we recommend using the Extended Withholding Tax functionality from Release 4.0. The Extended Withholding Tax functionality requires the use of the logistics invoice verification.</p> <b>Configuration - Extended Withholding Tax (WT) functionality</b><br /> <OL>1. IMG -&gt; Financial Accounting Global Settings -&gt; Company code</OL> <UL><LI>Activate Extended Withholding Tax</LI></UL> <OL>2. Financial Accounting Global Settings -&gt; Extended Withholding Tax -&gt; Basic Settings</OL> <OL><OL>a) Check WT countries</OL></OL> <UL><UL><LI>Add 'GB'</LI></UL></UL> <OL><OL>b) Define Official Withholding Tax Codes</OL></OL> <UL><UL><LI>CIS 4</LI></UL></UL> <UL><UL><LI>CIS 5</LI></UL></UL> <UL><UL><LI>CIS 6</LI></UL></UL> <OL><OL>c) Define reasons for exemption</OL></OL> <UL><UL><LI>01 Outside the scheme</LI></UL></UL> <UL><UL><LI>02 Valid Certificate</LI></UL></UL> <OL>3. Financial Accounting Global Settings -&gt; Extended Withholding Tax -&gt; Calculation -&gt; Withholding Tax Type</OL> <UL><LI>Define WT type for payment posting</LI></UL> <UL><UL><LI>04 CIS 4</LI></UL></UL> <UL><UL><LI>05 CIS 5</LI></UL></UL> <UL><UL><LI>06 CIS 6</LI></UL></UL> <UL><UL><LI>99 net with manual base amt</LI></UL></UL> <UL><UL><LI>The base amount should be net of VAT. Cash discount is calculated before withholding tax and ensure that field \"Post w/tax amount\" is flagged. Under control data flag field \"W/tax base manual\", field \"W/tax for payments\" and field \"No cert.numbering\".</LI></UL></UL> <OL>4. Financial Accounting Global Settings -&gt; Extended Withholding Tax -&gt; Calculation -&gt; WT codes</OL> <OL><OL>a) Define WT codes</OL></OL> <UL><UL><LI>04 01 4-01</LI></UL></UL> <UL><UL><LI>05 01 5-01</LI></UL></UL> <UL><UL><LI>06 01 6-01</LI></UL></UL> <UL><UL><LI>99 01 No certifiacte/card</LI></UL></UL> <UL><UL><LI>The base amount subject to tax is always 100% in the UK. The tax rate depends on the Inland Revenue and the tax year. It is currently 18%. The Posting Indicator should be \"1\" - create separate WT deduction line.</LI></UL></UL> <OL><OL>b) Financial Accounting Global Settings -&gt; Extended Withholding Tax -&gt; Company code</OL></OL> <b>Vendor master record -&#x00A0;&#x00A0;Extended Withholding Tax</b><br /> <UL><LI>The new 13 digit certificate/registration card number can be stored in the relevant field.</LI></UL> <UL><LI>The vendor's National Insurance number, which is required for reporting,  will be stored in the tax code field 2 (LFA1-STCD2).</LI></UL> <b>Vendor master record -&#x00A0;&#x00A0;Withholding Tax</b><br /> <UL><LI>The new 13 digit certificate/registration card number can be stored in the tax code field 1 (LFA1-STCD1).</LI></UL> <UL><LI>The vendor's National Insurance number, which is required for reporting,  will be stored in the tax code field 2 (LFA1-STCD2).</LI></UL> <b>General Configuration for Reporting</b><br /> <UL><LI>If you use EDI to submit data, a contractor EDI reference is required. We recommend that this is set up as a new parameter ID. For both EDI and pre-numbered vouchers a contractor tax reference is required.<br />Transaction: SE16 Table:&#x00A0;&#x00A0;T001I<br />Parameter type:&#x00A0;&#x00A0;GBCISE<br />Length:&#x00A0;&#x00A0;04<br />ISC-Code: GB<br />Text:&#x00A0;&#x00A0;Contractor EDI Id<br /><br />Parameter type&#x00A0;&#x00A0;GBCISR<br />Length&#x00A0;&#x00A0;13<br />ISC-Code GB<br />Text&#x00A0;&#x00A0;CIS Contractor Tax Reference<br /></LI></UL> <UL><LI>If you would to translate the parameter in a language different to your logon language:<br />Transaction: SE16<br />Table:&#x00A0;&#x00A0;T001J<br /><br />Language xx&#x00A0;&#x00A0;(xx = language key)<br />Parameter type&#x00A0;&#x00A0;GBCISE<br />Text&#x00A0;&#x00A0;Contractor EDI Id<br /><br />Language xx&#x00A0;&#x00A0;(xx = language key)<br />Parameter type&#x00A0;&#x00A0;GBCISR<br />Text&#x00A0;&#x00A0;CIS Contractor Tax Reference</LI></UL> <UL><LI>Update the additional parameter of your company code.</LI></UL> <p></p> <b>UK - Validation of CIS Certificates</b><br /> <p>If the vendor has no valid certificate, an invoice must be blocked for payment. The standard tool Validation offers the possibility to create individually customized checking rules. The validation will apply for invoices posted via FI or MM. Also the validation applies to the payment program, which would exclude documents of vendors without valid certificates.</p> <b>Customizing Validation</b><br /> <p>Following you will find the possible set-up. The vendor has to have the certificate number assigned in field tax number 1. If the vendor has no certificate, you will receive an error message, when you want to post the document without a payment block.</p> <OL>1. Create own Messages:</OL> <UL><LI>Tools -&gt; ABAP/4 Workbench -&gt; Development -&gt; Programming environment -&gt; Messages</LI></UL> <UL><LI>Create Header for Message class</LI></UL> <UL><LI>Create Message:<br />No. Description 001<br />Vendor without valid CIS certificate -&gt; no payment</LI></UL> <OL>2. Create User Exit:</OL> <p>Tools -&gt; ABAP/4 Workbench -&gt; ABAP/4 Editor<br />Create Report ZGGBRxxx. If this validation should be used for all clients, then the report name would be ZGGBR000, otherwise replace 'xxx' with your client. You could copy RGGBR000 as an example. Please, define your User Exit of client depend validation with the transaction GCX2.</p> <b>User Exit - Classic Withholding Tax<br />PROGRAM ZGGBR003 .&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;\"example for client 003<br />INCLUDE FGBBGD00.&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;\"Data types<br />&lt;&lt;&lt; Begin of Insert<br />tables: LFA1.<br />Tables: lfb1.<br />&lt;&lt;&lt; end of insert<br /><br />FORM GET_EXIT_TITLES TABLES ETAB.<br /><br />&#x00A0;&#x00A0;DATA: BEGIN OF EXITS OCCURS 50,<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;NAME(5)&#x00A0;&#x00A0;TYPE C,<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;PARAM&#x00A0;&#x00A0;&#x00A0;&#x00A0;LIKE C_EXIT_PARAM_NONE,<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;TITLE(60) TYPE C,<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;END OF EXITS.<br /><br />&lt;&lt;&lt; Begin of Insert<br />&#x00A0;&#x00A0; EXITS-NAME&#x00A0;&#x00A0;= 'U902'.<br />&#x00A0;&#x00A0;EXITS-PARAM = C_EXIT_PARAM_NONE.&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;\"Complete data used in exit.<br />&#x00A0;&#x00A0;EXITS-TITLE = TEXT-902.&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;\"UK valid Vendor certificate<br />&#x00A0;&#x00A0; APPEND EXITS.<br />&lt;&lt;&lt; end of insert<br /></b><br /> <p>Add the following module:<br />*eject<br />*----------------------------------------------------------------------*<br />*&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; FORM U902<br />*----------------------------------------------------------------------*<br />*&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Example of an exit using to validate the UK CIS certificate<br />*----------------------------------------------------------------------*<br />*&#x00A0;&#x00A0;--&gt;&#x00A0;&#x00A0;BOOL_DATA&#x00A0;&#x00A0;The complete posting data.<br />*&#x00A0;&#x00A0;&lt;--&#x00A0;&#x00A0;B_RESULT&#x00A0;&#x00A0;&#x00A0;&#x00A0;T = True&#x00A0;&#x00A0;F = False<br />*----------------------------------------------------------------------*<br />FORM U902&#x00A0;&#x00A0;USING B_RESULT.<br /><br />if bseg-qsskz &lt;&gt; space.<br />* select vendor<br />&#x00A0;&#x00A0;select single * from lfa1 where LIFNR = bseg-lifnr.<br />&#x00A0;&#x00A0;select single * from lfb1 where bukrs = bseg-bukrs<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;and LIFNR = bseg-lifnr.<br />* check, if it is a UK vendor has a certificate or a payment block, if<br />* the certificate is not maintained<br />&#x00A0;&#x00A0;if lfa1-land1 = 'GB'.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;if lfa1-STCD1 eq space and<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;BSEG-ZLSPR eq space.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;b_result&#x00A0;&#x00A0;= b_false.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;endif.<br />* check, if the certificate is valid<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;if lfb1-qszdt &lt; bkpf-budat and<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;BSEG-ZLSPR = space.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;b_result&#x00A0;&#x00A0;= b_false.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;endif.<br />endif.<br />ENDFORM.<br /></p> <b>User Exit - Extended Withholding Tax<br />PROGRAM ZGGBR003 .&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;\"example for client 003<br />INCLUDE FGBBGD00.&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;\"Data types<br /><br />&lt;&lt;&lt; Begin of Insert<br />tables: LFA1.<br />tables: lfbw.<br /><br />&lt;&lt;&lt; end of insert<br /><br />FORM GET_EXIT_TITLES TABLES ETAB.<br /><br />&#x00A0;&#x00A0;DATA: BEGIN OF EXITS OCCURS 50,<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;NAME(5)&#x00A0;&#x00A0;TYPE C,<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;PARAM&#x00A0;&#x00A0;&#x00A0;&#x00A0;LIKE C_EXIT_PARAM_NONE,<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;TITLE(60) TYPE C,<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;END OF EXITS.<br /><br />&lt;&lt;&lt; Begin of Insert<br />&#x00A0;&#x00A0; EXITS-NAME&#x00A0;&#x00A0;= 'U902'.<br />&#x00A0;&#x00A0;EXITS-PARAM = C_EXIT_PARAM_NONE.&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;\"Complete data used in exit.<br />&#x00A0;&#x00A0;EXITS-TITLE = TEXT-902.&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;\"UK valid Vendor certificate<br />&#x00A0;&#x00A0;APPEND EXITS.&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;\"Data types<br />&lt;&lt;&lt; end of insert<br /><br />*----------------------------------------------------------------------*<br />*&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;FORM U902<br />*----------------------------------------------------------------------*<br />*&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Example of an exit using to validate the UK CIS certificate EWT<br />*----------------------------------------------------------------------*<br />*&#x00A0;&#x00A0;--&gt;&#x00A0;&#x00A0;BOOL_DATA&#x00A0;&#x00A0;The complete posting data.<br />*&#x00A0;&#x00A0;&lt;--&#x00A0;&#x00A0;B_RESULT&#x00A0;&#x00A0;&#x00A0;&#x00A0;T = True&#x00A0;&#x00A0;F = False<br />*----------------------------------------------------------------------*<br /><br />FORM U902&#x00A0;&#x00A0;USING B_RESULT.<br /><br />* select vendor<br />&#x00A0;&#x00A0;select single * from lfa1 where LIFNR = bseg-lifnr.<br /><br />* check, if it is a UK vendor has a certificate or a payment block, if<br />* the certificate is not maintained<br />&#x00A0;&#x00A0;if lfa1-land1 = 'GB'.<br />*&#x00A0;&#x00A0;read ETW Data (vendors without any WT data will be excluded)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;select * from lfbw where bukrs = bseg-bukrs<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;and LIFNR = bseg-lifnr<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;and WT_EXDF &lt;= bkpf-budat<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;if lfbw-WT_EXNR eq space and<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;BSEG-ZLSPR eq space.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;b_result&#x00A0;&#x00A0;= b_false.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;endif.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;endselect.<br />&#x00A0;&#x00A0;endif.<br />ENDFORM.<br /></b><br /> <OL><OL>a) Define/Activate Validation for a company code:</OL></OL> <UL><LI>IMG -&gt; Financial&#x00A0;&#x00A0;Accounting -&gt; Financial Accounting Global Settings - Document -&gt; Document Header -&gt; Define validations for posting</LI></UL> <UL><LI>Define the validation for your company code and call-up point 2 and Activation level '1'</LI></UL> <OL><OL>a) Example: Validation of the certificate<br />Prerequisite<br /> &lt;BSEG&gt; $KOART = 'K'<br />Check<br />=U902<br />Message<br />Typ 'E' No. 001 Vendor without valid CIS certificate -&gt; no payment<br /><br />From Release 4.0A, please use exit name 'U902' instead of '=U902'.</OL></OL> <p>------------------------------------------------------------------------<br />Additional release information:</p> <UL><LI>Reference to Support package 46B: SAPKH46B04<br /><br /></LI></UL></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "FI-AP-AP-Q1 (Withholding Tax (Reporting))"}, {"Key": "Responsible                                                                                         ", "Value": "I034465"}, {"Key": "Processor                                                                                           ", "Value": "I033066"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000157009/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000157009/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000157009/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000157009/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000157009/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000157009/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000157009/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000157009/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000157009/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "390148", "RefComponent": "FI-AP-AP-Q", "RefTitle": "WTMG: Composite note for withholding tax changeover", "RefUrl": "/notes/390148"}, {"RefNumber": "351043", "RefComponent": "FI-AP-AP-Q", "RefTitle": "F4-Help is missing for w/tax code in RFKQST90", "RefUrl": "/notes/351043"}, {"RefNumber": "21738", "RefComponent": "BC-CCM-PRN-DVM", "RefTitle": "Device type SAPWIN", "RefUrl": "/notes/21738"}, {"RefNumber": "211317", "RefComponent": "XX-CSC", "RefTitle": "UK Annual CIS return - RFKQST90", "RefUrl": "/notes/211317"}, {"RefNumber": "163136", "RefComponent": "BC-CCM-PRN-DVM", "RefTitle": "Creating new paper size for Windows NT/2000/XP/2003", "RefUrl": "/notes/163136"}, {"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "21738", "RefComponent": "BC-CCM-PRN-DVM", "RefTitle": "Device type SAPWIN", "RefUrl": "/notes/21738 "}, {"RefNumber": "163136", "RefComponent": "BC-CCM-PRN-DVM", "RefTitle": "Creating new paper size for Windows NT/2000/XP/2003", "RefUrl": "/notes/163136 "}, {"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719 "}, {"RefNumber": "211317", "RefComponent": "XX-CSC", "RefTitle": "UK Annual CIS return - RFKQST90", "RefUrl": "/notes/211317 "}, {"RefNumber": "390148", "RefComponent": "FI-AP-AP-Q", "RefTitle": "WTMG: Composite note for withholding tax changeover", "RefUrl": "/notes/390148 "}, {"RefNumber": "351043", "RefComponent": "FI-AP-AP-Q", "RefTitle": "F4-Help is missing for w/tax code in RFKQST90", "RefUrl": "/notes/351043 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_APPL", "From": "30F", "To": "31I", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "40A", "To": "40B", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "45A", "To": "45B", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "46B", "To": "46B", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "46C", "To": "46C", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "470", "To": "470", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "SAP_APPL 31H", "SupportPackage": "SAPKH31H58", "URL": "/supportpackage/SAPKH31H58"}, {"SoftwareComponentVersion": "SAP_APPL 31H", "SupportPackage": "SAPKH31H57", "URL": "/supportpackage/SAPKH31H57"}, {"SoftwareComponentVersion": "SAP_APPL 31H", "SupportPackage": "SAPKH31H65", "URL": "/supportpackage/SAPKH31H65"}, {"SoftwareComponentVersion": "SAP_APPL 31H", "SupportPackage": "SAPKH31H62", "URL": "/supportpackage/SAPKH31H62"}, {"SoftwareComponentVersion": "SAP_HR 31H", "SupportPackage": "SAPKE31H57", "URL": "/supportpackage/SAPKE31H57"}, {"SoftwareComponentVersion": "SAP_HR 31H", "SupportPackage": "SAPKE31H58", "URL": "/supportpackage/SAPKE31H58"}, {"SoftwareComponentVersion": "SAP_HR 31H", "SupportPackage": "SAPKE31H65", "URL": "/supportpackage/SAPKE31H65"}, {"SoftwareComponentVersion": "SAP_HR 31H", "SupportPackage": "SAPKE31H62", "URL": "/supportpackage/SAPKE31H62"}, {"SoftwareComponentVersion": "SAP_APPL 31I", "SupportPackage": "SAPKH31I36", "URL": "/supportpackage/SAPKH31I36"}, {"SoftwareComponentVersion": "SAP_HR 31I", "SupportPackage": "SAPKE31I36", "URL": "/supportpackage/SAPKE31I36"}, {"SoftwareComponentVersion": "SAP_APPL 40B", "SupportPackage": "SAPKH40B29", "URL": "/supportpackage/SAPKH40B29"}, {"SoftwareComponentVersion": "SAP_APPL 40B", "SupportPackage": "SAPKH40B24", "URL": "/supportpackage/SAPKH40B24"}, {"SoftwareComponentVersion": "SAP_HR 40B", "SupportPackage": "SAPKE40B29", "URL": "/supportpackage/SAPKE40B29"}, {"SoftwareComponentVersion": "SAP_HR 40B", "SupportPackage": "SAPKE40B24", "URL": "/supportpackage/SAPKE40B24"}, {"SoftwareComponentVersion": "SAP_APPL 45B", "SupportPackage": "SAPKH45B10", "URL": "/supportpackage/SAPKH45B10"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}