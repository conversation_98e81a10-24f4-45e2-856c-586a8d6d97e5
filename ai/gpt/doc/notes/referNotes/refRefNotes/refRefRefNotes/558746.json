{"Request": {"Number": "558746", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 263, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000015296072017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000558746?language=E&token=5D0A2A49699503DB7BD27B99964A8AD8"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000558746", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000558746/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "558746"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 6}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Performance"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "28.06.2006"}, "SAPComponentKey": {"_label": "Component", "value": "BW-SYS-DB-ORA"}, "SAPComponentKeyText": {"_label": "Component", "value": "BW ORACLE"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "SAP Business Warehouse", "value": "BW", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Basis System and Installation", "value": "BW-SYS", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW-SYS*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "BW Database Platforms", "value": "BW-SYS-DB", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW-SYS-DB*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "BW ORACLE", "value": "BW-SYS-DB-ORA", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW-SYS-DB-ORA*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "558746 - Better Oracle Data Dictionary BW Performance"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>Statements for Access to Data Dictionary Views have long response times or<br />r3szchk is running too long</p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>BW Performance, Oracle Data Dictionary, Statistics, Optimizer, r3szchk</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>Rule Based Optimizer used for Data Dictionary Access in 8.1.7 and 9.2.0</p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>******&#x00A0;&#x00A0;THIS NOTE IS ONLY VALID FOR SAP BW (2.x and 3.x)&#x00A0;&#x00A0; *******<br />*****&#x00A0;&#x00A0; and for r3szchk runtime optimizations during the&#x00A0;&#x00A0; *******<br />*****...the preparation phase for system copies&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;*******<br /><B>WARNING:</B>&#x00A0;&#x00A0;When used for r3szchk runtime optimizations in a R/3<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;system you must delete the statistics after the r3szchk has<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;run successfully!!!<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; For a SAP BW system you must keep the statistics all the time!!!<br /><br />Statements accessing the Oracle Data Dictionary objects may cause long<br />response times in BW systems. SAP BW depends on the partitioning<br />concepts of Oracle to perform well. By using many partitions in the<br />database access to certain data dictionary objects may become slow.<br />Examples of these dictionary objects are DBA_EXTENTS, DBA_SEGMENTS,<br />USER_EXTENTS or USER_SEGMENTS.<br />In Oracle versions 8.1.7 and 9.2 statements accessing the Oracle data dictionary objects are optimized with the Rule Based Opimizer (RBO).<br />By using the Cost Based Optimizer access to these dictionary objects<br />may dramatically be improved. In an SAP BW installation all<br />optimization and execution techniques like HASH JOINS or BITMAP<br />OPERATIONS are enabled by having the correct initialization<br />parameters set in the init&lt;SID&gt;.ora<br /><br />To use the CBO for statements against the Oracle data dictionary<br />optimizer statistics have to created on data dictionary tables and<br />clusters.<br />The following script allows you to create the necessary optimizer<br />statistics.<br />The script must be run from sqlplus under the database user SYSDBA.<br /><br />Rem Copyright (c) 2002 by Oracle Corporation<br />Rem<br />Rem FUNCTION<br />Rem&#x00A0;&#x00A0; Creates statistics on Oracle Data Dictionary<br />Rem&#x00A0;&#x00A0; Improves performance of BW systems<br />Rem<br />Rem NOTES<br />Rem&#x00A0;&#x00A0; This script should only be applied to a BW system<br />Rem&#x00A0;&#x00A0; and NEVER (!!!) to a R/3 system<br />Rem<br />Rem&#x00A0;&#x00A0; Install from sqlplus as sysdba ( / as sysdba )<br />Rem<br />Rem MODIFIED<br />Rem&#x00A0;&#x00A0;&#x00A0;&#x00A0; jklokker&#x00A0;&#x00A0; 10/05/01 :&#x00A0;&#x00A0;Created<br />Rem<br />set timing on<br />set echo on<br />spool ana_dict<br />execute dbms_stats.gather_schema_stats ('SYS',cascade=&gt;true,degree=&gt;4);<br />analyze cluster C_OBJ# compute statistics ;<br />analyze cluster C_USER# compute statistics ;<br />analyze cluster C_FILE#_BLOCK# compute statistics ;<br />analyze cluster C_TS# compute statistics ;<br />analyze cluster C_COBJ# compute statistics ;<br />analyze cluster C_MLOG# compute statistics ;<br />analyze cluster C_RG# compute statistics ;<br />analyze cluster C_OBJ#_INTCOL# compute statistics ;<br />analyze cluster C_TOID_VERSION# compute statistics ;<br /><br /><br />The above script should be executed on a regular basis like once a<br />month. In addition for 8.1.7 the analyze commands have to be rerun<br />every time you start the database.<br /><br />Statistics on the Oracle Data Dictionary should be deleted before<br />you install a new Oracle Patch Set and should be recreated after<br />the Patch Set install was done.<br /><br />To undo the above changes to statistics on the Oracle data dictionary<br />the following script should be used.<br /><br />The script needs to be run from sqlplus as database user SYSDBA:<br /><br /><br />Rem Copyright (c) 2002 by Oracle Corporation<br />Rem<br />Rem FUNCTION<br />Rem&#x00A0;&#x00A0; Deletes statistics on Oracle Data Dictionary<br />Rem<br />Rem NOTES<br />Rem&#x00A0;&#x00A0; Install from sqlplus as sysdba ( / as sysdba )<br />Rem<br />Rem MODIFIED<br />Rem&#x00A0;&#x00A0;&#x00A0;&#x00A0; jklokker&#x00A0;&#x00A0; 10/05/01&#x00A0;&#x00A0;:&#x00A0;&#x00A0;Created<br />Rem<br />set timing on<br />set echo on<br />spool del_dict<br />execute dbms_stats.delete_schema_stats ('SYS') ;<br />analyze cluster C_OBJ# delete statistics ;<br />analyze cluster C_USER# delete statistics ;<br />analyze cluster C_FILE#_BLOCK# delete statistics ;<br />analyze cluster C_TS# delete statistics ;<br />analyze cluster C_COBJ# delete statistics ;<br />analyze cluster C_MLOG# delete statistics ;<br />analyze cluster C_RG# delete statistics ;<br />analyze cluster C_OBJ#_INTCOL# delete statistics ;<br />analyze cluster C_TOID_VERSION# delete statistics ;<br /></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "BC-DB-ORA (Oracle)"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON> (C5004095)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (C5014651)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "J", "TranslationLanguage": "日本語", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000558746/J"}, {"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000558746/D"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000558746/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000558746/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000558746/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000558746/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000558746/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000558746/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000558746/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "936441", "RefComponent": "BC-DB-ORA", "RefTitle": "Oracle settings for R3load based system copy", "RefUrl": "/notes/936441"}, {"RefNumber": "871455", "RefComponent": "BC-DB-ORA", "RefTitle": "Bad performance when accessing DBA and V$ views", "RefUrl": "/notes/871455"}, {"RefNumber": "618868", "RefComponent": "BC-DB-ORA", "RefTitle": "FAQ: Oracle performance", "RefUrl": "/notes/618868"}, {"RefNumber": "588668", "RefComponent": "BC-DB-ORA", "RefTitle": "FAQ: Database statistics", "RefUrl": "/notes/588668"}, {"RefNumber": "567745", "RefComponent": "BW", "RefTitle": "Composite note BW 3.x performance: DB-specific setting", "RefUrl": "/notes/567745"}, {"RefNumber": "565075", "RefComponent": "BW-SYS-DB-ORA", "RefTitle": "Recommendations for BW systems with Oracle 8.1.x", "RefUrl": "/notes/565075"}, {"RefNumber": "558197", "RefComponent": "BC-DB-ORA", "RefTitle": "upgrade hangs in PARCONV_UPG, XPRAS_UPG, SHADOW_IMPORT_UPG2,", "RefUrl": "/notes/558197"}, {"RefNumber": "519448", "RefComponent": "BC-DB-ORA", "RefTitle": "Performance problems when deactivating aggregates", "RefUrl": "/notes/519448"}, {"RefNumber": "519407", "RefComponent": "BC-DB-ORA", "RefTitle": "Performance problems accessing the datadictionary views", "RefUrl": "/notes/519407"}, {"RefNumber": "387946", "RefComponent": "BW-SYS-DB-ORA", "RefTitle": "USE OF LOCALLY MANAGED TABLESPACES FOR BW SYSTEMS", "RefUrl": "/notes/387946"}, {"RefNumber": "354080", "RefComponent": "BC-DB-ORA", "RefTitle": "Note collection for Oracle performance problems", "RefUrl": "/notes/354080"}, {"RefNumber": "335505", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/335505"}, {"RefNumber": "180605", "RefComponent": "BW-SYS-DB", "RefTitle": "Oracle database parameter settings for BW", "RefUrl": "/notes/180605"}, {"RefNumber": "138639", "RefComponent": "BC-DB-ORA", "RefTitle": "Poor DML performance with Oracle DDIC statistics", "RefUrl": "/notes/138639"}, {"RefNumber": "1319517", "RefComponent": "BC-I18-UNI", "RefTitle": "Unicode Collection Note", "RefUrl": "/notes/1319517"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1319517", "RefComponent": "BC-I18-UNI", "RefTitle": "Unicode Collection Note", "RefUrl": "/notes/1319517 "}, {"RefNumber": "871455", "RefComponent": "BC-DB-ORA", "RefTitle": "Bad performance when accessing DBA and V$ views", "RefUrl": "/notes/871455 "}, {"RefNumber": "618868", "RefComponent": "BC-DB-ORA", "RefTitle": "FAQ: Oracle performance", "RefUrl": "/notes/618868 "}, {"RefNumber": "936441", "RefComponent": "BC-DB-ORA", "RefTitle": "Oracle settings for R3load based system copy", "RefUrl": "/notes/936441 "}, {"RefNumber": "588668", "RefComponent": "BC-DB-ORA", "RefTitle": "FAQ: Database statistics", "RefUrl": "/notes/588668 "}, {"RefNumber": "519448", "RefComponent": "BC-DB-ORA", "RefTitle": "Performance problems when deactivating aggregates", "RefUrl": "/notes/519448 "}, {"RefNumber": "558197", "RefComponent": "BC-DB-ORA", "RefTitle": "upgrade hangs in PARCONV_UPG, XPRAS_UPG, SHADOW_IMPORT_UPG2,", "RefUrl": "/notes/558197 "}, {"RefNumber": "180605", "RefComponent": "BW-SYS-DB", "RefTitle": "Oracle database parameter settings for BW", "RefUrl": "/notes/180605 "}, {"RefNumber": "354080", "RefComponent": "BC-DB-ORA", "RefTitle": "Note collection for Oracle performance problems", "RefUrl": "/notes/354080 "}, {"RefNumber": "138639", "RefComponent": "BC-DB-ORA", "RefTitle": "Poor DML performance with Oracle DDIC statistics", "RefUrl": "/notes/138639 "}, {"RefNumber": "567745", "RefComponent": "BW", "RefTitle": "Composite note BW 3.x performance: DB-specific setting", "RefUrl": "/notes/567745 "}, {"RefNumber": "387946", "RefComponent": "BW-SYS-DB-ORA", "RefTitle": "USE OF LOCALLY MANAGED TABLESPACES FOR BW SYSTEMS", "RefUrl": "/notes/387946 "}, {"RefNumber": "565075", "RefComponent": "BW-SYS-DB-ORA", "RefTitle": "Recommendations for BW systems with Oracle 8.1.x", "RefUrl": "/notes/565075 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": []}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}