{"Request": {"Number": "144864", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 458, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000014660912017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000144864?language=E&token=42574FA0AFDAED795A9E5D804BF1E4FA"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000144864", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000144864/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "144864"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 35}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Help for error analysis"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "14.03.2024"}, "SAPComponentKey": {"_label": "Component", "value": "SV-SMG-SER"}, "SAPComponentKeyText": {"_label": "Component", "value": "SAP Support Services"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Service", "value": "SV", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'SV*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "SAP Solution Manager", "value": "SV-SMG", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'SV-SMG*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "SAP Support Services", "value": "SV-SMG-SER", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'SV-SMG-SER*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "144864 - Setting Up the ABAP Workload Monitor"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>The <em>Service Data Quality </em>section of an <em>SAP Service</em> report (e.g. an&#160;<em>SAP EarlyWatch Alert </em>[EWA] report) highlights an issue with the ABAP <em>Workload Monitor</em> (transaction ST03).</p>\r\n<p>You want to prepare the&#160;<em>Workload Monitor&#160;</em>for the delivery of&#160;<em>SAP Services&#160;</em>such as a&#160;<em>SAP GoingLive Service&#160;</em>or a&#160;<em>SAP EarlyWatch Check.</em></p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>SAP EarlyWatch Alert, SAP Services, EarlyWatch Service, GoingLive Check, GoingLive Functional Upgrade Check, OS/DB Migration Service, Workload, ST03, ST03N</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>This SAP Note provides basic information about using the <em>Workload Monitor </em>for <em>SAP Services</em>.</p>\r\n<p>The workload of an ABAP system is logged in&#160;<a target=\"_blank\" href=\"https://blogs.sap.com/2007/01/12/statistical-records-part-1-inside-stad/#\">statistical records</a>&#160;which are stored in files on each application server. These&#160;<a target=\"_blank\" href=\"https://blogs.sap.com/2007/01/12/statistical-records-part-1-inside-stad/#\">statistical records</a>&#160;are aggregated by background job SAP_COLLECTOR_FOR_PERFMONITOR (report RSCOLL00).&#160;The retention time for the statistical records is controlled by the parameter stat/max_files, which determines how many files are used. The file in use is switched every hour. When the limit stat/max_files is reached, the kernel automatically overwrites the oldest file. The default value for stat/max_files is 48. With the default configuration the aggregation of statistical records into ST03 aggregates is finished before statistical records get overwritten.</p>\r\n<p>The ST03 aggregates are stored in the database of the ABAP system. The&#160;<em>Workload Monitor</em>&#160;is the viewing tool for these aggregates. For&#160;services, an&#160;<a target=\"_blank\" href=\"https://blogs.sap.com/2007/04/08/statistical-records-part-4-how-to-read-st03n-datasets-from-db-in-nw2004/#\">API</a>&#160;is used to read some of these aggregates and put them into the service data.</p>\r\n<p>All of this works out of the box after installing the system. This SAP Note provides information in case troubleshooting is required.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p>The&#160;<em>Workload Monitor&#160;</em>is the essential tool for workload and performance analysis of the ABAP system.</p>\r\n<p><strong>Standard Settings</strong></p>\r\n<ol>\r\n<li>Background job SAP_COLLECTOR_FOR_PERFMONITOR is scheduled. This is one of the standard jobs documented in <a target=\"_blank\" href=\"/notes/2190119\">SAP Note 2190119</a> (S/4HANA) or <a target=\"_blank\" href=\"/notes/16083\">SAP Note 16083</a> respectively.</li>\r\n<li>The job is configured correctly in table TCOLL. See&#160;the following SAP Notes which also describe the required authorization and other prerequisites.&#160;</li>\r\n<ul>\r\n<li><a target=\"_blank\" href=\"/notes/966309\">SAP Note 966309</a>&#160;Content of table TCOLL in SAP_BASIS 700 - 7.02 and 7.31 - 7.56</li>\r\n<li><a target=\"_blank\" href=\"/notes/966631\">SAP Note 966631</a>&#160;Content of TCOLL table in SAP_BASIS 710</li>\r\n</ul>\r\n<li><a target=\"_blank\" href=\"https://blogs.sap.com/2007/01/12/statistical-records-part-1-inside-stad/#\">Statistical records</a>&#160;are switched on. This is on nearly all systems the case, and controlled by the default value of parameter '<em>stat/level = 1</em>'. The value must not be changed and the recommendation is to remove this parameter from all profile files.</li>\r\n</ol>\r\n<p><strong>Settings to Control the Amount of Data Stored in the <em>Workload Monitor</em></strong></p>\r\n<p>There are different aggregates in ST03. Each aggregate can be individually switched off and the retention period set ( = \"reorganization\").</p>\r\n<p>Call transaction ST03. Ensure you are in \"Expert\" mode.</p>\r\n<ul>\r\n<li><em>Instance Collector&#160;</em>per application server<br />In the tree, you will find the item \"Control\" under \"Collector &amp; Performance DB -&gt; Workload Collector -&gt; Instance Collector\".<br />Activate all entries in the table.</li>\r\n</ul>\r\n<ul>\r\n<li><em>TOTAL Collector&#160;</em>for whole system<br />In the tree, you will find the item \"Control\" under \"Collector &amp; Performance DB -&gt; Workload Load Collector-&gt; TOTAL Collector\". For all entries in this table activate at least the weekly aggregates.</li>\r\n</ul>\r\n<ul>\r\n<li><em>Reorganization</em><br />In the tree on the left side of the screen, you will find the item \"Control\" under \"Collector &amp; Performance DB\" -&gt; \"Performance&#160;&#160;Database\" -&gt; \"Workload Collector Database\" -&gt; \"Reorganization\".</li>\r\n<ul>\r\n<li>The retention time for daily aggregates must be at least 7 days. SAP recommends 14 days.</li>\r\n<li>The retention time for weekly aggregates must be at least 5 weeks. SAP recommends 9 weeks.</li>\r\n<li>The retention time for monthly aggregates must be at least 2 months. SAP recommends 12 months.</li>\r\n<li>For the entry \"WA\" SAP recommends a retention time of 31 days, 20 weeks and 14 months.</li>\r\n</ul>\r\n<li>Minimum retention time for the&#160;<em>EarlyWatch Alert </em>service:&#160;The&#160;<em>EarlyWatch Alert&#160;</em>service&#160;evaluates data of the last week. Therefore for the EWA a retention time of 2 weeks for weekly aggregates is sufficient. <br />The EWA also reads some monthly aggregates of the last (complete) month. But if this it missing it has only minor impact on the content of the EWA service.<br /> For other <em>SAP Services</em>, the general rule applies: the longer the retention time and the more data is available, the better for the individual analysis. For example, the month end closing activity will only be covered at any time with the recommended 5 weeks.<br />(For completeness a special case of minor interest is mentioned here: When the EWA is executed for the first time it will use the whole history of weekly aggregates to be able to compare (high level) data over time. (Only summary if transaction profile is used here.) The EWA then builds such a history on its own.)</li>\r\n</ul>\r\n<ul>\r\n<li><em>Cumulation (Compression)<br /></em>In the tree on the left side of the screen, you will find the item \"Cumulation\" (or \"Compression\")&#160;under \"Collector &amp; Performance DB\" -&gt; \"Workload Collector\".<em><br /></em></li>\r\n<ul>\r\n<li>Cumulation Options: Exclude selected fields from the ST03 aggregates. See <a target=\"_blank\" href=\"/notes/1110822\">SAP Note 1110822</a>. Fields that are especially relevant for&#160;<em>SAP Services&#160;</em>including the&#160;<em>EarlyWatch Alert&#160;</em>are marked in the UI with a star.</li>\r\n<li>Normalization of Background Jobs (or run&#160;report SWNC_CONFIG_JOBNAME): If the transaction aggregate is to large, a solution may be to normalize background jobs by common patterns in the job names. See <a target=\"_blank\" href=\"/notes/1631033\">SAP Note&#160;1631033</a>.</li>\r\n<li>Normalization of URLs (or run&#160;report SWNC_CONFIG_PATH): If the WEB Server aggregate is to large, a solution may be to normalize URLs by common patterns in the URL.&#160;See&#160;<a target=\"_blank\" href=\"/notes/1631033\">SAP Note&#160;1631033</a>.</li>\r\n<li>URL Evaluation: Relevant only for WebDynpro ABAP and BSP Applications it can be chosen one of three different levels of detail for the URL in the transaction profile of ST03: 'Break-Down by URL', 'Breakdown by Application', or no details.&#160;See&#160;<a target=\"_blank\" href=\"/notes/1634757\">SAP Note 1634757</a>, section&#160;<em>ST03N - Workload Monitor&#160;</em>(or identically described in&#160;<a target=\"_blank\" href=\"/notes/1323405\">SAP Note&#160;1323405</a>, section&#160;<em>ST03N - Workload Monitor</em>). Can also be set with report SWNC_CONFIG_URL (see SAP Note 992474) with setting 'Display Current Configuration'.</li>\r\n</ul>\r\n</ul>\r\n<p>(The exact names in the tree may differ slightly by the SAP release.)</p>\r\n<p><strong>Individual Settings to Record Additional Details<strong>&#160;in the&#160;<em>Workload Monitor</em></strong></strong></p>\r\n<p>Additional details can be stored in sub-records of statistical records. There are further configuration options for specific fields in ST03 aggregates.</p>\r\n<ul>\r\n<li>Additional data can be stored in RFC sub-records about called function modules, see&#160;<a target=\"_blank\" href=\"/notes/1964997\">SAP Note 1964997</a>.</li>\r\n<li>DB table access sub-records in statistical records&#160;or&#160;<a target=\"_blank\" href=\"/notes/1634757\">SAP Note 1634757</a>, section&#160;<em>STAD - Statistical Records</em>).</li>\r\n</ul>\r\n<p><strong>Hints for Troubleshooting</strong></p>\r\n<ul>\r\n<li>Run report RTCCTOOL (can also be started in <em>Service Data Control Center&#160;</em>[transaction SDCCN] as&#160;<em>Service Preparation Check&#160;</em>task) to check</li>\r\n<ul>\r\n<li>if a background job running&#160;<em>RSCOLL00</em>&#160;runs frequently enough</li>\r\n<li>table&#160;<em>TCOLL</em>&#160;is set up to match the recommendations in SAP Notes&#160;<a target=\"_blank\" href=\"/notes/966309\">966309</a>&#160;or&#160;<a target=\"_blank\" href=\"/notes/966631\">966631</a>&#160;mentioned before.</li>\r\n</ul>\r\n<li><a target=\"_blank\" href=\"https://ga.support.sap.com/dtp/viewer/index.html#/tree/1543/actions/19613\">Guided Answer for ST03</a></li>\r\n<li><a target=\"_blank\" href=\"/notes/2561607\">SAP Knowledge Base Article&#160;2561607</a></li>\r\n</ul></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "SV-BO (Backoffice Service Delivery)"}, {"Key": "Other Components", "Value": "SV-SMG-SER-EWA (EarlyWatch Alert)"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON> (D028075)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D036173)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000144864/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000144864/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000144864/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000144864/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000144864/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000144864/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000144864/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000144864/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000144864/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2561607", "RefComponent": "SV-SMG-SER-EWA", "RefTitle": "Quality Of Service Data in ST-PI message shown for EarlyWatch Alert (EWA)", "RefUrl": "/notes/2561607"}, {"RefNumber": "970449", "RefComponent": "BC-CCM-MON-TUN", "RefTitle": "Content of table TCOLL in Release SAP_BASIS 640", "RefUrl": "/notes/970449"}, {"RefNumber": "966631", "RefComponent": "BC-CCM-MON-TUN", "RefTitle": "Content of table TCOLL in Release SAP_BASIS 710", "RefUrl": "/notes/966631"}, {"RefNumber": "966309", "RefComponent": "BC-CCM-MON-TUN", "RefTitle": "Content of table TCOLL in SAP_BASIS 700 - 7.02 and 7.31 - 7.56", "RefUrl": "/notes/966309"}, {"RefNumber": "91488", "RefComponent": "SV-SMG-SER", "RefTitle": "SAP Support Services - Central preparatory note", "RefUrl": "/notes/91488"}, {"RefNumber": "1634757", "RefComponent": "SV-SMG-SER", "RefTitle": "Guided Self Service 'Performance Optimization'", "RefUrl": "/notes/1634757"}, {"RefNumber": "1631033", "RefComponent": "BC-CCM-MON-TUN", "RefTitle": "Normalization of background job names for statistics data", "RefUrl": "/notes/1631033"}, {"RefNumber": "16083", "RefComponent": "BC-CCM-BTC", "RefTitle": "Standard jobs, reorganization jobs", "RefUrl": "/notes/16083"}, {"RefNumber": "1443908", "RefComponent": "BC-CCM-MON-TUN", "RefTitle": "ST03N - Weekly workload data can no longer be displayed", "RefUrl": "/notes/1443908"}, {"RefNumber": "1323405", "RefComponent": "SV-SMG-SER", "RefTitle": "Technical Preparation of a CQC BPPO service", "RefUrl": "/notes/1323405"}, {"RefNumber": "12103", "RefComponent": "BC-CCM-MON-TUN", "RefTitle": "Contents of table TCOLL", "RefUrl": "/notes/12103"}, {"RefNumber": "1110822", "RefComponent": "BC-CCM-MON-TUN", "RefTitle": "Workload collector: Reducing the size of aggregates", "RefUrl": "/notes/1110822"}, {"RefNumber": "2190119", "RefComponent": "BC-CCM-BTC-JR", "RefTitle": "Background information about SAP S/4HANA technical job repository", "RefUrl": "/notes/2190119"}, {"RefNumber": "1964997", "RefComponent": "BC-CST-ST", "RefTitle": "ST: Enhancement of kernel statistics for RFC subrecords", "RefUrl": "/notes/1964997"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2675074", "RefComponent": "BC-CCM-MON-TUN", "RefTitle": "No workload data in transaction ST03N or ST03", "RefUrl": "/notes/2675074 "}, {"RefNumber": "91488", "RefComponent": "SV-SMG-SER", "RefTitle": "SAP Support Services - Central preparatory note", "RefUrl": "/notes/91488 "}, {"RefNumber": "966309", "RefComponent": "BC-CCM-MON-TUN", "RefTitle": "Content of table TCOLL in SAP_BASIS 700 - 7.02 and 7.31 - 7.56", "RefUrl": "/notes/966309 "}, {"RefNumber": "966631", "RefComponent": "BC-CCM-MON-TUN", "RefTitle": "Content of table TCOLL in Release SAP_BASIS 710", "RefUrl": "/notes/966631 "}, {"RefNumber": "970449", "RefComponent": "BC-CCM-MON-TUN", "RefTitle": "Content of table TCOLL in Release SAP_BASIS 640", "RefUrl": "/notes/970449 "}, {"RefNumber": "12103", "RefComponent": "BC-CCM-MON-TUN", "RefTitle": "Contents of table TCOLL", "RefUrl": "/notes/12103 "}, {"RefNumber": "1443908", "RefComponent": "BC-CCM-MON-TUN", "RefTitle": "ST03N - Weekly workload data can no longer be displayed", "RefUrl": "/notes/1443908 "}, {"RefNumber": "628183", "RefComponent": "BC-SEC", "RefTitle": "Top 33 general notes", "RefUrl": "/notes/628183 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": []}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}