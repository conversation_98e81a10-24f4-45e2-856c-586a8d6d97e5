{"Request": {"Number": "619188", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 387, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000015449542017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000619188?language=E&token=52F87B908330BBFCD5ECCFFA860D6269"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000619188", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000619188/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "619188"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 238}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "FAQ"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "20.01.2021"}, "SAPComponentKey": {"_label": "Component", "value": "BC-DB-ORA"}, "SAPComponentKeyText": {"_label": "Component", "value": "Oracle"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Basis Components", "value": "BC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Database Interface, Database Platforms", "value": "BC-DB", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-DB*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Oracle", "value": "BC-DB-ORA", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-DB-ORA*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "619188 - FAQ: Oracle wait events"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<ol>1. What are wait events?</ol><ol>2. What is the connection between wait events and database performance?</ol><ol>3. For what purpose are wait events used?</ol><ol>4. How can I access wait event information?</ol><ol>5. How can I determine which wait events are defined?</ol><ol>6. What are idle events?</ol><ol>7. What are the most important idle events?</ol><ol>8. How can I determine which wait classes add to the response time and the extent to which they add to it?</ol><ol>9. How can I determine which wait events in particular cause a high load?</ol><ol>10. How can I find out which Oracle session belongs to an R/3 work process?</ol><ol>11. How can I determine the wait events that the Oracle sessions are currently waiting for?</ol><ol>12. How should I interpret the WAIT_TIME from V$SESSION_WAIT?</ol><ol>13. How can I trace the wait events of an Oracle session?</ol><ol>14. Where do I find historical wait event information?</ol><ol>15. How can I optimize the individual wait events?</ol><ol>16. Does Real Application Cluster (RAC) have different events?</ol><ol>17. Which wait events are Exadata specific?</ol><ol>18. BACKGROUND processes generating FOREGROUND events.</ol><ol>19. What else should I check in relation to wait events?</ol><ol>20. Where can I find further information about wait events?</ol>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>FAQ, queue event, event, queue events, events</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<ol>1. What are wait events?</ol>\r\n<p style=\"padding-left: 30px;\">Wait events are characteristic parts of the Oracle kernel source code that can contain Oracle sessions during execution. Oracle provides statistical information on the wait events, which comprises the following components:</p>\r\n<ul>\r\n<ul>\r\n<li>Name of the wait event</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Optional: Up to three parameters of different importance depending on the wait event.</li>\r\n</ul>\r\n</ul>\r\n<ol>2. What is the connection between wait events and database performance?</ol>\r\n<p style=\"padding-left: 30px;\">The database response time is largely determined by the following two components:</p>\r\n<ul>\r\n<ul>\r\n<li>Waiting in the context of wait events</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>CPU consumption</li>\r\n</ul>\r\n</ul>\r\n<p style=\"padding-left: 30px;\">In well-tuned systems, the wait events make up about 60% of the response time. Otherwise, the proportion may be much higher, which has a negative effect on the response times. Wait event tuning can therefore frequently bring about a significant improvement in database performance.</p>\r\n<p style=\"padding-left: 30px;\">For information on the distribution between wait event time and CPU time, refer to the initial screen of transaction ST04 (\"Busy wait time\" and \"CPU time\"). Note, however, that ST04 may also include Idle Wait Events in the Busy Wait Time, which is why it is best to check the accuracy of the Busy Wait Time with the data from V$SYSTEM_EVENT (see below).</p>\r\n<ol>3. For what purpose are wait events used?</ol>\r\n<p style=\"padding-left: 30px;\">By determining the wait event, you can determine what a session is currently doing or what it is waiting for. In the case of performance problems, you can therefore determine whether they are caused by database locks, disk accesses, latches or another cause.</p>\r\n<p style=\"padding-left: 30px;\">Looking at all the wait events that have accumulated since the start database was started enables you to draw conclusions as to the extent of the potential for optimization and where you need to focus to increase global database performance.</p>\r\n<p style=\"padding-left: 30px;\">If sporadic performance bottlenecks occur, analyzing the wait events that occurred during the problematic period can also be very helpful.</p>\r\n<ol>4. How can I access wait event information?</ol>\r\n<p style=\"padding-left: 30px;\">You can use V$-Views to access wait event information. The following is important in this context:</p>\r\n<ul>\r\n<ul>\r\n<li>V$SYSTEM_EVENT: Wait events accumulated system-wide since the database was started.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>V$SESSION_EVENT: Wait events accumulated per session since the database was started.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>V$SESSION_WAIT: Current wait events.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>V$EVENT_NAME: Name and parameter of the wait events</li>\r\n</ul>\r\n</ul>\r\n<p style=\"padding-left: 30px;\">In addition to a direct access at Oracle level, you can also use transaction \"ST04 -&gt; Detail analysis menu -&gt; Display V$ values\" or the RSORAVDV (or /SDF/RSORAVDV) to access these views. This note primarily describes the direct Oracle queries because you can sometimes no longer use R/3 if extreme performance problems occur.</p>\r\n<ol>5. How I can determine which wait events are defined?</ol>\r\n<p style=\"padding-left: 30px;\">This information is stored in V$EVENT_NAME. At Oracle level, you can use the following call to receive a list of all wait events:<br />SELECT NAME FROM V$EVENT_NAME;</p>\r\n<p style=\"padding-left: 30px;\">You can obtain a short description of the three parameters relevant for an event using:<br />SELECT PARAMETER1, PARAMETER2, PARAMETER3 FROM V$EVENT_NAME<br />WHERE NAME = '&lt;wait_event&gt;';</p>\r\n<ol>6. What are idle events?</ol>\r\n<p style=\"padding-left: 30px;\">It is useful to distinguish between idle events as defined by Oracle and idle events as defined by SAP.</p>\r\n<ul>\r\n<ul>\r\n<li>Idle events, as defined by Oracle, are wait events that are reported when the Oracle process has nothing to do. The idle event \"SQL*Net message from client\" means that a shadow process is currently waiting for the next request from the client (that is, from R/3).</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Idle events, as defined by SAP, are the events that do not have any influence over database response time. These include, for example, \"db file parallel write\", because the related writing of the DBWR processes occurs asynchronously and a client inquiry does not have to wait for this. Those events whose times are completely covered in the context of other non-idle events (for example, \"db file parallel write\", which is contained in the \"log file sync\") are also considered idle events from an SAP point of view.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>All events that are not defined by SAP as idle events are entered in the database response time and are therefore included in the core of a database performance analysis.</li>\r\n</ul>\r\n</ul>\r\n<ol>7. What are the most important idle events?</ol>\r\n<ul>\r\n<ul>\r\n<li>Idle events as defined by SAP and Oracle:</li>\r\n</ul>\r\n</ul>\r\n<p style=\"padding-left: 60px;\"><span style=\"font-size: 14px;\">ASM background timer</span><br style=\"font-size: 14px;\" /><span style=\"font-size: 14px;\">class slave wait</span><br style=\"font-size: 14px;\" /><span style=\"font-size: 14px;\">DIAG idle wait</span><br style=\"font-size: 14px;\" /><span style=\"font-size: 14px;\">dispatcher timer</span><br style=\"font-size: 14px;\" /><span style=\"font-size: 14px;\">EMON idle wait</span><br style=\"font-size: 14px;\" /><span style=\"font-size: 14px;\">gcs for action</span><br style=\"font-size: 14px;\" /><span style=\"font-size: 14px;\">gcs remote message</span><br style=\"font-size: 14px;\" /><span style=\"font-size: 14px;\">ges remote message</span><br style=\"font-size: 14px;\" /><span style=\"font-size: 14px;\">HS message to agent</span><br style=\"font-size: 14px;\" /><span style=\"font-size: 14px;\">i/o slave wait</span><br style=\"font-size: 14px;\" /><span style=\"font-size: 14px;\">jobq slave wait</span><br style=\"font-size: 14px;\" /><span style=\"font-size: 14px;\">JS external job</span><br style=\"font-size: 14px;\" /><span style=\"font-size: 14px;\">KSV master wait</span><br style=\"font-size: 14px;\" /><span style=\"font-size: 14px;\">LNS ASYNC archive log</span><br style=\"font-size: 14px;\" /><span style=\"font-size: 14px;\">LNS ASYNC dest activation</span><br style=\"font-size: 14px;\" /><span style=\"font-size: 14px;\">LNS ASYNC end of log</span><br style=\"font-size: 14px;\" /><span style=\"font-size: 14px;\">lock manager wait for remote message</span><br style=\"font-size: 14px;\" /><span style=\"font-size: 14px;\">LogMiner: client waiting for transaction</span><br style=\"font-size: 14px;\" /><span style=\"font-size: 14px;\">LogMiner: slave waiting for activate message</span><br style=\"font-size: 14px;\" /><span style=\"font-size: 14px;\">LogMiner: wakeup event for builder</span><br style=\"font-size: 14px;\" /><span style=\"font-size: 14px;\">LogMiner: wakeup event for preparer</span><br style=\"font-size: 14px;\" /><span style=\"font-size: 14px;\">LogMiner: wakeup event for reader</span><br style=\"font-size: 14px;\" /><span style=\"font-size: 14px;\">Null event</span><br style=\"font-size: 14px;\" /><span style=\"font-size: 14px;\">parallel query dequeue</span><br style=\"font-size: 14px;\" /><span style=\"font-size: 14px;\">parallel recovery coordinator waits for cleanup of slaves</span><br style=\"font-size: 14px;\" /><span style=\"font-size: 14px;\">pipe get</span><br style=\"font-size: 14px;\" /><span style=\"font-size: 14px;\">PL/SQL lock timer</span><br style=\"font-size: 14px;\" /><span style=\"font-size: 14px;\">pmon timer</span><br style=\"font-size: 14px;\" /><span style=\"font-size: 14px;\">PX Deq Credit: need buffer</span><br style=\"font-size: 14px;\" /><span style=\"font-size: 14px;\">PX Deq Credit: send blkd</span><br style=\"font-size: 14px;\" /><span style=\"font-size: 14px;\">PX Deq: Execute Reply</span><br style=\"font-size: 14px;\" /><span style=\"font-size: 14px;\">PX Deq: Execution Msg</span><br style=\"font-size: 14px;\" /><span style=\"font-size: 14px;\">PX Deq: Index Merge Close</span><br style=\"font-size: 14px;\" /><span style=\"font-size: 14px;\">PX Deq: Index Merge Execute</span><br style=\"font-size: 14px;\" /><span style=\"font-size: 14px;\">PX Deq: Index Merge Reply</span><br style=\"font-size: 14px;\" /><span style=\"font-size: 14px;\">PX Deq: Join ACK</span><br style=\"font-size: 14px;\" /><span style=\"font-size: 14px;\">PX Deq: kdcphc_ack</span><br style=\"font-size: 14px;\" /><span style=\"font-size: 14px;\">PX Deq: kdcph_mai</span><br style=\"font-size: 14px;\" /><span style=\"font-size: 14px;\">PX Deq: Msg Fragment</span><br style=\"font-size: 14px;\" /><span style=\"font-size: 14px;\">PX Deq: Par Recov Change Vector</span><br style=\"font-size: 14px;\" /><span style=\"font-size: 14px;\">PX Deq: Par Recov Execute</span><br style=\"font-size: 14px;\" /><span style=\"font-size: 14px;\">PX Deq: Par Recov Reply</span><br style=\"font-size: 14px;\" /><span style=\"font-size: 14px;\">PX Deq: Parse Reply</span><br style=\"font-size: 14px;\" /><span style=\"font-size: 14px;\">PX Deq: Signal ACK</span><br style=\"font-size: 14px;\" /><span style=\"font-size: 14px;\">PX Deq: Table Q Normal</span><br style=\"font-size: 14px;\" /><span style=\"font-size: 14px;\">PX Deq: Table Q Sample</span><br style=\"font-size: 14px;\" /><span style=\"font-size: 14px;\">PX Deq: Txn Recovery Reply</span><br style=\"font-size: 14px;\" /><span style=\"font-size: 14px;\">PX Deq: Txn Recovery Start</span><br style=\"font-size: 14px;\" /><span style=\"font-size: 14px;\">PX Deque wait</span><br style=\"font-size: 14px;\" /><span style=\"font-size: 14px;\">PX Idle Wait</span><br style=\"font-size: 14px;\" /><span style=\"font-size: 14px;\">queue messages</span><br style=\"font-size: 14px;\" /><span style=\"font-size: 14px;\">rdbms ipc message</span><br style=\"font-size: 14px;\" /><span style=\"font-size: 14px;\">SGA: MMAN sleep for component shrink</span><br style=\"font-size: 14px;\" /><span style=\"font-size: 14px;\">single-task message</span><br style=\"font-size: 14px;\" /><span style=\"font-size: 14px;\">slave wait</span><br style=\"font-size: 14px;\" /><span style=\"font-size: 14px;\">smon timer</span><br style=\"font-size: 14px;\" /><span style=\"font-size: 14px;\">SQL*Net message from client</span><br style=\"font-size: 14px;\" /><span style=\"font-size: 14px;\">SQL*Net message from dblink</span><br style=\"font-size: 14px;\" /><span style=\"font-size: 14px;\">Streams AQ: deallocate messages from Streams Pool</span><br style=\"font-size: 14px;\" /><span style=\"font-size: 14px;\">Streams AQ: delete acknowledged messages</span><br style=\"font-size: 14px;\" /><span style=\"font-size: 14px;\">Streams AQ: qmn coordinator idle wait</span><br style=\"font-size: 14px;\" /><span style=\"font-size: 14px;\">Streams AQ: qmn slave idle wait</span><br style=\"font-size: 14px;\" /><span style=\"font-size: 14px;\">Streams AQ: RAC qmn coordinator idle wait</span><br style=\"font-size: 14px;\" /><span style=\"font-size: 14px;\">Streams AQ: waiting for messages in the queue</span><br style=\"font-size: 14px;\" /><span style=\"font-size: 14px;\">Streams AQ: waiting for time management or cleanup tasks</span><br style=\"font-size: 14px;\" /><span style=\"font-size: 14px;\">Streams fetch slave: waiting for txns</span><br style=\"font-size: 14px;\" /><span style=\"font-size: 14px;\">virtual circuit status</span><br style=\"font-size: 14px;\" /><span style=\"font-size: 14px;\">wait for unread message on broadcast channel</span><br style=\"font-size: 14px;\" /><span style=\"font-size: 14px;\">wait for unread message on multiple broadcast channels</span><br style=\"font-size: 14px;\" /><span style=\"font-size: 14px;\">wakeup time manager</span><br style=\"font-size: 14px;\" /><span style=\"font-size: 14px;\">watchdog main loop</span></p>\r\n<p style=\"padding-left: 30px;\">As of Oracle 10g, idle events as defined by Oracle can be determined with the following command (WAIT_CLASS = 'Idle):<br />SELECT NAME FROM V$EVENT_NAME WHERE WAIT_CLASS# = 6;</p>\r\n<ul>\r\n<ul>\r\n<li>Idle events as defined by SAP only:</li>\r\n</ul>\r\n</ul>\r\n<p style=\"padding-left: 90px;\"><span style=\"font-size: 14px;\">db file parallel write</span><br style=\"font-size: 14px;\" /><span style=\"font-size: 14px;\">Log archive I/O</span><br style=\"font-size: 14px;\" /><span style=\"font-size: 14px;\">log file parallel write</span><br style=\"font-size: 14px;\" /><span style=\"font-size: 14px;\">log file sequential read</span><br style=\"font-size: 14px;\" /><span style=\"font-size: 14px;\">ARCH wait on SENDREQ</span></p>\r\n<p style=\"padding-left: 30px;\">In general, all wait events for background processes are defined by SAP as idle events. The non-idle waits that are allotted to background processes (such as \"db file sequential read\") are therefore also not relevant from an SAP perspective. As of Oracle 10g, these wait events for the wait class \"System I/O\" are grouped together (WAIT_CLASS# = 9).<br />\"log file sequential read\" may represent an exception. It can also be executed by shadow processes that read information from the redo logs in the case of errors in order to generate trace files that contain sufficient information. If this is the case a lower filling of the oraarch directory can reduce the read time significantly. This can be achieved by more often saving/deleting archivelogs so that the retention time is&#160;lower. Do NOT&#160;save less copies of archivelogs just to have a lower impact of this event. Especially in recovery situations with ora-00600 [3020] errors a lot of time could be saved by removing already applied archivelogs.</p>\r\n<ol>8. How can I determine which wait classes add to the response time and the extent to which they add to it?</ol>\r\n<p style=\"padding-left: 30px;\">As of Oracle 10g, wait events are assigned to specific wait classes such as \"User I/O\" or \"Concurrency\". You can use the following command to determine what proportion of the entire non-idle wait time is taken up by these wait classes:</p>\r\n<p style=\"padding-left: 60px;\">SELECT<br />SUBSTR(WAIT_CLASS, 1, 30) WAIT_CLASS,<br />ROUND(TIME_WAITED/100) \"TIME_WAITED (S)\",<br />ROUND(RATIO_TO_REPORT(TIME_WAITED) OVER () * 100) PERCENT<br />FROM<br />(SELECT WAIT_CLASS, SUM(TIME_WAITED) TIME_WAITED<br />FROM V$SYSTEM_EVENT<br />WHERE WAIT_CLASS NOT IN ('Idle', 'System I/O')<br />GROUP BY WAIT_CLASS)<br />ORDER BY 2 DESC;</p>\r\n<ol>9. How can I determine which wait events in particular cause a high load?</ol>\r\n<p style=\"padding-left: 30px;\">V$SYSTEM_EVENT lists the accumulated values for all wait events since the database was started. You can use the following SQL command to determine the longest wait event:</p>\r\n<p style=\"padding-left: 60px;\">SELECT EVENT, TIME_WAITED, AVERAGE_WAIT<br />FROM V$SYSTEM_EVENT<br />ORDER BY TIME_WAITED DESC;</p>\r\n<p style=\"padding-left: 30px;\">Go through this list from top to bottom, ignoring idle events. This means that you get non idle events for which the system had to wait the longest (TIME_WAITED). You can now use this information to analyze the determined wait event more precisely.</p>\r\n<p style=\"padding-left: 30px;\">Important: The smaller the queue time of a wait event compared to the queue time of the uppermost non idle event, the less you need to tune this wait event (at least for the global database performance).</p>\r\n<p style=\"padding-left: 30px;\">As of Oracle 10g, you can use the command TimedEvents_TopTimedEvents from SAP Note 1438410 to determine the most important components of the database time.</p>\r\n<ol>10. How can I find out which Oracle session belongs to an R/3 work process?</ol>\r\n<p style=\"padding-left: 30px;\">Determine the PID &lt;rpid&gt; of the R/3 work process (for example, by viewing the PID column in Transaction SM50). Now search in \"Transaction ST04 -&gt; Detail analysis menu -&gt; Oracle session\" for the row with \"Clnt proc.\" = &lt;rpid&gt;. In this row, you can now find the relevant &lt;opid&gt; Oracle PID in the \"PID\" column.</p>\r\n<p style=\"padding-left: 30px;\">You can determine the &lt;opid&gt; at Oracle level from the &lt;rpid&gt; as follows:</p>\r\n<p style=\"padding-left: 60px;\">SELECT SID FROM V$SESSION WHERE PROCESS = &lt;rpid&gt;;</p>\r\n<ol>11. How can I determine the wait events that the Oracle sessions are currently waiting for?</ol>\r\n<p style=\"padding-left: 30px;\">You can determine the current wait events using \"ST04 -&gt; Detail analysis menu -&gt; Oracle session\".</p>\r\n<p style=\"padding-left: 30px;\">At database level, the following command gives you an initial overview as to which session is waiting, or using the CPU, with which (abbreviated) SQL command for which wait event:</p>\r\n<ul>\r\n<ul>\r\n<li>Oracle 9i or lower:</li>\r\n</ul>\r\n</ul>\r\n<p style=\"padding-left: 90px;\"><span style=\"font-size: 14px;\">SELECT</span><br style=\"font-size: 14px;\" /><span style=\"font-size: 14px;\">SUBSTR(S.SID, 1, 3) SID,</span><br style=\"font-size: 14px;\" /><span style=\"font-size: 14px;\">DECODE(ST.SQL_TEXT, NULL, AA.NAME,</span><br style=\"font-size: 14px;\" /><span style=\"font-size: 14px;\">SUBSTR(ST.SQL_TEXT, 1, 32)) SQLTEXT,</span><br style=\"font-size: 14px;\" /><span style=\"font-size: 14px;\">SUBSTR(DECODE(SW.WAIT_TIME, 0, SUBSTR(SW.EVENT, 1, 30), 'CPU'),</span><br style=\"font-size: 14px;\" /><span style=\"font-size: 14px;\">1, 20) ACTION,</span><br style=\"font-size: 14px;\" /><span style=\"font-size: 14px;\">SW.P1 P1,</span><br style=\"font-size: 14px;\" /><span style=\"font-size: 14px;\">SW.P2 P2,</span><br style=\"font-size: 14px;\" /><span style=\"font-size: 14px;\">SW.P3 P3</span><br style=\"font-size: 14px;\" /><span style=\"font-size: 14px;\">FROM</span><br style=\"font-size: 14px;\" /><span style=\"font-size: 14px;\">V$SESSION S, V$SESSION_WAIT SW, V$SQLTEXT ST, AUDIT_ACTIONS AA</span><br style=\"font-size: 14px;\" /><span style=\"font-size: 14px;\">WHERE</span><br style=\"font-size: 14px;\" /><span style=\"font-size: 14px;\">S.STATUS = 'ACTIVE' AND S.SID = SW.SID AND</span><br style=\"font-size: 14px;\" /><span style=\"font-size: 14px;\">S.SQL_HASH_VALUE = ST.HASH_VALUE (+) AND</span><br style=\"font-size: 14px;\" /><span style=\"font-size: 14px;\">S.SQL_ADDRESS = ST.ADDRESS (+) AND ST.PIECE (+) = 0 AND</span><br style=\"font-size: 14px;\" /><span style=\"font-size: 14px;\">AA.ACTION = S.COMMAND</span><br style=\"font-size: 14px;\" /><span style=\"font-size: 14px;\">ORDER BY</span><br style=\"font-size: 14px;\" /><span style=\"font-size: 14px;\">S.SID;</span></p>\r\n<ul>\r\n<ul>\r\n<li>Oracle 10g or higher:</li>\r\n</ul>\r\n</ul>\r\n<p style=\"padding-left: 90px;\"><span style=\"font-size: 14px;\">SELECT</span><br style=\"font-size: 14px;\" /><span style=\"font-size: 14px;\">SID,</span><br style=\"font-size: 14px;\" /><span style=\"font-size: 14px;\">DECODE(S.WAIT_TIME, 0, S.EVENT, 'CPU') ACTION,</span><br style=\"font-size: 14px;\" /><span style=\"font-size: 14px;\">S.P1 P1,</span><br style=\"font-size: 14px;\" /><span style=\"font-size: 14px;\">S.P2 P2,</span><br style=\"font-size: 14px;\" /><span style=\"font-size: 14px;\">S.P3 P3,</span><br style=\"font-size: 14px;\" /><span style=\"font-size: 14px;\">SUBSTR(DECODE(SS.SQL_TEXT, NULL, AA.NAME, SS.SQL_TEXT),</span><br style=\"font-size: 14px;\" /><span style=\"font-size: 14px;\">1, 45) SQLTEXT</span><br style=\"font-size: 14px;\" /><span style=\"font-size: 14px;\">FROM</span><br style=\"font-size: 14px;\" /><span style=\"font-size: 14px;\">V$SESSION S, V$SQLSTATS SS, AUDIT_ACTIONS AA</span><br style=\"font-size: 14px;\" /><span style=\"font-size: 14px;\">WHERE</span><br style=\"font-size: 14px;\" /><span style=\"font-size: 14px;\">S.STATUS = 'ACTIVE' AND</span><br style=\"font-size: 14px;\" /><span style=\"font-size: 14px;\">S.SQL_ID = SS.SQL_ID (+) AND</span><br style=\"font-size: 14px;\" /><span style=\"font-size: 14px;\">AA.ACTION = S.COMMAND AND</span><br style=\"font-size: 14px;\" /><span style=\"font-size: 14px;\">S.TYPE = 'USER'</span><br style=\"font-size: 14px;\" /><span style=\"font-size: 14px;\">ORDER BY</span><br style=\"font-size: 14px;\" /><span style=\"font-size: 14px;\">S.SID;</span></p>\r\n<p style=\"padding-left: 30px;\">If you want more exact details for an Oracle session with the &lt;osid&gt; Oracle SID, you can use the following statement:</p>\r\n<p style=\"padding-left: 60px;\">SELECT<br />S.SID SID,<br />DECODE(ST.SQL_TEXT, NULL, AA.NAME, ST.SQL_TEXT) SQLTEXT,<br />SUBSTR(DECODE(SW.WAIT_TIME, 0, SW.EVENT, 'CPU'),<br />1, 20) ACTION,<br />SW.P1 P1,<br />SW.P2 P2,<br />SW.P3 P3<br />FROM<br />V$SESSION S, V$SESSION_WAIT SW, V$SQLTEXT ST, AUDIT_ACTIONS AA<br />WHERE<br />S.SID = 14 AND S.SID = SW.SID AND<br />S.SQL_HASH_VALUE = ST.HASH_VALUE (+) AND<br />AA.ACTION = S.COMMAND<br />ORDER BY<br />ST.PIECE;</p>\r\n<p style=\"padding-left: 30px;\">This gives you the complete SQL statement, which is split up into several rows if it exceeds a certain length.</p>\r\n<ol>12. How should I interpret the wait time?</ol>\r\n<p style=\"padding-left: 30px;\">The WAIT_TIME column from V$SESSION_WAIT has the following meaning:</p>\r\n<ul>\r\n<ul>\r\n<li>0: The wait event is currently active.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>&gt; 0: The wait event has already been terminated and was active for the specified number of 1/100 seconds.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>-1: The wait event has already been terminated and was active for less than 1/100 seconds.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>-2: The wait event has already been terminated, but no time information is available because timed_statistics is set to FALSE;</li>\r\n</ul>\r\n</ul>\r\n<p style=\"padding-left: 30px;\">Caution: The \"waittime (sec)\" column in \"ST04 -&gt; Detail analysis menu -&gt; Oracle session\" does not refer to the WAIT_TIME of V$SESSION_WAIT, but to SECONDS_IN_WAIT! This column is therefore only suitable for an evaluation under certain conditions.</p>\r\n<ol>13. How can I trace the Wait Events of a session?</ol>\r\n<p style=\"padding-left: 30px;\">You can create an ORADEBUG trace with trace level 10 or 12 to determine what a particular Oracle Session is waiting for in a particular period. This may be useful if an SAP transaction takes an unusually long time on the database, without there being any obvious explanation for this in the SAP System. For a thorough description of using ORADEBUG, see Note 613872.</p>\r\n<p style=\"padding-left: 30px;\">You can also obtain a rough overview of the activities of a &lt;sid&gt; session using snapshots on V$SESSTAT and V$SESSION_EVENT. This allows you to carry out the following statement twice with a given interval (for example, a minute) to determine the proportion of individual wait events and CPU in the intervening period using the data received:</p>\r\n<p style=\"padding-left: 60px;\">SELECT SUBSTR(EVENT, 1, 45) EVENT, TOTAL_WAITS, TIME_WAITED<br />FROM V$SESSION_EVENT<br />WHERE SID = &lt;sid&gt;<br />UNION<br />SELECT 'CPU', NULL, VALUE<br />FROM V$SESSTAT<br />WHERE SID = &lt;sid&gt; AND STATISTIC# = 12<br />ORDER BY 3 DESC;</p>\r\n<p style=\"padding-left: 30px;\">Now calculate the TIME_WAITED difference of the snapshots for the individual rows and divide this result by 100. This gives you the number of seconds that the session required for the individual wait events or CPU. This result can also be divided by the difference of the TOTAL_WAITS to determine the average duration of every wait event.</p>\r\n<ol>14. Where do I find historical wait event information?</ol>\r\n<ul>\r\n<ul>\r\n<li>Using the report /SDF/RSORAVSE, you can determine the wait events accumulated hour by hour for past periods. It makes sense in this case to display the \"Idle Events\" using the button \"Without Idle Events\". Double-click on the day to get the statistics in hours.</li>\r\n</ul>\r\n</ul>\r\n<p style=\"padding-left: 60px;\">The report /SDF/RSORAVSH must be scheduled hourly for the data to be collected on an hourly basis. With earlier releases/patches, the reports were still called RSORAVSE and RSORAVSH. Refer also to Notes 549298, 560475 and 564446 for information about these reports.<br />The bug described in Note 607415 can result in incorrect average values in RSORAVSE. Implement the correction instructions or a relevant Support Package to eliminate the problem.</p>\r\n<ul>\r\n<ul>\r\n<li>As of Oracle 10g, you can use V$ACTIVE_SESSION_HISTORY to display historical data of active sessions. <strong>Note that acessing this view requires the Oracle Diagnostic Pack</strong>. Each second, the system checks whether a session is currently active (that is, it is waiting for a non-idle wait event or is consuming CPU). If it is active, an entry is created in V$ACTIVE_SESSION_HISTORY. Use the following command to obtain the last 20 activities of a session &lt;sid&gt;, including the respective SQL statement:</li>\r\n</ul>\r\n</ul>\r\n<p style=\"padding-left: 90px;\"><span style=\"font-size: 14px;\">SET LINESIZE 120</span><br style=\"font-size: 14px;\" /><span style=\"font-size: 14px;\">SELECT * FROM</span><br style=\"font-size: 14px;\" /><span style=\"font-size: 14px;\">(SELECT</span><br style=\"font-size: 14px;\" /><span style=\"font-size: 14px;\">TO_CHAR(ASH.SAMPLE_TIME, 'dd.mm.yyyy hh24:mi:ss')</span><br style=\"font-size: 14px;\" /><span style=\"font-size: 14px;\">\"TIMESTAMP\",</span><br style=\"font-size: 14px;\" /><span style=\"font-size: 14px;\">DECODE(ASH.WAIT_TIME, 0, SUBSTR(ASH.EVENT, 1, 30), 'CPU')</span><br style=\"font-size: 14px;\" /><span style=\"font-size: 14px;\">ACTION,</span><br style=\"font-size: 14px;\" /><span style=\"font-size: 14px;\">SUBSTR(O.OBJECT_NAME, 1, 30) OBJECT_NAME,</span><br style=\"font-size: 14px;\" /><span style=\"font-size: 14px;\">SUBSTR(S.SQL_TEXT, 1, 30) SQLTEXT</span><br style=\"font-size: 14px;\" /><span style=\"font-size: 14px;\">FROM</span><br style=\"font-size: 14px;\" /><span style=\"font-size: 14px;\">V$ACTIVE_SESSION_HISTORY ASH,</span><br style=\"font-size: 14px;\" /><span style=\"font-size: 14px;\">V$SQL S,</span><br style=\"font-size: 14px;\" /><span style=\"font-size: 14px;\">DBA_OBJECTS O</span><br style=\"font-size: 14px;\" /><span style=\"font-size: 14px;\">WHERE</span><br style=\"font-size: 14px;\" /><span style=\"font-size: 14px;\">ASH.SQL_ID = S.SQL_ID (+) AND</span><br style=\"font-size: 14px;\" /><span style=\"font-size: 14px;\">ASH.CURRENT_OBJ# = O.OBJECT_ID (+) AND</span><br style=\"font-size: 14px;\" /><span style=\"font-size: 14px;\">ASH.SESSION_ID = &lt;sid&gt;</span><br style=\"font-size: 14px;\" /><span style=\"font-size: 14px;\">GROUP BY</span><br style=\"font-size: 14px;\" /><span style=\"font-size: 14px;\">ASH.SAMPLE_TIME, ASH.WAIT_TIME, ASH.EVENT,</span><br style=\"font-size: 14px;\" /><span style=\"font-size: 14px;\">O.OBJECT_NAME, S.SQL_TEXT</span><br style=\"font-size: 14px;\" /><span style=\"font-size: 14px;\">ORDER BY ASH.SAMPLE_TIME DESC</span><br style=\"font-size: 14px;\" /><span style=\"font-size: 14px;\">)</span><br style=\"font-size: 14px;\" /><span style=\"font-size: 14px;\">WHERE ROWNUM &lt;= 20;</span></p>\r\n<p style=\"padding-left: 60px;\">Sometimes - for example, in connection with enqueue waits - it makes sense to determine which objects are principally associated with a certain wait event or group of wait events. As of 10g, you can use the following query on V$ACTIVE_SESSION_HISTORY to determine the top objects as regards the wait event &lt;event_pattern&gt; (for example, \"enq%\" for enqueue waits):</p>\r\n<p style=\"padding-left: 90px;\">SELECT * FROM<br />(SELECT<br />SUBSTR(ASH.EVENT, 1, 30) EVENT,<br />COUNT(*) \"COUNT\",<br />SUBSTR(O.OBJECT_NAME, 1, 30) OBJECT_NAME<br />FROM<br />V$ACTIVE_SESSION_HISTORY ASH,<br />DBA_OBJECTS O<br />WHERE<br />ASH.CURRENT_OBJ# = O.OBJECT_ID (+) AND<br />ASH.WAIT_TIME = 0 AND<br />ASH.EVENT LIKE '&lt;event_pattern&gt;'<br />GROUP BY ASH.EVENT, O.OBJECT_NAME<br />ORDER BY 2 DESC<br />)<br />WHERE ROWNUM &lt;= 20;</p>\r\n<p style=\"padding-left: 60px;\">Note that V$ACTIVE_SESSION_HISTORY only covers a limited period of time and is therefore not always representative. You can obtain snapshots of earlier periods in DBA_HIST_ACTIVE_SESS_HISTORY. As of Oracle 10.2, you can also create detailed ASH reports in Oracle. See Note 853576 for further details.</p>\r\n<ol>15. How can I optimize the individual wait events?</ol>\r\n<p style=\"padding-left: 30px;\">The following provides explanations, rules of thumb, analysis steps and optimization options for the most important wait events. In addition, the three relevant wait event parameters are specified under \"Parameter\". Note that implementing these recommendations requires detailed knowledge of Oracle. In addition, note that many of the wait events below can also be optimized with SQL statement tuning (Note 766349).</p>\r\n<ul>\r\n<li>db file sequential read</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Meaning: Waiting for a block requested by the operating system</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Parameter: File number/Block number/1</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Rule of thumb: The average value for the wait time displayed in V$SYSTEM_EVENT should not exceed 15ms.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Optimization steps:</li>\r\n</ul>\r\n</ul>\r\n<p style=\"padding-left: 60px;\"><span style=\"font-size: 14px;\">Depending on the reason for the optimization, you must work with different reference values: If the system suddenly displays worse db file sequential read times than previously, the previous, system-specific value must be taken as the reference value. If, however, you want to compare the stable (apart from slight fluctuations) db file sequential read average with other systems in order to estimate the potential improvement to be achieved by means of a hardware upgrade, you can use the rule of thumb value as the reference value. Note here that 15 ms is a relatively high value. Modern storage subsystems achieve db file sequential read averages in the low one-digit range. High-end storage subsystems with solid state technology, for example, achieve db file sequential read averages that are significantly below one ms. </span></p>\r\n<p style=\"padding-left: 60px;\"><span style=\"font-size: 14px;\">The waiting time for \"db file sequential read\" includes the time from the issue of a read IO request to the end of its processing. Since data is also cached at OS level, storage level, and so on, a db file sequential read does not need to lead to a physical disk access. The average \"db file sequential read\" time is therefore composed of the comparatively long disk access times and the fast cache access times (typically &lt; 1 ms). As of Oracle 10g, you can use V$EVENT_HISTOGRAM to recognize these two peak areas.</span></p>\r\n<p style=\"padding-left: 60px;\">Check whether the system uses the existing physical memory in a suitable way; if required, enlarge the Oracle buffer pool (but ensure that no paging occurs). In individual cases, due to the recommended deactivation of the file system cache (FILESYSTEMIO_OPTIONS = SETALL), the positive effect of a large file system cache may be lost and the \"db file sequential read\" times may be longer. In this case, you must enlarge the Oracle buffer pool to even out the removal of the file system cache.</p>\r\n<p style=\"padding-left: 60px;\">Optimize processing-intensive SQL statements using a large number of disk reads (Note 766349).</p>\r\n<p style=\"padding-left: 60px;\">With suboptimal, average \"db file sequential read\" times, you can use V$FILESTAT and - as of Oracle 10g - V$FILE_HISTOGRAM to localize hot spots in the disk area. In cooperation with your hardware partner, you should also check whether the hardware works correctly and the setup is optimum. Also refer to Note 793113 for an optimal I/O configuration.</p>\r\n<p style=\"padding-left: 60px;\">Note that even apparently good access times (8 ms, for example) may result in poor performance if the times previously observed were significantly lower (2 ms, for example). You should therefore always compare the current average values with past values (for example, using the /SDF/RSORAVSE report).</p>\r\n<p style=\"padding-left: 60px;\">A sudden increase of db file sequential read times can also be a consequence of an IO overload. Reasons for that can be a new IO intensive statement, extraordinary DB or storage maintenance activity (reorganizations, verifications, mirror synchronizations). Also other systems sharing the IO with the problem system can be the rootcause for an IO overload. The DB tools can just show IO KPIs coming from the problematic DB. Please check the IO volume and average then on layers below the DB.</p>\r\n<ul>\r\n<li>db file parallel read</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Meaning: Waiting for blocks to be read in parallel from the disk</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Parameter: File number/block numbers/requests</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Optimization steps:</li>\r\n</ul>\r\n</ul>\r\n<p style=\"padding-left: 60px;\">The wait event \"db file parallel read\" occurs in association with a recovery. As of Oracle 9i, blocks can also be read in parallel by the disk at another location, for example, as part of index prefetches. For information about tuning this event, see the relevant section of \"db file sequential read\".</p>\r\n<ul>\r\n<li>db file scattered read</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Meaning: Waiting for several blocks to be read from the disk</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Parameter: File number/block number/number of groups</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Optimization steps:</li>\r\n</ul>\r\n</ul>\r\n<p style=\"padding-left: 60px;\">The wait event \"db file scattered read\" always occurs where several physically sequential blocks are read from disk. Typical cases are Full Table Scans or Index Fast Full Scans. In individual cases, \"db file scattered read\" waits may occur when prefetch operations (for example, table prefetch using a _TABLE_LOOKUP_PREFETCH_SIZE that is higher than 0) are performed, when the Oracle buffer pool is \"warmed\" using Oracle 10g or higher (_DB_CACHE_PRE_WARM = TRUE) or during other accesses (for example, index range scans). Therefore, check which statements execute full table scans and optimize them. Though, a high number of disk reads in the SQL Cache (\"ST04 -&gt; Detail analysis menu -&gt; SQL requests\") is normally a good indication. You should therefore specifically check all SQL statements for Full Table Scans that are at the very top in the SQL cache in relation to disk reads.</p>\r\n<p style=\"padding-left: 60px;\">If an increased number of \"db file scattered read\" waits occur directly after a database is started and critical activities are slowed down as a result, you can deactivate the underlying cache prewarming by the Oracle parameter _DB_CACHE_PRE_WARM = FALSE.</p>\r\n<p style=\"padding-left: 60px;\">With the following statement, you can determine the objects at Oracle level, that currently have the most blocks due to full table scans or index fast full scans in the Oracle buffer pool:</p>\r\n<p style=\"padding-left: 90px;\">SELECT * FROM<br />(SELECT SUBSTR(O.OWNER, 1, 15) OWNER,<br />SUBSTR(O.OBJECT_NAME, 1, 35) OBJECT,<br />COUNT(*) BLOCKS,<br />DECODE(O.OBJECT_TYPE, 'TABLE', 'FULL TABLE SCAN',<br />'INDEX', 'FAST FULL SCAN',<br />'OTHER') \"SCAN TYPE\"<br />FROM DBA_OBJECTS O, X$BH B<br />WHERE B.OBJ = O.DATA_OBJECT_ID AND<br />STANDARD.BITAND(B.FLAG, 524288) &gt; 0 AND<br />O.OWNER != 'SYS'<br />GROUP BY O.OWNER, O.OBJECT_NAME, O.OBJECT_TYPE<br />ORDER BY COUNT(*) DESC)<br />WHERE ROWNUM &lt;=20;</p>\r\n<p style=\"padding-left: 60px;\"><br />As of Oracle 9i, the view V$SQL_PLAN is also available, which can be used to determine SQL statements that use full table scans or index fast full scans. The following statement returns the 20 SQL statements that are responsible for the most disk reads in the context of full table scans and index fast full scans:</p>\r\n<p style=\"padding-left: 90px;\">SELECT * FROM<br />(SELECT SUBSTR(SA.SQL_TEXT, 1, 68) SQL_TEXT,<br />SA.DISK_READS DISK_READS<br />FROM V$SQLAREA SA WHERE<br />(SA.ADDRESS, SA.HASH_VALUE) IN<br />(SELECT ADDRESS, HASH_VALUE FROM V$SQL_PLAN<br />WHERE OPERATION = 'TABLE ACCESS' AND<br />OPTIONS = 'FULL' OR<br />OPERATION = 'INDEX' AND<br />OPTIONS LIKE 'FAST FULL%')<br />ORDER BY 2 DESC)<br />WHERE ROWNUM &lt;=20;</p>\r\n<p style=\"padding-left: 60px;\">As of Oracle 10g, V$SEGMENT_STATISTICS contains information about the number of Full Table Scans or Index Fast Full scans per segment:</p>\r\n<p style=\"padding-left: 90px;\">SELECT * FROM<br />( SELECT OWNER, OBJECT_NAME, VALUE<br />FROM V$SEGMENT_STATISTICS<br />WHERE STATISTIC_NAME = 'segment scans'<br />ORDER BY VALUE DESC )<br />WHERE ROWNUM &lt;=20;<br />If you are using AIX, see also Note 610357.</p>\r\n<ul>\r\n<li>Oracle &lt;= 9i: direct path read / direct path read (lob)<br />Oracle &gt;= 10g: direct path read / direct path read temp</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Meaning: Waiting for blocks that are read directly from the disk drive to avoid the Oracle buffer pool</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Parameter: Descriptor/DBA/number of blocks</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Rule of thumb: The total wait time for these wait events should not exceed 5% of the relevant database time.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Optimization steps:</li>\r\n</ul>\r\n</ul>\r\n<p style=\"padding-left: 60px;\">Direct path operations, with which the Oracle buffer pool is bypassed, are used when PSAPTEMP is accessed (sortings, hash joins, bitmap operations), in the case of parallel query and when LOB data that is not cached is accessed. As of Oracle 11g, full table scans for larger tables are read via direct path.<br />Depending on your Oracle release, these activities are assigned to the wait events as follows:</p>\r\n<p style=\"padding-left: 90px;\">PSAPTEMP accesses: direct path read (&lt;= 9i oder DMTS) / direct path read temp (&gt;= 10g, LMTS)<br />Parallel Execution: direct path read<br />Full table scans on large tables: direct path read (&gt;= 11g)<br />LOB accesses: direct path read (lob) (&lt;= 9i) / direct path read (&gt;= 10g)</p>\r\n<p style=\"padding-left: 60px;\">The same checks as those described under \"db file sequential read\" can be carried out to optimize this wait event - with particular emphasis on PSAPTEMP.<br />The number of PSAPTEMP accesses can be reduced by increasing the PGA (see Note 789011).<br />Also check whether many unnecessary parallel query operations are carried out (see Note 651060).<br />You can reduce or avoid LOB-related direct path accesses by caching LOBs as described in Note 563359.<br />As described in Note 659946, also use V$SQL_WORKAREA_ACTIVE to check whether large hash joins, sortings or bitmap operations have been executed, which can be optimized.<br />\"direct path read temp\" waits for INSERTs in J2EE environments are triggered by temporary LOBs (Note 500340).</p>\r\n<ul>\r\n<li>Oracle &lt;= 9i: direct path write / direct path write (lob)<br />Oracle &gt;= 10g: direct path write / direct path write temp</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Meaning: Writing of a block directly on the hard drive to avoid the Oracle buffer pool</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Parameter: Descriptor/DBA/number of blocks</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Rule of thumb: This should not appear under the Top 10 wait events in terms of the cumulative queue time</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Optimization steps:</li>\r\n</ul>\r\n</ul>\r\n<p style=\"padding-left: 60px;\">The \"direct path write\" waits correspond to the above \"direct path read\" waits. Depending on your Oracle release, these activities are assigned to the wait events as follows:</p>\r\n<p style=\"padding-left: 90px;\">PSAPTEMP accesses: direct path write (&lt;= 9i) / direct path write temp (&gt;= 10g)<br />Parallel Query: direct path write<br />LOB accesses: direct path write (lob) (&lt;= 9i) / direct path write (&gt;= 10g)</p>\r\n<p style=\"padding-left: 60px;\">To optimize the accesses, proceed as described under \"direct path read\".</p>\r\n<ul>\r\n<li>Log file sync</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Meaning: Waiting until the LGWR has written all data from the redo buffer to the online redo log (for example, as part of a commit, if the DBWR triggers the LGWR because a dirty block that is to be written to the hard disk has not yet been entered in the redo logs, or if LGWR is triggered in another way to write the redo buffer to the online redo log)</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Parameter: Buffer number / - / -</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Rule of thumb: The average value for the wait time displayed in V$SYSTEM_EVENT should not exceed 15ms.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Optimization steps:</li>\r\n</ul>\r\n</ul>\r\n<p style=\"padding-left: 60px;\">In most cases the increased \"log file sync\" times are related to I/O problems for the LGWR process. Therefore you must check the operating system and the hardware you are using for bottlenecks during write-access to the Online Redo log. Since increased access times are generally caused by a bottleneck in the operating system or hardware / input/output area, please consult your operating system and hardware partner for a more specific analysis. Also check whether the current Oracle configuration can be optimized regarding the I/O configuration according to Note 793113.<br />\"log file sync\" waits may also occur when the system accesses unbuffered number ranges because the system performs a COMMIT for each of the numbers involved. In this situation, check whether buffering is possible for the affected number ranges. If the problem occurs in the BI environment with DIMD and SID number ranges, see Note 857998.</p>\r\n<p style=\"padding-left: 60px;\">If \"log file sync\" waits occur in BW systems when you change large data volumes (for example, loading or compressing data), check whether indexes can be temporarily dropped or used with NOLOGGING (see Note 853084).</p>\r\n<p style=\"padding-left: 60px;\">It may also be advisable to use NOLOGGING for other I/O intensive operations according to Note 806554 in order to minimize \"log file sync\" wait situations.</p>\r\n<p style=\"padding-left: 60px;\">If increased \"log file sync\" times occur during an online backup with BACKINT, check whether backup_dev_type = util_file_online can be set instead of util_file. This means that tablespaces are set to backup mode, which reduces the load on the online redo log.</p>\r\n<p style=\"padding-left: 60px;\">As of Oracle 10g, Oracle gives you the option of controlling the performance of \"log file sync\" using the parameter COMMIT_WRITE. Due to the fact that the consistency of the application can no longer be guaranteed if settings are changed, changing this parameter in a way that deviates from the standard system is generally not allowed in the SAP environment.</p>\r\n<p style=\"padding-left: 60px;\">The operation of a dataguard standby database in the \"MAXIMUM AVAILABILITY\" mode or \"MAXIMUM PROTECTION\" mode may also contribute to increased \"log file sync\" times because the redo data during the COMMIT is propagated synchronously with the standby database in this case. In this case, the LGWR process waits for events such as \"LGWR-LNS wait on channel\" or \"LGWR wait on LNS\". In this situation, optimize the dataguard configuration and use the \"MAXIMUM PERFORMANCE\" mode.</p>\r\n<p style=\"padding-left: 60px;\">In individual cases, increased \"log file sync\" times can also be caused by other effects. This means that an ARCH process can hold a control file enqueue over a long period of time (see Note 745639), which means that the LGWR process, which also requires this enqueue cannot carry out a log switch. As a result, all DML operations must wait for \"log file sync\" until the ARCH process has released the enqueue. Situations have also been observed in which the DBWR processes had to wait for latches due to an Oracle bug and as a result, the LGWR processes could not continue processing (\"LGWR wait for redo copy\" waits). This also led to massive \"log file sync\" waits.</p>\r\n<ul>\r\n<li>log buffer space</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Meaning: Waiting for freespace in the redo buffer</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Optimization steps:</li>\r\n</ul>\r\n</ul>\r\n<p style=\"padding-left: 60px;\">If the size of the redo buffer (parameter log_buffer) is less than 1 MB, you can increase this memory area to 1 MB. However, frequent \"log buffer space\" wait events are generally triggered by I/O problems with the LGWR. You should therefore refer to the tuning notes for the \"log file sync\" wait event.</p>\r\n<ul>\r\n<li>log file switch</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Meaning: Waiting for a redo log switch</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Optimization steps:</li>\r\n</ul>\r\n</ul>\r\n<p style=\"padding-left: 60px;\">In the case of \"log file switch (archiving needed)\", check in accordance with Note 391 whether an Archiver Stuck has occurred.<br />In the case of \"log file switch (checkpoint incomplete)\", see Note 79341 to solve the \"checkpoint not complete\" situation.<br />In the case of \"log file switch (private strand flush incomplete)\", refer to Note 793113 and optimize the DBWR performance.<br />In the case of \"log file switch completion\", you have to wait for a log switch to end. Provide a sufficient redo log size to ensure that there are not too many log switches (more than one per minute) for optimizing this wait event. Note also the optimization possibilities in the \"log file sync\" section because during a log switch, the system also flushes the redo buffer. Increased \"log file switch completion\"-times may also be a consequence of \"checkpoint not complete\" problems. Therefore, check if \"log file switch (checkpoint incomplete)\"-waits occur, and if so, proceed as described in Note 79341.</p>\r\n<ul>\r\n<li>log file parallel write</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Meaning: LGWR waiting for blocks to be written to disk</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Parameter: File/block/ I/O requests</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Optimization steps:</li>\r\n</ul>\r\n</ul>\r\n<p style=\"padding-left: 60px;\">Tune the LGWR I/O as described in the context of the \"log file sync\" wait event.</p>\r\n<ul>\r\n<li>Oracle &lt;= 9i: buffer busy waits<br />Oracle &gt;= 10g: read by other session / buffer busy waits</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Meaning: Waiting for a block because it is currently being imported or changed by another session.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Parameter: File number/Block number/ ID (&lt;= 9i)</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Rule of thumb: The average wait time of \"read by other session\" should be below 10 ms. \"buffer busy waits\" should be less than 1 % of the database time.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Optimization steps:</li>\r\n</ul>\r\n</ul>\r\n<p style=\"padding-left: 60px;\">If the rule of thumb is exceeded for \"read by other session\", this is generally due to problems in the I/O area. You should therefore also carry out the same checks in this case as described in the section \"DB file sequential read\".</p>\r\n<p style=\"padding-left: 60px;\">The ID specified as a third parameter gives more exact information as to why the block cannot be accessed. It is generally the case that: if the first number is a 1, just the block is read. If the first number is a 2, the block is kept in an incompatible mode. As of Oracle 10g, the third parameter for the class of the affected block is available:</p>\r\n<p style=\"padding-left: 90px;\">1 Data block<br />2 Sort block<br />3 Save undo block<br />4 Segment header<br />5 Save undo header<br />6 Free List<br />7 Extent map<br />8 1st level bitmap block<br />9 2nd level bitmap block<br />10 3rd level bitmap block<br />11 Bitmap block<br />12 Bitmap index block<br />13 File header block<br />14 Unused<br />15 System undo block<br />16 System undo block<br />17 Undo header<br />18 Undo block</p>\r\n<p style=\"padding-left: 60px;\">As of Oracle 10g, the name of the wait event tells you whether it is a read wait event (\"read by other session\") or a compatibility wait event (\"buffer busy waits\").</p>\r\n<p style=\"padding-left: 60px;\">If the waits occur frequently on certain blocks, you can use the following SELECT to determine the corresponding segment (&lt;file#&gt; and &lt;block#&gt; are parameters 1 and 2 of the wait event). In addition, the HDR column contains information as to whether it is a header block or not:</p>\r\n<p style=\"padding-left: 90px;\">SELECT SUBSTR(SEGMENT_NAME, 1, 30), SEGMENT_TYPE,<br />DECODE(&lt;block#&gt; - BLOCK_ID + EXTENT_ID, 0, 'YES', 'NO') HDR<br />FROM DBA_EXTENTS<br />WHERE FILE_ID = &lt;file#&gt; AND<br />&lt;block#&gt; BETWEEN BLOCK_ID AND BLOCK_ID + BLOCKS - 1;</p>\r\n<p style=\"padding-left: 60px;\">You can use the following SELECT to determine the data files in which most of the buffer busy waits occurred:</p>\r\n<p style=\"padding-left: 90px;\">SELECT * FROM<br />(SELECT COUNT, FILE#, SUBSTR(NAME, 1, 50) FILE_NAME<br />FROM X$KCBFWAIT, V$DATAFILE<br />WHERE INDX + 1 = FILE#<br />ORDER BY COUNT DESC)<br />WHERE ROWNUM &lt;=20;</p>\r\n<p style=\"padding-left: 60px;\">As of Oracle 9i, V$SEGMENT_STATISTICS contains information as to how many Busy Wait buffers occurred for every segment. In the following query, for example, you can determine the 20 segments with most Buffer Busy Waits:</p>\r\n<p style=\"padding-left: 90px;\">SELECT * FROM<br />(SELECT OBJECT_NAME, VALUE FROM V$SEGMENT_STATISTICS<br />WHERE STATISTIC_NAME = 'buffer busy waits'<br />ORDER BY VALUE DESC )<br />WHERE ROWNUM &lt;=20;</p>\r\n<p style=\"padding-left: 60px;\">The Oracle view V$WAITSTAT contains an overview of how often Buffer Busy Waits occurred in the individual block types since the database was started as well as the average length of the waits. Depending on the type of the block, take the following measures:</p>\r\n<ul>\r\n<ul>\r\n<li>Data block / bitmap block / bitmap index block: If INSERTs run onto the \"buffer busy waits\", the number of the FREELISTs can be increased for the affected segment. This change can be carried out dynamically - it is not necessary to reorganize the object. The FREELISTs are assigned to the Oracle processors according to the algorithm MOD(PID, #FREELISTs) + 1 (where PID denotes the Oracle-PID from V$PROCESS).</li>\r\n</ul>\r\n</ul>\r\n<p style=\"padding-left: 90px;\">If this change does not result in a successful outcome, you can increase the number of FREELIST GROUPs. It is useful to use a different prime number for the number of FREELISTS and FREELIST GROUPs because the calculation of the used FREELIST GROUP in a non-RAC environment occurs according to the same algorithm as the FREELIST calculation.<br />Another solution is to change to ASSM (see Note 620803). Otherwise you must optimize the SQL statement, the application and/or the I/O subsystem. You may need to increase the db_block_buffers Oracle parameter.</p>\r\n<ul>\r\n<ul>\r\n<li>Segment header: Use several FREELIST GROUPs for the segment in question. You can only change the number of FREELIST GROUPs when reconstructing the object. Another solution is to change to ASSM (see Note 620803).</li>\r\n<li>Undo block: The I/O times for the rollback segments must be optimized.</li>\r\n<li>Undo header: There are too few rollback segments. Increase the number of the rollback segments to at least a quarter of the number of work processes.</li>\r\n</ul>\r\n</ul>\r\n<p style=\"padding-left: 60px;\">Buffer busy waits that have a significantly high average wait time may be the consequence of a simultaneous \"archiver stuck\". In this case, the \"buffer busy\" waits are only a consequence of the problem, and do not require further analysis.</p>\r\n<p style=\"padding-left: 60px;\">Buffer busy waits may be caused by \"log buffer space\" waits. Therefore, use (for example) V$ACTIVE_SESSION_HISTORY on Oracle 10g to check whether \"buffer busy waits\" usually occur at the same time as \"log buffer space\" waits.</p>\r\n<p style=\"padding-left: 60px;\">Buffer busy waits may also occur due to \"async disk IO\" waits or \"Data file init write\" waits caused by AUTOEXTEND operations. In this case, these are \"File Header Block\" waits in V$WAITSTAT.</p>\r\n<p style=\"padding-left: 60px;\">Buffer busy waits may also occur as a result of \"free buffer waits\". You must therefore check whether \"free buffer waits\" also occur at the same time as \"buffer busy waits\".</p>\r\n<ul>\r\n<li>write complete waits.</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Meaning: Waiting until the DBWR has written a necessary block to the disk</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Parameter: File number/Block number/ID</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Rule of thumb: Should not be in the top 10 of V$SYSTEM_EVENT</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Optimization steps:</li>\r\n</ul>\r\n</ul>\r\n<p style=\"padding-left: 60px;\">You must tune the DBWR performance. Check whether the I/O subsystem can be tuned, whether the database buffer pool (db_block_buffers) is set too small and whether it would be useful to define several DBWR processes.</p>\r\n<ul>\r\n<li>free buffer waits</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Meaning: Waiting for the DBWR to write dirty blocks to disk so that they can be replaced by new blocks</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Parameter: File number/Block number/ID</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Rule of thumb: Should not be in the top 10 of V$SYSTEM_EVENT</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Optimization steps:</li>\r\n</ul>\r\n</ul>\r\n<p style=\"padding-left: 60px;\">\"Free buffer\" waits are an indication that the DBWR processes are not writing changed blocks to the disk quickly enough. Therefore, check the Oracle I/O configuration as described in Note 793113. Also ensure that the Oracle buffer pool is large enough (refer to Note 789011).</p>\r\n<p style=\"padding-left: 60px;\">If you determine that not all DBWR processes are continually active, dispite very large \"free buffer\" waits, you can reduce the Oracle parameter FAST_START_MTTR_TARGET as a test, to force the DBWR processes to become more active. However, bear in mind that reducing this parameter causes a greater write-load on the DBWR processes, which can be counterproductive.</p>\r\n<ul>\r\n<li>Oracle &lt;= 9i: latch free<br />Oracle &gt;= 10g: latch: &lt;latch_name&gt; / latch free</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Meaning: Waiting for a latch to be released; a latch is a very basic serialization mechanism that can be used to prevent data structures in the SGA being accessed simultaneously.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Parameter: latch address/latch number/number of sleeps</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Optimization steps:</li>\r\n</ul>\r\n</ul>\r\n<p style=\"padding-left: 60px;\">See Note 767414.</p>\r\n<ul>\r\n<li>Oracle &lt;= 9i: enqueue<br />Oracle &gt;= 10g: enq: &lt;typ&gt; - &lt;description&gt;</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Meaning: waiting for an Oracle-Lock</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Parameter: Type/ID1/ID2</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Optimization steps:</li>\r\n</ul>\r\n</ul>\r\n<p style=\"padding-left: 60px;\">See Note 745639.</p>\r\n<ul>\r\n<li>library cache lock<br />library cache load lock<br />library cache pin</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Meaning: Waiting for exclusive access to data of the library cache</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Optimization steps:</li>\r\n</ul>\r\n</ul>\r\n<p style=\"padding-left: 60px;\">If accesses are hanging on certain tables on these wait events, the problem can be triggered by a hanging Oracle session. For more information, see Note 20071.</p>\r\n<p style=\"padding-left: 60px;\">If you are using Oracle &lt;= ******* with 64-bit on UNIX, see also Note 376905.</p>\r\n<p style=\"padding-left: 60px;\">If you notice \"library cache pin\" waits with Oracle ******* or *******, refer also to Note 649876.</p>\r\n<p style=\"padding-left: 60px;\">Also check for Oracle &lt;= ******* whether you have the bug described in Note 768308, and import a fix.</p>\r\n<p style=\"padding-left: 60px;\">If the problem occurs with Oracle *******, refer to Note 846364.</p>\r\n<p style=\"padding-left: 60px;\">If these wait events occur when running catalog scripts (Note 582427), make sure that no parallel activities are running on the database (for example by monitoring tools such as PATROL).</p>\r\n<p style=\"padding-left: 60px;\">If a deadlock of TX enqueues or library cache locks occurs with row cache locks in connection with parallel index rebuilds, note the bug described in Note 904188.</p>\r\n<p style=\"padding-left: 60px;\">Deadlocks of \"row cache lock\" and \"library cache lock\" may also be triggered in connection with ALTER INDEX ... COALESCE because unlike in the standard system behavior, this operation requests a \"row cache lock\" first and then a \"library cache lock\". This incorrect behavior is addressed in Oracle bug 6051177.<br />\"library cache lock\" waits can appear for any access if a VALIDATE STRUCTURE CASCADE ONLINE, an INDEX COALESCE or an EXPLAIN and an ALTER, ANALYZE STATISTICS or DBMS_STATS command is executed on the same segment (or on a table and relevant index). In this case, one of the two sessions must be cancelled to allow the table to be accessed again.</p>\r\n<p style=\"padding-left: 60px;\">You can use the following statement to determine the sessions that are currently retaining a library cache lock and that are blocking other sessions:</p>\r\n<p style=\"padding-left: 90px;\">SELECT DISTINCT LOCK_A.KGLLKSNM<br />FROM X$KGLLK LOCK_A, X$KGLLK LOCK_B<br />WHERE<br />LOCK_A.KGLLKHDL = LOCK_B.KGLLKHDL AND<br />LOCK_A.KGLLKREQ = 0 AND<br />LOCK_B.KGLLKSES IS NOT NULL AND<br />LOCK_B.KGLLKREQ &gt; 0;</p>\r\n<p style=\"padding-left: 60px;\">As of Oracle 10g, you can also use the BLOCKING_SESSION entry in V$SESSION to determine the session that is blocking another session &lt;sid&gt; using a library cache lock (or another lock). See also the corresponding SELECT in Note 20071.</p>\r\n<p style=\"padding-left: 60px;\">\"Library cache lock\" waits that last a long time under Oracle 9.2.0.8 or ******** may also be a consequence of the bug described in Note 971261.</p>\r\n<p style=\"padding-left: 60px;\">Another result of an ORA-04031 error may be waits on \"library cache pin\" or \"library cache load lock\". Therefore, you must check in the alert log whether one of these errors occurred during the period in question and refer to Note 869006.</p>\r\n<p style=\"padding-left: 60px;\">If these wait situations occur in relation to a FOR ALL ENTRIES access that is executed in parallel frequently, you may consider to increase the blocking factors (Note 881083).</p>\r\n<p style=\"padding-left: 60px;\">If a session waits for more than five minutes for \"library cace lock\" or \"library cache pin\", the session is terminated with the error ORA-04021. If a deadlock occurs, the system returns error ORA-04020.</p>\r\n<p style=\"padding-left: 60px;\">Wait situations on \"library cache lock\" can also be follow-on problems of an archiver stuck (\"log file switch (archiving needed)\").</p>\r\n<ul>\r\n<li>row cache lock</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Meaning: Waiting for exclusive row cache access</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Parameter: Cache ID/lock mode/request</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Optimization steps:</li>\r\n</ul>\r\n</ul>\r\n<p style=\"padding-left: 60px;\">High \"row cache lock\" wait times are usually triggered by Oracle bugs. Check whether you have one of the bugs described in Notes 738641 and 768308.<br />If a deadlock of TX enqueues or library cache locks occurs with row cache locks in connection with parallel index rebuilds, note the bug described in Note 904188.</p>\r\n<p style=\"padding-left: 60px;\">When you use a large shared pool and a high rate of CREATE, DROP and TRUNCATE statements (such as in the BW environment, for instance), certain accesses to the row cache can slow down significantly because the management structures are getting bigger and bigger in the shared pool. In such a case, you can perform a flush of the shared pool as a workaround:<br />ALTER SYSTEM FLUSH SHARED_POOL;</p>\r\n<p style=\"padding-left: 60px;\">The first argument of the \"row cache lock\" event contains the ID of the specified subcache. You can use this value to determine the row cache area in question, as follows:</p>\r\n<p style=\"padding-left: 90px;\">SELECT * FROM V$ROWCACHE WHERE CACHE# = '';</p>\r\n<p style=\"padding-left: 60px;\">In many cases, you can use the following statement to determine the sessions that are currently holding a row cache lock and the respective sub-cache:</p>\r\n<p style=\"padding-left: 90px;\">SELECT S.SID, R.LOCK_MODE, SUBSTR(R.CACHE_NAME, 1, 30) CACHE_NAME<br />FROM V$ROWCACHE_PARENT R, V$SESSION S<br />WHERE R.SADDR = S.SADDR;</p>\r\n<p style=\"padding-left: 60px;\">As of Oracle 10g, you can determine the lock holder on the basis of the column BLOCKING_SESSION in V$SESSION. See the corresponding SQL statement in Note 20071.</p>\r\n<p style=\"padding-left: 60px;\">A permanent lock on the row cache can also be triggered by the network problem described in Note 20071.</p>\r\n<p style=\"padding-left: 60px;\">Large-scale \"row cache lock\" wait situations can also occur in connection with an archiver stuck (\"log file switch (archiving needed\", Note 391) and are only a subsequent problem in this case.</p>\r\n<p style=\"padding-left: 60px;\">If \"row cache lock\" deadlocks occur with Oracle 10g, refer to Note 1017970.</p>\r\n<p style=\"padding-left: 60px;\">If \"row cache lock\" waits occur on partitions on Oracle ******** in connection with SHRINK SPACE COMPACT, see Note 1121838.</p>\r\n<p style=\"padding-left: 60px;\">Deadlocks of \"row cache lock\" and \"library cache lock\" may also be triggered in connection with ALTER INDEX ... COALESCE because unlike in the standard system behavior, this operation requests a \"row cache lock\" first and then a \"library cache lock\". This incorrect behavior is addressed in Oracle bug 6051177.</p>\r\n<p style=\"padding-left: 60px;\">An ORA-04031 error may also cause \"row cache lock\". Therefore, you must check in the alert log whether one of these errors occurred during the period in question and refer to Note 869006.</p>\r\n<p style=\"padding-left: 60px;\">If you experience long wait times with \"row cache lock\", the system may display the following log entries: \"WAITED TOO LONG FOR A ROW CACHE ENQUEUE LOCK!\".</p>\r\n<ul>\r\n<li>Null event</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Meaning: Unspecific wait event</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Parameter: -</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Optimization steps:</li>\r\n</ul>\r\n</ul>\r\n<p style=\"padding-left: 60px;\">\"Null event\" is usually an idle event. There is therefore normally no handling requirement. However, in exceptional cases it is possible that a session may hang for a longer period of time on \"Null event\" (with WAIT_TIME = 0).</p>\r\n<p style=\"padding-left: 60px;\">A possible scenario for a hanging \"Null event\" is an Outer Join (for instance, using \"(+)\" in the SQL statement) on Oracle 9i. In this case, see Note 626172.</p>\r\n<p style=\"padding-left: 60px;\">Hints such as USE_CONCAT can cause a \"Null event\" on Oracle 9i. Problems like this can be solved in the CO area, for instance, by implementing the correction instructions 552217 from Note 545932.</p>\r\n<p style=\"padding-left: 60px;\">Otherwise, contact SAP Support if statements appear on \"Null event\" for a prolonged period.</p>\r\n<ul>\r\n<li>db file parallel write</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Meaning: waiting of the DBWR, until blocks have been written to disk</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Optimization steps:</li>\r\n</ul>\r\n</ul>\r\n<p style=\"padding-left: 60px;\">Since the writing takes place asynchronously, this event is usually not critical. If the DBWR really does have problems with the write performance, this appears in wait events such as \"free buffer waits\" or \"log file switch (checkpoint incomplete)\", to which you can then react as described.</p>\r\n<p style=\"padding-left: 60px;\">Depending on your Oracle version and operating system, the the logged times may be too low (for example, HP-UX) or too high (for example, AIX).<br />The times must always be set in relation to the number of written requests (P1). If there is a greater number of written requests, runtimes in the second area are acceptable.</p>\r\n<ul>\r\n<li>DB file single write</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Meaning: Writing individual blocks to the hard disk</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Parameter: File number/Block number/1</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Optimization steps:</li>\r\n</ul>\r\n</ul>\r\n<p style=\"padding-left: 60px;\">Optimization steps: \"db file single write\" waits occur if the header of a data file is changed by the background process during ALTER TABLESPACE BEGIN BACKUP or ALTER TABLESPACE END BACKUP. Exceptions concerning this wait event are triggered by I/O problems. See the information under \"log file sync\".</p>\r\n<ul>\r\n<li>rdbms ipc reply</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Meaning: shadow process waiting for a background process</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Parameter: PID of the background process/timeout in seconds/-</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Optimization steps:</li>\r\n</ul>\r\n</ul>\r\n<p style=\"padding-left: 60px;\">If a session hangs for a prolonged period on this wait event, the PID of the background process can be used to determine the background process it is waiting for:</p>\r\n<p style=\"padding-left: 90px;\">SELECT NAME FROM V$BGPROCESS WHERE PADDR =<br />(SELECT ADDR FROM V$PROCESS WHERE PID = );</p>\r\n<p style=\"padding-left: 60px;\">You can then continue to analyze its activity (for example, using an ORADEBUG trace as described in Note 613872).</p>\r\n<p style=\"padding-left: 60px;\">\"rdbms ipc reply\" waits usually occur with BEGIN BACKUP, TRUNCATE and DROP operations for which the CKPT process must execute a checkpoint.</p>\r\n<p style=\"padding-left: 60px;\">Furthermore, there have to be \"rdbms ipc reply\" waits in the context of a buffer pool flush. There are also \"rdbms ipc reply\" waits on the DBWR in the context of RESIZE operations.</p>\r\n<p style=\"padding-left: 60px;\">If \"rdbms ipc reply\" occurs with TRUNCATE on Oracle ******* or lower, refer to Note 695841.</p>\r\n<p style=\"padding-left: 60px;\">With Oracle 9i or lower, the design flaws described in Note 758989 result in increased \"rdbms ipc reply\" times in the sequence of checkpoints for BEGIN BACKUP, DROP and TRUNCATE. Oracle 10g solves this problem. You can alleviate the problem by reducing the buffer pool, but often you do not want this because of a deterioration in performance elsewhere. For information on optimizing BEGIN BACKUP runtimes, see also Note 875477.</p>\r\n<p style=\"padding-left: 60px;\">In BW, you can convert TRUNCATEs on small tables into DELETEs by maintaining the ORA_TABTRUNCATE_MINIMUM parameter in RSADMIN (Note 840553).</p>\r\n<p style=\"padding-left: 60px;\">The duration of \"rdbms ipc reply\" waits may also be associated with a poor I/O performance. In this context, refer to Note 793113 and, if necessary, configure several DBWR processes.</p>\r\n<p style=\"padding-left: 60px;\">\"rdbms ipc reply\" waits are closely associated with CI enqueues (Note 745639). A session that waits for \"rdbms ipc reply\" always allocates the CI enqueue. If necessary, further sessions must wait for the CI enqueue.</p>\r\n<ul>\r\n<li>refresh controlfile command</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Meaning: Refreshing the controlfile size information</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Optimization steps:</li>\r\n</ul>\r\n</ul>\r\n<p style=\"padding-left: 60px;\">This wait event is generally an uncritical event. It is involved with control file information in conjunction with accesses to X$ views (and the V$ views based on this) and should not last any longer than a few milliseconds. Significantly longer wait times have thus far only been observed on AIX if the Oracle kernel extension (pw-syscall) was loaded in various different versions in parallel or if a reboot was forgotten after the extension was imported. See Note 328822.</p>\r\n<ul>\r\n<li>SQL*Net message to client<br />SQL*Net message to dblink<br />SQL*Net more data to client<br />SQL*Net more data to dblink<br />SQL*Net message from client<br />SQL*Net more data from client<br />SQL*Net message from dblink<br />SQL*Net more data from dblink</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Meaning: Communication between the shadow process and the client</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Parameter: Driver ID/Bytes/-</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Optimization steps:</li>\r\n</ul>\r\n</ul>\r\n<p style=\"padding-left: 60px;\">Wait events such as \"SQL*Net message from client\" or \"SQL*Net message from dblink\" are categorized as idle events, but (just like the other SQL*Net wait events) they may be indicative of communication problems between Oracle and the Oracle client (that is, SAP). As of Oracle 11g, \"SQL*Net message from dblink\" is ranked as a \"Network\" wait event rather than an \"Idle\" wait event.</p>\r\n<p style=\"padding-left: 60px;\">Note that it is normal that \"SQL*Net message from client\" appears high up in the V$SYSTEM_EVENT, because this is the main idle event for Oracle shadow processes.</p>\r\n<p style=\"padding-left: 60px;\">If the system often waits for these events DURING THE RUNTIME of transactions, you should check your network configuration. As well as operating system tools such as PING, the SAP tool NIPING (Note 500235) can also be used to analyze the network.</p>\r\n<p style=\"padding-left: 60px;\">You must also check that the Oracle net configuration is correct (Note 562403). When you use Oracle 9i or lower, make sure that TCP.NODELAY in protocol.ora, .protocol.ora and/or sqlnet.ora is set correctly (Notes 72638 and 198752).</p>\r\n<p style=\"padding-left: 60px;\">If you are in any doubt as to whether TCP.NODELAY is drawn correctly, you can draw an Oracle net client trace with SUPPORT level (Note 562403) to obtain more accurate information. If TCP.NODELAY is drawn correctly, an entry such as the following must appear in the trace file that is created:</p>\r\n<p style=\"padding-left: 90px;\">nttcon: got tcp.nodelay = 1</p>\r\n<p style=\"padding-left: 60px;\">If this entry is missing, and instead a message such as</p>\r\n<p style=\"padding-left: 90px;\">ntvllt: No network parameter file found</p>\r\n<p style=\"padding-left: 60px;\">is logged, there are problems when you access protocol.ora or sqlnet.ora. As well as using .protocol.ora as described in Note 198752, instead of protocol.ora, the problem can also be caused by incorrectly installed client libraries. Refer to Note 180430 and the sub-notes referred to there for a description of the installation of the Oracle client software.</p>\r\n<p style=\"padding-left: 60px;\">Small SDU sizes are another reason for \"SQL*Net more data to client\" and \"SQL*Net more data from client\" (\"Session Data Unit\") occurring frequently, whereby a larger dataset is transferred through the network in several small packages. The SDU values in tnsnames.ora and listener.ora can be increased in this case (Note 562403).</p>\r\n<p style=\"padding-left: 60px;\">\"SQL*Net more data from dblink\" also contains the time that the remote session requires to procure the data. Therefore, if the \"SQL*Net more data from dblink\" times increase, check if the processing of the database link queries on the remote database can be optimized.</p>\r\n<ul>\r\n<li>index block split</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Meaning: Waiting for the split of an index block as part of an INSERT.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Parameter: Root DBA/level/child DBA</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Rule of thumb: This wait event should never appear under the top 10 wait events. A process must never wait longer than a fraction of a second for \"index block split\".</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Optimization steps:</li>\r\n</ul>\r\n</ul>\r\n<p style=\"padding-left: 60px;\">If this wait event occurs, it is usually an individual problem with an index through which an Oracle session goes into a loop and waits permanently for \"index block split\". Check whether a REBUILD ONLINE for the affected index solves the problem. If not, open an SAP customer message for a more thorough analysis.</p>\r\n<ul>\r\n<li>SQL*Net break/reset to client<br />SQL*Net break/reset to client</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Meaning: The execution of a statement is prematurely terminated</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Optimization steps:</li>\r\n</ul>\r\n</ul>\r\n<p style=\"padding-left: 60px;\">If these wait events occur increasingly, you must first determine the cause of the terminations. In the SAP environment, the most frequent cause is INSERTs that encounter an ORA-00001 because a data record with the same unique key already exists in the table. However, SELECTs from a closed cursor or a non-existing table may also lead to this type of wait event. The same applies for SQL statements that contain syntax errors and for cursor fetches for which the last data record was already previously delivered.</p>\r\n<p style=\"padding-left: 60px;\">If this problem occurs in connection with duplicate keys for INSERTs, in the application you must check whether you can reduce or prevent these INSERTs.</p>\r\n<ul>\r\n<li>io done</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Meaning: Waiting for a synchronous write I/O to finish</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Optimization steps:</li>\r\n</ul>\r\n</ul>\r\n<p style=\"padding-left: 60px;\">The event \"io done\" only occurs if a synchronous I/O is used. Therefore, check (as described in Note 793113) whether an asynchronous I/O can be used and whether the parameters DISK_ASYNCH_IO and FILESYSTEMIO_OPTIONS are set correctly.</p>\r\n<ul>\r\n<li>imm op</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Meaning: Waiting for an IMMEDIATE I/O request to a&#160;child process to end.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Optimization steps:</li>\r\n</ul>\r\n</ul>\r\n<p style=\"padding-left: 60px;\">The event \"imm op\" may occur if I/O&#160;child processes&#160;are used, that is, if parameters such as DBWR_IO_SLAVES or BACKUP_TAPE_IO_SLAVES are used. Generally, the \"imm op\" event does not pose a problem.</p>\r\n<p style=\"padding-left: 60px;\">If DBWR_IO_SLAVES are set to 1 or higher, we recommend that you check whether you can use multiple DBWR processes instead (DB_WRITER_PROCESSES parameter).</p>\r\n<p style=\"padding-left: 60px;\">BACKUP_TAPE_IO_SLAVES is set to TRUE in the case of a RMAN backup if dedicated I/O&#160;child processes are to copy backup write processes to tape and not the Oracle shadow processes. In this case, \"imm op\" waits only affect the backup runtime, but not the live system.</p>\r\n<ul>\r\n<li>index (re)build online start<br />index (re)build online cleanup<br />index (re)build online merge</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Meaning: Wait situations during an ALTER INDEX REBUILD ONLINE</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Parameter: Object ID / - / -</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Optimization steps:</li>\r\n</ul>\r\n</ul>\r\n<p style=\"padding-left: 60px;\">The wait events may occur if the system is waiting for an as yet uncommitted change during a REBUILD ONLINE (see Note 869521). Therefore, avoid running REBUILD ONLINE in parallel to a large number or long-time uncommitted changes to the corresponding table.</p>\r\n<ul>\r\n<li>ksu process alloc latch yield</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Optimization steps:</li>\r\n</ul>\r\n</ul>\r\n<p style=\"padding-left: 60px;\">Optimization steps: If the problem occurs with Oracle 9.2.0.7, implement the correction from Note 894078.</p>\r\n<ul>\r\n<li>checkpoint completed</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Meaning: Waits for a checkpoint to be completed</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Optimization steps:</li>\r\n</ul>\r\n</ul>\r\n<p style=\"padding-left: 60px;\">The \"checkpoint completed\" Wait event occurs if a session explicitly has to wait for a checkpoint to finish in certain situations (for example, when stopping or sometimes even starting up the database). To optimize this, proceed as described in the process for the wait events \"checkpoint not complete\".</p>\r\n<ul>\r\n<li>Oracle &lt;= 9i: sbtinit / sbtopen / sbtread / sbtwrite / sbtclose / sbtinfo / sbtremove / sbtbackup / sbtclose2 / sbtcommand / sbtend / sbterror / sbtinfo2 / sbtinit2 / sbtread2 / sbtremove2 / sbtrestore / sbtwrite2 / sbtpcbackup / sbtpccancel / sbtpccommit / sbtpcend / sbtpcquerybackup / sbtpcqueryrestore / sbtpcrestore / sbtpcstart / sbtpcstatus / sbtpcvalidate</li>\r\n</ul>\r\n<p style=\"padding-left: 30px;\">Oracle &gt; = 10g: Backup: sbtinit / backup: sbtopen / backup: sbtread / backup: sbtwrite / backup: sbtclose / backup: sbtinfo / backup: sbtremove / backup: sbtbackup / backup: sbtclose2 / backup: sbtcommand / backup: sbtend / backup: sbterror / backup: sbtinfo2 / backup: sbtinit2 / backup: sbtread2 / backup: sbtremove2 / backup: sbtrestore / backup: sbtwrite2 / backup: sbtpcbackup / backup: sbtpccancel / backup: sbtpccommit / backup: sbtpcend / backup: sbtpcquerybackup / backup: sbtpcqueryrestore / backup: sbtpcrestore / backup: sbtpcstart / backup: sbtpcstatus / backup: sbtpcvalidate / RMAN backup &amp; recovery I/O</p>\r\n<ul>\r\n<ul>\r\n<li>Meaning: Wait situations during RMAN backup</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Optimization steps:</li>\r\n</ul>\r\n</ul>\r\n<p style=\"padding-left: 60px;\">The wait events that begin with \"sbt\" (Oracle &lt;= 9i) or \"Backup: sbt\" (Oracle &gt;= 10g) are connected with wait situations during an RMAN backup. This means they only affect the backup runtime, but not the SAP database accesses. Because of this, you can ignore these wait events when you analyze the performance of an SAP system.</p>\r\n<ul>\r\n<li>Wait for shrink lock2 (Oracle 10g or higher)</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Meaning: A shrink is waiting for an enqueue to be released.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Optimization steps:</li>\r\n</ul>\r\n</ul>\r\n<p style=\"padding-left: 60px;\">\"Wait for shrink lock2\" occurs in a shrink (Note 910389) if a TM enqueue (Note 766349) on the segment is already being held and requested by other sessions at the same time. For this reason, avoid carrying out other activities on the table in question during the shrink.</p>\r\n<ul>\r\n<li>sort segment request</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Meaning: Waiting for the allocation of a sort segment</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Optimization steps:</li>\r\n</ul>\r\n</ul>\r\n<p style=\"padding-left: 60px;\">This wait event occurs when the SMON process is very busy, and is no longer able to quickly process the incoming requests. This might be the case, for example, if large rollback activities are carried out.</p>\r\n<p style=\"padding-left: 60px;\">Even if the SMON process executes space transactions, it can lead to follow-on problems such as wait situations at \"sort segment request\". In this case, refer to the \"TYPE = ST\" section from Note 745639.</p>\r\n<p style=\"padding-left: 60px;\">Older Oracle releases sometimes contain errors which can lead to unnecessary \"sort segment request\" waits. Therefore, check in the first step whether the problem also occurs with the latest Oracle patch set. If this is the case, create an SAP message.</p>\r\n<ul>\r\n<li>wait for stopper event to be increased</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Meaning: SMON rollback operations in the background</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Optimization steps:</li>\r\n</ul>\r\n</ul>\r\n<p style=\"padding-left: 60px;\">The event usually occurs in connection with extensive rollback operations after a transaction recovery, or SHUTDOWN ABORT and STARTUP. This only affects the SMON process. If no other processes are affected, there is no need for further processing. This can, however, mean that subsequent problems occur such as \"sort segment shrink\" or there are SS enqueue waits, which then have to be optimized.<br />Also note the special case described in Note 963894.</p>\r\n<ul>\r\n<li>wait for an undo record</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Meaning: A PX session waits for an undo record while a transaction recovery is running.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Optimization steps:</li>\r\n</ul>\r\n</ul>\r\n<p style=\"padding-left: 60px;\">If several parallel execution&#160;child processes&#160;want to access the same undo record as part of a parallelized transaction recovery, the wait event \"wait for a undo record\" may occur. If this causes delays in the transaction recovery or has an effect on the running system, you can deactivate it as follows:<br />FAST_START_PARALLEL_ROLLBACK = FALSE</p>\r\n<ul>\r\n<li>wait list latch free</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Meaning: Waiting for a latch</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Optimization steps:</li>\r\n</ul>\r\n</ul>\r\n<p style=\"padding-left: 60px;\">The \"wait list latch free\" optimization runs at the same time as the \"latch free\" optimization. Therefore, refer to the information for the \"latch free\" wait event.</p>\r\n<ul>\r\n<li>async disk IO<br />ksfd: async disk IO (Oracle &gt;= 10g)</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Meaning: Waiting for asynchronous disk accesses</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Optimization steps:</li>\r\n</ul>\r\n</ul>\r\n<p style=\"padding-left: 60px;\">\"async disk IO\" or \"ksfd: async disk IO\" can occur in different situations: For example, during the creation of tablespaces, the creation or expansion of data files, the execution of RMAN backups, the archiving of redo logs with ARCH processes, or AUTOEXTEND extensions (such as during INSERT operations). You can use V$SESSION_EVENT to determine which sessions are mainly responsible for these wait situations. For optimization options, see Note 793113.</p>\r\n<ul>\r\n<li>PX Deq: Execute Reply<br />PX Deq: Table Q Normal<br />PX Deq Credit: send blkd<br />PX Deq: Execution Msg<br />PX qref latch<br />PX Deq: Signal ACK<br />PX Deq: Join ACK<br />PX Deq Credit: need buffer<br />PX Deq: Parse Reply<br />reliable message</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Meaning: Wait events in the parallel execution area</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Optimization steps:</li>\r\n</ul>\r\n</ul>\r\n<p style=\"padding-left: 60px;\">See Note 651060.</p>\r\n<ul>\r\n<li>local write wait</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Meaning: Waiting for a write operation to the hard disk</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Optimization steps:</li>\r\n</ul>\r\n</ul>\r\n<p style=\"padding-left: 60px;\">\"local write wait\" mainly occur in connection with TRUNCATE operations. You can tune this wait event to optimize the I/O behavior. For more information, see Note 793113.</p>\r\n<ul>\r\n<li>control file single write</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Meaning: Write access to control file</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Optimization steps:</li>\r\n</ul>\r\n</ul>\r\n<p style=\"padding-left: 60px;\">\"control file single write\" waits appear with operations that access the control file (for example, BACKUP CONTROLFILE, END BACKUP). Generally, these waits do not cause any performance problems.</p>\r\n<ul>\r\n<li>Wait for Table Lock</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Meaning: Wait for exclusive TM enqueue in materialized view operations.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Optimization steps:</li>\r\n</ul>\r\n</ul>\r\n<p style=\"padding-left: 60px;\">In the SAP environment, materialized views (Note 741478) are only implicitly used as standard when you carry out an online reorganization. Therefore, the \"Wait for Table Lock\" wait event generally only occurs in connection with an online reorganization. If you reorganize a table online and execute commands such as DBMS_REDEFINITION.START_REDEF_TABLE or DBMS_REDEFINITION.FINISH_REDEF_TABLE, and if another session simultaneously holds an exclusive TM enqueue (Note 745639) on the same table, the reorg session waits for \"Wait for Table Lock\".</p>\r\n<p style=\"padding-left: 60px;\">Therefore, this wait event only affects the runtime of reorganizations, and does not affect the production operation. You can reduce the number of times \"Wait for Table Lock\" occurs by not carrying out any other activities that set an exclusive TM enqueue at the same time as the online reorganization (also see Note 745639).</p>\r\n<ul>\r\n<li>writes stopped by instance recovery or database suspension</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Meaning: This is the wait situation of processes when you stop the database using ALTER SYSTEM SUSPEND (for example, during a split mirror backup).</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Optimization steps:</li>\r\n</ul>\r\n</ul>\r\n<p style=\"padding-left: 60px;\">Check if it is possible to reduce the frequency with which ALTER SYSTEM SUSPEND is executed, or if you can reduce the time between SUSPEND and RESUME. Also check whether you can execute the SUSPEND at a timem of lower system load.</p>\r\n<p style=\"padding-left: 60px;\">This wait event does not only prevent blocks from being written to the disk - it also stops the system from executing DML operations such as INSERT or UPDATE.</p>\r\n<ul>\r\n<li>cursor: mutex X cursor: mutex S<br />cursor: pin S wait on X<br />cursor: pin X<br />cursor: pin S<br />library cache: mutex X<br />library cache: mutex S</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Meaning: Wait situations involving mutexes (Oracle 10g and higher)</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Optimization steps:</li>\r\n</ul>\r\n</ul>\r\n<p style=\"padding-left: 60px;\">See Note 964344.</p>\r\n<ul>\r\n<li>Data file init write (Oracle 10g or higher)</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Meaning: Wait for the initialization of datafile blocks</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Optimization steps:</li>\r\n</ul>\r\n</ul>\r\n<p style=\"padding-left: 30px;\">This wait situation may occur during productive operation when AUTOEXTEND operations are carried out. To decrease the number of AUTOEXTEND operations and the appearance of these wait events, you can create larger datafiles from the outset.</p>\r\n<ul>\r\n<li>Datapump dump file I/O (Oracle 10g or higher)<br />kupp process wait (Oracle 10g or higher)</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Meaning: These Wait events are connected to Data Pump.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Optimization steps:</li>\r\n</ul>\r\n</ul>\r\n<p style=\"padding-left: 60px;\">See Note 1013049.</p>\r\n<ul>\r\n<li>Streams AQ: qmn coordinator waiting for&#160;child process&#160;to start</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Meaning: Waiting for the QMNC process to start an advanced queuing&#160;child process.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Optimization steps:</li>\r\n</ul>\r\n</ul>\r\n<p style=\"padding-left: 90px;\">In the SAP environment, streams can only be used in connection with Data Pump. See Note 1013049.</p>\r\n<ul>\r\n<li>Statement suspended, wait error to be cleared</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Meaning: Wait for the clean-up of a space error in an activated RESUMABLE.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Optimization steps:</li>\r\n</ul>\r\n</ul>\r\n<p style=\"padding-left: 60px;\">This wait event can only occur if the RESUMABLE option was individually activated for sessions, and this is never the case in the SAP standard system. The wait event occurs when a space error occurs in the RESUMABLE session, and it can be removed by cleaning up the storage problem or by terminating the waiting session.</p>\r\n<ul>\r\n<li>resmgr:become active (Oracle 10g and higher)</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Meaning: Preventing database connections due to an active QUIESCE session</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Optimization steps:</li>\r\n</ul>\r\n</ul>\r\n<p style=\"padding-left: 60px;\">Generally, this wait situation occurs when you execute certain EMCA operations such as the operation for creating the EM repository. As a result of these operations, the systems implicity switches to QUIESCE mode. Therefore, all database connections (except users SYS and SYSTEM) must wait for \"resmgr:become active\". In this case, refer to Note 1044758 and execute the following command if necessary:<br />ALTER SYSTEM UNQUIESCE;</p>\r\n<ul>\r\n<li>resmgr:CPU quantum</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Meaning: Waiting for limitations of the Oracle Resource Manager</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Optimization steps:</li>\r\n</ul>\r\n</ul>\r\n<p style=\"padding-left: 60px;\">Make sure that the Oracle Resource Manager is configured correctly (Note 1589924) or that it is deactivated completely. To deactivate it, the parameter RESOURCE_MANAGER_PLAN must be blank and you must refer to Note 1579946 for maintenance windows.</p>\r\n<ul>\r\n<li>SGA: allocation forcing component growth</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Meaning: The session waits until the system is finished using \"ALTER SYSTEM SET\" to adjust the size of the dynamic SGA components.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Optimization steps:</li>\r\n</ul>\r\n</ul>\r\n<p style=\"padding-left: 60px;\">Only the session that is currently being adjusted has to wait for this wait event. Other sessions can continue to work as normal. Therefore, there is no tuning requirement.</p>\r\n<ul>\r\n<li>kdic_do_merge</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Meaning: INDEX REBUILD is waiting for a library cache lock to be released.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Optimization steps:</li>\r\n</ul>\r\n</ul>\r\n<p style=\"padding-left: 60px;\">To avoid critical deadlocks between INDEX REBUILDs and other DDL operations, we introduced the wait event \"kdic_do_merge\" that takes about two seconds in the Oracle bug 3424721. The system uses this wait event if an offline INDEX REBUILD must wait for a library cache lock that is being held by another DDL operation. This can be solved by avoiding DDL activities that exist in parallel to INDEX REBUILD.</p>\r\n<ul>\r\n<li>control file sequential read</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Meaning: A block from the control file is read</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Optimization steps:</li>\r\n</ul>\r\n</ul>\r\n<p style=\"padding-left: 60px;\">These waits occur, among other things, if control file-related V$ views are accessed. Generally, the number of accesses should be manageable and non-critical. However, unfavorable access plans for V$ views may result in an increased number of accesses to control files. For example, the problem described in Note 1011731 when accessing V$RMAN_STATUS may result in the object X$KCCRSR (which is references as part of this view) being used an internal table for extensive nested loop joins, which requires a lot of individual control file accesses.</p>\r\n<p style=\"padding-left: 60px;\">If these problems occur, check whether a bug may be responsible. Oracle 10g and higher should also ensure that DDIC and fixed object statistics exist and are up-to-date in accordance with Note 838725.</p>\r\n<ul>\r\n<li>opishd</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Meaning: Waiting for successful shutdown</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Optimization steps:</li>\r\n</ul>\r\n</ul>\r\n<p style=\"padding-left: 30px;\">If a session performs a shutdown and has to wait for active processes, it results in \"opishd\" waits. After an hour, the shutdown is terminates (\"SHUTDOWN: Active sessions prevent database close operation\").</p>\r\n<p style=\"padding-left: 30px;\">In such cases, check which processes were responsible for the long shutdown (also see Note 521264) and remove the problem.</p>\r\n<ul>\r\n<li>flashback buf free by RVWR</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Meaning: Wait on writing in Fast Recovery Area</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Optimization steps:</li>\r\n</ul>\r\n</ul>\r\n<p style=\"padding-left: 60px;\">Read Note 966117, make sure that the Fast Recovery Area is on the fastest disks possible, and if possible, minimize the I/O in the Fast Recovery Area (for example, by using Restore Points instead of the general Flashback Database).</p>\r\n<ul>\r\n<li>block change tracking buffer space change tracking file synchronous write<br />change tracking file synchronous read</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Meaning: Wait situation in the context of block change tracking</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Optimization steps:</li>\r\n</ul>\r\n</ul>\r\n<p style=\"padding-left: 60px;\">See Note 964619.</p>\r\n<ul>\r\n<li>Disk file operations I/O</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Meaning: Wait on special file operations such as OPEN, CLOSE or RESIZE.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Optimization steps:</li>\r\n</ul>\r\n</ul>\r\n<p style=\"padding-left: 60px;\">Check which SQL statements mainly wait on this wait event and optimize them. The waits often occur in connection with Oracle DDIC accesses.</p>\r\n<ol start=\"16\">\r\n<li>Does Real Application Cluster (RAC) have different events?</li>\r\n</ol>\r\n<p style=\"padding-left: 60px;\"><span style=\"font-size: 14px;\">Yes. See Note 1609612 for RAC details.</span></p>\r\n<ol start=\"17\">\r\n<li>Which wait events are Exadata specific?</li>\r\n</ol>\r\n<p style=\"padding-left: 30px;\">Exadata was introduced in 2008 as the first in Oracle \"Engineered Systems\". It is a combination of compute and storage running Oracle Software. The storage servers can perform block storage functions (column filtering, predicate filtering, storage indexing...) and also run application software to offload data-intensive database processing into storage closest to the data.</p>\r\n<p style=\"padding-left: 30px;\">The introduction of this type of engineered system by oracle also increased the amount of wait events specific for Exadata. You can find the list with the following command:</p>\r\n<p style=\"padding-left: 60px;\">SELECT name FROM v$event_name WHERE name LIKE 'cell%';</p>\r\n<p style=\"padding-left: 30px;\">Output is (aproximately) 17 for 11g, 22-23 for 12c</p>\r\n<p style=\"padding-left: 30px;\">The most important wait events in exadata are the following:</p>\r\n<ul>\r\n<li>cell single block physical read</li>\r\n</ul>\r\n<p style=\"padding-left: 30px;\">Meaning: Conventional index access. This wait event is equivalent to db file sequential read for a cell.</p>\r\n<ul>\r\n<li>cell multiblock physical read</li>\r\n</ul>\r\n<p style=\"padding-left: 30px;\">Meaning: Conventional Full Table table scan/index fast full scan. This wait event is equivalent to db file scattered read for a cell.</p>\r\n<ul>\r\n<li>cell smart table scan</li>\r\n</ul>\r\n<p style=\"padding-left: 30px;\">Meaning: Smart Scan<sup>1</sup> on a Table.</p>\r\n<ul>\r\n<li>cell smart index scan</li>\r\n</ul>\r\n<p style=\"padding-left: 30px;\">Meaning: Smart Scan<sup>1</sup> on an index. This wait event appears when the database is waiting for index or index-organized table (IOT) fast full scans.</p>\r\n<ul>\r\n<li>cell list of blocks physical read</li>\r\n</ul>\r\n<p style=\"padding-left: 30px;\">Meaning: This wait event is equivalent to database file parallel read for a cell.</p>\r\n<p style=\"padding-left: 30px;\"><span style=\"font-size: 14px;\">&#160;<sup>1</sup> Smart scan. In a Exadata machine, most of of the typical accesses are the same as in non Exadata. When a user selects a row the entire block containing that row is fetched from the disk to the data buffer cache and the selected row (or row columns) is extracted from the block and returned to the client session. In the case of Direct path accesses (full table scans and full index scans) the access is different in an Exadata Machine. The Exadata machine can take the specific rows (or columns) directly from the disk and send them to the database nodes saving I/O. This is known as Smart Scan.</span></p>\r\n<ol start=\"18\">\r\n<li>BACKGROUND processes generating FOREGROUND events.</li>\r\n</ol>\r\n<p style=\"padding-left: 30px;\"><span style=\"font-size: 14px;\">From Oracle 12c on, you might see the following events from class \"<strong>Other</strong>\" being caused mainly by a background process but appearing in monitoring tools. <strong>IF</strong> the mentioned events are not causing performance issues <strong>and</strong> they are asociated to a background program then they should be consider idle.&#160;</span></p>\r\n<p style=\"padding-left: 30px;\"><span style=\"font-size: 14px;\">You can use the following query to find out the process that is generating the event, if it is a <strong>BACKGROUND</strong> or FOREGROUND process and its percentages.</span></p>\r\n<p style=\"padding-left: 60px;\">SELECT <br />instance_number, session_type, program, event, <br />COUNT(*) cnt, TO_CHAR(ROUND(RATIO_TO_REPORT(COUNT(*)) OVER ()*100,2), 99.99) percentage<br />FROM <br />DBA_HIST_ACTIVE_SESS_HISTORY&#160;<br />WHERE <br />event = '&lt;event_name&gt;'<br />GROUP BY <br />instance_number, session_type, program, event<br />ORDER BY cnt DESC</p>\r\n<ul>\r\n<li>\"ges generic event\": RAC event. This is an idle wait event for the RMV* processes. This event is related to Bug 28388910 - fixed in 19c database release. The bug fix needed an idle wait event for the RMV* processes and since a new wait event could not be added in a backport (i.e. in database releases lower than 19.1 in this case) the \"ges generic event\" was reused in 12.1, 12.2 and 18c. The Bug 30794929 is open and it will change the wait class of the event \"ges generic event\" so that it belongs to idle wait class i.e. from the 'Other' wait class to 'Idle' wait class. The bugfix is included in the SAP Bundle Patch for February 2020.</li>\r\n<li>\"RMA: IPC0 Completion sync\": Also a RAC event. This wait event is generated by the background process IPC0. IPC0 is the background process that handles very high rates of incoming connect requests, as well as, completing reconfigurations to support basic messaging and RDMA primitives over several transports such as UDP, RDS, InfiniBand and RC.&#160;There is an internal Oracle bug 28807706 - 'RMA: IPC0 COMPLETION SYNC\" AMONG TOP TIMED EVENTS IN AWR' that might solve the issue. If you see this event in your monitoring tool caused by a background process, please open an incident on the component BC-DB-ORA indicating the background job (with the output of provided statement) and including the output of \"opatch lsinventory\" and an AWR Report.</li>\r\n<li>\"enq: IV contention\": If this wait event is generated by the background process MZ00 (or similar Mnnn which are MMON child processes) we have a similar situation as before. Please, open an incident on the component BC-DB-ORA indicating the background job (with the output of provided statement).</li>\r\n</ul>\r\n<ol start=\"19\">\r\n<li>What else should I check in relation to wait events?</li>\r\n</ol>\r\n<p style=\"padding-left: 30px;\">If you see a number of individual wait events that is inexplicably high, this may be due to a CPU bottleneck on the database server. It is therefore conceivable that an Oracle process that has a lock may be displaced by other processes from the CPU. As a result, the period of the lock stopping greatly increases and other processes serialize on this lock. You must therefore use transaction ST06/OS07 to check whether there are sufficient CPU resources. SAP recommends an idle proportion of at least 30% per hour.</p>\r\n<p style=\"padding-left: 30px;\">Significantly increased average values may occur frequently for individual wait events, due to measurement errors. To prevent the values displayed resulting from individual incorrect statistical values, you can carry out a reset in many R/3 screens. The values collected afterward are generally correct since it is unlikely that an incorrect statistical value will appear after a reset.</p>\r\n<ol start=\"20\">\r\n<li>Where can I find further information about wait events?</li>\r\n</ol>\r\n<p style=\"padding-left: 30px;\">The Oracle 12.1 online documentation contains information on wait events in the following books:</p>\r\n<ul>\r\n<li>Database Administration</li>\r\n<ul>\r\n<li><a target=\"_blank\" href=\"https://docs.oracle.com/database/121/TGDBA/pfgrf_instance_tune.htm#TGDBA95374\">Chapter 10</a> Instance Tuning Using Performance Views</li>\r\n<li><a target=\"_blank\" href=\"https://docs.oracle.com/database/121/REFRN/GUID-03BFEEFB-1020-4F3F-8CF8-A23E7026684B.htm#REFRN101\">Appendix C</a> Oracle Wait Events</li>\r\n</ul>\r\n<li>Performance Tuning Guide</li>\r\n</ul>\r\n<p style=\"padding-left: 30px;\">Chapter 10 Instance Tuning Using Performance Views</p>\r\n<p style=\"padding-left: 60px;\"><a target=\"_blank\" href=\"https://docs.oracle.com/database/121/TGDBA/pfgrf_instance_tune.htm#TGDBA02410\">Wait Events statistcs</a></p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p>FAQ only.</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "BW-SYS-DB-ORA (BW ORACLE)"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON> (D048845)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON> (D048845)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000619188/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000619188/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000619188/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000619188/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000619188/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000619188/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000619188/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000619188/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000619188/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "971261", "RefComponent": "BW-SYS-DB-ORA", "RefTitle": "Oracle 9.2 / 10.2: Hanging queries with STAR_TRANSFORMATION", "RefUrl": "/notes/971261"}, {"RefNumber": "964619", "RefComponent": "BC-DB-ORA", "RefTitle": "RMAN: Incremental backups with block change tracking", "RefUrl": "/notes/964619"}, {"RefNumber": "964344", "RefComponent": "BC-DB-ORA", "RefTitle": "FAQ: Oracle mutexes", "RefUrl": "/notes/964344"}, {"RefNumber": "963894", "RefComponent": "BC-DB-ORA", "RefTitle": "SMON blocks processes in rollback of large transactions", "RefUrl": "/notes/963894"}, {"RefNumber": "904188", "RefComponent": "BC-DB-ORA", "RefTitle": "Locks when you execute Index Rebuilds Online in parallel", "RefUrl": "/notes/904188"}, {"RefNumber": "894078", "RefComponent": "BC-DB-ORA", "RefTitle": "9.2.0.7: Long waits for KSU PROCESS ALLOC LATCH YIELD", "RefUrl": "/notes/894078"}, {"RefNumber": "875477", "RefComponent": "BC-DB-ORA", "RefTitle": "Avoiding long runtimes with BEGIN BACKUP", "RefUrl": "/notes/875477"}, {"RefNumber": "869006", "RefComponent": "BC-DB-ORA", "RefTitle": "Composite SAP note: ORA-04031", "RefUrl": "/notes/869006"}, {"RefNumber": "857998", "RefComponent": "BW-BEX-OT", "RefTitle": "Number range buffering for DIM IDs and SIDs", "RefUrl": "/notes/857998"}, {"RefNumber": "857973", "RefComponent": "BC-DB-ORA", "RefTitle": "Deleting clients efficiently using Oracle", "RefUrl": "/notes/857973"}, {"RefNumber": "853576", "RefComponent": "BC-DB-ORA", "RefTitle": "Oracle 10g: Performance analysis w/ ASH and Oracle Advisors", "RefUrl": "/notes/853576"}, {"RefNumber": "853084", "RefComponent": "BW-BEX-OT-DBIF", "RefTitle": "Transferring temporary /BI0/06 tables to another tablespace", "RefUrl": "/notes/853084"}, {"RefNumber": "846364", "RefComponent": "BW-SYS-DB-ORA", "RefTitle": "Parse of star query may spin", "RefUrl": "/notes/846364"}, {"RefNumber": "842240", "RefComponent": "BC-DB-ORA", "RefTitle": "FAQ: Backup strategy for large and highly available databases", "RefUrl": "/notes/842240"}, {"RefNumber": "840553", "RefComponent": "BW-SYS-DB-ORA", "RefTitle": "BW Oracle: TRUNCATE table slower than DELETE", "RefUrl": "/notes/840553"}, {"RefNumber": "830576", "RefComponent": "BC-DB-ORA", "RefTitle": "Parameter recommendations for Oracle 10g", "RefUrl": "/notes/830576"}, {"RefNumber": "825653", "RefComponent": "BC-DB-ORA", "RefTitle": "Oracle: Popular misconceptions", "RefUrl": "/notes/825653"}, {"RefNumber": "806554", "RefComponent": "BC-DB-ORA", "RefTitle": "FAQ: I/O-intensive database operations", "RefUrl": "/notes/806554"}, {"RefNumber": "805934", "RefComponent": "BC-DB-ORA", "RefTitle": "FAQ: Database time", "RefUrl": "/notes/805934"}, {"RefNumber": "79341", "RefComponent": "BC-DB-ORA", "RefTitle": "Checkpoint not complete", "RefUrl": "/notes/79341"}, {"RefNumber": "793113", "RefComponent": "BC-DB-ORA", "RefTitle": "FAQ: Oracle I/O configuration", "RefUrl": "/notes/793113"}, {"RefNumber": "789011", "RefComponent": "BC-DB-ORA", "RefTitle": "FAQ: Oracle memory areas", "RefUrl": "/notes/789011"}, {"RefNumber": "768308", "RefComponent": "BW-SYS-DB-ORA", "RefTitle": "Deadlock with row cache locks and library cache locks", "RefUrl": "/notes/768308"}, {"RefNumber": "767414", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/767414"}, {"RefNumber": "766827", "RefComponent": "SCM-APO-MD", "RefTitle": "Composite SAP note: Performance SCM 4.0", "RefUrl": "/notes/766827"}, {"RefNumber": "766349", "RefComponent": "BC-DB-ORA", "RefTitle": "FAQ: Oracle SQL optimization", "RefUrl": "/notes/766349"}, {"RefNumber": "758989", "RefComponent": "BC-DB-ORA", "RefTitle": "Poor performance with TRUNCATEs", "RefUrl": "/notes/758989"}, {"RefNumber": "745639", "RefComponent": "BC-DB-ORA", "RefTitle": "FAQ: Oracle enqueues", "RefUrl": "/notes/745639"}, {"RefNumber": "738641", "RefComponent": "BW-BEX-OT-AGGR", "RefTitle": "Change run/rollup stops with simultaneous COALESCE", "RefUrl": "/notes/738641"}, {"RefNumber": "72638", "RefComponent": "BC-DB-ORA", "RefTitle": "Performance problems with SQL*Net", "RefUrl": "/notes/72638"}, {"RefNumber": "712624", "RefComponent": "BC-DB-ORA", "RefTitle": "High CPU consumption by Oracle", "RefUrl": "/notes/712624"}, {"RefNumber": "659946", "RefComponent": "BC-DB-ORA", "RefTitle": "FAQ: Temporary tablespaces", "RefUrl": "/notes/659946"}, {"RefNumber": "651060", "RefComponent": "BC-DB-ORA", "RefTitle": "FAQ: Oracle Parallel Execution", "RefUrl": "/notes/651060"}, {"RefNumber": "649876", "RefComponent": "BC-DB-ORA", "RefTitle": "Library Cache Pin Contention after Upg. to ******* - *******", "RefUrl": "/notes/649876"}, {"RefNumber": "626172", "RefComponent": "BC-DB-ORA", "RefTitle": "Performance problems with Outer Join queries in Oracle 9.2", "RefUrl": "/notes/626172"}, {"RefNumber": "620803", "RefComponent": "BC-DB-ORA", "RefTitle": "Oracle 9i: Automatic Segment Space Management", "RefUrl": "/notes/620803"}, {"RefNumber": "618868", "RefComponent": "BC-DB-ORA", "RefTitle": "FAQ: Oracle performance", "RefUrl": "/notes/618868"}, {"RefNumber": "613872", "RefComponent": "BC-DB-ORA", "RefTitle": "Oracle traces with ORADEBUG", "RefUrl": "/notes/613872"}, {"RefNumber": "610357", "RefComponent": "BC-DB-ORA", "RefTitle": "Oracle hang with event DB_FILE_SCATTERED_READ", "RefUrl": "/notes/610357"}, {"RefNumber": "607415", "RefComponent": "BC-CCM-MON-TUN", "RefTitle": "RSORAVSE displays poor averages", "RefUrl": "/notes/607415"}, {"RefNumber": "596748", "RefComponent": "BC-ABA-LA", "RefTitle": "Simultaneous generation of selection screens in background", "RefUrl": "/notes/596748"}, {"RefNumber": "596420", "RefComponent": "BC-DB-ORA", "RefTitle": "System standstill during deadlock (ORA-60)", "RefUrl": "/notes/596420"}, {"RefNumber": "582427", "RefComponent": "BC-DB-ORA", "RefTitle": "Error due to inconsistent Oracle DDIC", "RefUrl": "/notes/582427"}, {"RefNumber": "564446", "RefComponent": "SV-SMG-SDD", "RefTitle": "Scheduling of background job BTCH_RSORAVSH", "RefUrl": "/notes/564446"}, {"RefNumber": "563359", "RefComponent": "BC-DB-ORA", "RefTitle": "Performance optimization for tables with LOB columns", "RefUrl": "/notes/563359"}, {"RefNumber": "562403", "RefComponent": "BC-DB-ORA", "RefTitle": "FAQ: Oracle Net", "RefUrl": "/notes/562403"}, {"RefNumber": "556764", "RefComponent": "BC-UPG", "RefTitle": "Upgrade hangs in phase ACT_<REL>", "RefUrl": "/notes/556764"}, {"RefNumber": "549298", "RefComponent": "SV-SMG", "RefTitle": "RSORAVSH: LOAD_PROGRAM_NOT FOUND during the run of RSCOLL00", "RefUrl": "/notes/549298"}, {"RefNumber": "541538", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "FAQ: Reorganization", "RefUrl": "/notes/541538"}, {"RefNumber": "521230", "RefComponent": "BC-DB-ORA", "RefTitle": "FAQ: Client software 9i or lower on UNIX", "RefUrl": "/notes/521230"}, {"RefNumber": "500235", "RefComponent": "BC-NET", "RefTitle": "Network Diagnosis with NIPING", "RefUrl": "/notes/500235"}, {"RefNumber": "488583", "RefComponent": "BC-DB-ORA", "RefTitle": "Database hangs: Waits for cache buffer chain latch", "RefUrl": "/notes/488583"}, {"RefNumber": "449136", "RefComponent": "BC-DB-ORA", "RefTitle": "\"cache buffers chains\" latch contention on 8.1", "RefUrl": "/notes/449136"}, {"RefNumber": "400698", "RefComponent": "BC-DB-ORA", "RefTitle": "ORA-4031 Memory leak on Oracle 8.1.7", "RefUrl": "/notes/400698"}, {"RefNumber": "391", "RefComponent": "BC-DB-ORA", "RefTitle": "Archiver stuck", "RefUrl": "/notes/391"}, {"RefNumber": "376905", "RefComponent": "BC-DB-ORA", "RefTitle": "Long parsing times and Library Cache Locks", "RefUrl": "/notes/376905"}, {"RefNumber": "359835", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/359835"}, {"RefNumber": "328822", "RefComponent": "BC-DB-ORA", "RefTitle": "AIX kernel extension for Oracle", "RefUrl": "/notes/328822"}, {"RefNumber": "20071", "RefComponent": "BC-DB-ORA", "RefTitle": "Permanent lock after connection termination", "RefUrl": "/notes/20071"}, {"RefNumber": "198752", "RefComponent": "BC-DB-ORA", "RefTitle": "TCP delay problem under Oracle 8.1.x", "RefUrl": "/notes/198752"}, {"RefNumber": "180430", "RefComponent": "BC-DB-ORA-SYS", "RefTitle": "Installing the ORACLE client software for UNIX", "RefUrl": "/notes/180430"}, {"RefNumber": "164925", "RefComponent": "BC-DB-ORA", "RefTitle": "Storage parameter of tablespace PSAPTEMP", "RefUrl": "/notes/164925"}, {"RefNumber": "1611285", "RefComponent": "SCM-FRE-DIF", "RefTitle": "FRP sequence 1: Performance improvement for DIF access", "RefUrl": "/notes/1611285"}, {"RefNumber": "1609612", "RefComponent": "BC-DB-ORA-RAC", "RefTitle": "FAQ: Oracle Real Application Clusters - Performance", "RefUrl": "/notes/1609612"}, {"RefNumber": "1487556", "RefComponent": "BC-DB-ORA-RAC", "RefTitle": "Oracle RAC: Global Cache Service & Wait Events", "RefUrl": "/notes/1487556"}, {"RefNumber": "1438410", "RefComponent": "BC-DB-ORA", "RefTitle": "SQL script collection for Oracle", "RefUrl": "/notes/1438410"}, {"RefNumber": "1176846", "RefComponent": "BC-CCM-MON-ORA", "RefTitle": "DBA Cockpit: Adjusting the Oracle idle events", "RefUrl": "/notes/1176846"}, {"RefNumber": "1155529", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1155529"}, {"RefNumber": "1121838", "RefComponent": "BW-SYS-DB-ORA", "RefTitle": "SELECT on partition hangs if a shrink compact is running", "RefUrl": "/notes/1121838"}, {"RefNumber": "1044758", "RefComponent": "BC-DB-ORA", "RefTitle": "EM repository operations with EMCA and Oracle quiesce", "RefUrl": "/notes/1044758"}, {"RefNumber": "1017970", "RefComponent": "BC-DB-ORA", "RefTitle": "DBMS_ADVISOR may result in deadlock", "RefUrl": "/notes/1017970"}, {"RefNumber": "1013049", "RefComponent": "BC-DB-ORA", "RefTitle": "FAQ: Oracle Data Pump", "RefUrl": "/notes/1013049"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2547373", "RefComponent": "BC-DB-ORA", "RefTitle": "Too much waits on COMMITs - what to do?", "RefUrl": "/notes/2547373 "}, {"RefNumber": "2462701", "RefComponent": "BC-DB-ORA", "RefTitle": "Job /SDF/UPL_PERIODIC_EXT_JOB fails with ORA-00054", "RefUrl": "/notes/2462701 "}, {"RefNumber": "1438410", "RefComponent": "BC-DB-ORA", "RefTitle": "SQL script collection for Oracle", "RefUrl": "/notes/1438410 "}, {"RefNumber": "618868", "RefComponent": "BC-DB-ORA", "RefTitle": "FAQ: Oracle performance", "RefUrl": "/notes/618868 "}, {"RefNumber": "745639", "RefComponent": "BC-DB-ORA", "RefTitle": "FAQ: Oracle enqueues", "RefUrl": "/notes/745639 "}, {"RefNumber": "830576", "RefComponent": "BC-DB-ORA", "RefTitle": "Parameter recommendations for Oracle 10g", "RefUrl": "/notes/830576 "}, {"RefNumber": "563359", "RefComponent": "BC-DB-ORA", "RefTitle": "Performance optimization for tables with LOB columns", "RefUrl": "/notes/563359 "}, {"RefNumber": "766827", "RefComponent": "SCM-APO-MD", "RefTitle": "Composite SAP note: Performance SCM 4.0", "RefUrl": "/notes/766827 "}, {"RefNumber": "596748", "RefComponent": "BC-ABA-LA", "RefTitle": "Simultaneous generation of selection screens in background", "RefUrl": "/notes/596748 "}, {"RefNumber": "789011", "RefComponent": "BC-DB-ORA", "RefTitle": "FAQ: Oracle memory areas", "RefUrl": "/notes/789011 "}, {"RefNumber": "793113", "RefComponent": "BC-DB-ORA", "RefTitle": "FAQ: Oracle I/O configuration", "RefUrl": "/notes/793113 "}, {"RefNumber": "840553", "RefComponent": "BW-SYS-DB-ORA", "RefTitle": "BW Oracle: TRUNCATE table slower than DELETE", "RefUrl": "/notes/840553 "}, {"RefNumber": "500235", "RefComponent": "BC-NET", "RefTitle": "Network Diagnosis with NIPING", "RefUrl": "/notes/500235 "}, {"RefNumber": "712624", "RefComponent": "BC-DB-ORA", "RefTitle": "High CPU consumption by Oracle", "RefUrl": "/notes/712624 "}, {"RefNumber": "766349", "RefComponent": "BC-DB-ORA", "RefTitle": "FAQ: Oracle SQL optimization", "RefUrl": "/notes/766349 "}, {"RefNumber": "1487556", "RefComponent": "BC-DB-ORA-RAC", "RefTitle": "Oracle RAC: Global Cache Service & Wait Events", "RefUrl": "/notes/1487556 "}, {"RefNumber": "869006", "RefComponent": "BC-DB-ORA", "RefTitle": "Composite SAP note: ORA-04031", "RefUrl": "/notes/869006 "}, {"RefNumber": "1611285", "RefComponent": "SCM-FRE-DIF", "RefTitle": "FRP sequence 1: Performance improvement for DIF access", "RefUrl": "/notes/1611285 "}, {"RefNumber": "659946", "RefComponent": "BC-DB-ORA", "RefTitle": "FAQ: Temporary tablespaces", "RefUrl": "/notes/659946 "}, {"RefNumber": "857998", "RefComponent": "BW-BEX-OT", "RefTitle": "Number range buffering for DIM IDs and SIDs", "RefUrl": "/notes/857998 "}, {"RefNumber": "541538", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "FAQ: Reorganization", "RefUrl": "/notes/541538 "}, {"RefNumber": "767414", "RefComponent": "BC-DB-ORA", "RefTitle": "FAQ: Oracle latches", "RefUrl": "/notes/767414 "}, {"RefNumber": "805934", "RefComponent": "BC-DB-ORA", "RefTitle": "FAQ: Database time", "RefUrl": "/notes/805934 "}, {"RefNumber": "806554", "RefComponent": "BC-DB-ORA", "RefTitle": "FAQ: I/O-intensive database operations", "RefUrl": "/notes/806554 "}, {"RefNumber": "391", "RefComponent": "BC-DB-ORA", "RefTitle": "Archiver stuck", "RefUrl": "/notes/391 "}, {"RefNumber": "825653", "RefComponent": "BC-DB-ORA", "RefTitle": "Oracle: Popular misconceptions", "RefUrl": "/notes/825653 "}, {"RefNumber": "964619", "RefComponent": "BC-DB-ORA", "RefTitle": "RMAN: Incremental backups with block change tracking", "RefUrl": "/notes/964619 "}, {"RefNumber": "964344", "RefComponent": "BC-DB-ORA", "RefTitle": "FAQ: Oracle mutexes", "RefUrl": "/notes/964344 "}, {"RefNumber": "562403", "RefComponent": "BC-DB-ORA", "RefTitle": "FAQ: Oracle Net", "RefUrl": "/notes/562403 "}, {"RefNumber": "613872", "RefComponent": "BC-DB-ORA", "RefTitle": "Oracle traces with ORADEBUG", "RefUrl": "/notes/613872 "}, {"RefNumber": "79341", "RefComponent": "BC-DB-ORA", "RefTitle": "Checkpoint not complete", "RefUrl": "/notes/79341 "}, {"RefNumber": "1155529", "RefComponent": "FI-SL-IS-A", "RefTitle": "Performance Issues - Common customer issues.", "RefUrl": "/notes/1155529 "}, {"RefNumber": "853576", "RefComponent": "BC-DB-ORA", "RefTitle": "Oracle 10g: Performance analysis w/ ASH and Oracle Advisors", "RefUrl": "/notes/853576 "}, {"RefNumber": "20071", "RefComponent": "BC-DB-ORA", "RefTitle": "Permanent lock after connection termination", "RefUrl": "/notes/20071 "}, {"RefNumber": "1044758", "RefComponent": "BC-DB-ORA", "RefTitle": "EM repository operations with EMCA and Oracle quiesce", "RefUrl": "/notes/1044758 "}, {"RefNumber": "651060", "RefComponent": "BC-DB-ORA", "RefTitle": "FAQ: Oracle Parallel Execution", "RefUrl": "/notes/651060 "}, {"RefNumber": "853084", "RefComponent": "BW-BEX-OT-DBIF", "RefTitle": "Transferring temporary /BI0/06 tables to another tablespace", "RefUrl": "/notes/853084 "}, {"RefNumber": "1176846", "RefComponent": "BC-CCM-MON-ORA", "RefTitle": "DBA Cockpit: Adjusting the Oracle idle events", "RefUrl": "/notes/1176846 "}, {"RefNumber": "842240", "RefComponent": "BC-DB-ORA", "RefTitle": "FAQ: Backup strategy for large and highly available databases", "RefUrl": "/notes/842240 "}, {"RefNumber": "549298", "RefComponent": "SV-SMG", "RefTitle": "RSORAVSH: LOAD_PROGRAM_NOT FOUND during the run of RSCOLL00", "RefUrl": "/notes/549298 "}, {"RefNumber": "72638", "RefComponent": "BC-DB-ORA", "RefTitle": "Performance problems with SQL*Net", "RefUrl": "/notes/72638 "}, {"RefNumber": "1013049", "RefComponent": "BC-DB-ORA", "RefTitle": "FAQ: Oracle Data Pump", "RefUrl": "/notes/1013049 "}, {"RefNumber": "1121838", "RefComponent": "BW-SYS-DB-ORA", "RefTitle": "SELECT on partition hangs if a shrink compact is running", "RefUrl": "/notes/1121838 "}, {"RefNumber": "521230", "RefComponent": "BC-DB-ORA", "RefTitle": "FAQ: Client software 9i or lower on UNIX", "RefUrl": "/notes/521230 "}, {"RefNumber": "620803", "RefComponent": "BC-DB-ORA", "RefTitle": "Oracle 9i: Automatic Segment Space Management", "RefUrl": "/notes/620803 "}, {"RefNumber": "971261", "RefComponent": "BW-SYS-DB-ORA", "RefTitle": "Oracle 9.2 / 10.2: Hanging queries with STAR_TRANSFORMATION", "RefUrl": "/notes/971261 "}, {"RefNumber": "1017970", "RefComponent": "BC-DB-ORA", "RefTitle": "DBMS_ADVISOR may result in deadlock", "RefUrl": "/notes/1017970 "}, {"RefNumber": "875477", "RefComponent": "BC-DB-ORA", "RefTitle": "Avoiding long runtimes with BEGIN BACKUP", "RefUrl": "/notes/875477 "}, {"RefNumber": "582427", "RefComponent": "BC-DB-ORA", "RefTitle": "Error due to inconsistent Oracle DDIC", "RefUrl": "/notes/582427 "}, {"RefNumber": "564446", "RefComponent": "SV-SMG-SDD", "RefTitle": "Scheduling of background job BTCH_RSORAVSH", "RefUrl": "/notes/564446 "}, {"RefNumber": "894078", "RefComponent": "BC-DB-ORA", "RefTitle": "9.2.0.7: Long waits for KSU PROCESS ALLOC LATCH YIELD", "RefUrl": "/notes/894078 "}, {"RefNumber": "963894", "RefComponent": "BC-DB-ORA", "RefTitle": "SMON blocks processes in rollback of large transactions", "RefUrl": "/notes/963894 "}, {"RefNumber": "164925", "RefComponent": "BC-DB-ORA", "RefTitle": "Storage parameter of tablespace PSAPTEMP", "RefUrl": "/notes/164925 "}, {"RefNumber": "768308", "RefComponent": "BW-SYS-DB-ORA", "RefTitle": "Deadlock with row cache locks and library cache locks", "RefUrl": "/notes/768308 "}, {"RefNumber": "556764", "RefComponent": "BC-UPG", "RefTitle": "Upgrade hangs in phase ACT_<REL>", "RefUrl": "/notes/556764 "}, {"RefNumber": "904188", "RefComponent": "BC-DB-ORA", "RefTitle": "Locks when you execute Index Rebuilds Online in parallel", "RefUrl": "/notes/904188 "}, {"RefNumber": "758989", "RefComponent": "BC-DB-ORA", "RefTitle": "Poor performance with TRUNCATEs", "RefUrl": "/notes/758989 "}, {"RefNumber": "857973", "RefComponent": "BC-DB-ORA", "RefTitle": "Deleting clients efficiently using Oracle", "RefUrl": "/notes/857973 "}, {"RefNumber": "846364", "RefComponent": "BW-SYS-DB-ORA", "RefTitle": "Parse of star query may spin", "RefUrl": "/notes/846364 "}, {"RefNumber": "738641", "RefComponent": "BW-BEX-OT-AGGR", "RefTitle": "Change run/rollup stops with simultaneous COALESCE", "RefUrl": "/notes/738641 "}, {"RefNumber": "649876", "RefComponent": "BC-DB-ORA", "RefTitle": "Library Cache Pin Contention after Upg. to ******* - *******", "RefUrl": "/notes/649876 "}, {"RefNumber": "607415", "RefComponent": "BC-CCM-MON-TUN", "RefTitle": "RSORAVSE displays poor averages", "RefUrl": "/notes/607415 "}, {"RefNumber": "626172", "RefComponent": "BC-DB-ORA", "RefTitle": "Performance problems with Outer Join queries in Oracle 9.2", "RefUrl": "/notes/626172 "}, {"RefNumber": "198752", "RefComponent": "BC-DB-ORA", "RefTitle": "TCP delay problem under Oracle 8.1.x", "RefUrl": "/notes/198752 "}, {"RefNumber": "610357", "RefComponent": "BC-DB-ORA", "RefTitle": "Oracle hang with event DB_FILE_SCATTERED_READ", "RefUrl": "/notes/610357 "}, {"RefNumber": "488583", "RefComponent": "BC-DB-ORA", "RefTitle": "Database hangs: Waits for cache buffer chain latch", "RefUrl": "/notes/488583 "}, {"RefNumber": "400698", "RefComponent": "BC-DB-ORA", "RefTitle": "ORA-4031 Memory leak on Oracle 8.1.7", "RefUrl": "/notes/400698 "}, {"RefNumber": "449136", "RefComponent": "BC-DB-ORA", "RefTitle": "\"cache buffers chains\" latch contention on 8.1", "RefUrl": "/notes/449136 "}, {"RefNumber": "376905", "RefComponent": "BC-DB-ORA", "RefTitle": "Long parsing times and Library Cache Locks", "RefUrl": "/notes/376905 "}, {"RefNumber": "328822", "RefComponent": "BC-DB-ORA", "RefTitle": "AIX kernel extension for Oracle", "RefUrl": "/notes/328822 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": []}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}