{"Request": {"Number": "620803", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 376, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000015453322017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000620803?language=E&token=5AC66CCB92033FDD83C522B1A2279317"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000620803", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000620803/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "620803"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 24}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Performance"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "12.07.2007"}, "SAPComponentKey": {"_label": "Component", "value": "BC-DB-ORA"}, "SAPComponentKeyText": {"_label": "Component", "value": "Oracle"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Basis Components", "value": "BC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Database Interface, Database Platforms", "value": "BC-DB", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-DB*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Oracle", "value": "BC-DB-ORA", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-DB-ORA*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "620803 - Oracle 9i: Automatic Segment Space Management"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p><br /><br /></p> <b>Automatic Segment Space Management (ASSM)</b><br /> <p>This note describes the Oracle function 'Automatic Segment Space Management (ASSM).<br /><br />The new ASSM function is available as of Oracle Release 9i. As of Oracle 10g, ASSM is already contained in the standard system, and new tablespaces are by default created as ASSM tablespaces.<br /><br />This note applies to Oracle Releases 9i and 10g.<br /></p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p><br /><br /><br />PCTUSED, FREELISTS, FREELIST GROUPS<br />Contention, concurrent access<br /></p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p><br /><br /><br />Documentation/migration note for ASSM (9i, 10g)<br /></p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p><br /><br /></p> <b>Description of ASSM<br /></b><br /> <p>Automatic Segment Space Management (ASSM) is available as a new function as of Release 9i. It improves and automizes the space management within segments. For database administration, ASSM simplifies space management for segments.<br /><br />This note describes the benfits of ASSM and the prerequisites for using it. Furthermore, this note desribes migration methods and provides recommendations for migration.<br /><br />When you use CREATE TABLESPACE to create a locally managed tablespace (LMTS), you can use the<B> SEGMENT SPACE MANAGEMENT {MANUAL|AUTO}</B> clause to specify how the free and occupied space in a segment is to be managed.<br /><br />For example:<br />CREATE TABLESPACE TSP_ASSM<br />DATAFILE '/oracle/SID/sapdata1/test/test.data1' SIZE 100M<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;EXTENT MANAGEMENT LOCAL<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;SEGMENT SPACE MANAGEMENT AUTO;<br /><br /><B>MANUAL</B><br />Up to Oracle 9i, MANUAL was the only method that Oracle provided for segment space management. This method uses free storage list (freelists). If you use this method, it is possible (and sometimes required) to explicitly set and tune the PCTUSED, FREELISTS, and FREELIST_GROUPS storage parameters.<br />MANUAL is the default method in Oracle Release 9i (9.2).<br /><br /><B>AUTO</B><br />ASSM is a simpler and more efficient method than the previous manual method for managing the freespace in segments. Oracle uses bitmaps as storage structures for managing freespace in ASSM tablespaces. ASSM-managed segments are sometimes referred to as bitmap-managed segments. It is no longer required to tune the PCTUSED/FREELISTS/FREELISTGROUPS storage parameters manually. If these parameters are specified during creation despite this, they are ignored.<br />AUTO is the default method in Oracle Release 10g (10.2).<br /><br />Advantages of ASSM are:</p> <UL><LI>Easier management<br />It is no longer necessary to tune the PCTUSED/FREELISTS/FREELISTGROUPS storage parameters at segment level during database administration.<br />You no longer have to optimize segment storage parameters.</LI></UL> <UL><LI>Storage management with ASSM is more efficient than manual tuning.<br />Better use of space, improved reuse of free space<br />Maximum level information on block level at exactly 25% (more precise than the manual method); this provides better decision options with regard to the selection of suitable blocks during INSERT operations</LI></UL> <UL><LI>ASSM is scalable both with the number of users and the number of RAC instances<br />The segment storage parameters do not have to be reconfigured when changes are made to the RAC configuration.</LI></UL> <UL><LI>Online Segment Shrinking<br />As of Oracle 10g, segments managed by ASSM can be minimized online.&#x00A0;&#x00A0;For more information, see Note 910389.<br /></LI></UL> <p>Relevant advantages for Real Application Cluster (RAC):</p> <UL><LI>You do not have to retune FREELIST parameters if new instances are added; you no longer have to reorganize segments.<br />--&gt; automatic scaling with the number of instances</LI></UL> <UL><LI>The reduction of contention on the segment header (parallel DMLs from several instances), this results in improved performance with ASSM compared with manual free space management.<br /></LI></UL> <p>In most cases, the overall performance of the application is better with ASSM than with systems that use a well configured manual segment space management.<br /><br />In other words: Up to now (up to and including Oracle 8i), the DBA used the FREELISTS, FREELIST GROUPS and PCTUSED parameters to influence the free space management within a segment and also optimized it (although this was relatively time-consuming). ASSM dispenses with this time-consuming work entirely because the precise filling level of a data block in a 25% grid value (0-25%, 25-50%, 50-75%, 75-100%) is known at any time as a result of the administrative structure within the segment. This improved information also enables you to make better use of space and better reuse of new free space. In addition, simultaneous DML operations on a table are processed faster by ASSM because simultaneous access to the segment header without contention is made possible by the bitmap structure. This advantage is particularly apparent in the case of RAC installations if several instances simultaneously execute parallel DML operations (insert, update, delete) on a table. In the case of RAC installations with ASSM, you can dispense with reorganizing tables (downtime) in order to increase the FREELISTS and FREELIST GROUPS parameters if new Oracle instances are added.<br /><br /></p> <b>Restrictions with regard to ASSM</b><br /> <p><br />There are the following restrictions with regard to ASSM:</p> <UL><LI>You cannot create the SYSTEM tablespace as an ASSM tablespace. This applies to Release 9i and to Release 10g.</LI></UL> <UL><LI>You cannot create dictionary-managed tablespaces (DMTS) as ASSM tablespaces.</LI></UL> <p></p> <b>Support of ASSM by BR*Tools<br /></b><br /> <p>The SAP BR*Tools support ASSM as of SAP Release 6.20 (for example, during the creation of tablespaces with ASSM).<br /><br />As of Release 6.40, BR*Tools support the online reorganization of tables. This function simplifies the migration of tables to new ASSM tablespaces and is the recommended method for migrating to ASSM (see recommendations). For more information about using BR*Tools for online reorganization, see Notes 646681 and 647697.<br /></p> <b>General Recommendation<br /></b><br /> <p>ASSM has been established as the standard in the SAP environment: For SAP installations as of SAP WebAS Release 6.40, ASSM is the default setting when creating tablespaces.<br /><br />Since ASSM uses memory efficiently, it is recommended as of Oracle 9.2. Existing SAP installations can also use ASSM. The procedure for the changeover to ASSM tablespaces is described here.<br /><br />In RAC systems, ASSM improves performance due to the reduced contention on the segment header.<br /></p> <b>Prerequisites for ASSM<br /></b><br /> <UL><LI>Oracle Release 9i (higher than 9.2.0.5) or</LI></UL> <UL><LI>Oracle Release 10g (10.2.0)</LI></UL> <p><br />For Oracle Release 9i, note:</p> <UL><LI>ASSM can be used as of Oracle Release 9.2.0.5. Also refer to Note 764015 (\"Segment corruption in ASSM tablespace2) and import the patch.</LI></UL> <UL><LI>Oracle bug 3120807, described in Note 708721, is solved as of Oracle 9.2.0.5.</LI></UL> <UL><LI>For 9.2.0.6/9.2.0.7, see Note 912918.</LI></UL> <p><br /></p> <b>Properties of ASSM<br /></b><br /> <UL><LI>ASSM is a tablespace attribute that must be defined when a tablespace is created. It cannot be changed.</LI></UL> <UL><LI>Segments only receive the ASSM attribute if they are created in an ASSM tablespace.</LI></UL> <UL><LI>At the same time, an ASSM tablespace is also managed locally (LMTS).<br /></LI></UL> <b>Planning of the migration to ASSM<br /></b><br /> <p>You use dictionary-managed tablespaces (DMTS) or locally managed tablespaces (LMTS) in your system. To be able to use ASSM, first create new tablespaces with ASSM. Afterwards, move the objects from the old DMTS or LMTS to the ASSM tablespace that you created. If you want to execute the migration online, you can use the BRSPACE function for reorganizing tables (see BRSPACE in Note 6464681).<br /><br />Also note the recommendations for the migration in Note 214995.<br /><br />Further positive side effects of the changeover to ASSM tablespaces:</p> <UL><LI>Indexes are rebuilt.</LI></UL> <UL><LI>You can change over step by step to the new tablespace concept at the same time.</LI></UL> <p><br /></p> <b>ASSM - Recommendations for RAC<br /></b><br /> <p>Oracle recommends that you use ASSM for Oracle RAC in particular because when you use ASSM, each RAC instance prefers its own block for inserts (affinity of blocks to instances). Without ASSM wait situations (buffer busy waits) may occur on the segment header.<br />See also the note for RAC: 621293.<br /></p> <b>Migration of DMTS to ASSM or from LMTS to ASSM<br /></b><br /> <p>Note 646681 describes the steps for the migration from dictionary-managed to locally-managed tablespaces using BRSPACE. In principle, the procedure to migrate dictionary-managed tablespaces according to ASSM occurs in the same way.<br /><br />In the course of migrating to ASSM, you can also (if required) change from the previous tablespace layout (SAP data is distributed onto several data tablespaces: PSAPSTABD, PSAPSOURCED, PSAPSOURCEI, and so on) to the new tablespace layout (basically, this is still only a tablespace for all data and indexes (see Note 355771).<br /><br />The entire migration process to ASSM with BRSPACE consists of the following steps:<br /><br /><STRONG>Check the prerequisites<br /></STRONG><br />The temporary tablespace of the SYS user should not be the SYSTEM tablespace, since problems may occur during the reorganization (also see Note 646681). It is recommended to define a default temporary tablespace (see Note 683075).<br /><STRONG>1. Step: Perform a complete database backup<br /></STRONG><br /><STRONG>2. Step: Create target tablespace(s) with an ASSM attribute<br /></STRONG><br /><STRONG>3. Step: Reorganization Part 1: Online<br /></STRONG><br />&#x00A0;&#x00A0;- All tables except for those with LONG/LONG RAW<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0; can be reorganized online. This restriction applies to<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;Oracle 9i. As of 10g, also tables with LONG data types<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0; can be reorganized online (see Note 828268).<br />&#x00A0;&#x00A0;- This step can take some time,<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0; depending on the size of the database and the underlying hardware.<br /><br /><STRONG>4. Step: Perform a complete database backup<br /></STRONG><br />&#x00A0;&#x00A0; - This is an optional, but recommended, step.<br /><br /><STRONG>5. Step: Reorganization Part 2: Offline (SAP System stopped)<br /></STRONG><br />&#x00A0;&#x00A0;This step consists of several individual steps<br />&#x00A0;&#x00A0; that are described in Note 646681. The SAP system is unavailable during this time.<br /><br />&#x00A0;&#x00A0;In this step, all tables with LONG/LONG RAW columns<br />&#x00A0;&#x00A0; are exported from a non-ASSM tablespace and are then<br />&#x00A0;&#x00A0;imported in an ASSM tablespace.<br />&#x00A0;&#x00A0; As of Oracle Release 10g, this step is also executed online.<br /><br /><br /><STRONG>6. Step: Closing operations (Cleanup)<br /></STRONG><br />&#x00A0;&#x00A0; - Drop empty tablespaces<br />&#x00A0;&#x00A0; - Update statistics (brconnect -f stats)<br />&#x00A0;&#x00A0; - Check the database (brconnect -f check)<br />&#x00A0;&#x00A0; - Perform a complete database backup<br /><br />Characteristics of this procedure:</p> <UL><LI>The majority of tables can be migrated to ASSM during normal operation (online).</LI></UL> <UL><LI>As of Oracle 10g, the migration to ASSM can entirely be executed online.</LI></UL> <UL><LI>The old (non-ASSM) tablespaces and the new (ASSM) tablespaces exist simultaneously for the duration of the online reorganization.</LI></UL> <UL><LI>You can parallelize the reorganization with BRSPACE. The more simultaneous online reorganizations that are active, the fewer resources remain for the online operation.</LI></UL> <UL><LI>Tables with LONG/LONG RAW columns can only be reorganized offline in Oracle Release 9.2. As of Oracle Database Release 10g, this is also possible online.<br /></LI></UL> <b>Usage of ASSM for Oracle RAC systems<br /></b><br /> <p>To take immediate advantage of ASSM when you convert an Oracle single instance system to Real Application Clusters (RAC) while simultaneously minimizing the initial reorganization costs, you can proceed selectively and move to ASSM tablespaces only those tables/indexes that are most frequently modified by several instances in parallel:<br />1. Identify the tables/indexes with DML from several instances<br />2. Create one or more new ASSM tablespaces<br />3. Reorganize the objects identified in step 1 in the newly<br />&#x00A0;&#x00A0; created tablespaces (online or offline)<br /></p> <b>ASSM - Administration and Monitoring<br /> <br /></b><br /> <p>Which tablespaces are ASSM tablespaces?<br />sqlplus:<br />SQL&gt;select tablespace_name, extent_management, segment_space_management<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;from dba_tablespaces<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;where segment_space_management = 'AUTO';<br /><br />Which tables are ASSM-managed?<br />A segment is ASSM-managed if it is in an ASSM tablespace. However, you can also recognize ASSM segments by the fact that the 'FREELISTS' and 'FREELIST_GROUPS' columns of the dba_tables Oracle dictionary table contain NULL values.<br />sqlplus:<br />SQL&gt;select table_name, owner,<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;freelists, freelist_groups,<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;pct_free, pct_used,<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;initial_extent, min_extents, max_extents<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;from dba_tables<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;where freelist_groups is null or freelists is null;<br /><br />Syntax for creating an ASSM tablespace manually<br />sqlplus:<br />SQL&gt;CREATE TABLESPACE \"LMTS_ASSM\"<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;LOGGING<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;DATAFILE '&lt;path&gt;' SIZE &lt;n&gt;M<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;EXTENT MANAGEMENT LOCAL<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;SEGMENT SPACE MANAGEMENT AUTO;<br /><br /></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "BC-DB-ORA-DBA (Database Administration)"}, {"Key": "Database System", "Value": "ORACLE"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON> (C5000979)"}, {"Key": "Processor                                                                                           ", "Value": "D000674"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000620803/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000620803/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000620803/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000620803/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000620803/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000620803/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000620803/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000620803/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000620803/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "912918", "RefComponent": "BC-DB-ORA", "RefTitle": "Massive UNDO/REDO generation when you use ASSM", "RefUrl": "/notes/912918"}, {"RefNumber": "830965", "RefComponent": "BC-DB-ORA", "RefTitle": "VBDATA, DDLOG: size and increase in size", "RefUrl": "/notes/830965"}, {"RefNumber": "821687", "RefComponent": "BC-DB-ORA", "RefTitle": "FAQ: Space utilization and fragmentation in Oracle", "RefUrl": "/notes/821687"}, {"RefNumber": "775810", "RefComponent": "BC-DB-ORA-SYS", "RefTitle": "Use of ASSM in R3 Releases 31I to 610", "RefUrl": "/notes/775810"}, {"RefNumber": "764015", "RefComponent": "BC-DB-ORA", "RefTitle": "Segment corruption in ASSM tablespace", "RefUrl": "/notes/764015"}, {"RefNumber": "708721", "RefComponent": "BC-DB-ORA", "RefTitle": "Table enlargement possible with ASSM", "RefUrl": "/notes/708721"}, {"RefNumber": "647697", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "BRSPACE - New tool for Oracle database administration", "RefUrl": "/notes/647697"}, {"RefNumber": "646681", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Reorganization of tables with BRSPACE", "RefUrl": "/notes/646681"}, {"RefNumber": "619188", "RefComponent": "BC-DB-ORA", "RefTitle": "FAQ: Oracle wait events", "RefUrl": "/notes/619188"}, {"RefNumber": "598678", "RefComponent": "BC-DB-ORA", "RefTitle": "Oracle 9i: New functions", "RefUrl": "/notes/598678"}, {"RefNumber": "572060", "RefComponent": "BC-DB-ORA", "RefTitle": "Options in the Oracle database during archiving", "RefUrl": "/notes/572060"}, {"RefNumber": "541538", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "FAQ: Reorganization", "RefUrl": "/notes/541538"}, {"RefNumber": "214995", "RefComponent": "BC-DB-ORA", "RefTitle": "Oracle locally managed tablespaces in the SAP environment", "RefUrl": "/notes/214995"}, {"RefNumber": "1037755", "RefComponent": "BC-DB-ORA", "RefTitle": "Performance problems with ASSM tablespaces", "RefUrl": "/notes/1037755"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "646681", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Reorganization of tables with BRSPACE", "RefUrl": "/notes/646681 "}, {"RefNumber": "619188", "RefComponent": "BC-DB-ORA", "RefTitle": "FAQ: Oracle wait events", "RefUrl": "/notes/619188 "}, {"RefNumber": "821687", "RefComponent": "BC-DB-ORA", "RefTitle": "FAQ: Space utilization and fragmentation in Oracle", "RefUrl": "/notes/821687 "}, {"RefNumber": "541538", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "FAQ: Reorganization", "RefUrl": "/notes/541538 "}, {"RefNumber": "214995", "RefComponent": "BC-DB-ORA", "RefTitle": "Oracle locally managed tablespaces in the SAP environment", "RefUrl": "/notes/214995 "}, {"RefNumber": "775810", "RefComponent": "BC-DB-ORA-SYS", "RefTitle": "Use of ASSM in R3 Releases 31I to 610", "RefUrl": "/notes/775810 "}, {"RefNumber": "647697", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "BRSPACE - New tool for Oracle database administration", "RefUrl": "/notes/647697 "}, {"RefNumber": "1037755", "RefComponent": "BC-DB-ORA", "RefTitle": "Performance problems with ASSM tablespaces", "RefUrl": "/notes/1037755 "}, {"RefNumber": "830965", "RefComponent": "BC-DB-ORA", "RefTitle": "VBDATA, DDLOG: size and increase in size", "RefUrl": "/notes/830965 "}, {"RefNumber": "598678", "RefComponent": "BC-DB-ORA", "RefTitle": "Oracle 9i: New functions", "RefUrl": "/notes/598678 "}, {"RefNumber": "912918", "RefComponent": "BC-DB-ORA", "RefTitle": "Massive UNDO/REDO generation when you use ASSM", "RefUrl": "/notes/912918 "}, {"RefNumber": "764015", "RefComponent": "BC-DB-ORA", "RefTitle": "Segment corruption in ASSM tablespace", "RefUrl": "/notes/764015 "}, {"RefNumber": "572060", "RefComponent": "BC-DB-ORA", "RefTitle": "Options in the Oracle database during archiving", "RefUrl": "/notes/572060 "}, {"RefNumber": "708721", "RefComponent": "BC-DB-ORA", "RefTitle": "Table enlargement possible with ASSM", "RefUrl": "/notes/708721 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": []}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}