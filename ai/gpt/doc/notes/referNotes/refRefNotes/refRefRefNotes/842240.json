{"Request": {"Number": "842240", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 228, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000015890122017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000842240?language=E&token=9F4CF1A333BF8B8CC5E3AFA1457F7ECF"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000842240", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000842240/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "842240"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 15}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "FAQ"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "15.05.2015"}, "SAPComponentKey": {"_label": "Component", "value": "BC-DB-ORA"}, "SAPComponentKeyText": {"_label": "Component", "value": "Oracle"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Basis Components", "value": "BC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Database Interface, Database Platforms", "value": "BC-DB", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-DB*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Oracle", "value": "BC-DB-ORA", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-DB-ORA*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "842240 - FAQ: Backup strategy for large and highly available databases"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<ol>1. Which areas does this SAP Note cover?</ol><ol>2. How can I speed up the backup and restore?</ol><ol>3. How can I speed up the recovery?</ol><ol>4. How can I minimize the effect of a backup on the production system?</ol><ol><ol>5. Where can I find more information?</ol></ol>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>FAQ, frequently asked questions</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<ol>1. Which areas does this SAP Note cover?</ol>\r\n<p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;This note shows options to shorten the duration of the backup, restore and recovery and to minimize the effect of backups on productive operation.</p>\r\n<p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;These topics are particularly relevant in systems that must be highly available or for which only short downtimes are prescribed in the SLAs.</p>\r\n<ol>2. How can I speed up the backup and restore?</ol>\r\n<p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;The following points and options can be considered to optimize the runtime of a backup:</p>\r\n<ul>\r\n<li>Hardware</li>\r\n</ul>\r\n<p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;The hardware used during the backup (tape drives, hard disks, system and I/O buses) plays an essential role for the data throughput of a backup. If you have any questions on this topic, contact your hardware partner.</p>\r\n<ul>\r\n<li>Using the BACKINT interface</li>\r\n</ul>\r\n<p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;You can use the BACKINT interface to connect external backup tools to the BR*TOOLS.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;If you have questions regarding optimal BACKINT performance, contact your BACKINT partner.</p>\r\n<ul>\r\n<li>Parallel backup (with non-BACKINT backups)</li>\r\n</ul>\r\n<p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;The runtime of a backup can be significantly reduced by parallel processing on several tape devices. A parallel backup can be activated using the EXEC_PARALLEL = &lt;parallel_degree&gt; parameter and by specifying all parallel disk drives in the TAPE_ADDRESS parameter in init&lt;sid&gt;.sap.</p>\r\n<ul>\r\n<li>Using DD (with non-BACKINT backups)</li>\r\n</ul>\r\n<p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;If you save with BRBACKUP by default, you should not use CPIO to copy the data files. Instead, you can use DD, which provides significantly improved performance. You can use the TAPE_COPY_CMD parameter to define the copying tool to be used in init&lt;sid&gt;.sap.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;If you use DD to copy the data, you can use the DD_FLAGS BRBACKUP parameter to configure as large a block size as possible (for instance, 64K). The bigger the block size, the better the performance will generally be.</p>\r\n<ul>\r\n<li>Optimizing the runtimes of BEGIN BACKUP</li>\r\n</ul>\r\n<p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Setting tablespaces to backup mode using BEGIN BACKUP can take an appreciable amount of time. In order to minimize this time, see Note 875477.</p>\r\n<ul>\r\n<li>Incremental backup</li>\r\n</ul>\r\n<p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;When you use RMAN, incremental backups can be created. These only save the blocks that have been changed since the last full backup, which significantly reduces the data volume of the backup. The runtime may also be slightly reduced. However, as all blocks have to be scanned in each case, the runtime is not reduced to the same extent as the data volume. As of Oracle 10g, you can also activate Block Change Tracking as described in Note 964619, which often allows significant improvements in runtime to be achieved.</p>\r\n<ul>\r\n<ul>\r\n<li>Advantages:</li>\r\n</ul>\r\n</ul>\r\n<p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Only blocks that are actually changed are saved.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;The system also only saves the blocks that contain data in the case of an RMAN full backup.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Runtimes are shorter, especially in conjunction with Block Change Tracking.</p>\r\n<ul>\r\n<ul>\r\n<li>Disadvantages:</li>\r\n</ul>\r\n</ul>\r\n<p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;The restore takes more time and resources, since a full backup and an incremental backup have to be restored.</p>\r\n<ul>\r\n<li>Partial backup</li>\r\n</ul>\r\n<p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;If a backup of the complete database takes too long, several partial backups can be carried out. It is important that every file of the database is saved during at least one partial backup.</p>\r\n<ul>\r\n<ul>\r\n<li>Advantages:</li>\r\n</ul>\r\n</ul>\r\n<p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;The runtime of the backup can be adjusted to suit the available timeslots.</p>\r\n<ul>\r\n<ul>\r\n<li>Disadvantages:</li>\r\n</ul>\r\n</ul>\r\n<p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;In the case of a restore, files may have to be retrieved from several backups.</p>\r\n<ul>\r\n<li>Two-phase backup</li>\r\n</ul>\r\n<p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Instead of saving the database directly to tape, a fast backup is performed to a disk area in the first step of the two-phase backup. From there it can then be saved to tape in the second step.</p>\r\n<ul>\r\n<ul>\r\n<li>Advantages:</li>\r\n</ul>\r\n</ul>\r\n<p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Shorter runtime of the primary backup<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;If the disk backup is also kept, it can be retrieved quickly in the case of a restore.</p>\r\n<ul>\r\n<ul>\r\n<li>Disadvantages:</li>\r\n</ul>\r\n</ul>\r\n<p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Additional disk space is required.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Creating the disk backup takes significantly longer than in a split mirror backup or snapshot.</p>\r\n<ul>\r\n<li>Split-mirror backup</li>\r\n</ul>\r\n<p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;In a split-mirror backup, the tablespaces are moved into backup mode (online backup) or the database is stopped (offline backup). The mirrored disk areas are then split. Subsequently - after a very short time - you can then end the backup mode again and start the database. The split hard disks can now be saved to tape. To ensure an especially fast restore, you can keep one or several disk mirrorings for the last few hours or days on disk. If a restore is required, the previous status can be restored by mounting the mirror disks without the need for a time-consuming restore from the tape.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;You can receive details on the implementation of the split and the later resynchronization from your hardware partner.</p>\r\n<ul>\r\n<ul>\r\n<li>Advantages:</li>\r\n</ul>\r\n</ul>\r\n<p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Disk backups are generated very quickly.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;The mirror backup is created independently of the database that is running.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;If the backup on the split mirror snapshot is kept for longer, a quick restore can be carried out if necessary.</p>\r\n<ul>\r\n<ul>\r\n<li>Disadvantages:</li>\r\n</ul>\r\n</ul>\r\n<p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Overhead for synchronizing the mirrors<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Additional disk space is required.</p>\r\n<ul>\r\n<li>Snapshots</li>\r\n</ul>\r\n<p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;The snapshot method allows all data for a particular point in time to be permanently frozen at I/O system level. If data is subsequently changed, the new version is ALSO stored. By creating snapshots (database offline or in the backup mode), backups can be created very rapidly on disk. Contact your hardware partner for details about how you can use snapshots.</p>\r\n<ul>\r\n<ul>\r\n<li>Advantages:</li>\r\n</ul>\r\n</ul>\r\n<p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Disk backups are generated very quickly.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;It is possible to create multiple snapshots of different times.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Less space is required than for the split-mirror backup, since only the blocks that are changed have to be stored twice.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;If the Snapshot is stored for a long time, you can carry out a quick restore if required.</p>\r\n<ul>\r\n<ul>\r\n<li>Disadvantages:</li>\r\n</ul>\r\n</ul>\r\n<p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;The backup runs in the same storage area as the production database (-&gt; resource conflict is possible).<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;In the case of block corruptions due to hardware problems, the snapshots may also be corrupt.</p>\r\n<ul>\r\n<li>Standby database</li>\r\n</ul>\r\n<p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;A standby database is set up in addition to the primary database and is recovered with the data of the primary database in real time or with a defined time offset. If necessary, you can start the standby database as the primary database without having to carry out a restore.</p>\r\n<ul>\r\n<ul>\r\n<li>Advantages:</li>\r\n</ul>\r\n</ul>\r\n<p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;This provides an additional complete database that can be activated quickly if required.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;As part of the recovery, redo logs are automatically checked for consistency.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Can also be used to eliminate logical errors if the recovery is delayed.</p>\r\n<ul>\r\n<ul>\r\n<li>Disadvantages:</li>\r\n</ul>\r\n</ul>\r\n<p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;This method has relatively high hardware requirements.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;If a logical error is not recognized fast enough, it is also present in the standby database.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;It is not possible to use the NOLOGGING feature in a useful way (see Notes 547464 and 442763).</p>\r\n<ul>\r\n<li>Avoiding direct I/O</li>\r\n</ul>\r\n<p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Although there are advantages to not using the file system cache for the Oracle database that is running (see Note 793113), the backup performance may deteriorate as a result of this. Where possible, you should therefore avoid activating direct I/O at file system level. Therefore, in accordance with Note 892631, it is not useful to mount the file system using direct I/O when you are using SOLARIS with Oracle 9i or higher. Instead, it is sufficient that the Oracle instance uses FILESYSTEMIO_OPTIONS = SETALL and direct I/O to access the files.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;If you cannot avoid activating direct I/O at the file system level, you must ensure that the file is accessed using the largest possible block sizes (for example, the largest possible block size value \"bs\" for DD).<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;To avoid the necessity for time-consuming restores from tape as much as possible, you can also combine several set ups. However, note that the necessity of a restore from tape can NEVER be ruled out, and you must therefore ensure that enough backup tapes are available.</p>\r\n<ol>3. How can I speed up the recovery?</ol>\r\n<p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;The recovery can be accelerated by parallel processing. To do this, the PARALLEL option can also be specified with the RECOVER command:<br /><br />RECOVER DATABASE ... [PARALLEL &lt;degree&gt;]</p>\r\n<p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;When you use BRRECOVER, the degree of parallelism can also be controlled using the \"-e &lt;degree&gt;\" parameter. For more information, see Note 806554.</p>\r\n<ol>4. How can I minimize the effect of a backup on the production system?</ol>\r\n<p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;You can use the following measures to minimize the effects of a backup on the production system:</p>\r\n<ul>\r\n<li>Using a backup network</li>\r\n</ul>\r\n<p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;To ensure that the network communication between SAP and Oracle is not negatively affected, a backup should always run through an independent backup network.</p>\r\n<ul>\r\n<li>Execution of online backups</li>\r\n</ul>\r\n<p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;In contrast to offline backups, you do not have to stop SAP or Oracle when you use online backups, which is why production operation can be continued without interruption.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Note that online backups of standby databases are supported by SAP only on the basis of RMAN in accordance with Note 968507.</p>\r\n<ul>\r\n<li>Backup of a copy of the production system</li>\r\n</ul>\r\n<p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;If you use technologies such as split mirror, standby database or two-phase backup, you can save the deduction of the productive data without accessing the production system itself.</p>\r\n<ol>5. Where can I find more information?</ol>\r\n<p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;You will find more detailed information about backing up large databases in the SAP environment in the online documentation at:<br /><br />SAP Database Guide: Oracle<br />-&gt; Backup, restore and recovery<br />-&gt; Advanced Backup and Recovery<br />-&gt; Backup of large Oracle Databases</p>\r\n<p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;You will find further information about backups, restoring and recovery on the homepage of the Advanced Technology Group:<br /><br /><a target=\"_blank\" href=\"http://service.sap.com/bestpractices\">http://service.sap.com/bestpractices</a></p>\r\n<p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;The ATG also provides the \"SAP Continuity Management\" service for optimizing your backup and restore/recovery strategy.</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "BC-DB-ORA-DBA (Database Administration)"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D030484)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON> (D021978)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000842240/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000842240/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000842240/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000842240/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000842240/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000842240/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000842240/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000842240/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000842240/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "964619", "RefComponent": "BC-DB-ORA", "RefTitle": "RMAN: Incremental backups with block change tracking", "RefUrl": "/notes/964619"}, {"RefNumber": "875477", "RefComponent": "BC-DB-ORA", "RefTitle": "Avoiding long runtimes with BEGIN BACKUP", "RefUrl": "/notes/875477"}, {"RefNumber": "806554", "RefComponent": "BC-DB-ORA", "RefTitle": "FAQ: I/O-intensive database operations", "RefUrl": "/notes/806554"}, {"RefNumber": "619188", "RefComponent": "BC-DB-ORA", "RefTitle": "FAQ: Oracle wait events", "RefUrl": "/notes/619188"}, {"RefNumber": "618868", "RefComponent": "BC-DB-ORA", "RefTitle": "FAQ: Oracle performance", "RefUrl": "/notes/618868"}, {"RefNumber": "592393", "RefComponent": "BC-DB-ORA", "RefTitle": "FAQ: Oracle", "RefUrl": "/notes/592393"}, {"RefNumber": "547464", "RefComponent": "BW-SYS-DB-ORA", "RefTitle": "Nologging Option when creating indexes", "RefUrl": "/notes/547464"}, {"RefNumber": "442763", "RefComponent": "BW-SYS-DB-ORA", "RefTitle": "Avoid NOLOGGING during the index structure (Oracle)", "RefUrl": "/notes/442763"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "618868", "RefComponent": "BC-DB-ORA", "RefTitle": "FAQ: Oracle performance", "RefUrl": "/notes/618868 "}, {"RefNumber": "592393", "RefComponent": "BC-DB-ORA", "RefTitle": "FAQ: Oracle", "RefUrl": "/notes/592393 "}, {"RefNumber": "619188", "RefComponent": "BC-DB-ORA", "RefTitle": "FAQ: Oracle wait events", "RefUrl": "/notes/619188 "}, {"RefNumber": "806554", "RefComponent": "BC-DB-ORA", "RefTitle": "FAQ: I/O-intensive database operations", "RefUrl": "/notes/806554 "}, {"RefNumber": "964619", "RefComponent": "BC-DB-ORA", "RefTitle": "RMAN: Incremental backups with block change tracking", "RefUrl": "/notes/964619 "}, {"RefNumber": "547464", "RefComponent": "BW-SYS-DB-ORA", "RefTitle": "Nologging Option when creating indexes", "RefUrl": "/notes/547464 "}, {"RefNumber": "442763", "RefComponent": "BW-SYS-DB-ORA", "RefTitle": "Avoid NOLOGGING during the index structure (Oracle)", "RefUrl": "/notes/442763 "}, {"RefNumber": "875477", "RefComponent": "BC-DB-ORA", "RefTitle": "Avoiding long runtimes with BEGIN BACKUP", "RefUrl": "/notes/875477 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": []}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}