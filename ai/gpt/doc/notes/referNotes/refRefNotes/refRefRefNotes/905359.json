{"Request": {"Number": "905359", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 454, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000016026212017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000905359?language=E&token=D9D59FA4FF53095C7BC747075D7D067F"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000905359", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000905359/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "905359"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 16}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Consulting"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "02.12.2016"}, "SAPComponentKey": {"_label": "Component", "value": "BC-DB-ORA-DBA"}, "SAPComponentKeyText": {"_label": "Component", "value": "Database Administration"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Basis Components", "value": "BC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Database Interface, Database Platforms", "value": "BC-DB", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-DB*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Oracle", "value": "BC-DB-ORA", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-DB-ORA*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Database Administration", "value": "BC-DB-ORA-DBA", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-DB-ORA-DBA*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "905359 - Using BR*Tools for Oracle RAC databases"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>BR*Tools 6.40 or higher are released for use in Oracle RAC databases.<br />For a detailed description including prerequisites and recommendations, see the official SAP documentation for BR*Tools.<br />No different versions are currently available.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>Oracle Real Application Cluster (RAC)</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>Consulting</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p>For the most current BR*Tool documentation, including the description of Oracle RAC support, see the chapter \"Oracle Real Application Cluster\" under<br /><strong>https://www.sdn.sap.com/irj/sdn/ora -&gt; Oracle DBA Overview -&gt;</strong><br /><strong>SAP Database Guide: Oracle</strong><br />For the most current Oracle white papers concerning the use of RAC in the SAP environment, see<br /><strong>https://www.sdn.sap.com/irj/sdn/ora -&gt; SAP on Oracle Real Application Clusters (RAC)</strong><br /><br />The most important prerequisites for using BR*Tools for Oracle RAC databases are:<br /><br />1. All Oracle instances (including the local instance) is specified in the init&lt;DBSID&gt;.sap profile parameter \"parallel_instances\".<br /><br />2. The log directories for the BR*Tools saparch, sapbackup, sapcheck and sapreorg are located on shared file systems.<br /><br />3. The Oracle archiving directory (oraarch) is on a shared file system.<br /><br />4. The Oracle profile directory (Unix: dbs, Windows: database), the Oracle Trace directory (saptrace), and the Pracle audit directory (~/rdbms/audit) are on shared file systems. The entire Oracle home should be located on a shared file system.<br /><br />5. All Oracle instances use a shared spfile named spfile.ora (not spfile&lt;DBSID&gt;.ora) that is located in the profile directory (Unix: dbs, Windows: database). All RAC instances use the spfile. It contains all instance-independent parameters and all instance-dependent parameters for all Oracle instances. You must have defined at least the following instance-dependent parameters there:<br />&lt;INST_NAME&gt;.instance_number<br />&lt;INST_NAME&gt;.instance_name<br />&lt;INST_NAME&gt;.thread<br />&lt;INST_NAME&gt;.service_names<br />&lt;INST_NAME&gt;.undo_tablespace<br />For a list of instance-dependent parameters, see Note 830982.<br /><br />6. The Oracle SQL*Net configuration conforms to the recommendations from the Oracle White Paper on SDN:<br />http://www.sdn.sap.com/irj/sdn/ora<br />-&gt; SAP on Oracle Real Application Clusters (RAC)<br />Oracle 10g:<br />-&gt; \"SQL*Net Configuration for SAP taking RAC as an example\"<br />Oracle 11g:<br />-&gt; \"Configuration of SAP NetWeaver for Oracle Grid Infrastructure 11.2 with Oracle Real Application Clusters 11g Release 2\"<br /><br />7. The BR*Tools are normally started on a dedicated RAC node, which is defined in SAP Note 621293. BRARCHIVE is started on one node only, and saves offline redolog files for all RAC instances automatically.<br />If backups are to be created on the dedicated RAC nodes, but a restore to a different RAC nodes takes place, the summary log of BRBACKUP must be copied.<br />Example:<br />&gt; cd /oracle/&lt;DBSID&gt;/sapbackup<br />&gt; cp back&lt;DBSID1&gt;.log back&lt;DBSID2&gt;.log<br />A simpler solution on Unix platforms would be to create a soft link instead:<br />&gt; ln -s&#x00A0;back&lt;DBSID1&gt;.log back&lt;DBSID2&gt;.log<br /><br />8. On Unix systems, BR*Tools use a shared Oracle password file named \"orapw\" (not orapw&lt;DBSID&gt;) as standard for remote database connection. All RAC instances use this password file. For more information, see Note 131610.<br />On Windows systems, you do not require an Oracle password file because a remote access with SYSDBA authorization is possible if the following parameter is set in sqlnet.ora:<br />SQLNET.AUTHENTICATION_SERVICES = (NTS)<br /><br />9. If you want to use the Oracle password file described above in Unix systems, you should set the option \"-u &lt;user&gt;/&lt;password&gt;\" with a specific user (for example, SYSTEM) when you call BR* tools (see SAP Note 131610). You can avoid this with BRARCHIVE, BRBACKUP and BRCONNECT, but not with BRSPACE and BRRECOVER. You can find more information about this in Note 914174 point 4. As is the case for standard installations BRSPACE, BRRESTORE and BRRECOVER can only be called on Unix under ora&lt;dbsid&gt;.<br /><br />Caution:<br />Oracle RAC has no effect on the specification of the BACKINT interface.</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "D000674"}, {"Key": "Processor                                                                                           ", "Value": ""}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000905359/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000905359/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000905359/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000905359/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000905359/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000905359/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000905359/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000905359/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000905359/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "914174", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Minor functional enhancements in BR*Tools (1)", "RefUrl": "/notes/914174"}, {"RefNumber": "830982", "RefComponent": "BC-DB-ORA-RAC", "RefTitle": "SAP recommendations for Oracle RAC 9.2.0.x config.", "RefUrl": "/notes/830982"}, {"RefNumber": "621293", "RefComponent": "BC-DB-ORA", "RefTitle": "Oracle 9i/10g: Real Application Clusters", "RefUrl": "/notes/621293"}, {"RefNumber": "581320", "RefComponent": "BC-DB-ORA-RAC", "RefTitle": "FAQ: Oracle Real Application Clusters (RAC)", "RefUrl": "/notes/581320"}, {"RefNumber": "527843", "RefComponent": "BC-DB-ORA", "RefTitle": "Oracle RAC support in the SAP environment", "RefUrl": "/notes/527843"}, {"RefNumber": "1598594", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "BR*Tools configuration for Oracle installation using user \"oracle\"", "RefUrl": "/notes/1598594"}, {"RefNumber": "131610", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Database logon using standby/backup host or with Real Application Cluster (RAC)", "RefUrl": "/notes/131610"}, {"RefNumber": "1033126", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "BR*Tools support for Oracle 10g RAC", "RefUrl": "/notes/1033126"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1598594", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "BR*Tools configuration for Oracle installation using user \"oracle\"", "RefUrl": "/notes/1598594 "}, {"RefNumber": "527843", "RefComponent": "BC-DB-ORA", "RefTitle": "Oracle RAC support in the SAP environment", "RefUrl": "/notes/527843 "}, {"RefNumber": "914174", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Minor functional enhancements in BR*Tools (1)", "RefUrl": "/notes/914174 "}, {"RefNumber": "830982", "RefComponent": "BC-DB-ORA-RAC", "RefTitle": "SAP recommendations for Oracle RAC 9.2.0.x config.", "RefUrl": "/notes/830982 "}, {"RefNumber": "131610", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Database logon using standby/backup host or with Real Application Cluster (RAC)", "RefUrl": "/notes/131610 "}, {"RefNumber": "581320", "RefComponent": "BC-DB-ORA-RAC", "RefTitle": "FAQ: Oracle Real Application Clusters (RAC)", "RefUrl": "/notes/581320 "}, {"RefNumber": "621293", "RefComponent": "BC-DB-ORA", "RefTitle": "Oracle 9i/10g: Real Application Clusters", "RefUrl": "/notes/621293 "}, {"RefNumber": "1033126", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "BR*Tools support for Oracle 10g RAC", "RefUrl": "/notes/1033126 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": []}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}