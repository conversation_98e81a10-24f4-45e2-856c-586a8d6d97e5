{"Request": {"Number": "1601951", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 344, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000017262912017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001601951?language=E&token=DA04F696CAA19AB30C3642BD37217EAF"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001601951", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0001601951/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1601951"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 10}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "FAQ"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "22.08.2017"}, "SAPComponentKey": {"_label": "Component", "value": "SV-SMG-SER"}, "SAPComponentKeyText": {"_label": "Component", "value": "SAP Support Services"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Service", "value": "SV", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'SV*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "SAP Solution Manager", "value": "SV-SMG", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'SV-SMG*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "SAP Support Services", "value": "SV-SMG-SER", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'SV-SMG-SER*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1601951 - Self Service 'SQL Statement Tuning' - Prerequisites and FAQ"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>It is considered or planned to perform a Self Service \"SQL Statement Tuning\" (currently possible for systems on SAP HANA and for ABAP based systems running with Oracle, DB2 UDB for Unix, Linux and Windows release &gt;= 9.5, or MSSQL release &gt;= 2005. )</p>\r\n<ul>\r\n<li>1 When to perform this service</li>\r\n</ul>\r\n<ul>\r\n<li>2 How to perform the service</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>2.1 General Steps</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>2.2 How to perform those steps in the Solution Manager</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li>3 Prerequisites</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>3.1 Prerequisites on Solution Manager</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>3.2 Prerequisites on Managed System</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>3.3 Non-technical Prerequisites.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li>4 Frequently Asked Questions</li>\r\n</ul>\r\n<p><br />Note that those topics are discussed in detail in the corresponding \"Expert Guided Implementation session\" (see http://service.sap.com/expert-guided-implementation).</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>GSS_SQL, performance, expensive statements</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p><br /><strong>1 When to perform this service</strong><br /><br />The Self Service \"SQL Statement Tuning\" aims to optimize the general system performance by reducing the impact from those SQL statements that have the highest contribution to the overall DB load. This means: in order to get a noticeable effect, this service should be performed if:</p>\r\n<ul>\r\n<ul>\r\n<li>There is a general performance problem.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Database time contributes significantly to the overall response time.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Significant DB load is caused by only a small number of SQL statements.</li>\r\n</ul>\r\n</ul>\r\n<p><br />In an EarlyWatch Alert report, the rating of the section \"Database server load from expensive SQL statements\" is defined according to those criteria (See SAP Notes 551646, 1625795, 2021756&#160;for details). If this section is rated \"yellow\" or \"red\", an \"Alert\" is raised inside the EarlyWatch Alert session and the recommendation to perform the Self Service \"SQL Statement Tuning\" is provided in the report.<br /><br /><br /><strong>2 How to perform the service</strong></p>\r\n<p><strong>2.1 General Steps</strong><br /><br /></p>\r\n<p>The service consists of steps:</p>\r\n<ul>\r\n<li><strong>Preparation:</strong></li>\r\n</ul>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;First, the system to be analyzed needs to be chosen. Then, it has to be decided whether the analysis should be based on data already collected in a previous EarlyWatch Alert session (for example when trying to analyze exactly those SQL statements responsible for \"yellow\" or \"red\" rating in an EarlyWatch Alert report) or whether fresh data need to be collected. Note: While the collection of fresh data takes some time (depending on the system, the collection time can vary between several minutes to more than one hour), no delay is caused when existing data from a previous EarlyWatch Alert report are to be analyzed.</p>\r\n<ul>\r\n<li><strong>Analysis: </strong></li>\r\n</ul>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;The data collected in the \"Preparation\" step lead to a list of SQL statements to be analyzed. Technical details of those SQL statements are provided both to support and to document the analysis. Each SQL statement is classified and an appropriate analysis path is provided. The order of the steps in the analysis path follows the preference for the different possible solutions. Once one of the steps in the analysis path provides a solution for the analyzed SQL statement, the next SQL statement can be analyzed. Finally, an HTML or Word document can be generated, documenting both the details of the problematic SQL statements and the solution found in the analysis.</p>\r\n<ul>\r\n<li><strong>Follow Up: </strong></li>\r\n</ul>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;After the analysis is finished, a follow-up is required: the measures recommended in the analysis step needs to be performed, i.e. the appropriate person or organization needs to be informed.</p>\r\n<p><strong>2.2 How to perform those steps in the Solution Manager</strong><br /><br /></p>\r\n<p>The Solution Manager helps to perform those steps. Depending on the release of the Solution Manager system, performing the Self Service \"SQL Statement Tuning\" in a slightly different way.</p>\r\n<ul>\r\n<li><strong>Solution Manager runs ST 7.1 or 7.2</strong></li>\r\n</ul>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;Using transaction \"solman_workcenter\", the Work Center \"SAP Engagement and Service Delivery\" can be opened. By pressing \"Services\", an overview of the existing services for the selected \"Solution\" is given. Pressing \"Create\" allows the creation of the (Guided) Self Service \"SQL Statement Tuning\". A Guided Procedure appears with the steps (1) \"Preparation\" (2) \"Analysis\" (3) \"Report\" and (4) \"Follow-Up\".<br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;In the first sub-step of the \"Preparation\", an overview of the systems in the solution is given together with information whether an analysis can be performed and whether an analysis was recommended in a recent EarlyWatch Alert report. The final step \"Follow-Up\" links the results of the Analysis with the \"Issue Management\" of the Solution Manager: for each finding in the \"Analysis\", a \"Task\" is created that can be assigned to the appropriate processor. If the \"Issue Management\" in the solution manager is not used, the follow-up steps have to be performed without support by the tool.</p>\r\n<ul>\r\n<li><strong>Solution Manager runs ST 400 and ST-SER 701_2010_1</strong></li>\r\n</ul>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;Like in case of a Solution Manager with ST 7. 1, the service (\"Guided SQL Tuning - Session\") can be created in the Work Center \"SAP Engagement and Service Delivery\". However, after the service is created, the session will not be opened automatically. Instead, it is necessary first to click the triangle symbol in front of the service name. After this is done, a new line with the session title \"SQL Statement Tuning\" appears. This line has to be marked in order to see the&#160;&#160;\"Details of Service\". In the tab \"Sessions\" of the \"Details of Service\", the button \"Create Questionnaire\" needs to be pressed to create a second entry \"Guided Self Service: SQL Tuning - Questionnaire\" along the previous entry \"Guided Self Service: SQL Tuning - Session\" in the table \"Session\".<br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;After the \"Questionnaire\" and the \"Session\" are created, the procedure is similar to the ST 7.1 situation: By clicking \"Guided Self Service: SQL Tuning - Questionnaire\", a new screen opens and allows to perform the first step \"Preparation\". After the \"Preparation\" is completed, the step \"Analysis\" can be performed by clicking \"Guided Self Service: SQL Tuning - Session\". After the analysis is completed, the button \"Word Document\" can be pressed inside the session in order to create the report. Finally, also the follow-up steps can be performed (if required) with the \"Issue Management\" functionality of the Solution Manager: After the report has been created, the \"Details of Service: SQL Statement Tuning\" needs to be refreshed: after the refresh, two new tabs appear: in the tab \"Attachments\", the word report can be found and in the tab \"Assigned Issues\", the tasks created by the analysis are listed and can be distributed to the appropriate person.</p>\r\n<ul>\r\n<li><strong>Solution Manager runs ST 400 and ST-SER older than 701_2010_1</strong></li>\r\n</ul>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;In this situation, the tool does not provide a convenient guidance through the various steps. Nonetheless, the essential building blocks are already available and the process can be performed, although additional manual steps are required.<br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;The motivation to perform an SQL tuning can still be provided by the EarlyWatch Alert report: if the section \"Database server load from expensive SQL statements\" points to problems caused by \"expensive\" SQL statements, those statement should and can be investigated with already existing tool: In the Work Center \"SAP Engagement and Service Delivery\", a \"SAP EarlyWatch Health Check\" for specific system can be created. After the session is created, the service data needs to be collected. This happens usually once per night, i.e. the session can be performed only at the following day (unless the collection of service data is initiated manually).<br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;Before starting the \"EarlyWatch Health Check\", it needs to be verified that the service data are already available. This can be done in the tab \"Sessions\" in \"Details of Service: EarlyWatch Health Check\": Clicking on \"SDCC Download\" results either in the warning \"No SDCC download exists.\" or the collected data are displayed in a new screen. Only after the SDCC download is available, session \"Self Service: SAP EarlyWatch Health Check\" can be started successfully.<br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;Performing the \"EarlyWatch Health Check\" could be done in two different ways: if a general health check is to be performed, all the steps of this session need to be performed. However, if the analysis should focus only on expensive SQL Statements, many steps can be ignored: First, just the step \"Session Initialization\" needs to be saved. After this step has been performed, only the check \"SQL Cache and ABAP Program Analysis\" and its sub-steps are to be executed.<br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;The word report can be created by pressing the button \"Word Report\" within the \"EarlyWatch Health Check\". In case the analysis was concentrating exclusively on the SQL statements, the generated report needs some \"cleaning up\": sections that were generated automatically as part of a general analysis should be removed.<br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;In case the \"Issue Management\" is used, issues need to be both created and assigned to the \"EarlyWatch Health Check\" manually.<br /><br /><br /><strong>3 Prerequisites</strong></p>\r\n<p><strong>3.1 Prerequisites on Solution Manager</strong></p>\r\n<ul>\r\n<li><strong>Software:</strong></li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>For Oracle, a basic version of the \"SQL Statement Tuning\" should be possible with a Solution Manager running on ST 7.0 SP 18 with ST 400 SP18 and the content edition ST-SER 701_2008_2. If the session is to be performed for DB2 UDB LUW, ST-SER 701_2010_1 with SP 10 is required, in case of SAP HANA, SP 18 and&#160;for&#160;MSSQL SP 23 is required. Note: Instead&#160;of applying the ST-SER Support Package, also the&#160;Service Content Update (SCU) mechanism could be used as described in SAP Note&#160;1143775.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>In order to profit both from bug-fixes and further improvement, we recommend to use the content edition ST-SER 720 either with the highest possible support package (see SAP Note 569116) or (even more convenient) by activating the \"SAP service content update\" (See SAP Note 1143775).</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>For ST 400, note that the possibility to perform the SQL Tuning with the dedicated \"Guided SQL Tuning - Session\" (as described in the second option in section 2.2) is available only if ST-SER 2001_2010_1 Support Package 2 is installed or if the content update is used. In that latter case, in addition to use the content update, SAP Note 1317901 (for ST 400, SP 16 - 24) or 1491227 (for higher SP) has to be taken into account: In point 1 of those note, it is described how to \"register a new service\": this step needs to be done also to have the \"Guided SQL Tuning - Session\" available: After implementing the corrections from the corresponding SAP Note, transaction AGS_UPDATE needs to be called. After pressing \"Register New Service\" the service with the Package ID \"GSS_SQL\" has to be marked.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>While as of ST 400 SP 22, no additional steps are required (as long as Customizing is implemented with transaction SOLMAN_SETUP), in other case, the Business Configuration (\"BC\") set SOLMAN40_ISTR_ADDFUNC_007 needs to be activated with transaction SCPR20 (see SAP Note 903528)</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>For ST 400 SP &gt; 23 and SP &lt; 27: Implement SAP Note 1517658, 1550309</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li><strong>Customizing:</strong></li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>If the session is to be performed for a DB2 UDB LUW system with ST-PI 2008_1_[46C-710] SP &lt; 5 or on a Solution Manager system with ST-SER 701_2010_1 SP &lt; 12, a valid database connection to the database has to be maintained (see SAP Note 1267189 for details). This database connection should be assigned to the managed system with transaction SMSY (or LMDB): search for the related database, choose the \"Other Attributes\" tab, and enter the remote database connection as the value for the \"Remote Database Connection\" attribute (Note: if the Solution Manager is running on ST 7.1, trying to edit those values in SMSY will open a window from the LMDB. The attribute \"Remote Database Connection\" can be maintained in the tab \"Custom Attribute\" of this window.)</li>\r\n<li>If the session is to be performed for an SAP HANA database, data are obtained directly from the database using a dbcon connection. If the dbcon connection was already created in order to monitor the system in the DBA Cockpit (see <a target=\"_blank\" href=\"/notes/1265134\">SAP Note 1265134</a>), this connection can be usd.&#160;In case another dbcon connection is to be used, the database user should have at least the permissions of the database user used for the DBA Cockpit (see <a target=\"_blank\" href=\"/notes/1640741\">SAP Note 1640741</a>).&#160;&#160;</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li><strong>Solution:</strong></li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>The system to be analyzed has to be assigned to the solution in which the service is created.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li><strong>EarlyWatch Alert:</strong></li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>The EarlyWatch alert report helps to motivate performing the SQL Tuning Service and provides background information about other issues on the system to be investigated. It is therefore highly recommended to have the EarlyWatch Alert activated for that system (see SAP Note 1257308 for details about \"setup\" and \"troubleshooting\" of the EarlyWatch Alert).</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li><strong>Authorizations:</strong></li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>The user performing the session in the Solution Manager needs the authorization to use transaction solman_workcenter, to access the Work Center \"SAP Engagement and Service Delivery\" and to create and process a self service within this Work Center. This could be achieved by assigning the role SAP_SERVICE_EXE_ALL_COMP (or, when not yet available: SAP_SOLMAN_ONSITE_COMP) to the user.</li>\r\n</ul>\r\n</ul>\r\n<p><br /><br /><br /><strong>3.2 Prerequisites Managed System</strong><br /><br /></p>\r\n<ul>\r\n<li><strong>General</strong></li>\r\n<ul>\r\n<li>Currently, the \"SQL Tuning Service\" supports&#160;SAP HANA&#160;and&#160;ABAP based systems running Oracle, DB2 UDB for Linux, Unix and Windows and MSSQL</li>\r\n</ul>\r\n<li><strong>Software:</strong></li>\r\n<ul>\r\n<li>For Oracle and DB2 LUW: ST-PI 2008_1_[46C-710] or higher (Oracle: If SP 4 is installed, SAP Note 1564508 needs to be applied, DB2 LUW: at least SP 5)</li>\r\n<li>For DB2 UDB LUW: the managed system needs SAP Basis release &gt;= 7.0.</li>\r\n<li>For DB2 UDB LUW: the database release has to be 9.5 or higher.</li>\r\n<li>For MSSQL: ST-A/PI is 01P_*&#160;or higher (Note: in case 01Q*, also at least SP1 or SAP Note 1822866 have to be applied)</li>\r\n<li>For MSSQL: the database release has to be&#160;2005 or higher.</li>\r\n<li>For MSSQL: in case no SQL Statements are listed in the EWA (and the \"SQL Tuning Service\"), check whether SAP&#160;<a target=\"_blank\" href=\"/notes/2155771\">SAP Note 2155771</a>&#160;is applicable.</li>\r\n</ul>\r\n<li><strong>Authorizations:</strong></li>\r\n<ul>\r\n<li>The person performing the \"SQL Tuning Service\" has to have the authorization to access the managed system and to use transaction sdccn and st04. The needs are met when the user is created with the \"Minimum Authorization Profile for Remote Service\" as described in SAP Note 1405975.</li>\r\n</ul>\r\n<ul>\r\n<li>In the preparation step, the possibility is offered to collect current data from the solution manage system. Depending on the authorizations of the user of the \"read\" connection, the collection of the data might be started immediately or only with some delay. If a delay is observed, it needs to be ensured the user used in the \"Read\" RFC connection has the necessary authorization. This could be achieved either by re-creating the user with an updated profile based on SAP Note 1572183. Alternatively, the profile S_SDCC_SERVN (see SAP Note 763561) could be assigned manually to the user used for the read connection.</li>\r\n</ul>\r\n</ul>\r\n<p><strong>3.3 Non-Technical Prerequisites</strong></p>\r\n<ul>\r\n<li><strong>Skill-set of the person performing the \"SQL Tuning Service\":</strong></li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>The person performing the service should be familiar with the technical monitoring tools of ABAP based SAP Systems.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Basic knowledge on databases and SQL Statement tuning is essential in order to use the tool in an efficient way. Note: <a target=\"_blank\" href=\"/notes/2000002\">SAP Note&#160;2000002</a>&#160;gives an introduction to&#160;SQL Statement analysis for SAP&#160;HANA,&#160;&#160;<a target=\"_blank\" href=\"/notes/766349\">SAP Note 766349</a> provides a first introduction into \"Oracle SQL optimization\".</li>\r\n</ul>\r\n</ul>\r\n<p><br /><strong>4 Frequently Asked Questions</strong></p>\r\n<ul>\r\n<li><strong>Is the \"SQL Tuning Service\" available only for Oracle, DB2 UDB LUW, MSSQL, SAP HANA&#160;?</strong></li>\r\n</ul>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;While currently, only Oracle, DB2 UDB LUW, MSSQL and SAP HANA&#160;are supported, extending the service to support other databases in the future might be considered if demand growht.</p>\r\n<ul>\r\n<li><strong>Is there an \"SQL Tuning Service\" available for JAVA?</strong></li>\r\n</ul>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;No. Currently, even in an SAP delivered service, the statement analysis is done without tool-support.&#160;&#160;Especially the \"business background\" cannot be determined in a straightforward way as in case of an ABAP based system. On the other hand, currently, there is only limited demand for such services. Therefore, for the time being, there are no plans to develop tools to support the analysis in an SAP delivered service. Consequently, it is currently not possible to offer such a service as a Self Service which would have even higher demands on the usability and robustness than a SAP delivered service.</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D031817)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D023762)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0001601951/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001601951/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001601951/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001601951/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001601951/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001601951/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001601951/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001601951/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001601951/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2000002", "RefComponent": "HAN-DB-PERF", "RefTitle": "FAQ: SAP HANA SQL Optimization", "RefUrl": "/notes/2000002"}, {"RefNumber": "903528", "RefComponent": "SV-SMG-SVD", "RefTitle": "Solution Manager issue management and service plan: BC sets", "RefUrl": "/notes/903528"}, {"RefNumber": "872800", "RefComponent": "SV-SMG-SER", "RefTitle": "Roles for SAP Solution Manager: SAP service provider", "RefUrl": "/notes/872800"}, {"RefNumber": "766349", "RefComponent": "BC-DB-ORA", "RefTitle": "FAQ: Oracle SQL optimization", "RefUrl": "/notes/766349"}, {"RefNumber": "763561", "RefComponent": "SV-SMG-SDD", "RefTitle": "Service Data Control Center (SDCCN)  - FAQ", "RefUrl": "/notes/763561"}, {"RefNumber": "69455", "RefComponent": "SV-SMG-SDD", "RefTitle": "Servicetools for Applications ST-A/PI (ST14, RTCCTOOL, ST12)", "RefUrl": "/notes/69455"}, {"RefNumber": "569116", "RefComponent": "SV-SMG-SER", "RefTitle": "Release strategy for Solution Manager Service Tools (ST-SER)", "RefUrl": "/notes/569116"}, {"RefNumber": "551646", "RefComponent": "SV-SMG-SER", "RefTitle": "Oracle SQL check rating strategy for EarlyWatch Alert", "RefUrl": "/notes/551646"}, {"RefNumber": "2021756", "RefComponent": "SV-SMG-SER", "RefTitle": "MSSQL Expensive SQL statements check. Rating strategy for EarlyWatch Alert", "RefUrl": "/notes/2021756"}, {"RefNumber": "1648506", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1648506"}, {"RefNumber": "1640741", "RefComponent": "HAN-DB", "RefTitle": "FAQ: \"DB users for the DBA Cockpit for SAP HANA\"", "RefUrl": "/notes/1640741"}, {"RefNumber": "1625795", "RefComponent": "SV-SMG-SER", "RefTitle": "DB2 UDB (LUW) SQL check rating strategy for EarlyWatch Alert", "RefUrl": "/notes/1625795"}, {"RefNumber": "1609155", "RefComponent": "SV-SMG-SER", "RefTitle": "Guided Self Services", "RefUrl": "/notes/1609155"}, {"RefNumber": "1572183", "RefComponent": "SV-SMG-AUT", "RefTitle": "Authorizations for SAP Solution Manager RFC users", "RefUrl": "/notes/1572183"}, {"RefNumber": "1564508", "RefComponent": "SV-SMG-SER", "RefTitle": "/SDF/RSORADLD_NEW: No statements during autom. download", "RefUrl": "/notes/1564508"}, {"RefNumber": "1550309", "RefComponent": "SV-SMG-SVD", "RefTitle": "DSWP: Questionnaire related error", "RefUrl": "/notes/1550309"}, {"RefNumber": "1517658", "RefComponent": "SV-SMG-SVD", "RefTitle": "SD: Incorrect error message while opening self service", "RefUrl": "/notes/1517658"}, {"RefNumber": "1491227", "RefComponent": "SV-SMG-SVD-SCU", "RefTitle": "Adjustments to service content update from ST 700 SP 23", "RefUrl": "/notes/1491227"}, {"RefNumber": "1405975", "RefComponent": "SV-SMG-SER", "RefTitle": "Minimum Authorization Profile for Remote Service Delivery", "RefUrl": "/notes/1405975"}, {"RefNumber": "1317901", "RefComponent": "SV-SMG-OP", "RefTitle": "Adjustments to service content update up to ST 700 SP 22", "RefUrl": "/notes/1317901"}, {"RefNumber": "1267189", "RefComponent": "BC-DB-DB6-CCM", "RefTitle": "DBA Cockpit: DB2 for LUW as Remote Database", "RefUrl": "/notes/1267189"}, {"RefNumber": "1265134", "RefComponent": "BC-DB-MON", "RefTitle": "DBA Cockpit: Connection of a remote database", "RefUrl": "/notes/1265134"}, {"RefNumber": "1257308", "RefComponent": "SV-SMG-SER-EWA", "RefTitle": "FAQ: Using EarlyWatch Alert", "RefUrl": "/notes/1257308"}, {"RefNumber": "1143775", "RefComponent": "SV-SMG-SVD-SCU", "RefTitle": "SAP Service Content Update", "RefUrl": "/notes/1143775"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2221525", "RefComponent": "SV-SMG-SER", "RefTitle": "Oracle SQL check rating strategy for EarlyWatch Alert", "RefUrl": "/notes/2221525 "}, {"RefNumber": "1609155", "RefComponent": "SV-SMG-SER", "RefTitle": "Guided Self Services", "RefUrl": "/notes/1609155 "}, {"RefNumber": "1517658", "RefComponent": "SV-SMG-SVD", "RefTitle": "SD: Incorrect error message while opening self service", "RefUrl": "/notes/1517658 "}, {"RefNumber": "1572183", "RefComponent": "SV-SMG-AUT", "RefTitle": "Authorizations for SAP Solution Manager RFC users", "RefUrl": "/notes/1572183 "}, {"RefNumber": "1491227", "RefComponent": "SV-SMG-SVD-SCU", "RefTitle": "Adjustments to service content update from ST 700 SP 23", "RefUrl": "/notes/1491227 "}, {"RefNumber": "1550309", "RefComponent": "SV-SMG-SVD", "RefTitle": "DSWP: Questionnaire related error", "RefUrl": "/notes/1550309 "}, {"RefNumber": "766349", "RefComponent": "BC-DB-ORA", "RefTitle": "FAQ: Oracle SQL optimization", "RefUrl": "/notes/766349 "}, {"RefNumber": "1405975", "RefComponent": "SV-SMG-SER", "RefTitle": "Minimum Authorization Profile for Remote Service Delivery", "RefUrl": "/notes/1405975 "}, {"RefNumber": "1267189", "RefComponent": "BC-DB-DB6-CCM", "RefTitle": "DBA Cockpit: DB2 for LUW as Remote Database", "RefUrl": "/notes/1267189 "}, {"RefNumber": "903528", "RefComponent": "SV-SMG-SVD", "RefTitle": "Solution Manager issue management and service plan: BC sets", "RefUrl": "/notes/903528 "}, {"RefNumber": "1648506", "RefComponent": "BC-DB-MSS", "RefTitle": "No SQL statement analysis in the guided self-service session", "RefUrl": "/notes/1648506 "}, {"RefNumber": "1625795", "RefComponent": "SV-SMG-SER", "RefTitle": "DB2 UDB (LUW) SQL check rating strategy for EarlyWatch Alert", "RefUrl": "/notes/1625795 "}, {"RefNumber": "1564508", "RefComponent": "SV-SMG-SER", "RefTitle": "/SDF/RSORADLD_NEW: No statements during autom. download", "RefUrl": "/notes/1564508 "}, {"RefNumber": "763561", "RefComponent": "SV-SMG-SDD", "RefTitle": "Service Data Control Center (SDCCN)  - FAQ", "RefUrl": "/notes/763561 "}, {"RefNumber": "551646", "RefComponent": "SV-SMG-SER", "RefTitle": "Oracle SQL check rating strategy for EarlyWatch Alert", "RefUrl": "/notes/551646 "}, {"RefNumber": "569116", "RefComponent": "SV-SMG-SER", "RefTitle": "Release strategy for Solution Manager Service Tools (ST-SER)", "RefUrl": "/notes/569116 "}, {"RefNumber": "872800", "RefComponent": "SV-SMG-SER", "RefTitle": "Roles for SAP Solution Manager: SAP service provider", "RefUrl": "/notes/872800 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "ST-SER", "From": "701_2008_2", "To": "701_2010_1", "Subsequent": "X"}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}