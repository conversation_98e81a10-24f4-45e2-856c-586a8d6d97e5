{"Request": {"Number": "927637", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 857, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000016065992017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000927637?language=E&token=C1B46B93A5F0308EFDF6C6EF571FAC4D"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000927637", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000927637/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "927637"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 22}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Release planning information"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "29.07.2016"}, "SAPComponentKey": {"_label": "Component", "value": "BC-CST-STS"}, "SAPComponentKeyText": {"_label": "Component", "value": "Startup Service"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Basis Components", "value": "BC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Client/Server Technology", "value": "BC-CST", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-CST*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Startup Service", "value": "BC-CST-STS", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-CST-STS*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "927637 - Web service authentication in sapstartsrv as of Release 7.00"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>The Web service interface of sapstartsrv requires a user authentication from the client (MMC, SAP MC, sapcontrol and so on) for protected operations such as starting and stopping the SAP instance. This authentication may fail even if the specifications are correct.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>sapstartsrv, MMC, sapcontrol, SAP MC, Invalid Credentials, Permission denied, Web service authentication, nosuid</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>An OS user that can be verified is required for authentication on the target host on which the SAP instance runs. If the user/password check was successful, the system checks whether the user has execute rights for the file sapstartsrv.exe (Windows) or write authorization for the file sapstartsrv (non-Windows platforms). If this is the case, the required operation is executed. Thus, you can set the access to the protected operations with greater precision by setting the file access authorizations.<br /><br />As of Release 640 patch 337, Release 700 patch 263, Release 701 patch 30, Release 710 patch 139, Release 711 patch 26 und Release 720, you can use the profile parameter service/admin_users (list of OS users separated by blank characters) to authorize additional OS users.&#x00A0;The profile parameter service/admin_groups can also be used to authorize further OS groups (on Windows, only in current sapstartsrv versions, and only for authentication with explicit password specification; see SAP Note 877795, Section 119). As of Release 710 patch 139, Release 711 patch 26, and Release 720, you can also use the profile parameter service/java_debug_users to authorize OS users to use the functions J2EEEnableDbgSession and J2EEDisableDbgSession that are required for Java debugging.<br /><br />If the user/password check fails, the system generates an \"Invalid Credentials\" SOAP exception. If it was successful, but the user does not have the authorization required, the system generates a \"Permission denied\" SOAP exception.<br /><br />Sapstartsrv controls an internal list of protected operations (default depends on the release, for example.: \"Start Stop StopService J2EEControlProcess SendSignal OSExecute\"). If required, you can change this using the start profile parameter<br />\"service/protectedwebmethods\". If required, you can also use the start profile parameter \"service/hostname\" to specify the IP address/host name to which the Web service port should be linked (default: all/0.0.0.0) to restrict access in the network. You must then restart sapstartsrv.<br /><br />As of Release 640 patch 337, Release 700 patch 263, Release 701 patch 30, Release 710 patch 139, Release 711 patch 26 und Release 720, sapstartsrv supports an enhanced syntax for \"service/protectedwebmethods\":<br /><br />[ALL|NONE|DEFAULT] +|-&lt;method1&gt; +|-&lt;method2&gt;... +|-&lt;methodN&gt;<br /><br />You can thus choose one of three default lists (ALL: All methods; NONE: No methods; DEFAULT: Default list of critical methods). Then, optionally, you can add or remove individual methods, such as \"DEFAULT +ReadLogFile\" or \"ALL -GetEnvironment -GetInstanceProperties -GetSystemInstanceList -ParameterValue -GetProcessList\".<br /><br />CAUTION: This enhanced syntax must be used only as of the specified patch levels. An older sapstartsrv would interpret the list as a simple method list so that the system would no longer protect any of the methods.<br /><br />As of Release 640 patch 337, Release 700 patch 263, Release 701 patch 101, Release 710 patch 208,<br />Release 711 patch 93, and Release 720 patch 42, sapstartsrv also supports the setting \"SDEFAULT\" for \"service/protectedwebmethods\" (default value as of Kernel Release &gt;= 738) and single sign-on based on X.509 certificates (see SAP Note 1439348).</p>\r\n<p>CAUTION: The setting of \"service/protectedwebmethods = NONE\" deactivates the \"OSExecute\" Web method for security reasons. The method \"OSExecute\" also always remains locked for all non-&lt;sid&gt;adm users regardless of the value of \"service/protectedwebmethods\".</p>\r\n<p><strong>Windows</strong></p>\r\n<p>Windows does not require any special authorization for the user/password check, so sapstartsrv can check any user specifications. For entries without user domains, the system searches for a suitable user account in all trusted domains. If you change the Windows-specific DCOM interface to a platform-independent Web service interface, the SAP MMC SnapIn cannot carry out an implicit authentication with the current user. For this reason, the MMC requires you to input a user and password as of Release 7.00 when calling a protected operation.</p>\r\n<p><strong>Unix</strong></p>\r\n<p>Some mechanisms have been introduced on other operating systems that only allow a password check for certain users with root rights (for example, shadow passwd). Sapstartsrv cannot generally verify users of this type. NIS users can be used to avoid the problem, for example. On many UNIX platforms (Linux, Sun, AIX), sapstartsrv also supports authentication using PAM. If configured correctly, this should at least allow you to check your own user (in other words, &lt;sid&gt;adm). Unfortunately, there are problems with the PAM configuration on certain platforms (see the attached platform notes). If a Windows domain user is used for the authentication (&lt;domain&gt;\\&lt;user&gt;), only the user part is used for the authentication on platforms other than Windows.<br /><br />To work around these restrictions on the UNIX platforms, we introduced an authentication in sapstarstrv using the external help program \"sapuxuserchk\" as of 640 patch 222, 700 patch 149, and 710 patch 89. If this is installed with user ID bit for root (s-bit), the restrictions resulting from the use of the &lt;sid&gt;adm user no longer apply. However, the installation or the upgrade and sapcpe cannot set the s-bit automatically. Therefore, this must be performed manually in every executable directory. Unfortunately, due to a kernel update (complete SAPEXE.SAR archive that also contains sapuxuserchk) sapuxuserchk may be overwritten by sapcpe and the s-bit configuration is lost. To set up the s-bit configuration, log on as the root user and navigate to the instance executable directory that contains sapuxuserchk, for example:<br /><br />/usr/sap/BIN/D53/exe:<br /><br />-rwxr-xr-x 1 <USER> <GROUP> 1509173 2008-02-29 08:21 sapuxuserchk<br /><br />Execute the following commands:<br />chown root:sapsys sapuxuserchk<br />chmod u+s,o-rwx sapuxuserchk<br /><br />The authorization should now look, for example, as follows:<br />-rwsr-x--- 1 root sapsys 1509173 2008-02-29 08:21 sapuxuserchk<br /><br />In addition to this, you should check if the following file system is mounted with \"nosuid\":<br />/usr/sap<br /><br />You can check this using the commands \"df\" and \"mount\". For example:<br /><br />hostname:~ # df -h /usr/sap/<br />Filesystem Size Used Avail Use% Mounted on<br />/dev/mapper/vg_sys_r1-usr_x<br /> 9.9G 2.2G 7.3G 23% /usr/sap<br />hostname:~ # mount | grep /dev/mapper/vg_sys_r1-usr_x<br />/dev/mapper/vg_sys_r1-usr_x on /usr/sap type ext3 (rw,nosuid,nodev,noatime,acl,user_xattr)<br /><br />If this is the case, you have to delete the following option in the settings for this entry in the \"mount\":<br />\"nosuid\"<br /><br />Otherwise, this setting prevents evaluation of the \"s-bit\" for sapuxuserchk.<br /><br />As of 640 patch level 392, 700 patch level 330, 701 patch level 170, 710 patch level 262, 711 patch level 149, 720 patch level 113, 800 patch level 46, 802 patch level 24, and 803 patch level 2, sapstartsrv also searches for /usr/sap/hostctrl/exe/sapuxuserchk with an s-bit configuration. In many cases (if an SAP Host Agent is installed), this renders the s-bit configuration described above unnecessary, because the SAP Host Agent installation automatically performs an s-bit configuration of /usr/sap/hostctrl/exe/sapuxuserchk.<br /><br />Note that sapuxuserchk is only contained in the complete SAPEXE.SAR archives (and *.lst sapcpe replication lists contained in these archives), but not in the dw.SAR kernel patch archive.</p>\r\n<p><strong>Trusted connect</strong></p>\r\n<p>In addition, sapstartsrv permits what is known as a \"trusted connect\" using UNIX domain sockets that are already access-protected (/tmp/.sapstream5&lt;NR&gt;13) or Windows named pipes (\\\\.\\pipe\\sapcontrol_&lt;NR&gt;). If you use the options \"-prot NI_HTTP\" or \"- prot PIPE\", sapcontrol can use this connection. If you are able to set the connection up in this way, no further authentication is necessary. This should always be possible if you log on as the user &lt;sid&gt;adm. However, the mechanism is platform-specific and therefore does not work between UNIX and Windows hosts. In addition, you can only use the UNIX domain sockets for communication on your own host.</p>\r\n<p><strong>Trusted connect with system PKI</strong></p>\r\n<p>As of SAP Kernel Release 742, sapstartsrv also automatically initializes a system PKI (see also <a target=\"_blank\" href=\"http://scn.sap.com/community/security/blog/2015/04/04/secure-server-communication-in-sap-netweaver-as-abap\">http://scn.sap.com/community/security/blog/2015/04/04/secure-server-communication-in-sap-netweaver-as-abap</a>), which assigns a sap_system_pki_instance certificate (saved via a PIN in the secure storage) to each instance. Clients that authenticate themselves with a certificate of the system PKI of the system are automatically authorized to execute all Web service methods. With the option \"-systempki &lt;profile&gt;\", sapcontrol offers this option (\"-prot NI_HTTPS\" is implicitly activated). Usage requires access to the central secure storage and sap_system_pki_instance.pse via the configuration of the specified profile.&#x00A0;<br /><br /></p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p>Depending on the platform you use and the problem you are experiencing, carry out the following steps:</p>\r\n<ol>1. Use a user that is correct and that can be verified (for example, Windows domain user, NIS user, not a shadow passwd user).</ol><ol>2. Configure PAM correctly.</ol><ol>3. Configure the s-bit for the utility program \"sapuxuserchk\" or install an SAP Host Agent and a relevant kernel patch to use \"sapuxuserchk\" automatically from the SAP Host Agent.</ol><ol>4. Use the \"trusted connect\" or \"trusted connect with system PKI\" feature in sapcontrol.</ol>\r\n<p style=\"margin-right: 0px;\"><strong>Typical configuration examples</strong></p>\r\n<p style=\"margin-right: 0px;\">You want to authorize a further OS user (for example, \"sapsupport\" for support purposes) to use the protected functions of the Web service interface. If you have not already created the OS user, create it and configure \"service/admin_users=sapsupport\", for example, in the default profile of the system (if you want the user to be authorized in all instances) or in the instance profile of the relevant \"sapstartsrv\" (if you only want the user to be authorized for a certain instance). On Windows systems, you might have to specify the user domain, too, for example \"service/admin_users=GLOBAL\\sapsupport\" or \"service/admin_users=.\\sapsupport\".</p>\r\n<p style=\"margin-right: 0px;\">You want to authorize a local SAP Solution Manager Diagnostics Agent (SMDA) to use the protected functions of the Web service interface. For UNIX systems, first make sure that the prerequisites specified in SAP Note 1932225 have been met. Determine the OS user used by the SMDA (this is the user with which the \"sapstartsrv\" assigned to the SMDA runs), for example, \"daaadm\" (UNIX) or \".\\SAPServiceDAA\" (Windows), and configure this using \"service/admin_users\", for example \"service/admin_users=daaadm\" or \"service/admin_users=.\\SAPServiceDAA\".</p>\r\n<p style=\"margin-right: 0px;\">If you need to authorize several users, separate them by means of a blank character, for example \"service/admin_users=sapsupport daadm\" or \"service/admin_users=GLOBAL\\sapsupport .\\SAPServiceDAA\".</p>\r\n<p style=\"margin-right: 0px;\">To activate the parameter changes, you must then restart the relevant \"sapstartsrv\". This can also happen with the instance running for example with \"sapcontrol -nr &lt;XX&gt; -function RestartService\" or SAP MMC \"All Tasks-&gt;Restart Service\". In the case of highly available instances, you must first ensure that the used HA solution correctly supports a \"sapstartsrv\" restart to prevent, for example, provoking an undesired failover of the instance. If the HA solution does not support this (for example, Microsoft Cluster integration), you must stop the instance - and possibly the entire system - beforehand.</p>\r\n<p style=\"margin-right: 0px;\">You can then check the authentication and authorization using \"sapcontrol -prot GSOAP_HTTP -nr &lt;XX&gt; -queryuser -function AccessCheck Stop\", for example. Following the entry of the user and password, the Web service method \"AccessCheck\" checks whether the specified user can be authenticated and is authorized to call the (protected) Web service method \"Stop\" (without actually executing it).</p>\r\n<p style=\"margin-right: 0px;\">You can also test or use the local trust used by the SMDA using \"RequestLogonFile\" with \"sapcontrol\". On UNIX, note the prerequisites specified in SAP Note 1932225. Log on with the OS user to be checked and execute \"sapcontrol -prot GSOAP_HTTP -nr &lt;XX&gt; -user \"\" \"\" -function AccessCheck Stop\". The specification of an empty user and password causes \"sapcontrol\" to first issue a logon ticket from \"sapstartsrv\" for the current OS user with \"RequestLogonFile\" and then to use this for the call of \"AccessCheck\".</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON><PERSON> (D022091)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON><PERSON> (D040050)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000927637/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000927637/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000927637/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000927637/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000927637/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000927637/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000927637/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000927637/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000927637/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "995116", "RefComponent": "BC-CST-STS", "RefTitle": "Backward porting of sapstartsrv for earlier releases", "RefUrl": "/notes/995116"}, {"RefNumber": "992907", "RefComponent": "BC-OP-SUN", "RefTitle": "sapstartsrv user authentication on Solaris", "RefUrl": "/notes/992907"}, {"RefNumber": "992016", "RefComponent": "BC-TRX", "RefTitle": "TREX 7.0: Cannot start/stop TREX by SAP Management Console", "RefUrl": "/notes/992016"}, {"RefNumber": "958253", "RefComponent": "BC-OP-LNX-SUSE", "RefTitle": "SUSE LINUX Enterprise Server 10: Installation notes", "RefUrl": "/notes/958253"}, {"RefNumber": "953763", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/953763"}, {"RefNumber": "877795", "RefComponent": "BC-CST-STS", "RefTitle": "Problems with sapstartsrv from Release 7.00 and 6.40 patch 169", "RefUrl": "/notes/877795"}, {"RefNumber": "797084", "RefComponent": "BC-OP-LNX-SUSE", "RefTitle": "SUSE LINUX Enterprise Server 9: Installation notes", "RefUrl": "/notes/797084"}, {"RefNumber": "1857007", "RefComponent": "BC-OP-AS4", "RefTitle": "IBM i: sapstartsrv user authentication", "RefUrl": "/notes/1857007"}, {"RefNumber": "1834579", "RefComponent": "BC-DWB-JAV-NMC", "RefTitle": "OS level login dialog appears in SAP MC (Developer Studio)", "RefUrl": "/notes/1834579"}, {"RefNumber": "1798556", "RefComponent": "BC-VCM-LVM", "RefTitle": "SAP Landscape Virtualization Management 2.0 standard edition", "RefUrl": "/notes/1798556"}, {"RefNumber": "1783702", "RefComponent": "BC-VCM-LVM", "RefTitle": "SAP Landscape Virtualization Management 2.0 - Enterprise Edition", "RefUrl": "/notes/1783702"}, {"RefNumber": "1771916", "RefComponent": "SV-SMG-MON-ALR-PRV", "RefTitle": "Metrics collected via SAPStartSrv are grey/wrong", "RefUrl": "/notes/1771916"}, {"RefNumber": "1709155", "RefComponent": "BC-VCM-LVM", "RefTitle": "System Provisioning with SAP Landscape Management", "RefUrl": "/notes/1709155"}, {"RefNumber": "1650797", "RefComponent": "BC-UPG-RDM", "RefTitle": "Prevent overwriting of sapuxuserchk during update/upgrade", "RefUrl": "/notes/1650797"}, {"RefNumber": "1631616", "RefComponent": "BC-JAS-ADM-LOG", "RefTitle": "Log Viewer displays message \"Log files are not loaded yet\"", "RefUrl": "/notes/1631616"}, {"RefNumber": "1630050", "RefComponent": "BC-VCM-LVM", "RefTitle": "SAP NetWeaver Landscape Virtualization Management 1.0, standard edition", "RefUrl": "/notes/1630050"}, {"RefNumber": "1588682", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1588682"}, {"RefNumber": "1565645", "RefComponent": "BC-CST-STS", "RefTitle": "SAP composite note: sapcontrol", "RefUrl": "/notes/1565645"}, {"RefNumber": "1552929", "RefComponent": "XX-INT-SR", "RefTitle": "Collective Security Note for known VulnDisco Vulnerabilities", "RefUrl": "/notes/1552929"}, {"RefNumber": "1527538", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1527538"}, {"RefNumber": "1474866", "RefComponent": "BC-VCM-LVM", "RefTitle": "Instance Agent Security Settings", "RefUrl": "/notes/1474866"}, {"RefNumber": "1462332", "RefComponent": "BC-AC-7XX", "RefTitle": "ACC 7.3 - Adaptive Computing Controller Collect. Note", "RefUrl": "/notes/1462332"}, {"RefNumber": "1439348", "RefComponent": "BC-CST-STS", "RefTitle": "Extended security settings for sapstartsrv", "RefUrl": "/notes/1439348"}, {"RefNumber": "1401712", "RefComponent": "BC-UPG-OCS-SPJ", "RefTitle": "JSPM/SAPJup/SAPehpi/SUM cannot detect all cluster instances", "RefUrl": "/notes/1401712"}, {"RefNumber": "1393207", "RefComponent": "BI-BIP-INS", "RefTitle": "Configuring SAP BusinessObjects Edge 3.1 Monitoring", "RefUrl": "/notes/1393207"}, {"RefNumber": "1375863", "RefComponent": "BC-AC-7XX", "RefTitle": "ACC 7.2 - Adaptive Computing Controller Collect. Note", "RefUrl": "/notes/1375863"}, {"RefNumber": "1330017", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1330017"}, {"RefNumber": "1287407", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1287407"}, {"RefNumber": "1140980", "RefComponent": "BC-OP-AIX", "RefTitle": "sapstartsrv user authentication on AIX", "RefUrl": "/notes/1140980"}, {"RefNumber": "1113545", "RefComponent": "BC-CCM-HAG", "RefTitle": "Problems with SAP Host Agent", "RefUrl": "/notes/1113545"}, {"RefNumber": "1063897", "RefComponent": "BC-OP-HPX", "RefTitle": "sapstartsrv user authentication on HP-UX", "RefUrl": "/notes/1063897"}, {"RefNumber": "1008828", "RefComponent": "BC-AC-7XX", "RefTitle": "ACC 7.1 PI / Adaptive Computing Controller Collective Note", "RefUrl": "/notes/1008828"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "3032711", "RefComponent": "BC-CST-STS", "RefTitle": "Wrong system type in GetSystemInstanceList result", "RefUrl": "/notes/3032711 "}, {"RefNumber": "2099650", "RefComponent": "BC-JAS-ADM-MON", "RefTitle": "The entries of logfiles are not visible in NWA or Visual Administrator Log Viewer", "RefUrl": "/notes/2099650 "}, {"RefNumber": "2878387", "RefComponent": "BC-JAS-ADM-LOG", "RefTitle": "No logs can be displayed in NWA Log Viewer - Cannot retrieve log records from server", "RefUrl": "/notes/2878387 "}, {"RefNumber": "2845135", "RefComponent": "XX-PART-WILY", "RefTitle": "No J2EE Process node available in Introscope Webview", "RefUrl": "/notes/2845135 "}, {"RefNumber": "2840793", "RefComponent": "XX-HST-OPR-SRV-MON", "RefTitle": "Metrics for \"Java Server node status\" do not return values in HEC FRUN", "RefUrl": "/notes/2840793 "}, {"RefNumber": "2819326", "RefComponent": "SV-FRN-INF-SDA", "RefTitle": "Trusted connect with user sapadm to SAPControl webservice on instance <SYSTEM NUMBER> of system (undefined) failed- Simple Diagnostic Agent", "RefUrl": "/notes/2819326 "}, {"RefNumber": "2810705", "RefComponent": "BC-CST-STS", "RefTitle": "RKS: Manual Update Check failure from MMC with Permission denied in AccessCheck", "RefUrl": "/notes/2810705 "}, {"RefNumber": "2284028", "RefComponent": "BC-UPG-TLS-TLA", "RefTitle": "SUM SL Common UI : Troubleshooting problems with the new SUM UI", "RefUrl": "/notes/2284028 "}, {"RefNumber": "2384202", "RefComponent": "BC-CST-STS", "RefTitle": "Wrong result in GetSystemInstanceList", "RefUrl": "/notes/2384202 "}, {"RefNumber": "2428412", "RefComponent": "BC-CST-STS", "RefTitle": "RequestLogonFile -- \"FAIL: Cannot create logon file\" error", "RefUrl": "/notes/2428412 "}, {"RefNumber": "2010805", "RefComponent": "BC-CST-STS", "RefTitle": "SAP MMC common issues", "RefUrl": "/notes/2010805 "}, {"RefNumber": "2544271", "RefComponent": "BC-JAS-ADM-MON", "RefTitle": "NWA \"System Overview\" shows gray lights - WebServiceException - Invalid Response code (401) - com.sun.proxy.$ProxyXXXX.Method", "RefUrl": "/notes/2544271 "}, {"RefNumber": "2506964", "RefComponent": "BC-JAS-ADM-LOG", "RefTitle": "Log Viewer displays error messages", "RefUrl": "/notes/2506964 "}, {"RefNumber": "2598431", "RefComponent": "BC-UPG-TLS-TLJ", "RefTitle": "ServerSOAPFaultException: Client received SOAP Fault from server: Invalid Credentials", "RefUrl": "/notes/2598431 "}, {"RefNumber": "1883695", "RefComponent": "BC-JAS-ADM-ADM", "RefTitle": "Newly added Java server node is not visible in NWA", "RefUrl": "/notes/1883695 "}, {"RefNumber": "2549282", "RefComponent": "HAN-STD-ADM-DBA", "RefTitle": "Error: [SOAP-ENV:Server] Permission denied showed in HANA Studio", "RefUrl": "/notes/2549282 "}, {"RefNumber": "2510439", "RefComponent": "BC-CCM-HAG", "RefTitle": "Error: \"soap_check_permission authentication: ( sapadm, GetComputerSystem ) FAILED [DefaultOpera 163]  Invalid Credentials pam_authenticate ( sapadm ) failed : Permission denied\" While Sap Host Agent is not connect - Solution manager 7.2", "RefUrl": "/notes/2510439 "}, {"RefNumber": "2348537", "RefComponent": "SV-SMG-MON-ALR", "RefTitle": "How to troubleshoot error \"cannot connect to service sapstartsrv\" in Solution manager 7.2", "RefUrl": "/notes/2348537 "}, {"RefNumber": "2020318", "RefComponent": "BC-JAS-ADM-ADM", "RefTitle": "No Java processes in the NWA under Start&Stop", "RefUrl": "/notes/2020318 "}, {"RefNumber": "2432795", "RefComponent": "BC-CCM-HAG", "RefTitle": "sapuxuserchk locks <sid>adm or sapadm user", "RefUrl": "/notes/2432795 "}, {"RefNumber": "2946300", "RefComponent": "HAN-DB", "RefTitle": "AccessCheck request failed: Invalid Credentials", "RefUrl": "/notes/2946300 "}, {"RefNumber": "2559588", "RefComponent": "HAN-DB", "RefTitle": "Increasing the security level of sapstartsrv within SAP HANA Database and corresponding tool settings that need to be adapted", "RefUrl": "/notes/2559588 "}, {"RefNumber": "2417690", "RefComponent": "BC-CST-WDP", "RefTitle": "ICM/Web Dispatcher - Issues in OS user authentication", "RefUrl": "/notes/2417690 "}, {"RefNumber": "2309137", "RefComponent": "SV-SMG-INS-DIA", "RefTitle": "SRSM: Collective Note to implement on managed Java systems for Simple Run Solution Manager", "RefUrl": "/notes/2309137 "}, {"RefNumber": "2309076", "RefComponent": "SV-SMG-INS-DIA", "RefTitle": "SRSM: Collective Note to implement on managed ABAP systems for Simple Run Solution Manager", "RefUrl": "/notes/2309076 "}, {"RefNumber": "2246609", "RefComponent": "BC-JAS-ADM-LOG", "RefTitle": "The logs from an instance are missing in the Log Viewer", "RefUrl": "/notes/2246609 "}, {"RefNumber": "2021738", "RefComponent": "BC-CCM-MON-SLG", "RefTitle": "SM21: gSOAP popup appears that request user and password", "RefUrl": "/notes/2021738 "}, {"RefNumber": "2045796", "RefComponent": "SV-SMG-ADM-CNT", "RefTitle": "Guided Procedure Plugin - Java Rolling Restart", "RefUrl": "/notes/2045796 "}, {"RefNumber": "2039994", "RefComponent": "BC-VCM-LVM", "RefTitle": "Managing system landscapes with SAP Landscape Management Standard Edition", "RefUrl": "/notes/2039994 "}, {"RefNumber": "2039615", "RefComponent": "BC-VCM-LVM", "RefTitle": "Managing system landscapes with SAP Landscape Management Enterprise Edition", "RefUrl": "/notes/2039615 "}, {"RefNumber": "1113545", "RefComponent": "BC-CCM-HAG", "RefTitle": "Problems with SAP Host Agent", "RefUrl": "/notes/1113545 "}, {"RefNumber": "1924710", "RefComponent": "BC-JAS-ADM-MON", "RefTitle": "AS Java System overview shows incorrect number of started nodes - 0 of 0", "RefUrl": "/notes/1924710 "}, {"RefNumber": "1771916", "RefComponent": "SV-SMG-MON-ALR-PRV", "RefTitle": "Metrics collected via SAPStartSrv are grey/wrong", "RefUrl": "/notes/1771916 "}, {"RefNumber": "1709155", "RefComponent": "BC-VCM-LVM", "RefTitle": "System Provisioning with SAP Landscape Management", "RefUrl": "/notes/1709155 "}, {"RefNumber": "1630050", "RefComponent": "BC-VCM-LVM", "RefTitle": "SAP NetWeaver Landscape Virtualization Management 1.0, standard edition", "RefUrl": "/notes/1630050 "}, {"RefNumber": "953763", "RefComponent": "BC-INS-JCI", "RefTitle": "OBSOLETE: Installation of SAP NetWeaver CE 7.1", "RefUrl": "/notes/953763 "}, {"RefNumber": "877795", "RefComponent": "BC-CST-STS", "RefTitle": "Problems with sapstartsrv from Release 7.00 and 6.40 patch 169", "RefUrl": "/notes/877795 "}, {"RefNumber": "1401712", "RefComponent": "BC-UPG-OCS-SPJ", "RefTitle": "JSPM/SAPJup/SAPehpi/SUM cannot detect all cluster instances", "RefUrl": "/notes/1401712 "}, {"RefNumber": "1857007", "RefComponent": "BC-OP-AS4", "RefTitle": "IBM i: sapstartsrv user authentication", "RefUrl": "/notes/1857007 "}, {"RefNumber": "958253", "RefComponent": "BC-OP-LNX-SUSE", "RefTitle": "SUSE LINUX Enterprise Server 10: Installation notes", "RefUrl": "/notes/958253 "}, {"RefNumber": "1631616", "RefComponent": "BC-JAS-ADM-LOG", "RefTitle": "Log Viewer displays message \"Log files are not loaded yet\"", "RefUrl": "/notes/1631616 "}, {"RefNumber": "1834579", "RefComponent": "BC-DWB-JAV-NMC", "RefTitle": "OS level login dialog appears in SAP MC (Developer Studio)", "RefUrl": "/notes/1834579 "}, {"RefNumber": "1375863", "RefComponent": "BC-AC-7XX", "RefTitle": "ACC 7.2 - Adaptive Computing Controller Collect. Note", "RefUrl": "/notes/1375863 "}, {"RefNumber": "1650797", "RefComponent": "BC-UPG-RDM", "RefTitle": "Prevent overwriting of sapuxuserchk during update/upgrade", "RefUrl": "/notes/1650797 "}, {"RefNumber": "1462332", "RefComponent": "BC-AC-7XX", "RefTitle": "ACC 7.3 - Adaptive Computing Controller Collect. Note", "RefUrl": "/notes/1462332 "}, {"RefNumber": "1474866", "RefComponent": "BC-VCM-LVM", "RefTitle": "Instance Agent Security Settings", "RefUrl": "/notes/1474866 "}, {"RefNumber": "1600846", "RefComponent": "BC-UPG-OCS-SPJ", "RefTitle": "SUM calls sapcontrol without user credentials", "RefUrl": "/notes/1600846 "}, {"RefNumber": "995116", "RefComponent": "BC-CST-STS", "RefTitle": "Backward porting of sapstartsrv for earlier releases", "RefUrl": "/notes/995116 "}, {"RefNumber": "1063897", "RefComponent": "BC-OP-HPX", "RefTitle": "sapstartsrv user authentication on HP-UX", "RefUrl": "/notes/1063897 "}, {"RefNumber": "1588682", "RefComponent": "BC-JAS-COR", "RefTitle": "version shell script in ./bootstrap/scripts return error", "RefUrl": "/notes/1588682 "}, {"RefNumber": "1140980", "RefComponent": "BC-OP-AIX", "RefTitle": "sapstartsrv user authentication on AIX", "RefUrl": "/notes/1140980 "}, {"RefNumber": "1393207", "RefComponent": "BI-BIP-INS", "RefTitle": "Configuring SAP BusinessObjects Edge 3.1 Monitoring", "RefUrl": "/notes/1393207 "}, {"RefNumber": "797084", "RefComponent": "BC-OP-LNX-SUSE", "RefTitle": "SUSE LINUX Enterprise Server 9: Installation notes", "RefUrl": "/notes/797084 "}, {"RefNumber": "1565645", "RefComponent": "BC-CST-STS", "RefTitle": "SAP composite note: sapcontrol", "RefUrl": "/notes/1565645 "}, {"RefNumber": "992016", "RefComponent": "BC-TRX", "RefTitle": "TREX 7.0: Cannot start/stop TREX by SAP Management Console", "RefUrl": "/notes/992016 "}, {"RefNumber": "1008828", "RefComponent": "BC-AC-7XX", "RefTitle": "ACC 7.1 PI / Adaptive Computing Controller Collective Note", "RefUrl": "/notes/1008828 "}, {"RefNumber": "1249503", "RefComponent": "MDM-FN", "RefTitle": "Further Notes about Installation of MDM 7.1", "RefUrl": "/notes/1249503 "}, {"RefNumber": "992907", "RefComponent": "BC-OP-SUN", "RefTitle": "sapstartsrv user authentication on Solaris", "RefUrl": "/notes/992907 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_BASIS", "From": "640", "To": "640", "Subsequent": ""}, {"SoftwareComponent": "SAP_BASIS", "From": "700", "To": "702", "Subsequent": ""}, {"SoftwareComponent": "SAP_BASIS", "From": "710", "To": "730", "Subsequent": ""}, {"SoftwareComponent": "SAP_BASIS", "From": "731", "To": "731", "Subsequent": "X"}, {"SoftwareComponent": "SAP_BASIS", "From": "72L", "To": "804", "Subsequent": "X"}, {"SoftwareComponent": "SAP_BASIS", "From": "740", "To": "740", "Subsequent": "X"}, {"SoftwareComponent": "SAP_BASIS", "From": "DEV", "To": "DEV", "Subsequent": "X"}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "SAP KERNEL 8.03 64-BIT UNICODE", "SupportPackage": "SP002", "SupportPackagePatch": "000002", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200314690200016159&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 8.02 64-BIT UNICODE", "SupportPackage": "SP024", "SupportPackagePatch": "000024", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200314690200015012&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.20 32-BIT", "SupportPackage": "SP114", "SupportPackagePatch": "000114", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200615320200013053&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.20 32-BIT UNICODE", "SupportPackage": "SP114", "SupportPackagePatch": "000114", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200615320200013054&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.20 64-BIT", "SupportPackage": "SP114", "SupportPackagePatch": "000114", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200615320200013055&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.20 64-BIT UNICODE", "SupportPackage": "SP114", "SupportPackagePatch": "000114", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200615320200013056&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.20 32-BIT", "SupportPackage": "SP113", "SupportPackagePatch": "000113", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200615320200013053&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 8.00 64-BIT UNICODE", "SupportPackage": "SP046", "SupportPackagePatch": "000046", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200615320200017704&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.00 32-BIT", "SupportPackage": "SP330", "SupportPackagePatch": "000330", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200314690200004059&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.10 32-BIT", "SupportPackage": "SP262", "SupportPackagePatch": "000262", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200314690200004834&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.10 64-BIT UNICODE", "SupportPackage": "SP262", "SupportPackagePatch": "000262", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200314690200004838&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.10 64-BIT", "SupportPackage": "SP262", "SupportPackagePatch": "000262", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200314690200004839&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.10 32-BIT UNICODE", "SupportPackage": "SP262", "SupportPackagePatch": "000262", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200314690200004840&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.01 32-BIT", "SupportPackage": "SP171", "SupportPackagePatch": "000171", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200615320200011145&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.01 32-BIT UNICODE", "SupportPackage": "SP171", "SupportPackagePatch": "000171", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200615320200011146&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.01 64-BIT", "SupportPackage": "SP171", "SupportPackagePatch": "000171", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200615320200011147&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.01 64-BIT UNICODE", "SupportPackage": "SP171", "SupportPackagePatch": "000171", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200615320200011148&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.11 32-BIT", "SupportPackage": "SP149", "SupportPackagePatch": "000149", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200615320200011149&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.11 32-BIT UNICODE", "SupportPackage": "SP149", "SupportPackagePatch": "000149", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200615320200011150&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.11 64-BIT", "SupportPackage": "SP149", "SupportPackagePatch": "000149", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200615320200011151&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.11 64-BIT UNICODE", "SupportPackage": "SP149", "SupportPackagePatch": "000149", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200615320200011152&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 6.40 32-BIT UNICODE", "SupportPackage": "SP392", "SupportPackagePatch": "000392", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200314690200004051&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 6.40 64-BIT UNICODE", "SupportPackage": "SP392", "SupportPackagePatch": "000392", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200314690200004052&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 6.40 32-BIT", "SupportPackage": "SP392", "SupportPackagePatch": "000392", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200615320200006931&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 6.40 64-BIT", "SupportPackage": "SP392", "SupportPackagePatch": "000392", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200615320200006932&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 6.40_EX2 32-BIT", "SupportPackage": "SP392", "SupportPackagePatch": "000392", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200615320200009586&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 6.40_EX2 32-BIT UC", "SupportPackage": "SP392", "SupportPackagePatch": "000392", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200615320200009588&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 6.40_EX2 64-BIT", "SupportPackage": "SP392", "SupportPackagePatch": "000392", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200615320200009590&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 6.40_EX2 64-BIT UC", "SupportPackage": "SP392", "SupportPackagePatch": "000392", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200615320200009591&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.01 32-BIT", "SupportPackage": "SP170", "SupportPackagePatch": "000170", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200615320200011145&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.01 32-BIT UNICODE", "SupportPackage": "SP170", "SupportPackagePatch": "000170", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200615320200011146&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.01 64-BIT", "SupportPackage": "SP170", "SupportPackagePatch": "000170", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200615320200011147&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.01 64-BIT UNICODE", "SupportPackage": "SP170", "SupportPackagePatch": "000170", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200615320200011148&V=MAINT"}]}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}