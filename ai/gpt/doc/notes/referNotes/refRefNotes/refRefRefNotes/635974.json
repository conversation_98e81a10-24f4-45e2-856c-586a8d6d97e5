{"Request": {"Number": "635974", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 386, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000015699302017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000635974?language=E&token=8E343C524E765991436ACF486D9084B9"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000635974", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000635974/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "635974"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 5}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Upgrade information"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "22.11.2005"}, "SAPComponentKey": {"_label": "Component", "value": "BC-DB-ORA"}, "SAPComponentKeyText": {"_label": "Component", "value": "Oracle"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Basis Components", "value": "BC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Database Interface, Database Platforms", "value": "BC-DB", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-DB*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Oracle", "value": "BC-DB-ORA", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-DB-ORA*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "635974 - Error when upgrading to Oracle 9.2.X - Windows"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>DUA hangs and an error is returned when you are upgrading to Oracle 9.2.</p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>DBMA<br />ODMA<br />*******.0<br />*******<br />*******<br />Windows<br />NT</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>There are various why the tool could hang. Therefore, check the following first:</p> <OL>1. Error in the DUA log file<br />The log file is created on the disk drive where the 9.2 ORACLE_HOME is located. The new subdirectory &lt;drive&gt;:\\oracle\\admin\\&lt;SID&gt;\\&lt;number&gt; is created each time you start DUA.</OL> <UL><LI>&lt;drive&gt;:\\oracle\\admin\\&lt;SID&gt;\\1 for the first attempt</LI></UL> <UL><LI>&lt;drive&gt;:\\oracle\\admin\\&lt;SID&gt;\\11 for the second attempt</LI></UL> <UL><LI>&lt;drive&gt;:\\oracle\\admin\\&lt;SID&gt;\\12 for the third attempt.<br />An 'upgrade' subdirectory is located in the corresponding directory that contains the upgrade log files.</LI></UL><OL>2. Error in the oracle &lt;SID&gt;alrt.log<br />In addition to the normal &lt;SID&gt;alrt.log in the directory &lt;drive&gt;:\\oracle\\&lt;SID&gt;\\saptrace\\background, &lt;SID&gt;alrt.logfiles may have been created in the following directories:</OL> <UL><LI>9.2 ORACLE_HOME\\rdbms\\trace</LI></UL> <UL><LI>8.1.7 ORACLE_HOME\\rdbms\\trace</LI></UL> <UL><LI>&lt;drive&gt;:\\oracle\\admin\\&lt;SID&gt;\\&lt;number&gt;\\bdump</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Check to what extent a &lt;SID&gt;alrt. log file is contained with a new time stamp in all four directories. In the latest file, or the file that is close to a time stamp for which the upgrade was made, you should check the following: <UL><LI>Were errors reported when you tried to upgrade your system?</LI></UL> <UL><LI>Was the database already started with Oracle 9 software during the upgrade?<br />The entry 'Starting up ORACLE RDBMS Version: 9.2.0.X.X' would indicate that the system has already been started with 9.2.</LI></UL> <UL><LI>Was a startup migrate carried out?<br />DUA first starts the database with 'startup migrate' to upgrade the Oracle data dictionary. If this was executed successfully, the system is upgraded successfully.</LI></UL> <OL>3. Error because ORACLE_HOME is still set<br />This usually results in the service being created with oradim again, but as an 8.1.7 service rather than as an 9.2 service.<br />This is most likely the case if</OL> <UL><LI>DUA was started in oracle-alert&lt;SID&gt;.log but a startup with Oracle 9.2 software could not be identified</LI></UL> <UL><LI>There is an 817_OBSOLETE service</LI></UL> <UL><LI>The key imagepath in the registry in hkey_local_machine\\service\\currentcontrolset\\Oracleservice&lt;SID&gt; refers to the Oracle executable in the old 8.1.7 Oracle home.</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Unfortunately, it is usually not easy to find out where ORACLE_HOME is still set. Always check the following and, if necessary, delete ORACLE_HOME if it is still set here. <UL><LI>My Computer -&gt; Properties -&gt; Advanced -&gt; Environment Variables - check to what extent oracle home is set both in the system and in the user variables for &lt;SID&gt;adm.</LI></UL> <UL><LI>My Computer -&gt; Manage -&gt; Services and Applications -&gt; Services - check which user starts oracleservice&lt;SID&gt;. Make sure that Oracle home is deleted in the user environment for this user. If necessary, log on at operating system level with this user to ensure that this is the case.</LI></UL> <UL><LI>In the registry under hkey_local_machine\\software\\sap\\&lt;SID&gt;</LI></UL> <UL><LI></LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;After you have successfully deleted Oracle home, start the server again, because only this will guarantee that all references to the ORACLE_HOME variables set previously no longer exist.<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>If none of the options mentioned above is successful, you can also upgrade the system manually (without the upgrade assistants).<br />To do this, you must carry out the following steps:</p> <OL>1. Make sure that the environment variable ORACLE_HOME is no longer set (see point 3 above).</OL> <OL>2. Check to what extent the service ORACLESERVICE&lt;SID&gt; is started with a certain user:<br />My Computer -&gt; Manage -&gt; Services and Applications -&gt; Services -&gt; oracleservice&lt;SID&gt; -&gt; Properties -&gt; Log On</OL> <OL>3. Open a command window and set the following environment variables:<br />set ORACLE_HOME=&lt;path for 8.1.7 Oracle software&gt;<br />set PATH=&lt;path for 8.1.7 Oracle software&gt;\\bin;%PATH%</OL> <OL>4. Stop the Oracle services<br />net stop oracleservice&lt;SID&gt;</OL> <OL>5. Delete the Services<br />oradim -delete -SID &lt;SID&gt;</OL> <OL>6. Close this command window to avoid subsequent errors.</OL> <OL>7. In a new command window, create the service again<br />oradim -new -SID &lt;SID&gt;</OL> <OL>8. In the registry, verify that the service has now been created as an Oracle 9 service:<br />regedit<br />hkey_local_machine\\system\\currentcontrolset\\services\\oracleservice&lt;SID&gt;<br />Key imagepath should refer to the 9.20 Oracle executable.</OL> <OL>9. If Oracle 8.1.7 was started with a certain user (see test under item 5), you should now change the service back to this user:<br />My Computer -&gt; Manage -&gt; Services and Applications -&gt; Services -&gt; oracleservice&lt;SID&gt; -&gt; Properties -&gt; Log On<br />Specify the user name and password of the user under 'This account'.</OL> <OL>10. Stop and restart OracleService&lt;SID&gt;.</OL> <OL>11. Copy the initialization files from the old ORACLE_HOME into the new ORACLE_HOME<br />These consist of the following files:</OL> <UL><LI>init&lt;SID&gt;.ora</LI></UL> <UL><LI>init&lt;SID&gt;.dba</LI></UL> <UL><LI>init&lt;SID&gt;.sap</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;They are in the directory &lt; old ORACLE_HOME&gt;\\database and must be copied into the directory &lt;new ORACLE_HOME&gt;\\database. <OL>12. Check the file init&lt;SID&gt;.ora in the new ORCLE_HOME<br />Some parameters are no longer supported with 9.20. For more information, read Note 667236. Delete this parameter from the file init&lt;SID&gt;.ora.</OL><OL>13. Copy the network configuration files.</OL> <OL>14. These consist of the following files:</OL> <UL><LI>sqlnet.ora</LI></UL> <UL><LI>tnsnames.ora</LI></UL> <UL><LI>listener.ora</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;You must copy these from the directory &lt;old ORACLE_HOME\\network\\admin into the directory &lt;new ORACLE_HOME&gt;\\network\\admin. <OL>15. Update the Oracle data dictionaries<br />In a command window, switch to the directory &lt;new ORACLE_HOME&gt;\\admin.<br />sqlplus \"/ as sysdba\"<br />SQL&gt; startup migrate;<br />SQL&gt; spool upgrade_920.txt<br />SQL&gt; @U0801070.sql</OL> <OL>16. Check the file upgrade_920.txt for errors. Unfortunately, this is very comprehensive and contains a lot of error messages that can be ignored.<br />The end of Note 582427 contains the list of error messages that can be ignored.<br />However, if errors that are not noted here do occur, create a problem message with the error text (including the object for which the error occurred) so that you can check to what extent you can ignore the error.</OL> <OL>17. You can now stop and restart the database. Now continue implementing the actions in the installation/migration instructions to be executed after you start the DUA.<br />(Section 2, post-upgrade activities).</OL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Note that you can skip the step for creating a PFILE from the SPFILE if no SPFILE was created due to the manual upgrade.</div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON> (I800932)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (C5050086)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "J", "TranslationLanguage": "日本語", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000635974/J"}, {"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000635974/D"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000635974/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000635974/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000635974/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000635974/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000635974/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000635974/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000635974/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "741361", "RefComponent": "BC-DB-ORA", "RefTitle": "Problem when installing Oracle patch set 9.2.0.x on Windows", "RefUrl": "/notes/741361"}, {"RefNumber": "667236", "RefComponent": "BW-SYS-DB", "RefTitle": "Obsolete parameters in Oracle 9.2.0.*", "RefUrl": "/notes/667236"}, {"RefNumber": "632556", "RefComponent": "BW-SYS-DB", "RefTitle": "Oracle 9.2.0.* database parameterization for BW", "RefUrl": "/notes/632556"}, {"RefNumber": "592393", "RefComponent": "BC-DB-ORA", "RefTitle": "FAQ: Oracle", "RefUrl": "/notes/592393"}, {"RefNumber": "582427", "RefComponent": "BC-DB-ORA", "RefTitle": "Error due to inconsistent Oracle DDIC", "RefUrl": "/notes/582427"}, {"RefNumber": "539970", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/539970"}, {"RefNumber": "499663", "RefComponent": "BC-DB-ORA", "RefTitle": "Manually upgrading the Oracle database", "RefUrl": "/notes/499663"}, {"RefNumber": "124361", "RefComponent": "BC-DB-ORA", "RefTitle": "Oracle parameterization (R/3 >= 4.x, Oracle 8.x/9.x)", "RefUrl": "/notes/124361"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "592393", "RefComponent": "BC-DB-ORA", "RefTitle": "FAQ: Oracle", "RefUrl": "/notes/592393 "}, {"RefNumber": "741361", "RefComponent": "BC-DB-ORA", "RefTitle": "Problem when installing Oracle patch set 9.2.0.x on Windows", "RefUrl": "/notes/741361 "}, {"RefNumber": "124361", "RefComponent": "BC-DB-ORA", "RefTitle": "Oracle parameterization (R/3 >= 4.x, Oracle 8.x/9.x)", "RefUrl": "/notes/124361 "}, {"RefNumber": "632556", "RefComponent": "BW-SYS-DB", "RefTitle": "Oracle 9.2.0.* database parameterization for BW", "RefUrl": "/notes/632556 "}, {"RefNumber": "582427", "RefComponent": "BC-DB-ORA", "RefTitle": "Error due to inconsistent Oracle DDIC", "RefUrl": "/notes/582427 "}, {"RefNumber": "667236", "RefComponent": "BW-SYS-DB", "RefTitle": "Obsolete parameters in Oracle 9.2.0.*", "RefUrl": "/notes/667236 "}, {"RefNumber": "499663", "RefComponent": "BC-DB-ORA", "RefTitle": "Manually upgrading the Oracle database", "RefUrl": "/notes/499663 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": []}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}