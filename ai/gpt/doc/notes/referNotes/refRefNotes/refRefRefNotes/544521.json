{"Request": {"Number": "544521", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 397, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000015266242017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000544521?language=E&token=D3651A106E258CAB6D91994298CEF62B"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000544521", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000544521/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "544521"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 6}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Customizing"}, "Priority": {"_label": "Priority", "value": "Correction with medium priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "14.07.2011"}, "SAPComponentKey": {"_label": "Component", "value": "BW-SYS-DB-ORA"}, "SAPComponentKeyText": {"_label": "Component", "value": "BW ORACLE"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "SAP Business Warehouse", "value": "BW", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Basis System and Installation", "value": "BW-SYS", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW-SYS*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "BW Database Platforms", "value": "BW-SYS-DB", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW-SYS-DB*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "BW ORACLE", "value": "BW-SYS-DB-ORA", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW-SYS-DB-ORA*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "544521 - Controlling the PARALLEL degree"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>For the aggregate structure, a PARALLEL(F,DEFAULT) optimizer hint is generated into the SELECT statement by default.&#x00A0;&#x00A0;Depending on the Oracle database configuration, this may result in the aggregate structure occupying all or most of the CPUs, so that almost no resources are available for other processes.<br />This also applies to the parallel creation of the secondary indexes of the fact tables. Up to now, the clause PARALLEL (DEGREE DEFAULT) is attached here.<br />This also applies when the data is activated in the ODS object. A FETCH whose SELECT is also provided with a hint is executed on the activation queue here.<br /></p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>Business Information Warehouse, BW, Oracle, PARALLEL, degree, aggregate, indexes, fact tables, ODS object, activation, ORA_PARALLEL_DEGREE, ORA_PARALLEL_DEG, RSADMINC, RSADMIN, RSCUSTV26<br /></p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p></p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>In the table RSADMIN, set the parameter ORA_PARALLEL_DEGREE (using the report SAP_RSADMIN_MAINTAIN). The following values are valid here:</p> <UL><UL><LI>0: DEFAULT; Oracle determines the PARALLEL degree</LI></UL></UL> <UL><UL><LI>1: No PARALLEL degree; the hint is omitted</LI></UL></UL> <UL><UL><LI>2, 3, 4, ...: Value of the PARALLEL degree</LI></UL></UL> <p>This function is available only as of the Support Packages specified below.&#x00A0;&#x00A0;The parameter influences the index creation only as of Support Packages BW 2.0B Support Package 29, BW 2.1C Support Package 21, BW 3.0B Support Package 9.<br />When requests are activated in the ODS object, this parameter is taken into account as of Support Package 15 BW 3.0B or Support Package 09 BW 3.1C.<br /></p> <UL><LI>BW 3.0A</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Import Support Package 12 for 3.0A (BW 3.0A Patch 12 or <B>SAPKW30A12</B>) into your BW system. The Support Package is available when <B>Note 523217</B> with the short text, \"SAPBWNews BW 3.0A Support Package 12\", which describes this Support Package in more detail, is released for customers.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;To provide information in advance, Note 523217 may already be available before the Support Package is released. In this case, the short text of the note contains the words \"Preliminary version\".</p> <UL><LI>BW 3.0B</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Import Support Package 05 for 3.0B (BW 3.0B Patch 05 or <B>SAPKW30B05</B>) into your BW system. This Support Package will be available once <B>Note 493976</B> \"SAPBWNews BW 3.0B Support Package 5\", which describes this Support Package in more detail, has been released for customers.</p> <UL><LI>BW 2.0B</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Import Support Package 28 for 2.0B (BW 2.0B Patch 28 or <B>SAPKW20B28</B>) into your BW system. The Support Package is available once <B>Note 523185</B> \"SAPBWNews BW 2.0B Support Package 28\", which describes this Support Package in more detail, has been released for customers.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;To provide information in advance, Note 523185 may already be available before the Support Package is released. In this case, the short text of the note contains the words \"Preliminary version\".</p> <UL><LI>BW 2.1C</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Import Support Package 20 for 2.1C (BW 2.1C Patch 20 or <B>SAPKW21C20</B>) into your BW system. The Support Package is available once <B>Note 523186</B> \"SAPBWNews BW 2.1C Support Package 20\" is released for customers.<br /><br />For further information, see Note 110934, which contains information about BW Support Packages.<br /><br />For further information, see Note 110934, which contains information about BW Support Packages.<br /><br />The setting via the table RSADMIN is valid only for Releases BW 2.x and BW 3.x. For later releases, you can make the setting using the Implementation Guide (IMG) (transaction SPRO or RSCUSTV26) so that the parameter ORA_PARALLEL_DEG is set in the table RSADMINC.<br />An automatic conversion from the old (RSADMIN - ORA_PARALELL_DEGREE) parameter to the new parameter is not carried out.<br /><br /> <br /></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "BW-SYS-DB (BW Database Platforms)"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D025899)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON> (D032185)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000544521/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000544521/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000544521/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000544521/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000544521/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000544521/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000544521/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000544521/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000544521/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "957153", "RefComponent": "BW-WHM-DBA-MD", "RefTitle": "BW 3.50 (SP 18): Delete master data terminates w/ TIME_OUT", "RefUrl": "/notes/957153"}, {"RefNumber": "832728", "RefComponent": "BW-WHM-DBA-DSO", "RefTitle": "ODS object: Error in RSADMIN value ORA_PARALLEL_DEGREE = 1", "RefUrl": "/notes/832728"}, {"RefNumber": "651060", "RefComponent": "BC-DB-ORA", "RefTitle": "FAQ: Oracle Parallel Execution", "RefUrl": "/notes/651060"}, {"RefNumber": "629987", "RefComponent": "BW-WHM-DBA-DSO", "RefTitle": "ODS object, ORACLE: Parallel processing during fetch", "RefUrl": "/notes/629987"}, {"RefNumber": "601028", "RefComponent": "BW", "RefTitle": "SAPBWNews BW 2.1C Support Package 29", "RefUrl": "/notes/601028"}, {"RefNumber": "571720", "RefComponent": "BW", "RefTitle": "SAPBWNews BW 3.1 Content Support Package 09", "RefUrl": "/notes/571720"}, {"RefNumber": "571698", "RefComponent": "BW", "RefTitle": "SAPBWNews BW 3.0B Support Package 15", "RefUrl": "/notes/571698"}, {"RefNumber": "567745", "RefComponent": "BW", "RefTitle": "Composite note BW 3.x performance: DB-specific setting", "RefUrl": "/notes/567745"}, {"RefNumber": "523249", "RefComponent": "BW", "RefTitle": "SAPBWNews BW 3.0B Support Package 12", "RefUrl": "/notes/523249"}, {"RefNumber": "523217", "RefComponent": "BW", "RefTitle": "SAPBWNews for BW 3.0A Support Package 12", "RefUrl": "/notes/523217"}, {"RefNumber": "523186", "RefComponent": "BW", "RefTitle": "SAPBWNews BW 2.1C Support Package 20", "RefUrl": "/notes/523186"}, {"RefNumber": "523185", "RefComponent": "BW", "RefTitle": "SAPBWNews BW 2.0B Support Package 28", "RefUrl": "/notes/523185"}, {"RefNumber": "493976", "RefComponent": "BW", "RefTitle": "SAPBWNews BW 3.0B Support Package 05", "RefUrl": "/notes/493976"}, {"RefNumber": "1013912", "RefComponent": "BC-DB-ORA", "RefTitle": "FAQ: Oracle BW performance", "RefUrl": "/notes/1013912"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1013912", "RefComponent": "BC-DB-ORA", "RefTitle": "FAQ: Oracle BW performance", "RefUrl": "/notes/1013912 "}, {"RefNumber": "957153", "RefComponent": "BW-WHM-DBA-MD", "RefTitle": "BW 3.50 (SP 18): Delete master data terminates w/ TIME_OUT", "RefUrl": "/notes/957153 "}, {"RefNumber": "493976", "RefComponent": "BW", "RefTitle": "SAPBWNews BW 3.0B Support Package 05", "RefUrl": "/notes/493976 "}, {"RefNumber": "523217", "RefComponent": "BW", "RefTitle": "SAPBWNews for BW 3.0A Support Package 12", "RefUrl": "/notes/523217 "}, {"RefNumber": "523249", "RefComponent": "BW", "RefTitle": "SAPBWNews BW 3.0B Support Package 12", "RefUrl": "/notes/523249 "}, {"RefNumber": "651060", "RefComponent": "BC-DB-ORA", "RefTitle": "FAQ: Oracle Parallel Execution", "RefUrl": "/notes/651060 "}, {"RefNumber": "523186", "RefComponent": "BW", "RefTitle": "SAPBWNews BW 2.1C Support Package 20", "RefUrl": "/notes/523186 "}, {"RefNumber": "601028", "RefComponent": "BW", "RefTitle": "SAPBWNews BW 2.1C Support Package 29", "RefUrl": "/notes/601028 "}, {"RefNumber": "832728", "RefComponent": "BW-WHM-DBA-DSO", "RefTitle": "ODS object: Error in RSADMIN value ORA_PARALLEL_DEGREE = 1", "RefUrl": "/notes/832728 "}, {"RefNumber": "567745", "RefComponent": "BW", "RefTitle": "Composite note BW 3.x performance: DB-specific setting", "RefUrl": "/notes/567745 "}, {"RefNumber": "571720", "RefComponent": "BW", "RefTitle": "SAPBWNews BW 3.1 Content Support Package 09", "RefUrl": "/notes/571720 "}, {"RefNumber": "571698", "RefComponent": "BW", "RefTitle": "SAPBWNews BW 3.0B Support Package 15", "RefUrl": "/notes/571698 "}, {"RefNumber": "629987", "RefComponent": "BW-WHM-DBA-DSO", "RefTitle": "ODS object, ORACLE: Parallel processing during fetch", "RefUrl": "/notes/629987 "}, {"RefNumber": "523185", "RefComponent": "BW", "RefTitle": "SAPBWNews BW 2.0B Support Package 28", "RefUrl": "/notes/523185 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_BW", "From": "20B", "To": "20B", "Subsequent": "X"}, {"SoftwareComponent": "SAP_BW", "From": "21C", "To": "21C", "Subsequent": "X"}, {"SoftwareComponent": "SAP_BW", "From": "30A", "To": "30B", "Subsequent": "X"}, {"SoftwareComponent": "SAP_BW", "From": "310", "To": "310", "Subsequent": "X"}, {"SoftwareComponent": "SAP_BW", "From": "350", "To": "350", "Subsequent": "X"}, {"SoftwareComponent": "SAP_BW", "From": "700", "To": "700", "Subsequent": "X"}, {"SoftwareComponent": "SAP_BW", "From": "710", "To": "710", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "SAP_BW 20B", "SupportPackage": "SAPKW20B28", "URL": "/supportpackage/SAPKW20B28"}, {"SoftwareComponentVersion": "SAP_BW 30A", "SupportPackage": "SAPKW30A12", "URL": "/supportpackage/SAPKW30A12"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}