{"Request": {"Number": "353255", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 388, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000001469022017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000353255?language=E&token=8AEA6DDBE9B4F63196AAE6F2DDC2F281"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000353255", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000353255/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "353255"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 12}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Advance development"}, "Priority": {"_label": "Priority", "value": "Correction with medium priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "31.07.2017"}, "SAPComponentKey": {"_label": "Component", "value": "PS-IS-LOG"}, "SAPComponentKeyText": {"_label": "Component", "value": "Logistics"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Project System", "value": "PS", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'PS*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Information System", "value": "PS-IS", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'PS-IS*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Logistics", "value": "PS-IS-LOG", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'PS-IS-LOG*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "353255 - New development - individual overviews"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p><br />The new individual overviews announced in SAP Note 326591 are provided with this note. SAP AG supports the individual overviews provided with this SAP Note as well as SAP Note 326591, whereby the following application scenarios are distinguished:</p>\r\n<ul>\r\n<li>For the new individual overviews, particular attention was paid to simple and intuitive usability. The transactions have the essential functions and are addressed to occasional users.</li>\r\n</ul>\r\n<ul>\r\n<li>The classic individual overviews are intended for the power user who uses these transactions frequently and requires the full range of functions. For an implicit call of the individual overviews, for example from the structure overview and the planning board, you call the classic individual overviews. As of the next standard release, a setting option will be available here.</li>\r\n</ul>\r\n<p>A structure overview with a newly designed interface is not provided, because the existing technical options are not sufficient to develop a newly designed interface with a range of functions necessary for effective evaluation. However, the structure overview provided with SAP Note 326591 is still available to you without restrictions.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>Structure overview, project definition, individual overviews</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>Reduction of functions due to SAP Note 326591</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<ul>\r\n<li>Implement the attached program corrections.</li>\r\n</ul>\r\n<ul>\r\n<li>Make sure that the following SAP Notes are implemented in your system:</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>399013</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>395728</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>395158</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>394518</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>394238</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>387434</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>377688</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>375503</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>365224</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li>The individual overviews cannot be implemented manually, but only by importing</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>4.6C: Hot Package 24 (which contains all functions except for the change described in SAP Note 434400)</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>4.6B: Hot Package 33</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li>Alternatively, you can import the files stored in the directory general/R3server/abap/note.0353255 on the SAPSERV</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>4.6C: K000264.K9C and R000264.K9C (corresponds to Hot Package 24)</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>4.6B: R000242.K9B and K000242.K9B</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li>(see SAP Note 13719 for procedure). The documentation of the new individual overviews is also stored in this directory. Note that the language-dependent source code and the documentation are available in English and German. Unfortunately, it is not possible to support additional languages. Apply SAPSERV transport only if the highest Hot Package you imported is smaller than</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>4.6C: Hot Package 24</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>4.6B: Hot Package 33</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li>Implement SAP Note 434400 (4.6C only) and 437124 (4.6B only).</li>\r\n</ul>\r\n<ul>\r\n<li>No additional Customizing is required, and the maintenance of the interface is only possible directly from within the transaction.</li>\r\n</ul>\r\n<p><strong>Documentation for the new individual overviews<br /></strong></p>\r\n<p>You call the individual overviews by directly entering the relevant transaction; access via the menu is not possible in 4.6B and 4.6C.<br />The transaction code has the form cnXXn, whereby XX corresponds with the numbers of the classic transactions. The following overview indicates which transaction code you use to call the individual overview for which object:<br /><br />CN42N Project Definition<br />CN43N WBS Elements<br />CN44N Planned Orders<br />CN45N Orders<br />CN46N Networks<br />CN47N Processes / Elements<br />CN48N Confirmations<br />CN49N Relationships<br />CN50N Capacity Requirements<br />CN51N Production Resources / Tools<br />CN52N Components<br />CN53N Milestones<br /><br />The &#x201C;Enter Profile&#x201D; window is displayed. Enter the desired database profile (DB profile) or select it from the list and choose <em>\"Continue\"</em>.<br />The initial screen is displayed. Make the necessary and desired entries and choose <em>\"Continue\"</em>.<br />On the initial screen, you can also directly enter an ALV layout. If a default layout exists, it was entered previously. If there is no default layout, the layout &#x201C;1SAP&#x201D; delivered by SAP was entered previously. You can overwrite the proposal for the layout.<br />All functions in the individual overview are accessible either via the menu or using pushbuttons. In the following, access using the menus is described. In addition to the standard ALV functions, the following additional functions are available to you:</p>\r\n<ul>\r\n<li>Display / change object</li>\r\n</ul>\r\n<p> 1. Select a cell or line.<br /> 2. Choose \"Goto -&gt; Object -&gt; Display Object\" or \"Change Object\".<br /> The system branches to the Project Builder, in which you can display or change the selected object, or to suitable master data transactions.</p>\r\n<ul>\r\n<li>Display / hide lines</li>\r\n</ul>\r\n<p> 1. Select one or more lines that you want to hide or show again.<br /> 2. To hide lines or show hidden lines again, choose \"Edit --&gt; Lines --&gt; Hide Lines\" or \"Display Hidden Lines Again\".<br /> The hidden lines can be seen neither on the screen nor on the printout. When you display them, all of the previously hidden lines are displayed again.</p>\r\n<ul>\r\n<li>Set / Delete Filter</li>\r\n</ul>\r\n<p> 1. Select one or more columns.<br /> 2. Choose \"Edit -&gt; Filter -&gt; Set Filter\". <br /> 3. Enter a &#x201C;From&#x201D; and a &#x201C;To&#x201D; criterion in the dialog box for each column selected and choose \"Execute\".<br /> If you have set a filter, the system displays only lines with data that lie within the specified interval.<br /> To delete the filter, choose \"Edit -&gt; Filter -&gt; Delete Filter\".</p>\r\n<ul>\r\n<li>Set / Delete Exceptions</li>\r\n</ul>\r\n<p> Exceptions are a special form of filters. With a filter, the objects that do not correspond to the criteria selected are hidden. In the case of an exception, all objects are displayed, but they are marked by a red or green traffic light.<br /> 1. Choose \"Edit -&gt; Filter -&gt; Set Exception\".<br /> 2. In the window \"Define Filter Criteria\", select the columns for which you want to set an exception, and choose \"Copy\".<br /> 3. Enter a &#x201C;From&#x201D; and a &#x201C;To&#x201D; criterion in the dialog box for each column selected and choose \"Execute\".<br /> If you have set an exception, the objects that do not meet the conditions selected are given a red traffic light. Objects that fulfill the conditions are flagged with a green traffic light.<br /> To delete the exception, choose \"Edit -&gt; Filter -&gt; Delete Exception\".</p>\r\n<ul>\r\n<li>Converting units</li>\r\n</ul>\r\n<p> 1. Select a suitable column.<br /> Columns that contain no values cannot be converted.<br /> 2. Choose \"Edit --&gt; Convert Units\".<br /> The system displays a dialog box with the possible target units. Possible target units are units with the same dimension as the original unit, for example &#x201C;time&#x201D;..<br /> 3. Select a target unit.<br /> The values in the selected column are converted.</p>\r\n<ul>\r\n<li>Refresh</li>\r\n</ul>\r\n<p> Use this function to update the individual overview. <br /> WARNING: Some settings are lost during refresh.<br /> To update the individual overview, choose \"Refresh\".<br /> NOTE: This function is only possible using the pushbutton.<br /> The data is updated. When you do this, the system issues the following settings only:</p>\r\n<ul>\r\n<ul>\r\n<li>Temporary changes of the database profile</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Display of deleted objects</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Layout</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Header and title text</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Fields displayed</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Sorting</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Filter</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>EXCEPTIONS</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Hidden lines</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Unit conversion</li>\r\n</ul>\r\n</ul>\r\n<p> All other settings are lost.</p>\r\n<ul>\r\n<li>Drilldown report</li>\r\n</ul>\r\n<p> You can assign project drilldown reports to the individual overviews.<br /> NOTE: The assignment of other report types than drilldown reports is not possible.<br /> 1. Choose \"Goto --&gt; Drilldown Report --&gt; Assign Drilldown Report\".<br /> The \"Assign Reports\" window appears.<br /> 2. Select the desired reports or add new reports.<br /> To execute the report, select a line or cell and choose \"Goto --&gt; Drilldown Report --&gt; Execute Drilldown Report\".<br /> NOTE: The assignment and execution of drilldown reports is only possible for cost-relevant objects. The following objects are cost-relevant:</p>\r\n<ul>\r\n<ul>\r\n<li>WBS elements</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Orders</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Networks</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Operations / elements</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Components</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li>Executing another individual overview</li>\r\n</ul>\r\n<p> You can jump to the individual overview &#x201C;Subordinate Objects&#x201D; from an individual overview.<br /> 1. Select a line or cell in the table.<br /> 2. Choose \"Other Individual Overview\".<br /> NOTE: This function is only possible using the pushbutton.<br /> A list with the available jumps is displayed. This list is created according to the objects of the current overview.<br /> 3. Select the required navigation.<br /> The individual overview selected is displayed. The settings of the DB profile that you made previously are retained.</p>\r\n<ul>\r\n<li>Confirmation</li>\r\n</ul>\r\n<p> For networks and maintenance orders, you can create or cancel confirmations for operations. Reversal is also possible in the individual overview for confirmations. For other objects, these functions are not available.</p>\r\n<ul>\r\n<ul>\r\n<li>To create a confirmation, choose \"Goto -&gt; Confirmation -&gt; Create\".</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>To reverse a confirmation, choose \"Goto -&gt; Confirmation -&gt; Cancel\".</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li>Maintain title and header text</li>\r\n</ul>\r\n<p> You can maintain a title text and / or a header text. During printing, a header text is output at the start of every page, and a title text is only displayed once at the start of the expression.<br /> 1. Choose \"Edit --&gt; Maintain Texts --&gt; Maintain Title Text\" or \"Maintain Header Text\".<br /> 2. Enter a text.</p>\r\n<ul>\r\n<li>Displaying deleted objects</li>\r\n</ul>\r\n<p> You can define whether or not the system is to display deleted objects. To do this, choose \"Edit --&gt; Define Deleted Objects --&gt; Display\" or \"Do Not Display\". If you select data from the archive, the system always displays the deleted objects.<br /> <br /> <br /><br />Information about the other pushbuttons is available in the SAP Library under \"Introduction to the SAP System --&gt; Lists --&gt; SAP List Viewer (ALV) Grid Control (BC-SRV-ALV)\" and, under \"Basis -&gt; Basis Services / Communication interface (BC-SRV) --&gt; SAP List Viewer (BC-SRV-ALV) --&gt; ALV Grid Control (BC-SRV-ALV)\".</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D041635)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON> (I328030)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000353255/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000353255/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000353255/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000353255/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000353255/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000353255/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000353255/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000353255/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000353255/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "700875", "RefComponent": "PS-IS-LOG", "RefTitle": "FAQ 2: Structure Overview / Individual Overviews", "RefUrl": "/notes/700875"}, {"RefNumber": "516479", "RefComponent": "PS-IS-LOG", "RefTitle": "Consulting notes Logistic Information System", "RefUrl": "/notes/516479"}, {"RefNumber": "502879", "RefComponent": "PS-IS-LOG", "RefTitle": "(NIO) Executing in the background", "RefUrl": "/notes/502879"}, {"RefNumber": "458602", "RefComponent": "PS-IS-LOG", "RefTitle": "Individual overview WBS elements:SD document is missing", "RefUrl": "/notes/458602"}, {"RefNumber": "434400", "RefComponent": "PS-IS-LOG", "RefTitle": "(NIO) Branch to operation maintenance order", "RefUrl": "/notes/434400"}, {"RefNumber": "399013", "RefComponent": "PS", "RefTitle": "Additional calculation for external activities", "RefUrl": "/notes/399013"}, {"RefNumber": "395728", "RefComponent": "PS", "RefTitle": "CXPS - Scheduling modules: several minor points", "RefUrl": "/notes/395728"}, {"RefNumber": "395158", "RefComponent": "PS", "RefTitle": "PS: Duration calculations for network/order", "RefUrl": "/notes/395158"}, {"RefNumber": "394518", "RefComponent": "PS-IS", "RefTitle": "Order number not in defined field of activity", "RefUrl": "/notes/394518"}, {"RefNumber": "394238", "RefComponent": "PS", "RefTitle": "I_FCALID is not transferred", "RefUrl": "/notes/394238"}, {"RefNumber": "387434", "RefComponent": "PS", "RefTitle": "PS: Date calculations for milestone", "RefUrl": "/notes/387434"}, {"RefNumber": "377688", "RefComponent": "PS-ST-PB", "RefTitle": "Call of Project Builder from info system", "RefUrl": "/notes/377688"}, {"RefNumber": "375503", "RefComponent": "PS", "RefTitle": "PS: Date calculations for activity / WBS", "RefUrl": "/notes/375503"}, {"RefNumber": "365224", "RefComponent": "PS-REV-RA", "RefTitle": "Results analysis ignores WBS elements", "RefUrl": "/notes/365224"}, {"RefNumber": "326591", "RefComponent": "PS-IS-LOG", "RefTitle": "PS: Structure overview, individual overviews in 4.6", "RefUrl": "/notes/326591"}, {"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719 "}, {"RefNumber": "700875", "RefComponent": "PS-IS-LOG", "RefTitle": "FAQ 2: Structure Overview / Individual Overviews", "RefUrl": "/notes/700875 "}, {"RefNumber": "516479", "RefComponent": "PS-IS-LOG", "RefTitle": "Consulting notes Logistic Information System", "RefUrl": "/notes/516479 "}, {"RefNumber": "375503", "RefComponent": "PS", "RefTitle": "PS: Date calculations for activity / WBS", "RefUrl": "/notes/375503 "}, {"RefNumber": "365224", "RefComponent": "PS-REV-RA", "RefTitle": "Results analysis ignores WBS elements", "RefUrl": "/notes/365224 "}, {"RefNumber": "326591", "RefComponent": "PS-IS-LOG", "RefTitle": "PS: Structure overview, individual overviews in 4.6", "RefUrl": "/notes/326591 "}, {"RefNumber": "377688", "RefComponent": "PS-ST-PB", "RefTitle": "Call of Project Builder from info system", "RefUrl": "/notes/377688 "}, {"RefNumber": "399013", "RefComponent": "PS", "RefTitle": "Additional calculation for external activities", "RefUrl": "/notes/399013 "}, {"RefNumber": "395158", "RefComponent": "PS", "RefTitle": "PS: Duration calculations for network/order", "RefUrl": "/notes/395158 "}, {"RefNumber": "394238", "RefComponent": "PS", "RefTitle": "I_FCALID is not transferred", "RefUrl": "/notes/394238 "}, {"RefNumber": "395728", "RefComponent": "PS", "RefTitle": "CXPS - Scheduling modules: several minor points", "RefUrl": "/notes/395728 "}, {"RefNumber": "387434", "RefComponent": "PS", "RefTitle": "PS: Date calculations for milestone", "RefUrl": "/notes/387434 "}, {"RefNumber": "502879", "RefComponent": "PS-IS-LOG", "RefTitle": "(NIO) Executing in the background", "RefUrl": "/notes/502879 "}, {"RefNumber": "458602", "RefComponent": "PS-IS-LOG", "RefTitle": "Individual overview WBS elements:SD document is missing", "RefUrl": "/notes/458602 "}, {"RefNumber": "434400", "RefComponent": "PS-IS-LOG", "RefTitle": "(NIO) Branch to operation maintenance order", "RefUrl": "/notes/434400 "}, {"RefNumber": "394518", "RefComponent": "PS-IS", "RefTitle": "Order number not in defined field of activity", "RefUrl": "/notes/394518 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_APPL", "From": "46A", "To": "46B", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "46C", "To": "46C", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "SAP_APPL 46B", "SupportPackage": "SAPKH46B33", "URL": "/supportpackage/SAPKH46B33"}, {"SoftwareComponentVersion": "SAP_APPL 46C", "SupportPackage": "SAPKH46C24", "URL": "/supportpackage/SAPKH46C24"}, {"SoftwareComponentVersion": "SAP_APPL 46C", "SupportPackage": "SAPKH46C25", "URL": "/supportpackage/SAPKH46C25"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": [{"SoftwareComponent": "SAP_APPL", "NumberOfCorrin": 1, "URL": "/corrins/0000353255/1"}]}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 1, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 1, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": [{"SoftwareComponent": "SAP_APPL", "ValidFrom": "40B", "ValidTo": "46C", "Number": "388497 ", "URL": "/notes/388497 ", "Title": "LDB PSJ: Constant generation of reports III", "Component": "PS-IS"}]}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}