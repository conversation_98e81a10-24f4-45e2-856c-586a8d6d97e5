{"Request": {"Number": "364368", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 389, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000014936132017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000364368?language=E&token=3939D14281E21FA2838EECDE2BAF71CA"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000364368", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000364368/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "364368"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 39}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Help for error analysis"}, "Priority": {"_label": "Priority", "value": "Correction with high priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "09.11.2022"}, "SAPComponentKey": {"_label": "Component", "value": "CO-PC-ACT"}, "SAPComponentKeyText": {"_label": "Component", "value": "Actual Costing"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Controlling", "value": "CO", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'CO*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Product Cost Controlling", "value": "CO-PC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'CO-PC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Actual Costing", "value": "CO-PC-ACT", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'CO-PC-ACT*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "364368 - Material ledger help desk"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>This note contains information about the material ledger help desk, a collection of programs used by SAP Support to correct data inconsistencies in the material ledger area.</p>\r\n<p><strong>Help desk versions currently available:</strong></p>\r\n<ul>\r\n<li>Version 1.9 for R/3 4.6B</li>\r\n</ul>\r\n<ul>\r\n<li>Version 3.9 for R/3 4.6C</li>\r\n</ul>\r\n<ul>\r\n<li>Version 2.2 for R/3 Enterprise 4.70, ERP 5.00, 6.00, and all enhancement packages from 6.02 to 6.18 and other versions, SAP Simple Finance 1.0 to 3.0 (SAP_FIN 700, 720, and 730) and S/4 CORE 100</li>\r\n</ul>\r\n<p><strong>This SAP Note is no longer valid as of Release S/4 CORE 101</strong> since as of this release, the help desk is delivered in the standard system (transaction FCMLHELP).</p>\r\n<p>For all previous releases, the standard help desk can be installed with SAP Notes 2148839 and 2987906. The transports of this SAP Note are then no longer required.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>MMINKON, ML, material ledger, CF_KEPH_IS_INITIAL, C+048, MLCCS, CKMHELP, FCMLHELP</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>SAP Support requires these programs to correct data inconsistencies.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p>You cannot implement this note using transaction SNOTE.<br />Under \"Attachments\", this note provides transport files that are packed in archives (*.ZIP, *.RAR). These files contain the programs of the material ledger help desk. Download the archive that is valid for your release, unpack the files and install the transport files.<br /><br />For Release 4.70 and higher, the archive consists of two subarchives. Download the two archive files and save these in the same directory. You only have to unpack the first archive file. The unpacker automatically unpacks the second file.<br /><br />Note that the help desk version of Release SAP R/3 Enterprise 4.70 is also valid for higher releases (ERP 5.00, 6.00 and all Enhancement Packages for ERP 6.00).<br /><br />See Note 13719 for information about how to import the files into your system.<br /><br />Note that the programs are updated and enhanced on a regular basis; you may therefore have to import a newer version of the programs.<br /><br />Importing the files into your system is not disruptive because the tools are independent objects that do not interfere with existing  source code of the standard SAP system. All of the installed objects are contained in the package MLHELPDESK, which is not part of the standard ERP system. Only SAP Development Support can implement corrections using this tool.<br /><br />After the installation, use transaction OMX4 to define a number range interval for the material ledger number range group \"RE (ML repair)\". See the solution section of Note 122391 for more information about how to do this.<br /><br />Note that you can execute all ML help desk programs to analyze the<br />data in the test run. Any corrections in <br />the update run are to be implemented by SAP Support only.<br /><br />When you import the transport in Release 4.70, a generation error can occur in earlier Support Package levels. These errors are not serious, as the incorrect programs are no longer needed.<br />If, as a result of this generation error, the help desk cannot be transported to the production system, delete the incorrect programs from the transport request. Alternatively, delete the entire source code of the incorrect programs.</p>\r\n<p><strong>Note for the upgrade</strong></p>\r\n<p>After an upgrade, it is possible that the functions of the material ledger help desk are no longer available.  In such cases, you must reinstall this note. For this, you must note that you may have to implement another help desk version relevant for your new Release. </p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON> (D024835)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON>-Scham<PERSON> (D034994)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "J", "TranslationLanguage": "日本語", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000364368/J"}, {"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000364368/D"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000364368/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000364368/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000364368/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000364368/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000364368/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000364368/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000364368/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": [{"FileName": "Note_364368_Rel_46C.zip", "FileSize": "1304", "MimeType": "application/x-zip-compressed", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=011000358700008914842001&iv_version=0039&iv_guid=51E3449F2563754AAF7AA4B6E0FD719A"}, {"FileName": "Note_364368_Rel_46B.zip", "FileSize": "913", "MimeType": "application/x-zip-compressed", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=011000358700008914842001&iv_version=0039&iv_guid=6D23F1CC911BBE41A313CDAF63B3F9B6"}, {"FileName": "Note_364368_Rel_470_500_60x.part2.rar", "FileSize": "432", "MimeType": "application/octet-stream", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=011000358700008914842001&iv_version=0039&iv_guid=1AE74E8751D60E47886A690D7ABBFEDE"}, {"FileName": "Note_364368_Rel_470_500_60x.part1.rar", "FileSize": "2048", "MimeType": "application/octet-stream", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=011000358700008914842001&iv_version=0039&iv_guid=984A33D4A03C2E4F804BD50D488E6A16"}]}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "81463", "RefComponent": "CO-PC-ACT", "RefTitle": "Convert material Ledger from 3.0 to 4.0", "RefUrl": "/notes/81463"}, {"RefNumber": "699816", "RefComponent": "CO-PC-ACT-AVR", "RefTitle": "Explanation facility for AVR", "RefUrl": "/notes/699816"}, {"RefNumber": "645083", "RefComponent": "CO-PC-ACT", "RefTitle": "Material period status set to \"Closing Entry Completed\"", "RefUrl": "/notes/645083"}, {"RefNumber": "63845", "RefComponent": "XX-SER-NET", "RefTitle": "Corrections on sapserv - searching for files", "RefUrl": "/notes/63845"}, {"RefNumber": "597505", "RefComponent": "CO-PC-ACT", "RefTitle": "Correction of inconsistencies in productive systems", "RefUrl": "/notes/597505"}, {"RefNumber": "575794", "RefComponent": "CO-PC-ACT", "RefTitle": "Syntax Error Program MLHELP_ORDER_ANALYZE", "RefUrl": "/notes/575794"}, {"RefNumber": "542798", "RefComponent": "MM-IV-CA", "RefTitle": "MR11: Reports for correcting the purchase order history", "RefUrl": "/notes/542798"}, {"RefNumber": "2987906", "RefComponent": "CO-PC-ACT", "RefTitle": "UDO report for transfer of help desk programs to standard system", "RefUrl": "/notes/2987906"}, {"RefNumber": "2148839", "RefComponent": "CO-PC-ACT", "RefTitle": "Transfer of material ledger help desk programs to standard system", "RefUrl": "/notes/2148839"}, {"RefNumber": "1811872", "RefComponent": "CO-PC-ACT-AVR", "RefTitle": "MLHELP_AVR_EXPLANATION_TOOL: Select data from old periods", "RefUrl": "/notes/1811872"}, {"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719"}, {"RefNumber": "1366291", "RefComponent": "CO-PC-ACT", "RefTitle": "MLHELP_CHECK_MULTIPLE_CKMLHD does not find results", "RefUrl": "/notes/1366291"}, {"RefNumber": "122391", "RefComponent": "CO-PC-ACT", "RefTitle": "Number range maintenance material ledger (C+606)", "RefUrl": "/notes/122391"}, {"RefNumber": "1020215", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1020215"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2882178", "RefComponent": "CO-PC-ACT", "RefTitle": "Costing run shows error message C+700", "RefUrl": "/notes/2882178 "}, {"RefNumber": "2181638", "RefComponent": "CO-PC-ACT", "RefTitle": "Transaction CKM3 shows message C+000 \"Internal error. Notify your system administrator\"", "RefUrl": "/notes/2181638 "}, {"RefNumber": "2790523", "RefComponent": "CO-PC-ACT", "RefTitle": "FORM/FUNCTION CKMS_SMBEW_SET_FOREIGN_FIELDS", "RefUrl": "/notes/2790523 "}, {"RefNumber": "2781241", "RefComponent": "CO-PC-ACT", "RefTitle": "C+099 \"Internal error in FORM/FUNCTION OUTSPLIT_SCALE_UPDOWN in position 1 with RC 4\"", "RefUrl": "/notes/2781241 "}, {"RefNumber": "2746689", "RefComponent": "CO-PC-ACT", "RefTitle": "C+606 Number assignment for ML document is incorrect", "RefUrl": "/notes/2746689 "}, {"RefNumber": "2149876", "RefComponent": "CO-PC-ACT", "RefTitle": "C+048 root cause analysis for inconsistencies MM-ML", "RefUrl": "/notes/2149876 "}, {"RefNumber": "1511937", "RefComponent": "CO-PC-ACT", "RefTitle": "C+065 or C+470 or C+019... no ML flag when ML active for a plant, MLAST MLMAA fields missing", "RefUrl": "/notes/1511937 "}, {"RefNumber": "2558592", "RefComponent": "CO-PC-ACT", "RefTitle": "CONVT_OVERFLOW Transaction RSA3 with extractor 0CO_PC_ACT_10", "RefUrl": "/notes/2558592 "}, {"RefNumber": "2539631", "RefComponent": "CO-PC-ACT", "RefTitle": "Transaction CKMLCP displays error message F5201", "RefUrl": "/notes/2539631 "}, {"RefNumber": "2517781", "RefComponent": "CO-PC-ACT", "RefTitle": "Error message C+874 during Material ledger closing.", "RefUrl": "/notes/2517781 "}, {"RefNumber": "2499050", "RefComponent": "CO-PC-ACT", "RefTitle": "Error C+824 C+097, SAP S/4HANA, on-premise edition 1511", "RefUrl": "/notes/2499050 "}, {"RefNumber": "2496923", "RefComponent": "CO-PC-ACT", "RefTitle": "S/4HANA 1511: The data of tables MBEW, EBEW, OBEW or QBEW are not found", "RefUrl": "/notes/2496923 "}, {"RefNumber": "2433733", "RefComponent": "CO-PC-ACT", "RefTitle": "ML Helpdesk tool in S/4HANA", "RefUrl": "/notes/2433733 "}, {"RefNumber": "1366291", "RefComponent": "CO-PC-ACT", "RefTitle": "MLHELP_CHECK_MULTIPLE_CKMLHD does not find results", "RefUrl": "/notes/1366291 "}, {"RefNumber": "1811872", "RefComponent": "CO-PC-ACT-AVR", "RefTitle": "MLHELP_AVR_EXPLANATION_TOOL: Select data from old periods", "RefUrl": "/notes/1811872 "}, {"RefNumber": "645083", "RefComponent": "CO-PC-ACT", "RefTitle": "Material period status set to \"Closing Entry Completed\"", "RefUrl": "/notes/645083 "}, {"RefNumber": "1020215", "RefComponent": "CO-PC-ACT", "RefTitle": "Very high priority at the weekend", "RefUrl": "/notes/1020215 "}, {"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719 "}, {"RefNumber": "699816", "RefComponent": "CO-PC-ACT-AVR", "RefTitle": "Explanation facility for AVR", "RefUrl": "/notes/699816 "}, {"RefNumber": "122391", "RefComponent": "CO-PC-ACT", "RefTitle": "Number range maintenance material ledger (C+606)", "RefUrl": "/notes/122391 "}, {"RefNumber": "81463", "RefComponent": "CO-PC-ACT", "RefTitle": "Convert material Ledger from 3.0 to 4.0", "RefUrl": "/notes/81463 "}, {"RefNumber": "542798", "RefComponent": "MM-IV-CA", "RefTitle": "MR11: Reports for correcting the purchase order history", "RefUrl": "/notes/542798 "}, {"RefNumber": "611263", "RefComponent": "CO-PC-ACT", "RefTitle": "C+048 occurs in the system.", "RefUrl": "/notes/611263 "}, {"RefNumber": "597505", "RefComponent": "CO-PC-ACT", "RefTitle": "Correction of inconsistencies in productive systems", "RefUrl": "/notes/597505 "}, {"RefNumber": "575794", "RefComponent": "CO-PC-ACT", "RefTitle": "Syntax Error Program MLHELP_ORDER_ANALYZE", "RefUrl": "/notes/575794 "}, {"RefNumber": "63845", "RefComponent": "XX-SER-NET", "RefTitle": "Corrections on sapserv - searching for files", "RefUrl": "/notes/63845 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_APPL", "From": "46B", "To": "46B", "Subsequent": "X"}, {"SoftwareComponent": "SAP_APPL", "From": "46C", "To": "46C", "Subsequent": "X"}, {"SoftwareComponent": "SAP_APPL", "From": "470", "To": "470", "Subsequent": "X"}, {"SoftwareComponent": "SAP_APPL", "From": "500", "To": "500", "Subsequent": "X"}, {"SoftwareComponent": "SAP_APPL", "From": "600", "To": "600", "Subsequent": "X"}, {"SoftwareComponent": "SAP_APPL", "From": "602", "To": "602", "Subsequent": "X"}, {"SoftwareComponent": "SAP_APPL", "From": "603", "To": "603", "Subsequent": "X"}, {"SoftwareComponent": "SAP_APPL", "From": "604", "To": "604", "Subsequent": "X"}, {"SoftwareComponent": "SAP_APPL", "From": "605", "To": "605", "Subsequent": "X"}, {"SoftwareComponent": "SAP_APPL", "From": "606", "To": "606", "Subsequent": "X"}, {"SoftwareComponent": "SAP_APPL", "From": "616", "To": "616", "Subsequent": "X"}, {"SoftwareComponent": "SAP_FIN", "From": "617", "To": "617", "Subsequent": "X"}, {"SoftwareComponent": "SAP_FIN", "From": "618", "To": "618", "Subsequent": "X"}, {"SoftwareComponent": "SAP_FIN", "From": "700", "To": "700", "Subsequent": "X"}, {"SoftwareComponent": "SAP_FIN", "From": "720", "To": "720", "Subsequent": "X"}, {"SoftwareComponent": "SAP_FIN", "From": "730", "To": "730", "Subsequent": ""}, {"SoftwareComponent": "S4CORE", "From": "100", "To": "100", "Subsequent": "X"}, {"SoftwareComponent": "EA-APPL", "From": "110", "To": "110", "Subsequent": "X"}, {"SoftwareComponent": "EA-APPL", "From": "200", "To": "200", "Subsequent": "X"}, {"SoftwareComponent": "EA-APPL", "From": "500", "To": "500", "Subsequent": "X"}, {"SoftwareComponent": "EA-APPL", "From": "600", "To": "600", "Subsequent": "X"}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "EA-APPL 110", "SupportPackage": "SAPKGPAA01", "URL": "/supportpackage/SAPKGPAA01"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}