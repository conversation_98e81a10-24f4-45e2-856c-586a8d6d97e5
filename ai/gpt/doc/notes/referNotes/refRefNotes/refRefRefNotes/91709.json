{"Request": {"Number": "91709", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 404, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000014537912017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000091709?language=E&token=7FFD1DC178B9D23EA0E686547ECA22CA"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000091709", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000091709/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "91709"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 127}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Upgrade information"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "16.04.2002"}, "SAPComponentKey": {"_label": "Component", "value": "BC-UPG-RDM"}, "SAPComponentKeyText": {"_label": "Component", "value": "README: Upgrade Supplements"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Basis Components", "value": "BC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Upgrade - general", "value": "BC-UPG", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-UPG*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "README: Upgrade Supplements", "value": "BC-UPG-RDM", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-UPG-RDM*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "91709 - Addition upgrade to 4.0B"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>There are errors in the guide or in the upgrade procedure.</p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>Update, migration, release, release upgrade, maintenance level, R3up, PREPARE</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>*</p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p><B>Attention: This note is no longer maintained since this specific upgrade is no longer supported.<B></B></B><br />If you want to upgrade to Release 4.0B, use the <B>upgrade to 4.0B Support Release 1</B>. The associated upgrade note is Note <B>147337</B>.<br /><br /><br /><B>CAUTION:</B> If your <B>R/3 source release is 2.2x</B>, then other notes apply for the upgrade to Release 4.0B, see the guides<br />&#x00A0;&#x00A0; \"Upgrade from Release 2.2x to Release 4.0B: UNIX\" and<br />&#x00A0;&#x00A0;\"Upgrade from Release 2.2x to Release 4.0B: Windows NT\".<br /><br />*******************************************************************<br />Also refer to the upgrade notes, in which database-specific problems are discussed (see also related notes):<br />&#x00A0;&#x00A0;98129 ADABAS D<br />&#x00A0;&#x00A0;99379 DB2/400<br />&#x00A0;&#x00A0;99208 DB2/390<br />&#x00A0;&#x00A0;98597 DB2 common server<br />&#x00A0;&#x00A0; 94663 INFORMIX<br />&#x00A0;&#x00A0; 98029 MS SQL Server<br />&#x00A0;&#x00A0; 95999 ORACLE<br /><br />Also read the following notes:<br />&#x00A0;&#x00A0; 98904&#x00A0;&#x00A0;&#x00A0;&#x00A0;Additions to the upgrade to 4.0B Online Docu.<br />&#x00A0;&#x00A0; 97583&#x00A0;&#x00A0;&#x00A0;&#x00A0;Current note on language import Release 4.0B<br />&#x00A0;&#x00A0;102603&#x00A0;&#x00A0;&#x00A0;&#x00A0;Additional Info: Upgrading to Japanese 4.0B UNIX<br />&#x00A0;&#x00A0;103533&#x00A0;&#x00A0;&#x00A0;&#x00A0;Additional Info: Upgrading to Japanese 4.0B NT<br />&#x00A0;&#x00A0;111601&#x00A0;&#x00A0;&#x00A0;&#x00A0;Ready-to-Run R/3: Information on the upgrade<br /><br />*******************************************************************<br /><br /><B>CAUTION: This note is updated frequently, therefore make sure</B><br /><B>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;to read it again immediately before the upgrade.</B><br /><br /><br />&#x00A0;&#x00A0; Changes listed by date/time (one line is inserted for each change)<br />&#x00A0;&#x00A0; ==================================================================<br /><br />Date&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Topic&#x00A0;&#x00A0;Short Description<br />----------------------------------------------------------------------<br />16/APR/02&#x00A0;&#x00A0;V&#x00A0;&#x00A0;&#x00A0;&#x00A0;Basis Support Package 72 not highest SP<br />27/NOV/01&#x00A0;&#x00A0; II&#x00A0;&#x00A0;&#x00A0;&#x00A0;Import R3trans patch<br />23/FEB/01&#x00A0;&#x00A0; V&#x00A0;&#x00A0;&#x00A0;&#x00A0; Data loss with certain start releases<br />02/JAN/01&#x00A0;&#x00A0; V&#x00A0;&#x00A0;&#x00A0;&#x00A0; HR Construction industry: Loss of data in Table T5DB4<br />08/JAN/01&#x00A0;&#x00A0; IX&#x00A0;&#x00A0;&#x00A0;&#x00A0;Component PS (CIM) only: Wrong screens processed<br />22/DEC/00&#x00A0;&#x00A0;II&#x00A0;&#x00A0;&#x00A0;&#x00A0;Exchange of the SAP kernel<br />04/OCT/00&#x00A0;&#x00A0;V&#x00A0;&#x00A0;&#x00A0;&#x00A0; ICNV of cluster tables<br />24/JAN/00&#x00A0;&#x00A0; VIII/ Activation error in table S404<br />20/DEZ/99&#x00A0;&#x00A0; VIII/ Warning/termination in PCON_31I for EWUPAK<br />14/APR/99&#x00A0;&#x00A0; II/&#x00A0;&#x00A0; 4.0B Support Release 1 planned for end of May<br />29/MAR/99&#x00A0;&#x00A0; VII/&#x00A0;&#x00A0;PREPARE/JOB_RADDRCHK: TG063 for BAMUIVIEW, SQLRTAB<br />29/MAR/99&#x00A0;&#x00A0;&#x00A0;&#x00A0;V/&#x00A0;&#x00A0; HR for start with Release 3.x: person group key<br />26/JAN/99&#x00A0;&#x00A0;&#x00A0;&#x00A0;V/&#x00A0;&#x00A0; CO: Overhead calculation (Tab. COEP) -&gt; Note 135132<br />21/DEZ/98&#x00A0;&#x00A0;&#x00A0;&#x00A0;V/&#x00A0;&#x00A0;SAPoffice: First save calender notes<br />16/DEZ/98&#x00A0;&#x00A0; II/&#x00A0;&#x00A0; Start with 3.1H or 3.1I: Comparison quantity too high<br />07/DEZ/98&#x00A0;&#x00A0;&#x00A0;&#x00A0;V/&#x00A0;&#x00A0; Upgrade can destroy customer-defined report variants<br />17/NOV/98&#x00A0;&#x00A0;III/&#x00A0;&#x00A0; Release Note 'Migration of Matchcodes to search<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;helps'<br />11/NOV/98&#x00A0;&#x00A0;III/&#x00A0;&#x00A0;4.0B-Upgrade also possible with source release 3.1I!<br />11/NOV/98&#x00A0;&#x00A0;III/&#x00A0;&#x00A0;No import of 3.1H-AKK, but of current AKK<br />10/NOV/98&#x00A0;&#x00A0; II/&#x00A0;&#x00A0; HR-Customers including LCPs in the target release<br />29/OCT/98&#x00A0;&#x00A0;&#x00A0;&#x00A0;V/&#x00A0;&#x00A0;HR-Customers: Process Note 118310 first<br />26/OCT/98&#x00A0;&#x00A0; IX/&#x00A0;&#x00A0;Possible data loss with conversion CDCLS and EDIDOC<br />12/OCT/98&#x00A0;&#x00A0; II/&#x00A0;&#x00A0;Hardware (CPU, main memory) check<br />29/SEP/98&#x00A0;&#x00A0;III/&#x00A0;&#x00A0; UNIX: English Version, 1.100 MB in /usr/sap/put<br />28/SEP/98&#x00A0;&#x00A0; II/&#x00A0;&#x00A0; Start with 3.1H: SPDD for KOMPAKE, KOMKAKE, COPABBSEG<br />28/SEP/98&#x00A0;&#x00A0;&#x00A0;&#x00A0;V/&#x00A0;&#x00A0;Saving events for table maintenace dialoges<br />24/SEP/98 VIII/&#x00A0;&#x00A0; PCON_40B: Termination with inconsistency &#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;TTODIR/LTODIR<br />21/SEP/98 VIII/&#x00A0;&#x00A0; XPRAS_40B: Termination in program RPRTRV10<br />21/SEP/98 VIII/&#x00A0;&#x00A0; ReliantUNIX: Upgrade or import hangs<br />15/SEP/98&#x00A0;&#x00A0;&#x00A0;&#x00A0;V/&#x00A0;&#x00A0;Process batch input session before the upgrade<br />14/SEP/98&#x00A0;&#x00A0;VII/&#x00A0;&#x00A0;PREPARE, Phase CNV_AVOID<br />11/SEP/98&#x00A0;&#x00A0;VII/&#x00A0;&#x00A0;WinNT: Phase STARTR3_NBAS: R/3 Kernel does not start<br />07/SEP/98&#x00A0;&#x00A0; VI/&#x00A0;&#x00A0;Digital UNIX 3.2: Use scroll mode<br />01/SEP/98&#x00A0;&#x00A0;&#x00A0;&#x00A0;V/&#x00A0;&#x00A0; Start with 3.x: save workload statistics (ST03)<br />26/AUG/98&#x00A0;&#x00A0;&#x00A0;&#x00A0;V/&#x00A0;&#x00A0; SAP Retail 1.2x: implement Note 114346 beforehand<br />25/AUG/98&#x00A0;&#x00A0;III/&#x00A0;&#x00A0; UNIX/Windows NT: DB13 changed for all databases<br />24/AUG/98&#x00A0;&#x00A0; VI/&#x00A0;&#x00A0; Windows NT: Start Prepare modul import in MSDOS box<br />12/AUG/98&#x00A0;&#x00A0; IX/&#x00A0;&#x00A0; Care with modifications in SAPMP56R and SAPMP56S 07/AUG/98&#x00A0;&#x00A0;III/&#x00A0;&#x00A0; Appendix Q, Prepare phase READDATA asks for another CD06/AUG/98&#x00A0;&#x00A0; IX/&#x00A0;&#x00A0;DB13 runtime error: CONNE_IMPORT_WRONG_FIELD_TYPE<br />05/AUG/98 VIII/&#x00A0;&#x00A0; ACT_40B: Table DBSTATC is created by sapdba<br />27/JUL/98&#x00A0;&#x00A0;&#x00A0;&#x00A0;V/&#x00A0;&#x00A0; Customer-specific MC IDs destroyed after 4.0B upgrade<br />20/JUL/98 VIII/&#x00A0;&#x00A0; JOB_DRDOC_40B terminates or hangs -&gt; Note 108728<br />03/JUL/98&#x00A0;&#x00A0;&#x00A0;&#x00A0;V/&#x00A0;&#x00A0;Only for start with 4.0A: empty tables (SE14)<br />01/JUL/98&#x00A0;&#x00A0;VII/&#x00A0;&#x00A0;RFC-Error: error correction does not work<br />30/JUN/98 VIII/&#x00A0;&#x00A0;PCON_40B: Message TG450 to TG453 -&gt; Note 73999<br />24/JUN/98&#x00A0;&#x00A0; II/&#x00A0;&#x00A0;IS-IS customers: prevent conversion of table REGUP<br />24/JUN/98 VIII/&#x00A0;&#x00A0;XPRAS_40B, XPRA RGXGBRS4: ignore GU093 and GB209<br />17/JUN/98&#x00A0;&#x00A0;&#x00A0;&#x00A0;V/&#x00A0;&#x00A0;Avoiding loss of customer-specific screen texts<br />17/JUN/98&#x00A0;&#x00A0;VII/&#x00A0;&#x00A0;Do not repeat module \"Parameter Input\" on its own<br />16/JUN/98&#x00A0;&#x00A0;III/&#x00A0;&#x00A0;ReliantUNIX: CDS++ runtime system version 1.0C32<br />12/JUN/98&#x00A0;&#x00A0;III/&#x00A0;&#x00A0;Transaction ICNV: incorrect menu texts in the guide<br />09/JUN/98&#x00A0;&#x00A0; II/&#x00A0;&#x00A0;Loss of individual documentation possible<br />04/JUN/98&#x00A0;&#x00A0; VI/&#x00A0;&#x00A0;AIX: at least Java Version 1.1.4 for UaGui/UaServer<br />22/MAY/98&#x00A0;&#x00A0;&#x00A0;&#x00A0;V/&#x00A0;&#x00A0;ReliantUNIX:/install/os_version/ReliantUNIX=SINIX<br />19/MAY/98&#x00A0;&#x00A0;&#x00A0;&#x00A0;V/&#x00A0;&#x00A0;Check of table T510Q before the upgrade<br />19/MAY/98&#x00A0;&#x00A0; II/&#x00A0;&#x00A0;4.0B upgrade documentation also on CD<br />15/MAY/98&#x00A0;&#x00A0; IX/&#x00A0;&#x00A0;Generation error when applying Hot Packages<br />15/MAY/98&#x00A0;&#x00A0;VII/&#x00A0;&#x00A0;Prepare: Termination of SPAM in phase BIND_PATCH<br />15/MAY/98&#x00A0;&#x00A0;II/&#x00A0;&#x00A0;For 3.1I kernel: Kernel must have patch level 3<br />13/MAY/98 VIII/&#x00A0;&#x00A0;PCON_40B: Restarting after termination<br />13/MAY/98&#x00A0;&#x00A0; IX/&#x00A0;&#x00A0;Workitems missing after upgrade of 3.0x, 3.1X<br />04/MAY/98&#x00A0;&#x00A0;&#x00A0;&#x00A0;V/&#x00A0;&#x00A0;Status of Hot Packages is not checked<br />27/APR/98&#x00A0;&#x00A0; VI/&#x00A0;&#x00A0;MS Internet Expl. 4.0 must have version 4.72.2106.8<br />27/APR/98&#x00A0;&#x00A0; VI/&#x00A0;&#x00A0;HP-UX: Problems with the R/3 Upgrade Assistant<br />21/APR/98&#x00A0;&#x00A0;VII/&#x00A0;&#x00A0;Prepare: JOB_RADDRCHK: Processing error TG063!<br />20/APR/98&#x00A0;&#x00A0;VII/&#x00A0;&#x00A0;Only FCS: Release of open requests with SAP objects<br />20/APR/98 VIII/&#x00A0;&#x00A0;XPRAS_40B: Ignore messages of the XPRA RLXPR440<br />14/APR/98&#x00A0;&#x00A0; VI/&#x00A0;&#x00A0;Windows NT: Upgrade Assistant \"Waiting for input...\"<br />09/APR/98&#x00A0;&#x00A0;VII/&#x00A0;&#x00A0;ACTREF_CHK: displays too many deleted data elements<br />07/APR/98&#x00A0;&#x00A0; IX/&#x00A0;&#x00A0;Only FCS: Import administration data for online docu.<br />06/APR/98&#x00A0;&#x00A0; II/&#x00A0;&#x00A0;Retail customers: Read Note 94345 or 98585<br />06/APR/98&#x00A0;&#x00A0;IX/&#x00A0;&#x00A0;ReliantUNIX: HRTIME/HRVTIME at least 1000<br />06/APR/98 VIII/&#x00A0;&#x00A0;Only FCS, XPRAS_40B: Ignore syslog entries<br />06/APR/98&#x00A0;&#x00A0; IX/&#x00A0;&#x00A0;For serial numbers: Proceed according to Note 52994<br />06/APR/98 VIII/&#x00A0;&#x00A0;ACT_40B/SPDD: Transports QE1K900002, TCVK900029<br />06/APR/98&#x00A0;&#x00A0; IX/&#x00A0;&#x00A0;Hot Packages and upgrade adjusted together<br />06/APR/98&#x00A0;&#x00A0;VII/&#x00A0;&#x00A0;TOOLIMPD1 or TOOLIMPD2: Termination distribution error<br />17/MAR/98&#x00A0;&#x00A0;II/&#x00A0;&#x00A0;After Prepare no add-ons in source release<br />30/DEC/97&#x00A0;&#x00A0;&#x00A0;&#x00A0;I/&#x00A0;&#x00A0;R3up Password<br />----------------------------------------------------------------------<br /><br /><STRONG>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;The topics are<br /></STRONG><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;I/&#x00A0;&#x00A0;&#x00A0;&#x00A0;R3up Password (Key word)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;II/&#x00A0;&#x00A0; Important general comments<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;III/&#x00A0;&#x00A0;Correction of the guides<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;IV/&#x00A0;&#x00A0; Errors on CD ROM<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0; V/&#x00A0;&#x00A0;&#x00A0;&#x00A0;Checks before the upgrade<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;VI/&#x00A0;&#x00A0; Problems with the R/3 Upgrade Assistant<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;VII/&#x00A0;&#x00A0;Problems with Prepare<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;VIII/ Problems in the individual upgrade phases<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0; IX/&#x00A0;&#x00A0; Problems after the upgrade<br /><br /><br /><STRONG>I/&#x00A0;&#x00A0;&#x00A0;&#x00A0;R3up Password (key word)<br /><br /></STRONG><br />------------------------&lt; D019132 30/DEC/97&gt;-------------------------<br />* R3up Password (to be entered during the first call of R3up):&#x00A0;&#x00A0;90511<br />----------------------------------------------------------------------<br /><br /><STRONG>II/&#x00A0;&#x00A0; Important general comments<br /></STRONG><br /><br />------------------------&lt; D025323 27/NOV/01 &gt;-------------------------<br /><B>Import R3trans patch</B><br />To prevent the loss of join conditions during the upgrade, you have to replace the R3trans tool in the &lt;DIR_PUT&gt;/exe directory after PREPARE and before starting the upgrade.<br />For more information see <B>Note 444045</B>.<br /><br />------------------------&lt; D019144 14/APR/99 &gt;-------------------------<br /><B>4.0B Support Release 1 planned!</B><br /><br />We are planning to make the 4.0B Support Release available at the end of May.<br />It will contain the Hot Packages or Legal Change Patches HR up to and including patch number 13.<br /><br />Take this into consideration in your upgrade planning.<br />You can request the release as usual by contacting us.<br /><br />------------------------&lt; D022030 22/DEC/00 &gt;-------------------------<br /><B>Exchange of the SAP kernel</B><br /><br />An exchange of the SAP kernel deletes the contents of the kernel directory. For safety reasons you should create a backup copy of the directory prior to the upgrade. For more information please see the PREPARE log CHECKS.LOG.<br /><br />------------------------&lt; D021738 12/OCT/98 &gt;-------------------------<br /><B>Check hardware (CPU, main memory)</B><br /><br />The R/3 target release requires more hardware than your source release.<br />The additional hard disk requirement is described in the upgrade guide.<br />You may also require more main memory or improved processor operations.<br />Check this before the upgrade with the following tools:</p> <UL><LI><B>Note 89305 </B></LI></UL> <UL><LI>Quick Sizing in SAPNet<br />This is an interactive tool for a rough determination of the size of the required hardware. You find it in SAPNet via SELF SERVICE -&gt; International -&gt; Quick Sizing.<br /><br /></LI></UL> <p>--------changed---------&lt; D019132 12/JUN/98 &gt;-------------------------<br />------------------------&lt; D019132 09/JUN/98 &gt;-------------------------<br /><B>Incomplete export of individual documents (mostly source release 4.0A)</B><br /><br />Problem:&#x00A0;&#x00A0;Error in report RSPUSCAD when you export documentation in the<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;customer namespace, for example, IMG notes<br /><br />Solution: If the upgrade has already been completed, you can only<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;reimport the deleted documents from a second system or from<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;the restored copy of the source release.<br /><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;If you have not yet started with the upgrade or if you have<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;not yet executed phase JOB_RSPUSCAD, proceed as follows:<br /></p><OL>1. Make sure that Prepare is completed since the incorrect version of RSPUSCAD is imported in the Prepare module \"Import\".</OL> <OL>2. Download the corrected version of RSPUSCAD from sapservX in general/R3server/abap/note.0091709.</OL> <OL>3. Check the connection from R3trans as &lt;sapsid&gt;adm with the following call: R3trans -d</OL> <OL>4. If the Connect is successful, import the file RSPUSCAD.DAT with the following call: R3trans -i RSPUSCAD.DAT Note: the report load of the RSPUSCAD cannot be generated at this time; the report is only free of syntax errors during the call in the upgrade phase JOB_RSPUSCAD.</OL> <OL>5. Start the upgrade with R3up or, if it was already started, continue with the upgrade.<br /><br /></OL> <p>----------------------&lt; D019144 19/MAY/98 &gt;---------------------------<br /><B>4.0B upgrade documentation also on CD</B><br /><br />Title of CD&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Hot Package Collection 4.0B/1<br />Sent as of&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; approx. 22nd calendar week to all customers<br />Directory&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;DOCU<br />Format&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;PDF<br />Detailed information&#x00A0;&#x00A0;\\DOCU\\README.TXT<br />Platforms&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Documentation for AS/400, Windows NT and UNIX<br /><br /><br />------------------------&lt; D021250 15/MAY/98 &gt;-------------------------<br />--------changed---------&lt; D023648 04/JUN/98 &gt;-------------------------<br />Only when using the downward-compatible 3.1I kernel:<br /><br /><B>CAUTION: 3.1I kernel must have at least patch level 3 !!!!</B><br /><br />Problem: If you import a 3.1I kernel before upgrading to<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;R/3 Release 4.0B, it is essential that it has at<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;least patch level 3.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Otherwise data might be destroyed.<br /><br />Solution: download the newest 3.1I kernel patches from<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;sapservX as descibed in Note 19466.<br /><br /><br />------------------------&lt; D019132 10/NOV/98 &gt;-------------------------<br /><B>Only for HR Customers:</B></p> <UL><LI>If your source release contains <B>Legal Change Patches HR (LCPs HR)</B><br /><br />In this case an upgrade to Release 4.0B is only possible if the corresponding LCPs are also available in Release 4.0B.<br /><br />As of source Release 3.1H you are warned explicitly by R3up (phase PATCH_CHK), when you want to apply LCPs.<br />As of source Release 3.0F the warning no longer occurs. However, here you always have to apply the LCPs in question for Release 4.0B.<br />You find informationen on LCPs in <B>Note 73510.</B><br /><br />After confirming the intention to integrate patches in the upgrade in the SCROLL mode of R3up the superfluous and misleading query is displayed:<br /><br />Please enter one of the following options:<br />- Legal Change Patch<br /><br />Enter the exact text here (Upper/lower case, blanks),<br /><B>Legal Change Patch</B><br />R3up then proceeds correctly.<br /><br />To avoid unnecessary long runtimes in upgrade phase XPRAS_40B we recommend that you suppress the generation of applied LCPs. To do this use the Hold for SPDD or, as an alternative stop the upgrade before phase ACT_40B (Note 48184, remember that SPDD does not yet work at this place). Enter the following commands for each embedded LCP from the subdirectory 'bin' of the upgrade directory:<br /> tp&#x00A0;&#x00A0;modbuffer&#x00A0;&#x00A0;SAPKE40Bnn&#x00A0;&#x00A0;&lt;SID&gt;&#x00A0;&#x00A0;mode=G0<BR/> tp&#x00A0;&#x00A0;modbuffer&#x00A0;&#x00A0;SAPKE40Bnn&#x00A0;&#x00A0;mode=G0&#x00A0;&#x00A0;buffer=ACT40B.BUF<br />Here nn specifies the number of the applied LCPs (01, 02,...), &lt;SID&gt; specifies the system name. This manipulation suppresses the generation programs.</LI></UL> <UL><LI>If your source release does not contain <B>any Legal Change Patches HR (LCPs HR) </B>:<br /><br />In this case R3up also asks in the upgrade phase BIND_PATCH whether you want to apply LCPs for the target release.<br />Ignore the question.</LI></UL> <UL><LI>If your source release <B> Legal Change Patches HR (LCPs HR)</B> also contains one or several add-on products:<br />Including the LCPs requires <B> Conflict Resolution Transports (CRTs) </B>with conflicts between the add-on and the LCPs. These CRTs must be imported after the Upgrade. The documentation of the add-on supplier informs you about the requirements and the procedure.</LI></UL><p><br /><br />-------changed----------&lt; D023536 11/SEP/98 &gt;-------------------------<br />------------------------&lt; D024610 24/JUN/98 &gt;-------------------------<br /><B>Only for IS-IS customers</B><br /><br />Problem:&#x00A0;&#x00A0;Table REGUP is converted during the upgrade although this is<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;not necessary.<br />Solution: After successful execution of Prepare proceed as follows:</p> <OL>1. Edit the file SORTTABS.LST in the subdirectory bin of the upgrade directory (for UNIX /usr/sap/put/bin).<br />Insert the following line (left-aligned):<br />REGUP</OL> <OL>2. Edit the file R3UPPHAS.BRK in the subdirectory bin of the upgrad directory (for UNIX /usr/sap/put/bin). Add the following line (left justified):<br />PCON_40B.</OL> <OL>3. Only then start the upgrade with R3up.</OL> <OL>4. Upgrade stops before phase PCON_40B. If this happens, then activate in<br />the Dictionary (SE11) the table REGUP and continue with the upgrade.</OL> <p><br />------------------------&lt; D020738 06/APR/98&gt;------------------------<br /><B>Special features for SAP Retail customers</B><br /><br />If you have already been an SAP Retail customer or want to become an SAP Retail customer as of Release 4.0B, additional actions are necessary.<br /><br />Case 1: In your R/3 System the add-on SAP Retail (3.0F + Retail<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;1.2Bx) is installed before the upgrade to 4.0.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Then you should read <B>Note 94345.</B><br /><br />Case 2: Your source release is smaller than 4.0. You have not<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;installed any add-ons yet. After the upgrade to 4.0 you want<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;to use your R/3 System as SAP Retail.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;You have to implement <B>Note 98585</B> after the<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;upgrade to 4.0 (follow-up).<br /><br />Case 3: Your source release is 4.0A.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Furthermore, if your system is an SAP Retail, then it will<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;also be SAP Retail after the upgrade to 4.0B.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Implement <B>Note 98585</B> after the upgrade to 4.0B if your<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;system is not an SAP Retail system (follow-up).<br /><br /><br />------------------------ &lt; D019132 17/MAR/98&gt;-----------------------<br />Symptom: After executing Prepare, you can no longer install an add-on<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; or install an add-on update in the source release.<br /><br />Solution: <B>Note 98673</B><br /><br /><br />------------------------&lt; D019132 16/DEZ/98 &gt;-------------------------<br />Only for source release 3.1H and 3.1I<br />Problem:<br />The scope of the objects, wich are displayed in the adjustment transactions SPDD and SPAU erscheinen may be too large. By mistake also those objects have to be adjusted, which have been only changed by SAP within the Releases 3.x, but not for Rel. 4.0.<br /><br />Solution:<br />If the release comparison shows, that SAP has not made any changes, you can get back the old, modified version. This especially applies to jobs for modification&#x00A0;&#x00A0;(sometimes wrongly called \"Customer Exits\", but that is something different) predefined by SAP.<br />With these objects, do not go \"back to standard\", otherwise this modification will be lost again in the future.<br /><br />------------------------&lt; D019924 28/SEP/98 &gt;-------------------------<br />Only for source Release 3.1H:<br />Only if objects are to be compared with SPDD:<br /><br />Problem:<br />Do not create append structures <B> for the SPDD comparison for the tables KOMPAKE, KOMKAKE COPABBSEG</B>.<br />If the system suggests this, ignore the suggestion!<br />Otherwise subsequent problems may occur.<br /><br />Solution:<br />Go via&#x00A0;&#x00A0;-&gt;Utilities -&gt;Phase 1 &lt;-&gt; Phase 2&#x00A0;&#x00A0;(or F09)<br />directly to phase 2 of the comparison.<br />There an appropriate comparison is suggested without using the append structures.<br />Copy this. There you are requested to transfer the table to repair.<br /><br />If you already executed the comparison by creating an append structure then continue with the upgrade. When the upgrade is finished proceed as describes in <B>Note 118664 </B>.<br /><br />----------------------------------------------------------------------<br /><br /><STRONG>III/&#x00A0;&#x00A0;Correction of the guides<br /></STRONG><br />------------------------&lt; D019144 17/NOV/98 &gt;-------------------------<br />Where do I find the Release Info \"Migration of Matchcodes to search helps\"?<br /><br />The following guides are concerned:<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;\"Upgrade to Release 4.0B: UNIX\"<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;\"Upgrade to Release 4.0B: Windows NT\"<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;\"Upgrade from Release 3.1I to Release 4.0B: Windows NT\"<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;\"Upgrade to Release 4.0B: IBM AS/400\"<br /><br />Supplements to chapter 5 \"Follow up\", section \"Only for source release 3.0x/3.1x: Migration of Matchcodes to search helps\"<br /><br />The Release note \"Migration of Matchcodes to search helps\" has Release 4.0A. You can find it in the R/3 system via:<br />Help -&gt; Release notes -&gt; complete list Rel. 4.0 -&gt; 40A -&gt; Basis Components -&gt; ABAP Workbench -&gt; ABAP Dictionary -&gt; Activation Program, Conversion Program, DB-Utility, MC, SPDD -&gt; Migration of Matchcodes to search helps<br /><br />You can also find this Release note offline via a similar path on the documentation CD.<br /><br />------------------------&lt; D019144 11/NOV/98 &gt;-------------------------<br />Concerns: Guide \"Upgrade to Release 4.0B: UNIX\", Chapter 2<br /><br />Problem:<br />At the time of the guide generation the 3.1H kernel&#x00A0;&#x00A0;was the current downward-compatible R/3 kernel (AKK). By now this is the 3.1I kernel.<br /><br />Solution:<br />In general you have to <B>import the current AKK </B>always.<br />In the case of the 3.1I kernel make sure, that it has at least patch level 3.<br /><br />Correct the following pages in chapter 3 of the guide:<br /><br /><B>Page 2-4</B> in table \"Required notes for all databases\"<br />wrong:&#x00A0;&#x00A0;Note&#x00A0;&#x00A0;79376&#x00A0;&#x00A0;Installation 3.1H-Kernel<br />correct:&#x00A0;&#x00A0;Note 102445&#x00A0;&#x00A0;Installation 3.1I-Kernel<br /><br />wrong:&#x00A0;&#x00A0;Note 98787&#x00A0;&#x00A0;Documentation for CD 3.1H SAP KERNEL PATCH<br />correct:&#x00A0;&#x00A0;Note 112196 or 122548, depending on CD used<br /><br /><B>Page 2-14</B> DB2 for OS/390, Patch level of kernel<br />wrong:&#x00A0;&#x00A0;Your 3.1H kernel must have Patch level 55 or higher.<br />correct: Import the current AKK.<br /><br /><B>Page 2-15</B> INFORMIX<br />wrong:&#x00A0;&#x00A0;3. If ....., import the R/3-Kernel with Release 3.1H.<br />correct: 3. IF ....., import the current AKK.<br /><br /><B>Page 2-16</B> ORACLE, Section \"Procedure with source release 3.0x/3.1x\":<br />The statements in the grey note text are outdated by now.<br />The production operation with ORACLE 8.0.4 is also possible independent from the 4.0B upgrade. Detailed information is contained in note 98507.<br /><br /><B>Page 2-17</B> ORACLE<br />wrong:&#x00A0;&#x00A0;3. If ....., import the R/3 kernel Release 3.1H .<br />correct: 3. If ....., import the current AKK.<br /><br /><br />------------------------&lt; D019144 11/NOV/98 &gt;-------------------------<br />Only for <B>Source release 3.1I under UNIX:</B><br /><br />Problem:<br />when the guide \"Upgrade to Release 4.0B: UNIX\" was created it was not known, that also Release 3.1I will be possible as source release.<br /><br />Solution:<br />Correct the following pages in the guide:<br /><br />Page 2-10, for all databases:<br />Add Release 3.1I as possible source release.<br /><br />Page 2-13, DB2 common server:<br />Delete the section for DB2 common server and proceed instead as described in <B>Note 98597 </B>.<br /><br />Page 2-14, DB2 for OS/390:<br />Delete the section&#x00A0;&#x00A0;\"Patch level of the kernel\". Since you already apply kernel 3.1I (current AKK) no actions have to be taken.<br /><br />Page 2-15, INFORMIX:<br />Delete the steps 3, 4 and 5.<br />Since you are already on Rel. 3.1 you already use the AKK 3.1I and a database compatible with 4.0B.<br /><br />Page 2-17, ORACLE, \"Procedure with source release 3.0x/3.1x\":<br />Delete step c), since you already use kernel 3.1I (current AKK).<br />Delete step d). Instead import, if desired, the current 3.1I kernel patches.<br /><br />------------------------&lt; D019144 29/SEP/98 &gt;-------------------------<br />Regarding: Only the English version of the guide \"Upgrade to Release 4.0B: UNIX\"<br /><br />There is an error on page 2-8:<br />Not approx. 610 MB free <B>space in /usr/sap/put </B> is required, but <B>1.100 MB.</B><br /><br />------------------------&lt; D019144 25/AUG/98 &gt;-------------------------<br />Regarding: Guidelines \"Upgrade to Release 4.0B: UNIX\"<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;and \"Upgrade to Release 4.0B: Windows NT\"<br /><br />Internal job formats for Transaction DB13 do not change for all databases for Release 4.0B, contrary to what the guidelines say but only for INFORMIX and ORACLE.<br /><br /><B>Therefore, you have to delete the schedulings from DB13 in any case at the start of the downtime</B>, as descibed in appendix C, section \"Isolation of the central R/3 instance\".<br /><br />Following the upgrade you have to reschedule your jobs again with the changed DB13.<br /><br />------------------------&lt; D019144 06/AUG/98 &gt;-------------------------<br />Concerns: Guides \"Upgrading to Release 4.0B: UNIX\"<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; and \"Upgrading to Release 4.0B: Windows NT\"<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;and \"Upgrading to Release 4.0B: IBM AS/400\"<br /><br />Error in appendix A in the description of phase READDATA<br />(Prepare module import):<br /><br />wrong:&#x00A0;&#x00A0;&#x00A0;&#x00A0;Data is read by the CD \"SAP Kernel\"<br />correct:&#x00A0;&#x00A0; Data is read by the CD \"Report Load\"<br /><br />------------------------&lt; D019144 12/JUN/98 &gt;-------------------------<br />Only for source release 3.0x/3.1x:<br /><br />Problem: <B>Incorrect menu texts for Transaction ICNV</B><br /><br />Guide:&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Upgrade to Release 4.0B (AS/400, Windows NT, UNIX)<br />Chapter:&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; Subsequent Processing<br />Section:&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; Special Handling of Table Clusters EDIDOC und CDCLS<br />Subsection:&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Converting Tables EDIDOC and CDCLS (AS/400)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Controlling the conversion (Windows NT, UNIX)<br /><br />Solution: Correct the following menu texts:<br /><br />wrong&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;: Control-&gt;Data transfer-&gt;Optimize processes<br />correct&#x00A0;&#x00A0;&#x00A0;&#x00A0;: Control-&gt;Data transfer-&gt;Optimize<br /><br />wrong&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;: Control-&gt;Switch to new table<br />correct&#x00A0;&#x00A0;&#x00A0;&#x00A0;: Control-&gt;Switch<br /><br />wrong&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;: Control-&gt;Finish conversion<br />correct&#x00A0;&#x00A0;&#x00A0;&#x00A0;: Control-&gt;Delete entry<br /><br />------------------------&lt; ******** 16/JUN/98 &gt;------------------------<br />Only for <B>ReliantUNIX</B>:<br /><br />Subject: Guide \"Upgrading to Release 4.0B: UNIX\", page 3-31.<br /><br />The runtime system ReliantUNIX <B>C++ Runtime System</B>must be at least <B>version 1.0C32</B>.<br /><br />----------------------------------------------------------------------<br /><br /><STRONG>IV/&#x00A0;&#x00A0; Errors on the CD ROM<br /></STRONG><br />----------------------------------------------------------------------<br /><br /><STRONG>V/&#x00A0;&#x00A0;&#x00A0;&#x00A0;Checks before the upgrade<br /></STRONG><br />------------------------&lt; D022803 16/APR/02 &gt;-------------------------<br /><B>Do not include Basis Support Package 72 as the highest Support Package</B><br />When you include Basis Support Package 72 into the upgrade as the highest Support Package, Transaction SPDD cannot be used to compare tables. In this case, a syntax error occurs in program SAPLTMSC.<br />For further information refer to <B>Note 512241</B>. Under 'References to Support Packages', you can find the corresponding Support Package which contains the solution to the problem.<br /><br />------------------------&lt; D025323 23/FEB/01 &gt;-------------------------<br />To avoid the loss of table fields or data, you must attach a certain minimum number of support packages to the upgrade with certain start releases.<br />This affects source releases:<br />&#x00A0;&#x00A0; 31I&#x00A0;&#x00A0; from package SAPKH31I68<br />&#x00A0;&#x00A0;40B&#x00A0;&#x00A0; from package SAPKH40B58<br />&#x00A0;&#x00A0;45B&#x00A0;&#x00A0; from package SAPKH45B35<br />&#x00A0;&#x00A0; 46B<br />You will find further information in <B>Note 383126</B>.<br /> ------------------------&lt; D029407 02/FEB/01 &gt;-------------------------<br /><B>Only for HR customers with payroll for construction industry </B><br />All entries in Table T5DB4 are deleted for an upgrade with source<br />release 3.1I. Before you update, save the data in the database and<br />reload it again after the update.<br />For more information, see <B>Note 156568</B>.<br /><br />------------------------&lt; D021177 04/OCT/00 &gt;-------------------------<br /><B>ICNV of cluster tables</B><br /><br />When incrementally converting cluster tables by means of transaction ICNV data may partially be lost in very rare cases due to a program error. For more information please see <B>Note 106412</B>.<br /><br />------------------------&lt; D022600 29/MAR/99 &gt;-------------------------<br /><B>Only for HR customers with source Release 3.0x/3.1x: </B><br /><br />Problem: The person group key in the DUV table of cluster RD&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;is emptied during the upgrade.<br /><br />Solution: Described in <B>Note 141319</B><br /><br />------------------------&lt; D023078 26/JAN/99 &gt;-------------------------<br /><B>Only for overhead calculation in CO, table COEP: </B><br /><br />Problem: long runtime of upgrade phase PCON_40B<br />Solution:&#x00A0;&#x00A0;can be found in <B>Note 135132 </B><br /><br />------------------------&lt; D025533 21/DEC/98 &gt;-------------------------<br /><B>SAPoffice / appointments diary: avoid loss of calender notes</B><br /><br />Problem: During the upgrade old calender notes are lost.<br />Solution:&#x00A0;&#x00A0;can be found in <B>Note 131530 </B><br /><br />------------------------&lt; D000706 07/DEC/98 &gt;-------------------------<br /><B>Upgrade can destroy customer-defined report variants </B><br /><br />Problem:<br />During the upgrade customer-defined report variants can be destroyed. The variants are not completely deleted, however, administration information is lost.<br /><br />Solution: can be found in <B>Note 128589</B><br /><br />------------------------&lt; D023441 28/SEP/98 &gt;-------------------------<br /><B>Saving events to table maintenace dialoges</B><br /><br />Only for customers who:</p> <UL><LI>generated their own dialogs for the view and table maintenance using Transaction SE54, indepent of the fact whether customer or SAP tables were concerned.</LI></UL> <UL><LI>In addition used events for the modification of their dialogs.<br /></LI></UL> <p>Problem:<br />During the upgrade events for customer-specific generated table maintenance dialogs are overwritten.<br /><br />Solution:<br />Save the data in question before the upgrade as described in <B>Note 118435</B>.<br /><br />------------------------&lt; D026122 15/SEP/98 &gt;-------------------------<br />Only for source Release 3.0x/3.1x:<br /><B>Process batch input sessions</B>before the upgrade<br /><br />Problem:<br />Due to a change to the data structure old session can no longer be processed after the upgrade.<br /><br />Solution:<br />Make sure that the batch input session are processed completely before the upgrade.<br /><br />------------------------&lt; D019780 01/SEP/98 &gt;-------------------------<br /><br />Symptom: Workload statistics (ST03) are deleted during the upgrade<br /><br />Solution: If you still require the workload statistics from Release<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;3.0x/3.1x after the upgrade, then proceed before the upgrade<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;as described in <B>Note 115364 </B>.<br /><br /><br />----------------------&lt; D020694 26/AUG/98&gt;-----------------------------<br /><B>Only for SAP Retail Release 1.2x:</B><br /><br />Problem:<br />By changing the field name of one of the key fields in the module text table WSOT the entries in this table will only be partially copied during the upgrade. It can lead to problems during the maintenance of manual assortment modules, among other things.<br /><br />Solution: Proceed as described in <B>Note 114346</B>before carrying out the upgrade.<br /><br />------------------------&lt; D001532 27/JUL/98 &gt;------------------------<br /><B>Only for customer-specific matchcode IDs:</B><br /><br />Symptom: After the upgrade the definitions of customer-specific<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;matchcode IDs are incomplete. The DD29L entries are missing<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;although the log SAPECCC40B.&lt;sid&gt; shows that these entries<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; were copied.<br /><br />Solution: contained in <B>Note 109059 </B><br /><br />------------------------&lt; D019301 03/JUL/98 &gt;------------------------<br />Only for <B>source release 4.0A:</B><br /><br /><B>Deleting the administration data for the online documentation </B><br /><br />If you have imported the administration data for the online documentation in Release 4.0A in accordance with the guide \"Installation of the online documentation\" to your system, we recommend that you delete this administration data <B>prior to the downtime</B>. This time is important since the administration data is used in Release 4.0A.<br /><br />Reason:<br />This administration data is not used in Release 4.0B. The tables containing this data should be emptied (but not deleted!) to save<br />space.<br /><br />Advantages:<br />If these tables are filled with contents, the upgrade can take 2-3 hours longer.<br />Furthermore, an <B>overflow of rollback segments in phase TABIM_40B</B> can occur with database ORACLE (for example with ora-1652).<br /><br />Proceed as follows:</p> <OL>1. Carry out an upgrade until the beginning of the downtime. Add the following steps when \"isolating the central R/3 instance\".</OL> <OL>2. Start Transaction SE14.</OL> <OL>3. For the following database tables first carry out the function 'Delete' and then the function 'Create'.<br /><br />&#x00A0;&#x00A0;IWB1PHF<br />&#x00A0;&#x00A0; IWB1PHHR<br />&#x00A0;&#x00A0; IWB1PHIO<br />&#x00A0;&#x00A0; IWB1PHPR<br />&#x00A0;&#x00A0; IWB1PHRE<br /></OL> <p>NOTE:<br />These tables can also be deleted after the upgrade to Release 4.0B.<br /><br />------------------------&lt; D000706 17/JUN/98 &gt;------------------------<br />Only if you have <B>separate short texts for screens</B>:<br /><br />Problem: During the upgrade the short texts of customer-specific<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; screens are lost.<br /><br />Solution: in <B>Note 92665</B><br /><br />------------------------&lt; D023648 22/MAY/98 &gt;------------------------<br />Only for <B>ReliantUNIX: </B><br /><br />Before the upgrade make sure that the instance profiles of all R/3 instances contain the following line: &#x00A0;&#x00A0; /install/os_version/ReliantUNIX = SINIX<br />Further information is contained in <B>Note 39745. </B><br /><br />------------------------&lt; D019132 04/MAY/98 &gt;-------------------------<br /><B>Hot Packages check deactivated</B><br /><br />Symptom:&#x00A0;&#x00A0;Unconfirmed Hot Packages and Hot Packages which are newer<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;than Release 4.0B are not reported. The check is<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;restricted to HR Legal Change Patches by mistake.<br /><br />Solution: You can manually check the status of Hot Packages by<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;means of Transaction SPAM. The highest Hot Package<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;applied must be confirmed. Afterwards check<br /> <B>Note 73510</B> with regard to an exception condition<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;for this status (source release, Hot Package xx)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;and for the upgrade to 4.0B. The following<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Hot Package statuses are supported in all cases:<br /><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;HOTMAX: 3.0D&#x00A0;&#x00A0;43<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;HOTMAX: 3.0E&#x00A0;&#x00A0;25<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;HOTMAX: 3.0F&#x00A0;&#x00A0;29<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;HOTMAX: 3.1G&#x00A0;&#x00A0;11<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;HOTMAX: 3.1H&#x00A0;&#x00A0;14<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;HOTMAX: 4.0A&#x00A0;&#x00A0;00<br /><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;If the highest Hot Package applied is smaller that or equal to<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; the specified number, you do not need <B>Note 73510</B>.<br /><br />-------------------------&lt;D001634 19/MAY/98&gt;--------------------------<br />Only for source releases smaller than 3.1H:<br /><br />Problem: Table T510Q may contain non-numerical entries in field<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;T510Q-BETRG for clients not equal to '000'.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;These would cause <B>termination of the upgrades in phase</B><br /><B>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; PCON_40B</B> during table conversion.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;The following runtime error would occur: CONUT_NO_NUMBER<br /><br />Solution: Obtain <B>Note 83728</B> before the upgrade.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Follow the instructions in that note to avoid a termination<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;in phase PCON_40B.<br /><br />--------------------&lt; D023913 29/OCT/98 &gt;------------------------------<br /><B>Only for HR-Customers:</B><br /><br />Problem: The text of customer-specific processing and evaluation<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; classes may be lost.<br /><br />Solution: Proceed as described in <B>Note 118310</B> before starting<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;the upgrade.<br /><br />-----------------------------------------------------------------------<br /><br /><STRONG>VI/&#x00A0;&#x00A0; Problems with the R/3 Upgrade Assistant<br /></STRONG><br />----------------------&lt; D024648 07/SEP/98 &gt;----------------------------<br /><B>Only for digital UNIX 3.2: </B><br /><br />Since there is no porting of Java Virtual Machine Version 1.1.x for digital UNIX 3.2, the R/3 upgrade cannot be used if the PREPARE should be carried out before the operating system upgrade to digital UNIX 4.0. In this case you must use Scroll mode.<br /><br />------------------------&lt; D024648 04/JUN/98 &gt;-------------------------<br /><B>Only for AIX:</B><br /><br />For AIX, you should use the Version 1.1.4 or higher of the Java Virtual Machine (JDK or JRE). Otherwise the error can occur that UaGui cannot connect the UaServer.<br /><br /><br />------------------------&lt; D021848 14/APR/98&gt;--------------------------<br /><B>Only for Windows NT:</B><br /><br />Problem:<br />If you zoom out the R/3 upgrade assistant to icon size and R3up demands an entry during this time, the corresponding input dialog is no longer created when you zoom in.<br />After you zoom in, the output window contains an entry as a last line \"Waiting for input since ... \" , however, no input dialog is set up on the administrator GUI.<br /><br />Solution:<br />Do not zoom out the R/3 upgrade assistant to icon size. If the status described above has already occurred, create the input dialog by executing the following menu options on the administrator GUI:<br />&#x00A0;&#x00A0;Administrator -&gt; Disconnect from R3up<br />&#x00A0;&#x00A0;Administrator -&gt; Connect to R3up<br /><br />------------------------&lt; D021848 24/AUG/98 &gt;-------------------------<br /><B>Only for Windows NT:</B><br /><br />Problem:<br />When you first execute Prepare a number of CAR packages is unpacked (Prepare module import, phase READDATA). The routines which carry out the unpacking make text outputs in what is known as standard output. These outputs can also be seen in scroll mode.<br /><br />If Prepare was started via the R/3 Upgrade Assistant (Menu Administrator -&gt; Start Prepare) this causes Prepare to \"stop\" under NT since the text outputs could not be carried out.<br /><br />Solution:<br />Proceed as follows:</p> <OL>1. During the first execution of phase READDATA (Prepare module import)<br />start Prepare manually in an MSDOS box.</OL> <OL>2. Choose the SERVER mode.</OL> <OL>3. Select Administrator -&gt; Connect to R3up<br />in the GUI of the Upgrade Assistant in the main menu.</OL> <OL>4. Execute Prepare as usual from the Upgrade Assistant.<br /></OL> <p>------------------------&lt; D021848 06/AUG/98 &gt;-------------------------<br />------------------------&lt; D021848 27/APR/98 &gt;-------------------------<br /><B>Only for HP-UX:</B><br /><br />Problem:<br />In version 1.1.x of the Java Virtual Machine (JDK or JRE) on HP-UK the stack size was reduced from 1 MB to 126 KB to improve performance. In the case of the Upgrade Assistant (both GUI and server) this can cause a stack overflow. The stack overflows by triggering a stack overflow exception and even creating a core file.<br /><br />Solution:<br />The stack size can be changed externally. The calls for GUI and server are then:<br /> &#x00A0;&#x00A0; jre -nojit -ss256k -cp /usr/sap/put/ua/ua.zip&#x00A0;&#x00A0;&#x00A0;&#x00A0;UaServer<BR/> &#x00A0;&#x00A0; jre -nojit -ss256k -cp /usr/sap/put/ua/uagui.zip UaGui<br />The version of the Java Virtual Machine can be determined with the following call:<br /> &#x00A0;&#x00A0; jre -version<br />------------------------&lt; D021848 27/APR/98 &gt;-------------------------<br />Only when the <B>MS Internet Explorer 4.0</B> is being used:<br /><br />As of the version of the R/3 Upgrade Assistant that is delivered with the 4.0B upgrade is it possible to download and execute the R/3 Upgrade Assistant GUI (abbrev. UaGui) with the MS Internet Explorer 4.0 without taking further action.<br />The URL to be used is<br /><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0; http://&lt;Internet name of the upgrade computer&gt;/ua/UaGui.html<br /><br />The MS Internet Explorer 4.0 must have at least version 4.72.2106.8. You can determine the version number of the MS Internet Explorer 4.0 via the following menu:<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0; Help -&gt; About Internet Explorer<br /><br />For lower versions the error is displayed to the effect that the UaGui does not accept any more entries after the logon.<br /><br />-----------------addition---&lt; D019132 31/OCT/98 &gt;--------------------<br /><br />The Internet Explorer is not supported on Windows 3.11.<br /><br />----------------------------------------------------------------------<br /><br /><br /><STRONG>VII/&#x00A0;&#x00A0;Problems with Prepare<br /></STRONG><br />------------------------&lt; D019132 01/JUL/98 &gt;-------------------------<br /><B>RFC-Error: error correction does not work</B><br /><br />Symptom: The cause for the RFC-error was eliminated (for example, &#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;system start) yet RFC continous to fail.<br />Solution: Leave R3up with CANCEL, ..., EXIT and start R3up again. The &#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;error no longer occurs.<br /><br />------------------------&lt; D019132 17/JUN/98 &gt;-------------------------<br /><B>Do not execute Prepare module \"Parameter Input\" on its own</B><br /><br />Symptom: The extraction of the language CD terminates in phase<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; LANG_REQ with the exception NO_DELTA_REL. This is caused by<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; the Prepare module \"Parameter Input\" being repeated on its<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; own. By mistake, R3up does not request the repetition of<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;the following modules (see guide, page 3.3)<br /><br />Solution: Repeat all Prepare modules from the beginning. It is<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;possible to change the parameters at any time using<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;the R3up call \"R3up set stdpar\" (the current R3up<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;must be terminated).<br /><br />------------------------&lt; D019132 17/MAR/98&gt;--------------------------<br /><B>Prepare module Import, phase TOOLIMPD1 or TOOLIMPD2</B><br /><br />Symptom: Termination with distribution error<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;(error in log DS&lt;date&gt;.&lt;SAPSID&gt;)<br /><br />Solution: <B>Note 98198</B><br /><br />------------------------&lt; D018403 15/MAY/98 &gt;-------------------------<br /><B>Prepare module CD read, Phase BIND_PATCH </B><br /><br />Symptom: Transaction SPAM terminates.<br /><br />Solution: <B>Note 99300 </B><br /><br />------------------------&lt; D019983 20/APR/98 &gt;-------------------------<br /><B>Only for FCS (First Customer Shipment):</B><br />Prepare module general checks, phase REPACHK<br /><br />Symptom: Prepare determines SAP objects which are locked in tasks or<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; requests. Their release in the Workbench Organizer fails,<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; however with the following error message:<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;TR811&#x00A0;&#x00A0;Requests cannot be released during the upgrade<br /><br />Solution:&#x00A0;&#x00A0; Proceed as follows:</p> <OL>1. set Prepare completely as described in the upgrade guide on page 3-13, section \"Resetting Prepare\".</OL> <OL>2. Release <B>all </B>tasks and requests in which SAP objects are locked.</OL> <OL>3. Start Prepare again.<br /></OL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; Note: Before starting the upgrade with R3up make sure that<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; no more SAP objects are changed.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; If R3up determines open tasks or requests again in phase<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; REPACHK1 or REPACHK2, you can no longer release them.<br /><br />------------------------&lt; D021091 21/APR/98 &gt;-------------------------<br />----------changed-------&lt; D021091 28/APR/98 &gt;-------------------------<br />Only when starting 3.0x/3.1x:<br />Prepare module Activation checks, Phase JOB_RADDRCHK<br /><br />WARNING: If the following message occurs in log RADDRCHK.&lt;SAPSID&gt; :<BR/> <B>TG063: naming conflict for name &amp; will cause activation error</B><br /><br />&#x00A0;&#x00A0; you <B>must process this during the upgrade preparation !</B><br /><br />Otherwise there will be activation errors in tables/structures in phase ACT_40B of the upgrade which are based on naming conflicts with data elements (Message DT121 or D0751). These errors can normally only be eliminated by SAP.<br /><br />If messages in Prepare were ignored, what should you do if errors occur in phase ACT_40B?<br /><br />In certain error scenarios the naming conflict can be eliminated by creating the data element under a new name and replacing every instance of the old name with the new name. This is the case when a customer has created a data element in SAP namespace (see long text of message TG063).<br /><br />If the data element is used in a table/structure of the same name this has the following consequences:</p> <UL><LI>The data element cannot be deleted because it is still being used.</LI></UL> <UL><LI>The table/structure in question can be changed, however when it is activated, it causes an error message (DT121) because the naming conflict with the data element still exists.<br /></LI></UL> <p>In this case you must contact SAP!<br /><br />The table must be activated with the help of SAP by a force activation. Only then can the data element affected be deleted and the naming conflict be eliminated.<br /><br />------------------------&lt; D021091 29/MAR/99 &gt;-------------------------<br /><B>PREPARE module activation checks, phase JOB_RADDRCHK </B><br /><br />Symptom:<br />While preparing the upgrade, namespace conflicts for the following objects were detected:</p> <UL><LI>Table and data element <B>BAMUIVIEW </B></LI></UL> <UL><LI>Table and data element <B>SQLRTAB</B><br /></LI></UL> <p>The the following messages appear in system log RADDRCHK.&lt;SAPSID&gt;:</p> <UL><LI>TG063: Naming conflict for name BAMUIVIEW leads to activation error</LI></UL> <UL><LI>TG063: Naming conflict for name SQLRTAB leads to activation error<br /><br />Solution: Refer to <B>Note 133017 </B><br /><br /></LI></UL> <p>------changed-----------&lt; D021091 12/JUN/98 &gt;-------------------------<br />------------------------&lt; D021091 09/APR/98&gt;-----------------------<br /><B>Prepare module activation checks, phase ACTREF_CHK</B><br /><br />Problem:<br />Too many deleted data elements are displayed when you check customer references to deleted SAP dictionary objects.<br /><br />Solution:<br />If a customer reference is displayed on an SAP data element, check whether the name of the data element appears in table DDDTRENUPG in field NAME_OLD. The message can be ignored in this case.<br /><br /><br /><br />------------------------&lt; D021842 14/SEP/98 &gt;-------------------------<br /><B>PREPARE module Necessary checks for conversions, phase CNV_AVOID</B><br /><br />Symptom:<br />The PREPARE module recommends in file CHECKS.LOG, to minimize the size of a pool table (for example ATAB) with the R/3 Note 76431.<br />Example (extract form CHECKS.LOG):<br /><br />#====================================================================#<br /># Requests and information for module Necessary checks for conversion#<br />#====================================================================#<br />The following tables of your R/3 System are very large.<br />They can be reduced in size to minimize the conversion time during the<br />upgrade. Please carry out the actions recommended in OSS Note 76431 and<br />related notes to reduce the downtime of the upgrade.<br />The names of the tables are:<br />table name&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;table size (MB)<br />ATAB&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;74.00<br /><br />Solution:<br />The tables listed here that are not explicitly named in <B>Note 76431 </B> can be ignored, since no special recommendations for the data reductions exist up to now.<br /><br />----------------------------------------------------------------------<br /><br /><STRONG>VIII/&#x00A0;&#x00A0;Problems in the individual upgrade phases<br /><br />------------------------&lt; D028953 20/DEZ/99 &gt;-------------------------<br />Phase <B>PCON_31I </B><br /><br />Problems: Warnings or terminations can occur because of table EWUPAK<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;(loss of customer data)<br /><br />Solution: Refer to <B>Note 190527 </B><br /></STRONG><br />------------------------&lt; C1016615 21/SEP/98 &gt;------------------------<br />Only for <B>ReliantUNIX:</B> Upgrade or Import hangs<br /><br />Problem:<br />In the log&#x00A0;&#x00A0;/usr/sap/put/log/SLOG40B or R3up.ECO the following messages are generated:<br /><br />ERROR:&#x00A0;&#x00A0;Background job RDDIMPDP could not be started or terminated<br />ERROR:&#x00A0;&#x00A0;Please check that the R/3 system is running.<br />ERROR:&#x00A0;&#x00A0; Please check the system. Use transactions SM21, SM37, SM50..<br /><br />Solution:</p> <OL>1. Carry out the checks suggested by the upgrade program.</OL> <OL>2. Only if 1. is not successful exchange program sapevt in the R/3 kernle directory. <B>Note 67482</B>describes where you find the progrm on sapservX.<br /></OL> <p>------------------------&lt; D020847 11/SEP/98 &gt;-------------------------<br /><B>Phase STARTR3_NBAS</B><br /><br />Symptom:<br />When you start the R/3 kernels under R/3 Release 4.x all work processes die. In the developertrace \\usr\\sap\\&lt;SID&gt;\\&lt;INSTANZ&gt;\\work\\stderr2 you find the following entry:<br />*** ERROR =&gt; E:\\usr\\sap\\DEV\\SYS\\exe\\run\\disp+work.exe stopped:<br />DpCheckProfile [dpxxdisp 0658]<br />Get security of file: E:\\usr\\sap\\DEV\\SYS\\exe\\run\\disp+work.exe succeed.<br /><br />Solution: <B>Note 105153</B><br /><br />------------------------&lt; D001330 24/DEZ/00 &gt;-------------------------<br /><B>Phase ACT_40B</B><br /><br />This can only occur if Hot Packages or LCPs up to and including 35 are contained in the upgrade.<br /><br />Symptom: Table S404 cannot be activated since reference fields are<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; missing.<br /><br />Solution: Proceed as described in <B>Note 195424</B>.<br /><br />------------------------&lt; D022085 06/APR/98&gt;------------------------<br /><B>Phase ACT_40B/adjustment with SPDD</B><br />It can only occur with source release 3.0D or 3.0E:<br /><br />Symptom: In SPDD, transports QE1K900002 or TCVK900029<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; are displayed by mistake as customer modification.<br /><br />Solution: <B>Note 74831 </B><br /><br />------------------------&lt; D019132 05/AUG/98 &gt;-------------------------<br />Phase <B>ACT_40B</B><br /><br />Symptom: Table DBSTATC exists on the database with an incorrect<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;structure. The error is only realized later by the upgrade<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; proces in <B>TABIM_40B</B> but it can be prevented at the<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;time of the SPDD adjustment.<br /><br />Solution: Use Transaction SE14 to check the consistency of the<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;database (it must be 16 fields). If 3 fields are missing<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;select the option \"Delete data\" and execute the function<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;\"Activate and adapt database\".<br /><br />------------------------&lt; D019132 13/MAY/98 &gt;-------------------------<br /><B>Phase PCON_40B</B><br />Symptom:&#x00A0;&#x00A0;After a termination of the conversion as a result, for<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;example, of insufficent resources on the database,<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;terminations occur in the R/3 System.<br />Solution: Restarting the R/3 System removes the symptom. The problem is<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;caused by inconsistent buffers which were not invalidated<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;for performace reasons.<br /><br />------------------------&lt; D019924 30/JUN/98 &gt;-------------------------<br /><B>Phase PCON_40B</B><br /><br />Symptom: The upgrade stops in this phase.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; In the log you find one of the following messages:<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;TG450 to TG453 \"Table &lt;tabname&gt; is losing customer fields,...\"<br /><br />Solution: Proceed as described in <B>Note 73999</B><br /><br />------------------------&lt; D023648 24/SEP/98 &gt;-------------------------<br /><B>Phase PCON_40B</B><br /><br />PCON terminates with inckonsistencies on TTODIR/LTODIR<br /><br />Symptom:<br />The upgrade stops in this phase. In the log file PCON40B.ELG you find in section DS&lt;date&gt;.&lt;SAPSID&gt; the following lines:<br /> ...<BR/> 4 EDA470 Edited object: \"LTODIR\"<BR/> 4 EDA437 Object: \"LTODIR\" Object type: \"TRANSP\" is in the database<BR/> 2EEDA428 An SQL error occurred during a test access of the table<BR/> 2EEDA427 An inconsistency between the nametab and database may exist<BR/> ...<br /><br />The same messages you find in table TTODIR.<br /><br />Solution: is in <B>Note 115979 </B><br /><br />------------------------&lt; D022085 06/APR/98&gt;-----------------------<br />Only for FCS (First Customer Shipment):<br /><B>Phase XPRAS_40B</B><br /><br />Symptom: The XPRA RGZZGLUX writes the following entries to the<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;system log (SYSLOG):<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Output device \"LP01\" is unknown<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Spool: Invalid entries for output device<br /><br />Solution: Ignore these entries.<br /><br />------------------------&lt; D000546 21/SEP/98 &gt;-------------------------<br /><B>Phase XPRAS_40B </B><br /><br />Problem: Job RDDEXECL in program RPRTRV10 terminates.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; Various error messages may be generated.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Thus phase XPRAS_40B is not finished successfully.<br /><br />Symptoms and solution: see <B>Note 109469 </B><br /><br />------------------------&lt; D019301 20/APR/98 &gt;-------------------------<br /><B>Phase XPRAS_40B </B><br /><br />Symptom: The XPRA RGZZGLUX writes the following entries to the<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; log of phase XPRAS_40B:<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; EL4181 Problems occurred with entries in the following tables<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; EPU139 Postprocessing necessary when upgrade has been<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;completed\"L4\"\"18<br /><br />Solution:&#x00A0;&#x00A0;Ignore these entries.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; For more information read <B>Note 101242.</B><br /><br />------------------------&lt; D021243 24/JUN/98 &gt;-------------------------<br /><B>Phase XPRAS_40B</B><br /><br />Symptom: The XPRA RGXGBRS4 writes the following entries in the log of<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;phase XPRAS_40B:<br /><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; GU093 Application: FI-SL Client 003, XPRA RGXGBRS4<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;-&gt; Read long text<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; GB209 Syntax error in logical expression HL-TEST<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; GU093 Application: FI-SL Client 003, XPRA RGXGBRS4<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;-&gt; Read long text<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; GB209 Syntax error in logical expression HL-TEST1<br /><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;(GU093 is a P message, GB209 is an I message).<br /><br />Solution: Ignore these entries<br /><br />------------------------&lt; D021091 20/JUL/98 &gt;-------------------------<br /><B>Phase JOB_DRDOC_40B </B><br /><br />Symptom: the phase hangs or terminates.<br /><br />Solution: is contained in <B>Note 108728</B><br /><br />----------------------------------------------------------------------<br /><br /><STRONG>IX/&#x00A0;&#x00A0; Problems after the upgrade<br /></STRONG><br />--------------------------&lt; D021867 08/JAN/01 &gt;-----------------------<br /><B>Component PS (CIM) only:</B> Wrong screens processed<br />In some transactions, an error may occur in next screen processing after an upgrade from 3.0F. For more information please see <B>Note 370862.</B><br /><br />--------------------------&lt; D019141 26/OCT/98 &gt;-----------------------<br />Conversion of CDCLS and EDIDOC:<br /><br />Make sure to read <B>Note 106412</B> \"Conversion of tables EDIDOC &amp; CDCLS, Release 4.0B\" for the follow-up processing of the upgrade.<br />Ignoring the note may result in partial <B> dataloss</B>.<br /><br />------------------------&lt; D019718 12/AUG/98 &gt;-------------------------<br />Care with <B>Modifications in SAPMP56R and SAPMP56S</B><br /><br />Problem: You have made modifications within the module pools SAPMP56R<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; and SAPMP56S. These module pools are deleted in Release<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 4.0A and replaced by SAPMP56T. If you have made modifications<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; in SAPMP56R or SAPMP56S, these are not displayed with<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Transaction SPAU and are deleted during the upgrade.<br /><br />Solution: Proceed as described in <B>Note 113045</B>.<br /><br />------------------------&lt; D000674 07/AUG/98 &gt;-------------------------<br /><B>Reschedule jobs prior to 4.0B from DB13</B><br /><br />Problem: If you have not followed the instructions in the upgrade guide<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; to delete the jobs from the CCMS Transaction<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;DB13 which come from releases prior to 4.0B,<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;you must do this now.<br /><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; Otherwise DB13 may terminate with the runtime error<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;CONNE_IMPORT_WRONG_FIELD_TYPE.<br /><br />Solution: After the upgrade you can only delete these jobs using<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Transaction SM37. If you have already scheduled other<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;jobs with the changed Transaction DB13, and now have a<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;mixture of old and new jobs, these new jobs are later<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;deleted.<br /><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;The exact procedure is described in <B>Note 112571.</B><br /><br />------------------------&lt; D023461 15/MAY/98 &gt;-------------------------<br /><B>Applying Hot Packages </B><br /><br />Symptom: When applying a Hot Package, the phase<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 'ABAP/Dynp generation' is exited with return code 8.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;The log points to syntax errors in one or several<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; ABAP programs.<br /><br />Solution: <B>Note 103585</B><br /><br />------------------------&lt; D022578 13/MAY/98 &gt;-------------------------<br />Only for source release 3.0x/3.1x:<br /><B>Workitems missing after upgrade</B><br /><br />Symptom: If you started workflows in Releases 3.0D-3.0F, 3.1G-3.1H<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; and if you carried out an upgrade to 4.0A or 4.0B for already<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; existing workitems, the respective workitems may no longer<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;occur in the inbox of the user where they were found prior to<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; the Upgrade.<br /><br />Solution: <B>Note 103720</B><br /><br />------------------------&lt; D021211 07/APR/98&gt;----------------------<br />Only for FCS (First Customer Shipment):<br /><br />To display the online documentation (HTML), import the administration data after the upgrade into your R/3 System.<br /><br />The following transport request contains administration data:<br />CD&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;REPORT LOAD<br />Directory&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;FIXES<br />Request&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;SAPK40BNN2<br /><br />For the import proceed as follows:</p> <OL>1. Use the global transport directory for the import (for UNIX and AS/400 /usr/sap/trans).</OL> <OL>2. Ensure, that your R/3 System is entered correctly in file TPPARAM.</OL> <OL><OL>a) To do this change to the directory: AS/400:&#x00A0;&#x00A0;&#x00A0;&#x00A0; as &lt;SID&gt;OFR with:&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;cd '/usr/sap/trans/bin'<BR/> UNIX:&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; as &lt;sapsid&gt;adm with:&#x00A0;&#x00A0; cd /usr/sap/trans/bin<BR/> Windows NT: as &lt;sapsid&gt;adm with:&#x00A0;&#x00A0; cd \\usr\\sap\\trans\\bin</OL></OL> <OL><OL>b) Use the following command to test whether program tp can connect a link to the database:<br />AS/400:&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;tp 'connect &lt;SID&gt;'<br />UNIX and Windows NT:&#x00A0;&#x00A0;tp connect &lt;SAPSID&gt;<br />The following message must be displayed:<br />&#x00A0;&#x00A0; Connection to database of &lt;SAPSID&gt; was successful</OL></OL> <OL>3. Copy data file R40BNN2.SAP and information file K40BNN2.SAP from the CD \"REPORT LOAD\" (directory FIXES) into subdirectory data or cofiles of the global transport directory: AS/400:&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;CPY OBJ('&lt;CD-Dir&gt;/FIXES/R40BNN2.SAP')<BR/> &#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; TODIR('../data')<BR/> &#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; CPY OBJ('&lt;CD-Dir&gt;/FIXES/K40BNN2.SAP')<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;TODIR('../cofiles')<BR/> UNIX:&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;cp &lt;CD-Dir&gt;/FIXES/R40BNN2.SAP ../data<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;cp &lt;CD-Dir&gt;/FIXES/K40BNN2.SAP ../cofiles<BR/>Windows NT:&#x00A0;&#x00A0;Copy, for example with Explorer &#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; &lt;CD-DRIVE&gt;:\\FIXES\\R40BNN2.SAP<BR/> &#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; to&#x00A0;&#x00A0; \\usr\\sap\\trans\\data\\R40BNN2.SAP<BR/> &#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; and<BR/> &#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; &lt;CD-DRIVE&gt;:\\FIXES\\K40BNN2.SAP<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; to&#x00A0;&#x00A0; \\usr\\sap\\trans\\cofiles\\K40BNN2.SAP</OL> <OL>4. Import the request with: AS/400:&#x00A0;&#x00A0; tp 'addtobuffer SAPK40BNN2 &lt;SID&gt;'<BR/> &#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;tp 'import SAPK40BNN2 &lt;SID&gt;'<BR/>UNIX and Windows NT: &#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;tp addtobuffer SAPK40BNN2 &lt;SAPSID&gt;<BR/> &#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;tp import SAPK40BNN2 &lt;SAPSID&gt;</OL> <OL>5. Check the log files in subdirectory log of the global transport directory.<br /></OL> <p>------------------------&lt; D019989 06/APR/98&gt;--------------------<br />Only if you carry out the modification adjustment for the R/3 upgrade and Hot Packages together, with the goal of an automatic adjustment in subsequent systems:<br /><br />In the initial system proceed as follows:</p> <OL>1. Ignore the request for the modification adjustment in SPAM.</OL> <OL>2. Then adjust the objects from the upgrade and from the Hot Packages in conjunction with SPAU.</OL> <OL>3. Select function \"Mark transport\" in SPAU.<br /><br />Adhere to the procedure described in <B>Note 69990</B> in the subsequent system.<br />Otherwise, in the subsequent system, objects contained in Hot Packages which already have been adjusted are overwritten again!<br /></OL> <p>------------------------&lt; C1016615 06/APR/98&gt;---------------------<br /><B>Only for ReliantUNIX:</B><br /><br />Check the following kernel parameters after the R/3 upgrade for ReliantUNIX: * HRTIME<BR/> * HRVTIMEBoth parameters must have a minimum value of 1000!<br /><br />------------------------&lt; D015998 06/APR/98&gt;-----------------------<br />Only for customers who used deliveries with serial numbers (logistics) as of Release 2.2:<br /><br />Execute a report directly after the upgrade.<br />You find the instructions in Note 52994.<br /><br />Otherwise the display for the history of a serial number does not work correctly.<br /><br />----------------------------------------------------------------------</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON> (D022030)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON> (D022030)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000091709/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000091709/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000091709/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000091709/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000091709/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000091709/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000091709/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000091709/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000091709/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "99379", "RefComponent": "BC-UPG-RDM", "RefTitle": "Additional Info: Upgrading to 4.0B    DB2/400", "RefUrl": "/notes/99379"}, {"RefNumber": "99300", "RefComponent": "BC-UPG-OCS", "RefTitle": "OCS info: Integrating LCPs into the upgrade", "RefUrl": "/notes/99300"}, {"RefNumber": "99208", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/99208"}, {"RefNumber": "98904", "RefComponent": "BC-UPG-RDM", "RefTitle": "Additional Info: Upgrading to 4.0B     Online docu.", "RefUrl": "/notes/98904"}, {"RefNumber": "98673", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/98673"}, {"RefNumber": "98597", "RefComponent": "BC-UPG-RDM", "RefTitle": "Enhancements to the 4.0B DB2/CS upgrade", "RefUrl": "/notes/98597"}, {"RefNumber": "98585", "RefComponent": "IS-R", "RefTitle": "Use R/3 as an SAP Retail System", "RefUrl": "/notes/98585"}, {"RefNumber": "98198", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/98198"}, {"RefNumber": "98129", "RefComponent": "BC-UPG-RDM", "RefTitle": "Additional Info: Upgrading to 4.0B       ADABAS", "RefUrl": "/notes/98129"}, {"RefNumber": "98029", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/98029"}, {"RefNumber": "97583", "RefComponent": "BC-CTS-LAN", "RefTitle": "Current note about language import 4.0B", "RefUrl": "/notes/97583"}, {"RefNumber": "95999", "RefComponent": "BC-UPG-RDM", "RefTitle": "Additional Info: Upgrading to 4.0B    ORACLE", "RefUrl": "/notes/95999"}, {"RefNumber": "94663", "RefComponent": "BC-UPG-RDM", "RefTitle": "Additional Information: Upgrading to 4.0B INFORMIX", "RefUrl": "/notes/94663"}, {"RefNumber": "94345", "RefComponent": "IS-R", "RefTitle": "Addit.info on 4.0A/4.0B upgr. f. R/3 Retail customers", "RefUrl": "/notes/94345"}, {"RefNumber": "92665", "RefComponent": "BC-UPG", "RefTitle": "Upgrade to 4.0A/B: screen texts are deleted", "RefUrl": "/notes/92665"}, {"RefNumber": "89305", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/89305"}, {"RefNumber": "86895", "RefComponent": "BC-UPG-RDM", "RefTitle": "Additional Info: Upgrading to 4.0x       PC inst.", "RefUrl": "/notes/86895"}, {"RefNumber": "83728", "RefComponent": "PY-DE", "RefTitle": "Check table T510Q before the upgrade", "RefUrl": "/notes/83728"}, {"RefNumber": "74831", "RefComponent": "BC-CCM-MON", "RefTitle": "SPDD:Transports QE1K900002, TCVK900029, EWSK900052", "RefUrl": "/notes/74831"}, {"RefNumber": "73999", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/73999"}, {"RefNumber": "73510", "RefComponent": "BC-UPG", "RefTitle": "Problems during upgrade of patched source releases", "RefUrl": "/notes/73510"}, {"RefNumber": "69990", "RefComponent": "BC-UPG", "RefTitle": "SPAU: \"Mark transport\" and Hot Packages", "RefUrl": "/notes/69990"}, {"RefNumber": "67921", "RefComponent": "PA-PA-XX-BS", "RefTitle": "References to upgrade notes concerning HR 3.1G-4.7", "RefUrl": "/notes/67921"}, {"RefNumber": "67482", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/67482"}, {"RefNumber": "52994", "RefComponent": "LO-MD-SN", "RefTitle": "Converting deliveries with serial numbers from 2.2", "RefUrl": "/notes/52994"}, {"RefNumber": "512241", "RefComponent": "BC-CTS-TMS", "RefTitle": "Syntax error in the SAPLTMSC program while upgrading", "RefUrl": "/notes/512241"}, {"RefNumber": "48550", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/48550"}, {"RefNumber": "48184", "RefComponent": "BC-UPG", "RefTitle": "Upgrade: Stopping a running R3up or SAPup", "RefUrl": "/notes/48184"}, {"RefNumber": "444045", "RefComponent": "BC-CTS-TLS", "RefTitle": "Views after import/upgrade without join conditions", "RefUrl": "/notes/444045"}, {"RefNumber": "39745", "RefComponent": "BC-ABA-NL", "RefTitle": "setlocale on ReliantUNIX (SINIX) and table TCP0C", "RefUrl": "/notes/39745"}, {"RefNumber": "383126", "RefComponent": "BC-SRV-BSF-CUR", "RefTitle": "Additions upgrade: expiring currencies", "RefUrl": "/notes/383126"}, {"RefNumber": "370862", "RefComponent": "XX-SER-SWFL", "RefTitle": "Processing of incorrect screens", "RefUrl": "/notes/370862"}, {"RefNumber": "212876", "RefComponent": "BC-INS", "RefTitle": "The new archiving tool SAPCAR", "RefUrl": "/notes/212876"}, {"RefNumber": "202169", "RefComponent": "BC-UPG-RDM", "RefTitle": "Additional Info: Upgrade to 4.6C - DB2/400", "RefUrl": "/notes/202169"}, {"RefNumber": "195424", "RefComponent": "BC-DWB-DIC-AC", "RefTitle": "Activation error for table S404 (AD857)", "RefUrl": "/notes/195424"}, {"RefNumber": "190527", "RefComponent": "CA-EUR-CNV", "RefTitle": "EMU: Error/warning message w/ upgrade table EWUPAK", "RefUrl": "/notes/190527"}, {"RefNumber": "178823", "RefComponent": "BC-UPG-RDM", "RefTitle": "Additional Information: Upgrade to 4.6B - DB2/400", "RefUrl": "/notes/178823"}, {"RefNumber": "162117", "RefComponent": "BC-UPG-RDM", "RefTitle": "Additional Information: Upgrade to 4.6A - DB2/400", "RefUrl": "/notes/162117"}, {"RefNumber": "160502", "RefComponent": "BC-SRV-ADR", "RefTitle": "RSXADR03 takes too long to run", "RefUrl": "/notes/160502"}, {"RefNumber": "158404", "RefComponent": "BC-CTS-LAN", "RefTitle": "Latest note on language import 4.0B / SR1", "RefUrl": "/notes/158404"}, {"RefNumber": "156568", "RefComponent": "PY-DE-CI", "RefTitle": "Data loss of T5DB4 after upgrade to 4.0", "RefUrl": "/notes/156568"}, {"RefNumber": "147340", "RefComponent": "BC-UPG-RDM", "RefTitle": "Additional info on upgrade to 4.0B SR DB2/CS", "RefUrl": "/notes/147340"}, {"RefNumber": "141661", "RefComponent": "BC-UPG-RDM", "RefTitle": "Additional Info: Upgrading to 4.5B    DB2/400", "RefUrl": "/notes/141661"}, {"RefNumber": "141319", "RefComponent": "PY-DE-FP-DU", "RefTitle": "HR-DEUEV: person group key for upgrade 3.x -> 4.x", "RefUrl": "/notes/141319"}, {"RefNumber": "135132", "RefComponent": "CO", "RefTitle": "Upgrade to Release 4.0B or 4.5B, performance", "RefUrl": "/notes/135132"}, {"RefNumber": "133017", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/133017"}, {"RefNumber": "131663", "RefComponent": "BC-INS", "RefTitle": "Additional Info: Install/Upgrade Chinese Trad. 4.0B", "RefUrl": "/notes/131663"}, {"RefNumber": "131660", "RefComponent": "BC-INS", "RefTitle": "Additional Info: Install/Upgrade Chinese 4.0B", "RefUrl": "/notes/131660"}, {"RefNumber": "131530", "RefComponent": "BC-SRV-GBT-CAL", "RefTitle": "Appoint.cal.: daily notes lost during upgrade", "RefUrl": "/notes/131530"}, {"RefNumber": "129630", "RefComponent": "BC-DB-MSS", "RefTitle": "Handling of NULL values and the ansi_nulls setting", "RefUrl": "/notes/129630"}, {"RefNumber": "128589", "RefComponent": "BC-UPG-TLS", "RefTitle": "Upgrade to 4.0B/4.5A destroys customer variants", "RefUrl": "/notes/128589"}, {"RefNumber": "127865", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/127865"}, {"RefNumber": "127037", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/127037"}, {"RefNumber": "120143", "RefComponent": "BC-ABA-SC", "RefTitle": "Batch input: Upgrade to Release 4.xx", "RefUrl": "/notes/120143"}, {"RefNumber": "118664", "RefComponent": "CO-PA-ACT", "RefTitle": "Upgrade ZZKOMPAKE/ZZKOMKAKE appends", "RefUrl": "/notes/118664"}, {"RefNumber": "118435", "RefComponent": "BC-CUS-TOL-TME", "RefTitle": "Events for table maint. dialogs are missing", "RefUrl": "/notes/118435"}, {"RefNumber": "118310", "RefComponent": "PY-XX", "RefTitle": "Texts of the customer-specific proc./eval.", "RefUrl": "/notes/118310"}, {"RefNumber": "115979", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/115979"}, {"RefNumber": "115364", "RefComponent": "BC-CCM-MON-TUN", "RefTitle": "Workload statistics deleted during upgrade to 4.x", "RefUrl": "/notes/115364"}, {"RefNumber": "114346", "RefComponent": "IS-R-BD-LST", "RefTitle": "Module descript. after Upgrad 1.2x->4.y not complete", "RefUrl": "/notes/114346"}, {"RefNumber": "113045", "RefComponent": "FI-TV", "RefTitle": "Caution during modif. in SAPMP56R and SAPMP56S", "RefUrl": "/notes/113045"}, {"RefNumber": "112571", "RefComponent": "BC-DB-ORA-CCM", "RefTitle": "DB13 runtime error: CONNE_IMPORT_WRONG_FIELD_TYPE", "RefUrl": "/notes/112571"}, {"RefNumber": "112029", "RefComponent": "BC-CTS-LAN", "RefTitle": "Current note about language import 4.5A/BW 1.2B", "RefUrl": "/notes/112029"}, {"RefNumber": "111601", "RefComponent": "BC-UPG-ADDON", "RefTitle": "Ready-to-Run R/3: Information on the upgrade", "RefUrl": "/notes/111601"}, {"RefNumber": "111492", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/111492"}, {"RefNumber": "109469", "RefComponent": "FI-TV", "RefTitle": "Termination during upgrade to 4.0B in program RPRTRV10", "RefUrl": "/notes/109469"}, {"RefNumber": "109059", "RefComponent": "BC-UPG-TLS", "RefTitle": "Customer-specific matchcodes destroyed after 4.0B upgrade", "RefUrl": "/notes/109059"}, {"RefNumber": "108728", "RefComponent": "BC-DWB-DIC-AC", "RefTitle": "Upgrade phase JOB_DRDOC_40B hangs or terminates", "RefUrl": "/notes/108728"}, {"RefNumber": "108377", "RefComponent": "BC-UPG-RDM", "RefTitle": "Additional Info: Upgrading to 4.5A       DB2/400", "RefUrl": "/notes/108377"}, {"RefNumber": "106412", "RefComponent": "BC-DWB-DIC-AC", "RefTitle": "Conversion of tables EDIDOC & CDCLS, Release 4.0B", "RefUrl": "/notes/106412"}, {"RefNumber": "105153", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/105153"}, {"RefNumber": "103747", "RefComponent": "SV-PERF", "RefTitle": "Performance: Parameter recommendations as of Release 4.0", "RefUrl": "/notes/103747"}, {"RefNumber": "103720", "RefComponent": "BC-BMT-OM", "RefTitle": "Missing work items after a 3.0X,3.1X->4.0B upgrade", "RefUrl": "/notes/103720"}, {"RefNumber": "103585", "RefComponent": "FI-SL-VSR", "RefTitle": "Generation error after applying Hot Package", "RefUrl": "/notes/103585"}, {"RefNumber": "103533", "RefComponent": "BC-UPG-RDM", "RefTitle": "Additional Info: Upgrading to Japanese 4.0B NT", "RefUrl": "/notes/103533"}, {"RefNumber": "102603", "RefComponent": "BC-UPG-RDM", "RefTitle": "Additional Info: Upgrading to Japanese 4.0B UNIX", "RefUrl": "/notes/102603"}, {"RefNumber": "102445", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/102445"}, {"RefNumber": "101242", "RefComponent": "LE-WM", "RefTitle": "RLXPR440 with error message after upgradng to 4.0B", "RefUrl": "/notes/101242"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "99379", "RefComponent": "BC-UPG-RDM", "RefTitle": "Additional Info: Upgrading to 4.0B    DB2/400", "RefUrl": "/notes/99379 "}, {"RefNumber": "108377", "RefComponent": "BC-UPG-RDM", "RefTitle": "Additional Info: Upgrading to 4.5A       DB2/400", "RefUrl": "/notes/108377 "}, {"RefNumber": "141661", "RefComponent": "BC-UPG-RDM", "RefTitle": "Additional Info: Upgrading to 4.5B    DB2/400", "RefUrl": "/notes/141661 "}, {"RefNumber": "162117", "RefComponent": "BC-UPG-RDM", "RefTitle": "Additional Information: Upgrade to 4.6A - DB2/400", "RefUrl": "/notes/162117 "}, {"RefNumber": "178823", "RefComponent": "BC-UPG-RDM", "RefTitle": "Additional Information: Upgrade to 4.6B - DB2/400", "RefUrl": "/notes/178823 "}, {"RefNumber": "202169", "RefComponent": "BC-UPG-RDM", "RefTitle": "Additional Info: Upgrade to 4.6C - DB2/400", "RefUrl": "/notes/202169 "}, {"RefNumber": "103747", "RefComponent": "SV-PERF", "RefTitle": "Performance: Parameter recommendations as of Release 4.0", "RefUrl": "/notes/103747 "}, {"RefNumber": "118435", "RefComponent": "BC-CUS-TOL-TME", "RefTitle": "Events for table maint. dialogs are missing", "RefUrl": "/notes/118435 "}, {"RefNumber": "160502", "RefComponent": "BC-SRV-ADR", "RefTitle": "RSXADR03 takes too long to run", "RefUrl": "/notes/160502 "}, {"RefNumber": "212876", "RefComponent": "BC-INS", "RefTitle": "The new archiving tool SAPCAR", "RefUrl": "/notes/212876 "}, {"RefNumber": "48184", "RefComponent": "BC-UPG", "RefTitle": "Upgrade: Stopping a running R3up or SAPup", "RefUrl": "/notes/48184 "}, {"RefNumber": "512241", "RefComponent": "BC-CTS-TMS", "RefTitle": "Syntax error in the SAPLTMSC program while upgrading", "RefUrl": "/notes/512241 "}, {"RefNumber": "444045", "RefComponent": "BC-CTS-TLS", "RefTitle": "Views after import/upgrade without join conditions", "RefUrl": "/notes/444045 "}, {"RefNumber": "94663", "RefComponent": "BC-UPG-RDM", "RefTitle": "Additional Information: Upgrading to 4.0B INFORMIX", "RefUrl": "/notes/94663 "}, {"RefNumber": "112029", "RefComponent": "BC-CTS-LAN", "RefTitle": "Current note about language import 4.5A/BW 1.2B", "RefUrl": "/notes/112029 "}, {"RefNumber": "97583", "RefComponent": "BC-CTS-LAN", "RefTitle": "Current note about language import 4.0B", "RefUrl": "/notes/97583 "}, {"RefNumber": "67921", "RefComponent": "PA-PA-XX-BS", "RefTitle": "References to upgrade notes concerning HR 3.1G-4.7", "RefUrl": "/notes/67921 "}, {"RefNumber": "112571", "RefComponent": "BC-DB-ORA-CCM", "RefTitle": "DB13 runtime error: CONNE_IMPORT_WRONG_FIELD_TYPE", "RefUrl": "/notes/112571 "}, {"RefNumber": "118664", "RefComponent": "CO-PA-ACT", "RefTitle": "Upgrade ZZKOMPAKE/ZZKOMKAKE appends", "RefUrl": "/notes/118664 "}, {"RefNumber": "103585", "RefComponent": "FI-SL-VSR", "RefTitle": "Generation error after applying Hot Package", "RefUrl": "/notes/103585 "}, {"RefNumber": "73510", "RefComponent": "BC-UPG", "RefTitle": "Problems during upgrade of patched source releases", "RefUrl": "/notes/73510 "}, {"RefNumber": "141319", "RefComponent": "PY-DE-FP-DU", "RefTitle": "HR-DEUEV: person group key for upgrade 3.x -> 4.x", "RefUrl": "/notes/141319 "}, {"RefNumber": "383126", "RefComponent": "BC-SRV-BSF-CUR", "RefTitle": "Additions upgrade: expiring currencies", "RefUrl": "/notes/383126 "}, {"RefNumber": "98129", "RefComponent": "BC-UPG-RDM", "RefTitle": "Additional Info: Upgrading to 4.0B       ADABAS", "RefUrl": "/notes/98129 "}, {"RefNumber": "83728", "RefComponent": "PY-DE", "RefTitle": "Check table T510Q before the upgrade", "RefUrl": "/notes/83728 "}, {"RefNumber": "131530", "RefComponent": "BC-SRV-GBT-CAL", "RefTitle": "Appoint.cal.: daily notes lost during upgrade", "RefUrl": "/notes/131530 "}, {"RefNumber": "370862", "RefComponent": "XX-SER-SWFL", "RefTitle": "Processing of incorrect screens", "RefUrl": "/notes/370862 "}, {"RefNumber": "120143", "RefComponent": "BC-ABA-SC", "RefTitle": "Batch input: Upgrade to Release 4.xx", "RefUrl": "/notes/120143 "}, {"RefNumber": "103720", "RefComponent": "BC-BMT-OM", "RefTitle": "Missing work items after a 3.0X,3.1X->4.0B upgrade", "RefUrl": "/notes/103720 "}, {"RefNumber": "98597", "RefComponent": "BC-UPG-RDM", "RefTitle": "Enhancements to the 4.0B DB2/CS upgrade", "RefUrl": "/notes/98597 "}, {"RefNumber": "109469", "RefComponent": "FI-TV", "RefTitle": "Termination during upgrade to 4.0B in program RPRTRV10", "RefUrl": "/notes/109469 "}, {"RefNumber": "111601", "RefComponent": "BC-UPG-ADDON", "RefTitle": "Ready-to-Run R/3: Information on the upgrade", "RefUrl": "/notes/111601 "}, {"RefNumber": "106412", "RefComponent": "BC-DWB-DIC-AC", "RefTitle": "Conversion of tables EDIDOC & CDCLS, Release 4.0B", "RefUrl": "/notes/106412 "}, {"RefNumber": "92665", "RefComponent": "BC-UPG", "RefTitle": "Upgrade to 4.0A/B: screen texts are deleted", "RefUrl": "/notes/92665 "}, {"RefNumber": "128589", "RefComponent": "BC-UPG-TLS", "RefTitle": "Upgrade to 4.0B/4.5A destroys customer variants", "RefUrl": "/notes/128589 "}, {"RefNumber": "101242", "RefComponent": "LE-WM", "RefTitle": "RLXPR440 with error message after upgradng to 4.0B", "RefUrl": "/notes/101242 "}, {"RefNumber": "118310", "RefComponent": "PY-XX", "RefTitle": "Texts of the customer-specific proc./eval.", "RefUrl": "/notes/118310 "}, {"RefNumber": "98585", "RefComponent": "IS-R", "RefTitle": "Use R/3 as an SAP Retail System", "RefUrl": "/notes/98585 "}, {"RefNumber": "190527", "RefComponent": "CA-EUR-CNV", "RefTitle": "EMU: Error/warning message w/ upgrade table EWUPAK", "RefUrl": "/notes/190527 "}, {"RefNumber": "99300", "RefComponent": "BC-UPG-OCS", "RefTitle": "OCS info: Integrating LCPs into the upgrade", "RefUrl": "/notes/99300 "}, {"RefNumber": "158404", "RefComponent": "BC-CTS-LAN", "RefTitle": "Latest note on language import 4.0B / SR1", "RefUrl": "/notes/158404 "}, {"RefNumber": "103533", "RefComponent": "BC-UPG-RDM", "RefTitle": "Additional Info: Upgrading to Japanese 4.0B NT", "RefUrl": "/notes/103533 "}, {"RefNumber": "102603", "RefComponent": "BC-UPG-RDM", "RefTitle": "Additional Info: Upgrading to Japanese 4.0B UNIX", "RefUrl": "/notes/102603 "}, {"RefNumber": "74831", "RefComponent": "BC-CCM-MON", "RefTitle": "SPDD:Transports QE1K900002, TCVK900029, EWSK900052", "RefUrl": "/notes/74831 "}, {"RefNumber": "131660", "RefComponent": "BC-INS", "RefTitle": "Additional Info: Install/Upgrade Chinese 4.0B", "RefUrl": "/notes/131660 "}, {"RefNumber": "195424", "RefComponent": "BC-DWB-DIC-AC", "RefTitle": "Activation error for table S404 (AD857)", "RefUrl": "/notes/195424 "}, {"RefNumber": "156568", "RefComponent": "PY-DE-CI", "RefTitle": "Data loss of T5DB4 after upgrade to 4.0", "RefUrl": "/notes/156568 "}, {"RefNumber": "129630", "RefComponent": "BC-DB-MSS", "RefTitle": "Handling of NULL values and the ansi_nulls setting", "RefUrl": "/notes/129630 "}, {"RefNumber": "94345", "RefComponent": "IS-R", "RefTitle": "Addit.info on 4.0A/4.0B upgr. f. R/3 Retail customers", "RefUrl": "/notes/94345 "}, {"RefNumber": "135132", "RefComponent": "CO", "RefTitle": "Upgrade to Release 4.0B or 4.5B, performance", "RefUrl": "/notes/135132 "}, {"RefNumber": "131663", "RefComponent": "BC-INS", "RefTitle": "Additional Info: Install/Upgrade Chinese Trad. 4.0B", "RefUrl": "/notes/131663 "}, {"RefNumber": "147340", "RefComponent": "BC-UPG-RDM", "RefTitle": "Additional info on upgrade to 4.0B SR DB2/CS", "RefUrl": "/notes/147340 "}, {"RefNumber": "86895", "RefComponent": "BC-UPG-RDM", "RefTitle": "Additional Info: Upgrading to 4.0x       PC inst.", "RefUrl": "/notes/86895 "}, {"RefNumber": "39745", "RefComponent": "BC-ABA-NL", "RefTitle": "setlocale on ReliantUNIX (SINIX) and table TCP0C", "RefUrl": "/notes/39745 "}, {"RefNumber": "109059", "RefComponent": "BC-UPG-TLS", "RefTitle": "Customer-specific matchcodes destroyed after 4.0B upgrade", "RefUrl": "/notes/109059 "}, {"RefNumber": "115364", "RefComponent": "BC-CCM-MON-TUN", "RefTitle": "Workload statistics deleted during upgrade to 4.x", "RefUrl": "/notes/115364 "}, {"RefNumber": "95999", "RefComponent": "BC-UPG-RDM", "RefTitle": "Additional Info: Upgrading to 4.0B    ORACLE", "RefUrl": "/notes/95999 "}, {"RefNumber": "69990", "RefComponent": "BC-UPG", "RefTitle": "SPAU: \"Mark transport\" and Hot Packages", "RefUrl": "/notes/69990 "}, {"RefNumber": "108728", "RefComponent": "BC-DWB-DIC-AC", "RefTitle": "Upgrade phase JOB_DRDOC_40B hangs or terminates", "RefUrl": "/notes/108728 "}, {"RefNumber": "114346", "RefComponent": "IS-R-BD-LST", "RefTitle": "Module descript. after Upgrad 1.2x->4.y not complete", "RefUrl": "/notes/114346 "}, {"RefNumber": "98904", "RefComponent": "BC-UPG-RDM", "RefTitle": "Additional Info: Upgrading to 4.0B     Online docu.", "RefUrl": "/notes/98904 "}, {"RefNumber": "113045", "RefComponent": "FI-TV", "RefTitle": "Caution during modif. in SAPMP56R and SAPMP56S", "RefUrl": "/notes/113045 "}, {"RefNumber": "52994", "RefComponent": "LO-MD-SN", "RefTitle": "Converting deliveries with serial numbers from 2.2", "RefUrl": "/notes/52994 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_APPL", "From": "40B", "To": "40B", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}