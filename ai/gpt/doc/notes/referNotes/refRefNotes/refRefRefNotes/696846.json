{"Request": {"Number": "696846", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 351, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000003727202017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/**********?language=E&token=2244296D2BCA34E287CCEE9FA0221A18"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/**********", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/**********/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "696846"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 8}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Advance development"}, "Priority": {"_label": "Priority", "value": "Correction with medium priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "29.03.2004"}, "SAPComponentKey": {"_label": "Component", "value": "PM-EQM-EQ"}, "SAPComponentKeyText": {"_label": "Component", "value": "Equipment"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Plant Maintenance", "value": "PM", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'PM*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Equipment and Technical Objects", "value": "PM-EQM", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'PM-EQM*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Equipment", "value": "PM-EQM-EQ", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'PM-EQM-EQ*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "696846 - PMAA: Recording time segments in the asset"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>You change the data of an equipment. This equipment is assigned to an asset. The changed data of the equipment should be synchronized to the asset according to the Customizing settings. The data change in the equipment also affects such data that represents time-dependent data in the asset. Time-dependent data that is related to the asset is, for example, business area, cost center or plant. The change of this data should result in a new time segment for the time-dependent data in the asset. This is not possible with the currently existing function.<br /></p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>PMAA, PM-AA, AAPM, time-dependent data<br />AAPM_PM_PROCESS_ASSET, LAAPMF01, LAAPMTOP<br /></p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>This function does not exist.<br /></p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p><U>Implementation of the solution:</U><br />Implement the new function using the corresponding Support Package or the transport file attached to this note. You can find the necessary ZIP archive files attached to this note. For details on the procedure refer to attached Notes 480180 and 13719.<br /><br />The transport file is currently only available for Release 4.6C:</p> <UL><LI>Transport request:&#x00A0;&#x00A0;P9CK391117</LI></UL> <UL><LI>ZIP archive:&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;TRANSPORT_46C.ZIP</LI></UL> <UL><LI>KO file:&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;K391117.P9C</LI></UL> <UL><LI>Data file:&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;R391117.P9C<br /></LI></UL> <b>It is not possible to implement the correction using the correction instructions because a newly defined BAdI has to be created. The correction instruction only serves as documentation!<br /></b><br /> <p><U>Description of the solution:</U><br />Using method PM_DETERMINE_ADATU of new BAdI AAPM you determine the date of the validity start date of the new time segment to be created in the asset.<br /><br />The customer assumes the responsibility of deactivating method PM_DETERMINE_ADATU of BAdI AAPM<br /><br />Prerequisites and restrictions:</p> <UL><LI>The method is only called from the changed equipment data in case of direct synchronization of the asset data.</LI></UL> <UL><LI>The asset must already exist. This method is not called during the creation of an asset.</LI></UL> <UL><LI>Time-dependent data fields of the asset must be affected during synchronization.<br /></LI></UL> <p>Consider that an unfavorable choice of the date of the validity start date E_ADATU - for which a time segment of the time-dependent system data is created - can result in an irreversible asynchronization of the automatic synchronization between equipment and asset. This issue is subject to be checked exclusively by method PM_DETERMINE_ADATU, and thus by the user. Where necessary, this behavior can also be required.<br /><br />Refer to the BAdI documentation for further details.</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "FI-AA (Asset Accounting)"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D034421)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D025982)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/**********/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": [{"FileName": "TRANSPORT_46C.ZIP", "FileSize": "21", "MimeType": "application/x-zip-compressed", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012003146900000031142004&iv_version=0008&iv_guid=458793EC0C977D41B1B967AAB5CF80F3"}]}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "370884", "RefComponent": "PM-EQM-EQ", "RefTitle": "PM-AA 4.6C: Consulting on sync activation + corrections", "RefUrl": "/notes/370884"}, {"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "370884", "RefComponent": "PM-EQM-EQ", "RefTitle": "PM-AA 4.6C: Consulting on sync activation + corrections", "RefUrl": "/notes/370884 "}, {"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_APPL", "From": "46C", "To": "46C", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "470", "To": "470", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "SAP_APPL 46C", "SupportPackage": "SAPKH46C47", "URL": "/supportpackage/SAPKH46C47"}, {"SoftwareComponentVersion": "SAP_APPL 470", "SupportPackage": "SAPKH47021", "URL": "/supportpackage/SAPKH47021"}, {"SoftwareComponentVersion": "SAP_APPL 500", "SupportPackage": "SAPKH50005", "URL": "/supportpackage/SAPKH50005"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": [{"SoftwareComponent": "SAP_APPL", "NumberOfCorrin": 2, "URL": "/corrins/**********/1"}]}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 2, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 8, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": [{"SoftwareComponent": "SAP_APPL", "ValidFrom": "46C", "ValidTo": "46C", "Number": "302168 ", "URL": "/notes/302168 ", "Title": "Incorrect text fields for AAPM integration", "Component": "FI-AA-AA-A"}, {"SoftwareComponent": "SAP_APPL", "ValidFrom": "46C", "ValidTo": "46C", "Number": "370270 ", "URL": "/notes/370270 ", "Title": "PM-AA 4.6C: Asset in batch input not created", "Component": "PM-EQM-EQ"}, {"SoftwareComponent": "SAP_APPL", "ValidFrom": "46C", "ValidTo": "46C", "Number": "388943 ", "URL": "/notes/388943 ", "Title": "Equipment: Basic consistency check before update", "Component": "PM-EQM-EQ"}, {"SoftwareComponent": "SAP_APPL", "ValidFrom": "46C", "ValidTo": "46C", "Number": "398711 ", "URL": "/notes/398711 ", "Title": "Techn.object: Slight corrections (UCCHECK)", "Component": "PM-EQM"}, {"SoftwareComponent": "SAP_APPL", "ValidFrom": "46C", "ValidTo": "46C", "Number": "400125 ", "URL": "/notes/400125 ", "Title": "PM-AA 4.6C:Changing asset number in the equipment master", "Component": "PM-EQM-EQ"}, {"SoftwareComponent": "SAP_APPL", "ValidFrom": "46C", "ValidTo": "46C", "Number": "448886 ", "URL": "/notes/448886 ", "Title": "PM-AA 4.6C: Executing workflow items in succession", "Component": "PM-EQM-EQ"}, {"SoftwareComponent": "SAP_APPL", "ValidFrom": "46C", "ValidTo": "46C", "Number": "452552 ", "URL": "/notes/452552 ", "Title": "PM-AA 4.6C:problems with direct synchronization", "Component": "PM-EQM-EQ"}, {"SoftwareComponent": "SAP_APPL", "ValidFrom": "46C", "ValidTo": "470", "Number": "566207 ", "URL": "/notes/566207 ", "Title": "PM-AA 4.6C: Dump MOVE_TO_LIT_NOTALLOWED_NODATA when saving", "Component": "PM-EQM-EQ"}]}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}