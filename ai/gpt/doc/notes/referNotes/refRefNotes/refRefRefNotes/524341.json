{"Request": {"Number": "524341", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 716, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000015224142017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000524341?language=E&token=23B78E4B6A139172BE8CEDC287832DED"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000524341", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000524341/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "524341"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 14}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Advance development"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "22.02.2008"}, "SAPComponentKey": {"_label": "Component", "value": "BC-DB-ORA-DBA"}, "SAPComponentKeyText": {"_label": "Component", "value": "Database Administration"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Basis Components", "value": "BC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Database Interface, Database Platforms", "value": "BC-DB", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-DB*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Oracle", "value": "BC-DB-ORA", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-DB-ORA*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Database Administration", "value": "BC-DB-ORA-DBA", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-DB-ORA-DBA*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "524341 - Updating the statistics for individual partitions"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>BRCONNECT does not update the statistics of partitioned tables although partitions were reloaded. For example, the statistics stored in the DBA_TAB_PARTITIONS table display 0 records in the partition (NUM_ROWS).</p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>BW, APO</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>Up to now, BRCONNECT has only monitored the global statistics of partitioned tables. This means that changes within a partition (also important changes) only resulted in the statistics being updated if the number of changes has exceeded a threshold value (stats_change_threshold) in relation to the total number of records in the table. The relative changes in a partition were therefore ignored.</p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>BRCONNECT can now check and update the statistics on partition level, if necessary. You must, however, activate the MONITORING attribute for all partitioned tables to achieve this (see Note 408527).<br />Updating the statistics at partition level increases their accuracy, which is particularly important after the partitions were initially loaded. In this case, the partitions in a BW system only receive initial statistics, which do not display any records (NUM_ROWS = 0). This means that the statistics are not always up-to-date after the partitions were loaded. This situation is now recognized and corrected by BRCONNECT.<br />To further simplify the handling of partitioned tables, a new keyword called ALL_PART was inserted into the following init&lt;SID&gt;.sap parameter (and relevant options):<br /><br />1. stats_table = all_part&#x00A0;&#x00A0;&#x00A0;&#x00A0;or option&#x00A0;&#x00A0;&#x00A0;&#x00A0; -t all_part<br />Only partitioned tables are selected for updating the statistics.<br /><br />2. stats_exclude = all_part&#x00A0;&#x00A0;&#x00A0;&#x00A0;or option&#x00A0;&#x00A0;&#x00A0;&#x00A0;-e all_part<br />Partitioned tables for the update are excluded from the statistics update.<br /><br />3. stats_dbms_stats = all_part<br />Partitioned tables are processed using the DBMS_STATS package.<br /><br />Enhanced in BRCONNECT 6.20 patch level 9.<br />Enhanced in BRCONNECT 6.10 patch level 40.<br />For more information about downloading patches, see Notes 12741 and 19466.<br /><br />Caution (this applies to BW 2.X and 3.0A, see Note 129252):<br />------------------------------------------------------<br />In BW 2.X and 3.0A, statistics are created by default for all InfoCube tables using the ABAP program SAP_ANALYZE_ALL_INFOCUBES. In the course of this, all InfoCube tables and partitioned tables are entered in control table DBSTATC with the active flag set to \"I\" (ignore). This means that BRCONNECT ignores those tables, and the system does not update their statistics.<br />This is the recommended procedure.<br />To be able to process it in spite of this (which, as mentioned above, is not recommended but technically possible), you must change or delete the relevant DBSTATC entries.&#x00A0;&#x00A0;This action must be repeated after each loading process of the fact tables.<br />Alternatively, you can use option \"-f allsel\" to force the system to process InfoCube tables and partitioned tables. Updating the statistics for all tables in a BW system must be carried out in two steps:<br /><br />brconnect -u / -c -f stats -t all -e all_part,info_cubes<br />brconnect -u / -c -f stats -t all_part,info_cubes -f allsel<br /><br />After you have switched the statistics update for BW systems to BRCONNECT, you no longer have to execute the ABAP program SAP_ANALYZE_ALL_INFOCUBES.<br />Use BRCONNECT 6.10 Patch 53/6.20 Patch 22 or higher for these calls, and execute them once a week.<br />You can start these two runs from transaction DB13 after you have converted it to BRCONNECT (Note 403704).<br />For this purpose, change the PSTRING field in the entry with SHORTCUT \"ANAT\" from:<br />\"-u / -c -f stats -t all\"<br /><br />\"-u / -c -f stats -t all -e all_part,info_cubes\"<br />and change the fields SHORTNAM, LONGNAME, and PSTRING in the entry with SHORTCUT \"ANA\" from:<br />\"CheckOpt\"<br />\"Check optimizer statistics (1st phase)\"<br />\"-u / -c -f stats -t all -f nocoll\"<br />respectively to:<br />\"CubesStats\"<br />\"Check and update INFO_CUBES statistics\"<br />\"-u / -c -f stats -t all_part,info_cubes -f allsel\"<br /><br />This option is available as of the following Support Packages:<br />SAP Basis component 4.6B - Support Package 47.<br />SAP Basis component 4.6C - Support Package 39.<br />SAP Basis component 4.6D - Support Package 27.<br /><br />Instead of applying Support Packages, you can also make the following ABAP changes manually:<br /><br />In SAP Basis Release 4.6X:<br />--------------------------<br />Program RSORAADM, subprogram TRANSLATE_RESULT_TABLE, replace lines:<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;when \"st\".<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;bresults-obj = \"ANAT\".<br />with the following lines:<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;when \"st\".<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;if bresults-obj &lt;&gt; \"INFO_CUBES\" and bresults-obj &lt;&gt; \"ALL_PART\".<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;bresults-obj = \"ANAT\".<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;else.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;bresults-obj = \"ANA\".<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;endif.</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "D000674"}, {"Key": "Processor                                                                                           ", "Value": "D000674"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000524341/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000524341/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000524341/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000524341/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000524341/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000524341/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000524341/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000524341/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000524341/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "744483", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Changes in collection of statistics in BRCONNECT 6.40", "RefUrl": "/notes/744483"}, {"RefNumber": "651452", "RefComponent": "BC-DB-ORA-CCM", "RefTitle": "DB13: No action log found for this action", "RefUrl": "/notes/651452"}, {"RefNumber": "588668", "RefComponent": "BC-DB-ORA", "RefTitle": "FAQ: Database statistics", "RefUrl": "/notes/588668"}, {"RefNumber": "514205", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Corrections in BR*Tools version 6.20", "RefUrl": "/notes/514205"}, {"RefNumber": "428212", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Using BRCONNECT to update InfoCube statistics", "RefUrl": "/notes/428212"}, {"RefNumber": "403706", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Corrections in BR*Tools Version 6.10", "RefUrl": "/notes/403706"}, {"RefNumber": "129252", "RefComponent": "BW-SYS-DB-ORA", "RefTitle": "Oracle DB Statistics for BW Tables", "RefUrl": "/notes/129252"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "744483", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Changes in collection of statistics in BRCONNECT 6.40", "RefUrl": "/notes/744483 "}, {"RefNumber": "588668", "RefComponent": "BC-DB-ORA", "RefTitle": "FAQ: Database statistics", "RefUrl": "/notes/588668 "}, {"RefNumber": "428212", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Using BRCONNECT to update InfoCube statistics", "RefUrl": "/notes/428212 "}, {"RefNumber": "651452", "RefComponent": "BC-DB-ORA-CCM", "RefTitle": "DB13: No action log found for this action", "RefUrl": "/notes/651452 "}, {"RefNumber": "514205", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Corrections in BR*Tools version 6.20", "RefUrl": "/notes/514205 "}, {"RefNumber": "403706", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Corrections in BR*Tools Version 6.10", "RefUrl": "/notes/403706 "}, {"RefNumber": "129252", "RefComponent": "BW-SYS-DB-ORA", "RefTitle": "Oracle DB Statistics for BW Tables", "RefUrl": "/notes/129252 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_BASIS", "From": "610", "To": "620", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}