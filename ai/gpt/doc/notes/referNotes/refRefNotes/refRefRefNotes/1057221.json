{"Request": {"Number": "1057221", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 1992, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "D", "_masterLanguage": "D", "_loadedLanguage": "D", "_machineTranslationLanguage": "E", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Acknowledge", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "If the quality of the machine translation is not sufficient for your purposes, click here to request a revised version.", "_type": "EVENT", "_visible": true}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000006841152017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/**********?language=E&token=3D1FF8A4B49E833D0A689C0F16A6FF35"}, "ShareByEmail": {"_label": "Share via Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/**********", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/**********/D/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1057221"}, "Type": {"_label": "Type", "value": "SAP-Hinweis"}, "Version": {"_label": "Version", "value": 14}, "Recency": {"_label": "Currentness", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "German"}, "MasterLanguage": {"_label": "Original Language", "value": "German"}, "Category": {"_label": "Category", "value": "Advance development"}, "Priority": {"_label": "Priority", "value": "Correction with medium priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "14.03.2008"}, "SAPComponentKey": {"_label": "Component", "value": "XX-CSC-CH-IS-H"}, "SAPComponentKeyText": {"_label": "Component", "value": "Hospital"}, "SAPComponentPath": [{"_label": "Services &amp; Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs &amp; Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Miscellaneous", "value": "XX", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Country/Region-Specific Developments", "value": "XX-CSC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-CSC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Switzerland", "value": "XX-CSC-CH", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-CSC-CH*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Industry-specific component", "value": "XX-CSC-CH-IS", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-CSC-CH-IS*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Hospital", "value": "XX-CSC-CH-IS-H", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-CSC-CH-IS-H*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1057221 - IS-H CH: Insurance Provider Groups Similar to Service Groups"}, "LongText": {"_label": "Description", "value": "<div id=\"DISCLAIMER\" class=\"sapMMsgStrip sapMMsgStripWarning sapUiSmallMarginTop sapUiMediumMarginBottom\"><div class=\"sapMMsgStripMessage\"><span dir=\"auto\" class=\"sapMText sapUiSelectable sapMTextMaxWidth\" style=\"text-align: left;\"><strong>ATTENTION!</strong> This is a machine-translated document that has not been reviewed for accuracy. To provide feedback regarding the machine translation, please <a style=\"color:var(--sapLinkColor);\" href=\"https://sapinsights.eu.qualtrics.com/jfe/form/SV_6nI2C1MOwEviVQG?NoteNumber=1057221&TargetLanguage=EN&Component=XX-CSC-CH-IS-H&SourceLanguage=DE&Priority=03\" target=\"_blank\">click here</a>.<br/>You can access the original document via the following link: <a style=\"color:var(--sapLinkColor);\" href=\"/notes/1057221/D\" target=\"_blank\">/notes/1057221/D</a>.</span></div></div><div><div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>This SAP Note is relevant only for the country version Switzerland (CH).<br />To reduce the Customizing effort, a grouping of insurance providers is now introduced in the same way as for the service groups.<br />This means that you can now also store a previously defined insurance provider group in the Insurance Provider field in the following Swiss-specific tables:</p> <UL><LI>Determination of Billing Agreements (TNCH52)</LI></UL> <UL><LI>Contract Schemes Assignment Table (TNWCH74)</LI></UL> <UL><LI>Insurance Verification - Reason for Rejection Assignment (TNWCH76)</LI></UL> <UL><LI>Charge Factor Value Determination (TNWCH80)</LI></UL> <UL><LI>Determination of Billing Types (TNWCH81)</LI></UL> <UL><LI>Charge Factor Value Determination for TARMED Services (TNWCH82)</LI></UL> <UL><LI>Control EDI Procedure for Insurance Provider (TNWCH_EDIKTR)</LI></UL> <p><br />The insurance provider groups are managed using transaction NWCHKTRGR. You must explode the entries entered in maintenance view V_TNWCHKTRGR using report IS-H CH: Report for Assigning Insurance Providers to Insurance Provider Groups (RNWCHGEN_KTRZUO) and transfer them to table NWCHKTRGRZUO.<br />The manual explosion in the table NWCHKTRGRZUO is used for performance optimization. As a result, the corresponding applications (e.g. Charge factor determination) does not explode the cost object groups during runtime, but can read the assignments of the cost objects to the insurance provider groups directly in table NWCHKTRGRZUO.</p><h3 data-toc-skip class=\"section\" id=\"Weitere Begriffe\">Other Terms</h3><p>Insurance provider groups in the same way as service groups</p><h3 data-toc-skip class=\"section\" id=\"Ursache und Voraussetzungen\">Reason and Prerequisites</h3><p>Program enhancement</p><h3 data-toc-skip class=\"section\" id=\"L&ouml;sung\">Solution</h3><OL>1. Before you implement the source code corrections, implement the attached attachment as follows:</OL> <OL><OL>a) Unpack the attached file.</OL></OL> <p>                       HW1057221_60.zip for IS-H Version 6.0 <p>                       HW1057221_60_01.zip for IS-H Version 6.0 <p>                       HW1057221_60_02.zip for IS-H Version 6.0 <p>                       HW1057221_60_03.zip for IS-H Version 6.0 <p>                       HW1057221_472.zip for IS-H Version 4.72 <p>                       HW1057221_472_01.zip for IS-H Version 4.72 <p>                       HW1057221_472_02.zip for IS-H Version 4.72 <p>                       Note that you cannot download the attached files using OSS, but only from SAP Service Marketplace (see also SAP Notes 480180 and 13719 for information about importing attachments). <p>                       Import the unpacked requests into your system. During the generation of the programs used, errors may occur that are corrected after you implement the source code corrections from this SAP Note. <OL>2. Now implement the source code corrections from this SAP Note.</OL> <p><br /><B><B>Note:</B></B> Up to ERP2005s (Release 7.00), the use of this advance implementation is subject to a charge and requires a license key.</p></div></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "IS-H (Hospital)"}, {"Key": "Owner                                                                                    ", "Value": "<PERSON> (********)"}, {"Key": "Processor                                                                                          ", "Value": "<PERSON> (********)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/**********/D"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "MIME Type", "URL": "URL"}, "Items": [{"FileName": "HW1057221_472.zip", "FileSize": "31", "MimeType": "application/x-zip-compressed", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000330512007&iv_version=0014&iv_guid=B621C520A906624D99676D559CD15564"}, {"FileName": "HW1057221_60_01.zip", "FileSize": "4", "MimeType": "application/x-zip-compressed", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000330512007&iv_version=0014&iv_guid=49522562D44DCA42999A8B03F3A4E638"}, {"FileName": "HW1057221_60_03.zip", "FileSize": "4", "MimeType": "application/x-zip-compressed", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000330512007&iv_version=0014&iv_guid=481DE12CB555F9499A130B78CA4037C9"}, {"FileName": "HW1057221_60.zip", "FileSize": "30", "MimeType": "application/x-zip-compressed", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000330512007&iv_version=0014&iv_guid=88C2C151B971334D952E59ABF3F40C49"}, {"FileName": "HW1057221_60_02.zip", "FileSize": "12", "MimeType": "application/x-zip-compressed", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000330512007&iv_version=0014&iv_guid=ACC84F99D297114992B9FD34D7C46F89"}, {"FileName": "HW1057221_472_01.zip", "FileSize": "11", "MimeType": "application/x-zip-compressed", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000330512007&iv_version=0014&iv_guid=AA4BDA875D385A4395066FFE162EC3D8"}, {"FileName": "HW1057221_472_02.zip", "FileSize": "4", "MimeType": "application/x-zip-compressed", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000330512007&iv_version=0014&iv_guid=D7D4CF2C61C94E40BAA528D2B89A398D"}]}, "References": {"_label": "References", "RefTo": {"_label": "This document references", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "URL"}, "Items": [{"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Advance transports to customer (note for customers)", "RefUrl": "/notes/13719"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "URL"}, "Items": [{"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Advance transports to customer (note for customers)", "RefUrl": "/notes/13719 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And the following"}, "Items": [{"SoftwareComponent": "IS-H", "From": "472", "To": "472", "Subsequent": ""}, {"SoftwareComponent": "IS-H", "From": "600", "To": "600", "Subsequent": ""}, {"SoftwareComponent": "IS-H", "From": "602", "To": "602", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software-Komponentenversion", "SupportPackage": "Support Package", "URL": "URL"}, "Items": [{"SoftwareComponentVersion": "IS-H 472", "SupportPackage": "SAPKIPHF24", "URL": "/supportpackage/SAPKIPHF24"}, {"SoftwareComponentVersion": "IS-H 472", "SupportPackage": "SAPKIPHF23", "URL": "/supportpackage/SAPKIPHF23"}, {"SoftwareComponentVersion": "IS-H 600", "SupportPackage": "SAPK-60011INISH", "URL": "/supportpackage/SAPK-60011INISH"}, {"SoftwareComponentVersion": "IS-H 600", "SupportPackage": "SAPK-60010INISH", "URL": "/supportpackage/SAPK-60010INISH"}, {"SoftwareComponentVersion": "IS-H 600", "SupportPackage": "SAPK-60012INISH", "URL": "/supportpackage/SAPK-60012INISH"}, {"SoftwareComponentVersion": "IS-H 602", "SupportPackage": "SAPK-60201INISH", "URL": "/supportpackage/SAPK-60201INISH"}, {"SoftwareComponentVersion": "IS-H 602", "SupportPackage": "SAPK-60201INISH", "URL": "/supportpackage/SAPK-60201INISH"}]}, "SupportPackagePatch": {"_label": "Support Packages and Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "URL"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "URL"}, "Items": [{"SoftwareComponent": "IS-H", "NumberOfCorrin": 2, "URL": "/corrins/**********/6"}]}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about correction instructions", "Corrections": {"_label": "Correction", "value": 2, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisite", "value": 17, "state": "None"}}, "Preconditions": {"_label": "Prerequisite", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "URL", "Title": "Title", "Component": "Component"}, "Items": [{"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "788582 ", "URL": "/notes/788582 ", "Title": "IS-H CH: RNCHABRV - New Pushbutton Update for Change SETC", "Component": "XX-CSC-CH-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "835240 ", "URL": "/notes/835240 ", "Title": "IS-H CH: MEDIDATA 4.0 - New Version", "Component": "XX-CSC-CH-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "850954 ", "URL": "/notes/850954 ", "Title": "IS-H CH: MEDIDATA 4.0 - New Version", "Component": "XX-CSC-CH-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "919821 ", "URL": "/notes/919821 ", "Title": "IS-H CH: Generate ABRV - Consider OU Correctly", "Component": "XX-CSC-CH-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "1000133 ", "URL": "/notes/1000133 ", "Title": "IS-H CH: RNCHABRV - Multiple Outpatient Same Day Visits", "Component": "XX-CSC-CH-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "1009743 ", "URL": "/notes/1009743 ", "Title": "Change to Processing of Cases with Benefit Generation Date", "Component": "XX-CSC-CH-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "1015998 ", "URL": "/notes/1015998 ", "Title": "IS-H CH: INXML - Validity Date for TNWCH_EDIKTR", "Component": "XX-CSC-CH-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "1032106 ", "URL": "/notes/1032106 ", "Title": "IS-H CH: RNCHABRV - Dump due to too large range table", "Component": "XX-CSC-CH-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "600", "ValidTo": "600", "Number": "885806 ", "URL": "/notes/885806 ", "Title": "IS-H CH: INXML - External Service Code", "Component": "XX-CSC-CH-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "600", "ValidTo": "600", "Number": "919821 ", "URL": "/notes/919821 ", "Title": "IS-H CH: Generate ABRV - Consider OU Correctly", "Component": "XX-CSC-CH-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "600", "ValidTo": "600", "Number": "922087 ", "URL": "/notes/922087 ", "Title": "IS-H CH: Customizing Rate Categories for Invoice (Print, INXML)", "Component": "XX-CSC-CH-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "600", "ValidTo": "600", "Number": "932056 ", "URL": "/notes/932056 ", "Title": "IS-H CH: NTPKCH - Adjustments Due to Key Change", "Component": "XX-CSC-CH-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "600", "ValidTo": "600", "Number": "935770 ", "URL": "/notes/935770 ", "Title": "IS-H CH: INXML - Problems with Multiple, Same IR", "Component": "XX-CSC-CH-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "600", "ValidTo": "600", "Number": "940069 ", "URL": "/notes/940069 ", "Title": "IS-H CH: Data Exchange - INXML Legal Representative", "Component": "XX-CSC-CH-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "600", "ValidTo": "600", "Number": "954442 ", "URL": "/notes/954442 ", "Title": "IS-H CH: INXML - Check Service Catalog Group of Canceled Services", "Component": "XX-CSC-CH-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "600", "ValidTo": "600", "Number": "968481 ", "URL": "/notes/968481 ", "Title": "IS-H CH: Data Exchange - INXML Comment Service Long", "Component": "XX-CSC-CH-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "600", "ValidTo": "600", "Number": "992365 ", "URL": "/notes/992365 ", "Title": "IS-H CH: INXML Comment Long Text of Insurance", "Component": "XX-CSC-CH-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "600", "ValidTo": "600", "Number": "999419 ", "URL": "/notes/999419 ", "Title": "IS-H CH: INXML - Error Determining External Service Code", "Component": "XX-CSC-CH-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "600", "ValidTo": "600", "Number": "1000133 ", "URL": "/notes/1000133 ", "Title": "IS-H CH: RNCHABRV - Multiple Outpatient Same Day Visits", "Component": "XX-CSC-CH-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "600", "ValidTo": "600", "Number": "1009743 ", "URL": "/notes/1009743 ", "Title": "Change to Processing of Cases with Benefit Generation Date", "Component": "XX-CSC-CH-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "600", "ValidTo": "600", "Number": "1015998 ", "URL": "/notes/1015998 ", "Title": "IS-H CH: INXML - Validity Date for TNWCH_EDIKTR", "Component": "XX-CSC-CH-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "600", "ValidTo": "600", "Number": "1032106 ", "URL": "/notes/1032106 ", "Title": "IS-H CH: RNCHABRV - Dump due to too large range table", "Component": "XX-CSC-CH-IS-H"}]}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated as helpful", "Helpful-Yes": "0 persons"}, "RatingQuality": {"_label": "Quality Assessment"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document eliminates side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "URL"}, "Items": []}, "SideEffectsSolving": {"_label": "This document causes side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "URL"}, "Items": []}}, "DisclaimerForMachineTranslation": {"Header": "ATTENTION!", "Message": "This is a machine-translated document that has not been reviewed for accuracy.", "Survey": "To provide feedback regarding the machine translation, please <a href=\"https://sapinsights.eu.qualtrics.com/jfe/form/SV_6nI2C1MOwEviVQG?NoteNumber=1057221&TargetLanguage=EN&Component=XX-CSC-CH-IS-H&SourceLanguage=DE&Priority=03\" target=\"_blank\">click here</a>.", "Request": "If the quality of the machine translation is not sufficient for your purposes, click here to request a revised version.", "Origin": "You can access the original document via the following link: <a href=\"/notes/1057221/D\" target=\"_blank\">/notes/1057221/D</a>."}}}}