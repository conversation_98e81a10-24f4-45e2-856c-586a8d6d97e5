{"Request": {"Number": "894279", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 523, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000015978452017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000894279?language=E&token=A62C5A2D208872D98FC45D1F8AE175D9"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000894279", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000894279/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "894279"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 42}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "FAQ"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "15.06.2022"}, "SAPComponentKey": {"_label": "Component", "value": "SV-SMG-INS-CFG"}, "SAPComponentKeyText": {"_label": "Component", "value": "Setup and Configuration of the Solution Manager system"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Service", "value": "SV", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'SV*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "SAP Solution Manager", "value": "SV-SMG", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'SV-SMG*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Installation, Configuration and Upgrade of Solution Manager", "value": "SV-SMG-INS", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'SV-SMG-INS*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Setup and Configuration of the Solution Manager system", "value": "SV-SMG-INS-CFG", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'SV-SMG-INS-CFG*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "894279 - Background processing in SAP Solution Manager"}, "LongText": {"_label": "Description", "value": "<h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>This note aims to document the background jobs specific to SAP Solution Manager.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>SAP SOLUTION MANAGER, SOLUTION_MANAGER, DSWP, DSMOP, SM:SCHEDULER, DSWPJOB, SMCONFIGJOBS, SM:EXEC SERVICES</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>This note provides additional documentation related to SAP Solution Manager background processing.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p>This note is valid for&#160;SAP Solution Manager&#160;4.0, 7.0, 7.0 EHP1,&#160;7.1&#160;and 7.2 releases and describes two related job set-up frameworks.</p>\r\n<p>The initial/old job set-up framework was relevant for release 4.0 up to support package 21 (included).<br />In subsequent releases and support packages a second - additional - job set-up framework has been introduced.<br />Also, starting with 7.1 SP05, all jobs set-up in the old fashion were migrated to the new framework. <br />But the old framework master job - named SM:SCHEDULER - which was taking care of the job initial scheduling was kept active.<br />Finally, with SAP Solution Manager 7.1 SP10 the old framework has been retired, and the associated \"SM:SCHEDULER\" job removed.</p>\r\n<p>The SM_CONFIG_JOBS transaction is now opening the relevant job set-up step of the \"Basic configuration\" scenario, within the SAP Solution Manager Configuration (SOLMAN_SETUP).<br /><br />Find below the overview respective to both frameworks. Remind to perform the job set-up (based on the new framework), using the \"Basic Configuration\" scenario within SOLMAN_SETUP transaction.</p>\r\n<p>Note: To trigger background jobs with customer-specific variants, continue to use the classical SAP job scheduling infrastructure, that is, transactions SM37 and SM36.&#160;Also make sure to define new job names, that are not listed in this SAP note. Do not add any customer-specific programs in these job set-up frameworks.</p>\r\n<p><strong><strong>Overview of&#160;SAP Solution Manager job set-up</strong></strong></p>\r\n<ul>\r\n<li>By definition, the SAP Solution Manager job configuration delivers the required SAP Solution Manager standard jobs with predefined start conditions.&#160;The initial scheduling is triggered by the system adminstrator using SM_CONFIG_JOBS transaction (up to 7.0 SP 17) or transaction SOLMAN_SETUP as&#160;of release 7.0 EHP1. However, any job listed in the old configuration table (DSWPJOB) and not in the new one (SMCONFIGJOBS) was still scheduled by the SM:SCHEDULER job and this until 7.1 SP10, as mentioned earlier.</li>\r\n</ul>\r\n<ul>\r\n<li>The two frameworks handle periodic jobs in different ways. With the new framework, job periodicity is directly handled by the standard SAP job scheduler, which means that periodic jobs entries are created and visible in SM37 transaction. The old job set-up framework used a dedicated report (RDSWPJOBSCHEDULER) triggered hourly to create, if required, associated non periodic job entries (also visible in SM37). It was therefore only possible to set-up jobs having a periodicity being a multiple of one hour.</li>\r\n</ul>\r\n<ul>\r\n<li>The SAP Solution Manager jobs configuration stored in the SMCONFIGJOBS table offers more flexibility than the old job set-up framework. Typically the ISIMMEDIATE flag allows an immediate scheduling of the corresponding jobs, and this at the time the system administrator executes the automatic activity \"Schedule Sol. Manager Background Jobs\", or the step \"Schedule Jobs\" with SAP Solution Manager 7.2. Moreover the COMM2SAP flag is used to mark the jobs in communication with a SAP system. Starting from 4.0 SP26 and 7.1 SP01 those kind of jobs being declared as non immediate are started with a random time offset to ensure that the SAP Solution Manager customer systems, from a given time zone, do not access the SAP Support Portal at the same moment. For more details about this global randomization and how to unschedule the existing jobs with a fixed start time causing problems to SAP Support Portal, please see SAP notes 1538950 and 1604572.</li>\r\n</ul>\r\n<ul>\r\n<li>Starting with 7.1 SP05, an additional mechanism was introducted in the scheduling of SAP Solution Manager jobs using SOLMAN_SETUP to remove or change the scheduling of existing periodic jobs, in case they do not match the settings defined in the SMCONFIGJOBS table. Indeed the content of this table may change with each SP delivery, not only because of new jobs added, but also because of modification of the existing scheduling rules. Moreover some jobs can become obsolete and are then marked as inactive in the table. It is important to know is that jobs are identified by using the job names and not the report names, because we consider job names defined in SMCONFIGJOBS as reserved names for jobs scheduled by the SAP Solution Manager Configuration. Indeed they are frequently using the \"SM:\" prefix. Jobs executing the same report but using a different job name are not taken into account in this process.</li>\r\n</ul>\r\n<ul>\r\n<li>More precisely, modifications of existing periodic job schedules are done as follows: Jobs marked as inactive are removed. Jobs using a different period are changed to fulfill the period defined in SMCONFIGJOBS table. Jobs in communication with SAP Support Portal (COMM2SAP flag set) that are not defined as to be started immediately (ISIMMEDIATE flag not set) are checked according to the problem of random time bug (described in SAP notes 1538950 and 1604572) and changed with a real random start time offset. Jobs with a period multiple of 24 hours defined to start at a specific time are changed to fulfill the start time if required. Jobs for which the report name or the variant name was changed are corrected. And finally jobs supposed to use a dedicated batch user like SM_EFWK user for the E2E_EFWK_RESOURCE_MGR report are also modified. This does not impact jobs for which SAP recommends to use the standard batch user, which is by default SOLMAN_BTC.</li>\r\n</ul>\r\n<ul>\r\n<li>Also starting with 7.1 SP05 a dependency to SAP Solution Manager Configuration activities was introduced for the jobs defined in SMCONFIGJOBS table. Now it is possible to reuse the same job scheduling logic for other automatic activities within the SAP Solution Manager Configuration. Even if most of the jobs are set-up by&#160;the automatic activity \"Schedule Sol. Manager Background Jobs\", or the step \"Schedule Jobs\" since SAP Solution Manager 7.2, now it's also possible to schedule dedicated jobs in the context of specific scenarios such as \"Data Volume Management\", activity&#160;\"Schedule DVM Background Jobs\" (since 7.1 SP07), or \"IT Service Management\" / \"Incident, Problem &amp; Request Management\", activity \"Schedule Background Job\" of sub-step \"Configure Automatically\" (since 7.1 SP05). Very likely there will be in the future other places in the setup where scenario specific jobs will be scheduled.</li>\r\n</ul>\r\n<p style=\"padding-left: 30px;\"><strong>Modifications in the job set-up framework with 7.1 SP12</strong>:</p>\r\n<ul>\r\n<li>Scheduling of OSS-connected jobs shall be confirmed manually: In the context of non-productive SAP Solution Manager systems (as defined in IT Admin role of LMDB), the activities that schedule jobs exchanging data with the SAP Support Portal initially return a red status. This indicates that the job scheduling process was interrupted, as a new manual confirmation is required. To launch the manual confirmation, click the URL link available in the corresponding log message. A list of jobs that have to be confirmed manually is displayed. Click the job name to obtain the purpose description.<br />The \"Current State\" column indicates whether the job is currently scheduled in the system.<br />The \"Confirm Future State\" column allows to decide whether the job should be scheduled. The first time, it contains the job schedule as recommended by SAP. You can change the schedules individually, as needed. Then choose \"Confirm\" to return to SAP Solution Manager Configuration.<br />The job scheduling automatic activity shall then be executed again. Once the activity execution is completed, the last two columns in the job schedule confirmation dialog window should be identical.<br /><strong>Note</strong>: The manual confirmation is not required for productive SAP Solution Manager installations, as all jobs are scheduled, like this was the case with earlier versions.</li>\r\n<li>Enhanced handling of &#8220;immediate&#8221; SAP Solution Manager jobs: Before SP12, there was a 30 minute interval between the triggering of two immediate jobs. The first job was started during execution of the job setup activity and, depending on the number of immediate jobs to schedule, the last job sometimes was executed much later.<br />With SP12, the overall execution of the immediate jobs is done much faster, using an asynchronous process based on the job periodicity (from most to least frequent). Jobs are executed sequentially one after the other, except when the job processing time reaches 2 minutes. In this case, the next job is started in parallel, without waiting for the end of the previous one. The status of the job activity remains yellow until the asynchronous scheduling of immediate jobs is finished. <br /><strong>Note:</strong> You have to manually refresh the logs to get the most recent immediate job schedules.</li>\r\n<li>Batch user name change: When the SAP Solution Manager batch user name (SOLMAN_BTC) is changed, a notification to re-execute the main job activities is raised, and the corresponding activities are flagged for re-execution. During execution, the activities will remove existing job schedules that use the old batch user name (now invalid) as execution user, and replace each of&#160;them with a schedule using the new batch processing user name.</li>\r\n<li>Job description: A dedicated documentation is associated to every new job (as defined in the SMCONFIGJOBS table). The documentation can be displayed from SAP Solution Manager Configuration.</li>\r\n<li>Enhanced logging of job activities: Additional log messages have been added for jobs communicating with the SAP Support Portal, for the scheduling of immediate jobs, and whenever the job documentation is opened using the URL link. Moreover, if existing job schedules are removed, and a \"Show Details&#8221; link of the corresponding log message displays the complete list of jobs that were removed and the reason for the removal.</li>\r\n</ul>\r\n<p style=\"padding-left: 30px;\"><strong>Remark</strong>: Changes implemented with 7.1 SP12, concerning non-productive SAP Solution Manager systems for jobs exchanging data with the SAP Support Portal, may cause a red status about a non existing active nametab for the PAT03_STARTPOINT table. This returned error is not relevant. Implement SAP Note 2058991 to resolve the issue.</p>\r\n<p style=\"padding-left: 30px;\"><strong><strong>Modifications in the job set-up framework with 7.2 SP03</strong>:</strong></p>\r\n<ul>\r\n<li>The \"Schedule Sol.Manager Background Jobs\" main job activity that was in ST710 part of \"Basic Configuration\" setup scenario, \"Configure Automatically\" step 5, is now in ST720 release transformed into a \"Schedule Jobs\" job-only dedicated step 2 of \"Basic Configuration\" scenario. In this step are displayed in a table all the required cross-scenarios jobs schedules, with their current schedules as detected in the system, and what would be their future schedules in case the \"Schedule Jobs as Planned\" execution button is pressed. If the Solution Manager is not a productive system, the planned scheduling of the jobs connected to the SAP Support Backbone only can be chosen manually by the setup administrator according to business requirements, or follow SAP individual recommendations. Indeed SAP does not recommend by default to schedule all the jobs connected to the SAP Support Backbone system in a non-productive environment.</li>\r\n<li>In case the Solution Manager is a productive system, this possibility of manual adjustments can also be activated from the \"Advanced Options\" of \"Define System Role\" step 1 in \"System Preparation\" setup scenario, to offer an option to remove the scheduling of some SAP Backbone connected jobs. Indeed the normal process on a productive installation is to schedule all the jobs.</li>\r\n<li>Remark: Due to this change from activity to step, the jobs scheduling manual confirmation window for OSS-connected jobs, introduced in ST710 since SP12 and triggered from an URL link in the logs area, was simply removed. The other jobs activities used in other setup scenarios were not changed with respect to ST710 release.</li>\r\n</ul>\r\n<p style=\"padding-left: 30px;\"><strong><strong>Modifications in the job set-up framework with 7.2 SP09</strong>:</strong></p>\r\n<ul>\r\n<li>The table SMCONFIGJOBS was enhanced with three additional&#160;fields: EVENTID, USER_GS_KEY&#160;and USER_ACTIVITY_ID. Their usage is:</li>\r\n<ul>\r\n<li><strong>EVENTID</strong>&#160;indicates the Event ID (see transaction SM64) that triggers the corresponding job</li>\r\n<li><strong>USER_GS_KEY</strong>&#160;indicates the User Key&#160;in Generic Storage, valid&#160;to search for the correct user and password to execute the corresponding job</li>\r\n<li><strong>USER_ACTIVITY_ID</strong> gives the User Activity ID where this user configuration is&#160;provided. It's used in Generic Storage in combination with USER_GS_KEY</li>\r\n</ul>\r\n<li>According to prior point is now possible to schedule&#160;jobs triggered by events and specify&#160;users authorized&#160;to run jobs.</li>\r\n</ul>\r\n<p><br /><strong>Detailed list of jobs/reports:</strong></p>\r\n<ul>\r\n<li><strong>New since 7.2</strong>:<br />&#160;</li>\r\n<ul>\r\n<li>&#160;&#160;&#160;&#160;&#160; ACC_NOTIFICATION_ENGINE report (ALERT_NOTIFICATION_ENGINE job): This job enables you to use advanced features such as sending automated additional notifications. It schedules the notification engine program, which sends additional notifications, to run every minute. Scheduled every minute, since ST720 SP5, from Schedule Jobs step of Basic Configuration scenario.</li>\r\n<li>&#160;&#160;&#160;&#160;&#160; ACE_CALCULATION_CONTROLLER report (SAP_ALERT_CALCULATION_ENGINE job): This job evaluates the reported metrics, and updates the status of the monitored components, for the monitoring and alerting infrastructure. Scheduled every minute, since ST720 SP5, from Schedule Jobs step of Basic Configuration scenario.</li>\r\n<li>&#160;&#160;&#160;&#160;&#160; ACE_DELETE_EXPIRED_EVENTS report (SAP_METRIC_STORE_CLEANUP job): This job performs monitoring and alerting infrastructure housekeeping. It deletes outdated and no longer required metrics. Scheduled hourly, since ST720 SP5, from Schedule Jobs step of Basic Configuration scenario.</li>\r\n<li>\r\n<div style=\"padding-left: 30px;\">AGSCCL_COLL_MONITORING report (SM_CCL_COLLECTOR_MONITORING_JOB job): This report regulargly starts and monitors Custom Code Library Collector jobs. It ensures that the statuses of custom code library collectors are shown correctly to the end user in the UI. Scheduled every 30 minutes, since ST720 SP3.</div>\r\n</li>\r\n<li>\r\n<div style=\"padding-left: 30px;\">AGSNO_RPT_COLLECT_DATA report (SM:SYSTEM RECOMMENDATIONS job): This report periodically fetches SAP Notes, especially security notes and HotNews from the SAP backbone, gets implemented SAP Notes from managed systems, and calculates relevant SAP Notes for systems, individually, based on the information in LMDB. This information is displayed in System Recommendations, and can be used to monitor missing SAP Notes. Scheduled weekly, since ST720 SP1.</div>\r\n</li>\r\n<li>\r\n<div style=\"padding-left: 30px;\">AGS_ADAPTER_SD_SYNC_COMPONENTS report (SM:AGS_ADAPTER_APP_COMPONENTS_S job): This report is periocially executed to ensure that the Partner Test Management tool can synchronize with incidents in SAP Solution Manager Service Desk, and enter information about the SAP system, client, and application component in the external system. The system automatically transfers the list of application components in SAP Solution Manager Service Desk, and the systems of a Solution Documentation Branch to defect management. Scheduled daily, since ST720 SP3.</div>\r\n</li>\r\n<li>\r\n<div style=\"padding-left: 30px;\">AGS_ADAPTER_SOLDOC_SYNC report (SM:AGS_ADAPTER_SOLDOC_SYNC job): This report is periodically executed to automatically push the Solution Documentation from SAP Solution Manager to the Partner Test Magagement system. Scheduled daily, since ST720 SP3.</div>\r\n</li>\r\n<li>\r\n<div style=\"padding-left: 30px;\">AGS_ADAPTER_TST_RESULT_SYNC report (SM:AGS_ADAPTER_TST_RESULT_SYNC job): This report regularly pulls the test results from the Partner Test Management system into the SAP Solution Manager system. Scheduled hourly, since ST720 SP3.</div>\r\n</li>\r\n<li>\r\n<div style=\"padding-left: 30px;\">AGS_SERV_EWA_SETTGNS_MIGRATION report (SM:EWA_SETTINGS_MIGRATION job): This report migrates the old EarlyWatch Alert service settings (scheduling days, information to send, emails to be notified, Business Processing Analysis settings) existing in Solution Manager releases older than 720, that were attached to the deprecated Solutions objects, and makes them simply dependent of managed systems in ST720. Scheduled once, since ST720 SP1.</div>\r\n</li>\r\n<li>\r\n<div style=\"padding-left: 30px;\">AGS_SISE_SUPHUB_OUTBOX_PROCESS report (SM:AGS_SISE_SUPHUB_OUTBOX_PROCE job): This job periodically processes messages to be sent to SAP via the support hub channel. The job reads the Local Support hub message outbox, collects messages, and sends them to the support hub backbone via the support hub channel. The scheduling of this job is very important, as more and more applications are sending data via the support hub, and not anymore via old RFC communication. Scheduled hourly, since ST720 SP1.</div>\r\n</li>\r\n<li>\r\n<div style=\"padding-left: 30px;\">AI_SC_AISYNCH_2_AIUPDSYSSYNCH report (SM:MIGRATE TO AIUPDSYSSYNCH job): This report migrates the data in table AISYNCHRONIZE to table AIUPDSYSSYNCH, which will be used to record the synchronization status when uploading system data from LMDB to the SAP Support Portal. This report is required to be run once before the system data uploading is performed, when the system is go-live to Solution Manager 7.2 for the first time. Scheduled once, since ST720 SP1.</div>\r\n</li>\r\n<li>\r\n<div style=\"padding-left: 30px;\">AI_SC_GET_SAP_CUSTOMER_NUMBERS report (SM:AI_SC_GET_SAP_CUSTOMER_NUMBE job): This report downloads the SAP customer number of the SAP Solution Manager system from SAP Global Support Backbone and inserts it into view V_AISAPCUSTNOS. Scheduled once, only in ST720 SP1.</div>\r\n</li>\r\n<li>\r\n<div style=\"padding-left: 30px;\">AI_SC_MIGRATION report (SM:SERVICE_CONNECTION_MIGRATION job): This program migrates service connection data from the SAP Support Portal to SAP Solution Manager. This is required for the Service Connections application (transaction SOLMAN_CONNECT). The report copies the system data and the service connections to all known systems from the SAP Support Portal to SAP Solution Manager. It must run once with the default values before you can use the service connection functions in SAP Solution Manager.<br />Scheduled once, since ST720 SP1.</div>\r\n</li>\r\n<li>\r\n<div style=\"padding-left: 30px;\">AI_SC_UPLOAD_SYSTEM_DATA report (SM:UPLOAD SYSTEM DATA job): This report is scheduled to automatically upload to the SAP Support Portal the data of technical systems that were changed in the landscape management database (transaction LMDB). It replaces in ST720 the background job LANDSCAPE FETCH (report RSGET_SMSY), that is deactivated. Scheduled daily, since ST720 SP1.</div>\r\n</li>\r\n<li>\r\n<div style=\"padding-left: 30px;\">BSP_DYN_DESIGN_SHM_BUILD report (SM:BSP_DYN_DESIGN_SHM_BUILD job): This job fills the shared memory area (see SHMM transaction), and eventually activates it. Scheduled daily, since ST720 SP1, from IT Service Management setup scenario.</div>\r\n</li>\r\n<li>\r\n<div style=\"padding-left: 30px;\">CNM_SCHEDULED_NOTIFICATION report (SM:CNM_SCHEDULED_NOTIFICATION job): This report checks, builds and sends scheduled notifications by email, for consumer applications registered in the Scheduled Central Notification application. In this application, users can subscribe for notifications to be sent before/after different events. Scheduled hourly, since ST720 SP3.</div>\r\n</li>\r\n<li>\r\n<div style=\"padding-left: 30px;\">DRM_NOTIFICATION report (SM:DRM_NOTIFY job): This report generates notifications for a list of configured recipients about the status of the configured scenarios in Data Readiness Monitoring (DRM). The recipient list is maintained in Step 4 of DRM configuration. Scheduled every 5 minutes, since ST720 SP3.</div>\r\n</li>\r\n<li>&#160;&#160;&#160;&#160;&#160; E2EA_HOUSEKEEPING report (SAP_ALERT_HOUSEKEEPING job): This job evaluates the expiry of alerts in the alert store, and removes expired entities, according to housekeeping settings. Scheduled hourly, since ST720 SP5, from Schedule Jobs step of Basic Configuration scenario.</li>\r\n<li>&#160;&#160;&#160;&#160;&#160; E2E_DCC_SELFMON report (E2E DPC SELFMON job): This job transfers monitoring data for all Diagnostics Agents from the Java stack of the SAP Solution Manager system into the ABAP stack, where the Monitoring and Alerting Infrastructure accesses the data. Scheduled every 15 minutes,&#160;since ST720 SP5 from Schedule Jobs step of Basic Configuration scenario.</li>\r\n<li>\r\n<div style=\"padding-left: 30px;\">RAGS_CC_SCMON_CONTROL report (SM:SCMON_CONTROL job): This job periodically checks all the managed systems for which the user activates the UPL/SCMON data collection. Based on the available software release in the managed system, the suitable data collection mechanisms are determined for the managed system. Scheduled daily, since ST720 SP1.</div>\r\n</li>\r\n<li>\r\n<div style=\"padding-left: 30px;\">RAGS_SCMON_UPLOAD_STATUS_HK report (SM:SCMON_UPLOAD_STATUS_HK job): This job cleans-up outdated UPL/SCMON uploading status tracking records from database based on the predefined threshold. Scheduled daily, since ST720 SP1.</div>\r\n</li>\r\n<li>\r\n<div style=\"padding-left: 30px;\">SAM_CREATE_AP_CMP report (SM:SAM_CREATE_AP_CMP job): In the Service Availability Management application, users can create service availability definitions for the technical systems (days for which the services would be available and a Contractual Maintenance Period - CMP), to help in reporting on the availability of the services. As service availability definitions can be created for very long time periods, it is required to limit the creation of CMP constituents for optimization purpose. This periodic job checks all the service availability definitions and only generate enough constituents in future as necessary.<br />Scheduled daily, since ST720 SP3.</div>\r\n</li>\r\n<li>&#160;&#160;&#160;&#160;&#160; WMM_DAILY_PUSH_WMS_CLOUD report (SM:WMM_DAILY_PUSH_WMS_CLOUD job). This job is required for the Planning Calendar service available in One Support Launchpad. It pushes the 91th day down time data from the on premise Solution Manager to the SAP support backend. Scheduled daily, since ST720 SP6.</li>\r\n<li>&#160;&#160;&#160;&#160;&#160; WMM_INIT_PUSH_WMS_CLOUD report (SM:WMM_INIT_PUSH_WMS_CLOUD job). This job is required for the Planning Calendar service available in One Support Launchpad. It pushes the initial downtime data for the next 90 days from the on premise Solution Manager to the SAP Support backend.&#160;Scheduled only once, since ST720 SP6.</li>\r\n<li>\r\n<div style=\"padding-left: 30px;\">WMM_GENERATE_CONSTITUENTS report (SM:WMM_GENERATE_CONSTITUENTS job): In workmode management application, users can schedule workmodes for Planned Downtimes/Peak Business hours etc., as part of their landscape maintenance. As some recurrent workmodes schedules can cover a large time period, all the constituent workmodes are not generated immediately for performance reasons. This periodic job checks all the managed objects where recurrent workmodes are scheduled and generate next 100 constituents from the today's date. Scheduled daily, since ST720 SP1.</div>\r\n</li>\r\n<li>&#160;&#160;&#160;&#160;&#160; WMM_SYNC_WMS_CLOUD report (SM:WMM_SYNC_WMS_CLOUD job).&#160;This job is required for the Planning Calendar service available in One Support Launchpad. It pushes the changed downtime data in the past 1 hour from the on premise Solution Manager to the SAP support backend.&#160;Scheduled hourly, since ST720 SP6.</li>\r\n<li>\r\n<div style=\"padding-left: 30px;\">WMM_TRIGGER report (SM:WMM_TRIGGER job): Workmode application is used to create and query very frequently for workmodes on any managed object (technical system, database, host). Due to the increasing number of consumers (Alert Inbox, BI Reporting) which want to be notified about changes in workmodes, the pooling from consumers is not possible anymore and was replaced by this trigger job pushing the changed workmode information to consumers by calling BAdI asynchronouskly. Scheduled every minute, since ST720 SP1.</div>\r\n</li>\r\n<li>\r\n<div style=\"padding-left: 30px;\">WMM_HOUSEKEEPING report (SM:WMM_HOUSEKEEPING job): Workmode application is used to create and query for workmodes on any managed object (Technical System,&#160; Database,&#160; Host). Currently customers create workmodes for different systems in their landscape(even daily recurring workmodes). With time, the workmode database grows too big based on the number of systems in the customer's landscape and the number of workmodes per system. Considering that Workmode application is basically a planning tool and too old data is not needed, we want to clean the old workmode data. This background job(SM:WMM_HOUSEKEEPING) will clean workmodes older than 365 days. If the customer wishes, they can create their own variant with the number of days and use that while scheduling the job.</div>\r\n</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li><strong>Deprecated since January 8, 2020:<br /><br /></strong></li>\r\n<ul>\r\n<li>AGSSISE_REFRESH_RFCDEST report (SM:REFRESH_RFCDEST): This report regularly checks whether the RFC connections to the SAP Support Portal use the load balancing, and whether they work correctly. Scheduled daily, since ST720 SP1.<br /><br /></li>\r\n</ul>\r\n<li><strong>Deprecated since 7.2</strong>:</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>/TMWFLOW/RMO_STACKUPDATE&#160;report (SM:STACK_INFO_UPDATE job)</li>\r\n<li>AGSSISE_SYSSOL_COLLECTOR&#160;report (SM:SISE_SYSSOL_COLLECTOR job)</li>\r\n<li>AGS_DVM_EFWK_AUTO_ADJUST&#160;report (SM:AGS_DVM_EFWK_AUTO_ADJUST&#160;job)</li>\r\n<li>AGS_DVM_IMPACT_ANALYSIS_PREP&#160;report (SM:DVM PROJECT PRECALC&#160;job)</li>\r\n<li>AGS_DVM_IMPACT_ANALYSIS_PREP&#160;report (SM:DVM PROJECT PRECALC IMMEDIAT&#160;job)</li>\r\n<li>AGS_DVM_TIME_CORRECTION&#160;&#160;report (SM: AGS_DVM_TIME_CORRECTION&#160;job)</li>\r\n<li>AI_SDK_FILL_FILE_TYPE_TABLE&#160;report (SM: GET FILE TYPES FROM SAP&#160;job)</li>\r\n<li>DSWP_BPM_REORGANISATION&#160;&#160;report (SM:REORG BPMON ALERTS&#160;job)</li>\r\n<li>DSWP_CI_ISSUE_BUFFER_TABLE&#160;report (SM:UPDATE ISSUE BUFFER&#160;job)</li>\r\n<li>HRBCI_ATTRIBUTES_BUFFER_UPDATE&#160;report (HRBCI_ATTRIBUTES_BUFFER_UPDATE&#160;job)</li>\r\n<li>RAGS_ENGAGE_CYCLE_DB_2_CRM&#160;report (SM: TRANSFER CYCLE (DB-&gt;CRM)&#160;job)</li>\r\n<li>RAGS_SERVREQ_LOAD_FROM_SAP report (SM:SYNC SERVICE REQUESTS job)</li>\r\n<li>RAGS_WORK_BPM_CACHE&#160;&#160;report (SM:FILL BPM CACHE FOR WORKCENTE&#160;job)</li>\r\n<li>RBM_REFOBJ_BUFFER_UPDATE&#160;report (SOLMAN_ISSUE_STATUS_REFRESH&#160;job)</li>\r\n<li>RDMD_ACCELERATE_DOC_USAGE&#160;report (SM:ACCELERATE DOC USAGE&#160;job)</li>\r\n<li>RDMD_MIGRATE_OBJS_2_LANG_INDEP&#160;report (SM:MIGRATE LANGU DMD OBJ&#160;job)</li>\r\n<li>RDMD_MIGRATE_TECH_INTERFACE&#160;report (SM:MIGRATE_TECH_INTERFACE&#160;job)</li>\r\n<li>RDMD_REFRESH_INDEX&#160;&#160;report (SM:REFRESH_INDEX&#160;job)</li>\r\n<li>RDMD_REMOVE_INCON&#160;&#160;report (SM:REMOVE INCONSISTENCIES&#160;job)</li>\r\n<li>RDSMOPCOLLECTSOLUTIONDATA&#160;report (SM:SEND_SOLUTIONS_TO_SAP&#160;job)</li>\r\n<li>RDSWPCI_ISSUE_PROJECT_CONTEXT1&#160;report (SM:MIGRATE_ISSUE_PROJECT_CONTEX&#160;job)</li>\r\n<li>RDSWPCI_SURVEY_TRANSFER&#160;&#160;report (SM:SURVEY TRANSFER&#160;job)</li>\r\n<li>RDSWPJOBSCHEDULER&#160;&#160;report (SM:SCHEDULER&#160;job)</li>\r\n<li>RDSWPMIGRATEEWACUSTOMIZING&#160;report (SM:MIGRATE EWACUSTOMIZING&#160;job)</li>\r\n<li>RDSWPSETDEFAULTRATINGHIERARCHY&#160;report (SM:SET DEFAULT RATING&#160;job)</li>\r\n<li>RDSWP_CLEAN_DSWPJOB&#160;&#160;report (SM:RDSWP_CLEAN_DSWPJOB&#160;job)</li>\r\n<li>RDSWP_NA_DEL_NOTIF_ADMIN_DATA&#160;report (SAP_NOTIFADMIN_DELETE_USERS&#160;job)</li>\r\n<li>RDSWP_SC_MANAGED_SYSTEMS&#160;report (SM:SELFDIAGNOSISSELFCHECK&#160;job)</li>\r\n<li>RDSWP_SSA_MOVE_2_ARCHIVE_QUEUE&#160;report (SM:MOVE TO ARCHIVE QUEUE&#160;job)</li>\r\n<li>RLMDB_OSS_SYNC_CONF_AUTO_MIG&#160;report (SM:RLMDB_OSS_SYNC_CONF_AUTO_MIG&#160;job)</li>\r\n<li>RMIGRATE_LANG_DEP_SAPSCRIPT&#160;report (SM:MIGRATE_LANG_DEP_SAPSCRIPT&#160;job)</li>\r\n<li>RNOTIFUPDATE01 report (REFRESH MESSAGE STATUS&#160;job)</li>\r\n<li>RSGET_SMSY report (LANDSCAPE FETCH&#160;job)</li>\r\n<li>RSOLDIAG_CHECK_FOR_UPDATE&#160;report (SM:SOLMAN_DIAG_UPDATE&#160;job)</li>\r\n<li>RS_WBA_REFRESH_POWL_BUFFER&#160;report (SM:RS_WBA_REFRESH_POWL_BUFFER&#160;job)</li>\r\n<li>RWBA_RFC_WATCHER report (SM:RFC MONITORING&#160;job)</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li><strong>Deprecated since 7.2 SP12</strong></li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>RDSWPDOWNLOADDELETION report (Job SM:DOWNLOAD DELETION): it has been deactivated because it&#8217;s redundant, as its activity is done by SDCCN Maintenance Package task, that implicitly runs the old download deletion.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li><strong>Deprecated since 7.2 SP14</strong></li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>WMM_DAILY_PUSH_WMS_CLOUD report (Job SM:WMM_DAILY_PUSH_WMS_CLOUD)</li>\r\n<li>WMM_INIT_PUSH_WMS_CLOUD report (Job SM:WMM_INIT_PUSH_WMS_CLOUD)</li>\r\n<li>WMM_SYNC_WMS_CLOUD report (Job SM:WMM_SYNC_WMS_CLOUD)</li>\r\n</ul>\r\n</ul>\r\n<p style=\"padding-left: 60px;\"><span style=\"font-size: 14px;\">The three jobs above&#160;</span>were created to push the onPremise events data to the cloud for IT calendar that has been developed in the cloud &#8211; launchpad. This calendar is not used anymode so these jobs are useless.</p>\r\n<ul>\r\n<li><strong>Introduced with 4.0 and higher&#160;</strong>:&#160;</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>/SDF/MON_REORG report (Job SNAPSHOT MONITORING REORG): Reorganization of obsolete /SDF/MON analyses, scheduled daily since ST710 SP1 version.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>/TMWFLOW/CMSSYSCOL2 report (Job SM:TMWFLOW_CMSSYSCOL): Program for collecting data of transports, notes and support packages of each System defined in table /TMWFLOW/CMSCONF using asynchronous RFC-function calls, scheduled daily, since ST400.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>/TMWFLOW/RMO_STACKUPDATE report (Job SM:STACK_INFO_UPDATE): Update the stacks that are available for a product version (see SAP note 1581095), scheduled daily since ST400.</li>\r\n<li>AC_JOBMON_CONSISTENCY_CHECK report (SM:JOBMON_CONSISTENCY_CHECKER job):&#160;This report performs periodically a consistency check to show to the end user if any configuration in the job monitoring is out-of-sync with the managed systems. Scheduled weekly, since ST710 SP12.</li>\r\n<li>AC_JOBMON_CPS_CONSISTNCY_CHCKR report (SM:JOBMON_CPS_CONSISTNCY_CHCKR job): This report performs periodically a consistency check to show to the end user if any configuration in the job monitoring is out-of-sync with the monitoring objects running in the Central Processing Scheduler (CPS). Scheduled weekly, since ST710 SP13.</li>\r\n</ul>\r\n</ul>\r\n<p>&#160;</p>\r\n<ul>\r\n<ul>\r\n<li>ACC_ALERTSTORE_CLEANUP report (Job SM:ACC_ALERTSTORE_CLEANUP): Purges old alert groups of Job Monitoring Scenario which are not closed from alerting runtime table. Scheduled daily since ST710 SP11 (for SP10 this job schedule has to be created manually using SM36 transaction, with a start time&#160;= 12:00:00; Use the SM:ACC_ALERTSTORE_CLEANUP jobname for SP10 to avoid duplicate jobs with the next ST upgrade; otherwise applications of Technical Monitoring will suffer from performance issues).</li>\r\n<li>ACC_HOUSEKEEP_CONFIRMED report (Job SM:ACC_HOUSEKEEP_CONFIRMED):&#160;This report periodically deletes the confirmed alerts from Monitoring and Alerting Infrastructure tables, as specified under SOLMAN_SETUP -&gt; Technical Monitoring -&gt; Housekeeping -&gt; 'Automatically delete the confirmed alert older than' option. Scheduled daily, since ST710 SP9.</li>\r\n</ul>\r\n</ul>\r\n<p>&#160;</p>\r\n<ul>\r\n<ul>\r\n<li>AGS_CC_SP12_MIGRATION report (Job SM:AGS_CC_SP12_MIGRATION_JOBS): The objective of this migration job is the adjustment of CCLM library for SP12 release: remove Custom Code Items, migrate to Custom Code Object and fetch last usage data for CC objects and update related database table, update CCLM attributes and adjust Duplicate CC. Scheduled only once, since ST710 SP12, from Custom Code Management setup scenario</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>AGS_DVM_EFWK_AUTO_ADJUST report (Job SM:AGS_DVM_EFWK_AUTO_ADJUST): This program automatically adjusts the extractor settings in case there was a software change in any related managed system. To keep the extractor definition in sync with the software changes reflected in LMDB, this job checks regularly if the managed system was patched (e.g. SP update, EhP implementation or release upgrade) and adjusts the extractor settings correspondingly. Scheduled daily since ST710 SP9 in \"Data Volume Management\" setup scenario, deprecated in ST720.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>AGS_DVM_IMPACT_ANALYSIS_PREP report (Job SM:DVM PROJECT PRECALC): Precalculation job required to use the newly introduced scenario for Data Volume Management Impact and Reference Analysis. As a main input for this new feature your project and process information in your SAP Solution Manager system is the key information. This job will make sure that all currently available information will be evaluated and prepares the system for the first usage of this new feature. Scheduled weekly since ST710 SP7 in \"Data Volume Management\" setup scenario, deprecated in ST720.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>AGS_DVM_TIME_CORRECTION report (Job SM: AGS_DVM_TIME_CORRECTION): Conversion job intended to convert data in the BW infrastructure (InfoCubes) related to Data Volume Management. Depending from which release or SP level the current SAP Solution Manager system has been updated to its current release some structure changes might have happened. To be able to use data which has been extracted in earlier releases or SP levels also in new reporting functions a conversion is necessary. The job SM: AGS_DVM_TIME_CORRECTION is just required to start once when you have performed an SP stack implementation in your SAP Solution Manager system. The job might run for hours in case a lot of data has to be converted. Scheduled once since ST710 SP5 in \"Data Volume Management\" setup scenario, deprecated in ST720.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>AGSSISE_DELETE_TMP_RFC report (Job SM:REMOVE TEMPORARY RFC): Delete temporary RFCs created during solman setup in case this was not done there due to some error, scheduled hourly, since ST710 SP1.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>AGSSISE_SYSSOL_COLLECTOR report (Job SM:SISE_SYSSOL_COLLECTOR): The purpose of this report is to add all production systems to one standard solution, so that EarlyWatch can be scheduled automatically. A standard solution is created during the initial configuration of the Solution Manager (transaction SOLMAN_SETUP). This standard solution is used in this report. When the solution is created, the system proposes the name #SAP Solution#, which can be changed. If there is no standard solution, this report creates a standard solution #SAP Solution#. The report then selects all production systems belonging to the customer number using the installation number. All logical components containing one of the systems in the system role that correspond to the leading roles in the solution are then assigned to the solution. So that the Solution Manager is also included in this standard solution, the logical component #Z_SOLMAN_COMP# is created and assigned to the standard solution during the initial configuration. If this does not exist, it is created by the report. In addition, a special solution can be created for each customer number. This special solution is then used instead of the standard solution. You can maintain these customer-specific solutions in the view V_AISAPCUSTNOS (transaction SM30). The report automatically fills the customer number field in the settings of the corresponding solution for all solutions assigned to a customer number using V_AISAPCUSTNOS. Scheduled weekly since ST400, deprecated in ST720.</li>\r\n</ul>\r\n</ul>\r\n<p>&#160;</p>\r\n<ul>\r\n<ul>\r\n<li>AGSSISE_USER_CLEANUP report (Job SM:AGSSISE_USER_CLEANUP): This report periodically deletes all users entries still existing in Solution Manager User Plugin but already deleted from SU01 transaction. Scheduled daily, since ST710 SP12, from Basic Configuration setup scenario.</li>\r\n</ul>\r\n</ul>\r\n<p>&#160;</p>\r\n<ul>\r\n<ul>\r\n<li>AGSSISE_USER_STATUS_UPDATE report (Job SM:USR_STATUS_UPDATE): The job updates users statuses each night. Those statuses will then be displayed when navigating&#160;to &#8216;Create users&#8217; step and in Solution Manager User Administration. Scheduled daily, since ST710 SP12, from Basic Configuration setup scenario.</li>\r\n</ul>\r\n</ul>\r\n<p>&#160;</p>\r\n<ul>\r\n<ul>\r\n<li>AI_CRM_AIDIAGNOTIF_CREATE report (Job AI_CRM_AIDIAGNOTIF_CREATE): This report takes care of notifications coming from LMDB or CMDB, stating that a system or a device has been changed or created. It creates or updates an IObject for the system or device related to the notification. This IObject is then used for referencing in an Incident Message or other CRM documents. Scheduled daily during night, since ST710 SP5.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>AI_CRM_IM_UPDATE_FROM_SAP report (Job REFRESH MESSAGE STATUS): This report is used for incident management in solution manager, to retrieve or replicate SAP messages. It replaces for ST 7.10 the old report RNOTIFUPDATE01 (see note 1566108). Scheduled hourly, since ST710 SP1. Since ST710 SP5 this job is scheduled in the \"IT Service Management\", \"Incident, Problem &amp; Request Management\" specific setup scenario.</li>\r\n<li>AI_CRM_PROCESS_SLA report (SM:ITTM_SET_OVERDUE_STATUS):&#160;This report periodically sets SLA status values, such as IRT Warning or MPT Exceeded, in incidents and in operational tasks. Scheduled hourly, since ST710 SP13.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>AI_SC_LISTENER report (Job SERVICE_CONNECTION_LISTENER): This report periodically checks whether a service connection is planned to be opened. It replaces the previously necessary reservation. If a future connection event is selected in the transaction SOLMAN_CONNECT, it is saved locally in the Solution Manager. This program is scheduled every 10 minutes, and opens the system if required. Since ST400.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>AI_SC_REFRESH_READ_ONLY_DATA report (Job REFRESH_ADMIN_DATA_FROM_SUPPORT): This report reads various information from the SAP Support Portal (like maintenance key data, system numbers of all known product systems in SMSY, installation numbers of these systems, contact persons, service types for remote connection, SAProuter) and saves it locally in the Solution Manager. If customer numbers are maintained in the maintenance view V_AISCUSTNOS, the report can create business partners per customer number, get installation numbers for customer numbers, put the product systems of these installations in SMSY. It is scheduled every day since ST400.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>AI_SC_SEND_SYSTEM_RELATIONSHIP report (Job SEND_SYSTEM_RELATIONSHIP_TO_SUPP): This report sends information to the SAP Support Portal, which is required by the service connection types \"SAP Solution Manager\" and \"SolMan Diagnostics\". The numbers of all product systems known to transaction SMSY are sent. Systems which are known to the Solution Manager diagnostics are flagged, and the system numbers of Solution Manager and Solution Manager Diagnostic systems are sent to SAP. See SAP note 962516 for further information. It is scheduled every day since ST400.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>AI_SDK_FILL_FILE_TYPE_TABLE report (Job SM: GET FILE TYPES FROM SAP): For security reasons, only special file types are allowed as attachments in messages sent to SAP - all other attachments will be rejected. The program determines all relevant file types from CSS. Scheduled every 3 months since ST400, until ST710 SP8 included. This report was replaced by RAGS_FILE_EXT_L_DL report, also scheduled in the solution manager setup at the same place. In case SM: GET FILE TYPES FROM SAP job is failing, then check the job logs and if you find a message telling that the new report is RAGS_FILE_EXT_L_DL, then just unschedule it manually from SM37.</li>\r\n</ul>\r\n</ul>\r\n<p>&#160;</p>\r\n<ul>\r\n<ul>\r\n<li>DATA_POOL_AVAILABILITY_PROG report (Job SM:AGS_CC_BUSI_CRIT_AVAIL): this report checks periodically the infrastructure availability for data pool (i.e. metric to measure the criticality for custom code object)&#160;for different managed systems. On the one hand it&#160;checks whether data is available in general for a system and pool. On the other hand the first available month of data should be determined. Scheduled on a weekly basis, since ST710 SP11, from Custom Code Management setup scenario.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>DSVAS_APPL_CSA_REORG_TASKTABLE report (Job SM:CSA SESSION REFRESH): This report clears the CSA task status icons in the graphical overview of central systems administration. It is scheduled every day since ST400.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>DSVAS_APPL_CSA_UPD_TASKSTATUS report (Job SM:CSA UPDATE TASKSTATUS) updates status symbols of CSA tasks in the graphical overview of central systems administration. It is scheduled every 30 minutes, since ST400.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>DSWP_BPM_REORGANISATION report (Job SM:REORG BPMON ALERTS): This report performs the reorganization of Business Process Monitoring alert data for all solution which contain monitoring data. Scheduled daily during night, since ST710 SP5, deprecated in ST720.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>DSWP_CI_ISSUE_BUFFER_TABLE report (Job SM:UPDATE ISSUE BUFFER). This report updates the buffer for the issue management (issues and their joins are stored in temporary tables for performance optimization). It is scheduled every day, since ST400 until ST710 SP10 included. Starting SP11 existing schedules of this job are removed. Also deprecated in ST720.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>DSWP_GET_CSN_COMPONENTS report (Job SM:GET CSN COMPONENTS): this report gets the application components from CSN and saves them into tables. It is scheduled every week, since ST400.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>DSWP_GET_PPMS_DATA_AUS_OSS report (Job SM:GET_PPMS_DATA_FROM_OSS): This report retrieves PPMS data from the SAP Support Portal and saves it in the Solution Manager. This information is required for the Service Sessions. Scheduled daily (random execution time per customer system), since ST400 SP22.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>E2E_DCC_AGT_MON report (Job E2E DCC AGENT MONITORING): This report collects the diagnostic agents statuses (metric \"Diagnostic Agent unavailable\") into the Monitoring and Alerting Infrastructure. It is scheduled every 10 minutes, since ST710 SP1.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>E2E_EFWK_HOUSEKEEPER report (Job E2E EFWK HOUSEKEEPING): Housekeeping job for the extractor framework. Scheduled daily. Since ST710 SP1.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>E2E_EFWK_RESOURCE_MGR report (Job EFWK RESOURCE MANAGER): This job is checking if data extractors defined within the EFWK in table E2E_ACTIVE_WLI are due to execution regarding their actual status described in table E2E_EFWK_STATUS. If data extractors are due, then they are executed in the managed systems via RFC call due to the settings in table E2E_RESOURCES. If no data extractors are due, the report is finished immediately, generating no further workload. Scheduled every minute since ST710 SP1. Since ST710 SP5 this job has to use the dedicated SM_EFWK batch user.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>E2E_HK_CONTROLLER report (Job E2E BI HOUSEKEEPING): This report performs housekeeping activities on the E2E Diagnostics BI Infocubes statistical records (see SAP note 1480588). Scheduled daily. Since ST710 SP1.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>E2EEM_HOUSEKEEPING_REPORT report (Job SM:EXCEPTION MGNT HOUSEKEEPING): Housekeeping report to delete old data from EM/BPCC store from Solman and managed systems as per the configuration maintained in EM/BPCC tables. Scheduled daily since ST710 SP4</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>GN_GENERATE_CHECK report (Job GN_GENERATE_CHECK). This report creates a worklist with objects (some industry specific function modules) which need generation. To be run before GN_WORKLIST_GENERATE, Scheduled every day, since ST400.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>GN_WORKLIST_GENERATE report (Job GN_WORKLIST_GENERATE): This report generates the objects (some industry specific function modules) in the worklist created before by GN_GENERATE_CHECK report. Scheduled every day, since ST400.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>HRBCI_ATTRIBUTES_BUFFER_UPDATE report (Job HRBCI_ATTRIBUTES_BUFFER_UPDATE): This report updates CRM organizational data (model and attributes) into the buffer for a specific day. The program also offers you the option of preparing mobile client distribution for the scenarios SALE and SERVICE. Scheduled daily. Since ST400, deprecated in ST720.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>PR_DIAGLS_CLEANUP report (Job DIAGLS_CLEANUP): This report purges the E2E Diagnostics logs. Scheduled daily, since ST710 SP1.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>PR_DIAGLS_COMPUTE_STATUS report (Job DIAGLS_COMPUTE_STATUS): This report updates the status of the elements of the landscape API. Scheduled daily, since ST710 SP1.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>PRGM_DSWP_NA_SYNC_DLS report (Job SAP_NOTIFADMIN_SYNC_DLS): This report triggers a synchronization of distribution lists from mail server to the database. Scheduled daily, since ST710 SP1.</li>\r\n</ul>\r\n</ul>\r\n<p>&#160;</p>\r\n<ul>\r\n<ul>\r\n<li>RAGS_CCM_HK_TRIGGER report (Job SM:RAGS_CCM_HK_TRIGGER): this report performs BI housekeeping activities (0SM_CCLG , 0SM_CCREF info-cubes) related to Custom Code Management in Solution Manager BI client only. It is scheduled on a daily basis, since ST710 SP11, from Custom Code Management setup scenario.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>RAGS_DSWP_SERV_CONTENT_UPDATE report (Job SM:Service Content Update): The job should run once a day if it is set to active in the SD Work Center.&#160;&#160;The job performs an update of all the service content. For more details please see SAP note 1143775. As of ST400 Ehp1.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>RAGS_ENGAGE_CYCLE_DB_2_CRM report (Job SM: TRANSFER CYCLE (DB-&gt;CRM)): Since ST400 SP26, solution manager can handle transaction type 'SLFC' for Engagement Cycle in \"SAP Engagement and Service Delivery\" work center. As the DB structure was changed, this report transfers the data created before SP26 to the CRM and the new DB schema. Scheduled only once, since ST400 SP26 (Ehp1), deprecated in ST720.</li>\r\n</ul>\r\n</ul>\r\n<p>&#160;</p>\r\n<ul>\r\n<ul>\r\n<li>RAGS_ESD_STORE_STATUS_DATA report (Job SM:STORE-ISSUE-STATUS-DATA): This report writes periodically the status of ESD issues into database table AGS_ESD_RECORD. This is required if you want to get the status of issues on time scale, for example to show the dashboard \"Progress Reporting for Value Based Delivery\".&#160;Scheduled daily, since ST710 SP12.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>RAGS_FILE_EXT_L_DL report (Job SM:LONG FILE EXT DOWNLOAD): Download from CSN and update the long file extensions that can be handled for incident exchange (Alert inbox and message creation). Scheduled weekly, since ST400. This report replaces the old AI_SDK_FILL_FILE_TYPE_TABLE report.</li>\r\n</ul>\r\n</ul>\r\n<p>&#160;</p>\r\n<ul>\r\n<ul>\r\n<li>RAGS_JSM_SMSE_DATA_REORG report (Job SM:REORG SMSE JOB DATA): This report reorganize the data that is pushed from an external job scheduler via the SMSE (Solution Manager Scheduling Enabler) interface to SAP Solution Manager. It periodically removes the data for terminated external jobs older than one day and for all other external jobs older than 5 days. In order to change these retention periods a variant has to be defined for report RAGS_JSM_SMSE_DATA_REORG specifying the desired number of days for final and all jobs. Then the job named SM:REORG SMSE JOB DATA has to be updated with the previously created variant. Keep in mind that the modified job may be removed by SOLMAN_SETUP if the Solution Manager batch user name is changed and if the SOLMAN_SETUP activity scheduling the job in Basic Configuration scenario is executed again because the default configured variant in SOLMAN_SETUP cannot be modified nor deactivated, and therefore will be applied again. Scheduled daily, since ST710 SP12, from Basic Configuration setup scenario.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>RAGS_SERVICE_NOTIFICATION report (Job SM:SERVICE-DELIVERY-NOTIFICATION): This report checks upcoming service sessions and notifies you to prepare and execute the session. Scheduled daily since ST710 SP5</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>RAGS_SERVREQ_LOAD_FROM_SAP report (Job SM:SYNC SERVICE REQUESTS): This report regularly collects service requests from SAP. Scheduled hourly, since ST400 SP23.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>RAGS_WORK_BPM_CACHE report (Job SM:FILL BPM CACHE FOR WORKCENTER): After creating a solution (in DSWP or 'SAP Solution Manager Administration' work center with ST710 release), the new solution does not appear in Business Process Operation work center automatically. This report refreshes solution landscape buffers for BPO work center. Scheduled daily, since ST400, deprecated in ST720.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>RAGS_WORK_SD_CACHE report (Job SM:FILL SD CACHE FOR WORKCENTER): This job fills the cache for the SAP Engagement and Service Delivery work center. Scheduled hourly, since ST400 (this report does nothing starting ST710 SP1, as buffers are not used anymore for both services and issues data which are read directly).</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>RBM_REFOBJ_BUFFER_UPDATE report with SAP&amp;ISSUES variant (Job SOLMAN_ISSUE_STATUS_REFRESH): The SAP Solution Manager buffers message attributes such as the current user and the processing status. This periodic job collects these message attributes from the message system and makes them available for analysis. Scheduled hourly, since ST400, deprecated in ST720.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>RCSDCCHANDLETASKS report (Job SM:CSDCC HANDLE TASKS): This report synchronizes the list of requests for the SDCCN download with the list of SAP Solution Manager service sessions. Scheduled daily, since ST400.</li>\r\n</ul>\r\n</ul>\r\n<p>&#160;</p>\r\n<ul>\r\n<ul>\r\n<li>RCSU_ARCHIVE_WRITE report (Job SM:RCSU_ARCHIVE_WRITE): This report moves the downloaded support packages of software component ST-CONT that are no longer required by the Rapid Content Delivery application from the database to the archive, and then deletes the archived packages from the database. Scheduled every 3 months, since ST710 SP12, from Basic Configuration setup scenario.</li>\r\n</ul>\r\n</ul>\r\n<p>&#160;</p>\r\n<ul>\r\n<ul>\r\n<li>RCSU_AUTO_NOTIFY_AND_DWLD report (Job SM:RCD_CHECK_UPDATES): This report periodically checks for the latest support package updates of the software component ST-CONT in SAP Service Marketplace. If updates are available and if you have maintained the auto-download settings in the manual activity \"Configuration for Rapid Content Delivery\", the report automatically downloads the updates from SAP Service Marketplace. Scheduled weekly, since ST710 SP12, from Basic Configuration setup scenario.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>RDMD_ACCELERATE_DOC_USAGE report (Job SM:ACCELERATE DOC USAGE): This report accelerates the where-used list for documents in the Solution Manager. Scheduled daily, since ST400, deprecated in ST720.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>RDMD_MIGRATE_TECH_INTERFACE report (Job SM:MIGRATE_TECH_INTERFACE): As of ST400 SP15, it is possible to create and use interfaces in the solution directory of SAP Solution Manager. This report generates the interfaces for any existing technical interfaces. Scheduled daily, since ST400, deprecated in ST720.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>RDMD_REFRESH_INDEX report (Job SM:REFRESH_INDEX): This job updates the index for solutions once a day. This is updated on demand for changes in solutions. But this job should run in order to obtain a secure status. Scheduled daily, since ST400, deprecated in ST720.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>RDMD_REMOVE_INCON report (Job SM:REMOVE INCONSISTENCIES): This report removes potential data inconsistencies in the solutions. Scheduled daily, since ST400, deprecated in ST720.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>RDMD_REORG_APPLICATION_LOG report (Job SM:REORG APPLICATION LOG): This report reorganizes the application logs. Scheduled daily since ST400.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>RDSMOP_SESSSION_RESET report (Job SM:SESSIONS RESET): This report performs session initialization. The set-up sessions are automatically reset after a new ST-SER release is implemented or after a new Support Package is imported. This ensures that these sessions always run on the newest check source code. Scheduled daily since ST400.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>RDSMOPBACK_AUTOSESSIONS_ALL report (Job SM:EXEC SERVICES): This report executes service sessions in Solution Manager, carries out services daily (or weekly) and schedules new services. Scheduled daily, since ST400.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>RDSMOPCOLLECTSOLUTIONDATA report (Job SM:SEND_SOLUTIONS_TO_SAP): This report sends the data of the appropriately configured solutions to SAP. Scheduled daily, since ST400, deprecated in ST720.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>RDSMOPSERVICEINFOS report (Job SM:SYNC SOLMAN INFO): To transfer the information about SAP Solution Manager usage (components used by customers) to SAP. Scheduled daily, since ST400.</li>\r\n<li>\r\n<p>RDSMOPSOLUTIONLISTUPDATE report (Job SM:REFRESH ENTRYSCREEN): This job determines the status of the solutions&#160;for the overview list. Scheduled daily in ST400, deprecated in ST710 SP1.</p>\r\n<p>&#160;</p>\r\n</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>RDSWP_ARCHIVE_WMS report (Job DSWP_ARCHIVE_WORKMODES): This report archives work modes older than 30 days. See SAP note 1538133 for more details. Scheduled every 12 hours, since ST710 SP1.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>RDSWP_CI_MIG_SERVICE_DATE_SP15 report (Job SM:MIG_SERVICE_DATE_SP1): Old database migration program for service dates required starting ST400 SP15. Scheduled once since ST400.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>RDSWP_CLEAN_DSWPJOB report (Job SM:RDSWP_CLEAN_DSWPJOB): This report removes some unnecessary jobs. Scheduled monthly in ST400, daily in ST710, deprecated in ST720.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>RDSWP_DTM_UPDATE_DT_STATUS report (Job UPDATE DOWNTIME STATUS): This report updates the Downtime Manager data (downtime status) to ensure semantic consistency. Scheduled daily, from ST400 SP15 to ST710 SP2.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>RDSWP_FILL_CCMS_ALERTS report (Job SM:SOLMAN MONITORING): Supplies the monitoring object of the CCMS for every solution with data from the Solution Manager, for example EWA, SL Reporting and Transaction SDCCN. Scheduled hourly since ST400.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>RDSWP_ISSUE_REFRESH report (Job SM:SYNC ISSUES FROM CRM): The table DSWPISSUE, which contains information from the CRM document and the support message (context), is updated by this report. This function is required for issue tracking and Service Plan. Scheduled hourly, since ST400.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>RDSWP_ITC_DATA_MIGRATE_SP05 report (Job SM:RDSWP_ITC_DATA_MIGRATE_SP05): This report should be run only once after an upgrade to Solution Manager SP&gt;=5 has been done. This report performs data migration to relevant Downtime/Uptime data available in IT Calendar events table created via Downtime Manager/IT Calendar, before the upgrade. Scheduled once, since ST710 SP5.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>RDSWP_NA_DEL_NOTIF_ADMIN_DATA report (Job SAP_NOTIFADMIN_DELETE_USERS): This report deletes recipients or recipient lists used for notification for which the delete flag is true and last_changed_by date is older than 30 days (by default). The job name was changed into SAP_NOTIFADMIN_SYNC_RECIPIENTS in ST710 starting SP15 and in ST720. Scheduled daily, since ST710 SP1.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>RDSWP_NM_SYNC_USER_DATA report (Job SM:SYNCHRONIZE USER DATA FOR NOT): This report synchronizes user data for the Notification Engine from Solution Manager satellite systems. Scheduled daily, since ST400 SP15.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>RDSWP_SC_MANAGED_SYSTEMS report (Job SM:SELFDIAGNOSISSELFCHECK): This report executes self checks on the managed systems of the Solution Manager. Scheduled weekly, since ST710 SP1, deprecated in ST720.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>RDSWP_SELF_DIAGNOSIS (Job SM:SELFDIAGNOSIS): This report updates self-diagnosis information (solution-specific and cross-solutions yellow and red alerts used in service desk, issue management...). More details can be found in SAP note 1073382. Scheduled daily, since ST400.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>RDSWP_SELF_DIAGNOSIS_CRM_TRF report (Job SM:SELFDIAGNOSIS_SEND_TO_SAP): This report sends data from self-diagnosis application on customer's solution manager system to SAP (CSS system). Scheduled monthly, since ST400 SP26.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>RDSWP_SSA_MIGRATE_SESS_DL report (Job SM:MIGRATE SESS DL.): Migration report for the initial filling of table DSWPDOWNLOADADM based on the service sessions present in table DSVASSESSADMIN. Scheduled once, since ST400.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>RDSWP_SSA_MOVE_2_ARCHIVE_QUEUE report (Job SM:MOVE TO ARCHIVE QUEUE): This report triggers the archiving of the services and sessions (EWAs), for which the preset retention period was exceeded. Scheduled daily, since ST400, deprecated in ST720.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>RDSWPCI_ISSUE_PROJECT_CONTEXT1 report (Job SM:MIGRATE_ISSUE_PROJECT_CONTEXT): This report migrates the project contexts for issues. Scheduled daily, since ST400, deprecated in ST720.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>RDSWPCI_SURVEY_TRANSFER report (Job SM:SURVEY TRANSFER): This report transfers the questionnaires for customer satisfaction with the service session and issue processing to SAP. Scheduled weekly, since ST400, deprecated starting ST710 SP9 and&#160;in ST720.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>RDSWPCI_TOPISSUE_TRANSFER report (Job SM:TOP ISSUE TRANSFER): This report transfers the top issues that you have exchanged with SAP. Scheduled daily, since ST400.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>RDSWPCISERVICEPLAN report (Job SM:SYNC SAP SESSIONS): The session scheduling in the service plan is updated daily by SAP. This report is required to receive service plans from SAP. Scheduled daily, since ST400.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>RDSWPDOWNLOADDELETION report (Job SM:DOWNLOAD DELETION): This report deletes download data (transaction SDCCN), which is more than 30 days old. Scheduled every 3 days, since ST400. Be aware that its job is inactive since ST 720 SP12 because it&#8217;s redundant, as its activity is done by SDCCN Maintenance Package task, that implicitly runs the old download deletion.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>RDSWPJOBSCHEDULER report (Job SM:SCHEDULER): This report triggers all the other background jobs in the old job framework (job entries from the DSWPJOB table not in SMCONFIGJOBS table). Scheduled hourly, since ST710 SP1 until SP8 included, also deprecated in ST720.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>RDSWPMIGRATEEWACUSTOMIZING report (Job SM:MIGRATE EWACUSTOMIZING): Migration report to propagate the values for \"EWA Period\" and \"EWA Weekday\" from Solutions to systems. Scheduled once, since ST400, deprecated in ST720.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>RDSWPRULESUPDATE report (Job SM:UPDATE RULES): This report downloads a new set of rules for the Solution Manager. This set of rules controls the services and documents that can be offered (inline) for the information about system infrastructure and processes, which are maintained in the Solution Manager. Scheduled daily, since ST400.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>RDSWPSETDEFAULTRATINGHIERARCHY report (Job SM:SET DEFAULT RATING): Report to set the default of the rating hierarchy for Solution Monitoring (SysMon and BPM). Scheduled once, since ST400, deprecated in ST720.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>RLMDB_OSS_SYNC_CONF_AUTO_MIG report (Job SM:RLMDB_OSS_SYNC_CONF_AUTO_MIG): SAP Solution Manager uploads system data to SAP Support Portal as described in SAP note 993775. With Solution Manager 7.1 SP05 the data source switches from SMSY to LMDB. This report gets executed once and ensures that the upload does not get blocked due to missing system specific configuration data in LMDB. Scheduled once, since ST710 SP5, deprecated in ST720.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>RMIGRATE_LANG_DEP_SAPSCRIPT report (Job SM:MIGRATE_LANG_DEP_SAPSCRIPT): This report optimizes the data for the document references to achieve an optimized access for the where-used list. Scheduled weekly, since ST400, deprecated in ST720.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>RNOTIFUPDATE01 report (Job REFRESH MESSAGE STATUS): This report refreshes the contents of Support Desk or Expert-on-Demand messages that have been processed by SAP. Recommendation: use the Implementation Guide to schedule a customer-specific variant for the SAP Solution Manager. Scheduled hourly, in ST400 only. Replaced by AI_CRM_IM_UPDATE_FROM_SAP report since ST710 SP1, also deprecated in ST720.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>RS_WBA_REFRESH_POWL_BUFFER report (Job SM:RS_WBA_REFRESH_POWL_BUFFER): This job ensures that the system lists in the work centers for the technical system administrator (System Administration, System Monitoring and System Landscape Management) are updated in the Solution Manager (that is, adjusted to changes in the system infrastructure). Scheduled hourly, since ST400 (defined in the old DSWPJOB job framework for ST400, activated in SMCONFIGJOBS new job framework since ST710 SP1), deprecated in ST720.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>RSDDSTAT_DATA_DELETE report (Job SM:REORG_BI_STATISTIC_TABLES): This report deletes old statistical data for BI statistics. Scheduled daily since ST710 SP5.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>RSGET_SMSY report (Job LANDSCAPE FETCH): This job gets system data for the Solution Manager system landscape by automatic data transfer from TMS/RFC or the System Landscape Directory (SLD). Depending on the synchronization status maintained for the systems, it also tries to synchronize information for the systems in SMSY with the SAP Marketplace. For more details see SAP note 1632036. Scheduled daily, since ST400, deprecated in ST720 (replaced by AI_SC_UPLOAD_SYSTEM_DATA report, SM:UPLOAD SYSTEM DATA job).</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>RWBA_RFC_WATCHER report (Job SM:RFC MONITORING): This report checks RFC connections using RFCPING or RFC_PING. Scheduled hourly, since ST400, deprecated in ST720.</li>\r\n<li>SAM_COMPLETE_OUT_REP_PERIOD (Job&#160;SM:SAM_FINALIZE_OUTAGES): In SAM (Service Availability Management), outages are created from Planned Downtimes or alerts. When these Planned Downtimes or alert span across reporting periods, the outage will not be finalized and will not be visible in the report generated for the month(reporting period). The report finalizes the outages which are spanning across different reporting periods so the reports are up to date.&#160;Scheduled monthly, since ST 720 SP14.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>SMO6_REORG2 report (Job SMO6_REORG2): This report reorganizes several kinds of data of CRM middleware such as BDOC messages and statistics, trace and key gen data... More details are available in SAP note 713173. Scheduled daily, since ST400.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>SMWP_BATCH report (Job SMWP_BATCH): This report updates the data displayed in the CRM middleware monitoring cockpit (SMWP transaction). Scheduled daily, since ST400.</li>\r\n</ul>\r\n</ul>\r\n<p>&#160;</p>\r\n<ul>\r\n<ul>\r\n<li>TIN_SEND_TASK_NOTIF report (Job&#160;SM:ITTM_SEND_NOTIF): This report periodically checks for new tasks, tasks which are due today and overdue tasks and sends e-mail notification to the assigned processors. Scheduled hourly, since ST710 SP12, from IT Task Management setup scenario.</li>\r\n</ul>\r\n</ul>\r\n<p>&#160;</p>\r\n<ul>\r\n<ul>\r\n<li>TP_SCHEDULER report (Job SM:ITTM_CREATE_TASK): This program reads task plans from IT Task Planning. It creates or changes the planned tasks which are due in the next two weeks according to the planned schedule. Scheduled daily, since ST710 SP12, from IT Task Management setup scenario.</li>\r\n</ul>\r\n</ul>\r\n<p>&#160;</p>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "Stefano GAGLIARDI (I025810)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON> (I068523)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000894279/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000894279/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000894279/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000894279/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000894279/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000894279/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000894279/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000894279/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000894279/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "946607", "RefComponent": "SV-SMG-OP", "RefTitle": "Automatic diagnosis in SAP Solution Manager", "RefUrl": "/notes/946607"}, {"RefNumber": "917896", "RefComponent": "SV-SMG-OP", "RefTitle": "MIGRATE_LANG_DEP_SAPSCRIPT: LOAD_PROGRAM_NOT_FOUND", "RefUrl": "/notes/917896"}, {"RefNumber": "897060", "RefComponent": "SV-SMG-OP", "RefTitle": "Update of support message for Issue Tracking", "RefUrl": "/notes/897060"}, {"RefNumber": "896280", "RefComponent": "SV-SMG-OP", "RefTitle": "Information about report DSWP_FILL_CCMS_ALERTS", "RefUrl": "/notes/896280"}, {"RefNumber": "743259", "RefComponent": "SV-SMG-OP", "RefTitle": "Background processing in SAP Solution Manager 3.X", "RefUrl": "/notes/743259"}, {"RefNumber": "2058991", "RefComponent": "SV-SMG-INS-CFG", "RefTitle": "\"No active nametab exists for PAT03_STARTPOINT error during solman_setup, schedule background jobs activity", "RefUrl": "/notes/2058991"}, {"RefNumber": "1658794", "RefComponent": "SV-SMG-OP", "RefTitle": "FAQ: Solutions: Sending Data to SAP", "RefUrl": "/notes/1658794"}, {"RefNumber": "1604572", "RefComponent": "SV-SMG-INS", "RefTitle": "OSS performance problems due to solman_setup background jobs", "RefUrl": "/notes/1604572"}, {"RefNumber": "1538950", "RefComponent": "SV-SMG-INS", "RefTitle": "Random scheduling of background jobs in SOLMAN_SETUP", "RefUrl": "/notes/1538950"}, {"RefNumber": "1407140", "RefComponent": "SV-SMG-OP", "RefTitle": "Solution Manager jobs 'SM:*' no longer run", "RefUrl": "/notes/1407140"}, {"RefNumber": "1366685", "RefComponent": "SV-SMG-OP", "RefTitle": "Correction JOB-SCHEDULER connection to OSS", "RefUrl": "/notes/1366685"}, {"RefNumber": "1325644", "RefComponent": "SV-SMG-OP", "RefTitle": "Job SM:EXEC SERVICES terminates with error SYSTEM_NO_ROLL", "RefUrl": "/notes/1325644"}, {"RefNumber": "1163920", "RefComponent": "SV-SMG-OP", "RefTitle": "SM:Scheduler: Improved display in job log", "RefUrl": "/notes/1163920"}, {"RefNumber": "1161294", "RefComponent": "SV-SMG", "RefTitle": "Name chg, SAP Solution Manager 4.0 to Solution Manager 7.0", "RefUrl": "/notes/1161294"}, {"RefNumber": "1111104", "RefComponent": "SV-SMG-OP", "RefTitle": "Language problems with solution delivered to AGS", "RefUrl": "/notes/1111104"}, {"RefNumber": "1084173", "RefComponent": "SV-SMG-ADM-CSA", "RefTitle": "CSA: Change of Background Job <PERSON>uling", "RefUrl": "/notes/1084173"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2881697", "RefComponent": "BC-CCM-PRN", "RefTitle": "The current ABAP program E2E_DCC_SELFMON had to be terminated", "RefUrl": "/notes/2881697 "}, {"RefNumber": "2626859", "RefComponent": "SV-SMG-SDD", "RefTitle": "How to remove the download data of service sessions", "RefUrl": "/notes/2626859 "}, {"RefNumber": "2513636", "RefComponent": "SV-SMG-MON-BPM-MON", "RefTitle": "Troubleshooting for SM:REORG BPMON ALERTS job failures in SAP Solution Manager 7.1", "RefUrl": "/notes/2513636 "}, {"RefNumber": "2441057", "RefComponent": "SV-SMG-INS-CFG", "RefTitle": "Program \"/TMWFLOW/RMO_STACKUPDATE\" not found", "RefUrl": "/notes/2441057 "}, {"RefNumber": "1143775", "RefComponent": "SV-SMG-SVD-SCU", "RefTitle": "SAP Service Content Update", "RefUrl": "/notes/1143775 "}, {"RefNumber": "569392", "RefComponent": "SV-SMG-SUP-INT", "RefTitle": "SAP Support Desk: Send attachments to SAP", "RefUrl": "/notes/569392 "}, {"RefNumber": "2075483", "RefComponent": "SV-SMG-OP", "RefTitle": "EarlyWatch Alert Archiving", "RefUrl": "/notes/2075483 "}, {"RefNumber": "1658794", "RefComponent": "SV-SMG-OP", "RefTitle": "FAQ: Solutions: Sending Data to SAP", "RefUrl": "/notes/1658794 "}, {"RefNumber": "1604572", "RefComponent": "SV-SMG-INS", "RefTitle": "OSS performance problems due to solman_setup background jobs", "RefUrl": "/notes/1604572 "}, {"RefNumber": "1538950", "RefComponent": "SV-SMG-INS", "RefTitle": "Random scheduling of background jobs in SOLMAN_SETUP", "RefUrl": "/notes/1538950 "}, {"RefNumber": "1084173", "RefComponent": "SV-SMG-ADM-CSA", "RefTitle": "CSA: Change of Background Job <PERSON>uling", "RefUrl": "/notes/1084173 "}, {"RefNumber": "1442573", "RefComponent": "SV-SMG-OP", "RefTitle": "EarlyWatch Alert: Latest Available Patch Level is empty", "RefUrl": "/notes/1442573 "}, {"RefNumber": "1407140", "RefComponent": "SV-SMG-OP", "RefTitle": "Solution Manager jobs 'SM:*' no longer run", "RefUrl": "/notes/1407140 "}, {"RefNumber": "1325644", "RefComponent": "SV-SMG-OP", "RefTitle": "Job SM:EXEC SERVICES terminates with error SYSTEM_NO_ROLL", "RefUrl": "/notes/1325644 "}, {"RefNumber": "1366685", "RefComponent": "SV-SMG-OP", "RefTitle": "Correction JOB-SCHEDULER connection to OSS", "RefUrl": "/notes/1366685 "}, {"RefNumber": "1163920", "RefComponent": "SV-SMG-OP", "RefTitle": "SM:Scheduler: Improved display in job log", "RefUrl": "/notes/1163920 "}, {"RefNumber": "1161294", "RefComponent": "SV-SMG", "RefTitle": "Name chg, SAP Solution Manager 4.0 to Solution Manager 7.0", "RefUrl": "/notes/1161294 "}, {"RefNumber": "1111104", "RefComponent": "SV-SMG-OP", "RefTitle": "Language problems with solution delivered to AGS", "RefUrl": "/notes/1111104 "}, {"RefNumber": "896280", "RefComponent": "SV-SMG-OP", "RefTitle": "Information about report DSWP_FILL_CCMS_ALERTS", "RefUrl": "/notes/896280 "}, {"RefNumber": "946607", "RefComponent": "SV-SMG-OP", "RefTitle": "Automatic diagnosis in SAP Solution Manager", "RefUrl": "/notes/946607 "}, {"RefNumber": "917896", "RefComponent": "SV-SMG-OP", "RefTitle": "MIGRATE_LANG_DEP_SAPSCRIPT: LOAD_PROGRAM_NOT_FOUND", "RefUrl": "/notes/917896 "}, {"RefNumber": "897060", "RefComponent": "SV-SMG-OP", "RefTitle": "Update of support message for Issue Tracking", "RefUrl": "/notes/897060 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "ST", "From": "400", "To": "400", "Subsequent": ""}, {"SoftwareComponent": "ST", "From": "710", "To": "710", "Subsequent": ""}, {"SoftwareComponent": "ST", "From": "720", "To": "720", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "ST 400", "SupportPackage": "SAPKITL425", "URL": "/supportpackage/SAPKITL425"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}