{"Request": {"Number": "553854", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 733, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000015286752017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000553854?language=E&token=68CC4DBC744F7366019BD7ED77702B19"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000553854", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000553854/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "553854"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 10}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Help for error analysis"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "11.06.2007"}, "SAPComponentKey": {"_label": "Component", "value": "BC-DB-ORA-DBA"}, "SAPComponentKeyText": {"_label": "Component", "value": "Database Administration"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Basis Components", "value": "BC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Database Interface, Database Platforms", "value": "BC-DB", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-DB*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Oracle", "value": "BC-DB-ORA", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-DB-ORA*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Database Administration", "value": "BC-DB-ORA-DBA", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-DB-ORA-DBA*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "553854 - Oracle: Problems with file size limit"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>An export or reorganization fails and the system issues the following message:<br /><br />EXP-00002: error in writing to export file<br />EXP-00002: error in writing to export fileerror closing export file<br />EXP-00000: Export terminated unsuccessfully<br />SAPDBA: Fatal export error!<br /><br />BR252E Function fwrite() failed for '&lt;exp_dump&gt;' at location<br />&#x00A0;&#x00A0;file_chop-6<br /><br />The process of creating or accessing a data file fails with errors such as:<br /><br />error in creating database file '&lt;file&gt;'<br />OSD-04005: SetFilePointer() failure, unable to read from file<br />O/S-Error: (OS 131) An attempt was made to move the file<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;pointer before the beginning of the file<br />ORA-01157: cannot identify/lock data file<br />ORA-01119: error in creating database file '&lt;file&gt;'<br />ORA-19510: failed to set size of blocks for file '&lt;file&gt;'<br />ORA-27037: unable to obtain file status<br />ORA-27039: create file failed, file size limit reached<br />ORA-27044: unable to write the header block of file<br />ORA-27092: skgfofi: size of file exceeds file size limit of the process<br />&lt;unix&gt; Error: 22: Invalid argument<br />&lt;unix&gt; Error: 27: File too large<br />&lt;unix&gt; Error: 75: Value too large for defined data type<br /><br />Oracle causes the following type of errors during production operation:<br /><br />KCF: write/open error block=0x40209 online=1<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;file=&lt;file_nr&gt; &lt;file&gt;<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;error=27072 txt: '&lt;unix&gt; Error: 27: File too large<br />Additional information: 262665'<br />Automatic datafile offline due to write error on<br />file &lt;file_nr&gt;: &lt;file&gt;<br /><br />The following are possible follow-on errors:<br /><br />ORA-00376: file &lt;file_nr&gt; cannot be read at this time<br />ORA-01110: data file &lt;file_nr&gt;: '&lt;file&gt;'<br />ORACLE Instance C11 (pid = 6) - Error 376 encountered while recovering<br />transaction (12, 61) on object 24261.<br />BR252E Function fseek() failed for<br />'/oracle/&lt;sid&gt;/sapbackup/...' at location BrSparseCreate-8<br /></p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>sapdba, brbackup, brarchive, brrestore</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>Various factors limit the maximum possible file size. A termination occurs if the size of a file increases above this limit (for example, an export dump file), if a larger file is created from the outset (for example, a new data file), or if a file that is too large is accessed in read mode.<br /><br />To determine whether this is actually a file size problem, you should first check to see whether you can reproduce the error and, in the case of an export, determine the size of the file when the termination occurs (for example, for 2 GB).<br /><br />This error may be caused by the following:</p> <OL>1. Predefined limit on the operating system</OL> <OL>2. Limit on Oracle (server)</OL> <OL>3. Limit on Oracle (client)</OL> <OL>4. UNIX: User-specific limit</OL> <OL>5. Limit on Oracle (listener)</OL> <OL>6. HP-UX: Large files are not activated for the file system.</OL> <OL>7. OS scheduler with a user limit that has been set<br /></OL><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><OL>1. If a predefined limit exists on the operating system (for example, 2 GB), you must use smaller files only. You can do this as follows:</OL> <UL><LI>Data file: Specify a file size that is smaller than the limit on the operating system.</LI></UL> <UL><LI>Export dump file: Set the max_file_size sapdba parameter to the operating system limit as described in Note 72105.</LI></UL><p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;If you have questions about the maximum file size permitted at operating system level, contact your hardware partner. Notes 129439 and 20823 may also provide you with useful information about individual operating systems. <OL>2. Older Oracle releases partially limited the maximum possible size of data files. For more information, see Note 129439. Either limit the size of the data files or change to a newer Oracle release.</OL><p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Up to and including Oracle 8.1. 7, Linux allows file sizes of up to 2 GB only. File sizes greater than 2 GB are possible as of Oracle 9 only. <OL>3. Sapdba and the BR tools are linked to special Oracle client versions (for example, mostly Oracle 8.0.x for the 4.x versions of the tools) that may differ from the actual Oracle version. To make sure that this does not cause any unnecessary file size restrictions, you should install the 6.x patches for the tools as described in Note 12741. These are linked by default to Oracle 8.1.7 and higher, whereby the file size restrictions no longer exist.</OL> <OL>4. On UNIX, you can use \"ulimit\" to define a user-specific limit for the file size. If the limit that is set is too restrictive, the aforementioned terminations may occur. You can use \"ulimit\" to check how the limit for ora&lt;sid&gt; and &lt;sid&gt;adm is set. The \"unlimited\" value is useful here. Also note that the \"ulimit\" is not output in bytes. Instead, it is specified in operating system blocks. Therefore, in the case of 512-byte blocks, the output \"4194303\" indicates that the limit is 2 GB.</OL> <OL>5. Check the user under which the listener process was started and make sure that this user has sufficient user limitations (see above).</OL> <OL>6. On HP-UX, you can only create files of more than 2 GB if you have activated the large files option for the file system. To check whether this large files option is active for the file system in question, use:<br /><br />&#x00A0;&#x00A0;fstyp -V &lt;logical_volume&gt;</OL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;If \"f_flag=16\" appears in the output, then the large files option is active. If not, you can activate it using<br /><br />&#x00A0;&#x00A0;/usr/sbin/fsadm -F vxfs -o largefiles &lt;device&gt; <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;<OL>7. If you execute a reorganization in the background rather than immediately, sapdba transfers this task to an operating system scheduler such as CRON or AT. These can now be configured in such a way that a separate user limit is activated for the file size (see Note 387723). Therefore, check the generated scheduler scripts for \"ulimit\" entries and correct the configuration of the scheduler if necessary.</OL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;The same applies if you use a scheduler to start Oracle. <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;In the case of HP-UX, a file called \". proto\" controls the ulimit value for AT and CRON. Depending on the configuration, this is located in a directory such as /var/spool/cron/atjobs or /var/adm/cron. This file mostly looks as follows:<br /><br /># @(#) $Revision: 27.1 $<br />cd $d<br />ulimit $l<br />umask $m <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Here, change the ulimit entry to \"ulimit unlimited\" to permanently avoid the user restriction in the scheduler script.</div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "BC-DB-ORA (Oracle)"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D030484)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON> (D021978)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "J", "TranslationLanguage": "日本語", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000553854/J"}, {"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000553854/D"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000553854/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000553854/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000553854/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000553854/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000553854/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000553854/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000553854/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "72105", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "SAPDBA: Profile parameter max_file_size", "RefUrl": "/notes/72105"}, {"RefNumber": "592393", "RefComponent": "BC-DB-ORA", "RefTitle": "FAQ: Oracle", "RefUrl": "/notes/592393"}, {"RefNumber": "387723", "RefComponent": "BC-DB-ORA", "RefTitle": "Incorrect ulimit values with AT command", "RefUrl": "/notes/387723"}, {"RefNumber": "20823", "RefComponent": "BC-CTS", "RefTitle": "R3trans termin.: Cannot write to datafile any more", "RefUrl": "/notes/20823"}, {"RefNumber": "129439", "RefComponent": "BC-DB-ORA", "RefTitle": "Maximum file sizes with Oracle", "RefUrl": "/notes/129439"}, {"RefNumber": "12741", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Current versions of BR*Tools", "RefUrl": "/notes/12741"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "12741", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Current versions of BR*Tools", "RefUrl": "/notes/12741 "}, {"RefNumber": "592393", "RefComponent": "BC-DB-ORA", "RefTitle": "FAQ: Oracle", "RefUrl": "/notes/592393 "}, {"RefNumber": "129439", "RefComponent": "BC-DB-ORA", "RefTitle": "Maximum file sizes with Oracle", "RefUrl": "/notes/129439 "}, {"RefNumber": "20823", "RefComponent": "BC-CTS", "RefTitle": "R3trans termin.: Cannot write to datafile any more", "RefUrl": "/notes/20823 "}, {"RefNumber": "387723", "RefComponent": "BC-DB-ORA", "RefTitle": "Incorrect ulimit values with AT command", "RefUrl": "/notes/387723 "}, {"RefNumber": "72105", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "SAPDBA: Profile parameter max_file_size", "RefUrl": "/notes/72105 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": []}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}