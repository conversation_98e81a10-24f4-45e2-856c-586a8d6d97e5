{"Request": {"Number": "2591575", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 250, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000001166582018"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0002591575?language=E&token=045EAB77F31E642615D104FC2FD66F71"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0002591575", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0002591575/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "2591575"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 20}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Consulting"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "25.08.2022"}, "SAPComponentKey": {"_label": "Component", "value": "BC-DB-ORA"}, "SAPComponentKeyText": {"_label": "Component", "value": "Oracle"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Basis Components", "value": "BC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Database Interface, Database Platforms", "value": "BC-DB", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-DB*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Oracle", "value": "BC-DB-ORA", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-DB-ORA*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "2591575 - Using Oracle Transparent Data Encryption (TDE) with SAP NetWeaver"}, "LongText": {"_label": "Description", "value": "<h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p><strong>Oracle Transparent Data Encryption (TDE)</strong></p>\r\n<p>This SAP note describes how you can use Oracle Transparent Data Encryption (TDE) in an SAP NetWeaver environment.</p>\r\n<p>News:</p>\r\n<ul>\r\n<li>2022-Aug-25 - new TDE parameter TABLESPACE_ENCRYPTION (19.16 or higher)</li>\r\n<li>2021-May-17 - Database Configuration with OKV</li>\r\n<li>2021-May-10 - No SAP Support for TDE configuration with Hardware Security Module (HSM)</li>\r\n<li>2021-May-07 - No SAP Support for TDE configuration with Oracle Key Vault (OKV)</li>\r\n<li>2020-Apr-17 - Updated TDE administration commands</li>\r\n<li>2020-Feb-06 - Added TDE administration commands (section Appendix)</li>\r\n<li>2020-Feb-05 - Updated for 19c</li>\r\n<li>2020-Feb-05 - Added TDE default configuration for SAP NetWeaver</li>\r\n<li>2020-Feb-05 - Added steps how to migrate from ENCRYPTION_WALLET_LOCATION to WALLET_ROOT configuration</li>\r\n</ul>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>Oracle Transparent Data Encryption<br />Oracle Database Security, Oracle Software Keystore, Oracle TDE Encryption Wallet<br />WALLET_ROOT, TDE_CONFIGURATION, ENCRYPT_NEW_TABLESPACES<br />V$ENCRYPTION_WALLET, V$ENCRYPTION_KEYS, V$ENCRYPTED_TABLESPACES, V$DATABASE_KEY_INFO<br />ADMINISTER KEY MANAGEMENT (AKM)</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>This SAP note is valid for Oracle Database 12c Release 1&#160;or higher. For Oracle Database 10g Release 2 (10.2) or Oracle Database 11g Release 2 (11.2), see SAP Note <a target=\"_blank\" href=\"/notes/974876\">974876</a>.</p>\r\n<p><span style=\"text-decoration: underline;\">Reference</span></p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td><span style=\"text-decoration: underline;\">Oracle Database&#160;Security</span></td>\r\n<td><span style=\"text-decoration: underline;\">Advanced Security Guide</span></td>\r\n<td><span style=\"text-decoration: underline;\">Introduction to&#160;TDE</span></td>\r\n</tr>\r\n<tr>\r\n<td><a target=\"_blank\" href=\"https://docs.oracle.com/en/database/oracle/oracle-database/19/security.html\">Security 19c</a></td>\r\n<td><a target=\"_blank\" href=\"https://docs.oracle.com/en/database/oracle/oracle-database/19/asoag/index.html\">Advanced Security Guide 19c</a></td>\r\n<td><a target=\"_blank\" href=\"https://docs.oracle.com/en/database/oracle/oracle-database/19/asoag/asopart1.html\">Using TDE 19c</a></td>\r\n</tr>\r\n<tr>\r\n<td><a target=\"_blank\" href=\"https://docs.oracle.com/en/database/oracle/oracle-database/18/security.html\">Security 18c</a></td>\r\n<td><a target=\"_blank\" href=\"https://docs.oracle.com/en/database/oracle/oracle-database/18/asoag/index.html\">Advanced Security Guide 18c</a></td>\r\n<td><a target=\"_blank\" href=\"https://docs.oracle.com/en/database/oracle/oracle-database/18/asoag/asopart1.html\">Using TDE 18c</a></td>\r\n</tr>\r\n<tr>\r\n<td><a target=\"_blank\" href=\"https://docs.oracle.com/en/database/oracle/oracle-database/12.2/security.html\">Security 12.2</a></td>\r\n<td><a target=\"_blank\" href=\"https://docs.oracle.com/en/database/oracle/oracle-database/12.2/asoag/index.html\">Advanced Security Guide 12.2</a></td>\r\n<td><a target=\"_blank\" href=\"https://docs.oracle.com/en/database/oracle/oracle-database/12.2/asoag/asopart1.html\">Using TDE 12.2</a></td>\r\n</tr>\r\n<tr>\r\n<td><a target=\"_blank\" href=\"https://docs.oracle.com/database/121/nav/portal_25.htm\">Security 12.1</a></td>\r\n<td><a target=\"_blank\" href=\"https://docs.oracle.com/database/121/ASOAG/toc.htm\">Advanced Security Guide 12.1</a></td>\r\n<td><a target=\"_blank\" href=\"https://docs.oracle.com/database/121/ASOAG/asopart1.htm\">Using TDE 12.1</a></td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><span style=\"text-decoration: underline;\">Related SAP Notes</span></p>\r\n<ul>\r\n<li><a target=\"_blank\" href=\"/notes/974876\">974876</a>&#160;- TDE Support for SAP NetWeaver with Oracle 10g / 11g</li>\r\n<li><a target=\"_blank\" href=\"/notes/2485122\">2485122</a>&#160;- TDE Support in SWPM</li>\r\n<li><a target=\"_blank\" href=\"/notes/2799991\">2799991</a> - TDE Tablespace Conversions</li>\r\n</ul>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p>&#160;<a target=\"_self\" href=\"#TDE_INTRODUCTION\"><span style=\"color: black;\"><br /></span></a></p>\r\n<p><strong><a target=\"_blank\" name=\"TDE_INTRODUCTION\"></a>&#65279;Oracle Transparent Data Encryption</strong></p>\r\n<p><span style=\"text-decoration-line: underline;\">Introduction</span></p>\r\n<p>Oracle Transparent Data Encryption (TDE) enables you to encrypt sensitive data&#160;stored in tables and tablespaces. After the data is encrypted, this data is <span style=\"text-decoration: underline;\">transparently decrypted</span> for authorized users or applications when they access this data. TDE helps protect data stored on media (also called data at rest) in the event that the storage media or data file is stolen.</p>\r\n<p>To prevent unauthorized decryption, Oracle stores the&#160;encryption keys&#160;in a security module external to the database, called a <span style=\"text-decoration: underline;\">keystore</span>.</p>\r\n<p><span style=\"text-decoration: underline;\">Benefits</span></p>\r\n<ul>\r\n<li>Sensitive data is encrypted in the data files&#160;to prevent&#160;unauthorized access to this data through OS layer using OS tools (e.g. hex editor). Sensitive data is encrypted and therefore safe in case that storage media, data files or backups are stolen.</li>\r\n<li>Data that is stored in the database is transparently encrypted and decrypted. The Oracle database manages encryption and decryption transparently for the application.</li>\r\n<li>SAP supports TDE for SAP NetWeaver based applictions to protect sensitive SAP application data.</li>\r\n<li>TDE works transparently for the SAP application. No configuration changes are required..</li>\r\n</ul>\r\n<p><span style=\"text-decoration-line: underline;\">TDE Architecture and TDE Types</span></p>\r\n<p>Reference:</p>\r\n<ul>\r\n<li>Advanced Security Guide, Types and Components of Transparent Data Encryption</li>\r\n<li>SAP Note <a target=\"_blank\" href=\"/notes/974876\">974876</a></li>\r\n</ul>\r\n<p>You can encrypt sensitive data in the Oracle database at column level (-&gt; <span style=\"text-decoration: underline;\"><a target=\"_self\" href=\"#TDE_COLUMN_ENCRYPTION\">TDE Column Encryption</a></span>) or at tablespace level (-&gt; <span style=\"text-decoration: underline;\"><a target=\"_self\" href=\"#TDE_TABLESPACE_ENCRYPTION\">TDE Tablespace Encryption</a></span>).</p>\r\n<p style=\"padding-left: 30px;\">Note: TDE Column Encryption is not supported for SAP NetWeaver</p>\r\n<p>Both TDE column encryption and TDE tablespace encryption use a two-tiered key-based architecture. Unauthorized users cannot read the data from storage and back up media unless they have the TDE master encryption key to decrypt it. The TDE master encryption key is stored in an external security module outside of the database, which can be an Oracle software keystore (TDE wallet) or hardware keystore (HSM, Hardware Security Module). This design&#160;makes it possible to separate&#160;database administration tasks from and security administration tasks. In earelier releases, the 'Oracle software keystore' was&#160;named 'TDE Encryption Wallet' (see SAP Note <a target=\"_blank\" href=\"/notes/974876\">974876</a>).</p>\r\n<p><span style=\"text-decoration: underline;\"><a target=\"_blank\" name=\"TDE_COLUMN_ENCRYPTION\"></a>&#65279;TDE Column Encryption</span></p>\r\n<p>Transparent Data Encryption (TDE) column encryption protects confidential data that is stored in table columns.</p>\r\n<p style=\"padding-left: 30px;\">Note: <strong>TDE column encryption is not supported for SAP</strong>. If you still use TDE column encryption in your database, all tables with encrypted columns must be migrated into&#160;encrypted tablespaces. The procedure is described below.</p>\r\n<p>TDE column encryption was introduced in Oracle Database 10g&#160;and certified for SAP NetWeaver with Oracle Database 10.2.0.5.&#160;You can use it&#160;for SAP NetWeaver based systems with Oracle Database 10g and 11g, but not with Oracle 12c or higher. For more information, see '<a target=\"_blank\" href=\"https://docs.oracle.com/en/database/oracle/oracle-database/12.2/asoag/introduction-to-transparent-data-encryption.html\">Introduction to Transparent Data Encryption</a>'&#160;in the Advanced Security Guide&#160;and SAP Note <a target=\"_blank\" href=\"/notes/974876\">974876</a>.</p>\r\n<p>There are certain technical&#160;restrictions with TDE column encryption:</p>\r\n<ul>\r\n<li>TDE column encryption has&#160;certain technical restrictions (data types, index columns, storage overhead, performance).</li>\r\n<li>TDE column encryption requires that you identify which table columns must be encrypted. The SAP NetWeaver database schema contains thousands of tables, depending on SAP release, patch level and installed SAP product. Managing such a&#160;list of tables and columns&#160;that are candidates for encryption is not possible. Even if you identify all columns, this list is only valid for a given SAP product, SAP release and patch level.&#160;</li>\r\n</ul>\r\n<p><span style=\"text-decoration: underline;\"><a target=\"_blank\" name=\"TDE_TABLESPACE_ENCRYPTION\"></a>&#65279;TDE Tablespace Encryption</span></p>\r\n<p>Transparent Data Encryption (TDE) tablespace encryption enables you to encrypt an entire tablespace.</p>\r\n<p>TDE tablespace encryption encrypts all of the data stored in an encrypted tablespace including its redo data. TDE tablespace encryption does not encrypt data that is stored outside of the tablespace. All of the data in an encrypted tablespace is stored in encrypted format on the disk. A database user or application does not need to know if the data in a particular table is encrypted on the disk. In the event that the data files on a disk or backup media is stolen, the data is not compromised.</p>\r\n<p>Note:</p>\r\n<ul>\r\n<li>The encrypted data is protected during operations such as <code class=\"codeph\">JOIN</code> and <code class=\"codeph\">SORT</code>. This means that the data is safe when it is moved to temporary tablespaces. Data in undo and redo logs is also protected.</li>\r\n<li>TDE tablespace encryption also allows index range scans on data in encrypted tablespaces. This is not possible with TDE column encryption.</li>\r\n</ul>\r\n<p><span style=\"text-decoration: underline;\">TDE Keystores and Key Management Framework</span></p>\r\n<p>Oracle Database provides a key management framework for Transparent Data Encryption that stores and manages keys and credentials.</p>\r\n<ul>\r\n<li>Keystore: The keystore securely stores the TDE master encryption keys.</li>\r\n<li>Management Framework: The management framework manages keystore and key operations.</li>\r\n</ul>\r\n<p>Benefits of the TDE key management framework are:</p>\r\n<ul>\r\n<li>The design allows separation of duty between the database administrator and the security administrator who manages the keys.</li>\r\n<li>You can grant the <code class=\"codeph\">ADMINISTER KEY MANAGEMENT</code> or <code class=\"codeph\">SYSKM</code> privilege to users who are responsible for managing the keystore and key operations.</li>\r\n<li>You must make a backup of the keystore for all of the critical keystore operations.</li>\r\n<li>You must&#160;make a backup of the TDE master encryption key before you reset or rekey this TDE master encryption key.</li>\r\n<li>Allows to store the keystore on an ASM file system.</li>\r\n</ul>\r\n<p>Note: all commands&#160;of the&#160;TDE key managment framework that manage the keystore and/or TDE master key are SQL commands that can be run from a SQL*Plus command line (or from SAP BR*Tools / BRSPACE program).</p>\r\n<p><span style=\"text-decoration: underline;\">Software Keystores and External Key Managers</span></p>\r\n<p>Oracle supports the following software keystore types:</p>\r\n<ul>\r\n<li>\"password-based software keystore\" are protected by a password <em>&lt;keystore_password&gt;</em></li>\r\n<ul>\r\n<li><em>name: ewallet.p12</em></li>\r\n</ul>\r\n<li>\"Auto-login software keystores\" are protected by a system-generated password, and do not need to be explicitly opened by a security administrator.</li>\r\n<ul>\r\n<li>name: cwallet.sso</li>\r\n</ul>\r\n<li>\"Local auto-login software keystores\" are auto-login software keystores that are local to the computer on which they are created.</li>\r\n<ul>\r\n<li>name: cwallet.sso</li>\r\n<li>Local auto-login keystores cannot be opened on any computer other than the one on which they are created.</li>\r\n<li>Local Auto-login software keystores are not supported for SAP NetWeaver.</li>\r\n</ul>\r\n</ul>\r\n<p>The file name of the password-based software keystore is 'ewallet.p12'. The file name of the (local) auto-login software keystore is 'cwallet.sso'. Software keystores can be stored on ASM disk groups or in a regular file system.</p>\r\n<p>Oracle supports the following external key managers:</p>\r\n<ul>\r\n<li>Hardware Security Module (HSM)<br />Hardware security modules are third-party physical devices that provide secure storage for encryption keys, in external keystores.</li>\r\n<li>Oracle Key Vault (OKV)<br />Oracle Key Vault is a full-stack, security-hardened software appliance built to centralize the management of TDE keystores.</li>\r\n</ul>\r\n<p><span style=\"text-decoration: underline;\">Support for Hardware Security Modules (HSM)</span></p>\r\n<p>Use of HSM is allowed, but no support from SAP (installation, configuration, administration, issues). For more information, see SAP Note&#160;<a target=\"_blank\" href=\"/notes/105047\">105047</a><a target=\"_blank\" href=\"/notes/%value\">.</a></p>\r\n<p><span style=\"text-decoration: underline;\">Support for Oracle Key Vault (OKV)</span></p>\r\n<p>Use of OKV is allowed, but no support from SAP (installation, configuration, administration, issues). For more information, see SAP Note&#160;<a target=\"_blank\" href=\"/notes/105047\">105047</a><a target=\"_blank\" href=\"/notes/%value\">.</a></p>\r\n<p>Reference: https://docs.oracle.com/en/database/oracle/key-vault/index.html</p>\r\n<p>Oracle Key Vault is a full-stack, security-hardened software appliance built to centralize the management of TDE keystores.</p>\r\n<ul>\r\n<li>You can configure Oracle Key Vault as part of the TDE implementation. This enables you to centrally manage TDE keystores (called TDE wallets in Oracle Key Vault) in your enterprise.</li>\r\n<li>SAP tools like SAP BR*Tools or SWPM do not support OKV.</li>\r\n<li>You can use OKV in SAP NetWeaver environments, but without any support from SAP (installation, configuration, administration, issues). For more information, see SAP Note&#160;<a target=\"_blank\" href=\"/notes/105047\">105047</a>.</li>\r\n</ul>\r\n<p><span style=\"text-decoration-line: underline;\">TDE Encryption Algorithms and Integrity Algorithms</span></p>\r\n<p>For TDE tablespace encryption and database encryption, the default is to use the Advanced Encryption Standard with a 128-bit length cipher key (AES128). In addition, salt is always added to plaintext before encryption.</p>\r\n<p>Note: There is no recommendation to use a specific&#160;algorithm to encrypt data in&#160;your SAP database.</p>\r\n<p>For more information, see '<a target=\"_blank\" href=\"https://docs.oracle.com/en/database/oracle/oracle-database/12.2/asoag/introduction-to-transparent-data-encryption.html\">Introduction to Transparent Data Encryption</a>'&#160;in the Advanced Security Guide.</p>\r\n<p><span style=\"text-decoration: underline;\">TDE Dictionary Views</span></p>\r\n<p>For information about&#160;TDE configuration status, you can query the following Oracle dictionary views:</p>\r\n<ul>\r\n<li>V$ENCRYPTED_TABLESPACES</li>\r\n<li>V$DATABASE_KEY_INFO</li>\r\n<li>V$ENCRYPTION_KEYS</li>\r\n<li>V$ENCRYPTION_WALLET</li>\r\n</ul>\r\n<p><span style=\"text-decoration-line: underline;\">TDE and Oracle Database Vault</span></p>\r\n<p>While Oracle Transparent Data Encryption encrypts 'data at rest' in data files and backup files, high-privileged database administrators can access sensitive application data.&#160;You can&#160;protect sensitive data from being&#160;unauthorized access using Oracle Database Vault. For more information, see SAP Note <a target=\"_blank\" href=\"/notes/2218115\">2218115</a>.</p>\r\n<p><span style=\"text-decoration: underline;\">TDE Administration System Privileges (SYSDBA / SYSKM)</span></p>\r\n<p>You must be granted the ADMINISTER KEY MANAGEMENT system privilege to configure TDE. And you must be granted the SYSKM administrative privilege (which includes the ADMINISTER KEY MANAGEMENT system privilege) to open the keystore when the database is mounted.</p>\r\n<p>Note:</p>\r\n<ul>\r\n<li>By default SAP does not&#160;configure an additional dedicated TDE Security administor &lt;tde_sec_admin&gt; OS account for your SAP database.</li>\r\n<li>You can configure TDE using the&#160;SYSDBA administrative privilege of your normal SAP database administrator OS account (e.g ora&lt;dbsid&gt;).</li>\r\n<li>You can create a dedicated OS account - e.g orasec&lt;dbsid&gt; or oratde&lt;dbsid&gt; - for TDE administration.</li>\r\n</ul>\r\n<p>&#160;</p>\r\n<p><a target=\"_blank\" name=\"TDE_NEW_FEATURES\"></a><strong>&#65279;TDE New Features</strong></p>\r\n<p><span style=\"text-decoration: underline;\">New in 19c</span></p>\r\n<p>Oracle supports TDE Online Conversion with Auto-Renaming of datafiles as of Oracle Database 19c.. You no longer need to use FILE_NAME_CONVERT clause for this operation (as in 18c). For more information, see <a target=\"_blank\" href=\"https://docs.oracle.com/en/database/oracle/oracle-database/19/asoag/release-changes.html#GUID-********-D07E-4290-871D-8BBF20C3DA83\">https://docs.oracle.com/en/database/oracle/oracle-database/19/asoag/release-changes.html#GUID-********-D07E-4290-871D-8BBF20C3DA83</a>.</p>\r\n<p>TDE Conversion of tablespaces is&#160;supported for SAP NetWeaver based installations for Oracle database 19c or higher. For more information, see SAP Note <a target=\"_blank\" href=\"/notes/2799991\">2799991</a>.</p>\r\n\r\n<p><span style=\"text-decoration: underline;\">New in 12.2</span></p>\r\n<p>Oracle&#160;supports&#160;encryption of Oracle-managed/Oracle-supplied tablespaces (SYSTEM, SYSAUX, TEMP, UNDO) as of Oracle Database 12c Release 2. For more information, see <a target=\"_blank\" href=\"https://docs.oracle.com/en/database/oracle/oracle-database/12.2/asoag/release-changes.html#GUID-327E436F-A384-4301-9D7E-762CCE108915\">https://docs.oracle.com/en/database/oracle/oracle-database/12.2/asoag/release-changes.html</a>.</p>\r\n<ul>\r\n<li>Encryption of Oracle-supplied tablespaces SYSTEM, SYSAUX, PSAPTEMP and PSAPUNDO is supported for SAP NetWeaver systems as of Oracle Database 19c.</li>\r\n<li>Encryption of Oracle-managed tablespaces is not supported for SAP NetWeaver for Oracle database versions &lt; 19c.</li>\r\n<li>For more information, see SAP Note <a target=\"_blank\" href=\"/notes/2799991\">2799991</a>.</li>\r\n<li>Reference: Oracle Support Document <a target=\"_blank\" href=\"https://support.oracle.com/epmos/faces/DocumentDisplay?id=2393734.1\">2393734.1</a></li>\r\n</ul>\r\n\r\n<p><span style=\"text-decoration: underline;\">New in 12.1</span></p>\r\n<ul>\r\n<li>New key management SQL statements (ADMINISTER KEY MANAGEMENT)</li>\r\n<li>New administrative privilege SYSKM</li>\r\n<li>Support for storing TDE keystores directly on ASM disk groups</li>\r\n</ul>\r\n<p>For more information, see <a target=\"_blank\" href=\"https://docs.oracle.com/database/121/NEWFT/chapter12101.htm#NEWFT397\">Oracle Database 12c Release 1 (12.1.0.1) New Features</a>.</p>\r\n<p><strong><a target=\"_blank\" name=\"TDE_PREREQUISITES\"></a>&#65279;TDE Prerequisites&#160;</strong></p>\r\n<p><span style=\"text-decoration: underline;\">Oracle License / ASO License</span></p>\r\n<p>Transparent Data Encryption (TDE)&#160;requires&#160;an Advanced Security Option&#160;license (ASO). For more information, see SAP Note <a target=\"_blank\" href=\"/notes/740897\">740897</a>.</p>\r\n<p>For&#160;additional information, see the \"Oracle Database Licensing Information User Manual\" of your Oracle database release.</p>\r\n<p><strong><a target=\"_blank\" name=\"TDE_RECOMMENDATIONS\"></a>&#65279;TDE Recommendations for SAP NetWeaver</strong></p>\r\n<p><span style=\"text-decoration: underline;\">Migrate from TDE Column Encryption&#160;to TDE&#160;Tablespace Encryption</span></p>\r\n<p>TDE column encryption is not supported&#160;for in SAP NetWeaver environments for SAP&#160;databases of Oracle Database 12c or higher.</p>\r\n<p>For Oracle Database 11g or higher, you should use TDE Tablespace Encryption instead of TDE column encryption. If you still use TDE column encryption, you should migrate to TDE tablespace encryption. The procedure is:</p>\r\n<ol>\r\n<li>Create an encrypted tablespace.</li>\r\n<li>Migrate all tables with encrypted columns into this new tablespace.</li>\r\n<li>Decrypt all encrypted columns.</li>\r\n</ol>\r\n<p><span style=\"text-decoration: underline;\">Migrate from SQLNET.ENCRYPTION_WALLET_LOCATION to WALLET_ROOT</span></p>\r\n<p>SQLNET.ORA parameter ENCRYPTION_WALLET_LOCATION is deprecated (see Oracle Database 19c Advanced Security Guide,&#160;'<a target=\"_blank\" href=\"https://docs.oracle.com/en/database/oracle/oracle-database/19/asoag/using-sqlnet-ora-to-configure-tde-keystores.html\">Using sqlnet.ora to Configure Transparent Data Encryption Keystores</a>'). SAP systems that use this parameter for TDE&#160;must migrate to WALLET_ROOT. This recommendation applies to SAP NetWeaver systems with Oracle Database 12.2 or higher.</p>\r\n<p>Steps are:</p>\r\n<ol>\r\n<li>Backup file &lt;ORACLE_HOME&gt;/network/admin/sqlnet.ora</li>\r\n<li>Check&#160;ENCRYPTION_WALLET_LOCATION in SQLNET.ORA</li>\r\n<li>Check \"SAP Default Configuration for TDE\" below for SAP&#160;standard location of software keystore.</li>\r\n<li>Configure initialization parameter WALLET_ROOT: SQL&gt; ALTER SYSTEM SET ... (or via brtools)</li>\r\n<li>Configure initialization parameter TDE_CONFIGURATION: SQL&gt; ALTER SYSTEM SET ... (or via brtools)</li>\r\n<li>Backup software keystore (ewallet.p12, cwallet.sso) in old location</li>\r\n<li>Move/Copy software keystore (ewallet.p12, cwallet.sso) from old location to new location (*)</li>\r\n<li>Restart database</li>\r\n<li>Verify&#160;parameter settings and check status of software keystore</li>\r\n<li>Remove ENCRYPTION_WALLET_LOCATION&#160;from SQLNET.ORA</li>\r\n</ol>\r\n<p>(*) Regarding step 7:</p>\r\n<p>If you migrate from SQLNET.ORA ENCRYPTION_WALLET_LOCATION=/oracle/&lt;DBSID&gt;/orawallet to WALLET_ROOT=/oracle/&lt;DBSID&gt;/orawallet, don't forget to create subdirectory 'tde'.</p>\r\n<p style=\"padding-left: 30px;\">OS&gt; mkdir -p&#160;/oracle/&lt;DBSID&gt;/orawallet/tde<br />OS&gt; cp /oracle/&lt;DBSID&gt;/orawallet/ewallet.p12&#160;/oracle/&lt;DBSID&gt;/orawallet/tde<br />OS&gt; cp&#160;/oracle/&lt;DBSID&gt;/orawallet/cwallet.sso /oracle/&lt;DBSID&gt;/orawallet/tde</p>\r\n<p><span style=\"text-decoration: underline;\">Setting Database Parameter 'WALLET_ROOT' and 'TDE_CONFIGURATION'</span></p>\r\n<p>Before you can convert your existing database to TDE, you must set database parameter WALLET_ROOT and TDE_CONFIGURATION.</p>\r\n<p>Steps are:</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td><span style=\"text-decoration: underline;\">Step</span></td>\r\n<td><span style=\"text-decoration: underline;\">brspace Command</span></td>\r\n<td><span style=\"text-decoration: underline;\">SQL Command</span></td>\r\n</tr>\r\n<tr>\r\n<td>Configure wallet_root</td>\r\n<td>brspace -f dbparam -a change -p wallet_root</td>\r\n<td>SQL&gt; alter system set wallet_root = '&lt;wallet_location&gt;' scope = spfile;</td>\r\n</tr>\r\n<tr>\r\n<td>restart database</td>\r\n<td>brspace -f dbstart -force</td>\r\n<td>\r\n<p>sqlplus (SI):<br />SQL&gt; conn / as sysdba<br />SQL&gt; shutdown immediate<br />SQL&gt; startup</p>\r\n<p>srvctl (RAC/ASM):<br />OS&gt; srvctl stop database -db &lt;dbsid&gt; -stopoption immediate<br />OS&gt; srvctl start database -db &lt;dbsid&gt;</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>Configure tde_configuration</td>\r\n<td>brspace -f dbparam -a change -p tde_configuration</td>\r\n<td>SQL&gt; alter system set tde_configuration = 'KEYSTORE_CONFIGURATION=FILE' scope = both;</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p>&#160;</p>\r\n<p><strong><a target=\"_blank\" name=\"TDE_CONFIGURATION\"></a>&#65279;TDE Configuration Standards for SAP NetWeaver</strong></p>\r\n<p><span style=\"text-decoration: underline;\"><span style=\"text-decoration: underline;\">Software Keystore 10g and 11g</span></span></p>\r\n<p>In previous releases (10.2 and&#160;11.2, see SAP Note <a target=\"_blank\" href=\"/notes/974876\">974876</a>),&#160;the TDE encryption wallet&#160;of an SAP database was stored inside the Oracle home.</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td><span style=\"text-decoration: underline;\">Scenario</span></td>\r\n<td><span style=\"text-decoration: underline;\"><span style=\"text-decoration: underline;\">&lt;wallet_location&gt;</span></span></td>\r\n<td><span style=\"text-decoration: underline;\"><span style=\"text-decoration: underline;\">Example</span></span></td>\r\n</tr>\r\n<tr>\r\n<td>Unix</td>\r\n<td>\r\n<p>&lt;ORACLE_HOME&gt;/dbs</p>\r\n</td>\r\n<td>\r\n<p>ENCRYPTION_WALLET_LOCATION=(SOURCE = (METHOD = FILE) (METHOD_DATA = (DIRECTORY =&#160;/oracle/PRD/112/dbs</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>Windows</td>\r\n<td>\r\n<p>&lt;ORACLE_HOME&gt;\\DATABASE</p>\r\n</td>\r\n<td>\r\n<p>ENCRYPTION_WALLET_LOCATION=(SOURCE = (METHOD = FILE) (METHOD_DATA = (DIRECTORY =&#160;&lt;DRIVE&gt;:\\oracle\\PRD\\11204\\DATABASE</p>\r\n</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><span style=\"text-decoration: underline;\">Software Keystore 12c (12.1)&#160;or higher</span></p>\r\n<p>Starting 12c, the software keystore for an SAP database is&#160;stored outside the Oracle home in an SAP-specific location &lt;wallet_location&gt;. This has the advantage that the software keystore does not need to be moved or copied when the Oracle home is changed when the database is upgraded or patched.</p>\r\n<p>For an SAP database, the&#160;standard location &lt;wallet_location&gt; of the Oracle software keystore is described in the following table:</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td>&nbsp;</td>\r\n<td><span style=\"text-decoration: underline;\">&lt;wallet_location&gt;</span></td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>&#160;Database on File System</p>\r\n</td>\r\n<td>\r\n<p>&lt;SAPDATA_HOME&gt;/orawallet</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>&#160;Database on ASM</p>\r\n</td>\r\n<td>\r\n<p>\"+&lt;DG&gt;/&lt;DBNAME&gt;/orawallet\"</p>\r\n</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<ul>\r\n<li>&lt;SAPDATA_HOME&gt;: base directory of SAPDATA directories, environment variable $SAPDATA_HOME</li>\r\n<li>&lt;DG&gt;: Name of ASM Disk Group</li>\r\n<li>&lt;DBNAME&gt;: Name of the SAP database</li>\r\n</ul>\r\n<p>Note: SAP supports TDE configuration with software keystores. Use of external key managers HSM or OKV is allowed, but no support from SAP (see SAP Note&#160;<a target=\"_blank\" href=\"/notes/105047\">105047</a>).</p>\r\n<p><span style=\"text-decoration: underline;\">SAP Database Configuration for TDE for Software Keystore</span></p>\r\n<p>The following table summarizes the SAP default&#160;configuration for TDE for 18c or higher for software keystores:</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td><span style=\"text-decoration: underline;\">TDE Parameter / Configuration</span></td>\r\n<td><span style=\"text-decoration: underline;\">SAP Standard TDE Configuration</span></td>\r\n<td><span style=\"text-decoration: underline;\">Oracle Default</span></td>\r\n<td><span style=\"text-decoration: underline;\">Reference / Recommendation</span></td>\r\n</tr>\r\n<tr>\r\n<td rowspan=\"3\">WALLET_ROOT</td>\r\n<td>\r\n<p>'&lt;SAPDATA_HOME&gt;/orawallet' (UNIX)</p>\r\n</td>\r\n<td rowspan=\"3\">\r\n<p>no default value</p>\r\n</td>\r\n<td rowspan=\"3\">\r\n<p>SAP Note <a target=\"_blank\" href=\"/notes/2470718\">2470718</a></p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>'&lt;SAPDATA_HOME&gt;\\orawallet' (Windows)</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>'+&lt;DG&gt;/&lt;DBNAME&gt;/orawallet' (ASM)</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>TDE_CONFIGURATION</td>\r\n<td>\r\n<p>'KEYSTORE_CONFIGURATION=FILE'</p>\r\n</td>\r\n<td>\r\n<p>no default value</p>\r\n</td>\r\n<td>\r\n<p>SAP Note <a target=\"_blank\" href=\"/notes/2470718\">2470718</a></p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>ENCRYPT_NEW_TABLESPACES</td>\r\n<td>&lt;not set for SAP, using Oracle default&gt;</td>\r\n<td>CLOUD_ONLY</td>\r\n<td>\r\n<p>SAP Note <a target=\"_blank\" href=\"/notes/2470718\">2470718</a></p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>TABLESPACE_ENCRYPTION (19.16 or higher)</td>\r\n<td>&lt;not set for SAP, using Oracle default&gt;</td>\r\n<td>on-prem: MANUAL_ENABLE<br />cloud: AUTO_ENABLE</td>\r\n<td>\r\n<p>SAP Note&#160;<a target=\"_blank\" href=\"/notes/2470718\">2470718</a></p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>SQLNET.ENCRYPTION_WALLET_LOCATION</td>\r\n<td>do not use (deprecated)</td>\r\n<td>-</td>\r\n<td>migrate to WALLET_ROOT/TDE_CONFIGURATION</td>\r\n</tr>\r\n<tr>\r\n<td>password-protected software keystore (ewallet.p12)</td>\r\n<td>must exist</td>\r\n<td>-</td>\r\n<td>-</td>\r\n</tr>\r\n<tr>\r\n<td>AUTO-LOGIN software keystore (cwallet.sso)</td>\r\n<td>must exist</td>\r\n<td>-</td>\r\n<td>-</td>\r\n</tr>\r\n<tr>\r\n<td>LOCAL&#160;AUTO-LOGIN software keystore (cwallet.sso)</td>\r\n<td>do not use</td>\r\n<td>-</td>\r\n<td>-</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p>Table: SAP Default Configuration for TDE (valid for 18c or higher)</p>\r\n<p><span style=\"text-decoration: underline;\">Oracle Database 19c TDE Parameter Reference</span></p>\r\n<ul>\r\n<li>Mandatory parameter:</li>\r\n<ul>\r\n<li><a target=\"_blank\" href=\"https://docs.oracle.com/en/database/oracle/oracle-database/19/refrn/WALLET_ROOT.html\">WALLET_ROOT (oracle.com)</a></li>\r\n<li><a target=\"_blank\" href=\"https://docs.oracle.com/en/database/oracle/oracle-database/19/refrn/TDE_CONFIGURATION.html\">TDE_CONFIGURATION (oracle.com)</a></li>\r\n</ul>\r\n<li>Optional parameter:</li>\r\n<ul>\r\n<li><a target=\"_blank\" href=\"https://docs.oracle.com/en/database/oracle/oracle-database/19/refrn/TABLESPACE_ENCRYPTION.html\">TABLESPACE_ENCRYPTION (oracle.com)</a></li>\r\n<li><a target=\"_blank\" href=\"https://docs.oracle.com/en/database/oracle/oracle-database/19/refrn/ENCRYPT_NEW_TABLESPACES.html\">ENCRYPT_NEW_TABLESPACES (oracle.com)</a></li>\r\n</ul>\r\n</ul>\r\n<p><span style=\"text-decoration: underline;\">Software Keystore in file system or on ASM?</span></p>\r\n<ul>\r\n<li>If the database is stored in ASM, the software keystore can be stored either in ASM (recommended) or in&#160;file system.</li>\r\n<li>For a single instance database:</li>\r\n<ul>\r\n<li>SI/FS: software keystore on file system</li>\r\n<li>SI/ASM: software keystore on ASM (recommended) or&#160;on&#160;file system</li>\r\n</ul>\r\n<li>For a RAC database, the software keystore must be stored on a shared storage that is accessible by all RAC nodes.</li>\r\n<ul>\r\n<li>RAC/FS (RAC on file system): software keystore on shared file system</li>\r\n<li>RAC/ASM (RAC on ASM): software keystore on ASM (recommended) or&#160;on shared file system</li>\r\n</ul>\r\n</ul>\r\n<p>SAP BR*Tools support software keystore in ASM (see SAP Note <a target=\"_blank\" href=\"/notes/2087004\">2087004</a>).</p>\r\n\r\n<p><span style=\"text-decoration: underline;\">SAP BR*Tools</span></p>\r\n<p>BRBACKUP / BRARCHIVE automatically backup the software keystore if the following prerequisites are met:</p>\r\n<ul>\r\n<li>The software keystore is located in the SAP standard location &lt;wallet_location&gt; as described&#160; in the table above.</li>\r\n<li>For ASM: the software keystore must be located on the same ASM &lt;DG&gt; as the spfile (see SAP Note <a target=\"_blank\" href=\"/notes/2087004\">2087004</a>)</li>\r\n</ul>\r\n<p>If these requirements are not met, BRARCHIVE / BRBACKUP do not automatically backup the TDE software keystore. You can verify whether the software keystore is saved by BRBACKUP/BRARCHIVE. Check the output / log files for the following message: \"<span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">BR1733I Database wallet will be saved:</span>&#65279;\".</p>\r\n<p><span style=\"text-decoration: underline;\">SAP Database Configuration for TDE for External Key Managers (HSM, OKV)</span></p>\r\n<p>Note: Oracle Key Vault (OKV) and Hardware Security Modules (HSM) are not supported by SAP support. For more information, see SAP Note&#160;<a target=\"_blank\" href=\"/notes/105047\">105047</a>.</p>\r\n<p>Requirements:</p>\r\n<ul>\r\n<li>18c or higher</li>\r\n</ul>\r\n<p>For external key managers - HSM or OKV - you should configure database parameter 'TDE_CONFIGURATION' in your SAP database as follows:</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td><span style=\"text-decoration: underline;\">Parameter Name</span></td>\r\n<td>\r\n<p><span style=\"text-decoration: underline;\">Parameter Value</span></p>\r\n</td>\r\n<td>\r\n<p><span style=\"text-decoration: underline;\">Remark</span></p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>SQLNET.ENCRYPTION_WALLET_LOCATION</td>\r\n<td>\r\n<p>-</p>\r\n</td>\r\n<td>do not use (deprecated)</td>\r\n</tr>\r\n<tr>\r\n<td>WALLET_ROOT</td>\r\n<td>\r\n<p>see above</p>\r\n</td>\r\n<td>\r\n<p>-</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td rowspan=\"2\">TDE_CONFIGURATION</td>\r\n<td>\r\n<p>'KEYSTORE_CONFIGURATION=HSM|FILE'</p>\r\n</td>\r\n<td>\r\n<p>Auto-Login HSM configuration</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>'KEYSTORE_CONFIGURATION=OKV|FILE'</p>\r\n</td>\r\n<td>\r\n<p>Auto-Login OKV configuration</p>\r\n</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p>Table: Database Configuration for TDE for HSM or OKV (valid for 18c or higher)</p>\r\n<p>Reference:</p>\r\n<ul>\r\n<li>SAP Note&#160;<a target=\"_blank\" href=\"/notes/2470718\">2470718</a></li>\r\n<li>https://docs.oracle.com/en/database/oracle/oracle-database/19/refrn/TDE_CONFIGURATION.html</li>\r\n</ul>\r\n<p><strong><a target=\"_blank\" name=\"TDE_SAP_SUPPORT\"></a>&#65279;Support for&#160;TDE in SAP Tools</strong></p>\r\n<p>Encrypting and decrypting data in the Oracle database using TDE is transparent for&#160;SAP NetWeaver based SAP applications. However, administration or installation tools like BRBACKUP, BRSPACE or SWPM which manage and install the SAP database need to be aware of TDE.</p>\r\n<p><strong><span style=\"text-decoration: underline;\">Support for TDE in SAP BR*Tools</span></strong></p>\r\n<p>Prerequisites:</p>\r\n<ul>\r\n<li>Oracle Database 12c Release 1 or&#160;higher</li>\r\n<li>BR*Tools 7.40 Patch Level 14 or higher (see SAP Note <a target=\"_blank\" href=\"/notes/2087004\">2087004</a>)</li>\r\n<li>Release 12.1: SAP BR*Tools 7.40 Patch Level 14 or higher (see SAP Note <a target=\"_blank\" href=\"/notes/1914631\">1914631</a>)</li>\r\n<li>Release 12.2: SAP BR*Tools 7.40 Patch Level 32 or higher (see SAP Note <a target=\"_blank\" href=\"/notes/2470660\">2470660</a>)</li>\r\n<li>Release 19c: SAP BR*Tools 7.40 Patch Level 40 or higher (see SAP Note <a target=\"_blank\" href=\"/notes/2799900\">2799900</a>)</li>\r\n<li>The BR*Tools profile 'init&lt;DBSID&gt;.sap' must be located in &lt;SAPDATA_HOME&gt;/sapprof' (see SAP Note <a target=\"_blank\" href=\"/notes/1598594\">1598594</a>).</li>\r\n</ul>\r\n<p>Recommendation:</p>\r\n<ul>\r\n<li>Use current/latest version of SAP BR*Tools 7.40.</li>\r\n</ul>\r\n<p>For more information, see the following SAP Notes:</p>\r\n<ul>\r\n<li>SAP note&#160;<a target=\"_blank\" href=\"/notes/1838850\">1838850</a> (BR*Tools 7.40),</li>\r\n<li>SAP note&#160;<a target=\"_blank\" href=\"/notes/2333995\">2333995</a> (BR*Tools Support for Multitenant)</li>\r\n<li>SAP note&#160;<a target=\"_blank\" href=\"/notes/1598594\">1598594</a> (BR*Tools configuration for 'oracle' software owner).</li>\r\n</ul>\r\n<p><span style=\"text-decoration: underline;\">&#65279;Support of Oracle Transparent Data Encryption for SAP NetWeaver in SAP BR*Tools</span></p>\r\n<p>SAP BR*Tools support ...</p>\r\n<ul>\r\n<li>SAP BR*Tools support Oracle TDE&#160;administration&#160;starting Oracle Database 10g Release 2.</li>\r\n<li>SAP BR*Tools support Oracle TDE Tablespace Encryption starting Oracle Database 11g Release 2.</li>\r\n<li>SAP BR*Tools support AKM&#160;SQL&#160;commands (ADMINISTER KEY MANAGMENT) starting Oracle Database 12c (12.1).</li>\r\n<li>SAP BR*Tools support initialization parameter WALLET_ROOT / TDE_CONFIGURATION for Oracle Database 18c or higher.</li>\r\n<li>SAP BR*Tools support Full Database Encryption and TDE Tablespace Conversion for Oracle Database 19c or higher.</li>\r\n</ul>\r\n<p><span style=\"text-decoration: underline;\">Changes in BR*Tools&#160;for Oracle Database 19c</span></p>\r\n<p>TDE tablespace conversion functionality was added. For more information, see SAP Note <a target=\"_blank\" href=\"/notes/2799991\">2799991</a>.</p>\r\n<p><span style=\"text-decoration: underline;\"><span style=\"text-decoration: underline;\">Changes in BR*Tools</span>&#160;for Oracle Database&#160;18c</span></p>\r\n<p>The software keystore default type&#160;has changed from LOCAL AUTO-LOGIN to AUTO-LOGIN.</p>\r\n<p><span style=\"text-decoration: underline;\">Support for TDE in BRBACKUP/BRARCHIVE/BRRECOVER</span></p>\r\n<p>SAP BR*Tools support backup and recovery of SAP databases with TDE.</p>\r\n\r\n<p>The SAP BR*Tools backup and recovery&#160;strategy is as follows:</p>\r\n<p><span style=\"text-decoration: underline;\">Automatic Backup (SAP Recommended)</span></p>\r\n<p>If TDE is configured in an SAP database, BRBACKUP / BRARCHIVE always include a backup of the software keystore in&#160;every database backup.</p>\r\n<ul>\r\n<li>BRBACKUP/BRARCHIVE automatically include a backup of the software keystore in every database backup if the software keystore is located in the SAP default location.</li>\r\n<li>With this backup strategy it is impossible&#160;not to backup the software keystore.</li>\r\n<li>The auto-login software keystore (auto-open, auto-login) is never included in a backup.</li>\r\n<li>This backup strategy guarantees that you always backup your current software keystore at the same time when you backup your database.</li>\r\n</ul>\r\n<p>SAP BR*Tools&#160;follow the Oracle recommendation to backup the software keystore at the same time with the database, but not follow the recommendation not to include the wallet on the same media as the database backup&#160;(attached, see <a target=\"_blank\" href=\"https://www.oracle.com/technetwork/database/security/twp-transparent-data-encryption-bes-130696.pdf\">Oracle White Paper</a>). Advantages of this SAP backup strategy are</p>\r\n<ul>\r\n<li>Automatic software keystore backup requires no configuration changes for BR*Tools because of TDE. The software keystore must be in the SAP default location.</li>\r\n<li>Backups are consistent and self-contained.</li>\r\n<li>The Software Keystore is always protected by a secure password. There is no need&#160;to backup&#160;software keystore to separate media.</li>\r\n<li>Backing up the software keystore to a separate media would&#160;require separate storage locations for database backups and software keystore backups making database backups complicated from an organizational perspective.</li>\r\n</ul>\r\n<p><span style=\"text-decoration: underline;\">Manual Backup</span></p>\r\n<p>If you don't want BRBACKUP / BRARCHIVE to&#160;automatically include the software keystore in every database backup, you can do the following:</p>\r\n<p>Use a&#160;location for the software keystore that is different from the&#160;SAP default location.&#160;With such a configuration BRBACKUP / BRARCHIVE will not backup the software keystore automatically. But&#160;you are now responsible to backup the software keystore using other means and not to forget to backup the software keystore. You should backup the software keystore regularly and you should backup the software keystore before and after changes in the software keystore (e.g. after new master password or new master keys).</p>\r\n<p>You can use BRSPACE function \"<span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">mdencr</span>\" with action \"<span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">save</span>\" to manually backup the software keystore&#160;to disk.</p>\r\n\r\n<p>Note: If the software keystore is not located in the SAP default location, you will receive a warning from BRSPACE function \"mdencr\":<br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">BR1703W Wallet directory '&lt;non-standard-location&gt;' not set to the recommended value of '&lt;standard-location&gt;'&#65279;</span></p>\r\n<p>You can suppress this warning by setting the&#160;following init&lt;DBSID&gt;.sap profile special parameter:<br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">_wallet_loc_info = yes&#65279;</span></p>\r\n<p>&#160;</p>\r\n\r\n<p>&#160;</p>\r\n<p><span style=\"text-decoration: underline;\">Support for TDE in BRSPACE</span></p>\r\n<p>SAP BR*Tools (BRSPACE) have implemented management operations for software keystores (wallet operations, TDE master key operations, administration of&#160;encrypted tablespaces) and&#160;basic&#160;management operations&#160;for hardware keystores (HSM). For more information, see SAP Note <a target=\"_blank\" href=\"/notes/1279682\">1279682</a>.</p>\r\n<p>You can run BR*Tools commands&#160;for Oracle TDE adminstration as follows:</p>\r\n<ul>\r\n<li>Option #1: <span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">brtools -&gt; 1 - Instance Management -&gt; 7 - Manage Data Encryption&#65279;</span></li>\r\n<li>Option #2: <span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">brspace -f mdencr&#160;&#65279;</span>&#65279;&#65279;&#65279;</li>\r\n</ul>\r\n<p>BRSPACE: Options for 'mdencr' function:</p>\r\n<p>For more information, run 'brspace -help' and search for function code 'mdencr'.</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td><span style=\"text-decoration: underline;\">brspace command</span></td>\r\n<td><span style=\"text-decoration: underline;\">description</span></td>\r\n</tr>\r\n<tr>\r\n<td>brspace -f mdencr -a open<br />brspace -f mdencr -a close</td>\r\n<td>open database wallet<br />close database wallet</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>brspace -f mdencr -a create<br />brspace -f mdencr -a delete</p>\r\n</td>\r\n<td>create database wallet<br />delete database wallet</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>brspace -f mdencr -a save</p>\r\n</td>\r\n<td>save database wallet</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>brspace -f mdencr -a chpass</p>\r\n</td>\r\n<td>change wallet password</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>brspace -f mdencr -a newkey</p>\r\n</td>\r\n<td>generate new master key</td>\r\n</tr>\r\n<tr>\r\n<td>brspace -f mdencr -a rekey</td>\r\n<td>re-key encrypted tables</td>\r\n</tr>\r\n<tr>\r\n<td>brspace -f mdencr -a enable<br />brspace -f mdencr -a disable</td>\r\n<td>enable auto-open wallet<br />disable auto-open wallet</td>\r\n</tr>\r\n<tr>\r\n<td>brspace -f mdencr -a display<br />brspace -f mdencr -a show</td>\r\n<td>display database wallet info<br />show encryption status</td>\r\n</tr>\r\n<tr>\r\n<td>brspace -f mdencr -a list<br />brspace -f mdencr -a listts</td>\r\n<td>list encrypted tables/columns<br />list encrypted tablespaces</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p>&#160;</p>\r\n<p>&#160;</p>\r\n\r\n<p><strong><span style=\"text-decoration: underline;\">Support for TDE in SAP Software Provisioning Manager (SWPM)</span></strong></p>\r\n<p>SWPM&#160;supports TDE Tablespace Encryption. For more information, see SAP Note <a target=\"_blank\" href=\"/notes/2485122\">2485122</a>.</p>\r\n\r\n<p>&#160;</p>\r\n<p><strong><a target=\"_blank\" name=\"TDE_SAP_CONFIGURATION\"></a>SAP&#160;Configuration Procedure for TDE</strong></p>\r\n<p>The following steps enable&#160;Oracle Transparent Data Encryption (Tablespace Encryption) in your SAP database. For all steps the corresponding SQL command and BRSPACE command line are shown. We strongly recommend to use the corresponding BRSPACE command, especially when the keystore or TDE master key is changed. BRSPACE always automatically creates a backup of the software keystore before and after the operation.</p>\r\n<p>Reference: Oracle Advanced Security Guide (12.2), <a target=\"_blank\" href=\"https://docs.oracle.com/en/database/oracle/oracle-database/12.2/security.html\">Security</a>, <a target=\"_blank\" href=\"https://docs.oracle.com/en/database/oracle/oracle-database/12.2/asoag/index.html\">Advanced Security Guide</a>, <a target=\"_blank\" href=\"https://docs.oracle.com/en/database/oracle/oracle-database/12.2/asoag/configuring-transparent-data-encryption.html\">Configuring Transparent Data Encryption</a></p>\r\n<ol>\r\n<li>Step 0: Preparation</li>\r\n<li>Step 1: Set the Keystore Location (sqlnet.ora or wallet_root)</li>\r\n<li>Step 2: Create the Software Keystore</li>\r\n<li>Step 3: Open the Software Keystore</li>\r\n<li>Step 4: Set the Software TDE Master Encryption Key</li>\r\n<li>Step 5: Encrypt Your Data&#160;</li>\r\n</ol>\r\n<p><span style=\"text-decoration: underline;\">Step 0: Preparation</span></p>\r\n<p>Before you modify your SAP database for use with TDE, you should backup your database.</p>\r\n<p>In the&#160;following table you find a list of basic brspace and SQL commands to check the current configuration status of TDE. You can use these commands&#160;before, during and after configuration of TDE.</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td><span style=\"text-decoration: underline;\">Task</span></td>\r\n<td><span style=\"text-decoration: underline;\">brspace</span></td>\r\n<td><span style=\"text-decoration: underline;\"><span style=\"text-decoration: underline;\">SQL Command</span></span></td>\r\n</tr>\r\n<tr>\r\n<td>Check Keystore Status</td>\r\n<td>OS&gt; brspace -f mdencr -a display<br />OS&gt; brspace -f mdencr -a display -password &lt;wallet password&gt;</td>\r\n<td>\r\n<p>SQL&gt; select WRL_PARAMETER \"TDE&#160;KEYSTORE LOCATION\", STATUS \"TDE&#160;KEYSTORE STATUS\" from v$encryption_wallet;</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>List encrypted tablespaces</td>\r\n<td>\r\n<p>OS&gt; brspace -f mdencr -a listts</p>\r\n</td>\r\n<td>\r\n<p>-</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>List tablespaces</p>\r\n</td>\r\n<td>\r\n<p>OS&gt; brspace -f dbshow -class tsinfo</p>\r\n</td>\r\n<td>\r\n<p>-</p>\r\n</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p>&#160;</p>\r\n<p><span style=\"text-decoration: underline;\">Step 1: Set the Keystore Location&#160;(ENCRYPTION_WALLET_LOCATION or WALLET_ROOT)</span></p>\r\n\r\n<p>ENCRYPTION_WALLET_LOCATION (SQLNET.ORA) is deprecated. For 18c or higher, you therefore use initialization parameter WALLET_ROOT instead.</p>\r\n<p>For releases &gt;= 18c, you configure initialization parameter WALLET_ROOT.</p>\r\n<p style=\"padding-left: 30px;\">Example (WALLET_ROOT, file system):<br />WALLET_ROOT=&lt;SAPDATA_HOME&gt;/orawallet</p>\r\n<p>For releases &lt;= 12.2, you configure ENCRYPTION_WALLET_LOCATION (SQLNET.ORA).</p>\r\n<p style=\"padding-left: 30px;\">Example (SQLNET.ORA, file system):<br />ENCRYPTION_WALLET_LOCATION = (SOURCE = (METHOD = FILE) (METHOD_DATA = (DIRECTORY = &lt;SAPDATA_HOME&gt;/orawallet )))</p>\r\n<p>Create the directory for the TDE keystore.</p>\r\n<p style=\"padding-left: 30px;\">Make sure that the directory is empty.<br />Make sure that the database instance has read and write permissions.<br />Ensure that only the database instance (Oracle software owner) has access and write permissions.</p>\r\n<p>Example (Unix, file system):</p>\r\n<p style=\"padding-left: 30px;\">With &lt;SAPDATA_HOME&gt; = /oracle/&lt;DBSID&gt;:<br />OS&gt; mkdir -p /oracle/&lt;DBSID&gt;/orawallet/tde<br />OS&gt; chown -R oracle:oinstall /oracle/&lt;DBSID&gt;/orawallet<br />OS&gt; chmod -R 700 /oracle/&lt;DBSID&gt;/orawallet</p>\r\n<p>Restart the instance&#160;for the changes&#160;to take effect, then check the result:</p>\r\n<p style=\"padding-left: 30px;\">OS&gt; brspace -f mdencr -a display<br />or<br />sqlplus / as sysdba<br />SQL&gt; select WRL_PARAMETER \"TDE&#160;KEYSTORE LOCATION\", STATUS \"TDE&#160;KEYSTORE STATUS\" from v$encryption_wallet;</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td><span style=\"text-decoration: underline;\">brspace</span></td>\r\n<td><span style=\"text-decoration: underline;\"><span style=\"text-decoration: underline;\">SQL Command</span></span></td>\r\n</tr>\r\n<tr>\r\n<td>OS&gt; brspace -f mdencr -a display<br />OS&gt; brspace -f mdencr -a display -password &lt;wallet password&gt;</td>\r\n<td>\r\n<p>SQL&gt; select WRL_PARAMETER \"TDE&#160;KEYSTORE LOCATION\", STATUS \"TDE&#160;KEYSTORE STATUS\" from v$encryption_wallet;</p>\r\n</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n\r\n<p><span style=\"text-decoration: underline;\">Step 2: Create the Software Keystore</span></p>\r\n<p>After you have specified a directory location for the software keystore in step 1, you can create the keystore.</p>\r\n<p>Note: you can create 3 different types of keystore:</p>\r\n<ul>\r\n<li>password-based</li>\r\n<li>auto-login</li>\r\n<li>local auto-login</li>\r\n</ul>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td><span style=\"text-decoration: underline;\">brspace</span></td>\r\n<td><span style=\"text-decoration: underline;\">SQL Command</span></td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>OS&gt; brspace -f mdencr -a create<br />OS&gt; brspace -f mdencr -a create -password &lt;wallet password&gt;</p>\r\n</td>\r\n<td>\r\n<p>SQL&gt; administer key management create keystore identified by \"&lt;wallet password&gt;\";</p>\r\n</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p>Example: brspace -f mdencr -a create -password abcd1234</p>\r\n<p>After this operation the keystore (TDE encryption wallet ewallet.p12) has been created.</p>\r\n<p>Note the following when you create the TDE software keystore:</p>\r\n<ul>\r\n<li>Initialization parameter WALLET_ROOT must be configured to '&lt;wallet_root&gt;' as described above.</li>\r\n<li>If the keystore location is in ASM, Oracle automatically creates the corresponding directories in ASM.</li>\r\n<li>If the keystore location is in a filesystem, the corresponding directory '&lt;wallet_root&gt;/tde' must be created before you can create the keystore.</li>\r\n<ul>\r\n<li>If you use BRSPACE to create the keystore, BRSPACE will automatically create this directory and set ownership and permissions.</li>\r\n<li>If you create the keystore with SQL commands, you must manually create this directory and set ownership and permissions.</li>\r\n</ul>\r\n<li>If the WALLET_ROOT parameter is set, you can and you should omit the path from the ADMINISTER KEY MANAGEMENT CREATE KEYSTORE command.&#160;</li>\r\n<ul>\r\n<li>Do not use: SQL&gt; administer key management create keystore '&lt;wallet_root&gt;' identified by \"&lt;wallet password&gt;\";</li>\r\n<li>Use command: SQL&gt; administer key management create keystore identified by \"&lt;wallet password&gt;\";</li>\r\n</ul>\r\n<li>If WALLET_ROOT is set to '&lt;wallet_root&gt;', 'ADMINISTER KEY MANAGEMENT CREATE KEYSTORE IDENTIFIED BY ...' creates the keystore in '&lt;wallet_root&gt;/tde'.</li>\r\n<li>If WALLET_ROOT is set to '&lt;wallet_root&gt;', 'ADMINISTER KEY MANAGEMENT CREATE KEYSTORE '&lt;wallet_root&gt;' IDENTIFIED BY ...' creates the keystore in '&lt;wallet_root&gt;'.</li>\r\n<li>If you set parameter WALLET_ROOT='&lt;wallet_root&gt;', the real location of the keystore that is shown in v$encryption_wallet is '&lt;wallet_root&gt;/tde'.</li>\r\n<li>Reference:&#160;https://docs.oracle.com/en/database/oracle/oracle-database/19/refrn/WALLET_ROOT.html&#160;</li>\r\n</ul>\r\n\r\n<p><span style=\"text-decoration: underline;\">Step 3: Open the Software Keystore</span></p>\r\n<p>Depending on the type of keystore you create, you must manually open the keystore before you can use it.</p>\r\n<p>A password-based software keystore must be open before any TDE master encryption keys can be created or accessed in the keystore.</p>\r\n<p>You do not need to manually open auto-login or local auto-login software keystores. These keystore are automatically opened when it is required, that is, when an encryption operation must access the key. If necessary, you can explicitly close any of these types of keystores. Keystores can be in the following states: open, closed, open but with no master key, open but with an unknown master key, undefined, or not available (that is, not present in the <code class=\"codeph\">sqlnet.ora</code> location).</p>\r\n<p>You can check the status of whether a keystore is open or not by querying the <code class=\"codeph\">STATUS</code> column of the <code class=\"codeph\">V$ENCRYPTION_WALLET</code> view.</p>\r\n<p>After you open a keystore, it remains open until you manually close it. Each time you restart a database instance, you must manually open the password keystore to reenable encryption and decryption operations.</p>\r\n<p>&#160;</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td><span style=\"text-decoration: underline;\">brspace</span></td>\r\n<td><span style=\"text-decoration: underline;\">SQL Command</span></td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>OS&gt; brspace -f mdencr -a open<br />OS&gt; brspace -f mdencr -a&#160;open -password &lt;wallet password&gt;</p>\r\n</td>\r\n<td>\r\n<p>SQL&gt; administer key management set keystore open force keystore identified by \"&lt;wallet password&gt;\"</p>\r\n</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n\r\n<p><span style=\"text-decoration: underline;\">Step 4: Set the Software TDE Master Encryption Key</span></p>\r\n<p>The TDE master encryption key is stored in the keystore. This key protects the TDE table keys (TDE column encryption) and TDE tablespace encryption keys (TDE tablespace encryption).</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td><span style=\"text-decoration: underline;\">brspace</span></td>\r\n<td><span style=\"text-decoration: underline;\">SQL Command</span></td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>OS&gt; brspace -f mdencr -a newkey<br />OS&gt; brspace -f mdencr -a&#160;newkey -password &lt;wallet password&gt;</p>\r\n</td>\r\n<td>\r\n<p>SQL&gt; administer key management set encryption key <br />using tag '&lt;brspace_tag&gt;.mde' force keystore <br />identified by \"&lt;wallet password&gt;\" <br />with backup using '&lt;brspace_tag&gt;'</p>\r\n</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n\r\n<p><span style=\"text-decoration: underline;\">Step 5: Encrypt Your Data</span></p>\r\n<p>After you complete the software keystore configuration, you can begin to encrypt data.</p>\r\n<p>The standard procedure is to create a new encrypted tablespace with brspace and then migrate all tablespace&#160;objects&#160;(tables, indexes, partitions)&#160;from the&#160;existing unencrypted tablespace to the new, encrypted tablespace. You can do this online or offline.</p>\r\n<p>Note: Before you start this operation, you should backup the database.</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td><span style=\"text-decoration: underline;\">Task / Step</span></td>\r\n<td><span style=\"text-decoration: underline;\">Detailed Description</span></td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>Create new encrypted tablespace(s)</p>\r\n</td>\r\n<td>\r\n<p><span style=\"text-decoration: underline;\">BRSPACE Command (Example)</span></p>\r\n<p>OS&gt; brspace -f tscreate -encryption yes -t &lt;new_ts&gt; -class &lt;option&gt;</p>\r\n<p><span style=\"text-decoration: underline;\">Description</span></p>\r\n<p>This command creates a new encrypted tablespace. In the next step all segments from the old tablespace will be moved into the new tablespace. When finished,&#160;the empty old tablespace will be dropped.</p>\r\n<p><span style=\"text-decoration: underline;\">Example</span></p>\r\n<p>&lt;old_ts&gt;=PSAPSR3<br />&lt;new_ts&gt;=PSAPSR3ENC</p>\r\n<p>OS&gt; brspace -f tscreate -encryption yes -t&#160;psapsr3enc -class &lt;tab_class_option&gt;</p>\r\n<p><span style=\"text-decoration: underline;\">Additional Notes</span></p>\r\n<p>Using the option '-class &lt;tab_class_option&gt;' ('-l&#160;&lt;tab_class_option&gt;')&#160;is very important for tablespace reorganisations when tables/indexes are moved into another tablespace. All TABARTs (table data classes) from the old tablespace must be&#160;assigned to the new tablespace. For more information, see SAP Note <a target=\"_blank\" href=\"/notes/646681\">646681</a>, sections II.1, IV.1 and V.6.</p>\r\n<p>&#160;</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>Perform Tablespace Reorganization</p>\r\n</td>\r\n<td>\r\n<p><span style=\"text-decoration: underline;\">BRSPACE Command (Example)</span></p>\r\n<p>OS&gt; brspace -f tbreorg -s&#160;&lt;old_ts&gt; -n &lt;new_ts&gt; -t allsel -p 8</p>\r\n<p><span style=\"text-decoration: underline;\">Description</span></p>\r\n<p>All tables and indexes from source tablespace PSAPSR3 are moved into the new encrypted tablespace PSAPSR3ENC using BRSPACE Online Reorganization.</p>\r\n<p><span style=\"text-decoration: underline;\">Example</span></p>\r\n<p>OS&gt; brspace -f tbreorg -s psapsr3 -n psapsr3enc -t allsel -p 8</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>Drop old&#160;empty tablespace(s)</p>\r\n</td>\r\n<td>\r\n<p><span style=\"text-decoration: underline;\">BRSPACE Command (Example)</span></p>\r\n<p>OS&gt; brspace -f tsdrop -t &lt;old_ts&gt;</p>\r\n<p><span style=\"text-decoration: underline;\">Description</span></p>\r\n<p>Drop old empty tablespace.</p>\r\n<p><span style=\"text-decoration: underline;\">Example</span></p>\r\n<p>OS&gt; brspace -f tsdrop -t psapsr3</p>\r\n<p><span style=\"text-decoration: underline;\">Additional Notes</span></p>\r\n<p>Do not drop the datafiles of the tablespace. They can contain unencrypted 'ghost copies' of the SAP data. For more information, see&#160; 'Ghost copies' in SAP Note <a target=\"_blank\" href=\"/notes/974876\">974876</a>.</p>\r\n</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p>&#160;</p>\r\n<p>&#160;</p>\r\n\r\n<p>&#160;</p>\r\n<p><strong><strong><a target=\"_blank\" name=\"TDE_CONSIDERATIONS\"></a>&#65279;General Considerations&#160;When Using TDE</strong></strong></p>\r\n<p>Reference: For more information, see Advanced Security Guide, <a target=\"_blank\" href=\"https://docs.oracle.com/en/database/oracle/oracle-database/12.2/asoag/general-considerations-of-using-transparent-data-encryption.html\">General Considerations of Using TDE</a></p>\r\n<p><span style=\"text-decoration: underline;\">Compression</span></p>\r\n<p>With tablespace encryption, Oracle Database compresses tables and indexes before encrypting the tablespace. This ensures that you receive the maximum space and performance benefits from compression, while also receiving the security of encryption at rest. In the <code class=\"codeph\">CREATE TABLESPACE</code> SQL statement, include both the <code class=\"codeph\">COMPRESS</code> and <code class=\"codeph\">ENCRYPT</code> clauses.</p>\r\n<p>With column encryption, Oracle Database compresses the data after it encrypts the column. This means that compression will have minimal effectiveness on encrypted columns.</p>\r\n<p>For more information, see SAP Notes <a target=\"_blank\" href=\"/notes/1436352\">1436352</a> (11g) and <a target=\"_blank\" href=\"/notes/2138262\">2138262</a> (12c).</p>\r\n<p><span style=\"text-decoration: underline;\">Column Encryption and Plain Text Ghost Copies</span></p>\r\n<p>Encrypted column data stays encrypted in the data files, undo logs, redo logs, and the buffer cache of the system global area (SGA). However, decrypted data&#160;may appear in the swap file on the disk. Data files may also still contain some plaintext fragments, called ghost copies, left over by past data operations on the table. You can minimize the risk of ghost copies by moving the data into a new tablespace and securely delete the old datafile afterwards using platform-specific and filesystem-specific utilities (e.g. shred (Linux) or sdelete (Windows)).</p>\r\n<p><span style=\"text-decoration: underline;\">Performance Overhead and Storage Overhead (Tablespace Encryption)</span></p>\r\n<p>TDE&#160;tablespace encryption has small associated performance overhead. <br />TDE tablespace encryption has no storage overhead.</p>\r\n<p><span style=\"text-decoration: underline;\">Performance Overhead and Storage Overhead (Column Encryption)</span></p>\r\n<p>TDE column encryption can have significant performance overhead which depends on the number of encrypted columns and their frequency of access. <br />TDE column encryption has some associated storage overhead (up to 52 bytes per encrypted value):</p>\r\n<ul>\r\n<li>Encrypted column data has more storage space than plaintext data.</li>\r\n<li>Each encrypted value is also associated with a 20-byte integrity check.</li>\r\n<li>Data that was encrypted with salt requires an additional 16 bytes of storage.</li>\r\n<li>The maximum storage overhead for each encrypted value is from one to 52 bytes (!).</li>\r\n</ul>\r\n<p><span style=\"text-decoration: underline;\">ALTER SYSTEM/ORAPKI vs. ADMINISTER KEY MANAGEMENT</span></p>\r\n<p>In&#160;Oracle Database 10g and 11g, TDE was managed by a set of ALTER SYSTEM and ORAPKI commands. In 12c, a new system of SQL commands was introduced: ADMINISTER KEY MANAGEMENT. Although the old commands still work in 12c,&#160;you should&#160;use the new commands in 12c and higher.</p>\r\n<p>For more information, see <a target=\"_blank\" href=\"https://docs.oracle.com/en/database/oracle/oracle-database/12.2/asoag/general-considerations-of-using-transparent-data-encryption.html\">General Considerations of Using TDE</a>, section 'How ALTER SYSTEM and orapki Map to ADMINISTER KEY MANAGEMENT'</p>\r\n<p><span style=\"text-decoration: underline;\">Transparent Data Encryption and Database Close Operations</span></p>\r\n<p>You should ensure that the software or hardware keystore is open before you close the database. The master keys may be required during the database close operation. The database close operation automatically closes the software or hardware keystore. Therefore: Do not close the keystore&#160;manually before you close the database. Let the database close the keystore.</p>\r\n<p><strong>Using Transparent Data Encryption with Other Oracle Features</strong></p>\r\n<p>Reference: Advanced Security Guide, <a target=\"_blank\" href=\"https://docs.oracle.com/en/database/oracle/oracle-database/12.2/asoag/using-transparent-data-encryption-with-other-oracle-features.html\">Using Transparent Data Encryption with Other Oracle Features</a></p>\r\n<p><span style=\"text-decoration: underline;\">How TDE works with Export and Import</span></p>\r\n<p>Oracle Data Pump can export and import tables that contain encrypted columns, as well as encrypt entire dump sets. Sensitive data that is encrypted in the database should be encrypted when it is exported out from the database into a dump file in the file system.</p>\r\n<p>For more information, see <a target=\"_blank\" href=\"https://docs.oracle.com/en/database/oracle/oracle-database/12.2/asoag/using-transparent-data-encryption-with-other-oracle-features.html\">Using Transparent Data Encryption with Other Oracle Features</a>.</p>\r\n<p><span style=\"text-decoration: underline;\">How TDE works with Data Guard</span></p>\r\n<p>If the primary database uses TDE, then each standby database in a Data Guard configuration must have a copy of the encryption keystore from the primary database.</p>\r\n<p>Please note:&#180;Encrypted data in log files remains encrypted when data is transferred to the standby database. Encrypted data also stays encrypted during transit.</p>\r\n<p>For more information, see <a target=\"_blank\" href=\"https://docs.oracle.com/en/database/oracle/oracle-database/12.2/asoag/using-transparent-data-encryption-with-other-oracle-features.html\">Using Transparent Data Encryption with Other Oracle Features</a>.</p>\r\n<p><span style=\"text-decoration: underline;\">How TDE works with Real Application Clusters (RAC)</span></p>\r\n<p>Oracle RAC nodes can share both a software keystore and a hardware security module.</p>\r\n<p>Oracle recommends that you create the software keystore on a shared file system. This enables all of the instances to access the same shared software keystore. If you configure Oracle RAC to use Automatic Storage Management (ASM), then store the keystore on the ASM disk group. If you do not use a shared file system to store the software keystore, then you must copy the keystore to the associated nodes.</p>\r\n<p>For more information, see <a target=\"_blank\" href=\"https://docs.oracle.com/en/database/oracle/oracle-database/12.2/asoag/using-transparent-data-encryption-with-other-oracle-features.html\">Using Transparent Data Encryption with Other Oracle Features</a>.</p>\r\n<p><span style=\"text-decoration: underline;\">How TDE works with in a Multitenant Environment</span></p>\r\n<p>For more information, see <a target=\"_blank\" href=\"https://docs.oracle.com/en/database/oracle/oracle-database/12.2/asoag/using-transparent-data-encryption-with-other-oracle-features.html\">Using Transparent Data Encryption with Other Oracle Features</a>.</p>\r\n<ul>\r\n<li>In 12.1 and 12.2 there is one&#160;keystore&#160;for a&#160;CDB. Every PDB in a CDB uses the same keystore.</li>\r\n<li>Each PDB has its own TDE master encryption key (V$ENCRYPTION_KEYS).</li>\r\n<li>You can manage the TDE master encryption keys for a given PDB independently from the other PDBs.</li>\r\n<li>You perform most keystore operations from the root.</li>\r\n<li>Some Keystore operations such as opening, closing, resetting and creating keys can be performed within a PDB.</li>\r\n</ul>\r\n<p>Certain keystore operations&#160;must be performed in Root, some keystore operations can use the CONTAINER=ALL clause, some can be performed in Root or in a PDB.</p>\r\n<p><span style=\"text-decoration: underline;\">How Transparent Data Encryption Works with Oracle Call Interface</span></p>\r\n<p>Transparent Data Encryption does not have any effect on the operation of Oracle Call Interface (OCI). TDE is transparent to OCI except for the row shipping feature.</p>\r\n<p><span style=\"text-decoration: underline;\">Configuring Transparent Data Encryption to Work in a Multidatabase Environment</span></p>\r\n<p>Each Oracle database on the same server must access its own TDE keystore. Keystores are not designed to be shared among databases. By design, there must be one keystore per database. You cannot use the same keystore for more than one database. Using a keystore from another database can cause partial or complete data loss.</p>\r\n\r\n<p>&#160;</p>\r\n<p><strong><a target=\"_blank\" name=\"TDE_BEST_PRACTISES\"></a>&#65279;Best Practise Recommendations and Database Administration Changes</strong></p>\r\n<p>In this section you find recommendations regarding the administration of an SAP system&#160;that has&#160;TDE enabled in the database.</p>\r\n<p><span style=\"text-decoration: underline;\">Exporting SAP data with SAP R3Load, SAP JLoad&#160;or Oracle Data Pump</span></p>\r\n<p>Oracle TDE encrypts SAP application data that is stored inside the Oracle database. When the data is written to Oracle database files, online redo log files or archive files, the data is encrypted. Oracle TDE is only effective and protects your SAP application data as long as you keep it inside the Oracle database.</p>\r\n<p>Whenever you export SAP application data with SAP R3Load&#160;(SAP ABAP) or SAP JLoad (SAP Java)&#160;from the Oracle database into R3load / JLoad dump files, the SAP application data is not encrypted and not protected any more by Oracle Transparent Data Encryption.</p>\r\n<p>When you export SAP application data with Oracle data pump into encrypted Oracle data pump export files, the data remains encrypted. This method allows you to safely export/import Oracle data from one Oracle database to another Oracle database. To perform import or export operations with Oracle tools, you must use Oracle Data Pump. For more information, see here:</p>\r\n<p><a target=\"_blank\" href=\"https://docs.oracle.com/en/database/oracle/oracle-database/19/asoag/configuring-transparent-data-encryption.html#GUID-EA4F9C84-0BE8-44AF-8638-3B7EFE4CA92C\">https://docs.oracle.com/en/database/oracle/oracle-database/19/asoag/configuring-transparent-data-encryption.html#GUID-EA4F9C84-0BE8-44AF-8638-3B7EFE4CA92C</a></p>\r\n<p><span style=\"text-decoration: underline;\">Database Administration with TDE</span></p>\r\n<p>Reference: MOS <a target=\"_blank\" href=\"https://support.oracle.com/epmos/faces/DocumentDisplay?id=1228046.1\">1228046.1</a>&#160; (Master Note for TDE)</p>\r\n<p>The software keystore (wallet) is a critical component. It should be backed up in a secure location. If the&#160;software keystore&#160;containing the master keys is lost or if its password is forgotten then the encrypted data will not be accessible anymore. Make sure that the software keystore (wallet) is backed up in the following scenarios:</p>\r\n<ol>\r\n<li>Immediately after creating it.</li>\r\n<li>When regenerating the master key.</li>\r\n<li>When backing up the database. Make sure that the wallet backup is not stored in the same location with the database backup.</li>\r\n<li>Before/After changing the wallet password.</li>\r\n</ol>\r\n<p>SAP BR*Tools (brspace -f mdencr) follow these best practices.</p>\r\n\r\n<p><span style=\"text-decoration: underline;\">Starting&#160;an&#160;Oracle Database Instance with Oracle TDE Enabled</span></p>\r\n<p>When you&#160;startup&#160;a database and&#160;where no auto-login keystore is configured, then you must startup the database first into MOUNT state, open the keystore and then open the database. When you shutdown a databaase with TDE ensure that the keystore containing the TDE master keys is open when you close / shut down the database.</p>\r\n<p>Starting&#160;a single instance without AUTO-LOGIN software keystore:</p>\r\n<p style=\"padding-left: 30px;\">CONNECT / AS SYSDBA<br />SHUTDOWN<br />STARTUP MOUNT;<br />ADMINISTER KEY MANAGEMENT SET KEYSTORE OPEN IDENTIFIED BY keystore_password;<br />ALTER DATABASE OPEN;</p>\r\n<p>Starting a single instance with AUTO-LOGIN software keystore:</p>\r\n<p style=\"padding-left: 30px;\">CONNECT / AS SYSDBA<br />SHUTDOWN<br />STARTUP</p>\r\n<p>For RAC databases, use 'srvctl' instead of 'sqlplus' to start/stop the instance.</p>\r\n<p><span style=\"text-decoration: underline;\">Stopping&#160;an Oracle Database Instance with Oracle TDE Enabled</span></p>\r\n<p>The&#160;procedure to&#160;shutdown&#160;an Oracle database instance where TDE is enabled is the same to shutting down an Oracle database where TDE is not enabled.</p>\r\n<p><span style=\"text-decoration: underline;\">Manually Closing an the TDE Software Keystore</span></p>\r\n<p>Never manually close the TDE software keystore.</p>\r\n<p><span style=\"text-decoration: underline;\">Administrative Privilege SYSKM</span></p>\r\n<p>Oracle Reference: <a target=\"_blank\" href=\"https://docs.oracle.com/en/database/oracle/oracle-database/19/admin/getting-started-with-database-administration.html\">https://docs.oracle.com/en/database/oracle/oracle-database/19/admin/getting-started-with-database-administration.html</a> (Creating and Maintaining a Database Password File)</p>\r\n<p>SAP BR*Tools and SWPM use SYSDBA administrative privilege (Administrator privilege) to configure and manage Transparent Data Encryption. Oracle provides SYSKM administrative privilege (Key Management) for administration of Oracle TDE. You&#160;can create a separate dedicated database user and grant SYSKM administrative privilege to this database user. By default, SAP does not create a dedicated SYSKM administrative user for TDE administration.</p>\r\n\r\n<p>&#160;</p>\r\n<p><strong><a target=\"_blank\" name=\"APPENDIX\"></a>&#65279;Appendix</strong></p>\r\n<p>&#160;</p>\r\n\r\n<p>&#160;</p>\r\n<p><strong><strong>Appendix&#160;B</strong>&#160;- </strong><strong>TDE Administration Commands</strong></p>\r\n<p>SWPM uses the following Oracle SQL commands to install and configure Oracle database with TDE. The following table shows these commands including the corresponding SAP BR*Tools command.</p>\r\n<p>For more information, see Oracle Database 19c, SQL Language Reference,&#160;<a target=\"_blank\" href=\"https://docs.oracle.com/en/database/oracle/oracle-database/19/sqlrf/ADMINISTER-KEY-MANAGEMENT.html#GUID-E5B2746F-19DC-4E94-83EC-A6A5C84A3EA9\">Administer Key Management</a>.</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td>Task</td>\r\n<td>SQL command</td>\r\n<td>BR*Tools command / BRSPACE command</td>\r\n</tr>\r\n<tr>\r\n<td>Configure Software Keystore Location</td>\r\n<td>ALTER SYSTEM SET WALLET_ROOT='/oracle/&lt;DBSID&gt;/orawallet' scope = spfile;</td>\r\n<td>\r\n<p>brspace -f dbparam -a change -p wallet_root</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>Configure Software Keystore Type&#160;</td>\r\n<td>ALTER SYSTEM SET TDE_CONFIGURATION='KEYSTORE_CONFIGURATION=FILE' scope = both;</td>\r\n<td>brspace -f dbparam -a change -p tde_configuration</td>\r\n</tr>\r\n<tr>\r\n<td>Create software keystore (password)</td>\r\n<td>ADMINISTER KEY MANAGEMENT CREATE KEYSTORE 'keystore_location' <br />IDENTIFIED BY <em>\"&lt;software_keystore_password&gt;\"</em>;</td>\r\n<td>brspace -f mdencr -a&#160;create [-p | -P]</td>\r\n</tr>\r\n<tr>\r\n<td>Create software keystore (auto_login)</td>\r\n<td>ADMINISTER KEY MANAGEMENT CREATE AUTO_LOGIN KEYSTORE FROM KEYSTORE '<em>keystore_location</em>' <br />IDENTIFIED BY <em>\"&lt;software_keystore_password&gt;\"</em>;</td>\r\n<td>brspace -f mdencr -a enable -local no [-p|-P]</td>\r\n</tr>\r\n<tr>\r\n<td>Create software keystore (local auto_login)</td>\r\n<td>ADMINISTER KEY MANAGEMENT CREATE LOCAL AUTO_LOGIN KEYSTORE FROM KEYSTORE '<em>keystore_location</em>' <br />IDENTIFIED BY <em>\"&lt;software_keystore_password&gt;\"</em>;</td>\r\n<td>brspace -f mdencr -a enable -local&#160;yes [-p|-P]</td>\r\n</tr>\r\n<tr>\r\n<td>Delete software keystore (auto_login)</td>\r\n<td>\r\n<p>Manual procedure to disable local auto_login or auto_login software keystore:</p>\r\n<ol>\r\n<li>Rename auto-login wallet: $mv cwallet.sso cwallet.sso.bkp</li>\r\n<li>Close wallet: <br />SQL&gt; alter system set wallet close;</li>\r\n<li>Open wallet as a password keystore: <br />SQL&gt; ADMINISTER KEY MANAGEMENT SET KEYSTORE OPEN IDENTIFIED BY password;</li>\r\n</ol></td>\r\n<td>brspace -f mdencr -a disable</td>\r\n</tr>\r\n<tr>\r\n<td>Open software Keystore</td>\r\n<td>\r\n<p>ADMINISTER KEY MANAGEMENT&#160; SET KEYSTORE OPEN FORCE KEYSTORE IDENTIFIED BY <em>\"&lt;software_keystore_password&gt;\"</em>;</p>\r\n</td>\r\n<td>brspace -f mdencr -a open [-p | -P]</td>\r\n</tr>\r\n<tr>\r\n<td>Close software keystore</td>\r\n<td>\r\n<p>ADMINISTER KEY MANAGEMENT&#160; SET KEYSTORE CLOSE&#160;IDENTIFIED BY <em>\"&lt;software_keystore_password&gt;\"</em>;</p>\r\n</td>\r\n<td>brspace -f mdencr -a close [-p | -P]</td>\r\n</tr>\r\n<tr>\r\n<td>Check software keystore status</td>\r\n<td>\r\n<p>SELECT STATUS FROM V$ENCRYPTION_WALLET;</p>\r\n</td>\r\n<td>brspace -f mdencr -a show</td>\r\n</tr>\r\n<tr>\r\n<td>Show encrypted tablespaces</td>\r\n<td>\r\n<p>SELECT * FROM V$ENCRYPTED_TABLESPACES;</p>\r\n</td>\r\n<td>brspace -f mdencr -a listts</td>\r\n</tr>\r\n<tr>\r\n<td>Create TDE Master Encryption Key</td>\r\n<td>\r\n<p>ADMINISTER KEY MANAGEMENT SET ENCRYPTION KEY [USING TAG '&lt;tag&gt;']<br />FORCE KEYSTORE<br />IDENTIFIED BY <em>\"&lt;software_keystore_password&gt;\"</em><br />WITH BACKUP USING '&lt;tag&gt;'</p>\r\n</td>\r\n<td>brspace -f mdencr -a newkey</td>\r\n</tr>\r\n<tr>\r\n<td>Set new keystore password</td>\r\n<td>\r\n<p>ADMINISTER KEY MANAGEMENT ALTER KEYSTORE PASSWORD [FORCE KEYSTORE]<br />IDENTIFIED BY <em>\"&lt;old_keystore_password&gt;\" </em>SET <em>\"&lt;new_keystore_password&gt;\"</em><br />WITH BACKUP [USING '<em>&lt;tag&gt;</em>'];</p>\r\n</td>\r\n<td>brspace -f mdencr -a chpass [-p|-P][-n|-N]</td>\r\n</tr>\r\n<tr>\r\n<td>Create encrypted tablespace</td>\r\n<td>CREATE TABLESPACE &lt;tablespace_name&gt; ... ENCRYPTION [USING 'AES256'] ENCRYPT ...;</td>\r\n<td>TBD</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p>&#160;</p>\r\n<p><strong><strong>Appendix&#160;C</strong>&#160;- Checking&#160;Status of&#160;TDE in a Database</strong></p>\r\n<p>Start SQL*Plus and connect as SYSDBA or SYSKM.</p>\r\n<p><span style=\"text-decoration: underline;\">Check TDE Parameter</span></p>\r\n<p style=\"padding-left: 30px;\">show parameter wallet_root<br />show parameter tde_configuration<br />show parameter encrypt_new_tablespaces</p>\r\n<p><span style=\"text-decoration: underline;\">Check TDE Software Keystore Status</span></p>\r\n<p style=\"padding-left: 30px;\">select wrl_type, wallet_type from v$encryption_wallet;<br />select wrl_parameter, status from v$encryption_wallet;</p>\r\n<p style=\"padding-left: 30px;\">WALLET_TYPE values: UNKNOWN, PASSWORD, AUTOLOGIN, LOCAL_AUTOLOGIN</p>\r\n<p><span style=\"text-decoration: underline;\">Check TDE Software Keystore Encryption Keys</span></p>\r\n<p style=\"padding-left: 30px;\">select key_id, creation_time, activation_time from v$encryption_keys;<br />select count(key_id) from v$encryption_keys;</p>\r\n<p><span style=\"text-decoration: underline;\">Check whether TDE has been implemented in the database</span></p>\r\n<p>Reference: MOS 1541818.1</p>\r\n<p style=\"padding-left: 30px;\">select mkloc from x$kcbdbk;&#160;<br />select decode(mkloc,0,'TDE was never implemented', 'TDE was implemented') \"Database TDE Status\" from x$kcbdbk;</p>\r\n<p>Another method to check whether TDE is activated for a database is to use the following query. You get the information whether the master key has been set (YES) or not (NO) for this container or database. For more information, see <a target=\"_blank\" href=\"https://docs.oracle.com/en/database/oracle/oracle-database/19/refrn/V-DATABASE_KEY_INFO.html\">https://docs.oracle.com/en/database/oracle/oracle-database/19/refrn/V-DATABASE_KEY_INFO.html</a>.</p>\r\n<p style=\"padding-left: 30px;\">select master_activated from v$database_key_info;</p>\r\n<p><span style=\"text-decoration: underline;\">Check&#160;which tablespaces are encrypted</span></p>\r\n<p style=\"padding-left: 30px;\">select ts.ts#, ts.name, ets.encryptedts, ets.encryptionalg, ets.status from v$tablespace ts, v$encrypted_tablespaces ets where ts.ts# = ets.ts#;</p>\r\n<p><span style=\"text-decoration: underline;\">Check Default&#160;Database Default Key</span></p>\r\n<p>Reference: <a target=\"_blank\" href=\"https://docs.oracle.com/en/database/oracle/oracle-database/19/refrn/V-DATABASE_KEY_INFO.html\">https://docs.oracle.com/en/database/oracle/oracle-database/19/refrn/V-DATABASE_KEY_INFO.html</a></p>\r\n<p style=\"padding-left: 30px;\">select * from v$database_key_info;</p>\r\n<p><strong><strong>Appendix&#160;D</strong>&#160;-&#160;Configure TDE in a database</strong></p>\r\n<p>The following commands are used to configure TDE in an Oracle database.</p>\r\n<p>Prerequisites:</p>\r\n<ul>\r\n<li>Oracle Database 12c or higher (18c or higher recommended)</li>\r\n<li>TDE has never been configured in this database before</li>\r\n</ul>\r\n<p>Reference: <a target=\"_blank\" href=\"https://docs.oracle.com/en/database/oracle/oracle-database/19/asoag/configuring-transparent-data-encryption.html\">https://docs.oracle.com/en/database/oracle/oracle-database/19/asoag/configuring-transparent-data-encryption.html</a></p>\r\n<p><span style=\"text-decoration: underline;\">TDE Configuration Testing</span></p>\r\n<p>Once you have configured TDE for your database, you cannot delete the TDE software keystore. Therefore, if you are testing the TDE configuration procedure multiple times you must backup your database before.</p>\r\n<ol>\r\n<li>Backup your database</li>\r\n<li>Configure TDE</li>\r\n<li>Encrypt tablespaces</li>\r\n<li>If you are done with this test, you restore the database backup, then you can run another TDE configuration procedure test.</li>\r\n</ol>\r\n<p>Alternatively you can create a guaranteed restore point.</p>\r\n<ol>\r\n<li>Create a GRP</li>\r\n<li>Configure TDE</li>\r\n<li>Encrypt tablespaces</li>\r\n<li>If you are done with this test, flashback your database back, then you can run another TDE configuration procedure test.</li>\r\n</ol>\r\n<p><span style=\"text-decoration: underline;\">Configure Parameter</span></p>\r\n<p>SQL&gt; connect / as sysdba<br />SQL&gt; select * from v$encryption_wallet;</p>\r\n<p>SQL&gt; REM Configure WALLET_ROOT<br />SQL&gt; alter system set wallet_root='&lt;SAPDATA_HOME&gt;/orawallet';</p>\r\n<p>SQL&gt; REM Restart instance to enable WALLET_ROOT<br />SQL&gt; shutdown immediate<br />SQL&gt; startup<br />SQL&gt; show parameter wallet_root<br />SQL&gt; select * from v$encryption_wallet;</p>\r\n<p>SQL&gt; REM Configure TDE_CONFIGURATION<br />SQL&gt; ALTER SYSTEM SET TDE_CONFIGURATION=\"KEYSTORE_CONFIGURATION=FILE\" SCOPE=BOTH SID='*';<br />SQL&gt; select * from v$encryption_wallet; -- now you see the wallet_root value in v$encryption_wallet.wrl_parameter<br /><br />SQL&gt; REM Show parameter<br />SQL&gt; show parameter wallet_root<br />SQL&gt; show parameter tde_configuration</p>\r\n<p><span style=\"text-decoration: underline;\">Create TDE Software Keystore</span></p>\r\n<p>Explanation:</p>\r\n<ul>\r\n<li>if parameter 'wallet_root' is set, you don't need to specify the &lt;key_store_location&gt; in the AKM commands</li>\r\n</ul>\r\n<p>SQL&gt; select * from v$encryption_wallet;<br />SQL&gt; select * from v$encryption_keys;<br />SQL&gt; ADMINISTER KEY MANAGEMENT CREATE KEYSTORE [&lt;keystore_location_optional&gt;] IDENTIFIED BY \"&lt;keystore_password&gt;\";<br />SQL&gt; ADMINISTER KEY MANAGEMENT CREATE AUTO_LOGIN KEYSTORE [&lt;keystore_location_optional&gt;] FROM KEYSTORE&#160;IDENTIFIED BY \"&lt;keystore_password&gt;\";<br />SQL&gt; select * from v$encryption_wallet; -- OPEN_NO_MASTER_KEY, LOCAL_AUTOLOGIN<br />SQL&gt; select * from v$encryption_keys;</p>\r\n<p><span style=\"text-decoration: underline;\">Change Password for TDE Software Keystore (optional)</span></p>\r\n<p>SQL&gt; ADMINISTER KEY MANAGEMENT ALTER KEYSTORE PASSWORD IDENTIFIED BY old_keystore_password SET new_keystore_password WITH BACKUP USING 'pwd_change';</p>\r\n<p><span style=\"text-decoration: underline;\">Create TDE Master Encryption Key</span></p>\r\n<p>Explanation:</p>\r\n<ul>\r\n<li>'with backup' clause must be used for password-protected keystores (see <a target=\"_blank\" href=\"https://docs.oracle.com/en/database/oracle/oracle-database/19/asoag/configuring-transparent-data-encryption.html\">https://docs.oracle.com/en/database/oracle/oracle-database/19/asoag/configuring-transparent-data-encryption.html</a>)</li>\r\n<li>'force keystore' clause&#160;should be&#160;included if the keystore is closed&#160;(see <a target=\"_blank\" href=\"https://docs.oracle.com/en/database/oracle/oracle-database/19/asoag/configuring-transparent-data-encryption.html\">https://docs.oracle.com/en/database/oracle/oracle-database/19/asoag/configuring-transparent-data-encryption.html</a>)</li>\r\n<li>Database must be opened READ WRITE</li>\r\n</ul>\r\n<p>SQL&gt; select open_mode from v$database; -- must be READ WRITE<br />SQL&gt; select decode(mkloc,0,'TDE was never implemented', 'TDE was implemented') \"Database TDE Status\" from x$kcbdbk; -- optional<br />SQL&gt; ADMINISTER KEY MANAGEMENT SET ENCRYPTION KEY FORCE KEYSTORE IDENTIFIED BY&#160;\"&lt;keystore_password&gt;\" WITH BACKUP;<br />SQL&gt; select decode(mkloc,0,'TDE was never implemented', 'TDE was implemented') \"Database TDE Status\" from x$kcbdbk; -- optional</p>\r\n<p>From now on TDE is implemented in the database. You must never remove the TDE software keystore from the database again. Now you can start to encrypt tablespaces.</p>\r\n<p><strong><strong>Appendix&#160;E</strong>&#160;- Deconfigure TDE in a database / Decrypting the Database</strong></p>\r\n<p>You can not deconfigure TDE in an Oracle database where TDE was configured before.</p>\r\n<p><strong><strong>Appendix&#160;F</strong>&#160;- SQLNET.ENCRYPTION_WALLET_LOCATION</strong></p>\r\n<p>The SQLNET.ORA&#160;parameter <code class=\"codeph\">SQLNET.ENCRYPTION_WALLET_LOCATION </code>is deprecated. For more information, see <a target=\"_blank\" href=\"https://docs.oracle.com/en/database/oracle/oracle-database/19/refrn/WALLET_ROOT.html\">https://docs.oracle.com/en/database/oracle/oracle-database/19/refrn/WALLET_ROOT.html</a>.</p>\r\n<ul>\r\n<li>For Oracle Database 18c or higher SAP&#160;recommends to migrate to the new initialization parameter 'wallet_root'.</li>\r\n<li>SWPM does not support TDE configurations&#160;with <code class=\"codeph\">SQLNET.ENCRYPTION_WALLET_LOCATION</code>.&#160;SWPM uses the new database initialization parameters WALLET_ROOT and TDE_CONFIGURATION which are available&#160;as of&#160;18c. For more information about these new parameters, see <a target=\"_blank\" href=\"https://docs.oracle.com/en/database/oracle/oracle-database/18/asoag/release-changes.html\">Oracle Database 18c, Advanced Security Guide</a>.</li>\r\n<li>For more information see also <a target=\"_blank\" href=\"https://docs.oracle.com/en/database/oracle/oracle-database/19/asoag/using-sqlnet-ora-to-configure-tde-keystores.html\">https://docs.oracle.com/en/database/oracle/oracle-database/19/asoag/using-sqlnet-ora-to-configure-tde-keystores.html</a>.</li>\r\n</ul>\r\n<p><strong><strong>Appendix&#160;G</strong>&#160;- TDE FAQs</strong></p>\r\n<p>Reference: Oracle Support Document <a target=\"_blank\" href=\"https://support.oracle.com/epmos/faces/DocumentDisplay?id=2253348.1\">2253348.1</a> (TDE 12c : Frequently Asked Questions) can be found at: <a target=\"_blank\" href=\"https://support.oracle.com/epmos/faces/DocumentDisplay?id=2253348.1\">https://support.oracle.com/epmos/faces/DocumentDisplay?id=2253348.1</a></p>\r\n<p><span style=\"text-decoration: underline;\">SYSKM administrative privilege</span></p>\r\n<p>This is a new privilege introduced in 12c. It enables the user to manage keystore related operations. SYSDBA privilege is not mandatory to manage the keystore.</p>\r\n<p><span style=\"text-decoration: underline;\">Can we use 'orapki' to manage the TDE Software Keystore?</span></p>\r\n<p>For SAP installations you should not use&#160;orapki for TDE keystore operations. For more information, see Oracle Support Document <a target=\"_blank\" href=\"https://support.oracle.com/epmos/faces/DocumentDisplay?id=2253348.1\">2253348.1</a>.</p>\r\n<p><span style=\"text-decoration: underline;\">Can we use 'alter system set encryption key' in 12c, 18c, 19c or higher?</span></p>\r\n<p>For SAP installations you should not use SQL commands from previous Oracle releases. You should use only AKM SQL commands.</p>\r\n<p><span style=\"text-decoration: underline;\">Can a TDE keystore be deleted and recreated in 12c or higher?</span></p>\r\n<p>Once created, the TDE keystores&#160;must never be deleted. This is true even when there are no encrypted objects in the database.</p>\r\n<p><span style=\"text-decoration: underline;\">Can&#160;existing tablespaces and databases be encrypted and decrypted?</span></p>\r\n<p>Yes. In 12.2, Oracle&#160;can perform offline and online encryption conversion of&#160;tablespaces including Oracle-supplied tablespaces (SYSTEM, SYSAUX, TEMP, UNDO). For more information, see&#160;MOS 2255611.1.</p>\r\n<p><span style=\"text-decoration: underline;\">How to copy the wallet file from the primary to the standby server if the wallet is present on ASM?</span></p>\r\n<p>See MOS 2251874.1.</p>\r\n<p><span style=\"text-decoration: underline;\">Can we delete the old master keys from the TDE keystore?</span></p>\r\n<p>No. There is no way to delete old master keys from the existing keystore. For more information, see How to delete old master keys from 12c TDE keystore doc id 2216279.1.</p>\r\n<p><span style=\"text-decoration: underline;\">Will exporting the master key from a keystore delete the master key(s) from it?</span></p>\r\n<p>Keystore export operation copies the master keys and does not remove them from the original keystore.</p>\r\n<p><span style=\"text-decoration: underline;\">How can we check whether datafiles are encrypted?</span></p>\r\n<p><span class=\"kmContent\" style=\"font-size: small; color: black;\">Reference: </span></p>\r\n<ul>\r\n<li><span class=\"kmContent\" style=\"font-size: small; color: black;\">MOS <a target=\"_blank\" href=\"https://support.oracle.com/epmos/faces/DocumentDisplay?id=2544479.1\">2544479.1</a>&#160;(How to check if the TDE datafiles are encrypted or unencrypted)</span></li>\r\n<li><span class=\"kmContent\" style=\"font-size: small; color: black;\"><span class=\"kmContent\" id=\"kmPgTpl:r1:ot71\" style=\"font-size: small; color: black;\"><a target=\"_blank\" href=\"https://www.oracle.com/technetwork/database/availability/tde-conversion-dg-3045460.pdf\">https://www.oracle.com/technetwork/database/availability/tde-conversion-dg-3045460.pdf</a></span></span></li>\r\n</ul>\r\n<p><span class=\"kmContent\" id=\"kmPgTpl:r1:ot71\" style=\"font-size: small; color: black;\">To check if TDE datafiles are encrypted, use DBVERIFY to confirm used blocks are encrypted. For more information, see MOS <a target=\"_blank\" href=\"https://support.oracle.com/epmos/faces/DocumentDisplay?id=2544479.1\">2544479.1</a>.</span></p>\r\n<p><span class=\"kmContent\" style=\"font-size: small; color: black;\">Unencrypted Datafile:<br /></span>$ dbv file=&lt;file&gt; USERID=&lt;user&gt;/&lt;password&gt;<br />...<br />Total Pages Processed (Data) : 26&#160; ==&gt; Unencrypted Data<br />Total Pages Processed (Index): 2&#160;&#160; ==&gt; Unencrypted Index<br />Total Pages Encrypted&#160;&#160;&#160;&#160;&#160;&#160;&#160; : 0&#160;&#160; ==&gt; No encrypted pages<br />...</p>\r\n<p>Encrypted Datafile:<br />$ dbv file=&lt;file&gt; USERID=&lt;user&gt;/&lt;password&gt;<br />...<br />Total Pages Processed (Data) : 0&#160;&#160; ==&gt; No Unencrypted Data<br />Total Pages Processed (Index): 0&#160;&#160; ==&gt; No Unencrypted Index<br />Total Pages Encrypted&#160;&#160;&#160;&#160;&#160;&#160;&#160; : 28&#160; ==&gt; Encrypted Pages (Data and Index)<br />...</p>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "BC-DB-ORA-SEC (Security Messages)"}, {"Key": "Database System", "Value": "ORACLE"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON> (C5000979)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (C5014980)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0002591575/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002591575/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002591575/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002591575/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002591575/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002591575/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002591575/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002591575/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002591575/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": [{"FileName": "twp-transparent-data-encryption-bes-130696.pdf", "FileSize": "573", "MimeType": "application/pdf", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=002075125800000334972018&iv_version=0020&iv_guid=00109B36D6621ED8AB8B460259A720C8"}]}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "974876", "RefComponent": "BC-DB-ORA", "RefTitle": "Oracle Transparent Data Encryption (TDE)", "RefUrl": "/notes/974876"}, {"RefNumber": "2799991", "RefComponent": "BC-DB-ORA", "RefTitle": "TDE Encryption Conversions for Tablespaces and Databases", "RefUrl": "/notes/2799991"}, {"RefNumber": "2485122", "RefComponent": "BC-DB-ORA", "RefTitle": "Support for Oracle Transparent Data Encryption (TDE) in SWPM", "RefUrl": "/notes/2485122"}, {"RefNumber": "2218115", "RefComponent": "BC-DB-ORA", "RefTitle": "Oracle Database Vault for SAP NetWeaver", "RefUrl": "/notes/2218115"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2485122", "RefComponent": "BC-DB-ORA", "RefTitle": "Support for Oracle Transparent Data Encryption (TDE) in SWPM", "RefUrl": "/notes/2485122 "}, {"RefNumber": "2799991", "RefComponent": "BC-DB-ORA", "RefTitle": "TDE Encryption Conversions for Tablespaces and Databases", "RefUrl": "/notes/2799991 "}, {"RefNumber": "974876", "RefComponent": "BC-DB-ORA", "RefTitle": "Oracle Transparent Data Encryption (TDE)", "RefUrl": "/notes/974876 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": []}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}