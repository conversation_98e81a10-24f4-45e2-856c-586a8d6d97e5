{"Request": {"Number": "890976", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 316, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000005103122017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000890976?language=E&token=67B26FDF7BD5F8975A5842E1A70B1B14"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000890976", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000890976/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "890976"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 4}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Program error"}, "Priority": {"_label": "Priority", "value": "Correction with medium priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "19.12.2005"}, "SAPComponentKey": {"_label": "Component", "value": "FI-AA-AA-E"}, "SAPComponentKeyText": {"_label": "Component", "value": "Periodic Posting"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Financial Accounting", "value": "FI", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'FI*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Asset Accounting", "value": "FI-AA", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'FI-AA*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Basic Functions", "value": "FI-AA-AA", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'FI-AA-AA*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Periodic Posting", "value": "FI-AA-AA-E", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'FI-AA-AA-E*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "890976 - Converting closing report to internal doc number assgnmnt"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>When you implement this note, the depreciation posting run (RAPOST2000) and the periodic posting run for APC values (RAPERB2000) for release mySAP ERP 2005 is converted to use document number ranges that use an internal document number assignment.<br /></p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>RABUCH00, RABUCH30, RAPOST2000, RAPOST2001, depreciation run, document type, RAPERB2000, AA776, AA 776, AC 196, AC196, AC 197, AC197, APERB 007, APERB007, APERB 017, APERB017, APERB 010, APERB010, APERB 011 , APERB011, APERB 050, APERB050, APERB 051, APERB051, BADI_FIAA_DOCLINE<br /></p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p><B>The conversion is necessary for the following reasons: </B></p> <OL>1. As a result of the changes from Note 841449, a consistent document &#x00A0;&#x00A0;number assignment was no longer ensured in the general ledger (when  you use the new general ledger in connection with ledger groups).</OL> <OL>2. When you use the Business Add-In 'BADI_FIAA_DOCLINE', the document type and therefore the document number range can be replaced individually for each depreciation area. This indirectly enables the function to use document number ranges for the depreciation run individually per depreciation area.</OL> <p><br /><B>Advantages and improvements resulting from the conversion:</B></p> <OL>1. The internal document number assignment reduces the possibility of errors relating to the document number handling. The following examples continuously resulted in problems in customer systems and could often only be corrected by SAP support or SAP remote consulting.</OL> <OL><OL>a) Depreciation posting run: You can now manually post documents to the document interval of the document type of the depreciation run without the next depreciation posting run terminating with error AA728 (Documents manually posted within dep doc number range).</OL></OL> <OL><OL>b) Periodic APC values posting report: if the document number range of the document type was changed for the periodic APC values posting, the log table TABAS had to be manually adjusted by ABAP so that the next posting run for APC values could determine the correct start document number.</OL></OL> <OL><OL>c) Direct update: From now on, the direct update posts the documents with the same document type as the periodic APC values posting report. That standardizes the document reporting and the original documents, which were posted with the external document number assignment, can still be updated directly without having to uninstall the corresponding BADI.</OL></OL> <OL>2. In Customizing, the same document type can now be defined for the depreciation posting run and for the periodic posting run for APC values. This means that, if you want to, you can identify the periodic G/L documents with a single document type. The document type for cross-company-code cost accounting in the foreign company code can now also be identical to the document type of the depreciation posting run that is \"normally\" used.<br /><U>Important:</U> However, for the periodic posting reports, SAP still recommends only using dedicated document types with dedicated number range intervals that are only used by the corresponding report. This simplifies the subsequent reconciliation of the general ledger and the subsidiary ledger for the periodically posted documents.</OL><p><br /><B>Report-related changes that result from this note:</B></p> <UL><LI>The G/L document number is no longer displayed on the output list of the depreciation posting run, instead only the reference document number - with which all accounting documents were posted - is displayed. The corresponding general ledger document number(s) are logged in the job log and also in the Schedule Manager (Transaction ARMO). You can then navigate to the accounting documents via the depreciation log.</LI></UL> <UL><LI>The created accounting documents can also be found by means of Transaction FB03. To do this, you must call the \"Document List\" function on the initial screen of the document display. The Company Code and Fiscal Year fields should then be entered individually. You can also enter 'AMDP' as a Reference Transaction. To find all general ledger documents for a certain reference document number, enter the following selection criterion in the \"Reference Key\" field:<br />Reference key = Reference document number + company code + fiscal year<br />As the company code and fiscal year, you must enter the parameters with which the depreciation posting run was executed.<br />Example:<br />- Reference document number from the depreciation posting run  **********<br />- Company code    &#x00A0;&#x00A0;1000<br />- Fiscal year     &#x00A0;&#x00A0;2005<br />=&gt; Reference key = **********10002005</LI></UL> <UL><LI>In the periodic APC values posting report, the general ledger document number is also set in the spool list because there is no log report for this report. The created G/L documents can also be found here by executing Transaction FB03 with the reference key.</LI></UL> <UL><LI>In the test run during the \"ordinary posting run\", the periodic APC values posting report only displays temporary (reference) document numbers. These begin with '$' to clearly indicate that these will not be the document numbers later specified in the update run. In the update run, the actual reference document numbers used are displayed in the list. The actual reference document numbers used are also displayed in the \"Restart Posting Run\", since these were already reserved in the update run.</LI></UL> <UL><LI>Both reports now display the created reference document numbers and the G/L document numbers in the job log and during the logging in the Schedule Monitor (Transaction ARMO). This data then also remains in the Schedule Manager if job log and corresponding spool lists no longer exist.</LI></UL> <p><br /><br /><B>Logic for Customizing document types in MySAPErp 2005: </B></p> <OL>1. The document type for the depreciation posting (IMG Asset Accounting -&gt; Integration with the General Ledger -&gt; Post Depreciation to the General Ledger -&gt; Specify Document Type for Posting of Depreciation)<br /><br />he document type for the depreciation posting must always be maintained in the IMG. The assigned document number range may only have internal document number assignment.</OL> <OL>2. Document type for the depreciation posting (IMG Asset Accounting -&gt; Integration with the General Ledger -&gt; Post Depreciation into the general ledger -&gt; Define Document Type for posting the Depreciation)<br /><br />The document type for cross-company code postings can be maintained, but can also be left initial (empty). In this case, the depreciation posting run takes the regular depreciation posting run document type to post to the foreign company code. A document type for cross-company code postings must use an internal document number assignment. SAP discourages the use of external document number ranges in the foreign company code, although doing so can work under certain circumstances.</OL> <OL>3. Document type for periodic postings (IMG Asset Accounting -&gt; Integration with the General Ledger -&gt; Post APC Values periodically to the General Ledger -&gt; Define Document Type for APC Periodic Postings)<br /><br />The document type for the periodic APC postings must be maintained in the IMG if at least one depreciation area from the assigned chart of depreciation periodically posts asset balances to the general ledger. If no area posts periodically, the document type may be empty.<br /><br />The assigned document number range may only have internal document number assignment.</OL> <OL>4. The consistency check reports in the Customizing check the above logic and issue corresponding messages if there are differences.</OL> <OL>5. Up to now, it was not possible to subsequently change the depreciation document type in the Customizing if depreciation posting runs had already occurred for the company code. Message AC 086 (You cannot change document type since depreciation has been posted) was issued in these depreciation posting runs. You can change the depreciation document type at any time in the future as this note eliminates the previous restriction.</OL> <OL>6. <B>Note: </B>By means of the Business-AddIn BADI_FIAA_DOCLINES, the document types for the runtime in the line item generator of the Asset Accounting can be replaced individually. The CHANGE_DOC_TYPE method, intended for this, can be freely swapped using a separate implementation. For example, you can use this function to change the individual depreciation document type to be used for each depreciation area, so that a document interval can be reached continuously in the general ledger for individual areas. If you would like to use this function, contact SAP remote consulting or your local consultant.</OL> <p></p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>The changes are delivered in the standard in the Support Package mentioned in this note.<br /><br /><B>Note on MySAP ERP 2004:</B><br /><B>Caution:</B> When you implement this note, the function is only prepared for the depreciation posting run in MySAP ERP 2004 in the standard (in the background). This is required in order to later activate the internal document number assignment through a modification and also to keep the modifications to a minimum. For the depreciation posting run in MySAP ERP 2004, the internal document number assignment can be activated by means of the changes from <B>Note 903118</B>. Contact SAP support to activate this function. The logic for the customizing of the document types in MySAP ERP 2004 is also described in Note 903118.<br /><br />If you want to implement the changes manually, proceed as follows:<br /><br /><B>Implement the following changes in MySAP ERP 2005 and MySAP ERP 2004:</B><br /></p> <OL>1. Use Transaction SE91 to create and change messages: (Note on maintaining long texts: Insert placeholders &lt;(&gt;&amp;&lt;)&gt;V1&lt;(&gt;&amp;&lt;)&gt; and &lt;(&gt;&amp;&lt;)&gt;V2&lt;(&gt;&amp;&lt;)&gt; in the long texts by using \"Edit -&gt; Command -&gt; Insert Command -&gt; Symbols\" in the long text of the message.)</OL> <OL><OL>a) Create: Message AC196 Short text: Number range &lt;(&gt;&amp;&lt;)&gt; in company code &lt;(&gt;&amp;&lt;)&gt; for document type &lt;(&gt;&amp;&lt;)&gt; must be defined internallyDeactivate the \"Self-explanatory\" indicator.<br /><br />Maintain the long text:<br />Diagnosis<br />The number range for the document type to post the depreciation is not defined internally. Only internal number assignment is possible for this transaction.<br />System responses<br />Procedure <br />Either correct the document type or change the definition of the number range.</OL></OL> <OL><OL>b) Create: Message AC197 Short text: Document type &lt;(&gt;&amp;&lt;)&gt; does not exist for periodic APC postings in company code &lt;(&gt;&amp;&lt;)&gt;Deactivate the \"Self-explanatory\" indicator.<br /><br />Maintain the long text:<br />Diagnosis<br />Document type does not exist for periodic APC postings in company code .<br />System responses<br />The document type you entered to post the periodic APC postings is not defined.<br />Procedure<br />Correct the entries you have made or define the document type in the Customizing of the financial accounting.</OL></OL> <OL><OL>c) Create: Message AA 776 Short text: Create document number range &lt;(&gt;&amp;&lt;)&gt; with internal number assignmentDeactivate the \"Self-explanatory\" indicator.<br /><br />Maintain the long text:<br />Diagnosis <br />The processing had to be terminated because the document number range &amp;V1&amp; for year &amp;V2&amp; was set up with an external number assignment. <br />System responses<br />For the periodic depreciation postings, you have to create a document number range with an internal document number because the document numbers are assigned from Financial Accounting.<br />Procedure <br />By means of the Asset Accounting Customizing, change number range &amp;V1&amp; from external number assignment to internal number assignment. See also Note 890976.</OL></OL> <OL><OL>d) Create message APERB050 Short text: Create document number range &lt;(&gt;&amp;&lt;)&gt;1 with internal number assignmentDeactivate the \"Self-explanatory\" indicator.<br /><br />Maintain the long text:<br />Diagnosis<br />The processing had to be terminated since the document number range was set up with an external number assignment.<br />System responses<br />For periodic APC postings, you have to create a document number range with an internal document because the document numbers are assigned from Financial Accounting and not from the posting program of Asset Accounting.<br />Procedure<br />Change the number ranges from external to internal number assignment using Customizing for Asset Accounting.<br />Procedure for the system administration</OL></OL> <OL><OL>e) Create: Message APERB051 Reference document &lt;(&gt;&amp;&lt;)&gt;1 is created in company code &lt;(&gt;&amp;&lt;)&gt;2 for fiscal year &lt;(&gt;&amp;&lt;)&gt;3.Activate the \"Self-explanatory\" indicator.<br /></OL></OL> <OL><OL>f) Change: Message APERB010 Short text: G/L document &lt;(&gt;&amp;&lt;)&gt;1 posted in company code &lt;(&gt;&amp;&lt;)&gt;2</OL></OL> <OL><OL>g) Change: Message APERB011 Short text: Ledger document &lt;(&gt;&amp;&lt;)&gt;1 is created for accounting principle &lt;(&gt;&amp;&lt;)&gt;2 in year &lt;(&gt;&amp;&lt;)&gt;3</OL></OL> <OL>2. Use Transaction SE11 to change objects from the Data Dictionary:</OL> <OL><OL>a) Change: Structure FIAA_SALVTAB_RAPOST<br />Rename field 'BELNR' to 'AWREF'. Also change the component type from BELNR_D to AWREF.</OL></OL> <OL><OL>b) <B>Perform the following step in MySAPErp2005 only:</B><br />Change: FIAA_ALV_RAPERB<br />Rename field 'PBELNR' to 'AWREF'. Also change the component type from BELNR_RAPBER to AWREF.<br /></OL></OL> <OL>3. Use Transaction SE37 to enhance the function module interface:</OL> <OL><OL>a) Function: Add an import parameter to AM_NUMMERNINTERVALL_ANLEGEN<br />Parameter Type spec. Associated type<br />I_NUMKRS LIKE  NRIV-NRRANGENR<br />Do not activate the \"Optional\" and \"Pass Value\" checkboxes.</OL></OL> <OL><OL>b) Function: FIAA_DOCUMENT_TYPE_CHECK<br />Activate the \"Optional\" indicator on the \"Import\" tabstrip for all parameters on the interface.</OL></OL> <OL><OL>c) <B>Only carry out in MySAP ERP 2004:</B><br />Create function module: FIAA_DOCUMENT_TYPE_CHECK_DIFF. Use the following parameters:</OL></OL> <UL><LI>Function module FIAA_DOCUMENT_TYPE_CHECK_DIFF</LI></UL> <UL><LI>Function group ACHK</LI></UL> <UL><LI>Short Text Check whether document types for periodic reports are different<br />   </LI></UL> <UL><LI>Import parameter</LI></UL> <UL><UL><LI>I_BUKRS  LIKE T093C-BUKRS</LI></UL></UL> <UL><UL><LI>I_DOC_TYPE_DEPR LIKE T093C-AFBLRT</LI></UL></UL> <UL><UL><LI>I_DOC_TYPE_PER LIKE T093C-APERBLRT</LI></UL></UL> <UL><LI>Exceptions</LI></UL> <UL><UL><LI>DOCUMENT_TYPES_IDENTICAL</LI></UL></UL> <OL>4. Implement the source code corrections.</OL> <p><br /><br /><br /><B>Changes to Customizing:</B><br />After you implement these changes, a document type with internal number assignment must be defined in the Customizing for the depreciation run and the periodic posting run to APC values.<br /><br /><br /><br /><br /><B>Supplement: </B> If you use the \"simplified\" depreciation run <B>RAPOST00</B>, contact the SAP Development Support. To do this, create a \"Low\" priority customer message. You can find more detailed information about RAPOST00 in Notes 127887 and 183910.</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D033258)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D033258)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "J", "TranslationLanguage": "日本語", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000890976/J"}, {"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000890976/D"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000890976/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000890976/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000890976/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000890976/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000890976/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000890976/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000890976/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "961410", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info. on upgrading to SAP ECC 6.0 SR2 ABAP", "RefUrl": "/notes/961410"}, {"RefNumber": "924089", "RefComponent": "FI-AA-AA-E", "RefTitle": "Display of documents in ledger group without leading ledger", "RefUrl": "/notes/924089"}, {"RefNumber": "913971", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info. on upgrading to SAP ERP Central Component 6.0 SR1", "RefUrl": "/notes/913971"}, {"RefNumber": "909753", "RefComponent": "FI-AA-AA-E", "RefTitle": "RAPERB2000: Job status canceled with message BL 191", "RefUrl": "/notes/909753"}, {"RefNumber": "867950", "RefComponent": "FI-GL-GL", "RefTitle": "Incorrect number range when posting to non-leading ledger", "RefUrl": "/notes/867950"}, {"RefNumber": "841449", "RefComponent": "FI-GL", "RefTitle": "Gap-free document number assignment", "RefUrl": "/notes/841449"}, {"RefNumber": "826092", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info. on upgrading to SAP ERP Central Component 6.0", "RefUrl": "/notes/826092"}, {"RefNumber": "648606", "RefComponent": "FI-AA-AA-E", "RefTitle": "RAPOST2000: Cross-company code cost accounting", "RefUrl": "/notes/648606"}, {"RefNumber": "183910", "RefComponent": "FI-AA", "RefTitle": "Differences RAPOST00 / RABUCH00", "RefUrl": "/notes/183910"}, {"RefNumber": "1292069", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info. on upgrading to SAP ERP 6.0 EHP4 SR1 ABAP", "RefUrl": "/notes/1292069"}, {"RefNumber": "127887", "RefComponent": "FI-AA", "RefTitle": "Performance measures for large datasets", "RefUrl": "/notes/127887"}, {"RefNumber": "1156968", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info. on upgrading to EHP 4 for SAP ERP 6.0 ABAP", "RefUrl": "/notes/1156968"}, {"RefNumber": "1088904", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info. on upgrading to SAP ECC 6.0 SR3 ABAP", "RefUrl": "/notes/1088904"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2433076", "RefComponent": "FI-AA-AA", "RefTitle": "Error NR600 in transaction SNRO", "RefUrl": "/notes/2433076 "}, {"RefNumber": "2550006", "RefComponent": "FI-AA-AA", "RefTitle": "When running T-code : AFAB to perform depreciation posting, error AAPO519 occurs", "RefUrl": "/notes/2550006 "}, {"RefNumber": "826092", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info. on upgrading to SAP ERP Central Component 6.0", "RefUrl": "/notes/826092 "}, {"RefNumber": "1088904", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info. on upgrading to SAP ECC 6.0 SR3 ABAP", "RefUrl": "/notes/1088904 "}, {"RefNumber": "903118", "RefComponent": "FI-AA-AA-E", "RefTitle": "Changeover balancing report to internal doc number assgmt", "RefUrl": "/notes/903118 "}, {"RefNumber": "1156968", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info. on upgrading to EHP 4 for SAP ERP 6.0 ABAP", "RefUrl": "/notes/1156968 "}, {"RefNumber": "1292069", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info. on upgrading to SAP ERP 6.0 EHP4 SR1 ABAP", "RefUrl": "/notes/1292069 "}, {"RefNumber": "648606", "RefComponent": "FI-AA-AA-E", "RefTitle": "RAPOST2000: Cross-company code cost accounting", "RefUrl": "/notes/648606 "}, {"RefNumber": "913971", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info. on upgrading to SAP ERP Central Component 6.0 SR1", "RefUrl": "/notes/913971 "}, {"RefNumber": "961410", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info. on upgrading to SAP ECC 6.0 SR2 ABAP", "RefUrl": "/notes/961410 "}, {"RefNumber": "924089", "RefComponent": "FI-AA-AA-E", "RefTitle": "Display of documents in ledger group without leading ledger", "RefUrl": "/notes/924089 "}, {"RefNumber": "909753", "RefComponent": "FI-AA-AA-E", "RefTitle": "RAPERB2000: Job status canceled with message BL 191", "RefUrl": "/notes/909753 "}, {"RefNumber": "867950", "RefComponent": "FI-GL-GL", "RefTitle": "Incorrect number range when posting to non-leading ledger", "RefUrl": "/notes/867950 "}, {"RefNumber": "841449", "RefComponent": "FI-GL", "RefTitle": "Gap-free document number assignment", "RefUrl": "/notes/841449 "}, {"RefNumber": "127887", "RefComponent": "FI-AA", "RefTitle": "Performance measures for large datasets", "RefUrl": "/notes/127887 "}, {"RefNumber": "183910", "RefComponent": "FI-AA", "RefTitle": "Differences RAPOST00 / RABUCH00", "RefUrl": "/notes/183910 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_APPL", "From": "500", "To": "500", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "600", "To": "600", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "SAP_APPL 500", "SupportPackage": "SAPKH50012", "URL": "/supportpackage/SAPKH50012"}, {"SoftwareComponentVersion": "SAP_APPL 600", "SupportPackage": "SAPKH60003", "URL": "/supportpackage/SAPKH60003"}, {"SoftwareComponentVersion": "SAP_APPL 600", "SupportPackage": "SAPKH60005", "URL": "/supportpackage/SAPKH60005"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": [{"SoftwareComponent": "SAP_APPL", "NumberOfCorrin": 2, "URL": "/corrins/0000890976/1"}]}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 2, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 12, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": [{"SoftwareComponent": "SAP_APPL", "ValidFrom": "470", "ValidTo": "500", "Number": "690454 ", "URL": "/notes/690454 ", "Title": "RAPOST2000 stays in in process status", "Component": "FI-AA-AA-E"}, {"SoftwareComponent": "SAP_APPL", "ValidFrom": "470", "ValidTo": "500", "Number": "801356 ", "URL": "/notes/801356 ", "Title": "RAPOST2000: Error without message during document creation", "Component": "FI-AA-AA-E"}, {"SoftwareComponent": "SAP_APPL", "ValidFrom": "470", "ValidTo": "600", "Number": "793326 ", "URL": "/notes/793326 ", "Title": "RAPOST2001 document display", "Component": "FI-AA-AA-E"}, {"SoftwareComponent": "SAP_APPL", "ValidFrom": "470", "ValidTo": "600", "Number": "865511 ", "URL": "/notes/865511 ", "Title": "RAPOST2000: incorrect CO information in ANLP", "Component": "FI-AA-AA-E"}, {"SoftwareComponent": "SAP_APPL", "ValidFrom": "500", "ValidTo": "500", "Number": "722691 ", "URL": "/notes/722691 ", "Title": "RAPOST2000: Improving error analysis", "Component": "FI-AA-AA-E"}, {"SoftwareComponent": "SAP_APPL", "ValidFrom": "500", "ValidTo": "500", "Number": "752329 ", "URL": "/notes/752329 ", "Title": "Adjustment of reconciliation reports to new general ledger", "Component": "FI-AA-AA-E"}, {"SoftwareComponent": "SAP_APPL", "ValidFrom": "500", "ValidTo": "500", "Number": "758279 ", "URL": "/notes/758279 ", "Title": "Parallel currency with derived depreciation areas", "Component": "FI-AA-AA-E"}, {"SoftwareComponent": "SAP_APPL", "ValidFrom": "500", "ValidTo": "500", "Number": "788380 ", "URL": "/notes/788380 ", "Title": "Messages AC068 or AC678 with consistency check", "Component": "FI-AA"}, {"SoftwareComponent": "SAP_APPL", "ValidFrom": "500", "ValidTo": "500", "Number": "830393 ", "URL": "/notes/830393 ", "Title": "RAPERB2000: doubled APC postings after you restart", "Component": "FI-AA-AA-E"}, {"SoftwareComponent": "SAP_APPL", "ValidFrom": "500", "ValidTo": "500", "Number": "843612 ", "URL": "/notes/843612 ", "Title": "RAPOST2000: Restart indicator set incorrectly", "Component": "FI-AA-AA-E"}, {"SoftwareComponent": "SAP_APPL", "ValidFrom": "500", "ValidTo": "500", "Number": "891104 ", "URL": "/notes/891104 ", "Title": "RAPOST2000: Restart indicator set incorrectly", "Component": "FI-AA-AA-E"}, {"SoftwareComponent": "SAP_APPL", "ValidFrom": "500", "ValidTo": "600", "Number": "864465 ", "URL": "/notes/864465 ", "Title": "Short dump RW018 with RAPERB2000 (ASKB/ASKBN)", "Component": "FI-AA-AA-E"}]}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "841449", "RefTitle": "Gap-free document number assignment", "RefUrl": "/notes/0000841449"}]}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "909753", "RefTitle": "RAPERB2000: Job status canceled with message BL 191", "RefUrl": "/notes/0000909753"}, {"RefNumber": "935528", "RefTitle": "Document posted by RAPERB2000 is not found", "RefUrl": "/notes/0000935528"}]}}}}}