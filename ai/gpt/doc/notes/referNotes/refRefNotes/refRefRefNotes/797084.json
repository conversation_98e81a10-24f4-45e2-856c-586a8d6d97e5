{"Request": {"Number": "797084", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 398, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000015805502017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000797084?language=E&token=7630ADFEC6BB1CE886997DDC911D37F6"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000797084", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000797084/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "797084"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 81}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Installation information"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "17.03.2011"}, "SAPComponentKey": {"_label": "Component", "value": "BC-OP-LNX-SUSE"}, "SAPComponentKeyText": {"_label": "Component", "value": "SUSE Linux"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Basis Components", "value": "BC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Operating System Platforms", "value": "BC-OP", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-OP*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Linux", "value": "BC-OP-LNX", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-OP-LNX*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "SUSE Linux", "value": "BC-OP-LNX-SUSE", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-OP-LNX-SUSE*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "797084 - SUSE LINUX Enterprise Server 9: Installation notes"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>You want to use SAP software on SUSE LINUX Enterprise Server 9.<br /></p> <b>Other terms</b><br /> <p>SUSE, SLES, Enterprise Server, suse, SuSE, Novell, OES, Open Enterprise Server, novell, Power, Linux on POWER, ppc64, ppc, iSeries, pSeries, as/400, as400, LINUX, Blade Server, Blade, PowerPC, power, ia64, x86, x86_64<br /></p> <b>Reason and Prerequisites</b><br /> <p>You want to use SAP software on SUSE LINUX Enterprise Server 9.<br /></p> <b>Solution</b><br /> <p>This document deals with installation and configuration of SAP Software on SUSE LINUX Enterprise Server 9, as well as upgrading an existing SAP system from SUSE LINUX Enterprise Server 8 (SLES 8) and earlier to SUSE LINUX Enterprise Server 9.<br />This note does not describe how to install or configure SUSE LINUX Enterprise Server 9 as a basis for a database server.<br /><br />The following hardware platforms are certified to use SAP software on SUSE LINUX Enterprise Server 9 (SLES 9):</p> <UL><LI>x86 (Intel-compatible 32-bit -- \"i386/i586/i686\")<br />SUSE LINUX Enterprise Server 9 (SLES 9) for x86</LI></UL> <UL><LI>x86_64 (AMD and Intel-compatible 32- and 64-bit - \"x86_64\")<br />SUSE LINUX Enterprise Server 9 (SLES 9) for AMD64 and Intel EM64T</LI></UL> <UL><LI>IA64 (Intel Itanium2 64-bit - \"ia64\")<br />SUSE LINUX Enterprise Server 9 (SLES 9) for Itanium Processor Family</LI></UL> <UL><LI>IBM Power (64-bit - \"ppc\")</LI></UL> <UL><LI>SUSE LINUX Enterprise Server 9 (SLES 9) for IBM POWER</LI></UL> <UL><LI>IBM zSeries (64-bit - \"s390x\")<br />SUSE LINUX Enterprise Server 9 (SLES 9) for IBM zSeries<br /></LI></UL> <b>Requirements for support<br /></b><br /> <p>To receive full support for your SAP system, the following requirements must be met:</p> <UL><LI>To ensure support for problems that may occur with the operating system, you require a valid support contract for SUSE LINUX Enterprise Server.<br />A support contract can be made directly with Novell, or with a support partner who is authorized to redirect possible level 3 support queries to Novell.</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;For more information, see also:<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;http://support.novell.com/linux/<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;http://www.novell.com/licensing/linux_upg/index.html<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;or contact your local Novell sales representative.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;For more information about \"SUSE Priority Support for SAP applications\", please, refer to SAP Note 1056161 for further informations.</p> <UL><LI>You must use hardware that is certified by your hardware vendor for use with SAP on Linux.<br />See SAP Note 171356 for a list of the corresponding notes of hardware partners.</LI></UL> <UL><LI>You may use any Linux kernel provided by Novell SUSE for your architecture.<br />Usually this will be the kernel selected as default to be installed by YaST, unless you up-front are aware of special needs (like needing \"kernel-bigsmp\" on 32-bit architectures).<br /><br /></LI></UL> <b>Life cycle of SUSE LINUX Enterprise Server 9 (SLES 9)<br /></b><br /> <p>An overview over the various life cycles for the SUSE LINUX Enterprise Server product line can be found under the URL:<br /><br />&#x00A0;&#x00A0;http://support.novell.com/lifecycle/index.jsp?sourceident=suplnav5_lifecycle<br /><br />Under the heading \"Support status by product\" (found in the upper right corner of the general description of the life cycles) enter the word \"LINUX\" (without the quotes) and click on the \"Search\" button to get the life cycles for the SLE (SUSE LINUX Enterprise) products, and look for the version(s) that has your interest.<br /><br /></p> <b>Service Packs for SUSE LINUX Enterprise Server 9 (SLES 9)<br /></b><br /> <p>Updates for SLES 9 are released in the form of Service Packs (SP). When you import a Service Pack, many RPM packages are updated to a new version.<br />For SLES 9 you may use all Service Packs officially released by Novell.<br />It is sufficient and highly recommended to install the most current Service Pack released by Novell.<br /><br />The most current Service Pack for SLES 9 at present time is Service Pack 4.<br /><br />After making changes to the \"glibc\" (for example, after an update) you must reinstall the \"saplocales\" package, for further information refer to below and to SAP note 516716.<br /><br /></p> <b>Installing SUSE LINUX Enterprise Server 9 (SLES 9)<br /></b><br /> <p>Install the operating system as described in the documentation delivered with the product, the \"Start-up Guide\", as well as digital documents available in PDF format in the \"/docu/\" directory of the installation medium under the respective languages supported in the current release (e.g. English is found under \"/docu/en/\"):<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;\"manual.pdf\", \"preparation.pdf\", and \"startup.pdf\".<br /></p> <UL><LI>Select English as the installation and system language.<br /></LI></UL> <UL><LI>Select the \"C/C++ Compiler and Tools\" in the package selection menu to be installed.<br />This should be done as a general rule, and <B>must</B> be done when ORACLE is being installed on the system, while the ORACLE install process requires the compiling tools to be present.</LI></UL> <UL><LI>If you intend to install Oracle and SAP on the same system, please, <B>do not</B> select the \"Oracle Server Base\" in the package selection menu (i.e. do not installation the \"orarun\" package) on the system.<br />Doing so, will prevent \"sapinst\" from doing a correct installation of the SAP system parts.<br /></LI></UL> <p>Note the following additional instructions:</p> <UL><LI>Manually adjust the hard disk partitioning to the requirements of the SAP components. SAP components and any database components are mostly installed on separate partitions.</LI></UL> <UL><LI>Use the standard installation as a starting point when selecting the packages in YaST.</LI></UL> <UL><LI>If you can access an NTP server, you should configure and activate the Network Time Protocol service (this can easily be done using YaST -&gt; Network services -&gt; NTP client).<br />This automatically synchronizes the date and time of all SAP application servers.</LI></UL> <UL><LI>After the installation, carry out an online update either using YaST or manually, to bring the system up to date.</LI></UL> <UL><LI>The recommended size of the transfer memory (swap space) is double that of the main memory (2 x RAM).<br />The Linux kernel usually requires little swap space because other limits are reached before the swap space can be used completely (\"late assignment\").<br />If you decide that a larger swap space is nevertheless necessary for operating SAP software, the necessary enhancements can be carried out at any time using distribution-specific tools.</LI></UL> <UL><LI>The \"hostname\" command may only output the host name and not the \"Qualified Domain Name\".<br />Example: Fully qualified domain name is \"ls3001.example.com\"; needed output of \"hostname\" is \"ls3001\".<br />When the system has been correctly configured, \"hostname -f\" can be used to get the fully qualified domain name.<br /></LI></UL> <UL><LI>On ssh connections to the machine, some environment settings from the client are preserved. This is undesireable when executing management<br />commands to the SAP software. In order to suppress this, you need to edit the file \"/etc/ssh/sshd_config\" and remove or comment out the following lines:<br />AcceptEnv LANG LC_CTYPE LC_NUMERIC LC_TIME LC_COLLATE LC_MONETARY LC_MESSAGES<br />AcceptEnv LC_PAPER LC_NAME LC_ADDRESS LC_TELEPHONE LC_MEASUREMENT<br />AcceptEnv LC_IDENTIFICATION LC_ALL<br /></LI></UL><b>Linux kernel<br /></b><br /> <p>For SLES 9 you may use all Linux kernel packages officially released by Novell. According to functional and security patches we recommend to install a Linux kernel package which was released quite recently.<br /><br />If you do need to install a new kernel version on your system manually, proceed as follows:<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Log on to the system as system administrator (the \"root\" user) and install the kernel package required:<br /><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;rpm -Uvh &lt;kernel.rpm&gt;<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;The kernel and initrd images, shared libraries and kernel modules are installed.<br />In addition, the bootloader configuration in the file \"/boot/grub/menu.lst\" is automatically adjusted to the new kernel version (default bootloader GRUB).<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;<B>Note:</B><br /> In some cases, it is better to keep the old kernel version in addition to the new kernel. In this case, install the new kernel by using the command:<br /><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;rpm -ivh --force &lt;kernel.rpm&gt;<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;The old kernel and initrd is still available under \"vmlinuz. previous\" respectively \"initrd.previous\".<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;To be able to boot this kernel, you must create a corresponding entry in the file:<br /><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; /boot/grub/menu.lst<br /><br /></p> <b>GLIBC<br /></b><br /> <p>For SLES 9 you may use all Linux glibc packages officially released by Novell. According to functional and security patches we recommend to install a Linux glibc package which was released quite recently.<br /><br />After making changes to the glibc (for example, after an update) you must reinstall the \"saplocales\" package.<br />Attached to SAP Note 171356 is the current \"saplocales\" package and additional information to it.<br />You can install the \"saplocales\" package (as user \"root\") as follows:<br /><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;rpm -Uvh --force &lt;saplocales.rpm&gt;<br /><br /></p> <b>Linux kernel parameters<br /></b><br /> <p>The \"sapinit.rpm\" package<br /><br />In order to activate the specific Linux kernel parameters needed by the SAP software, you need to have the \"sapinit\" RPM package installed on the system.<br />For details about the sapinit rpm, please refer to SAP Note: 1275776.<br /></p> <b>Upgrading from SUSE LINUX Enterprise Server 8 (SLES 8)<br /></b><br /> <p>Customers who previously used SLES 8 can use YaST to upgrade their system directly to SLES 9.<br />This means that they do not require a new installation.<br /><br />You will find a detailed description of the upgrade from SLES 8 to SLES 9 in the documentation delivered on the SLES 9 installation media (\"manual.pdf\") in the section entitled \"Updating the System and Package Administration\".<br />The delivered PDF document \"manual.pdf\" can be found in the language specific directory of the current supported languages on the first CD under the \"/docu\" directory -- e.g. the English version is found under \"/docu/en/manual.pdf\".<br /><br />Kindly notice that the first CD of the SLES 9 SPx CD-set also contains the \"/docu\" directory, and that the documentation found here may be newer than the documentation found on the original SLES 9 CD-set.<br /><br /></p> <b>Using raw devices, \"Journaling File System\" and LVM<br /></b><br /> <p>SAP Note 405827 contains information on using raw devices or a 'journaling file system'.<br /><br />For further information about the \"Logical Volume Manager\" (LVM), see SAP Note 597415.<br /><br /></p> <b>Additional notes on installing an SAP system on SLES 9<br /></b><br /> <p>Number of \"file descriptors\"<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Some SAP applications (for example, J2EE Engine, MaxDB, XI, portal) need a larger number of \"file descriptors\" than is set by default (1024).<br />If an application exceeds the limit, you will get some error messages in your logs, e.g.:<br /><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;open files rlimit 1024 reached for uid 203 pid 16249<br /><br /> If you have installed the SLES9 sapinit rpm the number of file descriptors will be set automatically to the values specified in:<br /><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;/etc/sysconfig/sapinit<br /><br /> If, for any reason you cannot use the automatic setup of this limit, you<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;can also set it manually by editing the following file:<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;/etc/security/limits.conf<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;and add the following lines to it:<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;*&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;hard&#x00A0;&#x00A0;&#x00A0;&#x00A0; nofile&#x00A0;&#x00A0;&#x00A0;&#x00A0;32800<br />*&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;soft&#x00A0;&#x00A0;&#x00A0;&#x00A0; nofile&#x00A0;&#x00A0;&#x00A0;&#x00A0; 32800<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;These changes take effect after you log off and log back on again (in \"bash\" test with: ulimit -n).<br />If you need more than 32800 \"file descriptors\", you can increase the value even further -- e.g. when using the SAP J2EE Engine.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;If your systems are started via <B>sapcontrol</B> or a web service client like SAP MMC, than the limits configured via /etc/sysconfig/sapconf will not be applied to the SAP processes. To adjust system limits for SAP systems controlled in such an environment the system limits must be set in the file /usr/sap/sapservices as described in SAP note 1437105. You may decide if your system is under control of sapcontrol by checking if its start command is contained in /usr/sap/sapservices.<br /><br />Patching SAPINST<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;In some cases, SAPINST does not start correctly or terminates abnormally.<br />In this situation, set the \"LD_ASSUME_KERNEL\" environment variable as follows in the environment of the user who starts SAPINST (when using the \"csh\" or \"tcsh\" shells):<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;setenv LD_ASSUME_KERNEL 2.4.1<br /><br />When using the \"bash\" shell, use:<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;export LD_ASSUME_KERNEL=\"2.4.1\"<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;If this also fails, you will need to patch the SAPINST.&#x00A0;&#x00A0;SAP Note 722890 contains information about this.<br /><br />On \"x86_64\", the SAPINST installation of a Java component terminates abnormally<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;During the installation of the J2EE Addins, SAPinst terminates in the \"Map SLD Roles\" phase with an error message.<br />During the installation or patching of a Java component, SAPINST terminates with \"no such child process\".<br />If this happens, the installation can be continued using the following method:</p> <UL><UL><LI>Stop SAPINST and set the \"LD_ASSUME_KERNEL\" environment variable:<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;setenv LD_ASSUME_KERNEL 2.4.1</LI></UL></UL> <UL><UL><LI>Then restart SAPINST and continue the installation.</LI></UL></UL> <UL><UL><LI>The installation should then run successfully.</LI></UL></UL> <UL><UL><LI>The environment variable \"LD_ASSUME_KERNEL\" can then be removed again.<br /></LI></UL></UL> <p>Patches for the SAP kernel<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;If errors occur when installing SAP R/3, or if the installed system does not start, you may need to import the latest patch for the SAP kernel before continuing to install the SAP kernel.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;To ensure secure operation of the SAP system on SLES 9 (using the Linux kernel 2.6.x), you must use the following patch level or higher of the SAP kernel:<br /><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; SAP kernel&#x00A0;&#x00A0;&#x00A0;&#x00A0;Minimum patch level<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 46D_EXT&#x00A0;&#x00A0;&gt;= 1811<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 620&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&gt;= 1441<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 640&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&gt;= 12<br /><br />Error messages when executing SAPINST</p> <UL><LI>The \"connect screen\" dialog terminates with the error message SAPINST: GUI no connect\".<br />The reason for this is that the DNS name resolve used by SAPINST does not work due to the IPv6 support, activated in SLES9 by default.<br />Solution:</LI></UL> <UL><UL><LI>Enter the IP \"127.0.0.1\" instead of \"localhost\" in the dialog.</LI></UL></UL> <UL><LI>For installations with SAPDB 7.4, installation terminates in the step \"RFCRSWBOINI_IND_IND\" with the following error message:<br />\"*** ERROR =&gt; ADABAS Log Segment Size of 42666 pages is too small, MINIMUM size has to be 49990 pages\"<br />after which the SAP system cannot be started.<br />Solution:</LI></UL> <UL><UL><LI>Stop the SAP system (\"stopsap\" as user \"&lt;SID&gt;adm\"), change the \"log segment size\" in the database by using DBMGUI.</LI></UL></UL> <UL><UL><LI>Then restart the database by using DBMGUI and restart the SAP system (\"startsap\" as user \"&lt;SID&gt;adm\").</LI></UL></UL> <UL><UL><LI>It may also be necessary to import a current patch level of the SAP kernel.<br /></LI></UL></UL> <p>NPTL<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;SLES 9 delivers the Native Posix Threading Library (NPTL), an improved threading implementation for Linux. NPTL follows the POSIX threading standards much more strictly than the LinuxThreads formerly used.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;If your SAP kernel is not compatible with NPTL, you require the newest available SAP kernel from the SAP Service Marketplace.<br />Your SAP kernel is not compatible with NPTL if, for example, calling \"disp+work\" ends with a \"Floating Point Exception\".<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;For SAP kernel version 7.00 and higher, NPTL compatibility is an absolute prerequisite of the operating system.<br />In other words, the GLIBC <B>must</B> be compiled for the \"i686\" architecture (this applies <B>only</B> for the Intel 32-bit architecture generally known under the name \"i386\" or \"ix86\".&#x00A0;&#x00A0;All other architectures have NPTL natively compiled in).<br />You can check this with the following command:<br /><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;rpm -qi glibc | grep Distribution<br /><br />NPTL and LinuxThreads -- special handling of known programs<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Note that the lists below may not be complete or valid for all versions and platforms.</p> <UL><LI>Programs you should not execute with NPTL:</LI></UL> <UL><UL><LI>SAP Kernel &lt;= 6.20 (except for the latest releases of 4.6D_EXT and 6.20).</LI></UL></UL> <UL><UL><LI>SAPinst</LI></UL></UL> <UL><UL><LI>SAPDB, MAXDB Version 7.5 (except on \"x86_64\"), refer to SAP Note 788272.</LI></UL></UL> <UL><UL><LI>ORACLE \"runInstaller\".</LI></UL></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Here the environment variable \"LD_ASSUME_KERNEL\" must be set to \"2.4.1\":<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;setenv LD_ASSUME_KERNEL 2.4.1</p> <UL><LI>Programs you should not execute with LinuxThreads:</LI></UL> <UL><UL><LI>SAP Kernel &gt;= 7.00<br />In this case, the environment variable \"LD_ASSUME_KERNEL\" may <B>not</B> be set!<br /></LI></UL></UL> <p>PAM configuration needed for \"sapstartsrv\".<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;To enable the Web Service Interface (which uses \"sapstartsrv\" and \"sapcontrol\"), the PAM configuration file named \"/etc/pam.d/sapstartsrv\" has to exist.&#x00A0;&#x00A0; This file should have been installed on your system as part of the installation of the \"sapstartsrv\" service.<br /><br />If not, you have to create the file \"/etc/pam.d/sapstartsrv\" manually using your favorite editor (as \"root\") with the following contents:<br /><br />&#x00A0;&#x00A0; #%PAM-1.0<br />&#x00A0;&#x00A0;auth&#x00A0;&#x00A0;&#x00A0;&#x00A0;requisite&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;pam_unix_auth.so&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;nullok<br /><br />The file must be owned by \"root\" in group \"root\" and be readonly (i.e. having the file permission modes 444 (\"r--r--r--\")).<br /><br />Corrections to previous versions of this note regarding the PAM configuration for \"sapstartsrv\".<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;In the previous versions of this note (English versions with version numbers &lt;= 62 from 2007-01-11), it was described how to change the contents of the PAM configuration file \"/etc/pam.d/login\".&#x00A0;&#x00A0;As this file is a system wide used PAM configuration file, it is <B>not</B> recommendable to make <B>any</B> changes to this file in order for the \"sapstartsrv\" to work.<br />The \"sapstartsrv\" now works using its own PAM configuration file (see previous section).<br /><br />Because of the above, kindly make sure that the (system) PAM configuration file \"/etc/pam.d/login\" does <B>not</B> contain the line:<br /><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;auth&#x00A0;&#x00A0;&#x00A0;&#x00A0;requisite&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;pam_unix_auth.so&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;nullok&#x00A0;&#x00A0;#set_secrpc<br /><br />Should the file contain the above line, you <B>must</B> replace the string \"pam_unix_auth.so\" with \"pam_unix2.so\", as well as follow the instructions as given in the previous section (if necessary).<br /></p> <b>Additional notes for IBM DB2</b><br /> <UL><LI>The installation of the RDBMS software IBM DB2 V8.x is only possible in scroll mode with the following command (no graphical user interface):</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;db2_install<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;After the DB2 installation is complete, the license must be added manually as described in SAP Note 801415.</p> <UL><LI>Under the URL:</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;http://www.ibm.com/support/docview.wss?rs=865&amp;uid=swg21237911<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;you can read more about the prerequisites for DB2 installations under SLES 9.<br /><br />Under the URL (although this does not specifically refer to Linux nor SLES 9):<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;http://www-1.ibm.com/support/docview.wss?rs=865&amp;uid=swg21238113<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;you can read more about DB2 product installation on NFS (Network File System), including a reference to the IBM white paper \"Setting Up DB2 on NFS Mounted File System\".<br /><br /></p> <b>Additional notes for MAXDB.<br /></b><br /> <p>The following error may occur when the database is loaded:<br /><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;ERROR : Prepare/modify for UpdateIndexTable failed (dbrc=99).<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;(SQL error -7009)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;error message returned by DbSl:<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;NULL value in key column not allowed<br /><br />In this case, you must update the SAP kernel to the latest version.<br /><br /></p> <b>General notes for both ORACLE 9.x and ORACLE 10.x</b><br /> <UL><LI>Please keep in mind not to use \"cpio\" when creating database backups with SAP tool BRBACKUP.<br />The \"cpio\" program is not capable of handling file sizes equal to or greater than 2 GiB.<br />As preferred solution use the \"dd\" program instead.<br />More information can be found in SAP note 20577.</LI></UL> <UL><LI>When using the ORACLE database together with the \"reiserfs\" file system type (the default suggested file system type, when using YaST to create file systems on partitions), it is a <B>must</B> to mount the file system, where the ORACLE system and database has been installed, with the \"reiserfs\" mount option <B>notail</B>, e.g. by inserting this in the \"/etc/fstab\" entry for the file system(s) in question:</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;/dev/sda1&#x00A0;&#x00A0; /oracle&#x00A0;&#x00A0; reiserfs&#x00A0;&#x00A0; notail,acl,user_xattr&#x00A0;&#x00A0; 1&#x00A0;&#x00A0;2<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;For further informations refer to SAP Note 914177 (resp. 834343 for<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Oracle 9.2 or 999524 for Oracle 10.2).</p> <UL><LI>When creating the database, the process may terminate with the error message:</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;ORA-27125: \"unable to create shared memory segment\"<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;To avoid this problem during the installation as well as when the ORACLE database is being started after a reboot of the system, you need to add the following line:<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;vm. disable_cap_mlock=1<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;to the file \"/etc/sysctl. conf\" This requires that the \"sapinit.rpm\" (SLES 9 SP3 and future releases) RPM package has been installed on the system - see above under the section <B>Linux kernel parameters</B> for further details on this RPM package).<br /><br />In order for the change to take effect in the running system, you must run the following command as the user \"root\":<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;/etc/init.d/boot.sapconf restart</p> <UL><LI>Kindly refer to SAP Note 986578 for specific installation instructions of ORACLE on the Intel Itanium (\"ia64\") platform.<br /><br /></LI></UL> <b>Additional notes for ORACLE 9.x<br /></b><br /> <p>When you install the current ORACLE Version 9.x released for SAP on SUSE LINUX Enterprise Server 9, there are various settings and procedures to be followed.</p> <UL><LI>ORACLE patch set ******* (at least) is essential to use on all platforms, except for the platform \"x86_64\" (AMD64 and Intel EM64T), where you must install at least patch set *******.</LI></UL> <UL><LI>For the Intel Itanium \"ia64\" architecture, refer to SAP Note 875206.</LI></UL> <UL><LI>There is also a detailed installation manual for ORACLE 9.2.x in the Novell network under:</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;http://ftp.novell.com/partners/oracle/docs/9205_sles9_install.pdf<br /><br />The following examples all assume the usage of an Intel-compatible 32-bit architecture (\"i386/i484/i586/i686\") unless otherwise noted.<br />Please, change the directory paths and names depending on your system settings.</p> <UL><LI>Obtain the ORACLE Support Packages and ORACLE patch set required directly from the SAP Service Marketplace:</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;http://service.sap.com/patches<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Downloads &gt; Database Patches &gt; Oracle &gt; Oracle 32-Bit &gt;<br />&#x00A0;&#x00A0; Oracle 9.2.0. 32-Bit &gt; Oracle 9.2.0.4. 32-Bit &gt; Linux<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Downloads &gt; Database Patches &gt; Oracle &gt; Oracle 32-Bit &gt;<br />&#x00A0;&#x00A0; Oracle 9.2.0. 32-Bit &gt; Oracle *******. 32-Bit &gt; Linux<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Downloads &gt; Database Patches &gt; Oracle &gt; Oracle 32-Bit &gt;<br />&#x00A0;&#x00A0; Oracle 9.2.0. 32-Bit &gt; Oracle *******. 32-Bit &gt; Linux</p> <UL><LI>In YaST2, menu \"Install and Remove Software\", make sure that the menu options \"Graphical Base System\" and \"C/C++ Compiler and Tools\" are installed (checked) under the filter entry \"Filter: Selections\".</LI></UL> <UL><LI>Before you run the \"runinstaller\", you need to set the environment variable \"LD_ASSUME_KERNEL\" to \"2.4.1\":</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;setenv LD_ASSUME_KERNEL 2.4.1<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;</p> <UL><LI>On the \"i386\" architecture, a further environment variable \"LD_PRELOAD\"</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;needs to be set (as the user \"ora&lt;SID&gt; \" who will start \"runinstaller\"):<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;setenv LD_PRELOAD /usr/lib/libInternalSymbols.so<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;</p> <UL><LI>On the the \"x86_64\" architecture (AMD64 and Intel EM64T), you do not need to set the environment variable \"LD_PRELOAD\".</LI></UL> <UL><LI>On \"ia64\", you still <B>have</B> to make use of the <B>binutils 2.12</B> for installing Oracle 9 (see SLES 8 SAP Note 767814).</LI></UL> <UL><LI>\"runinstaller\" can be started.</LI></UL> <UL><LI>Ignore the error message:<br />&#x00A0;&#x00A0;\"Error in invoking target install of makefile<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;/oracle/&lt;SID&gt;/920_32/rdbms/lib/ins_rdbms.mk\".<br />This error is corrected, when you install the patchset ******* or *******.<br />This error does not occur on \"x86_64\".</LI></UL> <UL><LI>Ignore the error message:<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;\"Error in invoking target install of makefile<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;/oracle/&lt;SID&gt;/920_32/ctx/lib/ins_ctx.mk\".<br />This error is corrected, when you install the patchset ******* or *******.<br />This error does not occur on \"x86_64\".</LI></UL> <UL><LI>Ignore the error message:<br />&#x00A0;&#x00A0;\"Error in invoking target install of makefile<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;/oracle/&lt;SID&gt;/920_32/network/lib/ins_oemagent.mk\".<br />This error is corrected by installing patch 3119415, see below.<br />These errors do not occur on \"x86_64\".</LI></UL> <UL><LI>Install the ORACLE patch set ******* or *******, as described in SAP Note 539921.</LI></UL> <UL><LI>On \"x86_64\", you <B>must</B> install ORACLE patch set *******.</LI></UL> <UL><LI>On \"ia64\" the ORACLE patchset ******* has to be installed in order to fix the problems with starting the ORACLE database with the environment variable \"LD_ASSUME_KERNEL\" set, which is <B>not</B> allowed on \"ia64\".<br />See also SAP note 906403.</LI></UL> <UL><LI>Install ORACLE patch \"p3119415_9205_LINUX.zip\" (for ORACLE *******) or \"p3119415_9206_LINUX.zip\" (for ORACLE *******); for more information, see SAP Note 306408.<br />You do not need to install \"p3119415_9206_LINUX.zip\" (for ORACLE *******) on \"x86_64\".</LI></UL> <p>During the SAP installation with the \"SAPinst\" program, the installation process terminates in the dialog \"create sap license\" with the error message \"library iamodora missing\", while several required libraries are missing.<br />To correct this error, install these libraries in the installation directory either after the installer has terminated or before calling \"SAPinst\", using the following command:</p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;SAPCAR -xvf /&lt;sapinst-CD&gt;/SAPINST/UNIX/LINUX_32/SAPPROD.SAR<br /></p> <b>Previous Service Packs<br /><br />Please note that all Service Pack before SLES9 SP3 are obsolete and have fallen out of support.<br />Hence it is strongly adviced to update your system at least to SLES9 SP3.<br /><br />If you want to use several languages with the SLES9 initial release, thefollowing workaround must be applied:<br /></b><br /> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;cp /usr/lib/gconv/gconv-modules /usr/lib64/gconv/<br /><br />Starting with SLES 9 Service Package 1 (SLES 9 SP1) and later this work around is no longer needed.<br /><br />Since NIS is currently not \"thread safe\", problems and errors may occur in multithreading applications of the SAP server, if NIS is used to resolve hostnames, services or RPC calls.<br />Therefore, wehn you activate NIS during the installation, you need to check the \"/etc/nsswitch.conf\" file after the first system restart. The entries in \"/etc/nsswitch.conf\":<br /><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;hosts:<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;networks:<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;rpc:<br /> <br />must not contain \"nis\" as lookup service. If they do,&#x00A0;&#x00A0;delete \"nis\" or replace it with another service.<br /><br />For details about this, see the manpage for \"nsswitch.conf\".<br />This problem has been solved in SLES 9 Service Pack 3 (SLES 9 SP3).<br /><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Work-around for bug in kernel delivered with SLES 9 SP3<br /><br />Due to a bug in the delivered kernel with SLES 9 SP3 (kernel 2.6.5-7.244), the activation of the settings found in the file \"/etc/sysctl.conf\" will result in the following error message:<br /><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;error: \"vm.heap-stack-gap\" is an unknown key<br /><br />In order to fix this, please, update your system with the newest available kernel via YOU (any update kernel with a version number higherthan 2.6.5-7.252 fixes this issue).<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON> (C5126315)"}, {"Key": "Processor                                                                                           ", "Value": "D029037"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000797084/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000797084/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000797084/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000797084/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000797084/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000797084/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000797084/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000797084/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000797084/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "958253", "RefComponent": "BC-OP-LNX-SUSE", "RefTitle": "SUSE LINUX Enterprise Server 10: Installation notes", "RefUrl": "/notes/958253"}, {"RefNumber": "947991", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/947991"}, {"RefNumber": "941735", "RefComponent": "BC-OP-LNX", "RefTitle": "SAP memory management system for 64-bit Linux systems", "RefUrl": "/notes/941735"}, {"RefNumber": "939677", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info. on upgrading to SAP NetWeaver 7.1 AS ABAP", "RefUrl": "/notes/939677"}, {"RefNumber": "936887", "RefComponent": "BC-OP-LNX", "RefTitle": "End of maintenance for Linux distributions", "RefUrl": "/notes/936887"}, {"RefNumber": "927637", "RefComponent": "BC-CST-STS", "RefTitle": "Web service authentication in sapstartsrv as of Release 7.00", "RefUrl": "/notes/927637"}, {"RefNumber": "914177", "RefComponent": "BC-DB-ORA", "RefTitle": "File system reiserfs Oracle parameter filesystemio_options", "RefUrl": "/notes/914177"}, {"RefNumber": "906403", "RefComponent": "BC-DB-ORA", "RefTitle": "ORA-7445 during DESCRIBE", "RefUrl": "/notes/906403"}, {"RefNumber": "875206", "RefComponent": "BC-DB-ORA", "RefTitle": "Certifiction Oracle 9.2. on SLES9 Itanium", "RefUrl": "/notes/875206"}, {"RefNumber": "839624", "RefComponent": "BC-DB-ORA", "RefTitle": "ORA-27125 while starting Oracle instance", "RefUrl": "/notes/839624"}, {"RefNumber": "834343", "RefComponent": "BC-DB-ORA", "RefTitle": "Oracle 9.2 Platform Support for Async IO", "RefUrl": "/notes/834343"}, {"RefNumber": "825822", "RefComponent": "BC-OP-LNX-X64", "RefTitle": "Installation 4.6C SR2 on Linux x86_64 with DB2", "RefUrl": "/notes/825822"}, {"RefNumber": "81737", "RefComponent": "BC-DB-DB2", "RefTitle": "DB2-z/OS: APAR List", "RefUrl": "/notes/81737"}, {"RefNumber": "816145", "RefComponent": "BC-OP-LNX-X64", "RefTitle": "Installation 4.6C SR2 on Linux x86_64 with Oracle", "RefUrl": "/notes/816145"}, {"RefNumber": "801415", "RefComponent": "BC-DB-DB6", "RefTitle": "DB6 Installation on Unix with db2_install", "RefUrl": "/notes/801415"}, {"RefNumber": "788272", "RefComponent": "BC-DB-SDB", "RefTitle": "Compatibility of Linux Kernel 2.6/NPTL with MaxDB", "RefUrl": "/notes/788272"}, {"RefNumber": "784391", "RefComponent": "BC-OP-LNX", "RefTitle": "SAP support terms and 3rd-party Linux kernel drivers", "RefUrl": "/notes/784391"}, {"RefNumber": "767814", "RefComponent": "BC-OP-LNX-SUSE", "RefTitle": "SUSE LINUX Enterprise Server 8: Installation notes", "RefUrl": "/notes/767814"}, {"RefNumber": "765424", "RefComponent": "BC-OP-PLNX", "RefTitle": "Linux: Released IBM Hardware - POWER based servers", "RefUrl": "/notes/765424"}, {"RefNumber": "722890", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/722890"}, {"RefNumber": "597415", "RefComponent": "BC-OP-LNX", "RefTitle": "Logical volume manager (LVM) on Linux", "RefUrl": "/notes/597415"}, {"RefNumber": "539921", "RefComponent": "BC-DB-ORA", "RefTitle": "Current patch set for Oracle 9.2.0", "RefUrl": "/notes/539921"}, {"RefNumber": "531069", "RefComponent": "BC-OP-LNX", "RefTitle": "Heterogeneous Unix - Unix Systems", "RefUrl": "/notes/531069"}, {"RefNumber": "516716", "RefComponent": "BC-OP-LNX", "RefTitle": "Linux: Problems with locales after glibc update", "RefUrl": "/notes/516716"}, {"RefNumber": "405827", "RefComponent": "BC-OP-LNX", "RefTitle": "Linux: Recommended file systems", "RefUrl": "/notes/405827"}, {"RefNumber": "386605", "RefComponent": "BC-OP-LNX", "RefTitle": "SAP Memory Management for Linux (32-bit)", "RefUrl": "/notes/386605"}, {"RefNumber": "306408", "RefComponent": "BC-DB-ORA", "RefTitle": "Oracle 9.2.0: Using OPatch to install patches", "RefUrl": "/notes/306408"}, {"RefNumber": "20577", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Cpio cannot backup files larger than or equal to 2 GB", "RefUrl": "/notes/20577"}, {"RefNumber": "171356", "RefComponent": "BC-OP-LNX", "RefTitle": "SAP Software on Linux: General information", "RefUrl": "/notes/171356"}, {"RefNumber": "1452070", "RefComponent": "BC-OP-ZLNX", "RefTitle": "DB2-z/OS: SAP on Linux on IBM Z and z/VM", "RefUrl": "/notes/1452070"}, {"RefNumber": "1437105", "RefComponent": "BC-CST-STS", "RefTitle": "Operating system limits for SAP instances", "RefUrl": "/notes/1437105"}, {"RefNumber": "1367498", "RefComponent": "BC-JVM", "RefTitle": "SAP JVM installation prerequisites", "RefUrl": "/notes/1367498"}, {"RefNumber": "1349794", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1349794"}, {"RefNumber": "1240731", "RefComponent": "BC-OP-LNX-SUSE", "RefTitle": "Linux: Incorrect write I/O on SLES9", "RefUrl": "/notes/1240731"}, {"RefNumber": "1172419", "RefComponent": "BC-OP-LNX-JSEI", "RefTitle": "Linux: Supported Java versions on the x86_64 platform", "RefUrl": "/notes/1172419"}, {"RefNumber": "1108861", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1108861"}, {"RefNumber": "1088904", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info. on upgrading to SAP ECC 6.0 SR3 ABAP", "RefUrl": "/notes/1088904"}, {"RefNumber": "1066142", "RefComponent": "BC-OP-LNX-JSEI", "RefTitle": "Linux: libj9vm22.so: cannot open shared object file", "RefUrl": "/notes/1066142"}, {"RefNumber": "1021236", "RefComponent": "BC-OP-LNX", "RefTitle": "Linux: Using SAP Kernel 7.01 and higher on RHEL4 and SLES9", "RefUrl": "/notes/1021236"}, {"RefNumber": "1019585", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1019585"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2065323", "RefComponent": "BC-OP-LNX-HDS", "RefTitle": "Linux: Released Hitachi / HDS Hardware", "RefUrl": "/notes/2065323 "}, {"RefNumber": "1367498", "RefComponent": "BC-JVM", "RefTitle": "SAP JVM installation prerequisites", "RefUrl": "/notes/1367498 "}, {"RefNumber": "1088904", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info. on upgrading to SAP ECC 6.0 SR3 ABAP", "RefUrl": "/notes/1088904 "}, {"RefNumber": "816145", "RefComponent": "BC-OP-LNX-X64", "RefTitle": "Installation 4.6C SR2 on Linux x86_64 with Oracle", "RefUrl": "/notes/816145 "}, {"RefNumber": "958253", "RefComponent": "BC-OP-LNX-SUSE", "RefTitle": "SUSE LINUX Enterprise Server 10: Installation notes", "RefUrl": "/notes/958253 "}, {"RefNumber": "936887", "RefComponent": "BC-OP-LNX", "RefTitle": "End of maintenance for Linux distributions", "RefUrl": "/notes/936887 "}, {"RefNumber": "1172419", "RefComponent": "BC-OP-LNX-JSEI", "RefTitle": "Linux: Supported Java versions on the x86_64 platform", "RefUrl": "/notes/1172419 "}, {"RefNumber": "1437105", "RefComponent": "BC-CST-STS", "RefTitle": "Operating system limits for SAP instances", "RefUrl": "/notes/1437105 "}, {"RefNumber": "927637", "RefComponent": "BC-CST-STS", "RefTitle": "Web service authentication in sapstartsrv as of Release 7.00", "RefUrl": "/notes/927637 "}, {"RefNumber": "839624", "RefComponent": "BC-DB-ORA", "RefTitle": "ORA-27125 while starting Oracle instance", "RefUrl": "/notes/839624 "}, {"RefNumber": "1452070", "RefComponent": "BC-OP-ZLNX", "RefTitle": "DB2-z/OS: SAP on Linux on IBM Z and z/VM", "RefUrl": "/notes/1452070 "}, {"RefNumber": "531069", "RefComponent": "BC-OP-LNX", "RefTitle": "Heterogeneous Unix - Unix Systems", "RefUrl": "/notes/531069 "}, {"RefNumber": "405827", "RefComponent": "BC-OP-LNX", "RefTitle": "Linux: Recommended file systems", "RefUrl": "/notes/405827 "}, {"RefNumber": "1240731", "RefComponent": "BC-OP-LNX-SUSE", "RefTitle": "Linux: Incorrect write I/O on SLES9", "RefUrl": "/notes/1240731 "}, {"RefNumber": "1021236", "RefComponent": "BC-OP-LNX", "RefTitle": "Linux: Using SAP Kernel 7.01 and higher on RHEL4 and SLES9", "RefUrl": "/notes/1021236 "}, {"RefNumber": "784391", "RefComponent": "BC-OP-LNX", "RefTitle": "SAP support terms and 3rd-party Linux kernel drivers", "RefUrl": "/notes/784391 "}, {"RefNumber": "939677", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info. on upgrading to SAP NetWeaver 7.1 AS ABAP", "RefUrl": "/notes/939677 "}, {"RefNumber": "386605", "RefComponent": "BC-OP-LNX", "RefTitle": "SAP Memory Management for Linux (32-bit)", "RefUrl": "/notes/386605 "}, {"RefNumber": "941735", "RefComponent": "BC-OP-LNX", "RefTitle": "SAP memory management system for 64-bit Linux systems", "RefUrl": "/notes/941735 "}, {"RefNumber": "539921", "RefComponent": "BC-DB-ORA", "RefTitle": "Current patch set for Oracle 9.2.0", "RefUrl": "/notes/539921 "}, {"RefNumber": "306408", "RefComponent": "BC-DB-ORA", "RefTitle": "Oracle 9.2.0: Using OPatch to install patches", "RefUrl": "/notes/306408 "}, {"RefNumber": "1349794", "RefComponent": "BC-OP-LNX-JSEI", "RefTitle": "Linux: Analyzing problems with IBM JDK 1.4.2 Linux x86_64", "RefUrl": "/notes/1349794 "}, {"RefNumber": "20577", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Cpio cannot backup files larger than or equal to 2 GB", "RefUrl": "/notes/20577 "}, {"RefNumber": "914177", "RefComponent": "BC-DB-ORA", "RefTitle": "File system reiserfs Oracle parameter filesystemio_options", "RefUrl": "/notes/914177 "}, {"RefNumber": "825822", "RefComponent": "BC-OP-LNX-X64", "RefTitle": "Installation 4.6C SR2 on Linux x86_64 with DB2", "RefUrl": "/notes/825822 "}, {"RefNumber": "834343", "RefComponent": "BC-DB-ORA", "RefTitle": "Oracle 9.2 Platform Support for Async IO", "RefUrl": "/notes/834343 "}, {"RefNumber": "767814", "RefComponent": "BC-OP-LNX-SUSE", "RefTitle": "SUSE LINUX Enterprise Server 8: Installation notes", "RefUrl": "/notes/767814 "}, {"RefNumber": "1066142", "RefComponent": "BC-OP-LNX-JSEI", "RefTitle": "Linux: libj9vm22.so: cannot open shared object file", "RefUrl": "/notes/1066142 "}, {"RefNumber": "516716", "RefComponent": "BC-OP-LNX", "RefTitle": "Linux: Problems with locales after glibc update", "RefUrl": "/notes/516716 "}, {"RefNumber": "906403", "RefComponent": "BC-DB-ORA", "RefTitle": "ORA-7445 during DESCRIBE", "RefUrl": "/notes/906403 "}, {"RefNumber": "801415", "RefComponent": "BC-DB-DB6", "RefTitle": "DB6 Installation on Unix with db2_install", "RefUrl": "/notes/801415 "}, {"RefNumber": "788272", "RefComponent": "BC-DB-SDB", "RefTitle": "Compatibility of Linux Kernel 2.6/NPTL with MaxDB", "RefUrl": "/notes/788272 "}, {"RefNumber": "875206", "RefComponent": "BC-DB-ORA", "RefTitle": "Certifiction Oracle 9.2. on SLES9 Itanium", "RefUrl": "/notes/875206 "}, {"RefNumber": "597415", "RefComponent": "BC-OP-LNX", "RefTitle": "Logical volume manager (LVM) on Linux", "RefUrl": "/notes/597415 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": []}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}