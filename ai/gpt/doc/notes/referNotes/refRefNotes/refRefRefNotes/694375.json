{"Request": {"Number": "694375", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 753, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": false, "url": ""}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000694375?language=E&token=A13D7B6C7E0F4AA66138F6457A15ADD5"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000694375", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000694375/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "694375"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 1}, "Recency": {"_label": "Recency", "key": "", "value": ""}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Program error"}, "Priority": {"_label": "Priority", "value": "Correction with low priority"}, "Status": {"_label": "Release Status", "value": "In Process"}, "ReleasedOn": {"_label": "Released On", "value": "00.00.0000"}, "SAPComponentKey": {"_label": "Component", "value": "BC-CCM-MON-ORA"}, "SAPComponentKeyText": {"_label": "Component", "value": "Database Monitors for Oracle"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Basis Components", "value": "BC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Computer Center Management System (CCMS)", "value": "BC-CCM", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-CCM*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "CCMS Monitoring & Alerting", "value": "BC-CCM-MON", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-CCM-MON*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Database Monitors for Oracle", "value": "BC-CCM-MON-ORA", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-CCM-MON-ORA*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "694375 - Display V$ views(GV$ views):Oracle 9 monitor"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>Oracle database monitor to display the V$ views (and GV$ views), show<br />their contents and retrieve* back in time snapshots (conds apply).</p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>Oracle, Oracle 9i, V$ views, GV$ views, RAC, V$fixed_view_definition,<br />database monitors, st04n.</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>As of Oracle 9i (and eventually RAC), many new statistics-related views<br />have been added and existing ones updated. It has become imperative from<br />within SAP database screens to keep abreast with these latest changes by<br />creating a new monitor which deals with these views to make it easy<br />for the person concerned to administer them without the need for<br />hands-on SQLing.</p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p><br />This monitor aims to provide a list of all the views existing in Oracle<br />9.i single instance or RAC Database.<br />In addition, Some of the views have their 'contents' stored on<br />a regular basis into an SAP table because they are interesting from<br />the administrative and statistical points of view. This would permit us<br />to build a history feature which would provide the possibility to view<br />the content of the view and to make a query to display data at any point<br />in time (view conditions below).<br /><br />Oracle 9i-single instance:<br /><br />The monitor shows a list of all the V$ views available in the<br />the instance.<br />As the ABAP GUI objects are used in this monitor, a tabstrip will<br />display the data required through a Grid.<br />A double click on a row of the single-tab tabstrip shows the contents<br />of each view in the same tab.<br />For each V$ view that has a corresponding table in SAP dictionary,<br />a history will be provided. These views are displayed in small letters<br />within the grid. The history will be shown in the same tab<br />by choosing the From To and Up To timestamps. The list of the V$ views<br />in the tab will be arranged into a two-column list taking into account<br />the alphabetical order of the views' names.<br /><br /><br />RAC system:<br /><br />The monitor in this case, displays both the V$ views and the GV$ views<br />within a two-tab Tabstrip.<br />For each instance selected, the first tab will display the<br />corresponding V$ views and the second one will display the GV$ views.<br />As for the single instance case, data will&#x00A0;&#x00A0;be arranged into a<br />two-column list for both tabs and the Views which have a corresponding<br />table in SAP will be displayed in small letters.<br />The history will be shown in the same tab by choosing the From To<br />and Up To timestamps.<br />The layout of the table will contain two columns:<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;V$ views..&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; ..V$ views<br />The layout of the second table will contain two columns:<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;GV$ views..&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; ..GV$ views<br /><br />For both tabs, when double-clicking on one row, the contents of the<br />selected view is displayed in the same tab (and the caption of the tab<br />will bear the name of the selected view).<br /><br />We should mention that the GV$ views are not affected by instance<br />selection as these views are global. On the other hand, the V$ views<br />will not provide any result when the Total (over the instances) is<br />selected.<br /><br />Actually not all the views stored in SAP can be queried using both<br />From To and Up To timestamps. Indeed, some of the views which are<br />dealing with memory space calculation, statistics or time computation<br />can provide us with a significant information about the historical<br />changes the related objects underwent. For this case, it is quite useful<br />to query these tables using both From To and Up To timestamps in order<br />to view the relevant differences in this time window. For the views<br />which are not accumulating quantity-related records, i.e. which are<br />only used to display nearly-static like information, differences have<br />no meaning and so, displaying only a point in time snapshot would be<br />sufficient. For the latter case, it is only possible to select the From<br />To timestamp in order to view the data at both database start-up and<br />at any subsequent point in time.<br />Some features (or services) in Oracle have their own views.<br />When such features are not active, their related views will not provide<br />any result. One such feature is the Oracle Log Miner. Its related views (logmnr_*) can not be queried unless the service is up and running.<br /><br /><br /><br /><br /><br /><br /><br /><br /><br /><br /><br /><br /><br /></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "I023385"}, {"Key": "Processor                                                                                           ", "Value": "I023385"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000694375/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000694375/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000694375/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000694375/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000694375/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000694375/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000694375/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000694375/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000694375/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "598678", "RefComponent": "BC-DB-ORA", "RefTitle": "Oracle 9i: New functions", "RefUrl": "/notes/598678"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "598678", "RefComponent": "BC-DB-ORA", "RefTitle": "Oracle 9i: New functions", "RefUrl": "/notes/598678 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_BASIS", "From": "46C", "To": "46D", "Subsequent": ""}, {"SoftwareComponent": "SAP_BASIS", "From": "610", "To": "640", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}