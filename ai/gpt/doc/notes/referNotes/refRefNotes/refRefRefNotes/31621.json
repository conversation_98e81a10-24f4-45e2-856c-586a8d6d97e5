{"Request": {"Number": "31621", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 7525, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000014377152017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000031621?language=E&token=E4CAD2DCF508EB2B949EBD6469FA9CFB"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000031621", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000031621/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "31621"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 8}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Consulting"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "24.09.1999"}, "SAPComponentKey": {"_label": "Component", "value": "BC-BMT-OM"}, "SAPComponentKeyText": {"_label": "Component", "value": "Organizational Management"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Basis Components", "value": "BC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Business Management", "value": "BC-BMT", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-BMT*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Organizational Management", "value": "BC-BMT-OM", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-BMT-OM*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "31621 - PD and workflow application do not run correctly"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>Applications in Personnel Planning and Development (PD) as well as workflow applications either do not function at all or only partially. This applies in particular to Organizational Management!<br />Error messages may be output because of missing authorizations, where Transaction SU53 reports that all authorization checks were successful (these are what are known as PD authorizations).</p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>Missing table entries for tables T77* (generic display!)<br />Missing evaluation paths (T778A)</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>The client in question was set up by the client copier in a 2.X system. The system was meanwhile upgraded. The client copier was started so that the table contents of all the HR tables were not copied.</p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>Caution! Some of the missing table entries are Customizing entries which means that the Customizing settings of the organizational management are reset to the standard in the delivery by the client copier.<br />Release 3.0:<br />Use report RSCLTCOP to copy all table contents of tables T77* (generic entry !) from client 000 into the client in question. After you have done this, you should carry out Customizing for Personnel Planning and Development (and for Workflow).<br />Release 3.1:<br />The report RHTTCP76 is located on the Sapserv in the directory<br /><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; /general/R3server/abap/note.0031621/V31<br /><br />Refer to the remarks on Release 4.x. You can alternatively use the report RSCLTCOP, this always produces the delivery status of Customizing.<br />Release 4.x:<br />On the sapserv, report RHTTCP77 is made available.<br />The report is in the directory<br /><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; /general/R3server/abap/note.0031621<br /><br />Also refer to Note 13719 for the procedure.<br />This report has a test mode with which you can display the entries to be changed for the selected tables.<br />Start the report first in the test mode, enter a target client as well as a table name or 'T77*' for all tables. In the following list, you can display the entries to be copied for each table and in addition the table contents in client 000 and in the respective target client.<br />In the 'Insert' mode, only missing entries are copied from client 000, in the 'Modify' mode, only entries are changed which are different in the target client than in the client 000. Attempt to solve the problems in the 'Insert' mode first. First attempt to solve the problem in the 'Insert' mode.</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "BC-BMT-WFM (Business Workflow)"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON><PERSON> (D021534)"}, {"Key": "Processor                                                                                           ", "Value": ""}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000031621/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000031621/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000031621/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000031621/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000031621/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000031621/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000031621/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000031621/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000031621/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "53158", "RefComponent": "BC-CUS", "RefTitle": "Error messages after Customizing verification", "RefUrl": "/notes/53158"}, {"RefNumber": "51025", "RefComponent": "BC-BMT-WFM", "RefTitle": "PD/Workflow applications do not work correctly", "RefUrl": "/notes/51025"}, {"RefNumber": "50076", "RefComponent": "BC-BMT-OM-OM", "RefTitle": "HR-OM: Structural organization - no authorization", "RefUrl": "/notes/50076"}, {"RefNumber": "43970", "RefComponent": "BC-BMT-OM", "RefTitle": "SAPLRHGB RHORGMAN RAISE_EXCEPTION ROOT_NOT_FOUND", "RefUrl": "/notes/43970"}, {"RefNumber": "391717", "RefComponent": "PA-BC-BS", "RefTitle": "Problems with report RHTTCP77", "RefUrl": "/notes/391717"}, {"RefNumber": "382528", "RefComponent": "CO-OM-CCA", "RefTitle": "Termination in the hierarchy framework", "RefUrl": "/notes/382528"}, {"RefNumber": "334104", "RefComponent": "IS-U-CS-BT-IO", "RefTitle": "ACB: Workflow \"automatic owner allocation\" abends", "RefUrl": "/notes/334104"}, {"RefNumber": "30575", "RefComponent": "BC-BMT-OM", "RefTitle": "PD: Transactions PPOM/OOOE cannot be accessed", "RefUrl": "/notes/30575"}, {"RefNumber": "177844", "RefComponent": "CO-OM-CCA-A", "RefTitle": "Enterprise org., standard hierarchy does not work", "RefUrl": "/notes/177844"}, {"RefNumber": "175063", "RefComponent": "CO-OM-CCA-A", "RefTitle": "Msg 5A252 whn displying/changing standard hierarchy", "RefUrl": "/notes/175063"}, {"RefNumber": "138411", "RefComponent": "BC-BMT-OM", "RefTitle": "Collective note: PD Transports", "RefUrl": "/notes/138411"}, {"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1938122", "RefComponent": "BC-BMT-WFM", "RefTitle": "Error message \"Task does not exist - 5W304\" in transaction PFTC", "RefUrl": "/notes/1938122 "}, {"RefNumber": "2851095", "RefComponent": "BC-BMT-WFM", "RefTitle": "Message no. 5A040  Task Group not found", "RefUrl": "/notes/2851095 "}, {"RefNumber": "2366252", "RefComponent": "BC-BMT-WFM", "RefTitle": "Transaction SWU3 explained", "RefUrl": "/notes/2366252 "}, {"RefNumber": "53158", "RefComponent": "BC-CUS", "RefTitle": "Error messages after Customizing verification", "RefUrl": "/notes/53158 "}, {"RefNumber": "138411", "RefComponent": "BC-BMT-OM", "RefTitle": "Collective note: PD Transports", "RefUrl": "/notes/138411 "}, {"RefNumber": "382528", "RefComponent": "CO-OM-CCA", "RefTitle": "Termination in the hierarchy framework", "RefUrl": "/notes/382528 "}, {"RefNumber": "391717", "RefComponent": "PA-BC-BS", "RefTitle": "Problems with report RHTTCP77", "RefUrl": "/notes/391717 "}, {"RefNumber": "175063", "RefComponent": "CO-OM-CCA-A", "RefTitle": "Msg 5A252 whn displying/changing standard hierarchy", "RefUrl": "/notes/175063 "}, {"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719 "}, {"RefNumber": "334104", "RefComponent": "IS-U-CS-BT-IO", "RefTitle": "ACB: Workflow \"automatic owner allocation\" abends", "RefUrl": "/notes/334104 "}, {"RefNumber": "177844", "RefComponent": "CO-OM-CCA-A", "RefTitle": "Enterprise org., standard hierarchy does not work", "RefUrl": "/notes/177844 "}, {"RefNumber": "50076", "RefComponent": "BC-BMT-OM-OM", "RefTitle": "HR-OM: Structural organization - no authorization", "RefUrl": "/notes/50076 "}, {"RefNumber": "30575", "RefComponent": "BC-BMT-OM", "RefTitle": "PD: Transactions PPOM/OOOE cannot be accessed", "RefUrl": "/notes/30575 "}, {"RefNumber": "51025", "RefComponent": "BC-BMT-WFM", "RefTitle": "PD/Workflow applications do not work correctly", "RefUrl": "/notes/51025 "}, {"RefNumber": "43970", "RefComponent": "BC-BMT-OM", "RefTitle": "SAPLRHGB RHORGMAN RAISE_EXCEPTION ROOT_NOT_FOUND", "RefUrl": "/notes/43970 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_APPL", "From": "300", "To": "31I", "Subsequent": "X"}, {"SoftwareComponent": "SAP_APPL", "From": "40A", "To": "40B", "Subsequent": "X"}, {"SoftwareComponent": "SAP_APPL", "From": "45A", "To": "45B", "Subsequent": "X"}, {"SoftwareComponent": "SAP_APPL", "From": "46A", "To": "46B", "Subsequent": "X"}, {"SoftwareComponent": "SAP_APPL", "From": "46C", "To": "46C", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}