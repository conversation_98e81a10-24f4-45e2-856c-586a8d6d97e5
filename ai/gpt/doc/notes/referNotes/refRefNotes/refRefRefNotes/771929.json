{"Request": {"Number": "771929", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 371, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000015757102017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000771929?language=E&token=0362BFBF3A5341775BA96596831DD726"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000771929", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000771929/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "771929"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 40}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "FAQ"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "04.08.2010"}, "SAPComponentKey": {"_label": "Component", "value": "BC-DB-ORA"}, "SAPComponentKeyText": {"_label": "Component", "value": "Oracle"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Basis Components", "value": "BC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Database Interface, Database Platforms", "value": "BC-DB", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-DB*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Oracle", "value": "BC-DB-ORA", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-DB-ORA*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "771929 - FAQ: Index fragmentation"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><OL>1. How are indexes built?</OL> <OL>2. What does index fragmentation mean?</OL> <OL>3. What problems are caused by index fragmentation?</OL> <OL>4. What triggers an index fragmentation?</OL> <OL>5. Which SAP tables typically undergo index fragmentation?</OL> <OL>6. How can I measure index fragmentation?</OL> <OL>7. As of which percentage value do I need to rebuild indexes?</OL> <OL>8. Which restrictions exist for these methods?</OL> <OL>9. How can I detect fragmented indexes?</OL> <OL>10. Why can I not use ANALYZE INDEX VALIDATE STRUCTURE ONLINE?</OL> <OL>11. Which Oracle commands can I use to defragment an index?</OL> <OL>12. Which SAP tools can I use to defragment an index?<br /></OL><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>FAQ, index fragmentation</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><OL>1. How are indexes built?</OL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;An index contains the values of the indexed table columns in a sorted order. <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;A normal B* tree index is a tree made up of blocks with up to five levels. The highest level consists of only one block which is called root block and which is the basis for every index access. The lowest level consists of a potentially very large number of blocks that are called leaf blocks and that contain the actual data from left to right in a sorted order (values of the indexed columns and ROWID for the access to the relevant table entry). The entries are called leaf rows. The leaf rows also include earlier index entries that were not physically removed from the index, but were only marked as deleted. This special type of leaf row is called a deleted leaf row. <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Depending on the size of the index, additional index levels with branch blocks may exist between the root blocks and leaf blocks, and these contain control data for a fast search of the relevant data in the index. <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;If an index only consists of one level, the root block is also a leaf block and contains the actual data. If an index consists of several levels, the root block can be compared with the branch blocks and contains information about navigating in the index. <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;In general, an index mainly consists of leaf blocks. If there is a very large number of index entries or if a COALESCE has been executed for a fragmented index, the number of branch blocks may be comparatively high. <OL>2. What does index fragmentation mean?</OL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;The fragmentation of an index specifies how well the space is used in the B* tree of an index. The smaller the space that is actually used, the more fragmented the index. In this case, the index may also be called unbalanced or degenerated. <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;If the space outside the structure of the index B* tree in an index segment is not used, then this is not strictly speaking index fragmentation. In this case, the use of space is simply not optimal. For more information, also see Note 821687. <OL>3. What problems are caused by index fragmentation?</OL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Index fragmentation causes the following problems: <UL><LI>Database growth</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;The greater the index fragmentation, the more space is unused in the index B* tree. This means that the index segment and the entire database grow more quickly than is necessary.</p> <UL><LI>Poor performance by the index range scan</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;If an index is fragmented, then index accesses using the index range scan may have to read more blocks than is actually necessary. The 'buffer gets' and 'disk reads' associated with this process increase the access time considerably.</p> <UL><LI>Poor global performance as a result of block displacements in the buffer pool</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;If, due to index fragmentation, more blocks must be read into the Oracle buffer pool than is strictly necessary, blocks are unnecessarily displaced from other segments. This in turn means that the buffer must be repeatedly reloaded, which can impede the performance of the entire database.</p> <OL>4. What triggers an index fragmentation?</OL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Index fragmentation usually occurs when data is repeatedly deleted from the index and reinserted at a different location. In particular, this happens when a field is indexed whose values increase with time. If older entries are deleted, it is always from the left side of the index while new entries are always inserted on the right side of the index. As a result, the index tree is more or less empty on the left side, but keeps growing on the right side. <OL>5. Which SAP tables typically undergo index fragmentation?</OL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;In particular, index fragmentation affects tables that are subject to considerable data changes and that also index any column values that increase with time. RFC tables, in particular, belong to this category. For more information, see Notes 706478 and 435125. <OL>6. How can I measure index fragmentation?</OL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;There are two approaches for measuring index fragmentation: <UL><LI>Storage quality</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;The storage quality compares the available space with the space used, whereby the two values are roughly determined as follows:</p> <UL><UL><LI>The available space is a result of the number of index blocks used multiplied by the block size (8192 bytes) minus the administration overhead for the block header and similar.</LI></UL></UL> <UL><UL><LI>The space that is filled is a result of the number of index entries multiplied by the average length of an entry plus 6 bytes for the ROWID (-&gt; space used in the leaf blocks), bytes for marking the start of a record or column, and an overhead for the root blocks and branch blocks.</LI></UL></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;The smaller the space actually used in comparison with the total available space, the lower the index storage quality and the more fragmented the index.</p> <UL><LI>Leaf row quality</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;The number of deleted leaf rows compared with all leaf rows is another measure for index fragmentation. The larger the portion of the deleted leaf rows compared with all leaf rows, the more fragmented the index.</p> <OL>7. As of which percentage value do I need to rebuild indexes?</OL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;It is not possible to give a general recommendation for the percentage value index at which fragmentation becomes critical. However, empirical values have the following tendencies: <UL><LI>Storage quality of 50% or higher: No action required</LI></UL> <UL><LI>Storage quality between 25% and 50%: Action required in individual cases</LI></UL> <UL><LI>Storage quality of 25% or lower: The index should be rebuilt.</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;It is logical to rebuild an index if it is clear that it occupies an unnecessarily large number of blocks in the buffer pool or that it is responsible for a large number of 'buffer gets' as a result of the fragmentation. <OL>8. Which restrictions exist for these methods?</OL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;You must bear the following restrictions in mind when determining the storage quality: <UL><LI>Especially with small indexes, the space used is often very small compared with the available space (for example, a 20-byte entry in an 8K leaf block if the index only contains one row). Therefore, for small indexes, the storage quality is only useful to a certain extent. This is not a severe restriction because small indexes usually do not trigger performance problems due to fragmentation.</LI></UL> <UL><LI>Generally speaking, it is difficult to correctly include ALL factors that contribute to the space calculations. For this reason, the mechanism of the index block splits, block-internal fragmentation due to record lengths, or storage parameters such as PCTFREE also play significant roles in terms of how much space can potentially be used.</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;The method of the deleted leaf rows has the following restrictions: <UL><LI>If an entry is added to a leaf block, all deleted leaf rows are removed from this block. The block may then be almost empty. However, this is not recognized because deleted leaf rows no longer exist.</LI></UL><UL><LI>The deleted leaf rows only ever refer to the leaf blocks. The number of existing branch blocks is ignored. For example, a COALESCE always results in 0 deleted leaf rows even though the index still has the same number of branch blocks and therefore can still be fragmented.</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;For these reasons, both methods can only be an INDICATOR of the level of index fragmentation, but they can never PROVE it. Therefore, fixed thresholds as of which we could say that an index is fragmented in a way that affects performance cannot be specified for the deleted leaf rows and the storage quality. <OL>9. How can I detect fragmented indexes?</OL> <UL><LI>Oracle Segment Advisor (Oracle 10g or higher)</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Based on the Oracle Automatic Segment Advisor information (Note 927813), fragmentation information can be determined relatively quickly.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Advantages:</p> <UL><UL><LI>A lock is not necessary.</LI></UL></UL> <UL><UL><LI>There is a low runtime for a query because data collection runs independent of this.</LI></UL></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Disadvantages:</p> <UL><UL><LI>There is a certain system load during data collection.</LI></UL></UL> <UL><LI>Report RSORATAD</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;To determine the index storage quality, SAP provides the RSORATAD report, which you can call directly in transaction SE38, or by using:<br /><br />DB02<br />-&gt; Detailed Analysis<br />-&gt; Enter Index<br />-&gt; Detailed Analysis<br />-&gt; Analyze Index<br />-&gt; Storage Quality<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;For more information, see Note 444287.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Advantages:</p> <UL><UL><LI>A lock is not necessary.</LI></UL></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Disadvantages:</p> <UL><UL><LI>It can only be executed for one index at a time.</LI></UL></UL> <UL><UL><LI>It has a long runtime.</LI></UL></UL> <UL><UL><LI>It causes a high system load.</LI></UL></UL> <UL><UL><LI>There is no support for bitmap indexes and partitioned indexes.</LI></UL></UL> <UL><UL><LI>You can only trust the results up to a point because B*TREE blocks on the FREELIST are not included in the calculation.</LI></UL></UL> <UL><LI>Report RSORAISQN</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Unlike RSORATAD, you can use RSORAISQN to start an analysis of several or all indexes. For more information, see Note 970538.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Advantages:</p> <UL><UL><LI>A lock is not necessary.</LI></UL></UL> <UL><UL><LI>You can access historical data.</LI></UL></UL> <UL><UL><LI>You can analyze several or all indexes with one call.</LI></UL></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Disadvantages:</p> <UL><UL><LI>It has a long runtime.</LI></UL></UL> <UL><UL><LI>It causes a high system load.</LI></UL></UL> <UL><UL><LI>There is no support for bitmap indexes and partitioned indexes.</LI></UL></UL> <UL><UL><LI>You can only trust the result up to a point because B*TREE blocks on the FREELIST are not included in the calculation. Therefore, a significantly higher storage quality may be calculated.</LI></UL></UL> <UL><LI>ANALYZE INDEX VALIDATE STRUCTURE</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;You can use the Oracle command ANALYZE INDEX VALIDATE STRUCTURE to determine both the storage quality and the leaf row quality of an index. Note 444287 describes how you can execute the command in transaction DB02 or at Oracle level, and how you can interpret the results.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Advantages:</p> <UL><UL><LI>Since it is script-based, you can analyze ALL indexes.</LI></UL></UL> <UL><UL><LI>It retrieves exact information about the index fragmentation.</LI></UL></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Disadvantages:</p> <UL><UL><LI>The table is locked for DML statements (INSERT, UPDATE, DELETE) during the analysis.</LI></UL></UL> <UL><UL><LI>It has a long runtime.</LI></UL></UL> <UL><UL><LI>It causes a high system load.</LI></UL></UL> <UL><LI>BRCONNECT (option INDEX_STORE)</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;You can use the BRCONNECT option INDEX_STORE to determine the storage quality of indexes on the basis of ANALYZE TABLE VALIDATE STRUCTURE:<br /><br />brconnect -u / -c -f stats -t &lt;tables&gt; -v index_store<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;The results are stored in the DBSTATIORA table. The storage quality is determined by the relationship of the column values INDRU and INDBS. For more detailed information, see Note 554031.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Advantages:</p> <UL><UL><LI>Any number of indexes can be analyzed in a run.</LI></UL></UL> <UL><UL><LI>It retrieves exact information about the index fragmentation.</LI></UL></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Disadvantages:</p> <UL><UL><LI>The table is locked for DML statements (INSERT, UPDATE, DELETE) during the analysis.</LI></UL></UL> <UL><UL><LI>It has a long runtime.</LI></UL></UL> <UL><UL><LI>It causes a high system load.</LI></UL></UL> <UL><LI>Index tree dump</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;You can use the object ID of an index to create a tree dump of the index from which you can retrieve information about leaf rows and deleted leaf rows:<br /><br />SELECT OBJECT_ID FROM DBA_OBJECTS<br />WHERE OBJECT_NAME = '&lt;index_name&gt;';<br /><br />ALTER SESSION SET EVENTS<br />'immediate trace name treedump level &lt;object_id&gt;';<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;The tree dump is stored in the saptrace/usertrace directory and contains a row for each index block. For example:<br /><br />----- begin tree dump<br />branch: 0x4437342 71529282 (0: nrow: 7, level: 2)<br />&#x00A0;&#x00A0; branch: 0x8d5bab 9264043 (-1: nrow: 671, level: 1)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;leaf: 0x4437343 71529283 (-1: nrow: 540 rrow: 540)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;leaf: 0x4437344 71529284 (0: nrow: 533 rrow: 522)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;leaf: 0x4437345 71529285 (1: nrow: 533 rrow: 517)<br />...<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;In the next step, you can display the leaf block rows (that are preceded by \"leaf:\" in the example above).&#x00A0;&#x00A0; The number behind 'nrow' is the number of leaf rows in the block while 'rrow' specifies the number of leaf rows that actually exist. Therefore, the difference between the two values is the number of deleted leaf rows in the block. If you add 'nrow' and 'nrow - rrow' for all leaf block rows, you get the number of leaf rows and deleted leaf rows in the entire index. You can use a script to automate this calculation. Example (&lt;dump_file&gt;: name of the tree dump file):<br /><br />grep leaf &lt;dump_file&gt; | perl -p -i -e 's/.*nrow: (.*) rrow:<br />(.*)\\)/$1\\t$2/' | perl -e 'while (&lt;&gt;) { $leaf_rows += (split)[0];<br />$del_leaf_rows += (split)[0] - (split)[1];} print \"Leaf Rows:<br />$leaf_rows\\nDeleted Leaf Rows: $del_leaf_rows\\nDeleted Leaf Rows<br />(%): \" . int(($del_leaf_rows / $leaf_rows) * 100 + 0.5) . \"\\n\"'<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Advantages:</p> <UL><UL><LI>A lock is not necessary.</LI></UL></UL> <UL><UL><LI>It retrieves exact information about the index fragmentation.</LI></UL></UL> <UL><UL><LI>It has a slightly shorter runtime than VALIDATE STRUCTURE.</LI></UL></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Disadvantages:</p> <UL><UL><LI>It has a long runtime.</LI></UL></UL> <UL><UL><LI>It causes a high system load.</LI></UL></UL> <UL><UL><LI>Space is required for the dump file.</LI></UL></UL> <UL><UL><LI>You require an analysis script for the tree dump file.</LI></UL></UL> <UL><UL><LI>The format of the dump file may vary between different Oracle releases.</LI></UL></UL> <UL><LI>CBO statistics</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;When creating CBO statistics, information that can be used to determine the storage quality of indexes is collected (for example, average column length, number of index leaf blocks, and number of index entries). Provided that the statistics are up-to-date and not too inaccurate, you can use them to determine the approximate storage quality of indexes. You can use the command \"Space_SegmentFragmentation_CBOStatistics.txt\" from Note 1438410 for determining fragmented indexes.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Advantages</p> <UL><UL><LI>A lock is not necessary.</LI></UL></UL> <UL><UL><LI>The system load is insignificant.</LI></UL></UL> <UL><UL><LI>It has a short runtime.</LI></UL></UL> <UL><UL><LI>It provides relatively precise information regarding index fragmentation.</LI></UL></UL> <UL><UL><LI>(provided that the statistics are adequate)</LI></UL></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Disadvantages</p> <UL><UL><LI>It does not provide any information about indexes without statistics.</LI></UL></UL> <UL><UL><LI>The quality of the result relies heavily on the available CBO statistics being up-to-date and accurate.</LI></UL></UL> <UL><UL><LI>Fragmentation effects in root blocks and branch blocks are not examined.</LI></UL></UL> <UL><UL><LI>It cannot be used for bitmap indexes.</LI></UL></UL> <UL><LI>RSORAISQN</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;The new RSORAISQN function described in Notes 970538 and 979054 combines the analysis options of RSORAISQ (\"Exact\" analysis) with the approach to CBO statistics (\"Fast\" analysis). The advantages and disadvantages are the same as those associated with RSORAISQ or the approach to CBO statistics.</p> <UL><LI>BR986W warnings during BRCONNECT statistical runs</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Check the log files of the BRCONNECT statistical runs for entries of the following type:<br /><br />BR986W Index &lt;index&gt; is unbalanced - please rebuild the index<br />BR0986W index &lt;index&gt; is unbalanced - please rebuild the index<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;These entries indicate that the index is fragmented. The system always reports the primary index to determine the necessity of statistics, since this is the only one analyzed by BRCONNECT. However, ALL of the indexes of the relevant tables should be rebuilt because it is likely that all indexes will be fragmented to a certain extent.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Advantages:</p> <UL><UL><LI>A lock is not necessary.</LI></UL></UL> <UL><UL><LI>An additional system load is not caused because it is a by-product of the statistics that are generated anyway.</LI></UL></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Disadvantages:</p> <UL><UL><LI>There is no guarantee that all fragmented indexes are recognized or that the index mentioned in the warning is actually fragmented (see Note 439783).</LI></UL></UL> <UL><LI>Index size</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Highly fragmented large indexes, in particular, can often be detected by comparing the table sizes (for example, in transaction DB02). If an index is larger than the table, this is an indicator that the index is fragmented. It is rare that all of the most important table columns are contained in the same index.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Even if the index is smaller than the table, you may detect a possible fragmentation when you use a plausibility check based on the indexed columns and typical column entries. For example, if an index only indexes two columns with a maximum of five characters each, while the entries in the table contain a large number of additional longer columns, the index is probably already fragmented even if it is only half the size of the table.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Advantages</p> <UL><UL><LI>There is no significant system load.</LI></UL></UL> <UL><UL><LI>The runtime is short.</LI></UL></UL> <UL><UL><LI>A lock is not necessary.</LI></UL></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Disadvantages</p> <UL><UL><LI>It is only a very rough indicator.</LI></UL></UL> <UL><UL><LI>Blocks that were already allocated during an extent, but are not yet contained in the index tree, are included in the calculation even though they are irrelevant for the index fragmentation.</LI></UL></UL> <UL><LI>Index blocks in the Oracle buffer pool</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;If an index is fragmented, more blocks must be kept in the Oracle buffer pool. Therefore, it may be useful to determine the indexes with the highest number of blocks in the buffer pool so that you can subsequently check for fragmentation. You can use the SQL statement contained in Note 789011 to determine the objects with the most blocks in the pool buffer.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Advantages:</p> <UL><UL><LI>The system load is low.</LI></UL></UL> <UL><UL><LI>The runtime is short.</LI></UL></UL> <UL><UL><LI>A lock is not necessary.</LI></UL></UL> <UL><UL><LI>It analyzes all indexes at the same time, and not only one index.</LI></UL></UL> <UL><UL><LI>It returns the indexes that currently have a lot of memory allocated to them, and that are therefore also relevant for performance.</LI></UL></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Disadvantages:</p> <UL><UL><LI>It is only a rough guideline that also often returns indexes that are not fragmented.</LI></UL></UL> <UL><UL><LI>Indexes that currently cannot be accessed are ignored because they do not have any blocks or only a few blocks in the buffer pool.</LI></UL></UL> <UL><LI>Block accesses during each execution</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;You can also use the block accesses during each execution, which can be determined with an SQL analysis (see Note 766349) in the shared cursor cache, as an indicator for a fragmented index. This means, for example, that an INDEX RANGE SCAN with a subsequent TABLE ACCESS BY INDEX ROWID in which 100 entries are read, does not require much more than 200 block accesses if all selective columns in the selection condition are specified with \"=\". If the actual values are higher, this is often due to the effects of an index fragmentation.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Advantages:</p> <UL><UL><LI>It provides very precise information about the influence of the fragmentation on the number of the block accesses (and therefore on performance).</LI></UL></UL> <UL><UL><LI>The system load is low.</LI></UL></UL> <UL><UL><LI>The runtime is short.</LI></UL></UL> <UL><UL><LI>A lock is not necessary.</LI></UL></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Disadvantages:</p> <UL><UL><LI>You require good knowledge of SQL optimization.</LI></UL></UL> <UL><UL><LI>You cannot always come to a conclusion.</LI></UL></UL> <UL><UL><LI>An increased number of block accesses may also have other causes (such as parallel changes or roundtrips).</LI></UL></UL> <UL><LI>Space utilization of the underlying table</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;If a table has a lot of free space, there may be fragmentation effects even in the indexes created on this table. Typically, this situation arises after a large amount of data is deleted from the table. For more information, see Note 821687. Point 5 describes how to identify tables with a low rate of utilization.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Advantages:</p> <UL><UL><LI>The system load is low.</LI></UL></UL> <UL><UL><LI>The runtime is short.</LI></UL></UL> <UL><UL><LI>A lock is not necessary.</LI></UL></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Disadvantages:</p> <UL><UL><LI>It ignores the indexes of tables that have no statistics.</LI></UL></UL> <UL><UL><LI>It ignores index fragmentation that is not triggered by mass deletion.</LI></UL></UL> <UL><LI>SAMPLE_SIZE much smaller than the selected statistic sample size</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;If the SAMPLE_SIZE to be found in DBA_TABLES is significantly smaller than the sample size selected when creating the statistics, this is a indicator of table fragmentation and therefore also index fragmentation. To determine these tables, proceed as described in Note 588668 (19).<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Advantages:</p> <UL><UL><LI>The system load is low.</LI></UL></UL> <UL><UL><LI>The runtime is short.</LI></UL></UL> <UL><UL><LI>A lock is not necessary.</LI></UL></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Disadvantages:</p> <UL><UL><LI>It ignores the indexes of tables that have no statistics.</LI></UL></UL> <UL><UL><LI>It ignores index fragmentation that does not correlate with table fragmentation.</LI></UL></UL> <UL><UL><LI>The sample size may be significantly below the standard BRCONNECT value for other reasons too, such as manually generated statistics.</LI></UL></UL> <UL><LI>Oracle 10g or higher: DBMS_SPACE function</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;As of Oracle 10g, the DBMS_SPACE function (described in Note 1295200) is available. You can use this function to determine index fragmentation, among other things. The Oracle Segment Advisor (Note 927813) is also based on this function and therefore can be used to determine index fragmentation.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Advantages:</p> <UL><UL><LI>A lock is not necessary.</LI></UL></UL> <UL><UL><LI>Exact information about used and allocated space</LI></UL></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Disadvantages:</p> <UL><UL><LI>It causes a high system load.</LI></UL></UL> <UL><UL><LI>It has a long runtime.</LI></UL></UL> <UL><UL><LI>It is available only with Oracle 10g or higher.</LI></UL></UL> <OL>10. Why can I not use ANALYZE INDEX VALIDATE STRUCTURE ONLINE?</OL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;You cannot use the online variant of ANALYZE INDEX VALIDATE STRUCTURE to determine index fragmentation because it does not return the necessary data for the leaf rows and the deleted leaf rows. Oracle regards this as a feature rather than a bug. <OL>11. Which Oracle commands can I use to defragment an index?</OL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;You have the following options to defragment an index: <UL><LI>DROP and CREATE</LI></UL> <UL><LI>REBUILD</LI></UL> <UL><LI>COALESCE</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;For a description of these options, including their advantages and disadvantages, see Note 332677.&#x00A0;&#x00A0;In the case of REBUILD ONLINE operations, see also the problems described in Note 682926. <OL>12. Which SAP tools can I use to defragment an index?</OL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;SAP delivers the following tools that are based on the Oracle commands described above: <UL><LI>brspace -f idrebuild -i &lt;index_list&gt;</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;With this BRSPACE function, you can use the REBUILD command to rebuild a list of indexes. For detailed information about BRSPACE, refer to the SAP online documentation for SAP Web Application Server 6.40 and higher, and also to SAP Service Marketplace under the alias DBAORA.</p> <UL><LI>Report RSANAORA</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;You can use the RSANAORA report to rebuild an index (by using the REBUILD command).</p> <UL><LI>Transaction DB02</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;You can use transaction DB02 to defragment an index as follows:<br /><br />DB02<br />-&gt; Detailed Analysis (in the \"Tables and Indexes\" section)<br />-&gt; Object Name: &lt;index&gt;<br />-&gt; Detailed Analysis<br />-&gt; Alter Index<br />-&gt; Coalesce / Rebuild</p> <UL><LI>Report RSORAISQN</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;The RSORAISQN report (Note 970538) allows you to rebuild indexes based on various criteria, such as index storage quality, for example.</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D030484)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON> (D021978)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000771929/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000771929/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000771929/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000771929/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000771929/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000771929/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000771929/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000771929/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000771929/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "979054", "RefComponent": "BC-DB-ORA", "RefTitle": "RSORAISQN", "RefUrl": "/notes/979054"}, {"RefNumber": "970538", "RefComponent": "BC-DB-ORA", "RefTitle": "Collective note RSORAISQN", "RefUrl": "/notes/970538"}, {"RefNumber": "928037", "RefComponent": "BC-DB-SDB", "RefTitle": "FAQ: SAP MaxDB indexes", "RefUrl": "/notes/928037"}, {"RefNumber": "927813", "RefComponent": "BC-DB-ORA", "RefTitle": "Oracle 10g: Using Oracle Segment Advisor to optimize space", "RefUrl": "/notes/927813"}, {"RefNumber": "915242", "RefComponent": "BC-DB-ORA", "RefTitle": "FAQ: Reverse key indexes", "RefUrl": "/notes/915242"}, {"RefNumber": "912620", "RefComponent": "BC-DB-ORA", "RefTitle": "FAQ: Oracle indexes", "RefUrl": "/notes/912620"}, {"RefNumber": "910389", "RefComponent": "BC-DB-ORA", "RefTitle": "FAQ: Oracle Segment Shrinking", "RefUrl": "/notes/910389"}, {"RefNumber": "883346", "RefComponent": "CRM-MW-SRV", "RefTitle": "Performance problems in realignment framework", "RefUrl": "/notes/883346"}, {"RefNumber": "882425", "RefComponent": "CRM-MW-COM", "RefTitle": "ConnTrans performance: Long Confirmation times", "RefUrl": "/notes/882425"}, {"RefNumber": "880580", "RefComponent": "CRM-MW-SRV", "RefTitle": "Index rebuild on Lookup tables for R&R Performance Improvmnt", "RefUrl": "/notes/880580"}, {"RefNumber": "875798", "RefComponent": "CRM-MW-SRV", "RefTitle": "Index rebuilds (Oracle) for R&R specific CRM tables", "RefUrl": "/notes/875798"}, {"RefNumber": "825653", "RefComponent": "BC-DB-ORA", "RefTitle": "Oracle: Popular misconceptions", "RefUrl": "/notes/825653"}, {"RefNumber": "821687", "RefComponent": "BC-DB-ORA", "RefTitle": "FAQ: Space utilization and fragmentation in Oracle", "RefUrl": "/notes/821687"}, {"RefNumber": "806554", "RefComponent": "BC-DB-ORA", "RefTitle": "FAQ: I/O-intensive database operations", "RefUrl": "/notes/806554"}, {"RefNumber": "789011", "RefComponent": "BC-DB-ORA", "RefTitle": "FAQ: Oracle memory areas", "RefUrl": "/notes/789011"}, {"RefNumber": "766349", "RefComponent": "BC-DB-ORA", "RefTitle": "FAQ: Oracle SQL optimization", "RefUrl": "/notes/766349"}, {"RefNumber": "706478", "RefComponent": "HAN-DB", "RefTitle": "Preventing Basis tables from increasing considerably", "RefUrl": "/notes/706478"}, {"RefNumber": "618868", "RefComponent": "BC-DB-ORA", "RefTitle": "FAQ: Oracle performance", "RefUrl": "/notes/618868"}, {"RefNumber": "444287", "RefComponent": "BC-DB-ORA", "RefTitle": "Checking the index storage quality", "RefUrl": "/notes/444287"}, {"RefNumber": "439783", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "BR986W Index ... is unbalanced - please rebuild the index", "RefUrl": "/notes/439783"}, {"RefNumber": "435125", "RefComponent": "BC-DB-ORA", "RefTitle": "Poor performance of tables used by CRM (Oracle only)", "RefUrl": "/notes/435125"}, {"RefNumber": "332677", "RefComponent": "BC-DB-ORA", "RefTitle": "Rebuilding fragmented indexes", "RefUrl": "/notes/332677"}, {"RefNumber": "1438410", "RefComponent": "BC-DB-ORA", "RefTitle": "SQL script collection for Oracle", "RefUrl": "/notes/1438410"}, {"RefNumber": "1295200", "RefComponent": "BC-DB-ORA", "RefTitle": "Oracle 10g or higher: Space statistics based on DBMS_SPACE", "RefUrl": "/notes/1295200"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2980187", "RefComponent": "SV-PERF-DB-ORA", "RefTitle": "How to do a mass fragmentation check for the oracle indexes", "RefUrl": "/notes/2980187 "}, {"RefNumber": "2931495", "RefComponent": "BC-DB-ORA", "RefTitle": "Program RSANAORA failed with DBIF_DSQL2_OBJ_UNKNOWN runtime error", "RefUrl": "/notes/2931495 "}, {"RefNumber": "2493563", "RefComponent": "BC-CTS-DTR", "RefTitle": "Internal error: factory method call with null resource", "RefUrl": "/notes/2493563 "}, {"RefNumber": "1905009", "RefComponent": "MDM-GDS", "RefTitle": "GDS FAQ - How to increase the GDS performance", "RefUrl": "/notes/1905009 "}, {"RefNumber": "1438410", "RefComponent": "BC-DB-ORA", "RefTitle": "SQL script collection for Oracle", "RefUrl": "/notes/1438410 "}, {"RefNumber": "618868", "RefComponent": "BC-DB-ORA", "RefTitle": "FAQ: Oracle performance", "RefUrl": "/notes/618868 "}, {"RefNumber": "821687", "RefComponent": "BC-DB-ORA", "RefTitle": "FAQ: Space utilization and fragmentation in Oracle", "RefUrl": "/notes/821687 "}, {"RefNumber": "970538", "RefComponent": "BC-DB-ORA", "RefTitle": "Collective note RSORAISQN", "RefUrl": "/notes/970538 "}, {"RefNumber": "789011", "RefComponent": "BC-DB-ORA", "RefTitle": "FAQ: Oracle memory areas", "RefUrl": "/notes/789011 "}, {"RefNumber": "706478", "RefComponent": "HAN-DB", "RefTitle": "Preventing Basis tables from increasing considerably", "RefUrl": "/notes/706478 "}, {"RefNumber": "928037", "RefComponent": "BC-DB-SDB", "RefTitle": "FAQ: SAP MaxDB indexes", "RefUrl": "/notes/928037 "}, {"RefNumber": "766349", "RefComponent": "BC-DB-ORA", "RefTitle": "FAQ: Oracle SQL optimization", "RefUrl": "/notes/766349 "}, {"RefNumber": "927813", "RefComponent": "BC-DB-ORA", "RefTitle": "Oracle 10g: Using Oracle Segment Advisor to optimize space", "RefUrl": "/notes/927813 "}, {"RefNumber": "332677", "RefComponent": "BC-DB-ORA", "RefTitle": "Rebuilding fragmented indexes", "RefUrl": "/notes/332677 "}, {"RefNumber": "806554", "RefComponent": "BC-DB-ORA", "RefTitle": "FAQ: I/O-intensive database operations", "RefUrl": "/notes/806554 "}, {"RefNumber": "825653", "RefComponent": "BC-DB-ORA", "RefTitle": "Oracle: Popular misconceptions", "RefUrl": "/notes/825653 "}, {"RefNumber": "1295200", "RefComponent": "BC-DB-ORA", "RefTitle": "Oracle 10g or higher: Space statistics based on DBMS_SPACE", "RefUrl": "/notes/1295200 "}, {"RefNumber": "912620", "RefComponent": "BC-DB-ORA", "RefTitle": "FAQ: Oracle indexes", "RefUrl": "/notes/912620 "}, {"RefNumber": "979054", "RefComponent": "BC-DB-ORA", "RefTitle": "RSORAISQN", "RefUrl": "/notes/979054 "}, {"RefNumber": "834118", "RefComponent": "BC-MID-ALE", "RefTitle": "RBDCPIDXRE in building online indexes", "RefUrl": "/notes/834118 "}, {"RefNumber": "910389", "RefComponent": "BC-DB-ORA", "RefTitle": "FAQ: Oracle Segment Shrinking", "RefUrl": "/notes/910389 "}, {"RefNumber": "880580", "RefComponent": "CRM-MW-SRV", "RefTitle": "Index rebuild on Lookup tables for R&R Performance Improvmnt", "RefUrl": "/notes/880580 "}, {"RefNumber": "435125", "RefComponent": "BC-DB-ORA", "RefTitle": "Poor performance of tables used by CRM (Oracle only)", "RefUrl": "/notes/435125 "}, {"RefNumber": "915242", "RefComponent": "BC-DB-ORA", "RefTitle": "FAQ: Reverse key indexes", "RefUrl": "/notes/915242 "}, {"RefNumber": "882425", "RefComponent": "CRM-MW-COM", "RefTitle": "ConnTrans performance: Long Confirmation times", "RefUrl": "/notes/882425 "}, {"RefNumber": "439783", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "BR986W Index ... is unbalanced - please rebuild the index", "RefUrl": "/notes/439783 "}, {"RefNumber": "883346", "RefComponent": "CRM-MW-SRV", "RefTitle": "Performance problems in realignment framework", "RefUrl": "/notes/883346 "}, {"RefNumber": "875798", "RefComponent": "CRM-MW-SRV", "RefTitle": "Index rebuilds (Oracle) for R&R specific CRM tables", "RefUrl": "/notes/875798 "}, {"RefNumber": "444287", "RefComponent": "BC-DB-ORA", "RefTitle": "Checking the index storage quality", "RefUrl": "/notes/444287 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": []}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}