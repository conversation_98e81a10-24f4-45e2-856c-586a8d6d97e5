{"Request": {"Number": "875986", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 878, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000005207392017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000875986?language=E&token=887D5FF0535CFCD1A4B4F020B5327D95"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000875986", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000875986/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "875986"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 123}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Program error"}, "Priority": {"_label": "Priority", "value": "Correction with high priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "25.01.2024"}, "SAPComponentKey": {"_label": "Component", "value": "BC-UPG-NA"}, "SAPComponentKeyText": {"_label": "Component", "value": "Note Assistant"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Basis Components", "value": "BC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Upgrade - general", "value": "BC-UPG", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-UPG*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Note Assistant", "value": "BC-UPG-NA", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-UPG-NA*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "875986 - Note Assistant: Important notes for SAP_BASIS up to 702"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>The Note Assistant allows you to automatically implement note corrections in your ABAP systems. You can find further information about the Note Assistant on SAP Service Marketplace at service.sap.com/noteassistant.<br /><br />Before you implement notes with the Note Assistant, you should upgrade to the latest version of the Note Assistant. This note references the most important notes for correcting errors and updating the Note Assistant. It is the successor of Note 560756.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>SAP notes, SNOTE, SAP_NOTES</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p><br /><strong>Implement the current version of this note to update the Note Assistant.</strong> This ensures that errors in the Note Assistant are corrected and that you can use the latest functions. During the implementation, all of the SAP notes that are listed in this note under III/ Attachment and that are relevant for your release level and Support Package level are implemented in your SAP system.<br />Depending on the release level and Support Package level of your SAP system, you have the following options:</p>\r\n<ul>\r\n<li>You can implement Note 875986 automatically using the Note Assistant.</li>\r\n</ul>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;The prerequisites and procedure can be found under \"<strong> I/ Implementing Note 875986 using the Note Assistant</strong>\".</p>\r\n<ul>\r\n<li>You can individually implement the notes that are relevant for your SAP system using the Note Assistant.</li>\r\n</ul>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;The prerequisites and procedure can be found under \"<strong> II/ Implementing notes from Note 875986 individually using the Note Assistant</strong>\".</p>\r\n<ul>\r\n<li>If your SAP system has a different release and Support Package level than those mentioned under I/ and II/, then the note is not relevant for your SAP system.</li>\r\n</ul>\r\n<p><strong>I/ Implementing Note 875986 using the Note Assistant<br /></strong></p>\r\n<p><strong>Prerequisites</strong><br />Your system must have one of the following release and Support Package levels or higher:</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\">\r\n<tbody>\r\n<tr><th>Soft.Comp.</th><th>Release</th><th>Support Package</th></tr>\r\n<tr>\r\n<td>SAP_BASIS</td>\r\n<td>702</td>\r\n<td>Without Support Packages</td>\r\n</tr>\r\n<tr>\r\n<td>SAP_BASIS</td>\r\n<td>701</td>\r\n<td>Without Support Packages</td>\r\n</tr>\r\n<tr>\r\n<td>SAP_BASIS</td>\r\n<td>700</td>\r\n<td>Support Package 9</td>\r\n</tr>\r\n<tr>\r\n<td>SAP_BASIS</td>\r\n<td>640</td>\r\n<td>Support Package 18</td>\r\n</tr>\r\n<tr>\r\n<td>SAP_BASIS</td>\r\n<td>620</td>\r\n<td>Support Package 60</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><strong>Preparations</strong></p>\r\n<ul>\r\n<li>If your system has SAP NetWeaver 7.0 Enhancement Package 1 (SAP_BASIS 701), Support Package 3 or lower Support Packages, carry out the manual activities contained in Note 1273082. If you have already imported Support Package 4 in Release 701, you do not need to carry out the manual activities.</li>\r\n</ul>\r\n<ul>\r\n<li>For other releases, carry out the following preparations:</li>\r\n</ul>\r\n<ol><ol>a) If you have not yet imported Support Package SAPKB62062, SAPKB64020, or SAPKB70011, use the Note Assistant to implement Note 992831.</ol></ol><ol><ol>b) Use transaction /NSNOTE to restart the Note Assistant.</ol></ol><ol><ol>c) If you have not yet imported Support Package SAPKB62061, SAPKB64019 or SAPKB70010, carry out the manual activities contained in Note 978398.</ol></ol><ol><ol>d) If you have not yet imported Support Package SAPKB70020, perform the manual tasks from Note 1273082.</ol></ol><ol><ol>e) If you have not yet imported Support Package SAPKB62062, SAPKB64020, or SAPKB70011, use the Note Assistant to implement Note 970837.</ol></ol><ol><ol><ol>f) Use transaction /NSNOTE to restart the Note Assistant.</ol></ol></ol>\r\n<p><strong>Procedure</strong></p>\r\n<ol>1. Use Note Assistant to implement Note 875986.</ol>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;<strong>Note the following</strong> : If you cannot implement changes during the implementation (yellow traffic light on the 'Confirm Changes' input screen), use the 'Cancel' (F12) function to terminate the implementation of the note and create a customer message under the application component BC-UPG-NA.</p>\r\n<ol>2. After you confirm the activation of the changed objects, the dialog window for selecting the main program for CWBNTCNS appears in certain SAP_BASIS Support Packages that were previously imported. Select one of the listed programs and choose \"Select\".</ol>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;<strong>Important</strong> : If you do not select a program and choose \"Terminate\" instead, the system does not activate the changes that you want to implement using the composite note.</p>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;All of the notes that are relevant for your release and Support Package level are implemented in your SAP system.</p>\r\n<ol>3. Use transaction /NSNOTE to restart the Note Assistant.</ol>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;The Note Assistant is now updated to the current version. You can implement more SAP notes using the Note Assistant.</p>\r\n<p><strong>II/ Implementing notes from Note 875986 individually using the Note Assistant</strong><br /><br />If your SAP system has one of the following release and Support Package levels, you cannot implement Note 875986 automatically.</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\">\r\n<tbody>\r\n<tr><th>Soft.Comp.</th><th>Release</th><th>Support Package</th></tr>\r\n<tr>\r\n<td>SAP_BASIS</td>\r\n<td>700</td>\r\n<td>Support Packages 5-8</td>\r\n</tr>\r\n<tr>\r\n<td>SAP_BASIS</td>\r\n<td>640</td>\r\n<td>Support Packages 13-17</td>\r\n</tr>\r\n<tr>\r\n<td>SAP_BASIS</td>\r\n<td>620</td>\r\n<td>Support Packages 55-59</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p>Instead, you can implement notes from Note 875986 individually using the Note Assistant.<br /><br /><strong>Procedure</strong></p>\r\n<ol>1. Load Note 875986 into your system.</ol><ol>2. Load all of the notes contained in Note 875986 into your system. Under \"III/ Attachment: List of notes from Note 875986\", you will find a list of the notes contained in Note 875986.</ol>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;If you load notes into the Note Assistant via an RFC connection, you can proceed as follows:</p>\r\n<ol><ol>a) Select all note numbers listed in Note 875986 under \"III/ Attachment: List of notes from Note 875986\"\" and copy them.</ol></ol><ol><ol>b) In the Note Assistant, choose \"Download SAP Note\".</ol></ol><ol><ol>c) Choose \"Multiple selection\".</ol></ol><ol><ol>d) Choose \"Upload from clipboard\".</ol></ol>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;The system displays a list of note numbers.</p>\r\n<ol><ol>e) Choose \"Copy\" and start the note download.</ol></ol>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;All of the notes contained in the note are loaded into your system.</p>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;The Note Assistant shows you which notes can be implemented in your system. You can classify notes that cannot be implemented as \"not relevant\" to remove them from your worklist.</p>\r\n<ol><ol>3. Implement the notes that can be implemented one after the other and pay attention to the note texts when you do this.</ol></ol>\r\n<p><strong>III Attachment: List of notes from Note 875986</strong><br />The corrections of the following listed notes are implemented in your system when Note 875986 is automatically implemented in your system if their release level and Support Package level is relevant for your system.<br />The list of notes corresponds mostly with the related notes.&#160;&#160;However, the related notes are displayed only in the download area, but not in the Note Assistant. You require the list to be able to download the notes together if you implement the notes individually from Note 875986 with the Note Assistant as described under II/.<br /><br />-----------------------------------------------------------------------<br />|The different notes will be implemented using this composite note&#160;&#160;&#160;&#160;|<br />|based on the release are given below&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; |<br />|---------------------------------------------------------------------|<br />|SAP_BASIS&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;|<br />|Release 620 and Release 640&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;|<br />|---------------------------------------------------------------------|<br />|853524&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;|875036&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; |875816&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;|877516&#160;&#160;&#160;&#160;&#160;&#160;|<br />|882246&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;|884377&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; |887393&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;|887495&#160;&#160;&#160;&#160;&#160;&#160;| |890198&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;|891060&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; |907071&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;|907329&#160;&#160;&#160;&#160;&#160;&#160;|<br />|914200&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;|914759&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; |915117&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;|918766&#160;&#160;&#160;&#160;&#160;&#160;|<br />|920714&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;|921344&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; |922354&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;|928595&#160;&#160;&#160;&#160;&#160;&#160;|<br />|932065&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;|935140&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; |943410&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;|948389&#160;&#160;&#160;&#160;&#160;&#160;|<br />|951571&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;|959711&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; |964580&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;|965756&#160;&#160;&#160;&#160;&#160;&#160;|<br />|966900&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;|967237&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; |968138&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;|969701&#160;&#160;&#160;&#160;&#160;&#160;|<br />|969846&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;|970391&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; |970837&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;|975510&#160;&#160;&#160;&#160;&#160;&#160;|<br />|976918&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;|976920&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; |977082&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;|978398&#160;&#160;&#160;&#160;&#160;&#160;|<br />|980288&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;|983212&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; |983534&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;|984173&#160;&#160;&#160;&#160;&#160;&#160;|<br />|984771&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;|986226&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; |987261&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;|989056&#160;&#160;&#160;&#160;&#160;&#160;|<br />|989122&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;|991520&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; |992831&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;|994673&#160;&#160;&#160;&#160;&#160;&#160;|<br />|1000332&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;|1000448&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;|1004110&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;|1004293&#160;&#160;&#160;&#160; |<br />|1004293&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;|1004661&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;|1004691&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;|1005849&#160;&#160;&#160;&#160; |<br />|1007754&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;|1008275&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;|1008436&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;|1008540&#160;&#160;&#160;&#160; |<br />|1009932&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;|1010679&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;|1020093&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;|1020107&#160;&#160;&#160;&#160; |<br />|1027648&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;|1029666&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;|1031630&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;|1031649&#160;&#160;&#160;&#160; |<br />|1034004&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;|1034360&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;|1037672&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;|1041386&#160;&#160;&#160;&#160; |<br />|1050569&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;|1058809&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;|1059121&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;|1059707&#160;&#160;&#160;&#160; |<br />|1064567&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;|1065164&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;|1066203&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;|1070182&#160;&#160;&#160;&#160; |<br />|1073502&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;|1077466&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;|1088433&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;|1092828&#160;&#160;&#160;&#160; |<br />|1094372&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;|1097666&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;|1106385&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;|1112141&#160;&#160;&#160;&#160; |<br />|1113339&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;|1114973&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;|1121795&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;|1126192&#160;&#160;&#160;&#160; |<br />|1130404&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;|1142128&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;|1147830&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;|1150982&#160;&#160;&#160;&#160; |<br />|1151059&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;|1151243&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;|&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;|&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;|<br />-----------------------------------------------------------------------<br /><br /><br /><br /><br /><br /><br /><br /><br /><br />-----------------------------------------------------------------------<br />|The different notes will be implemented using this composite note&#160;&#160;&#160;&#160;|<br />|based on the release are given below&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; |<br />|---------------------------------------------------------------------|<br />|SAP_BASIS&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;|SAP_BASIS&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;| SAP_BASIS&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; |<br />|Release 700&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; |Release 701&#160;&#160;&#160;&#160;&#160;&#160;| Release 702&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; |<br />|SP09 onwards&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;|SP00 onwards&#160;&#160;&#160;&#160;&#160;&#160;| SP02 onwards&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;|<br />|---------------------------------------------------------------------|<br />|884377&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;|1113339&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;|1260089&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;|<br />|907071&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;|1231230&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;|1334440&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;|<br />|964580&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;|1262653&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;|1350005&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;|<br />|965756&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;|1262826&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;|1365677&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;|<br />|966900&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;|1276929&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;|1379746&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;|<br />|967237&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;|1286126&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;|1409699&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;|<br />|968138&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;|1287384&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;|1425487&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;|<br />|969701&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;|1291055&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;|1428701&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;|<br />|969846&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;|1349277&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;|1433214&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;|<br />|970391&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;|1365677&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;|1437981&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;|<br />|970837&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;|1397709&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;|1469642&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;|<br />|975510&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;|1412719&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;|1473623&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;|<br />|976918&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;|1415680&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;|1478610&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;|<br />|976920&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;|1425487&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;|1485083&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;|<br />|977082&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;|1720495&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;|1487661&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;|<br />|978398&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;|&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;|1497520&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;|<br />|980288&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;|&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;|1497858&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;|<br />|983212&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;|&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;|1500456&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;|<br />|983534&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;|&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;|1506104&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;|<br />|984173&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;|&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;|1518861&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;|<br />|984771&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;|&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;|1523687&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;|<br />|986226&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;|&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;|1525115&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;|<br />|987261&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;|&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;|1530273&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;|<br />|989056&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;|&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;|1532264&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;|<br />|991520&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;|&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;|1534245&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;|<br />|992831&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;|&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;|1535724&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;|<br />|994673&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;|&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;|1539505&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;|<br />|1000332&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; |&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;|1541531&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;|<br />|1000448&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; |&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;|1549103&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;|<br />|1004110&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; |&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;|1552560&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;|<br />|1004293&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; |&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;|1552759&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;|<br />|1004661&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; |&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;|1557768&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;|<br />|1004691&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; |&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;|1566290&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;|<br />|1005849&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; |&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;|1571213&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;|<br />|1007754&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; |&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;|1537354&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;|<br />|1008275&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; |&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;|1619713&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;|<br />|1008436&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; |&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;|1621321&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;|<br />|1008540&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; |&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;|1624603&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;|<br />|1009932&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; |&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;|1627683&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;|<br />|1010679&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; |&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;|1639074&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;|<br />|1020093&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; |&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;|1685578&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;|<br />|1020107&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; |&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;|1690165&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;|<br />|1027648&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; |&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;|1720495&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;|<br />|1029666&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; |&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;|1738132&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;|<br />|1031630&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; |&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;|&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; |<br />|1031649&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; |&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;|&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; |<br />|1034004&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; |&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;|&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; |<br />|1034360&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; |&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;|&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; |<br />|1037672&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; |&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;|&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; |<br />-----------------------------------------------------------------------<br />|---------------------------------------------------------------------|<br />|SAP_BASIS&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;|SAP_BASIS&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;| SAP_BASIS&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;|<br />|Release 700&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; |Release 701&#160;&#160;&#160;&#160;&#160;&#160;| Release 702&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; |<br />|SP09 onwards&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;|SP00 onwards&#160;&#160;&#160;&#160;&#160;&#160;| SP02 onwards&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;|<br />-----------------------------------------------------------------------<br />|1041386&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; |&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;|&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; |<br />|1050569&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; |&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;|&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; |<br />|1059707&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; |&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;|&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; |<br />|1064567&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; |&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;|&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; |<br />|1065164&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; |&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;|&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; |<br />|1066203&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; |&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;|&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; |<br />|1070182&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; |&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;|&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; |<br />|1073502&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; |&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;|&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; |<br />|1077466&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; |&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;|&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; |<br />|1088433&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; |&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;|&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; |<br />|1092828&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; |&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;|&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; |<br />|1094372&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; |&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;|&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; |<br />|1097666&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; |&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;|&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; |<br />|1106385&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; |&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;|&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; |<br />|1112141&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; |&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;|&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; |<br />|1113339&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; |&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;|&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; |<br />|1114973&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; |&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;|&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; |<br />|1121795&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; |&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;|&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; |<br />|1126192&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; |&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;|&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; |<br />|1130404&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; |&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;|&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; |<br />|1142128&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; |&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;|&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; |<br />|1147830&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; |&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;|&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; |<br />|1150982&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; |&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;|&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; |<br />|1151059&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; |&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;|&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; |<br />|1151243&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; |&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;|&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; |<br />|1720495&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; |&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;|&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; |<br />-----------------------------------------------------------------------<br /><br />-----------------------------------------------------------------------<br />|The different notes need to be implemented manually to apply all the |<br />|correction of snote based on the release are given below&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; |<br />|---------------------------------------------------------------------|<br />|SAP_BASIS&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;|SAP_BASIS&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;| SAP_BASIS&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; |<br />|Release 700&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; |Release 701&#160;&#160;&#160;&#160;&#160;&#160;| Release 702&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; |<br />|SP09 onwards&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;|SP00 onwards&#160;&#160;&#160;&#160;&#160;&#160;| SP02 onwards&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;|<br />|---------------------------------------------------------------------|<br />|1231230&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;|1246964&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; |1291055&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;|<br />|1246964&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;|1246982&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; |1349277&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;|<br />|1246982&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;|1260089&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; |1415680&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;|<br />|1260089&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;|1316626&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; |1517468&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;|<br />|1262653&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;|1334440&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; |1537354&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;|<br />|1262826&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;|1342598&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; |1627683&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;|<br />|1276929&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;|1350005&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; |&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; | |1286126&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;|1379746&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; |&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; |<br />|1287384&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;|1409699&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; |&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; |<br />|1291055&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;|1428701&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; |&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; |<br />|1316626&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;|1433214&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; |&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; |<br />|1334440&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;|1437981&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; |&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; |<br />|1342598&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;|1469642&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; |&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; |<br />|1349277&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;|1473623&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; |&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; |<br />|1365677&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;|1478610&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; |&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; |<br />|1379746&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;|1485083&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; |&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;|<br />-----------------------------------------------------------------------<br />|---------------------------------------------------------------------|<br />|SAP_BASIS&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;|SAP_BASIS&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;| SAP_BASIS&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; |<br />|Release 700&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; |Release 701&#160;&#160;&#160;&#160;&#160;&#160;| Release 702&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; |<br />|SP09 onwards&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;|SP00 onwards&#160;&#160;&#160;&#160;&#160;&#160;| SP02 onwards&#160; &#160; &#160; &#160; &#160; &#160; &#160; &#160;|<br />|------------------------------------------------------------------------|<br />|1397709&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;|1487661&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; |&#160;2314876&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; |<br />|1409699&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;|1497520&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; |&#160;2328318&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; |<br />|1412719&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;|1497858&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; |&#160;2042123&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; |<br />|1415680&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;|1500456&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; |&#160;2589309&#160; &#160; &#160; &#160; &#160; &#160; &#160; &#160; &#160; &#160; &#160; &#160; &#160; &#160; |<br />|1425487&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;|1504500&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; | 2697766&#160; &#160;&#160; &#160; &#160; &#160; &#160; &#160; &#160; &#160; &#160; &#160; &#160; &#160; &#160;|<br />|1433214&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;|1506104&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; | 2691847&#160; &#160; &#160; &#160; &#160; &#160; &#160; &#160; &#160; &#160; &#160; &#160; &#160; &#160; |<br />|1437981&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;|1517468&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; |&#160;2671774&#160;&#160; &#160; &#160; &#160; &#160; &#160; &#160; &#160; &#160; &#160; &#160; &#160; &#160; &#160;|<br />|1469642&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;|1518861&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; |&#160;2623459&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; |<br />|1473623&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;|1523687&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; |&#160;2624337&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; |<br />|1478610&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;|1525115&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; |&#160;2617883&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; |<br />|1485083&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;|1530273&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; |&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; |<br />|1487661&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;|1532264&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; |&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; |<br />|1497520&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;|1535724&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; |&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; |<br />|1497858&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;|1537354&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; |&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; |<br />|1500456&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;|1539505&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; |&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; |<br />|1504500&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;|1541531&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; |&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; |<br />|1506104&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;|1549103&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; |&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; |<br />|1517468&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;|1552560&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; |&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; |<br />|1518861&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;|1557768&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; |&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; |<br />|1523687&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;|1566290&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; |&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; |<br />|1525115&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;|1571213&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; |&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; |<br />|1530273&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;|1619713&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; |&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; |<br />|1532264&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;|1621321&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; |&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; |<br />|1535724&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;|1627683&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; |&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; |<br />|1537354&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;|1639074&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; |&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; |<br />|1539505&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;|1690165&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; |&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; |<br />|1541531&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;|1738132&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; |&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; |<br />|1549103&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;|2328318&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;|&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; |<br />|1552560&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;|2314876&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;|&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; |<br />|1557768&#160; &#160; &#160; &#160; &#160; &#160; |2042123&#160; &#160; &#160; &#160; &#160; &#160;|&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; |<br />|1566290&#160; &#160; &#160; &#160; &#160; &#160; |2589309&#160; &#160; &#160; &#160; &#160; &#160;|&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; |<br />|1571213&#160; &#160; &#160; &#160; &#160; &#160; |2697766&#160; &#160; &#160; &#160; &#160; &#160;|&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; |<br />|1619713&#160; &#160; &#160; &#160; &#160; &#160; |2671774&#160; &#160; &#160; &#160; &#160; &#160;|&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; |<br />|1621321&#160; &#160; &#160; &#160; &#160; &#160; |2623459&#160; &#160; &#160; &#160; &#160; &#160;|&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; |<br />|1627683&#160; &#160; &#160; &#160; &#160; &#160; |2624337&#160; &#160; &#160; &#160; &#160; &#160;|&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; |<br />|1639074&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; |&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;|&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; |<br />|1690165&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; |&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;|&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; |<br />|1738132&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; |&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;|&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; |<br />|2328318&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; &#160;&#160; |&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;|&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; |<br />|2314876&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; |&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;|&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; |</p>\r\n<p>|2697766&#160; &#160; &#160; &#160; &#160; &#160; &#160;|&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;|&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; |</p>\r\n<p>|2623459&#160; &#160; &#160; &#160; &#160; &#160; &#160;|&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;|&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; |</p>\r\n<p>|2624337&#160; &#160; &#160; &#160; &#160; &#160; &#160;|&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;|&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; |</p>\r\n<p>|2671774&#160; &#160; &#160; &#160; &#160; &#160; &#160;|&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;|&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; |</p>\r\n<p>|2589309&#160; &#160; &#160; &#160; &#160; &#160; &#160;|&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;|&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; |</p>\r\n<p>|2715783</p>\r\n<p>|2715783&#160; &#160; &#160; &#160; &#160; &#160; &#160;|2715783&#160; &#160; &#160; &#160;|2715783&#160; &#160; &#160; &#160; &#160; &#160; &#160; &#160;|<br />|2714624 &#160;&#160; &#160; &#160; &#160; &#160; &#160;|2714624 &#160; &#160; &#160;&#160;|2714624&#160; &#160; &#160; &#160; &#160; &#160; &#160; &#160;|<br />|2757237 &#160;&#160; &#160; &#160; &#160; &#160; &#160;|2757237 &#160; &#160; &#160;&#160;|2757237&#160; &#160; &#160; &#160; &#160; &#160; &#160; &#160;|<br />|2764725 &#160;&#160; &#160; &#160; &#160; &#160; &#160;|2764725 &#160; &#160; &#160;&#160;|2764725&#160; &#160; &#160; &#160; &#160; &#160; &#160; &#160;|<br />|2765308 &#160;&#160; &#160; &#160; &#160; &#160; &#160;|2765308 &#160; &#160; &#160;&#160;|2765308&#160; &#160; &#160; &#160; &#160; &#160; &#160; &#160;|<br />|2459558 &#160;&#160; &#160; &#160; &#160; &#160; &#160;|2459558 &#160; &#160; &#160;&#160;|2459558&#160; &#160; &#160; &#160; &#160; &#160; &#160; &#160;|<br />|2212925 &#160;&#160; &#160; &#160; &#160; &#160; &#160;|2212925 &#160; &#160; &#160;&#160;|2212925&#160; &#160; &#160; &#160; &#160; &#160; &#160; &#160;|<br />|2606986 &#160;&#160; &#160; &#160; &#160; &#160; &#160;|2606986 &#160; &#160; &#160;&#160;|2606986&#160; &#160; &#160; &#160; &#160; &#160; &#160; &#160;|<br />|2541236 &#160;&#160; &#160; &#160; &#160; &#160; &#160;|2541236 &#160; &#160; &#160;&#160;|2541236&#160; &#160; &#160; &#160; &#160; &#160; &#160; &#160;|<br />|2536585 &#160;&#160; &#160; &#160; &#160; &#160; &#160;|2536585 &#160; &#160; &#160;&#160;|2536585&#160; &#160; &#160; &#160; &#160; &#160; &#160; &#160;|<br />|2368460 &#160;&#160; &#160; &#160; &#160; &#160; &#160;|2368460 &#160; &#160; &#160;&#160;|2368460&#160; &#160; &#160; &#160; &#160; &#160; &#160; &#160;|<br />|2598809 &#160;&#160; &#160; &#160; &#160; &#160; &#160;|2598809 &#160; &#160; &#160;&#160;|2598809&#160; &#160; &#160; &#160; &#160; &#160; &#160; &#160;|<br />|2597808 &#160;&#160; &#160; &#160; &#160; &#160; &#160;|2597808 &#160; &#160; &#160;&#160;|2597808&#160; &#160; &#160; &#160; &#160; &#160; &#160; &#160;|<br />|2568276 &#160;&#160; &#160; &#160; &#160; &#160; &#160;|2568276 &#160; &#160; &#160;&#160;|2568276&#160; &#160; &#160; &#160; &#160; &#160; &#160; &#160;|<br />|2292923 &#160;&#160; &#160; &#160; &#160; &#160; &#160;|2292923 &#160; &#160; &#160;&#160;|2292923&#160; &#160; &#160; &#160; &#160; &#160; &#160; &#160;|<br />|2739641 &#160;&#160; &#160; &#160; &#160; &#160; &#160;|2739641 &#160; &#160; &#160;&#160;|2739641&#160; &#160; &#160; &#160; &#160; &#160; &#160; &#160;|<br />|2671774 &#160;&#160; &#160; &#160; &#160; &#160; &#160;|2671774 &#160; &#160; &#160;&#160;|2671774&#160; &#160; &#160; &#160; &#160; &#160; &#160; &#160;|<br />|2770960 &#160;&#160; &#160; &#160; &#160; &#160; &#160;|2770960 &#160; &#160; &#160;&#160;|2770960&#160; &#160; &#160; &#160; &#160; &#160; &#160; &#160;|</p>\r\n<p>|2930611&#160; &#160; &#160; &#160; &#160; &#160; &#160;|2930611&#160; &#160; &#160; &#160;|2930611&#160; &#160; &#160; &#160; &#160; &#160; &#160; &#160;|</p>\r\n<p>|2910608&#160; &#160; &#160; &#160; &#160; &#160; &#160;|2910608&#160; &#160; &#160; &#160;|2910608&#160; &#160; &#160; &#160; &#160; &#160; &#160; &#160;|</p>\r\n<p>|2770960 &#160;&#160; &#160; &#160; &#160; &#160; &#160;|2770960 &#160; &#160; &#160;&#160;|2770960&#160; &#160; &#160; &#160; &#160; &#160; &#160; &#160;|</p>\r\n<p>-----------------------------------------------------------------------</p>\r\n<p>|---------------------------------------------------------------------|<br />|SAP_BASIS&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;|SAP_BASIS&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;| SAP_BASIS&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; |<br />|Release 700&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; |Release 701&#160;&#160;&#160;&#160;&#160;&#160;| Release 702&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; |<br />|SP09 onwards&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;|SP00 onwards&#160;&#160;&#160;&#160;&#160;&#160;| SP02 onwards&#160; &#160; &#160; &#160; &#160; &#160; &#160; &#160;|<br />|------------------------------------------------------------------------|</p>\r\n<p>|2860125&#160; &#160; &#160; &#160; &#160; &#160; &#160;|2860125&#160; &#160; &#160; &#160;|2860125&#160; &#160; &#160; &#160; &#160; &#160; &#160; |<br />|2930611&#160; &#160; &#160; &#160; &#160; &#160; &#160;|2930611&#160; &#160; &#160; &#160;|2930611&#160; &#160; &#160; &#160; &#160; &#160; &#160; |<br />|3006946&#160; &#160; &#160; &#160; &#160; &#160; &#160;|3006946&#160; &#160; &#160; &#160;|3006946&#160; &#160; &#160; &#160; &#160; &#160; &#160; |<br />|3008844&#160; &#160; &#160; &#160; &#160; &#160; &#160;|3008844&#160; &#160; &#160; &#160;|3008844&#160; &#160; &#160; &#160; &#160; &#160; &#160; |<br />|3007345&#160; &#160; &#160; &#160; &#160; &#160; &#160;|2991524&#160; &#160; &#160; &#160;|2991524&#160; &#160; &#160; &#160; &#160; &#160; &#160; |<br />|3041970&#160; &#160; &#160; &#160; &#160; &#160; &#160;|3041970&#160; &#160; &#160; &#160;|3041970&#160; &#160; &#160; &#160; &#160; &#160; &#160; |<br />|3047860&#160; &#160; &#160; &#160; &#160; &#160; &#160;|3047860&#160; &#160; &#160; &#160;|3047860&#160; &#160; &#160; &#160; &#160; &#160; &#160; |<br />|3079593&#160; &#160; &#160; &#160; &#160; &#160; &#160;|3079593&#160; &#160; &#160; &#160;|3079593&#160; &#160; &#160; &#160; &#160; &#160; &#160; |<br />|3085447&#160; &#160; &#160; &#160; &#160; &#160; &#160;|3085447&#160; &#160; &#160; &#160;|3085447&#160; &#160; &#160; &#160; &#160; &#160; &#160; |<br />|&#160; &#160; &#160; &#160; &#160; &#160; &#160; &#160; &#160; &#160; &#160; &#160; &#160; |&#160; &#160; &#160; &#160; &#160; &#160; &#160; &#160; &#160; &#160; |&#160; &#160; &#160; &#160; &#160; &#160; &#160; |</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON><PERSON> (I043353)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON><PERSON><PERSON><PERSON> (I567948)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000875986/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000875986/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000875986/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000875986/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000875986/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000875986/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000875986/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000875986/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000875986/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "994673", "RefComponent": "BC-UPG-NA", "RefTitle": "Note Assistant: Dump GETWA_NOT_ASSIGNED during note implem", "RefUrl": "/notes/994673"}, {"RefNumber": "992831", "RefComponent": "BC-UPG-NA", "RefTitle": "Note Assistant: Incompl correction instructions in note log", "RefUrl": "/notes/992831"}, {"RefNumber": "991520", "RefComponent": "BC-UPG-NA", "RefTitle": "Note Assistant: External PDF display in the note log", "RefUrl": "/notes/991520"}, {"RefNumber": "989122", "RefComponent": "BC-DWB-TOO-ENH", "RefTitle": "Error in SNOTE for enhancements", "RefUrl": "/notes/989122"}, {"RefNumber": "989056", "RefComponent": "BC-UPG-NA", "RefTitle": "Note implementation: Implementation-based instance relship", "RefUrl": "/notes/989056"}, {"RefNumber": "987261", "RefComponent": "BC-UPG-NA", "RefTitle": "Modification adjustment: Locks for objects", "RefUrl": "/notes/987261"}, {"RefNumber": "986226", "RefComponent": "BC-DWB-TOO-SCR", "RefTitle": "Termination CX_XSLT_RUNTIME_ERROR when downloading note", "RefUrl": "/notes/986226"}, {"RefNumber": "984771", "RefComponent": "BC-UPG-NA", "RefTitle": "ENHO-SNOTE : Missing SOTR texts produce red traffic light", "RefUrl": "/notes/984771"}, {"RefNumber": "984173", "RefComponent": "BC-UPG-NA", "RefTitle": "Termination when SOTR texts are missing in other languages", "RefUrl": "/notes/984173"}, {"RefNumber": "983534", "RefComponent": "BC-UPG-NA", "RefTitle": "Note Assistant: <PERSON>lank references enhancement implementation", "RefUrl": "/notes/983534"}, {"RefNumber": "983212", "RefComponent": "BC-DWB-TOO-CLA", "RefTitle": "Forward declaration is not deleted from metadata", "RefUrl": "/notes/983212"}, {"RefNumber": "980822", "RefComponent": "BC-DWB-TOO-CLA", "RefTitle": "CLSD_GET_OBJECT function module returns no Friends", "RefUrl": "/notes/980822"}, {"RefNumber": "980288", "RefComponent": "BC-UPG-NA", "RefTitle": "Note Assistant:incorrect version data for WebDynpro and WAPP", "RefUrl": "/notes/980288"}, {"RefNumber": "978398", "RefComponent": "BC-DWB-TOO-CLA", "RefTitle": "Changing the class include structure using SNOTE/SCWB", "RefUrl": "/notes/978398"}, {"RefNumber": "977082", "RefComponent": "BC-DWB-TOO-CLA", "RefTitle": "SE24: <PERSON><PERSON><PERSON> in connection with the Note Assistant", "RefUrl": "/notes/977082"}, {"RefNumber": "976920", "RefComponent": "BC-UPG-NA", "RefTitle": "SPAU: reset to original doesn't change the status of note", "RefUrl": "/notes/976920"}, {"RefNumber": "976918", "RefComponent": "BC-UPG-NA", "RefTitle": "Note Assistant: Error during note display with DYNP deltas", "RefUrl": "/notes/976918"}, {"RefNumber": "975510", "RefComponent": "BC-DWB-TOO-CLA", "RefTitle": "Corrections for classes/interfaces implemented incorrectly", "RefUrl": "/notes/975510"}, {"RefNumber": "970837", "RefComponent": "BC-DWB-TOO-CLA", "RefTitle": "Creating methods with Note Assistant - mod infos created", "RefUrl": "/notes/970837"}, {"RefNumber": "970391", "RefComponent": "BC-UPG-NA", "RefTitle": "Note Assistant: Dump after you double-click a delta", "RefUrl": "/notes/970391"}, {"RefNumber": "969846", "RefComponent": "BC-DWB-TOO-CLA", "RefTitle": "NoteAss: Unable to change the inheritance hierarchy", "RefUrl": "/notes/969846"}, {"RefNumber": "969701", "RefComponent": "BC-UPG-NA", "RefTitle": "Note Assistant: <PERSON><PERSON><PERSON> in merge editor for IAMU object", "RefUrl": "/notes/969701"}, {"RefNumber": "968138", "RefComponent": "BC-UPG-NA", "RefTitle": "Note Assistant: Restarting capability in the Note Assistant", "RefUrl": "/notes/968138"}, {"RefNumber": "967237", "RefComponent": "BC-DWB-TOO-CLA", "RefTitle": "No adjustment of type uses when note is implemented", "RefUrl": "/notes/967237"}, {"RefNumber": "966900", "RefComponent": "BC-UPG-NA", "RefTitle": "Entries not sorted by time in Note log", "RefUrl": "/notes/966900"}, {"RefNumber": "965756", "RefComponent": "BC-DWB-TOO-ENH", "RefTitle": "Upgrade indicator set for SCWB/SNOTE", "RefUrl": "/notes/965756"}, {"RefNumber": "964580", "RefComponent": "BC-CTS-ORG", "RefTitle": "Incorrect version data in sections of classes", "RefUrl": "/notes/964580"}, {"RefNumber": "959711", "RefComponent": "BC-UPG-NA", "RefTitle": "SNOTE: Error in composite enhancement spots", "RefUrl": "/notes/959711"}, {"RefNumber": "951571", "RefComponent": "BC-DWB-CEX", "RefTitle": "NA: BAdI interfaces are edited", "RefUrl": "/notes/951571"}, {"RefNumber": "948389", "RefComponent": "BC-UPG-NA", "RefTitle": "Note Assistant: Incorrect note removal", "RefUrl": "/notes/948389"}, {"RefNumber": "943410", "RefComponent": "BC-UPG-NA", "RefTitle": "SCWB/SNOTE: No processing with enhancement in the upgrade", "RefUrl": "/notes/943410"}, {"RefNumber": "935140", "RefComponent": "BC-DWB-TOO-CLA", "RefTitle": "SEO_CLASS_TYPEINFO_GET returns improper typesrc length", "RefUrl": "/notes/935140"}, {"RefNumber": "932065", "RefComponent": "BC-UPG-NA", "RefTitle": "Note Assistant: Error during adjustment for classes in SPAU", "RefUrl": "/notes/932065"}, {"RefNumber": "928595", "RefComponent": "BC-UPG-NA", "RefTitle": "Note Assistant: Error when making source code changes", "RefUrl": "/notes/928595"}, {"RefNumber": "922354", "RefComponent": "BC-UPG-NA", "RefTitle": "Note Assistant: Error during note implementation & new entry", "RefUrl": "/notes/922354"}, {"RefNumber": "921344", "RefComponent": "BC-DWB-TOO-CLA", "RefTitle": "Note cannot be implemented, due to complex type definitions", "RefUrl": "/notes/921344"}, {"RefNumber": "920714", "RefComponent": "BC-UPG-OCS", "RefTitle": "ENHO objects in SCWB/SNOTE are too large", "RefUrl": "/notes/920714"}, {"RefNumber": "918766", "RefComponent": "BC-UPG-NA", "RefTitle": "Note Assistant: Changing sections of classes", "RefUrl": "/notes/918766"}, {"RefNumber": "915117", "RefComponent": "BC-UPG-NA", "RefTitle": "SCWB/SNOTE terminates when transporting ENHSPOTS (new BAdIs)", "RefUrl": "/notes/915117"}, {"RefNumber": "914759", "RefComponent": "BC-UPG-NA", "RefTitle": "Error when importing enhancement with SNOTE/SCWB", "RefUrl": "/notes/914759"}, {"RefNumber": "914200", "RefComponent": "BC-UPG-NA", "RefTitle": "Termination in SCWB/SNOTE if SPCF texts are missing", "RefUrl": "/notes/914200"}, {"RefNumber": "907329", "RefComponent": "BC-DWB-TOO-SCR", "RefTitle": "Screen: Normalizing the element list", "RefUrl": "/notes/907329"}, {"RefNumber": "907071", "RefComponent": "BC-UPG-NA", "RefTitle": "Note Assistant: Partial implementation of changes in classes", "RefUrl": "/notes/907071"}, {"RefNumber": "891060", "RefComponent": "BC-DWB-TOO-ENH", "RefTitle": "Termination with Transaction SCWB/SNOTE with obj type ENHS", "RefUrl": "/notes/891060"}, {"RefNumber": "890198", "RefComponent": "BC-DWB-TOO-ENH", "RefTitle": "Corrupted HOOK sources in enhancements after SCWB/SNODE", "RefUrl": "/notes/890198"}, {"RefNumber": "887495", "RefComponent": "BC-UPG-NA", "RefTitle": "Note Assistant: Call contained error(s): ADS (2)", "RefUrl": "/notes/887495"}, {"RefNumber": "887393", "RefComponent": "BC-DWB-TOO", "RefTitle": "Dump in SCWB when processing object type ENHS", "RefUrl": "/notes/887393"}, {"RefNumber": "884377", "RefComponent": "BC-DWB-TOO-CLA", "RefTitle": "NoteAss: Class/interface if Modification Assistant on", "RefUrl": "/notes/884377"}, {"RefNumber": "882246", "RefComponent": "BC-UPG-NA", "RefTitle": "Note Assistant: Message TK594 for function modules", "RefUrl": "/notes/882246"}, {"RefNumber": "877516", "RefComponent": "BC-UPG-NA", "RefTitle": "Note Assistant: Notes with MIME repository objects", "RefUrl": "/notes/877516"}, {"RefNumber": "875816", "RefComponent": "BC-UPG-NA", "RefTitle": "Note Assistant: Notes after upgrade on new functions", "RefUrl": "/notes/875816"}, {"RefNumber": "875036", "RefComponent": "BC-UPG-NA", "RefTitle": "Note Assistant: Program terminates when you display a note", "RefUrl": "/notes/875036"}, {"RefNumber": "853524", "RefComponent": "BC-UPG-NA", "RefTitle": "Note Assistant: Message scwn 409 during note installation", "RefUrl": "/notes/853524"}, {"RefNumber": "2697766", "RefComponent": "BC-UPG-NA", "RefTitle": "SNOTE: Runtime Error CONVT_DATA_LOSS occurs while downloading/uploading SAP Notes", "RefUrl": "/notes/2697766"}, {"RefNumber": "2691847", "RefComponent": "BC-UPG-NA", "RefTitle": "Previously inactive object activated when current implementation of a note for the same object is cancelled", "RefUrl": "/notes/2691847"}, {"RefNumber": "2684471", "RefComponent": "BC-UPG-NA", "RefTitle": "SNOTE HTML Display: Empty 'Links to Support Packages' section", "RefUrl": "/notes/2684471"}, {"RefNumber": "2671774", "RefComponent": "BC-UPG-NA", "RefTitle": "Error during Note implementation: Unable to find delivery event <delivery event number>", "RefUrl": "/notes/2671774"}, {"RefNumber": "2624337", "RefComponent": "BC-UPG-NA", "RefTitle": "SNOTE - Note re implementation issue due to object support in newer SP's", "RefUrl": "/notes/2624337"}, {"RefNumber": "2623459", "RefComponent": "BC-UPG-NA", "RefTitle": "Unable to read Correction instruction (Unknown Format)", "RefUrl": "/notes/2623459"}, {"RefNumber": "2617883", "RefComponent": "BC-UPG-NA", "RefTitle": "TLOG object read during SPDD phase", "RefUrl": "/notes/2617883"}, {"RefNumber": "1909902", "RefComponent": "SV-SMG-MON-JBI-JOB", "RefTitle": "Job Mon: Prerequisities for SP10", "RefUrl": "/notes/1909902"}, {"RefNumber": "1817043", "RefComponent": "SV-SMG", "RefTitle": "SAP Solution Manager - Basic functions 7.1 SP9", "RefUrl": "/notes/1817043"}, {"RefNumber": "1775242", "RefComponent": "SV-SMG-INS-CFG", "RefTitle": "SAP Solution Manager 7.1 SP8 - Basic functions", "RefUrl": "/notes/1775242"}, {"RefNumber": "1738132", "RefComponent": "BC-DWB-TOO-CLA", "RefTitle": "Change to inheritance results in deletion of method implementation", "RefUrl": "/notes/1738132"}, {"RefNumber": "1734341", "RefComponent": "SV-SMG-INS-CFG", "RefTitle": "SAP Solution Manager 7.1 SP7 - Basic functions", "RefUrl": "/notes/1734341"}, {"RefNumber": "1731244", "RefComponent": "BW-BEX-OT", "RefTitle": "Implementation/deimplementation of advance corrections in SAP Notes", "RefUrl": "/notes/1731244"}, {"RefNumber": "1722332", "RefComponent": "SV-SMG-INS-CFG", "RefTitle": "SAP Solution Manager 7.1 SP6 - Basic functions", "RefUrl": "/notes/1722332"}, {"RefNumber": "1720495", "RefComponent": "BC-UPG-NA", "RefTitle": "Invalid deimplementation of obsolete notes by Snote tool", "RefUrl": "/notes/1720495"}, {"RefNumber": "1690165", "RefComponent": "BC-DWB-PRX", "RefTitle": "snote/scwb for proxies to external WSDLs", "RefUrl": "/notes/1690165"}, {"RefNumber": "1685578", "RefComponent": "BC-UPG-NA", "RefTitle": "Problem applying a note through Snote", "RefUrl": "/notes/1685578"}, {"RefNumber": "1668882", "RefComponent": "BC-UPG-NA", "RefTitle": "Note Assistant: Important notes for SAP_BASIS 730,731,740,750,751,752,753,754,755,756", "RefUrl": "/notes/1668882"}, {"RefNumber": "1645134", "RefComponent": "IS-U-MD", "RefTitle": "ES60: Field length for location details of premise", "RefUrl": "/notes/1645134"}, {"RefNumber": "1639074", "RefComponent": "BC-UPG-NA", "RefTitle": "SNOTE: Note data is not current or is incorrect", "RefUrl": "/notes/1639074"}, {"RefNumber": "1633725", "RefComponent": "SV-SMG-INS-CFG", "RefTitle": "SAP Solution Manager 7.1 SP4 - basic functions", "RefUrl": "/notes/1633725"}, {"RefNumber": "1627683", "RefComponent": "BC-UPG-NA", "RefTitle": "SCWB/SNOTE/SPAU: Changed development package", "RefUrl": "/notes/1627683"}, {"RefNumber": "1624603", "RefComponent": "BC-UPG-NA", "RefTitle": "SCWB: Transferring corrections via TLOG object types", "RefUrl": "/notes/1624603"}, {"RefNumber": "1621321", "RefComponent": "BC-UPG-NA", "RefTitle": "SNOTE/SCWB for screens: Field attribute minimum line/row", "RefUrl": "/notes/1621321"}, {"RefNumber": "1619713", "RefComponent": "BC-DWB-PRX", "RefTitle": "Transactions SCWB or SNOTE delete inline objects", "RefUrl": "/notes/1619713"}, {"RefNumber": "1613410", "RefComponent": "SV-SMG", "RefTitle": "SAP Solution Manager - Basic functions 7.1 SP3", "RefUrl": "/notes/1613410"}, {"RefNumber": "1571213", "RefComponent": "BC-DWB-TOO-CLA", "RefTitle": "SE24: Note Assistant does not adjust component type", "RefUrl": "/notes/1571213"}, {"RefNumber": "1566290", "RefComponent": "BC-DWB-TOO-CLA", "RefTitle": "Creation of interfaces using Note Assistant fails", "RefUrl": "/notes/1566290"}, {"RefNumber": "1557768", "RefComponent": "BC-UPG-NA", "RefTitle": "Note Assistant: find the notes that modify an object", "RefUrl": "/notes/1557768"}, {"RefNumber": "1552759", "RefComponent": "BC-UPG-NA", "RefTitle": "Note Assistant: TLOGO and generated objects", "RefUrl": "/notes/1552759"}, {"RefNumber": "1552560", "RefComponent": "BC-UPG-NA", "RefTitle": "SNOTE: Element sequence in queue for implem./deimplementing", "RefUrl": "/notes/1552560"}, {"RefNumber": "1549103", "RefComponent": "BC-UPG-NA", "RefTitle": "SNOTE: Problems with language-dependent notes", "RefUrl": "/notes/1549103"}, {"RefNumber": "1541531", "RefComponent": "BC-UPG-NA", "RefTitle": "Pushbuttons lost after nested popups", "RefUrl": "/notes/1541531"}, {"RefNumber": "1539505", "RefComponent": "BC-UPG-NA", "RefTitle": "SNOTE: Minor corrections", "RefUrl": "/notes/1539505"}, {"RefNumber": "1537354", "RefComponent": "BC-UPG-NA", "RefTitle": "Support for downloading huge notes over slow connections", "RefUrl": "/notes/1537354"}, {"RefNumber": "1535724", "RefComponent": "BC-UPG-NA", "RefTitle": "Activation problems with Enhancement Objects", "RefUrl": "/notes/1535724"}, {"RefNumber": "1534245", "RefComponent": "BC-UPG-NA", "RefTitle": "Notes can't be deimplemented: missing content table", "RefUrl": "/notes/1534245"}, {"RefNumber": "1532264", "RefComponent": "BC-UPG-NA", "RefTitle": "Further improved PDF downloads in Note Assistant", "RefUrl": "/notes/1532264"}, {"RefNumber": "1530273", "RefComponent": "BC-UPG-NA", "RefTitle": "Note Assistant: Incorrect error message for INTF methods", "RefUrl": "/notes/1530273"}, {"RefNumber": "1525115", "RefComponent": "BC-DWB-WD-ABA", "RefTitle": "Problem when retrieving view version", "RefUrl": "/notes/1525115"}, {"RefNumber": "1523687", "RefComponent": "BC-UPG-NA", "RefTitle": "SNOTE: Notes - Interface enhancement", "RefUrl": "/notes/1523687"}, {"RefNumber": "1518861", "RefComponent": "BC-UPG-NA", "RefTitle": "Wrong handling of deleted objects in SNOTE", "RefUrl": "/notes/1518861"}, {"RefNumber": "1517468", "RefComponent": "BC-UPG-NA", "RefTitle": "Corrupted versions when transferring them via RFC", "RefUrl": "/notes/1517468"}, {"RefNumber": "1512399", "RefComponent": "BC-UPG-NA", "RefTitle": "Necessity of implementing note 875986 or 991950", "RefUrl": "/notes/1512399"}, {"RefNumber": "1506104", "RefComponent": "BC-DWB-TOO-ENH", "RefTitle": "Error message when note implementation canceled", "RefUrl": "/notes/1506104"}, {"RefNumber": "1504500", "RefComponent": "BC-UPG-NA", "RefTitle": "Note Assistant: Navigation from Transport Organizer", "RefUrl": "/notes/1504500"}, {"RefNumber": "1500456", "RefComponent": "BC-UPG-NA", "RefTitle": "Note Assistant: Side effect notes that are not released", "RefUrl": "/notes/1500456"}, {"RefNumber": "1497858", "RefComponent": "BC-UPG-NA", "RefTitle": "Incorrect original system after implementing note", "RefUrl": "/notes/1497858"}, {"RefNumber": "1497520", "RefComponent": "BC-UPG-NA", "RefTitle": "Improvement of error messages in Note Assistant", "RefUrl": "/notes/1497520"}, {"RefNumber": "1487661", "RefComponent": "BC-CTS-ORG", "RefTitle": "Authorization check when deleting from version management", "RefUrl": "/notes/1487661"}, {"RefNumber": "1485083", "RefComponent": "BC-DWB-TOO-FUB", "RefTitle": "SE37: Dump when implementing notes or in transaction SCWB", "RefUrl": "/notes/1485083"}, {"RefNumber": "1478610", "RefComponent": "BC-DWB-CEX", "RefTitle": "SPAU: Note is displayed several times in SPAU", "RefUrl": "/notes/1478610"}, {"RefNumber": "1473623", "RefComponent": "BC-UPG-NA", "RefTitle": "Redundant PDF downloads in the Note Assistant", "RefUrl": "/notes/1473623"}, {"RefNumber": "1469642", "RefComponent": "BC-DWB-CEX", "RefTitle": "SPAU: Dump DATA_OFFSET_NEGATIVE when double-clicking note", "RefUrl": "/notes/1469642"}, {"RefNumber": "1468591", "RefComponent": "SV-SMG-INS-CFG", "RefTitle": "SAP Solution Manager - basic functions SP25 - SP27", "RefUrl": "/notes/1468591"}, {"RefNumber": "1437981", "RefComponent": "BC-UPG-NA", "RefTitle": "Note Assistant: Monitoring tool", "RefUrl": "/notes/1437981"}, {"RefNumber": "1433214", "RefComponent": "BC-UPG-NA", "RefTitle": "SCWB_NOTE_ACTIVATE: misleading status message", "RefUrl": "/notes/1433214"}, {"RefNumber": "1428701", "RefComponent": "BC-DWB-WD-ABA", "RefTitle": "Note Ass.: Red traffic light in merge editor for view def.", "RefUrl": "/notes/1428701"}, {"RefNumber": "1425487", "RefComponent": "BC-DWB-TOO-CLA", "RefTitle": "SE24: Error regarding READ-ONLY for complex attributes", "RefUrl": "/notes/1425487"}, {"RefNumber": "1415680", "RefComponent": "BC-UPG-NA", "RefTitle": "Note Assistant: Incorrect status in subsequent systems", "RefUrl": "/notes/1415680"}, {"RefNumber": "1412719", "RefComponent": "BC-DWB-TOO-ENH", "RefTitle": "SNOTE: error when implementing enhancement implementations", "RefUrl": "/notes/1412719"}, {"RefNumber": "1409699", "RefComponent": "BC-DWB-WD-ABA", "RefTitle": "Error SWDP_WB_TOOL 437 or 442 during WD note implementation", "RefUrl": "/notes/1409699"}, {"RefNumber": "1397709", "RefComponent": "BC-UPG-NA", "RefTitle": "Ignore Dynpro element fields AGLT and ADEZ in SNOTE/CWB", "RefUrl": "/notes/1397709"}, {"RefNumber": "1379746", "RefComponent": "BC-DWB-TOO-CLA", "RefTitle": "Implement note: Modification Assistant dialog box displayed", "RefUrl": "/notes/1379746"}, {"RefNumber": "1365677", "RefComponent": "BC-UPG-NA", "RefTitle": "Note Assistant: Runtime error MOVE_CAST_ERROR during implmtn", "RefUrl": "/notes/1365677"}, {"RefNumber": "1350005", "RefComponent": "BC-DWB-TOO-SFW", "RefTitle": "SCWB dump: Access via 'NULL' object reference not possible", "RefUrl": "/notes/1350005"}, {"RefNumber": "1349277", "RefComponent": "BC-UPG-NA", "RefTitle": "Note Assistant: Method cannot be implemented", "RefUrl": "/notes/1349277"}, {"RefNumber": "1342598", "RefComponent": "BC-UPG-NA", "RefTitle": "SCWB: Unnecessary locks when maintaining special approvals", "RefUrl": "/notes/1342598"}, {"RefNumber": "1334440", "RefComponent": "BC-DWB-TOO-CLA", "RefTitle": "SE24: Error if creating with Note Assistant or SCWB", "RefUrl": "/notes/1334440"}, {"RefNumber": "1316626", "RefComponent": "BC-UPG-NA", "RefTitle": "CWB: Many error messages when you grant special approvals", "RefUrl": "/notes/1316626"}, {"RefNumber": "1291055", "RefComponent": "BC-UPG-NA", "RefTitle": "Note Assistant: Problem activating exception classes", "RefUrl": "/notes/1291055"}, {"RefNumber": "1287384", "RefComponent": "BC-DWB-TOO-CLA", "RefTitle": "SNOTE: Source of component cannot be changed", "RefUrl": "/notes/1287384"}, {"RefNumber": "1286126", "RefComponent": "BC-UPG-NA", "RefTitle": "Syntax error in CL_CWB_OBJECT_...", "RefUrl": "/notes/1286126"}, {"RefNumber": "1276929", "RefComponent": "BC-DWB-TOO-ENH", "RefTitle": "SNOTE: Error message \"Object cannot be created again...\"", "RefUrl": "/notes/1276929"}, {"RefNumber": "1262826", "RefComponent": "BC-UPG-NA", "RefTitle": "SNOTE: Error when deimplementing notes with ENHO objects", "RefUrl": "/notes/1262826"}, {"RefNumber": "1262653", "RefComponent": "BC-DWB-CEX", "RefTitle": "SPAU: New object is deleted after note is reset", "RefUrl": "/notes/1262653"}, {"RefNumber": "1260089", "RefComponent": "BC-DWB-TOO-ENH", "RefTitle": "SNOTE: Implementing notes that have ENHO and ENHS objects", "RefUrl": "/notes/1260089"}, {"RefNumber": "1246982", "RefComponent": "BC-DWB-TOO-ENH", "RefTitle": "SNOTE: Dump when implementing ENHO/ENHS objects", "RefUrl": "/notes/1246982"}, {"RefNumber": "1246964", "RefComponent": "BC-UPG-NA", "RefTitle": "Note Assistant: Master language of notes incorrect", "RefUrl": "/notes/1246964"}, {"RefNumber": "1231230", "RefComponent": "BC-UPG-NA", "RefTitle": "Note Assistant: Runtime error MOVE_TO_LIT_NOTALLOWED_NODATA", "RefUrl": "/notes/1231230"}, {"RefNumber": "1151243", "RefComponent": "BC-UPG-NA", "RefTitle": "Note Assistant: <PERSON><PERSON><PERSON> MOVE_CAST_ERROR when implementing", "RefUrl": "/notes/1151243"}, {"RefNumber": "1151059", "RefComponent": "BC-DWB-WD-ABA", "RefTitle": "Endless loop due to missing component usage", "RefUrl": "/notes/1151059"}, {"RefNumber": "1150982", "RefComponent": "BC-UPG-NA", "RefTitle": "Note Assistant: Deleting method def. when displaying a note", "RefUrl": "/notes/1150982"}, {"RefNumber": "1147830", "RefComponent": "BC-UPG-NA", "RefTitle": "Note Assistant: Changes to WAPP objs cannot be implemented", "RefUrl": "/notes/1147830"}, {"RefNumber": "1142128", "RefComponent": "BC-UPG", "RefTitle": "Note Assistant: Obsolete notes are incorrectly deimplemented", "RefUrl": "/notes/1142128"}, {"RefNumber": "1130404", "RefComponent": "BC-DWB-TOO-CLA", "RefTitle": "Problem in FB RPY_EXISTENCE_CHECK_C*", "RefUrl": "/notes/1130404"}, {"RefNumber": "1126192", "RefComponent": "BC-UPG-NA", "RefTitle": "Note Assistant: Error when checking dependent objects", "RefUrl": "/notes/1126192"}, {"RefNumber": "1121795", "RefComponent": "BC-DWB-WD-ABA", "RefTitle": "Text characteristics deleted after Web Dynpro note implement", "RefUrl": "/notes/1121795"}, {"RefNumber": "1114973", "RefComponent": "BC-UPG-NA", "RefTitle": "Note Assistant: Incorrect message when downloading note", "RefUrl": "/notes/1114973"}, {"RefNumber": "1113339", "RefComponent": "BC-DWB-TOO-CLA", "RefTitle": "SE24 / Note Assistant and methods with exception classes", "RefUrl": "/notes/1113339"}, {"RefNumber": "1112141", "RefComponent": "BC-UPG-NA", "RefTitle": "Note Assistant: Implementing notes using modules EXIT_", "RefUrl": "/notes/1112141"}, {"RefNumber": "1106385", "RefComponent": "BC-UPG-NA", "RefTitle": "Unrequired removal of correction instructions", "RefUrl": "/notes/1106385"}, {"RefNumber": "1097666", "RefComponent": "BC-UPG-NA", "RefTitle": "Note Assistant: Changes during note implementation", "RefUrl": "/notes/1097666"}, {"RefNumber": "1094372", "RefComponent": "BC-UPG-NA", "RefTitle": "Note Assistant: SMODISRC entries for Repository objects", "RefUrl": "/notes/1094372"}, {"RefNumber": "1092828", "RefComponent": "BC-DWB-TOO-CLA", "RefTitle": "SE24: Versioning of modification info & Note Assistant", "RefUrl": "/notes/1092828"}, {"RefNumber": "1088433", "RefComponent": "BC-UPG-NA", "RefTitle": "Note Assistant: Termination in the Merge Editor", "RefUrl": "/notes/1088433"}, {"RefNumber": "1077466", "RefComponent": "BC-UPG-NA", "RefTitle": "Note Assistant:\"Internal error\" when displaying note", "RefUrl": "/notes/1077466"}, {"RefNumber": "1073502", "RefComponent": "BC-UPG-NA", "RefTitle": "Note Assistant: Termination and log when downloading a note", "RefUrl": "/notes/1073502"}, {"RefNumber": "1070182", "RefComponent": "BC-DWB-UTL-BRR", "RefTitle": "Error during mass activation in the background", "RefUrl": "/notes/1070182"}, {"RefNumber": "1066203", "RefComponent": "BC-UPG-NA", "RefTitle": "Note Assistant: ITAB_DUPLICATE_KEY_IDX_OP when implementing", "RefUrl": "/notes/1066203"}, {"RefNumber": "1065164", "RefComponent": "BC-DWB-TOO-CLA", "RefTitle": "SE24: Corr<PERSON> Workbench and initial vals for interface attrib.", "RefUrl": "/notes/1065164"}, {"RefNumber": "1064567", "RefComponent": "BC-DWB-CEX", "RefTitle": "SNOTE: Enhancement of FB CLM_CHECK_SMODILOG_ENTRY", "RefUrl": "/notes/1064567"}, {"RefNumber": "1059707", "RefComponent": "BC-UPG-NA", "RefTitle": "Note Assistant: Dump when exporting the history (WAPP,WDYC)", "RefUrl": "/notes/1059707"}, {"RefNumber": "1059121", "RefComponent": "BC-DWB-TOO-ENH", "RefTitle": "SNOTE: Missing entries in composites type: ENSC, ENHC", "RefUrl": "/notes/1059121"}, {"RefNumber": "1058809", "RefComponent": "BC-DWB-TOO-ENH", "RefTitle": "Detailed delivery of enhancements using SNOTE", "RefUrl": "/notes/1058809"}, {"RefNumber": "1050569", "RefComponent": "BC-DWB-TOO-ENH", "RefTitle": "Duplicate filter vals w/ BAdIs when implementing using SNOTE", "RefUrl": "/notes/1050569"}, {"RefNumber": "1041386", "RefComponent": "BC-UPG-NA", "RefTitle": "Release info is incomplete after notes are transported", "RefUrl": "/notes/1041386"}, {"RefNumber": "1037672", "RefComponent": "BC-UPG-NA", "RefTitle": "Note Assistant: Different languages in the note display", "RefUrl": "/notes/1037672"}, {"RefNumber": "1034360", "RefComponent": "BC-DWB-TOO-ENH", "RefTitle": "ENHS object missing when implementing implicit ENHO objects", "RefUrl": "/notes/1034360"}, {"RefNumber": "1034004", "RefComponent": "BC-DWB-TOO-ENH", "RefTitle": "Confusing dialog box when implementing note for enh. impl.", "RefUrl": "/notes/1034004"}, {"RefNumber": "1031649", "RefComponent": "BC-UPG-NA", "RefTitle": "Note Assistant:Changes to WAPP cannot be implemented", "RefUrl": "/notes/1031649"}, {"RefNumber": "1031630", "RefComponent": "BC-UPG-NA", "RefTitle": "Note Assistant: \"Internal error\" with Web Dynpro objects", "RefUrl": "/notes/1031630"}, {"RefNumber": "1029666", "RefComponent": "BC-DWB-TOO-ENH", "RefTitle": "SNOTE/BAdI Impl.:Wrong filter value after you implement note", "RefUrl": "/notes/1029666"}, {"RefNumber": "1027648", "RefComponent": "BC-DWB-WD-ABA", "RefTitle": "Inconsistency when reading a component interface definition", "RefUrl": "/notes/1027648"}, {"RefNumber": "1020107", "RefComponent": "BC-UPG-NA", "RefTitle": "Note Assistant: Message type \" \" is unknown", "RefUrl": "/notes/1020107"}, {"RefNumber": "1020093", "RefComponent": "BC-DWB-TOO-ENH", "RefTitle": "SNOTE: Incorrect check on TADIR entry", "RefUrl": "/notes/1020093"}, {"RefNumber": "1010679", "RefComponent": "BC-DWB-TOO-ENH", "RefTitle": "Dump during enhancement versioning", "RefUrl": "/notes/1010679"}, {"RefNumber": "1009932", "RefComponent": "BC-UPG-NA", "RefTitle": "Note Assistant: Inconsistent data in a note with WDYN object", "RefUrl": "/notes/1009932"}, {"RefNumber": "1008540", "RefComponent": "BC-UPG-NA", "RefTitle": "Note Assistant: Dump when writing history", "RefUrl": "/notes/1008540"}, {"RefNumber": "1008436", "RefComponent": "BC-DWB-TOO-ENH", "RefTitle": "Duplicate parameters when importing a class enhancement", "RefUrl": "/notes/1008436"}, {"RefNumber": "1008275", "RefComponent": "BC-DWB-TOO-ENH", "RefTitle": "CWB: Dump when importing ENHS objects", "RefUrl": "/notes/1008275"}, {"RefNumber": "1007754", "RefComponent": "BC-UPG-NA", "RefTitle": "Note Assistant: Incorrect code for enhancement objects", "RefUrl": "/notes/1007754"}, {"RefNumber": "1005849", "RefComponent": "BC-DWB-TOO-ENH", "RefTitle": "Error when using SNOTE to implement BAdI definitions", "RefUrl": "/notes/1005849"}, {"RefNumber": "1004691", "RefComponent": "BC-UPG-NA", "RefTitle": "SNOTE: OBJECTS_OBJREF_NOT_ASSIGNED when you implement a note", "RefUrl": "/notes/1004691"}, {"RefNumber": "1004661", "RefComponent": "BC-DWB-TOO-ENH", "RefTitle": "SNOTE for BAdI: Error when implementing screen enhancements", "RefUrl": "/notes/1004661"}, {"RefNumber": "1004293", "RefComponent": "BC-UPG-NA", "RefTitle": "Note Assistant: Delta display in note log", "RefUrl": "/notes/1004293"}, {"RefNumber": "1004110", "RefComponent": "BC-UPG-NA", "RefTitle": "Note Assistant: Implementation status after note download", "RefUrl": "/notes/1004110"}, {"RefNumber": "1000448", "RefComponent": "BC-DWB-TOO-CLA", "RefTitle": "Redefinition deleted when implementing a note", "RefUrl": "/notes/1000448"}, {"RefNumber": "1000332", "RefComponent": "BC-UPG-NA", "RefTitle": "Note Assistant: Problems with source code and WDYC", "RefUrl": "/notes/1000332"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "3269532", "RefComponent": "BC-UPG-NA", "RefTitle": "Object unkown error while implementing SAP Note 2627665", "RefUrl": "/notes/3269532 "}, {"RefNumber": "3027713", "RefComponent": "XX-CSC-BR-SD", "RefTitle": "NF-e Technical Note 2020.006 v1.00 Troubleshooting Guide", "RefUrl": "/notes/3027713 "}, {"RefNumber": "2430089", "RefComponent": "BC-UPG-NA", "RefTitle": "Downloading note correction timeout after long duration", "RefUrl": "/notes/2430089 "}, {"RefNumber": "3024048", "RefComponent": "BC-UPG-NA", "RefTitle": "Format of correction instructions; unable to read corr. instruct.", "RefUrl": "/notes/3024048 "}, {"RefNumber": "2415916", "RefComponent": "BC-DWB-CEX", "RefTitle": "EU526 \"Carry out modification comparison first\" occurs when to change SAP Standard objects (1)", "RefUrl": "/notes/2415916 "}, {"RefNumber": "2971405", "RefComponent": "BC-UPG-NA", "RefTitle": "Message \"Data Dictionary Objects will not be de-implemented\" during note de-implementation", "RefUrl": "/notes/2971405 "}, {"RefNumber": "2950129", "RefComponent": "BC-UPG-NA", "RefTitle": "Dump OBJECTS_OBJREF_NOT_ASSIGNED during download a note", "RefUrl": "/notes/2950129 "}, {"RefNumber": "2915196", "RefComponent": "FI-LOC-FI-AR-REP", "RefTitle": "Argentina Tax Reporting (RPFIGLAR_TAXREPORTING): Installation Overview", "RefUrl": "/notes/2915196 "}, {"RefNumber": "1828131", "RefComponent": "BC-UPG-NA", "RefTitle": "SNOTE Error : Message no. TK111: Mixed Objects With and Without Change Recording", "RefUrl": "/notes/1828131 "}, {"RefNumber": "2786404", "RefComponent": "BC-DB-DB2-CCM", "RefTitle": "ABAP program RSDB2J00 errors with exception CX_SY_DYN_CALL_ILLEGAL_TYPE", "RefUrl": "/notes/2786404 "}, {"RefNumber": "2717209", "RefComponent": "BC-UPG-NA", "RefTitle": "Cannot deimplement note in Note Assistant", "RefUrl": "/notes/2717209 "}, {"RefNumber": "2688217", "RefComponent": "BC-UPG-NA", "RefTitle": "SPAM/SAINT: ST-A/PI or ST-PI Prerequisite check for note 1487337  fails in CHECK_REQUIREMENTS  step", "RefUrl": "/notes/2688217 "}, {"RefNumber": "1928534", "RefComponent": "BC-UPG-NA", "RefTitle": "Obsolete SAP notes de-implemented in SPAU with SCWN029", "RefUrl": "/notes/1928534 "}, {"RefNumber": "2525335", "RefComponent": "BC-UPG-NA", "RefTitle": "Software component XXX does not exist", "RefUrl": "/notes/2525335 "}, {"RefNumber": "2499284", "RefComponent": "XX-CSC-PT-FI-SAF", "RefTitle": "RPFIEU_SAFT - SAFT 1.04_01 validation errors - Portugal", "RefUrl": "/notes/2499284 "}, {"RefNumber": "2482850", "RefComponent": "BC-UPG-NA", "RefTitle": "Change user or transport during note implementation", "RefUrl": "/notes/2482850 "}, {"RefNumber": "2472425", "RefComponent": "BC-UPG-NA", "RefTitle": "Error while implementing note 2019086", "RefUrl": "/notes/2472425 "}, {"RefNumber": "1830987", "RefComponent": "BC-FES-ITS", "RefTitle": "ITS Note cannot be implemented in non-unicode systems", "RefUrl": "/notes/1830987 "}, {"RefNumber": "3140903", "RefComponent": "PY-RU", "RefTitle": "Wrong log of payroll function RUPRI", "RefUrl": "/notes/3140903 "}, {"RefNumber": "2241938", "RefComponent": "SV-SMG-INS-CFG", "RefTitle": "SAP Solution Manager 7.1 Support Package 15 - Basic functions", "RefUrl": "/notes/2241938 "}, {"RefNumber": "2110259", "RefComponent": "SV-SMG-INS-CFG", "RefTitle": "SAP Solution Manager 7.1 SP14 - basic functions", "RefUrl": "/notes/2110259 "}, {"RefNumber": "2048315", "RefComponent": "SV-SMG-INS-CFG", "RefTitle": "SAP Solution Manager 7.1 SP13 - Basic functions", "RefUrl": "/notes/2048315 "}, {"RefNumber": "1969109", "RefComponent": "SV-SMG-INS-CFG", "RefTitle": "SAP Solution Manager 7.1 SP12 - Basic functions", "RefUrl": "/notes/1969109 "}, {"RefNumber": "1972127", "RefComponent": "SV-SMG-MON-JBI-JOB", "RefTitle": "SP11: Job Monitoring prerequisites", "RefUrl": "/notes/1972127 "}, {"RefNumber": "1933506", "RefComponent": "SV-SMG-INS-CFG", "RefTitle": "SAP Solution Manager 7.1 SP11 - Basic functions", "RefUrl": "/notes/1933506 "}, {"RefNumber": "1834310", "RefComponent": "BW-SYS-DB-DB6", "RefTitle": "DB6: Use of Db2 BLU Acceleration with Db2 Near-Line Storage", "RefUrl": "/notes/1834310 "}, {"RefNumber": "1875627", "RefComponent": "SV-SMG-INS-CFG", "RefTitle": "SAP Solution Manager - basic functions for Release 7.1 Support Package 10", "RefUrl": "/notes/1875627 "}, {"RefNumber": "1557768", "RefComponent": "BC-UPG-NA", "RefTitle": "Note Assistant: find the notes that modify an object", "RefUrl": "/notes/1557768 "}, {"RefNumber": "1775242", "RefComponent": "SV-SMG-INS-CFG", "RefTitle": "SAP Solution Manager 7.1 SP8 - Basic functions", "RefUrl": "/notes/1775242 "}, {"RefNumber": "1817043", "RefComponent": "SV-SMG", "RefTitle": "SAP Solution Manager - Basic functions 7.1 SP9", "RefUrl": "/notes/1817043 "}, {"RefNumber": "1720495", "RefComponent": "BC-UPG-NA", "RefTitle": "Invalid deimplementation of obsolete notes by Snote tool", "RefUrl": "/notes/1720495 "}, {"RefNumber": "1909902", "RefComponent": "SV-SMG-MON-JBI-JOB", "RefTitle": "Job Mon: Prerequisities for SP10", "RefUrl": "/notes/1909902 "}, {"RefNumber": "1690165", "RefComponent": "BC-DWB-PRX", "RefTitle": "snote/scwb for proxies to external WSDLs", "RefUrl": "/notes/1690165 "}, {"RefNumber": "1619713", "RefComponent": "BC-DWB-PRX", "RefTitle": "Transactions SCWB or SNOTE delete inline objects", "RefUrl": "/notes/1619713 "}, {"RefNumber": "1633725", "RefComponent": "SV-SMG-INS-CFG", "RefTitle": "SAP Solution Manager 7.1 SP4 - basic functions", "RefUrl": "/notes/1633725 "}, {"RefNumber": "1685578", "RefComponent": "BC-UPG-NA", "RefTitle": "Problem applying a note through Snote", "RefUrl": "/notes/1685578 "}, {"RefNumber": "1523687", "RefComponent": "BC-UPG-NA", "RefTitle": "SNOTE: Notes - Interface enhancement", "RefUrl": "/notes/1523687 "}, {"RefNumber": "1722332", "RefComponent": "SV-SMG-INS-CFG", "RefTitle": "SAP Solution Manager 7.1 SP6 - Basic functions", "RefUrl": "/notes/1722332 "}, {"RefNumber": "1734341", "RefComponent": "SV-SMG-INS-CFG", "RefTitle": "SAP Solution Manager 7.1 SP7 - Basic functions", "RefUrl": "/notes/1734341 "}, {"RefNumber": "1668882", "RefComponent": "BC-UPG-NA", "RefTitle": "Note Assistant: Important notes for SAP_BASIS 730,731,740,750,751,752,753,754,755,756", "RefUrl": "/notes/1668882 "}, {"RefNumber": "1506237", "RefComponent": "BW-BEX-ET-WJR-BICS", "RefTitle": "BICS: Long time to execute SQL from BICS_PROV_GET_MEMBERS", "RefUrl": "/notes/1506237 "}, {"RefNumber": "1803939", "RefComponent": "CA-NO", "RefTitle": "Accelerators: Implementing Notes", "RefUrl": "/notes/1803939 "}, {"RefNumber": "1539505", "RefComponent": "BC-UPG-NA", "RefTitle": "SNOTE: Minor corrections", "RefUrl": "/notes/1539505 "}, {"RefNumber": "1468591", "RefComponent": "SV-SMG-INS-CFG", "RefTitle": "SAP Solution Manager - basic functions SP25 - SP27", "RefUrl": "/notes/1468591 "}, {"RefNumber": "1246964", "RefComponent": "BC-UPG-NA", "RefTitle": "Note Assistant: Master language of notes incorrect", "RefUrl": "/notes/1246964 "}, {"RefNumber": "1172948", "RefComponent": "SV-SMG", "RefTitle": "SAP Solution Manager: Basic functions", "RefUrl": "/notes/1172948 "}, {"RefNumber": "1613410", "RefComponent": "SV-SMG", "RefTitle": "SAP Solution Manager - Basic functions 7.1 SP3", "RefUrl": "/notes/1613410 "}, {"RefNumber": "1731244", "RefComponent": "BW-BEX-OT", "RefTitle": "Implementation/deimplementation of advance corrections in SAP Notes", "RefUrl": "/notes/1731244 "}, {"RefNumber": "1379746", "RefComponent": "BC-DWB-TOO-CLA", "RefTitle": "Implement note: Modification Assistant dialog box displayed", "RefUrl": "/notes/1379746 "}, {"RefNumber": "1738132", "RefComponent": "BC-DWB-TOO-CLA", "RefTitle": "Change to inheritance results in deletion of method implementation", "RefUrl": "/notes/1738132 "}, {"RefNumber": "1246982", "RefComponent": "BC-DWB-TOO-ENH", "RefTitle": "SNOTE: Dump when implementing ENHO/ENHS objects", "RefUrl": "/notes/1246982 "}, {"RefNumber": "1627683", "RefComponent": "BC-UPG-NA", "RefTitle": "SCWB/SNOTE/SPAU: Changed development package", "RefUrl": "/notes/1627683 "}, {"RefNumber": "1624603", "RefComponent": "BC-UPG-NA", "RefTitle": "SCWB: Transferring corrections via TLOG object types", "RefUrl": "/notes/1624603 "}, {"RefNumber": "1552560", "RefComponent": "BC-UPG-NA", "RefTitle": "SNOTE: Element sequence in queue for implem./deimplementing", "RefUrl": "/notes/1552560 "}, {"RefNumber": "1454577", "RefComponent": "BC-INS-TC-CNT", "RefTitle": "System Copy: Customer Enhancements", "RefUrl": "/notes/1454577 "}, {"RefNumber": "1512399", "RefComponent": "BC-UPG-NA", "RefTitle": "Necessity of implementing note 875986 or 991950", "RefUrl": "/notes/1512399 "}, {"RefNumber": "862198", "RefComponent": "PPM-PRO", "RefTitle": "cProject Suite 4.00 release notes and information notes", "RefUrl": "/notes/862198 "}, {"RefNumber": "1639074", "RefComponent": "BC-UPG-NA", "RefTitle": "SNOTE: Note data is not current or is incorrect", "RefUrl": "/notes/1639074 "}, {"RefNumber": "1497520", "RefComponent": "BC-UPG-NA", "RefTitle": "Improvement of error messages in Note Assistant", "RefUrl": "/notes/1497520 "}, {"RefNumber": "1645134", "RefComponent": "IS-U-MD", "RefTitle": "ES60: Field length for location details of premise", "RefUrl": "/notes/1645134 "}, {"RefNumber": "970837", "RefComponent": "BC-DWB-TOO-CLA", "RefTitle": "Creating methods with Note Assistant - mod infos created", "RefUrl": "/notes/970837 "}, {"RefNumber": "1059707", "RefComponent": "BC-UPG-NA", "RefTitle": "Note Assistant: Dump when exporting the history (WAPP,WDYC)", "RefUrl": "/notes/1059707 "}, {"RefNumber": "1549103", "RefComponent": "BC-UPG-NA", "RefTitle": "SNOTE: Problems with language-dependent notes", "RefUrl": "/notes/1549103 "}, {"RefNumber": "1621321", "RefComponent": "BC-UPG-NA", "RefTitle": "SNOTE/SCWB for screens: Field attribute minimum line/row", "RefUrl": "/notes/1621321 "}, {"RefNumber": "1334440", "RefComponent": "BC-DWB-TOO-CLA", "RefTitle": "SE24: Error if creating with Note Assistant or SCWB", "RefUrl": "/notes/1334440 "}, {"RefNumber": "1291055", "RefComponent": "BC-UPG-NA", "RefTitle": "Note Assistant: Problem activating exception classes", "RefUrl": "/notes/1291055 "}, {"RefNumber": "1488568", "RefComponent": "BW-BEX-OT-OLAP-HIER", "RefTitle": "Composite note :THJ", "RefUrl": "/notes/1488568 "}, {"RefNumber": "1535724", "RefComponent": "BC-UPG-NA", "RefTitle": "Activation problems with Enhancement Objects", "RefUrl": "/notes/1535724 "}, {"RefNumber": "1537354", "RefComponent": "BC-UPG-NA", "RefTitle": "Support for downloading huge notes over slow connections", "RefUrl": "/notes/1537354 "}, {"RefNumber": "1571213", "RefComponent": "BC-DWB-TOO-CLA", "RefTitle": "SE24: Note Assistant does not adjust component type", "RefUrl": "/notes/1571213 "}, {"RefNumber": "1566290", "RefComponent": "BC-DWB-TOO-CLA", "RefTitle": "Creation of interfaces using Note Assistant fails", "RefUrl": "/notes/1566290 "}, {"RefNumber": "1517468", "RefComponent": "BC-UPG-NA", "RefTitle": "Corrupted versions when transferring them via RFC", "RefUrl": "/notes/1517468 "}, {"RefNumber": "1504500", "RefComponent": "BC-UPG-NA", "RefTitle": "Note Assistant: Navigation from Transport Organizer", "RefUrl": "/notes/1504500 "}, {"RefNumber": "1437981", "RefComponent": "BC-UPG-NA", "RefTitle": "Note Assistant: Monitoring tool", "RefUrl": "/notes/1437981 "}, {"RefNumber": "1552759", "RefComponent": "BC-UPG-NA", "RefTitle": "Note Assistant: TLOGO and generated objects", "RefUrl": "/notes/1552759 "}, {"RefNumber": "1541531", "RefComponent": "BC-UPG-NA", "RefTitle": "Pushbuttons lost after nested popups", "RefUrl": "/notes/1541531 "}, {"RefNumber": "1464244", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "WJR:THJ hierarchy node selection is not retained", "RefUrl": "/notes/1464244 "}, {"RefNumber": "1534245", "RefComponent": "BC-UPG-NA", "RefTitle": "Notes can't be deimplemented: missing content table", "RefUrl": "/notes/1534245 "}, {"RefNumber": "1532264", "RefComponent": "BC-UPG-NA", "RefTitle": "Further improved PDF downloads in Note Assistant", "RefUrl": "/notes/1532264 "}, {"RefNumber": "1530273", "RefComponent": "BC-UPG-NA", "RefTitle": "Note Assistant: Incorrect error message for INTF methods", "RefUrl": "/notes/1530273 "}, {"RefNumber": "1506104", "RefComponent": "BC-DWB-TOO-ENH", "RefTitle": "Error message when note implementation canceled", "RefUrl": "/notes/1506104 "}, {"RefNumber": "1525115", "RefComponent": "BC-DWB-WD-ABA", "RefTitle": "Problem when retrieving view version", "RefUrl": "/notes/1525115 "}, {"RefNumber": "1518861", "RefComponent": "BC-UPG-NA", "RefTitle": "Wrong handling of deleted objects in SNOTE", "RefUrl": "/notes/1518861 "}, {"RefNumber": "1064567", "RefComponent": "BC-DWB-CEX", "RefTitle": "SNOTE: Enhancement of FB CLM_CHECK_SMODILOG_ENTRY", "RefUrl": "/notes/1064567 "}, {"RefNumber": "1349277", "RefComponent": "BC-UPG-NA", "RefTitle": "Note Assistant: Method cannot be implemented", "RefUrl": "/notes/1349277 "}, {"RefNumber": "1500456", "RefComponent": "BC-UPG-NA", "RefTitle": "Note Assistant: Side effect notes that are not released", "RefUrl": "/notes/1500456 "}, {"RefNumber": "1478610", "RefComponent": "BC-DWB-CEX", "RefTitle": "SPAU: Note is displayed several times in SPAU", "RefUrl": "/notes/1478610 "}, {"RefNumber": "1497858", "RefComponent": "BC-UPG-NA", "RefTitle": "Incorrect original system after implementing note", "RefUrl": "/notes/1497858 "}, {"RefNumber": "1487661", "RefComponent": "BC-CTS-ORG", "RefTitle": "Authorization check when deleting from version management", "RefUrl": "/notes/1487661 "}, {"RefNumber": "1485083", "RefComponent": "BC-DWB-TOO-FUB", "RefTitle": "SE37: Dump when implementing notes or in transaction SCWB", "RefUrl": "/notes/1485083 "}, {"RefNumber": "1473623", "RefComponent": "BC-UPG-NA", "RefTitle": "Redundant PDF downloads in the Note Assistant", "RefUrl": "/notes/1473623 "}, {"RefNumber": "1469642", "RefComponent": "BC-DWB-CEX", "RefTitle": "SPAU: Dump DATA_OFFSET_NEGATIVE when double-clicking note", "RefUrl": "/notes/1469642 "}, {"RefNumber": "1412719", "RefComponent": "BC-DWB-TOO-ENH", "RefTitle": "SNOTE: error when implementing enhancement implementations", "RefUrl": "/notes/1412719 "}, {"RefNumber": "984771", "RefComponent": "BC-UPG-NA", "RefTitle": "ENHO-SNOTE : Missing SOTR texts produce red traffic light", "RefUrl": "/notes/984771 "}, {"RefNumber": "1365677", "RefComponent": "BC-UPG-NA", "RefTitle": "Note Assistant: Runtime error MOVE_CAST_ERROR during implmtn", "RefUrl": "/notes/1365677 "}, {"RefNumber": "1020093", "RefComponent": "BC-DWB-TOO-ENH", "RefTitle": "SNOTE: Incorrect check on TADIR entry", "RefUrl": "/notes/1020093 "}, {"RefNumber": "1433214", "RefComponent": "BC-UPG-NA", "RefTitle": "SCWB_NOTE_ACTIVATE: misleading status message", "RefUrl": "/notes/1433214 "}, {"RefNumber": "1428701", "RefComponent": "BC-DWB-WD-ABA", "RefTitle": "Note Ass.: Red traffic light in merge editor for view def.", "RefUrl": "/notes/1428701 "}, {"RefNumber": "1260089", "RefComponent": "BC-DWB-TOO-ENH", "RefTitle": "SNOTE: Implementing notes that have ENHO and ENHS objects", "RefUrl": "/notes/1260089 "}, {"RefNumber": "1262653", "RefComponent": "BC-DWB-CEX", "RefTitle": "SPAU: New object is deleted after note is reset", "RefUrl": "/notes/1262653 "}, {"RefNumber": "1425487", "RefComponent": "BC-DWB-TOO-CLA", "RefTitle": "SE24: Error regarding READ-ONLY for complex attributes", "RefUrl": "/notes/1425487 "}, {"RefNumber": "1415680", "RefComponent": "BC-UPG-NA", "RefTitle": "Note Assistant: Incorrect status in subsequent systems", "RefUrl": "/notes/1415680 "}, {"RefNumber": "1409699", "RefComponent": "BC-DWB-WD-ABA", "RefTitle": "Error SWDP_WB_TOOL 437 or 442 during WD note implementation", "RefUrl": "/notes/1409699 "}, {"RefNumber": "1150982", "RefComponent": "BC-UPG-NA", "RefTitle": "Note Assistant: Deleting method def. when displaying a note", "RefUrl": "/notes/1150982 "}, {"RefNumber": "1397709", "RefComponent": "BC-UPG-NA", "RefTitle": "Ignore Dynpro element fields AGLT and ADEZ in SNOTE/CWB", "RefUrl": "/notes/1397709 "}, {"RefNumber": "1041386", "RefComponent": "BC-UPG-NA", "RefTitle": "Release info is incomplete after notes are transported", "RefUrl": "/notes/1041386 "}, {"RefNumber": "1342598", "RefComponent": "BC-UPG-NA", "RefTitle": "SCWB: Unnecessary locks when maintaining special approvals", "RefUrl": "/notes/1342598 "}, {"RefNumber": "1316626", "RefComponent": "BC-UPG-NA", "RefTitle": "CWB: Many error messages when you grant special approvals", "RefUrl": "/notes/1316626 "}, {"RefNumber": "1350005", "RefComponent": "BC-DWB-TOO-SFW", "RefTitle": "SCWB dump: Access via 'NULL' object reference not possible", "RefUrl": "/notes/1350005 "}, {"RefNumber": "1276929", "RefComponent": "BC-DWB-TOO-ENH", "RefTitle": "SNOTE: Error message \"Object cannot be created again...\"", "RefUrl": "/notes/1276929 "}, {"RefNumber": "1273778", "RefComponent": "BW", "RefTitle": "Currency conversion from SSK into Euro in SAP BW", "RefUrl": "/notes/1273778 "}, {"RefNumber": "1139316", "RefComponent": "AP-PRC-CON", "RefTitle": "Generation on the fly for test applications of cond.techn.", "RefUrl": "/notes/1139316 "}, {"RefNumber": "1262826", "RefComponent": "BC-UPG-NA", "RefTitle": "SNOTE: Error when deimplementing notes with ENHO objects", "RefUrl": "/notes/1262826 "}, {"RefNumber": "1286126", "RefComponent": "BC-UPG-NA", "RefTitle": "Syntax error in CL_CWB_OBJECT_...", "RefUrl": "/notes/1286126 "}, {"RefNumber": "1287384", "RefComponent": "BC-DWB-TOO-CLA", "RefTitle": "SNOTE: Source of component cannot be changed", "RefUrl": "/notes/1287384 "}, {"RefNumber": "1113339", "RefComponent": "BC-DWB-TOO-CLA", "RefTitle": "SE24 / Note Assistant and methods with exception classes", "RefUrl": "/notes/1113339 "}, {"RefNumber": "1070182", "RefComponent": "BC-DWB-UTL-BRR", "RefTitle": "Error during mass activation in the background", "RefUrl": "/notes/1070182 "}, {"RefNumber": "1065164", "RefComponent": "BC-DWB-TOO-CLA", "RefTitle": "SE24: Corr<PERSON> Workbench and initial vals for interface attrib.", "RefUrl": "/notes/1065164 "}, {"RefNumber": "1121795", "RefComponent": "BC-DWB-WD-ABA", "RefTitle": "Text characteristics deleted after Web Dynpro note implement", "RefUrl": "/notes/1121795 "}, {"RefNumber": "1151059", "RefComponent": "BC-DWB-WD-ABA", "RefTitle": "Endless loop due to missing component usage", "RefUrl": "/notes/1151059 "}, {"RefNumber": "905959", "RefComponent": "PPM-PRO", "RefTitle": "Dependencies between cProjects 4.0 SPs and NW 2004s SPs", "RefUrl": "/notes/905959 "}, {"RefNumber": "1231230", "RefComponent": "BC-UPG-NA", "RefTitle": "Note Assistant: Runtime error MOVE_TO_LIT_NOTALLOWED_NODATA", "RefUrl": "/notes/1231230 "}, {"RefNumber": "986226", "RefComponent": "BC-DWB-TOO-SCR", "RefTitle": "Termination CX_XSLT_RUNTIME_ERROR when downloading note", "RefUrl": "/notes/986226 "}, {"RefNumber": "1175671", "RefComponent": "BC-UPG-NA", "RefTitle": "Important information about problem analysis (CWB)", "RefUrl": "/notes/1175671 "}, {"RefNumber": "1112141", "RefComponent": "BC-UPG-NA", "RefTitle": "Note Assistant: Implementing notes using modules EXIT_", "RefUrl": "/notes/1112141 "}, {"RefNumber": "1158169", "RefComponent": "CRM-IU-S", "RefTitle": "IS-U CRM Integration- Enhancement spot ECRM_ISU_SI_TECHOBJ 1", "RefUrl": "/notes/1158169 "}, {"RefNumber": "967237", "RefComponent": "BC-DWB-TOO-CLA", "RefTitle": "No adjustment of type uses when note is implemented", "RefUrl": "/notes/967237 "}, {"RefNumber": "1147830", "RefComponent": "BC-UPG-NA", "RefTitle": "Note Assistant: Changes to WAPP objs cannot be implemented", "RefUrl": "/notes/1147830 "}, {"RefNumber": "1151243", "RefComponent": "BC-UPG-NA", "RefTitle": "Note Assistant: <PERSON><PERSON><PERSON> MOVE_CAST_ERROR when implementing", "RefUrl": "/notes/1151243 "}, {"RefNumber": "1004110", "RefComponent": "BC-UPG-NA", "RefTitle": "Note Assistant: Implementation status after note download", "RefUrl": "/notes/1004110 "}, {"RefNumber": "1073502", "RefComponent": "BC-UPG-NA", "RefTitle": "Note Assistant: Termination and log when downloading a note", "RefUrl": "/notes/1073502 "}, {"RefNumber": "1142128", "RefComponent": "BC-UPG", "RefTitle": "Note Assistant: Obsolete notes are incorrectly deimplemented", "RefUrl": "/notes/1142128 "}, {"RefNumber": "1114973", "RefComponent": "BC-UPG-NA", "RefTitle": "Note Assistant: Incorrect message when downloading note", "RefUrl": "/notes/1114973 "}, {"RefNumber": "1126192", "RefComponent": "BC-UPG-NA", "RefTitle": "Note Assistant: Error when checking dependent objects", "RefUrl": "/notes/1126192 "}, {"RefNumber": "1067808", "RefComponent": "FI-TV", "RefTitle": "FI-TV corrections checkman", "RefUrl": "/notes/1067808 "}, {"RefNumber": "1130404", "RefComponent": "BC-DWB-TOO-CLA", "RefTitle": "Problem in FB RPY_EXISTENCE_CHECK_C*", "RefUrl": "/notes/1130404 "}, {"RefNumber": "1106385", "RefComponent": "BC-UPG-NA", "RefTitle": "Unrequired removal of correction instructions", "RefUrl": "/notes/1106385 "}, {"RefNumber": "1090503", "RefComponent": "BW", "RefTitle": "Venezuelan currency conversion - Conversion in SAP BW", "RefUrl": "/notes/1090503 "}, {"RefNumber": "1083466", "RefComponent": "SRM-EBP", "RefTitle": "SRM 5.0 SP stack 10(09/2007) SAPKIBKT10 release/info note", "RefUrl": "/notes/1083466 "}, {"RefNumber": "1097666", "RefComponent": "BC-UPG-NA", "RefTitle": "Note Assistant: Changes during note implementation", "RefUrl": "/notes/1097666 "}, {"RefNumber": "983212", "RefComponent": "BC-DWB-TOO-CLA", "RefTitle": "Forward declaration is not deleted from metadata", "RefUrl": "/notes/983212 "}, {"RefNumber": "1092828", "RefComponent": "BC-DWB-TOO-CLA", "RefTitle": "SE24: Versioning of modification info & Note Assistant", "RefUrl": "/notes/1092828 "}, {"RefNumber": "1088433", "RefComponent": "BC-UPG-NA", "RefTitle": "Note Assistant: Termination in the Merge Editor", "RefUrl": "/notes/1088433 "}, {"RefNumber": "1082382", "RefComponent": "CRM-LAM-BF-FIM", "RefTitle": "Leasing: Rounding differences / alternative calculations", "RefUrl": "/notes/1082382 "}, {"RefNumber": "1004691", "RefComponent": "BC-UPG-NA", "RefTitle": "SNOTE: OBJECTS_OBJREF_NOT_ASSIGNED when you implement a note", "RefUrl": "/notes/1004691 "}, {"RefNumber": "1020107", "RefComponent": "BC-UPG-NA", "RefTitle": "Note Assistant: Message type \" \" is unknown", "RefUrl": "/notes/1020107 "}, {"RefNumber": "1074090", "RefComponent": "FI-TV", "RefTitle": "Travel ESS based on WD4A in ERP6.0 EHPs", "RefUrl": "/notes/1074090 "}, {"RefNumber": "1059121", "RefComponent": "BC-DWB-TOO-ENH", "RefTitle": "SNOTE: Missing entries in composites type: ENSC, ENHC", "RefUrl": "/notes/1059121 "}, {"RefNumber": "977082", "RefComponent": "BC-DWB-TOO-CLA", "RefTitle": "SE24: <PERSON><PERSON><PERSON> in connection with the Note Assistant", "RefUrl": "/notes/977082 "}, {"RefNumber": "1074625", "RefComponent": "FI-TV-COS", "RefTitle": "ESS EHP Address is not validated in request", "RefUrl": "/notes/1074625 "}, {"RefNumber": "1077466", "RefComponent": "BC-UPG-NA", "RefTitle": "Note Assistant:\"Internal error\" when displaying note", "RefUrl": "/notes/1077466 "}, {"RefNumber": "1066203", "RefComponent": "BC-UPG-NA", "RefTitle": "Note Assistant: ITAB_DUPLICATE_KEY_IDX_OP when implementing", "RefUrl": "/notes/1066203 "}, {"RefNumber": "1058809", "RefComponent": "BC-DWB-TOO-ENH", "RefTitle": "Detailed delivery of enhancements using SNOTE", "RefUrl": "/notes/1058809 "}, {"RefNumber": "1008540", "RefComponent": "BC-UPG-NA", "RefTitle": "Note Assistant: Dump when writing history", "RefUrl": "/notes/1008540 "}, {"RefNumber": "1050569", "RefComponent": "BC-DWB-TOO-ENH", "RefTitle": "Duplicate filter vals w/ BAdIs when implementing using SNOTE", "RefUrl": "/notes/1050569 "}, {"RefNumber": "965756", "RefComponent": "BC-DWB-TOO-ENH", "RefTitle": "Upgrade indicator set for SCWB/SNOTE", "RefUrl": "/notes/965756 "}, {"RefNumber": "1000448", "RefComponent": "BC-DWB-TOO-CLA", "RefTitle": "Redefinition deleted when implementing a note", "RefUrl": "/notes/1000448 "}, {"RefNumber": "1034004", "RefComponent": "BC-DWB-TOO-ENH", "RefTitle": "Confusing dialog box when implementing note for enh. impl.", "RefUrl": "/notes/1034004 "}, {"RefNumber": "1031630", "RefComponent": "BC-UPG-NA", "RefTitle": "Note Assistant: \"Internal error\" with Web Dynpro objects", "RefUrl": "/notes/1031630 "}, {"RefNumber": "1037672", "RefComponent": "BC-UPG-NA", "RefTitle": "Note Assistant: Different languages in the note display", "RefUrl": "/notes/1037672 "}, {"RefNumber": "1034360", "RefComponent": "BC-DWB-TOO-ENH", "RefTitle": "ENHS object missing when implementing implicit ENHO objects", "RefUrl": "/notes/1034360 "}, {"RefNumber": "989122", "RefComponent": "BC-DWB-TOO-ENH", "RefTitle": "Error in SNOTE for enhancements", "RefUrl": "/notes/989122 "}, {"RefNumber": "1029666", "RefComponent": "BC-DWB-TOO-ENH", "RefTitle": "SNOTE/BAdI Impl.:Wrong filter value after you implement note", "RefUrl": "/notes/1029666 "}, {"RefNumber": "1031649", "RefComponent": "BC-UPG-NA", "RefTitle": "Note Assistant:Changes to WAPP cannot be implemented", "RefUrl": "/notes/1031649 "}, {"RefNumber": "1027648", "RefComponent": "BC-DWB-WD-ABA", "RefTitle": "Inconsistency when reading a component interface definition", "RefUrl": "/notes/1027648 "}, {"RefNumber": "978398", "RefComponent": "BC-DWB-TOO-CLA", "RefTitle": "Changing the class include structure using SNOTE/SCWB", "RefUrl": "/notes/978398 "}, {"RefNumber": "975510", "RefComponent": "BC-DWB-TOO-CLA", "RefTitle": "Corrections for classes/interfaces implemented incorrectly", "RefUrl": "/notes/975510 "}, {"RefNumber": "920714", "RefComponent": "BC-UPG-OCS", "RefTitle": "ENHO objects in SCWB/SNOTE are too large", "RefUrl": "/notes/920714 "}, {"RefNumber": "976918", "RefComponent": "BC-UPG-NA", "RefTitle": "Note Assistant: Error during note display with DYNP deltas", "RefUrl": "/notes/976918 "}, {"RefNumber": "1004293", "RefComponent": "BC-UPG-NA", "RefTitle": "Note Assistant: Delta display in note log", "RefUrl": "/notes/1004293 "}, {"RefNumber": "983534", "RefComponent": "BC-UPG-NA", "RefTitle": "Note Assistant: <PERSON>lank references enhancement implementation", "RefUrl": "/notes/983534 "}, {"RefNumber": "1007754", "RefComponent": "BC-UPG-NA", "RefTitle": "Note Assistant: Incorrect code for enhancement objects", "RefUrl": "/notes/1007754 "}, {"RefNumber": "1009932", "RefComponent": "BC-UPG-NA", "RefTitle": "Note Assistant: Inconsistent data in a note with WDYN object", "RefUrl": "/notes/1009932 "}, {"RefNumber": "1010679", "RefComponent": "BC-DWB-TOO-ENH", "RefTitle": "Dump during enhancement versioning", "RefUrl": "/notes/1010679 "}, {"RefNumber": "1008436", "RefComponent": "BC-DWB-TOO-ENH", "RefTitle": "Duplicate parameters when importing a class enhancement", "RefUrl": "/notes/1008436 "}, {"RefNumber": "1008275", "RefComponent": "BC-DWB-TOO-ENH", "RefTitle": "CWB: Dump when importing ENHS objects", "RefUrl": "/notes/1008275 "}, {"RefNumber": "1005849", "RefComponent": "BC-DWB-TOO-ENH", "RefTitle": "Error when using SNOTE to implement BAdI definitions", "RefUrl": "/notes/1005849 "}, {"RefNumber": "1000332", "RefComponent": "BC-UPG-NA", "RefTitle": "Note Assistant: Problems with source code and WDYC", "RefUrl": "/notes/1000332 "}, {"RefNumber": "992831", "RefComponent": "BC-UPG-NA", "RefTitle": "Note Assistant: Incompl correction instructions in note log", "RefUrl": "/notes/992831 "}, {"RefNumber": "1004661", "RefComponent": "BC-DWB-TOO-ENH", "RefTitle": "SNOTE for BAdI: Error when implementing screen enhancements", "RefUrl": "/notes/1004661 "}, {"RefNumber": "959711", "RefComponent": "BC-UPG-NA", "RefTitle": "SNOTE: Error in composite enhancement spots", "RefUrl": "/notes/959711 "}, {"RefNumber": "935140", "RefComponent": "BC-DWB-TOO-CLA", "RefTitle": "SEO_CLASS_TYPEINFO_GET returns improper typesrc length", "RefUrl": "/notes/935140 "}, {"RefNumber": "980288", "RefComponent": "BC-UPG-NA", "RefTitle": "Note Assistant:incorrect version data for WebDynpro and WAPP", "RefUrl": "/notes/980288 "}, {"RefNumber": "968138", "RefComponent": "BC-UPG-NA", "RefTitle": "Note Assistant: Restarting capability in the Note Assistant", "RefUrl": "/notes/968138 "}, {"RefNumber": "987261", "RefComponent": "BC-UPG-NA", "RefTitle": "Modification adjustment: Locks for objects", "RefUrl": "/notes/987261 "}, {"RefNumber": "998705", "RefComponent": "BC-BW", "RefTitle": "BI generation tool: Error RG 102 \"Syntax error in GP_MET_\"", "RefUrl": "/notes/998705 "}, {"RefNumber": "989056", "RefComponent": "BC-UPG-NA", "RefTitle": "Note implementation: Implementation-based instance relship", "RefUrl": "/notes/989056 "}, {"RefNumber": "932065", "RefComponent": "BC-UPG-NA", "RefTitle": "Note Assistant: Error during adjustment for classes in SPAU", "RefUrl": "/notes/932065 "}, {"RefNumber": "994673", "RefComponent": "BC-UPG-NA", "RefTitle": "Note Assistant: Dump GETWA_NOT_ASSIGNED during note implem", "RefUrl": "/notes/994673 "}, {"RefNumber": "978148", "RefComponent": "BW", "RefTitle": "BI in SAP NetWeaver 2004s: OSS notes after SPS9", "RefUrl": "/notes/978148 "}, {"RefNumber": "884377", "RefComponent": "BC-DWB-TOO-CLA", "RefTitle": "NoteAss: Class/interface if Modification Assistant on", "RefUrl": "/notes/884377 "}, {"RefNumber": "991520", "RefComponent": "BC-UPG-NA", "RefTitle": "Note Assistant: External PDF display in the note log", "RefUrl": "/notes/991520 "}, {"RefNumber": "976920", "RefComponent": "BC-UPG-NA", "RefTitle": "SPAU: reset to original doesn't change the status of note", "RefUrl": "/notes/976920 "}, {"RefNumber": "966900", "RefComponent": "BC-UPG-NA", "RefTitle": "Entries not sorted by time in Note log", "RefUrl": "/notes/966900 "}, {"RefNumber": "907071", "RefComponent": "BC-UPG-NA", "RefTitle": "Note Assistant: Partial implementation of changes in classes", "RefUrl": "/notes/907071 "}, {"RefNumber": "948389", "RefComponent": "BC-UPG-NA", "RefTitle": "Note Assistant: Incorrect note removal", "RefUrl": "/notes/948389 "}, {"RefNumber": "984173", "RefComponent": "BC-UPG-NA", "RefTitle": "Termination when SOTR texts are missing in other languages", "RefUrl": "/notes/984173 "}, {"RefNumber": "980822", "RefComponent": "BC-DWB-TOO-CLA", "RefTitle": "CLSD_GET_OBJECT function module returns no Friends", "RefUrl": "/notes/980822 "}, {"RefNumber": "969846", "RefComponent": "BC-DWB-TOO-CLA", "RefTitle": "NoteAss: Unable to change the inheritance hierarchy", "RefUrl": "/notes/969846 "}, {"RefNumber": "956148", "RefComponent": "BW-BEX-OT-DBIF", "RefTitle": "Consistency check - Queries on aggregates during compression", "RefUrl": "/notes/956148 "}, {"RefNumber": "970391", "RefComponent": "BC-UPG-NA", "RefTitle": "Note Assistant: Dump after you double-click a delta", "RefUrl": "/notes/970391 "}, {"RefNumber": "966875", "RefComponent": "BW-BEX-OT-VC", "RefTitle": "Error reading a virtual infoprovider", "RefUrl": "/notes/966875 "}, {"RefNumber": "969701", "RefComponent": "BC-UPG-NA", "RefTitle": "Note Assistant: <PERSON><PERSON><PERSON> in merge editor for IAMU object", "RefUrl": "/notes/969701 "}, {"RefNumber": "964580", "RefComponent": "BC-CTS-ORG", "RefTitle": "Incorrect version data in sections of classes", "RefUrl": "/notes/964580 "}, {"RefNumber": "922354", "RefComponent": "BC-UPG-NA", "RefTitle": "Note Assistant: Error during note implementation & new entry", "RefUrl": "/notes/922354 "}, {"RefNumber": "951571", "RefComponent": "BC-DWB-CEX", "RefTitle": "NA: BAdI interfaces are edited", "RefUrl": "/notes/951571 "}, {"RefNumber": "943410", "RefComponent": "BC-UPG-NA", "RefTitle": "SCWB/SNOTE: No processing with enhancement in the upgrade", "RefUrl": "/notes/943410 "}, {"RefNumber": "928595", "RefComponent": "BC-UPG-NA", "RefTitle": "Note Assistant: Error when making source code changes", "RefUrl": "/notes/928595 "}, {"RefNumber": "936388", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "Using more than one group -> incorrect data", "RefUrl": "/notes/936388 "}, {"RefNumber": "914200", "RefComponent": "BC-UPG-NA", "RefTitle": "Termination in SCWB/SNOTE if SPCF texts are missing", "RefUrl": "/notes/914200 "}, {"RefNumber": "938296", "RefComponent": "SRM-EBP-BID", "RefTitle": "JavaScript runtime error in the bid invitation", "RefUrl": "/notes/938296 "}, {"RefNumber": "915117", "RefComponent": "BC-UPG-NA", "RefTitle": "SCWB/SNOTE terminates when transporting ENHSPOTS (new BAdIs)", "RefUrl": "/notes/915117 "}, {"RefNumber": "875036", "RefComponent": "BC-UPG-NA", "RefTitle": "Note Assistant: Program terminates when you display a note", "RefUrl": "/notes/875036 "}, {"RefNumber": "918766", "RefComponent": "BC-UPG-NA", "RefTitle": "Note Assistant: Changing sections of classes", "RefUrl": "/notes/918766 "}, {"RefNumber": "921344", "RefComponent": "BC-DWB-TOO-CLA", "RefTitle": "Note cannot be implemented, due to complex type definitions", "RefUrl": "/notes/921344 "}, {"RefNumber": "877516", "RefComponent": "BC-UPG-NA", "RefTitle": "Note Assistant: Notes with MIME repository objects", "RefUrl": "/notes/877516 "}, {"RefNumber": "914759", "RefComponent": "BC-UPG-NA", "RefTitle": "Error when importing enhancement with SNOTE/SCWB", "RefUrl": "/notes/914759 "}, {"RefNumber": "907329", "RefComponent": "BC-DWB-TOO-SCR", "RefTitle": "Screen: Normalizing the element list", "RefUrl": "/notes/907329 "}, {"RefNumber": "853524", "RefComponent": "BC-UPG-NA", "RefTitle": "Note Assistant: Message scwn 409 during note installation", "RefUrl": "/notes/853524 "}, {"RefNumber": "882246", "RefComponent": "BC-UPG-NA", "RefTitle": "Note Assistant: Message TK594 for function modules", "RefUrl": "/notes/882246 "}, {"RefNumber": "887495", "RefComponent": "BC-UPG-NA", "RefTitle": "Note Assistant: Call contained error(s): ADS (2)", "RefUrl": "/notes/887495 "}, {"RefNumber": "875816", "RefComponent": "BC-UPG-NA", "RefTitle": "Note Assistant: Notes after upgrade on new functions", "RefUrl": "/notes/875816 "}, {"RefNumber": "890198", "RefComponent": "BC-DWB-TOO-ENH", "RefTitle": "Corrupted HOOK sources in enhancements after SCWB/SNODE", "RefUrl": "/notes/890198 "}, {"RefNumber": "891060", "RefComponent": "BC-DWB-TOO-ENH", "RefTitle": "Termination with Transaction SCWB/SNOTE with obj type ENHS", "RefUrl": "/notes/891060 "}, {"RefNumber": "887393", "RefComponent": "BC-DWB-TOO", "RefTitle": "Dump in SCWB when processing object type ENHS", "RefUrl": "/notes/887393 "}, {"RefNumber": "560756", "RefComponent": "BC-UPG-NA", "RefTitle": "Note Assistant Version 1.2: Important notes", "RefUrl": "/notes/560756 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_BASIS", "From": "620", "To": "640", "Subsequent": ""}, {"SoftwareComponent": "SAP_BASIS", "From": "700", "To": "702", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": [{"SoftwareComponent": "SAP_BASIS", "NumberOfCorrin": 4, "URL": "/corrins/0000875986/41"}]}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 4, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 200, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": [{"SoftwareComponent": "SAP_BASIS", "ValidFrom": "", "ValidTo": "", "Number": "1595331 ", "URL": "/notes/1595331 ", "Title": "WMM: Monitoring Pause does not work across mid-night", "Component": "SV-SMG-ADM-DTM"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "610", "ValidTo": "610", "Number": "967237 ", "URL": "/notes/967237 ", "Title": "No adjustment of type uses when note is implemented", "Component": "BC-DWB-TOO-CLA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "620", "ValidTo": "620", "Number": "884377 ", "URL": "/notes/884377 ", "Title": "NoteAss: Class/interface if Modification Assistant on", "Component": "BC-DWB-TOO-CLA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "620", "ValidTo": "620", "Number": "907071 ", "URL": "/notes/907071 ", "Title": "Note Assistant: Partial implementation of changes in classes", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "620", "ValidTo": "620", "Number": "964580 ", "URL": "/notes/964580 ", "Title": "Incorrect version data in sections of classes", "Component": "BC-CTS-ORG"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "620", "ValidTo": "620", "Number": "967237 ", "URL": "/notes/967237 ", "Title": "No adjustment of type uses when note is implemented", "Component": "BC-DWB-TOO-CLA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "620", "ValidTo": "620", "Number": "969846 ", "URL": "/notes/969846 ", "Title": "NoteAss: Unable to change the inheritance hierarchy", "Component": "BC-DWB-TOO-CLA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "620", "ValidTo": "620", "Number": "970837 ", "URL": "/notes/970837 ", "Title": "Creating methods with Note Assistant - mod infos created", "Component": "BC-DWB-TOO-CLA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "620", "ValidTo": "620", "Number": "976918 ", "URL": "/notes/976918 ", "Title": "Note Assistant: Error during note display with DYNP deltas", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "620", "ValidTo": "620", "Number": "976920 ", "URL": "/notes/976920 ", "Title": "SPAU: reset to original doesn't change the status of note", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "620", "ValidTo": "620", "Number": "977082 ", "URL": "/notes/977082 ", "Title": "SE24: <PERSON><PERSON><PERSON> in connection with the Note Assistant", "Component": "BC-DWB-TOO-CLA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "620", "ValidTo": "620", "Number": "978398 ", "URL": "/notes/978398 ", "Title": "Changing the class include structure using SNOTE/SCWB", "Component": "BC-DWB-TOO-CLA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "620", "ValidTo": "620", "Number": "980288 ", "URL": "/notes/980288 ", "Title": "Note Assistant:incorrect version data for WebDynpro and WAPP", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "620", "ValidTo": "620", "Number": "980822 ", "URL": "/notes/980822 ", "Title": "CLSD_GET_OBJECT function module returns no Friends", "Component": "BC-DWB-TOO-CLA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "620", "ValidTo": "620", "Number": "983212 ", "URL": "/notes/983212 ", "Title": "Forward declaration is not deleted from metadata", "Component": "BC-DWB-TOO-CLA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "620", "ValidTo": "620", "Number": "986226 ", "URL": "/notes/986226 ", "Title": "Termination CX_XSLT_RUNTIME_ERROR when downloading note", "Component": "BC-DWB-TOO-SCR"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "620", "ValidTo": "620", "Number": "1000448 ", "URL": "/notes/1000448 ", "Title": "Redefinition deleted when implementing a note", "Component": "BC-DWB-TOO-CLA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "620", "ValidTo": "620", "Number": "1008540 ", "URL": "/notes/1008540 ", "Title": "Note Assistant: Dump when writing history", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "620", "ValidTo": "620", "Number": "1059707 ", "URL": "/notes/1059707 ", "Title": "Note Assistant: Dump when exporting the history (WAPP,WDYC)", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "620", "ValidTo": "620", "Number": "1064567 ", "URL": "/notes/1064567 ", "Title": "SNOTE: Enhancement of FB CLM_CHECK_SMODILOG_ENTRY", "Component": "BC-DWB-CEX"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "620", "ValidTo": "620", "Number": "1065164 ", "URL": "/notes/1065164 ", "Title": "SE24: Corr<PERSON> Workbench and initial vals for interface attrib.", "Component": "BC-DWB-TOO-CLA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "620", "ValidTo": "620", "Number": "1073502 ", "URL": "/notes/1073502 ", "Title": "Note Assistant: Termination and log when downloading a note", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "620", "ValidTo": "620", "Number": "1077466 ", "URL": "/notes/1077466 ", "Title": "Note Assistant:\"Internal error\" when displaying note", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "620", "ValidTo": "620", "Number": "1092828 ", "URL": "/notes/1092828 ", "Title": "SE24: Versioning of modification info & Note Assistant", "Component": "BC-DWB-TOO-CLA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "620", "ValidTo": "640", "Number": "991520 ", "URL": "/notes/991520 ", "Title": "Note Assistant: External PDF display in the note log", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "620", "ValidTo": "640", "Number": "1037672 ", "URL": "/notes/1037672 ", "Title": "Note Assistant: Different languages in the note display", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "620", "ValidTo": "640", "Number": "1130404 ", "URL": "/notes/1130404 ", "Title": "Problem in FB RPY_EXISTENCE_CHECK_C*", "Component": "BC-DWB-TOO-CLA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "620", "ValidTo": "640", "Number": "1147830 ", "URL": "/notes/1147830 ", "Title": "Note Assistant: Changes to WAPP objs cannot be implemented", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "620", "ValidTo": "700", "Number": "907071 ", "URL": "/notes/907071 ", "Title": "Note Assistant: Partial implementation of changes in classes", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "620", "ValidTo": "700", "Number": "966900 ", "URL": "/notes/966900 ", "Title": "Entries not sorted by time in Note log", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "620", "ValidTo": "700", "Number": "968138 ", "URL": "/notes/968138 ", "Title": "Note Assistant: Restarting capability in the Note Assistant", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "620", "ValidTo": "700", "Number": "970391 ", "URL": "/notes/970391 ", "Title": "Note Assistant: Dump after you double-click a delta", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "620", "ValidTo": "700", "Number": "986226 ", "URL": "/notes/986226 ", "Title": "Termination CX_XSLT_RUNTIME_ERROR when downloading note", "Component": "BC-DWB-TOO-SCR"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "620", "ValidTo": "700", "Number": "987261 ", "URL": "/notes/987261 ", "Title": "Modification adjustment: Locks for objects", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "620", "ValidTo": "700", "Number": "989056 ", "URL": "/notes/989056 ", "Title": "Note implementation: Implementation-based instance relship", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "620", "ValidTo": "700", "Number": "992831 ", "URL": "/notes/992831 ", "Title": "Note Assistant: Incompl correction instructions in note log", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "620", "ValidTo": "700", "Number": "994673 ", "URL": "/notes/994673 ", "Title": "Note Assistant: Dump GETWA_NOT_ASSIGNED during note implem", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "620", "ValidTo": "700", "Number": "1004110 ", "URL": "/notes/1004110 ", "Title": "Note Assistant: Implementation status after note download", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "620", "ValidTo": "700", "Number": "1004293 ", "URL": "/notes/1004293 ", "Title": "Note Assistant: Delta display in note log", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "620", "ValidTo": "700", "Number": "1004691 ", "URL": "/notes/1004691 ", "Title": "SNOTE: OBJECTS_OBJREF_NOT_ASSIGNED when you implement a note", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "620", "ValidTo": "700", "Number": "1088433 ", "URL": "/notes/1088433 ", "Title": "Note Assistant: Termination in the Merge Editor", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "620", "ValidTo": "700", "Number": "1112141 ", "URL": "/notes/1112141 ", "Title": "Note Assistant: Implementing notes using modules EXIT_", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "620", "ValidTo": "700", "Number": "1113339 ", "URL": "/notes/1113339 ", "Title": "SE24 / Note Assistant and methods with exception classes", "Component": "BC-DWB-TOO-CLA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "620", "ValidTo": "700", "Number": "1114973 ", "URL": "/notes/1114973 ", "Title": "Note Assistant: Incorrect message when downloading note", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "620", "ValidTo": "702", "Number": "1349277 ", "URL": "/notes/1349277 ", "Title": "Note Assistant: Method cannot be implemented", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "620", "ValidTo": "702", "Number": "1530273 ", "URL": "/notes/1530273 ", "Title": "Note Assistant: Incorrect error message for INTF methods", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "620", "ValidTo": "702", "Number": "1541531 ", "URL": "/notes/1541531 ", "Title": "Pushbuttons lost after nested popups", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "620", "ValidTo": "710", "Number": "1031649 ", "URL": "/notes/1031649 ", "Title": "Note Assistant:Changes to WAPP cannot be implemented", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "620", "ValidTo": "710", "Number": "1094372 ", "URL": "/notes/1094372 ", "Title": "Note Assistant: SMODISRC entries for Repository objects", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "620", "ValidTo": "710", "Number": "1097666 ", "URL": "/notes/1097666 ", "Title": "Note Assistant: Changes during note implementation", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "620", "ValidTo": "710", "Number": "1106385 ", "URL": "/notes/1106385 ", "Title": "Unrequired removal of correction instructions", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "620", "ValidTo": "710", "Number": "1150982 ", "URL": "/notes/1150982 ", "Title": "Note Assistant: Deleting method def. when displaying a note", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "620", "ValidTo": "711", "Number": "1262826 ", "URL": "/notes/1262826 ", "Title": "SNOTE: Error when deimplementing notes with ENHO objects", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "620", "ValidTo": "711", "Number": "1316626 ", "URL": "/notes/1316626 ", "Title": "CWB: Many error messages when you grant special approvals", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "620", "ValidTo": "711", "Number": "1342598 ", "URL": "/notes/1342598 ", "Title": "SCWB: Unnecessary locks when maintaining special approvals", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "620", "ValidTo": "720", "Number": "1397709 ", "URL": "/notes/1397709 ", "Title": "Ignore Dynpro element fields AGLT and ADEZ in SNOTE/CWB", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "620", "ValidTo": "720", "Number": "1433214 ", "URL": "/notes/1433214 ", "Title": "SCWB_NOTE_ACTIVATE: misleading status message", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "620", "ValidTo": "720", "Number": "1437981 ", "URL": "/notes/1437981 ", "Title": "Note Assistant: Monitoring tool", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "620", "ValidTo": "730", "Number": "1500456 ", "URL": "/notes/1500456 ", "Title": "Note Assistant: Side effect notes that are not released", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "620", "ValidTo": "730", "Number": "1552560 ", "URL": "/notes/1552560 ", "Title": "SNOTE: Element sequence in queue for implem./deimplementing", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "620", "ValidTo": "731", "Number": "1539505 ", "URL": "/notes/1539505 ", "Title": "SNOTE: Minor corrections", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "620", "ValidTo": "753", "Number": "1557768 ", "URL": "/notes/1557768 ", "Title": "Note Assistant: find the notes that modify an object", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "640", "ValidTo": "640", "Number": "884377 ", "URL": "/notes/884377 ", "Title": "NoteAss: Class/interface if Modification Assistant on", "Component": "BC-DWB-TOO-CLA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "640", "ValidTo": "640", "Number": "964580 ", "URL": "/notes/964580 ", "Title": "Incorrect version data in sections of classes", "Component": "BC-CTS-ORG"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "640", "ValidTo": "640", "Number": "967237 ", "URL": "/notes/967237 ", "Title": "No adjustment of type uses when note is implemented", "Component": "BC-DWB-TOO-CLA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "640", "ValidTo": "640", "Number": "969846 ", "URL": "/notes/969846 ", "Title": "NoteAss: Unable to change the inheritance hierarchy", "Component": "BC-DWB-TOO-CLA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "640", "ValidTo": "640", "Number": "970837 ", "URL": "/notes/970837 ", "Title": "Creating methods with Note Assistant - mod infos created", "Component": "BC-DWB-TOO-CLA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "640", "ValidTo": "640", "Number": "975510 ", "URL": "/notes/975510 ", "Title": "Corrections for classes/interfaces implemented incorrectly", "Component": "BC-DWB-TOO-CLA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "640", "ValidTo": "640", "Number": "976918 ", "URL": "/notes/976918 ", "Title": "Note Assistant: Error during note display with DYNP deltas", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "640", "ValidTo": "640", "Number": "976920 ", "URL": "/notes/976920 ", "Title": "SPAU: reset to original doesn't change the status of note", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "640", "ValidTo": "640", "Number": "977082 ", "URL": "/notes/977082 ", "Title": "SE24: <PERSON><PERSON><PERSON> in connection with the Note Assistant", "Component": "BC-DWB-TOO-CLA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "640", "ValidTo": "640", "Number": "978398 ", "URL": "/notes/978398 ", "Title": "Changing the class include structure using SNOTE/SCWB", "Component": "BC-DWB-TOO-CLA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "640", "ValidTo": "640", "Number": "980288 ", "URL": "/notes/980288 ", "Title": "Note Assistant:incorrect version data for WebDynpro and WAPP", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "640", "ValidTo": "640", "Number": "980822 ", "URL": "/notes/980822 ", "Title": "CLSD_GET_OBJECT function module returns no Friends", "Component": "BC-DWB-TOO-CLA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "640", "ValidTo": "640", "Number": "983212 ", "URL": "/notes/983212 ", "Title": "Forward declaration is not deleted from metadata", "Component": "BC-DWB-TOO-CLA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "640", "ValidTo": "640", "Number": "986226 ", "URL": "/notes/986226 ", "Title": "Termination CX_XSLT_RUNTIME_ERROR when downloading note", "Component": "BC-DWB-TOO-SCR"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "640", "ValidTo": "640", "Number": "1000448 ", "URL": "/notes/1000448 ", "Title": "Redefinition deleted when implementing a note", "Component": "BC-DWB-TOO-CLA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "640", "ValidTo": "640", "Number": "1008540 ", "URL": "/notes/1008540 ", "Title": "Note Assistant: Dump when writing history", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "640", "ValidTo": "640", "Number": "1059707 ", "URL": "/notes/1059707 ", "Title": "Note Assistant: Dump when exporting the history (WAPP,WDYC)", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "640", "ValidTo": "640", "Number": "1064567 ", "URL": "/notes/1064567 ", "Title": "SNOTE: Enhancement of FB CLM_CHECK_SMODILOG_ENTRY", "Component": "BC-DWB-CEX"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "640", "ValidTo": "640", "Number": "1065164 ", "URL": "/notes/1065164 ", "Title": "SE24: Corr<PERSON> Workbench and initial vals for interface attrib.", "Component": "BC-DWB-TOO-CLA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "640", "ValidTo": "640", "Number": "1073502 ", "URL": "/notes/1073502 ", "Title": "Note Assistant: Termination and log when downloading a note", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "640", "ValidTo": "640", "Number": "1092828 ", "URL": "/notes/1092828 ", "Title": "SE24: Versioning of modification info & Note Assistant", "Component": "BC-DWB-TOO-CLA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "640", "ValidTo": "700", "Number": "907071 ", "URL": "/notes/907071 ", "Title": "Note Assistant: Partial implementation of changes in classes", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "640", "ValidTo": "700", "Number": "969701 ", "URL": "/notes/969701 ", "Title": "Note Assistant: <PERSON><PERSON><PERSON> in merge editor for IAMU object", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "640", "ValidTo": "700", "Number": "1077466 ", "URL": "/notes/1077466 ", "Title": "Note Assistant:\"Internal error\" when displaying note", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "640", "ValidTo": "730", "Number": "1539505 ", "URL": "/notes/1539505 ", "Title": "SNOTE: Minor corrections", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "700", "ValidTo": "700", "Number": "884377 ", "URL": "/notes/884377 ", "Title": "NoteAss: Class/interface if Modification Assistant on", "Component": "BC-DWB-TOO-CLA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "700", "ValidTo": "700", "Number": "964580 ", "URL": "/notes/964580 ", "Title": "Incorrect version data in sections of classes", "Component": "BC-CTS-ORG"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "700", "ValidTo": "700", "Number": "965756 ", "URL": "/notes/965756 ", "Title": "Upgrade indicator set for SCWB/SNOTE", "Component": "BC-DWB-TOO-ENH"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "700", "ValidTo": "700", "Number": "967237 ", "URL": "/notes/967237 ", "Title": "No adjustment of type uses when note is implemented", "Component": "BC-DWB-TOO-CLA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "700", "ValidTo": "700", "Number": "969846 ", "URL": "/notes/969846 ", "Title": "NoteAss: Unable to change the inheritance hierarchy", "Component": "BC-DWB-TOO-CLA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "700", "ValidTo": "700", "Number": "970837 ", "URL": "/notes/970837 ", "Title": "Creating methods with Note Assistant - mod infos created", "Component": "BC-DWB-TOO-CLA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "700", "ValidTo": "700", "Number": "975510 ", "URL": "/notes/975510 ", "Title": "Corrections for classes/interfaces implemented incorrectly", "Component": "BC-DWB-TOO-CLA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "700", "ValidTo": "700", "Number": "976918 ", "URL": "/notes/976918 ", "Title": "Note Assistant: Error during note display with DYNP deltas", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "700", "ValidTo": "700", "Number": "976920 ", "URL": "/notes/976920 ", "Title": "SPAU: reset to original doesn't change the status of note", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "700", "ValidTo": "700", "Number": "977082 ", "URL": "/notes/977082 ", "Title": "SE24: <PERSON><PERSON><PERSON> in connection with the Note Assistant", "Component": "BC-DWB-TOO-CLA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "700", "ValidTo": "700", "Number": "978398 ", "URL": "/notes/978398 ", "Title": "Changing the class include structure using SNOTE/SCWB", "Component": "BC-DWB-TOO-CLA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "700", "ValidTo": "700", "Number": "980288 ", "URL": "/notes/980288 ", "Title": "Note Assistant:incorrect version data for WebDynpro and WAPP", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "700", "ValidTo": "700", "Number": "983212 ", "URL": "/notes/983212 ", "Title": "Forward declaration is not deleted from metadata", "Component": "BC-DWB-TOO-CLA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "700", "ValidTo": "700", "Number": "983534 ", "URL": "/notes/983534 ", "Title": "Note Assistant: <PERSON>lank references enhancement implementation", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "700", "ValidTo": "700", "Number": "984173 ", "URL": "/notes/984173 ", "Title": "Termination when SOTR texts are missing in other languages", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "700", "ValidTo": "700", "Number": "984771 ", "URL": "/notes/984771 ", "Title": "ENHO-SNOTE : Missing SOTR texts produce red traffic light", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "700", "ValidTo": "700", "Number": "986226 ", "URL": "/notes/986226 ", "Title": "Termination CX_XSLT_RUNTIME_ERROR when downloading note", "Component": "BC-DWB-TOO-SCR"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "700", "ValidTo": "700", "Number": "991520 ", "URL": "/notes/991520 ", "Title": "Note Assistant: External PDF display in the note log", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "700", "ValidTo": "700", "Number": "1000332 ", "URL": "/notes/1000332 ", "Title": "Note Assistant: Problems with source code and WDYC", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "700", "ValidTo": "700", "Number": "1000448 ", "URL": "/notes/1000448 ", "Title": "Redefinition deleted when implementing a note", "Component": "BC-DWB-TOO-CLA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "700", "ValidTo": "700", "Number": "1004661 ", "URL": "/notes/1004661 ", "Title": "SNOTE for BAdI: Error when implementing screen enhancements", "Component": "BC-DWB-TOO-ENH"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "700", "ValidTo": "700", "Number": "1005849 ", "URL": "/notes/1005849 ", "Title": "Error when using SNOTE to implement BAdI definitions", "Component": "BC-DWB-TOO-ENH"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "700", "ValidTo": "700", "Number": "1007754 ", "URL": "/notes/1007754 ", "Title": "Note Assistant: Incorrect code for enhancement objects", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "700", "ValidTo": "700", "Number": "1008275 ", "URL": "/notes/1008275 ", "Title": "CWB: Dump when importing ENHS objects", "Component": "BC-DWB-TOO-ENH"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "700", "ValidTo": "700", "Number": "1008436 ", "URL": "/notes/1008436 ", "Title": "Duplicate parameters when importing a class enhancement", "Component": "BC-DWB-TOO-ENH"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "700", "ValidTo": "700", "Number": "1008540 ", "URL": "/notes/1008540 ", "Title": "Note Assistant: Dump when writing history", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "700", "ValidTo": "700", "Number": "1009932 ", "URL": "/notes/1009932 ", "Title": "Note Assistant: Inconsistent data in a note with WDYN object", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "700", "ValidTo": "700", "Number": "1010679 ", "URL": "/notes/1010679 ", "Title": "Dump during enhancement versioning", "Component": "BC-DWB-TOO-ENH"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "700", "ValidTo": "700", "Number": "1019735 ", "URL": "/notes/1019735 ", "Title": "Modification dialog is displayed when generating classes", "Component": "BC-DWB-TOO-CLA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "700", "ValidTo": "700", "Number": "1020093 ", "URL": "/notes/1020093 ", "Title": "SNOTE: Incorrect check on TADIR entry", "Component": "BC-DWB-TOO-ENH"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "700", "ValidTo": "700", "Number": "1020107 ", "URL": "/notes/1020107 ", "Title": "Note Assistant: Message type \" \" is unknown", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "700", "ValidTo": "700", "Number": "1027648 ", "URL": "/notes/1027648 ", "Title": "Inconsistency when reading a component interface definition", "Component": "BC-DWB-WD-ABA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "700", "ValidTo": "700", "Number": "1029666 ", "URL": "/notes/1029666 ", "Title": "SNOTE/BAdI Impl.:Wrong filter value after you implement note", "Component": "BC-DWB-TOO-ENH"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "700", "ValidTo": "700", "Number": "1031630 ", "URL": "/notes/1031630 ", "Title": "Note Assistant: \"Internal error\" with Web Dynpro objects", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "700", "ValidTo": "700", "Number": "1034004 ", "URL": "/notes/1034004 ", "Title": "Confusing dialog box when implementing note for enh. impl.", "Component": "BC-DWB-TOO-ENH"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "700", "ValidTo": "700", "Number": "1034360 ", "URL": "/notes/1034360 ", "Title": "ENHS object missing when implementing implicit ENHO objects", "Component": "BC-DWB-TOO-ENH"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "700", "ValidTo": "700", "Number": "1037672 ", "URL": "/notes/1037672 ", "Title": "Note Assistant: Different languages in the note display", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "700", "ValidTo": "700", "Number": "1050569 ", "URL": "/notes/1050569 ", "Title": "Duplicate filter vals w/ BAdIs when implementing using SNOTE", "Component": "BC-DWB-TOO-ENH"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "700", "ValidTo": "700", "Number": "1059707 ", "URL": "/notes/1059707 ", "Title": "Note Assistant: Dump when exporting the history (WAPP,WDYC)", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "700", "ValidTo": "700", "Number": "1064567 ", "URL": "/notes/1064567 ", "Title": "SNOTE: Enhancement of FB CLM_CHECK_SMODILOG_ENTRY", "Component": "BC-DWB-CEX"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "700", "ValidTo": "700", "Number": "1065164 ", "URL": "/notes/1065164 ", "Title": "SE24: Corr<PERSON> Workbench and initial vals for interface attrib.", "Component": "BC-DWB-TOO-CLA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "700", "ValidTo": "700", "Number": "1066203 ", "URL": "/notes/1066203 ", "Title": "Note Assistant: ITAB_DUPLICATE_KEY_IDX_OP when implementing", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "700", "ValidTo": "700", "Number": "1070182 ", "URL": "/notes/1070182 ", "Title": "Error during mass activation in the background", "Component": "BC-DWB-UTL-BRR"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "700", "ValidTo": "700", "Number": "1073502 ", "URL": "/notes/1073502 ", "Title": "Note Assistant: Termination and log when downloading a note", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "700", "ValidTo": "700", "Number": "1092828 ", "URL": "/notes/1092828 ", "Title": "SE24: Versioning of modification info & Note Assistant", "Component": "BC-DWB-TOO-CLA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "700", "ValidTo": "700", "Number": "1121795 ", "URL": "/notes/1121795 ", "Title": "Text characteristics deleted after Web Dynpro note implement", "Component": "BC-DWB-WD-ABA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "700", "ValidTo": "700", "Number": "1126192 ", "URL": "/notes/1126192 ", "Title": "Note Assistant: Error when checking dependent objects", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "700", "ValidTo": "700", "Number": "1130404 ", "URL": "/notes/1130404 ", "Title": "Problem in FB RPY_EXISTENCE_CHECK_C*", "Component": "BC-DWB-TOO-CLA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "700", "ValidTo": "700", "Number": "1151059 ", "URL": "/notes/1151059 ", "Title": "Endless loop due to missing component usage", "Component": "BC-DWB-WD-ABA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "700", "ValidTo": "700", "Number": "1720495 ", "URL": "/notes/1720495 ", "Title": "Invalid deimplementation of obsolete notes by Snote tool", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "700", "ValidTo": "700", "Number": "1953150 ", "URL": "/notes/1953150 ", "Title": "Display for the non source code changes", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "700", "ValidTo": "700", "Number": "2116888 ", "URL": "/notes/2116888 ", "Title": "Table Short Description not copied", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "700", "ValidTo": "700", "Number": "2121268 ", "URL": "/notes/2121268 ", "Title": "TYPE-POOLS not generated if defined as IMPLICIT", "Component": "BC-DWB-TOO-CLA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "700", "ValidTo": "700", "Number": "2190181 ", "URL": "/notes/2190181 ", "Title": "Double click on the row in Note Log gives dump", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "700", "ValidTo": "700", "Number": "2248091 ", "URL": "/notes/2248091 ", "Title": "Change to reimplementation handling", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "700", "ValidTo": "700", "Number": "2254211 ", "URL": "/notes/2254211 ", "Title": "Fix for new reimplementation handling in SPAU", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "700", "ValidTo": "700", "Number": "2264123 ", "URL": "/notes/2264123 ", "Title": "Stopping deimplementation of obsolete notes", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "700", "ValidTo": "700", "Number": "2292923 ", "URL": "/notes/2292923 ", "Title": "Removing dependencies from the deleted CIs of a note", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "700", "ValidTo": "700", "Number": "2314876 ", "URL": "/notes/2314876 ", "Title": "Fix for a dump in reimplementation handling", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "700", "ValidTo": "700", "Number": "2328318 ", "URL": "/notes/2328318 ", "Title": "SNOTE: Avoid Obsolete Note Deimplementation", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "700", "ValidTo": "700", "Number": "2411418 ", "URL": "/notes/2411418 ", "Title": "Identifying TCI in old release where SAP Note 1995550 is not implemented", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "700", "ValidTo": "700", "Number": "2459558 ", "URL": "/notes/2459558 ", "Title": "Supported object type check in snote", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "700", "ValidTo": "700", "Number": "2536585 ", "URL": "/notes/2536585 ", "Title": "Upgrade: Missing SAP Notes in SPDD and SPAU", "Component": "BC-DWB-CEX"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "700", "ValidTo": "700", "Number": "2589309 ", "URL": "/notes/2589309 ", "Title": "Fixes to reimplementation handling - Ignore TADIR for new objects and invalid pre-requisite Notes", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "700", "ValidTo": "700", "Number": "2606986 ", "URL": "/notes/2606986 ", "Title": "Upgrade: You cannot reset obsolete SAP Notes in transaction SPAU", "Component": "BC-DWB-CEX"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "700", "ValidTo": "700", "Number": "2623459 ", "URL": "/notes/2623459 ", "Title": "Unable to read Correction instruction (Unknown Format)", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "700", "ValidTo": "700", "Number": "2624337 ", "URL": "/notes/2624337 ", "Title": "SNOTE - Note re implementation issue due to object support in newer SP's", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "700", "ValidTo": "700", "Number": "2671774 ", "URL": "/notes/2671774 ", "Title": "Error during Note implementation: Unable to find delivery event <delivery event number>", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "700", "ValidTo": "700", "Number": "2697766 ", "URL": "/notes/2697766 ", "Title": "SNOTE: Runtime Error CONVT_DATA_LOSS occurs while downloading/uploading SAP Notes", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "700", "ValidTo": "700", "Number": "2715783 ", "URL": "/notes/2715783 ", "Title": "Note Text is Empty in SPAU", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "700", "ValidTo": "700", "Number": "2757237 ", "URL": "/notes/2757237 ", "Title": "Reimplementation of note in SPDD and SPAU with no object change in CI", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "700", "ValidTo": "700", "Number": "2764725 ", "URL": "/notes/2764725 ", "Title": "Fix to reimplementation handling - avoid successor de-implementation during admin change", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "700", "ValidTo": "700", "Number": "2775698 ", "URL": "/notes/2775698 ", "Title": "Correction instruction creation ignores the local class definition, implementation, definition deffered, definition load , definition local friends changes", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "700", "ValidTo": "700", "Number": "2844646 ", "URL": "/notes/2844646 ", "Title": "Ignore the local class definition, implementation, definition deffered, definition load , definition local friends changes", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "700", "ValidTo": "700", "Number": "2860125 ", "URL": "/notes/2860125 ", "Title": "SNOTE - Handling unsaved objects to avoid dump", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "700", "ValidTo": "700", "Number": "2910608 ", "URL": "/notes/2910608 ", "Title": "Issue with applying changes to 'TYPES', 'DATA' or 'CONSTANTS' within ABAP Class definition", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "700", "ValidTo": "700", "Number": "2930611 ", "URL": "/notes/2930611 ", "Title": "SCWN_NOTE_STORE Fix for Efficient Note download processing time", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "700", "ValidTo": "700", "Number": "2991524 ", "URL": "/notes/2991524 ", "Title": "Supported Objects  FUGR TABD TYPD Properties and Validities Corrected", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "700", "ValidTo": "700", "Number": "3007345 ", "URL": "/notes/3007345 ", "Title": "Complex Attributes and Types are not picked during the Note implementation in SNOTE", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "700", "ValidTo": "700", "Number": "3008844 ", "URL": "/notes/3008844 ", "Title": "SNOTE: Exception handling during de-implementation while reading the history content", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "700", "ValidTo": "700", "Number": "3079593 ", "URL": "/notes/3079593 ", "Title": "Note download gives 'Error in serialization' in Non-Unicode system", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "700", "ValidTo": "700", "Number": "3085447 ", "URL": "/notes/3085447 ", "Title": "Class Deletion Issue While De-implementing the SAP Note", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "700", "ValidTo": "700", "Number": "3236648 ", "URL": "/notes/3236648 ", "Title": "Note Assistant (SNOTE): Move cast exceptions while displaying without delta objects in SNOTE/SNOTE_OLD and CI display", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "700", "ValidTo": "700", "Number": "3262964 ", "URL": "/notes/3262964 ", "Title": "SAP Note download using Download Service: Fix for not properly waiting for download completion", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "700", "ValidTo": "700", "Number": "3379455 ", "URL": "/notes/3379455 ", "Title": "SNOTE: Fix to show the correct pre-requisite SAP Note in the message \" SAP Note 0000000000 incomplete\" during SAP Note implementation", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "700", "ValidTo": "701", "Number": "1113339 ", "URL": "/notes/1113339 ", "Title": "SE24 / Note Assistant and methods with exception classes", "Component": "BC-DWB-TOO-CLA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "700", "ValidTo": "701", "Number": "2121268 ", "URL": "/notes/2121268 ", "Title": "TYPE-POOLS not generated if defined as IMPLICIT", "Component": "BC-DWB-TOO-CLA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "700", "ValidTo": "702", "Number": "1379746 ", "URL": "/notes/1379746 ", "Title": "Implement note: Modification Assistant dialog box displayed", "Component": "BC-DWB-TOO-CLA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "700", "ValidTo": "702", "Number": "1415680 ", "URL": "/notes/1415680 ", "Title": "Note Assistant: Incorrect status in subsequent systems", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "700", "ValidTo": "702", "Number": "1525115 ", "URL": "/notes/1525115 ", "Title": "Problem when retrieving view version", "Component": "BC-DWB-WD-ABA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "700", "ValidTo": "702", "Number": "1537354 ", "URL": "/notes/1537354 ", "Title": "Support for downloading huge notes over slow connections", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "700", "ValidTo": "702", "Number": "3041970 ", "URL": "/notes/3041970 ", "Title": "DEVC object  is not being captured as New object in the CI", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "700", "ValidTo": "710", "Number": "1041386 ", "URL": "/notes/1041386 ", "Title": "Release info is incomplete after notes are transported", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "700", "ValidTo": "710", "Number": "1142128 ", "URL": "/notes/1142128 ", "Title": "Note Assistant: Obsolete notes are incorrectly deimplemented", "Component": "BC-UPG"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "700", "ValidTo": "710", "Number": "1147830 ", "URL": "/notes/1147830 ", "Title": "Note Assistant: Changes to WAPP objs cannot be implemented", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "700", "ValidTo": "710", "Number": "1151243 ", "URL": "/notes/1151243 ", "Title": "Note Assistant: <PERSON><PERSON><PERSON> MOVE_CAST_ERROR when implementing", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "700", "ValidTo": "710", "Number": "1504500 ", "URL": "/notes/1504500 ", "Title": "Note Assistant: Navigation from Transport Organizer", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "700", "ValidTo": "711", "Number": "1231230 ", "URL": "/notes/1231230 ", "Title": "Note Assistant: Runtime error MOVE_TO_LIT_NOTALLOWED_NODATA", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "700", "ValidTo": "711", "Number": "1246964 ", "URL": "/notes/1246964 ", "Title": "Note Assistant: Master language of notes incorrect", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "700", "ValidTo": "711", "Number": "1246982 ", "URL": "/notes/1246982 ", "Title": "SNOTE: Dump when implementing ENHO/ENHS objects", "Component": "BC-DWB-TOO-ENH"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "700", "ValidTo": "711", "Number": "1286126 ", "URL": "/notes/1286126 ", "Title": "Syntax error in CL_CWB_OBJECT_...", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "700", "ValidTo": "711", "Number": "1287384 ", "URL": "/notes/1287384 ", "Title": "SNOTE: Source of component cannot be changed", "Component": "BC-DWB-TOO-CLA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "700", "ValidTo": "711", "Number": "1365677 ", "URL": "/notes/1365677 ", "Title": "Note Assistant: Runtime error MOVE_CAST_ERROR during implmtn", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "700", "ValidTo": "720", "Number": "1469642 ", "URL": "/notes/1469642 ", "Title": "SPAU: Dump DATA_OFFSET_NEGATIVE when double-clicking note", "Component": "BC-DWB-CEX"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "700", "ValidTo": "720", "Number": "1478610 ", "URL": "/notes/1478610 ", "Title": "SPAU: Note is displayed several times in SPAU", "Component": "BC-DWB-CEX"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "700", "ValidTo": "730", "Number": "1539505 ", "URL": "/notes/1539505 ", "Title": "SNOTE: Minor corrections", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "700", "ValidTo": "731", "Number": "3047860 ", "URL": "/notes/3047860 ", "Title": "SNOTE: Error in handling of transactions", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "700", "ValidTo": "740", "Number": "2025616 ", "URL": "/notes/2025616 ", "Title": "Performance improvement for note download", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "700", "ValidTo": "740", "Number": "2059257 ", "URL": "/notes/2059257 ", "Title": "Side effect note display in PDF", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "700", "ValidTo": "740", "Number": "2115211 ", "URL": "/notes/2115211 ", "Title": "Error downloading a not released note", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "700", "ValidTo": "750", "Number": "2192729 ", "URL": "/notes/2192729 ", "Title": "Version history screen - check display of retrieve button", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "700", "ValidTo": "750", "Number": "2289302 ", "URL": "/notes/2289302 ", "Title": "SNOTE: Object list pop-up navigates to a different object during note implementation/de-implementation", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "700", "ValidTo": "754", "Number": "3006946 ", "URL": "/notes/3006946 ", "Title": "SNOTE: Error SCWN 409 is displayed during Note Implementation when it contains MESS/MSAD object", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "700", "ValidTo": "757", "Number": "3307778 ", "URL": "/notes/3307778 ", "Title": "Remove SNOTE support for SHI3 Object Type if its incorrectly enabled", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "701", "ValidTo": "701", "Number": "1262653 ", "URL": "/notes/1262653 ", "Title": "SPAU: New object is deleted after note is reset", "Component": "BC-DWB-CEX"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "701", "ValidTo": "701", "Number": "1276929 ", "URL": "/notes/1276929 ", "Title": "SNOTE: Error message \"Object cannot be created again...\"", "Component": "BC-DWB-TOO-ENH"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "701", "ValidTo": "701", "Number": "1291055 ", "URL": "/notes/1291055 ", "Title": "Note Assistant: Problem activating exception classes", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "701", "ValidTo": "701", "Number": "1412719 ", "URL": "/notes/1412719 ", "Title": "SNOTE: error when implementing enhancement implementations", "Component": "BC-DWB-TOO-ENH"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "701", "ValidTo": "701", "Number": "1425487 ", "URL": "/notes/1425487 ", "Title": "SE24: Error regarding READ-ONLY for complex attributes", "Component": "BC-DWB-TOO-CLA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "701", "ValidTo": "701", "Number": "1720495 ", "URL": "/notes/1720495 ", "Title": "Invalid deimplementation of obsolete notes by Snote tool", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "701", "ValidTo": "701", "Number": "1930917 ", "URL": "/notes/1930917 ", "Title": "SPAU: reset obsolete note", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "701", "ValidTo": "701", "Number": "1953150 ", "URL": "/notes/1953150 ", "Title": "Display for the non source code changes", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "701", "ValidTo": "701", "Number": "2116888 ", "URL": "/notes/2116888 ", "Title": "Table Short Description not copied", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "701", "ValidTo": "701", "Number": "2121268 ", "URL": "/notes/2121268 ", "Title": "TYPE-POOLS not generated if defined as IMPLICIT", "Component": "BC-DWB-TOO-CLA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "701", "ValidTo": "701", "Number": "2248091 ", "URL": "/notes/2248091 ", "Title": "Change to reimplementation handling", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "701", "ValidTo": "701", "Number": "2254211 ", "URL": "/notes/2254211 ", "Title": "Fix for new reimplementation handling in SPAU", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "701", "ValidTo": "701", "Number": "2292923 ", "URL": "/notes/2292923 ", "Title": "Removing dependencies from the deleted CIs of a note", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "701", "ValidTo": "701", "Number": "2314876 ", "URL": "/notes/2314876 ", "Title": "Fix for a dump in reimplementation handling", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "701", "ValidTo": "701", "Number": "2328318 ", "URL": "/notes/2328318 ", "Title": "SNOTE: Avoid Obsolete Note Deimplementation", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "701", "ValidTo": "701", "Number": "2411418 ", "URL": "/notes/2411418 ", "Title": "Identifying TCI in old release where SAP Note 1995550 is not implemented", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "701", "ValidTo": "701", "Number": "2459558 ", "URL": "/notes/2459558 ", "Title": "Supported object type check in snote", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "701", "ValidTo": "701", "Number": "2536585 ", "URL": "/notes/2536585 ", "Title": "Upgrade: Missing SAP Notes in SPDD and SPAU", "Component": "BC-DWB-CEX"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "701", "ValidTo": "701", "Number": "2589309 ", "URL": "/notes/2589309 ", "Title": "Fixes to reimplementation handling - Ignore TADIR for new objects and invalid pre-requisite Notes", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "701", "ValidTo": "701", "Number": "2606986 ", "URL": "/notes/2606986 ", "Title": "Upgrade: You cannot reset obsolete SAP Notes in transaction SPAU", "Component": "BC-DWB-CEX"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "701", "ValidTo": "701", "Number": "2623459 ", "URL": "/notes/2623459 ", "Title": "Unable to read Correction instruction (Unknown Format)", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "701", "ValidTo": "701", "Number": "2624337 ", "URL": "/notes/2624337 ", "Title": "SNOTE - Note re implementation issue due to object support in newer SP's", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "701", "ValidTo": "701", "Number": "2671774 ", "URL": "/notes/2671774 ", "Title": "Error during Note implementation: Unable to find delivery event <delivery event number>", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "701", "ValidTo": "701", "Number": "2697766 ", "URL": "/notes/2697766 ", "Title": "SNOTE: Runtime Error CONVT_DATA_LOSS occurs while downloading/uploading SAP Notes", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "701", "ValidTo": "701", "Number": "2714624 ", "URL": "/notes/2714624 ", "Title": "Version Comparison False Result", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "701", "ValidTo": "701", "Number": "2715783 ", "URL": "/notes/2715783 ", "Title": "Note Text is Empty in SPAU", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "701", "ValidTo": "701", "Number": "2757237 ", "URL": "/notes/2757237 ", "Title": "Reimplementation of note in SPDD and SPAU with no object change in CI", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "701", "ValidTo": "701", "Number": "2764725 ", "URL": "/notes/2764725 ", "Title": "Fix to reimplementation handling - avoid successor de-implementation during admin change", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "701", "ValidTo": "701", "Number": "2775698 ", "URL": "/notes/2775698 ", "Title": "Correction instruction creation ignores the local class definition, implementation, definition deffered, definition load , definition local friends changes", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "701", "ValidTo": "701", "Number": "2844646 ", "URL": "/notes/2844646 ", "Title": "Ignore the local class definition, implementation, definition deffered, definition load , definition local friends changes", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "701", "ValidTo": "701", "Number": "2860125 ", "URL": "/notes/2860125 ", "Title": "SNOTE - Handling unsaved objects to avoid dump", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "701", "ValidTo": "701", "Number": "2910608 ", "URL": "/notes/2910608 ", "Title": "Issue with applying changes to 'TYPES', 'DATA' or 'CONSTANTS' within ABAP Class definition", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "701", "ValidTo": "701", "Number": "2930611 ", "URL": "/notes/2930611 ", "Title": "SCWN_NOTE_STORE Fix for Efficient Note download processing time", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "701", "ValidTo": "701", "Number": "2991524 ", "URL": "/notes/2991524 ", "Title": "Supported Objects  FUGR TABD TYPD Properties and Validities Corrected", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "701", "ValidTo": "701", "Number": "3008844 ", "URL": "/notes/3008844 ", "Title": "SNOTE: Exception handling during de-implementation while reading the history content", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "701", "ValidTo": "701", "Number": "3079593 ", "URL": "/notes/3079593 ", "Title": "Note download gives 'Error in serialization' in Non-Unicode system", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "701", "ValidTo": "701", "Number": "3085447 ", "URL": "/notes/3085447 ", "Title": "Class Deletion Issue While De-implementing the SAP Note", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "701", "ValidTo": "701", "Number": "3236648 ", "URL": "/notes/3236648 ", "Title": "Note Assistant (SNOTE): Move cast exceptions while displaying without delta objects in SNOTE/SNOTE_OLD and CI display", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "701", "ValidTo": "701", "Number": "3262964 ", "URL": "/notes/3262964 ", "Title": "SAP Note download using Download Service: Fix for not properly waiting for download completion", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "701", "ValidTo": "701", "Number": "3379455 ", "URL": "/notes/3379455 ", "Title": "SNOTE: Fix to show the correct pre-requisite SAP Note in the message \" SAP Note 0000000000 incomplete\" during SAP Note implementation", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "701", "ValidTo": "701", "Number": "3379660 ", "URL": "/notes/3379660 ", "Title": "DOCU: Stop Version Creation for LANG DOCU Object.", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "701", "ValidTo": "740", "Number": "2042123 ", "URL": "/notes/2042123 ", "Title": "Activation of objects in workbench locked by user", "Component": "BC-DWB-TOO"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "702", "ValidTo": "702", "Number": "1260089 ", "URL": "/notes/1260089 ", "Title": "SNOTE: Implementing notes that have ENHO and ENHS objects", "Component": "BC-DWB-TOO-ENH"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "702", "ValidTo": "702", "Number": "1334440 ", "URL": "/notes/1334440 ", "Title": "SE24: Error if creating with Note Assistant or SCWB", "Component": "BC-DWB-TOO-CLA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "702", "ValidTo": "702", "Number": "1350005 ", "URL": "/notes/1350005 ", "Title": "SCWB dump: Access via 'NULL' object reference not possible", "Component": "BC-DWB-TOO-SFW"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "702", "ValidTo": "702", "Number": "1409699 ", "URL": "/notes/1409699 ", "Title": "Error SWDP_WB_TOOL 437 or 442 during WD note implementation", "Component": "BC-DWB-WD-ABA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "702", "ValidTo": "702", "Number": "1425487 ", "URL": "/notes/1425487 ", "Title": "SE24: Error regarding READ-ONLY for complex attributes", "Component": "BC-DWB-TOO-CLA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "702", "ValidTo": "702", "Number": "1428701 ", "URL": "/notes/1428701 ", "Title": "Note Ass.: Red traffic light in merge editor for view def.", "Component": "BC-DWB-WD-ABA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "702", "ValidTo": "702", "Number": "1473623 ", "URL": "/notes/1473623 ", "Title": "Redundant PDF downloads in the Note Assistant", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "702", "ValidTo": "702", "Number": "1485083 ", "URL": "/notes/1485083 ", "Title": "SE37: Dump when implementing notes or in transaction SCWB", "Component": "BC-DWB-TOO-FUB"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "702", "ValidTo": "702", "Number": "1487661 ", "URL": "/notes/1487661 ", "Title": "Authorization check when deleting from version management", "Component": "BC-CTS-ORG"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "702", "ValidTo": "702", "Number": "1497520 ", "URL": "/notes/1497520 ", "Title": "Improvement of error messages in Note Assistant", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "702", "ValidTo": "702", "Number": "1497858 ", "URL": "/notes/1497858 ", "Title": "Incorrect original system after implementing note", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "702", "ValidTo": "702", "Number": "1506104 ", "URL": "/notes/1506104 ", "Title": "Error message when note implementation canceled", "Component": "BC-DWB-TOO-ENH"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "702", "ValidTo": "702", "Number": "1518861 ", "URL": "/notes/1518861 ", "Title": "Wrong handling of deleted objects in SNOTE", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "702", "ValidTo": "702", "Number": "1523687 ", "URL": "/notes/1523687 ", "Title": "SNOTE: Notes - Interface enhancement", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "702", "ValidTo": "702", "Number": "1532264 ", "URL": "/notes/1532264 ", "Title": "Further improved PDF downloads in Note Assistant", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "702", "ValidTo": "702", "Number": "1534245 ", "URL": "/notes/1534245 ", "Title": "Notes can't be deimplemented: missing content table", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "702", "ValidTo": "702", "Number": "1535724 ", "URL": "/notes/1535724 ", "Title": "Activation problems with Enhancement Objects", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "702", "ValidTo": "702", "Number": "1549103 ", "URL": "/notes/1549103 ", "Title": "SNOTE: Problems with language-dependent notes", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "702", "ValidTo": "702", "Number": "1552759 ", "URL": "/notes/1552759 ", "Title": "Note Assistant: TLOGO and generated objects", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "702", "ValidTo": "702", "Number": "1566290 ", "URL": "/notes/1566290 ", "Title": "Creation of interfaces using Note Assistant fails", "Component": "BC-DWB-TOO-CLA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "702", "ValidTo": "702", "Number": "1571213 ", "URL": "/notes/1571213 ", "Title": "SE24: Note Assistant does not adjust component type", "Component": "BC-DWB-TOO-CLA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "702", "ValidTo": "702", "Number": "1619713 ", "URL": "/notes/1619713 ", "Title": "Transactions SCWB or SNOTE delete inline objects", "Component": "BC-DWB-PRX"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "702", "ValidTo": "702", "Number": "1621321 ", "URL": "/notes/1621321 ", "Title": "SNOTE/SCWB for screens: Field attribute minimum line/row", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "702", "ValidTo": "702", "Number": "1624603 ", "URL": "/notes/1624603 ", "Title": "SCWB: Transferring corrections via TLOG object types", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "702", "ValidTo": "702", "Number": "1627683 ", "URL": "/notes/1627683 ", "Title": "SCWB/SNOTE/SPAU: Changed development package", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "702", "ValidTo": "702", "Number": "1639074 ", "URL": "/notes/1639074 ", "Title": "SNOTE: Note data is not current or is incorrect", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "702", "ValidTo": "702", "Number": "1685578 ", "URL": "/notes/1685578 ", "Title": "Problem applying a note through Snote", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "702", "ValidTo": "702", "Number": "1690165 ", "URL": "/notes/1690165 ", "Title": "snote/scwb for proxies to external WSDLs", "Component": "BC-DWB-PRX"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "702", "ValidTo": "702", "Number": "1720495 ", "URL": "/notes/1720495 ", "Title": "Invalid deimplementation of obsolete notes by Snote tool", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "702", "ValidTo": "702", "Number": "1930917 ", "URL": "/notes/1930917 ", "Title": "SPAU: reset obsolete note", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "702", "ValidTo": "702", "Number": "1953150 ", "URL": "/notes/1953150 ", "Title": "Display for the non source code changes", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "702", "ValidTo": "702", "Number": "2116888 ", "URL": "/notes/2116888 ", "Title": "Table Short Description not copied", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "702", "ValidTo": "702", "Number": "2159134 ", "URL": "/notes/2159134 ", "Title": "Objects repeated in pop-up during note implementation", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "702", "ValidTo": "702", "Number": "2212925 ", "URL": "/notes/2212925 ", "Title": "Fix for automatic handling of DDIC correction instructions", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "702", "ValidTo": "702", "Number": "2248091 ", "URL": "/notes/2248091 ", "Title": "Change to reimplementation handling", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "702", "ValidTo": "702", "Number": "2254211 ", "URL": "/notes/2254211 ", "Title": "Fix for new reimplementation handling in SPAU", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "702", "ValidTo": "702", "Number": "2264123 ", "URL": "/notes/2264123 ", "Title": "Stopping deimplementation of obsolete notes", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "702", "ValidTo": "702", "Number": "2292923 ", "URL": "/notes/2292923 ", "Title": "Removing dependencies from the deleted CIs of a note", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "702", "ValidTo": "702", "Number": "2314876 ", "URL": "/notes/2314876 ", "Title": "Fix for a dump in reimplementation handling", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "702", "ValidTo": "702", "Number": "2328318 ", "URL": "/notes/2328318 ", "Title": "SNOTE: Avoid Obsolete Note Deimplementation", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "702", "ValidTo": "702", "Number": "2398161 ", "URL": "/notes/2398161 ", "Title": "System Dumps when opening a note in PDF", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "702", "ValidTo": "702", "Number": "2411418 ", "URL": "/notes/2411418 ", "Title": "Identifying TCI in old release where SAP Note 1995550 is not implemented", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "702", "ValidTo": "702", "Number": "2459558 ", "URL": "/notes/2459558 ", "Title": "Supported object type check in snote", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "702", "ValidTo": "702", "Number": "2536585 ", "URL": "/notes/2536585 ", "Title": "Upgrade: Missing SAP Notes in SPDD and SPAU", "Component": "BC-DWB-CEX"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "702", "ValidTo": "702", "Number": "2541236 ", "URL": "/notes/2541236 ", "Title": "DDIC support in SNOTE - handling of note in SPDD", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "702", "ValidTo": "702", "Number": "2568276 ", "URL": "/notes/2568276 ", "Title": "Remote/update function modules in SNOTE correction instructions", "Component": "BC-DWB-TOO-FUB"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "702", "ValidTo": "702", "Number": "2589309 ", "URL": "/notes/2589309 ", "Title": "Fixes to reimplementation handling - Ignore TADIR for new objects and invalid pre-requisite Notes", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "702", "ValidTo": "702", "Number": "2597808 ", "URL": "/notes/2597808 ", "Title": "Table Type (TTYD): Delta change to primary key not calculated and applied correctly", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "702", "ValidTo": "702", "Number": "2598809 ", "URL": "/notes/2598809 ", "Title": "Table definition (TABD): .APPEND are included for delta calculation and implementation", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "702", "ValidTo": "702", "Number": "2606986 ", "URL": "/notes/2606986 ", "Title": "Upgrade: You cannot reset obsolete SAP Notes in transaction SPAU", "Component": "BC-DWB-CEX"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "702", "ValidTo": "702", "Number": "2617883 ", "URL": "/notes/2617883 ", "Title": "TLOG object read during SPDD phase", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "702", "ValidTo": "702", "Number": "2623459 ", "URL": "/notes/2623459 ", "Title": "Unable to read Correction instruction (Unknown Format)", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "702", "ValidTo": "702", "Number": "2624337 ", "URL": "/notes/2624337 ", "Title": "SNOTE - Note re implementation issue due to object support in newer SP's", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "702", "ValidTo": "702", "Number": "2671774 ", "URL": "/notes/2671774 ", "Title": "Error during Note implementation: Unable to find delivery event <delivery event number>", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "702", "ValidTo": "702", "Number": "2691847 ", "URL": "/notes/2691847 ", "Title": "Previously inactive object activated when current implementation of a note for the same object is cancelled", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "702", "ValidTo": "702", "Number": "2697766 ", "URL": "/notes/2697766 ", "Title": "SNOTE: Runtime Error CONVT_DATA_LOSS occurs while downloading/uploading SAP Notes", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "702", "ValidTo": "702", "Number": "2714624 ", "URL": "/notes/2714624 ", "Title": "Version Comparison False Result", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "702", "ValidTo": "702", "Number": "2715783 ", "URL": "/notes/2715783 ", "Title": "Note Text is Empty in SPAU", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "702", "ValidTo": "702", "Number": "2739641 ", "URL": "/notes/2739641 ", "Title": "ABAP Short Dump in SPDD phase during DDIC Note adjustment", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "702", "ValidTo": "702", "Number": "2757237 ", "URL": "/notes/2757237 ", "Title": "Reimplementation of note in SPDD and SPAU with no object change in CI", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "702", "ValidTo": "702", "Number": "2764725 ", "URL": "/notes/2764725 ", "Title": "Fix to reimplementation handling - avoid successor de-implementation during admin change", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "702", "ValidTo": "702", "Number": "2765308 ", "URL": "/notes/2765308 ", "Title": "SNOTE: Support for re-implementation of DDIC Correction Instructions", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "702", "ValidTo": "702", "Number": "2770960 ", "URL": "/notes/2770960 ", "Title": "SNOTE: Handling DDIC inactive objects", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "702", "ValidTo": "702", "Number": "2775698 ", "URL": "/notes/2775698 ", "Title": "Correction instruction creation ignores the local class definition, implementation, definition deffered, definition load , definition local friends changes", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "702", "ValidTo": "702", "Number": "2844646 ", "URL": "/notes/2844646 ", "Title": "Ignore the local class definition, implementation, definition deffered, definition load , definition local friends changes", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "702", "ValidTo": "702", "Number": "2860125 ", "URL": "/notes/2860125 ", "Title": "SNOTE - Handling unsaved objects to avoid dump", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "702", "ValidTo": "702", "Number": "2910608 ", "URL": "/notes/2910608 ", "Title": "Issue with applying changes to 'TYPES', 'DATA' or 'CONSTANTS' within ABAP Class definition", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "702", "ValidTo": "702", "Number": "2930611 ", "URL": "/notes/2930611 ", "Title": "SCWN_NOTE_STORE Fix for Efficient Note download processing time", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "702", "ValidTo": "702", "Number": "3008844 ", "URL": "/notes/3008844 ", "Title": "SNOTE: Exception handling during de-implementation while reading the history content", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "702", "ValidTo": "702", "Number": "3079593 ", "URL": "/notes/3079593 ", "Title": "Note download gives 'Error in serialization' in Non-Unicode system", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "702", "ValidTo": "702", "Number": "3085447 ", "URL": "/notes/3085447 ", "Title": "Class Deletion Issue While De-implementing the SAP Note", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "702", "ValidTo": "702", "Number": "3236648 ", "URL": "/notes/3236648 ", "Title": "Note Assistant (SNOTE): Move cast exceptions while displaying without delta objects in SNOTE/SNOTE_OLD and CI display", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "702", "ValidTo": "702", "Number": "3257359 ", "URL": "/notes/3257359 ", "Title": "SNOTE: To allow object overwriting if there is no customer modification", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "702", "ValidTo": "702", "Number": "3262964 ", "URL": "/notes/3262964 ", "Title": "SAP Note download using Download Service: Fix for not properly waiting for download completion", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "702", "ValidTo": "702", "Number": "3275194 ", "URL": "/notes/3275194 ", "Title": "INDX: De-implementation issue with SNOTE", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "702", "ValidTo": "702", "Number": "3379455 ", "URL": "/notes/3379455 ", "Title": "SNOTE: Fix to show the correct pre-requisite SAP Note in the message \" SAP Note 0000000000 incomplete\" during SAP Note implementation", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "702", "ValidTo": "702", "Number": "3379660 ", "URL": "/notes/3379660 ", "Title": "DOCU: Stop Version Creation for LANG DOCU Object.", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "702", "ValidTo": "730", "Number": "1738132 ", "URL": "/notes/1738132 ", "Title": "Change to inheritance results in deletion of method implementation", "Component": "BC-DWB-TOO-CLA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "702", "ValidTo": "731", "Number": "1539505 ", "URL": "/notes/1539505 ", "Title": "SNOTE: Minor corrections", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "702", "ValidTo": "750", "Number": "2212925 ", "URL": "/notes/2212925 ", "Title": "Fix for automatic handling of DDIC correction instructions", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "710", "ValidTo": "710", "Number": "986226 ", "URL": "/notes/986226 ", "Title": "Termination CX_XSLT_RUNTIME_ERROR when downloading note", "Component": "BC-DWB-TOO-SCR"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "710", "ValidTo": "710", "Number": "1059707 ", "URL": "/notes/1059707 ", "Title": "Note Assistant: Dump when exporting the history (WAPP,WDYC)", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "710", "ValidTo": "710", "Number": "1066203 ", "URL": "/notes/1066203 ", "Title": "Note Assistant: ITAB_DUPLICATE_KEY_IDX_OP when implementing", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "710", "ValidTo": "710", "Number": "1070182 ", "URL": "/notes/1070182 ", "Title": "Error during mass activation in the background", "Component": "BC-DWB-UTL-BRR"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "710", "ValidTo": "710", "Number": "1073502 ", "URL": "/notes/1073502 ", "Title": "Note Assistant: Termination and log when downloading a note", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "710", "ValidTo": "710", "Number": "1092828 ", "URL": "/notes/1092828 ", "Title": "SE24: Versioning of modification info & Note Assistant", "Component": "BC-DWB-TOO-CLA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "710", "ValidTo": "710", "Number": "1095426 ", "URL": "/notes/1095426 ", "Title": "Avoid Callback handling dump and logging for backend load", "Component": "BC-MOB-NWMA-MON"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "710", "ValidTo": "710", "Number": "1112141 ", "URL": "/notes/1112141 ", "Title": "Note Assistant: Implementing notes using modules EXIT_", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "710", "ValidTo": "710", "Number": "1113339 ", "URL": "/notes/1113339 ", "Title": "SE24 / Note Assistant and methods with exception classes", "Component": "BC-DWB-TOO-CLA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "710", "ValidTo": "710", "Number": "1114973 ", "URL": "/notes/1114973 ", "Title": "Note Assistant: Incorrect message when downloading note", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "710", "ValidTo": "710", "Number": "1121795 ", "URL": "/notes/1121795 ", "Title": "Text characteristics deleted after Web Dynpro note implement", "Component": "BC-DWB-WD-ABA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "710", "ValidTo": "710", "Number": "1126192 ", "URL": "/notes/1126192 ", "Title": "Note Assistant: Error when checking dependent objects", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "710", "ValidTo": "710", "Number": "1130404 ", "URL": "/notes/1130404 ", "Title": "Problem in FB RPY_EXISTENCE_CHECK_C*", "Component": "BC-DWB-TOO-CLA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "710", "ValidTo": "710", "Number": "1151059 ", "URL": "/notes/1151059 ", "Title": "Endless loop due to missing component usage", "Component": "BC-DWB-WD-ABA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "710", "ValidTo": "711", "Number": "1113339 ", "URL": "/notes/1113339 ", "Title": "SE24 / Note Assistant and methods with exception classes", "Component": "BC-DWB-TOO-CLA"}]}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2560666", "RefTitle": "SAP Screen Personas 3.0 SP06: Released Notes Information", "RefUrl": "/notes/0002560666"}]}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}