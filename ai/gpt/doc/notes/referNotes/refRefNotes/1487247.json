{"Request": {"Number": "1487247", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 304, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000017062602017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/**********?language=E&token=7C7D1E48B16FEA8FA363F01CADA03A4A"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/**********", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/**********/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1487247"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 10}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "FAQ"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "07.03.2014"}, "SAPComponentKey": {"_label": "Component", "value": "SV-SMG-DVM"}, "SAPComponentKeyText": {"_label": "Component", "value": "Data Volume Management"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Service", "value": "SV", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'SV*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "SAP Solution Manager", "value": "SV-SMG", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'SV-SMG*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Data Volume Management", "value": "SV-SMG-DVM", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'SV-SMG-DVM*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1487247 - Guided Self Service: Data Volume Management - Prerequisites"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>As of SAP Solution Manager 7.0 EhP1 SP23 a new self service session for Data Volume Management is available (technical name: SAP Data Volume Management - Best Practice Session).</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>SELF_DAO, DVM, Guided Self Service, DVM Best Practice Session</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>For the delivery of a DVM service a lot of analysis data is required which needs to be collected from the target system. The data collection part is partly tool-based which requires a minimum release of the SAP Active Global Support specific plug-ins, such as ST-A/PI or ST-PI. Additionally the SAP Solution Manager is used for the service delivery and also requires a minimum release level of certain components.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p><strong><span style=\"text-decoration: underline;\">Technical Prerequisits</span></strong><br /><br /><strong>a) SAP Solution Manager</strong><br />The SAP Data Volume Management Self Service is available as of SAP Solution Manager 7.0 EhP1 SP23 onwards. So the minimum stack required is level 23 (i.e. software component ST, release 400, Level 23+).<br />Additionally we highly recommend to activate the service content update for SAP service sessions as per SAP note 1143775. This will aways assure that your self service session is up to date to benefit from the latest content release.<br /><br /><strong>Note: </strong>Each so called service session is created in the context of a solution. In order to be prepared for the self service execution please take care that a solution is defined in your SAP Solution Manager system containing all necessary systems (logical components) for which you would like to deliver a self service. Please know that the authorizations provided in this SAP Note (see below) are only relevant for the service execution. Hence for the creation or update of a solution the authorizations might not be suitable. Please request these kind of changes by your SAP Solution Manager basis team before starting a Self Services.<br /><br /><strong>b) Managed System</strong><br />The analysis programs are all related to the software component ST-A/PI. The minimum release for this plug-in is <strong>ST-A/PI 01P*</strong>. For more information on that plug-in please refer to SAP Note 69455.<br /><br />It might be necessary that some SAP Notes are required in the managed system as well. The most efficient way of checking the technical readiness of any SAP system is to execute the report 'RTCCTOOL' via SE38 in the managed system (i.e. the system which is subject of the analysis).<br /><br />The report checks the system automatically and provides an overview of which technical prerequisites are missing in the system. It finally indicates which SAP Notes are required.<br /><br />In order to run the tool properly please execute the following steps in each system you would like to prepare for a DVM Self Service:</p>\r\n<ul>\r\n<li>Run RTCCTOOL via SE38/SA38</li>\r\n</ul>\r\n<ul>\r\n<li>Click on button 'Settings'</li>\r\n</ul>\r\n<ul>\r\n<li>Flag option 'Prepare for DVM (Data Volumt Mgmt) service ?'</li>\r\n</ul>\r\n<ul>\r\n<li>Click on 'save'</li>\r\n</ul>\r\n<p><br />Please implement all SAP Notes which are indicated with a red light.<br /><br />For your reference the most important SAP Notes are listed below:<br />- SAP Note 1466759<br />- SAP Note 1282667<br />- SAP Note 890712 (mandatory for CRM systems only)<br />- SAP Note 1099493 (mandatory for CRM systems only)<br />- SAP Note 1358933 (mandatory for CRM systems only)<br />- SAP Note 1002840 (the described settings have to be done for ORACLE databases using the new collector (SAP Note 868063))<br /><br /><br />'Recently supported Databases<br />Historically some databases were not supported by DVM related tools and SAP Solution Manager based capabilities. This restriction is not valid anymore, please find the details as follows:<br />- SAP system running on Informix you need to have ST-A/PI 01M* SP1 implemented.<br />- SAP systems running on AS400/DB400 are supported as of ST-A/PI 01M* SP1 with the additional implementation of SAP Note 1571029.<br /><br /><strong>Database Statistics</strong><br />In order to provide all necessary statistical data on DB level (such as table and index size and growth information) please check if the corresponding background jobs are scheduled hourly (SAP_COLLECTOR_FOR_PERFMONITOR)as mentioned in the SAP Notes 12103 and 16083.<br /><br /><strong>User Authorization</strong><br /><strong>a) SAP Solution Manager</strong><br />Please use the roles SAP_SMWORK_SERVICE_DEV, SAP_SMWORK_BASIC and SAP_SOLUTION_MANAGER_ONSITE for the user performing the Self Service in your SAP Solution Manager system. Please double check that the related profiles have been generated successfully otherwise you might encounter authorization issues even if the correct role is assigned.<br />Ideally you create your user via transaction SOLMAN_SETUP in your SAP Solution Manager system. The template users you will find in the scenario 'Data Volume Management' in step 'Create Template User'. Please be aware that a user needs to be created in the main SAP Solution Manager client as well as in the BW client of your SAP Solution Manager system.<br /><br /><strong>b) Managed System</strong><br />Please use the role SAP_DVM_SERVICE for all activities related to the service execution on the managed system. Please know that this role only exists if ST-PI 2008_1 SP2 is implemented in your system.<br /><br />If this plugin is not available, please follow this alternative approach and create a specific profile/role as follows:<br /><br />Due to some cross-application analyses the logon user performing the DVM analysis will require a special authorization profile 'SAP_CDMC_MASTER'.<br />For the execution of all specific DVM analyses the authorization for following objects will be necessary. Please take care that all authorizations are granted to avoid delays in the delivery process.<br />ACLA, ALO1, AL11, AOBJ, DB02, DB15, SARA, SARI, SE11, SE14, SE16, SE16N, SE37, SE38 or SA38, SM36, SM37, SM50, SM66, ST04, ST05, ST10, ST13, ST14, TAANA, SM51, SNOTE.<br /><br /><strong><span style=\"text-decoration: underline;\">Product Restrictions</span></strong><br />For the following products the application specific content is not yet part of the currently available DVM Self Service Sessions. Please know that common objects (basis &amp; cross application) might be available only, whereas application specific tables and document types will not be processed within the session:<br /><br />- SAP Bank Analyzer<br />- SAP Banking Services</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "SV-BO (Backoffice Service Delivery)"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON> (I046289)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D036024)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "J", "TranslationLanguage": "日本語", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/**********/J"}, {"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/**********/D"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1609155", "RefComponent": "SV-SMG-SER", "RefTitle": "Guided Self Services", "RefUrl": "/notes/1609155"}, {"RefNumber": "1549257", "RefComponent": "SV-SMG-DVM", "RefTitle": "DVM GSS FAQ", "RefUrl": "/notes/1549257"}, {"RefNumber": "1159758", "RefComponent": "SV-SMG-DVM", "RefTitle": "Data Volume Management: Central Preparation Note", "RefUrl": "/notes/1159758"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1609155", "RefComponent": "SV-SMG-SER", "RefTitle": "Guided Self Services", "RefUrl": "/notes/1609155 "}, {"RefNumber": "1159758", "RefComponent": "SV-SMG-DVM", "RefTitle": "Data Volume Management: Central Preparation Note", "RefUrl": "/notes/1159758 "}, {"RefNumber": "1549257", "RefComponent": "SV-SMG-DVM", "RefTitle": "DVM GSS FAQ", "RefUrl": "/notes/1549257 "}, {"RefNumber": "1610241", "RefComponent": "SV-SMG-DVM", "RefTitle": "DVM Service Preparaion Note", "RefUrl": "/notes/1610241 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": []}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}