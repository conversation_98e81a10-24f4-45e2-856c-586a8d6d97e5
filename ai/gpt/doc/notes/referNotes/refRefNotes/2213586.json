{"Request": {"Number": "2213586", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 374, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000013095032017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0002213586?language=E&token=4F5CF708C20384724DE5327DD05A4967"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0002213586", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0002213586/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "2213586"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 12}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Program error"}, "Priority": {"_label": "Priority", "value": "Correction with medium priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "24.01.2018"}, "SAPComponentKey": {"_label": "Component", "value": "XX-SER-SIZING"}, "SAPComponentKeyText": {"_label": "Component", "value": "SAP Support Portal - Sizing Tools"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Miscellaneous", "value": "XX", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Service Messages", "value": "XX-SER", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-SER*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "SAP Support Portal - Sizing Tools", "value": "XX-SER-SIZING", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-SER-SIZING*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "2213586 - Suite on HANA memory Sizing report - Advanced correction 5"}, "LongText": {"_label": "Description", "value": "<h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>This Note implements version&#160;59 of program /SDF/HDB_SIZING. Check Note <a target=\"_blank\" href=\"https://css.wdf.sap.corp/sap(bD1lbiZjPTAwMQ==)/bc/bsp/sno/ui_entry/entry.htm?iv_language=E&amp;param=69765F6D6F64653D3030312669765F7361706E6F7465735F6B65793D303132303033313436393030303030383035353732303133267361702D6C616E67756167653D452669765F6C616E67756167653D45\">1872170</a> in order to identify the Note containing the most recent version.&#160;On top of&#160;of all corrections from previous version,&#160;the Note&#160;adds the following functionalities to program /SDF/HDB_SIZING:</p>\r\n<ul>\r\n<li>S/4 HANA Sizing (S/4 HANA Finance and S/4 HANA Finance &amp; Logistics On-Premise). Note that Suite on HANA Sizing is still possible. (V55)</li>\r\n<li>Improvement of the estimation accuracy. (V55)</li>\r\n<li>Improvement to the result output. (V55)</li>\r\n<li>The report terminates with REPLACE_INFINITE_LOOP on non-HANA system (V56)</li>\r\n<li>The report terminates with&#160;COMPUTE_LOG_ERROR on non-HANA system (V57)</li>\r\n<li>Performance is improved when sizing a selection of tables and using a parralelism higher than 1. (V57)</li>\r\n<li>Data model changes on tables VBFA and KONV in S/4 Finance and Logistics are reflected more accuratly (V57)</li>\r\n<li>Error SAPSQL_PARSE_ERROR occurs on table KONV (V58)</li>\r\n<li>In rare cases, error GETWA_NOT_ASSIGNED could terminate the report on tables with a primary key with more than one field and&#160;all fields are of type \"mandant\". (V59)</li>\r\n<li>It is now possible not to read Hybrid LOB size when running on HANA. This improves greatly the performance. (V59)</li>\r\n<li>The deletion of secondary non-unique concatenated indexes is now suggested as a clean up option when run on HANA (V59)</li>\r\n<li>A table is now displayed to size the memory and disk consumption of upgrade shadow instances (V59)</li>\r\n</ul>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>Suite on HANA, Sizing, DVM Workcenter, ZNEWHDB_SIZE,&#160;&#160;/SDF/HDB_SIZING</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>Missing functionalities, program errors.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p>Implement the Note.</p>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D049993)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON> (D023487)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0002213586/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002213586/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002213586/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002213586/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002213586/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002213586/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002213586/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002213586/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002213586/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2175150", "RefComponent": "XX-SER-SIZING", "RefTitle": "Suite on HANA memory Sizing report - Advanced correction 4", "RefUrl": "/notes/2175150"}, {"RefNumber": "2080648", "RefComponent": "XX-SER-SIZING", "RefTitle": "Suite on HANA memory Sizing report - Advanced correction 3", "RefUrl": "/notes/2080648"}, {"RefNumber": "2062017", "RefComponent": "XX-SER-SIZING", "RefTitle": "Suite on HANA memory Sizing report - Advanced correction 2", "RefUrl": "/notes/2062017"}, {"RefNumber": "1995209", "RefComponent": "XX-SER-SIZING", "RefTitle": "Suite on HANA memory Sizing report - Advanced correction 1", "RefUrl": "/notes/1995209"}, {"RefNumber": "1872170", "RefComponent": "XX-SER-SIZING", "RefTitle": "ABAP on HANA sizing report (S/4HANA, Suite on HANA...)", "RefUrl": "/notes/1872170"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2486479", "RefComponent": "SV-SMG-DVM", "RefTitle": "HANA Sizing Report throwing Dump IMPORT_ALIGNMENT_MISMATCH", "RefUrl": "/notes/2486479 "}, {"RefNumber": "2621935", "RefComponent": "XX-SER-SIZING", "RefTitle": "HANA memory Sizing report - Advanced correction 10", "RefUrl": "/notes/2621935 "}, {"RefNumber": "2504480", "RefComponent": "XX-SER-SIZING", "RefTitle": "Suite on HANA memory Sizing report - Advanced correction 9", "RefUrl": "/notes/2504480 "}, {"RefNumber": "2303847", "RefComponent": "XX-SER-SIZING", "RefTitle": "Suite on HANA memory Sizing report - Advanced correction 6", "RefUrl": "/notes/2303847 "}, {"RefNumber": "1872170", "RefComponent": "XX-SER-SIZING", "RefTitle": "ABAP on HANA sizing report (S/4HANA, Suite on HANA...)", "RefUrl": "/notes/1872170 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "ST-PI", "From": "2008_1_700", "To": "2008_1_700", "Subsequent": "X"}, {"SoftwareComponent": "ST-PI", "From": "2008_1_710", "To": "2008_1_710", "Subsequent": "X"}, {"SoftwareComponent": "ST-PI", "From": "740", "To": "740", "Subsequent": "X"}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "ST-PI 2008_1_700", "SupportPackage": "SAPKITLRDN", "URL": "/supportpackage/SAPKITLRDN"}, {"SoftwareComponentVersion": "ST-PI 2008_1_710", "SupportPackage": "SAPKITLREN", "URL": "/supportpackage/SAPKITLREN"}, {"SoftwareComponentVersion": "ST-PI 740", "SupportPackage": "SAPK-74004INSTPI", "URL": "/supportpackage/SAPK-74004INSTPI"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": [{"SoftwareComponent": "ST-PI", "NumberOfCorrin": 12, "URL": "/corrins/0002213586/212"}]}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 12, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 5, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": [{"SoftwareComponent": "ST-PI", "ValidFrom": "2008_1_700", "ValidTo": "2008_1_700", "Number": "2080648 ", "URL": "/notes/2080648 ", "Title": "Suite on HANA memory Sizing report - Advanced correction 3", "Component": "XX-SER-SIZING"}, {"SoftwareComponent": "ST-PI", "ValidFrom": "2008_1_700", "ValidTo": "2008_1_700", "Number": "2175150 ", "URL": "/notes/2175150 ", "Title": "Suite on HANA memory Sizing report - Advanced correction 4", "Component": "XX-SER-SIZING"}, {"SoftwareComponent": "ST-PI", "ValidFrom": "2008_1_700", "ValidTo": "2008_1_700", "Number": "2213586 ", "URL": "/notes/2213586 ", "Title": "Suite on HANA memory Sizing report - Advanced correction 5", "Component": "XX-SER-SIZING"}, {"SoftwareComponent": "ST-PI", "ValidFrom": "2008_1_700", "ValidTo": "2008_1_710", "Number": "1995209 ", "URL": "/notes/1995209 ", "Title": "Suite on HANA memory Sizing report - Advanced correction 1", "Component": "XX-SER-SIZING"}, {"SoftwareComponent": "ST-PI", "ValidFrom": "2008_1_700", "ValidTo": "2008_1_710", "Number": "2062017 ", "URL": "/notes/2062017 ", "Title": "Suite on HANA memory Sizing report - Advanced correction 2", "Component": "XX-SER-SIZING"}, {"SoftwareComponent": "ST-PI", "ValidFrom": "2008_1_700", "ValidTo": "2008_1_710", "Number": "2080648 ", "URL": "/notes/2080648 ", "Title": "Suite on HANA memory Sizing report - Advanced correction 3", "Component": "XX-SER-SIZING"}, {"SoftwareComponent": "ST-PI", "ValidFrom": "740", "ValidTo": "740", "Number": "2062017 ", "URL": "/notes/2062017 ", "Title": "Suite on HANA memory Sizing report - Advanced correction 2", "Component": "XX-SER-SIZING"}, {"SoftwareComponent": "ST-PI", "ValidFrom": "740", "ValidTo": "740", "Number": "2080648 ", "URL": "/notes/2080648 ", "Title": "Suite on HANA memory Sizing report - Advanced correction 3", "Component": "XX-SER-SIZING"}, {"SoftwareComponent": "ST-PI", "ValidFrom": "740", "ValidTo": "740", "Number": "2175150 ", "URL": "/notes/2175150 ", "Title": "Suite on HANA memory Sizing report - Advanced correction 4", "Component": "XX-SER-SIZING"}, {"SoftwareComponent": "ST-PI", "ValidFrom": "740", "ValidTo": "740", "Number": "2213586 ", "URL": "/notes/2213586 ", "Title": "Suite on HANA memory Sizing report - Advanced correction 5", "Component": "XX-SER-SIZING"}]}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}