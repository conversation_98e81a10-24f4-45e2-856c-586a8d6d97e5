{"Request": {"Number": "13719", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 1681, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000014333182017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000013719?language=E&token=7235B7D49925959672D3C1F5C66A3A41"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000013719", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000013719/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "13719"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 30}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Advance development"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "26.04.2006"}, "SAPComponentKey": {"_label": "Component", "value": "BC-CTS"}, "SAPComponentKeyText": {"_label": "Component", "value": "Change and Transport System"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Basis Components", "value": "BC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Change and Transport System", "value": "BC-CTS", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-CTS*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "13719 - Preliminary transports to customers (note for customers)"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>An SAP consultant or support representative has made data available to the customers in the form of a note attachment (or - possible until January 2003 - on sapservX) for the import into the&#x00A0;&#x00A0;customer system (as described in the internal&#x00A0;&#x00A0;Note 11920).<br /></p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>sapserv3 Walldorf<br />sapserv4 Foster City<br />sapserv5 Tokyo<br />sapserv6 Sydney<br />sapserv7 Singapore<br /></p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>As of Release 3.0, preliminary transports (delivery of preliminary corrections) have been delivered in the form of Support Packages. There are also preliminary transports of developments that are imported using the manual procedure described here.<br /><br />When importing requests from SAP systems, you must ensure that requests in area 900000 - 999999 (for example, Q30K90821), possibly collide with customized transports, especially if there are customer systems with the same name (in the example, this would be Q30). These jobs should be rejected if the names are the same.<br /><br />In systems with Releases older than 4.6C the following is important: When you import jobs from SAP systems, note that jobs in the range 900000 - 999999 (for example Q30K900821) are treated as customer- specific transports. The transport objects are considered modified. Such jobs should therefore be rejected if the system is older than 4.6C. As of Release 4.6C, this restriction does not apply.<br /><br />The transport properties will be imported with the transported objects. After the import the original system will therefore no longer be SAP but for example, P30 if the export was carried out in P30. As user DDIC, you can maintain this in Transaction SM31 in Table TADIR, and change it to SAP again. This is required if a system with the same name as the export system exists in your system landscape.<br /></p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>Two files are generated when exporting:</p> <UL><UL><LI>The R3trans file which contains the actual data.</LI></UL></UL> <UL><UL><LI>The transport information file which contains the control-data for the transport.<br /></LI></UL></UL> <p>This data has been made available to your either as a note attachment or directly on a sapservX host.</p> <OL>1. Situation: The data is available as a note attachment (procedure as of February 2003).</OL> <OL><OL>a) Download the data from the note dealing with preliminary transport, as described in Note 480180.</OL></OL> <OL><OL>b) The data is contained either in a ZIP or in a SAR archive that you will need to unpack.</OL></OL> <OL>2. Situation: The data is available on sapservX and has to be transferred using ftp. You can find the data in directory general/R3server/abap/note.0012345.</OL> <p>The following describes the procedures for UNIX, WINDOWS, and OS/400.<br /></p> <OL><OL>a) UNIX:</OL></OL> <p>The binary mode MUST be switched on for the data transfer, otherwise the R3trans file will be destroyed.<br />The customer calls ftp as follows:<br />&#x00A0;&#x00A0;&lt;set up the connection to sapserv3&gt;<br />&#x00A0;&#x00A0;cd /usr/sap/trans/tmp&#x00A0;&#x00A0;&#x00A0;&#x00A0;/* cd at customer site&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;*/<br />&#x00A0;&#x00A0;ftp sapservx&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;/* Start file transfer program&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;*/<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;/* Enter 'ftp' as user and password */<br />&#x00A0;&#x00A0;cd general/R3server/abap/note.nnnnnnn /* directory&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;*/<br />&#x00A0;&#x00A0;bin&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;/* Switch to binary mode&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;*/<br />&#x00A0;&#x00A0;get Rnnnnnn.xxx&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;/* Fetch R3trans data file, name&#x00A0;&#x00A0;&#x00A0;&#x00A0;*/<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;/* depends on the transport request.*/<br />&#x00A0;&#x00A0;get Dnnnnnn.xxx&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;/* Fetch SDO file if it exists.&#x00A0;&#x00A0;&#x00A0;&#x00A0; */<br />&#x00A0;&#x00A0;get Knnnnnn.xxx&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; /* Fetch the transport info file.&#x00A0;&#x00A0; */<br />&#x00A0;&#x00A0;bye&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; /* End ftp.&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;*/<br />&#x00A0;&#x00A0;mv Rnnnnnn.xxx ../data&#x00A0;&#x00A0;&#x00A0;&#x00A0;/* Copy R3trans file into data dir. */<br />&#x00A0;&#x00A0;mv Dnnnnnn.xxx ../data&#x00A0;&#x00A0;&#x00A0;&#x00A0;/* SDO file into the data dir.&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;*/&#x00A0;&#x00A0;mv Knnnnnn.xxx ../cofiles /* info file into cofiles dir.&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;*/</p> <OL><OL>b) Windows<br />Since the text mode on WINDOWS is different from the text mode on UNIX, using ftp the text files (command files and buffer) have to be transferred in text mode and the data files in binary mode.<br />Example: Assuming that the transport request is called P21K000123, then the following files have to be transferred in the specified mode using ftp:<br /><br />Name&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Contents Mode&#x00A0;&#x00A0;&#x00A0;&#x00A0;Target directory&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Exists<br />R000123.P21&#x00A0;&#x00A0;&#x00A0;&#x00A0;data&#x00A0;&#x00A0;&#x00A0;&#x00A0;bin&#x00A0;&#x00A0;&#x00A0;&#x00A0;\\usr\\sap\\trans\\data&#x00A0;&#x00A0;&#x00A0;&#x00A0;always<br />K000123.P21&#x00A0;&#x00A0;&#x00A0;&#x00A0;info&#x00A0;&#x00A0;&#x00A0;&#x00A0;ascii&#x00A0;&#x00A0; \\usr\\sap\\trans\\cofiles always<br />D000123.P21&#x00A0;&#x00A0;&#x00A0;&#x00A0;data&#x00A0;&#x00A0;&#x00A0;&#x00A0;bin&#x00A0;&#x00A0;&#x00A0;&#x00A0;\\usr\\sap\\trans\\data&#x00A0;&#x00A0;&#x00A0;&#x00A0;only for ADO<br /><br />It is essential to use the correct mode when transferring data, otherwise the import cannot be carried out successfully.<br />The customer calls up ftp as follows:<br />&lt; Establish a connection to sapservX &gt;<br />cd \\usr\\sap\\trans\\tmp /* cd at the customer site&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;*/<br />ftp sapservX&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;/* start file transfer program&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;*/<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;/* enter 'ftp' as user and password&#x00A0;&#x00A0; */<br />cd dist general/R3server/abap/note.0012345 /* directory&#x00A0;&#x00A0;&#x00A0;&#x00A0;*/<br />bin&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;/* switch to binary mode&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;*/<br />get Rnnnnnn.xxx&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; /* fetch R3trans data file, name&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;*/<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;/* depends on the transport request&#x00A0;&#x00A0;*/<br />get Dnnnnnn.xxx&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; /* fetch SDO file, if it exists&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;*/<br />ascii&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;/* switch to text mode&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;*/<br />get Knnnnnn.xxx&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;/* fetch transport info file&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;*/<br />bye&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;/* end ftp&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;*/<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;/* Copy.....&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;*/<br />move Rnnnnnn.xxx ..\\data&#x00A0;&#x00A0;&#x00A0;&#x00A0; /*R3trans file to data dir.&#x00A0;&#x00A0;&#x00A0;&#x00A0;*/<br />move Dnnnnnn.xxx ..\\data&#x00A0;&#x00A0;&#x00A0;&#x00A0;/*SDO file to data directory&#x00A0;&#x00A0; */<br />move Knnnnnn.xxx ..\\cofiles&#x00A0;&#x00A0;/*Info file to cofile directory*/<br /></OL></OL> <OL><OL>c) AS/400<br />Use command APYABFIX: APYABFIX SID(&lt;SID&gt;) FROMHOST('sapserv3')<BR/> &#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; FROMDIR('general/R3server/abap/note.0012345')<BR/> &#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; TRANSPORT('*FROMDIR')<BR/> &#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; TPIMPORT(*YES)</OL></OL> <p>Replace the place-holders &lt;SID&gt;, sapserv3 and note.0012345.<br />The parameter value TRANSPORT(*FROMDIR) controls the automatic determination of the transport request number while the system searches through the specified directory for transport files. The last parameter TPIMPORT initiates the calls tp 'addtobuffer ...' and tp 'import ...' for the transport request.<br />If the execution of this command causes the error 'Too many files found in directory' copy the files manually into your data and cofile directory using ftp.<br /><br />&lt; Set up the connection to sapservX &gt;<br />ftp sapservX&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;/* start file transfer program&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;*/<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;/* enter 'ftp' as user and password */<br />cd dist general/R3server/abap/note.0012345<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;/* to change to the directory that&#x00A0;&#x00A0; */<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;/* contains the transport&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;*/<br />namefmt 1<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;/* to access the file system of AS/400&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;*/<br />lcd '/usr/sap/trans/data'<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;/* change to the transport data directory locally*/<br />bin&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;/* Switch to binary mode&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;*/<br />get Rnnnnnn.xxx&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; /* fetch R3trans data file, name of&#x00A0;&#x00A0;&#x00A0;&#x00A0; */<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;/* file depends on transport request. */<br />get Dnnnnnn.xxx&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; /* fetch SDO file if it exists. */<br />lcd '/usr/sap/trans/cofiles'<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;/* change to cofile directory locally&#x00A0;&#x00A0;*/<br />ascii&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;/* following transfer in text mode&#x00A0;&#x00A0;&#x00A0;&#x00A0; */<br />get Knnnnnn.xxx&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;/* fetch transport info file.&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;*/<br />quit&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;/* end ftp.&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;*/<br /><br />Manual import of the transports:<br />Logon as &lt;SID&gt;OFR<br />tp 'pf=&lt;Transportprofil&gt; addtobuffer &lt;Tr.Auftr.&gt; &lt;Zielsys&gt;'<br />tp 'pf=&lt;Transportprofil&gt; import &lt;Tr.Auftr.&gt; &lt;Zielsys&gt; U26'<br /><br />Starting the import:</p> <UL><LI>Start the import as follows:</LI></UL> <OL><OL>a) UNIX:<br />as user &lt;sid&gt;adm:<br />/usr/sap/&lt;sid&gt;/SYS/exe/run/tp addtobuffer &lt;Tr.Auftr.&gt; &lt;Zielsys&gt; \\<br />&#x00A0;&#x00A0;pf=&lt;Transportprofil&gt;<br />/usr/sap/&lt;sid&gt;/SYS/exe/run/tp import &lt;Tr.Auftr.&gt; &lt;Zielsys&gt; U26 \\<br />&#x00A0;&#x00A0;pf=&lt;Transportprofil&gt;<br /><br />Replace &lt;sid&gt; with the current SID, for example, C11.<br />Example:<br />/usr/sap/C11/SYS/exe/run/tp addtobuffer P21K000123 C11 pf=/usr/sap/trans<br />/bin/TP_DOMAINABC.PFL<br />/usr/sap/C11/SYS/exe/run/tp import P21K000123 C11 U26 pf=/usr/sap/trans<br />/bin/TP_DOMAINABC.PFL<br /></OL></OL> <OL><OL>b) Windows:<br />as user &lt;sid&gt;adm:<br />cd \\usr\\sap\\trans\\bin<br />\\usr\\sap\\&lt;sid&gt;\\SYS\\exe\\run\\tp addtobuffer &lt;Tr.requ.&gt; &lt;Targ.sys&gt;<br />\\usr\\sap\\&lt;sid&gt;\\SYS\\exe\\run\\tp import &lt;Tr.requ.&gt; &lt;Targ.sys&gt; U26<br />Replace &lt;sid&gt; by the current SID which is for example C11.<br />Example:<br />cd \\usr\\sap\\trans\\bin<br />\\usr\\sap\\C11\\SYS\\exe\\run\\tp addtobuffer P21K000123 C11<br />usr\\sap\\C11\\SYS\\exe\\run\\tp tst P21K000123 C11<br />\\usr\\sap\\C11\\SYS\\exe\\run\\tp import P21K000123 C11 U26<br /></OL></OL> <OL><OL>c) AS400: Already completed by the command above.<br /></OL></OL> <UL><LI>A prerequisite for the import is that the periodic background job RDDIMPDP has been scheduled in the target system (see manual BC System Administration, Appendix D).</LI></UL> <UL><LI>Specifying unconditional modes 2 and 6 (u26) results in original objects and objects in unconfirmed repairs being overwritten as well. If the \"tp tst\" call (test import) ends with return code 8 as a result of such objects, we recommend the release of the open repairs before the import so that the corresponding versions of the modified objects will be generated in the version database.</LI></UL></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Transaction codes", "Value": "MOVE"}, {"Key": "Transaction codes", "Value": "HIER"}, {"Key": "Transaction codes", "Value": "FILE"}, {"Key": "Transaction codes", "Value": "SM31"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D000706)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON> (D030435)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "J", "TranslationLanguage": "日本語", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000013719/J"}, {"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000013719/D"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000013719/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000013719/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000013719/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000013719/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000013719/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000013719/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000013719/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "99821", "RefComponent": "FI-LC-LC", "RefTitle": "Archiving/consolidation: new archiving programs", "RefUrl": "/notes/99821"}, {"RefNumber": "997653", "RefComponent": "XX-CSC-AT-IS-H", "RefTitle": "IS-H AT: Standard implementation EDIVKA 5.0 (Delta)", "RefUrl": "/notes/997653"}, {"RefNumber": "996998", "RefComponent": "PY-AT", "RefTitle": "JW 2006/07:Änderungen zum Lohnzettel Finanz (ab Rel ERP2004)", "RefUrl": "/notes/996998"}, {"RefNumber": "996997", "RefComponent": "PY-AT", "RefTitle": "JW 2006/07: Änderungen zum Lohnzettel Finanz (Rel 46C-470)", "RefUrl": "/notes/996997"}, {"RefNumber": "995676", "RefComponent": "FI-AA-IS", "RefTitle": "RASIMU02 - revised standard variants", "RefUrl": "/notes/995676"}, {"RefNumber": "995573", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/995573"}, {"RefNumber": "99271", "RefComponent": "CA-EUR-CUR", "RefTitle": "Curr.maintenance euro, Curr. Customizing Assistant", "RefUrl": "/notes/99271"}, {"RefNumber": "99056", "RefComponent": "XX-CSC-CO", "RefTitle": "Legal tax reports for Colombia.", "RefUrl": "/notes/99056"}, {"RefNumber": "990461", "RefComponent": "XX-PART-ISHMED-ORD", "RefTitle": "Clinical Work Station: Customer-specific view types", "RefUrl": "/notes/990461"}, {"RefNumber": "990227", "RefComponent": "XX-PART-ISHMED", "RefTitle": "Clin order: Migration report N1_MIGPRG - Error Messages", "RefUrl": "/notes/990227"}, {"RefNumber": "98876", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/98876"}, {"RefNumber": "988501", "RefComponent": "XX-PART-ISHMED", "RefTitle": "Medication: Documentation BAdI N1_GENERATE_MEEVENT", "RefUrl": "/notes/988501"}, {"RefNumber": "98642", "RefComponent": "IS-OIL-BC", "RefTitle": "Order of IS-OIL notes 2.0d & 1.0d on R/3 3.1H (SP)", "RefUrl": "/notes/98642"}, {"RefNumber": "985289", "RefComponent": "XX-CSC-AT-IS-H", "RefTitle": "IS-H AT: Intensivdaten - Prüfen bei OE-Wechsel", "RefUrl": "/notes/985289"}, {"RefNumber": "983299", "RefComponent": "XX-PART-ISHMED", "RefTitle": "Medication: Missing BAdIs in the IMG", "RefUrl": "/notes/983299"}, {"RefNumber": "98228", "RefComponent": "BC-CCM-MON-ORA", "RefTitle": "Transp. after database upgrade to Oracle 8.0 and 8.1", "RefUrl": "/notes/98228"}, {"RefNumber": "982031", "RefComponent": "XX-CSC-AT-IS-H", "RefTitle": "IS-H AT: Scoring - Anpassungen an das Modell 2007", "RefUrl": "/notes/982031"}, {"RefNumber": "981676", "RefComponent": "XX-PART-ISHMED-ORD", "RefTitle": "Clin. work station: Customer-specific view types", "RefUrl": "/notes/981676"}, {"RefNumber": "980828", "RefComponent": "XX-PART-ISHMED", "RefTitle": "Medication: BAdI for Customer-Specific Drug Search", "RefUrl": "/notes/980828"}, {"RefNumber": "978380", "RefComponent": "CRM-LOC-BR", "RefTitle": "Loc Brazil: Decimal places for rounding on Tax amounts", "RefUrl": "/notes/978380"}, {"RefNumber": "977290", "RefComponent": "XX-PART-ISHMED", "RefTitle": "Nursing: Nursing Plan - Determine Selection Period", "RefUrl": "/notes/977290"}, {"RefNumber": "976518", "RefComponent": "SRM-CAT-MDM", "RefTitle": "SRM MDM Catalog enhancement: BAdI Implementation", "RefUrl": "/notes/976518"}, {"RefNumber": "975898", "RefComponent": "XX-PART-ISHMED", "RefTitle": "Clin. Work Station: Form ISHMED_OPPLAN - Error Message", "RefUrl": "/notes/975898"}, {"RefNumber": "973701", "RefComponent": "XX-CSC-AT-IS-H", "RefTitle": "IS-H AT: Einzugsgebiete bei abweichenden Rechnungsadressen", "RefUrl": "/notes/973701"}, {"RefNumber": "971534", "RefComponent": "XX-CSC-AT-IS-H", "RefTitle": "IS-H AT: Report RNWAT_KORR_NTPKCH - Abgl. NTPKCH - NTSP", "RefUrl": "/notes/971534"}, {"RefNumber": "971179", "RefComponent": "XX-PART-ISHMED-ORD", "RefTitle": "Clin. work station: Create customer-specific view types", "RefUrl": "/notes/971179"}, {"RefNumber": "968928", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/968928"}, {"RefNumber": "968363", "RefComponent": "XX-CSC-CH-IS-H", "RefTitle": "IS-H CH: Rechnung V4.0, Überschrift TG/TP Rechnung", "RefUrl": "/notes/968363"}, {"RefNumber": "96732", "RefComponent": "BC-CTS-CCO", "RefTitle": "CC-INFO: DB2/390 - client copy is very slow", "RefUrl": "/notes/96732"}, {"RefNumber": "965183", "RefComponent": "SRM-CM", "RefTitle": "CatMan - Missing property keys in view BBPV_CM_PROPVALS", "RefUrl": "/notes/965183"}, {"RefNumber": "962704", "RefComponent": "XX-CSC-AR-IS-OIL", "RefTitle": "IS-Oil Localization of Argentinean Taxes in Sales", "RefUrl": "/notes/962704"}, {"RefNumber": "960915", "RefComponent": "FI-AA-IS", "RefTitle": "RAAEND01 - revised standard variants", "RefUrl": "/notes/960915"}, {"RefNumber": "955278", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/955278"}, {"RefNumber": "954447", "RefComponent": "XX-CSC-AT-IS-H", "RefTitle": "IS-H AT: ELDA - aktuelle Anpassungen (P321, ELDAL)", "RefUrl": "/notes/954447"}, {"RefNumber": "953877", "RefComponent": "PA-PA-US", "RefTitle": "Hardcoded Address Subtype in W2 Reprint Application.", "RefUrl": "/notes/953877"}, {"RefNumber": "952103", "RefComponent": "IS-H", "RefTitle": "Clinical order: migrated order categories - runtime error", "RefUrl": "/notes/952103"}, {"RefNumber": "951757", "RefComponent": "FI-AA-IS", "RefTitle": "RAANLA_ALV01 Revised standard variant SAP&001", "RefUrl": "/notes/951757"}, {"RefNumber": "948649", "RefComponent": "XX-CSC-AT-IS-H", "RefTitle": "IS-H AT: Intensivdokumentation - Stornieren nicht möglich", "RefUrl": "/notes/948649"}, {"RefNumber": "948607", "RefComponent": "PY-IT", "RefTitle": "HR-IT: 770 2006 Legal Change", "RefUrl": "/notes/948607"}, {"RefNumber": "945787", "RefComponent": "PY-BR", "RefTitle": "HBRCAGED: Record type C and X - Layout changes", "RefUrl": "/notes/945787"}, {"RefNumber": "945038", "RefComponent": "EHS-SAF-RCK", "RefTitle": "Substance volume tracking: Class and char. do not exist", "RefUrl": "/notes/945038"}, {"RefNumber": "944647", "RefComponent": "PY-AT", "RefTitle": "Infotyp 0632: Fehlermeldung: Geschuetzte Tabstrip Reiter...", "RefUrl": "/notes/944647"}, {"RefNumber": "937400", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/937400"}, {"RefNumber": "93715", "RefComponent": "SD-BF", "RefTitle": "Archiving: new functions SD_VBRK", "RefUrl": "/notes/93715"}, {"RefNumber": "936235", "RefComponent": "XX-PART-ISHMED", "RefTitle": "Clin. Work Station: Form ISHMED_OPPLAN - Overlaps", "RefUrl": "/notes/936235"}, {"RefNumber": "935683", "RefComponent": "PY-AT", "RefTitle": "Gemeindetabelle für Kommunalsteuer (aktualisierte Einträge)", "RefUrl": "/notes/935683"}, {"RefNumber": "931569", "RefComponent": "XX-PART-ISHMED", "RefTitle": "Performance Improvements Environment", "RefUrl": "/notes/931569"}, {"RefNumber": "92989", "RefComponent": "SD-BF", "RefTitle": "Archiving: new functions RV_LIKP", "RefUrl": "/notes/92989"}, {"RefNumber": "929353", "RefComponent": "CA-ESS-WD", "RefTitle": "Changes to the Home and Area page customizing for LWE", "RefUrl": "/notes/929353"}, {"RefNumber": "926976", "RefComponent": "PY-BR", "RefTitle": "DDIC: Missing domain PBR_INEMP", "RefUrl": "/notes/926976"}, {"RefNumber": "926566", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/926566"}, {"RefNumber": "925164", "RefComponent": "PY-BR", "RefTitle": "HBRRAIS0 Legal Changes 2006", "RefUrl": "/notes/925164"}, {"RefNumber": "923190", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/923190"}, {"RefNumber": "921823", "RefComponent": "XX-PART-ISHMED", "RefTitle": "Transactions NT32 and NT33 not Executable", "RefUrl": "/notes/921823"}, {"RefNumber": "920491", "RefComponent": "PA-ESS-XX", "RefTitle": "ESS:Change Own Data enhancement to allow multi record update", "RefUrl": "/notes/920491"}, {"RefNumber": "918548", "RefComponent": "EHS-SAF-RSH", "RefTitle": "You cannot assign several report categories to one country", "RefUrl": "/notes/918548"}, {"RefNumber": "917854", "RefComponent": "PY-BR", "RefTitle": "New DDIC objects: GPS and SEFIP Compensation", "RefUrl": "/notes/917854"}, {"RefNumber": "917199", "RefComponent": "PY-US-TR", "RefTitle": "1099R Correction Processing Forms", "RefUrl": "/notes/917199"}, {"RefNumber": "916939", "RefComponent": "XX-PART-ISHMED", "RefTitle": "Planning Grid - Appt Start Time Grid or Time Slot", "RefUrl": "/notes/916939"}, {"RefNumber": "916260", "RefComponent": "XX-PART-ISHMED", "RefTitle": "Planning: Appointment Template - Caller for BAdI N_APP_CONST", "RefUrl": "/notes/916260"}, {"RefNumber": "91615", "RefComponent": "EC-PCA-TL-ARC", "RefTitle": "Archiving/profit center: new archiving programs", "RefUrl": "/notes/91615"}, {"RefNumber": "91594", "RefComponent": "BC-UPG-RDM", "RefTitle": "Additional Info: Upgrading to Release 3.1I", "RefUrl": "/notes/91594"}, {"RefNumber": "915555", "RefComponent": "FI-GL-GL-F", "RefTitle": "Elec tax return special advance pymt and permanent extension", "RefUrl": "/notes/915555"}, {"RefNumber": "913171", "RefComponent": "PA-ESS-XX", "RefTitle": "Adapter enhancements for Allowed Infotype/subtype", "RefUrl": "/notes/913171"}, {"RefNumber": "913024", "RefComponent": "PY-BR", "RefTitle": "HBRSEF00 8.0 fixes", "RefUrl": "/notes/913024"}, {"RefNumber": "912286", "RefComponent": "XX-PART-ISHMED", "RefTitle": "Planning: Planning Grid - Documentation N1PTTAGE / N1PTRNO", "RefUrl": "/notes/912286"}, {"RefNumber": "911049", "RefComponent": "XX-CSC-AT-IS-H", "RefTitle": "IS-H AT: Report RNWATKUELIST um \"ohne VV/SV\" erweitern", "RefUrl": "/notes/911049"}, {"RefNumber": "910766", "RefComponent": "PY-US-TR", "RefTitle": "Form 940 is incorrect when employee has tips", "RefUrl": "/notes/910766"}, {"RefNumber": "910338", "RefComponent": "PY-US-TR", "RefTitle": "Form 940: Year End 2005 changes", "RefUrl": "/notes/910338"}, {"RefNumber": "907191", "RefComponent": "PY-US-TR", "RefTitle": "W-2 magnetic media for Puerto Rico", "RefUrl": "/notes/907191"}, {"RefNumber": "906756", "RefComponent": "XX-PART-ISHMED", "RefTitle": "Service Management: Synchronization Correction Appointment -", "RefUrl": "/notes/906756"}, {"RefNumber": "90633", "RefComponent": "LO-LIS-DC", "RefTitle": "New units in LIS", "RefUrl": "/notes/90633"}, {"RefNumber": "902951", "RefComponent": "PY-US-TR", "RefTitle": "Invalid SSN range in W-2 self-sealer form", "RefUrl": "/notes/902951"}, {"RefNumber": "902210", "RefComponent": "PY-AT-PS", "RefTitle": "Jahreswechsel 2005/06 (öffentlicher Dienst): Änderungen BVA", "RefUrl": "/notes/902210"}, {"RefNumber": "892401", "RefComponent": "FS-CML-PO", "RefTitle": "STM: Termination of DART extraction on DB2 systems", "RefUrl": "/notes/892401"}, {"RefNumber": "890833", "RefComponent": "PY-US-TR", "RefTitle": "U.S. Tax Reporter: Year End 2005 Phase II", "RefUrl": "/notes/890833"}, {"RefNumber": "890572", "RefComponent": "XX-PART-ISHMED", "RefTitle": "Clinical Order: Transport Order Types - Various Adjustments", "RefUrl": "/notes/890572"}, {"RefNumber": "88895", "RefComponent": "SD-BF", "RefTitle": "Archiving: New functions SD_VBAK", "RefUrl": "/notes/88895"}, {"RefNumber": "887484", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/887484"}, {"RefNumber": "886984", "RefComponent": "PY-AT", "RefTitle": "Wichtige Korrekturen zu E-Card", "RefUrl": "/notes/886984"}, {"RefNumber": "883862", "RefComponent": "XX-PROJ-CDP-010-R3", "RefTitle": "Guidelines to install MRS Utilization report on WAS 620", "RefUrl": "/notes/883862"}, {"RefNumber": "882093", "RefComponent": "PY-BR", "RefTitle": "HBRRAIS0 to run on all companies at once", "RefUrl": "/notes/882093"}, {"RefNumber": "875109", "RefComponent": "PY-BR", "RefTitle": "SEFIP: code updates", "RefUrl": "/notes/875109"}, {"RefNumber": "874686", "RefComponent": "PY-BR", "RefTitle": "HBRCFER0 - new BAdIs available for customer specific process", "RefUrl": "/notes/874686"}, {"RefNumber": "873326", "RefComponent": "PA-BN-CE", "RefTitle": "CE Enablement of Benefit Confirmation Form", "RefUrl": "/notes/873326"}, {"RefNumber": "870940", "RefComponent": "FI-AA-IS", "RefTitle": "Missing SAP system variants for standard queries", "RefUrl": "/notes/870940"}, {"RefNumber": "868660", "RefComponent": "CRM-MW-SRV", "RefTitle": "Inconsistencies between CRM Server and Mobile Client", "RefUrl": "/notes/868660"}, {"RefNumber": "864784", "RefComponent": "IS-OIL-DS-TDP", "RefTitle": "IS-OIL TDP LEGAL CHANGES FRANCE - PREREQUISITE LICENCE", "RefUrl": "/notes/864784"}, {"RefNumber": "864234", "RefComponent": "XX-PART-ISHMED", "RefTitle": "Clinical Order: Enhancement and Documentation of BAdIs", "RefUrl": "/notes/864234"}, {"RefNumber": "860908", "RefComponent": "XX-PART-ISHMED", "RefTitle": "Clin. Order: BAdI N1_CORDER_USER_COMM - Insufficient Docu", "RefUrl": "/notes/860908"}, {"RefNumber": "86037", "RefComponent": "MM-IS-IC", "RefTitle": "Collective note on doc.evaluations, Stock/Reqs", "RefUrl": "/notes/86037"}, {"RefNumber": "858371", "RefComponent": "XX-PART-ISHMED", "RefTitle": "i.s.h.med:16128 BAdI Quick Service Entry", "RefUrl": "/notes/858371"}, {"RefNumber": "857506", "RefComponent": "BC-DB-SDB", "RefTitle": "Correction of STRING fields after EXPORT/IMPORT with R3load", "RefUrl": "/notes/857506"}, {"RefNumber": "856110", "RefComponent": "PY-FI", "RefTitle": "HFILTALEL0 - use percentage stored in  V_T7FI02.", "RefUrl": "/notes/856110"}, {"RefNumber": "853134", "RefComponent": "PY-US-TR", "RefTitle": "Tax Reporter legal changes for 2nd Quarter 2005.", "RefUrl": "/notes/853134"}, {"RefNumber": "850954", "RefComponent": "XX-CSC-CH-IS-H", "RefTitle": "IS-H CH: MEDIDATA 4.0 - neue Version", "RefUrl": "/notes/850954"}, {"RefNumber": "847797", "RefComponent": "XX-PART-ISHMED", "RefTitle": "Clinical Order: Pregnancy Indicator", "RefUrl": "/notes/847797"}, {"RefNumber": "845320", "RefComponent": "PY-AT", "RefTitle": "Lohnkontenverordnung 2005 zum Kinderzuschlag v. 28.04.2005", "RefUrl": "/notes/845320"}, {"RefNumber": "843402", "RefComponent": "MM-PUR", "RefTitle": "CONNE_IMPORT_CONVERSION_ERROR", "RefUrl": "/notes/843402"}, {"RefNumber": "839054", "RefComponent": "XX-PART-ISHMED", "RefTitle": "Clin.Order/Prereg.: Migration - Context Data Correction", "RefUrl": "/notes/839054"}, {"RefNumber": "838704", "RefComponent": "PY-PT", "RefTitle": "HR-PT: Social balance - Absences introduced by CATS", "RefUrl": "/notes/838704"}, {"RefNumber": "838401", "RefComponent": "PY-PT", "RefTitle": "HR-PT: RPCOVCP0 - List employees with or w/o membership fees", "RefUrl": "/notes/838401"}, {"RefNumber": "836813", "RefComponent": "XX-CSC-AT-IS-H", "RefTitle": "IS-H AT: Intensivdaten - Online-Dialog", "RefUrl": "/notes/836813"}, {"RefNumber": "832577", "RefComponent": "BC-DWB-DIC-AC", "RefTitle": "Runtime error on Unicode - inconsistent table types", "RefUrl": "/notes/832577"}, {"RefNumber": "83204", "RefComponent": "CO-PA-TO", "RefTitle": "Usage strategy for summarization levels", "RefUrl": "/notes/83204"}, {"RefNumber": "831875", "RefComponent": "PY-PT", "RefTitle": "HR-PT: Minimum wage for Madeira", "RefUrl": "/notes/831875"}, {"RefNumber": "831538", "RefComponent": "PY-BR", "RefTitle": "IMG text missing in release 4.6C for object PAY_BR_AU020", "RefUrl": "/notes/831538"}, {"RefNumber": "831275", "RefComponent": "PY-BR", "RefTitle": "SEFIP: collective agreement and other enhancements", "RefUrl": "/notes/831275"}, {"RefNumber": "83076", "RefComponent": "AC-INT", "RefTitle": "MM_ACCTIT: Archiving programs for ACCTIT, ACCTHD, ACCTCR", "RefUrl": "/notes/83076"}, {"RefNumber": "827099", "RefComponent": "PY-PT", "RefTitle": "HR-PT: Leave Annual Summary - Compensation not displayed", "RefUrl": "/notes/827099"}, {"RefNumber": "826402", "RefComponent": "XX-CSC-AT-IS-H", "RefTitle": "IS-H AT: Änderungen an Bearbeitung der Intensivdaten", "RefUrl": "/notes/826402"}, {"RefNumber": "825920", "RefComponent": "FI-TV", "RefTitle": "Workflow WS01000087 does not exist or is incorrect", "RefUrl": "/notes/825920"}, {"RefNumber": "824630", "RefComponent": "XX-CSC-CH-IS-H", "RefTitle": "IS-H CH: Rechnungsdruck verhindern wenn EDI", "RefUrl": "/notes/824630"}, {"RefNumber": "824539", "RefComponent": "FS-AM-CM", "RefTitle": "Account Management (AM) - TRBK 3.0 - BAFin §24c KWG", "RefUrl": "/notes/824539"}, {"RefNumber": "824473", "RefComponent": "PY-US-PS-BN", "RefTitle": "Savings Plan (403b): Customer Exit for Investments", "RefUrl": "/notes/824473"}, {"RefNumber": "824299", "RefComponent": "BW-WHM-DST-ARC", "RefTitle": "NLS Prototype: Low performance of StorHouse data load", "RefUrl": "/notes/824299"}, {"RefNumber": "823641", "RefComponent": "XX-PART-ISHMED", "RefTitle": "Preregistration, Clinical Order: Create Surgery Preregistrat", "RefUrl": "/notes/823641"}, {"RefNumber": "822882", "RefComponent": "XX-PART-ISHMED", "RefTitle": "Accident Data: Create Movement - Acc. Data not Transferred", "RefUrl": "/notes/822882"}, {"RefNumber": "822871", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/822871"}, {"RefNumber": "822568", "RefComponent": "PY-BR", "RefTitle": "IRRF: 21 years old dependent", "RefUrl": "/notes/822568"}, {"RefNumber": "821434", "RefComponent": "PY-FI", "RefTitle": "VAKEY parameters deleted", "RefUrl": "/notes/821434"}, {"RefNumber": "818359", "RefComponent": "PY-BR", "RefTitle": "Legal change in HBRDIRF0 for year 2005", "RefUrl": "/notes/818359"}, {"RefNumber": "818019", "RefComponent": "XX-CSC-AT-IS-H", "RefTitle": "IS-H AT:Scoring:RNWAT_SCO_MSG_LOAD-neues Feld \"akzeptierbar\"", "RefUrl": "/notes/818019"}, {"RefNumber": "816605", "RefComponent": "CRM-MW-SRV", "RefTitle": "Loss of data during date confirmation processing", "RefUrl": "/notes/816605"}, {"RefNumber": "813671", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/813671"}, {"RefNumber": "813531", "RefComponent": "PY-BR", "RefTitle": "RAIS: Legal change 2005", "RefUrl": "/notes/813531"}, {"RefNumber": "813518", "RefComponent": "XX-CSC-AT-IS-H", "RefTitle": "IS-H AT: Probleme mit \"temporären Adressobjekten\"", "RefUrl": "/notes/813518"}, {"RefNumber": "811311", "RefComponent": "PY-PT", "RefTitle": "HR-PT: Annual Income Declaration - Electronic file", "RefUrl": "/notes/811311"}, {"RefNumber": "809424", "RefComponent": "CRM-MW-SRV", "RefTitle": "Account group field not updated on Mobile Client", "RefUrl": "/notes/809424"}, {"RefNumber": "808269", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/808269"}, {"RefNumber": "808266", "RefComponent": "XX-PART-ISHMED-ORD", "RefTitle": "IS-H/IS-H*MED: Clinical order: Order type - update termination", "RefUrl": "/notes/808266"}, {"RefNumber": "808187", "RefComponent": "XX-PART-ISHMED", "RefTitle": "IS-H*MED: <PERSON><PERSON>. Order: Error Message in Transaction St11", "RefUrl": "/notes/808187"}, {"RefNumber": "80279", "RefComponent": "IS-OIL-BC", "RefTitle": "Order of IS-OIL notes 2.0c/1 & 1.0c/1 on R/3 3.0D/2", "RefUrl": "/notes/80279"}, {"RefNumber": "80132", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/80132"}, {"RefNumber": "801104", "RefComponent": "XX-PART-ISHMED", "RefTitle": "IS-H/IS-H*MED: Clinical Order/Prereg.: Correction Report", "RefUrl": "/notes/801104"}, {"RefNumber": "798398", "RefComponent": "PY-BR", "RefTitle": "HR-BR: Discount percentage of 100% could not be reported", "RefUrl": "/notes/798398"}, {"RefNumber": "796169", "RefComponent": "XX-CSC-IN-SD", "RefTitle": "Transaction J2IUN CENVAT Utilization with Service Tax Credit", "RefUrl": "/notes/796169"}, {"RefNumber": "795492", "RefComponent": "XX-CSC-IN", "RefTitle": "Transaction J2IUN CENVAT Utilization with Service Tax Credit", "RefUrl": "/notes/795492"}, {"RefNumber": "791983", "RefComponent": "FS-CML-PO", "RefTitle": "STM: CML connection to the Data Retention Tool", "RefUrl": "/notes/791983"}, {"RefNumber": "791743", "RefComponent": "XX-CSC-CH-IS-H", "RefTitle": "IS-H CH: ELACH/MEDIDATA - Mehrwertsteuersätze", "RefUrl": "/notes/791743"}, {"RefNumber": "788591", "RefComponent": "XX-PART-ISHMED", "RefTitle": "IS-H*MED:Clin Order: N1SCRM Memory Effect -Correction Report", "RefUrl": "/notes/788591"}, {"RefNumber": "787916", "RefComponent": "XX-PART-ISHMED", "RefTitle": "IS-H*MED: Radiology: Call Radiology Work Station", "RefUrl": "/notes/787916"}, {"RefNumber": "787665", "RefComponent": "IS-H", "RefTitle": "IS-H*MED transfer function master data cl. order", "RefUrl": "/notes/787665"}, {"RefNumber": "786687", "RefComponent": "PY-BR", "RefTitle": "RAIS: reporting brazilian-naturalized employee", "RefUrl": "/notes/786687"}, {"RefNumber": "785863", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/785863"}, {"RefNumber": "784100", "RefComponent": "XX-PROJ-RE", "RefTitle": "Quarter days: Maintenance dialog", "RefUrl": "/notes/784100"}, {"RefNumber": "781711", "RefComponent": "BC-MOB", "RefTitle": "Uploading the Mobile Component Descriptor ISDFPS 1b SP2", "RefUrl": "/notes/781711"}, {"RefNumber": "781265", "RefComponent": "XX-PART-ISHMED", "RefTitle": "IS-H*MED: Service Management: Quick Service Entry", "RefUrl": "/notes/781265"}, {"RefNumber": "779726", "RefComponent": "BC-SEC-USR", "RefTitle": "Authorization object for local user administration", "RefUrl": "/notes/779726"}, {"RefNumber": "779702", "RefComponent": "BC-MOB-MI-SER-ABA", "RefTitle": "Required Customizing on the MI Server for ISDFPS SP2", "RefUrl": "/notes/779702"}, {"RefNumber": "773976", "RefComponent": "XX-CSC-CZ-IS-U", "RefTitle": "Individual Value Adjustment and Write-offs", "RefUrl": "/notes/773976"}, {"RefNumber": "772616", "RefComponent": "MM-IS-IC", "RefTitle": "BCO Idoc INVCON02", "RefUrl": "/notes/772616"}, {"RefNumber": "768257", "RefComponent": "XX-CSC-CH-IS-H", "RefTitle": "IS-H CH: MEDIDATA - Alternativen Leistungscode übermitteln", "RefUrl": "/notes/768257"}, {"RefNumber": "765983", "RefComponent": "BC-DB-DB2", "RefTitle": "DB2/zOS: Reducing number of DSNACCOR REORG recommendations", "RefUrl": "/notes/765983"}, {"RefNumber": "762639", "RefComponent": "IS-H", "RefTitle": "IS-H*MED: plan. board: plan appointmt - movemt for appointmt", "RefUrl": "/notes/762639"}, {"RefNumber": "761420", "RefComponent": "CRM-BF", "RefTitle": "Delta load exchange rates", "RefUrl": "/notes/761420"}, {"RefNumber": "760161", "RefComponent": "IS-H", "RefTitle": "IS-H/IS-H*MED: Clinical order - correction report context", "RefUrl": "/notes/760161"}, {"RefNumber": "758213", "RefComponent": "IS-H", "RefTitle": "IS-H/IS-H*MED: Clinical order - detailed print", "RefUrl": "/notes/758213"}, {"RefNumber": "758054", "RefComponent": "IS-HER-CM-GB", "RefTitle": "UCAS import - Reprocessing logic changes", "RefUrl": "/notes/758054"}, {"RefNumber": "757121", "RefComponent": "XX-PART-ISHMED", "RefTitle": "IS-H*MED:<PERSON><PERSON>.Order: Surg. Admission Order Item Correction", "RefUrl": "/notes/757121"}, {"RefNumber": "756058", "RefComponent": "PA-PA-BR", "RefTitle": "Views of tables T7BR93 and T7BR94 did not allow update", "RefUrl": "/notes/756058"}, {"RefNumber": "755527", "RefComponent": "XX-PART-ISHMED", "RefTitle": "IS-H*MED: Correction Report for Order Types", "RefUrl": "/notes/755527"}, {"RefNumber": "754995", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/754995"}, {"RefNumber": "753315", "RefComponent": "XX-PART-ISHMED", "RefTitle": "IS-H*MED: Authorization for Planning Authority", "RefUrl": "/notes/753315"}, {"RefNumber": "752440", "RefComponent": "XX-CSC-BR", "RefTitle": "Overview about changes for ISS, PIS, COFINS; CSSL, IR, WHT", "RefUrl": "/notes/752440"}, {"RefNumber": "751753", "RefComponent": "XX-PART-ISHMED", "RefTitle": "IS-H*MED: Correction Reports after Migration Execution", "RefUrl": "/notes/751753"}, {"RefNumber": "751119", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/751119"}, {"RefNumber": "750279", "RefComponent": "FI-AP-AP-Q1", "RefTitle": "Withholding tax reporting legal changes for Philippines", "RefUrl": "/notes/750279"}, {"RefNumber": "748933", "RefComponent": "XX-PART-ISHMED", "RefTitle": "IS-H/IS-H*MED: Clin. Order: Invalid Order Initiator/Filler", "RefUrl": "/notes/748933"}, {"RefNumber": "748486", "RefComponent": "XX-PART-ISHMED", "RefTitle": "IS-H*MED: Day-Based Planning: Appointment OU Missing", "RefUrl": "/notes/748486"}, {"RefNumber": "744693", "RefComponent": "PY-SG", "RefTitle": "Incorrect rounding of compulsory CPF for employee", "RefUrl": "/notes/744693"}, {"RefNumber": "742650", "RefComponent": "XX-PART-ISHMED", "RefTitle": "IS-H*MED: <PERSON><PERSON>. Order: Correction Report Tables N1FATTR*", "RefUrl": "/notes/742650"}, {"RefNumber": "742160", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/742160"}, {"RefNumber": "74099", "RefComponent": "XX-CSC-TH", "RefTitle": "Additional Info: Installation Thai Version 30D/1", "RefUrl": "/notes/74099"}, {"RefNumber": "738595", "RefComponent": "BC-DB-INF-CCM", "RefTitle": "INF-217 in RSINF076 after DB-upgrade to 9.40", "RefUrl": "/notes/738595"}, {"RefNumber": "736945", "RefComponent": "PY-TH", "RefTitle": "HTHCSSD1 - Code for the prefix in download format", "RefUrl": "/notes/736945"}, {"RefNumber": "735508", "RefComponent": "XX-CSC-CH-IS-H", "RefTitle": "IS-H CH: Leistungsregel C18 - Umschlüsselung \"in sich\"", "RefUrl": "/notes/735508"}, {"RefNumber": "734636", "RefComponent": "XX-CSC-CH-IS-H", "RefTitle": "IS-H CH: Rechnungsformular V4.0 - NIF-Nummer", "RefUrl": "/notes/734636"}, {"RefNumber": "733917", "RefComponent": "XX-PART-ISHMED", "RefTitle": "IS-H*MED: Planning Grid: Appointments/Tab Pages not Displaye", "RefUrl": "/notes/733917"}, {"RefNumber": "732660", "RefComponent": "XX-CSC-CH-IS-H", "RefTitle": "IS-H CH: Rechung Version 4.0 4.63B/19", "RefUrl": "/notes/732660"}, {"RefNumber": "731755", "RefComponent": "XX-CSC-CH-IS-H", "RefTitle": "IS-H CH: Summe TARMED AL in der Rechnung Version 4.0", "RefUrl": "/notes/731755"}, {"RefNumber": "731753", "RefComponent": "PY-PT", "RefTitle": "HR-PT: Article 220º \"Efeitos da suspensao do contrato ...\"", "RefUrl": "/notes/731753"}, {"RefNumber": "731503", "RefComponent": "XX-CSC-CH-IS-H", "RefTitle": "IS-H CH: Summe TARMED AL in der Rechnung TARMED", "RefUrl": "/notes/731503"}, {"RefNumber": "7312", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/7312"}, {"RefNumber": "730328", "RefComponent": "XX-CSC-CH-IS-H", "RefTitle": "IS-H CH: Int. Skalierungsfaktor Rechnung TARMED", "RefUrl": "/notes/730328"}, {"RefNumber": "730327", "RefComponent": "XX-CSC-CH-IS-H", "RefTitle": "IS-H CH: Int. Skalierungsfaktor Rechnung Version 4.0", "RefUrl": "/notes/730327"}, {"RefNumber": "727102", "RefComponent": "XX-CSC-BR", "RefTitle": "New DDIC Objects for ISS, PIS, COFINS; CSSL", "RefUrl": "/notes/727102"}, {"RefNumber": "724511", "RefComponent": "XX-CSC-FR", "RefTitle": "French Central Bank Report C80", "RefUrl": "/notes/724511"}, {"RefNumber": "722215", "RefComponent": "XX-CSC-CH-IS-H", "RefTitle": "IS-H CH: TARMED Rechnung mit filled Square", "RefUrl": "/notes/722215"}, {"RefNumber": "721081", "RefComponent": "PY-PT", "RefTitle": "HR-PT:Legal Changes to the Social Security number(11 digits)", "RefUrl": "/notes/721081"}, {"RefNumber": "718268", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/718268"}, {"RefNumber": "718103", "RefComponent": "XX-CSC-CH-IS-H", "RefTitle": "CH: Erweiterungen für Assistenzärzte und Prozente", "RefUrl": "/notes/718103"}, {"RefNumber": "717747", "RefComponent": "XX-CSC-CH-IS-H", "RefTitle": "IS-H CH: Aufschlüsselung Gesamtbetrag und 5-Rappenrundung", "RefUrl": "/notes/717747"}, {"RefNumber": "717565", "RefComponent": "XX-CSC-CH-IS-H", "RefTitle": "IS-H CH: Leistungsregeln - Anpassen an akt.Entwicklungsstand", "RefUrl": "/notes/717565"}, {"RefNumber": "713355", "RefComponent": "PY-AT", "RefTitle": "MV-Übertragungsbetrag - Eintragung auf Lohnzettel", "RefUrl": "/notes/713355"}, {"RefNumber": "712898", "RefComponent": "MM-PUR-GF-BW", "RefTitle": "Performance problems to extract the purchasing information", "RefUrl": "/notes/712898"}, {"RefNumber": "712368", "RefComponent": "PY-PT", "RefTitle": "HR-PT: New table T5P1P for IRS since 2004", "RefUrl": "/notes/712368"}, {"RefNumber": "712333", "RefComponent": "PY-PT", "RefTitle": "HR-PT: Upload of IRS tables 2004 into T5P1P", "RefUrl": "/notes/712333"}, {"RefNumber": "710689", "RefComponent": "XX-CSC-CH-IS-H", "RefTitle": "IS-H CH: BAPI-Leistungsschnittstelle: Fehlerhandling Vers. 2", "RefUrl": "/notes/710689"}, {"RefNumber": "709148", "RefComponent": "PY-BR", "RefTitle": "HBRPAYR0: Does not print the results in background", "RefUrl": "/notes/709148"}, {"RefNumber": "708384", "RefComponent": "PY-AT", "RefTitle": "Nachtrag für RPCL16A2 zum Jahreswechsel 2003/04", "RefUrl": "/notes/708384"}, {"RefNumber": "708330", "RefComponent": "XX-CSC-CH-IS-H", "RefTitle": "ISH CH Leistungsregel Abrechenbarkeit Materialien", "RefUrl": "/notes/708330"}, {"RefNumber": "707539", "RefComponent": "PY-AT", "RefTitle": "Neue Dienstgeberkontonummern Finanz ausgegeben -> Anpassung", "RefUrl": "/notes/707539"}, {"RefNumber": "707168", "RefComponent": "FS-AM-PR-CD", "RefTitle": "Reference interest rates: Authorization object is missing", "RefUrl": "/notes/707168"}, {"RefNumber": "706262", "RefComponent": "XX-CSC-CH-IS-H", "RefTitle": "IS-H CH: ELACH - Medidata-Rechnungs-Antwort", "RefUrl": "/notes/706262"}, {"RefNumber": "704587", "RefComponent": "XX-CSC-CH-IS-H", "RefTitle": "IS-H CH: ELACH - Medidata-Rechnungs-Antwort", "RefUrl": "/notes/704587"}, {"RefNumber": "702496", "RefComponent": "PY-AT", "RefTitle": "SV- Beitragsgruppen: Customizing unvollständig", "RefUrl": "/notes/702496"}, {"RefNumber": "702367", "RefComponent": "XX-CSC-CH-IS-H", "RefTitle": "IS-H CH: Rechnungsformular TARMED", "RefUrl": "/notes/702367"}, {"RefNumber": "701392", "RefComponent": "PY-US-TR", "RefTitle": "W-2: New MMREF-1 layout for Port Huron, MI", "RefUrl": "/notes/701392"}, {"RefNumber": "700160", "RefComponent": "XX-CSC-CH-IS-H", "RefTitle": "IS-H CH: Rechnungsformular TARMED", "RefUrl": "/notes/700160"}, {"RefNumber": "699131", "RefComponent": "XX-CSC-CH-IS-H", "RefTitle": "IS-H CH: Rechnungsformular TARMED", "RefUrl": "/notes/699131"}, {"RefNumber": "698677", "RefComponent": "PY-AT", "RefTitle": "Neue Dienstgeberkontonummern SV ausgegeben -> Anpassungen", "RefUrl": "/notes/698677"}, {"RefNumber": "697600", "RefComponent": "PY-SG", "RefTitle": "PY-SG:Incorrect alignment of IR8A/IR8S forms for year 2003", "RefUrl": "/notes/697600"}, {"RefNumber": "696846", "RefComponent": "PM-EQM-EQ", "RefTitle": "PMAA: Recording time segments in the asset", "RefUrl": "/notes/696846"}, {"RefNumber": "696417", "RefComponent": "IS-H-CM-OUT", "RefTitle": "IS-H §21:Neuerungen bei RNAP21K01/F01 zu 4.63B/18 u. 4.71/10", "RefUrl": "/notes/696417"}, {"RefNumber": "695686", "RefComponent": "PSM-FM-IS", "RefTitle": "Drilldown reporting: Error reading cluster table COIX ...", "RefUrl": "/notes/695686"}, {"RefNumber": "69455", "RefComponent": "SV-SMG-SDD", "RefTitle": "Servicetools for Applications ST-A/PI (ST14, RTCCTOOL, ST12)", "RefUrl": "/notes/69455"}, {"RefNumber": "692628", "RefComponent": "XX-PART-ISHMED", "RefTitle": "IS-H*MED: Planning Grid - Plan Appointments for Midnight (00", "RefUrl": "/notes/692628"}, {"RefNumber": "692596", "RefComponent": "XX-CSC-AT-IS-H", "RefTitle": "AT: Erfassung von Intensivdaten für Datensatz Intensiv", "RefUrl": "/notes/692596"}, {"RefNumber": "692178", "RefComponent": "PY-NO", "RefTitle": "New forms for 2003", "RefUrl": "/notes/692178"}, {"RefNumber": "691711", "RefComponent": "XX-CSC-CH-IS-H", "RefTitle": "IS-H CH: ELACH - Medidata-Anpassungen", "RefUrl": "/notes/691711"}, {"RefNumber": "689598", "RefComponent": "PY-AT", "RefTitle": "ELDA-Lohnzettel Finanz: Feld FANRL für Finanzämter 01 - 09", "RefUrl": "/notes/689598"}, {"RefNumber": "689549", "RefComponent": "PY-SG", "RefTitle": "PY-SG:New limit for additional wages w.e.f. 01.01.2004", "RefUrl": "/notes/689549"}, {"RefNumber": "689238", "RefComponent": "PY-US-TR", "RefTitle": "NY 4th QTR SAPscript: Incorrect amount in Line #12", "RefUrl": "/notes/689238"}, {"RefNumber": "688054", "RefComponent": "PY-PT", "RefTitle": "HR-PT: Overtime Communication - UNION", "RefUrl": "/notes/688054"}, {"RefNumber": "687704", "RefComponent": "XX-CSC-CH-IS-H", "RefTitle": "IS-H CH: Rechnungsformular TARMED", "RefUrl": "/notes/687704"}, {"RefNumber": "687491", "RefComponent": "PY-PT", "RefTitle": "HR-PT: Corrections to generic statistical (RPCGSRP0)", "RefUrl": "/notes/687491"}, {"RefNumber": "683090", "RefComponent": "IS-H", "RefTitle": "Erweiterung TNFPSE ab 1.1.2004 - Regeln FP/SE", "RefUrl": "/notes/683090"}, {"RefNumber": "682286", "RefComponent": "PY-PT", "RefTitle": "HR-PT: Overtime Record - RPCOVRP0", "RefUrl": "/notes/682286"}, {"RefNumber": "681090", "RefComponent": "FI-TV-PL", "RefTitle": "IATA locations not current", "RefUrl": "/notes/681090"}, {"RefNumber": "678364", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/678364"}, {"RefNumber": "677661", "RefComponent": "XX-CSC-CH-IS-H", "RefTitle": "IS-H CH: (+)%-Zuschlagsleistungen (Bastarde)", "RefUrl": "/notes/677661"}, {"RefNumber": "676589", "RefComponent": "XX-CSC-CH-IS-H", "RefTitle": "IS-H CH: XML-Leistungsschnittstelle", "RefUrl": "/notes/676589"}, {"RefNumber": "673550", "RefComponent": "CRM-IC", "RefTitle": "Error in CL_CRM_IC_TEXTEDIT", "RefUrl": "/notes/673550"}, {"RefNumber": "673207", "RefComponent": "XX-PART-ISHMED", "RefTitle": "IS-H/IS-H*MED: Priority of Prereg. Types Ignored", "RefUrl": "/notes/673207"}, {"RefNumber": "672949", "RefComponent": "PY-PT", "RefTitle": "HR-PT: Legal changes in Personnel Summary", "RefUrl": "/notes/672949"}, {"RefNumber": "671362", "RefComponent": "PY-BR", "RefTitle": "HBRRTER1: Legal change for printing differences", "RefUrl": "/notes/671362"}, {"RefNumber": "666915", "RefComponent": "XX-CSC-CH-IS-H", "RefTitle": "IS-H CH: RNWCHNTPK00 - <PERSON><PERSON>. \"Code Lim. Maxregel\"", "RefUrl": "/notes/666915"}, {"RefNumber": "663952", "RefComponent": "PY-US-TR", "RefTitle": "US Tax Reporter - YE 2003 Phase II", "RefUrl": "/notes/663952"}, {"RefNumber": "662341", "RefComponent": "CO-OM-OPA-B", "RefTitle": "Incorrect detail planning layouts w/ Funds Management", "RefUrl": "/notes/662341"}, {"RefNumber": "660836", "RefComponent": "PY-US-RP", "RefTitle": "CCR: User-exits in Cost Center Report.", "RefUrl": "/notes/660836"}, {"RefNumber": "658391", "RefComponent": "RE-IT", "RefTitle": "§ 15a Purchases Tax Law - new amendment in Germany", "RefUrl": "/notes/658391"}, {"RefNumber": "657549", "RefComponent": "PY-PT", "RefTitle": "HR-PT: TemSe functionality for RPSSESP0", "RefUrl": "/notes/657549"}, {"RefNumber": "656651", "RefComponent": "PY-AR", "RefTitle": "Retroactive Accounting and differences for SSO and TAX", "RefUrl": "/notes/656651"}, {"RefNumber": "656437", "RefComponent": "CRM-BF-SVY", "RefTitle": "CRM Survey Tool: Unicode ability in CRM 3.1", "RefUrl": "/notes/656437"}, {"RefNumber": "656281", "RefComponent": "CO-OM-OPA-B", "RefTitle": "No branch from structure planning into planning of SKF", "RefUrl": "/notes/656281"}, {"RefNumber": "650326", "RefComponent": "PY-PT", "RefTitle": "HR-PT: Complete company/subareas data in printed version", "RefUrl": "/notes/650326"}, {"RefNumber": "649978", "RefComponent": "PY-PT", "RefTitle": "HR-PT: Legal changes in Structured Employment Survey report", "RefUrl": "/notes/649978"}, {"RefNumber": "647579", "RefComponent": "XX-PART-ISHMED-ORD", "RefTitle": "IS-H*MED: Messages for closed OU", "RefUrl": "/notes/647579"}, {"RefNumber": "646474", "RefComponent": "IS-H", "RefTitle": "IS-H: DE: Customizingänderungen zu 4.63B/13", "RefUrl": "/notes/646474"}, {"RefNumber": "643192", "RefComponent": "XX-CSC-CH-IS-H", "RefTitle": "IS-H CH: PATREC-Statistik - Verschluesselungssoftware", "RefUrl": "/notes/643192"}, {"RefNumber": "641507", "RefComponent": "SD-SLS-GF-RE", "RefTitle": "Display variants: Incorrect display of crcy & qty fields", "RefUrl": "/notes/641507"}, {"RefNumber": "640046", "RefComponent": "XX-PART-ISHMED", "RefTitle": "IS-H*MED: Planning Grid Call - Short Dump", "RefUrl": "/notes/640046"}, {"RefNumber": "63841", "RefComponent": "IS-HT-SW", "RefTitle": "Release Information of  R/3 Add-On IS-SW & IS-HT", "RefUrl": "/notes/63841"}, {"RefNumber": "637253", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/637253"}, {"RefNumber": "632906", "RefComponent": "XX-PART-ISHMED", "RefTitle": "IS-H*MED: Incorrect Planning Object", "RefUrl": "/notes/632906"}, {"RefNumber": "632713", "RefComponent": "XX-CSC-CH-IS-H", "RefTitle": "IS-H CH: RNWCHNTPK00 - Einspielen TARMED-Leistungsstamm", "RefUrl": "/notes/632713"}, {"RefNumber": "629828", "RefComponent": "XX-PART-ISHMED", "RefTitle": "IS-H*MED: Support of Hebra<PERSON> Fonts", "RefUrl": "/notes/629828"}, {"RefNumber": "628624", "RefComponent": "SCM-APO-MD-TL", "RefTitle": "TL-TRANS: Problems with BUS11201 and BUS10009 BAPI objects", "RefUrl": "/notes/628624"}, {"RefNumber": "627033", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/627033"}, {"RefNumber": "624788", "RefComponent": "XX-PART-ISHMED", "RefTitle": "IS-H*MED: Planning Grid - More Than 100 Appts", "RefUrl": "/notes/624788"}, {"RefNumber": "622400", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/622400"}, {"RefNumber": "620283", "RefComponent": "PY-PT", "RefTitle": "HR-PT: New tax report RPCTAXP0", "RefUrl": "/notes/620283"}, {"RefNumber": "618885", "RefComponent": "PSM-FG-BL", "RefTitle": "Correction for note 606673", "RefUrl": "/notes/618885"}, {"RefNumber": "617096", "RefComponent": "BC-SRV-ASF-CHD", "RefTitle": "CD: Archiving programs for archiving object CHANGEDOCU", "RefUrl": "/notes/617096"}, {"RefNumber": "616463", "RefComponent": "BW-BEX-ET-WEB", "RefTitle": "Inconsistent 0IMODE_CHART_LIB and 0PIE_CHART_LIB templates", "RefUrl": "/notes/616463"}, {"RefNumber": "615597", "RefComponent": "PSM-FM-BCS", "RefTitle": "Adding Report Writer functionality to BCS", "RefUrl": "/notes/615597"}, {"RefNumber": "612965", "RefComponent": "PM-EQM-SF-MPC", "RefTitle": "RMP(EA200): Retrofit reference measuring points", "RefUrl": "/notes/612965"}, {"RefNumber": "610422", "RefComponent": "BC-SRV-ASF-CHD", "RefTitle": "CD:Archiving obj. CHANGEDOCU not complete in standard system", "RefUrl": "/notes/610422"}, {"RefNumber": "607234", "RefComponent": "PY-PT", "RefTitle": "HR-PT: Corrections to Leave annual summary (RPLESUP0)", "RefUrl": "/notes/607234"}, {"RefNumber": "604962", "RefComponent": "IS-H-CM-OUT", "RefTitle": "IS-H DE: P21 - Unterstützung individueller Datenermittlung", "RefUrl": "/notes/604962"}, {"RefNumber": "604784", "RefComponent": "IS-H-IS-GMS", "RefTitle": "IS-H DE: KHStatV 2002 - Vorabauslieferung", "RefUrl": "/notes/604784"}, {"RefNumber": "602415", "RefComponent": "PY-JP", "RefTitle": "LC2003 : Gross remuneration system vol.3 correction files", "RefUrl": "/notes/602415"}, {"RefNumber": "602168", "RefComponent": "PY-AT", "RefTitle": "Neu: Report RPCL16A2 für Lohnzettel Finanz überarbeitet", "RefUrl": "/notes/602168"}, {"RefNumber": "601765", "RefComponent": "FI-SL-IS-A", "RefTitle": "Configuration of report/report interface incomplete", "RefUrl": "/notes/601765"}, {"RefNumber": "600229", "RefComponent": "PY-DE-PS-ZV", "RefTitle": "ZV-Vorabtransport: DATÜV-ZVE, ATZ, Nettozusage,...", "RefUrl": "/notes/600229"}, {"RefNumber": "593469", "RefComponent": "BC-DB-DB2", "RefTitle": "DB2/390: Usage of global TEMP tables and related problems", "RefUrl": "/notes/593469"}, {"RefNumber": "588726", "RefComponent": "CO-OM-CCA-B", "RefTitle": "Copy programs for scheduling as a job", "RefUrl": "/notes/588726"}, {"RefNumber": "586506", "RefComponent": "FI-AP-AP-Q1", "RefTitle": "1099-MISC: SAPScript form F_RFW1099M_2001 inconsistent", "RefUrl": "/notes/586506"}, {"RefNumber": "585090", "RefComponent": "PSM-FM-BU-BF", "RefTitle": "Table entries for validation in FM", "RefUrl": "/notes/585090"}, {"RefNumber": "584058", "RefComponent": "RE-RT-SC", "RefTitle": "SCS according to Czech law", "RefUrl": "/notes/584058"}, {"RefNumber": "581432", "RefComponent": "IS-H", "RefTitle": "IS-H AT: Scoring - Änderungen und Korrekturen", "RefUrl": "/notes/581432"}, {"RefNumber": "581269", "RefComponent": "BC-DB-DB2", "RefTitle": "Scheduling via DB13C in target DB2/390 system fails", "RefUrl": "/notes/581269"}, {"RefNumber": "578961", "RefComponent": "XX-CSC-AR", "RefTitle": "AR: Credit Invoice - Transports and Correction Instructions", "RefUrl": "/notes/578961"}, {"RefNumber": "576295", "RefComponent": "BW-WHM-DST", "RefTitle": "P9:P29:Content transfer:InfoAreas:memory problems", "RefUrl": "/notes/576295"}, {"RefNumber": "574872", "RefComponent": "BC-UPG-NA", "RefTitle": "Cannot disassemble data file with version", "RefUrl": "/notes/574872"}, {"RefNumber": "572282", "RefComponent": "PY-MX", "RefTitle": "Sapserv for note 561686, Retrocalculation for Mexico", "RefUrl": "/notes/572282"}, {"RefNumber": "570920", "RefComponent": "BC-BW", "RefTitle": "\"I_S_HEADER3\" function parameter is not known", "RefUrl": "/notes/570920"}, {"RefNumber": "567422", "RefComponent": "BC-CTS-ORG", "RefTitle": "\"Object locked by upgrade\" error message", "RefUrl": "/notes/567422"}, {"RefNumber": "564784", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/564784"}, {"RefNumber": "560807", "RefComponent": "PSM-FG", "RefTitle": "Corrections to advanced delivery of report SF-224", "RefUrl": "/notes/560807"}, {"RefNumber": "560720", "RefComponent": "BC-CTS-ORG", "RefTitle": "Error messages in German when logged on in English", "RefUrl": "/notes/560720"}, {"RefNumber": "560411", "RefComponent": "BC-CCM-MON-TUN", "RefTitle": "Syntax error in the program SAPLSTMO", "RefUrl": "/notes/560411"}, {"RefNumber": "560382", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/560382"}, {"RefNumber": "558714", "RefComponent": "FI-FM-IS", "RefTitle": "Error reading cluster table COIX for report &", "RefUrl": "/notes/558714"}, {"RefNumber": "557148", "RefComponent": "BC-BW", "RefTitle": "Executing conversion exits", "RefUrl": "/notes/557148"}, {"RefNumber": "557033", "RefComponent": "PSM-FG", "RefTitle": "Advance delivery of FACTS I/II reports with 2002 requirments", "RefUrl": "/notes/557033"}, {"RefNumber": "556669", "RefComponent": "BC-BW", "RefTitle": "DSAA transport object", "RefUrl": "/notes/556669"}, {"RefNumber": "551944", "RefComponent": "PSM-FG", "RefTitle": "Corrections to advance delivery of report SF-224", "RefUrl": "/notes/551944"}, {"RefNumber": "551104", "RefComponent": "BC-DB-DB2", "RefTitle": "DB2/390: SAPLS_CCDSNU_DB2 terminates due to a timeout", "RefUrl": "/notes/551104"}, {"RefNumber": "550544", "RefComponent": "CA-EUR-CNV", "RefTitle": "IS-H AT: Euro changeover: Missing IS-H tables for AT and CH", "RefUrl": "/notes/550544"}, {"RefNumber": "549174", "RefComponent": "BC-DB-DB2", "RefTitle": "Transaction DB03 no longer supported for DB2/390", "RefUrl": "/notes/549174"}, {"RefNumber": "548897", "RefComponent": "PY-BR", "RefTitle": "HRMS-BR Vacation Provision", "RefUrl": "/notes/548897"}, {"RefNumber": "547156", "RefComponent": "PSM-FG", "RefTitle": "Advance delivery of Report SF-224 and Enhancement of BL", "RefUrl": "/notes/547156"}, {"RefNumber": "546624", "RefComponent": "CRM-BE-OC", "RefTitle": "Actions cannot be processed", "RefUrl": "/notes/546624"}, {"RefNumber": "546417", "RefComponent": "PY-MX", "RefTitle": "sapserv for note note.0545905", "RefUrl": "/notes/546417"}, {"RefNumber": "545400", "RefComponent": "IS-A-RL", "RefTitle": "RL71F:RL_ACCSTA displays Quantity in wrong decimal format", "RefUrl": "/notes/545400"}, {"RefNumber": "542913", "RefComponent": "FI-AP-AP-Q1", "RefTitle": "RFKQST00 - Collective note for error in withholding tax rep.", "RefUrl": "/notes/542913"}, {"RefNumber": "540025", "RefComponent": "SD-SLS", "RefTitle": "No agent determination during WF for credit memo request", "RefUrl": "/notes/540025"}, {"RefNumber": "535915", "RefComponent": "SCM-APO-CA-PER", "RefTitle": "PFM tool advance delivery", "RefUrl": "/notes/535915"}, {"RefNumber": "532793", "RefComponent": "SD-BF-TP", "RefTitle": "Text determination access sequences are missing", "RefUrl": "/notes/532793"}, {"RefNumber": "528179", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/528179"}, {"RefNumber": "526438", "RefComponent": "BW-BEX-ET-WEB-AD", "RefTitle": "Internal error with communication with the IGS", "RefUrl": "/notes/526438"}, {"RefNumber": "526117", "RefComponent": "BC-BMT-WFM", "RefTitle": "WF builder: Workflow template does not exist in Version xxxx", "RefUrl": "/notes/526117"}, {"RefNumber": "520745", "RefComponent": "CRM-BF-CFG", "RefTitle": "External CATServer: CRM 3.0 SP09 additional transport", "RefUrl": "/notes/520745"}, {"RefNumber": "519068", "RefComponent": "SD-SLS", "RefTitle": "Workflow WS 20000009 and WS 20000019 do not exist", "RefUrl": "/notes/519068"}, {"RefNumber": "518061", "RefComponent": "FI-AP-AP-Q1", "RefTitle": "Modelo 770 year 2002 (Italy)", "RefUrl": "/notes/518061"}, {"RefNumber": "516513", "RefComponent": "BC-CTS-TLS", "RefTitle": "Incomplete transport of CDAT objects", "RefUrl": "/notes/516513"}, {"RefNumber": "514894", "RefComponent": "CRM-MD-CON-IF", "RefTitle": "Data type changes of MXWRT/GKWRT in table CDBC_P_CN_LIMI", "RefUrl": "/notes/514894"}, {"RefNumber": "513455", "RefComponent": "XX-CSC-NL", "RefTitle": "Dutch Chain Liability (Wet Ketenaansprakelijkheid)", "RefUrl": "/notes/513455"}, {"RefNumber": "512463", "RefComponent": "CRM-MD-PCT", "RefTitle": "CRM product catalog - Individual replication", "RefUrl": "/notes/512463"}, {"RefNumber": "510859", "RefComponent": "FI-AP-AP-Q1", "RefTitle": "Missing or wrong messages of message class FR", "RefUrl": "/notes/510859"}, {"RefNumber": "510480", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/510480"}, {"RefNumber": "507824", "RefComponent": "BC-DB-DB2", "RefTitle": "DB2/390 V7: Real Time Statistics and DSNACCOR", "RefUrl": "/notes/507824"}, {"RefNumber": "507605", "RefComponent": "FS-CD", "RefTitle": "Fed wire Customer Transport", "RefUrl": "/notes/507605"}, {"RefNumber": "504304", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/504304"}, {"RefNumber": "503202", "RefComponent": "CRM-MSA-BP", "RefTitle": "R/3 customer hierarchy not visible at the CRM mobile client", "RefUrl": "/notes/503202"}, {"RefNumber": "499485", "RefComponent": "PY-MX", "RefTitle": "SAPServe transport because of annual processes", "RefUrl": "/notes/499485"}, {"RefNumber": "499100", "RefComponent": "PY-MX", "RefTitle": "sapserv for corrections of \"procesos anuales\"", "RefUrl": "/notes/499100"}, {"RefNumber": "498973", "RefComponent": "IS-H-CM-OUT", "RefTitle": "IS-H AT: ELDA - <PERSON><PERSON>en (vorgezogene Entwicklung)", "RefUrl": "/notes/498973"}, {"RefNumber": "495090", "RefComponent": "FI-AA-IS", "RefTitle": "RAGITT_ALV01: Report variant SAP&001 incorrect", "RefUrl": "/notes/495090"}, {"RefNumber": "494255", "RefComponent": "PY-MX", "RefTitle": "sapserv for note 0492203", "RefUrl": "/notes/494255"}, {"RefNumber": "493664", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/493664"}, {"RefNumber": "49365", "RefComponent": "XX-INT-FA-MAKE", "RefTitle": "iSeries: Applying a patch", "RefUrl": "/notes/49365"}, {"RefNumber": "492716", "RefComponent": "SCM-APO-MD-TL", "RefTitle": "TL EXIT: User exit within the displacement calculation", "RefUrl": "/notes/492716"}, {"RefNumber": "488627", "RefComponent": "BC-UPG-OCS", "RefTitle": "Test import has failed for SAPKE46C36", "RefUrl": "/notes/488627"}, {"RefNumber": "486617", "RefComponent": "PY-MX", "RefTitle": "sapserv for note 0483697", "RefUrl": "/notes/486617"}, {"RefNumber": "486096", "RefComponent": "BC-SEC-DIR", "RefTitle": "Consolidating DBOBJ and LDAPPROPx", "RefUrl": "/notes/486096"}, {"RefNumber": "485137", "RefComponent": "IS-OIL-PRA-PRD", "RefTitle": "QCI Failure calling MP Vols for gas", "RefUrl": "/notes/485137"}, {"RefNumber": "484643", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/484643"}, {"RefNumber": "483431", "RefComponent": "BC-CTS-TLS", "RefTitle": "Support Package incorrectly imported from sapservX using TMS", "RefUrl": "/notes/483431"}, {"RefNumber": "481055", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/481055"}, {"RefNumber": "480671", "RefComponent": "BC-I18", "RefTitle": "The Text Language Flag of LANG Fields", "RefUrl": "/notes/480671"}, {"RefNumber": "460205", "RefComponent": "XX-PROJ-GSC-ECLI", "RefTitle": "CPCC Service Pack 2.0.50", "RefUrl": "/notes/460205"}, {"RefNumber": "459562", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/459562"}, {"RefNumber": "459520", "RefComponent": "FI-AP-AP-Q1", "RefTitle": "1099-MISC report RFW1099M for USA", "RefUrl": "/notes/459520"}, {"RefNumber": "459410", "RefComponent": "IS-H", "RefTitle": "Erweiterung TNFPSE ab 1.1.2002 - Regeln FP/SE", "RefUrl": "/notes/459410"}, {"RefNumber": "459052", "RefComponent": "FI-AP-AP-B1", "RefTitle": "RFFOAT_A: Modif.of report for foreign payment trans.Austria", "RefUrl": "/notes/459052"}, {"RefNumber": "458910", "RefComponent": "FI-AP-AP-B1", "RefTitle": "DMEE: Changes to austrian format tree V3", "RefUrl": "/notes/458910"}, {"RefNumber": "457721", "RefComponent": "TR-TM-AC-SE", "RefTitle": "§341b German Coml Code (HGB) security valuatn advance trnsp.", "RefUrl": "/notes/457721"}, {"RefNumber": "457029", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/457029"}, {"RefNumber": "455423", "RefComponent": "XX-PROJ-PM", "RefTitle": "§341b HGB Securities Valuation Advance correction", "RefUrl": "/notes/455423"}, {"RefNumber": "454478", "RefComponent": "FI-AP-AP-Q1", "RefTitle": "New 1042 record format and form for year 2002", "RefUrl": "/notes/454478"}, {"RefNumber": "453322", "RefComponent": "CRM-BTX-ANA", "RefTitle": "Activity Monitor: Standard variants do not exist", "RefUrl": "/notes/453322"}, {"RefNumber": "452635", "RefComponent": "IM-FA-IS", "RefTitle": "Problems when executing a report with selection variants", "RefUrl": "/notes/452635"}, {"RefNumber": "452210", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/452210"}, {"RefNumber": "451134", "RefComponent": "IS-OIL-DS-TDP", "RefTitle": "Mass update of contract prices and external ED pricing key", "RefUrl": "/notes/451134"}, {"RefNumber": "451012", "RefComponent": "BC-BW", "RefTitle": "BW 1.2B: cannot activate rules for master data", "RefUrl": "/notes/451012"}, {"RefNumber": "450479", "RefComponent": "CRM-BTX", "RefTitle": "Performance: Incorrect frequency of calls in CRMC_EVENT_CALL", "RefUrl": "/notes/450479"}, {"RefNumber": "447943", "RefComponent": "FI-FM-AF-AR", "RefTitle": "Archiving for Funds Management (FMICOIT)", "RefUrl": "/notes/447943"}, {"RefNumber": "446363", "RefComponent": "CRM-BTX", "RefTitle": "Event Handler: Performance improvement", "RefUrl": "/notes/446363"}, {"RefNumber": "445988", "RefComponent": "CRM", "RefTitle": "Time stamps are not converted into date + time", "RefUrl": "/notes/445988"}, {"RefNumber": "444731", "RefComponent": "CRM-BF", "RefTitle": "Correction of table CRMC_EVENT_CALL", "RefUrl": "/notes/444731"}, {"RefNumber": "444641", "RefComponent": "SCM-APO-INT-SLS", "RefTitle": "Correction of incorrect sales order requirements with APO", "RefUrl": "/notes/444641"}, {"RefNumber": "444241", "RefComponent": "IS-B-BCA-PT-CV", "RefTitle": "Currency conversion in BCA", "RefUrl": "/notes/444241"}, {"RefNumber": "444187", "RefComponent": "FS-CD", "RefTitle": "BDT-Customizing after installation of INSURANCE Add-On", "RefUrl": "/notes/444187"}, {"RefNumber": "441965", "RefComponent": "XX-PROJ-CS-PP", "RefTitle": "Missing entries in control tables and FCODE table", "RefUrl": "/notes/441965"}, {"RefNumber": "440954", "RefComponent": "BC-DB-DB2", "RefTitle": "DB2/390: MCOD aspects in CCMS", "RefUrl": "/notes/440954"}, {"RefNumber": "438813", "RefComponent": "BC-SRV-SCR", "RefTitle": "RSTXTCAT variant SAP&EXTEND_SEL: blank fields", "RefUrl": "/notes/438813"}, {"RefNumber": "437554", "RefComponent": "IS-B-BCA-PT-CV", "RefTitle": "BTE currency conversion:limit rounding", "RefUrl": "/notes/437554"}, {"RefNumber": "437013", "RefComponent": "PSM-FM-IS", "RefTitle": "Import and activate GASB 34 report painter report", "RefUrl": "/notes/437013"}, {"RefNumber": "434096", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/434096"}, {"RefNumber": "433934", "RefComponent": "CRM-MD-CON-IF", "RefTitle": "Transfer ddic info for condition objects to mobile client", "RefUrl": "/notes/433934"}, {"RefNumber": "433876", "RefComponent": "IS-OIL-DS-MCO", "RefTitle": "Goods Recipient field is disabled in MIGO transaction", "RefUrl": "/notes/433876"}, {"RefNumber": "428398", "RefComponent": "XX-PROJ-SDP-004-CRM", "RefTitle": "DIVA ICP1/2: Preliminary Transport III (07/2001)", "RefUrl": "/notes/428398"}, {"RefNumber": "428368", "RefComponent": "FI-AP-AP-D", "RefTitle": "RFEPOS00: Syntax error during upgrade to Release 4.6", "RefUrl": "/notes/428368"}, {"RefNumber": "425488", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/425488"}, {"RefNumber": "423566", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/423566"}, {"RefNumber": "423419", "RefComponent": "IS-B", "RefTitle": "Additional info about 4.6C upgrade BANKING 4.63/CFM 2.0", "RefUrl": "/notes/423419"}, {"RefNumber": "423364", "RefComponent": "IS-B", "RefTitle": "Additional info about installing Banking 4.63 / CFM 2.0", "RefUrl": "/notes/423364"}, {"RefNumber": "422619", "RefComponent": "BC-FES-ITS", "RefTitle": "Enabling SAP Systems for ITS Flow-Logic", "RefUrl": "/notes/422619"}, {"RefNumber": "422291", "RefComponent": "BC-SRV-SCR", "RefTitle": "RSTXTCAT variant SAP&SHORT_SEL: blank field", "RefUrl": "/notes/422291"}, {"RefNumber": "420114", "RefComponent": "BW-BEX-ET", "RefTitle": "wdttree - type mismatch: Error in filter value selection", "RefUrl": "/notes/420114"}, {"RefNumber": "419321", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/419321"}, {"RefNumber": "418898", "RefComponent": "FI-AP-AP-B1", "RefTitle": "Foreign payments with LUM2 format, Finland", "RefUrl": "/notes/418898"}, {"RefNumber": "418880", "RefComponent": "BW-BEX-ET-MAP", "RefTitle": "No data display in BEx Map although query contains data", "RefUrl": "/notes/418880"}, {"RefNumber": "417148", "RefComponent": "PSM-FG-IS", "RefTitle": "Reports for FACTS I not available in release 462", "RefUrl": "/notes/417148"}, {"RefNumber": "417092", "RefComponent": "FI-AP-AP-Q1", "RefTitle": "RFKQST00 - Error in withholding tax report for Italy", "RefUrl": "/notes/417092"}, {"RefNumber": "414254", "RefComponent": "XX-CSC-KR", "RefTitle": "Migration Tool for Korean Add-on Tax Invoices", "RefUrl": "/notes/414254"}, {"RefNumber": "413885", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/413885"}, {"RefNumber": "411850", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/411850"}, {"RefNumber": "411798", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/411798"}, {"RefNumber": "410945", "RefComponent": "XX-CSC-CZ-IS-U", "RefTitle": "Dunning for incoming payment methods", "RefUrl": "/notes/410945"}, {"RefNumber": "410791", "RefComponent": "XX-TRANSL-JA", "RefTitle": "HRDSYS: Transport of missing JA transl. for OPEC/FUNT/FUNC", "RefUrl": "/notes/410791"}, {"RefNumber": "407240", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/407240"}, {"RefNumber": "407039", "RefComponent": "BW-BCT-PS", "RefTitle": "Error activating 0MLST_USE", "RefUrl": "/notes/407039"}, {"RefNumber": "406744", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/406744"}, {"RefNumber": "406585", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/406585"}, {"RefNumber": "406291", "RefComponent": "IS-OIL-DS-TDP", "RefTitle": "Pricing type 'T' in document copy control KNPRS", "RefUrl": "/notes/406291"}, {"RefNumber": "403575", "RefComponent": "BC-SRV-LTS", "RefTitle": "Lotus Notes Integration disrupted by hot package 17", "RefUrl": "/notes/403575"}, {"RefNumber": "403340", "RefComponent": "XX-CSC-US-PP", "RefTitle": "PPC Release 1: Documentation", "RefUrl": "/notes/403340"}, {"RefNumber": "402072", "RefComponent": "PSM-FM-UP-CM", "RefTitle": "Transaction FMNG: Missing Customizing for SN distribution", "RefUrl": "/notes/402072"}, {"RefNumber": "401658", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/401658"}, {"RefNumber": "400274", "RefComponent": "BC-BMT-WFM", "RefTitle": "WF Builder: CUA interface defect after SP03", "RefUrl": "/notes/400274"}, {"RefNumber": "400112", "RefComponent": "XX-PROJ-SDP-004", "RefTitle": "DIVA ICP1/2: Preliminary Transport II (05/2001)", "RefUrl": "/notes/400112"}, {"RefNumber": "399436", "RefComponent": "IS-OIL-DS-TDP", "RefTitle": "IS-OIL TDP Sales excise duty revenue adjustment", "RefUrl": "/notes/399436"}, {"RefNumber": "397983", "RefComponent": "PY-MX", "RefTitle": "sapserv for PTU corrections", "RefUrl": "/notes/397983"}, {"RefNumber": "397862", "RefComponent": "PSM-FG-BL", "RefTitle": "BL OFBL US federal government account derivation", "RefUrl": "/notes/397862"}, {"RefNumber": "397191", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/397191"}, {"RefNumber": "397105", "RefComponent": "FIN-FSCM-TRM-TM-TR", "RefTitle": "Dump CALL_FUNCTION_NOT_FOUND for changing accrual/deferral", "RefUrl": "/notes/397105"}, {"RefNumber": "396716", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/396716"}, {"RefNumber": "396583", "RefComponent": "PS-IS-LOG", "RefTitle": "PSIS: Download for GRANEDA: missing files", "RefUrl": "/notes/396583"}, {"RefNumber": "396044", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/396044"}, {"RefNumber": "395849", "RefComponent": "PY-IT", "RefTitle": "HR-IT: 770 2001-Overview", "RefUrl": "/notes/395849"}, {"RefNumber": "395392", "RefComponent": "PY-MX", "RefTitle": "sapserv for credito al salario", "RefUrl": "/notes/395392"}, {"RefNumber": "394328", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "Wrong price determination for HPM materials at GR", "RefUrl": "/notes/394328"}, {"RefNumber": "393262", "RefComponent": "PT-SP", "RefTitle": "ABAP runtime error DYNPRO_NOT_FOUND (SAPFH5AH)", "RefUrl": "/notes/393262"}, {"RefNumber": "392177", "RefComponent": "XX-PROJ-SDP-004", "RefTitle": "DIVA ICP1/2: Preliminary Transport (03/2001)", "RefUrl": "/notes/392177"}, {"RefNumber": "391556", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/391556"}, {"RefNumber": "390658", "RefComponent": "FI-GL-GL-G", "RefTitle": "RFBNUM00: error message FR264", "RefUrl": "/notes/390658"}, {"RefNumber": "390550", "RefComponent": "BC-UPG", "RefTitle": "ABAP Dictionary objects missing after upgrade", "RefUrl": "/notes/390550"}, {"RefNumber": "390080", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/390080"}, {"RefNumber": "389290", "RefComponent": "BC", "RefTitle": "HP Somersault Enqueue Functionality on 45B", "RefUrl": "/notes/389290"}, {"RefNumber": "388676", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/388676"}, {"RefNumber": "388118", "RefComponent": "BC-DB-DBI", "RefTitle": "ST05 - Syntax error in 46A (APO System)", "RefUrl": "/notes/388118"}, {"RefNumber": "387391", "RefComponent": "LO-LIS-DC", "RefTitle": "Additional checks in LIS", "RefUrl": "/notes/387391"}, {"RefNumber": "387123", "RefComponent": "FIN-FSCM-TRM-TM-TR", "RefTitle": "ZAN: Dump CALL_FUNCTION_NOT_FOUND w/ interest rate adjustmt", "RefUrl": "/notes/387123"}, {"RefNumber": "386823", "RefComponent": "CRM-ISA", "RefTitle": "Web file changes ISA 2.0b Support Package 9", "RefUrl": "/notes/386823"}, {"RefNumber": "385929", "RefComponent": "XX-CSC-BR-FI", "RefTitle": "DIRF -> File corrections 2000  (cont. Note 376013)", "RefUrl": "/notes/385929"}, {"RefNumber": "385850", "RefComponent": "XX-CSC-KR", "RefTitle": "New VAT reports for South Korea", "RefUrl": "/notes/385850"}, {"RefNumber": "385723", "RefComponent": "CRM-ISA", "RefTitle": "Web file changes ISA 2.0c Support Package 2", "RefUrl": "/notes/385723"}, {"RefNumber": "385675", "RefComponent": "XX-CSC-CZ-IS-U", "RefTitle": "Docu CZ/SK for doubtful receivables and write-offs", "RefUrl": "/notes/385675"}, {"RefNumber": "385589", "RefComponent": "FI-AA-IS", "RefTitle": "Revised SAP standard variants Release 4.6B and 4.6C", "RefUrl": "/notes/385589"}, {"RefNumber": "385484", "RefComponent": "XX-CSC-CZ-IS-U", "RefTitle": "Tax code for Statist. procedure of Budget billing", "RefUrl": "/notes/385484"}, {"RefNumber": "384378", "RefComponent": "XX-CSC-CZ-IS-U", "RefTitle": "Surcharge for violation of cos phi", "RefUrl": "/notes/384378"}, {"RefNumber": "383964", "RefComponent": "FI-AP", "RefTitle": "Expiring currencies: Advance implementation", "RefUrl": "/notes/383964"}, {"RefNumber": "383499", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/383499"}, {"RefNumber": "382735", "RefComponent": "SRM-EBP-CA-MSP", "RefTitle": "Prelim. transport f. TPD/UM sync. with MarketSet Procurement", "RefUrl": "/notes/382735"}, {"RefNumber": "382719", "RefComponent": "FI-AP-AP-Q1", "RefTitle": "Form 190 to the Tax Authorities (Spain)", "RefUrl": "/notes/382719"}, {"RefNumber": "382412", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/382412"}, {"RefNumber": "380185", "RefComponent": "IS-H", "RefTitle": "IS-H AT: Transport Korrekturen Scoring 2001", "RefUrl": "/notes/380185"}, {"RefNumber": "378722", "RefComponent": "SRM-EBP-CGS", "RefTitle": "Missng entries in BBPIMSCREEN for BBPCF01 & BBPIV02", "RefUrl": "/notes/378722"}, {"RefNumber": "378165", "RefComponent": "PM-EQM-EQ", "RefTitle": "List output for vehicles", "RefUrl": "/notes/378165"}, {"RefNumber": "378100", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/378100"}, {"RefNumber": "377368", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "Error in BW OLE DB f. OLAP components", "RefUrl": "/notes/377368"}, {"RefNumber": "376471", "RefComponent": "IS-H-PA", "RefTitle": "IS-H: Aktualisierte Version TNFPSE zum 1.1.2001", "RefUrl": "/notes/376471"}, {"RefNumber": "376248", "RefComponent": "FI-AA-AA-C", "RefTitle": "No user fields for posting transactions", "RefUrl": "/notes/376248"}, {"RefNumber": "376013", "RefComponent": "XX-CSC-BR", "RefTitle": "ASUG 2000 Brazil / DIRF requirements for WT/tax reporting", "RefUrl": "/notes/376013"}, {"RefNumber": "375568", "RefComponent": "CA-CL-CHR", "RefTitle": "Characteristics maintenance: update redesign", "RefUrl": "/notes/375568"}, {"RefNumber": "375515", "RefComponent": "CRM-MW-SRV", "RefTitle": "Keygen fix for 2.0C SP02", "RefUrl": "/notes/375515"}, {"RefNumber": "375038", "RefComponent": "CRM-MW-CCO", "RefTitle": "Infoagents SP2 release (r3 side functionality)", "RefUrl": "/notes/375038"}, {"RefNumber": "373992", "RefComponent": "BW-BEX-ET-QDEF", "RefTitle": "Hotfix query definition for 2.0B Frontend Patch 9", "RefUrl": "/notes/373992"}, {"RefNumber": "373712", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "Shipment Cost Worklist: user interface improved", "RefUrl": "/notes/373712"}, {"RefNumber": "373081", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "Dspl.deliv. in Doc.Selection ROIGSD01: VL03/VL33 outdated", "RefUrl": "/notes/373081"}, {"RefNumber": "37165", "RefComponent": "PA-PA", "RefTitle": "Consult: Automatic vacation adjustment when leaving", "RefUrl": "/notes/37165"}, {"RefNumber": "371288", "RefComponent": "BW-BCT-MTD", "RefTitle": "Termination in object coll.(RSO 296) due to ext.cube", "RefUrl": "/notes/371288"}, {"RefNumber": "367684", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/367684"}, {"RefNumber": "366560", "RefComponent": "FI-AP-AP-Q1", "RefTitle": "Update of RFKQSU30, 1099MISC reporting for US", "RefUrl": "/notes/366560"}, {"RefNumber": "366273", "RefComponent": "FS-CML-PO", "RefTitle": "RZH: Syntax error in function group FVD_BO_OL", "RefUrl": "/notes/366273"}, {"RefNumber": "366030", "RefComponent": "BC-SRV-LTS", "RefTitle": "English version of Lotus Domino Integration", "RefUrl": "/notes/366030"}, {"RefNumber": "364943", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "Wrong documents checked in doc.selection report ROIGSD01", "RefUrl": "/notes/364943"}, {"RefNumber": "364368", "RefComponent": "CO-PC-ACT", "RefTitle": "Material ledger help desk", "RefUrl": "/notes/364368"}, {"RefNumber": "364197", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "Missing Customizing Transaction O5AW for Shpm.Mass.Proc.TD", "RefUrl": "/notes/364197"}, {"RefNumber": "363306", "RefComponent": "PY-XX-FO", "RefTitle": "HRDSYS: Missing translations of documentation", "RefUrl": "/notes/363306"}, {"RefNumber": "361763", "RefComponent": "BW-BEX-ET", "RefTitle": "Error message 'Please select a valid hierarchy'", "RefUrl": "/notes/361763"}, {"RefNumber": "361643", "RefComponent": "SRM-EBP-APP", "RefTitle": "Problems with workflow/history display (applet)", "RefUrl": "/notes/361643"}, {"RefNumber": "361332", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/361332"}, {"RefNumber": "361271", "RefComponent": "CO-PC-PCP", "RefTitle": "Archiving product costing: archive info system", "RefUrl": "/notes/361271"}, {"RefNumber": "357720", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/357720"}, {"RefNumber": "355305", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/355305"}, {"RefNumber": "353686", "RefComponent": "PSM-FM", "RefTitle": "Prg term.:Diff no of parameters in FORM and PERFORM", "RefUrl": "/notes/353686"}, {"RefNumber": "353278", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/353278"}, {"RefNumber": "353255", "RefComponent": "PS-IS-LOG", "RefTitle": "New development - individual overviews", "RefUrl": "/notes/353255"}, {"RefNumber": "352089", "RefComponent": "CRM-MSA-CON", "RefTitle": "Contact person type only maintnd in English + German", "RefUrl": "/notes/352089"}, {"RefNumber": "351390", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/351390"}, {"RefNumber": "350016", "RefComponent": "BW-BCT-SD", "RefTitle": "Missing extraction program for S264", "RefUrl": "/notes/350016"}, {"RefNumber": "339946", "RefComponent": "IS-M", "RefTitle": "IS-M: Define BPs for Euro currency conversion", "RefUrl": "/notes/339946"}, {"RefNumber": "339682", "RefComponent": "MM-IS-IC-DTC", "RefTitle": "Quick correction S094", "RefUrl": "/notes/339682"}, {"RefNumber": "338706", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/338706"}, {"RefNumber": "338497", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "O4B1 Mass Processing: rc=1 on conf.schedule w/o user exit", "RefUrl": "/notes/338497"}, {"RefNumber": "338308", "RefComponent": "MM-IS-IC-DTC", "RefTitle": "Stock segment changes", "RefUrl": "/notes/338308"}, {"RefNumber": "335921", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/335921"}, {"RefNumber": "334114", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/334114"}, {"RefNumber": "334111", "RefComponent": "PM", "RefTitle": "Archiving PM-relevant data; Sapserv for 4.0B", "RefUrl": "/notes/334111"}, {"RefNumber": "333696", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/333696"}, {"RefNumber": "333448", "RefComponent": "IS-B-RA", "RefTitle": "Risk analysis: Inexact currency conversion", "RefUrl": "/notes/333448"}, {"RefNumber": "333128", "RefComponent": "XX-PROJ-CS-SD", "RefTitle": "SD:Order - redetermining def.vals in partner change", "RefUrl": "/notes/333128"}, {"RefNumber": "332993", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "Formula Price calculation in relation to quantity", "RefUrl": "/notes/332993"}, {"RefNumber": "332692", "RefComponent": "BW-BCT-MM", "RefTitle": "ABAP/4PERFORM_NOT_FOUND MCB1_505", "RefUrl": "/notes/332692"}, {"RefNumber": "331608", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/331608"}, {"RefNumber": "331366", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/331366"}, {"RefNumber": "330900", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/330900"}, {"RefNumber": "330563", "RefComponent": "MM-IS-IC", "RefTitle": "Composite note: INVCO Release 3.1I (September 2000)", "RefUrl": "/notes/330563"}, {"RefNumber": "329843", "RefComponent": "EC-CS-CSF-A", "RefTitle": "Development class /1SAP1/FC_FCG0 does not exist", "RefUrl": "/notes/329843"}, {"RefNumber": "329589", "RefComponent": "SV-ASA-PRE-CPG", "RefTitle": "Missing Business Configuration Sets", "RefUrl": "/notes/329589"}, {"RefNumber": "328529", "RefComponent": "BW-BEX-ET", "RefTitle": "Bad index errors when processing variables", "RefUrl": "/notes/328529"}, {"RefNumber": "327941", "RefComponent": "MM-IS-IC-RPT", "RefTitle": "Authorization profile for document evaluations", "RefUrl": "/notes/327941"}, {"RefNumber": "327820", "RefComponent": "SCM-APO-SNP-BF", "RefTitle": "Transports missing from system tables for SP1-SP4", "RefUrl": "/notes/327820"}, {"RefNumber": "327088", "RefComponent": "FI-AA-AA-C", "RefTitle": "Definition of the delivered transfer variants", "RefUrl": "/notes/327088"}, {"RefNumber": "326039", "RefComponent": "SCM-APO-CA-COP", "RefTitle": "Installation of ITS-Services (APO: 2.0)", "RefUrl": "/notes/326039"}, {"RefNumber": "324928", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/324928"}, {"RefNumber": "324393", "RefComponent": "BC-SRV-QUE", "RefTitle": "SAP query: Special copy functions", "RefUrl": "/notes/324393"}, {"RefNumber": "323383", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/323383"}, {"RefNumber": "323030", "RefComponent": "FI-FM-AF-AR", "RefTitle": "Funds Management archiving (4.5A-4.6C)", "RefUrl": "/notes/323030"}, {"RefNumber": "322580", "RefComponent": "TR-CB-IS", "RefTitle": "Hierarchy in standard reports", "RefUrl": "/notes/322580"}, {"RefNumber": "322459", "RefComponent": "FI-AR-AR-G", "RefTitle": "RFICRC00: Various errors", "RefUrl": "/notes/322459"}, {"RefNumber": "32236", "RefComponent": "MM-IM", "RefTitle": "Incorrect stock qty or stock value in material master", "RefUrl": "/notes/32236"}, {"RefNumber": "322155", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/322155"}, {"RefNumber": "322153", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/322153"}, {"RefNumber": "321751", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/321751"}, {"RefNumber": "321286", "RefComponent": "MM-IS-IC", "RefTitle": "Collective note: Invent. Controlling Rel. 4.6C (April 2001)", "RefUrl": "/notes/321286"}, {"RefNumber": "320555", "RefComponent": "EC-CS", "RefTitle": "Transaction data transport: Additional financial data", "RefUrl": "/notes/320555"}, {"RefNumber": "320387", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/320387"}, {"RefNumber": "320166", "RefComponent": "BC-CCM-MON-INF", "RefTitle": "No list in detailed analysis in DB02 (Informix)", "RefUrl": "/notes/320166"}, {"RefNumber": "317876", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/317876"}, {"RefNumber": "317716", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/317716"}, {"RefNumber": "317576", "RefComponent": "CRM-ISA", "RefTitle": "Missing or incorrect IMS.DLL", "RefUrl": "/notes/317576"}, {"RefNumber": "316418", "RefComponent": "TR-TM-TM", "RefTitle": "Treasury datafeed: Exch.rates w/indirect quotation", "RefUrl": "/notes/316418"}, {"RefNumber": "31621", "RefComponent": "BC-BMT-OM", "RefTitle": "PD and workflow application do not run correctly", "RefUrl": "/notes/31621"}, {"RefNumber": "315930", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/315930"}, {"RefNumber": "315377", "RefComponent": "BC-UPG-RDM", "RefTitle": "Additional information about upgrading to 3.1I SR1", "RefUrl": "/notes/315377"}, {"RefNumber": "315012", "RefComponent": "BW-BCT-LO", "RefTitle": "Problems with user-defined fields", "RefUrl": "/notes/315012"}, {"RefNumber": "314701", "RefComponent": "BC-SRV-MAP", "RefTitle": "Latest Outlook forms for SAP MAPI Service Provider", "RefUrl": "/notes/314701"}, {"RefNumber": "313901", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/313901"}, {"RefNumber": "313817", "RefComponent": "FI-AP-AP-Q1", "RefTitle": "Withholding tax reporting revised: RFKQST00", "RefUrl": "/notes/313817"}, {"RefNumber": "313419", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/313419"}, {"RefNumber": "313406", "RefComponent": "CA-ESS-ITS", "RefTitle": "Japanese characters in ESS tree applet", "RefUrl": "/notes/313406"}, {"RefNumber": "313400", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/313400"}, {"RefNumber": "312557", "RefComponent": "BC-TWB-TST-CAT", "RefTitle": "CATT for work load generation", "RefUrl": "/notes/312557"}, {"RefNumber": "311979", "RefComponent": "MM-IS-IC-DTC", "RefTitle": "Error message during check of S094", "RefUrl": "/notes/311979"}, {"RefNumber": "311513", "RefComponent": "FI-TV-PL", "RefTitle": "Train station tables are not current", "RefUrl": "/notes/311513"}, {"RefNumber": "311330", "RefComponent": "BW-BCT-PP", "RefTitle": "Blank units fields in S281 and S282", "RefUrl": "/notes/311330"}, {"RefNumber": "310788", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "No provision to extend Idoc OILDVA01 Driver/Veh", "RefUrl": "/notes/310788"}, {"RefNumber": "310480", "RefComponent": "PP-IS-DC", "RefTitle": "MCF_VERSION_UPD_V2 and MCF_VERSION_UPD_D deleted", "RefUrl": "/notes/310480"}, {"RefNumber": "310095", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "Wrong positioning in shpt scheduling - doc./compart. details", "RefUrl": "/notes/310095"}, {"RefNumber": "310047", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/310047"}, {"RefNumber": "309642", "RefComponent": "PY-AU", "RefTitle": "Tax Thresholds & Limits for 2000/01 Tax Year (PAYG).", "RefUrl": "/notes/309642"}, {"RefNumber": "308868", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Tank ID error on SD order items after hot package", "RefUrl": "/notes/308868"}, {"RefNumber": "308431", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/308431"}, {"RefNumber": "308018", "RefComponent": "BC-SRV-FRM", "RefTitle": "SAPMAPI: WI displ in Japanese Windows installation", "RefUrl": "/notes/308018"}, {"RefNumber": "307925", "RefComponent": "BW-BCT-PP", "RefTitle": "Missing control table entries for S280...S285", "RefUrl": "/notes/307925"}, {"RefNumber": "307858", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/307858"}, {"RefNumber": "307032", "RefComponent": "BW-BCT-PP", "RefTitle": "No delta update for info structure S282", "RefUrl": "/notes/307032"}, {"RefNumber": "306859", "RefComponent": "XX-CSC-PT", "RefTitle": "Mapa Fiscal 32.2: New Layout", "RefUrl": "/notes/306859"}, {"RefNumber": "306761", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "No warning for multiple Driver Vehicle Assignments", "RefUrl": "/notes/306761"}, {"RefNumber": "306559", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Inconsistent date used in order item create", "RefUrl": "/notes/306559"}, {"RefNumber": "306446", "RefComponent": "XX-CSC", "RefTitle": "Dictionary Definitions(Tables) for Modello 770 missing", "RefUrl": "/notes/306446"}, {"RefNumber": "305210", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/305210"}, {"RefNumber": "304923", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/304923"}, {"RefNumber": "304784", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/304784"}, {"RefNumber": "303619", "RefComponent": "FI-AP-AP-Q1", "RefTitle": "Version of Modello 770 / 2000", "RefUrl": "/notes/303619"}, {"RefNumber": "303220", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Tank ID defaulting in orders incorrect on change", "RefUrl": "/notes/303220"}, {"RefNumber": "303216", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/303216"}, {"RefNumber": "30318", "RefComponent": "SD", "RefTitle": "Valuation in SD documents / TCURR", "RefUrl": "/notes/30318"}, {"RefNumber": "302538", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/302538"}, {"RefNumber": "302451", "RefComponent": "IS-OIL-BC", "RefTitle": "IS-OIL language installation on R/3 4.6B", "RefUrl": "/notes/302451"}, {"RefNumber": "301875", "RefComponent": "XX-CSC-AU-GST", "RefTitle": "RFIDAU10: Summary Invoice Report for GST (IV & LIV)", "RefUrl": "/notes/301875"}, {"RefNumber": "301385", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "Population of ship-to in TAS shipment IDoc", "RefUrl": "/notes/301385"}, {"RefNumber": "301319", "RefComponent": "PS-PRG-EVA", "RefTitle": "Check Report for Progress Analysis", "RefUrl": "/notes/301319"}, {"RefNumber": "301021", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Inconsistent date used in order item create", "RefUrl": "/notes/301021"}, {"RefNumber": "301014", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Incorrect TPP check in IDOC_OUTPUT_OILTPI50", "RefUrl": "/notes/301014"}, {"RefNumber": "300242", "RefComponent": "CA-CL", "RefTitle": "Sytx err. in rpt SAPLBTOC: OUTBOUND_CALL_00004050_E", "RefUrl": "/notes/300242"}, {"RefNumber": "300109", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "No warning for multiple Driver Vehicle Assignments", "RefUrl": "/notes/300109"}, {"RefNumber": "25803", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/25803"}, {"RefNumber": "217538", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/217538"}, {"RefNumber": "217471", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/217471"}, {"RefNumber": "217082", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/217082"}, {"RefNumber": "216880", "RefComponent": "SRM-EBP-INB", "RefTitle": "BBP Inbox:Rej./Appr. at line level does not work", "RefUrl": "/notes/216880"}, {"RefNumber": "216249", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Status messages not returned to TPI", "RefUrl": "/notes/216249"}, {"RefNumber": "216236", "RefComponent": "BW-BCT-SD", "RefTitle": "Selection parameters for data upload from S260 - S264", "RefUrl": "/notes/216236"}, {"RefNumber": "216067", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Additional customer exits", "RefUrl": "/notes/216067"}, {"RefNumber": "214917", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/214917"}, {"RefNumber": "214780", "RefComponent": "BC-MID-BUS", "RefTitle": "Business Connector and business documents", "RefUrl": "/notes/214780"}, {"RefNumber": "214017", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/214017"}, {"RefNumber": "213870", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/213870"}, {"RefNumber": "213255", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/213255"}, {"RefNumber": "212057", "RefComponent": "BW-BCT-MM", "RefTitle": "Empty information structure S194", "RefUrl": "/notes/212057"}, {"RefNumber": "211598", "RefComponent": "BW-BCT-PP", "RefTitle": "Miss.entries in logist.extrct strctres <PERSON><PERSON><PERSON>", "RefUrl": "/notes/211598"}, {"RefNumber": "211472", "RefComponent": "FI-GL", "RefTitle": "RFAWVZ40: Performance", "RefUrl": "/notes/211472"}, {"RefNumber": "210435", "RefComponent": "RE-CP", "RefTitle": "Transport of text modules in foreign lang.from 4.6C", "RefUrl": "/notes/210435"}, {"RefNumber": "210066", "RefComponent": "BW-BCT-EC-CS", "RefTitle": "Hierarchies for 0CS_GROUP not displayed", "RefUrl": "/notes/210066"}, {"RefNumber": "209050", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/209050"}, {"RefNumber": "208919", "RefComponent": "BC-CTS-CCO", "RefTitle": "CC-INFO: Performance as of Release 4.6", "RefUrl": "/notes/208919"}, {"RefNumber": "20860", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/20860"}, {"RefNumber": "208587", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "No warning for multiple Driver Vehicle Assignments", "RefUrl": "/notes/208587"}, {"RefNumber": "207985", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/207985"}, {"RefNumber": "206742", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/206742"}, {"RefNumber": "206235", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Syntax error in function group RRX2", "RefUrl": "/notes/206235"}, {"RefNumber": "205629", "RefComponent": "BW-BCT-PP", "RefTitle": "Transfer info structures, conditions 002, 004, 005", "RefUrl": "/notes/205629"}, {"RefNumber": "203403", "RefComponent": "XX-PROJ-IHC", "RefTitle": "Inhouse Cash: Current transport for PAYRQ structure", "RefUrl": "/notes/203403"}, {"RefNumber": "203385", "RefComponent": "BW-BCT-LO", "RefTitle": "TMCEXACT no longer available in system", "RefUrl": "/notes/203385"}, {"RefNumber": "203365", "RefComponent": "FI-GL-GL-F", "RefTitle": "BAS - Report for processing the GST in Australia", "RefUrl": "/notes/203365"}, {"RefNumber": "203004", "RefComponent": "IS-M-AM-ST-A", "RefTitle": "M/AM: RJHXPRAI - restart capable version", "RefUrl": "/notes/203004"}, {"RefNumber": "202877", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/202877"}, {"RefNumber": "201913", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Function call conflict in OIK_TPI_ORDERS_CREATE", "RefUrl": "/notes/201913"}, {"RefNumber": "201262", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/201262"}, {"RefNumber": "200789", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/200789"}, {"RefNumber": "200706", "RefComponent": "IS-OIL-DS-BDRP", "RefTitle": "GUI status OIIPBLCT missing", "RefUrl": "/notes/200706"}, {"RefNumber": "200576", "RefComponent": "BW-BCT-FI", "RefTitle": "FI-AP/AR line item extraction with delta update", "RefUrl": "/notes/200576"}, {"RefNumber": "200316", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Error in incompletion for mandatory Tank ID", "RefUrl": "/notes/200316"}, {"RefNumber": "199541", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Endless loop in IDOC_OUTPUT_OILTPI50", "RefUrl": "/notes/199541"}, {"RefNumber": "199260", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/199260"}, {"RefNumber": "198935", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/198935"}, {"RefNumber": "198627", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Incorrect shipping point in O4PO", "RefUrl": "/notes/198627"}, {"RefNumber": "198465", "RefComponent": "CRM-MW", "RefTitle": "CRM 1.2 SP4: performance problems with sendbits", "RefUrl": "/notes/198465"}, {"RefNumber": "198097", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/198097"}, {"RefNumber": "197351", "RefComponent": "QM-PT-RP-PRC", "RefTitle": "Certificate processing in the MIGO", "RefUrl": "/notes/197351"}, {"RefNumber": "197182", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Quantity missing from OILSHI01 if CPLID not '1'", "RefUrl": "/notes/197182"}, {"RefNumber": "196981", "RefComponent": "XX-CSC-HU", "RefTitle": "Gesetzesänderung bei ungarischen Steuern", "RefUrl": "/notes/196981"}, {"RefNumber": "196249", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/196249"}, {"RefNumber": "196247", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/196247"}, {"RefNumber": "196075", "RefComponent": "IS-OIL-DS", "RefTitle": "Oil-specific messages missing in class VF", "RefUrl": "/notes/196075"}, {"RefNumber": "196052", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Error OC605 occurs incorrectly", "RefUrl": "/notes/196052"}, {"RefNumber": "196023", "RefComponent": "BC-CST-UP", "RefTitle": "Collective run update: Composite corr. for 40B,45B,46B,46C", "RefUrl": "/notes/196023"}, {"RefNumber": "195995", "RefComponent": "TR-CB", "RefTitle": "Organizational dimension in cash budget management", "RefUrl": "/notes/195995"}, {"RefNumber": "195707", "RefComponent": "IS-OIL-DS", "RefTitle": "Slow response to delivery create", "RefUrl": "/notes/195707"}, {"RefNumber": "195616", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/195616"}, {"RefNumber": "195515", "RefComponent": "FI-GL-GL-X", "RefTitle": "Installing ZFINDEX", "RefUrl": "/notes/195515"}, {"RefNumber": "195479", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/195479"}, {"RefNumber": "195308", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/195308"}, {"RefNumber": "195172", "RefComponent": "TR-TM-PO-SE", "RefTitle": "DEP: Inconsistency after securities act transfer", "RefUrl": "/notes/195172"}, {"RefNumber": "194340", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/194340"}, {"RefNumber": "194327", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Incorrect OIK37 updates via TPI", "RefUrl": "/notes/194327"}, {"RefNumber": "194271", "RefComponent": "XX-CSC-HU", "RefTitle": "Legal changes for Hungarian taxes", "RefUrl": "/notes/194271"}, {"RefNumber": "193505", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/193505"}, {"RefNumber": "193231", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "ROIKFCL1 functionality missing after Service Pack", "RefUrl": "/notes/193231"}, {"RefNumber": "193229", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Use cust.exit on KNA1 change pointer for updates to SCP OTWS", "RefUrl": "/notes/193229"}, {"RefNumber": "193227", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "Abort A222 in OIK_TAS_DATA_POST", "RefUrl": "/notes/193227"}, {"RefNumber": "193023", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Handling of ETAs for planned shipments", "RefUrl": "/notes/193023"}, {"RefNumber": "192273", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Collected changes for TPI", "RefUrl": "/notes/192273"}, {"RefNumber": "192046", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "No KNA1 change pointer for updates to SCP OTWS", "RefUrl": "/notes/192046"}, {"RefNumber": "192014", "RefComponent": "IS-OIL-DS-BDRP", "RefTitle": "Error OC709 occurs on TPI OTWS entry", "RefUrl": "/notes/192014"}, {"RefNumber": "191821", "RefComponent": "TR-CB", "RefTitle": "Cash Budget Management in Release 4.6", "RefUrl": "/notes/191821"}, {"RefNumber": "191806", "RefComponent": "XX-CSC-KR", "RefTitle": "CRT for 30FKV/HP79: Tax jurisdiction code on Downpayment", "RefUrl": "/notes/191806"}, {"RefNumber": "191696", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "OILSHI01 IDoc keeps status 64 after processing", "RefUrl": "/notes/191696"}, {"RefNumber": "191432", "RefComponent": "IS-OIL-DS-BDRP", "RefTitle": "Tank ID assignment at order change incorrect", "RefUrl": "/notes/191432"}, {"RefNumber": "191429", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "PBL OTWS entries missing from TPI Location IDoc", "RefUrl": "/notes/191429"}, {"RefNumber": "191401", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Action code in OILTPI50 populated incorrectly", "RefUrl": "/notes/191401"}, {"RefNumber": "191323", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/191323"}, {"RefNumber": "191240", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/191240"}, {"RefNumber": "191154", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "TPI IDocs fail with mixed user defaults", "RefUrl": "/notes/191154"}, {"RefNumber": "191151", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Missing TPI functionality", "RefUrl": "/notes/191151"}, {"RefNumber": "191149", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Abort A222 in OIK_TAS_DATA_POST", "RefUrl": "/notes/191149"}, {"RefNumber": "191042", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/191042"}, {"RefNumber": "190785", "RefComponent": "IS-M-SD-PS-SH-S", "RefTitle": "IS-M/SD: Standard delivery exception transport", "RefUrl": "/notes/190785"}, {"RefNumber": "190447", "RefComponent": "XX-IDES", "RefTitle": "IDES 4.0B turn of the year 1999-2000", "RefUrl": "/notes/190447"}, {"RefNumber": "190425", "RefComponent": "BW-SYS", "RefTitle": "Advance correction for BW 2.0A, patch 1", "RefUrl": "/notes/190425"}, {"RefNumber": "190248", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Generic price reference plant valuation", "RefUrl": "/notes/190248"}, {"RefNumber": "189765", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/189765"}, {"RefNumber": "189642", "RefComponent": "IS-H-PA", "RefTitle": "IS-H:Cont. flat rate per/pro.surch. on basis ICD-10", "RefUrl": "/notes/189642"}, {"RefNumber": "188279", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/188279"}, {"RefNumber": "188202", "RefComponent": "PP-IS-DC", "RefTitle": "Date initial in S023", "RefUrl": "/notes/188202"}, {"RefNumber": "1876296", "RefComponent": "FI-AP-AP-B1", "RefTitle": "CBR-PT: Portugal CBR Development", "RefUrl": "/notes/1876296"}, {"RefNumber": "187395", "RefComponent": "XX-PROJ-IHC", "RefTitle": "In House Cash: Current Transports", "RefUrl": "/notes/187395"}, {"RefNumber": "187344", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/187344"}, {"RefNumber": "187303", "RefComponent": "XX-CSC-US-CS", "RefTitle": "Contract Solution Component: Documentation", "RefUrl": "/notes/187303"}, {"RefNumber": "187191", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/187191"}, {"RefNumber": "187039", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/187039"}, {"RefNumber": "1862600", "RefComponent": "FI-LOC-SAF-PT", "RefTitle": "RPFIEU_SAFT Solution :DDIC Note", "RefUrl": "/notes/1862600"}, {"RefNumber": "186151", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Shipment inbound process update task", "RefUrl": "/notes/186151"}, {"RefNumber": "185756", "RefComponent": "MM-IS-IC-DTC", "RefTitle": "Quick correction of S032", "RefUrl": "/notes/185756"}, {"RefNumber": "185685", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Bad performance/Short dump for ROIAMMA3", "RefUrl": "/notes/185685"}, {"RefNumber": "185653", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "OILTPI01 Missing E1OILPS segment II (31H)", "RefUrl": "/notes/185653"}, {"RefNumber": "185620", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Incorrect UOM conversion in ROIKPALE", "RefUrl": "/notes/185620"}, {"RefNumber": "185617", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Error OC709 raised on change of OTWS entry", "RefUrl": "/notes/185617"}, {"RefNumber": "185616", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/185616"}, {"RefNumber": "185611", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/185611"}, {"RefNumber": "185453", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "OIGD Customer Area data cannot be saved on change", "RefUrl": "/notes/185453"}, {"RefNumber": "185348", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/185348"}, {"RefNumber": "185203", "RefComponent": "BW-BCT-LO", "RefTitle": "Error in InfoStructure of type 'T'", "RefUrl": "/notes/185203"}, {"RefNumber": "185087", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/185087"}, {"RefNumber": "185010", "RefComponent": "IS-OIL-DS", "RefTitle": "MM_EKKO: archiving run excludes contracts", "RefUrl": "/notes/185010"}, {"RefNumber": "1846104", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1846104"}, {"RefNumber": "1845747", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1845747"}, {"RefNumber": "184399", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/184399"}, {"RefNumber": "183533", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/183533"}, {"RefNumber": "1833300", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1833300"}, {"RefNumber": "183311", "RefComponent": "BC-DB-DB2", "RefTitle": "DB2/390: Automated PTF Check", "RefUrl": "/notes/183311"}, {"RefNumber": "182859", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Override of storage SOE failes on shipment inbound", "RefUrl": "/notes/182859"}, {"RefNumber": "182178", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "Shipment Load ID assigned with Valid-from > Valid-to", "RefUrl": "/notes/182178"}, {"RefNumber": "1821617", "RefComponent": "PY-ZA", "RefTitle": "Variable Pay Streamlined Timing - Tax Year 2013-14 - Phase 2", "RefUrl": "/notes/1821617"}, {"RefNumber": "181541", "RefComponent": "SD-IS-DC", "RefTitle": "SIS: Error/termination during update of S126", "RefUrl": "/notes/181541"}, {"RefNumber": "181435", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Override of storage SOE failes on shipment inbound", "RefUrl": "/notes/181435"}, {"RefNumber": "181286", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/181286"}, {"RefNumber": "181239", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Tank ID not stored in OIK37 for Oil TPI", "RefUrl": "/notes/181239"}, {"RefNumber": "1812089", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1812089"}, {"RefNumber": "1811063", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1811063"}, {"RefNumber": "181099", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Collective changes to shipment inbound 40B", "RefUrl": "/notes/181099"}, {"RefNumber": "181089", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/181089"}, {"RefNumber": "181086", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/181086"}, {"RefNumber": "181083", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/181083"}, {"RefNumber": "181082", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/181082"}, {"RefNumber": "181061", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Inconsistent data and status update in TPI interfa", "RefUrl": "/notes/181061"}, {"RefNumber": "181008", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Error in Shipment Create abends OILSHI01 processing", "RefUrl": "/notes/181008"}, {"RefNumber": "1808723", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1808723"}, {"RefNumber": "180759", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "OILTPI01 Missing E1OILPS segment", "RefUrl": "/notes/180759"}, {"RefNumber": "1807064", "RefComponent": "PY-ZA", "RefTitle": "Variable Pay Streamlined Timing - Tax Year 2013-14 - Phase 1", "RefUrl": "/notes/1807064"}, {"RefNumber": "180696", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Justification of storage object field", "RefUrl": "/notes/180696"}, {"RefNumber": "180435", "RefComponent": "XX-PROJ-CEM", "RefTitle": "P3G CEM V3.0A corrections / f. LCP/HP status 26", "RefUrl": "/notes/180435"}, {"RefNumber": "180418", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Collective changes distributed after Hot Pack nov 99", "RefUrl": "/notes/180418"}, {"RefNumber": "180401", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Collective changes to shipment inbound 31H", "RefUrl": "/notes/180401"}, {"RefNumber": "1794880", "RefComponent": "FI-AP-AP-B1", "RefTitle": "PT-CBR: Portugal Central Bank Reporting", "RefUrl": "/notes/1794880"}, {"RefNumber": "1794832", "RefComponent": "PY-CA", "RefTitle": "YE12: Delivery of Year End Transports for 2012", "RefUrl": "/notes/1794832"}, {"RefNumber": "179459", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/179459"}, {"RefNumber": "179319", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Wrong data in shipment tpi screens", "RefUrl": "/notes/179319"}, {"RefNumber": "179151", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Delivery creation uses system date", "RefUrl": "/notes/179151"}, {"RefNumber": "179143", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Wrong tank check on order entry", "RefUrl": "/notes/179143"}, {"RefNumber": "178954", "RefComponent": "MM-IM-GF-ARC", "RefTitle": "Corrections for Archiving Object MM_HDEL (RM07KOHDEL2)", "RefUrl": "/notes/178954"}, {"RefNumber": "178467", "RefComponent": "BC-DB-DBI", "RefTitle": "Syntax error in ST05 after implementing Note 116095", "RefUrl": "/notes/178467"}, {"RefNumber": "1784569", "RefComponent": "PY-US-TR", "RefTitle": "TR: Form 941 PR - Issue in boxes 7 and 9", "RefUrl": "/notes/1784569"}, {"RefNumber": "178423", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Changes delivered with CRT", "RefUrl": "/notes/178423"}, {"RefNumber": "178164", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "Screen 200 missing in program", "RefUrl": "/notes/178164"}, {"RefNumber": "177890", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/177890"}, {"RefNumber": "177825", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Order distribution action code", "RefUrl": "/notes/177825"}, {"RefNumber": "177717", "RefComponent": "XX-PROJ-CS-NF", "RefTitle": "Termntn sales document: PERFORM_PARAMETER_TOO_SHORT", "RefUrl": "/notes/177717"}, {"RefNumber": "1776403", "RefComponent": "PY-US-TR", "RefTitle": "TR: W-2 creation when earns only box 12 DD", "RefUrl": "/notes/1776403"}, {"RefNumber": "177563", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Save tank for delivery on shipment inbound", "RefUrl": "/notes/177563"}, {"RefNumber": "177556", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "change communication structure for delete IDoc processing", "RefUrl": "/notes/177556"}, {"RefNumber": "177304", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/177304"}, {"RefNumber": "176998", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/176998"}, {"RefNumber": "176218", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Handling type overwritten by incoming shipment IDoc", "RefUrl": "/notes/176218"}, {"RefNumber": "176214", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "SOE GRP from IDOC OILSHI01 does not override default values", "RefUrl": "/notes/176214"}, {"RefNumber": "175917", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Wrong quantity used for ASTM range check", "RefUrl": "/notes/175917"}, {"RefNumber": "175840", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Segments E1OILSI E1OILVH too short", "RefUrl": "/notes/175840"}, {"RefNumber": "175835", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/175835"}, {"RefNumber": "175833", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/175833"}, {"RefNumber": "175777", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Changes to order distribution", "RefUrl": "/notes/175777"}, {"RefNumber": "175646", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Wrong quantity used for ASTM range check", "RefUrl": "/notes/175646"}, {"RefNumber": "175378", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/175378"}, {"RefNumber": "174835", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/174835"}, {"RefNumber": "174710", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "shipment inbound, change plant", "RefUrl": "/notes/174710"}, {"RefNumber": "174462", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "TPI: Filter objects not possible", "RefUrl": "/notes/174462"}, {"RefNumber": "174368", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/174368"}, {"RefNumber": "174073", "RefComponent": "BC-CUS", "RefTitle": "Missing release notes after upgrade", "RefUrl": "/notes/174073"}, {"RefNumber": "173973", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/173973"}, {"RefNumber": "173969", "RefComponent": "PSM-FM-BU", "RefTitle": "RFFFMCPYI1 - Include not found", "RefUrl": "/notes/173969"}, {"RefNumber": "173841", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/173841"}, {"RefNumber": "173464", "RefComponent": "SD-IS", "RefTitle": "SIS variant configuration: Update of S126 for VC", "RefUrl": "/notes/173464"}, {"RefNumber": "1731206", "RefComponent": "PY-FR", "RefTitle": "DNAC-AED: AED statement viewer", "RefUrl": "/notes/1731206"}, {"RefNumber": "172175", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/172175"}, {"RefNumber": "171948", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/171948"}, {"RefNumber": "171661", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/171661"}, {"RefNumber": "171619", "RefComponent": "IS-B-DP-BD", "RefTitle": "Advance correction variable transaction on SAPSERV3", "RefUrl": "/notes/171619"}, {"RefNumber": "171399", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "TPI: Segment E1OILTW Error in OILTPI01", "RefUrl": "/notes/171399"}, {"RefNumber": "171098", "RefComponent": "BW-BCT-MM", "RefTitle": "BW/MM modifications in stnd system for Rel. BW 2.0B", "RefUrl": "/notes/171098"}, {"RefNumber": "170684", "RefComponent": "FI-AA", "RefTitle": "IMG FI-AA: Different view clusters are missing", "RefUrl": "/notes/170684"}, {"RefNumber": "170677", "RefComponent": "FI-AP-AP-Q", "RefTitle": "2nd correction of 770 module for fiscal year 1998", "RefUrl": "/notes/170677"}, {"RefNumber": "170583", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "ITA: Incompletion message for deleted contract/ order item", "RefUrl": "/notes/170583"}, {"RefNumber": "170381", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/170381"}, {"RefNumber": "170319", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/170319"}, {"RefNumber": "170271", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/170271"}, {"RefNumber": "170259", "RefComponent": "QM-PT-BD-SPL", "RefTitle": "Supplement to Note 112799: Screen RQDULM10 0333", "RefUrl": "/notes/170259"}, {"RefNumber": "169908", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/169908"}, {"RefNumber": "169078", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/169078"}, {"RefNumber": "1688449", "RefComponent": "XX-CSC-CH-IS-H", "RefTitle": "IS-H CH:Med.Statistik neu - SwissDRG Erhebung", "RefUrl": "/notes/1688449"}, {"RefNumber": "168675", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/168675"}, {"RefNumber": "1679450", "RefComponent": "SCM-APO-SNP-BF", "RefTitle": "Missing delivered objects from 9ASNP02 Planning Area", "RefUrl": "/notes/1679450"}, {"RefNumber": "167916", "RefComponent": "XX-CSC-US", "RefTitle": "CSC Release 1: Return Deletion Not Updating Contract", "RefUrl": "/notes/167916"}, {"RefNumber": "1678400", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1678400"}, {"RefNumber": "167690", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/167690"}, {"RefNumber": "167688", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/167688"}, {"RefNumber": "167500", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/167500"}, {"RefNumber": "167142", "RefComponent": "BW-BCT", "RefTitle": "Transaction LBW1", "RefUrl": "/notes/167142"}, {"RefNumber": "167098", "RefComponent": "FI-AA-IS", "RefTitle": "Missing variants for queries", "RefUrl": "/notes/167098"}, {"RefNumber": "167058", "RefComponent": "XX-CSC-XX", "RefTitle": "Costs Brazil - Model of Inventory Register Query", "RefUrl": "/notes/167058"}, {"RefNumber": "166994", "RefComponent": "BW-BCT-PP", "RefTitle": "Empty provision of data S282/2LIS_04_S282", "RefUrl": "/notes/166994"}, {"RefNumber": "166778", "RefComponent": "BW-BCT-SD", "RefTitle": "Current SD extractors for PI.1999 and higher", "RefUrl": "/notes/166778"}, {"RefNumber": "166640", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/166640"}, {"RefNumber": "166508", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/166508"}, {"RefNumber": "166096", "RefComponent": "BC-MID-RFC", "RefTitle": "qRFC installation for 3.xx, 4.0x, 4.5x and 4.6x", "RefUrl": "/notes/166096"}, {"RefNumber": "165951", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/165951"}, {"RefNumber": "165909", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/165909"}, {"RefNumber": "165882", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/165882"}, {"RefNumber": "165619", "RefComponent": "FI-AP-AP-Q", "RefTitle": "Correction of 770 module for fiscal year 1998", "RefUrl": "/notes/165619"}, {"RefNumber": "1654645", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1654645"}, {"RefNumber": "165205", "RefComponent": "IS-OIL", "RefTitle": "IS-OIL Y2000 core changes relevant for 3.0D basis", "RefUrl": "/notes/165205"}, {"RefNumber": "164885", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/164885"}, {"RefNumber": "1647554", "RefComponent": "RE-FX-LC-CH", "RefTitle": "National Register of Buildings and Dwellings (GWR)", "RefUrl": "/notes/1647554"}, {"RefNumber": "1645112", "RefComponent": "PY-GB-PS", "RefTitle": "PY-GB-PS: USS CARE Schemes", "RefUrl": "/notes/1645112"}, {"RefNumber": "164494", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/164494"}, {"RefNumber": "164317", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/164317"}, {"RefNumber": "163527", "RefComponent": "FI-AP-AP-Q1", "RefTitle": "Withholding Tax for USA 1099-MISC (Version 2001)", "RefUrl": "/notes/163527"}, {"RefNumber": "163415", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/163415"}, {"RefNumber": "162652", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "ITA: Missing UserExits in Function IDOC_INPUT_OILSH1", "RefUrl": "/notes/162652"}, {"RefNumber": "162534", "RefComponent": "BW-BCT-PP", "RefTitle": "PP/BW modifications in standard system for Rel 2.0", "RefUrl": "/notes/162534"}, {"RefNumber": "162514", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "ITA: missg. Mode of Transp. in Output Determination", "RefUrl": "/notes/162514"}, {"RefNumber": "1624220", "RefComponent": "PY-GB", "RefTitle": "Real Time Information", "RefUrl": "/notes/1624220"}, {"RefNumber": "1623643", "RefComponent": "XX-PART-ISHMED", "RefTitle": "Patient Organizer: N2WLD - Aspects (Program Termination)", "RefUrl": "/notes/1623643"}, {"RefNumber": "162250", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/162250"}, {"RefNumber": "1620536", "RefComponent": "BC-CST", "RefTitle": "Fix for error after import of note 1580017", "RefUrl": "/notes/1620536"}, {"RefNumber": "161416", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/161416"}, {"RefNumber": "161415", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/161415"}, {"RefNumber": "161414", "RefComponent": "XX-CSC-CO", "RefTitle": "Localization : Multiple withholding tax cancel invoice docum", "RefUrl": "/notes/161414"}, {"RefNumber": "161409", "RefComponent": "XX-CSC-CO", "RefTitle": "Localization : Multiple withholding tax invoice verification", "RefUrl": "/notes/161409"}, {"RefNumber": "161405", "RefComponent": "XX-CSC-CO", "RefTitle": "Localization : Multiple withholding tax at invoice", "RefUrl": "/notes/161405"}, {"RefNumber": "161100", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/161100"}, {"RefNumber": "1592478", "RefComponent": "IS-H-PM", "RefTitle": "IS-H-CH: Enhancements for Swiss DRG - STD delta", "RefUrl": "/notes/1592478"}, {"RefNumber": "159066", "RefComponent": "CRM-MW", "RefTitle": "CRM Middleware Notes Overview", "RefUrl": "/notes/159066"}, {"RefNumber": "157902", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/157902"}, {"RefNumber": "157651", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/157651"}, {"RefNumber": "157650", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/157650"}, {"RefNumber": "157009", "RefComponent": "XX-CSC-GB", "RefTitle": "Construction Industry Scheme (CIS)  UK", "RefUrl": "/notes/157009"}, {"RefNumber": "1557747", "RefComponent": "XX-PART-ISHMED-ORD", "RefTitle": "Clinical Order: Prioritization of Screen Modification", "RefUrl": "/notes/1557747"}, {"RefNumber": "1554835", "RefComponent": "PM-<PERSON>M-MP", "RefTitle": "CustConn EAM: Tech. prerequisite for Notes 1553174/1553173", "RefUrl": "/notes/1554835"}, {"RefNumber": "1547506", "RefComponent": "PY-AT", "RefTitle": "Einkommensbericht aufgrund Gleichbehandlungsgesetz", "RefUrl": "/notes/1547506"}, {"RefNumber": "1545704", "RefComponent": "XX-CSC-RU-IS-U", "RefTitle": "IS-U Localization RU: corrections", "RefUrl": "/notes/1545704"}, {"RefNumber": "154351", "RefComponent": "BC-CTS-LAN", "RefTitle": "Known problems during lang. import of patch texts", "RefUrl": "/notes/154351"}, {"RefNumber": "1543043", "RefComponent": "PY-BR", "RefTitle": "New Termination Term in release 4.6C", "RefUrl": "/notes/1543043"}, {"RefNumber": "1536216", "RefComponent": "PY-BR", "RefTitle": "DIRF and Tax Income Declaration enhancements in 4.6C", "RefUrl": "/notes/1536216"}, {"RefNumber": "153271", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/153271"}, {"RefNumber": "153004", "RefComponent": "XX-PROJ-CS", "RefTitle": "CS user exit prod.order before length calc. call", "RefUrl": "/notes/153004"}, {"RefNumber": "1527811", "RefComponent": "PY-BR", "RefTitle": "DIRF 2010 - Instrução Normativa Nº 1.033", "RefUrl": "/notes/1527811"}, {"RefNumber": "1523958", "RefComponent": "XX-CSC-BR-REP", "RefTitle": "EFD-Contributions New Legal File", "RefUrl": "/notes/1523958"}, {"RefNumber": "1520198", "RefComponent": "XX-PART-ISHMED", "RefTitle": "Patient Organizer/Patient Viewer: Standard Aspect SAP40", "RefUrl": "/notes/1520198"}, {"RefNumber": "151522", "RefComponent": "XX-PROJ-CS", "RefTitle": "Convert company code currency: Cable Solution", "RefUrl": "/notes/151522"}, {"RefNumber": "151050", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/151050"}, {"RefNumber": "151037", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/151037"}, {"RefNumber": "1510355", "RefComponent": "BW-BCT-GEN", "RefTitle": "missing objects for BI_CONT 704", "RefUrl": "/notes/1510355"}, {"RefNumber": "150831", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Movement based netting: VAT on MM side missing (II)", "RefUrl": "/notes/150831"}, {"RefNumber": "1506349", "RefComponent": "SRM-EBP-ADM-XBP", "RefTitle": "Approval Workflow Bidder/Supplier: ********** and **********", "RefUrl": "/notes/1506349"}, {"RefNumber": "1504810", "RefComponent": "XX-PART-ISHMED", "RefTitle": "CWS: Surgeries View Type - Calculate Function", "RefUrl": "/notes/1504810"}, {"RefNumber": "150437", "RefComponent": "LO-MD-BOM", "RefTitle": "ARCH: BOM archiving is available", "RefUrl": "/notes/150437"}, {"RefNumber": "150376", "RefComponent": "MM-IS-IC", "RefTitle": "Collective note BCO Release 4.0b to 4.6b (October 2000)", "RefUrl": "/notes/150376"}, {"RefNumber": "1497982", "RefComponent": "PY-BR", "RefTitle": "Ordinance 1620: Homolognet", "RefUrl": "/notes/1497982"}, {"RefNumber": "1490187", "RefComponent": "XX-PART-ISHMED", "RefTitle": "Medication: Various Corrections and Technical Adaptations", "RefUrl": "/notes/1490187"}, {"RefNumber": "1488921", "RefComponent": "XX-PART-ISHMED", "RefTitle": "Planning: Appt OU Presetting for Coordinating Facility", "RefUrl": "/notes/1488921"}, {"RefNumber": "148765", "RefComponent": "LO-BM", "RefTitle": "Sample source code f.internal batch no. allocation", "RefUrl": "/notes/148765"}, {"RefNumber": "148464", "RefComponent": "PY-NO", "RefTitle": "PY-NO: Changes in Norwegian tax reporting 1999", "RefUrl": "/notes/148464"}, {"RefNumber": "1480103", "RefComponent": "XX-PART-ISHMED", "RefTitle": "Standard Nursing Plan: Display Technical Properties Missing", "RefUrl": "/notes/1480103"}, {"RefNumber": "1475039", "RefComponent": "XX-PART-ISHMED", "RefTitle": "Surgery System: Surgery WS - BADI_ISHMED_BASEITEM_DOCUMENT", "RefUrl": "/notes/1475039"}, {"RefNumber": "1474670", "RefComponent": "XX-PART-ISHMED", "RefTitle": "Create Clinical Order: Runtime Error", "RefUrl": "/notes/1474670"}, {"RefNumber": "146906", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Movement based netting: VAT on MM side missing", "RefUrl": "/notes/146906"}, {"RefNumber": "146467", "RefComponent": "PP-IS-DC", "RefTitle": "Statistical setup repetitive manufacturing doc. log", "RefUrl": "/notes/146467"}, {"RefNumber": "1460368", "RefComponent": "XX-PART-ISHMED", "RefTitle": "Medication: Various Corrections and Technical Adaptations", "RefUrl": "/notes/1460368"}, {"RefNumber": "145854", "RefComponent": "IS-OIL-BC", "RefTitle": "Order of IS-OIL notes 4.0b on basis R/3 4.0B (LCP)", "RefUrl": "/notes/145854"}, {"RefNumber": "145850", "RefComponent": "IS-OIL-BC", "RefTitle": "Order of IS-OIL notes 4.0b on basis R/3 4.0B (SP)", "RefUrl": "/notes/145850"}, {"RefNumber": "145812", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/145812"}, {"RefNumber": "1454921", "RefComponent": "XX-PART-ISHMED", "RefTitle": "Medication: Various Corrections and Technical Adaptations", "RefUrl": "/notes/1454921"}, {"RefNumber": "1454353", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1454353"}, {"RefNumber": "145120", "RefComponent": "PP-IS-REP", "RefTitle": "Text display in flexible analysis", "RefUrl": "/notes/145120"}, {"RefNumber": "1450021", "RefComponent": "XX-PART-ISHMED", "RefTitle": "Medication: Various Corrections and Technical Adaptations", "RefUrl": "/notes/1450021"}, {"RefNumber": "1446978", "RefComponent": "XX-PART-ISHMED", "RefTitle": "Clinical Order: Screen Modification", "RefUrl": "/notes/1446978"}, {"RefNumber": "144572", "RefComponent": "PS-IS-LOG", "RefTitle": "Project Information System: Error in new fields", "RefUrl": "/notes/144572"}, {"RefNumber": "1443156", "RefComponent": "XX-PART-ISHMED", "RefTitle": "Problem in IMG: Radiology - Node Text Not Found", "RefUrl": "/notes/1443156"}, {"RefNumber": "1442119", "RefComponent": "XX-CSC-CH-IS-H", "RefTitle": "IS-H CH: TARMED V1.07 - Neue Reports (REORG, EINSP, ABGL)", "RefUrl": "/notes/1442119"}, {"RefNumber": "1441365", "RefComponent": "XX-PART-ISHMED", "RefTitle": "Medication: Various Corrections and Technical Adaptations", "RefUrl": "/notes/1441365"}, {"RefNumber": "143633", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/143633"}, {"RefNumber": "1434314", "RefComponent": "XX-PART-ISHMED", "RefTitle": "Medication: Various Corrections and Technical Adaptations", "RefUrl": "/notes/1434314"}, {"RefNumber": "1429456", "RefComponent": "XX-PART-ISHMED", "RefTitle": "Medication: Various Corrections and Technical Adaptations", "RefUrl": "/notes/1429456"}, {"RefNumber": "1425765", "RefComponent": "BC-CST-GW", "RefTitle": "Generating sec_info reg_info", "RefUrl": "/notes/1425765"}, {"RefNumber": "1423313", "RefComponent": "XX-PART-ISHMED", "RefTitle": "Medication: Various Corrections and Technical Adaptations", "RefUrl": "/notes/1423313"}, {"RefNumber": "140769", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/140769"}, {"RefNumber": "1401222", "RefComponent": "XX-PART-ISHMED", "RefTitle": "Medication: Various Corrections and Technical Adaptations", "RefUrl": "/notes/1401222"}, {"RefNumber": "1394387", "RefComponent": "XX-PART-ISHMED", "RefTitle": "Medication: Various Corrections and Technical Adaptations", "RefUrl": "/notes/1394387"}, {"RefNumber": "137842", "RefComponent": "FI-BL-PT-BA", "RefTitle": "Automatic account statement Austria", "RefUrl": "/notes/137842"}, {"RefNumber": "1377745", "RefComponent": "XX-PART-ISHMED", "RefTitle": "Medication: Various Corrections and Technical Adaptations", "RefUrl": "/notes/1377745"}, {"RefNumber": "1377146", "RefComponent": "XX-CSC-CH-IS-H", "RefTitle": "IS-H CH: Analyseleistungsregeln - Erweiterungen", "RefUrl": "/notes/1377146"}, {"RefNumber": "137248", "RefComponent": "LO-LIS", "RefTitle": "Cross-application planning", "RefUrl": "/notes/137248"}, {"RefNumber": "1366434", "RefComponent": "XX-CSC-CH-IS-H", "RefTitle": "IS-H CH: Analysenliste- Leistungsregeln", "RefUrl": "/notes/1366434"}, {"RefNumber": "1365394", "RefComponent": "XX-PART-ISHMED", "RefTitle": "Medication: Various Corrections and Technical Adaptations", "RefUrl": "/notes/1365394"}, {"RefNumber": "1364960", "RefComponent": "PA-PD-PM", "RefTitle": "Improvements to the Goals Functionality", "RefUrl": "/notes/1364960"}, {"RefNumber": "1364669", "RefComponent": "LOD-ESO-ERP", "RefTitle": "Integrating ERP with the SAP Sourcing (II)", "RefUrl": "/notes/1364669"}, {"RefNumber": "1362579", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1362579"}, {"RefNumber": "1362404", "RefComponent": "XX-CSC-AT-IS-H", "RefTitle": "IS-H AT: Felder LV AT + CH bei neuem Geschäftspart. fehlen", "RefUrl": "/notes/1362404"}, {"RefNumber": "1360162", "RefComponent": "PA-PD-PM", "RefTitle": "(Obsolete) Language issues in Goals Functionality", "RefUrl": "/notes/1360162"}, {"RefNumber": "1359388", "RefComponent": "XX-CSC-CH-IS-H", "RefTitle": "IS-H CH: Schweizer Versichertenkarte - CH-Korr 6.03", "RefUrl": "/notes/1359388"}, {"RefNumber": "1355103", "RefComponent": "XX-PART-ISHMED-ORD", "RefTitle": "Radiology: Findings Work Station - Previewer Missing", "RefUrl": "/notes/1355103"}, {"RefNumber": "1354157", "RefComponent": "XX-CSC-CH-IS-H", "RefTitle": "IS-H: ANA-LISTE (CH-Delta)", "RefUrl": "/notes/1354157"}, {"RefNumber": "1354156", "RefComponent": "XX-CSC-CH-IS-H", "RefTitle": "IS-H: ANA-LISTE (SAP-Delta)", "RefUrl": "/notes/1354156"}, {"RefNumber": "1352967", "RefComponent": "XX-CSC-CH-IS-H", "RefTitle": "IS-H CH: Schweizer Versichertenkarte - CH-Delta", "RefUrl": "/notes/1352967"}, {"RefNumber": "1352966", "RefComponent": "IS-H-PM", "RefTitle": "IS-H CH: Swiss health insurance card - SAP Delta", "RefUrl": "/notes/1352966"}, {"RefNumber": "135095", "RefComponent": "TR-CB", "RefTitle": "Auxiliary programs for Cash Budget Management", "RefUrl": "/notes/135095"}, {"RefNumber": "1345769", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1345769"}, {"RefNumber": "1345614", "RefComponent": "XX-CSC-CH-IS-H", "RefTitle": "IS-H CH: Fehlende Auftragsdaten am Subscreen für Ext.Auftr.", "RefUrl": "/notes/1345614"}, {"RefNumber": "1345069", "RefComponent": "XX-PART-ISHMED", "RefTitle": "Medication: Various Corrections and Technical Adaptations", "RefUrl": "/notes/1345069"}, {"RefNumber": "1341984", "RefComponent": "BW-WHM-DST-DTP", "RefTitle": "Error \"Field length too long for PSA fields\", BI_CONT 7.04", "RefUrl": "/notes/1341984"}, {"RefNumber": "1341931", "RefComponent": "BW-BCT-MM-BW", "RefTitle": "Load scenario in InfoCube 0IC_C03 contains syntax error", "RefUrl": "/notes/1341931"}, {"RefNumber": "1332333", "RefComponent": "PA-PD-PM", "RefTitle": "Goal function (Main note)", "RefUrl": "/notes/1332333"}, {"RefNumber": "1330228", "RefComponent": "XX-CSC-CH-IS-H", "RefTitle": "IS-H: Ext. Auftrag - Ermittlg Honaufteil.code neue Tabelle", "RefUrl": "/notes/1330228"}, {"RefNumber": "133017", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/133017"}, {"RefNumber": "1326618", "RefComponent": "PA-EC", "RefTitle": "Workflow Task 04000013 is missing from the transport", "RefUrl": "/notes/1326618"}, {"RefNumber": "132640", "RefComponent": "FI-FM-AF-AR", "RefTitle": "Archiving for Funds Management (FCABP, FMSU)", "RefUrl": "/notes/132640"}, {"RefNumber": "1326306", "RefComponent": "SCM-BAS-POS", "RefTitle": "Missing Delivery Objects in SCM SNC 7.0", "RefUrl": "/notes/1326306"}, {"RefNumber": "1318389", "RefComponent": "BC-CTS", "RefTitle": "How to Use .SAR/.CAR files and Risks Involved", "RefUrl": "/notes/1318389"}, {"RefNumber": "131702", "RefComponent": "BW-BCT-SD", "RefTitle": "Current SD extractors for BW Release 1.2B", "RefUrl": "/notes/131702"}, {"RefNumber": "1306514", "RefComponent": "XX-PART-ISHMED", "RefTitle": "Medication: Various Corrections and Technical Adaptations", "RefUrl": "/notes/1306514"}, {"RefNumber": "130538", "RefComponent": "SCM-APO-INT", "RefTitle": "Integration APO <--> R/3, purchasing (ATP)", "RefUrl": "/notes/130538"}, {"RefNumber": "1302988", "RefComponent": "XX-PART-ISHMED", "RefTitle": "Medication: Various Corrections and Technical Adaptations", "RefUrl": "/notes/1302988"}, {"RefNumber": "1302490", "RefComponent": "XX-CSC-CH-IS-H", "RefTitle": "IS-H CH: <PERSON><PERSON><PERSON><PERSON> zu Tarmed 1.6 II", "RefUrl": "/notes/1302490"}, {"RefNumber": "1300732", "RefComponent": "FS-CML", "RefTitle": "CML: UI Enhancement with respect to Table Controls", "RefUrl": "/notes/1300732"}, {"RefNumber": "1298409", "RefComponent": "BC-CTS-TLS", "RefTitle": "R3trans import deletes enhancement objects", "RefUrl": "/notes/1298409"}, {"RefNumber": "128835", "RefComponent": "XX-SER-Y2000", "RefTitle": "Error when importing Safety Check 2000", "RefUrl": "/notes/128835"}, {"RefNumber": "1282483", "RefComponent": "XX-CSC-CH-IS-H", "RefTitle": "IS-H CH: Fallabschluss/-öffnung", "RefUrl": "/notes/1282483"}, {"RefNumber": "1282372", "RefComponent": "XX-CSC-CH-IS-H", "RefTitle": "IS-H CH: Fallabschluss/-öffnung (SAP-Delta)", "RefUrl": "/notes/1282372"}, {"RefNumber": "1276101", "RefComponent": "XX-PART-ISHMED", "RefTitle": "Clinical Work Station: SAP Standard Function Variants", "RefUrl": "/notes/1276101"}, {"RefNumber": "1270584", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1270584"}, {"RefNumber": "1268987", "RefComponent": "XX-CSC-AT-IS-H", "RefTitle": "IS-H AT: EDIVKA - Änderungen", "RefUrl": "/notes/1268987"}, {"RefNumber": "1267695", "RefComponent": "XX-PART-ISHMED", "RefTitle": "Nursing: Nursing Plan - Performance Problem with Change Docu", "RefUrl": "/notes/1267695"}, {"RefNumber": "1266639", "RefComponent": "XX-CSC-FR-IS-H", "RefTitle": "IS-H FR: Incorrect German field name in EDI", "RefUrl": "/notes/1266639"}, {"RefNumber": "1265235", "RefComponent": "SCM-APO-FCS", "RefTitle": "Demand Planning: Report to display LiveCache Locks", "RefUrl": "/notes/1265235"}, {"RefNumber": "1264582", "RefComponent": "CRM-IM-IPM-RO", "RefTitle": "Missing BEFM entries after applying SP12", "RefUrl": "/notes/1264582"}, {"RefNumber": "1261113", "RefComponent": "FS-CML-AC-PI", "RefTitle": "BUC: Enhancement of BAdI FVD_LOAN_POST with AUFNR", "RefUrl": "/notes/1261113"}, {"RefNumber": "1256903", "RefComponent": "XX-CSC-AT-IS-H", "RefTitle": "IS-H AT: Dat.tr.aust. P321 - Stichtag für Name (SAP-Delta)", "RefUrl": "/notes/1256903"}, {"RefNumber": "1252417", "RefComponent": "XX-PART-ISHMED", "RefTitle": "Patient Organizer/Viewer: Health Problem - Uppercase", "RefUrl": "/notes/1252417"}, {"RefNumber": "1237763", "RefComponent": "XX-PART-ISHMED-ORD", "RefTitle": "Clinical Order: Incorrect Connection Radiological Documents", "RefUrl": "/notes/1237763"}, {"RefNumber": "1235289", "RefComponent": "FIN-FSCM-TRM-TM-IS", "RefTitle": "0CFM_MARKET_VALUES cannot be activated", "RefUrl": "/notes/1235289"}, {"RefNumber": "1232740", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1232740"}, {"RefNumber": "1229334", "RefComponent": "PY-PT", "RefTitle": "RPCAIDP0 and RPCIIDP0 improvements", "RefUrl": "/notes/1229334"}, {"RefNumber": "1223805", "RefComponent": "EHS-BD-SPE", "RefTitle": "Output variant for Excel- Excel template", "RefUrl": "/notes/1223805"}, {"RefNumber": "1222993", "RefComponent": "PY-PT-PS", "RefTitle": "Evaluation classes 18, 19 and 20 replaced", "RefUrl": "/notes/1222993"}, {"RefNumber": "121265", "RefComponent": "PY-US", "RefTitle": "Payroll Outsourcing on SAPSERV3/4", "RefUrl": "/notes/121265"}, {"RefNumber": "120529", "RefComponent": "IS-R-IFC-IN", "RefTitle": "Downgrade SA editor from release 4.6A to 4.0B", "RefUrl": "/notes/120529"}, {"RefNumber": "11920", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/11920"}, {"RefNumber": "118393", "RefComponent": "EC-CS-CSF-H", "RefTitle": "EC-CS: P/L in inventory available for 4.0A/B", "RefUrl": "/notes/118393"}, {"RefNumber": "1179880", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1179880"}, {"RefNumber": "1178734", "RefComponent": "BW-WHM-DST-ARC", "RefTitle": "PSA Archiving", "RefUrl": "/notes/1178734"}, {"RefNumber": "1178006", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1178006"}, {"RefNumber": "1177980", "RefComponent": "PA-PA-PT", "RefTitle": "Citizen's Card - new functionality of infotype 0185", "RefUrl": "/notes/1177980"}, {"RefNumber": "1176128", "RefComponent": "PA-PA-BR", "RefTitle": "MP039800:Correction for a text element, with no attributes", "RefUrl": "/notes/1176128"}, {"RefNumber": "1174532", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1174532"}, {"RefNumber": "1174130", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1174130"}, {"RefNumber": "1173582", "RefComponent": "BC-CTS-TLS", "RefTitle": "Error occurs during export: invalid value '...' in E071.LANG", "RefUrl": "/notes/1173582"}, {"RefNumber": "1173260", "RefComponent": "PY-PT-PS", "RefTitle": "Legal Change for CGA Magnetic File report", "RefUrl": "/notes/1173260"}, {"RefNumber": "1173178", "RefComponent": "BW-BCT-GEN", "RefTitle": "Missing InfoPackages for UD Connect", "RefUrl": "/notes/1173178"}, {"RefNumber": "1172050", "RefComponent": "BC-ESI-WS-ABA", "RefTitle": "User information contains errors in Web service consumer", "RefUrl": "/notes/1172050"}, {"RefNumber": "1168257", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1168257"}, {"RefNumber": "1168230", "RefComponent": "BW-BCT-GEN", "RefTitle": "Missing 7.00 DataSources", "RefUrl": "/notes/1168230"}, {"RefNumber": "1168114", "RefComponent": "XX-CSC-AT-IS-H", "RefTitle": "IS-H AT: P321 - Änderungen", "RefUrl": "/notes/1168114"}, {"RefNumber": "1166761", "RefComponent": "PY-BR", "RefTitle": "HR-BR: Validation of infotype 2001", "RefUrl": "/notes/1166761"}, {"RefNumber": "1166480", "RefComponent": "XX-CSC-AT-IS-H", "RefTitle": "IS-H: Patientenleitsystem Erweiterte Suche", "RefUrl": "/notes/1166480"}, {"RefNumber": "1165525", "RefComponent": "BW-BCT-GEN", "RefTitle": "Routines missing in BI_CONT 7.03 Support Package 09", "RefUrl": "/notes/1165525"}, {"RefNumber": "1160987", "RefComponent": "PY-BR", "RefTitle": "HBRRAIS0: correction in the processing of PPRP off-cycle", "RefUrl": "/notes/1160987"}, {"RefNumber": "115881", "RefComponent": "BW-BCT", "RefTitle": "BW extraction customer hierarchy missing in R/3 4.5", "RefUrl": "/notes/115881"}, {"RefNumber": "1158200", "RefComponent": "XX-CSC-AT-IS-H", "RefTitle": "IS-H: Patientenleitsystem", "RefUrl": "/notes/1158200"}, {"RefNumber": "1157802", "RefComponent": "XX-CSC-CH-IS-H", "RefTitle": "CH: Änd. für Rechn.nr. in nicht-abrechenb. Honorarleist.", "RefUrl": "/notes/1157802"}, {"RefNumber": "1157762", "RefComponent": "XX-CSC-CH-IS-H", "RefTitle": "IS-H CH: Erweiterung d. Leistungsregel C23 Spartenzuschlag", "RefUrl": "/notes/1157762"}, {"RefNumber": "115687", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/115687"}, {"RefNumber": "1155721", "RefComponent": "PY-BR", "RefTitle": "HR-BR: Fields not available in the view V_T7BR06", "RefUrl": "/notes/1155721"}, {"RefNumber": "1155459", "RefComponent": "PY-PT", "RefTitle": "RPCIIDP0: layout correction of company title in PDF format", "RefUrl": "/notes/1155459"}, {"RefNumber": "1154235", "RefComponent": "PY-BR", "RefTitle": "RAIS: Employees without remuneration in the year.", "RefUrl": "/notes/1154235"}, {"RefNumber": "1150330", "RefComponent": "SRM-EBP-PRO", "RefTitle": "Category Search Help - Toolbar not translated", "RefUrl": "/notes/1150330"}, {"RefNumber": "1148345", "RefComponent": "PY-PT", "RefTitle": "New processing method for handicapped and dependents", "RefUrl": "/notes/1148345"}, {"RefNumber": "1146033", "RefComponent": "PY-XX-RS", "RefTitle": "New function module to get wage type list by an eval. class", "RefUrl": "/notes/1146033"}, {"RefNumber": "1145684", "RefComponent": "PY-PT", "RefTitle": "RPCAIDP0: other deductions for Soc. Sec. contributions.", "RefUrl": "/notes/1145684"}, {"RefNumber": "114566", "RefComponent": "CA-EUR-CNV-FI", "RefTitle": "EMU: MQ530, MQ519 sec. index entries are missing", "RefUrl": "/notes/114566"}, {"RefNumber": "1144912", "RefComponent": "PY-PT-PS", "RefTitle": "New customizing for Sit. Code 01,30,32 of CGA Magnetic File", "RefUrl": "/notes/1144912"}, {"RefNumber": "114266", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/114266"}, {"RefNumber": "1141615", "RefComponent": "PY-AT", "RefTitle": "RPTSWAA0 Schwerarbeitsplatz §5 VO BMSG (diverse Anpassungen)", "RefUrl": "/notes/1141615"}, {"RefNumber": "1137593", "RefComponent": "PY-BR", "RefTitle": "HBRDIRF0: Layout adjusts for 2008", "RefUrl": "/notes/1137593"}, {"RefNumber": "113711", "RefComponent": "XX-CSC-MX", "RefTitle": "FA ISR Depreciation Adjust. & IMPAC Balance Average", "RefUrl": "/notes/113711"}, {"RefNumber": "1133698", "RefComponent": "PY-BR", "RefTitle": "BRPED: Simulation of a dismissal with vacation", "RefUrl": "/notes/1133698"}, {"RefNumber": "1131677", "RefComponent": "PY-AT", "RefTitle": "Kammerumlage- und IESG-Pflicht für freie Dienstnehmer", "RefUrl": "/notes/1131677"}, {"RefNumber": "1129826", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1129826"}, {"RefNumber": "1129349", "RefComponent": "PY-AT", "RefTitle": "RPCUBSA0RA: Erweiterungen für Hintergrundverarbeitung", "RefUrl": "/notes/1129349"}, {"RefNumber": "1129009", "RefComponent": "XX-CSC-CH-IS-H", "RefTitle": "IS-H: Externer Auftrag - F4-Hilfe Auftraggeber", "RefUrl": "/notes/1129009"}, {"RefNumber": "112771", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/112771"}, {"RefNumber": "1127132", "RefComponent": "PY-PT", "RefTitle": "New fields and records to Annual Income Declaration report", "RefUrl": "/notes/1127132"}, {"RefNumber": "1126405", "RefComponent": "IS-H", "RefTitle": "IS-H: Initialization of field EXTAUF in table TN14B", "RefUrl": "/notes/1126405"}, {"RefNumber": "1121452", "RefComponent": "PY-BR", "RefTitle": "UNION: The discount is generated again when retrocalculated", "RefUrl": "/notes/1121452"}, {"RefNumber": "1118193", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1118193"}, {"RefNumber": "1117928", "RefComponent": "PY-AT", "RefTitle": "JW 2007/08: Lohnzettel 5 für ALV (Zusatzreport)", "RefUrl": "/notes/1117928"}, {"RefNumber": "1116763", "RefComponent": "PA-PA-BR", "RefTitle": "HBRSEF00: validation error with especial characters", "RefUrl": "/notes/1116763"}, {"RefNumber": "1114543", "RefComponent": "PY-AT", "RefTitle": "Dienstgeberabgabe Wien (neue Version) Merkmal AUMGR fehlt", "RefUrl": "/notes/1114543"}, {"RefNumber": "1114298", "RefComponent": "XX-PART-ISHMED", "RefTitle": "Nursing: Missing prospective NSR classifications", "RefUrl": "/notes/1114298"}, {"RefNumber": "1113730", "RefComponent": "PY-PT", "RefTitle": "HR-PT: Update of Economical Activity Codes - T5PEA", "RefUrl": "/notes/1113730"}, {"RefNumber": "111299", "RefComponent": "PP-IS", "RefTitle": "Flexible analysis via Operations: too large values", "RefUrl": "/notes/111299"}, {"RefNumber": "1109554", "RefComponent": "XX-PART-ISHMED", "RefTitle": "Planning: Appointment, Movement not Linked with Appointment", "RefUrl": "/notes/1109554"}, {"RefNumber": "110910", "RefComponent": "BC-CTS-LAN", "RefTitle": "Deletion of language load", "RefUrl": "/notes/110910"}, {"RefNumber": "1109085", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1109085"}, {"RefNumber": "1107922", "RefComponent": "PY-BR", "RefTitle": "MANAD: Implementation of Blocks I and L", "RefUrl": "/notes/1107922"}, {"RefNumber": "1106624", "RefComponent": "XX-CSC-CH-IS-H", "RefTitle": "IS-H: Externer Auftrag - Korrekturen ( Ext. AG, ...)", "RefUrl": "/notes/1106624"}, {"RefNumber": "110275", "RefComponent": "PP-IS-DC", "RefTitle": "Units in S021 and S023 / update log", "RefUrl": "/notes/110275"}, {"RefNumber": "1101402", "RefComponent": "XX-CSC-CH-IS-H", "RefTitle": "IS-H: <PERSON><PERSON><PERSON> Auftrag - TNEO_CHARFAC_DET - Abrechnungsver.", "RefUrl": "/notes/1101402"}, {"RefNumber": "1099375", "RefComponent": "XX-CSC-CH-IS-H", "RefTitle": "IS-H: <PERSON><PERSON><PERSON> Auftrag - TNEO_CHARFAC_DET - Keyerweiterung", "RefUrl": "/notes/1099375"}, {"RefNumber": "1098253", "RefComponent": "PY-BR", "RefTitle": "BRD0: Payment Wage Types of Extra Hours", "RefUrl": "/notes/1098253"}, {"RefNumber": "1096503", "RefComponent": "XX-PART-ISHMED", "RefTitle": "Technical Adaptation", "RefUrl": "/notes/1096503"}, {"RefNumber": "1096367", "RefComponent": "PY-BR", "RefTitle": "Documentation for the GRRF (HBRCGRRF) report", "RefUrl": "/notes/1096367"}, {"RefNumber": "1093412", "RefComponent": "XX-PART-ISHMED", "RefTitle": "Clinical Order: Order Templates", "RefUrl": "/notes/1093412"}, {"RefNumber": "1088492", "RefComponent": "PY-AT", "RefTitle": "JW 2007/08: Schwerarbeitsplätze nach §5 VO des BMSG", "RefUrl": "/notes/1088492"}, {"RefNumber": "1087880", "RefComponent": "XX-PART-ISHMED-ORD", "RefTitle": "Patient register: Correction report, data transfer", "RefUrl": "/notes/1087880"}, {"RefNumber": "1086512", "RefComponent": "XX-PART-ISHMED-ORD", "RefTitle": "Nursing: 'End Nursing Plan': Cycle end date (corr. report)", "RefUrl": "/notes/1086512"}, {"RefNumber": "1085152", "RefComponent": "XX-PART-ISHMED", "RefTitle": "Technical Adaptation", "RefUrl": "/notes/1085152"}, {"RefNumber": "1083722", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1083722"}, {"RefNumber": "1083464", "RefComponent": "XX-PART-ISHMED-ORD", "RefTitle": "Clin. Patient Management: BAdI for Case Validity Check", "RefUrl": "/notes/1083464"}, {"RefNumber": "1081156", "RefComponent": "PY-BR", "RefTitle": "Accessible Simple Lists", "RefUrl": "/notes/1081156"}, {"RefNumber": "1077619", "RefComponent": "BC-CTS-TLS", "RefTitle": "R3trans deletes table entries protected by TRESC", "RefUrl": "/notes/1077619"}, {"RefNumber": "1075750", "RefComponent": "XX-PART-ISHMED", "RefTitle": "Technical adaptations", "RefUrl": "/notes/1075750"}, {"RefNumber": "1074757", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1074757"}, {"RefNumber": "1073475", "RefComponent": "PY-BR", "RefTitle": "HBRPAYR0 - corrections in alv list display", "RefUrl": "/notes/1073475"}, {"RefNumber": "107210", "RefComponent": "PY-PT", "RefTitle": "HR-PT: Update of legal limits (LCP)", "RefUrl": "/notes/107210"}, {"RefNumber": "1071989", "RefComponent": "PY-AT", "RefTitle": "ALV-Beitrag für Männer ab 56 (über Verrechnungsgruppen)", "RefUrl": "/notes/1071989"}, {"RefNumber": "1071848", "RefComponent": "XX-PART-ISHMED", "RefTitle": "Technical Adaptation", "RefUrl": "/notes/1071848"}, {"RefNumber": "1071837", "RefComponent": "XX-PART-ISHMED", "RefTitle": "Clinical Order: Duplicatre Entries in Table N1COMPA", "RefUrl": "/notes/1071837"}, {"RefNumber": "1071814", "RefComponent": "IS-DFS-PDR", "RefTitle": "UPS-Werksfilter", "RefUrl": "/notes/1071814"}, {"RefNumber": "1064225", "RefComponent": "PY-AT", "RefTitle": "Pendlerpauschale Anhebung ab 01.07.2007", "RefUrl": "/notes/1064225"}, {"RefNumber": "1061374", "RefComponent": "PY-AT", "RefTitle": "AV-Beitrag für Männer ab 56: Rückrechnung aus Folgeperioden", "RefUrl": "/notes/1061374"}, {"RefNumber": "1060793", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1060793"}, {"RefNumber": "1057221", "RefComponent": "XX-CSC-CH-IS-H", "RefTitle": "IS-H CH: Kostenträgergruppen analog zu Leistungsgruppen", "RefUrl": "/notes/1057221"}, {"RefNumber": "1053864", "RefComponent": "XX-CSC-AT-IS-H", "RefTitle": "IS-H AT: EDIVKA BC-Set ISH_TNWAT_EDI_CODES_S für 6.00", "RefUrl": "/notes/1053864"}, {"RefNumber": "1051775", "RefComponent": "XX-CSC-AT-IS-H", "RefTitle": "IS-H AT: Wikraf - V01-Datensatz übersteuern", "RefUrl": "/notes/1051775"}, {"RefNumber": "1051011", "RefComponent": "XX-CSC-AT-IS-H", "RefTitle": "IS-H AT: EDI Nachrichtenversand mit Kommentar", "RefUrl": "/notes/1051011"}, {"RefNumber": "1049498", "RefComponent": "XX-CSC-CH-IS-H", "RefTitle": "IS-H CH: Fallartwechsel stat->amb - Prf alte Aufn. Lgen.", "RefUrl": "/notes/1049498"}, {"RefNumber": "1049268", "RefComponent": "PY-US-TX", "RefTitle": "TAX: No cross-company cumul. for million $ suppl. wages.", "RefUrl": "/notes/1049268"}, {"RefNumber": "104838", "RefComponent": "PP-IS", "RefTitle": "Collective note SFIS, Release 3.1I", "RefUrl": "/notes/104838"}, {"RefNumber": "1046658", "RefComponent": "SRM-EBP-WFL", "RefTitle": "Workflow Transports", "RefUrl": "/notes/1046658"}, {"RefNumber": "1046190", "RefComponent": "CRM-ANA-OR", "RefTitle": "OLTP Reporting: Texts for Service Ticket Category", "RefUrl": "/notes/1046190"}, {"RefNumber": "1046118", "RefComponent": "FI-AA-IS", "RefTitle": "Audit Info System (FI-AA) - Supplement for Note 1013282", "RefUrl": "/notes/1046118"}, {"RefNumber": "104558", "RefComponent": "PP-IS", "RefTitle": "Collective note PPIS, Release 3.1H", "RefUrl": "/notes/104558"}, {"RefNumber": "1044755", "RefComponent": "XX-CSC-CH-IS-H", "RefTitle": "IS-H CH: Prüfung Änderung der Patientenklasse nach Lstgen", "RefUrl": "/notes/1044755"}, {"RefNumber": "1044189", "RefComponent": "XX-CSC-AT-IS-H", "RefTitle": "IS-H AT: PPR Einstufung - Auswertung RNWATPPR01", "RefUrl": "/notes/1044189"}, {"RefNumber": "1044007", "RefComponent": "PY-BE-PS", "RefTitle": "The B2A process for DmfAPPL declarations is not supported", "RefUrl": "/notes/1044007"}, {"RefNumber": "1042965", "RefComponent": "CRM-FRW-AFP", "RefTitle": "Launch transactions get an own authorization object", "RefUrl": "/notes/1042965"}, {"RefNumber": "1040911", "RefComponent": "PY-BR", "RefTitle": "HBRRAIS0 - 13th Salary advanced for the next year", "RefUrl": "/notes/1040911"}, {"RefNumber": "1038939", "RefComponent": "CO-PA-TO", "RefTitle": "Archiving profitability segments II.", "RefUrl": "/notes/1038939"}, {"RefNumber": "1038634", "RefComponent": "PY-PT", "RefTitle": "HR-PT: Legal change, fiscal status table update", "RefUrl": "/notes/1038634"}, {"RefNumber": "1037157", "RefComponent": "PY-BR", "RefTitle": "HR-BR: General enhancements", "RefUrl": "/notes/1037157"}, {"RefNumber": "103682", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/103682"}, {"RefNumber": "1034838", "RefComponent": "PY-PT", "RefTitle": "HR-PT: RPCGSRP0 changed to show error list", "RefUrl": "/notes/1034838"}, {"RefNumber": "1033795", "RefComponent": "SRM-EBP-INV", "RefTitle": "BBPERS: Value-added tax not displayed for each credit memo", "RefUrl": "/notes/1033795"}, {"RefNumber": "1028682", "RefComponent": "XX-CSC-CH-IS-H", "RefTitle": "IS-H CH: RNLAUS01 - Bestandsauszug um CH-<PERSON><PERSON> erweitern", "RefUrl": "/notes/1028682"}, {"RefNumber": "1025933", "RefComponent": "PY-BR", "RefTitle": "New electronic file MANAD", "RefUrl": "/notes/1025933"}, {"RefNumber": "1025461", "RefComponent": "XX-CSC-AT-IS-H", "RefTitle": "IS-H AT: Klinischer Arbeitsplatz (AT-Feldbefüllung)", "RefUrl": "/notes/1025461"}, {"RefNumber": "102306", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Wrong values in statistics update", "RefUrl": "/notes/102306"}, {"RefNumber": "1019028", "RefComponent": "XX-PROJ-CDP-SPL", "RefTitle": "CATFORD CDP SPL-CA 1c: modifications via crosstransport", "RefUrl": "/notes/1019028"}, {"RefNumber": "1017574", "RefComponent": "XX-CSC-AT-IS-H", "RefTitle": "IS-H AT: Einstellungen fürs Storno abhängiger Leistungen", "RefUrl": "/notes/1017574"}, {"RefNumber": "1014911", "RefComponent": "PY-BR", "RefTitle": "HBRRAIS0 Legal Changes 2007", "RefUrl": "/notes/1014911"}, {"RefNumber": "1014314", "RefComponent": "XX-PROJ-CDP-010-R3", "RefTitle": "Guidelines to install MRS Utilization report on WAS 620", "RefUrl": "/notes/1014314"}, {"RefNumber": "1013282", "RefComponent": "FI-AA-IS", "RefTitle": "Audit Information System (FI-AA) - missing functions", "RefUrl": "/notes/1013282"}, {"RefNumber": "1013196", "RefComponent": "XX-PART-ISHMED", "RefTitle": "Enable Nursing Staff Regulation Transfer for Prospective Ser", "RefUrl": "/notes/1013196"}, {"RefNumber": "1012649", "RefComponent": "PY-BR", "RefTitle": "Legal change in HBRDIRF0 for year 2007", "RefUrl": "/notes/1012649"}, {"RefNumber": "101217", "RefComponent": "BC-DB-DB2", "RefTitle": "DB2 z/OS: Overview of transports and corrections", "RefUrl": "/notes/101217"}, {"RefNumber": "1011956", "RefComponent": "XX-CSC-CH-IS-H", "RefTitle": "IS-H CH: RNWCHSKH1 - hospital statistics", "RefUrl": "/notes/1011956"}, {"RefNumber": "1009718", "RefComponent": "XX-CSC-AT-IS-H", "RefTitle": "IS-H AT: PPR Einstufung - prospektive Daten", "RefUrl": "/notes/1009718"}, {"RefNumber": "1006767", "RefComponent": "PY-AT-PS", "RefTitle": "Jahreswechsel 2006/07 (öffentlicher Dienst): Änderungen BVA", "RefUrl": "/notes/1006767"}, {"RefNumber": "100609", "RefComponent": "FI-GL-IS", "RefTitle": "Audit Information System (AIS) - installation", "RefUrl": "/notes/100609"}, {"RefNumber": "100475", "RefComponent": "CA-OIW", "RefTitle": "IMG documentation for Open information Warehouse", "RefUrl": "/notes/100475"}, {"RefNumber": "100218", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/100218"}, {"RefNumber": "1000077", "RefComponent": "MM-IM", "RefTitle": "FAQ: Problems when implementing Note 32236", "RefUrl": "/notes/1000077"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2554422", "RefComponent": "BC-ABA-TO", "RefTitle": "VARI and VARINUM different number of entries", "RefUrl": "/notes/2554422 "}, {"RefNumber": "2519954", "RefComponent": "BC-SRV-APS-EXT-FLD", "RefTitle": "Syntax Error on CL_CFD_ODATA_METADATA While Activating OData Service", "RefUrl": "/notes/2519954 "}, {"RefNumber": "2394936", "RefComponent": "BC-CTS-TMS", "RefTitle": "Which port is used during file transfer in TMS", "RefUrl": "/notes/2394936 "}, {"RefNumber": "3418787", "RefComponent": "PY-HU", "RefTitle": "HR-HU: SP02/2024 - Tax certificates 2024 - Attachm.", "RefUrl": "/notes/3418787 "}, {"RefNumber": "3334579", "RefComponent": "PY-HU", "RefTitle": "HR-HU: SP06/2023 - SI Enhancement I. - Attachm.", "RefUrl": "/notes/3334579 "}, {"RefNumber": "3327756", "RefComponent": "PY-HU", "RefTitle": "HR-HU: SP06/2023 - RPSMKFH2 Workforce cost entry - Attachm.", "RefUrl": "/notes/3327756 "}, {"RefNumber": "3321425", "RefComponent": "PY-PL", "RefTitle": "Labor code - Work-life balance - attachment", "RefUrl": "/notes/3321425 "}, {"RefNumber": "3288515", "RefComponent": "PY-HU", "RefTitle": "HR-HU: SP02/2023 - Payroll-M30-1405/1668 customizing 2023 - Attachm.", "RefUrl": "/notes/3288515 "}, {"RefNumber": "3287836", "RefComponent": "PY-HU", "RefTitle": "HR-HU: SP02/2023 - Data sheet on termination of employment - Attachm.", "RefUrl": "/notes/3287836 "}, {"RefNumber": "3214432", "RefComponent": "PY-HU", "RefTitle": "HR-HU: SP07/2022 - SI Reports UDO", "RefUrl": "/notes/3214432 "}, {"RefNumber": "3208706", "RefComponent": "PY-HU", "RefTitle": "HR-HU: SP07/2022 - RPCBRKH2 Payroll Account UDO", "RefUrl": "/notes/3208706 "}, {"RefNumber": "3203267", "RefComponent": "PY-HU", "RefTitle": "HR-HU: SP06/2022 - IT3265 External Service II. UDO", "RefUrl": "/notes/3203267 "}, {"RefNumber": "3198851", "RefComponent": "PY-HU", "RefTitle": "HR-HU: SP06/2022 - IT3265 External Service UDO", "RefUrl": "/notes/3198851 "}, {"RefNumber": "2984436", "RefComponent": "PY-CZ", "RefTitle": "HRCZ - Vacation entitlement in hours 2021 - [3] - Customizing", "RefUrl": "/notes/2984436 "}, {"RefNumber": "2910013", "RefComponent": "PY-AT", "RefTitle": "COVID-19-<PERSON><PERSON><PERSON><PERSON><PERSON>: Customizing via SAR-File", "RefUrl": "/notes/2910013 "}, {"RefNumber": "2853758", "RefComponent": "PY-AT", "RefTitle": "JW2019/20: ELDA Customizing", "RefUrl": "/notes/2853758 "}, {"RefNumber": "2733416", "RefComponent": "PY-US-TR", "RefTitle": "Year End 2018 Phase III for U.S. Tax Reporter", "RefUrl": "/notes/2733416 "}, {"RefNumber": "2723165", "RefComponent": "PY-US-TX", "RefTitle": "BSI: Tax Types 098 to 103 (Washington Paid Family and Medical Leave) [CE]", "RefUrl": "/notes/2723165 "}, {"RefNumber": "2716385", "RefComponent": "PY-NPO", "RefTitle": "UNJSPF FI interface: To generate the proxy objects in ECC system", "RefUrl": "/notes/2716385 "}, {"RefNumber": "2698729", "RefComponent": "PY-US-TR", "RefTitle": "Year End 2018 Phase II for U.S. Tax Reporter", "RefUrl": "/notes/2698729 "}, {"RefNumber": "2618262", "RefComponent": "PY-US-TR", "RefTitle": "TR: Q1/2018 Functional changes for U.S. Tax Reporter [IM]", "RefUrl": "/notes/2618262 "}, {"RefNumber": "2571096", "RefComponent": "PY-US-TR", "RefTitle": "Year End 2017 Phase III for U.S. Tax Reporter", "RefUrl": "/notes/2571096 "}, {"RefNumber": "2527783", "RefComponent": "CA-MDG", "RefTitle": "SAP S/4HANA Master Data Governance 1709 FPS0 missing translation", "RefUrl": "/notes/2527783 "}, {"RefNumber": "2494176", "RefComponent": "FI-AA-AA-A", "RefTitle": "Vorabauslieferung IDOC_INPUT_FIXEDASSET_LDTPOST", "RefUrl": "/notes/2494176 "}, {"RefNumber": "2441804", "RefComponent": "FI-AP-AP-B", "RefTitle": "F110 F111: Workflow-based release of payment proposal (installation for SAP Note 2441805)", "RefUrl": "/notes/2441804 "}, {"RefNumber": "1077403", "RefComponent": "BC-DB-DBI", "RefTitle": "Cluster table check with SDBI_CLUSTER_CHECK", "RefUrl": "/notes/1077403 "}, {"RefNumber": "2405731", "RefComponent": "BC-MID-AC", "RefTitle": "ABAP Channel spezifische SYSLOG Einträge lesen", "RefUrl": "/notes/2405731 "}, {"RefNumber": "2124000", "RefComponent": "BC-CTS-TLS", "RefTitle": "Entries of client dependent customizing tables are not imported to client 000", "RefUrl": "/notes/2124000 "}, {"RefNumber": "2272227", "RefComponent": "XX-CSC-HU", "RefTitle": "Audit Function for Tax Authority - transport files delivery", "RefUrl": "/notes/2272227 "}, {"RefNumber": "2254139", "RefComponent": "PA-PA-KR", "RefTitle": "YEA2015: New Reports for Easy YEA Submission and Year End Master Data Update", "RefUrl": "/notes/2254139 "}, {"RefNumber": "2067935", "RefComponent": "PY-AT", "RefTitle": "Buchung: Reporterweiterungen für NGL", "RefUrl": "/notes/2067935 "}, {"RefNumber": "2247761", "RefComponent": "PY-KR", "RefTitle": "YEA2015: EE Stock Tax Credit, Deduciton Order, and YED Receipt", "RefUrl": "/notes/2247761 "}, {"RefNumber": "2241141", "RefComponent": "BC-UPG-TLS-TLA", "RefTitle": "How to restore missing dynpros after upgrade", "RefUrl": "/notes/2241141 "}, {"RefNumber": "2167502", "RefComponent": "PA-PA-US-BN", "RefTitle": "BEN: Main Note - Affordable Care Act (ACA)", "RefUrl": "/notes/2167502 "}, {"RefNumber": "2175113", "RefComponent": "PA-PA-KR", "RefTitle": "LC2015: Simple Tax Table, Special Deduction of YEA Simulation Tax Method and Income Tax Percentage", "RefUrl": "/notes/2175113 "}, {"RefNumber": "2161214", "RefComponent": "PY-XX-TL", "RefTitle": "Delivery of HR Development Packages with correct software components", "RefUrl": "/notes/2161214 "}, {"RefNumber": "2148559", "RefComponent": "PY-KR", "RefTitle": "LC2015: 2014 YEA Retro-accounting Phase I - Payroll", "RefUrl": "/notes/2148559 "}, {"RefNumber": "2131541", "RefComponent": "PY-KR", "RefTitle": "LC2015: No Installment Option for YEA Collective Income Tax", "RefUrl": "/notes/2131541 "}, {"RefNumber": "2131405", "RefComponent": "PY-XX", "RefTitle": "Template - Software delivery for SAP Note YYYYYYY", "RefUrl": "/notes/2131405 "}, {"RefNumber": "2132726", "RefComponent": "XX-CSC-IL", "RefTitle": "Updated version for IL WHT report for year 2014", "RefUrl": "/notes/2132726 "}, {"RefNumber": "2116347", "RefComponent": "PY-KR", "RefTitle": "LC2015: Installment of YEA Collectible Income Tax", "RefUrl": "/notes/2116347 "}, {"RefNumber": "2116271", "RefComponent": "PY-KR", "RefTitle": "LC2015: Receipt of Credit Card and Other Expenses and Deduction of Pension Account, Special and Monthly Rental Tax Credit", "RefUrl": "/notes/2116271 "}, {"RefNumber": "2122002", "RefComponent": "PY-BR", "RefTitle": "eSocial: ABAP dictionary - Release 2015/02", "RefUrl": "/notes/2122002 "}, {"RefNumber": "2107672", "RefComponent": "PY-ID", "RefTitle": "Legal Change - New Social Security ID for BPJS <PERSON>", "RefUrl": "/notes/2107672 "}, {"RefNumber": "2113077", "RefComponent": "PY-TH", "RefTitle": "PY-LC: New Version of PIT91 Form for 2015 Year", "RefUrl": "/notes/2113077 "}, {"RefNumber": "2106250", "RefComponent": "XX-CSC-EE-FI", "RefTitle": "Estonian Value-Added TAX report for ECC500 version", "RefUrl": "/notes/2106250 "}, {"RefNumber": "2096839", "RefComponent": "PY-US-BSI", "RefTitle": "BSI: Employee Type Indicator and Date of Death enhancements", "RefUrl": "/notes/2096839 "}, {"RefNumber": "2047104", "RefComponent": "PY-JP", "RefTitle": "LC2014: Income Tax Rate Revision from 2015", "RefUrl": "/notes/2047104 "}, {"RefNumber": "2082316", "RefComponent": "PY-GB", "RefTitle": "PY-GB Missing objects of 6.08 Initial Delivery", "RefUrl": "/notes/2082316 "}, {"RefNumber": "2069440", "RefComponent": "PY-BR", "RefTitle": "NIS - Data Dictionary Objects", "RefUrl": "/notes/2069440 "}, {"RefNumber": "2061627", "RefComponent": "PA-PA-JP", "RefTitle": "LC2014: Output Reason of Normal Collection to SPR", "RefUrl": "/notes/2061627 "}, {"RefNumber": "2047800", "RefComponent": "PY-GB", "RefTitle": "PY-GB: RTI - EYU for previous years (Customizing entries)", "RefUrl": "/notes/2047800 "}, {"RefNumber": "2044528", "RefComponent": "PY-GB-PS", "RefTitle": "PY-GB: HESA 2013/14 - Extraction (Customzing entries)", "RefUrl": "/notes/2044528 "}, {"RefNumber": "2005618", "RefComponent": "PY-JP", "RefTitle": "LC2014: Health Insurance Dependent Declaration data files", "RefUrl": "/notes/2005618 "}, {"RefNumber": "2001214", "RefComponent": "PA-PA-JP", "RefTitle": "LC2014: Leave Information and Statutory Forms for Maternity Leave", "RefUrl": "/notes/2001214 "}, {"RefNumber": "1997972", "RefComponent": "PY-US-TR", "RefTitle": "TR: Electronic Confirmation Number for W-2/W-2c PR paper", "RefUrl": "/notes/1997972 "}, {"RefNumber": "1994860", "RefComponent": "PY-RU", "RefTitle": "New Form RSV-1 in 2014 year: Adobe, XML and Customizing", "RefUrl": "/notes/1994860 "}, {"RefNumber": "1985001", "RefComponent": "PY-KR", "RefTitle": "LC 2014: Special deduction for YEA simulation tax method", "RefUrl": "/notes/1985001 "}, {"RefNumber": "1961195", "RefComponent": "PY-SG", "RefTitle": "Overtime eligibility", "RefUrl": "/notes/1961195 "}, {"RefNumber": "1631657", "RefComponent": "BC-SRV-BSF-CUR", "RefTitle": "ESOA service ExchangeRateByExchangeRateQueryResponse", "RefUrl": "/notes/1631657 "}, {"RefNumber": "1982079", "RefComponent": "PY-BR", "RefTitle": "eSocial: Release 2014/04 - Advanced Delivery", "RefUrl": "/notes/1982079 "}, {"RefNumber": "1977843", "RefComponent": "PY-KR", "RefTitle": "LC2014: Simple Tax Table Effective on February 21", "RefUrl": "/notes/1977843 "}, {"RefNumber": "1977165", "RefComponent": "PY-KR", "RefTitle": "LC2014: Simple Tax Table Effective on February 21", "RefUrl": "/notes/1977165 "}, {"RefNumber": "1978246", "RefComponent": "PY-KZ", "RefTitle": "Missing country-specific customizing for HCM Kazakhstan", "RefUrl": "/notes/1978246 "}, {"RefNumber": "1967981", "RefComponent": "PY-BR", "RefTitle": "eSocial: ABAP dictionary - Release 2014.02", "RefUrl": "/notes/1967981 "}, {"RefNumber": "1946442", "RefComponent": "PY-US-TR", "RefTitle": "TR: Electronic Confirmation Number for W-2/W-2c PR paper and online", "RefUrl": "/notes/1946442 "}, {"RefNumber": "1953210", "RefComponent": "PY-KR", "RefTitle": "2014 Legal Changes regarding to Tax Calculation and Social Insurance", "RefUrl": "/notes/1953210 "}, {"RefNumber": "1961081", "RefComponent": "PY-BR", "RefTitle": "eSocial: ABAP dictionary - Release 2014.01", "RefUrl": "/notes/1961081 "}, {"RefNumber": "1953212", "RefComponent": "PY-KR", "RefTitle": "2014 Legal Changes regarding to Tax Calculation and Social Insurance", "RefUrl": "/notes/1953212 "}, {"RefNumber": "1946818", "RefComponent": "XX-CSC-PL-HR", "RefTitle": "HRPL: PIT-40 tax form - 2013, transport files", "RefUrl": "/notes/1946818 "}, {"RefNumber": "1946817", "RefComponent": "XX-CSC-PL-HR", "RefTitle": "HRPL: PIT-40 tax form - 2013, transport files", "RefUrl": "/notes/1946817 "}, {"RefNumber": "1909014", "RefComponent": "PY-US-BSI", "RefTitle": "BSI TaxFactory 10.0 - Phase II", "RefUrl": "/notes/1909014 "}, {"RefNumber": "1933131", "RefComponent": "XX-CSC-HU-HR", "RefTitle": "Corrections for Hungary before HR-CEE SP 11/2013pre Att.", "RefUrl": "/notes/1933131 "}, {"RefNumber": "1771064", "RefComponent": "PY-US", "RefTitle": "USCLM: Incorrect prorating with wage type grouping", "RefUrl": "/notes/1771064 "}, {"RefNumber": "1713226", "RefComponent": "PY-US", "RefTitle": "USCLM: U.S. Overpayment Wage Type Grouping - Advanced Deliv.", "RefUrl": "/notes/1713226 "}, {"RefNumber": "1862873", "RefComponent": "XX-CSC-SK-HR", "RefTitle": "HRSK: SEPA in SK master data - transport", "RefUrl": "/notes/1862873 "}, {"RefNumber": "1921347", "RefComponent": "XX-CSC-HU-HR", "RefTitle": "Corrections for Hungary before HR-CEE SP 11/2013pre1 Att.", "RefUrl": "/notes/1921347 "}, {"RefNumber": "69455", "RefComponent": "SV-SMG-SDD", "RefTitle": "Servicetools for Applications ST-A/PI (ST14, RTCCTOOL, ST12)", "RefUrl": "/notes/69455 "}, {"RefNumber": "1803009", "RefComponent": "XX-CSC-BR-NFE", "RefTitle": "Renewal of Screen Controls in Nota Fiscal Writer", "RefUrl": "/notes/1803009 "}, {"RefNumber": "1894670", "RefComponent": "PY-GB-PS", "RefTitle": "PY-GB-PS: Schools Workforce Census 2013", "RefUrl": "/notes/1894670 "}, {"RefNumber": "1860433", "RefComponent": "XX-CSC-BR-NFE", "RefTitle": "NF-e: Storing Additional Data for DANFE & Reporting", "RefUrl": "/notes/1860433 "}, {"RefNumber": "1868676", "RefComponent": "XX-CSC-RU-FI", "RefTitle": "J3RTAXREP - Transport files regarding XML Generator solution", "RefUrl": "/notes/1868676 "}, {"RefNumber": "1920654", "RefComponent": "PY-US-TR", "RefTitle": "Year End 2013 Phase II for U.S. Tax Reporter", "RefUrl": "/notes/1920654 "}, {"RefNumber": "1862600", "RefComponent": "FI-LOC-SAF-PT", "RefTitle": "RPFIEU_SAFT Solution :DDIC Note", "RefUrl": "/notes/1862600 "}, {"RefNumber": "1523958", "RefComponent": "XX-CSC-BR-REP", "RefTitle": "EFD-Contributions New Legal File", "RefUrl": "/notes/1523958 "}, {"RefNumber": "1850491", "RefComponent": "PY-GB", "RefTitle": "PY-GB: PAE Collection of PAE Corrections II", "RefUrl": "/notes/1850491 "}, {"RefNumber": "1912154", "RefComponent": "XX-CSC-HU-HR", "RefTitle": "Corrections for Hungary before HR-CEE SP 10/2013pre2 Att.", "RefUrl": "/notes/1912154 "}, {"RefNumber": "1912230", "RefComponent": "XX-CSC-HU-HR", "RefTitle": "Corrections for Hungary before HR-CEE SP 10/2013pre4 Att.", "RefUrl": "/notes/1912230 "}, {"RefNumber": "1904585", "RefComponent": "XX-CSC-HU-HR", "RefTitle": "Corrections for Hungary before HR-CEE SP 10/2013pre1 Att.", "RefUrl": "/notes/1904585 "}, {"RefNumber": "1876296", "RefComponent": "FI-AP-AP-B1", "RefTitle": "CBR-PT: Portugal CBR Development", "RefUrl": "/notes/1876296 "}, {"RefNumber": "1794880", "RefComponent": "FI-AP-AP-B1", "RefTitle": "PT-CBR: Portugal Central Bank Reporting", "RefUrl": "/notes/1794880 "}, {"RefNumber": "1790759", "RefComponent": "XX-CSC-HU-HR", "RefTitle": "Legal changes for Hungary before HR-CEE SP 12-2/2012 Attach.", "RefUrl": "/notes/1790759 "}, {"RefNumber": "1811664", "RefComponent": "XX-CSC-HU-HR", "RefTitle": "Legal changes for Hungary before HR-CEE SP 02/2013pre6 Att.", "RefUrl": "/notes/1811664 "}, {"RefNumber": "1808958", "RefComponent": "XX-CSC-HU-HR", "RefTitle": "Legal changes for Hungary before HR-CEE SP 02/2013pre5 Att.", "RefUrl": "/notes/1808958 "}, {"RefNumber": "1813535", "RefComponent": "XX-CSC-HU-HR", "RefTitle": "Legal changes for Hungary before HR-CEE SP 02/2013pre7 Att.", "RefUrl": "/notes/1813535 "}, {"RefNumber": "1835532", "RefComponent": "XX-CSC-HU-HR", "RefTitle": "Legal changes for Hungary before HR-CEE SP 04/2013pre3 Att.", "RefUrl": "/notes/1835532 "}, {"RefNumber": "1843755", "RefComponent": "XX-CSC-HU-HR", "RefTitle": "Legal changes for Hungary before HR-CEE SP 05/2013pre3 Att.", "RefUrl": "/notes/1843755 "}, {"RefNumber": "1843720", "RefComponent": "XX-CSC-HU-HR", "RefTitle": "Legal changes for Hungary before HR-CEE SP 05/2013pre2 Att.", "RefUrl": "/notes/1843720 "}, {"RefNumber": "1841315", "RefComponent": "XX-CSC-HU-HR", "RefTitle": "Legal changes for Hungary before HR-CEE SP 05/2013pre1 Att.", "RefUrl": "/notes/1841315 "}, {"RefNumber": "1892841", "RefComponent": "XX-CSC-BR", "RefTitle": "Master Data - DDics - Advanced delivery files", "RefUrl": "/notes/1892841 "}, {"RefNumber": "1851494", "RefComponent": "XX-CSC-HU-HR", "RefTitle": "Legal changes for Hungary before HR-CEE SP 06/2013pre3 Att.", "RefUrl": "/notes/1851494 "}, {"RefNumber": "1855654", "RefComponent": "XX-CSC-HU-HR", "RefTitle": "Legal changes for Hungary before HR-CEE SP 06/2013pre2 Att.", "RefUrl": "/notes/1855654 "}, {"RefNumber": "1851555", "RefComponent": "XX-CSC-HU-HR", "RefTitle": "Legal changes for Hungary before HR-CEE SP 06/2013pre1 Att.", "RefUrl": "/notes/1851555 "}, {"RefNumber": "1700332", "RefComponent": "XX-CSC-PT", "RefTitle": "Transport files for digital signature for OBD - Portugal", "RefUrl": "/notes/1700332 "}, {"RefNumber": "1893137", "RefComponent": "XX-CSC-HU-HR", "RefTitle": "Corrections for Hungary before HR-CEE SP 09/2013pre1 Att.", "RefUrl": "/notes/1893137 "}, {"RefNumber": "1845468", "RefComponent": "PY-US-TX", "RefTitle": "TAX: Wage types for Additional Medicare tax", "RefUrl": "/notes/1845468 "}, {"RefNumber": "1897465", "RefComponent": "XX-CSC-HU-HR", "RefTitle": "Corrections for Hungary before HR-CEE SP 09/2013pre2 Att.", "RefUrl": "/notes/1897465 "}, {"RefNumber": "1901223", "RefComponent": "XX-CSC-HU-HR", "RefTitle": "Legal changes for Hungary before HR-CEE SP 09/2013pre3 Att.", "RefUrl": "/notes/1901223 "}, {"RefNumber": "1706611", "RefComponent": "PY-IT", "RefTitle": "HR-IT: Modello 770-2012 - Advance delivery", "RefUrl": "/notes/1706611 "}, {"RefNumber": "1886530", "RefComponent": "XX-CSC-HU-HR", "RefTitle": "Legal changes for Hungary before HR-CEE SP 08/2013pre3 Att.", "RefUrl": "/notes/1886530 "}, {"RefNumber": "1426524", "RefComponent": "XX-CSC-XX", "RefTitle": "Calendar entries", "RefUrl": "/notes/1426524 "}, {"RefNumber": "1886532", "RefComponent": "XX-CSC-HU-HR", "RefTitle": "Legal changes for Hungary before HR-CEE SP 08/2013pre4 Att.", "RefUrl": "/notes/1886532 "}, {"RefNumber": "1889748", "RefComponent": "PY-IE", "RefTitle": "HRIE: LPT, XML definitions and HR Forms (SAR file)", "RefUrl": "/notes/1889748 "}, {"RefNumber": "1870139", "RefComponent": "PY-US-TR", "RefTitle": "Q2/2013: Functional changes for U.S. Tax Reporter.", "RefUrl": "/notes/1870139 "}, {"RefNumber": "1879924", "RefComponent": "PY-US-TR", "RefTitle": "Q2/2013: Functional changes for U.S. Tax Reporter.", "RefUrl": "/notes/1879924 "}, {"RefNumber": "1877805", "RefComponent": "PY-US-TR", "RefTitle": "TR: New format for SUI Magmedia for Michigan", "RefUrl": "/notes/1877805 "}, {"RefNumber": "1882788", "RefComponent": "XX-CSC-SI-HR", "RefTitle": "HRSI: ZPIZ-2 with validity 07/2013: payroll calculation-file", "RefUrl": "/notes/1882788 "}, {"RefNumber": "1885890", "RefComponent": "XX-CSC-HU-HR", "RefTitle": "Legal changes for Hungary before HR-CEE SP 08/2013pre2 Att.", "RefUrl": "/notes/1885890 "}, {"RefNumber": "1864828", "RefComponent": "PY-GB", "RefTitle": "PY-GB: HESA 2012/13 (SAR Files)", "RefUrl": "/notes/1864828 "}, {"RefNumber": "49365", "RefComponent": "XX-INT-FA-MAKE", "RefTitle": "iSeries: Applying a patch", "RefUrl": "/notes/49365 "}, {"RefNumber": "1881496", "RefComponent": "XX-CSC-HU-HR", "RefTitle": "Correction for Hungary before HR-CEE SP 08/2013pre1 Att.", "RefUrl": "/notes/1881496 "}, {"RefNumber": "1866656", "RefComponent": "XX-CSC-HU-HR", "RefTitle": "Legal changes for Hungary before HR-CEE SP 07/2013pre1 Att.", "RefUrl": "/notes/1866656 "}, {"RefNumber": "1836883", "RefComponent": "FS-LOC-GEN", "RefTitle": "Tax Manager: Adjustments for LATAM release", "RefUrl": "/notes/1836883 "}, {"RefNumber": "1875524", "RefComponent": "XX-CSC-HU-HR", "RefTitle": "Hungarian objects of HR-CEE SP 06/2013 Attachments", "RefUrl": "/notes/1875524 "}, {"RefNumber": "1865266", "RefComponent": "XX-CSC-HU-HR", "RefTitle": "Hungarian objects of HR-CEE SP 06/2013 Attachments", "RefUrl": "/notes/1865266 "}, {"RefNumber": "1811063", "RefComponent": "PY-AT", "RefTitle": "JW 2012/13 Beitragsnachweisung für überlassene Dienstnehmer", "RefUrl": "/notes/1811063 "}, {"RefNumber": "1870555", "RefComponent": "PY-GB", "RefTitle": "PY-GB: IT0065 and RTI Display report (SAR Files)", "RefUrl": "/notes/1870555 "}, {"RefNumber": "1867785", "RefComponent": "PY-IE", "RefTitle": "HRIE: Local Property Tax (SAR Files)", "RefUrl": "/notes/1867785 "}, {"RefNumber": "1867784", "RefComponent": "PY-IE", "RefTitle": "HRIE: Local Property Tax (SAR Files)", "RefUrl": "/notes/1867784 "}, {"RefNumber": "1857614", "RefComponent": "PY-GB", "RefTitle": "PY-GB: P11D - E-Filling (SAR files)", "RefUrl": "/notes/1857614 "}, {"RefNumber": "1791068", "RefComponent": "PA-PA-CN", "RefTitle": "Obsolete - Enhancement of HCM Report Management Platform", "RefUrl": "/notes/1791068 "}, {"RefNumber": "327088", "RefComponent": "FI-AA-AA-C", "RefTitle": "Definition of the delivered transfer variants", "RefUrl": "/notes/327088 "}, {"RefNumber": "1821617", "RefComponent": "PY-ZA", "RefTitle": "Variable Pay Streamlined Timing - Tax Year 2013-14 - Phase 2", "RefUrl": "/notes/1821617 "}, {"RefNumber": "1640083", "RefComponent": "PY-NPO", "RefTitle": "UNJSPF Common HR Interface", "RefUrl": "/notes/1640083 "}, {"RefNumber": "1859625", "RefComponent": "XX-CSC-SK-HR", "RefTitle": "HRSK: The Rounding of the Tax Calculation is Wrong", "RefUrl": "/notes/1859625 "}, {"RefNumber": "1353458", "RefComponent": "XX-CSC-IL", "RefTitle": "Migration to LOCILERP add-on solution", "RefUrl": "/notes/1353458 "}, {"RefNumber": "1861106", "RefComponent": "XX-CSC-HU-HR", "RefTitle": "Legal changes for Hungary before HR-CEE SP 06/2013pre4 Att.", "RefUrl": "/notes/1861106 "}, {"RefNumber": "1861123", "RefComponent": "XX-CSC-HU-HR", "RefTitle": "Legal changes for Hungary before HR-CEE SP 06/2013pre4 Att.", "RefUrl": "/notes/1861123 "}, {"RefNumber": "1858787", "RefComponent": "XX-CSC-HU-HR", "RefTitle": "Hungarian objects of HR-CEE SP 05/2013 Attachments", "RefUrl": "/notes/1858787 "}, {"RefNumber": "1851498", "RefComponent": "XX-CSC-HU-HR", "RefTitle": "Legal changes for Hungary before HR-CEE SP 06/2013pre3 Att.", "RefUrl": "/notes/1851498 "}, {"RefNumber": "1855657", "RefComponent": "XX-CSC-HU-HR", "RefTitle": "Legal changes for Hungary before HR-CEE SP 06/2013pre2 Att.", "RefUrl": "/notes/1855657 "}, {"RefNumber": "1851779", "RefComponent": "XX-CSC-HU-HR", "RefTitle": "Hungarian objects of HR-CEE SP 04/2013 Attachments", "RefUrl": "/notes/1851779 "}, {"RefNumber": "1851557", "RefComponent": "XX-CSC-HU-HR", "RefTitle": "Legal changes for Hungary before HR-CEE SP 06/2013pre1 Att.", "RefUrl": "/notes/1851557 "}, {"RefNumber": "1843761", "RefComponent": "XX-CSC-HU-HR", "RefTitle": "Legal changes for Hungary before HR-CEE SP 05/2013pre3 Att.", "RefUrl": "/notes/1843761 "}, {"RefNumber": "1843753", "RefComponent": "XX-CSC-HU-HR", "RefTitle": "Legal changes for Hungary before HR-CEE SP 05/2013pre2 Att.", "RefUrl": "/notes/1843753 "}, {"RefNumber": "1843662", "RefComponent": "XX-CSC-HU-HR", "RefTitle": "Legal changes for Hungary before HR-CEE SP 05/2013pre1 Att.", "RefUrl": "/notes/1843662 "}, {"RefNumber": "1851443", "RefComponent": "PY-NPO", "RefTitle": "ESS Applications : Enhancements and Corrections", "RefUrl": "/notes/1851443 "}, {"RefNumber": "1697948", "RefComponent": "XX-PROJ-IMS-HA", "RefTitle": "Analysis of access sequences using access type A", "RefUrl": "/notes/1697948 "}, {"RefNumber": "1849244", "RefComponent": "PY-US-TX", "RefTitle": "TAX: PA LST lump-sum/prorated amount customizing", "RefUrl": "/notes/1849244 "}, {"RefNumber": "1836869", "RefComponent": "PY-US-TX", "RefTitle": "TAX: PA LST lump-sum/prorated amount customizing", "RefUrl": "/notes/1836869 "}, {"RefNumber": "364368", "RefComponent": "CO-PC-ACT", "RefTitle": "Material ledger help desk", "RefUrl": "/notes/364368 "}, {"RefNumber": "1784569", "RefComponent": "PY-US-TR", "RefTitle": "TR: Form 941 PR - Issue in boxes 7 and 9", "RefUrl": "/notes/1784569 "}, {"RefNumber": "1807064", "RefComponent": "PY-ZA", "RefTitle": "Variable Pay Streamlined Timing - Tax Year 2013-14 - Phase 1", "RefUrl": "/notes/1807064 "}, {"RefNumber": "1826041", "RefComponent": "PY-US-TR", "RefTitle": "Q1/2013: Functional changes for U.S. Tax Reporter.", "RefUrl": "/notes/1826041 "}, {"RefNumber": "1803358", "RefComponent": "PY-US-TR", "RefTitle": "TR: SUI Monthly Reporting for the State of Illinois.", "RefUrl": "/notes/1803358 "}, {"RefNumber": "311979", "RefComponent": "MM-IS-IC-DTC", "RefTitle": "Error message during check of S094", "RefUrl": "/notes/311979 "}, {"RefNumber": "1738398", "RefComponent": "SD-BF-PR", "RefTitle": "Implementation of report CONDITION_PRESTEP_ANALYZE", "RefUrl": "/notes/1738398 "}, {"RefNumber": "1827904", "RefComponent": "PY-US-TR", "RefTitle": "Online W-2: Several W-2 displayed for overwrite scenarios", "RefUrl": "/notes/1827904 "}, {"RefNumber": "1776403", "RefComponent": "PY-US-TR", "RefTitle": "TR: W-2 creation when earns only box 12 DD", "RefUrl": "/notes/1776403 "}, {"RefNumber": "1647554", "RefComponent": "RE-FX-LC-CH", "RefTitle": "National Register of Buildings and Dwellings (GWR)", "RefUrl": "/notes/1647554 "}, {"RefNumber": "1769981", "RefComponent": "SD-BF-PR", "RefTitle": "Simplified adjustment of counters in access sequences", "RefUrl": "/notes/1769981 "}, {"RefNumber": "1624220", "RefComponent": "PY-GB", "RefTitle": "Real Time Information", "RefUrl": "/notes/1624220 "}, {"RefNumber": "1825466", "RefComponent": "PY-US-TR", "RefTitle": "TR: Delete Tax Reporter Test Execution", "RefUrl": "/notes/1825466 "}, {"RefNumber": "1829874", "RefComponent": "XX-CSC-HU-HR", "RefTitle": "Legal changes for Hungary before HR-CEE SP 04/2013pre2 Att.", "RefUrl": "/notes/1829874 "}, {"RefNumber": "1789179", "RefComponent": "PY-US-TR", "RefTitle": "Corrections to Online W-2: Wave 7", "RefUrl": "/notes/1789179 "}, {"RefNumber": "873320", "RefComponent": "FI-AA-SVA", "RefTitle": "Australian Asset Revaluation solutions", "RefUrl": "/notes/873320 "}, {"RefNumber": "1828490", "RefComponent": "XX-CSC-HU-HR", "RefTitle": "Legal changes for Hungary before HR-CEE SP 04/2013pre1 Att.", "RefUrl": "/notes/1828490 "}, {"RefNumber": "1820192", "RefComponent": "XX-CSC-HU-HR", "RefTitle": "Legal changes for Hungary before HR-CEE SP 03/2013pre3 Att.", "RefUrl": "/notes/1820192 "}, {"RefNumber": "1819217", "RefComponent": "XX-CSC-HU-HR", "RefTitle": "Legal changes for Hungary before HR-CEE SP 03/2013pre2 Att.", "RefUrl": "/notes/1819217 "}, {"RefNumber": "1817140", "RefComponent": "XX-CSC-HU-HR", "RefTitle": "Legal changes for Hungary before HR-CEE SP 03/2013pre1 Att.", "RefUrl": "/notes/1817140 "}, {"RefNumber": "1755902", "RefComponent": "RE-FX-LC-CH", "RefTitle": "National Register of Buildings and Dwellings (GWR)", "RefUrl": "/notes/1755902 "}, {"RefNumber": "1821553", "RefComponent": "PY-GB", "RefTitle": "PY-GB: PAE Collection of PAE Corrections", "RefUrl": "/notes/1821553 "}, {"RefNumber": "1730771", "RefComponent": "PY-BR", "RefTitle": "Termination Term: legal change 2012 in release 4.6c", "RefUrl": "/notes/1730771 "}, {"RefNumber": "1814638", "RefComponent": "PY-US-TR", "RefTitle": "Year End 2012 latest changes to magnetic media files", "RefUrl": "/notes/1814638 "}, {"RefNumber": "1812089", "RefComponent": "SRM-EBP-SHP", "RefTitle": "Database changes in SRM UI Add-on", "RefUrl": "/notes/1812089 "}, {"RefNumber": "678702", "RefComponent": "PP-SFC-IS", "RefTitle": "Mass processing using the info system not possible", "RefUrl": "/notes/678702 "}, {"RefNumber": "1764123", "RefComponent": "PY-US-TR", "RefTitle": "Corrections to Online W-2: Wave 6", "RefUrl": "/notes/1764123 "}, {"RefNumber": "1807998", "RefComponent": "PY-US-TX", "RefTitle": "TAX: Additional parameters for Tax Formula override.", "RefUrl": "/notes/1807998 "}, {"RefNumber": "1794832", "RefComponent": "PY-CA", "RefTitle": "YE12: Delivery of Year End Transports for 2012", "RefUrl": "/notes/1794832 "}, {"RefNumber": "1808723", "RefComponent": "XX-CSC-CN-EPIC", "RefTitle": "EPIC: Bank Reconcliation for pilot customer", "RefUrl": "/notes/1808723 "}, {"RefNumber": "1789552", "RefComponent": "XX-CSC-HU-HR", "RefTitle": "Hungarian objects of HR-CEE SP 12-1/2012 Attachments", "RefUrl": "/notes/1789552 "}, {"RefNumber": "1806892", "RefComponent": "XX-PROJ-MOF", "RefTitle": "PT-PS: New OM infotypes - advance delivery note", "RefUrl": "/notes/1806892 "}, {"RefNumber": "1808129", "RefComponent": "PY-AT", "RefTitle": "JW2012-2013: Änderungen zum Jahreswechsel nicht vorhanden", "RefUrl": "/notes/1808129 "}, {"RefNumber": "1804353", "RefComponent": "XX-CSC-HR-LO", "RefTitle": "Croatia Fiscalization : Transport Files", "RefUrl": "/notes/1804353 "}, {"RefNumber": "1806901", "RefComponent": "XX-CSC-HU-HR", "RefTitle": "Legal changes for Hungary before HR-CEE SP 02/2013pre3 Att.", "RefUrl": "/notes/1806901 "}, {"RefNumber": "1805629", "RefComponent": "XX-CSC-HU-HR", "RefTitle": "Legal changes for Hungary before HR-CEE SP 01/2013pre2 Att.", "RefUrl": "/notes/1805629 "}, {"RefNumber": "1789868", "RefComponent": "PY-US-TR", "RefTitle": "Year End 2012 Phase III for U.S. Tax Reporter", "RefUrl": "/notes/1789868 "}, {"RefNumber": "185348", "RefComponent": "BC-CUS", "RefTitle": "Customizing development menu SIMGA (as of 4.6A)", "RefUrl": "/notes/185348 "}, {"RefNumber": "96732", "RefComponent": "BC-CTS-CCO", "RefTitle": "CC-INFO: DB2/390 - client copy is very slow", "RefUrl": "/notes/96732 "}, {"RefNumber": "208919", "RefComponent": "BC-CTS-CCO", "RefTitle": "CC-INFO: Performance as of Release 4.6", "RefUrl": "/notes/208919 "}, {"RefNumber": "1534922", "RefComponent": "XX-CSC-BR-TRM", "RefTitle": "J1BTRMSTAX  - Error in the calculation", "RefUrl": "/notes/1534922 "}, {"RefNumber": "1800594", "RefComponent": "XX-CSC-PL-HR", "RefTitle": "HRPL: Limit on 50% income cost 2013 2 transport file", "RefUrl": "/notes/1800594 "}, {"RefNumber": "1799336", "RefComponent": "XX-CSC-HU-HR", "RefTitle": "Hungarian objects of HR-CEE SP 12-2/2012 Attachments", "RefUrl": "/notes/1799336 "}, {"RefNumber": "1671082", "RefComponent": "XX-CSC-HU-HR", "RefTitle": "Legal changes for Hungary before HR-CEE SP02/2012p3 Attachm.", "RefUrl": "/notes/1671082 "}, {"RefNumber": "1666380", "RefComponent": "XX-CSC-HU-HR", "RefTitle": "Legal changes for Hungary before HR-CEE SP01/2012 Attachm.", "RefUrl": "/notes/1666380 "}, {"RefNumber": "1658608", "RefComponent": "PY-BR", "RefTitle": "Income Declaration: legal change base year 2011", "RefUrl": "/notes/1658608 "}, {"RefNumber": "1785407", "RefComponent": "XX-CSC-CN-EPIC", "RefTitle": "EPIC: Editing, Bank Reconciliation for pilot customer", "RefUrl": "/notes/1785407 "}, {"RefNumber": "1300732", "RefComponent": "FS-CML", "RefTitle": "CML: UI Enhancement with respect to Table Controls", "RefUrl": "/notes/1300732 "}, {"RefNumber": "1793339", "RefComponent": "XX-CSC-HU-HR", "RefTitle": "Report about insured persons before HR-CEE SP 12-2/2012Att.", "RefUrl": "/notes/1793339 "}, {"RefNumber": "1791998", "RefComponent": "XX-CSC-HU-HR", "RefTitle": "HR-HU: Garnishments calc. error (HR-CEE SP12-2/2012pre1)Att.", "RefUrl": "/notes/1791998 "}, {"RefNumber": "1768878", "RefComponent": "PY-US-TR", "RefTitle": "Year End 2012 Phase II for U.S. Tax Reporter", "RefUrl": "/notes/1768878 "}, {"RefNumber": "1789734", "RefComponent": "XX-CSC-CN-EPIC", "RefTitle": "EPIC: BRS Enhancement for Pilot Customer", "RefUrl": "/notes/1789734 "}, {"RefNumber": "1779962", "RefComponent": "XX-CSC-CN-GTI", "RefTitle": "Deliver GTI functionality of EHP6 SP05 to SP04 customers", "RefUrl": "/notes/1779962 "}, {"RefNumber": "1774614", "RefComponent": "XX-CSC-CN-EPIC", "RefTitle": "EPIC: Editing, Bank Reconcliation for pilot customer", "RefUrl": "/notes/1774614 "}, {"RefNumber": "784100", "RefComponent": "XX-PROJ-RE", "RefTitle": "Quarter days: Maintenance dialog", "RefUrl": "/notes/784100 "}, {"RefNumber": "459562", "RefComponent": "XX-PROJ-RE", "RefTitle": "Quarter days", "RefUrl": "/notes/459562 "}, {"RefNumber": "1780616", "RefComponent": "XX-CSC-HU-HR", "RefTitle": "RPLAMLHx, RPU180Hx corrections HR-CEE HRCEESP12/2012pre1 Att", "RefUrl": "/notes/1780616 "}, {"RefNumber": "1779612", "RefComponent": "XX-CSC-CN-EPIC", "RefTitle": "EPIC: BRS Enhancement for Pilot Customer <PERSON>g", "RefUrl": "/notes/1779612 "}, {"RefNumber": "1703061", "RefComponent": "BW-WHM-MTD", "RefTitle": "Remote interface for reading BW models", "RefUrl": "/notes/1703061 "}, {"RefNumber": "1748448", "RefComponent": "PY-US-TR", "RefTitle": "Q3/2012: Functional changes for U.S. Tax Reporter.", "RefUrl": "/notes/1748448 "}, {"RefNumber": "1769336", "RefComponent": "XX-CSC-HU-HR", "RefTitle": "Program corrections before HR-CEE SP11/2012Pre0 Att.", "RefUrl": "/notes/1769336 "}, {"RefNumber": "1362579", "RefComponent": "SRM-EBP-ADM-ORG", "RefTitle": "Concurrent Employment Enablement of Shopping Cart", "RefUrl": "/notes/1362579 "}, {"RefNumber": "1396141", "RefComponent": "PY-BR", "RefTitle": "DIRF and Tax Income Declaration enhancements", "RefUrl": "/notes/1396141 "}, {"RefNumber": "1497982", "RefComponent": "PY-BR", "RefTitle": "Ordinance 1620: Homolognet", "RefUrl": "/notes/1497982 "}, {"RefNumber": "1444311", "RefComponent": "PY-IT", "RefTitle": "HR-IT: Modello 770/2010 - Advance Delivery", "RefUrl": "/notes/1444311 "}, {"RefNumber": "1737248", "RefComponent": "PY-BR", "RefTitle": "GPS: Substitution of Social Contribution", "RefUrl": "/notes/1737248 "}, {"RefNumber": "1750056", "RefComponent": "XX-CSC-HU-HR", "RefTitle": "Legal changes for Hungary before HR-CEE SP09/2012Pre1 Att.", "RefUrl": "/notes/1750056 "}, {"RefNumber": "1739856", "RefComponent": "XX-CSC-HU-HR", "RefTitle": "Hungarian objects of HR-CEE SP 06/2012 Attachments", "RefUrl": "/notes/1739856 "}, {"RefNumber": "1686792", "RefComponent": "XX-PART-ISHMED", "RefTitle": "i.s.h.med: RN1_ISHMED_USER_COUNT - Determine Users/Licenses", "RefUrl": "/notes/1686792 "}, {"RefNumber": "1731206", "RefComponent": "PY-FR", "RefTitle": "DNAC-AED: AED statement viewer", "RefUrl": "/notes/1731206 "}, {"RefNumber": "1728829", "RefComponent": "PY-GB-RP-SR", "RefTitle": "RTI proxy objects for EAS and FPS ZIP", "RefUrl": "/notes/1728829 "}, {"RefNumber": "1575364", "RefComponent": "XX-CSC-BR-NFE", "RefTitle": "NF-e: Legal Change  - Electronic Correction Letter (CC-e)", "RefUrl": "/notes/1575364 "}, {"RefNumber": "1743467", "RefComponent": "XX-CSC-HU-HR", "RefTitle": "Legal changes for Hungary before HR-CEE SP08/2012 pre5 Att.", "RefUrl": "/notes/1743467 "}, {"RefNumber": "1741756", "RefComponent": "XX-CSC-HU-HR", "RefTitle": "Legal changes for Hungary before HR-CEE SP08/2012 pre6 Att.", "RefUrl": "/notes/1741756 "}, {"RefNumber": "1533198", "RefComponent": "PY-US-TR", "RefTitle": "Year End 2010 Phase III for U.S. Tax Reporter", "RefUrl": "/notes/1533198 "}, {"RefNumber": "1497930", "RefComponent": "PY-US-TR", "RefTitle": "Q3/2010: Functional changes for U.S. Tax Reporter.", "RefUrl": "/notes/1497930 "}, {"RefNumber": "1730057", "RefComponent": "XX-CSC-HU-HR", "RefTitle": "Legal changes for Hungary before HR-CEE SP08/2012 Attachm.", "RefUrl": "/notes/1730057 "}, {"RefNumber": "1737959", "RefComponent": "XX-CSC-HU-HR", "RefTitle": "Legal changes for Hungary before HR-CEE SP08/2012 Attachm.", "RefUrl": "/notes/1737959 "}, {"RefNumber": "1731559", "RefComponent": "PY-US-TR", "RefTitle": "TR: Magmedia for W-2c Puerto Rico", "RefUrl": "/notes/1731559 "}, {"RefNumber": "1736558", "RefComponent": "XX-CSC-HU-HR", "RefTitle": "Legal changes for Hungary before HR-CEE SP08/2012 pre3 Att.", "RefUrl": "/notes/1736558 "}, {"RefNumber": "658721", "RefComponent": "BW-BCT-LO-LIS", "RefTitle": "LBWE/LIS: Statistics relevancy of (DDIC) transports", "RefUrl": "/notes/658721 "}, {"RefNumber": "1724250", "RefComponent": "PY-US-BSI", "RefTitle": "Tax Types 90 and 91 for Washington", "RefUrl": "/notes/1724250 "}, {"RefNumber": "1611266", "RefComponent": "PY-SG", "RefTitle": "SAR files for CPF rate changes 01 Sep 2011 : Pilot delivery", "RefUrl": "/notes/1611266 "}, {"RefNumber": "1723451", "RefComponent": "PY-AU", "RefTitle": "Tax thresholds & limits for 2012/2013 tax year", "RefUrl": "/notes/1723451 "}, {"RefNumber": "1722799", "RefComponent": "PY-US", "RefTitle": "Pennsylvania Act 32 - PSD Codes - May 2012", "RefUrl": "/notes/1722799 "}, {"RefNumber": "1723443", "RefComponent": "PY-US", "RefTitle": "TEMPLATE Note: Pennsylvania Act 32 - PSD Codes - Year 2012", "RefUrl": "/notes/1723443 "}, {"RefNumber": "1645112", "RefComponent": "PY-GB-PS", "RefTitle": "PY-GB-PS: USS CARE Schemes", "RefUrl": "/notes/1645112 "}, {"RefNumber": "1716594", "RefComponent": "XX-CSC-HU-HR", "RefTitle": "Legal changes for Hungary before HR-CEE SP06/2012 pre2 Att.", "RefUrl": "/notes/1716594 "}, {"RefNumber": "1547262", "RefComponent": "XX-CSC-BR-NFEIN", "RefTitle": "NF-e Incoming Automation: Pre-requisites in ERP", "RefUrl": "/notes/1547262 "}, {"RefNumber": "1691228", "RefComponent": "PY-GB-PS", "RefTitle": "HRGBPS: Public Sector Pensions (April 1st 2012)- SAR file", "RefUrl": "/notes/1691228 "}, {"RefNumber": "1702659", "RefComponent": "XX-CSC-SK-HR", "RefTitle": "HRSK: IT0022 - Code for school and branch", "RefUrl": "/notes/1702659 "}, {"RefNumber": "1702586", "RefComponent": "XX-CSC-HU-HR", "RefTitle": "Legal changes for Hungary before HR-CEE SP05/2012 Attachm.", "RefUrl": "/notes/1702586 "}, {"RefNumber": "1698854", "RefComponent": "XX-CSC-TH", "RefTitle": "Thailand tax id legal change-4.6c release", "RefUrl": "/notes/1698854 "}, {"RefNumber": "1695439", "RefComponent": "PY-BR", "RefTitle": "RAIS Year Base 2011 - Generic Layout", "RefUrl": "/notes/1695439 "}, {"RefNumber": "1704132", "RefComponent": "XX-CSC-HU-HR", "RefTitle": "Legal changes for Hungary before HR-CEE SP05/2012 Attachm.", "RefUrl": "/notes/1704132 "}, {"RefNumber": "1703948", "RefComponent": "XX-CSC-HU-HR", "RefTitle": "Legal changes for Hungary before HR-CEE SP05/2012 Attachm.", "RefUrl": "/notes/1703948 "}, {"RefNumber": "1173178", "RefComponent": "BW-BCT-GEN", "RefTitle": "Missing InfoPackages for UD Connect", "RefUrl": "/notes/1173178 "}, {"RefNumber": "1168230", "RefComponent": "BW-BCT-GEN", "RefTitle": "Missing 7.00 DataSources", "RefUrl": "/notes/1168230 "}, {"RefNumber": "1165525", "RefComponent": "BW-BCT-GEN", "RefTitle": "Routines missing in BI_CONT 7.03 Support Package 09", "RefUrl": "/notes/1165525 "}, {"RefNumber": "1699419", "RefComponent": "PA-PA-NL", "RefTitle": "EIR: Electronic Absence Reporting 2nd .SAR file delivery", "RefUrl": "/notes/1699419 "}, {"RefNumber": "1688449", "RefComponent": "XX-CSC-CH-IS-H", "RefTitle": "IS-H CH:Med.Statistik neu - SwissDRG Erhebung", "RefUrl": "/notes/1688449 "}, {"RefNumber": "1698910", "RefComponent": "XX-CSC-HU-HR", "RefTitle": "Hungarian objects of HR-CEE SP 04/2012 Attachments", "RefUrl": "/notes/1698910 "}, {"RefNumber": "1693486", "RefComponent": "XX-CSC-HU-HR", "RefTitle": "Legal changes for Hungary before HR-CEE SP04/2012 Attachm.", "RefUrl": "/notes/1693486 "}, {"RefNumber": "1693152", "RefComponent": "XX-CSC-HU-HR", "RefTitle": "Legal changes for Hungary before HR-CEE SP04/2012 Attachm.", "RefUrl": "/notes/1693152 "}, {"RefNumber": "1578055", "RefComponent": "PY-AT", "RefTitle": "Pilotauslieferung: Einkommensbericht", "RefUrl": "/notes/1578055 "}, {"RefNumber": "732499", "RefComponent": "FIN-FSCM-TRM-TM", "RefTitle": "FinServ: Activation interface for \"soft\" modifications", "RefUrl": "/notes/732499 "}, {"RefNumber": "1691433", "RefComponent": "XX-CSC-HU-HR", "RefTitle": "Legal changes for Hungary before HR-CEE SP04/2012 Attachm.", "RefUrl": "/notes/1691433 "}, {"RefNumber": "1677128", "RefComponent": "PY-GB-RP-SR", "RefTitle": "End Of Year legal Changes 2011-12 (DDIC)", "RefUrl": "/notes/1677128 "}, {"RefNumber": "1686179", "RefComponent": "XX-CSC-HU-HR", "RefTitle": "Hungarian objects of HR-CEE SP 03/2012 Attachments", "RefUrl": "/notes/1686179 "}, {"RefNumber": "899513", "RefComponent": "FS-CML-AC-PI", "RefTitle": "BUC: Change of the posting date", "RefUrl": "/notes/899513 "}, {"RefNumber": "622799", "RefComponent": "CRM-MD-BP-XIF", "RefTitle": "Release of segments in message type CRMXIF_PARTNER_SAVE_M", "RefUrl": "/notes/622799 "}, {"RefNumber": "1677144", "RefComponent": "PY-BR", "RefTitle": "DIRF: legal change base year 2011", "RefUrl": "/notes/1677144 "}, {"RefNumber": "1662817", "RefComponent": "XX-CSC-PL-HR", "RefTitle": "HRPL: PIT - tax forms 2012, limited usage of NIP", "RefUrl": "/notes/1662817 "}, {"RefNumber": "1681242", "RefComponent": "XX-CSC-HU-HR", "RefTitle": "Legal changes for Hungary before HR-CEE SP03/2012 Attachm.", "RefUrl": "/notes/1681242 "}, {"RefNumber": "1671927", "RefComponent": "FI-AP-AP-Q1", "RefTitle": "Transport files for Ireland legal change", "RefUrl": "/notes/1671927 "}, {"RefNumber": "1679450", "RefComponent": "SCM-APO-SNP-BF", "RefTitle": "Missing delivered objects from 9ASNP02 Planning Area", "RefUrl": "/notes/1679450 "}, {"RefNumber": "1669572", "RefComponent": "XX-CSC-AR-LO", "RefTitle": "AR WS: SAR file for authorization objects", "RefUrl": "/notes/1669572 "}, {"RefNumber": "1679843", "RefComponent": "XX-CSC-HU-HR", "RefTitle": "Legal changes for Hungary before HR-CEE SP03/2012 Attachm.", "RefUrl": "/notes/1679843 "}, {"RefNumber": "1391102", "RefComponent": "SCM-APO-SNP-BF", "RefTitle": "Missing delivered objects from 9ASNP02 Planning Area", "RefUrl": "/notes/1391102 "}, {"RefNumber": "1677809", "RefComponent": "XX-CSC-HU-HR", "RefTitle": "Legal changes for Hungary before HR-CEE SP03/2012 Attachm.", "RefUrl": "/notes/1677809 "}, {"RefNumber": "1669707", "RefComponent": "XX-CSC-LU-FI", "RefTitle": "LU annual VAT return: DDIC Objects", "RefUrl": "/notes/1669707 "}, {"RefNumber": "1673410", "RefComponent": "PY-FR", "RefTitle": "N4DS transport files (so-called SAR-CAR files) availability", "RefUrl": "/notes/1673410 "}, {"RefNumber": "1675638", "RefComponent": "XX-CSC-HU-HR", "RefTitle": "Legal changes for Hungary before HR-CEE SP02/2012 Attachm.", "RefUrl": "/notes/1675638 "}, {"RefNumber": "1672767", "RefComponent": "XX-CSC-HU-HR", "RefTitle": "Legal changes for Hungary before HR-CEE SP02/2012 Attachm.", "RefUrl": "/notes/1672767 "}, {"RefNumber": "1670302", "RefComponent": "XX-CSC-HU-HR", "RefTitle": "Legal changes for Hungary before HR-CEE SP02/2012 Attachm.", "RefUrl": "/notes/1670302 "}, {"RefNumber": "1364669", "RefComponent": "LOD-ESO-ERP", "RefTitle": "Integrating ERP with the SAP Sourcing (II)", "RefUrl": "/notes/1364669 "}, {"RefNumber": "1669451", "RefComponent": "XX-CSC-PL-HR", "RefTitle": "HRPL: T511K SIUWE - parental leave 2012, soc.ins. base - 1", "RefUrl": "/notes/1669451 "}, {"RefNumber": "1657719", "RefComponent": "XX-CSC-PL-LO", "RefTitle": "DP PL: One Time Customer-SAR files for BADI implementation", "RefUrl": "/notes/1657719 "}, {"RefNumber": "214780", "RefComponent": "BC-MID-BUS", "RefTitle": "Business Connector and business documents", "RefUrl": "/notes/214780 "}, {"RefNumber": "1658938", "RefComponent": "PY-CA", "RefTitle": "YE11 : Delivery of Year End Transports for 2011", "RefUrl": "/notes/1658938 "}, {"RefNumber": "1665565", "RefComponent": "XX-CSC-HU-HR", "RefTitle": "Hungarian objects of HR-CEE SP 12-2/2011", "RefUrl": "/notes/1665565 "}, {"RefNumber": "1665566", "RefComponent": "XX-CSC-HU-HR", "RefTitle": "Hungarian objects of HR-CEE SP 12-2/2011 Attachments", "RefUrl": "/notes/1665566 "}, {"RefNumber": "1474496", "RefComponent": "FS-CML", "RefTitle": "Consumer loan law - Release ERP 2005", "RefUrl": "/notes/1474496 "}, {"RefNumber": "1326306", "RefComponent": "SCM-BAS-POS", "RefTitle": "Missing Delivery Objects in SCM SNC 7.0", "RefUrl": "/notes/1326306 "}, {"RefNumber": "1658622", "RefComponent": "PY-US-NT-GR", "RefTitle": "GRN: IRS Publication 1494 for tax year 2012", "RefUrl": "/notes/1658622 "}, {"RefNumber": "184399", "RefComponent": "BC-DB-DB2", "RefTitle": "DB2/390: DDIC corrections (Releases 4.6A,4.6B,4.6C,4.6D)", "RefUrl": "/notes/184399 "}, {"RefNumber": "1645396", "RefComponent": "PY-US-TR", "RefTitle": "Year End 2011 Phase II for U.S. Tax Reporter", "RefUrl": "/notes/1645396 "}, {"RefNumber": "1659041", "RefComponent": "PA-BN", "RefTitle": "BEN: HIPAA EDI Legal Change - Advanced Delivery", "RefUrl": "/notes/1659041 "}, {"RefNumber": "166096", "RefComponent": "BC-MID-RFC", "RefTitle": "qRFC installation for 3.xx, 4.0x, 4.5x and 4.6x", "RefUrl": "/notes/166096 "}, {"RefNumber": "1653698", "RefComponent": "XX-CSC-SI-HR", "RefTitle": "HRSI: SEPA implementation in HR-SI - objects", "RefUrl": "/notes/1653698 "}, {"RefNumber": "1655242", "RefComponent": "XX-CSC-HU-HR", "RefTitle": "Corrections for Hungary before HR-CEE SP 12/2011 attach.", "RefUrl": "/notes/1655242 "}, {"RefNumber": "1657373", "RefComponent": "XX-CSC-CN-EPIC", "RefTitle": "EPIC: preliminary delivery of Note 1622119", "RefUrl": "/notes/1657373 "}, {"RefNumber": "1467210", "RefComponent": "FS-CML", "RefTitle": "Consumer loan law - repayment schedule", "RefUrl": "/notes/1467210 "}, {"RefNumber": "1544101", "RefComponent": "PA-PA-US", "RefTitle": "IT0057:Issues with structure HCMT_BSP_PA_US_R0057", "RefUrl": "/notes/1544101 "}, {"RefNumber": "1654645", "RefComponent": "BW-BEX-OT-BIA", "RefTitle": "Syntax error in class CL_RSDDB_PROC_ACTIVATE", "RefUrl": "/notes/1654645 "}, {"RefNumber": "32236", "RefComponent": "MM-IM", "RefTitle": "Incorrect stock qty or stock value in material master", "RefUrl": "/notes/32236 "}, {"RefNumber": "761420", "RefComponent": "CRM-BF", "RefTitle": "Delta load exchange rates", "RefUrl": "/notes/761420 "}, {"RefNumber": "1639695", "RefComponent": "PY-GB-PS", "RefTitle": "School's Work Force Census (2011 Legal Changes) DDIC changes", "RefUrl": "/notes/1639695 "}, {"RefNumber": "1554835", "RefComponent": "PM-<PERSON>M-MP", "RefTitle": "CustConn EAM: Tech. prerequisite for Notes 1553174/1553173", "RefUrl": "/notes/1554835 "}, {"RefNumber": "1623539", "RefComponent": "XX-CSC-PL-LO", "RefTitle": "DP PL: SAR files for BADI implementation", "RefUrl": "/notes/1623539 "}, {"RefNumber": "1629198", "RefComponent": "IS-HER-CM-GB", "RefTitle": "SLcM-UCAS Legal Change for September 2011 Cycle", "RefUrl": "/notes/1629198 "}, {"RefNumber": "1605688", "RefComponent": "XX-CSC-CZ-HR", "RefTitle": "HRCZ - RPCDNPT9 - NEMPRI XML (4/4) - new cluster manager", "RefUrl": "/notes/1605688 "}, {"RefNumber": "1582988", "RefComponent": "XX-CSC-CZ-HR", "RefTitle": "HRCZ - RPCDNPT9 - NEMPRI XML (1/4) - new technical objects", "RefUrl": "/notes/1582988 "}, {"RefNumber": "1012649", "RefComponent": "PY-BR", "RefTitle": "Legal change in HBRDIRF0 for year 2007", "RefUrl": "/notes/1012649 "}, {"RefNumber": "1025933", "RefComponent": "PY-BR", "RefTitle": "New electronic file MANAD", "RefUrl": "/notes/1025933 "}, {"RefNumber": "1628314", "RefComponent": "XX-CSC-HU-HR", "RefTitle": "Legal Changes for Hungary before HR-CEE SP 10/2011", "RefUrl": "/notes/1628314 "}, {"RefNumber": "1629550", "RefComponent": "XX-CSC-HU-HR", "RefTitle": "HR-HU: HRCEESP10/2011pre3 Personal calendar gen. - Attachm.", "RefUrl": "/notes/1629550 "}, {"RefNumber": "1626095", "RefComponent": "PA-PA-NL", "RefTitle": "EIR: Electronic Absence Reporting .SAR file delivery", "RefUrl": "/notes/1626095 "}, {"RefNumber": "1626283", "RefComponent": "XX-CSC-HU-HR", "RefTitle": "HRHU RPTGENH0 - error in generating for previous months", "RefUrl": "/notes/1626283 "}, {"RefNumber": "1623229", "RefComponent": "XX-CSC-AR-LO", "RefTitle": "AEI WS Domestic: SAR files for BADI implementations", "RefUrl": "/notes/1623229 "}, {"RefNumber": "1623643", "RefComponent": "XX-PART-ISHMED", "RefTitle": "Patient Organizer: N2WLD - Aspects (Program Termination)", "RefUrl": "/notes/1623643 "}, {"RefNumber": "1620536", "RefComponent": "BC-CST", "RefTitle": "Fix for error after import of note 1580017", "RefUrl": "/notes/1620536 "}, {"RefNumber": "1603761", "RefComponent": "XX-CSC-CZ-HR", "RefTitle": "HRCZ - RPCELDTMCL - corrections (July 2011)", "RefUrl": "/notes/1603761 "}, {"RefNumber": "848230", "RefComponent": "FS-CML-AC-RPM-IAS", "RefTitle": "IPD: Document segment text cannot be preassigned", "RefUrl": "/notes/848230 "}, {"RefNumber": "729442", "RefComponent": "FS-CML-AC-RPM-IAS", "RefTitle": "IPD: Posting date in FEBAN", "RefUrl": "/notes/729442 "}, {"RefNumber": "195995", "RefComponent": "TR-CB", "RefTitle": "Organizational dimension in cash budget management", "RefUrl": "/notes/195995 "}, {"RefNumber": "1601000", "RefComponent": "XX-CSC-SI-HR", "RefTitle": "HRSI: New education and profession classifications - objects", "RefUrl": "/notes/1601000 "}, {"RefNumber": "1609765", "RefComponent": "XX-CSC-SK-HR", "RefTitle": "HRSK: Health Insurance statement - File 514 - Before LCP", "RefUrl": "/notes/1609765 "}, {"RefNumber": "1604381", "RefComponent": "XX-CSC-PL-HR", "RefTitle": "HRPL: PLZLA - new BAdI method UPDATE_BONUSES_BEFORE_CALC Att", "RefUrl": "/notes/1604381 "}, {"RefNumber": "1547506", "RefComponent": "PY-AT", "RefTitle": "Einkommensbericht aufgrund Gleichbehandlungsgesetz", "RefUrl": "/notes/1547506 "}, {"RefNumber": "1588613", "RefComponent": "PY-AU", "RefTitle": "Australia: EOY Legal Changes 2011/2012 - Part 1", "RefUrl": "/notes/1588613 "}, {"RefNumber": "1506349", "RefComponent": "SRM-EBP-ADM-XBP", "RefTitle": "Approval Workflow Bidder/Supplier: ********** and **********", "RefUrl": "/notes/1506349 "}, {"RefNumber": "1592478", "RefComponent": "IS-H-PM", "RefTitle": "IS-H-CH: Enhancements for Swiss DRG - STD delta", "RefUrl": "/notes/1592478 "}, {"RefNumber": "1595715", "RefComponent": "XX-CSC-HU-HR", "RefTitle": "Legal changes for Hungary before HRCEE SP06/2011 part 4 Att.", "RefUrl": "/notes/1595715 "}, {"RefNumber": "1592172", "RefComponent": "XX-CSC-HU-HR", "RefTitle": "Corrections for Hungary before HRCEE SP06/2011 part 3 Att.", "RefUrl": "/notes/1592172 "}, {"RefNumber": "115899", "RefComponent": "MM-PUR-GF-SC", "RefTitle": "Correction Report for Subcontractor Requirements", "RefUrl": "/notes/115899 "}, {"RefNumber": "1541400", "RefComponent": "XX-CSC-HU-HR", "RefTitle": "Legal Changes for Hungary before HR-CEE SP 02/2011 attach.", "RefUrl": "/notes/1541400 "}, {"RefNumber": "773976", "RefComponent": "XX-CSC-CZ-IS-U", "RefTitle": "Individual Value Adjustment and Write-offs", "RefUrl": "/notes/773976 "}, {"RefNumber": "1571815", "RefComponent": "PY-US-BSI", "RefTitle": "PY: Pennsylvania ACT 32 Reciprocal Formula", "RefUrl": "/notes/1571815 "}, {"RefNumber": "962704", "RefComponent": "XX-CSC-AR-IS-OIL", "RefTitle": "IS-Oil Localization of Argentinean Taxes in Sales", "RefUrl": "/notes/962704 "}, {"RefNumber": "1586413", "RefComponent": "XX-CSC-HU-HR", "RefTitle": "Corrections for Hungary before HRCEE SP06/2011 part 2 Att.", "RefUrl": "/notes/1586413 "}, {"RefNumber": "1567318", "RefComponent": "PY-AT", "RefTitle": "Korrekturen zur gesetzlichen Änderung des Montageprivilegs", "RefUrl": "/notes/1567318 "}, {"RefNumber": "1585501", "RefComponent": "XX-CSC-HU-HR", "RefTitle": "Legal changes for Hungary before HRCEE SP06/2011 part 1 Att.", "RefUrl": "/notes/1585501 "}, {"RefNumber": "1126405", "RefComponent": "IS-H", "RefTitle": "IS-H: Initialization of field EXTAUF in table TN14B", "RefUrl": "/notes/1126405 "}, {"RefNumber": "1425765", "RefComponent": "BC-CST-GW", "RefTitle": "Generating sec_info reg_info", "RefUrl": "/notes/1425765 "}, {"RefNumber": "1578627", "RefComponent": "XX-CSC-HU-HR", "RefTitle": "Legal changes for Hungary before HRCEE SP05/2011 part 6 Att.", "RefUrl": "/notes/1578627 "}, {"RefNumber": "1575894", "RefComponent": "XX-CSC-HU-HR", "RefTitle": "Legal changes for Hungary before HRCEE SP05/2011 part 5 Att.", "RefUrl": "/notes/1575894 "}, {"RefNumber": "1536216", "RefComponent": "PY-BR", "RefTitle": "DIRF and Tax Income Declaration enhancements in 4.6C", "RefUrl": "/notes/1536216 "}, {"RefNumber": "1473895", "RefComponent": "FS-CML", "RefTitle": "Consumer loan law - Release ERP 2004", "RefUrl": "/notes/1473895 "}, {"RefNumber": "1573298", "RefComponent": "XX-CSC-HU-HR", "RefTitle": "Legal changes for Hungary before HRCEE SP05/2011 part 4 Att.", "RefUrl": "/notes/1573298 "}, {"RefNumber": "1573494", "RefComponent": "XX-CSC-HU-HR", "RefTitle": "Corrections for Hungary before HRCEE SP05/2011 part 3 Att.", "RefUrl": "/notes/1573494 "}, {"RefNumber": "433934", "RefComponent": "CRM-MD-CON-IF", "RefTitle": "Transfer ddic info for condition objects to mobile client", "RefUrl": "/notes/433934 "}, {"RefNumber": "1545704", "RefComponent": "XX-CSC-RU-IS-U", "RefTitle": "IS-U Localization RU: corrections", "RefUrl": "/notes/1545704 "}, {"RefNumber": "1570315", "RefComponent": "XX-CSC-PL-HR", "RefTitle": "HRPL: Job classification in 2010 for GUS Z-05 and Z-12 Att", "RefUrl": "/notes/1570315 "}, {"RefNumber": "1135662", "RefComponent": "PM-EQM-EQ", "RefTitle": "Correction of time stamps in equipment time segments", "RefUrl": "/notes/1135662 "}, {"RefNumber": "1571434", "RefComponent": "XX-CSC-HU-HR", "RefTitle": "Corrections for Hungary before HRCEE SP05/2011 part 2 Att.", "RefUrl": "/notes/1571434 "}, {"RefNumber": "1570908", "RefComponent": "XX-CSC-HU-HR", "RefTitle": "Program correction for Hungary before HRCEE SP04/2011 part 5", "RefUrl": "/notes/1570908 "}, {"RefNumber": "1562056", "RefComponent": "XX-CSC-HU-HR", "RefTitle": "Program correction for Hungary before HRCEE SP04/2011 part 2", "RefUrl": "/notes/1562056 "}, {"RefNumber": "588726", "RefComponent": "CO-OM-CCA-B", "RefTitle": "Copy programs for scheduling as a job", "RefUrl": "/notes/588726 "}, {"RefNumber": "1569679", "RefComponent": "XX-CSC-PL-HR", "RefTitle": "HRPL: HPLSZ120 - GUS Z 12 for year 2010 Att.", "RefUrl": "/notes/1569679 "}, {"RefNumber": "1569404", "RefComponent": "XX-CSC-HU-HR", "RefTitle": "Legal Changes for Hungary before HRCEE SP04/2011 part 4 Att.", "RefUrl": "/notes/1569404 "}, {"RefNumber": "1223805", "RefComponent": "EHS-BD-SPE", "RefTitle": "Output variant for Excel- Excel template", "RefUrl": "/notes/1223805 "}, {"RefNumber": "1565471", "RefComponent": "XX-CSC-UA-IS-U", "RefTitle": "Add-on Archiving:Extension of selection screens", "RefUrl": "/notes/1565471 "}, {"RefNumber": "98228", "RefComponent": "BC-CCM-MON-ORA", "RefTitle": "Transp. after database upgrade to Oracle 8.0 and 8.1", "RefUrl": "/notes/98228 "}, {"RefNumber": "738595", "RefComponent": "BC-DB-INF-CCM", "RefTitle": "INF-217 in RSINF076 after DB-upgrade to 9.40", "RefUrl": "/notes/738595 "}, {"RefNumber": "1564923", "RefComponent": "XX-CSC-HU-HR", "RefTitle": "Program correction for Hungary before HRCEE SP04/2011 part 3", "RefUrl": "/notes/1564923 "}, {"RefNumber": "1562062", "RefComponent": "XX-CSC-HU-HR", "RefTitle": "Program correction for Hungary before HRCEE SP04/2011 part 3", "RefUrl": "/notes/1562062 "}, {"RefNumber": "1562055", "RefComponent": "XX-CSC-HU-HR", "RefTitle": "Program correction for Hungary before HRCEE SP04/2011 part 2", "RefUrl": "/notes/1562055 "}, {"RefNumber": "1562461", "RefComponent": "XX-CSC-HU-HR", "RefTitle": "Legal Changes for Hungary before HRCEE SP04/2011 part 1 Att.", "RefUrl": "/notes/1562461 "}, {"RefNumber": "1560967", "RefComponent": "XX-CSC-HU-HR", "RefTitle": "Hungarian objects of HRCEE SP 03/2011", "RefUrl": "/notes/1560967 "}, {"RefNumber": "1345769", "RefComponent": "MM-PUR-GF-ES", "RefTitle": "OPS eSOA services for EhP4 SP03", "RefUrl": "/notes/1345769 "}, {"RefNumber": "1561195", "RefComponent": "XX-CSC-HU-HR", "RefTitle": "Hungarian objects of HRCEE SP 03/2011 Attachments", "RefUrl": "/notes/1561195 "}, {"RefNumber": "1554665", "RefComponent": "XX-CSC-HU-HR", "RefTitle": "Legal Changes for Hungary before HRCEE SP03/2011 part 2", "RefUrl": "/notes/1554665 "}, {"RefNumber": "1553590", "RefComponent": "XX-CSC-HU-HR", "RefTitle": "Legal Changes for Hungary before HRCEE SP03/2011 part 1", "RefUrl": "/notes/1553590 "}, {"RefNumber": "1557747", "RefComponent": "XX-PART-ISHMED-ORD", "RefTitle": "Clinical Order: Prioritization of Screen Modification", "RefUrl": "/notes/1557747 "}, {"RefNumber": "1553669", "RefComponent": "XX-CSC-UA-IS-U", "RefTitle": "Ukraine:Order 969 12.2010 Tax Voucher and correction change", "RefUrl": "/notes/1553669 "}, {"RefNumber": "1552747", "RefComponent": "XX-CSC-HU-HR", "RefTitle": "Legal Changes for Hungary before HRCEE SP03/2011 part 3 Att.", "RefUrl": "/notes/1552747 "}, {"RefNumber": "1527811", "RefComponent": "PY-BR", "RefTitle": "DIRF 2010 - Instrução Normativa Nº 1.033", "RefUrl": "/notes/1527811 "}, {"RefNumber": "1554666", "RefComponent": "XX-CSC-HU-HR", "RefTitle": "Legal Changes for Hungary before HRCEE SP03/2011 part 2 Att.", "RefUrl": "/notes/1554666 "}, {"RefNumber": "1553591", "RefComponent": "XX-CSC-HU-HR", "RefTitle": "Legal Changes for Hungary before HRCEE SP03/2011 part 1 Att.", "RefUrl": "/notes/1553591 "}, {"RefNumber": "1553589", "RefComponent": "XX-CSC-HU-HR", "RefTitle": "Legal Changes for Hungary before HRCEE SPmm/yyyy part n Att.", "RefUrl": "/notes/1553589 "}, {"RefNumber": "1546682", "RefComponent": "PY-US-NT-GR", "RefTitle": "GRN: IRS Publication 1494 for tax year 2011.", "RefUrl": "/notes/1546682 "}, {"RefNumber": "1549586", "RefComponent": "XX-CSC-HU-HR", "RefTitle": "Legal Changes for Hungary before HRCEE SP02/2011 part 3", "RefUrl": "/notes/1549586 "}, {"RefNumber": "1547560", "RefComponent": "XX-CSC-HU-HR", "RefTitle": "Legal Changes for Hungary before HRCEE SP02/2011 part 2", "RefUrl": "/notes/1547560 "}, {"RefNumber": "1543043", "RefComponent": "PY-BR", "RefTitle": "New Termination Term in release 4.6C", "RefUrl": "/notes/1543043 "}, {"RefNumber": "1542688", "RefComponent": "XX-CSC-HU-HR", "RefTitle": "Legal Changes for Hungary before HRCEESP02/2011 part 1 Att.", "RefUrl": "/notes/1542688 "}, {"RefNumber": "1542620", "RefComponent": "XX-CSC-HU-HR", "RefTitle": "Legal Changes for Hungary before HRCEESP02/2011 part 1", "RefUrl": "/notes/1542620 "}, {"RefNumber": "1549589", "RefComponent": "XX-CSC-HU-HR", "RefTitle": "Legal Changes for Hungary before HRCEE SP02/2011 part 3 Att.", "RefUrl": "/notes/1549589 "}, {"RefNumber": "1360162", "RefComponent": "PA-PD-PM", "RefTitle": "(Obsolete) Language issues in Goals Functionality", "RefUrl": "/notes/1360162 "}, {"RefNumber": "1547561", "RefComponent": "XX-CSC-HU-HR", "RefTitle": "Legal Changes for Hungary before HRCEE SP02/2011 part 2 Att.", "RefUrl": "/notes/1547561 "}, {"RefNumber": "1537009", "RefComponent": "PY-IE", "RefTitle": "PY-IE: Legal / Budget Changes Year - 2011", "RefUrl": "/notes/1537009 "}, {"RefNumber": "1529770", "RefComponent": "XX-CSC-HU-HR", "RefTitle": "Legal Changes for Hungary after HR-CEE SP 10/2010 attach.", "RefUrl": "/notes/1529770 "}, {"RefNumber": "1364960", "RefComponent": "PA-PD-PM", "RefTitle": "Improvements to the Goals Functionality", "RefUrl": "/notes/1364960 "}, {"RefNumber": "1514526", "RefComponent": "PY-US-TR", "RefTitle": "Year End 2010 Phase II for U.S. Tax Reporter", "RefUrl": "/notes/1514526 "}, {"RefNumber": "1504810", "RefComponent": "XX-PART-ISHMED", "RefTitle": "CWS: Surgeries View Type - Calculate Function", "RefUrl": "/notes/1504810 "}, {"RefNumber": "1526071", "RefComponent": "XX-CSC-HU-HR", "RefTitle": "Legal Changes for Hungary before HR-CEE SP 11/2010 part 5", "RefUrl": "/notes/1526071 "}, {"RefNumber": "1526070", "RefComponent": "XX-CSC-HU-HR", "RefTitle": "Legal Changes for Hungary before HR-CEE SP 11/2010 part 4.", "RefUrl": "/notes/1526070 "}, {"RefNumber": "1510463", "RefComponent": "XX-CSC-HU-HR", "RefTitle": "Legal Changes for Hungary before HR-CEE SP 10/2010", "RefUrl": "/notes/1510463 "}, {"RefNumber": "1519046", "RefComponent": "XX-CSC-HU-HR", "RefTitle": "Legal Changes for Hungary before HR-CEE SP 11/2010 att. II.", "RefUrl": "/notes/1519046 "}, {"RefNumber": "1510744", "RefComponent": "XX-CSC-HU-HR", "RefTitle": "Legal Changes for Hungary before HR-CEE SP 10/2010 attach.", "RefUrl": "/notes/1510744 "}, {"RefNumber": "1046658", "RefComponent": "SRM-EBP-WFL", "RefTitle": "Workflow Transports", "RefUrl": "/notes/1046658 "}, {"RefNumber": "1415022", "RefComponent": "SCM-APO-SNP-BF", "RefTitle": "Planning area 9AVMI03: Key figure 9ATSRESQ is missing", "RefUrl": "/notes/1415022 "}, {"RefNumber": "1488921", "RefComponent": "XX-PART-ISHMED", "RefTitle": "Planning: Appt OU Presetting for Coordinating Facility", "RefUrl": "/notes/1488921 "}, {"RefNumber": "1520198", "RefComponent": "XX-PART-ISHMED", "RefTitle": "Patient Organizer/Patient Viewer: Standard Aspect SAP40", "RefUrl": "/notes/1520198 "}, {"RefNumber": "1510355", "RefComponent": "BW-BCT-GEN", "RefTitle": "missing objects for BI_CONT 704", "RefUrl": "/notes/1510355 "}, {"RefNumber": "1512243", "RefComponent": "PA-PA-BE", "RefTitle": "DIMONA - Notes after the HRSP/CLC of 09/2010", "RefUrl": "/notes/1512243 "}, {"RefNumber": "1490187", "RefComponent": "XX-PART-ISHMED", "RefTitle": "Medication: Various Corrections and Technical Adaptations", "RefUrl": "/notes/1490187 "}, {"RefNumber": "1430270", "RefComponent": "PY-US-TX", "RefTitle": "TAX: Downport Work Tax Area Override w/ mult. rates of pay.", "RefUrl": "/notes/1430270 "}, {"RefNumber": "1487072", "RefComponent": "PY-BE", "RefTitle": "Legal Changes for DmfA, Quarter 2 2010", "RefUrl": "/notes/1487072 "}, {"RefNumber": "1475039", "RefComponent": "XX-PART-ISHMED", "RefTitle": "Surgery System: Surgery WS - BADI_ISHMED_BASEITEM_DOCUMENT", "RefUrl": "/notes/1475039 "}, {"RefNumber": "1487304", "RefComponent": "PA-PA-BE", "RefTitle": "DIMONA: New version 2010 - Customizing files (.CAR)", "RefUrl": "/notes/1487304 "}, {"RefNumber": "1495864", "RefComponent": "PY-TW", "RefTitle": "TW: NHI grades update 2010", "RefUrl": "/notes/1495864 "}, {"RefNumber": "1480103", "RefComponent": "XX-PART-ISHMED", "RefTitle": "Standard Nursing Plan: Display Technical Properties Missing", "RefUrl": "/notes/1480103 "}, {"RefNumber": "1049268", "RefComponent": "PY-US-TX", "RefTitle": "TAX: No cross-company cumul. for million $ suppl. wages.", "RefUrl": "/notes/1049268 "}, {"RefNumber": "925164", "RefComponent": "PY-BR", "RefTitle": "HBRRAIS0 Legal Changes 2006", "RefUrl": "/notes/925164 "}, {"RefNumber": "1446978", "RefComponent": "XX-PART-ISHMED", "RefTitle": "Clinical Order: Screen Modification", "RefUrl": "/notes/1446978 "}, {"RefNumber": "1443156", "RefComponent": "XX-PART-ISHMED", "RefTitle": "Problem in IMG: Radiology - Node Text Not Found", "RefUrl": "/notes/1443156 "}, {"RefNumber": "1286892", "RefComponent": "FS-CML-PO-DI", "RefTitle": "CHK: Changing disbursements", "RefUrl": "/notes/1286892 "}, {"RefNumber": "1478938", "RefComponent": "PY-BE", "RefTitle": "DeCava - Pilot delivery", "RefUrl": "/notes/1478938 "}, {"RefNumber": "1460368", "RefComponent": "XX-PART-ISHMED", "RefTitle": "Medication: Various Corrections and Technical Adaptations", "RefUrl": "/notes/1460368 "}, {"RefNumber": "1450021", "RefComponent": "XX-PART-ISHMED", "RefTitle": "Medication: Various Corrections and Technical Adaptations", "RefUrl": "/notes/1450021 "}, {"RefNumber": "1454921", "RefComponent": "XX-PART-ISHMED", "RefTitle": "Medication: Various Corrections and Technical Adaptations", "RefUrl": "/notes/1454921 "}, {"RefNumber": "1441365", "RefComponent": "XX-PART-ISHMED", "RefTitle": "Medication: Various Corrections and Technical Adaptations", "RefUrl": "/notes/1441365 "}, {"RefNumber": "1435694", "RefComponent": "PA-PD-PM", "RefTitle": "Possibility to separate the Corporate Goals from Core Values", "RefUrl": "/notes/1435694 "}, {"RefNumber": "570920", "RefComponent": "BC-BW", "RefTitle": "\"I_S_HEADER3\" function parameter is not known", "RefUrl": "/notes/570920 "}, {"RefNumber": "1355103", "RefComponent": "XX-PART-ISHMED-ORD", "RefTitle": "Radiology: Findings Work Station - Previewer Missing", "RefUrl": "/notes/1355103 "}, {"RefNumber": "959345", "RefComponent": "FS-CML", "RefTitle": "CAP: BAdI for the capital amounts disp/planned record update", "RefUrl": "/notes/959345 "}, {"RefNumber": "1330506", "RefComponent": "PY-IT", "RefTitle": "HR-IT: Modello 730 - 2009", "RefUrl": "/notes/1330506 "}, {"RefNumber": "1474670", "RefComponent": "XX-PART-ISHMED", "RefTitle": "Create Clinical Order: Runtime Error", "RefUrl": "/notes/1474670 "}, {"RefNumber": "1435322", "RefComponent": "PY-ZA", "RefTitle": "SAR Files for IRP5 2010 - Pilot Note Only (Risk Involved)", "RefUrl": "/notes/1435322 "}, {"RefNumber": "1475267", "RefComponent": "XX-CSC-HU-HR", "RefTitle": "Corrections for Hungary before LCP CE 06/2010 part4", "RefUrl": "/notes/1475267 "}, {"RefNumber": "1446333", "RefComponent": "PY-ZA", "RefTitle": "IRP5: issues with Infotype 0009, Code 4141/4142 & IT3(a)", "RefUrl": "/notes/1446333 "}, {"RefNumber": "1470933", "RefComponent": "XX-CSC-HU-HR", "RefTitle": "Legal Changes, corrections for Hungary before LCP CE 06/2010", "RefUrl": "/notes/1470933 "}, {"RefNumber": "860824", "RefComponent": "FS-CML", "RefTitle": "BUC: Flexible general ledger and CML", "RefUrl": "/notes/860824 "}, {"RefNumber": "1454820", "RefComponent": "XX-CSC-BR-NFE", "RefTitle": "New XML Layout Version 2.00: Pre-Implementation", "RefUrl": "/notes/1454820 "}, {"RefNumber": "1463561", "RefComponent": "XX-CSC-HU-HR", "RefTitle": "Hungarian objects of LCP CE 04/2010", "RefUrl": "/notes/1463561 "}, {"RefNumber": "1455261", "RefComponent": "XX-CSC-HU-HR", "RefTitle": "Legal Changes for Hungary before LCP CE 05/2010", "RefUrl": "/notes/1455261 "}, {"RefNumber": "1461581", "RefComponent": "XX-CSC-HU-HR", "RefTitle": "Hungarian objects of LCP CE 05/2010", "RefUrl": "/notes/1461581 "}, {"RefNumber": "1454353", "RefComponent": "XX-PROJ-CDP-TEST-059", "RefTitle": "SETI RE-FX", "RefUrl": "/notes/1454353 "}, {"RefNumber": "1447707", "RefComponent": "PY-AT", "RefTitle": "Pilotauslieferung: Zahlungen zum oder nach dem Austritt", "RefUrl": "/notes/1447707 "}, {"RefNumber": "1456915", "RefComponent": "XX-CSC-HU-HR", "RefTitle": "Legal Changes for Hungary before LCP CE 05/2010 part2", "RefUrl": "/notes/1456915 "}, {"RefNumber": "480671", "RefComponent": "BC-I18", "RefTitle": "The Text Language Flag of LANG Fields", "RefUrl": "/notes/480671 "}, {"RefNumber": "1447368", "RefComponent": "PY-ZA", "RefTitle": "IRP5 Implementation errors: Notes released after 1434570", "RefUrl": "/notes/1447368 "}, {"RefNumber": "1161674", "RefComponent": "PA-PA-US", "RefTitle": "DDHCHECK:Enhancement category for tables and structures", "RefUrl": "/notes/1161674 "}, {"RefNumber": "1163393", "RefComponent": "PM-EQM-EQ", "RefTitle": "Correction of time stamps in equipment time segments (2)", "RefUrl": "/notes/1163393 "}, {"RefNumber": "948607", "RefComponent": "PY-IT", "RefTitle": "HR-IT: 770 2006 Legal Change", "RefUrl": "/notes/948607 "}, {"RefNumber": "1438269", "RefComponent": "XX-CSC-HU-HR", "RefTitle": "Hungarian objects of LCP CE 03/2010", "RefUrl": "/notes/1438269 "}, {"RefNumber": "1442119", "RefComponent": "XX-CSC-CH-IS-H", "RefTitle": "IS-H CH: TARMED V1.07 - Neue Reports (REORG, EINSP, ABGL)", "RefUrl": "/notes/1442119 "}, {"RefNumber": "945038", "RefComponent": "EHS-SAF-RCK", "RefTitle": "Substance volume tracking: Class and char. do not exist", "RefUrl": "/notes/945038 "}, {"RefNumber": "1390629", "RefComponent": "FS-CML", "RefTitle": "New capital amounts with capitalized interest", "RefUrl": "/notes/1390629 "}, {"RefNumber": "1441588", "RefComponent": "XX-CSC-HU-HR", "RefTitle": "Legal Changes for Hungary before LCP CE 04/2010", "RefUrl": "/notes/1441588 "}, {"RefNumber": "1434923", "RefComponent": "XX-CSC-HU-HR", "RefTitle": "Legal Changes for Hungary before LCP CE 03/2010 part4", "RefUrl": "/notes/1434923 "}, {"RefNumber": "1434313", "RefComponent": "XX-CSC-HU-HR", "RefTitle": "Legal Changes for Hungary before LCP CE 03/2010 part3", "RefUrl": "/notes/1434313 "}, {"RefNumber": "1431462", "RefComponent": "XX-CSC-HU-HR", "RefTitle": "Legal Changes for Hungary before LCP CE 03/2010 part2", "RefUrl": "/notes/1431462 "}, {"RefNumber": "1431277", "RefComponent": "XX-CSC-HU-HR", "RefTitle": "Hungarian objects of LCP CE 02/2010", "RefUrl": "/notes/1431277 "}, {"RefNumber": "1434570", "RefComponent": "PY-ZA", "RefTitle": "IRP5: BAdI for Master data (Infotype 0006 & 0105)", "RefUrl": "/notes/1434570 "}, {"RefNumber": "770215", "RefComponent": "FS-CML-AC-RPM-IAS", "RefTitle": "ZEV: BTE 2850 and RFC", "RefUrl": "/notes/770215 "}, {"RefNumber": "1434314", "RefComponent": "XX-PART-ISHMED", "RefTitle": "Medication: Various Corrections and Technical Adaptations", "RefUrl": "/notes/1434314 "}, {"RefNumber": "1402150", "RefComponent": "PY-ZA", "RefTitle": "ZA SARS: IRP5 2010 & Payroll changes", "RefUrl": "/notes/1402150 "}, {"RefNumber": "1362657", "RefComponent": "IS-H-PM", "RefTitle": "IS-H CH: Swiss health insurance card PI connection SAP delta", "RefUrl": "/notes/1362657 "}, {"RefNumber": "1429456", "RefComponent": "XX-PART-ISHMED", "RefTitle": "Medication: Various Corrections and Technical Adaptations", "RefUrl": "/notes/1429456 "}, {"RefNumber": "1431457", "RefComponent": "XX-CSC-HU-HR", "RefTitle": "Legal Changes for Hungary before LCP CE 03/2010 part2", "RefUrl": "/notes/1431457 "}, {"RefNumber": "1318389", "RefComponent": "BC-CTS", "RefTitle": "How to Use .SAR/.CAR files and Risks Involved", "RefUrl": "/notes/1318389 "}, {"RefNumber": "1427948", "RefComponent": "XX-CSC-HU-HR", "RefTitle": "Legal Changes for Hungary before LCP CE 02/2010 part1", "RefUrl": "/notes/1427948 "}, {"RefNumber": "1429688", "RefComponent": "XX-CSC-HR-HR", "RefTitle": "HRHR: Legal Changes for Potvrda form before SP 02/2010", "RefUrl": "/notes/1429688 "}, {"RefNumber": "1428707", "RefComponent": "XX-CSC-HR-HR", "RefTitle": "HRHR: Legal Changes for IP form before SP 02/2010", "RefUrl": "/notes/1428707 "}, {"RefNumber": "1408909", "RefComponent": "PY-MX", "RefTitle": "Legal Change in Form 37 for 2009", "RefUrl": "/notes/1408909 "}, {"RefNumber": "1427083", "RefComponent": "PY-ES", "RefTitle": "Customizing for year beggining legal changes in 2010", "RefUrl": "/notes/1427083 "}, {"RefNumber": "1423313", "RefComponent": "XX-PART-ISHMED", "RefTitle": "Medication: Various Corrections and Technical Adaptations", "RefUrl": "/notes/1423313 "}, {"RefNumber": "1379641", "RefComponent": "SRM-XI", "RefTitle": "XML-message of PurchaseOrderRequest contains syntax error", "RefUrl": "/notes/1379641 "}, {"RefNumber": "1412161", "RefComponent": "SRM-EBP-PRC", "RefTitle": "PCARD workitem is reserved", "RefUrl": "/notes/1412161 "}, {"RefNumber": "1352966", "RefComponent": "IS-H-PM", "RefTitle": "IS-H CH: Swiss health insurance card - SAP Delta", "RefUrl": "/notes/1352966 "}, {"RefNumber": "1384039", "RefComponent": "SRM-EBP-WFL", "RefTitle": "Wrong languages in mail notifications (PO)", "RefUrl": "/notes/1384039 "}, {"RefNumber": "1409864", "RefComponent": "XX-CSC-HU-HR", "RefTitle": "Legal Changes for Hungary before LCP CE 12/2009", "RefUrl": "/notes/1409864 "}, {"RefNumber": "195515", "RefComponent": "FI-GL-GL-X", "RefTitle": "Installing ZFINDEX", "RefUrl": "/notes/195515 "}, {"RefNumber": "1402319", "RefComponent": "PY-AT", "RefTitle": "Rückstellungen: Berücksichtigung des Simulationsergebnises", "RefUrl": "/notes/1402319 "}, {"RefNumber": "1401222", "RefComponent": "XX-PART-ISHMED", "RefTitle": "Medication: Various Corrections and Technical Adaptations", "RefUrl": "/notes/1401222 "}, {"RefNumber": "1377745", "RefComponent": "XX-PART-ISHMED", "RefTitle": "Medication: Various Corrections and Technical Adaptations", "RefUrl": "/notes/1377745 "}, {"RefNumber": "1402463", "RefComponent": "XX-CSC-HU-HR", "RefTitle": "Hungarian objects of LCP CE 10/2009", "RefUrl": "/notes/1402463 "}, {"RefNumber": "1394387", "RefComponent": "XX-PART-ISHMED", "RefTitle": "Medication: Various Corrections and Technical Adaptations", "RefUrl": "/notes/1394387 "}, {"RefNumber": "1379891", "RefComponent": "PY-AT", "RefTitle": "Rückstellungen: Simulationsergebnis berücksichtigen", "RefUrl": "/notes/1379891 "}, {"RefNumber": "1400878", "RefComponent": "XX-CSC-HU-HR", "RefTitle": "Hungarian objects of LCP CE 11/2009", "RefUrl": "/notes/1400878 "}, {"RefNumber": "196023", "RefComponent": "BC-CST-UP", "RefTitle": "Collective run update: Composite corr. for 40B,45B,46B,46C", "RefUrl": "/notes/196023 "}, {"RefNumber": "1366667", "RefComponent": "XX-CSC-CH-IS-H", "RefTitle": "IS-H CH: Schweizer Versichertenkarte - PI-Korr 6.03", "RefUrl": "/notes/1366667 "}, {"RefNumber": "1355307", "RefComponent": "XX-CSC-CH-IS-H", "RefTitle": "IS-H CH: Schweizer Versichertenkarte PI-Anbindung", "RefUrl": "/notes/1355307 "}, {"RefNumber": "110910", "RefComponent": "BC-CTS-LAN", "RefTitle": "Deletion of language load", "RefUrl": "/notes/110910 "}, {"RefNumber": "1335149", "RefComponent": "PY-AR", "RefTitle": "Tax Report Argentina (HARUTAX0)", "RefUrl": "/notes/1335149 "}, {"RefNumber": "1000077", "RefComponent": "MM-IM", "RefTitle": "FAQ: Problems when implementing Note 32236", "RefUrl": "/notes/1000077 "}, {"RefNumber": "1173582", "RefComponent": "BC-CTS-TLS", "RefTitle": "Error occurs during export: invalid value '...' in E071.LANG", "RefUrl": "/notes/1173582 "}, {"RefNumber": "1352967", "RefComponent": "XX-CSC-CH-IS-H", "RefTitle": "IS-H CH: Schweizer Versichertenkarte - CH-Delta", "RefUrl": "/notes/1352967 "}, {"RefNumber": "978380", "RefComponent": "CRM-LOC-BR", "RefTitle": "Loc Brazil: Decimal places for rounding on Tax amounts", "RefUrl": "/notes/978380 "}, {"RefNumber": "1378183", "RefComponent": "XX-CSC-HU-HR", "RefTitle": "Legal Changes for Hungary before LCP CE 09/2009", "RefUrl": "/notes/1378183 "}, {"RefNumber": "1377146", "RefComponent": "XX-CSC-CH-IS-H", "RefTitle": "IS-H CH: Analyseleistungsregeln - Erweiterungen", "RefUrl": "/notes/1377146 "}, {"RefNumber": "1372564", "RefComponent": "XX-CSC-HR-HR", "RefTitle": "HRHR: Special tax", "RefUrl": "/notes/1372564 "}, {"RefNumber": "1369778", "RefComponent": "XX-CSC-HU-HR", "RefTitle": "Legal Changes for Hungary before LCP CE 08/2009", "RefUrl": "/notes/1369778 "}, {"RefNumber": "1278154", "RefComponent": "PY-ES", "RefTitle": "ES28.11: Affiliation Message (AFI) - Changes in RPCAFIE0", "RefUrl": "/notes/1278154 "}, {"RefNumber": "101217", "RefComponent": "BC-DB-DB2", "RefTitle": "DB2 z/OS: Overview of transports and corrections", "RefUrl": "/notes/101217 "}, {"RefNumber": "1258749", "RefComponent": "XX-CSC-BR-REP", "RefTitle": "SPED-EFD: Electronic Fiscal File - IM", "RefUrl": "/notes/1258749 "}, {"RefNumber": "1365115", "RefComponent": "XX-CSC-HU-HR", "RefTitle": "Legal Changes for Hungary before LCP CE 08/2009", "RefUrl": "/notes/1365115 "}, {"RefNumber": "1365394", "RefComponent": "XX-PART-ISHMED", "RefTitle": "Medication: Various Corrections and Technical Adaptations", "RefUrl": "/notes/1365394 "}, {"RefNumber": "1366434", "RefComponent": "XX-CSC-CH-IS-H", "RefTitle": "IS-H CH: Analysenliste- Leistungsregeln", "RefUrl": "/notes/1366434 "}, {"RefNumber": "985609", "RefComponent": "BW-WHM-DST-ARC", "RefTitle": "Lookup for active DataStore data in DB and near-line storage", "RefUrl": "/notes/985609 "}, {"RefNumber": "1332333", "RefComponent": "PA-PD-PM", "RefTitle": "Goal function (Main note)", "RefUrl": "/notes/1332333 "}, {"RefNumber": "1362404", "RefComponent": "XX-CSC-AT-IS-H", "RefTitle": "IS-H AT: Felder LV AT + CH bei neuem Geschäftspart. fehlen", "RefUrl": "/notes/1362404 "}, {"RefNumber": "1359388", "RefComponent": "XX-CSC-CH-IS-H", "RefTitle": "IS-H CH: Schweizer Versichertenkarte - CH-Korr 6.03", "RefUrl": "/notes/1359388 "}, {"RefNumber": "810638", "RefComponent": "BC-UPG-ADDON", "RefTitle": "IS-M: LIS info structures missing with new installation", "RefUrl": "/notes/810638 "}, {"RefNumber": "1354157", "RefComponent": "XX-CSC-CH-IS-H", "RefTitle": "IS-H: ANA-LISTE (CH-Delta)", "RefUrl": "/notes/1354157 "}, {"RefNumber": "1354156", "RefComponent": "XX-CSC-CH-IS-H", "RefTitle": "IS-H: ANA-LISTE (SAP-Delta)", "RefUrl": "/notes/1354156 "}, {"RefNumber": "1345069", "RefComponent": "XX-PART-ISHMED", "RefTitle": "Medication: Various Corrections and Technical Adaptations", "RefUrl": "/notes/1345069 "}, {"RefNumber": "560720", "RefComponent": "BC-CTS-ORG", "RefTitle": "Error messages in German when logged on in English", "RefUrl": "/notes/560720 "}, {"RefNumber": "1306514", "RefComponent": "XX-PART-ISHMED", "RefTitle": "Medication: Various Corrections and Technical Adaptations", "RefUrl": "/notes/1306514 "}, {"RefNumber": "1345614", "RefComponent": "XX-CSC-CH-IS-H", "RefTitle": "IS-H CH: Fehlende Auftragsdaten am Subscreen für Ext.Auftr.", "RefUrl": "/notes/1345614 "}, {"RefNumber": "1327680", "RefComponent": "PY-MY", "RefTitle": "New STD Formula 2009 - Part 2", "RefUrl": "/notes/1327680 "}, {"RefNumber": "1341984", "RefComponent": "BW-WHM-DST-DTP", "RefTitle": "Error \"Field length too long for PSA fields\", BI_CONT 7.04", "RefUrl": "/notes/1341984 "}, {"RefNumber": "1341931", "RefComponent": "BW-BCT-MM-BW", "RefTitle": "Load scenario in InfoCube 0IC_C03 contains syntax error", "RefUrl": "/notes/1341931 "}, {"RefNumber": "1326642", "RefComponent": "XX-CSC-FR-IS-H", "RefTitle": "IS-H FR: diverse Korrekturen", "RefUrl": "/notes/1326642 "}, {"RefNumber": "1266639", "RefComponent": "XX-CSC-FR-IS-H", "RefTitle": "IS-H FR: Incorrect German field name in EDI", "RefUrl": "/notes/1266639 "}, {"RefNumber": "1150330", "RefComponent": "SRM-EBP-PRO", "RefTitle": "Category Search Help - Toolbar not translated", "RefUrl": "/notes/1150330 "}, {"RefNumber": "1330228", "RefComponent": "XX-CSC-CH-IS-H", "RefTitle": "IS-H: Ext. Auftrag - Ermittlg Honaufteil.code neue Tabelle", "RefUrl": "/notes/1330228 "}, {"RefNumber": "99271", "RefComponent": "CA-EUR-CUR", "RefTitle": "Curr.maintenance euro, Curr. Customizing Assistant", "RefUrl": "/notes/99271 "}, {"RefNumber": "1298409", "RefComponent": "BC-CTS-TLS", "RefTitle": "R3trans import deletes enhancement objects", "RefUrl": "/notes/1298409 "}, {"RefNumber": "1326618", "RefComponent": "PA-EC", "RefTitle": "Workflow Task 04000013 is missing from the transport", "RefUrl": "/notes/1326618 "}, {"RefNumber": "1326719", "RefComponent": "XX-CSC-FR-IS-H", "RefTitle": "IS-H FR: Settings for certain messages cannot be performed", "RefUrl": "/notes/1326719 "}, {"RefNumber": "617096", "RefComponent": "BC-SRV-ASF-CHD", "RefTitle": "CD: Archiving programs for archiving object CHANGEDOCU", "RefUrl": "/notes/617096 "}, {"RefNumber": "610422", "RefComponent": "BC-SRV-ASF-CHD", "RefTitle": "CD:Archiving obj. CHANGEDOCU not complete in standard system", "RefUrl": "/notes/610422 "}, {"RefNumber": "11920", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (internally only)", "RefUrl": "/notes/11920 "}, {"RefNumber": "1309031", "RefComponent": "CO-OM-CCA-H", "RefTitle": "ALE CO-OM:Segment E1COKEY does not contain SEGMENT, PSEGMENT", "RefUrl": "/notes/1309031 "}, {"RefNumber": "1302988", "RefComponent": "XX-PART-ISHMED", "RefTitle": "Medication: Various Corrections and Technical Adaptations", "RefUrl": "/notes/1302988 "}, {"RefNumber": "1302490", "RefComponent": "XX-CSC-CH-IS-H", "RefTitle": "IS-H CH: <PERSON><PERSON><PERSON><PERSON> zu Tarmed 1.6 II", "RefUrl": "/notes/1302490 "}, {"RefNumber": "1297338", "RefComponent": "XX-CSC-AT-IS-H", "RefTitle": "IS-H AT: EDIVKA: Feldlängen ändern (SAP - Delta)", "RefUrl": "/notes/1297338 "}, {"RefNumber": "1282483", "RefComponent": "XX-CSC-CH-IS-H", "RefTitle": "IS-H CH: Fallabschluss/-öffnung", "RefUrl": "/notes/1282483 "}, {"RefNumber": "1282372", "RefComponent": "XX-CSC-CH-IS-H", "RefTitle": "IS-H CH: Fallabschluss/-öffnung (SAP-Delta)", "RefUrl": "/notes/1282372 "}, {"RefNumber": "1264582", "RefComponent": "CRM-IM-IPM-RO", "RefTitle": "Missing BEFM entries after applying SP12", "RefUrl": "/notes/1264582 "}, {"RefNumber": "1281821", "RefComponent": "XX-CSC-CH-IS-H", "RefTitle": "IS-H CH: Teil-Stationär entfällt in CH mit 01.01.2009", "RefUrl": "/notes/1281821 "}, {"RefNumber": "1265235", "RefComponent": "SCM-APO-FCS", "RefTitle": "Demand Planning: Report to display LiveCache Locks", "RefUrl": "/notes/1265235 "}, {"RefNumber": "1276101", "RefComponent": "XX-PART-ISHMED", "RefTitle": "Clinical Work Station: SAP Standard Function Variants", "RefUrl": "/notes/1276101 "}, {"RefNumber": "1229334", "RefComponent": "PY-PT", "RefTitle": "RPCAIDP0 and RPCIIDP0 improvements", "RefUrl": "/notes/1229334 "}, {"RefNumber": "1261113", "RefComponent": "FS-CML-AC-PI", "RefTitle": "BUC: Enhancement of BAdI FVD_LOAN_POST with AUFNR", "RefUrl": "/notes/1261113 "}, {"RefNumber": "1267041", "RefComponent": "XX-CSC-CH-IS-H", "RefTitle": "IS-H: Z<PERSON>tz<PERSON> Kopfumfang in Tabelle NGEB", "RefUrl": "/notes/1267041 "}, {"RefNumber": "1270584", "RefComponent": "XX-PROJ-CDP-030", "RefTitle": "Solution Manager template for CDP Richemont Gemini", "RefUrl": "/notes/1270584 "}, {"RefNumber": "1268987", "RefComponent": "XX-CSC-AT-IS-H", "RefTitle": "IS-H AT: EDIVKA - Änderungen", "RefUrl": "/notes/1268987 "}, {"RefNumber": "1267695", "RefComponent": "XX-PART-ISHMED", "RefTitle": "Nursing: Nursing Plan - Performance Problem with Change Docu", "RefUrl": "/notes/1267695 "}, {"RefNumber": "1173260", "RefComponent": "PY-PT-PS", "RefTitle": "Legal Change for CGA Magnetic File report", "RefUrl": "/notes/1173260 "}, {"RefNumber": "1222993", "RefComponent": "PY-PT-PS", "RefTitle": "Evaluation classes 18, 19 and 20 replaced", "RefUrl": "/notes/1222993 "}, {"RefNumber": "1256903", "RefComponent": "XX-CSC-AT-IS-H", "RefTitle": "IS-H AT: Dat.tr.aust. P321 - Stichtag für Name (SAP-Delta)", "RefUrl": "/notes/1256903 "}, {"RefNumber": "1252417", "RefComponent": "XX-PART-ISHMED", "RefTitle": "Patient Organizer/Viewer: Health Problem - Uppercase", "RefUrl": "/notes/1252417 "}, {"RefNumber": "1176128", "RefComponent": "PA-PA-BR", "RefTitle": "MP039800:Correction for a text element, with no attributes", "RefUrl": "/notes/1176128 "}, {"RefNumber": "1107922", "RefComponent": "PY-BR", "RefTitle": "MANAD: Implementation of Blocks I and L", "RefUrl": "/notes/1107922 "}, {"RefNumber": "1237763", "RefComponent": "XX-PART-ISHMED-ORD", "RefTitle": "Clinical Order: Incorrect Connection Radiological Documents", "RefUrl": "/notes/1237763 "}, {"RefNumber": "951067", "RefComponent": "FS-CML-AC-RPM-IOA", "RefTitle": "IOA: Addition to check report", "RefUrl": "/notes/951067 "}, {"RefNumber": "1174532", "RefComponent": "XAP-IC-BOP", "RefTitle": "CRM web services for BOP: implementation and configuration", "RefUrl": "/notes/1174532 "}, {"RefNumber": "1230742", "RefComponent": "FS-CML", "RefTitle": "New report RFVD_CHK_POSTED_RECORDS_VDBEPI", "RefUrl": "/notes/1230742 "}, {"RefNumber": "1235289", "RefComponent": "FIN-FSCM-TRM-TM-IS", "RefTitle": "0CFM_MARKET_VALUES cannot be activated", "RefUrl": "/notes/1235289 "}, {"RefNumber": "1177980", "RefComponent": "PA-PA-PT", "RefTitle": "Citizen's Card - new functionality of infotype 0185", "RefUrl": "/notes/1177980 "}, {"RefNumber": "715843", "RefComponent": "FS-CML", "RefTitle": "CORR: CML analysis reports", "RefUrl": "/notes/715843 "}, {"RefNumber": "1229631", "RefComponent": "XX-CSC-CH-IS-H", "RefTitle": "IS-H CH:Kostentrgrp. - Abfrage auf Kostentrschl.", "RefUrl": "/notes/1229631 "}, {"RefNumber": "1232740", "RefComponent": "IS-HER-CM", "RefTitle": "Special Development for PASSHE", "RefUrl": "/notes/1232740 "}, {"RefNumber": "831875", "RefComponent": "PY-PT", "RefTitle": "HR-PT: Minimum wage for Madeira", "RefUrl": "/notes/831875 "}, {"RefNumber": "838401", "RefComponent": "PY-PT", "RefTitle": "HR-PT: RPCOVCP0 - List employees with or w/o membership fees", "RefUrl": "/notes/838401 "}, {"RefNumber": "838704", "RefComponent": "PY-PT", "RefTitle": "HR-PT: Social balance - Absences introduced by CATS", "RefUrl": "/notes/838704 "}, {"RefNumber": "786687", "RefComponent": "PY-BR", "RefTitle": "RAIS: reporting brazilian-naturalized employee", "RefUrl": "/notes/786687 "}, {"RefNumber": "1178734", "RefComponent": "BW-WHM-DST-ARC", "RefTitle": "PSA Archiving", "RefUrl": "/notes/1178734 "}, {"RefNumber": "1038939", "RefComponent": "CO-PA-TO", "RefTitle": "Archiving profitability segments II.", "RefUrl": "/notes/1038939 "}, {"RefNumber": "1166761", "RefComponent": "PY-BR", "RefTitle": "HR-BR: Validation of infotype 2001", "RefUrl": "/notes/1166761 "}, {"RefNumber": "1174130", "RefComponent": "FS-PE", "RefTitle": "Consistency Checks", "RefUrl": "/notes/1174130 "}, {"RefNumber": "1096367", "RefComponent": "PY-BR", "RefTitle": "Documentation for the GRRF (HBRCGRRF) report", "RefUrl": "/notes/1096367 "}, {"RefNumber": "1172050", "RefComponent": "BC-ESI-WS-ABA", "RefTitle": "User information contains errors in Web service consumer", "RefUrl": "/notes/1172050 "}, {"RefNumber": "1153712", "RefComponent": "PA-ESS-XX-CE", "RefTitle": "IMG Node 'CE / GE' of ESS is missing from EHP3 SP2", "RefUrl": "/notes/1153712 "}, {"RefNumber": "1145684", "RefComponent": "PY-PT", "RefTitle": "RPCAIDP0: other deductions for Soc. Sec. contributions.", "RefUrl": "/notes/1145684 "}, {"RefNumber": "1148345", "RefComponent": "PY-PT", "RefTitle": "New processing method for handicapped and dependents", "RefUrl": "/notes/1148345 "}, {"RefNumber": "1168114", "RefComponent": "XX-CSC-AT-IS-H", "RefTitle": "IS-H AT: P321 - Änderungen", "RefUrl": "/notes/1168114 "}, {"RefNumber": "383964", "RefComponent": "FI-AP", "RefTitle": "Expiring currencies: Advance implementation", "RefUrl": "/notes/383964 "}, {"RefNumber": "1160987", "RefComponent": "PY-BR", "RefTitle": "HBRRAIS0: correction in the processing of PPRP off-cycle", "RefUrl": "/notes/1160987 "}, {"RefNumber": "1155459", "RefComponent": "PY-PT", "RefTitle": "RPCIIDP0: layout correction of company title in PDF format", "RefUrl": "/notes/1155459 "}, {"RefNumber": "1154235", "RefComponent": "PY-BR", "RefTitle": "RAIS: Employees without remuneration in the year.", "RefUrl": "/notes/1154235 "}, {"RefNumber": "946622", "RefComponent": "FS-CML-AC-RPM-IOA", "RefTitle": "IOA: Avoiding incorrect calculation of interest on arrears", "RefUrl": "/notes/946622 "}, {"RefNumber": "818359", "RefComponent": "PY-BR", "RefTitle": "Legal change in HBRDIRF0 for year 2005", "RefUrl": "/notes/818359 "}, {"RefNumber": "798398", "RefComponent": "PY-BR", "RefTitle": "HR-BR: Discount percentage of 100% could not be reported", "RefUrl": "/notes/798398 "}, {"RefNumber": "1166480", "RefComponent": "XX-CSC-AT-IS-H", "RefTitle": "IS-H: Patientenleitsystem Erweiterte Suche", "RefUrl": "/notes/1166480 "}, {"RefNumber": "1158200", "RefComponent": "XX-CSC-AT-IS-H", "RefTitle": "IS-H: Patientenleitsystem", "RefUrl": "/notes/1158200 "}, {"RefNumber": "1074757", "RefComponent": "SRM-EBP-WFL", "RefTitle": "Offline approval for Java PDAs, special characters", "RefUrl": "/notes/1074757 "}, {"RefNumber": "581269", "RefComponent": "BC-DB-DB2", "RefTitle": "Scheduling via DB13C in target DB2/390 system fails", "RefUrl": "/notes/581269 "}, {"RefNumber": "567422", "RefComponent": "BC-CTS-ORG", "RefTitle": "\"Object locked by upgrade\" error message", "RefUrl": "/notes/567422 "}, {"RefNumber": "560411", "RefComponent": "BC-CCM-MON-TUN", "RefTitle": "Syntax error in the program SAPLSTMO", "RefUrl": "/notes/560411 "}, {"RefNumber": "551104", "RefComponent": "BC-DB-DB2", "RefTitle": "DB2/390: SAPLS_CCDSNU_DB2 terminates due to a timeout", "RefUrl": "/notes/551104 "}, {"RefNumber": "549174", "RefComponent": "BC-DB-DB2", "RefTitle": "Transaction DB03 no longer supported for DB2/390", "RefUrl": "/notes/549174 "}, {"RefNumber": "526117", "RefComponent": "BC-BMT-WFM", "RefTitle": "WF builder: Workflow template does not exist in Version xxxx", "RefUrl": "/notes/526117 "}, {"RefNumber": "516513", "RefComponent": "BC-CTS-TLS", "RefTitle": "Incomplete transport of CDAT objects", "RefUrl": "/notes/516513 "}, {"RefNumber": "593469", "RefComponent": "BC-DB-DB2", "RefTitle": "DB2/390: Usage of global TEMP tables and related problems", "RefUrl": "/notes/593469 "}, {"RefNumber": "507824", "RefComponent": "BC-DB-DB2", "RefTitle": "DB2/390 V7: Real Time Statistics and DSNACCOR", "RefUrl": "/notes/507824 "}, {"RefNumber": "765983", "RefComponent": "BC-DB-DB2", "RefTitle": "DB2/zOS: Reducing number of DSNACCOR REORG recommendations", "RefUrl": "/notes/765983 "}, {"RefNumber": "444731", "RefComponent": "CRM-BF", "RefTitle": "Correction of table CRMC_EVENT_CALL", "RefUrl": "/notes/444731 "}, {"RefNumber": "320166", "RefComponent": "BC-CCM-MON-INF", "RefTitle": "No list in detailed analysis in DB02 (Informix)", "RefUrl": "/notes/320166 "}, {"RefNumber": "324393", "RefComponent": "BC-SRV-QUE", "RefTitle": "SAP query: Special copy functions", "RefUrl": "/notes/324393 "}, {"RefNumber": "328529", "RefComponent": "BW-BEX-ET", "RefTitle": "Bad index errors when processing variables", "RefUrl": "/notes/328529 "}, {"RefNumber": "440954", "RefComponent": "BC-DB-DB2", "RefTitle": "DB2/390: MCOD aspects in CCMS", "RefUrl": "/notes/440954 "}, {"RefNumber": "312557", "RefComponent": "BC-TWB-TST-CAT", "RefTitle": "CATT for work load generation", "RefUrl": "/notes/312557 "}, {"RefNumber": "438813", "RefComponent": "BC-SRV-SCR", "RefTitle": "RSTXTCAT variant SAP&EXTEND_SEL: blank fields", "RefUrl": "/notes/438813 "}, {"RefNumber": "366030", "RefComponent": "BC-SRV-LTS", "RefTitle": "English version of Lotus Domino Integration", "RefUrl": "/notes/366030 "}, {"RefNumber": "419321", "RefComponent": "CRM-MW-MRM", "RefTitle": "Mobile Client Recovery manager installation for 20C", "RefUrl": "/notes/419321 "}, {"RefNumber": "361763", "RefComponent": "BW-BEX-ET", "RefTitle": "Error message 'Please select a valid hierarchy'", "RefUrl": "/notes/361763 "}, {"RefNumber": "418880", "RefComponent": "BW-BEX-ET-MAP", "RefTitle": "No data display in BEx Map although query contains data", "RefUrl": "/notes/418880 "}, {"RefNumber": "420114", "RefComponent": "BW-BEX-ET", "RefTitle": "wdttree - type mismatch: Error in filter value selection", "RefUrl": "/notes/420114 "}, {"RefNumber": "373992", "RefComponent": "BW-BEX-ET-QDEF", "RefTitle": "Hotfix query definition for 2.0B Frontend Patch 9", "RefUrl": "/notes/373992 "}, {"RefNumber": "422619", "RefComponent": "BC-FES-ITS", "RefTitle": "Enabling SAP Systems for ITS Flow-Logic", "RefUrl": "/notes/422619 "}, {"RefNumber": "422291", "RefComponent": "BC-SRV-SCR", "RefTitle": "RSTXTCAT variant SAP&SHORT_SEL: blank field", "RefUrl": "/notes/422291 "}, {"RefNumber": "377368", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "Error in BW OLE DB f. OLAP components", "RefUrl": "/notes/377368 "}, {"RefNumber": "378722", "RefComponent": "SRM-EBP-CGS", "RefTitle": "Missng entries in BBPIMSCREEN for BBPCF01 & BBPIV02", "RefUrl": "/notes/378722 "}, {"RefNumber": "382735", "RefComponent": "SRM-EBP-CA-MSP", "RefTitle": "Prelim. transport f. TPD/UM sync. with MarketSet Procurement", "RefUrl": "/notes/382735 "}, {"RefNumber": "385723", "RefComponent": "CRM-ISA", "RefTitle": "Web file changes ISA 2.0c Support Package 2", "RefUrl": "/notes/385723 "}, {"RefNumber": "361643", "RefComponent": "SRM-EBP-APP", "RefTitle": "Problems with workflow/history display (applet)", "RefUrl": "/notes/361643 "}, {"RefNumber": "317576", "RefComponent": "CRM-ISA", "RefTitle": "Missing or incorrect IMS.DLL", "RefUrl": "/notes/317576 "}, {"RefNumber": "403575", "RefComponent": "BC-SRV-LTS", "RefTitle": "Lotus Notes Integration disrupted by hot package 17", "RefUrl": "/notes/403575 "}, {"RefNumber": "400274", "RefComponent": "BC-BMT-WFM", "RefTitle": "WF Builder: CUA interface defect after SP03", "RefUrl": "/notes/400274 "}, {"RefNumber": "483431", "RefComponent": "BC-CTS-TLS", "RefTitle": "Support Package incorrectly imported from sapservX using TMS", "RefUrl": "/notes/483431 "}, {"RefNumber": "178467", "RefComponent": "BC-DB-DBI", "RefTitle": "Syntax error in ST05 after implementing Note 116095", "RefUrl": "/notes/178467 "}, {"RefNumber": "178954", "RefComponent": "MM-IM-GF-ARC", "RefTitle": "Corrections for Archiving Object MM_HDEL (RM07KOHDEL2)", "RefUrl": "/notes/178954 "}, {"RefNumber": "154351", "RefComponent": "BC-CTS-LAN", "RefTitle": "Known problems during lang. import of patch texts", "RefUrl": "/notes/154351 "}, {"RefNumber": "1157802", "RefComponent": "XX-CSC-CH-IS-H", "RefTitle": "CH: Änd. für Rechn.nr. in nicht-abrechenb. Honorarleist.", "RefUrl": "/notes/1157802 "}, {"RefNumber": "1157762", "RefComponent": "XX-CSC-CH-IS-H", "RefTitle": "IS-H CH: Erweiterung d. Leistungsregel C23 Spartenzuschlag", "RefUrl": "/notes/1157762 "}, {"RefNumber": "1155721", "RefComponent": "PY-BR", "RefTitle": "HR-BR: Fields not available in the view V_T7BR06", "RefUrl": "/notes/1155721 "}, {"RefNumber": "983299", "RefComponent": "XX-PART-ISHMED", "RefTitle": "Medication: Missing BAdIs in the IMG", "RefUrl": "/notes/983299 "}, {"RefNumber": "1057221", "RefComponent": "XX-CSC-CH-IS-H", "RefTitle": "IS-H CH: Kostenträgergruppen analog zu Leistungsgruppen", "RefUrl": "/notes/1057221 "}, {"RefNumber": "1046190", "RefComponent": "CRM-ANA-OR", "RefTitle": "OLTP Reporting: Texts for Service Ticket Category", "RefUrl": "/notes/1046190 "}, {"RefNumber": "1121452", "RefComponent": "PY-BR", "RefTitle": "UNION: The discount is generated again when retrocalculated", "RefUrl": "/notes/1121452 "}, {"RefNumber": "1144912", "RefComponent": "PY-PT-PS", "RefTitle": "New customizing for Sit. Code 01,30,32 of CGA Magnetic File", "RefUrl": "/notes/1144912 "}, {"RefNumber": "1133698", "RefComponent": "PY-BR", "RefTitle": "BRPED: Simulation of a dismissal with vacation", "RefUrl": "/notes/1133698 "}, {"RefNumber": "1129349", "RefComponent": "PY-AT", "RefTitle": "RPCUBSA0RA: Erweiterungen für Hintergrundverarbeitung", "RefUrl": "/notes/1129349 "}, {"RefNumber": "1146033", "RefComponent": "PY-XX-RS", "RefTitle": "New function module to get wage type list by an eval. class", "RefUrl": "/notes/1146033 "}, {"RefNumber": "1037157", "RefComponent": "PY-BR", "RefTitle": "HR-BR: General enhancements", "RefUrl": "/notes/1037157 "}, {"RefNumber": "1144284", "RefComponent": "IS-ADEC-CC", "RefTitle": "IE4N: Problems with screen SAPLIPW4 0102 after Patch 0013", "RefUrl": "/notes/1144284 "}, {"RefNumber": "1141615", "RefComponent": "PY-AT", "RefTitle": "RPTSWAA0 Schwerarbeitsplatz §5 VO BMSG (diverse Anpassungen)", "RefUrl": "/notes/1141615 "}, {"RefNumber": "1116763", "RefComponent": "PA-PA-BR", "RefTitle": "HBRSEF00: validation error with especial characters", "RefUrl": "/notes/1116763 "}, {"RefNumber": "1137593", "RefComponent": "PY-BR", "RefTitle": "HBRDIRF0: Layout adjusts for 2008", "RefUrl": "/notes/1137593 "}, {"RefNumber": "822568", "RefComponent": "PY-BR", "RefTitle": "IRRF: 21 years old dependent", "RefUrl": "/notes/822568 "}, {"RefNumber": "1127132", "RefComponent": "PY-PT", "RefTitle": "New fields and records to Annual Income Declaration report", "RefUrl": "/notes/1127132 "}, {"RefNumber": "1129009", "RefComponent": "XX-CSC-CH-IS-H", "RefTitle": "IS-H: Externer Auftrag - F4-Hilfe Auftraggeber", "RefUrl": "/notes/1129009 "}, {"RefNumber": "1033795", "RefComponent": "SRM-EBP-INV", "RefTitle": "BBPERS: Value-added tax not displayed for each credit memo", "RefUrl": "/notes/1033795 "}, {"RefNumber": "1117928", "RefComponent": "PY-AT", "RefTitle": "JW 2007/08: Lohnzettel 5 für ALV (Zusatzreport)", "RefUrl": "/notes/1117928 "}, {"RefNumber": "1131677", "RefComponent": "PY-AT", "RefTitle": "Kammerumlage- und IESG-Pflicht für freie Dienstnehmer", "RefUrl": "/notes/1131677 "}, {"RefNumber": "83204", "RefComponent": "CO-PA-TO", "RefTitle": "Usage strategy for summarization levels", "RefUrl": "/notes/83204 "}, {"RefNumber": "1129826", "RefComponent": "PY-AT", "RefTitle": "Pilotkundenhinweis: Vorablieferung Jahreswechsel 2007/2008", "RefUrl": "/notes/1129826 "}, {"RefNumber": "313406", "RefComponent": "CA-ESS-ITS", "RefTitle": "Japanese characters in ESS tree applet", "RefUrl": "/notes/313406 "}, {"RefNumber": "898279", "RefComponent": "FS-CML-PO-DI", "RefTitle": "Disbrsmt: BAdI incidental costs (new import parameter)", "RefUrl": "/notes/898279 "}, {"RefNumber": "953877", "RefComponent": "PA-PA-US", "RefTitle": "Hardcoded Address Subtype in W2 Reprint Application.", "RefUrl": "/notes/953877 "}, {"RefNumber": "1064225", "RefComponent": "PY-AT", "RefTitle": "Pendlerpauschale Anhebung ab 01.07.2007", "RefUrl": "/notes/1064225 "}, {"RefNumber": "1081156", "RefComponent": "PY-BR", "RefTitle": "Accessible Simple Lists", "RefUrl": "/notes/1081156 "}, {"RefNumber": "1061374", "RefComponent": "PY-AT", "RefTitle": "AV-Beitrag für Männer ab 56: Rückrechnung aus Folgeperioden", "RefUrl": "/notes/1061374 "}, {"RefNumber": "1028212", "RefComponent": "PY-CH", "RefTitle": "WHT VD: Electronic communication with tax office (EMP-IS)", "RefUrl": "/notes/1028212 "}, {"RefNumber": "902210", "RefComponent": "PY-AT-PS", "RefTitle": "Jahreswechsel 2005/06 (öffentlicher Dienst): Änderungen BVA", "RefUrl": "/notes/902210 "}, {"RefNumber": "1071989", "RefComponent": "PY-AT", "RefTitle": "ALV-Beitrag für Männer ab 56 (über Verrechnungsgruppen)", "RefUrl": "/notes/1071989 "}, {"RefNumber": "1073475", "RefComponent": "PY-BR", "RefTitle": "HBRPAYR0 - corrections in alv list display", "RefUrl": "/notes/1073475 "}, {"RefNumber": "980153", "RefComponent": "PA-PA-IN", "RefTitle": "Inconsistent screen struct for ESS scenario of Personal Data", "RefUrl": "/notes/980153 "}, {"RefNumber": "913171", "RefComponent": "PA-ESS-XX", "RefTitle": "Adapter enhancements for Allowed Infotype/subtype", "RefUrl": "/notes/913171 "}, {"RefNumber": "920491", "RefComponent": "PA-ESS-XX", "RefTitle": "ESS:Change Own Data enhancement to allow multi record update", "RefUrl": "/notes/920491 "}, {"RefNumber": "923186", "RefComponent": "PA-ESS-OCY", "RefTitle": "ESS Scenarios for Finland", "RefUrl": "/notes/923186 "}, {"RefNumber": "929353", "RefComponent": "CA-ESS-WD", "RefTitle": "Changes to the Home and Area page customizing for LWE", "RefUrl": "/notes/929353 "}, {"RefNumber": "1114298", "RefComponent": "XX-PART-ISHMED", "RefTitle": "Nursing: Missing prospective NSR classifications", "RefUrl": "/notes/1114298 "}, {"RefNumber": "1109554", "RefComponent": "XX-PART-ISHMED", "RefTitle": "Planning: Appointment, Movement not Linked with Appointment", "RefUrl": "/notes/1109554 "}, {"RefNumber": "1113730", "RefComponent": "PY-PT", "RefTitle": "HR-PT: Update of Economical Activity Codes - T5PEA", "RefUrl": "/notes/1113730 "}, {"RefNumber": "1088492", "RefComponent": "PY-AT", "RefTitle": "JW 2007/08: Schwerarbeitsplätze nach §5 VO des BMSG", "RefUrl": "/notes/1088492 "}, {"RefNumber": "1077831", "RefComponent": "PY-AT", "RefTitle": "Bescheinigungswesen für Österreich - Transaktion PM20", "RefUrl": "/notes/1077831 "}, {"RefNumber": "1114543", "RefComponent": "PY-AT", "RefTitle": "Dienstgeberabgabe Wien (neue Version) Merkmal AUMGR fehlt", "RefUrl": "/notes/1114543 "}, {"RefNumber": "1034838", "RefComponent": "PY-PT", "RefTitle": "HR-PT: RPCGSRP0 changed to show error list", "RefUrl": "/notes/1034838 "}, {"RefNumber": "1077619", "RefComponent": "BC-CTS-TLS", "RefTitle": "R3trans deletes table entries protected by TRESC", "RefUrl": "/notes/1077619 "}, {"RefNumber": "940193", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "No selection or aggregation with direct access using DTP", "RefUrl": "/notes/940193 "}, {"RefNumber": "910338", "RefComponent": "PY-US-TR", "RefTitle": "Form 940: Year End 2005 changes", "RefUrl": "/notes/910338 "}, {"RefNumber": "883862", "RefComponent": "XX-PROJ-CDP-010-R3", "RefTitle": "Guidelines to install MRS Utilization report on WAS 620", "RefUrl": "/notes/883862 "}, {"RefNumber": "1106624", "RefComponent": "XX-CSC-CH-IS-H", "RefTitle": "IS-H: Externer Auftrag - Korrekturen ( Ext. AG, ...)", "RefUrl": "/notes/1106624 "}, {"RefNumber": "1098253", "RefComponent": "PY-BR", "RefTitle": "BRD0: Payment Wage Types of Extra Hours", "RefUrl": "/notes/1098253 "}, {"RefNumber": "1101402", "RefComponent": "XX-CSC-CH-IS-H", "RefTitle": "IS-H: <PERSON><PERSON><PERSON> Auftrag - TNEO_CHARFAC_DET - Abrechnungsver.", "RefUrl": "/notes/1101402 "}, {"RefNumber": "1099375", "RefComponent": "XX-CSC-CH-IS-H", "RefTitle": "IS-H: <PERSON><PERSON><PERSON> Auftrag - TNEO_CHARFAC_DET - Keyerweiterung", "RefUrl": "/notes/1099375 "}, {"RefNumber": "1093412", "RefComponent": "XX-PART-ISHMED", "RefTitle": "Clinical Order: Order Templates", "RefUrl": "/notes/1093412 "}, {"RefNumber": "1087527", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "Performance of direct access: runtime object for transformation", "RefUrl": "/notes/1087527 "}, {"RefNumber": "1085152", "RefComponent": "XX-PART-ISHMED", "RefTitle": "Technical Adaptation", "RefUrl": "/notes/1085152 "}, {"RefNumber": "1096503", "RefComponent": "XX-PART-ISHMED", "RefTitle": "Technical Adaptation", "RefUrl": "/notes/1096503 "}, {"RefNumber": "423419", "RefComponent": "IS-B", "RefTitle": "Additional info about 4.6C upgrade BANKING 4.63/CFM 2.0", "RefUrl": "/notes/423419 "}, {"RefNumber": "637812", "RefComponent": "XX-CSC-FI-HR", "RefTitle": "HFIN: Add-On Support Packages HFIN 461", "RefUrl": "/notes/637812 "}, {"RefNumber": "423364", "RefComponent": "IS-B", "RefTitle": "Additional info about installing Banking 4.63 / CFM 2.0", "RefUrl": "/notes/423364 "}, {"RefNumber": "1083464", "RefComponent": "XX-PART-ISHMED-ORD", "RefTitle": "Clin. Patient Management: BAdI for Case Validity Check", "RefUrl": "/notes/1083464 "}, {"RefNumber": "1013282", "RefComponent": "FI-AA-IS", "RefTitle": "Audit Information System (FI-AA) - missing functions", "RefUrl": "/notes/1013282 "}, {"RefNumber": "1083722", "RefComponent": "FI-AP-AP-B1", "RefTitle": "RFEBBECODA00: SAR file for the customers in 46C and 470", "RefUrl": "/notes/1083722 "}, {"RefNumber": "1087880", "RefComponent": "XX-PART-ISHMED-ORD", "RefTitle": "Patient register: Correction report, data transfer", "RefUrl": "/notes/1087880 "}, {"RefNumber": "1040911", "RefComponent": "PY-BR", "RefTitle": "HBRRAIS0 - 13th Salary advanced for the next year", "RefUrl": "/notes/1040911 "}, {"RefNumber": "504304", "RefComponent": "PY-US-RP", "RefTitle": "CCR: Cost Center Report - Phase 1", "RefUrl": "/notes/504304 "}, {"RefNumber": "1086512", "RefComponent": "XX-PART-ISHMED-ORD", "RefTitle": "Nursing: 'End Nursing Plan': Cycle end date (corr. report)", "RefUrl": "/notes/1086512 "}, {"RefNumber": "791983", "RefComponent": "FS-CML-PO", "RefTitle": "STM: CML connection to the Data Retention Tool", "RefUrl": "/notes/791983 "}, {"RefNumber": "353278", "RefComponent": "FS-CML-IS", "RefTitle": "REP: Statement of remaining terms for Release 4.6B/C", "RefUrl": "/notes/353278 "}, {"RefNumber": "1075750", "RefComponent": "XX-PART-ISHMED", "RefTitle": "Technical adaptations", "RefUrl": "/notes/1075750 "}, {"RefNumber": "1008250", "RefComponent": "BW-BCT-LO-LIS", "RefTitle": "Backup table for the queues of logistics extraction into BI", "RefUrl": "/notes/1008250 "}, {"RefNumber": "1071848", "RefComponent": "XX-PART-ISHMED", "RefTitle": "Technical Adaptation", "RefUrl": "/notes/1071848 "}, {"RefNumber": "1042965", "RefComponent": "CRM-FRW-AFP", "RefTitle": "Launch transactions get an own authorization object", "RefUrl": "/notes/1042965 "}, {"RefNumber": "1077041", "RefComponent": "PSM-FM-PO-RE", "RefTitle": "Workflow template 80500029 is not available", "RefUrl": "/notes/1077041 "}, {"RefNumber": "1060793", "RefComponent": "IS-M-AMC", "RefTitle": "Delta related to Prototype IS-M/AMC 3.0", "RefUrl": "/notes/1060793 "}, {"RefNumber": "1071837", "RefComponent": "XX-PART-ISHMED", "RefTitle": "Clinical Order: Duplicatre Entries in Table N1COMPA", "RefUrl": "/notes/1071837 "}, {"RefNumber": "996997", "RefComponent": "PY-AT", "RefTitle": "JW 2006/07: Änderungen zum Lohnzettel Finanz (Rel 46C-470)", "RefUrl": "/notes/996997 "}, {"RefNumber": "1071814", "RefComponent": "IS-DFS-PDR", "RefTitle": "UPS-Werksfilter", "RefUrl": "/notes/1071814 "}, {"RefNumber": "955278", "RefComponent": "PY-AT-PS", "RefTitle": "PM-SAP: Technische Voraussetzungen", "RefUrl": "/notes/955278 "}, {"RefNumber": "1019028", "RefComponent": "XX-PROJ-CDP-SPL", "RefTitle": "CATFORD CDP SPL-CA 1c: modifications via crosstransport", "RefUrl": "/notes/1019028 "}, {"RefNumber": "1051011", "RefComponent": "XX-CSC-AT-IS-H", "RefTitle": "IS-H AT: EDI Nachrichtenversand mit Kommentar", "RefUrl": "/notes/1051011 "}, {"RefNumber": "1051775", "RefComponent": "XX-CSC-AT-IS-H", "RefTitle": "IS-H AT: Wikraf - V01-Datensatz übersteuern", "RefUrl": "/notes/1051775 "}, {"RefNumber": "513455", "RefComponent": "XX-CSC-NL", "RefTitle": "Dutch Chain Liability (Wet Ketenaansprakelijkheid)", "RefUrl": "/notes/513455 "}, {"RefNumber": "1055322", "RefComponent": "BW-WHM-DST-ARC", "RefTitle": "Cannot diagnose near-line connection", "RefUrl": "/notes/1055322 "}, {"RefNumber": "832577", "RefComponent": "BC-DWB-DIC-AC", "RefTitle": "Runtime error on Unicode - inconsistent table types", "RefUrl": "/notes/832577 "}, {"RefNumber": "1044007", "RefComponent": "PY-BE-PS", "RefTitle": "The B2A process for DmfAPPL declarations is not supported", "RefUrl": "/notes/1044007 "}, {"RefNumber": "1053864", "RefComponent": "XX-CSC-AT-IS-H", "RefTitle": "IS-H AT: EDIVKA BC-Set ISH_TNWAT_EDI_CODES_S für 6.00", "RefUrl": "/notes/1053864 "}, {"RefNumber": "831275", "RefComponent": "PY-BR", "RefTitle": "SEFIP: collective agreement and other enhancements", "RefUrl": "/notes/831275 "}, {"RefNumber": "1049498", "RefComponent": "XX-CSC-CH-IS-H", "RefTitle": "IS-H CH: Fallartwechsel stat->amb - Prf alte Aufn. Lgen.", "RefUrl": "/notes/1049498 "}, {"RefNumber": "1046118", "RefComponent": "FI-AA-IS", "RefTitle": "Audit Info System (FI-AA) - Supplement for Note 1013282", "RefUrl": "/notes/1046118 "}, {"RefNumber": "1044189", "RefComponent": "XX-CSC-AT-IS-H", "RefTitle": "IS-H AT: PPR Einstufung - Auswertung RNWATPPR01", "RefUrl": "/notes/1044189 "}, {"RefNumber": "917854", "RefComponent": "PY-BR", "RefTitle": "New DDIC objects: GPS and SEFIP Compensation", "RefUrl": "/notes/917854 "}, {"RefNumber": "371288", "RefComponent": "BW-BCT-MTD", "RefTitle": "Termination in object coll.(RSO 296) due to ext.cube", "RefUrl": "/notes/371288 "}, {"RefNumber": "1044755", "RefComponent": "XX-CSC-CH-IS-H", "RefTitle": "IS-H CH: Prüfung Änderung der Patientenklasse nach Lstgen", "RefUrl": "/notes/1044755 "}, {"RefNumber": "1017574", "RefComponent": "XX-CSC-AT-IS-H", "RefTitle": "IS-H AT: Einstellungen fürs Storno abhängiger Leistungen", "RefUrl": "/notes/1017574 "}, {"RefNumber": "1038634", "RefComponent": "PY-PT", "RefTitle": "HR-PT: Legal change, fiscal status table update", "RefUrl": "/notes/1038634 "}, {"RefNumber": "935683", "RefComponent": "PY-AT", "RefTitle": "Gemeindetabelle für Kommunalsteuer (aktualisierte Einträge)", "RefUrl": "/notes/935683 "}, {"RefNumber": "882093", "RefComponent": "PY-BR", "RefTitle": "HBRRAIS0 to run on all companies at once", "RefUrl": "/notes/882093 "}, {"RefNumber": "1028682", "RefComponent": "XX-CSC-CH-IS-H", "RefTitle": "IS-H CH: RNLAUS01 - Bestandsauszug um CH-<PERSON><PERSON> erweitern", "RefUrl": "/notes/1028682 "}, {"RefNumber": "875109", "RefComponent": "PY-BR", "RefTitle": "SEFIP: code updates", "RefUrl": "/notes/875109 "}, {"RefNumber": "1025461", "RefComponent": "XX-CSC-AT-IS-H", "RefTitle": "IS-H AT: Klinischer Arbeitsplatz (AT-Feldbefüllung)", "RefUrl": "/notes/1025461 "}, {"RefNumber": "157009", "RefComponent": "XX-CSC-GB", "RefTitle": "Construction Industry Scheme (CIS)  UK", "RefUrl": "/notes/157009 "}, {"RefNumber": "868660", "RefComponent": "CRM-MW-SRV", "RefTitle": "Inconsistencies between CRM Server and Mobile Client", "RefUrl": "/notes/868660 "}, {"RefNumber": "816605", "RefComponent": "CRM-MW-SRV", "RefTitle": "Loss of data during date confirmation processing", "RefUrl": "/notes/816605 "}, {"RefNumber": "809424", "RefComponent": "CRM-MW-SRV", "RefTitle": "Account group field not updated on Mobile Client", "RefUrl": "/notes/809424 "}, {"RefNumber": "996998", "RefComponent": "PY-AT", "RefTitle": "JW 2006/07:Änderungen zum Lohnzettel Finanz (ab Rel ERP2004)", "RefUrl": "/notes/996998 "}, {"RefNumber": "1014911", "RefComponent": "PY-BR", "RefTitle": "HBRRAIS0 Legal Changes 2007", "RefUrl": "/notes/1014911 "}, {"RefNumber": "1013196", "RefComponent": "XX-PART-ISHMED", "RefTitle": "Enable Nursing Staff Regulation Transfer for Prospective Ser", "RefUrl": "/notes/1013196 "}, {"RefNumber": "1014314", "RefComponent": "XX-PROJ-CDP-010-R3", "RefTitle": "Guidelines to install MRS Utilization report on WAS 620", "RefUrl": "/notes/1014314 "}, {"RefNumber": "1006767", "RefComponent": "PY-AT-PS", "RefTitle": "Jahreswechsel 2006/07 (öffentlicher Dienst): Änderungen BVA", "RefUrl": "/notes/1006767 "}, {"RefNumber": "937400", "RefComponent": "PY-AT", "RefTitle": "PM-SAP: RPCSVBA2PBS und RPCBETA1 parallelisiert", "RefUrl": "/notes/937400 "}, {"RefNumber": "1011956", "RefComponent": "XX-CSC-CH-IS-H", "RefTitle": "IS-H CH: RNWCHSKH1 - hospital statistics", "RefUrl": "/notes/1011956 "}, {"RefNumber": "1009718", "RefComponent": "XX-CSC-AT-IS-H", "RefTitle": "IS-H AT: PPR Einstufung - prospektive Daten", "RefUrl": "/notes/1009718 "}, {"RefNumber": "997653", "RefComponent": "XX-CSC-AT-IS-H", "RefTitle": "IS-H AT: Standard implementation EDIVKA 5.0 (Delta)", "RefUrl": "/notes/997653 "}, {"RefNumber": "991745", "RefComponent": "RE-FX-IT", "RefTitle": "Input tax correction: Corrections in Support Package 7", "RefUrl": "/notes/991745 "}, {"RefNumber": "995676", "RefComponent": "FI-AA-IS", "RefTitle": "RASIMU02 - revised standard variants", "RefUrl": "/notes/995676 "}, {"RefNumber": "995573", "RefComponent": "FI-TV-COS-PS", "RefTitle": "PM-SAP: Auslieferung Erweiterung TRV Q4/2006", "RefUrl": "/notes/995573 "}, {"RefNumber": "990461", "RefComponent": "XX-PART-ISHMED-ORD", "RefTitle": "Clinical Work Station: Customer-specific view types", "RefUrl": "/notes/990461 "}, {"RefNumber": "727102", "RefComponent": "XX-CSC-BR", "RefTitle": "New DDIC Objects for ISS, PIS, COFINS; CSSL", "RefUrl": "/notes/727102 "}, {"RefNumber": "990227", "RefComponent": "XX-PART-ISHMED", "RefTitle": "Clin order: Migration report N1_MIGPRG - Error Messages", "RefUrl": "/notes/990227 "}, {"RefNumber": "418898", "RefComponent": "FI-AP-AP-B1", "RefTitle": "Foreign payments with LUM2 format, Finland", "RefUrl": "/notes/418898 "}, {"RefNumber": "660836", "RefComponent": "PY-US-RP", "RefTitle": "CCR: User-exits in Cost Center Report.", "RefUrl": "/notes/660836 "}, {"RefNumber": "982031", "RefComponent": "XX-CSC-AT-IS-H", "RefTitle": "IS-H AT: Scoring - Anpassungen an das Modell 2007", "RefUrl": "/notes/982031 "}, {"RefNumber": "988501", "RefComponent": "XX-PART-ISHMED", "RefTitle": "Medication: Documentation BAdI N1_GENERATE_MEEVENT", "RefUrl": "/notes/988501 "}, {"RefNumber": "985289", "RefComponent": "XX-CSC-AT-IS-H", "RefTitle": "IS-H AT: Intensivdaten - Prüfen bei OE-Wechsel", "RefUrl": "/notes/985289 "}, {"RefNumber": "971179", "RefComponent": "XX-PART-ISHMED-ORD", "RefTitle": "Clin. work station: Create customer-specific view types", "RefUrl": "/notes/971179 "}, {"RefNumber": "981676", "RefComponent": "XX-PART-ISHMED-ORD", "RefTitle": "Clin. work station: Customer-specific view types", "RefUrl": "/notes/981676 "}, {"RefNumber": "945787", "RefComponent": "PY-BR", "RefTitle": "HBRCAGED: Record type C and X - Layout changes", "RefUrl": "/notes/945787 "}, {"RefNumber": "980828", "RefComponent": "XX-PART-ISHMED", "RefTitle": "Medication: BAdI for Customer-Specific Drug Search", "RefUrl": "/notes/980828 "}, {"RefNumber": "971534", "RefComponent": "XX-CSC-AT-IS-H", "RefTitle": "IS-H AT: Report RNWAT_KORR_NTPKCH - Abgl. NTPKCH - NTSP", "RefUrl": "/notes/971534 "}, {"RefNumber": "977290", "RefComponent": "XX-PART-ISHMED", "RefTitle": "Nursing: Nursing Plan - Determine Selection Period", "RefUrl": "/notes/977290 "}, {"RefNumber": "976518", "RefComponent": "SRM-CAT-MDM", "RefTitle": "SRM MDM Catalog enhancement: BAdI Implementation", "RefUrl": "/notes/976518 "}, {"RefNumber": "975898", "RefComponent": "XX-PART-ISHMED", "RefTitle": "Clin. Work Station: Form ISHMED_OPPLAN - Error Message", "RefUrl": "/notes/975898 "}, {"RefNumber": "973701", "RefComponent": "XX-CSC-AT-IS-H", "RefTitle": "IS-H AT: Einzugsgebiete bei abweichenden Rechnungsadressen", "RefUrl": "/notes/973701 "}, {"RefNumber": "656651", "RefComponent": "PY-AR", "RefTitle": "Retroactive Accounting and differences for SSO and TAX", "RefUrl": "/notes/656651 "}, {"RefNumber": "968928", "RefComponent": "BW-WHM-DST-ARC", "RefTitle": "BI Nearline Interface: Preshipment of NW 2004s SP 09", "RefUrl": "/notes/968928 "}, {"RefNumber": "532793", "RefComponent": "SD-BF-TP", "RefTitle": "Text determination access sequences are missing", "RefUrl": "/notes/532793 "}, {"RefNumber": "813518", "RefComponent": "XX-CSC-AT-IS-H", "RefTitle": "IS-H AT: Probleme mit \"temporären Adressobjekten\"", "RefUrl": "/notes/813518 "}, {"RefNumber": "968363", "RefComponent": "XX-CSC-CH-IS-H", "RefTitle": "IS-H CH: Rechnung V4.0, Überschrift TG/TP Rechnung", "RefUrl": "/notes/968363 "}, {"RefNumber": "965183", "RefComponent": "SRM-CM", "RefTitle": "CatMan - Missing property keys in view BBPV_CM_PROPVALS", "RefUrl": "/notes/965183 "}, {"RefNumber": "795492", "RefComponent": "XX-CSC-IN", "RefTitle": "Transaction J2IUN CENVAT Utilization with Service Tax Credit", "RefUrl": "/notes/795492 "}, {"RefNumber": "960915", "RefComponent": "FI-AA-IS", "RefTitle": "RAAEND01 - revised standard variants", "RefUrl": "/notes/960915 "}, {"RefNumber": "902387", "RefComponent": "RE-IT", "RefTitle": "§15a USt 2005 (Deutschland): Auslieferung", "RefUrl": "/notes/902387 "}, {"RefNumber": "954447", "RefComponent": "XX-CSC-AT-IS-H", "RefTitle": "IS-H AT: ELDA - aktuelle Anpassungen (P321, ELDAL)", "RefUrl": "/notes/954447 "}, {"RefNumber": "772258", "RefComponent": "FS-CML-AC", "RefTitle": "BUC: Only user-specific layout possible", "RefUrl": "/notes/772258 "}, {"RefNumber": "712898", "RefComponent": "MM-PUR-GF-BW", "RefTitle": "Performance problems to extract the purchasing information", "RefUrl": "/notes/712898 "}, {"RefNumber": "952103", "RefComponent": "IS-H", "RefTitle": "Clinical order: migrated order categories - runtime error", "RefUrl": "/notes/952103 "}, {"RefNumber": "951757", "RefComponent": "FI-AA-IS", "RefTitle": "RAANLA_ALV01 Revised standard variant SAP&001", "RefUrl": "/notes/951757 "}, {"RefNumber": "948649", "RefComponent": "XX-CSC-AT-IS-H", "RefTitle": "IS-H AT: Intensivdokumentation - Stornieren nicht möglich", "RefUrl": "/notes/948649 "}, {"RefNumber": "923190", "RefComponent": "BW-WHM-DST-ARC", "RefTitle": "NLS prototype: Incorrect \"CREATE table\" statement", "RefUrl": "/notes/923190 "}, {"RefNumber": "926976", "RefComponent": "PY-BR", "RefTitle": "DDIC: Missing domain PBR_INEMP", "RefUrl": "/notes/926976 "}, {"RefNumber": "627033", "RefComponent": "CA-GTF-MS", "RefTitle": "Implementing Mass Change of Sales Orders in R/3 release 45B", "RefUrl": "/notes/627033 "}, {"RefNumber": "944647", "RefComponent": "PY-AT", "RefTitle": "Infotyp 0632: Fehlermeldung: Geschuetzte Tabstrip Reiter...", "RefUrl": "/notes/944647 "}, {"RefNumber": "557148", "RefComponent": "BC-BW", "RefTitle": "Executing conversion exits", "RefUrl": "/notes/557148 "}, {"RefNumber": "915555", "RefComponent": "FI-GL-GL-F", "RefTitle": "Elec tax return special advance pymt and permanent extension", "RefUrl": "/notes/915555 "}, {"RefNumber": "564784", "RefComponent": "CO-OM-OPA-B", "RefTitle": "Plankopieren im Testlauf verbucht", "RefUrl": "/notes/564784 "}, {"RefNumber": "936235", "RefComponent": "XX-PART-ISHMED", "RefTitle": "Clin. Work Station: Form ISHMED_OPPLAN - Overlaps", "RefUrl": "/notes/936235 "}, {"RefNumber": "931569", "RefComponent": "XX-PART-ISHMED", "RefTitle": "Performance Improvements Environment", "RefUrl": "/notes/931569 "}, {"RefNumber": "107210", "RefComponent": "PY-PT", "RefTitle": "HR-PT: Update of legal limits (LCP)", "RefUrl": "/notes/107210 "}, {"RefNumber": "926566", "RefComponent": "IS-DFS-MA-MT", "RefTitle": "Mobile Component Descriptor für MDS 1.0 (<PERSON><PERSON><PERSON><PERSON> obsolete)", "RefUrl": "/notes/926566 "}, {"RefNumber": "917199", "RefComponent": "PY-US-TR", "RefTitle": "1099R Correction Processing Forms", "RefUrl": "/notes/917199 "}, {"RefNumber": "907191", "RefComponent": "PY-US-TR", "RefTitle": "W-2 magnetic media for Puerto Rico", "RefUrl": "/notes/907191 "}, {"RefNumber": "910766", "RefComponent": "PY-US-TR", "RefTitle": "Form 940 is incorrect when employee has tips", "RefUrl": "/notes/910766 "}, {"RefNumber": "913024", "RefComponent": "PY-BR", "RefTitle": "HBRSEF00 8.0 fixes", "RefUrl": "/notes/913024 "}, {"RefNumber": "916939", "RefComponent": "XX-PART-ISHMED", "RefTitle": "Planning Grid - Appt Start Time Grid or Time Slot", "RefUrl": "/notes/916939 "}, {"RefNumber": "874686", "RefComponent": "PY-BR", "RefTitle": "HBRCFER0 - new BAdIs available for customer specific process", "RefUrl": "/notes/874686 "}, {"RefNumber": "918548", "RefComponent": "EHS-SAF-RSH", "RefTitle": "You cannot assign several report categories to one country", "RefUrl": "/notes/918548 "}, {"RefNumber": "890833", "RefComponent": "PY-US-TR", "RefTitle": "U.S. Tax Reporter: Year End 2005 Phase II", "RefUrl": "/notes/890833 "}, {"RefNumber": "902951", "RefComponent": "PY-US-TR", "RefTitle": "Invalid SSN range in W-2 self-sealer form", "RefUrl": "/notes/902951 "}, {"RefNumber": "921823", "RefComponent": "XX-PART-ISHMED", "RefTitle": "Transactions NT32 and NT33 not Executable", "RefUrl": "/notes/921823 "}, {"RefNumber": "916260", "RefComponent": "XX-PART-ISHMED", "RefTitle": "Planning: Appointment Template - Caller for BAdI N_APP_CONST", "RefUrl": "/notes/916260 "}, {"RefNumber": "911049", "RefComponent": "XX-CSC-AT-IS-H", "RefTitle": "IS-H AT: Report RNWATKUELIST um \"ohne VV/SV\" erweitern", "RefUrl": "/notes/911049 "}, {"RefNumber": "912286", "RefComponent": "XX-PART-ISHMED", "RefTitle": "Planning: Planning Grid - Documentation N1PTTAGE / N1PTRNO", "RefUrl": "/notes/912286 "}, {"RefNumber": "906756", "RefComponent": "XX-PART-ISHMED", "RefTitle": "Service Management: Synchronization Correction Appointment -", "RefUrl": "/notes/906756 "}, {"RefNumber": "892401", "RefComponent": "FS-CML-PO", "RefTitle": "STM: Termination of DART extraction on DB2 systems", "RefUrl": "/notes/892401 "}, {"RefNumber": "886984", "RefComponent": "PY-AT", "RefTitle": "Wichtige Korrekturen zu E-Card", "RefUrl": "/notes/886984 "}, {"RefNumber": "353255", "RefComponent": "PS-IS-LOG", "RefTitle": "New development - individual overviews", "RefUrl": "/notes/353255 "}, {"RefNumber": "839171", "RefComponent": "FS-CML-PO-DI", "RefTitle": "Payt: BAdI for cust-specific incidental cost creation/change", "RefUrl": "/notes/839171 "}, {"RefNumber": "890572", "RefComponent": "XX-PART-ISHMED", "RefTitle": "Clinical Order: Transport Order Types - Various Adjustments", "RefUrl": "/notes/890572 "}, {"RefNumber": "857506", "RefComponent": "BC-DB-SDB", "RefTitle": "Correction of STRING fields after EXPORT/IMPORT with R3load", "RefUrl": "/notes/857506 "}, {"RefNumber": "864784", "RefComponent": "IS-OIL-DS-TDP", "RefTitle": "IS-OIL TDP LEGAL CHANGES FRANCE - PREREQUISITE LICENCE", "RefUrl": "/notes/864784 "}, {"RefNumber": "881390", "RefComponent": "AP-PRC-PR", "RefTitle": "No prices or free goods found", "RefUrl": "/notes/881390 "}, {"RefNumber": "824473", "RefComponent": "PY-US-PS-BN", "RefTitle": "Savings Plan (403b): Customer Exit for Investments", "RefUrl": "/notes/824473 "}, {"RefNumber": "873326", "RefComponent": "PA-BN-CE", "RefTitle": "CE Enablement of Benefit Confirmation Form", "RefUrl": "/notes/873326 "}, {"RefNumber": "787665", "RefComponent": "IS-H", "RefTitle": "IS-H*MED transfer function master data cl. order", "RefUrl": "/notes/787665 "}, {"RefNumber": "866685", "RefComponent": "FS-AM-EP-ST", "RefTitle": "Creation of test report for Settlement for consistency check", "RefUrl": "/notes/866685 "}, {"RefNumber": "870940", "RefComponent": "FI-AA-IS", "RefTitle": "Missing SAP system variants for standard queries", "RefUrl": "/notes/870940 "}, {"RefNumber": "845320", "RefComponent": "PY-AT", "RefTitle": "Lohnkontenverordnung 2005 zum Kinderzuschlag v. 28.04.2005", "RefUrl": "/notes/845320 "}, {"RefNumber": "864234", "RefComponent": "XX-PART-ISHMED", "RefTitle": "Clinical Order: Enhancement and Documentation of BAdIs", "RefUrl": "/notes/864234 "}, {"RefNumber": "860908", "RefComponent": "XX-PART-ISHMED", "RefTitle": "Clin. Order: BAdI N1_CORDER_USER_COMM - Insufficient Docu", "RefUrl": "/notes/860908 "}, {"RefNumber": "846697", "RefComponent": "PY-JP", "RefTitle": "LC2005:<PERSON><PERSON><PERSON> at term. of Prntl Leave Vol.2 Implementation", "RefUrl": "/notes/846697 "}, {"RefNumber": "858371", "RefComponent": "XX-PART-ISHMED", "RefTitle": "i.s.h.med:16128 BAdI Quick Service Entry", "RefUrl": "/notes/858371 "}, {"RefNumber": "853134", "RefComponent": "PY-US-TR", "RefTitle": "Tax Reporter legal changes for 2nd Quarter 2005.", "RefUrl": "/notes/853134 "}, {"RefNumber": "520745", "RefComponent": "CRM-BF-CFG", "RefTitle": "External CATServer: CRM 3.0 SP09 additional transport", "RefUrl": "/notes/520745 "}, {"RefNumber": "843402", "RefComponent": "MM-PUR", "RefTitle": "CONNE_IMPORT_CONVERSION_ERROR", "RefUrl": "/notes/843402 "}, {"RefNumber": "856110", "RefComponent": "PY-FI", "RefTitle": "HFILTALEL0 - use percentage stored in  V_T7FI02.", "RefUrl": "/notes/856110 "}, {"RefNumber": "752440", "RefComponent": "XX-CSC-BR", "RefTitle": "Overview about changes for ISS, PIS, COFINS; CSSL, IR, WHT", "RefUrl": "/notes/752440 "}, {"RefNumber": "821434", "RefComponent": "PY-FI", "RefTitle": "VAKEY parameters deleted", "RefUrl": "/notes/821434 "}, {"RefNumber": "796169", "RefComponent": "XX-CSC-IN-SD", "RefTitle": "Transaction J2IUN CENVAT Utilization with Service Tax Credit", "RefUrl": "/notes/796169 "}, {"RefNumber": "850954", "RefComponent": "XX-CSC-CH-IS-H", "RefTitle": "IS-H CH: MEDIDATA 4.0 - neue Version", "RefUrl": "/notes/850954 "}, {"RefNumber": "847797", "RefComponent": "XX-PART-ISHMED", "RefTitle": "Clinical Order: Pregnancy Indicator", "RefUrl": "/notes/847797 "}, {"RefNumber": "658391", "RefComponent": "RE-IT", "RefTitle": "§ 15a Purchases Tax Law - new amendment in Germany", "RefUrl": "/notes/658391 "}, {"RefNumber": "777106", "RefComponent": "PY-MY", "RefTitle": "Minimum Normal Deemed Tax due to Other Payment", "RefUrl": "/notes/777106 "}, {"RefNumber": "699792", "RefComponent": "FS-CML-AC-RPM-IAS", "RefTitle": "IPD: Document display in FEBAN", "RefUrl": "/notes/699792 "}, {"RefNumber": "824630", "RefComponent": "XX-CSC-CH-IS-H", "RefTitle": "IS-H CH: Rechnungsdruck verhindern wenn EDI", "RefUrl": "/notes/824630 "}, {"RefNumber": "445988", "RefComponent": "CRM", "RefTitle": "Time stamps are not converted into date + time", "RefUrl": "/notes/445988 "}, {"RefNumber": "839054", "RefComponent": "XX-PART-ISHMED", "RefTitle": "Clin.Order/Prereg.: Migration - Context Data Correction", "RefUrl": "/notes/839054 "}, {"RefNumber": "836813", "RefComponent": "XX-CSC-AT-IS-H", "RefTitle": "IS-H AT: Intensivdaten - Online-Dialog", "RefUrl": "/notes/836813 "}, {"RefNumber": "578961", "RefComponent": "XX-CSC-AR", "RefTitle": "AR: Credit Invoice - Transports and Correction Instructions", "RefUrl": "/notes/578961 "}, {"RefNumber": "824299", "RefComponent": "BW-WHM-DST-ARC", "RefTitle": "NLS Prototype: Low performance of StorHouse data load", "RefUrl": "/notes/824299 "}, {"RefNumber": "831538", "RefComponent": "PY-BR", "RefTitle": "IMG text missing in release 4.6C for object PAY_BR_AU020", "RefUrl": "/notes/831538 "}, {"RefNumber": "361271", "RefComponent": "CO-PC-PCP", "RefTitle": "Archiving product costing: archive info system", "RefUrl": "/notes/361271 "}, {"RefNumber": "824539", "RefComponent": "FS-AM-CM", "RefTitle": "Account Management (AM) - TRBK 3.0 - BAFin §24c KWG", "RefUrl": "/notes/824539 "}, {"RefNumber": "811311", "RefComponent": "PY-PT", "RefTitle": "HR-PT: Annual Income Declaration - Electronic file", "RefUrl": "/notes/811311 "}, {"RefNumber": "721081", "RefComponent": "PY-PT", "RefTitle": "HR-PT:Legal Changes to the Social Security number(11 digits)", "RefUrl": "/notes/721081 "}, {"RefNumber": "822871", "RefComponent": "FI-TV-COS-PS", "RefTitle": "PM-SAP: Auslieferung Erweiterung TRV Q1/2005", "RefUrl": "/notes/822871 "}, {"RefNumber": "826402", "RefComponent": "XX-CSC-AT-IS-H", "RefTitle": "IS-H AT: Änderungen an Bearbeitung der Intensivdaten", "RefUrl": "/notes/826402 "}, {"RefNumber": "827099", "RefComponent": "PY-PT", "RefTitle": "HR-PT: Leave Annual Summary - Compensation not displayed", "RefUrl": "/notes/827099 "}, {"RefNumber": "825920", "RefComponent": "FI-TV", "RefTitle": "Workflow WS01000087 does not exist or is incorrect", "RefUrl": "/notes/825920 "}, {"RefNumber": "822882", "RefComponent": "XX-PART-ISHMED", "RefTitle": "Accident Data: Create Movement - Acc. Data not Transferred", "RefUrl": "/notes/822882 "}, {"RefNumber": "818019", "RefComponent": "XX-CSC-AT-IS-H", "RefTitle": "IS-H AT:Scoring:RNWAT_SCO_MSG_LOAD-neues Feld \"akzeptierbar\"", "RefUrl": "/notes/818019 "}, {"RefNumber": "823641", "RefComponent": "XX-PART-ISHMED", "RefTitle": "Preregistration, Clinical Order: Create Surgery Preregistrat", "RefUrl": "/notes/823641 "}, {"RefNumber": "801667", "RefComponent": "FIN-FSCM-TRM-TM", "RefTitle": "SEC: Missing legacy data transfer for EA-FINSERV 2.00", "RefUrl": "/notes/801667 "}, {"RefNumber": "731753", "RefComponent": "PY-PT", "RefTitle": "HR-PT: Article 220º \"Efeitos da suspensao do contrato ...\"", "RefUrl": "/notes/731753 "}, {"RefNumber": "813531", "RefComponent": "PY-BR", "RefTitle": "RAIS: Legal change 2005", "RefUrl": "/notes/813531 "}, {"RefNumber": "750279", "RefComponent": "FI-AP-AP-Q1", "RefTitle": "Withholding tax reporting legal changes for Philippines", "RefUrl": "/notes/750279 "}, {"RefNumber": "813671", "RefComponent": "IS-M-AMC", "RefTitle": "Special Customizing The Washington Post from SAP AM2-465", "RefUrl": "/notes/813671 "}, {"RefNumber": "200576", "RefComponent": "BW-BCT-FI", "RefTitle": "FI-AP/AR line item extraction with delta update", "RefUrl": "/notes/200576 "}, {"RefNumber": "811120", "RefComponent": "PY-US-TR", "RefTitle": "Form 940: NY rates for calculating credits", "RefUrl": "/notes/811120 "}, {"RefNumber": "808187", "RefComponent": "XX-PART-ISHMED", "RefTitle": "IS-H*MED: <PERSON><PERSON>. Order: Error Message in Transaction St11", "RefUrl": "/notes/808187 "}, {"RefNumber": "808266", "RefComponent": "XX-PART-ISHMED-ORD", "RefTitle": "IS-H/IS-H*MED: Clinical order: Order type - update termination", "RefUrl": "/notes/808266 "}, {"RefNumber": "785878", "RefComponent": "PY-TH", "RefTitle": "Pension Fund, Long Term Equity Fund, Charity for Education", "RefUrl": "/notes/785878 "}, {"RefNumber": "781711", "RefComponent": "BC-MOB", "RefTitle": "Uploading the Mobile Component Descriptor ISDFPS 1b SP2", "RefUrl": "/notes/781711 "}, {"RefNumber": "779702", "RefComponent": "BC-MOB-MI-SER-ABA", "RefTitle": "Required Customizing on the MI Server for ISDFPS SP2", "RefUrl": "/notes/779702 "}, {"RefNumber": "801104", "RefComponent": "XX-PART-ISHMED", "RefTitle": "IS-H/IS-H*MED: Clinical Order/Prereg.: Correction Report", "RefUrl": "/notes/801104 "}, {"RefNumber": "791743", "RefComponent": "XX-CSC-CH-IS-H", "RefTitle": "IS-H CH: ELACH/MEDIDATA - Mehrwertsteuersätze", "RefUrl": "/notes/791743 "}, {"RefNumber": "724511", "RefComponent": "XX-CSC-FR", "RefTitle": "French Central Bank Report C80", "RefUrl": "/notes/724511 "}, {"RefNumber": "785863", "RefComponent": "FI-TV-COS-PS", "RefTitle": "PM-SAP: Auslieferung Erweiterung TRV Q3/2004", "RefUrl": "/notes/785863 "}, {"RefNumber": "788591", "RefComponent": "XX-PART-ISHMED", "RefTitle": "IS-H*MED:Clin Order: N1SCRM Memory Effect -Correction Report", "RefUrl": "/notes/788591 "}, {"RefNumber": "781265", "RefComponent": "XX-PART-ISHMED", "RefTitle": "IS-H*MED: Service Management: Quick Service Entry", "RefUrl": "/notes/781265 "}, {"RefNumber": "726282", "RefComponent": "LE-DSD", "RefTitle": "Incorrect screen sequences in material master of DSD Backend", "RefUrl": "/notes/726282 "}, {"RefNumber": "787916", "RefComponent": "XX-PART-ISHMED", "RefTitle": "IS-H*MED: Radiology: Call Radiology Work Station", "RefUrl": "/notes/787916 "}, {"RefNumber": "696846", "RefComponent": "PM-EQM-EQ", "RefTitle": "PMAA: Recording time segments in the asset", "RefUrl": "/notes/696846 "}, {"RefNumber": "772616", "RefComponent": "MM-IS-IC", "RefTitle": "BCO Idoc INVCON02", "RefUrl": "/notes/772616 "}, {"RefNumber": "760919", "RefComponent": "PY-MY", "RefTitle": "Socso Borang 8A & 8B Reports", "RefUrl": "/notes/760919 "}, {"RefNumber": "615597", "RefComponent": "PSM-FM-BCS", "RefTitle": "Adding Report Writer functionality to BCS", "RefUrl": "/notes/615597 "}, {"RefNumber": "779726", "RefComponent": "BC-SEC-USR", "RefTitle": "Authorization object for local user administration", "RefUrl": "/notes/779726 "}, {"RefNumber": "701392", "RefComponent": "PY-US-TR", "RefTitle": "W-2: New MMREF-1 layout for Port Huron, MI", "RefUrl": "/notes/701392 "}, {"RefNumber": "385850", "RefComponent": "XX-CSC-KR", "RefTitle": "New VAT reports for South Korea", "RefUrl": "/notes/385850 "}, {"RefNumber": "612965", "RefComponent": "PM-EQM-SF-MPC", "RefTitle": "RMP(EA200): Retrofit reference measuring points", "RefUrl": "/notes/612965 "}, {"RefNumber": "758054", "RefComponent": "IS-HER-CM-GB", "RefTitle": "UCAS import - Reprocessing logic changes", "RefUrl": "/notes/758054 "}, {"RefNumber": "91615", "RefComponent": "EC-PCA-TL-ARC", "RefTitle": "Archiving/profit center: new archiving programs", "RefUrl": "/notes/91615 "}, {"RefNumber": "385484", "RefComponent": "XX-CSC-CZ-IS-U", "RefTitle": "Tax code for Statist. procedure of Budget billing", "RefUrl": "/notes/385484 "}, {"RefNumber": "669402", "RefComponent": "CA-TS", "RefTitle": "Generic usability issues in Transaction CAT2", "RefUrl": "/notes/669402 "}, {"RefNumber": "622567", "RefComponent": "CA-ESS", "RefTitle": "Generic usability issues in ESS Scenarios", "RefUrl": "/notes/622567 "}, {"RefNumber": "770195", "RefComponent": "PY-SG", "RefTitle": "Missing specifications for Wage type Evaluation class 11", "RefUrl": "/notes/770195 "}, {"RefNumber": "574872", "RefComponent": "BC-UPG-NA", "RefTitle": "Cannot disassemble data file with version", "RefUrl": "/notes/574872 "}, {"RefNumber": "768257", "RefComponent": "XX-CSC-CH-IS-H", "RefTitle": "IS-H CH: MEDIDATA - Alternativen Leistungscode übermitteln", "RefUrl": "/notes/768257 "}, {"RefNumber": "762639", "RefComponent": "IS-H", "RefTitle": "IS-H*MED: plan. board: plan appointmt - movemt for appointmt", "RefUrl": "/notes/762639 "}, {"RefNumber": "756058", "RefComponent": "PA-PA-BR", "RefTitle": "Views of tables T7BR93 and T7BR94 did not allow update", "RefUrl": "/notes/756058 "}, {"RefNumber": "100609", "RefComponent": "FI-GL-IS", "RefTitle": "Audit Information System (AIS) - installation", "RefUrl": "/notes/100609 "}, {"RefNumber": "753315", "RefComponent": "XX-PART-ISHMED", "RefTitle": "IS-H*MED: Authorization for Planning Authority", "RefUrl": "/notes/753315 "}, {"RefNumber": "760161", "RefComponent": "IS-H", "RefTitle": "IS-H/IS-H*MED: Clinical order - correction report context", "RefUrl": "/notes/760161 "}, {"RefNumber": "755527", "RefComponent": "XX-PART-ISHMED", "RefTitle": "IS-H*MED: Correction Report for Order Types", "RefUrl": "/notes/755527 "}, {"RefNumber": "758213", "RefComponent": "IS-H", "RefTitle": "IS-H/IS-H*MED: Clinical order - detailed print", "RefUrl": "/notes/758213 "}, {"RefNumber": "702367", "RefComponent": "XX-CSC-CH-IS-H", "RefTitle": "IS-H CH: Rechnungsformular TARMED", "RefUrl": "/notes/702367 "}, {"RefNumber": "732660", "RefComponent": "XX-CSC-CH-IS-H", "RefTitle": "IS-H CH: Rechung Version 4.0 4.63B/19", "RefUrl": "/notes/732660 "}, {"RefNumber": "712368", "RefComponent": "PY-PT", "RefTitle": "HR-PT: New table T5P1P for IRS since 2004", "RefUrl": "/notes/712368 "}, {"RefNumber": "757121", "RefComponent": "XX-PART-ISHMED", "RefTitle": "IS-H*MED:<PERSON><PERSON>.Order: Surg. Admission Order Item Correction", "RefUrl": "/notes/757121 "}, {"RefNumber": "748486", "RefComponent": "XX-PART-ISHMED", "RefTitle": "IS-H*MED: Day-Based Planning: Appointment OU Missing", "RefUrl": "/notes/748486 "}, {"RefNumber": "735426", "RefComponent": "MM-IM-ED", "RefTitle": "Incorrect screen sequences in mat master of excise duty", "RefUrl": "/notes/735426 "}, {"RefNumber": "751753", "RefComponent": "XX-PART-ISHMED", "RefTitle": "IS-H*MED: Correction Reports after Migration Execution", "RefUrl": "/notes/751753 "}, {"RefNumber": "748933", "RefComponent": "XX-PART-ISHMED", "RefTitle": "IS-H/IS-H*MED: Clin. Order: Invalid Order Initiator/Filler", "RefUrl": "/notes/748933 "}, {"RefNumber": "750185", "RefComponent": "FI-TV-COS-PS", "RefTitle": "PM-SAP: Auslieferung Erweiterung TRV Q2/2004", "RefUrl": "/notes/750185 "}, {"RefNumber": "736945", "RefComponent": "PY-TH", "RefTitle": "HTHCSSD1 - Code for the prefix in download format", "RefUrl": "/notes/736945 "}, {"RefNumber": "744693", "RefComponent": "PY-SG", "RefTitle": "Incorrect rounding of compulsory CPF for employee", "RefUrl": "/notes/744693 "}, {"RefNumber": "742650", "RefComponent": "XX-PART-ISHMED", "RefTitle": "IS-H*MED: <PERSON><PERSON>. Order: Correction Report Tables N1FATTR*", "RefUrl": "/notes/742650 "}, {"RefNumber": "656281", "RefComponent": "CO-OM-OPA-B", "RefTitle": "No branch from structure planning into planning of SKF", "RefUrl": "/notes/656281 "}, {"RefNumber": "733917", "RefComponent": "XX-PART-ISHMED", "RefTitle": "IS-H*MED: Planning Grid: Appointments/Tab Pages not Displaye", "RefUrl": "/notes/733917 "}, {"RefNumber": "384378", "RefComponent": "XX-CSC-CZ-IS-U", "RefTitle": "Surcharge for violation of cos phi", "RefUrl": "/notes/384378 "}, {"RefNumber": "726625", "RefComponent": "PY-MY", "RefTitle": "Socso Borang 8A & 8B Reports - New Format for year 2004", "RefUrl": "/notes/726625 "}, {"RefNumber": "735508", "RefComponent": "XX-CSC-CH-IS-H", "RefTitle": "IS-H CH: Leistungsregel C18 - Umschlüsselung \"in sich\"", "RefUrl": "/notes/735508 "}, {"RefNumber": "602415", "RefComponent": "PY-JP", "RefTitle": "LC2003 : Gross remuneration system vol.3 correction files", "RefUrl": "/notes/602415 "}, {"RefNumber": "734636", "RefComponent": "XX-CSC-CH-IS-H", "RefTitle": "IS-H CH: Rechnungsformular V4.0 - NIF-Nummer", "RefUrl": "/notes/734636 "}, {"RefNumber": "314701", "RefComponent": "BC-SRV-MAP", "RefTitle": "Latest Outlook forms for SAP MAPI Service Provider", "RefUrl": "/notes/314701 "}, {"RefNumber": "730535", "RefComponent": "PY-MY", "RefTitle": "CP22A Tax Report - New Format for year 2004", "RefUrl": "/notes/730535 "}, {"RefNumber": "731503", "RefComponent": "XX-CSC-CH-IS-H", "RefTitle": "IS-H CH: Summe TARMED AL in der Rechnung TARMED", "RefUrl": "/notes/731503 "}, {"RefNumber": "731755", "RefComponent": "XX-CSC-CH-IS-H", "RefTitle": "IS-H CH: Summe TARMED AL in der Rechnung Version 4.0", "RefUrl": "/notes/731755 "}, {"RefNumber": "730328", "RefComponent": "XX-CSC-CH-IS-H", "RefTitle": "IS-H CH: Int. Skalierungsfaktor Rechnung TARMED", "RefUrl": "/notes/730328 "}, {"RefNumber": "730327", "RefComponent": "XX-CSC-CH-IS-H", "RefTitle": "IS-H CH: Int. Skalierungsfaktor Rechnung Version 4.0", "RefUrl": "/notes/730327 "}, {"RefNumber": "727072", "RefComponent": "PY-MY", "RefTitle": "IC No. display in SOCSO reports", "RefUrl": "/notes/727072 "}, {"RefNumber": "717747", "RefComponent": "XX-CSC-CH-IS-H", "RefTitle": "IS-H CH: Aufschlüsselung Gesamtbetrag und 5-Rappenrundung", "RefUrl": "/notes/717747 "}, {"RefNumber": "722215", "RefComponent": "XX-CSC-CH-IS-H", "RefTitle": "IS-H CH: TARMED Rechnung mit filled Square", "RefUrl": "/notes/722215 "}, {"RefNumber": "710031", "RefComponent": "PA-ER", "RefTitle": "Date overviews: Individual characters displayed incorrectly", "RefUrl": "/notes/710031 "}, {"RefNumber": "712333", "RefComponent": "PY-PT", "RefTitle": "HR-PT: Upload of IRS tables 2004 into T5P1P", "RefUrl": "/notes/712333 "}, {"RefNumber": "619961", "RefComponent": "FS-AM-EP", "RefTitle": "Report settlement detail data and test utilities", "RefUrl": "/notes/619961 "}, {"RefNumber": "710689", "RefComponent": "XX-CSC-CH-IS-H", "RefTitle": "IS-H CH: BAPI-Leistungsschnittstelle: Fehlerhandling Vers. 2", "RefUrl": "/notes/710689 "}, {"RefNumber": "145850", "RefComponent": "IS-OIL-BC", "RefTitle": "Order of IS-OIL notes 4.0b on basis R/3 4.0B (SP)", "RefUrl": "/notes/145850 "}, {"RefNumber": "145854", "RefComponent": "IS-OIL-BC", "RefTitle": "Order of IS-OIL notes 4.0b on basis R/3 4.0B (LCP)", "RefUrl": "/notes/145854 "}, {"RefNumber": "718103", "RefComponent": "XX-CSC-CH-IS-H", "RefTitle": "CH: Erweiterungen für Assistenzärzte und Prozente", "RefUrl": "/notes/718103 "}, {"RefNumber": "717565", "RefComponent": "XX-CSC-CH-IS-H", "RefTitle": "IS-H CH: Leistungsregeln - Anpassen an akt.Entwicklungsstand", "RefUrl": "/notes/717565 "}, {"RefNumber": "713020", "RefComponent": "PA-PA-MY", "RefTitle": "BAPIS Personal data: field GBPAS filled incorrectly", "RefUrl": "/notes/713020 "}, {"RefNumber": "707168", "RefComponent": "FS-AM-PR-CD", "RefTitle": "Reference interest rates: Authorization object is missing", "RefUrl": "/notes/707168 "}, {"RefNumber": "713355", "RefComponent": "PY-AT", "RefTitle": "MV-Übertragungsbetrag - Eintragung auf Lohnzettel", "RefUrl": "/notes/713355 "}, {"RefNumber": "696417", "RefComponent": "IS-H-CM-OUT", "RefTitle": "IS-H §21:Neuerungen bei RNAP21K01/F01 zu 4.63B/18 u. 4.71/10", "RefUrl": "/notes/696417 "}, {"RefNumber": "368100", "RefComponent": "XX-CSC-US-PP", "RefTitle": "Price Protection Component - PPC - Corrections and Transport", "RefUrl": "/notes/368100 "}, {"RefNumber": "692628", "RefComponent": "XX-PART-ISHMED", "RefTitle": "IS-H*MED: Planning Grid - Plan Appointments for Midnight (00", "RefUrl": "/notes/692628 "}, {"RefNumber": "708384", "RefComponent": "PY-AT", "RefTitle": "Nachtrag für RPCL16A2 zum Jahreswechsel 2003/04", "RefUrl": "/notes/708384 "}, {"RefNumber": "708330", "RefComponent": "XX-CSC-CH-IS-H", "RefTitle": "ISH CH Leistungsregel Abrechenbarkeit Materialien", "RefUrl": "/notes/708330 "}, {"RefNumber": "707539", "RefComponent": "PY-AT", "RefTitle": "Neue Dienstgeberkontonummern Finanz ausgegeben -> Anpassung", "RefUrl": "/notes/707539 "}, {"RefNumber": "709906", "RefComponent": "PY-SG", "RefTitle": "New IR8A-Appendix 8B form for 2004", "RefUrl": "/notes/709906 "}, {"RefNumber": "708877", "RefComponent": "PY-SG", "RefTitle": "Change in Forms IR8A/8E and IR8S for year 2004", "RefUrl": "/notes/708877 "}, {"RefNumber": "671362", "RefComponent": "PY-BR", "RefTitle": "HBRRTER1: Legal change for printing differences", "RefUrl": "/notes/671362 "}, {"RefNumber": "709148", "RefComponent": "PY-BR", "RefTitle": "HBRPAYR0: Does not print the results in background", "RefUrl": "/notes/709148 "}, {"RefNumber": "698677", "RefComponent": "PY-AT", "RefTitle": "Neue Dienstgeberkontonummern SV ausgegeben -> Anpassungen", "RefUrl": "/notes/698677 "}, {"RefNumber": "702496", "RefComponent": "PY-AT", "RefTitle": "SV- Beitragsgruppen: Customizing unvollständig", "RefUrl": "/notes/702496 "}, {"RefNumber": "706262", "RefComponent": "XX-CSC-CH-IS-H", "RefTitle": "IS-H CH: ELACH - Medidata-Rechnungs-Antwort", "RefUrl": "/notes/706262 "}, {"RefNumber": "704587", "RefComponent": "XX-CSC-CH-IS-H", "RefTitle": "IS-H CH: ELACH - Medidata-Rechnungs-Antwort", "RefUrl": "/notes/704587 "}, {"RefNumber": "689238", "RefComponent": "PY-US-TR", "RefTitle": "NY 4th QTR SAPscript: Incorrect amount in Line #12", "RefUrl": "/notes/689238 "}, {"RefNumber": "678364", "RefComponent": "IS-U-BI", "RefTitle": "BRE (Backlog Reduction Engine) on IS-U/CCS 4.64", "RefUrl": "/notes/678364 "}, {"RefNumber": "687491", "RefComponent": "PY-PT", "RefTitle": "HR-PT: Corrections to generic statistical (RPCGSRP0)", "RefUrl": "/notes/687491 "}, {"RefNumber": "700160", "RefComponent": "XX-CSC-CH-IS-H", "RefTitle": "IS-H CH: Rechnungsformular TARMED", "RefUrl": "/notes/700160 "}, {"RefNumber": "695686", "RefComponent": "PSM-FM-IS", "RefTitle": "Drilldown reporting: Error reading cluster table COIX ...", "RefUrl": "/notes/695686 "}, {"RefNumber": "697600", "RefComponent": "PY-SG", "RefTitle": "PY-SG:Incorrect alignment of IR8A/IR8S forms for year 2003", "RefUrl": "/notes/697600 "}, {"RefNumber": "699131", "RefComponent": "XX-CSC-CH-IS-H", "RefTitle": "IS-H CH: Rechnungsformular TARMED", "RefUrl": "/notes/699131 "}, {"RefNumber": "689549", "RefComponent": "PY-SG", "RefTitle": "PY-SG:New limit for additional wages w.e.f. 01.01.2004", "RefUrl": "/notes/689549 "}, {"RefNumber": "681090", "RefComponent": "FI-TV-PL", "RefTitle": "IATA locations not current", "RefUrl": "/notes/681090 "}, {"RefNumber": "311513", "RefComponent": "FI-TV-PL", "RefTitle": "Train station tables are not current", "RefUrl": "/notes/311513 "}, {"RefNumber": "692178", "RefComponent": "PY-NO", "RefTitle": "New forms for 2003", "RefUrl": "/notes/692178 "}, {"RefNumber": "692596", "RefComponent": "XX-CSC-AT-IS-H", "RefTitle": "AT: Erfassung von Intensivdaten für Datensatz Intensiv", "RefUrl": "/notes/692596 "}, {"RefNumber": "672949", "RefComponent": "PY-PT", "RefTitle": "HR-PT: Legal changes in Personnel Summary", "RefUrl": "/notes/672949 "}, {"RefNumber": "687704", "RefComponent": "XX-CSC-CH-IS-H", "RefTitle": "IS-H CH: Rechnungsformular TARMED", "RefUrl": "/notes/687704 "}, {"RefNumber": "682286", "RefComponent": "PY-PT", "RefTitle": "HR-PT: Overtime Record - RPCOVRP0", "RefUrl": "/notes/682286 "}, {"RefNumber": "688054", "RefComponent": "PY-PT", "RefTitle": "HR-PT: Overtime Communication - UNION", "RefUrl": "/notes/688054 "}, {"RefNumber": "691711", "RefComponent": "XX-CSC-CH-IS-H", "RefTitle": "IS-H CH: ELACH - Medidata-Anpassungen", "RefUrl": "/notes/691711 "}, {"RefNumber": "689598", "RefComponent": "PY-AT", "RefTitle": "ELDA-Lohnzettel Finanz: Feld FANRL für Finanzämter 01 - 09", "RefUrl": "/notes/689598 "}, {"RefNumber": "647710", "RefComponent": "SCM-APO-ATP-BF-PAC", "RefTitle": "Internal document flow in APO", "RefUrl": "/notes/647710 "}, {"RefNumber": "683090", "RefComponent": "IS-H", "RefTitle": "Erweiterung TNFPSE ab 1.1.2004 - Regeln FP/SE", "RefUrl": "/notes/683090 "}, {"RefNumber": "663952", "RefComponent": "PY-US-TR", "RefTitle": "US Tax Reporter - YE 2003 Phase II", "RefUrl": "/notes/663952 "}, {"RefNumber": "628624", "RefComponent": "SCM-APO-MD-TL", "RefTitle": "TL-TRANS: Problems with BUS11201 and BUS10009 BAPI objects", "RefUrl": "/notes/628624 "}, {"RefNumber": "512463", "RefComponent": "CRM-MD-PCT", "RefTitle": "CRM product catalog - Individual replication", "RefUrl": "/notes/512463 "}, {"RefNumber": "673550", "RefComponent": "CRM-IC", "RefTitle": "Error in CL_CRM_IC_TEXTEDIT", "RefUrl": "/notes/673550 "}, {"RefNumber": "650326", "RefComponent": "PY-PT", "RefTitle": "HR-PT: Complete company/subareas data in printed version", "RefUrl": "/notes/650326 "}, {"RefNumber": "657549", "RefComponent": "PY-PT", "RefTitle": "HR-PT: TemSe functionality for RPSSESP0", "RefUrl": "/notes/657549 "}, {"RefNumber": "677661", "RefComponent": "XX-CSC-CH-IS-H", "RefTitle": "IS-H CH: (+)%-Zuschlagsleistungen (Bastarde)", "RefUrl": "/notes/677661 "}, {"RefNumber": "676589", "RefComponent": "XX-CSC-CH-IS-H", "RefTitle": "IS-H CH: XML-Leistungsschnittstelle", "RefUrl": "/notes/676589 "}, {"RefNumber": "672692", "RefComponent": "PA-ER", "RefTitle": "Status reason missing in workflow container for activities", "RefUrl": "/notes/672692 "}, {"RefNumber": "673207", "RefComponent": "XX-PART-ISHMED", "RefTitle": "IS-H/IS-H*MED: Priority of Prereg. Types Ignored", "RefUrl": "/notes/673207 "}, {"RefNumber": "459052", "RefComponent": "FI-AP-AP-B1", "RefTitle": "RFFOAT_A: Modif.of report for foreign payment trans.Austria", "RefUrl": "/notes/459052 "}, {"RefNumber": "666915", "RefComponent": "XX-CSC-CH-IS-H", "RefTitle": "IS-H CH: RNWCHNTPK00 - <PERSON><PERSON>. \"Code Lim. Maxregel\"", "RefUrl": "/notes/666915 "}, {"RefNumber": "453322", "RefComponent": "CRM-BTX-ANA", "RefTitle": "Activity Monitor: Standard variants do not exist", "RefUrl": "/notes/453322 "}, {"RefNumber": "457029", "RefComponent": "XX-PROJ-GSC-EVC", "RefTitle": "EVC Release 1: Documentation", "RefUrl": "/notes/457029 "}, {"RefNumber": "113711", "RefComponent": "XX-CSC-MX", "RefTitle": "FA ISR Depreciation Adjust. & IMPAC Balance Average", "RefUrl": "/notes/113711 "}, {"RefNumber": "616463", "RefComponent": "BW-BEX-ET-WEB", "RefTitle": "Inconsistent 0IMODE_CHART_LIB and 0PIE_CHART_LIB templates", "RefUrl": "/notes/616463 "}, {"RefNumber": "662341", "RefComponent": "CO-OM-OPA-B", "RefTitle": "Incorrect detail planning layouts w/ Funds Management", "RefUrl": "/notes/662341 "}, {"RefNumber": "132640", "RefComponent": "FI-FM-AF-AR", "RefTitle": "Archiving for Funds Management (FCABP, FMSU)", "RefUrl": "/notes/132640 "}, {"RefNumber": "659488", "RefComponent": "PA-ER", "RefTitle": "Follow-up of Support Package SAPK-200P1INERECRUIT", "RefUrl": "/notes/659488 "}, {"RefNumber": "31621", "RefComponent": "BC-BMT-OM", "RefTitle": "PD and workflow application do not run correctly", "RefUrl": "/notes/31621 "}, {"RefNumber": "656437", "RefComponent": "CRM-BF-SVY", "RefTitle": "CRM Survey Tool: Unicode ability in CRM 3.1", "RefUrl": "/notes/656437 "}, {"RefNumber": "649978", "RefComponent": "PY-PT", "RefTitle": "HR-PT: Legal changes in Structured Employment Survey report", "RefUrl": "/notes/649978 "}, {"RefNumber": "556669", "RefComponent": "BC-BW", "RefTitle": "DSAA transport object", "RefUrl": "/notes/556669 "}, {"RefNumber": "647579", "RefComponent": "XX-PART-ISHMED-ORD", "RefTitle": "IS-H*MED: Messages for closed OU", "RefUrl": "/notes/647579 "}, {"RefNumber": "444641", "RefComponent": "SCM-APO-INT-SLS", "RefTitle": "Correction of incorrect sales order requirements with APO", "RefUrl": "/notes/444641 "}, {"RefNumber": "643192", "RefComponent": "XX-CSC-CH-IS-H", "RefTitle": "IS-H CH: PATREC-Statistik - Verschluesselungssoftware", "RefUrl": "/notes/643192 "}, {"RefNumber": "646474", "RefComponent": "IS-H", "RefTitle": "IS-H: DE: Customizingänderungen zu 4.63B/13", "RefUrl": "/notes/646474 "}, {"RefNumber": "643843", "RefComponent": "CRM-ANA-EXT", "RefTitle": "Adv. Correction for Note 639072 (Parallel Processing)", "RefUrl": "/notes/643843 "}, {"RefNumber": "620283", "RefComponent": "PY-PT", "RefTitle": "HR-PT: New tax report RPCTAXP0", "RefUrl": "/notes/620283 "}, {"RefNumber": "641507", "RefComponent": "SD-SLS-GF-RE", "RefTitle": "Display variants: Incorrect display of crcy & qty fields", "RefUrl": "/notes/641507 "}, {"RefNumber": "640046", "RefComponent": "XX-PART-ISHMED", "RefTitle": "IS-H*MED: Planning Grid Call - Short Dump", "RefUrl": "/notes/640046 "}, {"RefNumber": "638170", "RefComponent": "XX-CSC-AT-IS-H", "RefTitle": "IS-H AT: Korrekturreport für Leistungen ohne NLLZ => LR A3", "RefUrl": "/notes/638170 "}, {"RefNumber": "632906", "RefComponent": "XX-PART-ISHMED", "RefTitle": "IS-H*MED: Incorrect Planning Object", "RefUrl": "/notes/632906 "}, {"RefNumber": "634981", "RefComponent": "XX-CSC-BR", "RefTitle": "Migration of Tax Entries To Conditions in Background", "RefUrl": "/notes/634981 "}, {"RefNumber": "323030", "RefComponent": "FI-FM-AF-AR", "RefTitle": "Funds Management archiving (4.5A-4.6C)", "RefUrl": "/notes/323030 "}, {"RefNumber": "632713", "RefComponent": "XX-CSC-CH-IS-H", "RefTitle": "IS-H CH: RNWCHNTPK00 - Einspielen TARMED-Leistungsstamm", "RefUrl": "/notes/632713 "}, {"RefNumber": "547156", "RefComponent": "PSM-FG", "RefTitle": "Advance delivery of Report SF-224 and Enhancement of BL", "RefUrl": "/notes/547156 "}, {"RefNumber": "551944", "RefComponent": "PSM-FG", "RefTitle": "Corrections to advance delivery of report SF-224", "RefUrl": "/notes/551944 "}, {"RefNumber": "629828", "RefComponent": "XX-PART-ISHMED", "RefTitle": "IS-H*MED: Support of Hebra<PERSON> Fonts", "RefUrl": "/notes/629828 "}, {"RefNumber": "548897", "RefComponent": "PY-BR", "RefTitle": "HRMS-BR Vacation Provision", "RefUrl": "/notes/548897 "}, {"RefNumber": "410945", "RefComponent": "XX-CSC-CZ-IS-U", "RefTitle": "Dunning for incoming payment methods", "RefUrl": "/notes/410945 "}, {"RefNumber": "624788", "RefComponent": "XX-PART-ISHMED", "RefTitle": "IS-H*MED: Planning Grid - More Than 100 Appts", "RefUrl": "/notes/624788 "}, {"RefNumber": "618885", "RefComponent": "PSM-FG-BL", "RefTitle": "Correction for note 606673", "RefUrl": "/notes/618885 "}, {"RefNumber": "610189", "RefComponent": "CA-GTF-TS-PPM", "RefTitle": "Advanced Support: Tools and utility programs", "RefUrl": "/notes/610189 "}, {"RefNumber": "600229", "RefComponent": "PY-DE-PS-ZV", "RefTitle": "ZV-Vorabtransport: DATÜV-ZVE, ATZ, Nettozusage,...", "RefUrl": "/notes/600229 "}, {"RefNumber": "542913", "RefComponent": "FI-AP-AP-Q1", "RefTitle": "RFKQST00 - Collective note for error in withholding tax rep.", "RefUrl": "/notes/542913 "}, {"RefNumber": "607234", "RefComponent": "PY-PT", "RefTitle": "HR-PT: Corrections to Leave annual summary (RPLESUP0)", "RefUrl": "/notes/607234 "}, {"RefNumber": "606673", "RefComponent": "PSM-FG-BL", "RefTitle": "Transport of Colombian Budgetary Ledger Functionality", "RefUrl": "/notes/606673 "}, {"RefNumber": "601765", "RefComponent": "FI-SL-IS-A", "RefTitle": "Configuration of report/report interface incomplete", "RefUrl": "/notes/601765 "}, {"RefNumber": "604784", "RefComponent": "IS-H-IS-GMS", "RefTitle": "IS-H DE: KHStatV 2002 - Vorabauslieferung", "RefUrl": "/notes/604784 "}, {"RefNumber": "602168", "RefComponent": "PY-AT", "RefTitle": "Neu: Report RPCL16A2 für Lohnzettel Finanz überarbeitet", "RefUrl": "/notes/602168 "}, {"RefNumber": "604962", "RefComponent": "IS-H-CM-OUT", "RefTitle": "IS-H DE: P21 - Unterstützung individueller Datenermittlung", "RefUrl": "/notes/604962 "}, {"RefNumber": "584058", "RefComponent": "RE-RT-SC", "RefTitle": "SCS according to Czech law", "RefUrl": "/notes/584058 "}, {"RefNumber": "163527", "RefComponent": "FI-AP-AP-Q1", "RefTitle": "Withholding Tax for USA 1099-MISC (Version 2001)", "RefUrl": "/notes/163527 "}, {"RefNumber": "586506", "RefComponent": "FI-AP-AP-Q1", "RefTitle": "1099-MISC: SAPScript form F_RFW1099M_2001 inconsistent", "RefUrl": "/notes/586506 "}, {"RefNumber": "191821", "RefComponent": "TR-CB", "RefTitle": "Cash Budget Management in Release 4.6", "RefUrl": "/notes/191821 "}, {"RefNumber": "585090", "RefComponent": "PSM-FM-BU-BF", "RefTitle": "Table entries for validation in FM", "RefUrl": "/notes/585090 "}, {"RefNumber": "576295", "RefComponent": "BW-WHM-DST", "RefTitle": "P9:P29:Content transfer:InfoAreas:memory problems", "RefUrl": "/notes/576295 "}, {"RefNumber": "581432", "RefComponent": "IS-H", "RefTitle": "IS-H AT: Scoring - Änderungen und Korrekturen", "RefUrl": "/notes/581432 "}, {"RefNumber": "167058", "RefComponent": "XX-CSC-XX", "RefTitle": "Costs Brazil - Model of Inventory Register Query", "RefUrl": "/notes/167058 "}, {"RefNumber": "459520", "RefComponent": "FI-AP-AP-Q1", "RefTitle": "1099-MISC report RFW1099M for USA", "RefUrl": "/notes/459520 "}, {"RefNumber": "488627", "RefComponent": "BC-UPG-OCS", "RefTitle": "Test import has failed for SAPKE46C36", "RefUrl": "/notes/488627 "}, {"RefNumber": "572282", "RefComponent": "PY-MX", "RefTitle": "Sapserv for note 561686, Retrocalculation for Mexico", "RefUrl": "/notes/572282 "}, {"RefNumber": "560807", "RefComponent": "PSM-FG", "RefTitle": "Corrections to advanced delivery of report SF-224", "RefUrl": "/notes/560807 "}, {"RefNumber": "557033", "RefComponent": "PSM-FG", "RefTitle": "Advance delivery of FACTS I/II reports with 2002 requirments", "RefUrl": "/notes/557033 "}, {"RefNumber": "518061", "RefComponent": "FI-AP-AP-Q1", "RefTitle": "Modelo 770 year 2002 (Italy)", "RefUrl": "/notes/518061 "}, {"RefNumber": "428368", "RefComponent": "FI-AP-AP-D", "RefTitle": "RFEPOS00: Syntax error during upgrade to Release 4.6", "RefUrl": "/notes/428368 "}, {"RefNumber": "397862", "RefComponent": "PSM-FG-BL", "RefTitle": "BL OFBL US federal government account derivation", "RefUrl": "/notes/397862 "}, {"RefNumber": "320555", "RefComponent": "EC-CS", "RefTitle": "Transaction data transport: Additional financial data", "RefUrl": "/notes/320555 "}, {"RefNumber": "187303", "RefComponent": "XX-CSC-US-CS", "RefTitle": "Contract Solution Component: Documentation", "RefUrl": "/notes/187303 "}, {"RefNumber": "454478", "RefComponent": "FI-AP-AP-Q1", "RefTitle": "New 1042 record format and form for year 2002", "RefUrl": "/notes/454478 "}, {"RefNumber": "190785", "RefComponent": "IS-M-SD-PS-SH-S", "RefTitle": "IS-M/SD: Standard delivery exception transport", "RefUrl": "/notes/190785 "}, {"RefNumber": "83076", "RefComponent": "AC-INT", "RefTitle": "MM_ACCTIT: Archiving programs for ACCTIT, ACCTHD, ACCTCR", "RefUrl": "/notes/83076 "}, {"RefNumber": "558714", "RefComponent": "FI-FM-IS", "RefTitle": "Error reading cluster table COIX for report &", "RefUrl": "/notes/558714 "}, {"RefNumber": "385675", "RefComponent": "XX-CSC-CZ-IS-U", "RefTitle": "Docu CZ/SK for doubtful receivables and write-offs", "RefUrl": "/notes/385675 "}, {"RefNumber": "514894", "RefComponent": "CRM-MD-CON-IF", "RefTitle": "Data type changes of MXWRT/GKWRT in table CDBC_P_CN_LIMI", "RefUrl": "/notes/514894 "}, {"RefNumber": "546624", "RefComponent": "CRM-BE-OC", "RefTitle": "Actions cannot be processed", "RefUrl": "/notes/546624 "}, {"RefNumber": "382719", "RefComponent": "FI-AP-AP-Q1", "RefTitle": "Form 190 to the Tax Authorities (Spain)", "RefUrl": "/notes/382719 "}, {"RefNumber": "166778", "RefComponent": "BW-BCT-SD", "RefTitle": "Current SD extractors for PI.1999 and higher", "RefUrl": "/notes/166778 "}, {"RefNumber": "550544", "RefComponent": "CA-EUR-CNV", "RefTitle": "IS-H AT: Euro changeover: Missing IS-H tables for AT and CH", "RefUrl": "/notes/550544 "}, {"RefNumber": "540025", "RefComponent": "SD-SLS", "RefTitle": "No agent determination during WF for credit memo request", "RefUrl": "/notes/540025 "}, {"RefNumber": "315377", "RefComponent": "BC-UPG-RDM", "RefTitle": "Additional information about upgrading to 3.1I SR1", "RefUrl": "/notes/315377 "}, {"RefNumber": "546417", "RefComponent": "PY-MX", "RefTitle": "sapserv for note note.0545905", "RefUrl": "/notes/546417 "}, {"RefNumber": "385589", "RefComponent": "FI-AA-IS", "RefTitle": "Revised SAP standard variants Release 4.6B and 4.6C", "RefUrl": "/notes/385589 "}, {"RefNumber": "545400", "RefComponent": "IS-A-RL", "RefTitle": "RL71F:RL_ACCSTA displays Quantity in wrong decimal format", "RefUrl": "/notes/545400 "}, {"RefNumber": "503202", "RefComponent": "CRM-MSA-BP", "RefTitle": "R/3 customer hierarchy not visible at the CRM mobile client", "RefUrl": "/notes/503202 "}, {"RefNumber": "171619", "RefComponent": "IS-B-DP-BD", "RefTitle": "Advance correction variable transaction on SAPSERV3", "RefUrl": "/notes/171619 "}, {"RefNumber": "388118", "RefComponent": "BC-DB-DBI", "RefTitle": "ST05 - Syntax error in 46A (APO System)", "RefUrl": "/notes/388118 "}, {"RefNumber": "417092", "RefComponent": "FI-AP-AP-Q1", "RefTitle": "RFKQST00 - Error in withholding tax report for Italy", "RefUrl": "/notes/417092 "}, {"RefNumber": "313817", "RefComponent": "FI-AP-AP-Q1", "RefTitle": "Withholding tax reporting revised: RFKQST00", "RefUrl": "/notes/313817 "}, {"RefNumber": "162534", "RefComponent": "BW-BCT-PP", "RefTitle": "PP/BW modifications in standard system for Rel 2.0", "RefUrl": "/notes/162534 "}, {"RefNumber": "316418", "RefComponent": "TR-TM-TM", "RefTitle": "Treasury datafeed: Exch.rates w/indirect quotation", "RefUrl": "/notes/316418 "}, {"RefNumber": "519068", "RefComponent": "SD-SLS", "RefTitle": "Workflow WS 20000009 and WS 20000019 do not exist", "RefUrl": "/notes/519068 "}, {"RefNumber": "535915", "RefComponent": "SCM-APO-CA-PER", "RefTitle": "PFM tool advance delivery", "RefUrl": "/notes/535915 "}, {"RefNumber": "526438", "RefComponent": "BW-BEX-ET-WEB-AD", "RefTitle": "Internal error with communication with the IGS", "RefUrl": "/notes/526438 "}, {"RefNumber": "402072", "RefComponent": "PSM-FM-UP-CM", "RefTitle": "Transaction FMNG: Missing Customizing for SN distribution", "RefUrl": "/notes/402072 "}, {"RefNumber": "135095", "RefComponent": "TR-CB", "RefTitle": "Auxiliary programs for Cash Budget Management", "RefUrl": "/notes/135095 "}, {"RefNumber": "308018", "RefComponent": "BC-SRV-FRM", "RefTitle": "SAPMAPI: WI displ in Japanese Windows installation", "RefUrl": "/notes/308018 "}, {"RefNumber": "169078", "RefComponent": "XX-PART-ISHMED", "RefTitle": "IS-H*MED: Plantafel mit vielen Spalten", "RefUrl": "/notes/169078 "}, {"RefNumber": "191042", "RefComponent": "LO-BM", "RefTitle": "AIM", "RefUrl": "/notes/191042 "}, {"RefNumber": "198935", "RefComponent": "SCM-APO-INT", "RefTitle": "Include FV45LF0M_MESSAGE_XVBFS_FETCH missng in PI99", "RefUrl": "/notes/198935 "}, {"RefNumber": "387391", "RefComponent": "LO-LIS-DC", "RefTitle": "Additional checks in LIS", "RefUrl": "/notes/387391 "}, {"RefNumber": "452635", "RefComponent": "IM-FA-IS", "RefTitle": "Problems when executing a report with selection variants", "RefUrl": "/notes/452635 "}, {"RefNumber": "333448", "RefComponent": "IS-B-RA", "RefTitle": "Risk analysis: Inexact currency conversion", "RefUrl": "/notes/333448 "}, {"RefNumber": "301319", "RefComponent": "PS-PRG-EVA", "RefTitle": "Check Report for Progress Analysis", "RefUrl": "/notes/301319 "}, {"RefNumber": "317716", "RefComponent": "XX-PROJ-RE-CRE", "RefTitle": "CRE: local currency changeover in CRE environment", "RefUrl": "/notes/317716 "}, {"RefNumber": "390658", "RefComponent": "FI-GL-GL-G", "RefTitle": "RFBNUM00: error message FR264", "RefUrl": "/notes/390658 "}, {"RefNumber": "137842", "RefComponent": "FI-BL-PT-BA", "RefTitle": "Automatic account statement Austria", "RefUrl": "/notes/137842 "}, {"RefNumber": "321286", "RefComponent": "MM-IS-IC", "RefTitle": "Collective note: Invent. Controlling Rel. 4.6C (April 2001)", "RefUrl": "/notes/321286 "}, {"RefNumber": "510859", "RefComponent": "FI-AP-AP-Q1", "RefTitle": "Missing or wrong messages of message class FR", "RefUrl": "/notes/510859 "}, {"RefNumber": "196981", "RefComponent": "XX-CSC-HU", "RefTitle": "Gesetzesänderung bei ungarischen Steuern", "RefUrl": "/notes/196981 "}, {"RefNumber": "301875", "RefComponent": "XX-CSC-AU-GST", "RefTitle": "RFIDAU10: Summary Invoice Report for GST (IV & LIV)", "RefUrl": "/notes/301875 "}, {"RefNumber": "447943", "RefComponent": "FI-FM-AF-AR", "RefTitle": "Archiving for Funds Management (FMICOIT)", "RefUrl": "/notes/447943 "}, {"RefNumber": "507605", "RefComponent": "FS-CD", "RefTitle": "Fed wire Customer Transport", "RefUrl": "/notes/507605 "}, {"RefNumber": "444187", "RefComponent": "FS-CD", "RefTitle": "BDT-Customizing after installation of INSURANCE Add-On", "RefUrl": "/notes/444187 "}, {"RefNumber": "498973", "RefComponent": "IS-H-CM-OUT", "RefTitle": "IS-H AT: ELDA - <PERSON><PERSON>en (vorgezogene Entwicklung)", "RefUrl": "/notes/498973 "}, {"RefNumber": "485137", "RefComponent": "IS-OIL-PRA-PRD", "RefTitle": "QCI Failure calling MP Vols for gas", "RefUrl": "/notes/485137 "}, {"RefNumber": "185203", "RefComponent": "BW-BCT-LO", "RefTitle": "Error in InfoStructure of type 'T'", "RefUrl": "/notes/185203 "}, {"RefNumber": "118393", "RefComponent": "EC-CS-CSF-H", "RefTitle": "EC-CS: P/L in inventory available for 4.0A/B", "RefUrl": "/notes/118393 "}, {"RefNumber": "375568", "RefComponent": "CA-CL-CHR", "RefTitle": "Characteristics maintenance: update redesign", "RefUrl": "/notes/375568 "}, {"RefNumber": "410791", "RefComponent": "XX-TRANSL-JA", "RefTitle": "HRDSYS: Transport of missing JA transl. for OPEC/FUNT/FUNC", "RefUrl": "/notes/410791 "}, {"RefNumber": "499485", "RefComponent": "PY-MX", "RefTitle": "SAPServe transport because of annual processes", "RefUrl": "/notes/499485 "}, {"RefNumber": "499100", "RefComponent": "PY-MX", "RefTitle": "sapserv for corrections of \"procesos anuales\"", "RefUrl": "/notes/499100 "}, {"RefNumber": "492716", "RefComponent": "SCM-APO-MD-TL", "RefTitle": "TL EXIT: User exit within the displacement calculation", "RefUrl": "/notes/492716 "}, {"RefNumber": "495090", "RefComponent": "FI-AA-IS", "RefTitle": "RAGITT_ALV01: Report variant SAP&001 incorrect", "RefUrl": "/notes/495090 "}, {"RefNumber": "385929", "RefComponent": "XX-CSC-BR-FI", "RefTitle": "DIRF -> File corrections 2000  (cont. Note 376013)", "RefUrl": "/notes/385929 "}, {"RefNumber": "322580", "RefComponent": "TR-CB-IS", "RefTitle": "Hierarchy in standard reports", "RefUrl": "/notes/322580 "}, {"RefNumber": "376013", "RefComponent": "XX-CSC-BR", "RefTitle": "ASUG 2000 Brazil / DIRF requirements for WT/tax reporting", "RefUrl": "/notes/376013 "}, {"RefNumber": "458910", "RefComponent": "FI-AP-AP-B1", "RefTitle": "DMEE: Changes to austrian format tree V3", "RefUrl": "/notes/458910 "}, {"RefNumber": "99821", "RefComponent": "FI-LC-LC", "RefTitle": "Archiving/consolidation: new archiving programs", "RefUrl": "/notes/99821 "}, {"RefNumber": "494255", "RefComponent": "PY-MX", "RefTitle": "sapserv for note 0492203", "RefUrl": "/notes/494255 "}, {"RefNumber": "484643", "RefComponent": "TR-TM-PO-SE", "RefTitle": "Security valuation with transaction figures", "RefUrl": "/notes/484643 "}, {"RefNumber": "90633", "RefComponent": "LO-LIS-DC", "RefTitle": "New units in LIS", "RefUrl": "/notes/90633 "}, {"RefNumber": "306859", "RefComponent": "XX-CSC-PT", "RefTitle": "Mapa Fiscal 32.2: New Layout", "RefUrl": "/notes/306859 "}, {"RefNumber": "451012", "RefComponent": "BC-BW", "RefTitle": "BW 1.2B: cannot activate rules for master data", "RefUrl": "/notes/451012 "}, {"RefNumber": "486096", "RefComponent": "BC-SEC-DIR", "RefTitle": "Consolidating DBOBJ and LDAPPROPx", "RefUrl": "/notes/486096 "}, {"RefNumber": "459410", "RefComponent": "IS-H", "RefTitle": "Erweiterung TNFPSE ab 1.1.2002 - Regeln FP/SE", "RefUrl": "/notes/459410 "}, {"RefNumber": "486617", "RefComponent": "PY-MX", "RefTitle": "sapserv for note 0483697", "RefUrl": "/notes/486617 "}, {"RefNumber": "181541", "RefComponent": "SD-IS-DC", "RefTitle": "SIS: Error/termination during update of S126", "RefUrl": "/notes/181541 "}, {"RefNumber": "460205", "RefComponent": "XX-PROJ-GSC-ECLI", "RefTitle": "CPCC Service Pack 2.0.50", "RefUrl": "/notes/460205 "}, {"RefNumber": "450479", "RefComponent": "CRM-BTX", "RefTitle": "Performance: Incorrect frequency of calls in CRMC_EVENT_CALL", "RefUrl": "/notes/450479 "}, {"RefNumber": "352089", "RefComponent": "CRM-MSA-CON", "RefTitle": "Contact person type only maintnd in English + German", "RefUrl": "/notes/352089 "}, {"RefNumber": "457721", "RefComponent": "TR-TM-AC-SE", "RefTitle": "§341b German Coml Code (HGB) security valuatn advance trnsp.", "RefUrl": "/notes/457721 "}, {"RefNumber": "203365", "RefComponent": "FI-GL-GL-F", "RefTitle": "BAS - Report for processing the GST in Australia", "RefUrl": "/notes/203365 "}, {"RefNumber": "455423", "RefComponent": "XX-PROJ-PM", "RefTitle": "§341b HGB Securities Valuation Advance correction", "RefUrl": "/notes/455423 "}, {"RefNumber": "63841", "RefComponent": "IS-HT-SW", "RefTitle": "Release Information of  R/3 Add-On IS-SW & IS-HT", "RefUrl": "/notes/63841 "}, {"RefNumber": "338308", "RefComponent": "MM-IS-IC-DTC", "RefTitle": "Stock segment changes", "RefUrl": "/notes/338308 "}, {"RefNumber": "91594", "RefComponent": "BC-UPG-RDM", "RefTitle": "Additional Info: Upgrading to Release 3.1I", "RefUrl": "/notes/91594 "}, {"RefNumber": "211472", "RefComponent": "FI-GL", "RefTitle": "RFAWVZ40: Performance", "RefUrl": "/notes/211472 "}, {"RefNumber": "452210", "RefComponent": "FIN-FSCM-TRM-TM", "RefTitle": "Data extraction for filling the Data Warehouse", "RefUrl": "/notes/452210 "}, {"RefNumber": "327941", "RefComponent": "MM-IS-IC-RPT", "RefTitle": "Authorization profile for document evaluations", "RefUrl": "/notes/327941 "}, {"RefNumber": "451134", "RefComponent": "IS-OIL-DS-TDP", "RefTitle": "Mass update of contract prices and external ED pricing key", "RefUrl": "/notes/451134 "}, {"RefNumber": "185756", "RefComponent": "MM-IS-IC-DTC", "RefTitle": "Quick correction of S032", "RefUrl": "/notes/185756 "}, {"RefNumber": "330563", "RefComponent": "MM-IS-IC", "RefTitle": "Composite note: INVCO Release 3.1I (September 2000)", "RefUrl": "/notes/330563 "}, {"RefNumber": "150437", "RefComponent": "LO-MD-BOM", "RefTitle": "ARCH: BOM archiving is available", "RefUrl": "/notes/150437 "}, {"RefNumber": "446363", "RefComponent": "CRM-BTX", "RefTitle": "Event Handler: Performance improvement", "RefUrl": "/notes/446363 "}, {"RefNumber": "329843", "RefComponent": "EC-CS-CSF-A", "RefTitle": "Development class /1SAP1/FC_FCG0 does not exist", "RefUrl": "/notes/329843 "}, {"RefNumber": "193505", "RefComponent": "TR-TM-TR-DE", "RefTitle": "GGD: Modules for generating variable Cashflows", "RefUrl": "/notes/193505 "}, {"RefNumber": "171098", "RefComponent": "BW-BCT-MM", "RefTitle": "BW/MM modifications in stnd system for Rel. BW 2.0B", "RefUrl": "/notes/171098 "}, {"RefNumber": "437554", "RefComponent": "IS-B-BCA-PT-CV", "RefTitle": "BTE currency conversion:limit rounding", "RefUrl": "/notes/437554 "}, {"RefNumber": "444241", "RefComponent": "IS-B-BCA-PT-CV", "RefTitle": "Currency conversion in BCA", "RefUrl": "/notes/444241 "}, {"RefNumber": "441965", "RefComponent": "XX-PROJ-CS-PP", "RefTitle": "Missing entries in control tables and FCODE table", "RefUrl": "/notes/441965 "}, {"RefNumber": "437013", "RefComponent": "PSM-FM-IS", "RefTitle": "Import and activate GASB 34 report painter report", "RefUrl": "/notes/437013 "}, {"RefNumber": "189765", "RefComponent": "CO-PA-ACT", "RefTitle": "Activity allocation: Cost component split in CO-PA", "RefUrl": "/notes/189765 "}, {"RefNumber": "376248", "RefComponent": "FI-AA-AA-C", "RefTitle": "No user fields for posting transactions", "RefUrl": "/notes/376248 "}, {"RefNumber": "428398", "RefComponent": "XX-PROJ-SDP-004-CRM", "RefTitle": "DIVA ICP1/2: Preliminary Transport III (07/2001)", "RefUrl": "/notes/428398 "}, {"RefNumber": "434096", "RefComponent": "IS-H-PA-SER", "RefTitle": "IS-H AT: Lregel <PERSON>herreihung, Dekadenleistung, Tagesklinik", "RefUrl": "/notes/434096 "}, {"RefNumber": "194271", "RefComponent": "XX-CSC-HU", "RefTitle": "Legal changes for Hungarian taxes", "RefUrl": "/notes/194271 "}, {"RefNumber": "322459", "RefComponent": "FI-AR-AR-G", "RefTitle": "RFICRC00: Various errors", "RefUrl": "/notes/322459 "}, {"RefNumber": "433876", "RefComponent": "IS-OIL-DS-MCO", "RefTitle": "Goods Recipient field is disabled in MIGO transaction", "RefUrl": "/notes/433876 "}, {"RefNumber": "302451", "RefComponent": "IS-OIL-BC", "RefTitle": "IS-OIL language installation on R/3 4.6B", "RefUrl": "/notes/302451 "}, {"RefNumber": "37165", "RefComponent": "PA-PA", "RefTitle": "Consult: Automatic vacation adjustment when leaving", "RefUrl": "/notes/37165 "}, {"RefNumber": "151522", "RefComponent": "XX-PROJ-CS", "RefTitle": "Convert company code currency: Cable Solution", "RefUrl": "/notes/151522 "}, {"RefNumber": "175378", "RefComponent": "TR-TM", "RefTitle": "Additional info for TR-TM customers Spain Hotp. 21", "RefUrl": "/notes/175378 "}, {"RefNumber": "150376", "RefComponent": "MM-IS-IC", "RefTitle": "Collective note BCO Release 4.0b to 4.6b (October 2000)", "RefUrl": "/notes/150376 "}, {"RefNumber": "397105", "RefComponent": "FIN-FSCM-TRM-TM-TR", "RefTitle": "Dump CALL_FUNCTION_NOT_FOUND for changing accrual/deferral", "RefUrl": "/notes/397105 "}, {"RefNumber": "363306", "RefComponent": "PY-XX-FO", "RefTitle": "HRDSYS: Missing translations of documentation", "RefUrl": "/notes/363306 "}, {"RefNumber": "406291", "RefComponent": "IS-OIL-DS-TDP", "RefTitle": "Pricing type 'T' in document copy control KNPRS", "RefUrl": "/notes/406291 "}, {"RefNumber": "417148", "RefComponent": "PSM-FG-IS", "RefTitle": "Reports for FACTS I not available in release 462", "RefUrl": "/notes/417148 "}, {"RefNumber": "395849", "RefComponent": "PY-IT", "RefTitle": "HR-IT: 770 2001-Overview", "RefUrl": "/notes/395849 "}, {"RefNumber": "396583", "RefComponent": "PS-IS-LOG", "RefTitle": "PSIS: Download for GRANEDA: missing files", "RefUrl": "/notes/396583 "}, {"RefNumber": "414254", "RefComponent": "XX-CSC-KR", "RefTitle": "Migration Tool for Korean Add-on Tax Invoices", "RefUrl": "/notes/414254 "}, {"RefNumber": "320387", "RefComponent": "PY-BR", "RefTitle": "Thirteenth salary server deliveries", "RefUrl": "/notes/320387 "}, {"RefNumber": "339946", "RefComponent": "IS-M", "RefTitle": "IS-M: Define BPs for Euro currency conversion", "RefUrl": "/notes/339946 "}, {"RefNumber": "303619", "RefComponent": "FI-AP-AP-Q1", "RefTitle": "Version of Modello 770 / 2000", "RefUrl": "/notes/303619 "}, {"RefNumber": "392177", "RefComponent": "XX-PROJ-SDP-004", "RefTitle": "DIVA ICP1/2: Preliminary Transport (03/2001)", "RefUrl": "/notes/392177 "}, {"RefNumber": "400112", "RefComponent": "XX-PROJ-SDP-004", "RefTitle": "DIVA ICP1/2: Preliminary Transport II (05/2001)", "RefUrl": "/notes/400112 "}, {"RefNumber": "104838", "RefComponent": "PP-IS", "RefTitle": "Collective note SFIS, Release 3.1I", "RefUrl": "/notes/104838 "}, {"RefNumber": "188202", "RefComponent": "PP-IS-DC", "RefTitle": "Date initial in S023", "RefUrl": "/notes/188202 "}, {"RefNumber": "386823", "RefComponent": "CRM-ISA", "RefTitle": "Web file changes ISA 2.0b Support Package 9", "RefUrl": "/notes/386823 "}, {"RefNumber": "159066", "RefComponent": "CRM-MW", "RefTitle": "CRM Middleware Notes Overview", "RefUrl": "/notes/159066 "}, {"RefNumber": "206742", "RefComponent": "PA-PA-BR", "RefTitle": "Legal Change RAIS Portaria 1740 year 1999", "RefUrl": "/notes/206742 "}, {"RefNumber": "407039", "RefComponent": "BW-BCT-PS", "RefTitle": "Error activating 0MLST_USE", "RefUrl": "/notes/407039 "}, {"RefNumber": "353686", "RefComponent": "PSM-FM", "RefTitle": "Prg term.:Diff no of parameters in FORM and PERFORM", "RefUrl": "/notes/353686 "}, {"RefNumber": "326039", "RefComponent": "SCM-APO-CA-COP", "RefTitle": "Installation of ITS-Services (APO: 2.0)", "RefUrl": "/notes/326039 "}, {"RefNumber": "193229", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Use cust.exit on KNA1 change pointer for updates to SCP OTWS", "RefUrl": "/notes/193229 "}, {"RefNumber": "310095", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "Wrong positioning in shpt scheduling - doc./compart. details", "RefUrl": "/notes/310095 "}, {"RefNumber": "137248", "RefComponent": "LO-LIS", "RefTitle": "Cross-application planning", "RefUrl": "/notes/137248 "}, {"RefNumber": "389290", "RefComponent": "BC", "RefTitle": "HP Somersault Enqueue Functionality on 45B", "RefUrl": "/notes/389290 "}, {"RefNumber": "403340", "RefComponent": "XX-CSC-US-PP", "RefTitle": "PPC Release 1: Documentation", "RefUrl": "/notes/403340 "}, {"RefNumber": "111299", "RefComponent": "PP-IS", "RefTitle": "Flexible analysis via Operations: too large values", "RefUrl": "/notes/111299 "}, {"RefNumber": "329589", "RefComponent": "SV-ASA-PRE-CPG", "RefTitle": "Missing Business Configuration Sets", "RefUrl": "/notes/329589 "}, {"RefNumber": "399436", "RefComponent": "IS-OIL-DS-TDP", "RefTitle": "IS-OIL TDP Sales excise duty revenue adjustment", "RefUrl": "/notes/399436 "}, {"RefNumber": "378165", "RefComponent": "PM-EQM-EQ", "RefTitle": "List output for vehicles", "RefUrl": "/notes/378165 "}, {"RefNumber": "394328", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "Wrong price determination for HPM materials at GR", "RefUrl": "/notes/394328 "}, {"RefNumber": "397983", "RefComponent": "PY-MX", "RefTitle": "sapserv for PTU corrections", "RefUrl": "/notes/397983 "}, {"RefNumber": "311330", "RefComponent": "BW-BCT-PP", "RefTitle": "Blank units fields in S281 and S282", "RefUrl": "/notes/311330 "}, {"RefNumber": "307925", "RefComponent": "BW-BCT-PP", "RefTitle": "Missing control table entries for S280...S285", "RefUrl": "/notes/307925 "}, {"RefNumber": "307032", "RefComponent": "BW-BCT-PP", "RefTitle": "No delta update for info structure S282", "RefUrl": "/notes/307032 "}, {"RefNumber": "332692", "RefComponent": "BW-BCT-MM", "RefTitle": "ABAP/4PERFORM_NOT_FOUND MCB1_505", "RefUrl": "/notes/332692 "}, {"RefNumber": "315012", "RefComponent": "BW-BCT-LO", "RefTitle": "Problems with user-defined fields", "RefUrl": "/notes/315012 "}, {"RefNumber": "211598", "RefComponent": "BW-BCT-PP", "RefTitle": "Miss.entries in logist.extrct strctres <PERSON><PERSON><PERSON>", "RefUrl": "/notes/211598 "}, {"RefNumber": "205629", "RefComponent": "BW-BCT-PP", "RefTitle": "Transfer info structures, conditions 002, 004, 005", "RefUrl": "/notes/205629 "}, {"RefNumber": "212057", "RefComponent": "BW-BCT-MM", "RefTitle": "Empty information structure S194", "RefUrl": "/notes/212057 "}, {"RefNumber": "203385", "RefComponent": "BW-BCT-LO", "RefTitle": "TMCEXACT no longer available in system", "RefUrl": "/notes/203385 "}, {"RefNumber": "166994", "RefComponent": "BW-BCT-PP", "RefTitle": "Empty provision of data S282/2LIS_04_S282", "RefUrl": "/notes/166994 "}, {"RefNumber": "366273", "RefComponent": "FS-CML-PO", "RefTitle": "RZH: Syntax error in function group FVD_BO_OL", "RefUrl": "/notes/366273 "}, {"RefNumber": "339682", "RefComponent": "MM-IS-IC-DTC", "RefTitle": "Quick correction S094", "RefUrl": "/notes/339682 "}, {"RefNumber": "310480", "RefComponent": "PP-IS-DC", "RefTitle": "MCF_VERSION_UPD_V2 and MCF_VERSION_UPD_D deleted", "RefUrl": "/notes/310480 "}, {"RefNumber": "145120", "RefComponent": "PP-IS-REP", "RefTitle": "Text display in flexible analysis", "RefUrl": "/notes/145120 "}, {"RefNumber": "146467", "RefComponent": "PP-IS-DC", "RefTitle": "Statistical setup repetitive manufacturing doc. log", "RefUrl": "/notes/146467 "}, {"RefNumber": "110275", "RefComponent": "PP-IS-DC", "RefTitle": "Units in S021 and S023 / update log", "RefUrl": "/notes/110275 "}, {"RefNumber": "104558", "RefComponent": "PP-IS", "RefTitle": "Collective note PPIS, Release 3.1H", "RefUrl": "/notes/104558 "}, {"RefNumber": "395392", "RefComponent": "PY-MX", "RefTitle": "sapserv for credito al salario", "RefUrl": "/notes/395392 "}, {"RefNumber": "393262", "RefComponent": "PT-SP", "RefTitle": "ABAP runtime error DYNPRO_NOT_FOUND (SAPFH5AH)", "RefUrl": "/notes/393262 "}, {"RefNumber": "390550", "RefComponent": "BC-UPG", "RefTitle": "ABAP Dictionary objects missing after upgrade", "RefUrl": "/notes/390550 "}, {"RefNumber": "387123", "RefComponent": "FIN-FSCM-TRM-TM-TR", "RefTitle": "ZAN: Dump CALL_FUNCTION_NOT_FOUND w/ interest rate adjustmt", "RefUrl": "/notes/387123 "}, {"RefNumber": "114566", "RefComponent": "CA-EUR-CNV-FI", "RefTitle": "EMU: MQ530, MQ519 sec. index entries are missing", "RefUrl": "/notes/114566 "}, {"RefNumber": "375515", "RefComponent": "CRM-MW-SRV", "RefTitle": "Keygen fix for 2.0C SP02", "RefUrl": "/notes/375515 "}, {"RefNumber": "181099", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Collective changes to shipment inbound 40B", "RefUrl": "/notes/181099 "}, {"RefNumber": "181061", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Inconsistent data and status update in TPI interfa", "RefUrl": "/notes/181061 "}, {"RefNumber": "181008", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Error in Shipment Create abends OILSHI01 processing", "RefUrl": "/notes/181008 "}, {"RefNumber": "180696", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Justification of storage object field", "RefUrl": "/notes/180696 "}, {"RefNumber": "180418", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Collective changes distributed after Hot Pack nov 99", "RefUrl": "/notes/180418 "}, {"RefNumber": "179319", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Wrong data in shipment tpi screens", "RefUrl": "/notes/179319 "}, {"RefNumber": "179143", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Wrong tank check on order entry", "RefUrl": "/notes/179143 "}, {"RefNumber": "177563", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Save tank for delivery on shipment inbound", "RefUrl": "/notes/177563 "}, {"RefNumber": "177556", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "change communication structure for delete IDoc processing", "RefUrl": "/notes/177556 "}, {"RefNumber": "176218", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Handling type overwritten by incoming shipment IDoc", "RefUrl": "/notes/176218 "}, {"RefNumber": "176214", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "SOE GRP from IDOC OILSHI01 does not override default values", "RefUrl": "/notes/176214 "}, {"RefNumber": "175917", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Wrong quantity used for ASTM range check", "RefUrl": "/notes/175917 "}, {"RefNumber": "175840", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Segments E1OILSI E1OILVH too short", "RefUrl": "/notes/175840 "}, {"RefNumber": "175777", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Changes to order distribution", "RefUrl": "/notes/175777 "}, {"RefNumber": "175646", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Wrong quantity used for ASTM range check", "RefUrl": "/notes/175646 "}, {"RefNumber": "174710", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "shipment inbound, change plant", "RefUrl": "/notes/174710 "}, {"RefNumber": "174462", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "TPI: Filter objects not possible", "RefUrl": "/notes/174462 "}, {"RefNumber": "200706", "RefComponent": "IS-OIL-DS-BDRP", "RefTitle": "GUI status OIIPBLCT missing", "RefUrl": "/notes/200706 "}, {"RefNumber": "192014", "RefComponent": "IS-OIL-DS-BDRP", "RefTitle": "Error OC709 occurs on TPI OTWS entry", "RefUrl": "/notes/192014 "}, {"RefNumber": "191432", "RefComponent": "IS-OIL-DS-BDRP", "RefTitle": "Tank ID assignment at order change incorrect", "RefUrl": "/notes/191432 "}, {"RefNumber": "177825", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Order distribution action code", "RefUrl": "/notes/177825 "}, {"RefNumber": "180759", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "OILTPI01 Missing E1OILPS segment", "RefUrl": "/notes/180759 "}, {"RefNumber": "191401", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Action code in OILTPI50 populated incorrectly", "RefUrl": "/notes/191401 "}, {"RefNumber": "375038", "RefComponent": "CRM-MW-CCO", "RefTitle": "Infoagents SP2 release (r3 side functionality)", "RefUrl": "/notes/375038 "}, {"RefNumber": "380185", "RefComponent": "IS-H", "RefTitle": "IS-H AT: Transport Korrekturen Scoring 2001", "RefUrl": "/notes/380185 "}, {"RefNumber": "373712", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "Shipment Cost Worklist: user interface improved", "RefUrl": "/notes/373712 "}, {"RefNumber": "373081", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "Dspl.deliv. in Doc.Selection ROIGSD01: VL03/VL33 outdated", "RefUrl": "/notes/373081 "}, {"RefNumber": "366560", "RefComponent": "FI-AP-AP-Q1", "RefTitle": "Update of RFKQSU30, 1099MISC reporting for US", "RefUrl": "/notes/366560 "}, {"RefNumber": "121265", "RefComponent": "PY-US", "RefTitle": "Payroll Outsourcing on SAPSERV3/4", "RefUrl": "/notes/121265 "}, {"RefNumber": "376471", "RefComponent": "IS-H-PA", "RefTitle": "IS-H: Aktualisierte Version TNFPSE zum 1.1.2001", "RefUrl": "/notes/376471 "}, {"RefNumber": "99056", "RefComponent": "XX-CSC-CO", "RefTitle": "Legal tax reports for Colombia.", "RefUrl": "/notes/99056 "}, {"RefNumber": "92989", "RefComponent": "SD-BF", "RefTitle": "Archiving: new functions RV_LIKP", "RefUrl": "/notes/92989 "}, {"RefNumber": "327820", "RefComponent": "SCM-APO-SNP-BF", "RefTitle": "Transports missing from system tables for SP1-SP4", "RefUrl": "/notes/327820 "}, {"RefNumber": "364943", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "Wrong documents checked in doc.selection report ROIGSD01", "RefUrl": "/notes/364943 "}, {"RefNumber": "148765", "RefComponent": "LO-BM", "RefTitle": "Sample source code f.internal batch no. allocation", "RefUrl": "/notes/148765 "}, {"RefNumber": "364197", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "Missing Customizing Transaction O5AW for Shpm.Mass.Proc.TD", "RefUrl": "/notes/364197 "}, {"RefNumber": "216236", "RefComponent": "BW-BCT-SD", "RefTitle": "Selection parameters for data upload from S260 - S264", "RefUrl": "/notes/216236 "}, {"RefNumber": "334111", "RefComponent": "PM", "RefTitle": "Archiving PM-relevant data; Sapserv for 4.0B", "RefUrl": "/notes/334111 "}, {"RefNumber": "338497", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "O4B1 Mass Processing: rc=1 on conf.schedule w/o user exit", "RefUrl": "/notes/338497 "}, {"RefNumber": "350016", "RefComponent": "BW-BCT-SD", "RefTitle": "Missing extraction program for S264", "RefUrl": "/notes/350016 "}, {"RefNumber": "333128", "RefComponent": "XX-PROJ-CS-SD", "RefTitle": "SD:Order - redetermining def.vals in partner change", "RefUrl": "/notes/333128 "}, {"RefNumber": "86037", "RefComponent": "MM-IS-IC", "RefTitle": "Collective note on doc.evaluations, Stock/Reqs", "RefUrl": "/notes/86037 "}, {"RefNumber": "333696", "RefComponent": "PM-EQM-SF-MPC", "RefTitle": "Transport der Meßwerterfassungsliste auf Rel. 40.x", "RefUrl": "/notes/333696 "}, {"RefNumber": "203004", "RefComponent": "IS-M-AM-ST-A", "RefTitle": "M/AM: RJHXPRAI - restart capable version", "RefUrl": "/notes/203004 "}, {"RefNumber": "174073", "RefComponent": "BC-CUS", "RefTitle": "Missing release notes after upgrade", "RefUrl": "/notes/174073 "}, {"RefNumber": "332993", "RefComponent": "IS-OIL-DS-MAP", "RefTitle": "Formula Price calculation in relation to quantity", "RefUrl": "/notes/332993 "}, {"RefNumber": "161415", "RefComponent": "XX-CSC-CO", "RefTitle": "Multiple withholding tax invoice - BAVARIA Colombia", "RefUrl": "/notes/161415 "}, {"RefNumber": "161414", "RefComponent": "XX-CSC-CO", "RefTitle": "Localization : Multiple withholding tax cancel invoice docum", "RefUrl": "/notes/161414 "}, {"RefNumber": "161409", "RefComponent": "XX-CSC-CO", "RefTitle": "Localization : Multiple withholding tax invoice verification", "RefUrl": "/notes/161409 "}, {"RefNumber": "161405", "RefComponent": "XX-CSC-CO", "RefTitle": "Localization : Multiple withholding tax at invoice", "RefUrl": "/notes/161405 "}, {"RefNumber": "131702", "RefComponent": "BW-BCT-SD", "RefTitle": "Current SD extractors for BW Release 1.2B", "RefUrl": "/notes/131702 "}, {"RefNumber": "202877", "RefComponent": "PM-EQM-SF-MPC", "RefTitle": "Trnsprt of measurmnt reading entry list to Rel 40.x", "RefUrl": "/notes/202877 "}, {"RefNumber": "309642", "RefComponent": "PY-AU", "RefTitle": "Tax Thresholds & Limits for 2000/01 Tax Year (PAYG).", "RefUrl": "/notes/309642 "}, {"RefNumber": "177717", "RefComponent": "XX-PROJ-CS-NF", "RefTitle": "Termntn sales document: PERFORM_PARAMETER_TOO_SHORT", "RefUrl": "/notes/177717 "}, {"RefNumber": "210435", "RefComponent": "RE-CP", "RefTitle": "Transport of text modules in foreign lang.from 4.6C", "RefUrl": "/notes/210435 "}, {"RefNumber": "313901", "RefComponent": "PSM-FM", "RefTitle": "IS-PS Sondertransport für SH/HH QNEK000004", "RefUrl": "/notes/313901 "}, {"RefNumber": "310788", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "No provision to extend Idoc OILDVA01 Driver/Veh", "RefUrl": "/notes/310788 "}, {"RefNumber": "191806", "RefComponent": "XX-CSC-KR", "RefTitle": "CRT for 30FKV/HP79: Tax jurisdiction code on Downpayment", "RefUrl": "/notes/191806 "}, {"RefNumber": "167098", "RefComponent": "FI-AA-IS", "RefTitle": "Missing variants for queries", "RefUrl": "/notes/167098 "}, {"RefNumber": "308868", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Tank ID error on SD order items after hot package", "RefUrl": "/notes/308868 "}, {"RefNumber": "306559", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Inconsistent date used in order item create", "RefUrl": "/notes/306559 "}, {"RefNumber": "306761", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "No warning for multiple Driver Vehicle Assignments", "RefUrl": "/notes/306761 "}, {"RefNumber": "210066", "RefComponent": "BW-BCT-EC-CS", "RefTitle": "Hierarchies for 0CS_GROUP not displayed", "RefUrl": "/notes/210066 "}, {"RefNumber": "195172", "RefComponent": "TR-TM-PO-SE", "RefTitle": "DEP: Inconsistency after securities act transfer", "RefUrl": "/notes/195172 "}, {"RefNumber": "306446", "RefComponent": "XX-CSC", "RefTitle": "Dictionary Definitions(Tables) for Modello 770 missing", "RefUrl": "/notes/306446 "}, {"RefNumber": "216880", "RefComponent": "SRM-EBP-INB", "RefTitle": "BBP Inbox:Rej./Appr. at line level does not work", "RefUrl": "/notes/216880 "}, {"RefNumber": "300242", "RefComponent": "CA-CL", "RefTitle": "Sytx err. in rpt SAPLBTOC: OUTBOUND_CALL_00004050_E", "RefUrl": "/notes/300242 "}, {"RefNumber": "302538", "RefComponent": "TR-TM-PO-SE", "RefTitle": "Internal: Spanish localization for Insurance", "RefUrl": "/notes/302538 "}, {"RefNumber": "301021", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Inconsistent date used in order item create", "RefUrl": "/notes/301021 "}, {"RefNumber": "301385", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "Population of ship-to in TAS shipment IDoc", "RefUrl": "/notes/301385 "}, {"RefNumber": "303220", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Tank ID defaulting in orders incorrect on change", "RefUrl": "/notes/303220 "}, {"RefNumber": "165619", "RefComponent": "FI-AP-AP-Q", "RefTitle": "Correction of 770 module for fiscal year 1998", "RefUrl": "/notes/165619 "}, {"RefNumber": "170677", "RefComponent": "FI-AP-AP-Q", "RefTitle": "2nd correction of 770 module for fiscal year 1998", "RefUrl": "/notes/170677 "}, {"RefNumber": "301014", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Incorrect TPP check in IDOC_OUTPUT_OILTPI50", "RefUrl": "/notes/301014 "}, {"RefNumber": "198627", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Incorrect shipping point in O4PO", "RefUrl": "/notes/198627 "}, {"RefNumber": "300109", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "No warning for multiple Driver Vehicle Assignments", "RefUrl": "/notes/300109 "}, {"RefNumber": "216067", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Additional customer exits", "RefUrl": "/notes/216067 "}, {"RefNumber": "216249", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Status messages not returned to TPI", "RefUrl": "/notes/216249 "}, {"RefNumber": "189642", "RefComponent": "IS-H-PA", "RefTitle": "IS-H:Cont. flat rate per/pro.surch. on basis ICD-10", "RefUrl": "/notes/189642 "}, {"RefNumber": "213870", "RefComponent": "XX-PROJ-PM", "RefTitle": "Hauswährungsumstellung Vorablösung", "RefUrl": "/notes/213870 "}, {"RefNumber": "187395", "RefComponent": "XX-PROJ-IHC", "RefTitle": "In House Cash: Current Transports", "RefUrl": "/notes/187395 "}, {"RefNumber": "206235", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Syntax error in function group RRX2", "RefUrl": "/notes/206235 "}, {"RefNumber": "203403", "RefComponent": "XX-PROJ-IHC", "RefTitle": "Inhouse Cash: Current transport for PAYRQ structure", "RefUrl": "/notes/203403 "}, {"RefNumber": "190447", "RefComponent": "XX-IDES", "RefTitle": "IDES 4.0B turn of the year 1999-2000", "RefUrl": "/notes/190447 "}, {"RefNumber": "208587", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "No warning for multiple Driver Vehicle Assignments", "RefUrl": "/notes/208587 "}, {"RefNumber": "144572", "RefComponent": "PS-IS-LOG", "RefTitle": "Project Information System: Error in new fields", "RefUrl": "/notes/144572 "}, {"RefNumber": "148464", "RefComponent": "PY-NO", "RefTitle": "PY-NO: Changes in Norwegian tax reporting 1999", "RefUrl": "/notes/148464 "}, {"RefNumber": "198465", "RefComponent": "CRM-MW", "RefTitle": "CRM 1.2 SP4: performance problems with sendbits", "RefUrl": "/notes/198465 "}, {"RefNumber": "197351", "RefComponent": "QM-PT-RP-PRC", "RefTitle": "Certificate processing in the MIGO", "RefUrl": "/notes/197351 "}, {"RefNumber": "201913", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Function call conflict in OIK_TPI_ORDERS_CREATE", "RefUrl": "/notes/201913 "}, {"RefNumber": "180435", "RefComponent": "XX-PROJ-CEM", "RefTitle": "P3G CEM V3.0A corrections / f. LCP/HP status 26", "RefUrl": "/notes/180435 "}, {"RefNumber": "88895", "RefComponent": "SD-BF", "RefTitle": "Archiving: New functions SD_VBAK", "RefUrl": "/notes/88895 "}, {"RefNumber": "199541", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Endless loop in IDOC_OUTPUT_OILTPI50", "RefUrl": "/notes/199541 "}, {"RefNumber": "200316", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Error in incompletion for mandatory Tank ID", "RefUrl": "/notes/200316 "}, {"RefNumber": "196075", "RefComponent": "IS-OIL-DS", "RefTitle": "Oil-specific messages missing in class VF", "RefUrl": "/notes/196075 "}, {"RefNumber": "197182", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Quantity missing from OILSHI01 if CPLID not '1'", "RefUrl": "/notes/197182 "}, {"RefNumber": "80279", "RefComponent": "IS-OIL-BC", "RefTitle": "Order of IS-OIL notes 2.0c/1 & 1.0c/1 on R/3 3.0D/2", "RefUrl": "/notes/80279 "}, {"RefNumber": "193023", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Handling of ETAs for planned shipments", "RefUrl": "/notes/193023 "}, {"RefNumber": "196052", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Error OC605 occurs incorrectly", "RefUrl": "/notes/196052 "}, {"RefNumber": "194327", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Incorrect OIK37 updates via TPI", "RefUrl": "/notes/194327 "}, {"RefNumber": "195707", "RefComponent": "IS-OIL-DS", "RefTitle": "Slow response to delivery create", "RefUrl": "/notes/195707 "}, {"RefNumber": "190425", "RefComponent": "BW-SYS", "RefTitle": "Advance correction for BW 2.0A, patch 1", "RefUrl": "/notes/190425 "}, {"RefNumber": "193231", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "ROIKFCL1 functionality missing after Service Pack", "RefUrl": "/notes/193231 "}, {"RefNumber": "93715", "RefComponent": "SD-BF", "RefTitle": "Archiving: new functions SD_VBRK", "RefUrl": "/notes/93715 "}, {"RefNumber": "193227", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "Abort A222 in OIK_TAS_DATA_POST", "RefUrl": "/notes/193227 "}, {"RefNumber": "192273", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Collected changes for TPI", "RefUrl": "/notes/192273 "}, {"RefNumber": "191696", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "OILSHI01 IDoc keeps status 64 after processing", "RefUrl": "/notes/191696 "}, {"RefNumber": "192046", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "No KNA1 change pointer for updates to SCP OTWS", "RefUrl": "/notes/192046 "}, {"RefNumber": "191151", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Missing TPI functionality", "RefUrl": "/notes/191151 "}, {"RefNumber": "191429", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "PBL OTWS entries missing from TPI Location IDoc", "RefUrl": "/notes/191429 "}, {"RefNumber": "191154", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "TPI IDocs fail with mixed user defaults", "RefUrl": "/notes/191154 "}, {"RefNumber": "191149", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Abort A222 in OIK_TAS_DATA_POST", "RefUrl": "/notes/191149 "}, {"RefNumber": "190248", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Generic price reference plant valuation", "RefUrl": "/notes/190248 "}, {"RefNumber": "186151", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Shipment inbound process update task", "RefUrl": "/notes/186151 "}, {"RefNumber": "185620", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Incorrect UOM conversion in ROIKPALE", "RefUrl": "/notes/185620 "}, {"RefNumber": "181435", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Override of storage SOE failes on shipment inbound", "RefUrl": "/notes/181435 "}, {"RefNumber": "185653", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "OILTPI01 Missing E1OILPS segment II (31H)", "RefUrl": "/notes/185653 "}, {"RefNumber": "185685", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Bad performance/Short dump for ROIAMMA3", "RefUrl": "/notes/185685 "}, {"RefNumber": "185617", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Error OC709 raised on change of OTWS entry", "RefUrl": "/notes/185617 "}, {"RefNumber": "181239", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Tank ID not stored in OIK37 for Oil TPI", "RefUrl": "/notes/181239 "}, {"RefNumber": "185453", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "OIGD Customer Area data cannot be saved on change", "RefUrl": "/notes/185453 "}, {"RefNumber": "167142", "RefComponent": "BW-BCT", "RefTitle": "Transaction LBW1", "RefUrl": "/notes/167142 "}, {"RefNumber": "178423", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Changes delivered with CRT", "RefUrl": "/notes/178423 "}, {"RefNumber": "185010", "RefComponent": "IS-OIL-DS", "RefTitle": "MM_EKKO: archiving run excludes contracts", "RefUrl": "/notes/185010 "}, {"RefNumber": "182178", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "Shipment Load ID assigned with Valid-from > Valid-to", "RefUrl": "/notes/182178 "}, {"RefNumber": "115881", "RefComponent": "BW-BCT", "RefTitle": "BW extraction customer hierarchy missing in R/3 4.5", "RefUrl": "/notes/115881 "}, {"RefNumber": "120529", "RefComponent": "IS-R-IFC-IN", "RefTitle": "Downgrade SA editor from release 4.6A to 4.0B", "RefUrl": "/notes/120529 "}, {"RefNumber": "182859", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Override of storage SOE failes on shipment inbound", "RefUrl": "/notes/182859 "}, {"RefNumber": "173969", "RefComponent": "PSM-FM-BU", "RefTitle": "RFFFMCPYI1 - Include not found", "RefUrl": "/notes/173969 "}, {"RefNumber": "30318", "RefComponent": "SD", "RefTitle": "Valuation in SD documents / TCURR", "RefUrl": "/notes/30318 "}, {"RefNumber": "128835", "RefComponent": "XX-SER-Y2000", "RefTitle": "Error when importing Safety Check 2000", "RefUrl": "/notes/128835 "}, {"RefNumber": "180401", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Collective changes to shipment inbound 31H", "RefUrl": "/notes/180401 "}, {"RefNumber": "130538", "RefComponent": "SCM-APO-INT", "RefTitle": "Integration APO <--> R/3, purchasing (ATP)", "RefUrl": "/notes/130538 "}, {"RefNumber": "179151", "RefComponent": "IS-OIL-DS-TPI", "RefTitle": "Delivery creation uses system date", "RefUrl": "/notes/179151 "}, {"RefNumber": "173464", "RefComponent": "SD-IS", "RefTitle": "SIS variant configuration: Update of S126 for VC", "RefUrl": "/notes/173464 "}, {"RefNumber": "100475", "RefComponent": "CA-OIW", "RefTitle": "IMG documentation for Open information Warehouse", "RefUrl": "/notes/100475 "}, {"RefNumber": "178164", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "Screen 200 missing in program", "RefUrl": "/notes/178164 "}, {"RefNumber": "153004", "RefComponent": "XX-PROJ-CS", "RefTitle": "CS user exit prod.order before length calc. call", "RefUrl": "/notes/153004 "}, {"RefNumber": "165205", "RefComponent": "IS-OIL", "RefTitle": "IS-OIL Y2000 core changes relevant for 3.0D basis", "RefUrl": "/notes/165205 "}, {"RefNumber": "167916", "RefComponent": "XX-CSC-US", "RefTitle": "CSC Release 1: Return Deletion Not Updating Contract", "RefUrl": "/notes/167916 "}, {"RefNumber": "170684", "RefComponent": "FI-AA", "RefTitle": "IMG FI-AA: Different view clusters are missing", "RefUrl": "/notes/170684 "}, {"RefNumber": "170259", "RefComponent": "QM-PT-BD-SPL", "RefTitle": "Supplement to Note 112799: Screen RQDULM10 0333", "RefUrl": "/notes/170259 "}, {"RefNumber": "171399", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "TPI: Segment E1OILTW Error in OILTPI01", "RefUrl": "/notes/171399 "}, {"RefNumber": "170583", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "ITA: Incompletion message for deleted contract/ order item", "RefUrl": "/notes/170583 "}, {"RefNumber": "150831", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Movement based netting: VAT on MM side missing (II)", "RefUrl": "/notes/150831 "}, {"RefNumber": "146906", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Movement based netting: VAT on MM side missing", "RefUrl": "/notes/146906 "}, {"RefNumber": "102306", "RefComponent": "IS-OIL-DS-EXG", "RefTitle": "Wrong values in statistics update", "RefUrl": "/notes/102306 "}, {"RefNumber": "167500", "RefComponent": "XX-CSC-US", "RefTitle": "Contract Solutions Component: Manual Assignment Not Consider", "RefUrl": "/notes/167500 "}, {"RefNumber": "162514", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "ITA: missg. Mode of Transp. in Output Determination", "RefUrl": "/notes/162514 "}, {"RefNumber": "162652", "RefComponent": "IS-OIL-DS-TAS", "RefTitle": "ITA: Missing UserExits in Function IDOC_INPUT_OILSH1", "RefUrl": "/notes/162652 "}, {"RefNumber": "74099", "RefComponent": "XX-CSC-TH", "RefTitle": "Additional Info: Installation Thai Version 30D/1", "RefUrl": "/notes/74099 "}, {"RefNumber": "98642", "RefComponent": "0 IS-OIL-BC", "RefTitle": "Order of IS-OIL notes 2.0d & 1.0d on R/3 3.1H (SP)", "RefUrl": "/notes/98642 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": []}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}