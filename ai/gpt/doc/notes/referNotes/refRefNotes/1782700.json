{"Request": {"Number": "1782700", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 498, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000010533802017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001782700?language=E&token=328C1934AFB15014AEB8E5CE434C8FA1"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001782700", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0001782700/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1782700"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 5}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Program error"}, "Priority": {"_label": "Priority", "value": "Correction with high priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "12.02.2014"}, "SAPComponentKey": {"_label": "Component", "value": "FIN-FSCM-TRM-TM-TR"}, "SAPComponentKeyText": {"_label": "Component", "value": "Transaction Management"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Financials", "value": "FIN", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'FIN*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Financial Supply Chain Management", "value": "FIN-FSCM", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'FIN-FSCM*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Treasury and Risk Management", "value": "FIN-FSCM-TRM", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'FIN-FSCM-TRM*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Transaction Manager", "value": "FIN-FSCM-TRM-TM", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'FIN-FSCM-TRM-TM*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Transaction Management", "value": "FIN-FSCM-TRM-TM-TR", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'FIN-FSCM-TRM-TM-TR*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1782700 - COMS: Commodity Swap, Commodity Forward: Usability"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>COMS Commodity Swap, Commodity Forward usability improvements (e.g. harmonization of screen fields, conversion rules, high amounts; COMS: netting payment date easy input, spread easy input, correspondence display, flow generator modus switch easier for single flow, quotation source derivation from commodity master, calendar longtext display).</p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>COMS, APCF, CF, COMMODITY, COMMODITIES, CONTANGO, CALC_PRICE, DUMP, BACKWARDATION, SPREAD, CX_SY_CONVERSION_OVERFLOW, COMPUTE_OVERFLOW, COR</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>Business Function FIN_TRM_COMM_RM (EHP5) and FIN_TRM_COMM_RM_2 (EHP6) is active. Commodity Forward and/or Commodity Swap are in use.</p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>Import the following correction.</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D026399)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D026399)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0001782700/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001782700/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001782700/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001782700/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001782700/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001782700/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001782700/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001782700/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001782700/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1783445", "RefComponent": "FIN-FSCM-TRM-TM-TR", "RefTitle": "COMS: Comp. SAP Note for user-friendliness in commodity swap", "RefUrl": "/notes/1783445"}, {"RefNumber": "1668882", "RefComponent": "BC-UPG-NA", "RefTitle": "Note Assistant: Important notes for SAP_BASIS 730,731,740,750,751,752,753,754,755,756", "RefUrl": "/notes/1668882"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1668882", "RefComponent": "BC-UPG-NA", "RefTitle": "Note Assistant: Important notes for SAP_BASIS 730,731,740,750,751,752,753,754,755,756", "RefUrl": "/notes/1668882 "}, {"RefNumber": "1783445", "RefComponent": "FIN-FSCM-TRM-TM-TR", "RefTitle": "COMS: Comp. SAP Note for user-friendliness in commodity swap", "RefUrl": "/notes/1783445 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "EA-FINSERV", "From": "605", "To": "605", "Subsequent": ""}, {"SoftwareComponent": "EA-FINSERV", "From": "606", "To": "606", "Subsequent": ""}, {"SoftwareComponent": "EA-FINSERV", "From": "616", "To": "616", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "EA-FINSERV 605", "SupportPackage": "SAPK-60512INEAFINSRV", "URL": "/supportpackage/SAPK-60512INEAFINSRV"}, {"SoftwareComponentVersion": "EA-FINSERV 606", "SupportPackage": "SAPK-60608INEAFINSRV", "URL": "/supportpackage/SAPK-60608INEAFINSRV"}, {"SoftwareComponentVersion": "EA-FINSERV 616", "SupportPackage": "SAPK-61602INEAFINSRV", "URL": "/supportpackage/SAPK-61602INEAFINSRV"}, {"SoftwareComponentVersion": "EA-FINSERV 616", "SupportPackage": "SAPK-61603INEAFINSRV", "URL": "/supportpackage/SAPK-61603INEAFINSRV"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": [{"SoftwareComponent": "EA-FINSERV", "NumberOfCorrin": 6, "URL": "/corrins/0001782700/201"}]}, "ManualActions": {"_label": "Manual Activities", "value": " <code>   <P><br/><br/>------------------------------------------------------------------------<br/>|Manual Pre-Implement.&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>------------------------------------------------------------------------<br/>|VALID FOR&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>|Software Component&nbsp;&nbsp;EA-FINSERV&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;SAP R/3 Enterpr...|<br/>| Release 605&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;SAPK-60503INEAFINSRV - SAPK-60511INEAFINSRV&nbsp;&nbsp;&nbsp;&nbsp; |<br/>| Release 606&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;SAPK-60601INEAFINSRV - SAPK-60607INEAFINSRV&nbsp;&nbsp;&nbsp;&nbsp; |<br/>| Release 616&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Until SAPK-61601INEAFINSRV&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;|<br/>------------------------------------------------------------------------<br/><br/><B>Caution</B>: You have to perform this manual pre-implementation step manually and separately in each system <B>before</B> you import the Note to implement.<br/><br/>Import SAP note 1851619.<br/>Call transaction SA38 and execute report NOTE_1782700.<br/></P> <br/>  </code> "}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 6, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 1, "state": "Error"}, "Prerequisites": {"_label": "Prerequisites", "value": 19, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": [{"SoftwareComponent": "EA-FINSERV", "ValidFrom": "605", "ValidTo": "605", "Number": "1609011 ", "URL": "/notes/1609011 ", "Title": "TM: Commodity Forward does not fill the TRADER field", "Component": "FIN-FSCM-TRM-TM"}, {"SoftwareComponent": "EA-FINSERV", "ValidFrom": "605", "ValidTo": "605", "Number": "1712227 ", "URL": "/notes/1712227 ", "Title": "TREA: Enhancement of payment details", "Component": "FIN-FSCM-TRM-TM"}, {"SoftwareComponent": "EA-FINSERV", "ValidFrom": "605", "ValidTo": "605", "Number": "1749398 ", "URL": "/notes/1749398 ", "Title": "Commodity Forward - Payment details missing", "Component": "FIN-FSCM-TRM-TM"}, {"SoftwareComponent": "EA-FINSERV", "ValidFrom": "605", "ValidTo": "605", "Number": "1757434 ", "URL": "/notes/1757434 ", "Title": "TREA: Adjustment of the enhancements in transact management", "Component": "FIN-FSCM-TRM-TM"}, {"SoftwareComponent": "EA-FINSERV", "ValidFrom": "605", "ValidTo": "605", "Number": "1765958 ", "URL": "/notes/1765958 ", "Title": "Multiple Flow Generation for Commodity Swap", "Component": "FIN-FSCM-TRM-TM-TR"}, {"SoftwareComponent": "EA-FINSERV", "ValidFrom": "605", "ValidTo": "605", "Number": "1770463 ", "URL": "/notes/1770463 ", "Title": "FMOD: Commodity Forward deals field change after posting", "Component": "FIN-FSCM-TRM-TM-TR"}, {"SoftwareComponent": "EA-FINSERV", "ValidFrom": "605", "ValidTo": "605", "Number": "1775755 ", "URL": "/notes/1775755 ", "Title": "Length of Commodity Swaps IDs", "Component": "FIN-FSCM-TRM-CRM-TM"}, {"SoftwareComponent": "EA-FINSERV", "ValidFrom": "605", "ValidTo": "605", "Number": "1776847 ", "URL": "/notes/1776847 ", "Title": "COMS: Cash management planned record update, reversal, UI", "Component": "FIN-FSCM-TRM-TM-TR"}, {"SoftwareComponent": "EA-FINSERV", "ValidFrom": "605", "ValidTo": "605", "Number": "1781478 ", "URL": "/notes/1781478 ", "Title": "Fields ready for entry in display and reverse mode", "Component": "FIN-FSCM-TRM-TM"}, {"SoftwareComponent": "EA-FINSERV", "ValidFrom": "605", "ValidTo": "605", "Number": "1826689 ", "URL": "/notes/1826689 ", "Title": "T1043: when mirroring interest rate instrument deal", "Component": "FIN-FSCM-TRM-TM"}, {"SoftwareComponent": "EA-FINSERV", "ValidFrom": "606", "ValidTo": "606", "Number": "1765958 ", "URL": "/notes/1765958 ", "Title": "Multiple Flow Generation for Commodity Swap", "Component": "FIN-FSCM-TRM-TM-TR"}, {"SoftwareComponent": "EA-FINSERV", "ValidFrom": "606", "ValidTo": "606", "Number": "1770463 ", "URL": "/notes/1770463 ", "Title": "FMOD: Commodity Forward deals field change after posting", "Component": "FIN-FSCM-TRM-TM-TR"}, {"SoftwareComponent": "EA-FINSERV", "ValidFrom": "606", "ValidTo": "606", "Number": "1775402 ", "URL": "/notes/1775402 ", "Title": "Add cash-settlement to commodity forward via BAPI error", "Component": "FIN-FSCM-TRM-TM"}, {"SoftwareComponent": "EA-FINSERV", "ValidFrom": "606", "ValidTo": "606", "Number": "1775755 ", "URL": "/notes/1775755 ", "Title": "Length of Commodity Swaps IDs", "Component": "FIN-FSCM-TRM-CRM-TM"}, {"SoftwareComponent": "EA-FINSERV", "ValidFrom": "606", "ValidTo": "606", "Number": "1776847 ", "URL": "/notes/1776847 ", "Title": "COMS: Cash management planned record update, reversal, UI", "Component": "FIN-FSCM-TRM-TM-TR"}, {"SoftwareComponent": "EA-FINSERV", "ValidFrom": "606", "ValidTo": "606", "Number": "1781478 ", "URL": "/notes/1781478 ", "Title": "Fields ready for entry in display and reverse mode", "Component": "FIN-FSCM-TRM-TM"}, {"SoftwareComponent": "EA-FINSERV", "ValidFrom": "606", "ValidTo": "606", "Number": "1826689 ", "URL": "/notes/1826689 ", "Title": "T1043: when mirroring interest rate instrument deal", "Component": "FIN-FSCM-TRM-TM"}, {"SoftwareComponent": "EA-FINSERV", "ValidFrom": "606", "ValidTo": "616", "Number": "1801241 ", "URL": "/notes/1801241 ", "Title": "Mandatory fields in commodity swaps can be left empty", "Component": "FIN-FSCM-TRM-TM"}, {"SoftwareComponent": "EA-FINSERV", "ValidFrom": "606", "ValidTo": "616", "Number": "1805187 ", "URL": "/notes/1805187 ", "Title": "Mandatory fields in commodity swaps can be left empty", "Component": "FIN-FSCM-TRM-TM"}, {"SoftwareComponent": "EA-FINSERV", "ValidFrom": "616", "ValidTo": "616", "Number": "1770463 ", "URL": "/notes/1770463 ", "Title": "FMOD: Commodity Forward deals field change after posting", "Component": "FIN-FSCM-TRM-TM-TR"}, {"SoftwareComponent": "EA-FINSERV", "ValidFrom": "616", "ValidTo": "616", "Number": "1775402 ", "URL": "/notes/1775402 ", "Title": "Add cash-settlement to commodity forward via BAPI error", "Component": "FIN-FSCM-TRM-TM"}, {"SoftwareComponent": "EA-FINSERV", "ValidFrom": "616", "ValidTo": "616", "Number": "1775755 ", "URL": "/notes/1775755 ", "Title": "Length of Commodity Swaps IDs", "Component": "FIN-FSCM-TRM-CRM-TM"}, {"SoftwareComponent": "EA-FINSERV", "ValidFrom": "616", "ValidTo": "616", "Number": "1776847 ", "URL": "/notes/1776847 ", "Title": "COMS: Cash management planned record update, reversal, UI", "Component": "FIN-FSCM-TRM-TM-TR"}, {"SoftwareComponent": "EA-FINSERV", "ValidFrom": "616", "ValidTo": "616", "Number": "1780782 ", "URL": "/notes/1780782 ", "Title": "Overflow when entering more than 10 digits in Quantity", "Component": "FIN-FSCM-TRM-TM"}, {"SoftwareComponent": "EA-FINSERV", "ValidFrom": "616", "ValidTo": "616", "Number": "1781478 ", "URL": "/notes/1781478 ", "Title": "Fields ready for entry in display and reverse mode", "Component": "FIN-FSCM-TRM-TM"}, {"SoftwareComponent": "EA-FINSERV", "ValidFrom": "616", "ValidTo": "616", "Number": "1782700 ", "URL": "/notes/1782700 ", "Title": "COMS: Commodity Swap, Commodity Forward: Usability", "Component": "FIN-FSCM-TRM-TM-TR"}, {"SoftwareComponent": "EA-FINSERV", "ValidFrom": "616", "ValidTo": "616", "Number": "1793408 ", "URL": "/notes/1793408 ", "Title": "BAPI: BAPI_FTR_CONDITION is missing Rounding fields", "Component": "FIN-FSCM-TRM-TM"}, {"SoftwareComponent": "EA-FINSERV", "ValidFrom": "616", "ValidTo": "616", "Number": "1797456 ", "URL": "/notes/1797456 ", "Title": "Enhancements for function groups: FTR_API, FTR_BAPI", "Component": "FIN-FSCM-TRM-TM-TR"}, {"SoftwareComponent": "EA-FINSERV", "ValidFrom": "616", "ValidTo": "616", "Number": "1813542 ", "URL": "/notes/1813542 ", "Title": "BAPI: BAPI_FTR_CONDITION missing field for exponential interest calculation", "Component": "FIN-FSCM-TRM-TM"}, {"SoftwareComponent": "EA-FINSERV", "ValidFrom": "616", "ValidTo": "616", "Number": "1826689 ", "URL": "/notes/1826689 ", "Title": "T1043: when mirroring interest rate instrument deal", "Component": "FIN-FSCM-TRM-TM"}]}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1851619", "RefTitle": "Preparation for SAP Note 1782700", "RefUrl": "/notes/0001851619"}]}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1879746", "RefTitle": "BAPIs fail Commodity Forward deal", "RefUrl": "/notes/0001879746"}]}}}}}