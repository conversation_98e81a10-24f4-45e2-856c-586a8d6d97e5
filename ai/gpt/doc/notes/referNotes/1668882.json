{"Request": {"Number": "1668882", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 1028, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000009908772017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001668882?language=E&token=CF8D9D8AEEC36FF5F70C8FEAF116963E"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001668882", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0001668882/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1668882"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 39}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Program error"}, "Priority": {"_label": "Priority", "value": "Correction with high priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "13.02.2024"}, "SAPComponentKey": {"_label": "Component", "value": "BC-UPG-NA"}, "SAPComponentKeyText": {"_label": "Component", "value": "Note Assistant"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Basis Components", "value": "BC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Upgrade - general", "value": "BC-UPG", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-UPG*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Note Assistant", "value": "BC-UPG-NA", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-UPG-NA*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1668882 - Note Assistant: Important notes for SAP_BASIS 730,731,740,750,751,752,753,754,755,756"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>The Note Assistant allows you to automatically implement note corrections in your ABAP systems. You can find further information about the Note Assistant on SAP Service Marketplace at service.sap.com/noteassistant.<br /><br />Before you implement notes with the Note Assistant, you should upgrade to the latest version of the Note Assistant. This note references the most important notes for correcting errors and updating the Note Assistant. It is the successor of Note 875986.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>SAP notes, SNOTE, SAP_NOTES</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>This note updates the application / transaction snote.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p><br /><br /><strong>Implement the current version of this note to update the Note Assistant.</strong> This ensures that errors in the Note Assistant are corrected and that you can use the latest functions. During the implementation, all of the SAP notes that are listed in this note under <strong>III/ Attachment</strong> and that are relevant for your release level and Support Package level are implemented in your SAP system.<br />Depending on the release level and Support Package level of your SAP system, you have the following options:<br /><br />You can implement Note 1668882 automatically using the Note Assistant.<br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; The prerequisites and procedure can be found under \"<strong>I/ Implementing Note 1668882 using the Note Assistant\".</strong><br /><br />You can individually implement the notes that are relevant for your SAP system using the Note Assistant.<br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; The prerequisites and procedure can be found under \"<strong>II/ Implementing notes from Note 1668882 individually using the Note Assistant\".</strong><br /><br />If your SAP system has a different release and Support Package level than those mentioned under I/ and II/, then the note is not relevant for your SAP system.<br /><br /><strong><span style=\"text-decoration: underline;\">I/ Implementing Note 1668882 using the Note Assistant</span></strong><br /><br /><br /><strong>Prerequisites</strong><br />Your system must have one of the following release and Support Package levels or higher:<br /><br /><strong>Soft.Comp. Release Support Package</strong><br />SAP_BASIS 7.30 Without Support Packages</p>\r\n<p>SAP_BASIS 7.31 Without Support Packages</p>\r\n<p>SAP_BASIS 7.40 Without Support Packages</p>\r\n<p>SAP_BASIS 7.50 Without Support Packages</p>\r\n<p>SAP_BASIS 7.51 Without Support Packages</p>\r\n<p>SAP_BASIS 7.52 Without Support Packages</p>\r\n<p>SAP_BASIS 7.53 Without Support Packages</p>\r\n<p>SAP_BASIS 7.54 Without Support Packages</p>\r\n<p>SAP_BASIS 7.55 Without Support Packages</p>\r\n<p>SAP_BASIS 7.56 Without Support Packages</p>\r\n<p><strong>Please implement note 2248091 before implementing this note.</strong></p>\r\n<p><br /><strong>Procedure</strong><br /><br />1. Use Note Assistant to implement Note 1668882.<br /><strong>Note the following:</strong> If you cannot implement changes during the implementation (yellow traffic light on the 'Confirm Changes' input screen), use the 'Cancel' (F12) function to terminate the implementation of the note and create a customer message under the application component BC-UPG-NA.<br /><br />2. After you confirm the activation of the changed objects, the dialog window for selecting the main program for CWBNTCNS appears in certain SAP_BASIS Support Packages that were previously imported. Select one of the listed programs and choose \"Select\".<br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;Important : If you do not select a program and choose \"Terminate\" instead, the system does not activate the changes that you want to implement using the composite note.<br /><br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;All of the notes that are relevant for your release and Support Package level are implemented in your SAP system.<br /><br />3. Use transaction /NSNOTE to restart the Note Assistant.<br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;The Note Assistant is now updated to the current version. You can implement more SAP notes using the Note Assistant.<br /><br /><br /><strong><span style=\"text-decoration: underline;\">II/ Implementing notes from Note 1668882 individually using the Note Assistant</span></strong><br />You can implement notes from Note 1668882 individually using the Note Assistant.<br /><br /><br /><br /><strong>Procedure</strong><br /><br />1. Load Note 1668882 into your system.<br />2. Load all of the notes contained in Note 1668882 into your system. Under \"III/ Attachment: List of notes from Note 1668882\", you will find a list of the notes contained in Note 1668882.<br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;If you load notes into the Note Assistant via an RFC connection, you can proceed as follows:<br /><br />a) Select all note numbers listed in Note 1668882 under \"III/ Attachment: List of notes from Note 1668882\"\" and copy them.<br />b) In the Note Assistant, choose \"Download SAP Note\".<br />c) Choose \"Multiple selection\".<br />d) Choose \"Upload from clipboard\".<br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;The system displays a list of note numbers.<br /><br />e) Choose \"Copy\" and start the note download.<br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;All of the notes contained in the note are loaded into your system.<br /><br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;The Note Assistant shows you which notes can be implemented in your system. You can classify notes that cannot be implemented as \"not relevant\" to remove them from your worklist.<br /><br />3. Implement the notes that can be implemented one after the other and pay attention to the note texts when you do this.<br /><br /><br /><br /><strong><span style=\"text-decoration: underline;\">III Attachment: List of notes from Note 1668882</span></strong><br />The corrections of the following listed notes are implemented in your system when Note 1668882 is automatically implemented in your system if their release level and Support Package level is relevant for your system.<br />The list of notes corresponds mostly with the related notes.&#160;&#160;However, the related notes are displayed only in the download area, but not in the Note Assistant. You require the list to be able to download the notes together if you implement the notes individually from Note 1668882 with the Note Assistant as described under II/.</p>\r\n<p>0001291055<br />0001487461<br />0001487489<br />0001487661<br />0001494322<br />0001500456<br />0001504500<br />0001505242<br />0001517468<br />0001518861<br />0001523687<br />0001524252<br />0001530273<br />0001532264<br />0001535724<br />0001537354<br />0001539505<br />0001541531<br />0001542835<br />0001543395<br />0001549103<br />0001552560<br />0001557768<br />0001566290<br />0001571213<br />0001610942<br />0001621321<br />0001627683<br />0001639074<br />0001673013<br />0001718058<br />0001720495<br />0001930917<br />0001953150<br />0001975910<br />0002007838<br />0002025616<br />0002059257<br />0002077553<br />0002115211<br />0002116888<br />0002130489<br />0002158475<br />0002159134<br />0002192729<br />0002248091<br />0002254096<br />0002254211<br />0002264123<br />0002289302<br />0002292923<br />0002314876<br />0002328318<br />0002411418<br />0002398161</p>\r\n<p>0002697766<br />0002691847<br />0002684471<br />0002671774<br />0002623459<br />0002624337<br />0002617883</p>\r\n<p>0002396769</p>\r\n<p>0002042123</p>\r\n<p>0002478661</p>\r\n<p>0002589309</p>\r\n<p>0003041970</p>\r\n<p>0003047860</p>\r\n<p>0003079593</p>\r\n<p>0003085447</p>\r\n<p>2715783<br />2757237<br />2764725<br />2765308<br />2459558<br />2212925<br />2606986<br />2541236<br />2536585<br />2368460<br />2598809<br />2597808<br />2568276<br />2292923<br />2739641<br />2770960</p>\r\n<p>2773977</p>\r\n<p>2775698</p>\r\n<p>2730170</p>\r\n<p>2792897</p>\r\n<p>2799582</p>\r\n<p>2802126</p>\r\n<p>2810041</p>\r\n<p>2844646</p>\r\n<p>2860125</p>\r\n<p>2910608</p>\r\n<p>2930611</p>\r\n<p>3008844</p>\r\n<p>3006946</p>\r\n<p>2958954</p>\r\n<p>2953369</p>\r\n<p>3001279</p>\r\n<p>3111925</p>\r\n<p>3123184</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "ABHIRAJ SUMAN (I069105)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON><PERSON><PERSON><PERSON> (I567948)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "J", "TranslationLanguage": "日本語", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0001668882/J"}, {"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0001668882/D"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001668882/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001668882/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001668882/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001668882/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001668882/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001668882/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001668882/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "875986", "RefComponent": "BC-UPG-NA", "RefTitle": "Note Assistant: Important notes for SAP_BASIS up to 702", "RefUrl": "/notes/875986"}, {"RefNumber": "1782700", "RefComponent": "FIN-FSCM-TRM-TM-TR", "RefTitle": "COMS: Commodity Swap, Commodity Forward: Usability", "RefUrl": "/notes/1782700"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "3322169", "RefComponent": "SV-SCS-S4R", "RefTitle": "Error \"The system is already a SAP S/4HANA system. This is not supported\" when running the Readiness Check Report for S/4HANA upgrades", "RefUrl": "/notes/3322169 "}, {"RefNumber": "3212023", "RefComponent": "BC-UPG-NA", "RefTitle": "Snote Error: Not all required corrections were implemented", "RefUrl": "/notes/3212023 "}, {"RefNumber": "3307107", "RefComponent": "FI-LOC-LO-BR", "RefTitle": "NF-e Technical Note 2023.001 and Monophasic ICMS for Fuels Troubleshooting Guide", "RefUrl": "/notes/3307107 "}, {"RefNumber": "3308795", "RefComponent": "SV-SCS-S4R", "RefTitle": "Error message \"Name <MIME objects> is not unique\" happens when implementing SAP note 2758146 for S/4 HANA Readiness Check", "RefUrl": "/notes/3308795 "}, {"RefNumber": "3269532", "RefComponent": "BC-UPG-NA", "RefTitle": "Object unkown error while implementing SAP Note 2627665", "RefUrl": "/notes/3269532 "}, {"RefNumber": "2950129", "RefComponent": "BC-UPG-NA", "RefTitle": "Dump OBJECTS_OBJREF_NOT_ASSIGNED during download a note", "RefUrl": "/notes/2950129 "}, {"RefNumber": "2174277", "RefComponent": "FI-LOC-FI-RU", "RefTitle": "SAP RU-FI, UA-FI, KZ-FI: How to check whether all Notes are implemented", "RefUrl": "/notes/2174277 "}, {"RefNumber": "3027713", "RefComponent": "XX-CSC-BR-SD", "RefTitle": "NF-e Technical Note 2020.006 v1.00 Troubleshooting Guide", "RefUrl": "/notes/3027713 "}, {"RefNumber": "2430089", "RefComponent": "BC-UPG-NA", "RefTitle": "Downloading note correction timeout after long duration", "RefUrl": "/notes/2430089 "}, {"RefNumber": "3009836", "RefComponent": "TM-FRM", "RefTitle": "When implementing 2780373, there is error: /SCMTMS/TOR_OH_RELDOC is unknown", "RefUrl": "/notes/3009836 "}, {"RefNumber": "3024048", "RefComponent": "BC-UPG-NA", "RefTitle": "Format of correction instructions; unable to read corr. instruct.", "RefUrl": "/notes/3024048 "}, {"RefNumber": "2415916", "RefComponent": "BC-DWB-CEX", "RefTitle": "EU526 \"Carry out modification comparison first\" occurs when to change SAP Standard objects (1)", "RefUrl": "/notes/2415916 "}, {"RefNumber": "2990337", "RefComponent": "BC-UPG-NA", "RefTitle": "SNOTE: Field LV_ABAP_SRC is unknown", "RefUrl": "/notes/2990337 "}, {"RefNumber": "2971405", "RefComponent": "BC-UPG-NA", "RefTitle": "Message \"Data Dictionary Objects will not be de-implemented\" during note de-implementation", "RefUrl": "/notes/2971405 "}, {"RefNumber": "2430077", "RefComponent": "BC-UPG-NA", "RefTitle": "Syntax Error in SAPLSCWU", "RefUrl": "/notes/2430077 "}, {"RefNumber": "2816193", "RefComponent": "TM-FRM", "RefTitle": "How to resolve inactive objects after a failed note implementation", "RefUrl": "/notes/2816193 "}, {"RefNumber": "1828131", "RefComponent": "BC-UPG-NA", "RefTitle": "SNOTE Error : Message no. TK111: Mixed Objects With and Without Change Recording", "RefUrl": "/notes/1828131 "}, {"RefNumber": "2786404", "RefComponent": "BC-DB-DB2-CCM", "RefTitle": "ABAP program RSDB2J00 errors with exception CX_SY_DYN_CALL_ILLEGAL_TYPE", "RefUrl": "/notes/2786404 "}, {"RefNumber": "2717209", "RefComponent": "BC-UPG-NA", "RefTitle": "Cannot deimplement note in Note Assistant", "RefUrl": "/notes/2717209 "}, {"RefNumber": "2717208", "RefComponent": "BW-SYS-DB-SYB", "RefTitle": "SAP Note 2640571 is incomplete - SAP BW on ASE", "RefUrl": "/notes/2717208 "}, {"RefNumber": "2692782", "RefComponent": "EPM-BPC-NW", "RefTitle": "Long and Short Descriptions of Dimensions are Missing in BPC Web", "RefUrl": "/notes/2692782 "}, {"RefNumber": "2583771", "RefComponent": "BC-UPG-NA", "RefTitle": "How to perform the manual steps of Note 2248091?", "RefUrl": "/notes/2583771 "}, {"RefNumber": "1928534", "RefComponent": "BC-UPG-NA", "RefTitle": "Obsolete SAP notes de-implemented in SPAU with SCWN029", "RefUrl": "/notes/1928534 "}, {"RefNumber": "2525335", "RefComponent": "BC-UPG-NA", "RefTitle": "Software component XXX does not exist", "RefUrl": "/notes/2525335 "}, {"RefNumber": "2499004", "RefComponent": "BC-WD-ABA", "RefTitle": "Unified Rendering update for Web Dynpro ABAP - Guided Answers", "RefUrl": "/notes/2499004 "}, {"RefNumber": "2499284", "RefComponent": "XX-CSC-PT-FI-SAF", "RefTitle": "RPFIEU_SAFT - SAFT 1.04_01 validation errors - Portugal", "RefUrl": "/notes/2499284 "}, {"RefNumber": "2370618", "RefComponent": "OPU-GW-COR", "RefTitle": "SAP Gateway $filter value not handed over correctly to the backend", "RefUrl": "/notes/2370618 "}, {"RefNumber": "2482850", "RefComponent": "BC-UPG-NA", "RefTitle": "Change user or transport during note implementation", "RefUrl": "/notes/2482850 "}, {"RefNumber": "2472425", "RefComponent": "BC-UPG-NA", "RefTitle": "Error while implementing note 2019086", "RefUrl": "/notes/2472425 "}, {"RefNumber": "3381999", "RefComponent": "FI-TV-ODT-MTE", "RefTitle": "S/4HANA Travel Expenses FIORI Application: No validation messages and BAdI errors ignored", "RefUrl": "/notes/3381999 "}, {"RefNumber": "3228321", "RefComponent": "BC-UPG-NZ-DT", "RefTitle": "NZDT - Composite SAP Note - S4CORE 107", "RefUrl": "/notes/3228321 "}, {"RefNumber": "3140903", "RefComponent": "PY-RU", "RefTitle": "Wrong log of payroll function RUPRI", "RefUrl": "/notes/3140903 "}, {"RefNumber": "3069330", "RefComponent": "FS-FPS", "RefTitle": "Release Information about S4FPSL200 Support Packages", "RefUrl": "/notes/3069330 "}, {"RefNumber": "3103762", "RefComponent": "BW-SYS-DB-SYB", "RefTitle": "SYB: Unpartitioned F fact table enhancement", "RefUrl": "/notes/3103762 "}, {"RefNumber": "3069126", "RefComponent": "FIN-CS-COR-IS", "RefTitle": "Access Controls refactoring downport", "RefUrl": "/notes/3069126 "}, {"RefNumber": "2992703", "RefComponent": "PP-MRP", "RefTitle": "Incorrect results, dumps, or long runtimes in MRP or MRP Live or MD04 (ERP / SAP On-Premise)", "RefUrl": "/notes/2992703 "}, {"RefNumber": "2981408", "RefComponent": "FS-FPS", "RefTitle": "Unexpected market data lock in for the process step Value TC for Insurance Contracts", "RefUrl": "/notes/2981408 "}, {"RefNumber": "2880866", "RefComponent": "BC-DB-SYB", "RefTitle": "SAP ASE - Known problems with Support Packages in SAP NetWeaver and SAP Business Suite", "RefUrl": "/notes/2880866 "}, {"RefNumber": "2794727", "RefComponent": "BW4-ME-HMOD", "RefTitle": "HMOD: Generation of External SAP HANA View fails with \"Inconsistent calculation model\"", "RefUrl": "/notes/2794727 "}, {"RefNumber": "2769106", "RefComponent": "BW-WHM-MTD-HMOD", "RefTitle": "HMOD: Guidance for use of column __AP_CHECK_INT in SAP HANA External Views and custom view", "RefUrl": "/notes/2769106 "}, {"RefNumber": "2740667", "RefComponent": "XX-SER-NET", "RefTitle": "SAP is going to close its proprietary RFC communication for automated data exchange between Customers & SAP Support Backbone (SAPOSS) by July 2020.", "RefUrl": "/notes/2740667 "}, {"RefNumber": "2189096", "RefComponent": "BW-SYS-DB-SYB", "RefTitle": "SYB: Extended parallel query support", "RefUrl": "/notes/2189096 "}, {"RefNumber": "2193724", "RefComponent": "BW-SYS-DB-SYB", "RefTitle": "SYB: SAP BW 7.50 / 7.51 / 7.52 Correction Collection", "RefUrl": "/notes/2193724 "}, {"RefNumber": "1821924", "RefComponent": "BW-SYS-DB-SYB", "RefTitle": "SYB: SAP BW 7.40 Correction Collection", "RefUrl": "/notes/1821924 "}, {"RefNumber": "2660932", "RefComponent": "BW-PLA-BPC", "RefTitle": "How to apply a BPC Web Client TCI Note", "RefUrl": "/notes/2660932 "}, {"RefNumber": "2091520", "RefComponent": "BW-SYS-DB", "RefTitle": "Correction Collection for InfoCube conversions between flat <-> non-flat", "RefUrl": "/notes/2091520 "}, {"RefNumber": "2640571", "RefComponent": "BW-SYS-DB-SYB", "RefTitle": "SYB: BW repartitioning split/merge partition is missing for aggregates", "RefUrl": "/notes/2640571 "}, {"RefNumber": "2494590", "RefComponent": "BW-SYS-DB-SYB", "RefTitle": "SYB: Adaptions for Database Parameter Check for SAP ASE", "RefUrl": "/notes/2494590 "}, {"RefNumber": "1691300", "RefComponent": "BW-SYS-DB-SYB", "RefTitle": "SYB: Unpartitioned F fact tables for InfoCubes", "RefUrl": "/notes/1691300 "}, {"RefNumber": "2371150", "RefComponent": "BW-SYS-DB-SYB", "RefTitle": "SYB: Check and correct field PARTMODE for InfoCubes in RSRV", "RefUrl": "/notes/2371150 "}, {"RefNumber": "2381796", "RefComponent": "BW-SYS-DB-SYB", "RefTitle": "SYB: Short dump in RSSYBPARTMON due to integer value overflow", "RefUrl": "/notes/2381796 "}, {"RefNumber": "2394678", "RefComponent": "BW-WHM-DBA-ICUB", "RefTitle": "RS_BW_PRE_MIGRATION fails for InfoCubes partitioned by calendar week (\"0CALWEEK\")", "RefUrl": "/notes/2394678 "}, {"RefNumber": "2396909", "RefComponent": "BW-SYS-DB-SYB", "RefTitle": "SYB: Hint for table type query result", "RefUrl": "/notes/2396909 "}, {"RefNumber": "2413442", "RefComponent": "BW-SYS-DB-SYB", "RefTitle": "SYB: Updates and extensions to RSSYBDBVERSION and RSSYBPARTMON", "RefUrl": "/notes/2413442 "}, {"RefNumber": "2420689", "RefComponent": "BW-SYS-DB-SYB", "RefTitle": "SYB: Index repair functionality for APO InfoCubes", "RefUrl": "/notes/2420689 "}, {"RefNumber": "2472156", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Updating an exchange rate is not reflected", "RefUrl": "/notes/2472156 "}, {"RefNumber": "2456669", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Conversion is applied to key figures without currency/unit", "RefUrl": "/notes/2456669 "}, {"RefNumber": "2382742", "RefComponent": "BW-SYS-DB-SYB", "RefTitle": "SYB: Emit OLAP Hint in Query Split without Fact Table", "RefUrl": "/notes/2382742 "}, {"RefNumber": "2342741", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "System error PROCESS_CONDITION_RANK_PERCENT-01- in program CL_RSR_RRK0_RESULT_SET_STL", "RefUrl": "/notes/2342741 "}, {"RefNumber": "2225070", "RefComponent": "SV-SMG-INS-CFG", "RefTitle": "SAP Solution Manager 7.2 Support Package 03 - Basic functions", "RefUrl": "/notes/2225070 "}, {"RefNumber": "2138636", "RefComponent": "SV-SMG-INS-CFG", "RefTitle": "SAP Solution Manager 7.2 SP02 - basic functions", "RefUrl": "/notes/2138636 "}, {"RefNumber": "1471153", "RefComponent": "FI-GL-REO", "RefTitle": "Composite note for profit center and FM reorganization", "RefUrl": "/notes/1471153 "}, {"RefNumber": "2262942", "RefComponent": "BW-SYS-DB-SYB", "RefTitle": "SYB: Support range partitioning in report SAP_PSA_PARTNO_CORRECT for SAP ASE", "RefUrl": "/notes/2262942 "}, {"RefNumber": "2087917", "RefComponent": "SV-SMG-INS-CFG", "RefTitle": "SAP Solution Manager 7.20 SP01 - Basic functions", "RefUrl": "/notes/2087917 "}, {"RefNumber": "1825340", "RefComponent": "BW-SYS-DB-DB6", "RefTitle": "DB6: Use of BLU Acceleration with SAP BW and Applications Based on SAP BW", "RefUrl": "/notes/1825340 "}, {"RefNumber": "2105395", "RefComponent": "BC-TRX-API", "RefTitle": "TREXADMIN: Writing trace into system log", "RefUrl": "/notes/2105395 "}, {"RefNumber": "2116630", "RefComponent": "BC-TRX-API", "RefTitle": "TREXADMIN : Wrong information is shown in Services tab when switching the display mode", "RefUrl": "/notes/2116630 "}, {"RefNumber": "1935958", "RefComponent": "SV-SMG-INS-CFG", "RefTitle": "SAP Solution Manager 7.20 SP0 - Grundfunktionalität", "RefUrl": "/notes/1935958 "}, {"RefNumber": "2022498", "RefComponent": "BW-WHM-DST-DS", "RefTitle": "Abbtruch bei Aktivierung einer DataSource auf einem DB-Connect Quellsystem vor 7.40 SP5", "RefUrl": "/notes/2022498 "}, {"RefNumber": "1992183", "RefComponent": "BW-WHM-DST-DS", "RefTitle": "ODP DataSource: Handling of message SODQ 107 in preview", "RefUrl": "/notes/1992183 "}, {"RefNumber": "1974794", "RefComponent": "BW-WHM-DST-DS", "RefTitle": "Short dump GETWA_NOT_ASSIGNED in CL_RSDS_ACCESS_ODP", "RefUrl": "/notes/1974794 "}, {"RefNumber": "1831407", "RefComponent": "SV-SMG-INS-CFG", "RefTitle": "SAP Solution Manager 7.12 - basic functions", "RefUrl": "/notes/1831407 "}, {"RefNumber": "1967829", "RefComponent": "BW-BEX-OT-ODP", "RefTitle": "An alternative ODP runtime is not used in CL_RODPS_SIMPLE_CONTEXT", "RefUrl": "/notes/1967829 "}, {"RefNumber": "1970373", "RefComponent": "BW-BEX-OT-ODP", "RefTitle": "Error in HANA ODP with variables of type AttributeValue", "RefUrl": "/notes/1970373 "}, {"RefNumber": "1889656", "RefComponent": "BW-SYS-DB-DB6", "RefTitle": "DB6: Mandatory SAP NW BW corrections for BLU Acceleration", "RefUrl": "/notes/1889656 "}, {"RefNumber": "1782700", "RefComponent": "FIN-FSCM-TRM-TM-TR", "RefTitle": "COMS: Commodity Swap, Commodity Forward: Usability", "RefUrl": "/notes/1782700 "}, {"RefNumber": "875986", "RefComponent": "BC-UPG-NA", "RefTitle": "Note Assistant: Important notes for SAP_BASIS up to 702", "RefUrl": "/notes/875986 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_BASIS", "From": "730", "To": "730", "Subsequent": ""}, {"SoftwareComponent": "SAP_BASIS", "From": "731", "To": "731", "Subsequent": ""}, {"SoftwareComponent": "SAP_BASIS", "From": "740", "To": "740", "Subsequent": ""}, {"SoftwareComponent": "SAP_BASIS", "From": "750", "To": "758", "Subsequent": ""}, {"SoftwareComponent": "SAP_BS_FND", "From": "731", "To": "731", "Subsequent": ""}, {"SoftwareComponent": "SAP_BS_FND", "From": "746", "To": "746", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": [{"SoftwareComponent": "SAP_BASIS", "NumberOfCorrin": 12, "URL": "/corrins/0001668882/41"}]}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 12, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 119, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": [{"SoftwareComponent": "SAP_BASIS", "ValidFrom": "620", "ValidTo": "730", "Number": "1487489 ", "URL": "/notes/1487489 ", "Title": "CWB: Error at creation of correction instruction", "Component": "BC-UPG-NV"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "620", "ValidTo": "730", "Number": "1494322 ", "URL": "/notes/1494322 ", "Title": "SCWB: Unrequired transfer of internal field length", "Component": "BC-UPG-NV"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "620", "ValidTo": "730", "Number": "1500456 ", "URL": "/notes/1500456 ", "Title": "Note Assistant: Side effect notes that are not released", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "620", "ValidTo": "730", "Number": "1524252 ", "URL": "/notes/1524252 ", "Title": "Note implemetation test: download unreleased notes", "Component": "BC-UPG-NV"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "620", "ValidTo": "730", "Number": "1542835 ", "URL": "/notes/1542835 ", "Title": "Note implementatn test w/ subcomponents on SP levels >= 100", "Component": "BC-UPG-NV"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "620", "ValidTo": "730", "Number": "1543395 ", "URL": "/notes/1543395 ", "Title": "Notes implementation test in delivery syst. w/ >= 2 releases", "Component": "BC-UPG-NV"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "620", "ValidTo": "730", "Number": "1552560 ", "URL": "/notes/1552560 ", "Title": "SNOTE: Element sequence in queue for implem./deimplementing", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "620", "ValidTo": "731", "Number": "1539505 ", "URL": "/notes/1539505 ", "Title": "SNOTE: Minor corrections", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "620", "ValidTo": "753", "Number": "1557768 ", "URL": "/notes/1557768 ", "Title": "Note Assistant: find the notes that modify an object", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "640", "ValidTo": "730", "Number": "1539505 ", "URL": "/notes/1539505 ", "Title": "SNOTE: Minor corrections", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "700", "ValidTo": "730", "Number": "1291055 ", "URL": "/notes/1291055 ", "Title": "Note Assistant: Problem activating exception classes", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "700", "ValidTo": "730", "Number": "1539505 ", "URL": "/notes/1539505 ", "Title": "SNOTE: Minor corrections", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "700", "ValidTo": "731", "Number": "1523687 ", "URL": "/notes/1523687 ", "Title": "SNOTE: Notes - Interface enhancement", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "700", "ValidTo": "731", "Number": "1627683 ", "URL": "/notes/1627683 ", "Title": "SCWB/SNOTE/SPAU: Changed development package", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "700", "ValidTo": "731", "Number": "1718058 ", "URL": "/notes/1718058 ", "Title": "Message EU 651 \"Invalid parameter OBJECT/OBJECTCLASS\"", "Component": "BC-DWB-TOO-FUB"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "700", "ValidTo": "731", "Number": "3047860 ", "URL": "/notes/3047860 ", "Title": "SNOTE: Error in handling of transactions", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "700", "ValidTo": "740", "Number": "2025616 ", "URL": "/notes/2025616 ", "Title": "Performance improvement for note download", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "700", "ValidTo": "740", "Number": "2059257 ", "URL": "/notes/2059257 ", "Title": "Side effect note display in PDF", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "700", "ValidTo": "740", "Number": "2115211 ", "URL": "/notes/2115211 ", "Title": "Error downloading a not released note", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "700", "ValidTo": "750", "Number": "2192729 ", "URL": "/notes/2192729 ", "Title": "Version history screen - check display of retrieve button", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "700", "ValidTo": "750", "Number": "2289302 ", "URL": "/notes/2289302 ", "Title": "SNOTE: Object list pop-up navigates to a different object during note implementation/de-implementation", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "700", "ValidTo": "754", "Number": "3006946 ", "URL": "/notes/3006946 ", "Title": "SNOTE: Error SCWN 409 is displayed during Note Implementation when it contains MESS/MSAD object", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "700", "ValidTo": "757", "Number": "3307778 ", "URL": "/notes/3307778 ", "Title": "Remove SNOTE support for SHI3 Object Type if its incorrectly enabled", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "701", "ValidTo": "740", "Number": "2042123 ", "URL": "/notes/2042123 ", "Title": "Activation of objects in workbench locked by user", "Component": "BC-DWB-TOO"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "702", "ValidTo": "731", "Number": "1539505 ", "URL": "/notes/1539505 ", "Title": "SNOTE: Minor corrections", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "702", "ValidTo": "750", "Number": "2212925 ", "URL": "/notes/2212925 ", "Title": "Fix for automatic handling of DDIC correction instructions", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "710", "ValidTo": "730", "Number": "1505242 ", "URL": "/notes/1505242 ", "Title": "SNOTE: Incorrect note status after deimplementation canceled", "Component": "BC-DWB-CEX"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "710", "ValidTo": "730", "Number": "1537354 ", "URL": "/notes/1537354 ", "Title": "Support for downloading huge notes over slow connections", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "710", "ValidTo": "730", "Number": "1541531 ", "URL": "/notes/1541531 ", "Title": "Pushbuttons lost after nested popups", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "710", "ValidTo": "731", "Number": "1610942 ", "URL": "/notes/1610942 ", "Title": "CONVERT_OTF error due to non matching languages", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "711", "ValidTo": "730", "Number": "1504500 ", "URL": "/notes/1504500 ", "Title": "Note Assistant: Navigation from Transport Organizer", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "711", "ValidTo": "730", "Number": "1530273 ", "URL": "/notes/1530273 ", "Title": "Note Assistant: Incorrect error message for INTF methods", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "730", "ValidTo": "730", "Number": "1487661 ", "URL": "/notes/1487661 ", "Title": "Authorization check when deleting from version management", "Component": "BC-CTS-ORG"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "730", "ValidTo": "730", "Number": "1517468 ", "URL": "/notes/1517468 ", "Title": "Corrupted versions when transferring them via RFC", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "730", "ValidTo": "730", "Number": "1518861 ", "URL": "/notes/1518861 ", "Title": "Wrong handling of deleted objects in SNOTE", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "730", "ValidTo": "730", "Number": "1523687 ", "URL": "/notes/1523687 ", "Title": "SNOTE: Notes - Interface enhancement", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "730", "ValidTo": "730", "Number": "1532264 ", "URL": "/notes/1532264 ", "Title": "Further improved PDF downloads in Note Assistant", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "730", "ValidTo": "730", "Number": "1535724 ", "URL": "/notes/1535724 ", "Title": "Activation problems with Enhancement Objects", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "730", "ValidTo": "730", "Number": "1549103 ", "URL": "/notes/1549103 ", "Title": "SNOTE: Problems with language-dependent notes", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "730", "ValidTo": "730", "Number": "1566290 ", "URL": "/notes/1566290 ", "Title": "Creation of interfaces using Note Assistant fails", "Component": "BC-DWB-TOO-CLA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "730", "ValidTo": "730", "Number": "1571213 ", "URL": "/notes/1571213 ", "Title": "SE24: Note Assistant does not adjust component type", "Component": "BC-DWB-TOO-CLA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "730", "ValidTo": "730", "Number": "1621321 ", "URL": "/notes/1621321 ", "Title": "SNOTE/SCWB for screens: Field attribute minimum line/row", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "730", "ValidTo": "730", "Number": "1627683 ", "URL": "/notes/1627683 ", "Title": "SCWB/SNOTE/SPAU: Changed development package", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "730", "ValidTo": "730", "Number": "1639074 ", "URL": "/notes/1639074 ", "Title": "SNOTE: Note data is not current or is incorrect", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "730", "ValidTo": "730", "Number": "1673013 ", "URL": "/notes/1673013 ", "Title": "SPAU: Adjustment mode for SAP Notes cannot be determined", "Component": "BC-DWB-CEX"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "730", "ValidTo": "730", "Number": "1720495 ", "URL": "/notes/1720495 ", "Title": "Invalid deimplementation of obsolete notes by Snote tool", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "730", "ValidTo": "730", "Number": "1930917 ", "URL": "/notes/1930917 ", "Title": "SPAU: reset obsolete note", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "730", "ValidTo": "730", "Number": "1953150 ", "URL": "/notes/1953150 ", "Title": "Display for the non source code changes", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "730", "ValidTo": "730", "Number": "1975910 ", "URL": "/notes/1975910 ", "Title": "Mass reset of obsolete notes in SPAU", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "730", "ValidTo": "730", "Number": "2007838 ", "URL": "/notes/2007838 ", "Title": "Job SCWB_BGR_OBS_NOTE_RESET keeps on running", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "730", "ValidTo": "730", "Number": "2042123 ", "URL": "/notes/2042123 ", "Title": "Activation of objects in workbench locked by user", "Component": "BC-DWB-TOO"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "730", "ValidTo": "730", "Number": "2077553 ", "URL": "/notes/2077553 ", "Title": "Obsolete version Implemented notes - Automatic adjustment for manual activities in SPAU", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "730", "ValidTo": "730", "Number": "2116888 ", "URL": "/notes/2116888 ", "Title": "Table Short Description not copied", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "730", "ValidTo": "730", "Number": "2130489 ", "URL": "/notes/2130489 ", "Title": "SNOTE: ABAP dump when downloading note in Non-Unicode system.", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "730", "ValidTo": "730", "Number": "2158475 ", "URL": "/notes/2158475 ", "Title": "Note version always shows the latest version of the note", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "730", "ValidTo": "730", "Number": "2159134 ", "URL": "/notes/2159134 ", "Title": "Objects repeated in pop-up during note implementation", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "730", "ValidTo": "730", "Number": "2264123 ", "URL": "/notes/2264123 ", "Title": "Stopping deimplementation of obsolete notes", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "730", "ValidTo": "730", "Number": "2292923 ", "URL": "/notes/2292923 ", "Title": "Removing dependencies from the deleted CIs of a note", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "730", "ValidTo": "730", "Number": "2328318 ", "URL": "/notes/2328318 ", "Title": "SNOTE: Avoid Obsolete Note Deimplementation", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "730", "ValidTo": "730", "Number": "2398161 ", "URL": "/notes/2398161 ", "Title": "System Dumps when opening a note in PDF", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "730", "ValidTo": "730", "Number": "2536585 ", "URL": "/notes/2536585 ", "Title": "Upgrade: Missing SAP Notes in SPDD and SPAU", "Component": "BC-DWB-CEX"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "730", "ValidTo": "730", "Number": "2606986 ", "URL": "/notes/2606986 ", "Title": "Upgrade: You cannot reset obsolete SAP Notes in transaction SPAU", "Component": "BC-DWB-CEX"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "730", "ValidTo": "730", "Number": "2623459 ", "URL": "/notes/2623459 ", "Title": "Unable to read Correction instruction (Unknown Format)", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "730", "ValidTo": "730", "Number": "2624337 ", "URL": "/notes/2624337 ", "Title": "SNOTE - Note re implementation issue due to object support in newer SP's", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "730", "ValidTo": "730", "Number": "2671774 ", "URL": "/notes/2671774 ", "Title": "Error during Note implementation: Unable to find delivery event <delivery event number>", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "730", "ValidTo": "730", "Number": "2691847 ", "URL": "/notes/2691847 ", "Title": "Previously inactive object activated when current implementation of a note for the same object is cancelled", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "730", "ValidTo": "730", "Number": "2697766 ", "URL": "/notes/2697766 ", "Title": "SNOTE: Runtime Error CONVT_DATA_LOSS occurs while downloading/uploading SAP Notes", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "730", "ValidTo": "730", "Number": "2715783 ", "URL": "/notes/2715783 ", "Title": "Note Text is Empty in SPAU", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "730", "ValidTo": "730", "Number": "2770960 ", "URL": "/notes/2770960 ", "Title": "SNOTE: Handling DDIC inactive objects", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "730", "ValidTo": "730", "Number": "2775698 ", "URL": "/notes/2775698 ", "Title": "Correction instruction creation ignores the local class definition, implementation, definition deffered, definition load , definition local friends changes", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "730", "ValidTo": "730", "Number": "2799582 ", "URL": "/notes/2799582 ", "Title": "SNOTE: Correction Instruction Queue Display Issue", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "730", "ValidTo": "730", "Number": "2844646 ", "URL": "/notes/2844646 ", "Title": "Ignore the local class definition, implementation, definition deffered, definition load , definition local friends changes", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "730", "ValidTo": "730", "Number": "2860125 ", "URL": "/notes/2860125 ", "Title": "SNOTE - Handling unsaved objects to avoid dump", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "730", "ValidTo": "730", "Number": "2910608 ", "URL": "/notes/2910608 ", "Title": "Issue with applying changes to 'TYPES', 'DATA' or 'CONSTANTS' within ABAP Class definition", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "730", "ValidTo": "730", "Number": "2930611 ", "URL": "/notes/2930611 ", "Title": "SCWN_NOTE_STORE Fix for Efficient Note download processing time", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "731", "ValidTo": "731", "Number": "1487461 ", "URL": "/notes/1487461 ", "Title": "SCWB: Improved copying of reference corrections", "Component": "BC-UPG-NV"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "731", "ValidTo": "731", "Number": "1621321 ", "URL": "/notes/1621321 ", "Title": "SNOTE/SCWB for screens: Field attribute minimum line/row", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "731", "ValidTo": "731", "Number": "1624603 ", "URL": "/notes/1624603 ", "Title": "SCWB: Transferring corrections via TLOG object types", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "731", "ValidTo": "731", "Number": "1627683 ", "URL": "/notes/1627683 ", "Title": "SCWB/SNOTE/SPAU: Changed development package", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "731", "ValidTo": "731", "Number": "1639074 ", "URL": "/notes/1639074 ", "Title": "SNOTE: Note data is not current or is incorrect", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "731", "ValidTo": "731", "Number": "1673013 ", "URL": "/notes/1673013 ", "Title": "SPAU: Adjustment mode for SAP Notes cannot be determined", "Component": "BC-DWB-CEX"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "731", "ValidTo": "731", "Number": "1720495 ", "URL": "/notes/1720495 ", "Title": "Invalid deimplementation of obsolete notes by Snote tool", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "731", "ValidTo": "731", "Number": "1930917 ", "URL": "/notes/1930917 ", "Title": "SPAU: reset obsolete note", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "731", "ValidTo": "731", "Number": "1953150 ", "URL": "/notes/1953150 ", "Title": "Display for the non source code changes", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "731", "ValidTo": "731", "Number": "1975910 ", "URL": "/notes/1975910 ", "Title": "Mass reset of obsolete notes in SPAU", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "731", "ValidTo": "731", "Number": "2007838 ", "URL": "/notes/2007838 ", "Title": "Job SCWB_BGR_OBS_NOTE_RESET keeps on running", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "731", "ValidTo": "731", "Number": "2077553 ", "URL": "/notes/2077553 ", "Title": "Obsolete version Implemented notes - Automatic adjustment for manual activities in SPAU", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "731", "ValidTo": "731", "Number": "2116888 ", "URL": "/notes/2116888 ", "Title": "Table Short Description not copied", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "731", "ValidTo": "731", "Number": "2130489 ", "URL": "/notes/2130489 ", "Title": "SNOTE: ABAP dump when downloading note in Non-Unicode system.", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "731", "ValidTo": "731", "Number": "2158475 ", "URL": "/notes/2158475 ", "Title": "Note version always shows the latest version of the note", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "731", "ValidTo": "731", "Number": "2159134 ", "URL": "/notes/2159134 ", "Title": "Objects repeated in pop-up during note implementation", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "731", "ValidTo": "731", "Number": "2212925 ", "URL": "/notes/2212925 ", "Title": "Fix for automatic handling of DDIC correction instructions", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "731", "ValidTo": "731", "Number": "2248091 ", "URL": "/notes/2248091 ", "Title": "Change to reimplementation handling", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "731", "ValidTo": "731", "Number": "2254211 ", "URL": "/notes/2254211 ", "Title": "Fix for new reimplementation handling in SPAU", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "731", "ValidTo": "731", "Number": "2264123 ", "URL": "/notes/2264123 ", "Title": "Stopping deimplementation of obsolete notes", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "731", "ValidTo": "731", "Number": "2292923 ", "URL": "/notes/2292923 ", "Title": "Removing dependencies from the deleted CIs of a note", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "731", "ValidTo": "731", "Number": "2314876 ", "URL": "/notes/2314876 ", "Title": "Fix for a dump in reimplementation handling", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "731", "ValidTo": "731", "Number": "2328318 ", "URL": "/notes/2328318 ", "Title": "SNOTE: Avoid Obsolete Note Deimplementation", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "731", "ValidTo": "731", "Number": "2398161 ", "URL": "/notes/2398161 ", "Title": "System Dumps when opening a note in PDF", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "731", "ValidTo": "731", "Number": "2411418 ", "URL": "/notes/2411418 ", "Title": "Identifying TCI in old release where SAP Note 1995550 is not implemented", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "731", "ValidTo": "731", "Number": "2459558 ", "URL": "/notes/2459558 ", "Title": "Supported object type check in snote", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "731", "ValidTo": "731", "Number": "2536585 ", "URL": "/notes/2536585 ", "Title": "Upgrade: Missing SAP Notes in SPDD and SPAU", "Component": "BC-DWB-CEX"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "731", "ValidTo": "731", "Number": "2541236 ", "URL": "/notes/2541236 ", "Title": "DDIC support in SNOTE - handling of note in SPDD", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "731", "ValidTo": "731", "Number": "2568276 ", "URL": "/notes/2568276 ", "Title": "Remote/update function modules in SNOTE correction instructions", "Component": "BC-DWB-TOO-FUB"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "731", "ValidTo": "731", "Number": "2589309 ", "URL": "/notes/2589309 ", "Title": "Fixes to reimplementation handling - Ignore TADIR for new objects and invalid pre-requisite Notes", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "731", "ValidTo": "731", "Number": "2597808 ", "URL": "/notes/2597808 ", "Title": "Table Type (TTYD): Delta change to primary key not calculated and applied correctly", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "731", "ValidTo": "731", "Number": "2598809 ", "URL": "/notes/2598809 ", "Title": "Table definition (TABD): .APPEND are included for delta calculation and implementation", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "731", "ValidTo": "731", "Number": "2606986 ", "URL": "/notes/2606986 ", "Title": "Upgrade: You cannot reset obsolete SAP Notes in transaction SPAU", "Component": "BC-DWB-CEX"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "731", "ValidTo": "731", "Number": "2617883 ", "URL": "/notes/2617883 ", "Title": "TLOG object read during SPDD phase", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "731", "ValidTo": "731", "Number": "2623459 ", "URL": "/notes/2623459 ", "Title": "Unable to read Correction instruction (Unknown Format)", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "731", "ValidTo": "731", "Number": "2624337 ", "URL": "/notes/2624337 ", "Title": "SNOTE - Note re implementation issue due to object support in newer SP's", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "731", "ValidTo": "731", "Number": "2671774 ", "URL": "/notes/2671774 ", "Title": "Error during Note implementation: Unable to find delivery event <delivery event number>", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "731", "ValidTo": "731", "Number": "2691847 ", "URL": "/notes/2691847 ", "Title": "Previously inactive object activated when current implementation of a note for the same object is cancelled", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "731", "ValidTo": "731", "Number": "2697766 ", "URL": "/notes/2697766 ", "Title": "SNOTE: Runtime Error CONVT_DATA_LOSS occurs while downloading/uploading SAP Notes", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "731", "ValidTo": "731", "Number": "2715783 ", "URL": "/notes/2715783 ", "Title": "Note Text is Empty in SPAU", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "731", "ValidTo": "731", "Number": "2739641 ", "URL": "/notes/2739641 ", "Title": "ABAP Short Dump in SPDD phase during DDIC Note adjustment", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "731", "ValidTo": "731", "Number": "2757237 ", "URL": "/notes/2757237 ", "Title": "Reimplementation of note in SPDD and SPAU with no object change in CI", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "731", "ValidTo": "731", "Number": "2764725 ", "URL": "/notes/2764725 ", "Title": "Fix to reimplementation handling - avoid successor de-implementation during admin change", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "731", "ValidTo": "731", "Number": "2765308 ", "URL": "/notes/2765308 ", "Title": "SNOTE: Support for re-implementation of DDIC Correction Instructions", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "731", "ValidTo": "731", "Number": "2770960 ", "URL": "/notes/2770960 ", "Title": "SNOTE: Handling DDIC inactive objects", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "731", "ValidTo": "731", "Number": "2773977 ", "URL": "/notes/2773977 ", "Title": "SNOTE :Syntax error after implementing changes of Class using SNOTE", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "731", "ValidTo": "731", "Number": "2775698 ", "URL": "/notes/2775698 ", "Title": "Correction instruction creation ignores the local class definition, implementation, definition deffered, definition load , definition local friends changes", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "731", "ValidTo": "731", "Number": "2792897 ", "URL": "/notes/2792897 ", "Title": "SNOTE - Deimplementation fails if any unsupported CI is identified", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "731", "ValidTo": "731", "Number": "2799582 ", "URL": "/notes/2799582 ", "Title": "SNOTE: Correction Instruction Queue Display Issue", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "731", "ValidTo": "731", "Number": "2844646 ", "URL": "/notes/2844646 ", "Title": "Ignore the local class definition, implementation, definition deffered, definition load , definition local friends changes", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "731", "ValidTo": "731", "Number": "2860125 ", "URL": "/notes/2860125 ", "Title": "SNOTE - Handling unsaved objects to avoid dump", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "731", "ValidTo": "731", "Number": "2910608 ", "URL": "/notes/2910608 ", "Title": "Issue with applying changes to 'TYPES', 'DATA' or 'CONSTANTS' within ABAP Class definition", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "731", "ValidTo": "731", "Number": "2930611 ", "URL": "/notes/2930611 ", "Title": "SCWN_NOTE_STORE Fix for Efficient Note download processing time", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "731", "ValidTo": "731", "Number": "2953369 ", "URL": "/notes/2953369 ", "Title": "Handling of Incorrect Adjustment Status of Note in SPDD", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "731", "ValidTo": "731", "Number": "3008844 ", "URL": "/notes/3008844 ", "Title": "SNOTE: Exception handling during de-implementation while reading the history content", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "731", "ValidTo": "731", "Number": "3079593 ", "URL": "/notes/3079593 ", "Title": "Note download gives 'Error in serialization' in Non-Unicode system", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "731", "ValidTo": "731", "Number": "3085447 ", "URL": "/notes/3085447 ", "Title": "Class Deletion Issue While De-implementing the SAP Note", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "731", "ValidTo": "731", "Number": "3236648 ", "URL": "/notes/3236648 ", "Title": "Note Assistant (SNOTE): Move cast exceptions while displaying without delta objects in SNOTE/SNOTE_OLD and CI display", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "731", "ValidTo": "731", "Number": "3262610 ", "URL": "/notes/3262610 ", "Title": "SNOTE: Fix in handling of re-implementation of SAP Note in case already implemented successor SAP Note is incomplete", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "731", "ValidTo": "731", "Number": "3262964 ", "URL": "/notes/3262964 ", "Title": "SAP Note download using Download Service: Fix for not properly waiting for download completion", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "731", "ValidTo": "731", "Number": "3275194 ", "URL": "/notes/3275194 ", "Title": "INDX: De-implementation issue with SNOTE", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "731", "ValidTo": "731", "Number": "3379455 ", "URL": "/notes/3379455 ", "Title": "SNOTE: Fix to show the correct pre-requisite SAP Note in the message \" SAP Note 0000000000 incomplete\" during SAP Note implementation", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "731", "ValidTo": "731", "Number": "3379660 ", "URL": "/notes/3379660 ", "Title": "DOCU: Stop Version Creation for LANG DOCU Object.", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "731", "ValidTo": "752", "Number": "2730170 ", "URL": "/notes/2730170 ", "Title": "SCWB/SNOTE Activation processes Methods in DDIC Phase", "Component": "BC-DWB-UTL-BRR"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "731", "ValidTo": "755", "Number": "3041970 ", "URL": "/notes/3041970 ", "Title": "DEVC object  is not being captured as New object in the CI", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "740", "ValidTo": "740", "Number": "1720495 ", "URL": "/notes/1720495 ", "Title": "Invalid deimplementation of obsolete notes by Snote tool", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "740", "ValidTo": "740", "Number": "1734555 ", "URL": "/notes/1734555 ", "Title": "SNOTE: Performance fix in Interface enhancement", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "740", "ValidTo": "740", "Number": "1800573 ", "URL": "/notes/1800573 ", "Title": "Comparision using message object type not possible", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "740", "ValidTo": "740", "Number": "1817142 ", "URL": "/notes/1817142 ", "Title": "Dump IMPORT_FORMAT_ERROR during display of versions", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "740", "ValidTo": "740", "Number": "1930917 ", "URL": "/notes/1930917 ", "Title": "SPAU: reset obsolete note", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "740", "ValidTo": "740", "Number": "1953150 ", "URL": "/notes/1953150 ", "Title": "Display for the non source code changes", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "740", "ValidTo": "740", "Number": "1975910 ", "URL": "/notes/1975910 ", "Title": "Mass reset of obsolete notes in SPAU", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "740", "ValidTo": "740", "Number": "2007838 ", "URL": "/notes/2007838 ", "Title": "Job SCWB_BGR_OBS_NOTE_RESET keeps on running", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "740", "ValidTo": "740", "Number": "2045124 ", "URL": "/notes/2045124 ", "Title": "Correction for the modif version of the Dynpro object", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "740", "ValidTo": "740", "Number": "2073856 ", "URL": "/notes/2073856 ", "Title": "Dump in Message class.", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "740", "ValidTo": "740", "Number": "2077553 ", "URL": "/notes/2077553 ", "Title": "Obsolete version Implemented notes - Automatic adjustment for manual activities in SPAU", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "740", "ValidTo": "740", "Number": "2116888 ", "URL": "/notes/2116888 ", "Title": "Table Short Description not copied", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "740", "ValidTo": "740", "Number": "2158475 ", "URL": "/notes/2158475 ", "Title": "Note version always shows the latest version of the note", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "740", "ValidTo": "740", "Number": "2159134 ", "URL": "/notes/2159134 ", "Title": "Objects repeated in pop-up during note implementation", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "740", "ValidTo": "740", "Number": "2212925 ", "URL": "/notes/2212925 ", "Title": "Fix for automatic handling of DDIC correction instructions", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "740", "ValidTo": "740", "Number": "2248091 ", "URL": "/notes/2248091 ", "Title": "Change to reimplementation handling", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "740", "ValidTo": "740", "Number": "2254096 ", "URL": "/notes/2254096 ", "Title": "Dump during archiving VERSIONS in SARA transaction", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "740", "ValidTo": "740", "Number": "2254211 ", "URL": "/notes/2254211 ", "Title": "Fix for new reimplementation handling in SPAU", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "740", "ValidTo": "740", "Number": "2264123 ", "URL": "/notes/2264123 ", "Title": "Stopping deimplementation of obsolete notes", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "740", "ValidTo": "740", "Number": "2292923 ", "URL": "/notes/2292923 ", "Title": "Removing dependencies from the deleted CIs of a note", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "740", "ValidTo": "740", "Number": "2314876 ", "URL": "/notes/2314876 ", "Title": "Fix for a dump in reimplementation handling", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "740", "ValidTo": "740", "Number": "2328318 ", "URL": "/notes/2328318 ", "Title": "SNOTE: Avoid Obsolete Note Deimplementation", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "740", "ValidTo": "740", "Number": "2396769 ", "URL": "/notes/2396769 ", "Title": "DDLS: Unpacking CDS source code truncates the source text and an internal comment string", "Component": "BC-DWB-DIC"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "740", "ValidTo": "740", "Number": "2398161 ", "URL": "/notes/2398161 ", "Title": "System Dumps when opening a note in PDF", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "740", "ValidTo": "740", "Number": "2411418 ", "URL": "/notes/2411418 ", "Title": "Identifying TCI in old release where SAP Note 1995550 is not implemented", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "740", "ValidTo": "740", "Number": "2459558 ", "URL": "/notes/2459558 ", "Title": "Supported object type check in snote", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "740", "ValidTo": "740", "Number": "2478661 ", "URL": "/notes/2478661 ", "Title": "Deletion of CDS views using transaction SCWB", "Component": "BC-DWB-DIC-AC"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "740", "ValidTo": "740", "Number": "2536585 ", "URL": "/notes/2536585 ", "Title": "Upgrade: Missing SAP Notes in SPDD and SPAU", "Component": "BC-DWB-CEX"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "740", "ValidTo": "740", "Number": "2541236 ", "URL": "/notes/2541236 ", "Title": "DDIC support in SNOTE - handling of note in SPDD", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "740", "ValidTo": "740", "Number": "2568276 ", "URL": "/notes/2568276 ", "Title": "Remote/update function modules in SNOTE correction instructions", "Component": "BC-DWB-TOO-FUB"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "740", "ValidTo": "740", "Number": "2589309 ", "URL": "/notes/2589309 ", "Title": "Fixes to reimplementation handling - Ignore TADIR for new objects and invalid pre-requisite Notes", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "740", "ValidTo": "740", "Number": "2597808 ", "URL": "/notes/2597808 ", "Title": "Table Type (TTYD): Delta change to primary key not calculated and applied correctly", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "740", "ValidTo": "740", "Number": "2598809 ", "URL": "/notes/2598809 ", "Title": "Table definition (TABD): .APPEND are included for delta calculation and implementation", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "740", "ValidTo": "740", "Number": "2606986 ", "URL": "/notes/2606986 ", "Title": "Upgrade: You cannot reset obsolete SAP Notes in transaction SPAU", "Component": "BC-DWB-CEX"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "740", "ValidTo": "740", "Number": "2617883 ", "URL": "/notes/2617883 ", "Title": "TLOG object read during SPDD phase", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "740", "ValidTo": "740", "Number": "2623459 ", "URL": "/notes/2623459 ", "Title": "Unable to read Correction instruction (Unknown Format)", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "740", "ValidTo": "740", "Number": "2624337 ", "URL": "/notes/2624337 ", "Title": "SNOTE - Note re implementation issue due to object support in newer SP's", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "740", "ValidTo": "740", "Number": "2671774 ", "URL": "/notes/2671774 ", "Title": "Error during Note implementation: Unable to find delivery event <delivery event number>", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "740", "ValidTo": "740", "Number": "2691847 ", "URL": "/notes/2691847 ", "Title": "Previously inactive object activated when current implementation of a note for the same object is cancelled", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "740", "ValidTo": "740", "Number": "2697766 ", "URL": "/notes/2697766 ", "Title": "SNOTE: Runtime Error CONVT_DATA_LOSS occurs while downloading/uploading SAP Notes", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "740", "ValidTo": "740", "Number": "2715783 ", "URL": "/notes/2715783 ", "Title": "Note Text is Empty in SPAU", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "740", "ValidTo": "740", "Number": "2739641 ", "URL": "/notes/2739641 ", "Title": "ABAP Short Dump in SPDD phase during DDIC Note adjustment", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "740", "ValidTo": "740", "Number": "2757237 ", "URL": "/notes/2757237 ", "Title": "Reimplementation of note in SPDD and SPAU with no object change in CI", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "740", "ValidTo": "740", "Number": "2764725 ", "URL": "/notes/2764725 ", "Title": "Fix to reimplementation handling - avoid successor de-implementation during admin change", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "740", "ValidTo": "740", "Number": "2765308 ", "URL": "/notes/2765308 ", "Title": "SNOTE: Support for re-implementation of DDIC Correction Instructions", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "740", "ValidTo": "740", "Number": "2770960 ", "URL": "/notes/2770960 ", "Title": "SNOTE: Handling DDIC inactive objects", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "740", "ValidTo": "740", "Number": "2773977 ", "URL": "/notes/2773977 ", "Title": "SNOTE :Syntax error after implementing changes of Class using SNOTE", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "740", "ValidTo": "740", "Number": "2775698 ", "URL": "/notes/2775698 ", "Title": "Correction instruction creation ignores the local class definition, implementation, definition deffered, definition load , definition local friends changes", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "740", "ValidTo": "740", "Number": "2792897 ", "URL": "/notes/2792897 ", "Title": "SNOTE - Deimplementation fails if any unsupported CI is identified", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "740", "ValidTo": "740", "Number": "2799582 ", "URL": "/notes/2799582 ", "Title": "SNOTE: Correction Instruction Queue Display Issue", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "740", "ValidTo": "740", "Number": "2802126 ", "URL": "/notes/2802126 ", "Title": "SNOTE: Manual Activity container size fix", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "740", "ValidTo": "740", "Number": "2810041 ", "URL": "/notes/2810041 ", "Title": "SNOTE: Note download fails when unsupported object type in SNOTE is identified as a supported type", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "740", "ValidTo": "740", "Number": "2844646 ", "URL": "/notes/2844646 ", "Title": "Ignore the local class definition, implementation, definition deffered, definition load , definition local friends changes", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "740", "ValidTo": "740", "Number": "2860125 ", "URL": "/notes/2860125 ", "Title": "SNOTE - Handling unsaved objects to avoid dump", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "740", "ValidTo": "740", "Number": "2910608 ", "URL": "/notes/2910608 ", "Title": "Issue with applying changes to 'TYPES', 'DATA' or 'CONSTANTS' within ABAP Class definition", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "740", "ValidTo": "740", "Number": "2930611 ", "URL": "/notes/2930611 ", "Title": "SCWN_NOTE_STORE Fix for Efficient Note download processing time", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "740", "ValidTo": "740", "Number": "3001279 ", "URL": "/notes/3001279 ", "Title": "Issues when implementing or de-implementing SAP Notes containing changes on Data Definitions", "Component": "BC-DWB-DIC-ED"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "740", "ValidTo": "740", "Number": "3008844 ", "URL": "/notes/3008844 ", "Title": "SNOTE: Exception handling during de-implementation while reading the history content", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "740", "ValidTo": "740", "Number": "3079593 ", "URL": "/notes/3079593 ", "Title": "Note download gives 'Error in serialization' in Non-Unicode system", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "740", "ValidTo": "740", "Number": "3085447 ", "URL": "/notes/3085447 ", "Title": "Class Deletion Issue While De-implementing the SAP Note", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "740", "ValidTo": "740", "Number": "3236648 ", "URL": "/notes/3236648 ", "Title": "Note Assistant (SNOTE): Move cast exceptions while displaying without delta objects in SNOTE/SNOTE_OLD and CI display", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "740", "ValidTo": "740", "Number": "3262610 ", "URL": "/notes/3262610 ", "Title": "SNOTE: Fix in handling of re-implementation of SAP Note in case already implemented successor SAP Note is incomplete", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "740", "ValidTo": "740", "Number": "3262964 ", "URL": "/notes/3262964 ", "Title": "SAP Note download using Download Service: Fix for not properly waiting for download completion", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "740", "ValidTo": "740", "Number": "3275194 ", "URL": "/notes/3275194 ", "Title": "INDX: De-implementation issue with SNOTE", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "740", "ValidTo": "740", "Number": "3289899 ", "URL": "/notes/3289899 ", "Title": "TLOGO: Enhancements to handle the deletion scenario via SNOTE", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "740", "ValidTo": "740", "Number": "3379455 ", "URL": "/notes/3379455 ", "Title": "SNOTE: Fix to show the correct pre-requisite SAP Note in the message \" SAP Note 0000000000 incomplete\" during SAP Note implementation", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "740", "ValidTo": "740", "Number": "3379660 ", "URL": "/notes/3379660 ", "Title": "DOCU: Stop Version Creation for LANG DOCU Object.", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "740", "ValidTo": "755", "Number": "3047860 ", "URL": "/notes/3047860 ", "Title": "SNOTE: Error in handling of transactions", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "740", "ValidTo": "757", "Number": "3289899 ", "URL": "/notes/3289899 ", "Title": "TLOGO: Enhancements to handle the deletion scenario via SNOTE", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "750", "ValidTo": "750", "Number": "2248091 ", "URL": "/notes/2248091 ", "Title": "Change to reimplementation handling", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "750", "ValidTo": "750", "Number": "2254096 ", "URL": "/notes/2254096 ", "Title": "Dump during archiving VERSIONS in SARA transaction", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "750", "ValidTo": "750", "Number": "2254211 ", "URL": "/notes/2254211 ", "Title": "Fix for new reimplementation handling in SPAU", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "750", "ValidTo": "750", "Number": "2264123 ", "URL": "/notes/2264123 ", "Title": "Stopping deimplementation of obsolete notes", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "750", "ValidTo": "750", "Number": "2292923 ", "URL": "/notes/2292923 ", "Title": "Removing dependencies from the deleted CIs of a note", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "750", "ValidTo": "750", "Number": "2314876 ", "URL": "/notes/2314876 ", "Title": "Fix for a dump in reimplementation handling", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "750", "ValidTo": "750", "Number": "2328318 ", "URL": "/notes/2328318 ", "Title": "SNOTE: Avoid Obsolete Note Deimplementation", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "750", "ValidTo": "750", "Number": "2368460 ", "URL": "/notes/2368460 ", "Title": "DDIC: Database system queries in database migration scenarios", "Component": "BC-DWB-DIC"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "750", "ValidTo": "750", "Number": "2396769 ", "URL": "/notes/2396769 ", "Title": "DDLS: Unpacking CDS source code truncates the source text and an internal comment string", "Component": "BC-DWB-DIC"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "750", "ValidTo": "750", "Number": "2398161 ", "URL": "/notes/2398161 ", "Title": "System Dumps when opening a note in PDF", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "750", "ValidTo": "750", "Number": "2411418 ", "URL": "/notes/2411418 ", "Title": "Identifying TCI in old release where SAP Note 1995550 is not implemented", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "750", "ValidTo": "750", "Number": "2459558 ", "URL": "/notes/2459558 ", "Title": "Supported object type check in snote", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "750", "ValidTo": "750", "Number": "2478661 ", "URL": "/notes/2478661 ", "Title": "Deletion of CDS views using transaction SCWB", "Component": "BC-DWB-DIC-AC"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "750", "ValidTo": "750", "Number": "2536585 ", "URL": "/notes/2536585 ", "Title": "Upgrade: Missing SAP Notes in SPDD and SPAU", "Component": "BC-DWB-CEX"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "750", "ValidTo": "750", "Number": "2541236 ", "URL": "/notes/2541236 ", "Title": "DDIC support in SNOTE - handling of note in SPDD", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "750", "ValidTo": "750", "Number": "2589309 ", "URL": "/notes/2589309 ", "Title": "Fixes to reimplementation handling - Ignore TADIR for new objects and invalid pre-requisite Notes", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "750", "ValidTo": "750", "Number": "2597808 ", "URL": "/notes/2597808 ", "Title": "Table Type (TTYD): Delta change to primary key not calculated and applied correctly", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "750", "ValidTo": "750", "Number": "2598809 ", "URL": "/notes/2598809 ", "Title": "Table definition (TABD): .APPEND are included for delta calculation and implementation", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "750", "ValidTo": "750", "Number": "2606986 ", "URL": "/notes/2606986 ", "Title": "Upgrade: You cannot reset obsolete SAP Notes in transaction SPAU", "Component": "BC-DWB-CEX"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "750", "ValidTo": "750", "Number": "2617883 ", "URL": "/notes/2617883 ", "Title": "TLOG object read during SPDD phase", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "750", "ValidTo": "750", "Number": "2623459 ", "URL": "/notes/2623459 ", "Title": "Unable to read Correction instruction (Unknown Format)", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "750", "ValidTo": "750", "Number": "2624337 ", "URL": "/notes/2624337 ", "Title": "SNOTE - Note re implementation issue due to object support in newer SP's", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "750", "ValidTo": "750", "Number": "2671774 ", "URL": "/notes/2671774 ", "Title": "Error during Note implementation: Unable to find delivery event <delivery event number>", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "750", "ValidTo": "750", "Number": "2684471 ", "URL": "/notes/2684471 ", "Title": "SNOTE HTML Display: Empty 'Links to Support Packages' section", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "750", "ValidTo": "750", "Number": "2691847 ", "URL": "/notes/2691847 ", "Title": "Previously inactive object activated when current implementation of a note for the same object is cancelled", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "750", "ValidTo": "750", "Number": "2697766 ", "URL": "/notes/2697766 ", "Title": "SNOTE: Runtime Error CONVT_DATA_LOSS occurs while downloading/uploading SAP Notes", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "750", "ValidTo": "750", "Number": "2715783 ", "URL": "/notes/2715783 ", "Title": "Note Text is Empty in SPAU", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "750", "ValidTo": "750", "Number": "2739641 ", "URL": "/notes/2739641 ", "Title": "ABAP Short Dump in SPDD phase during DDIC Note adjustment", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "750", "ValidTo": "750", "Number": "2757237 ", "URL": "/notes/2757237 ", "Title": "Reimplementation of note in SPDD and SPAU with no object change in CI", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "750", "ValidTo": "750", "Number": "2764725 ", "URL": "/notes/2764725 ", "Title": "Fix to reimplementation handling - avoid successor de-implementation during admin change", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "750", "ValidTo": "750", "Number": "2765308 ", "URL": "/notes/2765308 ", "Title": "SNOTE: Support for re-implementation of DDIC Correction Instructions", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "750", "ValidTo": "750", "Number": "2770960 ", "URL": "/notes/2770960 ", "Title": "SNOTE: Handling DDIC inactive objects", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "750", "ValidTo": "750", "Number": "2773977 ", "URL": "/notes/2773977 ", "Title": "SNOTE :Syntax error after implementing changes of Class using SNOTE", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "750", "ValidTo": "750", "Number": "2775698 ", "URL": "/notes/2775698 ", "Title": "Correction instruction creation ignores the local class definition, implementation, definition deffered, definition load , definition local friends changes", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "750", "ValidTo": "750", "Number": "2792897 ", "URL": "/notes/2792897 ", "Title": "SNOTE - Deimplementation fails if any unsupported CI is identified", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "750", "ValidTo": "750", "Number": "2799582 ", "URL": "/notes/2799582 ", "Title": "SNOTE: Correction Instruction Queue Display Issue", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "750", "ValidTo": "750", "Number": "2810041 ", "URL": "/notes/2810041 ", "Title": "SNOTE: Note download fails when unsupported object type in SNOTE is identified as a supported type", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "750", "ValidTo": "750", "Number": "2844646 ", "URL": "/notes/2844646 ", "Title": "Ignore the local class definition, implementation, definition deffered, definition load , definition local friends changes", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "750", "ValidTo": "750", "Number": "2860125 ", "URL": "/notes/2860125 ", "Title": "SNOTE - Handling unsaved objects to avoid dump", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "750", "ValidTo": "750", "Number": "2910608 ", "URL": "/notes/2910608 ", "Title": "Issue with applying changes to 'TYPES', 'DATA' or 'CONSTANTS' within ABAP Class definition", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "750", "ValidTo": "750", "Number": "2930611 ", "URL": "/notes/2930611 ", "Title": "SCWN_NOTE_STORE Fix for Efficient Note download processing time", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "750", "ValidTo": "750", "Number": "2953369 ", "URL": "/notes/2953369 ", "Title": "Handling of Incorrect Adjustment Status of Note in SPDD", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "750", "ValidTo": "750", "Number": "2958954 ", "URL": "/notes/2958954 ", "Title": "SNOTE: Note Implementation fails during ZDO Upgrade", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "750", "ValidTo": "750", "Number": "3001279 ", "URL": "/notes/3001279 ", "Title": "Issues when implementing or de-implementing SAP Notes containing changes on Data Definitions", "Component": "BC-DWB-DIC-ED"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "750", "ValidTo": "750", "Number": "3008844 ", "URL": "/notes/3008844 ", "Title": "SNOTE: Exception handling during de-implementation while reading the history content", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "750", "ValidTo": "750", "Number": "3079593 ", "URL": "/notes/3079593 ", "Title": "Note download gives 'Error in serialization' in Non-Unicode system", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "750", "ValidTo": "750", "Number": "3085447 ", "URL": "/notes/3085447 ", "Title": "Class Deletion Issue While De-implementing the SAP Note", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "750", "ValidTo": "750", "Number": "3236648 ", "URL": "/notes/3236648 ", "Title": "Note Assistant (SNOTE): Move cast exceptions while displaying without delta objects in SNOTE/SNOTE_OLD and CI display", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "750", "ValidTo": "750", "Number": "3262610 ", "URL": "/notes/3262610 ", "Title": "SNOTE: Fix in handling of re-implementation of SAP Note in case already implemented successor SAP Note is incomplete", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "750", "ValidTo": "750", "Number": "3262964 ", "URL": "/notes/3262964 ", "Title": "SAP Note download using Download Service: Fix for not properly waiting for download completion", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "750", "ValidTo": "750", "Number": "3275194 ", "URL": "/notes/3275194 ", "Title": "INDX: De-implementation issue with SNOTE", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "750", "ValidTo": "750", "Number": "3289899 ", "URL": "/notes/3289899 ", "Title": "TLOGO: Enhancements to handle the deletion scenario via SNOTE", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "750", "ValidTo": "750", "Number": "3379455 ", "URL": "/notes/3379455 ", "Title": "SNOTE: Fix to show the correct pre-requisite SAP Note in the message \" SAP Note 0000000000 incomplete\" during SAP Note implementation", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "750", "ValidTo": "750", "Number": "3379660 ", "URL": "/notes/3379660 ", "Title": "DOCU: Stop Version Creation for LANG DOCU Object.", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "750", "ValidTo": "751", "Number": "3123184 ", "URL": "/notes/3123184 ", "Title": "Information in Transaction SNOTE Regarding SNOTE Revamp Availability", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "750", "ValidTo": "752", "Number": "2568276 ", "URL": "/notes/2568276 ", "Title": "Remote/update function modules in SNOTE correction instructions", "Component": "BC-DWB-TOO-FUB"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "750", "ValidTo": "753", "Number": "3324076 ", "URL": "/notes/3324076 ", "Title": "Translations are getting deleted during SAP Note Implementation", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "750", "ValidTo": "754", "Number": "3324076 ", "URL": "/notes/3324076 ", "Title": "Translations are getting deleted during SAP Note Implementation", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "751", "ValidTo": "751", "Number": "2368460 ", "URL": "/notes/2368460 ", "Title": "DDIC: Database system queries in database migration scenarios", "Component": "BC-DWB-DIC"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "751", "ValidTo": "751", "Number": "2396769 ", "URL": "/notes/2396769 ", "Title": "DDLS: Unpacking CDS source code truncates the source text and an internal comment string", "Component": "BC-DWB-DIC"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "751", "ValidTo": "751", "Number": "2398161 ", "URL": "/notes/2398161 ", "Title": "System Dumps when opening a note in PDF", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "751", "ValidTo": "751", "Number": "2411418 ", "URL": "/notes/2411418 ", "Title": "Identifying TCI in old release where SAP Note 1995550 is not implemented", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "751", "ValidTo": "751", "Number": "2459558 ", "URL": "/notes/2459558 ", "Title": "Supported object type check in snote", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "751", "ValidTo": "751", "Number": "2478661 ", "URL": "/notes/2478661 ", "Title": "Deletion of CDS views using transaction SCWB", "Component": "BC-DWB-DIC-AC"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "751", "ValidTo": "751", "Number": "2536585 ", "URL": "/notes/2536585 ", "Title": "Upgrade: Missing SAP Notes in SPDD and SPAU", "Component": "BC-DWB-CEX"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "751", "ValidTo": "751", "Number": "2541236 ", "URL": "/notes/2541236 ", "Title": "DDIC support in SNOTE - handling of note in SPDD", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "751", "ValidTo": "751", "Number": "2589309 ", "URL": "/notes/2589309 ", "Title": "Fixes to reimplementation handling - Ignore TADIR for new objects and invalid pre-requisite Notes", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "751", "ValidTo": "751", "Number": "2597808 ", "URL": "/notes/2597808 ", "Title": "Table Type (TTYD): Delta change to primary key not calculated and applied correctly", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "751", "ValidTo": "751", "Number": "2598809 ", "URL": "/notes/2598809 ", "Title": "Table definition (TABD): .APPEND are included for delta calculation and implementation", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "751", "ValidTo": "751", "Number": "2606986 ", "URL": "/notes/2606986 ", "Title": "Upgrade: You cannot reset obsolete SAP Notes in transaction SPAU", "Component": "BC-DWB-CEX"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "751", "ValidTo": "751", "Number": "2617883 ", "URL": "/notes/2617883 ", "Title": "TLOG object read during SPDD phase", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "751", "ValidTo": "751", "Number": "2623459 ", "URL": "/notes/2623459 ", "Title": "Unable to read Correction instruction (Unknown Format)", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "751", "ValidTo": "751", "Number": "2624337 ", "URL": "/notes/2624337 ", "Title": "SNOTE - Note re implementation issue due to object support in newer SP's", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "751", "ValidTo": "751", "Number": "2671774 ", "URL": "/notes/2671774 ", "Title": "Error during Note implementation: Unable to find delivery event <delivery event number>", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "751", "ValidTo": "751", "Number": "2684471 ", "URL": "/notes/2684471 ", "Title": "SNOTE HTML Display: Empty 'Links to Support Packages' section", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "751", "ValidTo": "751", "Number": "2691847 ", "URL": "/notes/2691847 ", "Title": "Previously inactive object activated when current implementation of a note for the same object is cancelled", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "751", "ValidTo": "751", "Number": "2697766 ", "URL": "/notes/2697766 ", "Title": "SNOTE: Runtime Error CONVT_DATA_LOSS occurs while downloading/uploading SAP Notes", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "751", "ValidTo": "751", "Number": "2715783 ", "URL": "/notes/2715783 ", "Title": "Note Text is Empty in SPAU", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "751", "ValidTo": "751", "Number": "2739641 ", "URL": "/notes/2739641 ", "Title": "ABAP Short Dump in SPDD phase during DDIC Note adjustment", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "751", "ValidTo": "751", "Number": "2757237 ", "URL": "/notes/2757237 ", "Title": "Reimplementation of note in SPDD and SPAU with no object change in CI", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "751", "ValidTo": "751", "Number": "2764725 ", "URL": "/notes/2764725 ", "Title": "Fix to reimplementation handling - avoid successor de-implementation during admin change", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "751", "ValidTo": "751", "Number": "2765308 ", "URL": "/notes/2765308 ", "Title": "SNOTE: Support for re-implementation of DDIC Correction Instructions", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "751", "ValidTo": "751", "Number": "2770960 ", "URL": "/notes/2770960 ", "Title": "SNOTE: Handling DDIC inactive objects", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "751", "ValidTo": "751", "Number": "2773977 ", "URL": "/notes/2773977 ", "Title": "SNOTE :Syntax error after implementing changes of Class using SNOTE", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "751", "ValidTo": "751", "Number": "2775698 ", "URL": "/notes/2775698 ", "Title": "Correction instruction creation ignores the local class definition, implementation, definition deffered, definition load , definition local friends changes", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "751", "ValidTo": "751", "Number": "2792897 ", "URL": "/notes/2792897 ", "Title": "SNOTE - Deimplementation fails if any unsupported CI is identified", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "751", "ValidTo": "751", "Number": "2799582 ", "URL": "/notes/2799582 ", "Title": "SNOTE: Correction Instruction Queue Display Issue", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "751", "ValidTo": "751", "Number": "2810041 ", "URL": "/notes/2810041 ", "Title": "SNOTE: Note download fails when unsupported object type in SNOTE is identified as a supported type", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "751", "ValidTo": "751", "Number": "2844646 ", "URL": "/notes/2844646 ", "Title": "Ignore the local class definition, implementation, definition deffered, definition load , definition local friends changes", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "751", "ValidTo": "751", "Number": "2860125 ", "URL": "/notes/2860125 ", "Title": "SNOTE - Handling unsaved objects to avoid dump", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "751", "ValidTo": "751", "Number": "2910608 ", "URL": "/notes/2910608 ", "Title": "Issue with applying changes to 'TYPES', 'DATA' or 'CONSTANTS' within ABAP Class definition", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "751", "ValidTo": "751", "Number": "2930611 ", "URL": "/notes/2930611 ", "Title": "SCWN_NOTE_STORE Fix for Efficient Note download processing time", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "751", "ValidTo": "751", "Number": "2953369 ", "URL": "/notes/2953369 ", "Title": "Handling of Incorrect Adjustment Status of Note in SPDD", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "751", "ValidTo": "751", "Number": "2958954 ", "URL": "/notes/2958954 ", "Title": "SNOTE: Note Implementation fails during ZDO Upgrade", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "751", "ValidTo": "751", "Number": "3001279 ", "URL": "/notes/3001279 ", "Title": "Issues when implementing or de-implementing SAP Notes containing changes on Data Definitions", "Component": "BC-DWB-DIC-ED"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "751", "ValidTo": "751", "Number": "3008844 ", "URL": "/notes/3008844 ", "Title": "SNOTE: Exception handling during de-implementation while reading the history content", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "751", "ValidTo": "751", "Number": "3025331 ", "URL": "/notes/3025331 ", "Title": "SNOTE: ABAP Dump: \" Unknown column name \"OPERATION\" \" during system upgrade", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "751", "ValidTo": "751", "Number": "3079593 ", "URL": "/notes/3079593 ", "Title": "Note download gives 'Error in serialization' in Non-Unicode system", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "751", "ValidTo": "751", "Number": "3085447 ", "URL": "/notes/3085447 ", "Title": "Class Deletion Issue While De-implementing the SAP Note", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "751", "ValidTo": "751", "Number": "3111925 ", "URL": "/notes/3111925 ", "Title": "Versioning Issue during Upgrade/Update", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "751", "ValidTo": "751", "Number": "3236648 ", "URL": "/notes/3236648 ", "Title": "Note Assistant (SNOTE): Move cast exceptions while displaying without delta objects in SNOTE/SNOTE_OLD and CI display", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "751", "ValidTo": "751", "Number": "3262610 ", "URL": "/notes/3262610 ", "Title": "SNOTE: Fix in handling of re-implementation of SAP Note in case already implemented successor SAP Note is incomplete", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "751", "ValidTo": "751", "Number": "3262964 ", "URL": "/notes/3262964 ", "Title": "SAP Note download using Download Service: Fix for not properly waiting for download completion", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "751", "ValidTo": "751", "Number": "3275194 ", "URL": "/notes/3275194 ", "Title": "INDX: De-implementation issue with SNOTE", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "751", "ValidTo": "751", "Number": "3289899 ", "URL": "/notes/3289899 ", "Title": "TLOGO: Enhancements to handle the deletion scenario via SNOTE", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "751", "ValidTo": "751", "Number": "3379455 ", "URL": "/notes/3379455 ", "Title": "SNOTE: Fix to show the correct pre-requisite SAP Note in the message \" SAP Note 0000000000 incomplete\" during SAP Note implementation", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "751", "ValidTo": "751", "Number": "3379660 ", "URL": "/notes/3379660 ", "Title": "DOCU: Stop Version Creation for LANG DOCU Object.", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "751", "ValidTo": "756", "Number": "3111925 ", "URL": "/notes/3111925 ", "Title": "Versioning Issue during Upgrade/Update", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "752", "ValidTo": "752", "Number": "2411418 ", "URL": "/notes/2411418 ", "Title": "Identifying TCI in old release where SAP Note 1995550 is not implemented", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "752", "ValidTo": "752", "Number": "2536585 ", "URL": "/notes/2536585 ", "Title": "Upgrade: Missing SAP Notes in SPDD and SPAU", "Component": "BC-DWB-CEX"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "752", "ValidTo": "752", "Number": "2541236 ", "URL": "/notes/2541236 ", "Title": "DDIC support in SNOTE - handling of note in SPDD", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "752", "ValidTo": "752", "Number": "2589309 ", "URL": "/notes/2589309 ", "Title": "Fixes to reimplementation handling - Ignore TADIR for new objects and invalid pre-requisite Notes", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "752", "ValidTo": "752", "Number": "2597808 ", "URL": "/notes/2597808 ", "Title": "Table Type (TTYD): Delta change to primary key not calculated and applied correctly", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "752", "ValidTo": "752", "Number": "2598809 ", "URL": "/notes/2598809 ", "Title": "Table definition (TABD): .APPEND are included for delta calculation and implementation", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "752", "ValidTo": "752", "Number": "2606986 ", "URL": "/notes/2606986 ", "Title": "Upgrade: You cannot reset obsolete SAP Notes in transaction SPAU", "Component": "BC-DWB-CEX"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "752", "ValidTo": "752", "Number": "2617883 ", "URL": "/notes/2617883 ", "Title": "TLOG object read during SPDD phase", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "752", "ValidTo": "752", "Number": "2624337 ", "URL": "/notes/2624337 ", "Title": "SNOTE - Note re implementation issue due to object support in newer SP's", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "752", "ValidTo": "752", "Number": "2671774 ", "URL": "/notes/2671774 ", "Title": "Error during Note implementation: Unable to find delivery event <delivery event number>", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "752", "ValidTo": "752", "Number": "2684471 ", "URL": "/notes/2684471 ", "Title": "SNOTE HTML Display: Empty 'Links to Support Packages' section", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "752", "ValidTo": "752", "Number": "2691847 ", "URL": "/notes/2691847 ", "Title": "Previously inactive object activated when current implementation of a note for the same object is cancelled", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "752", "ValidTo": "752", "Number": "2697766 ", "URL": "/notes/2697766 ", "Title": "SNOTE: Runtime Error CONVT_DATA_LOSS occurs while downloading/uploading SAP Notes", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "752", "ValidTo": "752", "Number": "2715783 ", "URL": "/notes/2715783 ", "Title": "Note Text is Empty in SPAU", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "752", "ValidTo": "752", "Number": "2739641 ", "URL": "/notes/2739641 ", "Title": "ABAP Short Dump in SPDD phase during DDIC Note adjustment", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "752", "ValidTo": "752", "Number": "2757237 ", "URL": "/notes/2757237 ", "Title": "Reimplementation of note in SPDD and SPAU with no object change in CI", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "752", "ValidTo": "752", "Number": "2764725 ", "URL": "/notes/2764725 ", "Title": "Fix to reimplementation handling - avoid successor de-implementation during admin change", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "752", "ValidTo": "752", "Number": "2765308 ", "URL": "/notes/2765308 ", "Title": "SNOTE: Support for re-implementation of DDIC Correction Instructions", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "752", "ValidTo": "752", "Number": "2770960 ", "URL": "/notes/2770960 ", "Title": "SNOTE: Handling DDIC inactive objects", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "752", "ValidTo": "752", "Number": "2773977 ", "URL": "/notes/2773977 ", "Title": "SNOTE :Syntax error after implementing changes of Class using SNOTE", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "752", "ValidTo": "752", "Number": "2775698 ", "URL": "/notes/2775698 ", "Title": "Correction instruction creation ignores the local class definition, implementation, definition deffered, definition load , definition local friends changes", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "752", "ValidTo": "752", "Number": "2792897 ", "URL": "/notes/2792897 ", "Title": "SNOTE - Deimplementation fails if any unsupported CI is identified", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "752", "ValidTo": "752", "Number": "2799582 ", "URL": "/notes/2799582 ", "Title": "SNOTE: Correction Instruction Queue Display Issue", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "752", "ValidTo": "752", "Number": "2810041 ", "URL": "/notes/2810041 ", "Title": "SNOTE: Note download fails when unsupported object type in SNOTE is identified as a supported type", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "752", "ValidTo": "752", "Number": "2844646 ", "URL": "/notes/2844646 ", "Title": "Ignore the local class definition, implementation, definition deffered, definition load , definition local friends changes", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "752", "ValidTo": "752", "Number": "2860125 ", "URL": "/notes/2860125 ", "Title": "SNOTE - Handling unsaved objects to avoid dump", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "752", "ValidTo": "752", "Number": "2910608 ", "URL": "/notes/2910608 ", "Title": "Issue with applying changes to 'TYPES', 'DATA' or 'CONSTANTS' within ABAP Class definition", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "752", "ValidTo": "752", "Number": "2930611 ", "URL": "/notes/2930611 ", "Title": "SCWN_NOTE_STORE Fix for Efficient Note download processing time", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "752", "ValidTo": "752", "Number": "2953369 ", "URL": "/notes/2953369 ", "Title": "Handling of Incorrect Adjustment Status of Note in SPDD", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "752", "ValidTo": "752", "Number": "2958954 ", "URL": "/notes/2958954 ", "Title": "SNOTE: Note Implementation fails during ZDO Upgrade", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "752", "ValidTo": "752", "Number": "3001279 ", "URL": "/notes/3001279 ", "Title": "Issues when implementing or de-implementing SAP Notes containing changes on Data Definitions", "Component": "BC-DWB-DIC-ED"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "752", "ValidTo": "752", "Number": "3008844 ", "URL": "/notes/3008844 ", "Title": "SNOTE: Exception handling during de-implementation while reading the history content", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "752", "ValidTo": "752", "Number": "3025331 ", "URL": "/notes/3025331 ", "Title": "SNOTE: ABAP Dump: \" Unknown column name \"OPERATION\" \" during system upgrade", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "752", "ValidTo": "752", "Number": "3079593 ", "URL": "/notes/3079593 ", "Title": "Note download gives 'Error in serialization' in Non-Unicode system", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "752", "ValidTo": "752", "Number": "3085447 ", "URL": "/notes/3085447 ", "Title": "Class Deletion Issue While De-implementing the SAP Note", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "752", "ValidTo": "752", "Number": "3111925 ", "URL": "/notes/3111925 ", "Title": "Versioning Issue during Upgrade/Update", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "752", "ValidTo": "752", "Number": "3123184 ", "URL": "/notes/3123184 ", "Title": "Information in Transaction SNOTE Regarding SNOTE Revamp Availability", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "752", "ValidTo": "752", "Number": "3236648 ", "URL": "/notes/3236648 ", "Title": "Note Assistant (SNOTE): Move cast exceptions while displaying without delta objects in SNOTE/SNOTE_OLD and CI display", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "752", "ValidTo": "752", "Number": "3262610 ", "URL": "/notes/3262610 ", "Title": "SNOTE: Fix in handling of re-implementation of SAP Note in case already implemented successor SAP Note is incomplete", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "752", "ValidTo": "752", "Number": "3262964 ", "URL": "/notes/3262964 ", "Title": "SAP Note download using Download Service: Fix for not properly waiting for download completion", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "752", "ValidTo": "752", "Number": "3275194 ", "URL": "/notes/3275194 ", "Title": "INDX: De-implementation issue with SNOTE", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "752", "ValidTo": "752", "Number": "3289899 ", "URL": "/notes/3289899 ", "Title": "TLOGO: Enhancements to handle the deletion scenario via SNOTE", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "752", "ValidTo": "752", "Number": "3379455 ", "URL": "/notes/3379455 ", "Title": "SNOTE: Fix to show the correct pre-requisite SAP Note in the message \" SAP Note 0000000000 incomplete\" during SAP Note implementation", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "752", "ValidTo": "752", "Number": "3379660 ", "URL": "/notes/3379660 ", "Title": "DOCU: Stop Version Creation for LANG DOCU Object.", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "753", "ValidTo": "753", "Number": "2589309 ", "URL": "/notes/2589309 ", "Title": "Fixes to reimplementation handling - Ignore TADIR for new objects and invalid pre-requisite Notes", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "753", "ValidTo": "753", "Number": "2624337 ", "URL": "/notes/2624337 ", "Title": "SNOTE - Note re implementation issue due to object support in newer SP's", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "753", "ValidTo": "753", "Number": "2671774 ", "URL": "/notes/2671774 ", "Title": "Error during Note implementation: Unable to find delivery event <delivery event number>", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "753", "ValidTo": "753", "Number": "2684471 ", "URL": "/notes/2684471 ", "Title": "SNOTE HTML Display: Empty 'Links to Support Packages' section", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "753", "ValidTo": "753", "Number": "2691847 ", "URL": "/notes/2691847 ", "Title": "Previously inactive object activated when current implementation of a note for the same object is cancelled", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "753", "ValidTo": "753", "Number": "2697766 ", "URL": "/notes/2697766 ", "Title": "SNOTE: Runtime Error CONVT_DATA_LOSS occurs while downloading/uploading SAP Notes", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "753", "ValidTo": "753", "Number": "2715783 ", "URL": "/notes/2715783 ", "Title": "Note Text is Empty in SPAU", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "753", "ValidTo": "753", "Number": "2730170 ", "URL": "/notes/2730170 ", "Title": "SCWB/SNOTE Activation processes Methods in DDIC Phase", "Component": "BC-DWB-UTL-BRR"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "753", "ValidTo": "753", "Number": "2739641 ", "URL": "/notes/2739641 ", "Title": "ABAP Short Dump in SPDD phase during DDIC Note adjustment", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "753", "ValidTo": "753", "Number": "2757237 ", "URL": "/notes/2757237 ", "Title": "Reimplementation of note in SPDD and SPAU with no object change in CI", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "753", "ValidTo": "753", "Number": "2764725 ", "URL": "/notes/2764725 ", "Title": "Fix to reimplementation handling - avoid successor de-implementation during admin change", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "753", "ValidTo": "753", "Number": "2765308 ", "URL": "/notes/2765308 ", "Title": "SNOTE: Support for re-implementation of DDIC Correction Instructions", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "753", "ValidTo": "753", "Number": "2770960 ", "URL": "/notes/2770960 ", "Title": "SNOTE: Handling DDIC inactive objects", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "753", "ValidTo": "753", "Number": "2773977 ", "URL": "/notes/2773977 ", "Title": "SNOTE :Syntax error after implementing changes of Class using SNOTE", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "753", "ValidTo": "753", "Number": "2775698 ", "URL": "/notes/2775698 ", "Title": "Correction instruction creation ignores the local class definition, implementation, definition deffered, definition load , definition local friends changes", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "753", "ValidTo": "753", "Number": "2792897 ", "URL": "/notes/2792897 ", "Title": "SNOTE - Deimplementation fails if any unsupported CI is identified", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "753", "ValidTo": "753", "Number": "2799582 ", "URL": "/notes/2799582 ", "Title": "SNOTE: Correction Instruction Queue Display Issue", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "753", "ValidTo": "753", "Number": "2802126 ", "URL": "/notes/2802126 ", "Title": "SNOTE: Manual Activity container size fix", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "753", "ValidTo": "753", "Number": "2810041 ", "URL": "/notes/2810041 ", "Title": "SNOTE: Note download fails when unsupported object type in SNOTE is identified as a supported type", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "753", "ValidTo": "753", "Number": "2844646 ", "URL": "/notes/2844646 ", "Title": "Ignore the local class definition, implementation, definition deffered, definition load , definition local friends changes", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "753", "ValidTo": "753", "Number": "2860125 ", "URL": "/notes/2860125 ", "Title": "SNOTE - Handling unsaved objects to avoid dump", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "753", "ValidTo": "753", "Number": "2910608 ", "URL": "/notes/2910608 ", "Title": "Issue with applying changes to 'TYPES', 'DATA' or 'CONSTANTS' within ABAP Class definition", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "753", "ValidTo": "753", "Number": "2930611 ", "URL": "/notes/2930611 ", "Title": "SCWN_NOTE_STORE Fix for Efficient Note download processing time", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "753", "ValidTo": "753", "Number": "2953369 ", "URL": "/notes/2953369 ", "Title": "Handling of Incorrect Adjustment Status of Note in SPDD", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "753", "ValidTo": "753", "Number": "2958954 ", "URL": "/notes/2958954 ", "Title": "SNOTE: Note Implementation fails during ZDO Upgrade", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "753", "ValidTo": "753", "Number": "3001279 ", "URL": "/notes/3001279 ", "Title": "Issues when implementing or de-implementing SAP Notes containing changes on Data Definitions", "Component": "BC-DWB-DIC-ED"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "753", "ValidTo": "753", "Number": "3008844 ", "URL": "/notes/3008844 ", "Title": "SNOTE: Exception handling during de-implementation while reading the history content", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "753", "ValidTo": "753", "Number": "3025331 ", "URL": "/notes/3025331 ", "Title": "SNOTE: ABAP Dump: \" Unknown column name \"OPERATION\" \" during system upgrade", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "753", "ValidTo": "753", "Number": "3079593 ", "URL": "/notes/3079593 ", "Title": "Note download gives 'Error in serialization' in Non-Unicode system", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "753", "ValidTo": "753", "Number": "3085447 ", "URL": "/notes/3085447 ", "Title": "Class Deletion Issue While De-implementing the SAP Note", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "753", "ValidTo": "753", "Number": "3111925 ", "URL": "/notes/3111925 ", "Title": "Versioning Issue during Upgrade/Update", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "753", "ValidTo": "753", "Number": "3123184 ", "URL": "/notes/3123184 ", "Title": "Information in Transaction SNOTE Regarding SNOTE Revamp Availability", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "753", "ValidTo": "753", "Number": "3236648 ", "URL": "/notes/3236648 ", "Title": "Note Assistant (SNOTE): Move cast exceptions while displaying without delta objects in SNOTE/SNOTE_OLD and CI display", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "753", "ValidTo": "753", "Number": "3262610 ", "URL": "/notes/3262610 ", "Title": "SNOTE: Fix in handling of re-implementation of SAP Note in case already implemented successor SAP Note is incomplete", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "753", "ValidTo": "753", "Number": "3262964 ", "URL": "/notes/3262964 ", "Title": "SAP Note download using Download Service: Fix for not properly waiting for download completion", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "753", "ValidTo": "753", "Number": "3275194 ", "URL": "/notes/3275194 ", "Title": "INDX: De-implementation issue with SNOTE", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "753", "ValidTo": "753", "Number": "3289899 ", "URL": "/notes/3289899 ", "Title": "TLOGO: Enhancements to handle the deletion scenario via SNOTE", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "753", "ValidTo": "753", "Number": "3324076 ", "URL": "/notes/3324076 ", "Title": "Translations are getting deleted during SAP Note Implementation", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "753", "ValidTo": "753", "Number": "3379455 ", "URL": "/notes/3379455 ", "Title": "SNOTE: Fix to show the correct pre-requisite SAP Note in the message \" SAP Note 0000000000 incomplete\" during SAP Note implementation", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "753", "ValidTo": "753", "Number": "3379660 ", "URL": "/notes/3379660 ", "Title": "DOCU: Stop Version Creation for LANG DOCU Object.", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "754", "ValidTo": "754", "Number": "2589309 ", "URL": "/notes/2589309 ", "Title": "Fixes to reimplementation handling - Ignore TADIR for new objects and invalid pre-requisite Notes", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "754", "ValidTo": "754", "Number": "2799582 ", "URL": "/notes/2799582 ", "Title": "SNOTE: Correction Instruction Queue Display Issue", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "754", "ValidTo": "754", "Number": "2802126 ", "URL": "/notes/2802126 ", "Title": "SNOTE: Manual Activity container size fix", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "754", "ValidTo": "754", "Number": "2860125 ", "URL": "/notes/2860125 ", "Title": "SNOTE - Handling unsaved objects to avoid dump", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "754", "ValidTo": "754", "Number": "2910608 ", "URL": "/notes/2910608 ", "Title": "Issue with applying changes to 'TYPES', 'DATA' or 'CONSTANTS' within ABAP Class definition", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "754", "ValidTo": "754", "Number": "2930611 ", "URL": "/notes/2930611 ", "Title": "SCWN_NOTE_STORE Fix for Efficient Note download processing time", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "754", "ValidTo": "754", "Number": "2953369 ", "URL": "/notes/2953369 ", "Title": "Handling of Incorrect Adjustment Status of Note in SPDD", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "754", "ValidTo": "754", "Number": "2958954 ", "URL": "/notes/2958954 ", "Title": "SNOTE: Note Implementation fails during ZDO Upgrade", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "754", "ValidTo": "754", "Number": "3001279 ", "URL": "/notes/3001279 ", "Title": "Issues when implementing or de-implementing SAP Notes containing changes on Data Definitions", "Component": "BC-DWB-DIC-ED"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "754", "ValidTo": "754", "Number": "3008844 ", "URL": "/notes/3008844 ", "Title": "SNOTE: Exception handling during de-implementation while reading the history content", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "754", "ValidTo": "754", "Number": "3041970 ", "URL": "/notes/3041970 ", "Title": "DEVC object  is not being captured as New object in the CI", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "754", "ValidTo": "754", "Number": "3079593 ", "URL": "/notes/3079593 ", "Title": "Note download gives 'Error in serialization' in Non-Unicode system", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "754", "ValidTo": "754", "Number": "3085447 ", "URL": "/notes/3085447 ", "Title": "Class Deletion Issue While De-implementing the SAP Note", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "754", "ValidTo": "754", "Number": "3111925 ", "URL": "/notes/3111925 ", "Title": "Versioning Issue during Upgrade/Update", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "754", "ValidTo": "754", "Number": "3123184 ", "URL": "/notes/3123184 ", "Title": "Information in Transaction SNOTE Regarding SNOTE Revamp Availability", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "754", "ValidTo": "754", "Number": "3236648 ", "URL": "/notes/3236648 ", "Title": "Note Assistant (SNOTE): Move cast exceptions while displaying without delta objects in SNOTE/SNOTE_OLD and CI display", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "754", "ValidTo": "754", "Number": "3262610 ", "URL": "/notes/3262610 ", "Title": "SNOTE: Fix in handling of re-implementation of SAP Note in case already implemented successor SAP Note is incomplete", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "754", "ValidTo": "754", "Number": "3262964 ", "URL": "/notes/3262964 ", "Title": "SAP Note download using Download Service: Fix for not properly waiting for download completion", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "754", "ValidTo": "754", "Number": "3275194 ", "URL": "/notes/3275194 ", "Title": "INDX: De-implementation issue with SNOTE", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "754", "ValidTo": "754", "Number": "3289899 ", "URL": "/notes/3289899 ", "Title": "TLOGO: Enhancements to handle the deletion scenario via SNOTE", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "754", "ValidTo": "754", "Number": "3324076 ", "URL": "/notes/3324076 ", "Title": "Translations are getting deleted during SAP Note Implementation", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "754", "ValidTo": "754", "Number": "3379455 ", "URL": "/notes/3379455 ", "Title": "SNOTE: Fix to show the correct pre-requisite SAP Note in the message \" SAP Note 0000000000 incomplete\" during SAP Note implementation", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "754", "ValidTo": "754", "Number": "3379660 ", "URL": "/notes/3379660 ", "Title": "DOCU: Stop Version Creation for LANG DOCU Object.", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "755", "ValidTo": "755", "Number": "2953369 ", "URL": "/notes/2953369 ", "Title": "Handling of Incorrect Adjustment Status of Note in SPDD", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "755", "ValidTo": "755", "Number": "3001279 ", "URL": "/notes/3001279 ", "Title": "Issues when implementing or de-implementing SAP Notes containing changes on Data Definitions", "Component": "BC-DWB-DIC-ED"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "755", "ValidTo": "755", "Number": "3008844 ", "URL": "/notes/3008844 ", "Title": "SNOTE: Exception handling during de-implementation while reading the history content", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "755", "ValidTo": "755", "Number": "3079593 ", "URL": "/notes/3079593 ", "Title": "Note download gives 'Error in serialization' in Non-Unicode system", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "755", "ValidTo": "755", "Number": "3085447 ", "URL": "/notes/3085447 ", "Title": "Class Deletion Issue While De-implementing the SAP Note", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "755", "ValidTo": "755", "Number": "3111925 ", "URL": "/notes/3111925 ", "Title": "Versioning Issue during Upgrade/Update", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "755", "ValidTo": "755", "Number": "3123184 ", "URL": "/notes/3123184 ", "Title": "Information in Transaction SNOTE Regarding SNOTE Revamp Availability", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "755", "ValidTo": "755", "Number": "3236648 ", "URL": "/notes/3236648 ", "Title": "Note Assistant (SNOTE): Move cast exceptions while displaying without delta objects in SNOTE/SNOTE_OLD and CI display", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "755", "ValidTo": "755", "Number": "3262610 ", "URL": "/notes/3262610 ", "Title": "SNOTE: Fix in handling of re-implementation of SAP Note in case already implemented successor SAP Note is incomplete", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "755", "ValidTo": "755", "Number": "3262964 ", "URL": "/notes/3262964 ", "Title": "SAP Note download using Download Service: Fix for not properly waiting for download completion", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "755", "ValidTo": "755", "Number": "3275194 ", "URL": "/notes/3275194 ", "Title": "INDX: De-implementation issue with SNOTE", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "755", "ValidTo": "755", "Number": "3324076 ", "URL": "/notes/3324076 ", "Title": "Translations are getting deleted during SAP Note Implementation", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "755", "ValidTo": "755", "Number": "3379660 ", "URL": "/notes/3379660 ", "Title": "DOCU: Stop Version Creation for LANG DOCU Object.", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "756", "ValidTo": "756", "Number": "3079593 ", "URL": "/notes/3079593 ", "Title": "Note download gives 'Error in serialization' in Non-Unicode system", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "756", "ValidTo": "756", "Number": "3085447 ", "URL": "/notes/3085447 ", "Title": "Class Deletion Issue While De-implementing the SAP Note", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "756", "ValidTo": "756", "Number": "3111925 ", "URL": "/notes/3111925 ", "Title": "Versioning Issue during Upgrade/Update", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "756", "ValidTo": "756", "Number": "3123184 ", "URL": "/notes/3123184 ", "Title": "Information in Transaction SNOTE Regarding SNOTE Revamp Availability", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "756", "ValidTo": "756", "Number": "3236648 ", "URL": "/notes/3236648 ", "Title": "Note Assistant (SNOTE): Move cast exceptions while displaying without delta objects in SNOTE/SNOTE_OLD and CI display", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "756", "ValidTo": "756", "Number": "3262610 ", "URL": "/notes/3262610 ", "Title": "SNOTE: Fix in handling of re-implementation of SAP Note in case already implemented successor SAP Note is incomplete", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "756", "ValidTo": "756", "Number": "3262964 ", "URL": "/notes/3262964 ", "Title": "SAP Note download using Download Service: Fix for not properly waiting for download completion", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "756", "ValidTo": "756", "Number": "3275194 ", "URL": "/notes/3275194 ", "Title": "INDX: De-implementation issue with SNOTE", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "756", "ValidTo": "756", "Number": "3324076 ", "URL": "/notes/3324076 ", "Title": "Translations are getting deleted during SAP Note Implementation", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "756", "ValidTo": "756", "Number": "3379455 ", "URL": "/notes/3379455 ", "Title": "SNOTE: Fix to show the correct pre-requisite SAP Note in the message \" SAP Note 0000000000 incomplete\" during SAP Note implementation", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "756", "ValidTo": "756", "Number": "3379660 ", "URL": "/notes/3379660 ", "Title": "DOCU: Stop Version Creation for LANG DOCU Object.", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "756", "ValidTo": "757", "Number": "3307778 ", "URL": "/notes/3307778 ", "Title": "Remove SNOTE support for SHI3 Object Type if its incorrectly enabled", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "757", "ValidTo": "757", "Number": "3275194 ", "URL": "/notes/3275194 ", "Title": "INDX: De-implementation issue with SNOTE", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "757", "ValidTo": "757", "Number": "3324076 ", "URL": "/notes/3324076 ", "Title": "Translations are getting deleted during SAP Note Implementation", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "757", "ValidTo": "757", "Number": "3379455 ", "URL": "/notes/3379455 ", "Title": "SNOTE: Fix to show the correct pre-requisite SAP Note in the message \" SAP Note 0000000000 incomplete\" during SAP Note implementation", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "757", "ValidTo": "757", "Number": "3379660 ", "URL": "/notes/3379660 ", "Title": "DOCU: Stop Version Creation for LANG DOCU Object.", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "758", "ValidTo": "758", "Number": "3324076 ", "URL": "/notes/3324076 ", "Title": "Translations are getting deleted during SAP Note Implementation", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "758", "ValidTo": "758", "Number": "3379455 ", "URL": "/notes/3379455 ", "Title": "SNOTE: Fix to show the correct pre-requisite SAP Note in the message \" SAP Note 0000000000 incomplete\" during SAP Note implementation", "Component": "BC-UPG-NA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "758", "ValidTo": "758", "Number": "3379660 ", "URL": "/notes/3379660 ", "Title": "DOCU: Stop Version Creation for LANG DOCU Object.", "Component": "BC-UPG-NA"}]}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2560666", "RefTitle": "SAP Screen Personas 3.0 SP06: Released Notes Information", "RefUrl": "/notes/0002560666"}]}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}