{"Request": {"Number": "2549411", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 637, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000020119772017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0002549411?language=E&token=AA8B33855D7D8891DE825D0A0031896C"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0002549411", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0002549411/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "2549411"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 6}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Program error"}, "Priority": {"_label": "Priority", "value": "Correction with medium priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "22.05.2018"}, "SAPComponentKey": {"_label": "Component", "value": "SV-SMG-MON-BPM-ANA"}, "SAPComponentKeyText": {"_label": "Component", "value": "Analytics"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Service", "value": "SV", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'SV*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "SAP Solution Manager", "value": "SV-SMG", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'SV-SMG*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Monitoring & Alerting", "value": "SV-SMG-MON", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'SV-SMG-MON*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Business Process Operations", "value": "SV-SMG-MON-BPM", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'SV-SMG-MON-BPM*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Analytics", "value": "SV-SMG-MON-BPM-ANA", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'SV-SMG-MON-BPM-ANA*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "2549411 - ST-A/PI 01S SP3: Advance Corr. BP Analytics - TBIs for ERP"}, "LongText": {"_label": "Description", "value": "<h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>1.) Key figures \"Maintenance plans without any call-off orders\",&#160;\"Maintenance plans blocked by incomplete call-off orders\", and&#160;\"Maintenance plans overdue for order call-offs\" might be aborted with a dump GETWA_NOT_ASSIGNED.<br />2.) Key figure \"Old Planned Independent Requirements with MRP list\" wrongly shows also records for which the planned quantity is smaller or equal as the withdrawal quantity. <br />3.) Key figure \"Overdue reservation items with MRP list\" (KWRS000105)&#160;wrongly shows records for which the Quantity Withdrawn (ENMNG) is larger than the requirement Quantity (BDMNG). Furthermore the flag \"Effective&#160;for&#160;Materials&#160;Planning\" (RESB-NO_DISP), the \"Debit/Credit Indicator (RESB-SHKZG)\", the \"Phantom item indicator\" (RESB-DUMPS), the \"Item text indicator\" (RESB-TXTPS), and the \"Indicator: Bulk Material\"&#160;(RESB-SCHGT)&#160;are not checked.<br />4.) Key figure \"Overdue logistics execution requirement items\" (KPLE000303) shows too many entries as MDKP-DTART is not filtered on 'MD' and hence sometimes wrong 'LP' entries are shown as well as wrong duplicates.<br />5.) Key figure \"Insufficient&#160;stock&#160;during&#160;delivery&#160;creation&#160;(sales)\" (KPSD000315) should not only show message of type 'I'. Furthermore the plant is not determined in some cases, and the description text in BP Analytics is not available. <br />6.) Key figure \"Insufficient&#160;stock&#160;during&#160;delivery&#160;creation\" (KPPURCH507) should not only show message of type 'I'.<br />7.) Key figure \"CO orders w/o settlement rule\" (KFFI002303) should exclude statistical orders.<br />8.) In case of the following key figures the number of records might be too small in case documents are created resp changed by users which do not exist in table USR02. Instead of an INNER JOIN there should be a LEFT OUTER JOIN on table USR02:</p>\r\n<p>- Number of FI postings (aggregated) (KFFI000101)<br />- Number of FI line items posted (aggregated) (KFFI000102)<br />- Changes in FI documents (KFFI000104)<br />- Cancelled FI documents (KFFI000105)<br />- Number of FI postings (KFFI000106)<br />- Number of FI line items posted (KFFI000107)<br />- FI Documents manually created (KFFI000141)<br />- Changes to Material Master (KWMD000107)<br />- Changes to customer master (KWMD000203)<br />- Changes to Vendor Master (KWMD000309)<br />- Changes to Purchasing information records (KWMD000310)<br />- Changes in sales documents (KWCID00101)<br />- Changes in PM/CS notifications (KWPM000114)<br />- Changes in PM/CS orders (KWPM000213)<br />- Inspection Lots with outstanding quantities (KWQM000101)<br />- Inspection Lots without Usage Decision (KWQM000102)<br />- Inspection Lots without inspection completion (KWQM000103)<br />- Changes in purchase orders (KPPURCH221)<br />- Changes in purchase requisitions (KPPURCH309)<br />- Changes in shipments (KPLE000207)<br />- Deleted deliveries in shipments (KPLE000208)<br />- Changes in SD billing documents (KPSD000217)<br />- Changes in MM invoices (KPPURCH412)<br />- Manual Condition Changes on SD Document items (KPSD000323)<br />- Changes in sales documents (KPSD000324)<br />- Deleted Items in Sales documents&#160; (KPSD000327)<br />- Changes in deliveries (KPLE000307)<br /><br />9.) Key figures \"Exceptions for open items FI-AP during payment run\" (KFFI000210) resp. \"Exceptions for open items FI-AR during payment run\" (KFFI000307)&#160;do not select the field REGUP-HKONT (General Ledger Account).<br />10.) The following lead time key figures&#160;for PM/CS Orders show an average lead time of zero when executed in ST13: \"LT: PM/CS order creation &gt; release [days]\", \"LT: Creation-&gt;TECO (Prev.Day)[days]\", \"LT: PM/CS order creation &gt; TECO [days]\", \"LT: Release-&gt;TECO (Prev.Day)[days]\", \"LT: PM/CS order release &gt; TECO [days]\".<br />10.) Performance improvements for \"CO usage\" (monitor \"CO postings\", KFFI0023) key figures \"Cost center usage in actual CO line items\", \"\"Profit center usage in actual PCA line items (per CO area), \"Secondary cost element usage in actual CO line items\". To be able to use the fiscal year as parameter, also SAP Note 2600318 - ST-A/PI 01S SP3: Performance improvement of \"CO usage\" key figures (monitor \"CO Postings\") needs to be implemented manually.<br />11.) When using key figure \"Sales document items overdue for billing\" (KPSD0003, 19) it might happen that wrong items of a document are shown in case there are items with different billing relevance. To solve the issue, instead of selecting items with Billing Status for Order-Related Billing Documents VBUP-FKSAA not equal 'C', now those with either 'A' or 'B' are selected.<br />12.) Key figure \"Billing plan dates not billed\" (KPSD0003, 19) shows too many entries.<br />13.) For the master data key figures \"Changes to Material Master\" (KWMD000107), \"Changes to customer master\" (KWMD000203), \"Changes to Vendor Master\" (KWMD000309), resp. \"Changes to Purchasing information records\" (KWMD000310), the parameters \"All changes\" resp. \"Only dialog changes\" do not work.<br /><br /><br /><br /></p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>Business Process Analytics, Business Process Improvement, Business Process Monitoring, BPM, BPMon, /SSA/EKP, /SSA/EKF, /SSA/EKW, /SSA/EXM</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>Program errors</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p>Implement the attached correction instructions</p>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D041053)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON> (D027486)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0002549411/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002549411/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002549411/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002549411/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002549411/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002549411/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002549411/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002549411/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002549411/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2748075", "RefComponent": "SV-SMG-MON-BPM-ANA", "RefTitle": "Message error \"Error while data collection\" in job log while running RC_VALUE_DISCOVERY_COLL_DATA", "RefUrl": "/notes/2748075 "}, {"RefNumber": "2609795", "RefComponent": "SV-SMG-SER-EWA", "RefTitle": "How to enable Business Key Figures (BKF) for EarlyWatch Alert Report (EWA) - Guided Answer", "RefUrl": "/notes/2609795 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "ST-A/PI", "From": "01S_640", "To": "01S_640", "Subsequent": ""}, {"SoftwareComponent": "ST-A/PI", "From": "01S_700", "To": "01S_700", "Subsequent": ""}, {"SoftwareComponent": "ST-A/PI", "From": "01S_710", "To": "01S_710", "Subsequent": ""}, {"SoftwareComponent": "ST-A/PI", "From": "01S_731", "To": "01S_731", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": [{"SoftwareComponent": "ST-A/PI", "NumberOfCorrin": 9, "URL": "/corrins/0002549411/389"}]}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 9, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 1, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": [{"SoftwareComponent": "ST-A/PI", "ValidFrom": "01S_640", "ValidTo": "01S_731", "Number": "2549411 ", "URL": "/notes/2549411 ", "Title": "ST-A/PI 01S SP3: Advance Corr. BP Analytics - TBIs for ERP", "Component": "SV-SMG-MON-BPM-ANA"}]}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}