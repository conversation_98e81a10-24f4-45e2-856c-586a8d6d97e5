[{"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/a4bf5bb1c337d61022195030a0013184", "title": "Queries regarding Custom Code Analysis for S/4HANA 2023 FP01 and FP02", "problem": "The list of impacted custom objects for S/4HANA 2023 FP01 does not overlap with the list for FP02, with only 3 common objects.", "solution": "Explanation that differences are due to changes in specific objects between the runs, such as ZCL_FI_GL_UPLOAD_DPC_EXT and ZFI_REPT_BPAY_CREDIT.", "cause": "Changes made to specific custom code objects between the two analysis runs.", "processed_at": "2025-06-25T10:44:59.955428"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/a4bf5bb1c337d61022195030a0013184", "title": "Queries regarding Custom Code Analysis for S/4HANA 2023 FP01 and FP02", "problem": "Query on whether the list of impacted custom objects for FPS01 should be a subset of FPS02.", "solution": "Explanation that if no changes were made between the runs, the lists should be the same, but differences are possible due to changes in objects.", "cause": "Objects recorded in the simplification DB may have validity restrictions specific to each feature pack, leading to differences in the lists.", "processed_at": "2025-06-25T10:44:59.955443"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/a4bf5bb1c337d61022195030a0013184", "title": "Queries regarding Custom Code Analysis for S/4HANA 2023 FP01 and FP02", "problem": "Confusion over whether ATC checks can be configured for specific feature packs.", "solution": "Clarification that ATC checks cannot be configured for specific feature packs, but only for specific releases.", "cause": "ATC configuration limitation that only allows checks for specific releases, not individual feature packs.", "processed_at": "2025-06-25T10:44:59.955447"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/a4bf5bb1c337d61022195030a0013184", "title": "Queries regarding Custom Code Analysis for S/4HANA 2023 FP01 and FP02", "problem": "Concern that objects flagged for FPS01 might not need consideration if directly upgrading to FPS02.", "solution": "Confirmation that if upgrading directly to FP02, only the impacted custom objects for FP02 need consideration.", "cause": "The distinct custom code impacts derived from FPS02 are relevant for direct upgrades, making FPS01 impacts unnecessary.", "processed_at": "2025-06-25T10:44:59.955450"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/3e3c57b897e75ed8b86630200153af15", "title": "Readiness Check uploaded file is not valid", "problem": "Readiness Check uploaded file is not valid.", "solution": "Verify the file has been downloaded using the report from SAP Readiness Check Data Collector.", "cause": "The customer used the RC_VALUE_DISCOVERY_COLL_DATA report, which is not the Readiness Check report, resulting in a ZIP file not applicable for upload in the Readiness Check portal.", "processed_at": "2025-06-25T10:45:03.419984"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/3e3c57b897e75ed8b86630200153af15", "title": "Readiness Check uploaded file is not valid", "problem": "Mixing up different reports for Readiness Check Analysis.", "solution": "Clarify the intended report and refer to the appropriate guide or SAP Note. For *PERSON*, discovery edition, refer to the How-To Guide. For Readiness Check, refer to SAP Note 2913617.", "cause": "Customer aimed for a Readiness Check Analysis while using *PERSON*, discovery edition report, leading to confusion between different reports.", "processed_at": "2025-06-25T10:45:03.420003"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/ee2039e1c36f5e9c13c299bc7a01318b", "title": "Cannot Access Readiness Check for Results from 3193560", "problem": "Unable to upload the results of the readiness check; message \"The connection was reset.\"", "solution": "Verify if the S-user used to access the page has the necessary authorizations as per note 3310759. If authorizations are correct, try using an incognito tab or deactivate the blocking of third-party cookies.", "cause": "The S-user may not have the necessary authorizations or the web browser is blocking third-party cookies.", "processed_at": "2025-06-25T10:45:08.184060"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/ee2039e1c36f5e9c13c299bc7a01318b", "title": "Cannot Access Readiness Check for Results from 3193560", "problem": "Connection reset issue when accessing SAP Readiness Check, possibly due to duplicate files being loaded.", "solution": "Clear browser cache or change to another browser, and provide S-User logon data for further analysis. If duplicates are suspected, check and remove any duplicate files before uploading.", "cause": "Duplicate files being uploaded may cause the connection reset issue. Additionally, browser cache or settings may also be causing the problem.", "processed_at": "2025-06-25T10:45:08.184081"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/ee2039e1c36f5e9c13c299bc7a01318b", "title": "Cannot Access Readiness Check for Results from 3193560", "problem": "SAP Readiness Check could not open in SAP for Me.", "solution": "Refer to resolution in KBA 3330283 and follow steps for troubleshooting access issues as outlined in the provided notes.", "cause": "There could be an issue with the SAP for Me platform integration or authorization settings specific to the environment.", "processed_at": "2025-06-25T10:45:08.184087"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/cbd44da3c3e31adcecd47d9f05013128", "title": "SAP Readiness Check : Custom code Analysis jobs are failing", "problem": "SAP Readiness Check custom code analysis jobs (RC_COLLECT_ANALYSIS_DATA and TMW_RC_CCA_DATA_COLL) are failing with many short dumps during runtime.", "solution": "Assign a role with the necessary authorization object 'S_Q_GOVERN' to the executing user; specifically, ACTVT: 06 and ATC_OTYPGO: 01.", "cause": "Missing authorization for the user executing the jobs.", "processed_at": "2025-06-25T10:45:14.083668"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/cbd44da3c3e31adcecd47d9f05013128", "title": "SAP Readiness Check : Custom code Analysis jobs are failing", "problem": "Short dumps with ABAP runtime error LOAD_PROGRAM_CLASS_MISMATCH during the SAP Readiness Check.", "solution": "Regenerate classes CL_ABAP_COMPILER and CL_CI_TEST_ABAP_COMPILER by activating in transaction SE24 once the syntax error is fixed.", "cause": "Syntax error in function group STRD causing inconsistency in class interfaces.", "processed_at": "2025-06-25T10:45:14.083696"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/cbd44da3c3e31adcecd47d9f05013128", "title": "SAP Readiness Check : Custom code Analysis jobs are failing", "problem": "Syntax error in SAPLSTRD in system MWD preventing correct operation.", "solution": "Fix the syntax error in SAPLSTRD to allow normal system operation.", "cause": "Syntax error in function group STRD.", "processed_at": "2025-06-25T10:45:14.083700"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/cbd44da3c3e31adcecd47d9f05013128", "title": "SAP Readiness Check : Custom code Analysis jobs are failing", "problem": "Job TMW_RC_CCA_DATA_COLL fails due to an inability to delete the run from the baseline.", "solution": "Add necessary authorizations and check system configurations as per SAP Notes.", "cause": "System exception ERROR_MESSAGE due to authorization or configuration issues.", "processed_at": "2025-06-25T10:45:14.083703"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/cbd44da3c3e31adcecd47d9f05013128", "title": "SAP Readiness Check : Custom code Analysis jobs are failing", "problem": "SAP Note 2781766 cannot be implemented in the current S4HANA 2021 version.", "solution": "Review alternative solutions or updates from SAP regarding compatibility.", "cause": "The system version is incompatible with the SAP Note requirements.", "processed_at": "2025-06-25T10:45:14.083706"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/cbd44da3c3e31adcecd47d9f05013128", "title": "SAP Readiness Check : Custom code Analysis jobs are failing", "problem": "ABAP dumps related to syntax and runtime errors are being generated in large numbers during SAP Readiness Check execution.", "solution": "Ensure all SAP Notes are properly implemented and system configurations are updated according to SAP guidelines.", "cause": "Incomplete implementation of required SAP Notes and system prerequisites.", "processed_at": "2025-06-25T10:45:14.083710"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/5b8b17a7c3631a503331583bb0013195", "title": "Readiness check integration to Cloud ALM - SAP ECC upgrades?", "problem": "Integration of all types of readiness check analysis into SAP Cloud ALM", "solution": "Integration is possible for uploading readiness check results into CALM and creating follow-up items such as requirements and user stories.", "cause": "Uncertainty whether all types of readiness check results, including ECC to S/4HANA conversion readiness checks, can be integrated into CALM.", "processed_at": "2025-06-25T10:45:18.065043"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/5b8b17a7c3631a503331583bb0013195", "title": "Readiness check integration to Cloud ALM - SAP ECC upgrades?", "problem": "Confirmation of uploading SAP ECC to S/4HANA readiness check analysis results to Cloud ALM", "solution": "The analysis results can be uploaded to Cloud ALM using the same URL as the SAP Readiness Check Page.", "cause": "Need for clarification on the process and confirmation of integration capabilities.", "processed_at": "2025-06-25T10:45:18.065073"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/5b8b17a7c3631a503331583bb0013195", "title": "Readiness check integration to Cloud ALM - SAP ECC upgrades?", "problem": "Support for ECC version upgrades with Readiness Check", "solution": "Readiness Check is not supported for ECC version upgrades; it is only available for upgrades from ECC to S/4HANA or within S/4HANA versions.", "cause": "Misunderstanding if ECC upgrades can utilize Readiness Check and integrate with Cloud ALM.", "processed_at": "2025-06-25T10:45:18.065077"}]