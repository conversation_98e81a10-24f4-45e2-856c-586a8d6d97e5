[{"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/a4bf5bb1c337d61022195030a0013184", "title": "Queries regarding Custom Code Analysis for S/4HANA 2023 FP01 and FP02", "problem": "The customer reported discrepancies in the custom code analysis results for SAP S/4HANA 2023 Feature Pack Stack (FPS) 01 and FPS02, where the lists of impacted custom objects did not match, with only three objects in common.", "solution": "- Custom Code team analyzed the two ATC runs provided by the customer. - Identified differences due to changes in two objects: ZCL_FI_GL_UPLOAD_DPC_EXT (changed on 13.11) and ZFI_REPT_BPAY_CREDIT (changed on 14.11). - Confirmed that if no changes were made between the runs, the lists of impacted objects for FPS01 and FPS02 of the same product version should match. - Provided clarification that differences could occur due to changes in objects between the two runs. - Suggested that the list of impacted custom objects for FPS01 should be a subset of FPS02 if no changes were made between runs.", "cause": "The differences in the custom code analysis results were due to changes in specific objects between the two ATC runs.", "processed_at": "2025-06-24T17:05:52.181548"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/3e3c57b897e75ed8b86630200153af15", "title": "Readiness Check uploaded file is not valid", "problem": "Readiness Check uploaded file is not valid. The customer is attempting to upload a ZIP file generated by the RC_VALUE_DISCOVERY_COLL_DATA report, which is not applicable for the Readiness Check portal.", "solution": "- Clarify the intended report type with the customer. - If the customer aims for a \"*PERSON*, discovery edition\" report, refer to the How-To Guide linked at the provided URL for the relevant steps. - If the customer is aiming for a Readiness Check for converting SAP ERP 6 to a S/4HANA system, refer them to SAP Note ##2913617 - \"SAP Readiness Check for SAP S/4HANA\" for all necessary prerequisites and steps.", "cause": "The customer is mixing up different reports. The RC_VALUE_DISCOVERY_COLL_DATA report generates a file for \"*PERSON*, discovery edition\" and not for the Readiness Check portal, leading to an invalid file upload attempt.", "processed_at": "2025-06-24T17:05:56.154030"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/ee2039e1c36f5e9c13c299bc7a01318b", "title": "Cannot Access Readiness Check for Results from 3193560", "problem": "Cannot upload the results of the readiness check to the SAP Readiness Check URL; \"The connection was reset\" error message appears.", "solution": "- Verify if the S-user has the necessary authorizations as per note 3310759. - If authorizations are sufficient, try using an incognito tab or deactivate the blocking of third-party cookies: - Open browser configurations. - Deactivate the blocking of third-party cookies. - If changing browser settings is not possible: - Open a new browser session. - Access the login page at https://alm.me.sap.com/login/amalthea?tenant=pr and complete the user logon process with an S-user. - Open the Readiness Check landing page in the same browser window. - If prompted to log in again, use the same S-user.", "cause": "The issue may be due to either insufficient authorizations for the S-user or a web browser blocking third-party cookies.", "processed_at": "2025-06-24T17:06:02.240337"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/cbd44da3c3e31adcecd47d9f05013128", "title": "SAP Readiness Check : Custom code Analysis jobs are failing", "problem": "SAP Readiness Check custom code analysis jobs are failing with numerous short dumps during runtime.", "solution": "- The issue was resolved by adding a specific authorization object to the user executing the jobs. - The authorization object added was 'S_Q_GOVERN', with ACTVT: 06 (Delete) and ATC_OTYPGO: 01 (exemption). - Once the authorization was correctly assigned, the jobs RC_COLLECT_ANALYSIS_DATA and TMW_RC_CCA_DATA_COLL completed successfully.", "cause": "The root cause of the failure was identified as a missing authorization for the user executing the custom code analysis jobs.", "processed_at": "2025-06-24T17:06:06.301077"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/5b8b17a7c3631a503331583bb0013195", "title": "Readiness check integration to Cloud ALM - SAP ECC upgrades?", "problem": "The customer is inquiring whether all types of readiness check analyses, including ECC to S/4HANA conversion, can be integrated into SAP Cloud ALM (CALM) and from there create follow-up items.", "solution": "- The customer was provided with a link to a help document detailing the integration of readiness checks into SAP Cloud ALM. - It was confirmed that readiness check results can be uploaded to CALM, allowing the creation of follow-up items such as requirements and user stories. - For SAP ECC to S/4HANA readiness check, analysis results can be uploaded to Cloud ALM, which uses the same URL as the SAP Readiness Check page. - The readiness check is not supported for ECC upgrades; it is only applicable for ECC to S/4HANA and S/4HANA upgrades.", "cause": "The customer's uncertainty stemmed from a lack of clarity on whether all types of readiness checks, including ECC upgrades, could be integrated into Cloud ALM to create follow-up items.", "processed_at": "2025-06-24T17:06:10.693130"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/46b50a1797eb9a10b86630200153af59", "title": "No value was passed to the mandatory parameter \"PCT_DETAIL_LIST\"", "problem": "No value was passed to the mandatory parameter \"PCT_DETAIL_LIST\" causing a syntax error and ABAP dump when executing report RC_COLLECT_ANALYSIS_DATA.", "solution": "- De-implement SAP Notes 2758146 and 2745851 completely. - Download the latest versions of the SAP notes. - Re-implement the notes in the specified order, ensuring all necessary objects are checked for overwriting during implementation. - Run the repair report ZNOTE_2745851_REPAIR from SAP Note 3205320. - Re-implement SAP Note 2745851. - Verify that the ABAP dump is resolved when running the report RC_COLLECT_ANALYSIS_DATA.", "cause": "The issue was caused by outdated coding in the development system V59, which was not properly updated during the initial implementation of SAP Notes 2758146 and 2745851.", "processed_at": "2025-06-24T17:06:14.921752"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/cbe45875c3a71ed4553db11f0501319d", "title": "Not able to upload the readiness analysis file in manage analysis files app", "problem": "Not able to upload the readiness analysis file in the Manage Analysis Files app due to an unsupported file format error.", "solution": "- Review the SAP Note 3275056 for instructions on generating the analysis file. - Check the compatibility of the SAP Note with the SAP S/4HANA system, as the note may be intended for SAP ECC systems. - Contact SAP Support to verify the existence and applicability of the program RC_UDP_START_DMR2 for the S/4HANA system.", "cause": "The SAP Note referenced may be intended for SAP_APPL (SAP ECC) systems rather than S/4HANA, leading to an unsupported file format error during the upload process.", "processed_at": "2025-06-24T17:06:18.434425"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/79fdf13f977b1a5030c531f71153afae", "title": "Readiness Check WebView error", "problem": "Readiness Check WebView error - The SAP Readiness Check does not display data correctly in the \"Organizational structure - Overview\" area, showing discrepancies between the web view and Excel downloads.", "solution": "- The issue was identified as a display problem in the application web view, with the data in Excel being correct. - A fix was implemented and arrived at Production, resolving the display issue. - The customer was advised to check their analysis again to see if the numbers are now correctly displayed in the application web view.", "cause": "Unknown.", "processed_at": "2025-06-24T17:06:21.872211"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/0f499dc03b0c6e508c36d62a85e45abc", "title": "User's are unable to accesses the SAP Readinesscheck.", "problem": "Users are unable to access the SAP Readiness Check portal, encountering a blank page despite having the relevant RC* authorizations assigned.", "solution": "- Refer to the approaches outlined in the \"Resolution\" section of SAP KBA 3330283 - \"SAP Readiness Check could not open in SAP for Me.\"", "cause": "A completely empty screen is not an indication of missing authorizations, as the application is designed to output a message when accessed without relevant roles. The root cause might be related to other technical issues as described in SAP KBA 3330283.", "processed_at": "2025-06-24T17:06:25.343072"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/877262d097cce6907bcf59081153af35", "title": "<PERSON><PERSON><PERSON>heck,", "problem": "The customer is experiencing a syntax error when trying to activate the FIN_CORR_DISPLAY_ISSUE program, specifically with the method \"GET_INSTANCE\" being unknown or protected/private. This error causes the TMW_RC_FDQ_DATA_COLL job to terminate in the SAP Readiness Check process.", "solution": "- Import the syntax error program or class using SAP Note **********. - If similar errors are not present in the LBD development system, transport the healthy programs or classes from LBD to see if this resolves the errors. - Ensure valid user maintenance for both LBP and LBD systems in the secure area for further assistance.", "cause": "Improper user handling.", "processed_at": "2025-06-24T17:06:29.175053"}]