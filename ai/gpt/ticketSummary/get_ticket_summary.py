import csv
import json
import requests
import time
from datetime import datetime


def get_summary(ticket_content):
    base_url = "https://aicore-llm-proxy.internal.cfapps.eu12.hana.ondemand.com/"
    api_key = "sk-58f4db0abc225fb9fa70a80e71ec732bfd086076ddc44c83"

    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json"
    }

    request_body = {
        "model": "gpt-4o-2024-08-06",
        "messages": [
            {
                "role": "system",
                "content": "You are an expert SAP support analyst. Analyze the customer support ticket and identify ALL problems, solutions, and causes mentioned. "
                          "Provide a structured response using numbered items for each category:\n\n"
                          "Problem 1: [First problem or issue reported by the customer]\n"
                          "Problem 2: [Second problem if exists]\n"
                          "Problem N: [Additional problems if exist]\n\n"
                          "Solution 1: [First resolution step or solution provided]\n"
                          "Solution 2: [Second solution if exists]\n"
                          "Solution N: [Additional solutions if exist]\n\n"
                          "Cause 1: [First root cause identified]\n"
                          "Cause 2: [Second cause if exists]\n"
                          "Cause N: [Additional causes if exist]\n\n"
                          "Important: \n"
                          "- Use exactly the format above with 'Problem N:', 'Solution N:', and 'Cause N:' labels\n"
                          "- Number each item starting from 1\n"
                          "- If only one item exists in a category, still use the number (e.g., 'Problem 1:')\n"
                          "- If no information is available for a category, omit that entire category\n"
                          "- Extract ALL distinct problems, solutions, and causes mentioned in the ticket"
            },
            {
                "role": "user",
                "content": f"Please analyze this SAP support ticket and provide the structured response:\n\n{ticket_content}"
            }
        ],
        "temperature": 0.7,
        "max_tokens": 1000
    }

    try:
        response = requests.post(f"{base_url}v1/chat/completions",
                               headers=headers,
                               json=request_body,
                               timeout=30)
        response.raise_for_status()

        response_data = response.json()
        if 'choices' in response_data and len(response_data['choices']) > 0:
            return response_data['choices'][0]['message']['content']
        else:
            print(f"Response格式异常: {response_data}")
            return "Error: Invalid response format from AI Core"

    except requests.exceptions.RequestException as e:
        print(f"AI Core API调用失败: {e}")
        if hasattr(e, 'response') and e.response is not None:
            print(f"响应状态码: {e.response.status_code}")
            print(f"响应内容: {e.response.text}")
        return f"Error calling AI Core API: {str(e)}"
    except Exception as e:
        print(f"处理AI Core响应时出错: {e}")
        return f"Error processing AI Core response: {str(e)}"


def parse_structured_summary(response_text):
    """解析结构化的LLM响应，提取多个Problem、Solution和Cause"""
    import re

    result = {
        "problems": [],
        "solutions": [],
        "causes": []
    }

    if not response_text:
        return result

    lines = response_text.strip().split('\n')

    for line in lines:
        line = line.strip()
        if not line:
            continue

        # 匹配 Problem N: 格式
        problem_match = re.match(r'^problem\s+(\d+):\s*(.+)', line, re.IGNORECASE)
        if problem_match:
            problem_content = problem_match.group(2).strip()
            if problem_content:
                result["problems"].append(problem_content)
            continue

        # 匹配 Solution N: 格式
        solution_match = re.match(r'^solution\s+(\d+):\s*(.+)', line, re.IGNORECASE)
        if solution_match:
            solution_content = solution_match.group(2).strip()
            if solution_content:
                result["solutions"].append(solution_content)
            continue

        # 匹配 Cause N: 格式
        cause_match = re.match(r'^cause\s+(\d+):\s*(.+)', line, re.IGNORECASE)
        if cause_match:
            cause_content = cause_match.group(2).strip()
            if cause_content:
                result["causes"].append(cause_content)
            continue

    return result


def get_parsed_summary(ticket_content):
    try:
        ticket_summary = get_summary(ticket_content)
        return parse_structured_summary(ticket_summary)
    except Exception as e:
        print(f"AI Core API调用失败: {e}")
        # 返回空的结构化结果
        return {
            "problems": [],
            "solutions": [],
            "causes": []
        }



def process_tickets(csv_file_path, output_file_path):
    """处理tickets并生成分析结果"""
    results = []
    processed_count = 0
    error_count = 0

    print(f"开始处理文件: {csv_file_path}")
    print(f"输出文件: {output_file_path}")

    try:
        with open(csv_file_path, 'r', encoding='utf-8') as csvfile:
            reader = csv.DictReader(csvfile)
            total_rows = sum(1 for row in reader)
            csvfile.seek(0)
            reader = csv.DictReader(csvfile)

            print(f"总共需要处理 {total_rows} 条tickets")

            for row_num, row in enumerate(reader, 1):
                try:
                    url = row['url']
                    title = row['title']
                    text = row['text']

                    print(f"正在处理第 {row_num}/{total_rows} 条ticket: {title[:50]}...")

                    # 组合ticket内容
                    ticket_content = f"Title: {title}\n\nContent: {text}"

                    # 获取分析结果
                    structured_summary = get_parsed_summary(ticket_content)

                    # 保存结果
                    result = {
                        "url": url,
                        "title": title,
                        "problems": structured_summary.get("problems", []),
                        "solutions": structured_summary.get("solutions", []),
                        "causes": structured_summary.get("causes", []),
                        "processed_at": datetime.now().isoformat()
                    }
                    results.append(result)
                    processed_count += 1

                    print(f"✓ 完成第 {row_num} 条ticket的分析")

                    # 每处理10条记录就保存一次，防止数据丢失
                    if processed_count %  5 == 0:
                        save_results(results, output_file_path)
                        print(f"已保存前 {processed_count} 条记录到文件")

                    # 添加延迟避免API限制
                    time.sleep(1)

                except Exception as e:
                    error_count += 1
                    print(f"✗ 处理第 {row_num} 条ticket时出错: {str(e)}")
                    continue

    except FileNotFoundError:
        print(f"错误: 找不到文件 {csv_file_path}")
        return
    except Exception as e:
        print(f"读取CSV文件时出错: {str(e)}")
        return

    # 最终保存所有结果
    save_results(results, output_file_path)

    print(f"\n处理完成!")
    print(f"成功处理: {processed_count} 条tickets")
    print(f"处理失败: {error_count} 条tickets")
    print(f"结果已保存到: {output_file_path}")


def save_results(results, output_file_path):
    try:
        with open(output_file_path, 'w', encoding='utf-8') as jsonfile:
            json.dump(results, jsonfile, ensure_ascii=False, indent=2)
    except Exception as e:
        print(f"保存文件时出错: {str(e)}")


if __name__ == '__main__':
    csv_file_path = "RC Case_top500.csv"
    output_file_path = f"ticket_analysis_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"

    try:
        process_tickets(csv_file_path, output_file_path)
    except KeyboardInterrupt:
        print("\n⚠️  处理被用户中断")
    except Exception as e:
        print(f"❌ 处理过程中发生错误: {e}")

    print("\n✅ 处理完成！")
