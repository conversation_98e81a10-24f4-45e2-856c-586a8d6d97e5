import csv
import json
import requests
import time
from datetime import datetime


def get_summary(ticket_content):
    base_url = "https://aicore-llm-proxy.internal.cfapps.eu12.hana.ondemand.com/"
    api_key = "sk-58f4db0abc225fb9fa70a80e71ec732bfd086076ddc44c83"

    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json"
    }

    request_body = {
        "model": "gpt-4o-2024-08-06",
        "messages": [
            {
                "role": "system",
                "content": "You are an expert SAP support analyst. Analyze the customer support ticket and identify ALL distinct issue combinations mentioned "
                        "then provide concise, professional summaries. Focus on identifying the main issue, "
                        "key symptoms, resolution steps taken, and final outcome. Keep the summary clear and "
                        "structured, highlighting the most important technical details and business impact. "
                          "Each combination should include a Problem, its corresponding Solution, and the Cause. "
                          "Provide a structured response using numbered groups:\n\n"
                          "Group 1:\n"
                          "Problem: [A precise and short summary about the main issue or problem reported by the customer.]\n"
                          "Solution: [Resolution taken or solution provided step by step. Use bullet points when necessary. ]\n"
                          "Cause: [Root cause for this specific problem.]\n\n"
                          "Group 2:\n"
                          "Problem: [A totally different topic problem from problems in previous groups if existed]\n"
                          "Solution: [Resolution taken or solution provided step by step. Use bullet points when necessary.]\n"
                          "Cause: [Root cause for this specific problem]\n\n"
                          "Important: \n"
                          "- Each Group must contain Problem, Solution, and Cause as a complete set\n"
                          "- If Cause is missing for related Problem, leave that field empty but keep the label\n"
                          "- Only create groups when there is at least a Problem identified\n"
                          "- Number groups starting from 1\n"
                          "- Extract ALL distinct problem-solution-cause combinations from the ticket"
            },
            {
                "role": "user",
                "content": f"Please analyze this SAP support ticket and provide the structured response:\n\n{ticket_content}"
            }
        ],
        "temperature": 0.7,
        "max_tokens": 1000
    }

    try:
        response = requests.post(f"{base_url}v1/chat/completions",
                               headers=headers,
                               json=request_body,
                               timeout=30)
        response.raise_for_status()

        response_data = response.json()
        if 'choices' in response_data and len(response_data['choices']) > 0:
            return response_data['choices'][0]['message']['content']
        else:
            print(f"Response格式异常: {response_data}")
            return "Error: Invalid response format from AI Core"

    except requests.exceptions.RequestException as e:
        print(f"AI Core API调用失败: {e}")
        if hasattr(e, 'response') and e.response is not None:
            print(f"响应状态码: {e.response.status_code}")
            print(f"响应内容: {e.response.text}")
        return f"Error calling AI Core API: {str(e)}"
    except Exception as e:
        print(f"处理AI Core响应时出错: {e}")
        return f"Error processing AI Core response: {str(e)}"


def parse_structured_summary(response_text):
    """解析结构化的LLM响应，提取Problem-Solution-Cause组合"""
    import re

    result = {
        "groups": []
    }

    if not response_text:
        return result

    lines = response_text.strip().split('\n')
    current_group = None

    for line in lines:
        line = line.strip()
        if not line:
            continue

        # 匹配 Group N: 格式
        group_match = re.match(r'^group\s+(\d+):', line, re.IGNORECASE)
        if group_match:
            # 保存前一个组（如果存在且有内容）
            if current_group and current_group.get("problem"):
                result["groups"].append(current_group)
            # 开始新组
            current_group = {"problem": "", "solution": "", "cause": ""}
            continue

        # 匹配 Problem: 格式
        problem_match = re.match(r'^problem:\s*(.+)', line, re.IGNORECASE)
        if problem_match:
            if current_group is None:
                current_group = {"problem": "", "solution": "", "cause": ""}
            current_group["problem"] = problem_match.group(1).strip()
            continue

        # 匹配 Solution: 格式
        solution_match = re.match(r'^solution:\s*(.+)', line, re.IGNORECASE)
        if solution_match:
            if current_group is None:
                current_group = {"problem": "", "solution": "", "cause": ""}
            current_group["solution"] = solution_match.group(1).strip()
            continue

        # 匹配 Cause: 格式
        cause_match = re.match(r'^cause:\s*(.+)', line, re.IGNORECASE)
        if cause_match:
            if current_group is None:
                current_group = {"problem": "", "solution": "", "cause": ""}
            current_group["cause"] = cause_match.group(1).strip()
            continue

    # 保存最后一个组（如果存在且有内容）
    if current_group and current_group.get("problem"):
        result["groups"].append(current_group)

    return result


def get_parsed_summary(ticket_content):
    try:
        ticket_summary = get_summary(ticket_content)
        return parse_structured_summary(ticket_summary)
    except Exception as e:
        print(f"AI Core API调用失败: {e}")
        # 返回空的结构化结果
        return {
            "groups": []
        }



def process_tickets(csv_file_path, output_file_path):
    """处理tickets并生成分析结果"""
    results = []
    processed_count = 0
    error_count = 0

    print(f"开始处理文件: {csv_file_path}")
    print(f"输出文件: {output_file_path}")

    try:
        with open(csv_file_path, 'r', encoding='utf-8') as csvfile:
            reader = csv.DictReader(csvfile)
            total_rows = sum(1 for row in reader)
            csvfile.seek(0)
            reader = csv.DictReader(csvfile)

            print(f"总共需要处理 {total_rows} 条tickets")

            for row_num, row in enumerate(reader, 1):
                try:
                    url = row['url']
                    title = row['title']
                    text = row['text']

                    print(f"正在处理第 {row_num}/{total_rows} 条ticket: {title[:50]}...")

                    # 组合ticket内容
                    ticket_content = f"Title: {title}\n\nContent: {text}"

                    # 获取分析结果
                    structured_summary = get_parsed_summary(ticket_content)

                    # 获取组合列表
                    groups = structured_summary.get("groups", [])

                    # 只有当存在有效组合时才创建对象
                    if groups:
                        for group in groups:
                            result = {
                                "url": url,
                                "title": title,
                                "problem": group.get("problem", ""),
                                "solution": group.get("solution", ""),
                                "cause": group.get("cause", ""),
                                "processed_at": datetime.now().isoformat()
                            }
                            results.append(result)
                    else:
                        # 如果没有找到任何组合，记录但不创建对象
                        print(f"  ⚠️  未找到有效的Problem-Solution-Cause组合")

                    processed_count += 1

                    print(f"✓ 完成第 {row_num} 条ticket的分析")

                    # 每处理10条记录就保存一次，防止数据丢失
                    if processed_count %  5 == 0:
                        save_results(results, output_file_path)
                        print(f"已保存前 {processed_count} 条记录到文件")

                    # 添加延迟避免API限制
                    time.sleep(1)

                except Exception as e:
                    error_count += 1
                    print(f"✗ 处理第 {row_num} 条ticket时出错: {str(e)}")
                    continue

    except FileNotFoundError:
        print(f"错误: 找不到文件 {csv_file_path}")
        return
    except Exception as e:
        print(f"读取CSV文件时出错: {str(e)}")
        return

    # 最终保存所有结果
    save_results(results, output_file_path)

    print(f"\n处理完成!")
    print(f"成功处理: {processed_count} 条tickets")
    print(f"处理失败: {error_count} 条tickets")
    print(f"结果已保存到: {output_file_path}")


def save_results(results, output_file_path):
    try:
        with open(output_file_path, 'w', encoding='utf-8') as jsonfile:
            json.dump(results, jsonfile, ensure_ascii=False, indent=2)
    except Exception as e:
        print(f"保存文件时出错: {str(e)}")


if __name__ == '__main__':
    csv_file_path = "RC Case_top500.csv"
    output_file_path = f"ticket_analysis_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"

    try:
        process_tickets(csv_file_path, output_file_path)
    except KeyboardInterrupt:
        print("\n⚠️  处理被用户中断")
    except Exception as e:
        print(f"❌ 处理过程中发生错误: {e}")

    print("\n✅ 处理完成！")
