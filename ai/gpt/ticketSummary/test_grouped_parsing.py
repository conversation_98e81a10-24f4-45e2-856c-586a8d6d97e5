#!/usr/bin/env python3
"""
测试Problem-Solution-Cause组合解析功能
"""

import sys
import os
import json
from datetime import datetime
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from get_ticket_summary import parse_structured_summary, get_summary

def test_grouped_parsing():
    """测试组合解析功能"""
    print("🧪 测试Problem-Solution-Cause组合解析...")
    
    # 测试用例1：多个完整组合
    test_response_1 = """
    Group 1:
    Problem: Customer experiencing file upload errors in SAP Readiness Check portal with "Upload failed - invalid file format" message.
    Solution: Development team identified and fixed a bug in the upload validation logic. After the fix was deployed, customer successfully uploaded the file.
    Cause: Bug in the file validation logic that incorrectly rejected valid XML files due to parsing errors.

    Group 2:
    Problem: Analysis results are not displaying correctly after successful upload.
    Solution: Updated the display rendering component to properly show analysis results and cleared browser cache.
    Cause: Frontend rendering component had a compatibility issue with new data format.

    Group 3:
    Problem: System performance is slow during peak hours affecting user experience.
    Solution: Implemented caching mechanism and optimized database queries to improve performance.
    Cause: Database queries were not optimized and lacked proper indexing.
    """
    
    # 测试用例2：部分信息缺失的组合
    test_response_2 = """
    Group 1:
    Problem: SAP Custom Code Analysis jobs are failing during execution with timeout errors.
    Solution: Restarted the analysis service and updated job configuration parameters.
    Cause: 

    Group 2:
    Problem: Cannot access previous analysis results.
    Solution: 
    Cause: User session expired and permissions were not properly refreshed.
    """
    
    # 测试用例3：单个组合
    test_response_3 = """
    Group 1:
    Problem: Cannot access SAP Readiness Check results from previous analysis.
    Solution: Reset user permissions and cleared browser cache.
    Cause: User session expired and permissions were not properly refreshed.
    """
    
    # 测试用例4：无Group标签的格式
    test_response_4 = """
    Problem: File upload issue in portal.
    Solution: Fixed validation bug.
    Cause: Software bug in validation routine.
    """
    
    test_cases = [
        ("多个完整组合", test_response_1),
        ("部分信息缺失", test_response_2),
        ("单个组合", test_response_3),
        ("无Group标签", test_response_4)
    ]
    
    for test_name, response_text in test_cases:
        print(f"\n📋 测试用例: {test_name}")
        print("-" * 50)
        
        result = parse_structured_summary(response_text)
        groups = result.get("groups", [])
        
        print(f"找到 {len(groups)} 个Problem-Solution-Cause组合:")
        
        for i, group in enumerate(groups, 1):
            print(f"\n  组合 {i}:")
            print(f"    Problem: {group.get('problem', '')}")
            print(f"    Solution: {group.get('solution', '')}")
            print(f"    Cause: {group.get('cause', '')}")
        
        if groups:
            print("✅ 解析成功")
        else:
            print("⚠️  未找到有效组合")

def test_json_output_format():
    """测试最终JSON输出格式"""
    print("\n📋 测试最终JSON输出格式...")
    
    # 模拟解析结果
    mock_groups = [
        {
            "problem": "File upload errors in SAP Readiness Check portal",
            "solution": "Fixed validation bug and deployed patch",
            "cause": "Bug in file validation logic"
        },
        {
            "problem": "Analysis results not displaying correctly",
            "solution": "Updated rendering component",
            "cause": "Frontend compatibility issue"
        },
        {
            "problem": "System performance issues during peak hours",
            "solution": "Implemented caching and query optimization",
            "cause": "Poor database design and indexing"
        }
    ]
    
    # 模拟票据信息
    url = "https://sap.service-now.com/test/12345"
    title = "Multiple Issues with SAP Readiness Check"
    processed_at = datetime.now().isoformat()
    
    # 生成多个JSON对象
    results = []
    for group in mock_groups:
        result = {
            "url": url,
            "title": title,
            "problem": group["problem"],
            "solution": group["solution"],
            "cause": group["cause"],
            "processed_at": processed_at
        }
        results.append(result)
    
    print(f"从一个ticket生成了 {len(results)} 个JSON对象:")
    print(json.dumps(results, indent=2, ensure_ascii=False))

def test_real_api_call():
    """测试真实API调用"""
    print("\n🤖 测试真实API调用...")
    
    # 模拟一个包含多个问题的复杂票据
    complex_ticket_content = """
    Title: Multiple Issues with SAP System Upgrade Preparation
    
    Content: Customer reported several critical issues during SAP upgrade preparation:
    
    Issue 1: File upload functionality in SAP Readiness Check portal is failing. Users get "invalid format" errors when trying to upload XML analysis files, even though the files are correctly formatted according to documentation.
    
    Issue 2: Custom Code Analysis jobs are timing out during execution. Large codebases cause the analysis service to run out of memory and fail after 30 minutes.
    
    Issue 3: Previously completed analysis results are no longer accessible through the portal. Users cannot view or download reports from analyses completed last week.
    
    Support team investigation and resolution:
    
    For Issue 1: Development team found a bug in the file validation routine that was incorrectly rejecting valid XML files. They deployed a hotfix that corrected the parsing logic.
    
    For Issue 2: Infrastructure team increased memory allocation for the analysis service from 8GB to 16GB and optimized the job scheduling algorithm.
    
    For Issue 3: The problem was traced to a session management bug where user permissions were not being properly refreshed. The authentication service was restarted and session timeout logic was updated.
    
    Root causes identified:
    - Software bug in XML validation routine
    - Insufficient memory allocation for large analysis jobs  
    - Session management implementation had a race condition bug
    """
    
    try:
        print("📤 发送请求到AI Core...")
        response_text = get_summary(complex_ticket_content)
        
        print(f"🤖 API响应:")
        print("-" * 50)
        print(response_text)
        print("-" * 50)
        
        print("\n📊 解析结果:")
        result = parse_structured_summary(response_text)
        groups = result.get("groups", [])
        
        print(f"找到 {len(groups)} 个Problem-Solution-Cause组合:")
        
        # 生成最终JSON对象
        final_results = []
        for i, group in enumerate(groups, 1):
            print(f"\n  组合 {i}:")
            print(f"    Problem: {group.get('problem', '')}")
            print(f"    Solution: {group.get('solution', '')}")
            print(f"    Cause: {group.get('cause', '')}")
            
            result_obj = {
                "url": "https://sap.service-now.com/complex/12345",
                "title": "Multiple Issues with SAP System Upgrade Preparation",
                "problem": group.get('problem', ''),
                "solution": group.get('solution', ''),
                "cause": group.get('cause', ''),
                "processed_at": datetime.now().isoformat()
            }
            final_results.append(result_obj)
        
        if final_results:
            print(f"\n📋 最终JSON结构 ({len(final_results)} 个对象):")
            print(json.dumps(final_results, indent=2, ensure_ascii=False))
            
            # 保存测试结果
            output_file = f"grouped_parsing_test_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(final_results, f, ensure_ascii=False, indent=2)
            
            print(f"\n💾 测试结果已保存到: {output_file}")
        else:
            print("\n⚠️  未找到有效的Problem-Solution-Cause组合")
        
        return len(final_results) > 0
        
    except Exception as e:
        print(f"❌ API调用失败: {e}")
        return False

if __name__ == '__main__':
    print("🧪 测试Problem-Solution-Cause组合解析系统")
    print("=" * 60)
    
    # 运行所有测试
    test_grouped_parsing()
    test_json_output_format()
    
    print("\n" + "=" * 60)
    api_success = test_real_api_call()
    
    if api_success:
        print("\n✅ 所有测试通过!")
        print("系统可以正确处理Problem-Solution-Cause组合并生成多个JSON对象")
    else:
        print("\n⚠️  解析功能正常，但API调用失败")
        print("请检查API配置和网络连接")
