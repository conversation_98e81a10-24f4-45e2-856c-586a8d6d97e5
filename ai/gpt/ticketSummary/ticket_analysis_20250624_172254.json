[{"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/a4bf5bb1c337d61022195030a0013184", "title": "Queries regarding Custom Code Analysis for S/4HANA 2023 FP01 and FP02", "problem": "The list of impacted custom objects for S/4HANA 2023 FP01 differs significantly from the list for FP02, with only 3 objects in common, raising concerns about the subset inclusion of FP01 objects in FP02.", "solution": "Confirmed that if no changes were done between two runs, the list of impacted custom objects should be the same for FPS01 and FPS02 of the same product version.", "cause": "Differences in impacted custom objects lists are due to changes in specific objects between the two runs (ZCL_FI_GL_UPLOAD_DPC_EXT and ZFI_REPT_BPAY_CREDIT).", "processed_at": "2025-06-24T17:23:00.617574"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/a4bf5bb1c337d61022195030a0013184", "title": "Queries regarding Custom Code Analysis for S/4HANA 2023 FP01 and FP02", "problem": "Queries about whether impacted custom objects for FPS01 should be a subset of those for FPS02, and whether objects impacted for FP01 need to be considered when upgrading directly to FP02.", "solution": "Provided confirmation that if upgrading directly to FP02, only consider the list of impacted custom objects for FP02.", "cause": "ATC checks report issues for objects recorded in the simplification DB which have a validity period, leading to differences in findings between runs.", "processed_at": "2025-06-24T17:23:00.617589"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/a4bf5bb1c337d61022195030a0013184", "title": "Queries regarding Custom Code Analysis for S/4HANA 2023 FP01 and FP02", "problem": "ATC checks cannot be configured for specific feature packs, only for specific target releases.", "solution": "Custom Code team analyzed the differences and identified changes in specific objects (ZCL_FI_GL_UPLOAD_DPC_EXT and ZFI_REPT_BPAY_CREDIT) between the two runs.", "cause": "", "processed_at": "2025-06-24T17:23:00.617593"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/3e3c57b897e75ed8b86630200153af15", "title": "Readiness Check uploaded file is not valid", "problem": "The customer is attempting to upload the file \"BPIAnalysisDataPRO20241105.zip\" to SAP Readiness Check, but the uploaded file is not valid.", "solution": "The customer must clarify their aim: if they want a \"*PERSON*, discovery edition\" report, they should refer to the How-To Guide available at the provided link for relevant steps.", "cause": "The customer executed the wrong report, RC_VALUE_DISCOVERY_COLL_DATA, which generates a \"*PERSON*, discovery edition\" report instead of a Readiness Check report.", "processed_at": "2025-06-24T17:23:05.379605"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/3e3c57b897e75ed8b86630200153af15", "title": "Readiness Check uploaded file is not valid", "problem": "The customer is mixing up different reports, specifically using RC_VALUE_DISCOVERY_COLL_DATA instead of the correct Readiness Check report.", "solution": "If aiming for a Readiness Check for conversion from SAP ERP 6 to S/4HANA, the customer should refer to SAP Note 2913617 for necessary prerequisites and steps.", "cause": "The resulting ZIP file from RC_VALUE_DISCOVERY_COLL_DATA is not applicable for upload to the Readiness Check portal in SAP for Me.", "processed_at": "2025-06-24T17:23:05.379613"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/ee2039e1c36f5e9c13c299bc7a01318b", "title": "Cannot Access Readiness Check for Results from 3193560", "problem": "Unable to upload the results of the readiness check to the SAP Readiness Check URL. The message \"The connection was reset\" appears, blocking access to the readiness check.", "solution": "Verify if the S-user has the necessary authorizations for accessing the readiness check as mentioned in note 3310759.", "cause": "The S-user might not have the necessary authorizations to access the readiness check analyses.", "processed_at": "2025-06-24T17:23:10.507990"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/ee2039e1c36f5e9c13c299bc7a01318b", "title": "Cannot Access Readiness Check for Results from 3193560", "problem": "Files may have been uploaded, but the \"connection reset\" issue persists, possibly due to duplicate files being loaded.", "solution": "Use an incognito tab or deactivate the blocking of third-party cookies in the browser settings.", "cause": "Use of a web browser that blocks third-party cookies, causing connectivity issues.", "processed_at": "2025-06-24T17:23:10.507997"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/ee2039e1c36f5e9c13c299bc7a01318b", "title": "Cannot Access Readiness Check for Results from 3193560", "problem": "", "solution": "Open a new browser session, log in using the S-user ID, finish the user authorization, and access the readiness check landing page again.", "cause": "The \"connection reset\" issue may occur due to duplicate files being loaded.", "processed_at": "2025-06-24T17:23:10.507998"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/ee2039e1c36f5e9c13c299bc7a01318b", "title": "Cannot Access Readiness Check for Results from 3193560", "problem": "", "solution": "Refer to resolution in KBA 3330283 and provide further information if the issue persists.", "cause": "", "processed_at": "2025-06-24T17:23:10.507999"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/ee2039e1c36f5e9c13c299bc7a01318b", "title": "Cannot Access Readiness Check for Results from 3193560", "problem": "", "solution": "SAP Support uploaded the attached Zip files to the readiness check on behalf of the customer for immediate resolution.", "cause": "", "processed_at": "2025-06-24T17:23:10.508001"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/cbd44da3c3e31adcecd47d9f05013128", "title": "SAP Readiness Check : Custom code Analysis jobs are failing", "problem": "The SAP Readiness Check for custom code analysis jobs (RC_COLLECT_ANALYSIS_DATA and TMW_RC_CCA_DATA_COLL) are failing with errors and generating ABAP dumps during runtime.", "solution": "Assign a role to the executing user with the required authorization object 'S_Q_GOVERN' with specific values for ACTVT and ATC_OTYPGO.", "cause": "Missing authorization object 'S_Q_GOVERN' for the job executor.", "processed_at": "2025-06-24T17:23:17.278896"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/cbd44da3c3e31adcecd47d9f05013128", "title": "SAP Readiness Check : Custom code Analysis jobs are failing", "problem": "Missing authorization for the executing user causing job cancellation.", "solution": "Fix syntax errors in SAPLSTRD and regenerate classes CL_ABAP_COMPILER and CL_CI_TEST_ABAP_COMPILER.", "cause": "Syntax error in SAPLSTRD preventing the normal operation of the system.", "processed_at": "2025-06-24T17:23:17.278909"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/cbd44da3c3e31adcecd47d9f05013128", "title": "SAP Readiness Check : Custom code Analysis jobs are failing", "problem": "Syntax errors in SAPLSTRD in the system MWD preventing analysis.", "solution": "Implement necessary SAP Notes including 2436688 and 3059197, ensuring all prerequisites for the Readiness Check are met.", "cause": "Inconsistency and change in the class interface at runtime causing ABAP runtime error.", "processed_at": "2025-06-24T17:23:17.278912"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/cbd44da3c3e31adcecd47d9f05013128", "title": "SAP Readiness Check : Custom code Analysis jobs are failing", "problem": "ABAP runtime error LOAD_PROGRAM_CLASS_MISMATCH.", "solution": "Add the AUTH object and value to the job log for successful completion.", "cause": "Incorrect or incomplete implementation of SAP Notes and prerequisites for readiness checks.", "processed_at": "2025-06-24T17:23:17.278915"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/5b8b17a7c3631a503331583bb0013195", "title": "Readiness check integration to Cloud ALM - SAP ECC upgrades?", "problem": "Question regarding the integration of all types of readiness check analysis into SAP Cloud ALM, specifically whether ECC to S/4HANA conversion readiness check results and other readiness checks can be integrated into CALM to create follow-up items.", "solution": "It is possible to upload readiness check results to CALM, and once the results ZIP file is uploaded, follow-up items such as requirements and user stories can be created within CALM.", "cause": "The customer seeks confirmation on integrating readiness check results across all scenarios into Cloud ALM, potentially due to a lack of clarity on existing documentation or processes.", "processed_at": "2025-06-24T17:23:21.794644"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/5b8b17a7c3631a503331583bb0013195", "title": "Readiness check integration to Cloud ALM - SAP ECC upgrades?", "problem": "", "solution": "The Cloud ALM uses the same URL to upload the file as the SAP Readiness Check Page, thus allowing analysis results from ECC to S/4HANA readiness checks to be uploaded to Cloud ALM.", "cause": "The readiness check is primarily used for upgrading from ECC to S/4HANA or from one S/4HANA product version to another, and not for ECC version upgrades, leading to the customer's inquiry about ECC upgrade support.", "processed_at": "2025-06-24T17:23:21.794659"}]