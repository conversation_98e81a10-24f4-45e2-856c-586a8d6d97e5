[{"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/a4bf5bb1c337d61022195030a0013184", "title": "Queries regarding Custom Code Analysis for S/4HANA 2023 FP01 and FP02", "problem": "The customer ran a custom code analysis for SAP S/4HANA 2023 feature packs FPS01 and FPS02 and found discrepancies between the lists of impacted custom objects. They expected that the list for FPS02 should include all the objects flagged in FPS01, but only three objects were common between the two lists.", "solution": "- The Custom Code team analyzed the ATC runs provided by the customer. - They identified that two objects, ZCL_FI_GL_UPLOAD_DPC_EXT and ZFI_REPT_BPAY_CREDIT, had changes between the two runs which caused differences in the results. - It was explained that if no changes were made between two runs, the list of impacted custom objects for FPS01 and FPS02 should be the same, given that they are of the same product version. - The customer was informed that the discrepancies likely resulted from changes in specific objects between the two runs.", "cause": "The differences in the custom code analysis results between FPS01 and FPS02 were due to changes made to specific objects (ZCL_FI_GL_UPLOAD_DPC_EXT and ZFI_REPT_BPAY_CREDIT) between the two analysis runs.", "processed_at": "2025-06-24T17:00:21.215902"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/3e3c57b897e75ed8b86630200153af15", "title": "Readiness Check uploaded file is not valid", "problem": "The customer attempted to upload the file \"BPIAnalysisDataPRO20241105.zip\" to the SAP Readiness Check portal, receiving an error message stating the file is not valid. The customer used the report RC_VALUE_DISCOVERY_COLL_DATA for data collection, which is not the correct report for Readiness Check uploads.", "solution": "- Clarify the intended analysis or report: - If aiming for a \"*PERSON*, discovery edition\" report, follow the steps in the linked How-To Guide: [https://sdmp-prod-prod-bpi-sdmp-app.cfapps.eu20-001.hana.ondemand.com/create-request/create-request.html](https://sdmp-prod-prod-bpi-sdmp-app.cfapps.eu20-001.hana.ondemand.com/create-request/create-request.html) - For a Readiness Check for SAP ERP 6 to S/4HANA conversion, refer to SAP Note 2913617 for prerequisites and steps: [SAP Note 2913617](https://me.sap.com/notes/0002913617)", "cause": "The RC_VALUE_DISCOVERY_COLL_DATA report was incorrectly used instead of the correct Data Collector report for SAP Readiness Check, resulting in an incompatible file type for upload.", "processed_at": "2025-06-24T17:00:25.905361"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/ee2039e1c36f5e9c13c299bc7a01318b", "title": "Cannot Access Readiness Check for Results from 3193560", "problem": "Unable to upload the results of the readiness check to the SAP Readiness Check URL due to the message \"The connection was reset.\"", "solution": "- Verify if the S-user has the necessary authorizations mentioned in note 3310759. - If the S-user has the necessary authorizations, try using an incognito tab or deactivate the blocking of third-party cookies: - Open the configurations of the web browser. - Deactivate the blocking of third-party cookies. - If changing the browser settings is not possible: - Open a new browser session. - Navigate to https://alm.me.sap.com/login/amalthea?tenant=pr and use the S-user for logon. - After completing the user authorization process, open the Readiness Check landing page in the same browser window. - If prompted to log in again, do so with the same S-user. - SAP Support contacted the development team and uploaded the attached Zip files to the readiness check as a special handling measure. - Refer to KBA 3330283 for further resolution steps regarding access issues.", "cause": "- The issue may be caused by either a lack of necessary authorizations for the S-user or by using a web browser that blocks third-party cookies.", "processed_at": "2025-06-24T17:00:30.984182"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/cbd44da3c3e31adcecd47d9f05013128", "title": "SAP Readiness Check : Custom code Analysis jobs are failing", "problem": "The SAP Readiness Check for Custom Code Analysis jobs were failing with many short dumps during runtime, specifically RC_COLLECT_ANALYSIS_DATA and TMW_RC_CCA_DATA_COLL.", "solution": "- Reviewed system logs via SM37 for RC_COLLECT_ANALYSIS_DATA and ST22 for errors. - Identified missing authorization as a potential cause of job failures. - Recommended adding authorization object 'S_Q_GOVERN' with ACTVT: 06 and ATC_OTYPGO: 01 to the user executing the job. - The customer confirmed that the authorization value was added, and the jobs RC_COLLECT_ANALYSIS_DATA and TMW_RC_CCA_DATA_COLL finished successfully after the change.", "cause": "The problem was caused by a missing authorization object for the user executing the SAP Readiness Check jobs, which led to system exceptions and job cancellations. After assigning the necessary authorizations, the issue was resolved.", "processed_at": "2025-06-24T17:00:37.381675"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/5b8b17a7c3631a503331583bb0013195", "title": "Readiness check integration to Cloud ALM - SAP ECC upgrades?", "problem": "Is it possible to integrate all types of readiness check analyses into SAP Cloud ALM and create follow-up items, such as requirements and user stories, regardless of whether they are ECC to S4HANA conversion readiness check results or other types of readiness checks?", "solution": "- The customer was advised to refer to the help documentation available at: https://help.sap.com/docs/cloud-alm/applicationhelp/integrating-readiness-check for information on supported scenarios and checks integrated with SAP Cloud ALM. - The customer was informed that readiness check results can be uploaded to SAP Cloud ALM, and follow-up items can be created from there. - SAP Cloud ALM uses the same URL for uploading readiness check results, whether accessed directly or through the Cloud ALM tile. - It was clarified that Readiness Check is used for upgrading from ECC to S/4HANA or from one S/4HANA product version to another, but it is not supported for ECC version upgrades.", "cause": "The initial confusion seemed to stem from whether all types of readiness checks, including ECC upgrades, can be integrated into SAP Cloud ALM, and whether follow-up items can be created from these results. This was clarified through documentation and internal confirmation.", "processed_at": "2025-06-24T17:00:43.260537"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/46b50a1797eb9a10b86630200153af59", "title": "No value was passed to the mandatory parameter \"PCT_DETAIL_LIST\"", "problem": "The customer encountered an ABAP dump when executing the report RC_COLLECT_ANALYSIS_DATA due to a syntax error indicating that no value was passed to the mandatory parameter \"PCT_DETAIL_LIST\".", "solution": "- The customer was advised to de-implement SAP Notes 2758146 and 2745851 and then re-implement them in their latest available version and in the specified order. - During re-implementation, the customer was instructed to select checkboxes for objects marked with messages like \"already existing, will be overwritten\". - The customer ran the report \"ZNOTE_2745851_REPAIR\" from SAP Note 3205320, followed the steps provided, and re-implemented SAP Note 2745851, which resolved the syntax error issue. - After implementing the solution, the report RC_COLLECT_ANALYSIS_DATA ran without generating any ABAP dump.", "cause": "The issue was caused by old coding resulting from incomplete updates during the implementation of SAP Notes 2758146 and/or 2745851.", "processed_at": "2025-06-24T17:00:47.949495"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/cbe45875c3a71ed4553db11f0501319d", "title": "Not able to upload the readiness analysis file in manage analysis files app", "problem": "The customer is unable to upload a readiness analysis file in the \"Manage Analysis File\" app for their S4 HANA system. The error indicates that the file format is not supported and refers to SAP Note 3275056, which seems incorrectly targeted for SAP ECC systems rather than S4 HANA.", "solution": "- The customer reviewed SAP Note 3275056 and completed manual activities as specified but found a missing program, RC_UDP_START_DMR2. - The customer was asked to provide the Readiness Check data collection ZIP file for review and verification. - A screenshot of the error message referring to SAP Note 3275056 was requested for further investigation.", "cause": "The SAP Note 3275056 appears to be intended for SAP_APPL (SAP ECC) systems, not for S4 HANA, leading to confusion and incorrect guidance in generating the readiness analysis file.", "processed_at": "2025-06-24T17:00:54.429414"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/79fdf13f977b1a5030c531f71153afae", "title": "Readiness Check WebView error", "problem": "Readiness Check WebView error where the web view display of data in the \"Organizational structure - Overview\" area does not match the data downloaded to Excel.", "solution": "- The issue was identified as a display problem in the application web view, while the data in Excel was correct. - SAP support fixed the display issue, and the fix was implemented in the Production environment. - The customer was advised to check the analysis in the application web view post-fix to confirm the resolution. - The customer acknowledged the solution and closed the incident.", "cause": "Unknown.", "processed_at": "2025-06-24T17:00:59.051014"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/0f499dc03b0c6e508c36d62a85e45abc", "title": "User's are unable to accesses the SAP Readinesscheck.", "problem": "User is unable to access the SAP Readiness Check portal despite having the relevant RC* authorizations assigned to the S-User. The page remains blank.", "solution": "- It is suggested to refer to SAP Knowledge Base Article (KBA) 3330283 titled \"SAP Readiness Check could not open in SAP for Me\" for resolution approaches. - The KBA provides detailed steps and potential solutions for this specific issue.", "cause": "An empty screen is not an indication of missing authorizations, as the application is supposed to display a message when accessed without the necessary roles. Therefore, the issue might not be related to authorization but rather a technical problem that is addressed in the KBA.", "processed_at": "2025-06-24T17:01:02.691484"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/877262d097cce6907bcf59081153af35", "title": "<PERSON><PERSON><PERSON>heck,", "problem": "We have applied the notes to execute the Readiness Check, but some objects show an error. Specifically, the FIN_CORR_DISPLAY_ISSUE program encounters a syntax error stating that the method \"GET_INSTANCE\" is unknown or protected or private. This error causes the TMW_RC_FDQ_DATA_COLL job to terminate.", "solution": "- Ensure the syntax error program or class is correctly imported using Note **********. - Check if the same errors occur in the LBD development system. If not, transport the healthy programs or classes from LBD to the affected system to resolve the errors. - Maintain a valid user for LBP and LBD systems in the secure area for further assistance.", "cause": "Improper user handling.", "processed_at": "2025-06-24T17:01:06.906770"}]