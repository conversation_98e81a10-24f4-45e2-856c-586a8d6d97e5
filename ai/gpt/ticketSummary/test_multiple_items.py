#!/usr/bin/env python3
"""
测试多个Problem、Solution和Cause的解析功能
"""

import sys
import os
import json
from datetime import datetime
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from get_ticket_summary import parse_structured_summary, get_summary

def test_multiple_items_parsing():
    """测试多项解析功能"""
    print("🧪 测试多个Problem、Solution和Cause的解析...")
    
    # 测试用例1：多个问题、解决方案和原因
    test_response_1 = """
    Problem 1: Customer experiencing file upload errors in SAP Readiness Check portal with "Upload failed - invalid file format" message.
    Problem 2: Analysis results are not displaying correctly after successful upload.
    Problem 3: System performance is slow during peak hours affecting user experience.

    Solution 1: Development team identified and fixed a bug in the upload validation logic.
    Solution 2: Updated the display rendering component to properly show analysis results.
    Solution 3: Implemented caching mechanism and optimized database queries to improve performance.

    Cause 1: Bug in the file validation logic that incorrectly rejected valid XML files.
    Cause 2: Frontend rendering component had a compatibility issue with new data format.
    Cause 3: Database queries were not optimized and lacked proper indexing.
    """
    
    # 测试用例2：不同数量的项目
    test_response_2 = """
    Problem 1: SAP Custom Code Analysis jobs are failing during execution.
    
    Solution 1: Restarted the analysis service.
    Solution 2: Updated the job configuration parameters.
    
    Cause 1: Service memory overflow due to large codebase analysis.
    Cause 2: Incorrect timeout settings in job configuration.
    Cause 3: Network connectivity issues between analysis nodes.
    """
    
    # 测试用例3：单个项目（仍使用编号）
    test_response_3 = """
    Problem 1: Cannot access SAP Readiness Check results from previous analysis.
    
    Solution 1: Reset user permissions and cleared browser cache.
    
    Cause 1: User session expired and permissions were not properly refreshed.
    """
    
    test_cases = [
        ("多个问题、解决方案和原因", test_response_1),
        ("不同数量的项目", test_response_2),
        ("单个项目", test_response_3)
    ]
    
    for test_name, response_text in test_cases:
        print(f"\n📋 测试用例: {test_name}")
        print("-" * 50)
        
        result = parse_structured_summary(response_text)
        
        print(f"Problems ({len(result['problems'])}):")
        for i, problem in enumerate(result['problems'], 1):
            print(f"  {i}. {problem}")
        
        print(f"\nSolutions ({len(result['solutions'])}):")
        for i, solution in enumerate(result['solutions'], 1):
            print(f"  {i}. {solution}")
        
        print(f"\nCauses ({len(result['causes'])}):")
        for i, cause in enumerate(result['causes'], 1):
            print(f"  {i}. {cause}")
        
        # 验证结果
        if (isinstance(result['problems'], list) and 
            isinstance(result['solutions'], list) and 
            isinstance(result['causes'], list)):
            print("✅ 解析成功 - 所有字段都是列表格式")
        else:
            print("❌ 解析失败 - 字段格式不正确")

def test_edge_cases():
    """测试边界情况"""
    print("\n🔍 测试边界情况...")
    
    edge_cases = [
        ("空响应", ""),
        ("无编号标签", "Problem: Single problem without number\nSolution: Single solution\nCause: Single cause"),
        ("混合格式", "Problem 1: First problem\nProblem: Second problem without number\nSolution 1: First solution"),
        ("不规范编号", "Problem 5: Problem with number 5\nSolution 2: Solution with number 2\nCause 10: Cause with number 10"),
        ("重复编号", "Problem 1: First problem\nProblem 1: Duplicate problem\nSolution 1: First solution")
    ]
    
    for test_name, response_text in edge_cases:
        print(f"\n📋 边界测试: {test_name}")
        print("-" * 30)
        
        result = parse_structured_summary(response_text)
        print(f"Problems: {len(result['problems'])} items")
        print(f"Solutions: {len(result['solutions'])} items")
        print(f"Causes: {len(result['causes'])} items")

def test_real_api_call():
    """测试真实API调用"""
    print("\n🤖 测试真实API调用...")
    
    # 模拟一个复杂的票据内容
    complex_ticket_content = """
    Title: Multiple Issues with SAP Readiness Check and Custom Code Analysis
    
    Content: Customer reported several issues with their SAP upgrade preparation:
    
    1. File upload functionality in SAP Readiness Check portal is failing with "invalid format" errors
    2. Custom Code Analysis jobs are timing out during execution
    3. Results from previous analysis are not accessible through the portal
    4. System performance is degraded during peak usage hours
    
    Support team investigation revealed:
    - Upload validation logic had a bug affecting XML file processing
    - Analysis service was running out of memory due to large codebase
    - User session management had issues with permission refresh
    - Database queries were not optimized for concurrent access
    
    Resolution steps taken:
    - Fixed the file validation bug and deployed the patch
    - Increased memory allocation for analysis service
    - Updated session management logic
    - Implemented database query optimization and caching
    - Added monitoring for system performance
    
    Root causes identified:
    - Software bug in validation routine
    - Insufficient resource allocation
    - Outdated session management implementation
    - Poor database design and indexing
    """
    
    try:
        print("📤 发送请求到AI Core...")
        response_text = get_summary(complex_ticket_content)
        
        print(f"🤖 API响应:")
        print("-" * 50)
        print(response_text)
        print("-" * 50)
        
        print("\n📊 解析结果:")
        result = parse_structured_summary(response_text)
        
        print(f"\nProblems ({len(result['problems'])}):")
        for i, problem in enumerate(result['problems'], 1):
            print(f"  {i}. {problem}")
        
        print(f"\nSolutions ({len(result['solutions'])}):")
        for i, solution in enumerate(result['solutions'], 1):
            print(f"  {i}. {solution}")
        
        print(f"\nCauses ({len(result['causes'])}):")
        for i, cause in enumerate(result['causes'], 1):
            print(f"  {i}. {cause}")
        
        # 生成最终JSON结构
        final_result = {
            "url": "https://sap.service-now.com/complex/12345",
            "title": "Multiple Issues with SAP Readiness Check and Custom Code Analysis",
            "problems": result['problems'],
            "solutions": result['solutions'],
            "causes": result['causes'],
            "processed_at": datetime.now().isoformat()
        }
        
        print(f"\n📋 最终JSON结构:")
        print(json.dumps(final_result, indent=2, ensure_ascii=False))
        
        # 保存测试结果
        output_file = f"multiple_items_test_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump([final_result], f, ensure_ascii=False, indent=2)
        
        print(f"\n💾 测试结果已保存到: {output_file}")
        
        return True
        
    except Exception as e:
        print(f"❌ API调用失败: {e}")
        return False

if __name__ == '__main__':
    print("🧪 测试多项Problem、Solution和Cause解析系统")
    print("=" * 60)
    
    # 运行所有测试
    test_multiple_items_parsing()
    test_edge_cases()
    
    print("\n" + "=" * 60)
    api_success = test_real_api_call()
    
    if api_success:
        print("\n✅ 所有测试通过!")
        print("系统可以正确处理多个Problem、Solution和Cause")
    else:
        print("\n⚠️  解析功能正常，但API调用失败")
        print("请检查API配置和网络连接")
