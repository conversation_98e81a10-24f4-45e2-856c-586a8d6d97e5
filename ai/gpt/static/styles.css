/* Styles */
#chatbox {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  width: 50%;    /* change width as per requirement */
  height: auto;
  margin: 0 auto;
  padding: 10px;
  display: flex;
  justify-content: center;
  background-color: lightgrey;
}

#myMessage {
  width: 100%;
  margin-right: 10px;
  box-sizing: border-box;  /* This is important when setting width to 100% */
}

#sendButton {
  width: auto;
  flex-shrink: 0;  /* Prevent the button from shrinking if the text field takes up more space */
}

#messages {
  overflow-y:auto;
  height:calc(100vh - 100px);
  width: 50%;
  margin: 0 auto;
  padding: 30px;
  list-style-type: none; /* No bullets */
}

.userMessage {
  text-align: right;
  color: blue;
}

.systemResponse {
  text-align: left;
  color: green;
}