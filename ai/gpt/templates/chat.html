<!DOCTYPE html>
<html>
  <head>
    <title>Chat with Machine</title>
    <link rel="stylesheet" type="text/css" href="{{ url_for('static', filename='styles.css') }}">
  </style>
  </head>
  <body>
    <ul id="messages"></ul>
    <div id="chatbox">
      <input id="myMessage" width="80%" placeholder="Enter your message">
      <button id="sendButton">Send</button>
    </div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/socket.io/4.0.1/socket.io.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.4.1.min.js"></script>
    <script type="text/javascript">
      $(document).ready(function () {
        var socket = io.connect('http://' + document.domain + ':' + location.port);
        
        function sendMessage() {
            var msg = $('#myMessage').val();
            $('#messages').append($('<li class="userMessage">').text(msg));
            socket.emit('message', msg);
            $('#myMessage').val('');
        }

        $('#myMessage').on('keydown', function(e) {
            // Check if the key was Enter
            if (e.which == 13) {
                e.preventDefault();  // Prevent new line being entered in input box
                sendMessage();
            }
        });

        $('#sendButton').on('click', function() {
            sendMessage();
        });
        
        socket.on('message', function (msg) {
          $('#messages').append($('<li class="systemResponse">').text(msg));
        });

        // MutationObserver to autoscroll when new messages are added
        const messagesContainer = document.querySelector('#messages');
        const observer = new MutationObserver(() => {
            messagesContainer.scrollTop = messagesContainer.scrollHeight;
        });

        observer.observe(messagesContainer, { childList: true });
      });
    </script>
  </body>
</html>